{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "importHelpers": true, "esModuleInterop": true, "module": "esnext", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "moduleResolution": "node", "experimentalDecorators": true, "target": "ES2022", "lib": ["es2020", "dom"], "typeRoots": ["node_modules/@types"], "useDefineForClassFields": false}, "angularCompilerOptions": {"enableIvy": true}}