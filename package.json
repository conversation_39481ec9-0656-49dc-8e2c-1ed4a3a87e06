{"name": "gallop-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "extract-translations": "ngx-translate-extract --input ./src --output ./src/assets/i18n/*.json --clean --sort --format namespaced-json --marker _"}, "private": true, "dependencies": {"@angular-devkit/build-angular": "^19.2.12", "@angular-magic/ngx-gp-autocomplete": "^2.0.2", "@angular-slider/ngx-slider": "^19.0.0", "@angular/animations": "^19.2.11", "@angular/cdk": "^16.2.14", "@angular/cli": "^19.2.12", "@angular/common": "^19.2.11", "@angular/compiler": "^19.2.11", "@angular/compiler-cli": "^19.2.11", "@angular/core": "^19.2.11", "@angular/forms": "^19.2.11", "@angular/google-maps": "^19.2.16", "@angular/localize": "19.2.11", "@angular/material": "^16.2.14", "@angular/platform-browser": "^19.2.11", "@angular/platform-browser-dynamic": "^19.2.11", "@angular/router": "^19.2.11", "@googlemaps/js-api-loader": "^1.16.8", "@hakimio/ngx-google-analytics": "^15.0.0", "@kolkov/ngx-gallery": "^2.0.1", "@ng-bootstrap/ng-bootstrap": "^14.2.0", "@ng-select/ng-select": "^11.2.0", "@ngx-progressbar/core": "^5.3.2", "@ngx-progressbar/http": "^5.3.2", "@ngx-progressbar/router": "^5.3.2", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^3.0.1", "@popperjs/core": "^2.11.8", "@types/google.maps": "^3.58.1", "@types/jquery": "^3.5.32", "angular-font-awesome": "^3.1.2", "angular-ng-autocomplete": "^2.0.12", "angularx-social-login": "^4.1.0", "bootstrap": "^4.6.2", "chart.js": "^4.4.2", "chartjs-plugin-datalabels": "^2.2.0", "chartjs-plugin-labels": "^1.1.0", "core-js": "^2.6.12", "font-awesome": "^4.7.0", "google-libphonenumber": "^3.2.40", "jquery": "^3.7.1", "lz-string": "^1.5.0", "moment": "^2.30.1", "ng-connection-service": "^1.0.4", "ng-toast": "^2.0.0", "ng2-charts": "^3.1.2", "ng2-tooltip-directive": "^2.10.3", "ng5-slider": "^1.2.6", "ngx-bootstrap": "^19.0.2", "ngx-cookie-service": "^15.0.0", "ngx-gallery-9": "^1.0.6", "ngx-google-maps-places-autocomplete": "^19.0.1", "ngx-google-places-autocomplete": "^2.0.5", "ngx-infinite-scroll": "^9.1.0", "ngx-mask": "^16.4.2", "ngx-pagination": "^6.0.2", "ngx-progressbar": "^14.0.0", "ngx-smart-modal": "^14.0.2", "ngx-toastr": "^15.2.2", "ngx-truncate": "^0.0.1", "ngx-ui-loader": "^13.0.0", "ngx-ui-scroll": "^3.1.1", "ngx-ui-switch": "^12.0.0", "nouislider": "^11.1.0", "os": "^0.1.2", "rxjs": "^7.8.2", "sass": "^1.86.3", "tslib": "^2.0.0", "vscroll": "^1.6.3", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/language-service": "19.2.11", "@biesbjerg/ngx-translate-extract": "^2.3.4", "@ngtools/webpack": "^16.2.12", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "^2.0.13", "@types/node": "^12.20.55", "angular-ide": "^0.9.77", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "json-server": "^0.14.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~5.0.1", "tslint": "~6.1.0", "typescript": "^5.8.3"}}