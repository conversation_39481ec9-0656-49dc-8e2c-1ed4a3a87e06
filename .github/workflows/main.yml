name: Deployment Workflow

on:
  workflow_dispatch:
    inputs:
      branch_name:
        description: 'Branch name'
        required: true
        type: string
        default: 'dev_22aug23'
      env_value:
        description: 'Environment Value'
        required: true
        default: 'dev'
        type: string
        
jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
    - name: Build and Deploy Augular App
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.KEY }}
        script: |
         cd buildNg
         ./create_ng9_build_for_routespring.sh ${{ github.event.inputs.branch_name }} ${{ github.event.inputs.env_value }} y
