{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"gallop-ui": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/gallop-ui"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "allowedCommonJsDependencies": ["chart.js", "j<PERSON>y", "moment", "ngx-google-places-autocomplete", "google-libphonenumber"], "styles": ["src/icons.css", "src/styles-app-loading.scss", "src/styles.scss", "node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "./node_modules/ngx-ui-switch/ui-switch.component.css", "src/assets/css/tab-navigation-fix.css"], "scripts": ["node_modules/jquery/dist/jquery.js", "src/assets/js/library/bootstrap/bootstrap.min.js", "src/assets/js/library/material/material.min.js", "src/assets/js/library/jquery-ui/jquery-ui.min.js", "src/assets/js/library/jquery-ui/jquery.ui.touch-punch.min.js", "src/assets/js/library/jquery-validate/jquery.validate.js", "src/assets/js/library/jquery-validate/additional-methods.js", "src/assets/js/library/intlTelInput/intlTelInput.js", "src/assets/js/library/intlTelInput/utils.js", "src/assets/js/library/select2/select2.min.js", "src/assets/js/library/moment/moment.js", "src/assets/js/emailflow/script.js", "src/assets/js/emailflow/script1.js", "src/assets/js/user_profile_scripts.js", "src/assets/js/user_profile_form.js", "src/assets/js/user_profile_template.js", "src/assets/js/tab-navigation-fix.js", "src/assets/js/airlines.js"], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": false, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true}, "development": {"optimization": false, "sourceMap": true, "extractLicenses": false, "namedChunks": true}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"disableHostCheck": true, "buildTarget": "gallop-ui:build"}, "configurations": {"development": {"buildTarget": "gallop-ui:build:development"}, "production": {"buildTarget": "gallop-ui:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "gallop-ui:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/icons.css", "src/styles.scss"], "scripts": [], "assets": ["src/favicon.ico", "src/assets"]}}}}, "gallop-ui-e2e": {"root": "e2e/", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "gallop-ui:serve"}, "configurations": {"production": {"devServerTarget": "gallop-ui:serve:production"}}}}}}}