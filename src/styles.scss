// 1rem = 14px

// bootstrap overrides
/*
$grid-gutter-width: 8px;
$body-bg: #EEEDEB;

$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px
);

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1160px
);

$spacer: 1rem !default;
$spacers: () !default;
$spacers: map-merge((
  0: 0,
  1: ($spacer * .25),
  2: ($spacer * .5),
  3: $spacer,
  4: ($spacer * 1.5),
  5: ($spacer * 2)
), $spacers);
*/

// imports
@import "variables.scss";





// font family

@font-face {
    font-family: ApercuPro;
    src: url("./assets/fonts/apercu_regular_pro.otf") format("opentype");
}

@font-face {
    font-family: ApercuPro;
    font-weight: 300;
    src: url("./assets/fonts/apercu_light_pro.otf") format("opentype");
}

@font-face {
    font-family: ApercuPro;
    font-weight: 600;
    src: url("./assets/fonts/apercu_bold_pro.otf") format("opentype");
}

@font-face {
    font-family: password;
    src: url("./assets/fonts/password.ttf") format("truetype");;
  }

@font-face {
    font-family: ApercuProMono;
    src: url("./assets/fonts/apercu_mono_pro.otf") format("opentype"),
         url("./assets/fonts/apercu_mono_pro.eot") format('embedded-opentype');
}

@font-face {
    font-family: 'apercu-r';
    src: url("./assets/fonts/apercu_regular_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }
@font-face {
    font-family: 'apercu-i';
    src: url("./assets/fonts/apercu_regular_italic_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }

@font-face {
    font-family: 'apercu-mono';
    src: url("./assets/fonts/apercu_mono_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }

@font-face {
    font-family: 'apercu-m';
    src: url("./assets/fonts/apercu_medium_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }

@font-face {
    font-family: 'apercu-b';
    src: url("./assets/fonts/apercu_bold_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }

@font-face {
    font-family: 'apercu-mi';
    src: url("./assets/fonts/apercu_medium_italic_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }

@font-face {
    font-family: 'apercu-l';
    src: url("./assets/fonts/apercu_light_pro_000.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }


@font-face {
    font-family: 'sourceSansPro-r';
    src: url("./assets/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
    }
    @font-face {
      font-family: 'Inter';
      src: url("./assets/fonts/Inter-VariableFont_opsz,wght.ttf") format("opentype");
      font-weight: normal;
      font-style: normal;
      }

  @font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: local("Material Icons"), local("MaterialIcons-Regular"), url("./assets/fonts/material/fonts/material-icons.woff2") format("woff2"); }
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -moz-font-feature-settings: 'liga';
  -moz-osx-font-smoothing: grayscale; }


html,
body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: antialiased;
    font-family: ApercuPro;
    font-size: 14px;
    letter-spacing: 0.4px;
    color: $primary-text-color;
    background-color: #EDFAFC;
}

::placeholder {
    color: $secondary-text-color;
    opacity: 1;
    font-weight: 300;
}
:-ms-input-placeholder {
   color: $secondary-text-color;
   font-weight: 300;
}

::-ms-input-placeholder {
   color: $secondary-text-color;
   font-weight: 300;
}

::-ms-clear {
    display: none;
}

ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

// global
.mdc-text-field--invalid .mdc-text-field__input {
  caret-color: #000 !important;  /* Set cursor color */
  color: inherit !important;     /* Optional: prevent red text */
}
.ng-select .ng-select-container .ng-value-container .ng-input input{
    caret-color: black !important;
}
.ng-select .ng-select-container .ng-value-container .ng-input>input{
    caret-color: black !important;
}
.ng-select .ng-select-container .ng-value-container .ng-input>input {
    box-sizing: content-box;
    background: none;
    border: 0;
    box-shadow: none;
    outline: 0;
    cursor: pointer !important;
    width: 100%;
}
// div:focus,
// input:focus {
//     border: 1px solid $primary-color !important;
// }

.ng-select input {
    border: none !important;
}
.pac-container {
  z-index: 999999 !important;
}
.main-wrapper {
    padding: 0px 0;
    max-width: 1500px;
    margin: 0 auto;
}

.content {
    position: relative;
    z-index: 10;
}
.btn-secondary1 {
  min-height: 40px;
  height: auto;
  width: 160px;
  color: var(--button-font-color);
  border-radius: 0px !important;
  letter-spacing: 1px;
  background-color: var(--button-bg-color) !important;
  margin-top: 0px;
  border: none;
  box-shadow: none;
}
.btn-normal {
  height: 50px;
  width: 200px;
  letter-spacing: 1px;
  background-color: transparent !important;
  margin-top: 0px;
  border: none !important;
  box-shadow: none !important;
}
.travellerName{
  min-width: 200px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}
.add1{
  color: var(--hyperlink-color);   
}
.itinenary{
  color: var(--hyperlink-color);
  margin-left: 20px;
  cursor: pointer;
  position: relative;
  top: -11px;
  text-transform: capitalize;
}
.modal-title {
  text-align: left;
  font-family: $fontMono;
}
.modal-header {
  background-color: var(--hyperlink-color);
  color: #FFFFFF;
  font-size: 14px;
  height: 40px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 6px 6px 0 0;
  padding: 0 8px 0 22px !important;
  border-bottom: none;
}

.modal-header h5 {
  font-size: 14px;
  font-family: "apercu-mono";
}
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    flex-direction: row;

    .signin {
        color: $link-color;
        cursor: pointer;
    }

}
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100dvh !important;
    background-color: #000;
}
.top-strip{
  background-color: var(--dark-bg-color) !important;
}
.addlue{
  color: var(--hyperlink-color) !important;
}
.addlueBackground{
  background: var(--hyperlink-color) !important;
}
.profile {
    display: flex;
    align-items: center;
    cursor: pointer;
    .profile-image {
        height: 35px;
        width: 35px;
        border-radius: 50%;
        background: $border-dark-color;
        margin-right: 10px;
        border: 1px solid icon-color;
    }
}
.signout{
  align-items: center;
  cursor: pointer;
  color: $link-color;
  cursor: pointer;
  flex-direction: column;
  flex-flow: wrap;
}
.text-danger {
    color: $danger !important;
}
.text-gray{
    color: gray !important;
}
.text-primary {
    color: $primary-color !important;
}

.text-secondary {
    color: $secondary-text-color !important;
}

.text-link {
    color: $link-color !important;
}

.text-fare {
    color: #F5A623;
}

.cursor-pointer {
    cursor: pointer !important;
}
.ngx-slider .ngx-slider-bubble.ngx-slider-limit {
    color: #55637d;
    display: none !important;
}
#filter-container-time .ng-dropdown-panel .scroll-host {
    position: relative;
    display: block;
    max-height: 370px !important;
    -webkit-overflow-scrolling: touch;
}
.ngx-slider .ngx-slider-bubble {
    cursor: default;
    bottom: 16px;
    padding: 1px 3px;
    display: none !important;
    color: #55637d;
    font-size: 16px;
}
.cursor-default {
    cursor: default !important;
}
//mat input
// controls

.field-label {
    color: $secondary-text-color;
    margin-bottom: 8px;
}

.field-value {
    color: $link-color;
}

.text-input {
    position: relative;

    .icon {
        position: absolute;
        top:  19px;
        left:  20px;
        font-size: 1.7143rem;
    }

    input {
        border: none;
        padding: 20px;
        width: 100%;
        font-size: 1rem;
        color: $primary-text-color;
        border-radius: 6px;
        background: #fff;
        font-family: ApercuProMono;
        border: 1px solid transparent;

        &:focus,
        &:active {
            outline: none;
        }
    }

    .icon + input {
        padding-left: 56px;
    }

    &.invert {
        input {
            border: 1px solid $border-color;
            background: $background-light;
        }
    }
}

.radio-input {
    display: inline-block;
}
.ng-meteor {
    display: block;
    position: absolute;
    width: 100px;
    height: 100%;
    opacity: 0 !important;
    }
.radio-input [type="radio"]:checked,
.radio-input [type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
}

.radio-input [type="radio"]:checked + label,
.radio-input [type="radio"]:not(:checked) + label {
    position: relative;
    padding-left: 20px;
    cursor: pointer;
    line-height: 1rem;
    display: inline-block;
    color: $secondary-text-color;
    font-size: 1rem;
    margin-bottom: 0;
    font-weight: 300;
}

.radio-input [type="radio"]:checked + label {
    color: $primary-text-color;
}

.radio-input [type="radio"]:checked + label:before,
.radio-input [type="radio"]:not(:checked) + label:before {
    content: '';
    position: absolute;
    left: 0;
    top: 1px;
    width: 12px;
    height: 12px;
    background: #EEEDEB;
    border-radius: 100%;
}

.radio-input [type="radio"]:checked + label:before {
    border: 2px solid $accent-color;
    background: $accent-color;
}

.check-input {
    display: block;
    font-size: 12px;
}

.check-input input {
    padding: 0;
    height: initial;
    width: initial;
    margin-bottom: 0;
    display: none;
    cursor: pointer;
}

.check-input label {
    position: relative;
    cursor: pointer;
    margin: 0;
    color: $secondary-text-color;
    display: flex;
    align-items: center;
}

.check-input label:before {
    content:'';
    -webkit-appearance: none;
    background-color: transparent;
    border: 1px solid $border-dark-color;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
    padding: 5px;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    cursor: pointer;
    margin-right: 8px;
}

.check-input input:checked + label:after {
    content: '';
    display: block;
    position: absolute;
    top: 4px;
    left: 5px;
    width: 4px;
    height: 9px;
    border: solid $accent-color;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.date-input {
    margin: 0 10px 0 5px;
    position: relative;

    .field-value {
        cursor: pointer;
    }

    input {
        position: absolute;
        z-index: -1;
        top: 35px;
        width: 100%;
    }
}
.tab-list-item a {
  color: #FFFFFF !important;
 
}
.filter-item-link {

  color: #fff !important;
}
.select-input {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;

    &.checked {
        .label {
            color: $accent-color;
        }
    }

    .field-value {
        cursor: pointer;
    }

    .label {
        color: $secondary-text-color;
        margin-right: 10px;
    }

    .ng-select {
        position: absolute;
        z-index: -1;
        top: 0;
        width: 100%;
        padding-bottom: 0;
        height: 34px;
    }
}
.select-input12 {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;

    &.checked {
        .label {
            color: $accent-color;
        }
    }

    .field-value {
        cursor: pointer;
    }

    .label {
        color: $secondary-text-color;
        margin-right: 10px;
    }

    .ng-select {
        position: absolute;
        z-index: -1;
        top: 0;
        width: 100%;
        display: none;
        padding-bottom: 0;
        height: 34px;
    }
}
.btn {
    border-radius: 0;
    box-shadow: 0 0 20px 0 rgba(0,0,0,0.14);
    font-size: 1rem;
    border-radius: 6px;
    text-transform: uppercase;
    font-weight: 600;
}

.primary-button {
    background-color: var(--button-bg-color);
    color: var(--button-font-color);

    img {
        vertical-align: unset;
        margin-left: 3px;
    }

    &[disabled] {
        background-color: var(--button-bg-color);
        cursor: not-allowed;
    }
}

.secondary-button {
    background-color: $background-light;
    color: var(--button-bg-color);

    &[disabled] {
        background-color: rgba(243, 243, 243, 0.6);
        cursor: not-allowed;
    }
}

.link-button {
    color: var(--hyperlink-color);
    box-shadow: none;
    border: none;
    background: none;
    font-weight: 600;
    font-size: 18px;
    cursor: pointer;
    outline: none;
}

// datepicker override

.bs-datepicker {
    box-shadow: 0 8px 10px 0 rgba(0,0,0,0.14);
    margin-bottom: 20px;
    border-radius: 6px;
    position: relative;
    z-index: 30;

    .bs-datepicker-container {
        padding: 0;
    }

    .bs-datepicker-head {
        border-radius: 6px 6px 0 0;
        background: $accent-color;
    }

    .bs-datepicker-navigation-view {
        display: flex;
        justify-content: space-around;
    }

    .bs-datepicker-body {
        border-radius: 0 0 6px 6px;
    }

    .bs-datepicker-body table th {
        color: $primary-text-color;
    }

    .bs-datepicker-body table td {
        padding: 5px 7px;
        color: $primary-text-color;
    }

    .bs-datepicker-body table td span {
        font-size: 1rem;
    }

    .bs-datepicker-body table td span.disabled,
    .bs-datepicker-body table td.disabled span {
        color: $secondary-text-color;
    }

    .bs-datepicker-body table td span.selected,
    .bs-datepicker-body table td.selected span,
    .bs-datepicker-body table td span[class*="select-"]:after,
    .bs-datepicker-body table td[class*="select-"] span:after {
        background-color: $accent-color;
        color: var(--button-font-color);
    }
}

// ng-onSelect overrides

.ng-input input {
    outline: none !important;
}
#constraint-time .ng-dropdown-panel .scroll-host {
    overflow-x: auto !important;
    overflow-y: auto;
    position: relative;
    display: flex !important;
    -webkit-overflow-scrolling: touch;
} 
#constraint-time1 .ng-dropdown-panel .scroll-host {
    overflow-x: auto !important;
    overflow-y: auto;
    position: relative;
    display: flex !important;
    -webkit-overflow-scrolling: touch;
} 
#constraint-time-leg0 .ng-dropdown-panel .scroll-host {
    overflow-x: auto !important;
    overflow-y: auto;
    position: relative;
    display: flex !important;
    -webkit-overflow-scrolling: touch;
}
#constraint-time-leg1 .ng-dropdown-panel .scroll-host {
    overflow-x: auto !important;
    overflow-y: auto;
    position: relative;
    display: flex !important;
    -webkit-overflow-scrolling: touch;
}
#constraint-time-leg2 .ng-dropdown-panel .scroll-host {
    overflow-x: auto!important;
    overflow-y: auto;
    position: relative;
    display: flex !important;
    -webkit-overflow-scrolling: touch;
}
#constraint-time-leg3 .ng-dropdown-panel .scroll-host {
    overflow-x: auto!important;
    overflow-y: auto;
    position: relative;
    display: flex !important;
    -webkit-overflow-scrolling: touch;
}
#constraint-time-leg4 .ng-dropdown-panel .scroll-host {
    overflow-x: auto!important;
    overflow-y: auto;
    position: relative;
    display: flex !important;
    -webkit-overflow-scrolling: touch;
}
#constraint-time .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: -20% !important; right: auto !important; bottom: auto !important;}
#constraint-time1 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: -20% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg0 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: -20% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg1 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: -20% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg2 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: -20% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg3 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: -20% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg4 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: -20% !important; right: auto !important; bottom: auto !important;}
.ng-dropdown-panel-items {
    border-radius: 6px;
}

.ng-dropdown-panel.ng-select-bottom {
    margin-bottom: 20px;
    border-radius: 6px;
    box-shadow: 0 15px 15px -1px rgba(0,0,0,.4), 0 15px 15px 1px rgba(0,0,0,.14), 0 15px 15px 2px rgba(0,0,0,.12);
}

.ng-dropdown-panel  {
    font-family: ApercuProMono;
    width: auto !important;
}

.open-right.ng-dropdown-panel {
    right: -145px;
    left: auto !important;
}
.class-seater{
    right: 0px;
    left: auto !important;
}

#id-arrive-time-0 .ng-dropdown-panel{
    left:-95px !important;
}
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
    font-size: 1rem;
    border-bottom: 1px solid $border-color;
    padding: 0 24px;
    display: flex !important; // TODO: Fix this
    justify-content: space-between;
    color: $secondary-text-color;
    font-weight: 300;
}
.btn-secondary {
   
    background-color: var(--button-bg-color) !important;
    
}
.btn-dander {
   
    background-color: var(--button-bg-color) !important;
    
}
.phone-number .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{
    padding: 0px 10px;
    font-size: 10px;
}
.selectedTraveler .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{
    padding: 0px 0px 0px 15px;
   
}
.phone-number .ng-dropdown-panel  {
    width:140px !important;
}

  #typeEmployee .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
    overflow:visible !important;
    position: relative !important;
    top: -8px !important;
    color: gray !important;
}
#typeEmployee .ng-select.ng-select-container.ng-value-container.ng-input{
    position: fixed !important;
    top: -10px;
  }
#typeEmployee .ng-select .ng-arrow-wrapper .ng-arrow{
    display: none !important;
}
#typeEmployee .ng-select{
padding-left :30px !important;
}

#typeEmployee .ng-dropdown-panel {
    max-width: 305px !important;
    min-width: 305px !important;
      top: 65px !important;
      left: 0px !important;
      background: #F7F7F9 !important;
      right: auto !important;
      bottom: auto !important;
      overflow: scroll !important;
    }
#typeEmployee .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
    position: absolute !important;
    left: 0 !important;
    width: 100% !important;
    top: 4px !important;
}
    #typeEmployee .ng-select-container {
        border: none !important;
        &:after {
          display: none;
      }
    }
#typeEmployee .ng-select .ng-select-container .ng-value-container .ng-input > input {
    caret-color: black !important;
}
#addcardList .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#policylistDiv .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
  max-width: 310px !important;
}
#addcardList .ng-dropdown-panel {
    max-width: 99% !important;
    top: auto !important;
    left: 10px !important;
    background: #F7F7F9 !important;
    right: auto !important;
    bottom: auto !important;
    min-width: 96% !important;
    }
    #addcardList .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
        position: absolute !important;
        left: 0 !important;
        width: 100% !important;
        top: 2px !important;
    }
    #addcardList .ng-clear-wrapper{
      display: none !important;
    }
    #addcardList .ng-select span {
      box-sizing: border-box;
    }
    #addcardList .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;}
      #policylistDiv .ng-select.ng-select-container.ng-value-container.ng-input{
      position: fixed !important;
      top: -10px;
    }
    #addcardList .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
      overflow:visible !important;
      position: relative !important;
        top: -6px !important;
        max-width: 310px !important;
    }
    #addcardList .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
    #addcardList .ng-select-container {
      border: none !important;
      &:after {
        display: none;
    }
  }
#expenselistDiv1 .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}

#expenselistDiv1 .ng-dropdown-panel {
    max-width: 590px !important;
    min-width: 590px !important;
      top: auto !important;
      left: auto !important;
      overflow-x:scroll;
      background: #F7F7F9 !important;
      right: auto !important;
      bottom: auto !important;
    }
 
    #expenselistDiv1 .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
        position: absolute !important;
        left: 0 !important;
        width: 100% !important;
        top: 2px !important;
    }
    #expenselistDiv1 .ng-clear-wrapper{
     // display: none !important;
    }
    #expenselistDiv1 .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
        white-space: nowrap;
        text-overflow: ellipsis !important;
        overflow: hidden !important;
       
    }
    #expenselistDiv1 .ng-select.ng-select-single .ng-select-container .ng-clear-wrapper {
        -ms-flex-item-align: end;
        align-self: flex-end;
        bottom: 18px !important;
        font-size: 18px !important;
        color: var(--dark-bg-color) !important;
        left:5px;
        display: inline-block !important;
    }
    #expenselistDiv1 .ng-dropdown-panel .scroll-host {
        overflow-x: auto !important;
        overflow-y: auto;
        position: relative;
        display: flex !important;
        -webkit-overflow-scrolling: touch;
    }
    #expenselistDiv1 .ng-select span {
        max-width: 500px !important;
      box-sizing: border-box;
      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }
    #expenselistDiv1 .ng-select .ng-select-container .ng-value-container {
        -ms-flex-align: stretch;
        align-items: stretch;
        padding: 0.4375em 0;
        border-top: 0.34375em solid transparent !important;
      }
      #expenselistDiv1 .ng-select .ng-select-container .ng-value-container .ng-placeholder {
      position: absolute;
      color: black;
      -ms-transform-origin: 0 0;
      transform-origin: 0 0;
      transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),color 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      top: 5px !important;
      }
    #expenselistDiv1 .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;}
      #policylistDiv .ng-select.ng-select-container.ng-value-container.ng-input{
      position: fixed !important;
      top: -10px;
    }
    
    #expenselistDiv1 .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
        overflow:hidden !important;
        display: inline-block !important;
    }
    #expenselistDiv1 .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
    #expenselistDiv1 .ng-select-container {
      border: none !important;
      &:after {
        display: none;
    }
  }
  #expenselistDiv11 .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}

    #exportDropdown .ng-select .ng-select-container .ng-value-container .ng-placeholder {
        position: absolute;
        color: black;
        -ms-transform-origin: 0 0;
        transform-origin: 0 0;
        transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),color 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        top: -2px !important;
        }
#expenselistDiv11 .ng-dropdown-panel {
    max-width: 353px !important;
   width: 100% !important;
      top: auto !important;
      left: auto !important;
      overflow-x:scroll;
      background: #F7F7F9 !important;
      right: auto !important;
      bottom: auto !important;
    }
 
    #expenselistDiv11 .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
        position: absolute !important;
        left: 0 !important;
        width: 100% !important;
        top: 2px !important;
    }
    #expenselistDiv11 .ng-clear-wrapper{
     // display: none !important;
    }
    #expenselistDiv11 .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
        white-space: nowrap;
        text-overflow: ellipsis !important;
        overflow: hidden !important;
       
    }
    #expenselistDiv11 .ng-select.ng-select-single .ng-select-container .ng-clear-wrapper {
        -ms-flex-item-align: end;
        align-self: flex-end;
        bottom: 18px !important;
        font-size: 18px !important;
        color: var(--dark-bg-color) !important;
        left:5px;
    }
    #expenselistDiv11 .ng-dropdown-panel .scroll-host {
        overflow-x: auto !important;
        overflow-y: auto;
        position: relative;
        display: flex !important;
        -webkit-overflow-scrolling: touch;
    }
    #expenselistDiv11 .ng-select span {
      box-sizing: border-box;
      display: inline-block !important;
      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }
    #expenselistDiv11 .ng-select .ng-select-container .ng-value-container {
        -ms-flex-align: stretch;
        align-items: stretch;
        padding: 0.4375em 0;
        border-top: 0.34375em solid transparent !important;
      }
      #expenselistDiv11 .ng-select .ng-select-container .ng-value-container .ng-placeholder {
      position: absolute;
      color: black;
      -ms-transform-origin: 0 0;
      transform-origin: 0 0;
      transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),color 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      top: 5px !important;
      }
    #expenselistDiv11 .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;}
      #policylistDiv .ng-select.ng-select-container.ng-value-container.ng-input{
      position: fixed !important;
      top: -10px;
    }
    
    #expenselistDiv11 .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
        overflow:hidden !important;
        display: inline-block !important;
    }
    #expenselistDiv11 .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
    #expenselistDiv11 .ng-select-container {
      border: none !important;
      &:after {
        display: none;
    }
  }
#policylistDiv .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#policylistDiv .ng-dropdown-panel {
   max-width: 353px !important;
    top: auto !important;
    min-width: 353px !important;
    width: 100% !important;
      left: auto !important;
      background: #F7F7F9 !important;
      right: auto !important;
      bottom: auto !important;
    }
    #policylistDiv .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
        position: absolute !important;
        left: 0 !important;
        width: 100% !important;
        top: 2px !important;
    }
    #policylistDiv .ng-clear-wrapper{
      display: none !important;
    }
    #policylistDiv .ng-select span {
      box-sizing: border-box;
    }
    #policylistDiv .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;}
      #policylistDiv .ng-select.ng-select-container.ng-value-container.ng-input{
      position: fixed !important;
      top: -10px;
    }
    #policylistDiv .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
      overflow:visible !important;
      position: relative !important;
        top: -6px !important;
    }
    #policylistDiv .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
    #policylistDiv .ng-select-container {
      border: none !important;
      &:after {
        display: none;
    }
  }
  #addDepartmentList .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#addDepartmentList .ng-dropdown-panel {
    top: auto !important;
    min-width: 96% !important;
      left: auto !important;
      background: #F7F7F9 !important;
      right: auto !important;
      bottom: auto !important;
      max-width: 96% !important;
    }
    #addDepartmentList .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
        position: absolute !important;
        left: 0 !important;
        width: 100% !important;
        top: 2px !important;
    }
    #addDepartmentList .ng-clear-wrapper{
      display: none !important;
    }
    #addDepartmentList .ng-select span {
      box-sizing: border-box;
    }
    #addDepartmentList .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;}
      #policylistDiv .ng-select.ng-select-container.ng-value-container.ng-input{
      position: fixed !important;
      top: -10px;
    }
    #addDepartmentList .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
      overflow:visible !important;
      position: relative !important;
        top: -6px !important;
    }
    #addDepartmentList .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
    #addDepartmentList .ng-select-container {
      border: none !important;
      &:after {
        display: none;
    }
  }
  #addAirlineList .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#addAirlineList .ng-dropdown-panel {
    top: auto !important;
    min-width: 97% !important;
      left: auto !important;
      background: #F7F7F9 !important;
      right: auto !important;
      bottom: auto !important;
      max-width: 97% !important;
    }
    #addAirlineList .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
        position: absolute !important;
        left: 0 !important;
        width: 100% !important;
        top: 2px !important;
    }
    #addAirlineList .ng-clear-wrapper{
      display: none !important;
    }
    #addAirlineList .ng-select span {
      box-sizing: border-box;
    }
    #addAirlineList .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;}
      #policylistDiv .ng-select.ng-select-container.ng-value-container.ng-input{
      position: fixed !important;
      top: -10px;
    }
    #addAirlineList .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
      overflow:visible !important;
      position: relative !important;
        top: -6px !important;
    }
    #addAirlineList .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
    #addAirlineList .ng-select-container {
      border: none !important;
      &:after {
        display: none;
    }
  }
  #cardIssuelistDiv .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#cardIssuelistDiv .ng-dropdown-panel {
    top: auto !important;
    min-width: 353px !important;
    width: 100% !important;
      left: auto !important;
      background: #F7F7F9 !important;
      right: auto !important;
      bottom: auto !important;
    }
    #cardIssuelistDiv .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
        position: absolute !important;
        left: 0 !important;
        width: 100% !important;
        top: 2px !important;
    }
    #cardIssuelistDiv .ng-clear-wrapper{
      display: none !important;
    }
    #cardIssuelistDiv .ng-select span {
      box-sizing: border-box;
    }
    #cardIssuelistDiv .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;}
      #policylistDiv .ng-select.ng-select-container.ng-value-container.ng-input{
      position: fixed !important;
      top: -10px;
    }
    #cardIssuelistDiv .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
      overflow:hidden !important;
      position: relative !important;
        top: -4px !important;
        max-width: 310px !important;
    }
    #cardIssuelistDiv .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
    #cardIssuelistDiv .ng-select-container {
      border: none !important;
      &:after {
        display: none;
    }
  }
  #policylistDiv22 .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#policylistDiv22 .ng-dropdown-panel {
    max-width: 353px !important;
    min-width: 353px !important;
      top: auto !important;
      left: 0px !important;
      background: #F7F7F9 !important;
      right: auto !important;
      bottom: auto !important;
    }
    #policylistDiv22 .ng-clear-wrapper{
      display: none !important;
    }
    #policylistDiv22 .ng-select span {
      box-sizing: border-box;
    }
    #policylistDiv22 .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;}
      #policylistDiv .ng-select.ng-select-container.ng-value-container.ng-input{
      position: fixed !important;
      top: -10px;
    }
    #policylistDiv22 .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
      overflow:visible !important;
      position: relative !important;
        top: -2px !important;
    }
    #policylistDiv22 .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
    #policylistDiv22 .ng-select-container {
      border: none !important;
      &:after {
        display: none;
    }
  }
  #selectApproverDiv .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#selectApproverDiv .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
    position: absolute !important;
    left: 0 !important;
    width: 100% !important;
    top: 5px !important;
}
#selectApproverDiv .ng-dropdown-panel {
    max-width: 353px !important;
    min-width: 353px !important;
    top: auto !important;
    left:auto !important;
    background: #F7F7F9 !important;
    right: auto !important;
    bottom: auto !important;
  }
  #selectApproverDiv .ng-value-container {
        margin-left: 0px;
    }
    
    #selectApproverDiv .ng-select span {
      box-sizing: border-box;
    }
   
    #selectApproverDiv .ng-dropdown-panel-items .ng-option {
        height: 24px !important;
        padding-top: 0px !important;
        font-size: 12px !important;
        padding-left:0px !important;
        padding: 0 5px;
        color: #413E3B !important;
        line-height: normal;
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-pack: start;
        justify-content: flex-start;
        -ms-flex-align: center;
        align-items: center;
        float: left;
        width: 100%;
    }
    #selectApproverDiv .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
        overflow:visible !important;
        position: relative !important;
        top: -10px !important;
    } 
    #selectApproverDiv .ng-select.ng-select-opened .ng-select-container{
        z-index: 10 !important;
    }
    #selectApproverDiv .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;}
      #selectApproverDiv .ng-select.ng-select-container.ng-value-container.ng-input{
      position: fixed !important;
      top: -10px;
    }
    #selectApproverDiv .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
      overflow:visible !important;
      position: relative !important;
        top: -6px !important;
    }
    #selectApproverDiv .ng-select .ng-select-container .ng-value-container .ng-input > input {
        caret-color: black !important;
    }
    #selectApproverDiv .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
    #selectApproverDiv .ng-select-container {
      border: none !important;
      &:after {
        display: none;
    }
  }

  #selectApproverDiv11 .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#selectApproverDiv11 .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
    position: absolute !important;
    left: 0 !important;
    width: 100% !important;
    top: 10px !important;
}
#selectApproverDiv11 .ng-dropdown-panel {
    max-width: 353px !important;
    min-width: 353px !important;
    top: auto !important;
    left: 0px !important;
    background: #F7F7F9 !important;
    right: auto !important;
    bottom: auto !important;
  }
  #selectApproverDiv11 .ng-select .ng-arrow-wrapper{
      display: none !important;
  }
  
  #selectApproverDiv11 .ng-dropdown-panel-items .ng-option {
    height:26px !important;
    padding-top: 0px !important;
    font-size: 12px !important;
    padding-left: 0px !important;
    padding: 0 17px;
    color: #413E3B !important;
    line-height: normal;
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -ms-flex-align: center;
    align-items: center;
    float: left;
    width: 100%;
}
#selectApproverDiv11 .ng-clear-wrapper{
    display: none !important;
}
#selectApproverDiv11 .ng-select span {
    box-sizing: border-box;
}
#selectApproverDiv11 .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    display: none !important;
}
#selectApproverDiv .ng-select.ng-select-container.ng-value-container.ng-input{
    position: fixed !important;
    top: 5px !important;
}
#selectApproverDiv11 .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
    overflow:visible !important;
    position: relative !important;
    top: -10px !important;
}
#selectApproverDiv11 .ng-select .ng-select-container .ng-value-container .ng-input > input {
    caret-color: black !important;
}
#selectApproverDiv11 .ng-dropdown-panel.ng-select-bottom {
    border: 1px solid var(--dark-bg-color) !important;
}
#selectApproverDiv11 .ng-select-container {
    border: none !important;
    &:after {
        display: none;
    }
}
#selectApproverDiv11 .ng-value-container {
    margin-left: 0px;
}

  #cardlistDiv .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#cardlistDiv2 .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#cardlistDiv3 .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#cardlistDiv22 .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
}
#switchForDeparment .switch-medium.checked small{
    height: 22px !important;
    width: 22px !important;
    margin-top: 1px !important;
   background-color: var(--hyperlink-color) !important;
   border: none !important;
   box-sizing: none !important;
   left:37px !important;
  }
  #switchForDeparment .switch.switch-medium > .switch-pane > span {
    line-height: 24px !important;
    color: #fff !important;
}
  #switchForDeparment .switch.switch-medium small {
    width: 30px;
    height: 30px;
    right: calc(100% - 20px);
  }
  #switchForDeparment .switch{
    height: 24px !important;
    margin-top: 5px !important;
    border:1px solid var(--dark-bg-color);
    width: 60px  !important;
    border-radius: 30px  !important;
    background-color: gray  !important;
  }
  #switchForDeparment .switch.switch-medium.checked{
    border:1px solid var(--dark-bg-color) !important;
  }
  #switchForDeparment .switch-medium small{
    height: 22px !important;
    width: 22px !important;
    box-sizing: border-box;
    margin-top: 1px !important;
     border: 1px solid var(--dark-bg-color);
  }
    #cardlistDiv .ng-clear-wrapper{
      display: none !important;
    }
    #cardlistDiv .ng-select span {
      box-sizing: border-box;
    }
    #cardlistDiv .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;}
      #cardlistDiv .ng-select.ng-select-container.ng-value-container.ng-input{
      position: fixed !important;
      top: -10px;
    }
    #cardlistDiv .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
      overflow:visible !important;
      position: relative !important;
        top: -10px !important;
    }
    #cardlistDiv .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
    #cardlistDiv .ng-select-container {
      border: none !important;
      &:after {
        display: none;
    }
  }
  #cardlistDiv2 .ng-clear-wrapper{
    display: none !important;
  }
  #cardlistDiv2 .ng-select span {
    box-sizing: border-box;
  }
  #cardlistDiv2 .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    display: none !important;}
    #cardlistDiv2.ng-select.ng-select-container.ng-value-container.ng-input{
    position: fixed !important;
    top: -10px;
  }
  #cardlistDiv2 .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
    overflow:visible !important;
    position: relative !important;
      top: -10px !important;
  }
  #cardlistDiv2 .ng-dropdown-panel.ng-select-bottom {
    border: 1px solid var(--dark-bg-color) !important;
  }
  #cardlistDiv2 .ng-select-container {
    border: none !important;
    &:after {
      display: none;
  }
}
#cardlistDiv3 .ng-clear-wrapper{
    display: none !important;
  }
  #cardlistDiv3 .ng-select span {
    box-sizing: border-box;
  }
  #cardlistDiv3 .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    display: none !important;}
    #cardlistDiv2.ng-select.ng-select-container.ng-value-container.ng-input{
    position: fixed !important;
    top: -10px;
  }
  #cardlistDiv3 .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
    overflow:visible !important;
    position: relative !important;
      top: -10px !important;
  }
  #cardlistDiv3 .ng-dropdown-panel.ng-select-bottom {
    border: 1px solid var(--dark-bg-color) !important;
  }
  #cardlistDiv3 .ng-select-container {
    border: none !important;
    &:after {
      display: none;
  }
}
  
  #policylistDiv11 .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    display: none !important;}
  #policylistDiv11 .ng-select-container {
    border: none !important;
    &:after {
      display: none;
  }
}
#policylistDiv11 .ng-dropdown-panel.ng-select-bottom {
    border: 1px solid var(--dark-bg-color) !important;
}
  #policylistDiv11 .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
    overflow:visible !important;
    position: relative !important;
      top: 5px !important;
  }
  #policylistDiv11 .ng-dropdown-panel{
      top: 60px !important;
  }
  #policylistDiv11 .ng-dropdown-panel {
    box-sizing: border-box;
    max-width: 270px !important;
    min-width: auto !important;
    width: 100% !important;
    position: absolute;
    opacity: 0;
   // width: 300px !important;
    background: #F7F7F9 !important;
    top: 59px !important;
    right: auto !important; 
    z-index: 1050;
   // left: auto !important;
}
  #cardlistDiv .ng-dropdown-panel {
    box-sizing: border-box;
    max-width: 100% !important;
    min-width: 100% !important;
    position: absolute;
    opacity: 0;
   // width: 300px !important;
    background: #F7F7F9 !important;
    top: auto !important;
    right: auto !important; 
    z-index: 1050;
    left: auto !important;
}
#cardlistDiv2 .ng-dropdown-panel {
    box-sizing: border-box;
    max-width: 100% !important;
    min-width: 100% !important;
    position: absolute;
    opacity: 0;
   // width: 300px !important;
    background: #F7F7F9 !important;
    top: auto !important;
    right: auto !important; 
    z-index: 1050;
    left: auto !important;
}
#cardlistDiv3 .ng-dropdown-panel {
    box-sizing: border-box;
    max-width: 100% !important;
    min-width: 100% !important;
    position: absolute;
    opacity: 0;
    background: #F7F7F9 !important;
    top: auto !important;
    right: auto !important; 
    z-index: 1050;
    left: auto !important;
}
.formControl99{
#expennsify .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
    overflow:visible !important;
    position: relative !important;
      top: 0px !important;
  }
}
#cardlistDiv22 .ng-clear-wrapper{
    display: none !important;
  }
  #cardlistDiv22 .ng-select span {
    box-sizing: border-box;
  }
  #cardlistDiv22 .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    display: none !important;}
    #cardlistDiv22 .ng-select.ng-select-container.ng-value-container.ng-input{
    position: fixed !important;
    top: -10px;
  }
  #cardlistDiv22 .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
    overflow:visible !important;
    position: relative !important;
      top: -4px !important;
  }
  #cardlistDiv22 .ng-dropdown-panel.ng-select-bottom {
    border: 1px solid var(--dark-bg-color) !important;
  }
  #cardlistDiv22 .ng-select-container {
    border: none !important;
    &:after {
      display: none;
  }
}
#managerUpdateInAddEditemployee .ng-select .ng-select-container {
    color: #333;
    cursor: default;
    display: flex;
    outline: 0;
    overflow: hidden;
    position: relative;
    width: 100%;
    bottom: 8px !important;
  }
#alternatePolicylistDiv .ng-dropdown-panel{
    box-sizing: border-box;
    position: absolute;
    opacity: 0;
    width: 300px !important;
    top: 100px !important;
    /* right: 30px !important; */
    z-index: 1050;
    left: 180px !important;
}
#policylistDiv1 .ng-dropdown-panel{
    box-sizing: border-box;
    position: absolute;
    opacity: 0;
    width: 300px !important;
    top: 70px !important;
    /* right: 30px !important; */
    z-index: 1050;
    left: 180px !important;
}
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked,
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked.ng-option.ng-option-selected {
    background: $accent-color;
    color: #fff;
}
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected {
    background: transparent;
    color: $secondary-text-color;
}
#carOptions .ng-dropdown-panel.ng-select-bottom {
    border: 1px solid var(--dark-bg-color) !important;
  }
#carOptions .ng-dropdown-panel {
    box-sizing: border-box;
    max-width: 170px !important;
    min-width: 170px !important;
    position: absolute;
    opacity: 0;
    width: 300px !important;
    background: #F7F7F9 !important;
    top: 60px !important;
    /* right: 30px !important; */
    z-index: 1050;
    left: 8px !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items  {
    // 300px !important; // TODO: Fix this
     max-height: calc((100vh - 300px)/2) !important
}

.controls {
    .ng-dropdown-panel {
        margin-left: -50%;
    }
}
#carOptions .ng-select-container {
    border: none !important;
    &:after {
      display: none !important;
  }
  }
  #typeEmployee .ng-dropdown-panel .scroll-host {
   // overflow: scroll !important;
   // overflow-y: auto !important;
    position: relative;
    display: inline-block;
    width: fit-content;
}
#typeEmployee  .ng-dropdown-panel .ng-dropdown-panel-items {
    height: auto;
  //  overflow: scroll !important;
    box-sizing: border-box;
    max-height: 240px;
}
#carOptions .ng-select .ng-select-container .ng-value-container {
    //@at-root   -ms-flex-align: none !important;
      // align-items: none !important;
     padding-left: .0075em 0 !important;
     padding-right: .0075em 0 !important;
     padding-bottom: .0075em 0 !important;
     padding-top: 13px !important;
      // cursor: pointer !important;
      border-top: .84375em solid transparent !important;
      .ng-select.ng-select-single .ng-select-container .ng-value-container, .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
        overflow:visible !important;
        position: relative !important;
          top: 0px !important;
      }
   //border-top: .375em solid transparent !important;
     }
     #carOptions .ng-placeholder{
      margin-top:23px !important;
      font-size:25px !important;
      color: $secondary-text-color !important;;
    }
    #carOptions .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
        -ms-flex-item-align: end;
        align-self: flex-end;
        bottom: 19px;
        display: none !important;
    }
      
.field-input {
    .ng-select .ng-has-value .ng-placeholder,
    .ng-select.ng-select-focused .ng-select-container .ng-value-container .ng-placeholder {
        transform: none;
        color: inherit;
    }

    .ng-select .ng-has-value .ng-placeholder,
    .ng-select.ng-select-opened .ng-placeholder {
        display: none;
    }

    .ng-select.ng-select-focused .ng-select-container .ng-arrow-wrapper .ng-arrow {
        color: inherit;
    }
    #title-passenger{
        position: relative;
        border-radius: 6px;
        background: #f7f7f7;
    }
    #title-passenger .ng-arrow-wrapper .ng-arrow{
          border-top:0px;
    }
    #title-passenger .ng-arrow-wrapper{
         padding-right: 12px;
        & :after {
        content: ' \02C7';
        color: $link-color;
        font-size: 31px;
        font-weight: bold;
        position: absolute;
         top: -15px;
        }
    }
   
    .ng-select {
        padding: 0;

        .ng-select-container {
          border: 1px solid $border-light-color;
          background: #f7f7f7 !important;
          min-height: 65px !important;
          border: 2px solid $border-light-color;
          border-radius: 6px;
          font-family: ApercuProMono;
          padding-right:8px;
            &:after {
                display: none;
            }

            .ng-clear-wrapper {
                bottom: 18px;
            }

            .ng-arrow-wrapper {
                bottom: 20px;
            }

        }

        .ng-value-container {
            border-top: none;
            padding: 23px 5px 12px 16px;

            .ng-input {
                padding: 16px 5px 12px 16px;
                bottom: 0;
            }

            .ng-placeholder {
                color: $secondary-text-color;
            }
        }
    }
}
.field-input{
    .ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{
      // color: #413E3B !important;
        background: #f7f7f7 !important;
    }
     .ng-value-label.ng-star-inserted{
       // color: #413E3B !important;
        background: #f7f7f7 !important;
    }
}
.no-gutters {
    margin-right: 0;
    margin-left: 0;
    > .col,
    > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
    }
}

.slider-track {
    height: 100%;
    position: absolute;
    padding: 5px 0;
    top: 0;
    left: 100px;
    right: 100px;
    overflow: hidden;
}

.left-slider,
.right-slider {
    position: absolute;
    height: 100%;
    border-left: 2px dashed $border-dark-color;
    top: 0;
    margin: 0 3px;
}

// slider override

.noUi-target {
    background: $border-dark-color;
    border: none;
    box-shadow: none;
    border-radius: none;
}

.noUi-horizontal {
    height: 5px;
}

.noUi-horizontal .noUi-handle {
    top: -12px;
    width: 36px;
    cursor: pointer;
    box-shadow: none;
    border: none;
    border-radius: 0;
    outline: none;
}

.noUi-handle:before {
    content: "";
    display: block;
    position: absolute;
    height: 34px;
    width: 2px;
}

.footerimage {
  height: 30px;
  width: 30px;
  display: inline-block;
  margin-top: 0px;
  text-align: center;
  align-items: center;
}
.noUi-handle:after {
    font-family: 'icomoon';
    height: auto;
    width: auto;
    background: none;
    top: -2px;
    left: 7px;
    color: #fff;
    font-size: 22px;
}

.noUi-handle-lower {
    background: $primary-color;
}

.noUi-handle-lower.noUi-handle:before {
    background: $primary-color;
    bottom: 0;
    left: 0;
    top: auto;
}

.noUi-handle-lower.noUi-handle:after {
    content: "\e905";
}

.noUi-handle-upper {
    background: $accent-color;
}

.noUi-handle-upper.noUi-handle:before {
    background: $accent-color;
    top: 0;
    right: 0;
    left: auto;
}

.noUi-handle-upper.noUi-handle:after {
    content: "\e904";
}

.noUi-connect {
    background: linear-gradient(51.48deg, var(--dark-bg-color) 0%, #862EF6 10.56%, #7940F3 25.41%, #655EEE 42.81%, #4888E8 52.15%, #25BDE0 62.83%, var(--button-bg-color) 100%);;
}

// accordion

.card {
    border-radius: 0;
    border: none;
  }

  .card-header  {
    padding: 16px;
    border: none;
    background: #fff;
    cursor: pointer;
  }

  .card-body {
    padding: 16px;
    border: none;
    background: #fff;
  }
  .panel.disabled {
    .card-header {
        cursor: default;
    }
  }
  @media(max-width:1300px){
    #policylistDiv11 .ng-dropdown-panel {
        box-sizing: border-box;
        max-width: 270px !important;
        min-width: auto !important;
        position: absolute;
        opacity: 0;
        left: 10px !important;
       // width: 300px !important;
        background: #F7F7F9 !important;
        top: 59px !important;
        right: auto !important; 
        z-index: 1050;
       // left: auto !important;
    }
  }
// For Mobile
@media(max-width:991px){
    #constraint-time1 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: -30% !important; right: auto !important; bottom: auto !important;}
}
@media(max-width:1024px) and (min-width:1024px){
  
    .custom-selectbox {cursor: pointer;position: relative; display: inline-block; padding-right: 15px;margin-right:55px;}
}
@media(max-width:991px){
    #constraint-time1 .ng-dropdown-panel{ min-width: 365px !important;transform: translate(-50%, 0); top: 28px !important; left: -30% !important; right: auto !important; bottom: auto !important;}
}
@media(max-width:768px){
    #constraint-time .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
    #constraint-time1 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
    #typeEmployee .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{
        overflow:visible !important;
        position: relative !important;
        top: -6px !important;
        color: gray !important;
    }
    #constraint-time-leg0 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg1 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg2 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg3 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg4 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
    .date-input {
        margin: 0 10px 0 5px;
        position: relative;
    
        .field-value {
            cursor: pointer;
        }
    
        input {
            position: absolute;
            z-index: -1;
            top: 13px;
            width: 100%;
        }
    }
    #addcardList .ng-dropdown-panel {
        left: 51% !important;
        transform: translate(-50%, 0);
        min-width: 96% !important;
        max-width: 96% !important;
          top: auto !important;
        
          background: #F7F7F9 !important;
          right: auto !important;
          bottom: auto !important;
        }
    #addDepartmentList .ng-dropdown-panel {
        top: auto !important;
      
      
          background: #F7F7F9 !important;
          right: auto !important;
          bottom: auto !important;
          left: 50% !important;
          transform: translate(-50%, 0);
          min-width: 95% !important;
          max-width: 95% !important;
        }
    #addAirlineList .ng-dropdown-panel {
        top: auto !important;
      
          background: #F7F7F9 !important;
          right: auto !important;
          bottom: auto !important;
          left: 50% !important;
          transform: translate(-50%, 0);
          min-width: 95% !important;
          max-width: 95% !important;
        }
    #typeEmployee .ng-dropdown-panel {
        min-width: 300px !important;
        top: 42px !important;
        position: absolute !important;
        left: auto !important;
        background: #F7F7F9 !important;
        right: auto !important;
        -ms-transform: translate(0%);
        transform: translate(-2%);
        bottom: auto !important;
        overflow: scroll !important;
    }
    #typeEmployee .ng-select{
        padding-left :30px !important;
        font-size: 14px;
        }
}
@media (max-width: 767.98px) {
  .row{
    width: 100%;
    }
    #airlineFlyerNumberDiv input{
        caret-color: transparent;
    }
    #constraint-time .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
#constraint-time1 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg0 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg1 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg2 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg3 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
#constraint-time-leg4 .ng-dropdown-panel{ min-width: 365px !important; top: 28px !important; left: 50% !important; right: auto !important; bottom: auto !important;}
    #typeEmployee .ng-dropdown-panel {
        top: 42px !important;
        left: 50% !important;
        transform: translate(-50%, 0);
        max-height: 300px;
        position: absolute !important;
        min-width: 100% !important;
        right: auto !important;
        overflow: scroll !important;
    }
    bs-datepicker-container {
        position: fixed !important;
        display: block;
        top: calc((100% - 1025px)/2) !important;
        left: calc((100% - 240px)/2) !important;
        margin: -1000px;
        padding: 1000px;
        background-color: rgba(0, 0, 0, 0.3);
        transform: translate3d(-51px, 333px, 0px) !important;
    }
    bs-datepicker-navigation-view{
        display: inline-flex !important;
    }
    .bs-datepicker {
        margin-bottom: 0px;
    }
}
@media (max-width: 575.98px) {
    #airlineFlyerNumberDiv input{
        caret-color: transparent;
    }
    .field-input1{

     #typeEmployee .ng-dropdown-panel {
        min-width: 305px !important;
        top: 220px !important;
         left: 50% !important; 
        right: auto !important;
        bottom: auto !important;
    }
}
    html,
    body {
        font-size: 12px;
    }

    .main-wrapper {
        padding: 0px 0;
    }

    .text-input {

        .icon {
            top:  13px;
            left:  14px;
        }

        input {
            padding: 14px;

        }

        .icon + input {
            padding-left: 45px;
        }
    }

    .header {
        margin-left: 14px;
    }
}

.mdl-checkbox__box-outline{border: 1px solid #979797;width: 17px;height: 17px; border-radius: 0;}
.mdl-checkbox.is-checked .mdl-checkbox__box-outline{border: 1px solid $themeColor;}
.mdl-checkbox.is-checked .mdl-checkbox__tick-outline{ background-color: $themeColor; }

.inline-checkbox.mdl-checkbox{float: none; display: inline-block; padding-left: 0; padding-right: 24px;}
.inline-checkbox .mdl-checkbox__label{color: var(--dark-bg-color);
                                      font-size: 12px;
                                      letter-spacing: 1px;
                                      line-height: 18px;
                                      font-family: $fontBold;
                                      text-transform: uppercase;}

.inline-checkbox .mdl-checkbox__box-outline{ right: 0; left: auto; bottom: 1px; top: auto;}


@import "@ng-select/ng-select/themes/material.theme.css";
//@import "bootstrap/scss/bootstrap.scss";
//@import "assets/js/library/bootstrap/bootstrap.min.css";
@import "assets/js/library/select2/select2.min.css";
@import "assets/js/library/material/material.css";
@import "bootstrap/dist/css/bootstrap.min.css";
@import "assets/js/library/jquery-ui/jquery-ui.css";
@import "ngx-bootstrap/datepicker/bs-datepicker.css";
@import "nouislider/distribute/nouislider.min.css";
@import "font-awesome/css/font-awesome.css";
//@import 'ngx-toastr/toastr-bs4-alert';
//@import "assets/scss/component.scss";
@import "assets/scss/common.scss";
@import "assets/scss/style.scss";
@import "assets/scss/responsive.scss";
@import "assets/scss/user-profile/common.scss";
@import "assets/scss/user-profile/component.scss";
@import "assets/scss/user-profile/ios.scss";
@import "assets/scss/user-profile/style.scss";
@import "assets/scss/user-profile/responsive.scss";






