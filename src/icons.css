@font-face {
  font-family: 'icomoon';
  src:  url('assets/icons/icomoon.eot?7l3lf3');
  src:  url('assets/icons/icomoon.eot?7l3lf3#iefix') format('embedded-opentype'),
    url('assets/icons/icomoon.ttf?7l3lf3') format('truetype'),
    url('assets/icons/icomoon.woff?7l3lf3') format('woff'),
    url('assets/icons/icomoon.svg?7l3lf3#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-triangle:before {
  content: "\e917";
}
.icon-checkmark:before {
  content: "\e914";
}
.icon-calendar:before {
  content: "\e90c";
}
.icon-credit-card:before {
  content: "\e90d";
}
.icon-cross:before {
  content: "\e90e";
}
.icon-cross-bold:before {
  content: "\e90f";
}
.icon-customer:before {
  content: "\e910";
}
.icon-fare-changed:before {
  content: "\e911";
}
.icon-lock:before {
  content: "\e912";
}
.icon-no-flight:before {
  content: "\e913";
}
.icon-warning .path1:before {
  content: "\e915";
  color: rgb(245, 166, 35);
}
.icon-warning .path2:before {
  content: "\e916";
  margin-left: -1.05859375em;
  color: rgb(255, 255, 255);
}
.icon-setting:before {
  content: "\e909";
}
.icon-filter:before {
  content: "\e90a";
}
.icon-edit:before {
  content: "\e90b";
}
.icon-search-destination:before {
  content: "\e900";
}
.icon-arrow:before {
  content: "\e902";
}
.icon-down:before {
  content: "\e903";
}
.icon-plan-down:before {
  content: "\e904";
}
.icon-plan-up:before {
  content: "\e905";
}
.icon-plus:before {
  content: "\e906";
}
.icon-plane:before {
  content: "\e907";
}
.icon-up:before {
  content: "\e908";
}
.icon-search-from:before {
  content: "\e901";
}
