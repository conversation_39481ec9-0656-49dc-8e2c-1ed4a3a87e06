<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Routespring</title>
  <base href="./">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
  <meta http-equiv="cache-control" content="no-cache">
  <meta http-equiv="expires" content="0">
  <meta http-equiv="pragma" content="no-cache">

  <link rel="icon" type="image/x-icon" href="favicon.ico?v1">
  <link rel="stylesheet" type="text/css" href="assets/css/loading-bar.css" />
  <link href="assets/css/toggle-switch.css" rel="stylesheet" type="text/css" />
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA3lywR0fqq2uX6zwfiHpoRxah_zETrSPc&libraries=places&sensor=false&language=en&callback=Function.prototype" async defer></script>
  <script type="text/javascript" src="assets/js/loading-bar.min.js" ></script>
  <script src="https://cdn.plaid.com/link/v2/stable/link-initialize.js"></script>
  <script type="text/javascript" src="assets/js/config.js?v=_BUILD_VERSION_"></script>
  <script type="text/javascript" src="https://secure.rezserver.com/sdk/v1/8787/client.js" async defer></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
  <link href="assets/webdatarocks/webdatarocks.min.css" rel="stylesheet" />
  <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css">
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
  <script>
    function changeFavicon(url){
    let link = document.querySelector("link[rel*='icon']") || document.createElement('link');
    link.type = 'image/x-icon';
    link.rel = 'shortcut icon';
    link.href = url;
    document.head.appendChild(link);
  }
  
    function applyStyles(resp){
      document.documentElement.style.setProperty('--dark-bg-color', `#${resp.data.backGroundColor}`);
    document.documentElement.style.setProperty('--button-bg-color', `#${resp.data.primaryButtonColor}`);
    document.documentElement.style.setProperty('--genderSelected', `#${resp.data.primaryButtonColor}`);
    document.documentElement.style.setProperty('--button-font-color', `#${resp.data.primaryButtonTextColor}`);
    document.documentElement.style.setProperty('--secondarybutton-bg-color', `#${resp.data.secondaryButtonColor}`);
    document.documentElement.style.setProperty('--secondarybutton-font-color', `#${resp.data.secondaryButtonTextColor}`);
    document.documentElement.style.setProperty('--hyperlink-color', `#${resp.data.hyperLinkTextColor}`);
    document.documentElement.style.setProperty('--light-bg-color', `#${resp.data.primaryBrandColor}`);
    document.documentElement.style.setProperty('--primarydashboard-bg-color', `#${resp.data.primaryBrandColor}`);
    document.documentElement.style.setProperty('--background-image-url', `url(${resp.data.companyLogo})`);
    document.documentElement.style.setProperty('--background-image-url2', `url(${resp.data.companyLogoForDashboard})`);
    document.documentElement.style.setProperty('--background-image-url3', `url(${resp.data.footerLogo})`);
    changeFavicon(resp.data.favIcon);
   
    }
     let whitelLabelSetting = JSON.parse(localStorage.getItem('whiteLabelSetting'));
    if(whitelLabelSetting && whitelLabelSetting!=='null'){
     applyStyles(whitelLabelSetting);
    }else{
      let footerLogo = '/assets/images/footer-logo.png';
    window.addEventListener('DOMContentLoaded', function () {
    
  });
  window.onerror = function(message, source, lineno, colno, error) {
  console.error("Global Error:", message, source, lineno, colno, error);
};
      let darkBgColor = '8A27F7';
       let buttonbgcolor = '03efd8';
        let secondaryButtonbgcolor = 'F5F5FA';
    let secondaryButtonfontcolor ='808080';
      let hyperlinkcolor = '315EF6';
   let    lightBgColor  = 'EDFAFC';
  let buttonfontcolor = '315EF6';
 if(window.location.pathname==='/admin'){
    lightBgColor = 'F5F5FA';
    buttonfontcolor = 'fff';
 }else{
  lightBgColor = 'EDFAFC';
  buttonfontcolor = '315EF6';
 }
 let lightBgColor2 = 'F7F7F9';
  let genderButton='ecfffd';
   let favIcon = '/assets/images/footer-logo.png';
    let logo = '/assets/images/logo.png';

   
   let logo2 = '/assets/images/logo.webp';
      document.documentElement.style.setProperty('--dark-bg-color', `#${darkBgColor}`);
    document.documentElement.style.setProperty('--button-bg-color', `#${buttonbgcolor}`);
    document.documentElement.style.setProperty('--genderSelected', `#${buttonbgcolor}`);
    document.documentElement.style.setProperty('--button-font-color', `#${buttonfontcolor}`);
    document.documentElement.style.setProperty('--secondarybutton-bg-color', `#${secondaryButtonbgcolor}`);
    document.documentElement.style.setProperty('--secondarybutton-font-color', `#${secondaryButtonfontcolor}`);
    document.documentElement.style.setProperty('--hyperlink-color', `#${hyperlinkcolor}`);
    document.documentElement.style.setProperty('--light-bg-color', `#${lightBgColor}`);
    document.documentElement.style.setProperty('--primarydashboard-bg-color', `#${lightBgColor}`);
    document.documentElement.style.setProperty('--background-image-url', `url(${logo})`);
    document.documentElement.style.setProperty('--background-image-url2', `url(${logo2})`);
    document.documentElement.style.setProperty('--background-image-url3', `url(${footerLogo})`);
    changeFavicon(favIcon);
    }
  </script>

  <script type="text/javascript">
  var $zoho=$zoho || {};$zoho.salesiq = $zoho.salesiq || {widgetcode:"siq59809fe88cca1ac86305a467f8ccdc7dcddd989e2ff97ccb40e1153fab4f4c4d", values:{},ready:function(){}};
  var d=document;s=d.createElement("script");
  s.type="text/javascript";
  s.id="zsiqscript";
  s.style='display:none;';
  s.defer=true;s.src="/assets/js/zoho_siq_widget.js";
  t=d.getElementsByTagName("script")[0];
  t.parentNode.insertBefore(s,t);
  d.write("<div id='zsiqwidget' ></div>");
    var isOffline = false;
    var showRequested = false;
    function setZSIQNameAndMail_CBK(name, email) {
      if ($zoho.salesiq && $zoho.salesiq.visitor) {
        $zoho.salesiq.visitor.name(name); 
        $zoho.salesiq.visitor.email(email); 
      }
    }
    function showZSIQEWidget(){
      showRequested = true;
      if (isOnline && $zoho.salesiq){
        $zoho.salesiq.floatbutton.visible("show");
      }
    }
    function hideZSIQEWidget(){
      showRequested = false;
      if ($zoho.salesiq) {
        $zoho.salesiq.floatwindow.visible('hide')
        $zoho.salesiq.floatbutton.visible("hide");
      }
    }
    function setUPSalesIQReadyCBK(){
      if ($zoho.salesiq) {
        $zoho.salesiq.ready=initializeZSIQ;
      } else {
        setTimeout(() => {
          setUPSalesIQReadyCBK();
        }, 2000);
      }
    }
    function initializeZSIQ(){
      $zoho.salesiq.chat.online(cbk_online);
      $zoho.salesiq.chat.offline(cbk_offline)
      hideZSIQEWidget();
    }
    function cbk_online(){
      isOffline = false;
      if (showRequested) {
        showZSIQEWidget();
      }
    }
    function cbk_offline(){
      isOffline = true; hideZSIQEWidget();
    }
    function openZSIQChatWidow(){
      if ($zoho.salesiq && $zoho.salesiq.floatwindow) {
        $("div[data-id|='zsiqembed']").attr("style", "width: 100% !important;max-width: 460px !important");
        $zoho.salesiq.floatwindow.visible("show");
      }
    }
    function closeZSIQChatWidow(){

    }
    function getZohoSIQChatOfflineStatus(){
      return isOffline;
    }
    setUPSalesIQReadyCBK();

  </script>
  <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-TXZMJMZ');</script>
  <!-- End Google Tag Manager -->
  <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() { dataLayer.push(arguments); }
      function gTAGCallback(){
          gtag('js', new Date());
          gtag('config', 'G-H1T00GNSKD', {'send_page_view': false});
          gtag('get', 'G-H1T00GNSKD', 'client_id', (client_id) => {setTimeout(() => {setGAClientID(client_id); }, 1000);});

      }
      var ga_client_id;
      function setGAClientID(clientID){
          ga_client_id = clientID;
          gtag('set', 'user_properties', {'client_id': '' + ga_client_id });
      }
      function getGAClientID(){
          return ga_client_id;
      }
      function setCDValues(userId, company){ 
        gtag('set','user_properties', {'company_id': company });
        gtag('set','user_properties', {'login_id': userId });
        gtag('config', 'G-H1T00GNSKD', {'user_id': userId });
      }
      // Attach onload dynamically to avoid early call
      function loadGtagScript() {
          const script = document.createElement('script');
          script.src = "https://www.googletagmanager.com/gtag/js?id=G-H1T00GNSKD";
          script.async = true;
          script.onload = gTAGCallback;
          document.head.appendChild(script);
      }
        // Call loader
      loadGtagScript();

  </script>

  <script>
    var _rollbarConfig = {
      accessToken: "0f2131c4446f4a81b9b2deb71a8a783c",
      captureUncaught: true,
      captureUnhandledRejections: true,
      payload: {
        environment: "production"
      }
    };
    function setUPRollBar(postClientAccessToken, env) {
      _rollbarConfig.accessToken = postClientAccessToken;
      _rollbarConfig.payload.environment = env;
      !function (r) { var e = {}; function o(n) { if (e[n]) return e[n].exports; var t = e[n] = { i: n, l: !1, exports: {} }; return r[n].call(t.exports, t, t.exports, o), t.l = !0, t.exports } o.m = r, o.c = e, o.d = function (r, e, n) { o.o(r, e) || Object.defineProperty(r, e, { enumerable: !0, get: n }) }, o.r = function (r) { "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(r, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(r, "__esModule", { value: !0 }) }, o.t = function (r, e) { if (1 & e && (r = o(r)), 8 & e) return r; if (4 & e && "object" == typeof r && r && r.__esModule) return r; var n = Object.create(null); if (o.r(n), Object.defineProperty(n, "default", { enumerable: !0, value: r }), 2 & e && "string" != typeof r) for (var t in r) o.d(n, t, function (e) { return r[e] }.bind(null, t)); return n }, o.n = function (r) { var e = r && r.__esModule ? function () { return r.default } : function () { return r }; return o.d(e, "a", e), e }, o.o = function (r, e) { return Object.prototype.hasOwnProperty.call(r, e) }, o.p = "", o(o.s = 0) }([function (r, e, o) { "use strict"; var n = o(1), t = o(5); _rollbarConfig = _rollbarConfig || {}, _rollbarConfig.rollbarJsUrl = _rollbarConfig.rollbarJsUrl || "https://cdn.rollbar.com/rollbarjs/refs/tags/v2.26.4/rollbar.min.js", _rollbarConfig.async = void 0 === _rollbarConfig.async || _rollbarConfig.async; var a = n.setupShim(window, _rollbarConfig), l = t(_rollbarConfig); window.rollbar = n.Rollbar, a.loadFull(window, document, !_rollbarConfig.async, _rollbarConfig, l) }, function (r, e, o) { "use strict"; var n = o(2), t = o(3); function a(r) { return function () { try { return r.apply(this, arguments) } catch (r) { try { console.error("[Rollbar]: Internal error", r) } catch (r) { } } } } var l = 0; function i(r, e) { this.options = r, this._rollbarOldOnError = null; var o = l++; this.shimId = function () { return o }, "undefined" != typeof window && window._rollbarShims && (window._rollbarShims[o] = { handler: e, messages: [] }) } var s = o(4), d = function (r, e) { return new i(r, e) }, c = function (r) { return new s(d, r) }; function u(r) { return a((function () { var e = this, o = Array.prototype.slice.call(arguments, 0), n = { shim: e, method: r, args: o, ts: new Date }; window._rollbarShims[this.shimId()].messages.push(n) })) } i.prototype.loadFull = function (r, e, o, n, t) { var l = !1, i = e.createElement("script"), s = e.getElementsByTagName("script")[0], d = s.parentNode; i.crossOrigin = "", i.src = n.rollbarJsUrl, o || (i.async = !0), i.onload = i.onreadystatechange = a((function () { if (!(l || this.readyState && "loaded" !== this.readyState && "complete" !== this.readyState)) { i.onload = i.onreadystatechange = null; try { d.removeChild(i) } catch (r) { } l = !0, function () { var e; if (void 0 === r._rollbarDidLoad) { e = new Error("rollbar.js did not load"); for (var o, n, a, l, i = 0; o = r._rollbarShims[i++];)for (o = o.messages || []; n = o.shift();)for (a = n.args || [], i = 0; i < a.length; ++i)if ("function" == typeof (l = a[i])) { l(e); break } } "function" == typeof t && t(e) }() } })), d.insertBefore(i, s) }, i.prototype.wrap = function (r, e, o) { try { var n; if (n = "function" == typeof e ? e : function () { return e || {} }, "function" != typeof r) return r; if (r._isWrap) return r; if (!r._rollbar_wrapped && (r._rollbar_wrapped = function () { o && "function" == typeof o && o.apply(this, arguments); try { return r.apply(this, arguments) } catch (o) { var e = o; throw e && ("string" == typeof e && (e = new String(e)), e._rollbarContext = n() || {}, e._rollbarContext._wrappedSource = r.toString(), window._rollbarWrappedError = e), e } }, r._rollbar_wrapped._isWrap = !0, r.hasOwnProperty)) for (var t in r) r.hasOwnProperty(t) && (r._rollbar_wrapped[t] = r[t]); return r._rollbar_wrapped } catch (e) { return r } }; for (var p = "log,debug,info,warn,warning,error,critical,global,configure,handleUncaughtException,handleAnonymousErrors,handleUnhandledRejection,captureEvent,captureDomContentLoaded,captureLoad".split(","), f = 0; f < p.length; ++f)i.prototype[p[f]] = u(p[f]); r.exports = { setupShim: function (r, e) { if (r) { var o = e.globalAlias || "Rollbar"; if ("object" == typeof r[o]) return r[o]; r._rollbarShims = {}, r._rollbarWrappedError = null; var l = new c(e); return a((function () { e.captureUncaught && (l._rollbarOldOnError = r.onerror, n.captureUncaughtExceptions(r, l, !0), e.wrapGlobalEventHandlers && t(r, l, !0)), e.captureUnhandledRejections && n.captureUnhandledRejections(r, l, !0); var a = e.autoInstrument; return !1 !== e.enabled && (void 0 === a || !0 === a || "object" == typeof a && a.network) && r.addEventListener && (r.addEventListener("load", l.captureLoad.bind(l)), r.addEventListener("DOMContentLoaded", l.captureDomContentLoaded.bind(l))), r[o] = l, l }))() } }, Rollbar: c } }, function (r, e, o) { "use strict"; function n(r, e, o, n) { r._rollbarWrappedError && (n[4] || (n[4] = r._rollbarWrappedError), n[5] || (n[5] = r._rollbarWrappedError._rollbarContext), r._rollbarWrappedError = null); var t = e.handleUncaughtException.apply(e, n); o && o.apply(r, n), "anonymous" === t && (e.anonymousErrorsPending += 1) } r.exports = { captureUncaughtExceptions: function (r, e, o) { if (r) { var t; if ("function" == typeof e._rollbarOldOnError) t = e._rollbarOldOnError; else if (r.onerror) { for (t = r.onerror; t._rollbarOldOnError;)t = t._rollbarOldOnError; e._rollbarOldOnError = t } e.handleAnonymousErrors(); var a = function () { var o = Array.prototype.slice.call(arguments, 0); n(r, e, t, o) }; o && (a._rollbarOldOnError = t), r.onerror = a } }, captureUnhandledRejections: function (r, e, o) { if (r) { "function" == typeof r._rollbarURH && r._rollbarURH.belongsToShim && r.removeEventListener("unhandledrejection", r._rollbarURH); var n = function (r) { var o, n, t; try { o = r.reason } catch (r) { o = void 0 } try { n = r.promise } catch (r) { n = "[unhandledrejection] error getting `promise` from event" } try { t = r.detail, !o && t && (o = t.reason, n = t.promise) } catch (r) { } o || (o = "[unhandledrejection] error getting `reason` from event"), e && e.handleUnhandledRejection && e.handleUnhandledRejection(o, n) }; n.belongsToShim = o, r._rollbarURH = n, r.addEventListener("unhandledrejection", n) } } } }, function (r, e, o) { "use strict"; function n(r, e, o) { if (e.hasOwnProperty && e.hasOwnProperty("addEventListener")) { for (var n = e.addEventListener; n._rollbarOldAdd && n.belongsToShim;)n = n._rollbarOldAdd; var t = function (e, o, t) { n.call(this, e, r.wrap(o), t) }; t._rollbarOldAdd = n, t.belongsToShim = o, e.addEventListener = t; for (var a = e.removeEventListener; a._rollbarOldRemove && a.belongsToShim;)a = a._rollbarOldRemove; var l = function (r, e, o) { a.call(this, r, e && e._rollbar_wrapped || e, o) }; l._rollbarOldRemove = a, l.belongsToShim = o, e.removeEventListener = l } } r.exports = function (r, e, o) { if (r) { var t, a, l = "EventTarget,Window,Node,ApplicationCache,AudioTrackList,ChannelMergerNode,CryptoOperation,EventSource,FileReader,HTMLUnknownElement,IDBDatabase,IDBRequest,IDBTransaction,KeyOperation,MediaController,MessagePort,ModalWindow,Notification,SVGElementInstance,Screen,TextTrack,TextTrackCue,TextTrackList,WebSocket,WebSocketWorker,Worker,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload".split(","); for (t = 0; t < l.length; ++t)r[a = l[t]] && r[a].prototype && n(e, r[a].prototype, o) } } }, function (r, e, o) { "use strict"; function n(r, e) { this.impl = r(e, this), this.options = e, function (r) { for (var e = function (r) { return function () { var e = Array.prototype.slice.call(arguments, 0); if (this.impl[r]) return this.impl[r].apply(this.impl, e) } }, o = "log,debug,info,warn,warning,error,critical,global,configure,handleUncaughtException,handleAnonymousErrors,handleUnhandledRejection,_createItem,wrap,loadFull,shimId,captureEvent,captureDomContentLoaded,captureLoad".split(","), n = 0; n < o.length; n++)r[o[n]] = e(o[n]) }(n.prototype) } n.prototype._swapAndProcessMessages = function (r, e) { var o, n, t; for (this.impl = r(this.options); o = e.shift();)n = o.method, t = o.args, this[n] && "function" == typeof this[n] && ("captureDomContentLoaded" === n || "captureLoad" === n ? this[n].apply(this, [t[0], o.ts]) : this[n].apply(this, t)); return this }, r.exports = n }, function (r, e, o) { "use strict"; r.exports = function (r) { return function (e) { if (!e && !window._rollbarInitialized) { for (var o, n, t = (r = r || {}).globalAlias || "Rollbar", a = window.rollbar, l = function (r) { return new a(r) }, i = 0; o = window._rollbarShims[i++];)n || (n = o.handler), o.handler._swapAndProcessMessages(l, o.messages); window[t] = n, window._rollbarInitialized = !0 } } } }]);
    }
    function sendLogToRollBar(message){
      Rollbar.debug(message);
    }
    function sendCriticalErrorToRollBar(message){
      try { 
        Rollbar.critical(message);
      }catch (err) { 

      }
    }

    function configureRollBar(emailId, PARAM_APP_VERSION, PARAM_APPSTORE_VERSION,
      PARAM_APP_PLATFORM, PARAM_OTA_BUILD_NUMBER
    ){
      var userid = getGAClientID();
      Rollbar.configure({
        payload: {
          person: {
            id: userid,
            email: emailId
          }, 
          custom: {
            APP_VERSION: PARAM_APP_VERSION,
            APPSTORE_VERSION: PARAM_APPSTORE_VERSION,
            APP_PLATFORM: PARAM_APP_PLATFORM,
            OTA_BUILD_NUMBER: PARAM_OTA_BUILD_NUMBER
          }
        }
      });
    }
  </script>
  <script>

    (function () {
      var scriptElement = document.createElement('script');
      scriptElement.setAttribute('src', cc_ui_host + '/' + cc_ui_base_ctx + '/js/gallopPayment.js');
      scriptElement.setAttribute('async', 'true');
      scriptElement.setAttribute('defer', 'true');
      document.head.appendChild(scriptElement);
    })();

  </script>
</head>

<body style="
            display: inline-block; 
            background-color: var(--light-bg-color);
            overscroll-behavior:none;">
    <div style="position:fixed;height:100px;top:-100px;left:0;background-color:white;width: 100vw;z-index: 1000;" id="mobile-top-clipper"></div>
  <app-root>
    <div class="spinner">
      <div class="rect1"></div>
      <div class="rect2"></div>
      <div class="rect1"></div>
    </div>
    <div id="connectionError" style="display: none;height: 100vh;
    position: absolute;
    width: 100vw;
    background: rgba(0, 0, 0, 0.6);
    top: 0;
    left: 0;
    ">
      <div style="margin-top: 20px;background: #fff;
      box-sizing: border-box;
    border: 2px solid #fff;
    width: 320px;
    align-items: center;
    margin-left: auto;
    padding: 20px;
    margin-right: auto;
    border-radius: 8px;
    position: absolute;
    left: calc(( 100% - 320px)/2);
    top: calc((100% - 200px)/3);
    ">
        <div style="text-align: center;display: inline-block;padding-bottom: 10px;">
          <span style="width: 70px;height: 50px;
        background-image: url(assets/images/net_error.png);
        background-repeat: no-repeat;
        background-size: 50px;
        float:left;"></span>
          <span style=" font-size: 25px;
       font-weight: 600;
       position: relative;
       top: 5px;
       ">Network Error</span>
        </div>
        <div style="text-align: center;display: inline-block;">
          <div id="network-error-msg" style="margin-top:0px;
          font-size: 16px;
          margin-bottom:30px;
          letter-spacing: 1px;
          width: 100%;
          text-align: left;
          "> Modernizing Work-Crew Travel Management!
          </div>
          <div id="tryAgain" style="text-align: right;">
            <a style=" font-size:20px;
          color: var(--hyperlink-color) !important;
          cursor: pointer;
          ">Try Again</a>
          </div>
        </div>
      </div>
    </div>
    <script>
      var isAppLoaded = false;
      var isNetworkWorking = true;
      function setAppLoaded() {
        isAppLoaded = true;
      }
      // function testNetwork() {
      //   var xhttp = new XMLHttpRequest();
      //   xhttp.onreadystatechange = function () {
      //     if (this.readyState == 4 && (this.status == 200 || this.status == 404)) {
      //       isNetworkWorking = true;
      //     }
      //   };
      //   xhttp.open("GET", "URL here", true);
      //   xhttp.setRequestHeader("Content-type", "application/json");
      //   xhttp.send();
      // }
      // testNetwork();
      var container = document.querySelector('#tryAgain');
      document.addEventListener('click', function (event) {
        if (container.contains(event.target)) {
          window.location.reload()
        }
      });
      setTimeout(function () {
        if (!isAppLoaded) {
          if (isNetworkWorking){
            document.getElementById("network-error-msg").innerText = "Network is very slow. Please check your internet connection and try again.";
          }else{
            document.getElementById("network-error-msg").innerText = "Please check your internet connection and try again.";
          }
          document.getElementById("connectionError").style.display = "block";
        }
      }, 20000);
    </script>
  </app-root>
</body>

</html>