


.spinner {
  margin: 50vh auto 0 auto;
  width: 50px;
  height: 40px;
  text-align: center;
  font-size: 16px;
}

.spinner > div {
  background-color: var(--dark-bg-color);
  height: 100%;
  width: 2px;
  display: inline-block;
  margin: 0 2px;
  -webkit-animation: sk-stretchdelay .8s infinite ease-in-out;
  animation: sk-stretchdelay 0.8s infinite ease-in-out;
}
.spinner .rect1 {
  -webkit-animation-delay: -1.2s;
  animation-delay: -1.2s;
}

.spinner .rect2 {
  -webkit-animation-delay: -.8s;
  animation-delay: -.8s;
}

@-webkit-keyframes sk-stretchdelay {
  0%, 80%, 100% { -webkit-transform: scaleY(0.4) ; opacity: 1;}  
  40% { -webkit-transform: scaleY(1.0)  ; opacity: .4;}
}

@keyframes sk-stretchdelay {
  0%, 80%, 100% { 
    transform: scaleY(0.4);
    -webkit-transform: scaleY(0.4); ; opacity: 1;
  }  40% { 
    transform: scaleY(1.0);
    -webkit-transform: scaleY(1.0); ; opacity: .4;
  }
}