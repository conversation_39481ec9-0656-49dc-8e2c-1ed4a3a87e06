import { Component, OnInit, EventEmitter, Output, ViewChild } from '@angular/core';
import { NavigationExtras, Router, ActivatedRoute } from '@angular/router';
import { BookingHistoryService } from '../booking-history.service';
import { CommonUtils } from '../util/common-utils';
import { Constants } from '../util/constants';
import { DateUtils } from '../util/date-utils';
import { FlightUtils } from '../util/flight-utils';
import { TranslateService } from '@ngx-translate/core';
import { EmailQuoteOptionsService } from '../email-quote-options.service';
import { AdminPanelService } from '../admin-panel.service';
import { Location } from '@angular/common';
import { SearchResultService } from '../search-result.service';
import { BookingResponse } from '../entity/booking-response';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { BookingResponseErrorType } from '../enum/booking-response-error.type';
import { BookingService } from '../booking.service';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { BookingMessageModalComponent } from '../booking-message-modal/booking-message-modal.component';
import { ToastrService } from 'ngx-toastr';
import { Subscription, Subject } from 'rxjs';
import { UserAccountService } from '../user-account.service';
import { PopupComponent } from '../popup/popup.component';
import { SearchService } from '../search.service';
import { Title } from '@angular/platform-browser';
import { NgSelectComponent } from '@ng-select/ng-select';
import { DeviceDetailsService } from '../device-details.service';
import { BaggageDetails } from '../entity/baggage-details';
import { FareAttributes } from '../entity/fare-attributes';
import { HttpClient } from '@angular/common/http';
import { TypedRule } from 'tslint/lib/rules';
import { CancelSuccessComponent } from '../cancel-success/cancel-success.component';
@Component({
    selector: 'app-approval-detail',
    templateUrl: './approval-detail.component.html',
    styleUrls: ['./approval-detail.component.scss'],
    standalone: false
})
export class ApprovalDetailComponent implements OnInit {
  @Output() goBackEmitter = new EventEmitter();
  showDisapprovedReason = false;
  bookServiceSubscription: Subscription;
  companyApprovalSubscription:Subscription
  changingValue: Subject<any> = new Subject();
  modalOpen:any;
  pendingApprovals=[];
  spinnerStyle = false;
  employeedetail:any;
  waitData = true;
  disableApprovalBtn = false;
  bsModalRef: BsModalRef;
  showApprovedReason = false;
  messageForApprove:string="";
  responnsecame = false;
  disableSendNoteBtn = false;
  messageForDisapprove = '';
  pageMode = '';
  userEmail = '';
  availabilityError = false;
  classOptions = Constants.CLASS_OPTIONS;
  travellerName = '';
  ticketid = '';
  tripid = '';
  selectTransId = '';
  tripSessionId ='';
  startDate: Date = new Date();
  endDate: Date = new Date();
  errormsg = '';
  approvedReason = false;
  disApprovedReason = false;
  alreadyApproved = false;
  alreadyRejected = false;
  alreadyExpired = false;
  dropDownopen = [];
  error = false;
  detailList = [];
  flightList = [];
  cabinClass: any;
  airlineName:any;
  tagShow = [];
  tagset=[]
  selectedTag= [];
  airlines;
  airports;
  bookingStatusId;
  type = '';
  noOfPassenger = 0;
  flightLayoverInfoList: any[] = [];
  approvalTransactionDetail: any;
  isMobile1:boolean;
  queryParamSubscription: Subscription;
  deviceSubscription1: Subscription;
  flightFareChangeSubscription: Subscription;
  url: string = '/assets/test.json';
  detailsListLength: number;
  airLineName: any;
  tripDetails: any[];
  bookingHistoryDetailResp;
  transaction;
  checkBookingResponse;
  selectedDetail: any;
  showAllReadyHandeled: boolean = false;
  isSingleDetails = true;
  constructor(private activatedRoute: ActivatedRoute,
    private adminPanelService: AdminPanelService,
    public translateService: TranslateService,
    private bookingService: BookingService,
    private modalService: BsModalService,
    private location: Location,
    public deviceDetailsService: DeviceDetailsService,
    public router: Router,
    private toastr: ToastrService,
    private titleService: Title,
    private searchResultService: SearchResultService,
    private userAccountInfoService: UserAccountService,
    public searchService: SearchService,
    private http1: HttpClient,

  ) { }
  private bookingHistoryComponentInit() {

    if (this.pageMode === 'approval') {
      this.fetchBookingDetails();
    }

  }
  @ViewChild('chartDepartment') ngselect: NgSelectComponent;
  ngOnInit() {
    this.queryParamSubscription = this.activatedRoute.queryParams.subscribe(params => {
      this.setParameters(params);
      // if(params['type'] == 'list')
       this.bookingHistoryComponentInit();
    });
    this.modalService.onHide.subscribe(result => {
      if (this.searchService.networkErrorpopupHide && !this.pollingStarted) {
        this.disableSendNoteBtn = false;
        this.disableApprovalBtn = false;
        this.changingValue.next({value1:this.disableApprovalBtn});
      }
    });
    this.flightFareChangeSubscription = this.bookingService.flightFareChangeSubject.subscribe((newTransactionAmount) => {

      if (newTransactionAmount != null) {

        this.setPrice(this.detailList[0], newTransactionAmount);

      }
    });
    this.deviceSubscription1 = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
  }  
  isCreditApplied(){
    for(let item1 of this.transaction.data){
      if(item1 && item1.transactionEventType==='travel_credit'){
        return true
      }
    }
return false;
  }
  changeStyle() {
    if (this.isMobile1) {
      return { 'background-color': '#f7f7f7', 'border-top': '2px solid #e3e3e3','display':'block'};
    } else {
      return { 'background-color': '#f3f3f3', 'border-top': 'none','display':'flex','align-items':'center' }
    }
  }
  private setParameters(params) {
    if (params['ref']) {
      this.adminPanelService.refValueExist = params['ref'];
    }
    if (params['type']) {
      this.type = params['bookingType'];
      this.pageMode = params['view'];
      this.userEmail = params['userEmail'];
      this.travellerName = params['name'];
      this.tripid = params['tripid'];
      this.ticketid = params['ticketid'];
      this.selectTransId = params['transactionid'];
    }
    if(params['tripSessionId']){
      this.tripSessionId = params['tripSessionId'];
    }
    if (this.type === 'upcoming') {
      this.type = 'pending';
    }else{
     // this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ApprovalsHistory'));
    }
  }
  isTransactionBookingConfirmed = true;
  tempSelectTag =[];
  handleResponseFromDropdwn(event){
   this.tempSelectTag=event;
  }
 
  sortList(data) {
    data.sort(function (a, b) {
      if (a.tag_name.toLowerCase() < b.tag_name.toLowerCase()) { return -1; }
      if (a.tag_name.toLowerCase() > b.tag_name.toLowerCase()) { return 1; }
      return 0;
    })
    return data;
  }
  getAllTags() {
    const companyid = this.userAccountInfoService.getUserCompanyId();
    this.tagShow = [];
    this.tagset=[];
     this.selectedTag=[];
    this.adminPanelService.getTagAllList().subscribe(resp => {
      if (resp.status === 'success') {
        if (resp.data && Object.keys((resp.data)).length > 0) {
          this.getAllTagsSet()
          for (let key in resp.data) {
            let keysValue = key
            this.tagset.push(keysValue);
            this.dropDownopen.push(false);

          }
          this.tagShow = new Array(this.tagset.length).fill(null).map(_ => [])
          for(let i=0;i<this.tagset.length;i++){
            let arrayVAlue = resp.data[this.tagset[i]];
            for(let item1 of arrayVAlue){
              this.tagShow[i].push(item1);
            }
            this.tagShow[i] = this. sortList(this.tagShow[i]);
          }
          if (this.approvalTransactionDetail && this.transaction && this.transaction.projectTagId && this.transaction.projectTagId !== '') {
      
            this.selectedTag = CommonUtils.selectagDropdown(this.transaction.projectTagId,this.tagset,this.tagShow,this.selectedTag)
              
           }
          
        }
      } else {
        this.toastr.error("Apologies! something went wrong, we could'nt report data. Please try again later");
      }
    }, error => {
      if (error.status != 403) {
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
  }

  tagsSetData = {
    "isMandotary":false,
    "tags":[]
  }

  getAllTagsSet(){
    const companyid = this.userAccountInfoService.getUserCompanyId();
    this.adminPanelService.getTagSet(companyid).subscribe((res)=>{
      
      if(res.status == "success"){
        
       const  new_tagSets_array = res.data.tagsets;

        this.tagsSetData.tags = [];
        new_tagSets_array.forEach(e =>{
        let mandatory = false;
        if(e.mandatory && e.activeTagCount > 0){
          this.tagsSetData.isMandotary = true;
          mandatory = true;
        }
          const obj  = {
            "companyId": e.companyId,
            "tagsetId": e.tagsetId,
            "tagSetName": e.tagSetName,
            "mandatory": mandatory,
            "tags":""
         };
  
         this.tagsSetData.tags.push(obj)
      })
      }
    })
   
  }
  isThisTripHasCredits() {
    let showCredits = false;
    for (let item1 of this.transaction.data) {
      if (item1 && item1.transactionEventType === 'travel_credit') {
        showCredits = true;
        break;
      }
    }
    return showCredits
  }
  priceDiff(item,item2?) {

    let totalPrice = this.getExpectedPrice(this.transaction.data);
    if(item2 && item2==='hotel'){
      if(item.displayPrice){
        if (Math.round(item.displayPrice) !== Math.round(totalPrice)) {
          return true;
        } else {
          return false;
        }
      }else{
      if (Math.round(item.originalPrice) !== Math.round(totalPrice)) {
      return true;
    } else {
      return false;
    }
  }
    }else{
    if(item.displayPrice){
      if (Math.round(item.displayPrice) !== Math.round(totalPrice)) {
        return true;
      } else {
        return false;
      }
    }else{
    if (Math.round(item.price) !== Math.round(totalPrice)) {
      return true;
    } else {
      return false;
    }
  }
  }
}
  getHotelPriceItem(item){
    if(item.displayPrice){
      return item.displayPrice;
    }else{
      return item.originalPrice;
    }
  }
  
  cancellationPolicy = '';
  getCancellationPolicy(hotelOption){
    this.cancellationPolicy = '';
    if (hotelOption.cancellationPolicyInfo
      && hotelOption.cancellationPolicyInfo != null
      && hotelOption.cancellationPolicyInfo.trim().length > 0) {
      this.cancellationPolicy = hotelOption.cancellationPolicyInfo.toLowerCase();
    }
    if (hotelOption.rateDetail
      && hotelOption.rateDetail.cancelInfo
      && hotelOption.rateDetail.cancelInfo.text
      && hotelOption.rateDetail.cancelInfo.text.length > 0) {
      this.cancellationPolicy = hotelOption.rateDetail.cancelInfo.text[0];
    }
    if (hotelOption.hotelRooms && hotelOption.hotelRooms != null
      && hotelOption.hotelRooms.length > 0
      && hotelOption.hotelRooms[0].hotelRates
      && hotelOption.hotelRooms[0].hotelRates != null
      && hotelOption.hotelRooms[0].hotelRates.length > 0
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies != null
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies.length > 0
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].cancellationRule ) {
      
      this.cancellationPolicy = hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].cancellationRule ;

    }
    return this.cancellationPolicy;
  }
  isAmenityAvailable(type: string) {
    if (this.tripSelectedToshowOnpopUp && this.tripSelectedToshowOnpopUp.amenities) {
        return this.tripSelectedToshowOnpopUp.amenities[type];
    }
    return false;
}
  getLocationType1(item) {
    if (item && item !== '') {
      if (item === 'Terminal') {
        return 'At airport terminal'
      } else {
        return 'Outside airport - shuttle from airport terminal'
      }
    }
  }
  getBrandText(brandClass: string) {
    if (brandClass && brandClass.trim().length > 0) {
      return '(' + brandClass + ')';
    }
    return '';
  }
  getPerDayPrice(car) {
    if(car.displayPrice){
      var subTotal = (car.displayPrice / car.numberOfDay);
      return subTotal;
    }else{
    var subTotal = (car.price / car.numberOfDay);
    return subTotal;
    }
  }
  getPriceItem(item){
    if(item.displayPrice){
      return item.displayPrice;
    }else{
      return item.price;
    }
  }
  tripSelectedToshowOnpopUp:any;
  openModal(modal,type,item){
    this.tripSelectedToshowOnpopUp=item;
    this.tripSelectedToshowOnpopUp['type']=type;
    
    this.bsModalRef = this.modalService.show(modal);
    
  }
  getExpectedPrice(item?) {
    let item1;
    if(item){
      item1 =item;
    }else{
      item1 = this.transaction.data;
    }
    let totalFare = CommonUtils.calculateFare(item1);
    let totalFlightPrice = totalFare;
    const perTravellerPrice = (totalFlightPrice / this.noOfPassenger);
    for (let counter = 0; counter < this.transaction.data.length; counter++) {
      if (this.transaction.data[counter].transactionEventType === "travel_credit") {
        const item = this.transaction.data[counter];
        if(item.displayPrice){
          if (Math.abs(item.displayPrice) <= perTravellerPrice) {
            totalFare -= Math.abs(item.displayPrice);
          } else {
            totalFare -= perTravellerPrice;
          }
        }else{
        if (Math.abs(item.price) <= perTravellerPrice) {
          totalFare -= Math.abs(item.price);
        } else {
          totalFare -= perTravellerPrice;
        }
      }
    }
    }
    return totalFare;
  }
  getDurationOfhop(hop){
    let d = DateUtils.getDurationAsHrsMinObj(hop.duration);
    return d.hrs + ' hr ' + d.mins + ' min';
  }

  

  private fetchBookingDetails() {
    this.responnsecame = false;
    this.waitData = true;
    this.detailList = [];
    this.getAllTags();
    this.adminPanelService.getApprovalDetailByTripSessionId(this.userEmail, this.ticketid, this.tripid, this.selectTransId,this.tripSessionId).subscribe(resp => {this.setgetApprovalDetail(resp)});
  }

  
  private setgetApprovalDetail(resp){
    if (resp.success === true && resp.data) {
      this.waitData = false;
      this.errormsg = '';
      let bookingHistoryDetail = JSON.parse(JSON.stringify(resp));
      this.bookingHistoryDetailResp = bookingHistoryDetail;
      let bookingHistoryArray = bookingHistoryDetail.data.detail;
      this.approvalTransactionDetail = bookingHistoryDetail.data;
      for (let item of bookingHistoryArray) {
        if (item.type == 'flight') {
          item.option.flight_option.layoverInfo = this.getLegWiseLayoverList(item);
        };
      };
      this.tripDetails = [];
      let reviewedTransactions = [];

      if(bookingHistoryArray.length > 0){
        if(this.selectTransId){
          this.transaction =  bookingHistoryDetail.data.transactionList.find(e => e.transaction_id == this.selectTransId);
        }else{
          this.transaction =  bookingHistoryDetail.data.transactionList[0];
        }
  
        if(!this.isPendingApproval() && this.checkBookingResponse){
          if(this.checkBookingResponse.multiBookingResponse){
            for(let respItem of this.checkBookingResponse.multiBookingResponse){
              if(respItem.transactionId) {reviewedTransactions.push(respItem.transactionId)}
            }
          }else{
            if(this.checkBookingResponse.transactionId){
              reviewedTransactions.push(this.checkBookingResponse.transactionId)
            }
          }
        }
  
  
  
  
        if(bookingHistoryDetail.data.tripDetail){
          bookingHistoryDetail.data.tripDetail.forEach(e => {
            if(e.detail.length != 0){this.tripDetails.push(e)};
          });
        }else{
          bookingHistoryDetail.data.detail.forEach((e,i) => {
            this.tripSessionId = e.tripSessionId;
            if(reviewedTransactions.length == 0  || (reviewedTransactions.length > 0 && reviewedTransactions.includes(e.option.selectTransId))){
                let obj
                if(bookingHistoryDetail.data.transactionList){
                  let transaction = bookingHistoryDetail.data.transactionList.find( a => a.trip_id == e.tripid);
                   obj={
                    detail:[e],
                    transaction:transaction
                  }
                }else{
                  let transaction = bookingHistoryDetail.data.transactionList.find(a => a.trip_id == e.tripid);
                   obj={
                    detail:[e],
                    transaction:transaction
                  }
                } 
                this.tripDetails.push(obj)
                this.detailList.push(obj.detail[0]);
              }
          });
        }
          
        this.selectedDetail = this.detailList.find(e => e.transactionId == this.selectTransId);
        let newTransaction = this.tripDetails.find(e => e.transaction.transaction_id == this.selectTransId);
        this.transaction = newTransaction;
  
        
           
        if(this.selectedDetail == undefined){
          this.selectedDetail = this.detailList[0];
        }else{
          this.selectedDetail = this.detailList.find(e => e.transactionId == this.selectTransId);
        };
        if(this.transaction == undefined){
          this.transaction = this.tripDetails[0].transaction;
        }else{
          this.transaction = newTransaction.transaction;
        };
        if(!this.isPendingApproval() && this.tripDetails.length > 1 && this.checkBookingResponse == undefined){
          this.isSingleDetails = false;
          this.showAllReadyHandeled = true
          
        };
        
        this.airlines = bookingHistoryDetail.data.airlineNames;
        this.airports = bookingHistoryDetail.data.airports;
        for(var key in this.airlines){
          this.airLineName = this.airlines[key];
        }
        if(bookingHistoryDetail.data.employee){
          for(let key in bookingHistoryDetail.data.employee){
        this.employeedetail = bookingHistoryDetail.data.employee[key];
          }
        }
  
        if (bookingHistoryDetail.data.cabinClassNames) {
          this.cabinClass = bookingHistoryDetail.data.cabinClassNames
        }
        if (this.transaction && this.transaction.projectTagId && this.transaction.projectTagId !== '') {
      
         this.selectedTag = CommonUtils.selectagDropdown(this.transaction.projectTagId,this.tagset,this.tagShow,this.selectedTag)
           
        }
        this.isTransactionBookingConfirmed = true;
  
  
            let optionTripStatus = "selected";
            if (this.selectedDetail.option.status) optionTripStatus = this.selectedDetail.option.status;
            if (this.selectedDetail.type === 'flight') {
              this.isTransactionBookingConfirmed = this.isTransactionBookingConfirmed && (optionTripStatus === 'ticketed' || optionTripStatus === 'expensed');
            } else if (this.selectedDetail.type === 'hotel') {
              this.isTransactionBookingConfirmed = this.isTransactionBookingConfirmed && (optionTripStatus === 'booked' || optionTripStatus === 'expensed');
            } else if (this.selectedDetail.type === 'cars') {
              this.isTransactionBookingConfirmed = this.isTransactionBookingConfirmed && (optionTripStatus === 'booked' || optionTripStatus === 'expensed');
            }
  
  
        
       
          if (this.isPendingApproval()) {
            this.showDisapprovedReason = false;
            this.disApprovedReason = false;
            this.approvedReason = false;
          } else if (this.isApproved()) {
            this.approvedReason = true;
            this.showDisapprovedReason = false;
            this.disApprovedReason = false;
          } else if (this.isRejectedApproval()) {
            this.disApprovedReason = true;
            this.showDisapprovedReason = false;
            this.approvedReason = false;
          }
        
          // if(this.checkBookingResponse == undefined  && !this.isPendingApproval()){
          //   let allApproved : boolean = false;
          //   for( let e of this.approvalTransactionDetail.transactionList){
          //     if((e && e.approvalStatus) && (e.approvalStatus == 'rejected' || e.approvalStatus == 'expired')){
          //       allApproved = true;
          //       break;
          //     };
          //   };
          //   this.showAllReadyHandeled = allApproved;
          // }
  
         
        
        this.responnsecame = true;
        this.waitData = false;
      }else{
        this.bsModalRef = this.modalService.show(CancelSuccessComponent, {
          initialState: {
            message: this.translateService.instant('bookingHistory.NoResultsFound'),
          },
        });
      }
      
      
    } else {
      this.errormsg = resp.error_message;
      this.responnsecame = true;
    }
    
    this.detailsListLength = this.detailList.length;
  }
  private isPendingApproval(): boolean {
    let isPending : boolean = false;
    if(this.approvalTransactionDetail.transactionList){
      for( let transac of this.approvalTransactionDetail.transactionList){
        if(transac && transac.approvalStatus && transac.approvalStatus == 'pending'){
          isPending = true;
          break;
        }
      }
    }else{
      isPending = this.approvalTransactionDetail && this.transaction
      && this.transaction.approvalStatus
      && this.transaction.approvalStatus == 'pending';
    }
    return isPending;
  }

  private isRejectedApproval(): boolean {
    return this.approvalTransactionDetail && this.transaction
      && this.transaction.approvalStatus
      && this.transaction.approvalStatus == 'rejected';
  }
  isExpiredApproval(): boolean {
    return this.approvalTransactionDetail && this.transaction
      && this.transaction.approvalStatus
      && this.transaction.approvalStatus == 'expired';
  }

  private isApproved(): boolean {
    return this.approvalTransactionDetail && this.transaction
      && this.transaction.approvalStatus
      && this.transaction.approvalStatus == 'approved';
  }

 
  carUrl(car) {
    if (car.images) {
      return car.images.S;
    }
  }
  
  closeDropdown(i) {
    this.dropDownopen[i] = false;
  }
  isdropDownOpen(i) {

    this.dropDownopen[i] =true
  }
  getLegWiseLayoverList(flight): any[] {

    let flightLayoverInfoList = [];

    if (flight) {

      flight.option.flight_option.legs.forEach((leg) => {

        let numOfHops = leg.hops.length;

        let flightLegLayoverInfoList = [];

        if (numOfHops > 1) {

          for (let i = 0; i < numOfHops - 1; i++) {

            var layoverIn = (leg.hops[i].to == leg.hops[i + 1].from) ?
              leg.hops[i].to :
              leg.hops[i].to + "/" + leg.hops[i + 1].from;

            let flightLegLayoverInfo = { in: '', duration: {} };
            flightLegLayoverInfo.in = layoverIn;

            var hop1Date = new Date(leg.hops[i].ends);
            var hop2Date = new Date(leg.hops[i + 1].starts);

            var timeDiffInMin = DateUtils.getDateDiffInMinutes(hop2Date, hop1Date);

            flightLegLayoverInfo.duration = DateUtils.getDurationAsHrsMinObj(timeDiffInMin);

            flightLegLayoverInfoList.push(flightLegLayoverInfo);
          }
        }

        if (flightLegLayoverInfoList) {
          flightLayoverInfoList.push(flightLegLayoverInfoList);
        }

      })

    }

    return flightLayoverInfoList;
  }
  getFlightIconURL(flightCode: string) {
    return CommonUtils.getAirlineImageUrl(flightCode);
  }
  getLayoverDetails(item, i, j): boolean {
    if (item.option.flight_option.layoverInfo.length > 0) {
      if (item.option.flight_option.layoverInfo[i][j]) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }
  getAirportCity(code) {
    return code && this.airports[code] && this.airports[code]['name'] ? this.airports[code].name : code;
  }
  
  disapproved() {
    this.showDisapprovedReason = true;
    this.showApprovedReason =false;
    this.adminPanelService.gotoDetail = false;
  }
  approveed(item,modal){
    this.modalOpen =modal;
    if(item==='approve'){
      this.approve();
    }else{
      this.disapproveNote();
    }
   
  }
  initiateDelayedPolling(bookingRequestId: string, requestType: string, delay: number) {
    setTimeout(() => {
      if (this.userAccountInfoService.isConnected) {
        this.initiateImmediatePolling(bookingRequestId, requestType, delay);
      } else {
        this.initiateDelayedPolling(bookingRequestId, requestType, delay);
      }
    }, delay);
  }

  initiateImmediatePolling(bookingRequestId: string, requestType: string, delay) {
    this.bookServiceSubscription = this.bookingService.pollingForBookingResponse(bookingRequestId).subscribe((res) => {
      if (requestType == 'approve') {
        this.approveResponse(res);
      } else if (requestType == 'disApprove') {
        this.disApproveResponse(res);
        
      }
      // if(res.status == 'success'){
      //   this.fetchBookingDetails()
      // }
      
    }, error => {
      this.initiateDelayedPolling(bookingRequestId, requestType, delay);
    //  CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_BOOKINPROGRESS,this.translateService);
    });
  }
  disApproveResponse(resp) {
    let bookingResponse: BookingResponse = deserialize(resp, BookingResponse);
    this.checkBookingResponse = bookingResponse;
    let responseType = bookingResponse.status.toUpperCase();
    if (bookingResponse.bookingStatusId) {
      this.bookingStatusId = bookingResponse.bookingStatusId;
    }

    if (responseType === Constants.INPROGRESS) {
      this.initiateDelayedPolling(bookingResponse.bookingStatusId, 'disApprove', bookingResponse.nextCallAfter);
      return;
    }
    this.disableSendNoteBtn = false;
    if (responseType === Constants.ERROR) {
      responseType = bookingResponse.errors[0].errorType;
      if (responseType === BookingResponseErrorType.ERROR_AVAILABILITY
        || responseType === BookingResponseErrorType.ERROR_INTERNAL
        || responseType === BookingResponseErrorType.ERROR_API
        || responseType === BookingResponseErrorType.ERROR_TRIP_BOOKING_PENDING) {
        // this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: !this.hasHotelBillingItem() }, backdrop: true, ignoreBackdropClick: true });
      } else {
        let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse, false);
        this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: false, keyboard: false, ignoreBackdropClick: false });
        this.disableSendNoteBtn = false;
       
        this.disableApprovalBtn =false;
        this.changingValue.next({value1:this.disableApprovalBtn});
      }
    } else {
      let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
      let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);
      let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
      let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
  //this.adminPanelService.fetchCompanyApprovals(this.userAccountInfoService.getUserCompanyId(), startDate, endDate, 'all');
      if (bookingResponse.alreadyHandledResponse) {
        if (bookingResponse.alreadyHandledResponse === 'AlreadyRejected') {
          this.alreadyRejected = true;
        } else if (bookingResponse.alreadyHandledResponse === 'AlreadyExpired') {
          this.alreadyExpired = true;
        } else if (bookingResponse.alreadyHandledResponse === 'AlreadyApproved') {
          this.alreadyApproved = true;
        }
      }
      this.waitData = true;
      this.responnsecame = false;
      this.adminPanelService.companyApprovalRequest(this.userAccountInfoService.getUserCompanyId(), startDate, endDate,).subscribe((reportResponse) => {
        if (reportResponse && reportResponse.success) {
          if (reportResponse && reportResponse.data && reportResponse.data.pendingApprovals.length > 0) {
            this.pendingApprovals=[];
            for (let optionItem of reportResponse.data.pendingApprovals) {
              let reportItem = {};
              reportItem['name'] = reportResponse.data.users[optionItem.userid].userName;
              reportItem['expiryTime'] = optionItem.approvalExpiryTime;
              this.pendingApprovals.push(reportItem);
            }
            if(this.pendingApprovals.length >0){
              if(!this.bsModalRef){
                this.bsModalRef = this.modalService.show(this.modalOpen);
                this.fetchBookingDetails();
              }
            }
          }else{
            this.fetchBookingDetails();
          }
        }else{
          this.fetchBookingDetails();
        }
      });  
      
      this.disApprovedReason = false; 
      this.disableApprovalBtn = false;
      this.changingValue.next({value1:this.disableApprovalBtn});
     // this.showDisapprovedReason = false;
      this.error = false;
    }
  }
  routeToApproval(type, type11, item) {
    this.bsModalRef.hide();
    if(this.searchService.showApprovalaInMenu){
      
      this.goBackEmitter.emit(true);
    }else{
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'approvals',
          subType: 'pending'
        },
        replaceUrl: false
      }
    );
    }
  }

  disapproveNote() {
   // if (this.userAccountInfoService.isConnected) {
      if (this.messageForDisapprove) {
        this.bookingStatusId = '';
        this.disableSendNoteBtn = true;
        this.disableApprovalBtn = true;
        this.changingValue.next({value1:this.disableApprovalBtn});
        let reviewAction = 'rejected';
        let note = this.messageForDisapprove;
   //  this.tempSelectTag=   this.selectedTag
        this.adminPanelService.requestApprovalReviewAction(this.userEmail, this.ticketid, this.tripid, this.selectTransId, 
          this.tripSessionId, reviewAction, note, this.bookingStatusId, this.tempSelectTag).subscribe(resp => {
          if (this.bsModalRef) {
            this.bsModalRef.hide();
          }
          this.pollingStarted=true;
          this.disApproveResponse(resp);
        }, error => {
       //   CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_APPROVAL_REQUEST,this.translateService);
          // setTimeout(()=>{
          // this.disableSendNoteBtn=false;
          //let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          //this.toastr.error(resultErrorMessage);
          //},100);
        });
      } else {
        this.error = true;
      }
   
  }
  getLocationType(item) {
    if (item && item !== '') {
      if (item === 'Terminal') {
        return 'At airport terminal'
      } else {
        return 'Outside airport - shuttle from airport terminal'
      }
    }
  }
  getNUmberOfdigitOfPrice(symbol, price) {
   
    let str = symbol + price;
    if (!this.isMobile1) {
      if (str.length >= 8) {
        return { 'font-size': '14px' }
      } else if (str.length < 8) {
        return { 'font-size': '16px' }
      } else if (str.length <= 6) {
        return { 'font-size': '18px' }
      }
    } else {
      if (str.length > 6) {
        return { 'font-size': '10px' }
      } else if (str.length <= 6) {
        return { 'font-size': '12px' }
      } else if (str.length <= 5) {
        return { 'font-size': '14px' }
      }
    }
  }
  getNumberCarriers(item){
    let carrierArray=[];
      for(let hop of item.hops){
      if(carrierArray.indexOf(hop.carrier)===-1){
        carrierArray.push(hop.carrier)
      }
      
  
    }
  return (carrierArray.length - 1);
  }
  getLegRoomFareAttribute() {
    return this.getCurrentFlightFareAttributes().legRoom;
  }
  getLegroomFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).legRoom;
  }
  getDetailsStyleModal(){
    if(this.isMobile1){
      return {'justify-content':'center','border-left':'none'}
    }else{
      return {'justify-content':'start','border-left':'2px solid #e3e3e3'}
    }
  }
  getBaggageDetails(item) {
    let baggageDetails: BaggageDetails = this.tripSelectedToshowOnpopUp.hops[0].baggageAllowance;
    if (this.searchService.brandedFareCheckBox && this.tripSelectedToshowOnpopUp.hops[0].brandDetails && this.tripSelectedToshowOnpopUp.hops[0].brandDetails.length > 0) {
      baggageDetails = this.tripSelectedToshowOnpopUp.hops[0].brandDetails[0].baggageAllowance;
    }
    if (baggageDetails) {
      if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return (Number.parseInt(baggageDetails.numberOfPieceAllowed) === 1) ? this.translateService.instant('flightChart.1checkedbagperadult')
          : baggageDetails.numberOfPieceAllowed + ' ' + this.translateService.instant('flightChart.checkedbagsperadult');
      }
      if ((baggageDetails.maxWeightAllowed && baggageDetails.maxWeightAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.maxWeightAllowed) === 0) || (!baggageDetails.maxWeightAllowed)) {
        return this.translateService.instant('flightChart.Nocheckedbags');
      } else {
        return baggageDetails.maxWeightAllowed + ' ' + baggageDetails.weightUnit.toLowerCase() + this.translateService.instant('flightChart.peradult');
      }
    }
    return null;
  }
  getCorporateFareAttributeMoreOptions(item) {
    if(item.corporateRate){
    return item.corporateRate;
    }
  }
 isBaggageAvailable(item){
  let baggageDetails: BaggageDetails = this.tripSelectedToshowOnpopUp.hops[0].baggageAllowance;
  if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
  && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return true;
      } else {
return false;
      }
 }
  isWithinPolicy(item?) {
    if (this.tripSelectedToshowOnpopUp.hops[0].withinPolicy) {
      return this.tripSelectedToshowOnpopUp.hops[0].withinPolicy;
    }
  }
  getCurrentFlightFareAttributesForMoreOptions(item): FareAttributes {
    return item.fareAttributes;
  }
  isMixed(item): boolean {
    return this.tripSelectedToshowOnpopUp.hops[0].flightHighlights.mixedClass;
  }
  showWithinPolicy() {
    return  this.tripSelectedToshowOnpopUp.hops[0].withinPolicy;
  }
  getBaggageDetailsColor(item) {
    let baggageDetails: BaggageDetails = item.legs[0].baggageAllowance;
    if (this.searchService.brandedFareCheckBox && item.legs[0].brandDetails && item.legs[0].brandDetails.length > 0) {
      baggageDetails = item.legs[0].brandDetails[0].baggageAllowance;
    }
    if (baggageDetails) {
      if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return '#27c198 !important';
      }
      if ((baggageDetails.maxWeightAllowed && baggageDetails.maxWeightAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.maxWeightAllowed) === 0) || (!baggageDetails.maxWeightAllowed)) {
        return '#f93d30 !important';
      } else {
        return '#27c198 !important';
      }
    }
    return null;
  }
  getWifiFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).wifi;
  }
  getCheckedBagFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).checkedBag;
  }
  getCarryOnBagFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).carryBag;
  }
  getRefundAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).refund;
  }
  getChangesFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).rebooking;
  }
  getSeatFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).seat;
  }
  getCurrentFlightFareAttributes(): FareAttributes {
   
    return this.tripSelectedToshowOnpopUp.hops[0].fareAttributes;
  }
  getWifiAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).wifi;
  }
  getMealFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).meal;
  }
  getWifiFareAttribute() {
    return this.getCurrentFlightFareAttributes().wifi;
  }
  getCheckedBagFareAttribute() {
    return this.getCurrentFlightFareAttributes().checkedBag;
  }
  getCarryOnBagFareAttribute() {
    return this.getCurrentFlightFareAttributes().carryBag;
  }
  getRefundAttribute() {
    return this.getCurrentFlightFareAttributes().refund;
  }
  getChangesFareAttribute() {
    return this.getCurrentFlightFareAttributes().rebooking;
  }
  getSeatFareAttribute() {
    return this.getCurrentFlightFareAttributes().seat;
  }
  getWifiAttribute() {
    return this.getCurrentFlightFareAttributes().wifi;
  }
  getMealFareAttribute() {
    return this.getCurrentFlightFareAttributes().meal;
  }
  getHopDuration(layover){
    let time=0;
    const minutes = layover;
    const secondsOfMinutes = minutes * 60;
    time += secondsOfMinutes;
    let totalTime=0;
     totalTime = time;
    if(totalTime >0){
      return Math.floor(totalTime / 60) +' hr '+  " " + (totalTime % 60 ? totalTime % 60 : '00') +" min "
        
    }else{
      return null;
    }
  }
  getAllLayoverTimeHour(layover){
    let time=0;
    for(let i=1;i <layover[0].hops.length;i++){
     // const seconds = +parseInt(layover[0].hops[i].duration.split(' ')[2]);
      const minutes = layover[0].hops[i].duration
      const secondsOfMinutes = minutes;
      time += secondsOfMinutes;
      

    }
    let totalTime=0;
     totalTime = time;
    if(totalTime >0){
      return Math.floor(totalTime / 60) +' hr '+  " " + (totalTime % 60 ? totalTime % 60 : '00') +" min "
        
    }else{
      return null;
    }
  }
  backToApproval() {
    if(this.searchService.showApprovalaInMenu){
      this.goBackEmitter.emit(true);
      this.router.navigate(["approval"],{queryParams:{type: 'approvals',subType: 'pending'},replaceUrl: false});
    }else{

    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'approvals',
          subType: 'pending'
        },
        replaceUrl: false
      }
    );
    }
   
  }


  approveResponse(resp) {
    let bookingResponse: BookingResponse = deserialize(resp, BookingResponse);
    this.checkBookingResponse = bookingResponse;
    let responseType = bookingResponse.status.toUpperCase();
    if (bookingResponse.bookingStatusId) {
      this.bookingStatusId = bookingResponse.bookingStatusId;
    }

    if (responseType === Constants.INPROGRESS) {
      this.initiateDelayedPolling(bookingResponse.bookingStatusId, 'approve', bookingResponse.nextCallAfter);
      return;
    }
    this.disableApprovalBtn = false;
    if (bookingResponse.transactionId && bookingResponse.transactionId.length > 0) {
      this.selectTransId = bookingResponse.transactionId;
    }
    if (responseType === Constants.ERROR || (bookingResponse.errors.length > 0 && bookingResponse.errors[0].errorType === BookingResponseErrorType.ERROR_AVAILABILITY)) {
      responseType = bookingResponse.errors[0].errorType;
      if (responseType === BookingResponseErrorType.ERROR_AVAILABILITY) {
        this.availabilityError = true;
        this.showDisapprovedReason = false;
       // this.showApprovedReason = false;
        this.approvedReason = false;
      } else if (responseType === BookingResponseErrorType.ERROR_INTERNAL || responseType === BookingResponseErrorType.ERROR_API || responseType === BookingResponseErrorType.ERROR_TRIP_BOOKING_PENDING ) {
          this.showApprovedReason = false;
        // this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: !this.hasHotelBillingItem() }, backdrop: true, ignoreBackdropClick: true });
      } else {
        
        let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse, false);
        this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: false, keyboard: false, ignoreBackdropClick: false });
        this.disableSendNoteBtn = false;
        this.disableApprovalBtn =false;
      }

    } else {
      this.waitData = true;
      this.responnsecame = false;
      let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
      let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);
      let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
      let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
 // this.adminPanelService.fetchCompanyApprovals(this.userAccountInfoService.getUserCompanyId(), startDate, endDate, 'all');
      if (bookingResponse.errors.length > 0) {
        responseType = bookingResponse.errors[0].errorType;
        this.isTransactionBookingConfirmed = false;
      } else {
        this.isTransactionBookingConfirmed = true;
      }
      if (bookingResponse.alreadyHandledResponse) {
        if (bookingResponse.alreadyHandledResponse === 'AlreadyApproved') {
          this.alreadyApproved = true;
        } else if (bookingResponse.alreadyHandledResponse === 'AlreadyExpired') {
          this.alreadyExpired = true;
        } else if (bookingResponse.alreadyHandledResponse === 'AlreadyRejected') {
          this.alreadyRejected = true;
        }
      }
      
     
 
    this.adminPanelService.companyApprovalRequest(this.userAccountInfoService.getUserCompanyId(), startDate, endDate,).subscribe((reportResponse) => {
      if (reportResponse && reportResponse.success) {
        if (reportResponse && reportResponse.data && reportResponse.data.pendingApprovals.length > 0) {
          this.pendingApprovals=[];
          for (let optionItem of reportResponse.data.pendingApprovals) {
            let reportItem = {};
            reportItem['name'] = reportResponse.data.users[optionItem.userid].userName;
            reportItem['expiryTime'] = optionItem.approvalExpiryTime;
            this.pendingApprovals.push(reportItem);
          }
          if(this.pendingApprovals.length >0){
            if(!this.bsModalRef){
              this.bsModalRef = this.modalService.show(this.modalOpen);
              this.fetchBookingDetails();
            }
          }
        }else{
          this.fetchBookingDetails();
        }
      }else{
        this.fetchBookingDetails();
      }
    });  
  
    
      
      this.showApprovedReason = false;
      this.disableSendNoteBtn = false;
      this.disableApprovalBtn =false;
      this.showDisapprovedReason = false;
      this.approvedReason = false;

    }
  }
  pollingStarted=false;
  approve() {
   // if (this.userAccountInfoService.isConnected) {
      this.bookingStatusId = '';
      this.disableApprovalBtn = true;
      this.disableSendNoteBtn = true;
      this.changingValue.next({value1:this.disableApprovalBtn});
      this.adminPanelService.gotoDetail = false;
      let reviewAction = 'approved';
      let note = '';
      //different Api For session Id and NO Session Id
      if(this.tripSessionId != ""){

      }else{

      };
      this.adminPanelService.requestApprovalReviewAction(this.userEmail, this.ticketid, this.tripid, this.selectTransId, 
        this.tripSessionId, reviewAction, note, this.bookingStatusId, this.messageForDisapprove,this.selectedTag).subscribe(resp => {
        if (this.bsModalRef) {
          this.bsModalRef.hide();
          
        }
        this.pollingStarted=true;
        this.approveResponse(resp);
      }, error => {
     //   CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_APPROVAL_REQUEST,this.translateService);
        setTimeout(()=>{
        this.disableApprovalBtn=false;
        let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
        this.toastr.error(resultErrorMessage);
        },100);
      });
   
  }
  cancelDisapproval() {
    this.showDisapprovedReason = false;
  }
  cancelApproval() {
    this.showApprovedReason = false;
  }
  getCurrencySymbol(item): string {
    if(item.displayCurrency){
      return CommonUtils.getCurrencySymbol(item.displayCurrency);
    }else{
    return CommonUtils.getCurrencySymbol(item.currency);
    }
  }
  getClassName(id, hop?) {
    if (hop) {
      let cabinClass = CommonUtils.classNameReturnFromMap(this.cabinClass, hop.carrier, hop.type);
      if (cabinClass) {
        return cabinClass;
      }
    }
    let className = null;

    this.classOptions.map(item => {
      if (item.id.toLowerCase() == id.toLowerCase()) {
        className = item.value;
      }
    });

    if (className === null) {
      return id;
    }

    return className;

  }
  getClassNameAndBrandName(hop: any) {
    if (hop) {
      let cabinClass = CommonUtils.classNameReturnFromMap(this.cabinClass, hop.carrier, hop.type);
      if (cabinClass) {
        return cabinClass;
      }
    }
    let className = this.getClassName(hop.type);
    if (hop.brandDetail && hop.brandDetail.trim().length > 0 && hop.brandDetail.trim() != 'null') {
      let brandObj = JSON.parse(hop.brandDetail);
      if (brandObj.name && brandObj.name.trim().length > 0) {
        className = className + ' (' + brandObj.name.trim() + ')';
      }
    }
    return className;
  }
  getDuration(flight, legIndex: number) {
    return this.getFlightLegDuration(flight, legIndex);
  }
  getFlightLegDuration(flight, legIndex: number): any {
    let durationInMins = this.getFlightLegDurationInMin(flight, legIndex);
    return DateUtils.getDurationAsHrsMinObj(durationInMins);
  }

  getFlightLegDurationInMin(flight, legIndex: number): any {
   // if (!flight && !flight.option.flight_option.legs[legIndex]) return 0;
    let startDate ;
    let endDate ;
    if(flight && !flight.option){
      startDate = new Date(flight.legs[legIndex].hops[0].starts);
      endDate = new Date(flight.legs[legIndex].hops[flight.legs[legIndex].hops.length - 1].ends);
    }else if(flight && flight.option && flight.option.flight_option){
   
    startDate = new Date(flight.option.flight_option.legs[legIndex].hops[0].starts);
    endDate = new Date(flight.option.flight_option.legs[legIndex].hops[flight.option.flight_option.legs[legIndex].hops.length - 1].ends);
    }
  if(endDate && startDate){
    return DateUtils.getDateDiffInMinutes(endDate, startDate);
  }
  }
  getDisplayDate(dateString: string, format: string): string {
    return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
  }
  getDisplayFlightDate(dateString: string, format: string): string {
    return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }
  getCarDisplayDate(datestring, format) {
    return DateUtils.getFormattedDateForGivenTimeZone(datestring, format);
  }
  getAirlineFullName(code) {
    let airlineFullName = null;
    airlineFullName = this.airlines[code];
    return (airlineFullName === '' || airlineFullName === null  || airlineFullName === undefined) ? code : airlineFullName;
  }
  backToList() {
    //this.location.back();
  }
  getCancellationPolicyText(hotelOption: any) {
    if (hotelOption.cancellationPolicyInfo
      && hotelOption.cancellationPolicyInfo != null
      && hotelOption.cancellationPolicyInfo.trim().length > 0) {
      return this.translateService.instant('optionSelection.CancellationPolicy') + ": "
        + hotelOption.cancellationPolicyInfo.toLowerCase();
    }
    if (hotelOption.hotelRooms && hotelOption.hotelRooms != null
      && hotelOption.hotelRooms.length > 0
      && hotelOption.hotelRooms[0].hotelRates
      && hotelOption.hotelRooms[0].hotelRates != null
      && hotelOption.hotelRooms[0].hotelRates.length > 0
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies != null
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies.length > 0
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].cancellationDate) {
      let dateStr = new Date(DateUtils.getFormattedDateWithoutTimeZone(
        hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].cancellationDate
      )).toDateString().split(' ');
      return this.translateService.instant('optionSelection.CancellationPolicy').toString()
        + hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].amountAfterCancellation
        + this.translateService.instant('optionSelection.spacefeeafterspace').toString()
        + dateStr[1] + ' ' + dateStr[2];
    }

  }
  getRatingStarsMap(hotelStars) {
    return EmailQuoteOptionsService.getHotelRatingStarsMap(hotelStars);
  }
  getStopsUIText(item, legIndex: number) {
    let stopsText = this.translateService.instant('flightChart.nonstop');
    if (item[legIndex].length > 0) {
      if (item[legIndex].length == 1) {
        stopsText = this.translateService.instant('flightSelected.1stop');
      } else {
        stopsText = item[legIndex].length + this.translateService.instant('flightSelected.spacestops');
      }

      let stopAirports = undefined;
      for (let layover of item[legIndex]) {
        if (stopAirports) {
          stopAirports = stopAirports + ', ' + layover.in;
        } else {
          stopAirports = layover.in;
        }
      }
      stopsText = stopsText + ' ' + stopAirports;
    }
    return stopsText;
  }
  getPrice(item): any {
    if (item.option.flight_option.discountedPrice) {
      return item.option.flight_option.discountedPrice;
    } else {
      if(item.option.flight_option.displayPrice){
        return item.option.flight_option.displayPrice;
      }else{
      return item.option.flight_option.price;
    }
  }
  }


  setPrice(item, newTransactionAmount): any {
    if (newTransactionAmount.discountedPrice) {
      item.option.flight_option.discountedPrice = newTransactionAmount.discountedPrice;
    } else {
      item.option.flight_option.price = newTransactionAmount.price;
    }

    if (item.option.flight_option.discountedPrice) {
      return item.option.flight_option.discountedPrice;
    } else {
      return item.option.flight_option.price;
    }
  }
  //getNoOfHops(flightLegIndex:number):number{
  //if(this.flight.legs[flightLegIndex].flightHops) {
  //return this.flight.legs[flightLegIndex].flightHops.length;
  //}
  //}
  getHotelPolicy(hotel): string {
    let roomPolicy = '';
    let policyArray = hotel.option.hotel_option.hotelRooms[0].hotelRates[0].cancellationPolicies;
    if (policyArray) {
      for (let policy of policyArray) {
        if (policy.cancellationRule) {
          roomPolicy = roomPolicy + policy.cancellationRule + ',';
        }
      }
      return roomPolicy;
    } else {
      return null;
    }
  }
  onCancel(){
    this.bsModalRef.hide();
  }
  
  
  ngOnDestroy() {
    this.queryParamSubscription.unsubscribe();
    if(this.companyApprovalSubscription){
      this.companyApprovalSubscription.unsubscribe();
    }
    // this.adminPanelService.companySaveReport = undefined;
  }
  getTripDetails(){
    return this.tripDetails
  }
  getDetailsResponse(){
    return this.bookingHistoryDetailResp.data;
  }
  findTrip(trip){
    if(trip.tripSessionName){
      return trip.tripSessionName;
    }else{
      if(trip.type == "flight"){
        let to = ''
        let hops = trip.option.flight_option.legs[0].hops
        if(hops.length > 0){
          const index = hops.length - 1;
          to = hops[index].to;
        }else{
          to = hops[0].to;
        }
        if(this.airports[to] == undefined){
          return to;
        }else{
          return this.airports[to].name;
        }
      }else if(trip.type == "hotel"){
        return trip.option.hotel_option.address;
      }else if(trip.type == "cars"){
        return trip.option.car_option.dropOffLocation;
      }
    }
    
  }

  getReviewedBy(){
    let reviewedBy = this.selectedDetail.reviewedBy;
    if(reviewedBy){
      let employeeObj = this.bookingHistoryDetailResp.data.employee;
      if(employeeObj[reviewedBy] != undefined){
        if(employeeObj[reviewedBy].userName != null){
          return employeeObj[reviewedBy].userName;
        };
      };
    };
    return reviewedBy;
  };
}
