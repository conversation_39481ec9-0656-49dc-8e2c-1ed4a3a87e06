@import "../../variables.scss";

.flight-row{
  box-shadow: none;
}

.title {
  // height: 30px;
  margin-bottom: 0px !important;
  margin-top: 0px !important;
  color: #413E3B;
  font-family: "apercu-r";
  font-size: 22px;
  line-height: 25px;
}
.flight-layover-right {
  font-size: 16px;
}

.flight-box-right {
  padding-left: 18px;
  width: 100%;
}
.block-flight-trip-type {
  color: gray !important;
  font-size: 10px !important;
  text-transform: none !important;

}
.in-policy {
  color: rgb(136, 194, 94);
  font-family: "apercu-r"
}
.flight-timings-line2{
  position: absolute;
  height: 1px;
  width: calc(100% - 165px);
  left: 155px;
  content: "";
  background: #979797;
  top: 10px;
  z-index: 1;
}
.flight-timings-line2:after {
  position: absolute;
  content: '';
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 10px solid #979797;
  right: -2px;
  top: -4px;
}
.out-policy {
  color: rgb(249, 61, 48);
  font-family: "apercu-r"
}

.dot{
  color: #A6A5A4;
  margin-top: 10px;
  height: 5px;
  width: 5px;
  margin-right: 5px;
  background-color: #bbb;
  border-radius: 50%;
  display: inline-block;
}
.detailcity{
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  color: #817E7B;
  line-height: 18px;
  text-align: left;
}
.hotel-amenities-header {
  float: left;
  width: 100%;
  margin-bottom: 18px;
  display: flex;
  justify-content: space-between;
}
.flight-row-left{
   background: #fff !important;
   box-shadow: -1px 1px 10px 0 #D4D4D4;
}
.hotel-amenities-header h5 {
  font-size: 15px;
  line-height: 15px;
}
.flight-timings {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.flight-timings-section {
  position: relative;
  z-index: 2;
}

.flight-time {
  font-size: 21px;
  line-height: 20px;
  font-family: "apercu-b";
  display: inline-block !important;
  position:relative;
}
.hotel-amenities ul {
  width: 100%;
  max-width: 330px;
  display: inline-block;
  text-align: left;
}


.hotel-amenities ul li {
  display: inline-block;
  width: 100%;
  font-size: 12px;
  line-height: 15px;
  margin-bottom: 9px;
}

.hotel-amenities ul li img {
  margin-right: 10px;
}

.date-duration {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 7px 12px;
}
.flight-feature-list {
  text-align: left;
  display: flex;
  justify-content: space-between;
  float: left;
  width: 100%;
  padding: 8px 25px 24px 25px;
}
.fare-attr-red {
  margin-right: 5px;
  display: inline-block;
}
.flight-feature-list ul li {
  line-height: 15px;
}
.flight-feature-list {
  text-align: left;
  display: flex;
  justify-content: left;
  float: left;
  width: 100%;
  margin: 5px auto;
  padding: 0px;
}
.flight-feature-list li{
  margin: 1px;
}
.flight-feature-list ul li span{
  color: #1c1a1a;
  font-size: 10px;
  display: inline-block;
  margin: 2px 10px 2px 9px;
  vertical-align: middle;
  font-size: 13px;
  font-weight: 700;
}

.flight-feature-list ul li img {
  display: inline-block;
  vertical-align: middle;
  width: 15px;
}
.ffnSoucrname{
  max-width: 800px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: block;
}
.flight-facilities ul li span {
  font-size: 16px;
  line-height: 12px;
  color: #A6A5A4;
  display: inline-block;
  margin-left: 10px;
  vertical-align: middle;
}

.flight-facilities ul li.not-active .red-img {
  display: inline-block;
}
.dotBlack{
  color: black;
  margin-top: 2px;
  height: 5px;
  width: 5px;
  margin-left: 10px;
  background-color: black;
  border-radius: 50%;
  display: inline-block;
}
.fare-attr-red {
  color: rgb(249, 61, 48);
  font-size: small;
  font-weight: lighter;
  width: 10px;
}
.flight-airport {
  font-size: 14px;
  color: #817E7B;
  margin-top: 0px;
  max-width: 500px;
  text-overflow: ellipsis;
  overflow: hidden;
  display: block;
  white-space: nowrap;
}

.block {
  display: block;
}
.flight-box {
  float: left;
  width: 100%;
  display: flex;
  font-family: $fontRegular;
  padding: 12px 15px 7px 3px;
}
.btn-secondary {
  height: 40px;
  width: 250px;
  background-color: var(--button-bg-color) !important;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
  margin-top: 15px;
  margin-bottom: 0px;
  border: none;
  white-space: normal !important;
}
.showNumber{
  top: 25px;
  position: absolute;
  left:auto;
  font-size: 12px;
  color: #A7A7A7;
  right:10px
}
.labelClass{
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 250px;
  display: inline-block;
  font-weight: normal;
}
.add {
  height: 24px;
  width: 188px;
  color: var(--button-font-color);
  font-family: "apercu-r";
  font-size: 13px;
  font-weight: bold;
  letter-spacing: 0.6px;
  line-height: 25px;
  text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}
.card-div-inner {
  padding: 20px 25px 10px !important;
}
.crossTag {
  color: var(--dark-bg-color) !important;
  cursor: pointer;
  position: relative;
  top: -29px;
    right: 0px;
    float: right;
    left: -45px;

}
.TagShowDropdown{
  width:303px;position: relative;margin-right: 5px;
}
.view11 {
  position: absolute;
  top: 8px;
  margin-left: 45px;
}

.approvalButtonsBlock {
  display: inline-block;
    margin-top: 15px;
    margin-left: auto;
    margin-right: auto;
}


.approveButtonDiv{
  text-align: center;
  margin-top: 20px;
}

.input {
  cursor: pointer;
  text-align: center;
  width: 100%;
  padding-top: 5px;
  padding-left: 25px;
  background: #FCFCFC;
  border: 1px solid #8936F3;
  border-radius: 8px;
  height: 32px;
  text-align: left;
  font-size: 14px;
}
.tagsetDropdwon{
    display: inline-block;
    width:100%;
}
.down-arrow {
  position: absolute;
  top: 46px;
  right: 15px;
}

.image11 {
  width: 45px;
  height: 45px;
}

.view {
  height: 29px;
  width: auto;
  color: var(--dark-bg-color) !important;
  font-family: "apercu-r";
  font-size: 24px;
  line-height: 5px;
  padding-top: 18px;
}

.line1 {
  border-right: 2px solid #000000;
  margin-right: 15px !important;
  margin-left: 10px !important;
}

.back {
  height: 19px;
  width: auto;
  color: #fff !important;
  font-family: "apercu-r";
  font-size: 16px;
  letter-spacing: 0.69px;
  line-height: 20px;
}

.booking-history-heading-link {
  line-height: 10px;
  margin-right: 18px;
}

.top-strip {
  background: $themeColor2;
  float: left;
  width: 100%;
  height: 43px;
}

.booking-history-detail-heading {
  float: left;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 2px 30px;
}

.flight-details {
  margin-top: 20px;

}

.result-card-left {
  display: inline-block;
  width: 130px;
  margin-top: 120px;
  padding-left: 8px;
  margin-bottom: 120px;
}

.door {
  width: 15px !important;
  height: 15px !important;
}

.destination {
  color: rgb(255, 0, 64);
  font-size: 18px;
  font-family: "apercu-r";
}

.summery-passenger {
  color: gray;
  margin-top: 5px;
  font-size: 12px;
}

.card-div-inner {
  width: 100% !important;
}

.departureDate {
  font-size: 16px;
  margin-top: 8px;
}

.result-card-box {
  background: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
  border-radius: 6px;
  font-family: $fontRegular;
  float: left;
  width: 100%;
  margin-bottom: 18px;
}

.flight-detail {
  display: flex;
  justify-content: space-between;
  padding-bottom: 33px;
}

.flight-select-button {
  float: left;
  width: 100%;
}

.stop-div {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  top: 20px;
  z-index: 0;
}

.flight-timings {
  display: flex;
  justify-content: space-between;
}

.flight-timings-section {
  position: relative;
  z-index: 2;
}
.dateDetails{
  font-size: 14px;
  font-family: var(--globalFontfamilyr);font-weight: bold;;
  line-height: 18px
}
.flight-time {
  font-size: 24px;
  line-height: 20px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: none;
  background-color: #fff;
  white-space: nowrap;
  height: 42px;
}

.flight-airport {
  font-size: 14px;
  color: #A6A5A4;
  margin-top: 0px;
}

.block {
  display: block;
}

.stop-box {
  display: inline-block;
  height: 18px;
  width: 18px;
  border: 1px solid #979797;
  background-color: #817E7B;
  position: relative;
  top: 6px;
  border-radius: 100%;
  z-index: 2;
}
.numOfStops{
  color: #fff;
  position: relative;
  top: -4px;
  font-size: 14px;
  left: auto;
}

.flight-timings-div {
  width: calc(100% - 104px);
  position: relative;
}

.flight-timings {
  width: 100%;
  position: relative;
}

.flight-timings-line {
  position: absolute;
  height: 1px;
  width: calc(100% - 155px);
  left: 130px;
  content: "";
  background: #979797;
  top: 10px;
  z-index: 1;
}

.flight-timings-line:after {
  position: absolute;
  content: '';
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 10px solid #979797;
  right: -2px;
  top: -4px;
}

.approval-deatils {
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 20px;
}

.stop-box-container {
  position: relative;
  top: -14px;
}

.stop-box-div {
  height: 20px;
  display: inline-block;
}
.information{
  display: flex;
}
.message {
  font-size: 18px;
  font-family: "apercu-r";
  letter-spacing: 1px;
}
.textInformation {
  font-size: 18px;
  font-family: "apercu-r";
  letter-spacing: 1px;
  margin-left: 0px;
  word-wrap: break-word;
  white-space-collapse: break-spaces;
}
.messageForInformation{
  font-size: 18px;
  font-family: "apercu-r";
  letter-spacing: 1px;
  min-width: 160px;
  white-space: nowrap;
  max-width: 250px;

}

.message1 {
  font-size: 18px;
  font-family: "apercu-r";
  color: #000000;
  letter-spacing: 1px;
  padding-bottom: 15px;
}

.btn-normal {
  height: 40px;
  width: auto;
  background-color: transparent;
  letter-spacing: 1px;
  margin-top: 0px;
  border: none;
  box-shadow: none;
  margin-left: 20px;
}

.add1 {
  height: 24px;
  width: auto px;
  color: var(--hyperlink-color);
  font-family: "apercu-r";
  font-size: 12px;
  font-weight: none;
  letter-spacing: 1px;
  line-height: 25px;
}

.btn-secondary {
  height: 40px;
  width: auto;
  background-color: var(--button-bg-color) !important;
  letter-spacing: 1px;
  margin-top: 0px;
  margin-left: 15px;
  box-shadow: none;
  border: none;
}

.add {
  height: 24px;
  width: auto px;
  color: var(--button-font-color);
  font-family: "apercu-r";
  font-size: 12px;
  font-weight: none;
  letter-spacing: 1px;
  line-height: 25px;
}

.modal-textarea {
  height: 80.46px;
  border: 1px solid #979797;
  border-radius: 6px;
  background-color: #FFF;
  resize: none;
  margin-top: 20px;
}

.input-textfield {
  padding: 5px 15px;
  color: #000000;
  font-size: 12px;
  width: 100%;
}
.approver-info{padding: 10px 30px;}
.approval-details{margin-top: 10px;}
.main-wrapper{padding: 12px;}
@media (max-width: 360px) {
  .stop-box-container {
    display: none !important;

  }
}

@media(max-width:768px) {
  .input {
    cursor: pointer;
    text-align: center;
    width: 100%;
    padding-top: 5px;
    padding-left: 25px;
    background: #FCFCFC;
    border: 1px solid #8936F3;
    border-radius: 8px;
    height: 32px;
    text-align: left;
    font-size: 14px;
  }
  .approval-icon{
    
  }
  
}

@media(max-width:991px){
  .input-textfield {
    max-width: 100% !important;
}
.reviewers-note{padding-left: 50px;padding-top: 10px;}
}
@media (max-width: 767px) {
  .card-div.active {
    width: 100% !important;
    margin-left: auto !important;
    border-radius: 0;
    box-shadow: none;
    margin-right: auto !important;
}
.flight-airport {
  font-size: 14px;
  color: #817E7B;
  margin-top: 0px;
  white-space: nowrap;
  max-width: 250px;
  text-overflow: ellipsis;
  overflow: hidden;
  display: block;
}
.stop-box{
  display: inline-block;
  height: 17px;
  width: 17px;
  border: 1px solid #979797;
  background-color: #817E7B;
  position: relative;
  top: -8px;
  border-radius: 100%;
  z-index: 2;
}
.flight-timings-line {
  position: absolute;
  height: 1px;
  width: calc(100% - 85px);
left: 75px;
  content: "";
  background: #979797;
  top: 10px;
  z-index: 1;
}
.flight-time{
  font-size: 15px;
  line-height: 20px;
  font-family: "apercu-b";
  display: inline-block !important;
  position: relative;
}
.in-policy {
  
  font-size: 22px;
 float: left;
  white-space: nowrap;
  margin-top: 5px;
  margin-left: 5px;
}
.paymentdetailsModal{
  float: right;
color: var(--hyperlink-color);
cursor: pointer;
font-size: 22px;
margin-left: 5px;
}
.out-policy {
  
  font-size: 22px;
  float: left;
  word-wrap: break-word;
  margin-top: 7px;
  margin-left: 0px;
}
.information{
  // display: inline-block;
}
.title{
  line-height: normal;
}
.textInformation {
  font-size: 16px;
  word-break: break-word;
  font-family: "apercu-r";
  letter-spacing: 1px;
  margin-left: 0px;
}
.approval-trip-name{
  font-size: 16px;
  word-break: break-word;
  font-family: "apercu-r";
  letter-spacing: 1px;
  margin-left: 0px;
}
.approveButtonDiv{
  text-align: inherit;
  margin-top: 0px;
}
.tagsetDropdwon{
  display: block;
 // justify-content: space-evenly;
}
.card-div-inner {
  padding: 20px 15px 10px !important;
}
.TagShowDropdown{
  width:auto;position: relative;margin-bottom: 10px;
}
  .btn-normal {
    height: 40px;
    width: auto;
    background-color: transparent;
    letter-spacing: 1px;
    margin-top: 25px;
    border: none;
    box-shadow: none;
    margin-left: 20px;
  }
 
  .tagsetDropdwon{
    margin-right: 30px;
    }
    .down-arrow {
      position: absolute;
      top: 46px;
      right: 15px;
    }

  .btn-secondary {
    height: 40px;
    width: auto;
    background-color: var(--button-bg-color) !important;
    letter-spacing: 1px;
    margin-top: 25px;
    margin-left: 15px;
    box-shadow: none;
    border: none;
  }

  .approvalButtonsBlock {
    display: inline-block;
    margin-top: 15px;
  }

  :host ::ng-deep {

    #chartDepartment .ng-select.ng-select-single .ng-select-container .ng-value-container,
    .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
      overflow: visible !important;
    }

    .ng-dropdown-panel {
      position: absolute !important;
      min-width: 100% !important;
      top: auto !important;
      left: auto !important;
      -ms-transform: translate(0%);
      transform: translate(0%);
    }

    #chartDepartment .ng-select span {
      box-sizing: border-box;
      white-space: nowrap;
      text-overflow: ellipsis !important;
      overflow: hidden !important;
    }

    #chartDepartment .ng-select.ng-select-single .ng-select-container .ng-value-container,
    .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
    //  overflow: hidden !important;
      display: none !important;
      max-width: 220px !important;
      cursor: pointer;
    }
  }

  .result-card-box {
    margin-bottom: 14px;
  }

  .result-card-box-left {
    width: 100%;
  }

  .flight-detail-container {
    padding: 15px 10px 5px;
  }

  .flight-detail {
    padding-bottom: 7px;
  }

  .flight-duration {
    font-size: 10px;
    line-height: 12px;
    margin-top: 5px;
  }

  .flight-airport {
    font-size: 10px;
    margin-top: 1px;
  }

  .result-card-left {
    display: inline-block;
    width: 130px;
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .add {
    height: 24px;
    width: auto px;
    color: var(--button-font-color);
    font-family: "apercu-r";
    font-size: 10px !important;
    font-weight: none;
    letter-spacing: 1px;
    line-height: 25px;
  }

  .add1 {
    height: 24px;
    width: auto px;
    color: var(--secondarybutton-font-color);
    font-family: "apercu-r";
    font-size: 10px !important;
    font-weight: none;
    letter-spacing: 1px;
    line-height: 25px;
  }

  .card-div.active {

   // margin-left: 5px !important;
    border-radius: 0;
    box-shadow: none;
  }

  .flight-time {
    font-size: 12px;
    line-height: 12px;
  }

  .flight-price {
    font-size: 14px;
    letter-spacing: 1.17px;
    min-width: 65px;
  }

  .flight-name-duration {
    width: 60px;
  }

  .flight-timings-line {
    width: calc(100% - 120px);
    content: '';
    top: 6px;
    left: 65px;
  }

  .stop-box-container {
    position: relative;
    top: -6px;
  }

  .stop-box-div {
    line-height: 12px;
    height: 12px;
  }

  .stop-box {
    top: -7px !important;
    height: 7px;
    width: 7px;
  }

  .flight-price-div {
    width: auto;
    padding-left: 10px;
    margin-bottom: 0;
    margin-top: -3px;
  }

  .flight-timings-div {
    width: calc(100% - 60px);
  }

  .flight-name-logo img {
    max-height: 20px;
  }

  .info-div {
    top: auto;
    bottom: 4px;
    right: 10px;
  }
  .reviewers-note{padding-left: 50px;padding-top: 10px;}
  .title{
    line-height: normal;
    line-height: 20px;
    padding-bottom: 10px;
  }
}
@media (max-width: 360px) {
  .stop-box-container {
    display: none !important;

  }
}
@media(max-width:400px){
  .labelDiv {
    white-space: nowrap;
    position: absolute;
      top: 40px;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    display: inline-block;
    max-width: 130px;
    width: 100%;
  }
  .approver-info{padding: 10px 5px;}
  .main-wrapper{padding: 0px;}
  .card-div-inner{padding: 20px 5px 10px !important;}
  .approval-icon{width:25px;height:25px;}
  .title{font-family: var(--globalFontfamilyr);font-weight: bold;;font-size: 16px;font-weight: bolder;padding-bottom: 10px;}
  .reviewers-note{padding-left: 42px;padding-top: 10px;}
  .information2{display: flex !important;}
  .messageForInformation{font-size: 16px;min-width: auto;margin-right: 5px;}
  .approval-trip-name{font-size: 15px;}
  .booking-detail-data{padding: 0 0px 31px 0px }
  .information{display: block;}
}
.booking-img {
  line-height: 12px;
  margin-right: 12px;
  width: 40px;
}
.flight-image{
  background-color: white;
  border: none;
  box-shadow: none;
}
.vertical_dotted_line
{
    border-left: 1px dotted black;
    height: 100%;
    margin-left: 20px;
}