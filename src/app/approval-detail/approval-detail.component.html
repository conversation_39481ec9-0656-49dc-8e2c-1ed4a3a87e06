<div class="main-wrapper" >
    <div class="content">
        <div class="container">
            <div *ngIf="type=='pending'" class="top-strip booking-history-detail-heading" style="margin-bottom: 20px !important;">
                <a class="booking-history-heading-link" rel="tab1" href="javascript:void(0)" (click)="backToApproval()">
                    <img src="assets/images/hotel/backarrow.svg" alt="">
                </a>
                <span class="back" style="color:#fff !important"> {{'approvalDetail.Back' | translate }}</span>
            </div>
            <div *ngIf="!responnsecame " class="row" style="text-align: center;padding-top:30px;">
                <app-loader style="margin-right: auto;margin-left: auto;" [spinnerStyle]="true"></app-loader>
            </div>
            <div  *ngIf="!waitData  && responnsecame && isSingleDetails"  class="row approver-info">
                <div class="col-lg-6 col-sm-12" *ngIf="!this.disApprovedReason && !this.approvedReason" >
                        <div class="title"> {{this.travellerName}}'s  {{'approvalDetail.itineraryforApproval' | translate }}</div>
                        <div class="textInformation" *ngIf="this.approvedReason">{{'dashboard.ApprovedOn' | translate }} {{this.selectedDetail.reviewedOn | date:'MMM d, y'}}</div>
                        <div class="title" *ngIf="this.disApprovedReason">Disapproved on {{this.selectedDetail.reviewedOn | date:'MMM d, y'}}</div>
                        <div class="textInformation" *ngIf="this.disApprovedReason || this.approvedReason"><span>By</span> {{getReviewedBy()}}</div>
                </div>

                <div class="col-lg-6 col-sm-12" *ngIf="this.approvedReason && !this.disApprovedReason ">
                    <div class="d-flex">
                        <div style="margin-right: 10px;" class="approval-icon">
                            <img src="assets/images/approve-icon.svg" alt="">
                        </div>
                        <div style="width: 90%;" >
                            <div class="title" style="margin-top: 0px !important;margin-bottom: 0px !important;"> {{this.travellerName}}'s  Approved itinerary</div>
                            <div class="textInformation" *ngIf="this.approvedReason">{{'dashboard.ApprovedOn' | translate }} {{this.selectedDetail.reviewedOn | date:'MMM d, y'}}</div>
                            <div class="textInformation" *ngIf="this.disApprovedReason || this.approvedReason"><span>By</span> {{getReviewedBy()}}</div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-sm-12" *ngIf="this.disApprovedReason && !this.approvedReason">
                    <div class="d-flex">
                        <div style="margin-right: 10px;" class="approval-icon">
                            <img src="assets/images/disapproved-icon.svg" alt="">
                        </div>
                        <div style="width: 90%;">
                            <div class="title" style="margin-top: 0px !important;margin-bottom: 0px !important;"> {{this.travellerName}}'s  Disapproved itinerary</div>
                            <div class="textInformation" *ngIf="this.disApprovedReason">Disapproved on {{this.selectedDetail.reviewedOn | date:'MMM d, y'}}</div>
                            <div class="textInformation" *ngIf="this.disApprovedReason || this.approvedReason"><span>By</span> {{getReviewedBy()}}</div>
                        </div>
                    </div>                      
                </div>
                
                <div *ngIf="this.selectedDetail && this.selectedDetail.adminNote && this.selectedDetail.adminNote !=='' " class="col-lg-6 col-sm-12 reviewers-note">
                    <div style="font-family: var(--globalFontfamilyr);font-weight: bold;;">{{'approvalDetail.ReviewersNote' | translate }}:</div>
                    <div class="textInformation">
                        {{this.selectedDetail.adminNote}}
                    </div>
                </div>

            </div>
           
            <div style="text-align: center" *ngIf="this.errormsg !=='' && responnsecame">{{this.errormsg}}</div>

            <div *ngIf="!waitData && responnsecame && this.errormsg == ''" class="card-div active shadow" style="margin-top:20px !important;">
                <div class="card-div-inner" [ngStyle]="{'padding-bottom':this.dropDownopen ?'300px':'10px'}">
                        <div *ngFor="let item of this.detailList ;let i=index">
                                <div *ngIf=" i == 0" style="margin-top: 5px" class="approval-trip-name">
                                    <p style="font-family: var(--globalFontfamilyr);font-weight: bold;;"> {{this.travellerName}}'s Trip: {{findTrip(item)}}</p>  
                                </div>

                                <div *ngIf="(this.employeedetail && i == 0) && (!this.disApprovedReason && !this.approvedReason)" style="margin-top: 5px" class="information">
                                    <div class="messageForInformation" style="font-family: var(--globalFontfamilyr);font-weight: bold;;">{{'approvalDetail.Policystatus' | translate }}</div>  
                                    <div class="textInformation" *ngIf="this.detailList.length == 1">As per your company’s travel policy, this booking need to go to the admin for approval.</div>
                                    <div class="textInformation" *ngIf="this.detailList.length > 1">As per your company’s travel policy, all bookings need to go to the admin for approval.</div>
                                </div>

                                <div *ngIf="selectedDetail.noteToAdmin && i == 0" style="margin-top: 5px;" class="information">
                                    <div class="messageForInformation" style="font-family: var(--globalFontfamilyr);font-weight: bold;;">{{'approvalDetail.Travelersnote' | translate }}</div>  
                                    <div class="textInformation" >{{selectedDetail.noteToAdmin}} </div>
                                </div>

                                <div *ngIf="this.employeedetail && i == 0" style="margin-top: 5px" class="information information2">
                                    <div class="messageForInformation" style="font-family: var(--globalFontfamilyr);font-weight: bold;;">{{'approvalDetail.Bookername' | translate }}</div>  
                                    <span class="textInformation" >{{this.employeedetail.userName}}</span>
                                </div>
                                
                                <div *ngIf="this.employeedetail && i == 0" style="margin-top: 5px;padding-bottom: 10px" class="information information2">
                                    <div class="messageForInformation" style="font-family: var(--globalFontfamilyr);font-weight: bold;;">{{'approvalDetail.Bookeremail' | translate }}</div>  
                                    <span class="textInformation" >{{this.employeedetail.userId}}</span>
                                </div>

                                <app-trip-details-card 
                                [details]="item" 
                                [tripDeatilsIndex]="i"
                                [detailList]="this.detailList" 
                                [newtripDetails]="getTripDetails()" 
                                [showDetailsActionButtons]="false"
                                [getFullDetailsResponse]="getDetailsResponse()"
                                [forApproval]="true"
                                ></app-trip-details-card>

                            </div>
                            <div *ngIf="this.alreadyApproved || this.alreadyRejected || this.alreadyExpired">
                                <div *ngIf="this.alreadyApproved">
                                    <div class="message1">
                                        {{'approvalDetail.Followingitinerarywasalreadyapproved.' | translate }}
                                    </div>
                                </div>
                                <div *ngIf="this.alreadyRejected">
                                    <div class="message1">
                                        {{'approvalDetail.Followingitinerarywasalreadyrejected.' | translate }}
                                    </div>
                                </div>
                                <div *ngIf="this.alreadyExpired">
                                    <div class="message1">
                                        {{'approvalDetail.Followingitineraryhasexpired.' | translate }}
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="!(this.alreadyApproved || this.alreadyRejected || this.alreadyExpired) && !this.showAllReadyHandeled">
                                <div *ngIf="this.approvedReason" class="approval-details">
                                    <div *ngIf="!this.availabilityError" class="message1">
                                        {{'approvalDetail.Congratulations' | translate }}<br>
                                        {{'approvalDetail.Followingitineraryhasbeenapproved.' | translate }} 
                                        {{this.travellerName}} {{'approvalDetail.willshortlyreceivetheconfirmationemail.' | translate}}
                                    </div>
                                    <div *ngIf="this.availabilityError" class="message1">
                                        {{'approvalDetail.Theitineraryisnomoreavailableand' | translate }}
                                        {{this.travellerName}} {{'approvalDetail.hasbeeninstructedtobookagain.' | translate }}
                                    </div>
                                    <div *ngIf="!this.isTransactionBookingConfirmed && !this.availabilityError" class="message1">
                                        {{'approvalDetail.Bookingisinprogress' | translate }} {{this.travellerName}}
                                        {{'approvalDetail.willbeinformedwiththestatusshortly.Foranyquestionspleasecontactsupport' | translate }}
                                    </div>
                                </div>
                                <div *ngIf="this.disApprovedReason" class="approval-details">
                                    <div class="message1">
                                        {{'approvalDetail.Thedisapprovalnotehasbeensentto' | translate }}
                                        {{this.travellerName}}
                                        {{'approvalDetail.andthereservationactionforthefollowingitineraryhasbeencancelled.' | translate }}
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="this.showAllReadyHandeled">
                                <div class="message1">
                                    {{'approvalDetail.approvalishandeled' | translate }} 
                                </div>
                            </div>
                            <div *ngIf="isExpiredApproval() && !this.showAllReadyHandeled">
                                <div class="message"> {{'approvalDetail.TheapprovalperiodhasEXPIREDand' | translate }} {{this.travellerName}}  {{'approvalDetail.isinformedtobookanalternativeoption.' | translate }}</div>
                            </div>
                            <div *ngIf="!isExpiredApproval() && !this.showAllReadyHandeled">
                                <div *ngIf="!this.disApprovedReason && !this.approvedReason" class="approval-details">
                                    <div class="approvalButtonsBlock">
                                        <div *ngIf="tagset && tagset.length > 0" class="tagsetDropdwon">

                                            <app-dropdown 
                                            [dropDownopen]="dropDownopen" 
                                            [tagset]="tagset" 
                                            [selectedTag]="selectedTag"
                                            [tagShow]="tagShow" 
                                            [changing]="changingValue" 
                                            [newTagSetsArray]="tagsSetData.tags"
                                            (goBackDropDownOptions)="handleResponseFromDropdwn($event)">
                                            </app-dropdown>

                                        </div>
                                        <div class="message">{{'approvalDetail.PleaseclickApproveTraveltoapprovethereservationoffollowingitineraryandbookthetravelorclickDisapproveifyoudonotwanttoapprovethisreservation.'| translate }}</div>
                                        <div style="display: grid;position: relative;">
                                            <textarea 
                                                placeholder="{{'approvalDetail.Typeanoteforthetraveleroptional' | translate }}"
                                                maxlength="512" 
                                                [(ngModel)]="messageForDisapprove" 
                                                class="modal-textarea input-textfield"
                                                (focus)="this.error=false;" style="position: relative;padding-right: 55px;padding-top: 20px;"
                                                [attr.disabled]="this.disableSendNoteBtn ? true : null" 
                                                maxlength="512">
                                            </textarea>
                                            <span class="showNumber">
                                                {{messageForDisapprove.length}}/512
                                            </span>
                                            <div *ngIf="this.error" class="text-danger"> 
                                            {{'approvalDetail.Pleaseenterdisapprovalnote' | translate}}.
                                            </div>
                                        </div>
                                        <div class="approveButtonDiv">
                                            <button class="btn btn-secondary" (click)="approveed('approve',thankyoumodal)" [disabled]="this.disableApprovalBtn">
                                                <span class="add"> {{'approvalDetail.APPROVETRAVEL' | translate}}</span>
                                            </button>
                                            <button class="btn btn-normal" (click)="approveed('disapprove',thankyoumodal)" [disabled]="this.disableApprovalBtn">
                                                <span class="add1"> {{'approvalDetail.DISAPPROVE' | translate}}</span>
                                            </button>
                                            <app-loader *ngIf="this.disableSendNoteBtn" [spinnerStyle]="true"></app-loader>
                                        </div>
                                    </div>
                                </div>
                            
                            </div>
                        <hr>
                   
                </div>
            </div>
                <ng-template #thankyoumodal let-modal>
                        <div class="table-view">
                            <div class="table-cell-view">
                                <div class="modal-dialog modal-dialog-md" role="document">
                                    <div class="modal-content">
                                       
                                        <div class="modal-header">
                                                <span>
                                                        <img class="footerimage" [src]="this.searchService.footerLogo">
                                                      </span>
                                            <button *ngIf="!this.disabled" type="button" class="close" data-dismiss="modal"
                                                (click)="onCancel()">
                                                <i class="material-icons">close</i>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                           
                    
                                            <div class="modal-content-text modal-content-width">
                                               {{ 'employee.ThankyouforresolvingthisapprovalrequestThereare' | translate}} <span style="margin-left:5px;margin-right: 5px;">{{this.pendingApprovals.length}}</span>
                                              <span>{{ 'employee.morerequestspendingforapproval' | translate}}</span>
                                            </div>
                                          
                                            <div class="modal-content-button" [ngStyle]="{'margin-bottom': this.disabled ? '20px':'44px'}">
                                                <button class="btn btn-secondary"
                                                    (click)="routeToApproval('approvals','pending',false)"
                                                    [disabled]="this.disabled"><span class="add">{{'employee.ReviewPendingREQUEST' | translate}}</span>
                                                </button>
                                              
                                            </div>
                                          
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                    <ng-template #tripDetails let-modal>
                            <div class="modal-header" style="padding: 0px !important;
                            padding-right: 5px !important;">
                                    <button type="button" class="close" data-dismiss="modal"   (click)="onCancel()">
                                        <i class="material-icons">close</i>
                                    </button>
                                </div>
                                            <div class="modal-body" style="background-color: #FFF !important;">
                                                    <div class="flight-row detailed" style="background-color: #FFF !important;box-shadow: none !important;">
                                                            <div class="flight-row-left" style="padding: 10px;box-shadow: 0px 0px 1px 0 #D4D4D4;">
                                                                    <ng-container *ngIf="tripSelectedToshowOnpopUp.type==='flight'">
                                                                            <div class="date-duration" style="justify-content: unset;padding-left: 0px !important;">
                                                                                  <label class="date" style="margin-right: 4px;">{{'option.Traveltime' |
                                                                                        translate}}
                                                                                        </label>
                                                                                  <span class="date">  {{getDuration(this.tripSelectedToshowOnpopUp,0)?.hrs
                                                                                        + 'h ' + getDuration(this.tripSelectedToshowOnpopUp,0)?.mins + 'm'}}</span>
                                                                                     <span class="dotBlack"></span>
                                                                                   
                                                                                     <span class="block flight-airport" style="margin-left: 5px;"
                                                                                     *ngIf=" !tripSelectedToshowOnpopUp.hops[legIndex] && tripSelectedToshowOnpopUp.hops.length==0">{{'flightChart.nonstop'
                                                                                     | translate}}</span>
                                                                                 <span class="block flight-airport"
                                                                                     *ngIf="tripSelectedToshowOnpopUp.hops.length>0 && tripSelectedToshowOnpopUp.hops.length > 0">{{getStopsUIText(tripSelectedToshowOnpopUp.hops,0)
                                                                                     | translate}}</span>
                                                                                    
                                                                               
                                                                              
                                                                            </div>
                                                                         <div class="flight-schedule">
                                                                            <ng-container *ngFor="let hop of this.tripSelectedToshowOnpopUp.hops; let hopIndex = index">
                                                                               <div class="flight-box">
                                                                                  <div class="flight-box-left" style="max-width: 63px;">
                                                                                     <img
                                                                                        onerror="this.onerror = null; this.src = 'https://s3.amazonaws.com/images.biztravel.ai/template/default.gif';"
                                                                                        [src]="getFlightIconURL(hop.carrier)" />
                                                                                  </div>
                                                                                  <div class="flight-box-right">
                                                                                        <div class="row" style="margin-top: 10px;">
                                                                                        <div class="col-6" style="text-align: left;">
                                                                                                <div class="date">{{hop.carrier}} {{hop.flightNumber}}</div>
                                                                                        </div>
                                                                                        <div  class="col-6"  style="color:#817E7B;text-align: left;">
                                                                                              <p> {{hop.fareClassName}}
                                                                                                    {{getBrandText(hop.brandClass)}}</p>
                                                                                           </div>
                                                                                        </div>
                                                                                  
                                                                                     <div class="row" style="margin-top: 10px;">
                                                                                        <div class="col-6" style="text-align: left;">
                                                                                              <div
                                                                                              class="date">{{this.airlines[hop.carrier]}}</div>
                                                                                           <div style="margin-top: 10px;"
                                                                                           class="block flight-time">{{getDisplayFlightDate(hop.starts,
                                                                                            'h:mm a')}} <span
                                                                                           class="extra-time1"></span></div>
                                                                                           <div class="flight-timings-lineDetail"></div>
                                                                                           <div class="date" style="font-size: 14px;margin-top: 10px;">Departure</div>
                                                                                           <div class="detailcity" style="text-align: left;line-height: 18px"
                                                                                           >{{hop.from}} - {{this.airports[hop.from].name}}</div>
                                                                                           <div class="dateDetails" style="text-align: left;">{{getDisplayFlightDate(hop.starts,
                                                                                                'EEEE, MMMM d, y')}}
                                                                                              </div> 
                                                                                           </div>
                                                                                           
                                                                                           <div class="col-6"style="text-align: left;" >
                                                                                                 <div
                                                                                                 class="date">{{getDurationOfhop(hop)}}</div>
                                                                                           <div style="margin-top: 10px;"
                                                                                           class="block flight-time">{{getDisplayFlightDate(hop.ends,
                                                                                            'h:mm a')}}   <span class="extra-time"></span></div>
                                                                                           <div class="date" style="font-size: 14px;margin-top: 10px;">Arrival</div>
                                                                                           <div class="detailcity" style="line-height: 18px"
                                                                                           >{{hop.to}} - {{this.airports[hop.to].name}}</div>
                                                                                           <div class="dateDetails">{{getDisplayFlightDate(hop.ends,
                                                                                                'EEEE, MMMM d, y')}}
                                                                                              </div> 
                                                                                           </div>
                                                                                          
                                                                                       </div>
                                                                                      
                                                                                   
                                                                                   
                                                                                  </div>
                                                                               </div>
                                                                               <div class="flight-layover" *ngIf="this.tripSelectedToshowOnpopUp.hops[hopIndex+1] && this.tripSelectedToshowOnpopUp.hops[hopIndex+1].duration" style="justify-content: center;">
                                                                                     <span
                                                                                     class="flight-layover-right date">{{getHopDuration(this.tripSelectedToshowOnpopUp.hops[hopIndex+1].duration).hrs + 'h ' + getHopDuration(this.tripSelectedToshowOnpopUp.hops[hopIndex+1].duration).mins + 'm'}}</span> <span class="dotBlack" style="margin-right: 4px;margin-left: 4px;;"></span>
                                                                                  <span class="flight-layover-left">{{'flightChart.Layoverin' | translate}}
                                                                                     {{hop.to}}</span>
                                                                                  
                                                                               </div>
                                                                            </ng-container>
                                                                         </div>
                                                                      </ng-container>
                                                                      <ng-container *ngIf="tripSelectedToshowOnpopUp.type==='car'">
                                                                            <div class="date-duration" style="justify-content: unset;">
                                                                                  <label class="date" style="margin-right: 4px;">{{tripSelectedToshowOnpopUp.description}}
                                                                                        </label>
                                                                                
                                                                              
                                                                            </div>
                                                                         <div class="flight-schedule">
                                                                            <ng-container>
                                                                               <div class="flight-box">
                                                                                  <div class="flight-box-left" style="max-width: 63px;">
                                                                                      <div>
                                                                                        <img style="width:130px !important;" *ngIf="carUrl(tripSelectedToshowOnpopUp)"
                                                                                        src="{{carUrl(tripSelectedToshowOnpopUp)}}"
                                                                                        onerror="this.src='https://s3.amazonaws.com/images.biztravel.ai/ota/carrentals/vehicle_notavailable_S.jpg' " />
                                                                                      </div>
                                                                                      <div> <img
                                                                                        style="margin-top:5px;height:auto!important;width:100px !important;border-radius: 6px"
                                                                                        *ngIf="tripSelectedToshowOnpopUp.partnerLogo" src="{{ tripSelectedToshowOnpopUp.partnerLogo }}" />
                                                                                </div>
                                                                                  </div>
                                                                                  <div class="flight-box-right">
                                                                                  
                                                                                     <div class="row" style="margin-top: 10px;">
                                                                                        <div class="col-6" style="text-align: left;">
                                                                                              <div
                                                                                              class="date" style="font-size: 14px;white-space: nowrap;">{{tripSelectedToshowOnpopUp.carMake}}</div>
                                                                                           
                                                                                           <div class="date" style="font-size: 14px;margin-top: 10px;">{{tripSelectedToshowOnpopUp.pickUpLocationType}} {{'carresult.Pick-up' | translate }}</div>
                                                                                           <div class="detailcity" style="text-align: left;line-height: 18px"
                                                                                           >{{tripSelectedToshowOnpopUp.pickUpLocation}}</div>
                                                                                           <div class="dateDetails" style="text-align: left;">{{
                                                                                            getLocationType1(tripSelectedToshowOnpopUp.locationInformation) }}
                                                                                              </div> 
                                                                                              <div style="color:#9C4AF6;margin-top: 10px;">
                                                                                                {{getPerDayPrice(tripSelectedToshowOnpopUp) | currency : getCurrencySymbol(tripSelectedToshowOnpopUp.currency)
                                                                                                    : 'code' : "1.0-0"}} {{'carresult.avgday' | translate }}
                                                                                              </div>
                                                                                              <div style="margin-top: 10px;">
                                                                                                <li >
                                                                                                    <img
                                                                                                    *ngIf="tripSelectedToshowOnpopUp.policy"
                                                                                                    src="../../../assets/images/hotel/policy-active.svg">
                                                                                                 
                                                                                                    <img
                                                                                                    *ngIf="!tripSelectedToshowOnpopUp.policy"
                                                                                                    src="../../../assets/images/hotel/policy.svg">
                                              
                                              
                                                                                                 <span style="position: relative;top:
                                                                                                 2px;left:3px;">{{'flightChart.Policy' | translate}}</span>
                                              
                                                                                              </li>
                                                                                              </div>
                                                                                           </div>
                                                                                           
                                                                                           <div class="col-6"style="text-align: left;" >
                                                                                            <div
                                                                                              class="date"></div>
                                                                                           <div class="date" style="font-size: 14px;margin-top: 30px;">{{tripSelectedToshowOnpopUp.dropLocationType}} {{'carresult.Drop-off' | translate }}</div>
                                                                                           <div class="detailcity" style="line-height: 18px"
                                                                                           ><span
                                                                                           *ngIf="tripSelectedToshowOnpopUp.pickUpLocation !== tripSelectedToshowOnpopUp.dropOffLocation">
                                                                                           {{tripSelectedToshowOnpopUp.dropOffLocation}}</span><span
                                                                                           *ngIf="tripSelectedToshowOnpopUp.pickUpLocation === tripSelectedToshowOnpopUp.dropOffLocation"> {{'option.Sameaspick-up' | translate}}</span></div>
                                                                                          
                                                                                           </div>
                                                                                          
                                                                                       </div>
                                                                                      <hr *ngIf="tripSelectedToshowOnpopUp.type==='car' && this.isMobile1">
                                                                                       <div *ngIf="tripSelectedToshowOnpopUp.type==='car' && this.isMobile1" style="text-align: left;">
                                                                                            <div *ngIf="tripSelectedToshowOnpopUp.milage"
                                                                                            class="hotel-distance-address">
                                                                                            <i class="fa fa-tachometer" aria-hidden="true"></i><span
                                                                                                style="margin-left:4px;">{{'carresult.Unlimitedmileage' | translate}}</span>
                                                                                        </div>
                                                                                        <div *ngIf="tripSelectedToshowOnpopUp.automaticTransmission"
                                                                                            class="hotel-distance-address">
                                                                                            <span class="inlineblock icon-margin "><img class="door"
                                                                                                    src="assets/images/transmission_icon.png"></span><span
                                                                                                style="margin-left:4px;">{{'carresult.Automatictransmission' | translate}}</span>
                                                                                        </div>
                                                                                        <div *ngIf="tripSelectedToshowOnpopUp.doors"
                                                                                                    class="duration-section car-duration" style="display: block !important;">
                                                                                                    <span class="inlineblock icon-margin "><img class="door"
                                                                                                            src="assets/images/car_gate.svg"></span>
                                                                                                    <span class="inlineblock"
                                                                                                        style="margin-left:4px !important;">{{tripSelectedToshowOnpopUp.doors}}</span>
                                                                                                </div>
                                            
                                                                                                <div *ngIf="tripSelectedToshowOnpopUp.passengers"
                                                                                                    class="duration-section car-duration" style="display: block !important;">
                                                                                                    <span class="inlineblock icon-margin "><i class="fa fa-user"
                                                                                                            aria-hidden="true"></i></span>
                                                                                                    <span class="inlineblock"
                                                                                                        style="margin-left:4px !important;">{{tripSelectedToshowOnpopUp.passengers}}</span>
                                                                                                </div>
                                            
                                                                                                <div *ngIf="tripSelectedToshowOnpopUp.bags"
                                                                                                    class="duration-section walk-duration" style="display: block !important;">
                                                                                                    <span class="inlineblock icon-margin"><i class="fa fa-suitcase"
                                                                                                            aria-hidden="true"></i></span>
                                                                                                    <span class="inlineblock "
                                                                                                        style="margin-left:4px !important;">{{tripSelectedToshowOnpopUp.bags}}</span>
                                                                                                </div>
                                                                                         </div>
                                                                                   
                                                                                  </div>
                                                                               </div>
                                                                              
                                                                            </ng-container>
                                                                         </div>
                                                                      </ng-container>
                                                                      <ng-container *ngIf="tripSelectedToshowOnpopUp.type==='hotel'">
                                                                        <div class="date-duration" style="justify-content: unset;padding-left: 1px !important;">
                                                                              <label class="date" style="margin-right: 4px;text-align: left;">{{tripSelectedToshowOnpopUp.hotelRooms[0].hotelRoomName}}
                                                                                    </label>
                                                                            
                                                                          
                                                                        </div>
                                                                     <div class="flight-schedule">
                                                                        <ng-container>
                                                                           <div class="flight-box">
                                                                              <div class="flight-box-left" style="min-width: 63px;max-width: 63px;">
                                                                                <img onerror="this.onerror=null;this.src='assets/images/hotel/hotel.png'"
                                                                                *ngIf="tripSelectedToshowOnpopUp.thumbnailImage"
                                                                                src="{{tripSelectedToshowOnpopUp.thumbnailImage.url}}" />
                                                                            <img *ngIf="!tripSelectedToshowOnpopUp.thumbnailImage"
                                                                                src="assets/images/hotel/hotel.png" />
                                                                              </div>
                                                                              <div class="flight-box-right">
                                                                              
                                                                                 <div class="row" style="margin-top: -4px;">
                                                                                    <div class="col-12" style="text-align: left;">
                                                                                          <div
                                                                                          class="date" style="">{{tripSelectedToshowOnpopUp.hotelName}}</div>
                                                                                          <div class="date">{{tripSelectedToshowOnpopUp.distanceMile | number : '.2-2'}}
                                                                                                {{'hotelSelect.milesfromyourdestination' | translate}}</div>
                                                                                       
                                                                                       <div class="date" style="font-size: 14px;margin-top: 10px;"> 
                                                                                       </div>
                                                                                       <div class="detailcity" style="text-align: left;line-height: 18px"
                                                                                       >{{tripSelectedToshowOnpopUp.hotelAdress}}</div>
                                                                                       <div class="dateDetails" style="text-align: left;margin-top: 10px;"> <div>
                                                                                        <span class="distance-img-text" style="margin-left: -3px !important;">
                                                                                            <span class="inlineblock_m" style="margin-right: 10px;">
                                                                                                <img class="distance-time-img"
                                                                                                    src="assets/images/emailflow/car-distance.png"
                                                                                                    alt="" />
                                                                                                <span class="distance-time-text">{{tripSelectedToshowOnpopUp.carDistance}}
                                                                                                    {{'option.min' | translate}}</span>
                                                                                            </span>
                                                                                            <span *ngIf="tripSelectedToshowOnpopUp.walkingTime < 30"
                                                                                                class="inlineblock_m">
                                                                                                <img class="distance-time-img"
                                                                                                    src="assets/images/emailflow/walk-distance.png"
                                                                                                    alt="" />
                                                                                                <span
                                                                                                    class="distance-time-text">{{tripSelectedToshowOnpopUp.walkingTime}}
                                                                                                    {{'option.min' | translate}}</span>
                                                                                            </span>
                                                                                        </span>
                                                                                    </div>
                                                                                          </div> 
                                                                                          <div *ngIf="!tripSelectedToshowOnpopUp.loyaltyPointsSupported"
                                                                                          style="color:#f93d30; width:100%;text-align: left;margin-top:15px;"> <img
                                                                                              src="assets/images/flight-list/gen-negative.svg"> {{'option.Noloyaltyrewards' | translate}}
                                                                                      </div>
                                                                                      <div class="dateDetails" style="margin-top: 15px;color: #817E7B !important;">
                                                                                        {{'option.Cancellationpolicy' | translate}}: {{getCancellationPolicy(tripSelectedToshowOnpopUp)}}
                                                                                      </div>
                                                                                          
                                                                                          <div style="margin-top: 10px;">
                                                                                            <li>
                                                                                              
                                                                                                <img
                                                                                               
                                                                                                src="assets/images/hotel/policy{{tripSelectedToshowOnpopUp.inPolicy ? '-active' : ''}}.svg">
                                          
                                          
                                                                                             <span style="position: relative;top:
                                                                                             2px;left:3px;">{{'flightChart.Policy' | translate}}</span>
                                          
                                                                                          </li>
                                                                                          </div>
                                                                                       </div>
                                                                                       
                                                                                       
                                                                                   </div>
                                                                                  
                                                                               
                                                                               
                                                                              </div>
                                                                           </div>
                                                                          
                                                                        </ng-container>
                                                                     </div>
                                                                  </ng-container>
                                                                </div>
                                                    <div [hidden]="tripSelectedToshowOnpopUp.type==='car' && this.isMobile1" class="flight-row-right" [ngStyle]="getDetailsStyleModal()" style="padding-right:6px !important;background-color: #FFF !important;box-shadow: none!important;align-items:  baseline !important;">
                                                            <div class="flight-box" style="padding-top: 0px !important;padding-right: 0px !important;">
                                                                    <div *ngIf="false && this.isMobile1" class="flight-box-left" style="width: 93px;">
                                                                        </div>
                                                                        <div class="flight-box-right">
                                                            <div *ngIf="tripSelectedToshowOnpopUp.type==='flight'"class="flight-feature-list">
                                                                 
                                                                    <ul>
                                                                          
                                                                      
                                                                     
                                                                      
                                                                     
                                                                      
                                                                       <li  >
                                                                            <img
                                                                            *ngIf="this.tripSelectedToshowOnpopUp.withinPricePolicy == true"
                                                                            src="../../../assets/images/policy-green.svg"
                                                                            >
                                                                            <img
                                                                            *ngIf="this.tripSelectedToshowOnpopUp.withinPricePolicy == false"
                                                                               src="../../../assets/images/policy-red.svg"
                                                                               >
                                                                              
                                                                         
                      
                      
                                                                         <span>{{'flightChart.Policy' | translate}}</span>
                      
                                                                      </li>
                                                                      
                                                                      
                                                                      
                                                                      
                                                                    </ul>
                                                                 </div>
                                                                 <div *ngIf="tripSelectedToshowOnpopUp.type==='car' && !this.isMobile1" [ngStyle]="{'margin-left': this.isMobile1 ?  '30px' : '0px;'}"style="text-align: left;">
                                                                    <div *ngIf="tripSelectedToshowOnpopUp.milage"
                                                                    class="hotel-distance-address">
                                                                    <i class="fa fa-tachometer" aria-hidden="true"></i><span
                                                                        style="margin-left:4px;">{{'carresult.Unlimitedmileage' | translate}}</span>
                                                                </div>
                                                                <div *ngIf="tripSelectedToshowOnpopUp.automaticTransmission"
                                                                    class="hotel-distance-address">
                                                                    <span class="inlineblock icon-margin "><img class="door"
                                                                            src="assets/images/transmission_icon.png"></span><span
                                                                        style="margin-left:4px;">{{'carresult.Automatictransmission' | translate}}</span>
                                                                </div>
                                                                <div *ngIf="tripSelectedToshowOnpopUp.doors"
                                                                            class="duration-section car-duration" style="display: block !important;">
                                                                            <span class="inlineblock icon-margin "><img class="door"
                                                                                    src="assets/images/car_gate.svg"></span>
                                                                            <span class="inlineblock"
                                                                                style="margin-left:4px !important;">{{tripSelectedToshowOnpopUp.doors}}</span>
                                                                        </div>
                    
                                                                        <div *ngIf="tripSelectedToshowOnpopUp.passengers"
                                                                            class="duration-section car-duration" style="display: block !important;">
                                                                            <span class="inlineblock icon-margin "><i class="fa fa-user"
                                                                                    aria-hidden="true"></i></span>
                                                                            <span class="inlineblock"
                                                                                style="margin-left:4px !important;">{{tripSelectedToshowOnpopUp.passengers}}</span>
                                                                        </div>
                    
                                                                        <div *ngIf="tripSelectedToshowOnpopUp.bags"
                                                                            class="duration-section walk-duration" style="display: block !important;">
                                                                            <span class="inlineblock icon-margin"><i class="fa fa-suitcase"
                                                                                    aria-hidden="true"></i></span>
                                                                            <span class="inlineblock "
                                                                                style="margin-left:4px !important;">{{tripSelectedToshowOnpopUp.bags}}</span>
                                                                        </div>
                                                                 </div>
                                                                 <div *ngIf="tripSelectedToshowOnpopUp.type==='hotel'">
                                                                    <div class="hotel-amenities">
                                                                       
                                                                        <ul>
                                                                                <li class="inlineblock_m hotel-rating-stars">
                                                                                        <span
                                                                                            *ngFor="let starType of getRatingStarsMap(tripSelectedToshowOnpopUp.rating); let i = index;"
                                                                                            class="star">
                                                                                            <img *ngIf="starType == 'full'" src="assets/images/hotel/star-filled.svg" />
                                                                                            <img *ngIf="starType == 'half'" src="assets/images/hotel/halfstar.png" />
                                                                                            <img *ngIf="starType == 'none'" src="assets/images/hotel/star.svg" />
                                                                                        </span>
                                                                                        <!-- <span><img src="assets/images/hotel/star-filled.svg" alt="star-filled" /></span>
                                                                                        <span><img src="assets/images/hotel/star-filled.svg" alt="star-filled" /></span>
                                                                                        <span><img src="assets/images/hotel/star-filled.svg" alt="star-filled" /></span>
                                                                                        <span><img src="assets/images/hotel/star.svg" alt="star" /></span>
                                                                                        <span><img src="assets/images/hotel/star.svg" alt="star" /></span> -->
                                                                                        </li>
                                                                            <li>
                                                                                <img class="inlineblock_m"
                                                                                    src="assets/images/hotel/local_cafe{{isAmenityAvailable('AMENITY_TYPE_BKFAST') ? '-active' : ''}}.svg"
                                                                                    alt="" />
                                                                                <span class="inlineblock_m">{{'hotelSelect.Breakfast' | translate}}</span>
                                                                            </li>
                                        
                                                                            <li>
                                                                                <img class="inlineblock_m"
                                                                                    src="assets/images/hotel/wifi{{isAmenityAvailable('AMENITY_TYPE_WIFI') ? '-active' : ''}}.svg"
                                                                                    alt="" />
                                                                                <span class="inlineblock_m">{{'hotelSelect.FreeWiFi' | translate}}</span>
                                                                            </li>
                                        
                                                                            <li>
                                                                                <img class="inlineblock_m"
                                                                                    src="assets/images/hotel/fitness-dumbbell{{isAmenityAvailable('AMENITY_TYPE_GYM') ? '-active' : ''}}.svg"
                                                                                    alt="" />
                                                                                <span class="inlineblock_m">{{'hotelSelect.Gym' | translate}}</span>
                                                                            </li>
                                        
                                                                            <li>
                                                                                <img class="inlineblock_m"
                                                                                    src="assets/images/hotel/local_restaurant{{isAmenityAvailable('AMENITY_TYPE_RESTAURANT') ? '-active' : ''}}.svg"
                                                                                    alt="" />
                                                                                <span class="inlineblock_m">{{'hotelSelect.Restaurant' | translate}}</span>
                                                                            </li>
                                        
                                                                            <li>
                                                                                <img class="inlineblock_m"
                                                                                    src="assets/images/hotel/bell-call{{isAmenityAvailable('AMENITY_TYPE_ROOMSERVICE') ? '-active' : ''}}.svg"
                                                                                    alt="" />
                                                                                <span class="inlineblock_m">{{'hotelSelect.Concierge' | translate}}</span>
                                                                            </li>
                                        
                                                                            <li>
                                                                                <img class="inlineblock_m"
                                                                                    src="assets/images/hotel/swimming-pool-person{{isAmenityAvailable('AMENITY_TYPE_POOL') ? '-active' : ''}}.svg"
                                                                                    alt="" />
                                                                                <span class="inlineblock_m">{{'hotelSelect.Pool' | translate}}</span>
                                                                            </li>
                                        
                                                                            <li>
                                                                                <img class="inlineblock_m"
                                                                                    src="assets/images/hotel/bar{{isAmenityAvailable('AMENITY_TYPE_BAR') ? '-active' : ''}}.svg"
                                                                                    alt="" />
                                                                                <span class="inlineblock_m">{{'hotelSelect.Bar' | translate}}</span>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                 </div>
                                                                 </div>
                                                                 </div>
                                                        </div>
                                                        </div>
                                                </div>
                                           
                        </ng-template>