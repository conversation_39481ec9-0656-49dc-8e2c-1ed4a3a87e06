import { Component, OnInit, Input } from '@angular/core';
import { HotelSearchService } from '../hotel-search.service';
import { HotelSearchRequest } from '../entity/hotel-search-request';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { DateUtils } from '../util/date-utils';
import { Constants } from '../util/constants';
import { _ } from 'src/app/util/title';
import { TranslateService } from "@ngx-translate/core";
import { Title } from '@angular/platform-browser';
import { SearchService } from '../search.service';
import { UserAccountService } from '../user-account.service';


@Component({
    selector: 'hotel-request-view',
    templateUrl: './hotel-request-view.component.html',
    styleUrls: ['./hotel-request-view.component.scss'],
    standalone: false
})
export class HotelRequestViewComponent implements OnInit {

  hotelSearchRequest: HotelSearchRequest
  searchRequestSubscription: Subscription
  @Input() backToSearchSteps = 1;
  params = 'page=HotelResults'

  constructor(
    private searchService: HotelSearchService,
    private titleService: Title,
    private searchService1: SearchService,
    public router: Router,
    private userAccountService: UserAccountService,
    public translateService: TranslateService,
    private location: Location
  ) { }

  ngOnInit() {
    this.searchRequestSubscription = this.searchService.hotelRequest$.subscribe(request => {
      if (request) {
        // window.alert(JSON.stringify(request));
        this.hotelSearchRequest = this.searchService.getSearchRequest(request);
      }
    });
  }


  getHotelCity() {
    if (this.hotelSearchRequest) return this.hotelSearchRequest.destination;
  }

  getRoomCount() {
    if (this.hotelSearchRequest) return this.hotelSearchRequest.travellerRooms.length;
  }
  getTotalTravellers() {
    if (this.hotelSearchRequest) return this.hotelSearchRequest.totalTravellers;
  }

  getCheckInDate() {
    if (this.hotelSearchRequest) {
      return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(this.hotelSearchRequest.checkInDate), 'EEE MMM d');
    }
  }

  getCheckOutDate() {
    if (this.hotelSearchRequest) {
      return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(this.hotelSearchRequest.checkOutDate), 'EEE MMM d');
    }
  }

  getHotelChainName() {
    if (this.hotelSearchRequest && this.hotelSearchRequest.hotelChain.length > 0) {
      return Constants.HOTEL_BRAND_CODES_NAME_MAP.find(obj => obj.id === this.hotelSearchRequest.hotelChain).value;
    } else {
      return this.translateService.instant('hotelRequestView.AnyHotelChain');
    }
  }
  backToSearch() {
    // for(let stepCount = 1;stepCount <= this.backToSearchSteps;stepCount++) {
    //     this.location.back();
    // }
    this.searchService1.searchHeaderCliked = true;
    this.searchService.hotelSelectFromDropDown =false;
    this.titleService.setTitle('Hotel Search');
    this.router.navigate([this.userAccountService.getDefaultRoutePath()],
      {
        queryParams:
        {
          type: 'hotel',
        },
      }
    );


  }


}
