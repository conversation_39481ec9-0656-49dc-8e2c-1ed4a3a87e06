<div class="summery-view-container">
    <div class="summary-view">
        <div class="summary-view-inner">
            <div class="source-dest">
                <div class="source-dest-place">{{getHotelCity() ? getHotelCity():'Your current location'}}</div>
            </div>
            <div class="summary-view-middle">
                <div class="summery-room-details">
                    <div class="summery-room">{{getRoomCount()}} {{getRoomCount() > 1? ('hotelRequestView.Rooms' |
                        translate):('hotelRequestView.Room' | translate)}}</div>
                    <div class="summery-person">
                        <span class="container-icon">
                            <img alt="" src="assets/images/head.png">
                        </span>
                        <span>{{getTotalTravellers()}}</span>
                    </div>
                    <div class="summery-hotel">{{getHotelChainName() | translate}}</div>
                </div>
                <div>{{'hotelRequestView.Checkin' | translate}} {{getCheckInDate()}} - {{'hotelRequestView.Checkout' |
                    translate}} {{getCheckOutDate()}}</div>
            </div>
            <div class="edit-summery-view">
                <span *ngIf="backToSearchSteps==1" class="icon-edit" attr.data-track="EditHotelSearch"
                    attr.data-params="page=HotelResults" (click)="backToSearch()"></span>
                <span *ngIf="backToSearchSteps==2" class="icon-edit" attr.data-track="EditHotelSearch"
                    attr.data-params="page=HotelRooms" (click)="backToSearch()"></span>
            </div>
        </div>
    </div>
</div>