@import "../../variables.scss";

.summery-view-container {
  float: left;
  width: 100%;
  background: #fff;
}

.summary-view {
  float: left;
  width: 100%;
  background: #fff;
}

.summary-view-inner {
  float: left;
  width: 100%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: center;
  align-items: center;
  padding: 20px 40px;
}

.source-dest {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  letter-spacing: 0.42px;
  line-height: 17px;
  color: #000000;
}

.summary-view-middle {
  display: inline-flex;
  flex-direction: column;
  font-size: 12px;
  line-height: 15px;
  color: #AEAEAE;
}

.summary-view-middle .date {
  letter-spacing: -0.6px;
  margin-bottom: 5px;
}

.summery-room-details {
  display: flex;
  margin-bottom: 6px;
}

.edit-summery-view {
  display: inline-flex;
}

.edit-summery-view .icon-edit {
  background: #fff;
  height: 32px;
  width: 32px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
  color: var(--secondarybutton-font-color);
  cursor: pointer;
}

.summery-room {
  margin-right: 20px;
}

.no-flight-found-container {
  float: left;
  width: 100%;
  padding-top: 10px;
  padding-left: 60px;
}

.summery-view-container {
  float: left;
  width: 100%;
  background: #fff;
}

.tab-content-item {
  float: left;
  width: 100%;
}

.selected-flight-container {
  float: left;
  width: 100%;
  padding: 5px 20px 13px;
}

.summery-person {
  margin-right: 20px;
}

.summery-person .container-icon {
  margin-right: 8px;
}

@media (max-width: 991px) {
  .summary-view-inner {
    flex-wrap: wrap;
    padding: 9px 20px 17px;
  }

  .source-dest {
    order: 1;
    width: 100%;
  }

  .summary-view-middle {
    order: 2;
    width: 100%;
    max-width: 275px;
  }

  .edit-summery-view {
    order: 3;
    position: relative;
    top: 0px;
  }


}

@media(max-width:767px) {
  .edit-summery-view {
    order: 3 !important;
    position: relative;
    top: 0px;
  }
}