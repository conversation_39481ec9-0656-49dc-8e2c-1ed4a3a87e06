export var LANGUAGE_BY_LOCALE = 
[
  {
    id: "af-NA",
    label: "Afrikaans (Namibia)"
  },
  {
    id: "af-ZA",
    label: "Afrikaans (South Africa)"
  },
  {
    id: "ak-GH",
    label: "<PERSON><PERSON> (Ghana)"
  },
  {
    id: "sq-AL",
    label: "Albanian (Albania)"
  },
  {
    id: "am-ET",
    label: "Amharic (Ethiopia)"
  },
  {
    id: "ar-DZ",
    label: "Arabic (Algeria)"
  },
  {
    id: "ar-BH",
    label: "Arabic (Bahrain)"
  },
  {
    id: "ar-EG",
    label: "Arabic (Egypt)"
  },
  {
    id: "ar-IQ",
    label: "Arabic (Iraq)"
  },
  {
    id: "ar-JO",
    label: "Arabic (Jordan)"
  },
  {
    id: "ar-KW",
    label: "Arabic (Kuwait)"
  },
  {
    id: "ar-LB",
    label: "Arabic (Lebanon)"
  },
  {
    id: "ar-LY",
    label: "Arabic (Libya)"
  },
  {
    id: "ar-MA",
    label: "Arabic (Morocco)"
  },
  {
    id: "ar-OM",
    label: "Arabic (Oman)"
  },
  {
    id: "ar-QA",
    label: "Arabic (Qatar)"
  },
  {
    id: "ar-SA",
    label: "Arabic (Saudi Arabia)"
  },
  {
    id: "ar-SD",
    label: "Arabic (Sudan)"
  },
  {
    id: "ar-SY",
    label: "Arabic (Syria)"
  },
  {
    id: "ar-TN",
    label: "Arabic (Tunisia)"
  },
  {
    id: "ar-AE",
    label: "Arabic (United Arab Emirates)"
  },
  {
    id: "ar-YE",
    label: "Arabic (Yemen)"
  },
  {
    id: "hy-AM",
    label: "Armenian (Armenia)"
  },
  {
    id: "as-IN",
    label: "Assamese (India)"
  },
  {
    id: "asa-TZ",
    label: "Asu (Tanzania)"
  },
  {
    id: "az-Cyrl",
    label: "Azerbaijani (Cyrillic)"
  },
  {
    id: "az-Cyrl-AZ",
    label: "Azerbaijani (Cyrillic, Azerbaijan)"
  },
  {
    id: "az-Latn",
    label: "Azerbaijani (Latin)"
  },
  {
    id: "az-Latn-AZ",
    label: "Azerbaijani (Latin, Azerbaijan)"
  },
  {
    id: "bm-ML",
    label: "Bambara (Mali)"
  },
  {
    id: "eu-ES",
    label: "Basque (Spain)"
  },
  {
    id: "be-BY",
    label: "Belarusian (Belarus)"
  },
  {
    id: "bem-ZM",
    label: "Bemba (Zambia)"
  },
  {
    id: "bez-TZ",
    label: "Bena (Tanzania)"
  },
  {
    id: "bn-BD",
    label: "Bengali (Bangladesh)"
  },
  {
    id: "bn-IN",
    label: "Bengali (India)"
  },
  {
    id: "bs-BA",
    label: "Bosnian (Bosnia and Herzegovina)"
  },
  {
    id: "bg-BG",
    label: "Bulgarian (Bulgaria)"
  },
  {
    id: "my-MM",
    label: "Burmese (Myanmar [Burma])"
  },
  {
    id: "yue-Hant-HK",
    label: "Cantonese (Traditional, Hong Kong SAR China)"
  },
  {
    id: "ca-ES",
    label: "Catalan (Spain)"
  },
  {
    id: "tzm-Latn",
    label: "Central Morocco Tamazight (Latin)"
  },
  {
    id: "tzm-Latn-MA",
    label: "Central Morocco Tamazight (Latin, Morocco)"
  },
  {
    id: "chr-US",
    label: "Cherokee (United States)"
  },
  {
    id: "cgg-UG",
    label: "Chiga (Uganda)"
  },
  {
    id: "zh-Hans",
    label: "Chinese (Simplified Han)"
  },
  {
    id: "zh-Hans-CN",
    label: "Chinese (Simplified Han, China)"
  },
  {
    id: "zh-Hans-HK",
    label: "Chinese (Simplified Han, Hong Kong SAR China)"
  },
  {
    id: "zh-Hans-MO",
    label: "Chinese (Simplified Han, Macau SAR China)"
  },
  {
    id: "zh-Hans-SG",
    label: "Chinese (Simplified Han, Singapore)"
  },
  {
    id: "zh-Hant",
    label: "Chinese (Traditional Han)"
  },
  {
    id: "zh-Hant-HK",
    label: "Chinese (Traditional Han, Hong Kong SAR China)"
  },
  {
    id: "zh-Hant-MO",
    label: "Chinese (Traditional Han, Macau SAR China)"
  },
  {
    id: "zh-Hant-TW",
    label: "Chinese (Traditional Han, Taiwan)"
  },
  {
    id: "kw-GB",
    label: "Cornish (United Kingdom)"
  },
  {
    id: "hr-HR",
    label: "Croatian (Croatia)"
  },
  {
    id: "cs-CZ",
    label: "Czech (Czech Republic)"
  },
  {
    id: "da-DK",
    label: "Danish (Denmark)"
  },
  {
    id: "nl-BE",
    label: "Dutch (Belgium)"
  },
  {
    id: "nl-NL",
    label: "Dutch (Netherlands)"
  },
  {
    id: "ebu-KE",
    label: "Embu (Kenya)"
  },
  {
    id: "en-AS",
    label: "English (American Samoa)"
  },
  {
    id: "en-AU",
    label: "English (Australia)"
  },
  {
    id: "en-BE",
    label: "English (Belgium)"
  },
  {
    id: "en-BZ",
    label: "English (Belize)"
  },
  {
    id: "en-BW",
    label: "English (Botswana)"
  },
  {
    id: "en-CA",
    label: "English (Canada)"
  },
  {
    id: "en-GU",
    label: "English (Guam)"
  },
  {
    id: "en-HK",
    label: "English (Hong Kong SAR China)"
  },
  {
    id: "en-IN",
    label: "English (India)"
  },
  {
    id: "en-IE",
    label: "English (Ireland)"
  },
  {
    id: "en-IL",
    label: "English (Israel)"
  },
  {
    id: "en-JM",
    label: "English (Jamaica)"
  },
  {
    id: "en-MT",
    label: "English (Malta)"
  },
  {
    id: "en-MH",
    label: "English (Marshall Islands)"
  },
  {
    id: "en-MU",
    label: "English (Mauritius)"
  },
  {
    id: "en-NA",
    label: "English (Namibia)"
  },
  {
    id: "en-NZ",
    label: "English (New Zealand)"
  },
  {
    id: "en-MP",
    label: "English (Northern Mariana Islands)"
  },
  {
    id: "en-PK",
    label: "English (Pakistan)"
  },
  {
    id: "en-PH",
    label: "English (Philippines)"
  },
  {
    id: "en-SG",
    label: "English (Singapore)"
  },
  {
    id: "en-ZA",
    label: "English (South Africa)"
  },
  {
    id: "en-TT",
    label: "English (Trinidad and Tobago)"
  },
  {
    id: "en-UM",
    label: "English (U.S. Minor Outlying Islands)"
  },
  {
    id: "en-VI",
    label: "English (U.S. Virgin Islands)"
  },
  {
    id: "en-GB",
    label: "English (United Kingdom)"
  },
  {
    id: "en-US",
    label: "English (United States)"
  },
  {
    id: "en-ZW",
    label: "English (Zimbabwe)"
  },
  {
    id: "et-EE",
    label: "Estonian (Estonia)"
  },
  {
    id: "ee-GH",
    label: "Ewe (Ghana)"
  },
  {
    id: "ee-TG",
    label: "Ewe (Togo)"
  },
  {
    id: "fo-FO",
    label: "Faroese (Faroe Islands)"
  },
  {
    id: "fil-PH",
    label: "Filipino (Philippines)"
  },
  {
    id: "fi-FI",
    label: "Finnish (Finland)"
  },
  {
    id: "fr-BE",
    label: "French (Belgium)"
  },
  {
    id: "fr-BJ",
    label: "French (Benin)"
  },
  {
    id: "fr-BF",
    label: "French (Burkina Faso)"
  },
  {
    id: "fr-BI",
    label: "French (Burundi)"
  },
  {
    id: "fr-CM",
    label: "French (Cameroon)"
  },
  {
    id: "fr-CA",
    label: "French (Canada)"
  },
  {
    id: "fr-CF",
    label: "French (Central African Republic)"
  },
  {
    id: "fr-TD",
    label: "French (Chad)"
  },
  {
    id: "fr-KM",
    label: "French (Comoros)"
  },
  {
    id: "fr-CG",
    label: "French (Congo - Brazzaville)"
  },
  {
    id: "fr-CD",
    label: "French (Congo - Kinshasa)"
  },
  {
    id: "fr-CI",
    label: "French (Côte d’Ivoire)"
  },
  {
    id: "fr-DJ",
    label: "French (Djibouti)"
  },
  {
    id: "fr-GQ",
    label: "French (Equatorial Guinea)"
  },
  {
    id: "fr-FR",
    label: "French (France)"
  },
  {
    id: "fr-GA",
    label: "French (Gabon)"
  },
  {
    id: "fr-GP",
    label: "French (Guadeloupe)"
  },
  {
    id: "fr-GN",
    label: "French (Guinea)"
  },
  {
    id: "fr-LU",
    label: "French (Luxembourg)"
  },
  {
    id: "fr-MG",
    label: "French (Madagascar)"
  },
  {
    id: "fr-ML",
    label: "French (Mali)"
  },
  {
    id: "fr-MQ",
    label: "French (Martinique)"
  },
  {
    id: "fr-MC",
    label: "French (Monaco)"
  },
  {
    id: "fr-NE",
    label: "French (Niger)"
  },
  {
    id: "fr-RW",
    label: "French (Rwanda)"
  },
  {
    id: "fr-RE",
    label: "French (Réunion)"
  },
  {
    id: "fr-BL",
    label: "French (Saint Barthélemy)"
  },
  {
    id: "fr-MF",
    label: "French (Saint Martin)"
  },
  {
    id: "fr-SN",
    label: "French (Senegal)"
  },
  {
    id: "fr-CH",
    label: "French (Switzerland)"
  },
  {
    id: "fr-TG",
    label: "French (Togo)"
  },
  {
    id: "ff-SN",
    label: "Fulah (Senegal)"
  },
  {
    id: "gl-ES",
    label: "Galician (Spain)"
  },
  {
    id: "lg-UG",
    label: "Ganda (Uganda)"
  },
  {
    id: "ka-GE",
    label: "Georgian (Georgia)"
  },
  {
    id: "de-AT",
    label: "German (Austria)"
  },
  {
    id: "de-BE",
    label: "German (Belgium)"
  },
  {
    id: "de-DE",
    label: "German (Germany)"
  },
  {
    id: "de-LI",
    label: "German (Liechtenstein)"
  },
  {
    id: "de-LU",
    label: "German (Luxembourg)"
  },
  {
    id: "de-CH",
    label: "German (Switzerland)"
  },
  {
    id: "el-CY",
    label: "Greek (Cyprus)"
  },
  {
    id: "el-GR",
    label: "Greek (Greece)"
  },
  {
    id: "gu-IN",
    label: "Gujarati (India)"
  },
  {
    id: "guz-KE",
    label: "Gusii (Kenya)"
  },
  {
    id: "ha-Latn",
    label: "Hausa (Latin)"
  },
  {
    id: "ha-Latn-GH",
    label: "Hausa (Latin, Ghana)"
  },
  {
    id: "ha-Latn-NE",
    label: "Hausa (Latin, Niger)"
  },
  {
    id: "ha-Latn-NG",
    label: "Hausa (Latin, Nigeria)"
  },
  {
    id: "haw-US",
    label: "Hawaiian (United States)"
  },
  {
    id: "he-IL",
    label: "Hebrew (Israel)"
  },
  {
    id: "hi-IN",
    label: "Hindi (India)"
  },
  {
    id: "hu-HU",
    label: "Hungarian (Hungary)"
  },
  {
    id: "is-IS",
    label: "Icelandic (Iceland)"
  },
  {
    id: "ig-NG",
    label: "Igbo (Nigeria)"
  },
  {
    id: "id-ID",
    label: "Indonesian (Indonesia)"
  },
  {
    id: "ga-IE",
    label: "Irish (Ireland)"
  },
  {
    id: "it-IT",
    label: "Italian (Italy)"
  },
  {
    id: "it-CH",
    label: "Italian (Switzerland)"
  },
  {
    id: "ja-JP",
    label: "Japanese (Japan)"
  },
  {
    id: "kea-CV",
    label: "Kabuverdianu (Cape Verde)"
  },
  {
    id: "kab-DZ",
    label: "Kabyle (Algeria)"
  },
  {
    id: "kl-GL",
    label: "Kalaallisut (Greenland)"
  },
  {
    id: "kln-KE",
    label: "Kalenjin (Kenya)"
  },
  {
    id: "kam-KE",
    label: "Kamba (Kenya)"
  },
  {
    id: "kn-IN",
    label: "Kannada (India)"
  },
  {
    id: "kk-Cyrl",
    label: "Kazakh (Cyrillic)"
  },
  {
    id: "kk-Cyrl-KZ",
    label: "Kazakh (Cyrillic, Kazakhstan)"
  },
  {
    id: "km-KH",
    label: "Khmer (Cambodia)"
  },
  {
    id: "ki-KE",
    label: "Kikuyu (Kenya)"
  },
  {
    id: "rw-RW",
    label: "Kinyarwanda (Rwanda)"
  },
  {
    id: "kok-IN",
    label: "Konkani (India)"
  },
  {
    id: "ko-KR",
    label: "Korean (South Korea)"
  },
  {
    id: "khq-ML",
    label: "Koyra Chiini (Mali)"
  },
  {
    id: "ses-ML",
    label: "Koyraboro Senni (Mali)"
  },
  {
    id: "lag-TZ",
    label: "Langi (Tanzania)"
  },
  {
    id: "lv-LV",
    label: "Latvian (Latvia)"
  },
  {
    id: "lt-LT",
    label: "Lithuanian (Lithuania)"
  },
  {
    id: "luo-KE",
    label: "Luo (Kenya)"
  },
  {
    id: "luy-KE",
    label: "Luyia (Kenya)"
  },
  {
    id: "mk-MK",
    label: "Macedonian (Macedonia)"
  },
  {
    id: "jmc-TZ",
    label: "Machame (Tanzania)"
  },
  {
    id: "kde-TZ",
    label: "Makonde (Tanzania)"
  },
  {
    id: "mg-MG",
    label: "Malagasy (Madagascar)"
  },
  {
    id: "ms-BN",
    label: "Malay (Brunei)"
  },
  {
    id: "ms-MY",
    label: "Malay (Malaysia)"
  },
  {
    id: "ml-IN",
    label: "Malayalam (India)"
  },
  {
    id: "mt-MT",
    label: "Maltese (Malta)"
  },
  {
    id: "gv-GB",
    label: "Manx (United Kingdom)"
  },
  {
    id: "mr-IN",
    label: "Marathi (India)"
  },
  {
    id: "mas-KE",
    label: "Masai (Kenya)"
  },
  {
    id: "mas-TZ",
    label: "Masai (Tanzania)"
  },
  {
    id: "mer-KE",
    label: "Meru (Kenya)"
  },
  {
    id: "mfe-MU",
    label: "Morisyen (Mauritius)"
  },
  {
    id: "naq-NA",
    label: "Nama (Namibia)"
  },
  {
    id: "ne-IN",
    label: "Nepali (India)"
  },
  {
    id: "ne-NP",
    label: "Nepali (Nepal)"
  },
  {
    id: "nd-ZW",
    label: "North Ndebele (Zimbabwe)"
  },
  {
    id: "nb-NO",
    label: "Norwegian Bokmål (Norway)"
  },
  {
    id: "nn-NO",
    label: "Norwegian Nynorsk (Norway)"
  },
  {
    id: "nyn-UG",
    label: "Nyankole (Uganda)"
  },
  {
    id: "or-IN",
    label: "Oriya (India)"
  },
  {
    id: "om-ET",
    label: "Oromo (Ethiopia)"
  },
  {
    id: "om-KE",
    label: "Oromo (Kenya)"
  },
  {
    id: "ps-AF",
    label: "Pashto (Afghanistan)"
  },
  {
    id: "fa-AF",
    label: "Persian (Afghanistan)"
  },
  {
    id: "fa-IR",
    label: "Persian (Iran)"
  },
  {
    id: "pl-PL",
    label: "Polish (Poland)"
  },
  {
    id: "pt-BR",
    label: "Portuguese (Brazil)"
  },
  {
    id: "pt-GW",
    label: "Portuguese (Guinea-Bissau)"
  },
  {
    id: "pt-MZ",
    label: "Portuguese (Mozambique)"
  },
  {
    id: "pt-PT",
    label: "Portuguese (Portugal)"
  },
  {
    id: "pa-Arab",
    label: "Punjabi (Arabic)"
  },
  {
    id: "pa-Arab-PK",
    label: "Punjabi (Arabic, Pakistan)"
  },
  {
    id: "pa-Guru",
    label: "Punjabi (Gurmukhi)"
  },
  {
    id: "pa-Guru-IN",
    label: "Punjabi (Gurmukhi, India)"
  },
  {
    id: "ro-MD",
    label: "Romanian (Moldova)"
  },
  {
    id: "ro-RO",
    label: "Romanian (Romania)"
  },
  {
    id: "rm-CH",
    label: "Romansh (Switzerland)"
  },
  {
    id: "rof-TZ",
    label: "Rombo (Tanzania)"
  },
  {
    id: "ru-MD",
    label: "Russian (Moldova)"
  },
  {
    id: "ru-RU",
    label: "Russian (Russia)"
  },
  {
    id: "ru-UA",
    label: "Russian (Ukraine)"
  },
  {
    id: "rwk-TZ",
    label: "Rwa (Tanzania)"
  },
  {
    id: "saq-KE",
    label: "Samburu (Kenya)"
  },
  {
    id: "sg-CF",
    label: "Sango (Central African Republic)"
  },
  {
    id: "seh-MZ",
    label: "Sena (Mozambique)"
  },
  {
    id: "sr-Cyrl",
    label: "Serbian (Cyrillic)"
  },
  {
    id: "sr-Cyrl-BA",
    label: "Serbian (Cyrillic, Bosnia and Herzegovina)"
  },
  {
    id: "sr-Cyrl-ME",
    label: "Serbian (Cyrillic, Montenegro)"
  },
  {
    id: "sr-Cyrl-RS",
    label: "Serbian (Cyrillic, Serbia)"
  },
  {
    id: "sr-Latn",
    label: "Serbian (Latin)"
  },
  {
    id: "sr-Latn-BA",
    label: "Serbian (Latin, Bosnia and Herzegovina)"
  },
  {
    id: "sr-Latn-ME",
    label: "Serbian (Latin, Montenegro)"
  },
  {
    id: "sr-Latn-RS",
    label: "Serbian (Latin, Serbia)"
  },
  {
    id: "sn-ZW",
    label: "Shona (Zimbabwe)"
  },
  {
    id: "ii-CN",
    label: "Sichuan Yi (China)"
  },
  {
    id: "si-LK",
    label: "Sinhala (Sri Lanka)"
  },
  {
    id: "sk-SK",
    label: "Slovak (Slovakia)"
  },
  {
    id: "sl-SI",
    label: "Slovenian (Slovenia)"
  },
  {
    id: "xog-UG",
    label: "Soga (Uganda)"
  },
  {
    id: "so-DJ",
    label: "Somali (Djibouti)"
  },
  {
    id: "so-ET",
    label: "Somali (Ethiopia)"
  },
  {
    id: "so-KE",
    label: "Somali (Kenya)"
  },
  {
    id: "so-SO",
    label: "Somali (Somalia)"
  },
  {
    id: "es-AR",
    label: "Spanish (Argentina)"
  },
  {
    id: "es-BO",
    label: "Spanish (Bolivia)"
  },
  {
    id: "es-CL",
    label: "Spanish (Chile)"
  },
  {
    id: "es-CO",
    label: "Spanish (Colombia)"
  },
  {
    id: "es-CR",
    label: "Spanish (Costa Rica)"
  },
  {
    id: "es-DO",
    label: "Spanish (Dominican Republic)"
  },
  {
    id: "es-EC",
    label: "Spanish (Ecuador)"
  },
  {
    id: "es-SV",
    label: "Spanish (El Salvador)"
  },
  {
    id: "es-GQ",
    label: "Spanish (Equatorial Guinea)"
  },
  {
    id: "es-GT",
    label: "Spanish (Guatemala)"
  },
  {
    id: "es-HN",
    label: "Spanish (Honduras)"
  },
  {
    id: "es-419",
    label: "Spanish (Latin America)"
  },
  {
    id: "es-MX",
    label: "Spanish (Mexico)"
  },
  {
    id: "es-NI",
    label: "Spanish (Nicaragua)"
  },
  {
    id: "es-PA",
    label: "Spanish (Panama)"
  },
  {
    id: "es-PY",
    label: "Spanish (Paraguay)"
  },
  {
    id: "es-PE",
    label: "Spanish (Peru)"
  },
  {
    id: "es-PR",
    label: "Spanish (Puerto Rico)"
  },
  {
    id: "es-ES",
    label: "Spanish (Spain)"
  },
  {
    id: "es-US",
    label: "Spanish (United States)"
  },
  {
    id: "es-UY",
    label: "Spanish (Uruguay)"
  },
  {
    id: "es-VE",
    label: "Spanish (Venezuela)"
  },
  {
    id: "sw-KE",
    label: "Swahili (Kenya)"
  },
  {
    id: "sw-TZ",
    label: "Swahili (Tanzania)"
  },
  {
    id: "sv-FI",
    label: "Swedish (Finland)"
  },
  {
    id: "sv-SE",
    label: "Swedish (Sweden)"
  },
  {
    id: "gsw-CH",
    label: "Swiss German (Switzerland)"
  },
  {
    id: "shi-Latn",
    label: "Tachelhit (Latin)"
  },
  {
    id: "shi-Latn-MA",
    label: "Tachelhit (Latin, Morocco)"
  },
  {
    id: "shi-Tfng",
    label: "Tachelhit (Tifinagh)"
  },
  {
    id: "shi-Tfng-MA",
    label: "Tachelhit (Tifinagh, Morocco)"
  },
  {
    id: "dav-KE",
    label: "Taita (Kenya)"
  },
  {
    id: "ta-IN",
    label: "Tamil (India)"
  },
  {
    id: "ta-LK",
    label: "Tamil (Sri Lanka)"
  },
  {
    id: "te-IN",
    label: "Telugu (India)"
  },
  {
    id: "teo-KE",
    label: "Teso (Kenya)"
  },
  {
    id: "teo-UG",
    label: "Teso (Uganda)"
  },
  {
    id: "th-TH",
    label: "Thai (Thailand)"
  },
  {
    id: "bo-CN",
    label: "Tibetan (China)"
  },
  {
    id: "bo-IN",
    label: "Tibetan (India)"
  },
  {
    id: "ti-ER",
    label: "Tigrinya (Eritrea)"
  },
  {
    id: "ti-ET",
    label: "Tigrinya (Ethiopia)"
  },
  {
    id: "to-TO",
    label: "Tonga (Tonga)"
  },
  {
    id: "tr-TR",
    label: "Turkish (Turkey)"
  },
  {
    id: "uk-UA",
    label: "Ukrainian (Ukraine)"
  },
  {
    id: "ur-IN",
    label: "Urdu (India)"
  },
  {
    id: "ur-PK",
    label: "Urdu (Pakistan)"
  },
  {
    id: "uz-Arab",
    label: "Uzbek (Arabic)"
  },
  {
    id: "uz-Arab-AF",
    label: "Uzbek (Arabic, Afghanistan)"
  },
  {
    id: "uz-Cyrl",
    label: "Uzbek (Cyrillic)"
  },
  {
    id: "uz-Cyrl-UZ",
    label: "Uzbek (Cyrillic, Uzbekistan)"
  },
  {
    id: "uz-Latn",
    label: "Uzbek (Latin)"
  },
  {
    id: "uz-Latn-UZ",
    label: "Uzbek (Latin, Uzbekistan)"
  },
  {
    id: "vi-VN",
    label: "Vietnamese (Vietnam)"
  },
  {
    id: "vun-TZ",
    label: "Vunjo (Tanzania)"
  },
  {
    id: "cy-GB",
    label: "Welsh (United Kingdom)"
  },
  {
    id: "yo-NG",
    label: "Yoruba (Nigeria)"
  },
  {
    id: "zu-ZA",
    label: "Zulu (South Africa)"
  }
]
