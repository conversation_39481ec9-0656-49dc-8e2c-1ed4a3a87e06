export const countries=[
  {
    "name": "Afghanistan",
    "dial_code": "+93",
    "code": "AF",
    "nationality": "Afghan"
  },
  {
    "name": "Albania",
    "dial_code": "+355",
    "code": "AL",
    "nationality": "Albanian"
  },
  {
    "name": "Algeria",
    "dial_code": "+213",
    "code": "DZ",
    "nationality": "Algerian"
  },
  {
    "name": "AmericanSamoa",
    "dial_code": "+1 684",
    "code": "AS",
    "nationality": "American Samoan"
  },
  {
    "name": "Andorra",
    "dial_code": "+376",
    "code": "AD",
    "nationality": "Andorran"
  },
  {
    "name": "Angola",
    "dial_code": "+244",
    "code": "AO",
    "nationality": "Angolan"
  },
  {
    "name": "<PERSON><PERSON><PERSON>",
    "dial_code": "+1 264",
    "code": "AI",
    "nationality": "Anguillan"
  },
  {
    "name": "Antigua and Barbuda",
    "dial_code": "+1 268",
    "code": "AG",
    "nationality": "Antiguan or Barbudan"
  },
  {
    "name": "Argentina",
    "dial_code": "+54",
    "code": "AR",
    "nationality": "Argentine"
  },
  {
    "name": "Armenia",
    "dial_code": "+374",
    "code": "AM",
    "nationality": "Armenian"
  },
  {
    "name": "Aruba",
    "dial_code": "+297",
    "code": "AW",
    "nationality": "Aruban"
  },
  {
    "name": "Australia",
    "dial_code": "+61",
    "code": "AU",
    "nationality": "Australian"
  },
  {
    "name": "Austria",
    "dial_code": "+43",
    "code": "AT",
    "nationality": "Austrian"
  },
  {
    "name": "Azerbaijan",
    "dial_code": "+994",
    "code": "AZ",
    "nationality": "Azerbaijani, Azeri"
  },
  {
    "name": "Bahamas",
    "dial_code": "+1 242",
    "code": "BS",
    "nationality": "Bahamian"
  },
  {
    "name": "Bahrain",
    "dial_code": "+973",
    "code": "BH",
    "nationality": "Bahraini"
  },
  {
    "name": "Bangladesh",
    "dial_code": "+880",
    "code": "BD",
    "nationality": "Bangladeshi"
  },
  {
    "name": "Barbados",
    "dial_code": "+1 246",
    "code": "BB",
    "nationality": "Barbadian"
  },
  {
    "name": "Belarus",
    "dial_code": "+375",
    "code": "BY",
    "nationality": "Belarusian"
  },
  {
    "name": "Belgium",
    "dial_code": "+32",
    "code": "BE",
    "nationality": "Belgian"
  },
  {
    "name": "Belize",
    "dial_code": "+501",
    "code": "BZ",
    "nationality": "Belizean"
  },
  {
    "name": "Benin",
    "dial_code": "+229",
    "code": "BJ",
    "nationality": "Beninese, Beninois"
  },
  {
    "name": "Bermuda",
    "dial_code": "+1 441",
    "code": "BM",
    "nationality": "Bermudian, Bermudan"
  },
  {
    "name": "Bhutan",
    "dial_code": "+975",
    "code": "BT",
    "nationality": "Bhutanese"
  },
  {
    "name": "Bosnia and Herzegovina",
    "dial_code": "+387",
    "code": "BA",
    "nationality": "Bosnian or Herzegovinian"
  },
  {
    "name": "Botswana",
    "dial_code": "+267",
    "code": "BW",
    "nationality": "Motswana, Botswanan"
  },
  {
    "name": "Brazil",
    "dial_code": "+55",
    "code": "BR",
    "nationality": "Brazilian"
  },
  {
    "name": "British Indian Ocean Territory",
    "dial_code": "+246",
    "code": "IO",
    "nationality": "BIOT"
  },
  {
    "name": "Bulgaria",
    "dial_code": "+359",
    "code": "BG",
    "nationality": "Bulgarian"
  },
  {
    "name": "Burkina Faso",
    "dial_code": "+226",
    "code": "BF",
    "nationality": "Burkinabé"
  },
  {
    "name": "Burundi",
    "dial_code": "+257",
    "code": "BI",
    "nationality": "Burundian"
  },
  {
    "name": "Cambodia",
    "dial_code": "+855",
    "code": "KH",
    "nationality": "Cambodian"
  },
  {
    "name": "Cameroon",
    "dial_code": "+237",
    "code": "CM",
    "nationality": "Cameroonian"
  },
  {
    "name": "Canada",
    "dial_code": "+1",
    "code": "CA",
    "nationality": "Canadian"
  },
  {
    "name": "Cape Verde",
    "dial_code": "+238",
    "code": "CV",
    "nationality": "Cabo Verdean"
  },
  {
    "name": "Cayman Islands",
    "dial_code": "+1 345",
    "code": "KY",
    "nationality": "Caymanian"
  },
  {
    "name": "Central African Republic",
    "dial_code": "+236",
    "code": "CF",
    "nationality": "Central African"
  },
  {
    "name": "Chad",
    "dial_code": "+235",
    "code": "TD",
    "nationality": "Chadian"
  },
  {
    "name": "Chile",
    "dial_code": "+56",
    "code": "CL",
    "nationality": "Chilean"
  },
  {
    "name": "China",
    "dial_code": "+86",
    "code": "CN",
    "nationality": "Chinese"
  },
  {
    "name": "Christmas Island",
    "dial_code": "+61",
    "code": "CX",
    "nationality": "Christmas Island"
  },
  {
    "name": "Colombia",
    "dial_code": "+57",
    "code": "CO",
    "nationality": "Colombian"
  },
  {
    "name": "Comoros",
    "dial_code": "+269",
    "code": "KM",
    "nationality": "Comoran, Comorian"
  },
  {
    "name": "Congo",
    "dial_code": "+242",
    "code": "CG",
    "nationality": "Congolese"
  },
  {
    "name": "Cook Islands",
    "dial_code": "+682",
    "code": "CK",
    "nationality": "Cook Island"
  },
  {
    "name": "Costa Rica",
    "dial_code": "+506",
    "code": "CR",
    "nationality": "Costa Rican"
  },
  {
    "name": "Croatia",
    "dial_code": "+385",
    "code": "HR",
    "nationality": "Croatian"
  },
  {
    "name": "Cuba",
    "dial_code": "+53",
    "code": "CU",
    "nationality": "Cuban"
  },
  {
    "name": "Cyprus",
    "dial_code": "+537",
    "code": "CY",
    "nationality": "Cypriot"
  },
  {
    "name": "Czech Republic",
    "dial_code": "+420",
    "code": "CZ",
    "nationality": "Czech"
  },
  {
    "name": "Denmark",
    "dial_code": "+45",
    "code": "DK",
    "nationality": "Danish"
  },
  {
    "name": "Djibouti",
    "dial_code": "+253",
    "code": "DJ",
    "nationality": "Djiboutian"
  },
  {
    "name": "Dominican Republic",
    "dial_code": "+1 849",
    "code": "DO",
    "nationality": "Dominican Republic"
  },
  {
    "name": "Dominica",
    "dial_code": "+1 767",
    "code": "DM",
    "nationality": "Dominican"
  },
  
  {
    "name": "Ecuador",
    "dial_code": "+593",
    "code": "EC",
    "nationality": "Ecuadorian"
  },
  {
    "name": "Egypt",
    "dial_code": "+20",
    "code": "EG",
    "nationality": "Egyptian"
  },
  {
    "name": "El Salvador",
    "dial_code": "+503",
    "code": "SV",
    "nationality": "Salvadoran"
  },
  {
    "name": "Equatorial Guinea",
    "dial_code": "+240",
    "code": "GQ",
    "nationality": "Equatorial Guinean, Equatoguinean"
  },
  {
    "name": "Eritrea",
    "dial_code": "+291",
    "code": "ER",
    "nationality": "Eritrean"
  },
  {
    "name": "Estonia",
    "dial_code": "+372",
    "code": "EE",
    "nationality": "Estonian"
  },
  {
    "name": "Ethiopia",
    "dial_code": "+251",
    "code": "ET",
    "nationality": "Ethiopian"
  },
  {
    "name": "Faroe Islands",
    "dial_code": "+298",
    "code": "FO",
    "nationality": "Faroese"
  },
  {
    "name": "Fiji",
    "dial_code": "+679",
    "code": "FJ",
    "nationality": "Fijian"
  },
  {
    "name": "Finland",
    "dial_code": "+358",
    "code": "FI",
    "nationality": "Finnish"
  },
  {
    "name": "France",
    "dial_code": "+33",
    "code": "FR",
    "nationality": "French"
  },
  {
    "name": "French Guiana",
    "dial_code": "+594",
    "code": "GF",
    "nationality": "French Guianese"
  },
  {
    "name": "French Polynesia",
    "dial_code": "+689",
    "code": "PF",
    "nationality": "French Polynesian"
  },
  {
    "name": "Gabon",
    "dial_code": "+241",
    "code": "GA",
    "nationality": "Gabonese"
  },
  {
    "name": "Gambia",
    "dial_code": "+220",
    "code": "GM",
    "nationality": "Gambian"
  },
  {
    "name": "Georgia",
    "dial_code": "+995",
    "code": "GE",
    "nationality": "Georgian"
  },
  {
    "name": "Germany",
    "dial_code": "+49",
    "code": "DE",
    "nationality": "German"
  },
  {
    "name": "Ghana",
    "dial_code": "+233",
    "code": "GH",
    "nationality": "Ghanaian"
  },
  {
    "name": "Gibraltar",
    "dial_code": "+350",
    "code": "GI",
    "nationality": "Gibraltar"
  },
  {
    "name": "Greece",
    "dial_code": "+30",
    "code": "GR",
    "nationality": "Greek, Hellenic"
  },
  {
    "name": "Greenland",
    "dial_code": "+299",
    "code": "GL",
    "nationality": "Greenlandic"
  },
  {
    "name": "Grenada",
    "dial_code": "+1 473",
    "code": "GD",
    "nationality": "Grenadian"
  },
  {
    "name": "Guadeloupe",
    "dial_code": "+590",
    "code": "GP",
    "nationality": "Guadeloupe"
  },
  {
    "name": "Guam",
    "dial_code": "+1 671",
    "code": "GU",
    "nationality": "Guamanian, Guambat"
  },
  {
    "name": "Guatemala",
    "dial_code": "+502",
    "code": "GT",
    "nationality": "Guatemalan"
  },
  {
    "name": "Guinea",
    "dial_code": "+224",
    "code": "GN",
    "nationality": "Guinean"
  },
  {
    "name": "Guinea-Bissau",
    "dial_code": "+245",
    "code": "GW",
    "nationality": "Bissau-Guinean"
  },
  {
    "name": "Guyana",
    "dial_code": "+595",
    "code": "GY",
    "nationality": "Guyanese"
  },
  {
    "name": "Haiti",
    "dial_code": "+509",
    "code": "HT",
    "nationality": "Haitian"
  },
  {
    "name": "Honduras",
    "dial_code": "+504",
    "code": "HN",
    "nationality": "Honduran"
  },
  {
    "name": "Hungary",
    "dial_code": "+36",
    "code": "HU",
    "nationality": "Hungarian, Magyar"
  },
  {
    "name": "Iceland",
    "dial_code": "+354",
    "code": "IS",
    "nationality": "Icelandic"
  },
  {
    "name": "India",
    "dial_code": "+91",
    "code": "IN",
    "nationality": "Indian"
  },
  {
    "name": "Indonesia",
    "dial_code": "+62",
    "code": "ID",
    "nationality": "Indonesian"
  },
  {
    "name": "Iraq",
    "dial_code": "+964",
    "code": "IQ",
    "nationality": "Iraqi"
  },
  {
    "name": "Ireland",
    "dial_code": "+353",
    "code": "IE",
    "nationality": "Irish"
  },
  {
    "name": "Israel",
    "dial_code": "+972",
    "code": "IL",
    "nationality": "Israeli"
  },
  {
    "name": "Italy",
    "dial_code": "+39",
    "code": "IT",
    "nationality": "Italian"
  },
  {
    "name": "Jamaica",
    "dial_code": "+1 876",
    "code": "JM",
    "nationality": "Jamaican"
  },
  {
    "name": "Japan",
    "dial_code": "+81",
    "code": "JP",
    "nationality": "Japanese"
  },
  {
    "name": "Jordan",
    "dial_code": "+962",
    "code": "JO",
    "nationality": "Jordanian"
  },
  {
    "name": "Kazakhstan",
    "dial_code": "+7 7",
    "code": "KZ",
    "nationality": "Kazakhstani, Kazakh"
  },
  {
    "name": "Kenya",
    "dial_code": "+254",
    "code": "KE",
    "nationality": "Kenyan"
  },
  {
    "name": "Kiribati",
    "dial_code": "+686",
    "code": "KI",
    "nationality": "I-Kiribati"
  },
  {
    "name": "Kuwait",
    "dial_code": "+965",
    "code": "KW",
    "nationality": "Kuwaiti"
  },
  {
    "name": "Kyrgyzstan",
    "dial_code": "+996",
    "code": "KG",
    "nationality": "Kyrgyzstani, Kyrgyz, Kirgiz, Kirghiz"
  },
  {
    "name": "Latvia",
    "dial_code": "+371",
    "code": "LV",
    "nationality": "Latvian"
  },
  {
    "name": "Lebanon",
    "dial_code": "+961",
    "code": "LB",
    "nationality": "Lebanese"
  },
  {
    "name": "Lesotho",
    "dial_code": "+266",
    "code": "LS",
    "nationality": "Basotho"
  },
  {
    "name": "Liberia",
    "dial_code": "+231",
    "code": "LR",
    "nationality": "Liberian"
  },
  {
    "name": "Liechtenstein",
    "dial_code": "+423",
    "code": "LI",
    "nationality": "Liechtenstein"
  },
  {
    "name": "Lithuania",
    "dial_code": "+370",
    "code": "LT",
    "nationality": "Lithuanian"
  },
  {
    "name": "Luxembourg",
    "dial_code": "+352",
    "code": "LU",
    "nationality": "Luxembourg, Luxembourgish"
  },
  {
    "name": "Madagascar",
    "dial_code": "+261",
    "code": "MG",
    "nationality": "Malagasy"
  },
  {
    "name": "Malawi",
    "dial_code": "+265",
    "code": "MW",
    "nationality": "Malawian"
  },
  {
    "name": "Malaysia",
    "dial_code": "+60",
    "code": "MY",
    "nationality": "Malaysian"
  },
  {
    "name": "Maldives",
    "dial_code": "+960",
    "code": "MV",
    "nationality": "Maldivian"
  },
  {
    "name": "Mali",
    "dial_code": "+223",
    "code": "ML",
    "nationality": "Malian, Malinese"
  },
  {
    "name": "Malta",
    "dial_code": "+356",
    "code": "MT",
    "nationality": "Maltese"
  },
  {
    "name": "Marshall Islands",
    "dial_code": "+692",
    "code": "MH",
    "nationality": "Marshallese"
  },
  {
    "name": "Martinique",
    "dial_code": "+596",
    "code": "MQ",
    "nationality": "Martiniquais, Martinican"
  },
  {
    "name": "Mauritania",
    "dial_code": "+222",
    "code": "MR",
    "nationality": "Mauritanian"
  },
  {
    "name": "Mauritius",
    "dial_code": "+230",
    "code": "MU",
    "nationality": "Mauritian"
  },
  {
    "name": "Mayotte",
    "dial_code": "+262",
    "code": "YT",
    "nationality": "Mahoran"
  },
  {
    "name": "Mexico",
    "dial_code": "+52",
    "code": "MX",
    "nationality": "Mexican"
  },
  {
    "name": "Monaco",
    "dial_code": "+377",
    "code": "MC",
    "nationality": "Monégasque, Monacan"
  },
  {
    "name": "Mongolia",
    "dial_code": "+976",
    "code": "MN",
    "nationality": "Mongolian"
  },
  {
    "name": "Montenegro",
    "dial_code": "+382",
    "code": "ME",
    "nationality": "Montenegrin"
  },
  {
    "name": "Montserrat",
    "dial_code": "+1 664",
    "code": "MS",
    "nationality": "Montserratian"
  },
  {
    "name": "Morocco",
    "dial_code": "+212",
    "code": "MA",
    "nationality": "Moroccan"
  },
  {
    "name": "Myanmar",
    "dial_code": "+95",
    "code": "MM",
    "nationality": "Burmese"
  },
  {
    "name": "Namibia",
    "dial_code": "+264",
    "code": "NA",
    "nationality": "Namibian"
  },
  {
    "name": "Nauru",
    "dial_code": "+674",
    "code": "NR",
    "nationality": "Nauruan"
  },
  {
    "name": "Nepal",
    "dial_code": "+977",
    "code": "NP",
    "nationality": "Nepali, Nepalese"
  },
  {
    "name": "Netherlands",
    "dial_code": "+31",
    "code": "NL",
    "nationality": "Dutch, Netherlandic"
  },
  {
    "name": "New Caledonia",
    "dial_code": "+687",
    "code": "NC",
    "nationality": "New Caledonian"
  },
  {
    "name": "New Zealand",
    "dial_code": "+64",
    "code": "NZ",
    "nationality": "New Zealand, NZ"
  },
  {
    "name": "Nicaragua",
    "dial_code": "+505",
    "code": "NI",
    "nationality": "Nicaraguan"
  },
  {
    "name": "Niger",
    "dial_code": "+227",
    "code": "NE",
    "nationality": "Nigerien"
  },
  {
    "name": "Nigeria",
    "dial_code": "+234",
    "code": "NG",
    "nationality": "Nigerian"
  },
  {
    "name": "Niue",
    "dial_code": "+683",
    "code": "NU",
    "nationality": "Niuean"
  },
  {
    "name": "Norfolk Island",
    "dial_code": "+672",
    "code": "NF",
    "nationality": "Norfolk Island"
  },
  {
    "name": "Northern Mariana Islands",
    "dial_code": "+1 670",
    "code": "MP",
    "nationality": "Northern Marianan"
  },
  {
    "name": "Norway",
    "dial_code": "+47",
    "code": "NO",
    "nationality": "Norwegian"
  },
  {
    "name": "Oman",
    "dial_code": "+968",
    "code": "OM",
    "nationality": "Omani"
  },
  {
    "name": "Pakistan",
    "dial_code": "+92",
    "code": "PK",
    "nationality": "Pakistani"
  },
  {
    "name": "Palau",
    "dial_code": "+680",
    "code": "PW",
    "nationality": "Palauan"
  },
  {
    "name": "Panama",
    "dial_code": "+507",
    "code": "PA",
    "nationality": "Panamanian"
  },
  {
    "name": "Papua New Guinea",
    "dial_code": "+675",
    "code": "PG",
    "nationality": "Papua New Guinean, Papuan"
  },
  {
    "name": "Paraguay",
    "dial_code": "+595",
    "code": "PY",
    "nationality": "Paraguayan"
  },
  {
    "name": "Peru",
    "dial_code": "+51",
    "code": "PE",
    "nationality": "Peruvian"
  },
  {
    "name": "Philippines",
    "dial_code": "+63",
    "code": "PH",
    "nationality": "Philippine, Filipino"
  },
  {
    "name": "Poland",
    "dial_code": "+48",
    "code": "PL",
    "nationality": "Polish"
  },
  {
    "name": "Portugal",
    "dial_code": "+351",
    "code": "PT",
    "nationality": "Portuguese"
  },
  {
    "name": "Puerto Rico",
    "dial_code": "+1 939",
    "code": "PR",
    "nationality": "Puerto Rican"
  },
  {
    "name": "Qatar",
    "dial_code": "+974",
    "code": "QA",
    "nationality": "Qatari"
  },
  {
    "name": "Romania",
    "dial_code": "+40",
    "code": "RO",
    "nationality": "Romanian"
  },
  {
    "name": "Rwanda",
    "dial_code": "+250",
    "code": "RW",
    "nationality": "Rwandan"
  },
  {
    "name": "Samoa",
    "dial_code": "+685",
    "code": "WS",
    "nationality": "Samoan"
  },
  {
    "name": "San Marino",
    "dial_code": "+378",
    "code": "SM",
    "nationality": "Sammarinese"
  },
  {
    "name": "Saudi Arabia",
    "dial_code": "+966",
    "code": "SA",
    "nationality": "Saudi, Saudi Arabian"
  },
  {
    "name": "Senegal",
    "dial_code": "+221",
    "code": "SN",
    "nationality": "Senegalese"
  },
  {
    "name": "Serbia",
    "dial_code": "+381",
    "code": "RS",
    "nationality": "Serbian"
  },
  {
    "name": "Seychelles",
    "dial_code": "+248",
    "code": "SC",
    "nationality": "Seychellois"
  },
  {
    "name": "Sierra Leone",
    "dial_code": "+232",
    "code": "SL",
    "nationality": "Sierra Leonean"
  },
  {
    "name": "Singapore",
    "dial_code": "+65",
    "code": "SG",
    "nationality": "Singaporean"
  },
  {
    "name": "Slovakia",
    "dial_code": "+421",
    "code": "SK",
    "nationality": "Slovak"
  },
  {
    "name": "Slovenia",
    "dial_code": "+386",
    "code": "SI",
    "nationality": "Slovenian, Slovene"
  },
  {
    "name": "Solomon Islands",
    "dial_code": "+677",
    "code": "SB",
    "nationality": "Solomon Island"
  },
  {
    "name": "South Africa",
    "dial_code": "+27",
    "code": "ZA",
    "nationality": "South African"
  },
  {
    "name": "South Georgia and the South Sandwich Islands",
    "dial_code": "+500",
    "code": "GS",
    "nationality": "South Georgia or South Sandwich Islands"
  },
  {
    "name": "Spain",
    "dial_code": "+34",
    "code": "ES",
    "nationality": "Spanish"
  },
  {
    "name": "Sri Lanka",
    "dial_code": "+94",
    "code": "LK",
    "nationality": "Sri Lankan"
  },
  {
    "name": "Sudan",
    "dial_code": "+249",
    "code": "SD",
    "nationality": "Sudanese"
  },
  {
    "name": "Suriname",
    "dial_code": "+597",
    "code": "SR",
    "nationality": "Surinamese"
  },
  {
    "name": "Swaziland",
    "dial_code": "+268",
    "code": "SZ",
    "nationality": "Swazi"
  },
  {
    "name": "Sweden",
    "dial_code": "+46",
    "code": "SE",
    "nationality": "Swedish"
  },
  {
    "name": "Switzerland",
    "dial_code": "+41",
    "code": "CH",
    "nationality": "Swiss"
  },
  {
    "name": "Tajikistan",
    "dial_code": "+992",
    "code": "TJ",
    "nationality": "Tajikistani"
  },
  {
    "name": "Thailand",
    "dial_code": "+66",
    "code": "TH",
    "nationality": "Thai"
  },
  {
    "name": "Togo",
    "dial_code": "+228",
    "code": "TG",
    "nationality": "Togolese"
  },
  {
    "name": "Tokelau",
    "dial_code": "+690",
    "code": "TK",
    "nationality": "Tokelauan"
  },
  {
    "name": "Tonga",
    "dial_code": "+676",
    "code": "TO",
    "nationality": "Tongan"
  },
  {
    "name": "Trinidad and Tobago",
    "dial_code": "+1 868",
    "code": "TT",
    "nationality": "Trinidadian or Tobagonian"
  },
  {
    "name": "Tunisia",
    "dial_code": "+216",
    "code": "TN",
    "nationality": "Tunisian"
  },
  {
    "name": "Turkey",
    "dial_code": "+90",
    "code": "TR",
    "nationality": "Turkish"
  },
  {
    "name": "Turkmenistan",
    "dial_code": "+993",
    "code": "TM",
    "nationality": "Turkmen"
  },
  {
    "name": "Turks and Caicos Islands",
    "dial_code": "+1 649",
    "code": "TC",
    "nationality": "Turks and Caicos Island"
  },
  {
    "name": "Tuvalu",
    "dial_code": "+688",
    "code": "TV",
    "nationality": "Tuvaluan"
  },
  {
    "name": "Uganda",
    "dial_code": "+256",
    "code": "UG",
    "nationality": "Ugandan"
  },
  {
    "name": "Ukraine",
    "dial_code": "+380",
    "code": "UA",
    "nationality": "Ukrainian"
  },
  {
    "name": "United Arab Emirates",
    "dial_code": "+971",
    "code": "AE",
    "nationality": "Emirati, Emirian, Emiri"
  },
  {
    "name": "United Kingdom",
    "dial_code": "+44",
    "code": "GB",
    "nationality": "British, UK"
  },
  {
    "name": "United States",
    "dial_code": "+1",
    "code": "US",
    "nationality": "American"
  },
  {
    "name": "Uruguay",
    "dial_code": "+598",
    "code": "UY",
    "nationality": "Uruguayan"
  },
  
  {
    "name": "Uzbekistan",
    "dial_code": "+998",
    "code": "UZ",
    "nationality": "Uzbekistani, Uzbek"
  },
  {
    "name": "Vanuatu",
    "dial_code": "+678",
    "code": "VU",
    "nationality": "Ni-Vanuatu, Vanuatuan"
  },
  {
    "name": "Wallis and Futuna",
    "dial_code": "+681",
    "code": "WF",
    "nationality": "Wallis and Futuna, Wallisian or Futunan"
  },
  {
    "name": "Yemen",
    "dial_code": "+967",
    "code": "YE",
    "nationality": "Yemeni"
  },
  {
    "name": "Zambia",
    "dial_code": "+260",
    "code": "ZM",
    "nationality": "Zambian"
  },
  {
    "name": "Zimbabwe",
    "dial_code": "+263",
    "code": "ZW",
    "nationality": "Zimbabwean"
  },
  {
    "name": "Bolivia, Plurinational State of",
    "dial_code": "+591",
    "code": "BO",
    "nationality": "Bolivian"
  },
  {
    "name": "Brunei Darussalam",
    "dial_code": "+673",
    "code": "BN",
    "nationality": "Bruneian"
  },
  {
    "name": "Cocos (Keeling) Islands",
    "dial_code": "+61",
    "code": "CC",
    "nationality": "Cocos Island"
  },
  {
    "name": "Congo, The Democratic Republic of the",
    "dial_code": "+243",
    "code": "CD",
    "nationality": "Congolese Republic"
  },
  {
    "name": "Cote d'Ivoire",
    "dial_code": "+225",
    "code": "CI",
    "nationality": "Ivorian"
  },
  {
    "name": "Falkland Islands (Malvinas)",
    "dial_code": "+500",
    "code": "FK",
    "nationality": "Falkland Island"
  },
  {
    "name": "Holy See (Vatican City State)",
    "dial_code": "+379",
    "code": "VA",
    "nationality": "Vatican"
  },
  {
    "name": "Hong Kong",
    "dial_code": "+852",
    "code": "HK",
    "nationality": "Hong Kong, Hong Kongese"
  },
  {
    "name": "Iran, Islamic Republic of",
    "dial_code": "+98",
    "code": "IR",
    "nationality": "Iranian, Persian"
  },
  {
    "name": "Korea, Democratic People's Republic of",
    "dial_code": "+850",
    "code": "KP",
    "nationality": "North Korean"
  },
  {
    "name": "Korea, Republic of",
    "dial_code": "+82",
    "code": "KR",
    "nationality": "South Korean"
  },
  {
    "name": "Lao People's Democratic Republic",
    "dial_code": "+856",
    "code": "LA",
    "nationality": "Lao, Laotian"
  },
  {
    "name": "Libyan Arab Jamahiriya",
    "dial_code": "+218",
    "code": "LY",
    "nationality": "Libyan"
  },
  {
    "name": "Macao",
    "dial_code": "+853",
    "code": "MO",
    "nationality": "Macanese, Chinese"
  },
  {
    "name": "Macedonia, The Former Yugoslav Republic of",
    "dial_code": "+389",
    "code": "MK",
    "nationality": "Macedonian"
  },
  {
    "name": "Micronesia, Federated States of",
    "dial_code": "+691",
    "code": "FM",
    "nationality": "Micronesian"
  },
  {
    "name": "Moldova, Republic of",
    "dial_code": "+373",
    "code": "MD",
    "nationality": "Moldovan"
  },
  {
    "name": "Mozambique",
    "dial_code": "+258",
    "code": "MZ",
    "nationality": "Mozambican"
  },
  {
    "name": "Palestinian Territory, Occupied",
    "dial_code": "+970",
    "code": "PS",
    "nationality": "Palestinian"
  },
  {
    "name": "Pitcairn",
    "dial_code": "+872",
    "code": "PN",
    "nationality": "Pitcairn Island"
  },
  {
    "name": "Réunion",
    "dial_code": "+262",
    "code": "RE",
    "nationality": "Réunionese, Réunionnais"
  },
  {
    "name": "Russia",
    "dial_code": "+7",
    "code": "RU",
    "nationality": "Russian"
  },
  {
    "name": "Saint Barthélemy",
    "dial_code": "+590",
    "code": "BL",
    "nationality": "Barthélemois"
  },
  {
    "name": "Saint Helena, Ascension and Tristan Da Cunha",
    "dial_code": "+290",
    "code": "SH",
    "nationality": "Saint Helenian"
  },
  {
    "name": "Saint Kitts and Nevis",
    "dial_code": "+1 869",
    "code": "KN",
    "nationality": "Kittitian or Nevisian"
  },
  {
    "name": "Saint Lucia",
    "dial_code": "+1 758",
    "code": "LC",
    "nationality": "Saint Lucian"
  },
  {
    "name": "Saint Martin",
    "dial_code": "+590",
    "code": "MF",
    "nationality": "Saint-Martinoise"
  },
  {
    "name": "Saint Pierre and Miquelon",
    "dial_code": "+508",
    "code": "PM",
    "nationality": "Saint-Pierrais or Miquelonnais"
  },
  {
    "name": "Saint Vincent and the Grenadines",
    "dial_code": "+1 784",
    "code": "VC",
    "nationality": "Saint Vincentian, Vincentian"
  },
  {
    "name": "Sao Tome and Principe",
    "dial_code": "+239",
    "code": "ST",
    "nationality": "São Toméan"
  },
  {
    "name": "Somalia",
    "dial_code": "+252",
    "code": "SO",
    "nationality": "Somali, Somalian"
  },
  {
    "name": "Svalbard and Jan Mayen",
    "dial_code": "+47",
    "code": "SJ",
    "nationality": "Svalbard"
  },
  {
    "name": "Syrian Arab Republic",
    "dial_code": "+963",
    "code": "SY",
    "nationality": "Syrian"
  },
  {
    "name": "Taiwan, Province of China",
    "dial_code": "+886",
    "code": "TW",
    "nationality": "Chinese, Taiwanese"
  },
  {
    "name": "Tanzania, United Republic of",
    "dial_code": "+255",
    "code": "TZ",
    "nationality": "Tanzanian"
  },
  {
    "name": "Timor-Leste",
    "dial_code": "+670",
    "code": "TL",
    "nationality": "Timorese"
  },
  {
    "name": "Venezuela, Bolivarian Republic of",
    "dial_code": "+58",
    "code": "VE",
    "nationality": "Venezuelan"
  },
  {
    "name": "Viet Nam",
    "dial_code": "+84",
    "code": "VN",
    "nationality": "Vietnamese"
  },
  {
    "name": "Virgin Islands, British",
    "dial_code": "+1 284",
    "code": "VG",
    "nationality": "British Virgin Island"
  },
  {
    "name": "Virgin Islands, U.S.",
    "dial_code": "+1 340",
    "code": "VI",
    "nationality": "U.S. Virgin Island"
  }
];
export const currencyDefs = { "USD": { "code": "USD", "symbol": "$", "name": "US Dollors" }, "CAD": { "code": "CAD", "symbol": "CAD", "name": "Canada Dollors" }, "INR": { "code": "INR", "symbol": "INR", "name": "Indian Rupees" } };