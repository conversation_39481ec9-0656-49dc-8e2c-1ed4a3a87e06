import { _ } from 'src/app/util/title';
export const ALL_CARBRANDS = [
  {
    "key": "",
    "value": _('ngOption.SelectCarBrand'),
    "carcode": ""
  },
  {
    "key": "Car",
    "value": "Avis",
    "carcode": "AVS"
  },
  {
    "key": "Car",
    "value": "Budget",
    "carcode": "BGT"
  },
  {
    "key": "Car",
    "value": "Alamo",
    "carcode": "ALM"
  },
  {
    "key": "",
    "value": "National",
    "carcode": "NNL"
  },
  {
    "key": "",
    "value": "Enterprise",
    "carcode": "ENR"
  },
  {
    "key": "",
    "value": "Hertz",
    "carcode": "HRZ"
  },
  {
    "key": "",
    "value": "Dollar",
    "carcode": "DLR"
  },
  {
    "key": "",
    "value": "Thrifty",
    "carcode": "THR"
  },
  {
    "key": "",
    "value": "Payless",
    "carcode": "PYL"
  },
  {
    "key": "",
    "value": "Advantage",
    "carcode": "AVT"
  },
  {
    "key": "",
    "value": "Fox",
    "carcode": "FX"
  },
]
