export class BookingData {
    success: boolean;
    errors: any[];
    data: Data;
    policySet: boolean;
}
export class Data {
    upcoming: BookedOption[];
    past: BookedOption[];
    airlineNames: any = {};
    airports: any = {};
}

export class Hop {
    id: string;
    from: string;
    to: string;
    starts: Date;
    ends: Date;
    carrier: string;
    ocarrier: string;
    type: string;
    duration: string;
    terminal: string;
    flightNumber: string;
    pnr: string;
    imageUrl: string;
    avaailabiltyType: string;
    availabilitySource: string;
    classOfService: string;
    isBasicEconomy: boolean;
    allianceName: string;
    isLcc: boolean;
    fareBasisCode: string;
    BookingCode: string;
}

export class Leg {
    price: string;
    currency: string;
    duration: string;
    hops: Hop[];
    passportMandatory: boolean;
    zipcodeMandatory: boolean;
    flightHandlerType: string;
    legid: string;
    stops: string;
    lowToFairPrice: boolean;
    preferredAirline: boolean;
    preferredAirport: boolean;
    withinPricePolicy: boolean;
    isPassportMandatory: boolean;
    isZipcodeMandatory: boolean;
}

export class FlightOption {
    legid: string;
    price: string;
    discountedPrice: string;
    refundAmount: string;
    currency: string;
    legs: Leg[];
    layoverInfo: any[][];
    source: string;
    link: string;
    lowToFairPrice: boolean;
    preferredAirline: boolean;
    preferredAirport: boolean;
    withinPricePolicy: boolean;
    cancellationRequestReceived: boolean;
}

export class CancellationPolicy {
    errorMessage: any[];
    cancellationRule: string;
    success: boolean;
}

export class RoomOccupancyDTO {
    numberOfAdult: number;
    numberOfChildren: number;
    childrenAges?: any;
    adultsAges?: any;
}

export class HotelRate {
    hotelRateId: string;
    cancellationPolicies: CancellationPolicy[];
    roomOccupancyDTO: RoomOccupancyDTO;
}

export class HotelRoom {
    hotelRoomName: string;
    hotelRates: HotelRate[];
}

export class AMENITYTYPEBAR {
    code: number;
    name: string;
    chargeable: boolean;
}

export class AMENITYTYPEWIFI {
    code: number;
    name: string;
    chargeable: boolean;
}

export class AMENITYTYPEBKFAST {
    code: number;
    name: string;
    chargeable: boolean;
}

export class Amenities {
    AMENITY_TYPE_BAR: AMENITYTYPEBAR;
    AMENITY_TYPE_WIFI: AMENITYTYPEWIFI;
    AMENITY_TYPE_BKFAST: AMENITYTYPEBKFAST;
}

export class PhoneNumber {
    areaCode?: any;
    countryCode?: any;
    number: string;
    extension?: any;
}

export class HotelOption {
    source: string;
    refundAmount: string;
    link: string;
    bookingId: string;
    optimumPrice: number;
    hotelChain: string;
    hotelCode: string;
    hotelName: string;
    originalPrice: number;
    preferredbrand: boolean;
    distanceInMilesFromMeeting: number;
    hotelRooms: HotelRoom[];
    handleType: string;
    address: string;
    GDS: string;
    amenities: Amenities;
    rating: string;
    phoneNumber: PhoneNumber[];
    currency: string;
    minPrice: number;
    maxPrice: number;
    latitude: string;
    longitude: string;
    checkInDate: number;
    checkOutDate: number;
    stay: number;
    rateId: string;
    distance: number;
    walkingTime: number;
    inRange: boolean;
    inPolicy: boolean;
    isGreatPrice: boolean;
    nonrefundable: boolean;
    prepay: boolean;
    ratingBreakUp: string;
    gsaRate: number;
    gds: string;
    greatPrice: boolean;
    cancellationRequestReceived: boolean;
}

export class Option {
    flight_option: FlightOption;
    id: string;
    selected: boolean;
    expensified: boolean;
    status: string;
    selectTransId: string;
    hotel_option: HotelOption;
    car_option: any;
}

export class BookedOption {
    userid: string;
    approvalExpiryTime: any;
    primaryTraveller: any;
    ticketid: string;
    tripid: string;
    type: string;
    eventId: string;
    bookingDate: string;
    option: Option;
    billingType: string;
    destinationCity: string
    reviewedOn: Date
    reviewedBy: string;
    adminNote: string;
    approvalStatus: string;
    ticketnumber: any;
    tripStatus: string;
  groupTravelEventId: any;
}





