import { BookingData } from './bookingdata';
import * as LZString from 'lz-string';
import { find } from 'rxjs/operators';



export class OfflineTrips {



static getBookinglist():any{
    let tempBookingResp:any = localStorage.getItem('bookingList');
    if(tempBookingResp!==null){
        const decompressedString = LZString.decompress(tempBookingResp);

     
     tempBookingResp = JSON.parse(decompressedString);
    
    if(tempBookingResp && tempBookingResp.data.upcoming){
       return tempBookingResp;
    }
}
   
}
static removePastTrips(storedId,currentTripList){
   let index=0;
   for(let item of storedId){
      let findIndex = currentTripList.findIndex(trip => trip.data[0].transactionId===item.id)
      if(findIndex ===-1){
         storedId.splice(index, 1);
      }
      index++;
   }
   return storedId;
}
static getDetailsOfTrips(selectTransId):any{

let tempBookingResp:any =localStorage.getItem('detailList');
if(tempBookingResp){
    const decompressedString = LZString.decompress(tempBookingResp);

     
     tempBookingResp = JSON.parse(decompressedString);
     if(tempBookingResp){
let detailsObj = tempBookingResp.findIndex(item => item.id===selectTransId);
if(detailsObj  > -1){
   return tempBookingResp[detailsObj].details;
} 
     }
}
}
}