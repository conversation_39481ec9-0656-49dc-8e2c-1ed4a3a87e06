/* 
    Created on : 23 Jul, 2018, 11:07:35 AM
    Author     : bharat
*/

@import "../../variables.scss";

:host {
    width: 100vw;
}

.button-container {
    float: left;
    width: 100%;
}

.button {
    height: 50px;
    padding: 0 54px;
    border: none;
    text-transform: capitalize;
    font-size: 16px;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    letter-spacing: 0.86px;
}

.button-primary {
    cursor: pointer;
    background: $themeColor;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
}

.mdl-radio__ripple-container,
.mdl-radio__outer-circle,
.mdl-radio__inner-circle {
    display: none;
}

.mdl-radio {
    float: left;
    width: auto;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    height: 50px;
    padding: 16px 24px 15px 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.mdl-radio.is-checked {
    background: $themeColor;
    color: #fff;
}

.input-with-checkbox.disabled input {
    background-color: #E7E6E4;
    pointer-events: none;
}



.mdl-checkbox__ripple-container,
.mdl-checkbox__box-outline,
.mdl-checkbox__tick-outline,
.mdl-checkbox__focus-helper {
    display: none;
}

.mdl-checkbox {
    float: left;
    width: auto;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    height: 50px;
    padding: 15px 24px 15px 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
    margin-right: 8px;
    margin-bottom: 8px;
}

.mdl-checkbox:last-child {
    margin-right: 0;
}

.mdl-checkbox.is-checked {
    background: $themeColor;
}

.mdl-checkbox.checkbox-hover:hover {
    border-color: $themeColor;
}




/*placeholder*/
::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    opacity: 1;
    color: #AEAEAE;
}

::-moz-placeholder {
    /* Firefox 19+ */
    opacity: 1;
    color: #AEAEAE;
}

:-ms-input-placeholder {
    /* IE 10+ */
    opacity: 1;
    color: #AEAEAE;
}

:-moz-placeholder {
    /* Firefox 18- */
    opacity: 1;
    color: #AEAEAE;
}



.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
    background: $themeColor;
    border-color: $themeColor;
    color: inherit;

}

.bottom-button-margin {
    margin-top: 17px;
}


.ui-menu .ui-menu-item-wrapper {
    height: 45px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 14px;
    letter-spacing: 0.58px;
    line-height: 17px;
    font-family: $fontMono;
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 34px;
    padding-right: 34px;
    border-bottom: 1px solid #EDECEB;
    border-top: 0;
    border-left: 0;
    border-right: 0;
    color: $primaryColor  !important;
    margin: 0 !important;
}

.ui-menu-item-wrapper.ui-state-active,
.ui-menu-item-wrapper:hover {
    border-bottom: 1px solid #EDECEB !important;
    height: 45px !important;
    padding-top: 0;
    padding-bottom: 0;
    margin: 0 !important;
}



.ui-menu {
    box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.14);
    background-color: #FFFFFF;
    max-height: 265px;
    overflow-y: auto;
    overflow-x: hidden;
    border: none !important;
}


.chip {
    border: 2px solid var(--button-bg-color);
    background-color: #F7F7F7;
    margin-right: 8px;
    margin-top: 8px;
    width: auto;
    height: 50px;
    display: inline-flex;
    align-items: center;
    padding: 0 22px;
}

.chip .value {
    font-size: 16px;
    line-height: 20px;
    display: inline-block;
    vertical-align: middle;
}

.chip .remove {
    display: inline-block;
    vertical-align: middle;
    line-height: 0;
    margin-left: 10px;
    cursor: pointer;
}

.remove i {
    font-size: 15px;
    line-height: 15px;
}

.instructions-container {
    position: relative;
}

.instructions-container>img {
    cursor: pointer;
}

.instructions-card {
    background: #fff;
    width: 215.2px;
    height: 127px;
    padding: 20px 30px 20px 20px;
    position: absolute;
    left: 28px;
    top: -52px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.19);
    border-radius: 4px;
    font-size: 12px;
    line-height: 19px;
    font-family: $fontRegular;
    display: flex;
    justify-content: center;
    align-items: center;
    display: none;
}

.instructions-card:before {
    content: '';
    position: absolute;
    left: -7px;
    top: 55px;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;

    border-right: 7px solid #fff;
}

.close-popup {
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 10px;
}

.close-popup i {
    font-size: 15px;
}


.pac-item {
    font-size: 14px;
    font-family: $fontMono;
    height: 45px;
    display: flex;
    align-items: center;
}

.pac-item:hover,
.pac-item-selected {
    background: $themeColor;
}

/*background: $themeColor;*/

.chit {
    display: inline-flex;
    float: left;
    margin-right: 8px;
    background: $themeColor;
    height: 50px;
    border: 2px solid #E7E6E4;
    padding: 0 22px;
    justify-content: center;
    align-items: center;
}

.chit:last-child {
    margin-right: 0;
}


span.error {
    color: #f00;
    font-size: 12px;
}

@font-face {
    font-family: var(--globalFontfamilyr);
    src: url("../../assets/fonts/apercu_regular_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'apercu-i';
    src: url("../../assets/fonts/apercu_regular_italic_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'apercu-mono';
    src: url("../../assets/fonts/apercu_mono_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'apercu-m';
    src: url("../../assets/fonts/apercu_medium_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    src: url("../../assets/fonts/apercu_bold_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'apercu-mi';
    src: url("../../assets/fonts/apercu_medium_italic_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'apercu-l';
    src: url("../../assets/fonts/apercu_light_pro_000.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
}

/*common css starts*/
body {
    color: $primaryColor;
    margin: 0 auto;
    font-family: $fontMono;
    font-size: 16px;
    width: 100%;
    overflow-x: hidden;
}

.pageBody {
    margin: 0 auto;
    max-width: 1440px;
    width: 100%;
    background: #EEECEB;
    min-height: 100vh;
}

* {
    outline: none !important;
    list-style: none;
}

img {
    max-width: 100%;
    max-height: 100%;
}

a {
    color: $themeColor;
    cursor: pointer;
}

a:hover,
a:focus,
a:active {
    text-decoration: none;
    color: $themeColor;
}

p {
    margin-bottom: 0;
    font-size: inherit;
    line-height: normal;
}

ul {
    padding: 0;
    margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: $fontMono;
}

.themeColor2 {
    color: $themeColor2;
}

label {
    margin-bottom: 0;
}

*::-moz-focus-inner {
    border: 0 !important;
}

select:-moz-focusring {
    color: transparent;
    text-shadow: 0 0 0 #000;
}

.table-view {
    display: table;
}

.table-cell-view {
    display: table-cell;
    vertical-align: middle;
}

strong {
    font-weight: normal;
    font-family: $fontMedium;
}

b {
    font-weight: normal;
    font-family: $fontBold;
}

.inlineblock_m {
    display: inline-block;
    vertical-align: middle;
}

.inlineblock_b {
    display: inline-block;
    vertical-align: bottom;
}

.inlineblock_t {
    display: inline-block;
    vertical-align: top;
}

.middle {
    vertical-align: middle;
}

.top {
    vertical-align: top !important;
}

.container {
    width: 1340px;
}

.content {
    float: left;
    width: 100%;
    background: #EEECEB;
}

.card {
    background-color: #EEEDEB;
    box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
    float: left;
    width: 100%;
    margin-bottom: 24px;
    padding: 40px 47px 40px 47px;
}

.card-header {
    float: left;
    width: 100%;
    padding-bottom: 30px;
}

.card-header h3 {
    font-size: 22px;
    letter-spacing: -0.98px;
    line-height: 26px;
    margin: 0;
    display: inline-block;
}

.card-header p {
    font-size: 14px;
    line-height: 17px;
    font-family: $fontRegular;
    margin-top: 2px;
    margin-bottom: 1px;
}

.card-body {
    float: left;
    width: 100%;
}

.card-footer {
    height: 54px;
    background-color: #F7F7F7;
    float: left;
    width: 100%;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.input-box {
    float: left;
    width: 100%;
    margin-bottom: 23px;
    position: relative;
}

.label-inputbox {
    margin-bottom: 16px;
}

.input-label {
    float: left;
    width: 100%;
    color: #ABA7A4;
    font-size: 14px;
    line-height: 17px;
    margin-bottom: 8px;
    font-family: $fontRegular;
    font-weight: normal;
}

.input-textfield {
    float: left;
    width: 100%;
    border: 2px solid #E7E6E4;
    font-size: 16px;
    background-color: #FFFFFF;
    height: 50px;
    padding: 5px 16px;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
}

.input-textfield+.mdl-checkbox {
    width: auto;
    position: absolute;
    right: 19px;
    top: 38px;
}

.input-with-checkbox input {
    padding-right: 130px;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__label {
    font-size: 14px;
    line-height: 17px;
    font-weight: normal;
    font-family: $fontRegular;
}

.link-icon,
.link-text {
    display: inline-block;
    vertical-align: middle;
}

.mdl-checkbox__box-outline {
    border: 1px solid #979797;
    background-color: #FFFFFF;
    width: 17px;
    height: 17px;
    border-radius: 0;
}

.mdl-checkbox.is-checked .mdl-checkbox__box-outline {
    border: 1px solid $themeColor;
}

.mdl-checkbox.is-checked .mdl-checkbox__tick-outline {
    background-color: $themeColor;
}

/*common css ends*/


.header-left {
    padding-left: 15px;
}

/*user details page css starts*/

// .page-content{ padding-bottom: 62px; float:left; width: 100%; margin-top: -122px;}
.logo {
    line-height: 0;
}

.logo a {
    display: inline-block;
    line-height: 0;
}

header {
    float: left;
    background-size: auto 100%;
    width: 100%;
    /*background-image: url(../../assets/images/oval.svg)*/
    ;
    background-repeat: no-repeat;
    background-position: left center;
}

.header-inner {
    height: 374px;
    padding-top: 59px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.logo-icon {
    width: 214px;
    height: 71px;
    background-image: url(/../../assets/images/logo.png);
    background-repeat: no-repeat;
    display: inline-block;
    background-size: 214px;
}

.airlineSearch {
    width: 100%;
    max-width: 394px;
    padding-right: 40px;
    background-image: url(/../../assets/images/inactive-arrow.png);
    background-repeat: no-repeat;
    background-position: right 14px center;
}

.address-container {
    float: left;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.googleAddress {
    width: 100%;
    margin-right: 7px;
    max-width: 804px;
    padding-left: 46px;
    background-image: url(/../../assets/images/gallop-grey-icon.png);
    background-repeat: no-repeat;
    background-position: left 14px center;
}

.user-img {
    border-radius: 50%;
    overflow: hidden;
    height: 43px;
    width: 43px;
}

.user-name-img {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
}

.user-img img {
    width: 43px;
}

.user-name {
    font-size: 14px;
    line-height: 17px;
    font-family: $fontRegular;
    padding-left: 6px;
}

.down-icon {
    line-height: 0;
    margin-right: -5px;
}

.down-icon i {
    line-height: 10px;
    margin-top: 6px;
}

/*.user-img,.user-name,.down-icon{ display: inline-block; vertical-align: middle;}*/

.question-icon {
    font-family: arial;
    font-size: 14px;
    font-weight: bold;
    line-height: 14px;
    color: #000000;
    border: 1px solid #000;
    border-radius: 50%;
    height: 19px;
    width: 19px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.instructions-container.active .question-icon {
    background: $themeColor;
}

.overlay {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.5);
    display: none;
}

.toast-pref-page {
    left: 0;
    position: fixed;
    right: 0;
    text-align: center;
    top: 0;
    z-index: 9;
}

.toast-pref-page span {
    background: #fe6f53 none repeat scroll 0 0;
    color: #ffffff;
    display: inline-block;
    padding: 10px 18px;
    width: auto;
    font-size: 100%;
    border-radius: 4px;
    z-index: 9;
}


/*profile page css*/
.input-action-link {
    display: inline-flex;
    align-items: center;
    height: 50px;
}

.action-icon i {
    font-size: 30px;
}

.sec-email {
    float: left;
    width: 100%;
    margin-bottom: 8px;
}

.add-icon {
    display: none;
}

.remove-icon {
    display: none;
}

.sec-email:last-child .add-icon {
    display: inline-flex;
}

.sec-email-inner {
    max-width: 476px;
}

.sec-email-input {
    width: calc(100% - 90px);
    float: left;
}

.icons-container {
    width: 90px;
    float: left;
    padding-left: 20px;
}

.prev-password {
    position: relative;
    top: 4px;
}

@media (max-width:991px) {
    .closeChangePasswordLink {
        padding: 0 15px;
        display: inline-block;
    }
}

@media (max-width:767px) {
    .input-action-link {
        height: 40px;
    }

    .action-icon i {
        font-size: 24px;
    }

    .sec-email {
        margin-bottom: 0;
    }

    .sec-email-input {
        width: calc(100% - 65px);
    }

    .icons-container {
        width: 65px;
        padding-left: 10px;
    }
}

@media (max-width:1439px) and (min-width:1200px) {
    .container {
        width: 100%;
    }
}

@media (max-width:1199px) {
    .container {
        width: 970px;
    }

    .header-inner {
        height: 300px;
    }

    .logo-icon {
        width: 160px;
        height: 55px;
        background-size: 160px;
    }

    .instructions-card {
        left: -150px;
        top: -140px;
        z-index: 10;
    }


    .instructions-card::before {
        left: 50%;
        top: auto;
        border-top: 7px solid #fff;
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
        bottom: -16px;
        margin-left: 40px;

    }

    .header-left {
        padding-left: 0;
    }
}

@media (max-width:991px) {
    .container {
        width: 740px;
    }

    .header-inner {
        height: 230px;
        padding-top: 30px;
    }

    .logo-icon {
        width: 160px;
        height: 55px;
        background-size: 160px;
    }
}

@media (max-width:767px) {
    .airlineSearch {
        max-width: 100%;
    }

    .hotelSearch {
        max-width: 100%;
    }

    .container {
        width: 100%;
        padding: 0 15px;
    }

    .card-header h3 {
        font-size: 16px;
        letter-spacing: -0.71px;
        line-height: 16px;
    }

    .card-header p {
        font-size: 12px;
        line-height: 15px;
    }

    .input-label {
        font-size: 12px;
        line-height: 15px;
    }

    .input-textfield {
        height: 40px;
        font-size: 12px;
        padding: 5px 14px;
    }

    .appleDevice .input-textfield {
        font-size: 16px !important;
        letter-spacing: -1px;
    }

    .mdl-checkbox {
        height: 40px;
    }

    .mdl-checkbox__label {
        font-size: 12px;
        line-height: 15px;
    }

    .googleAddress {
        margin-right: 0;
        padding-left: 38px;
        padding-right: 30px;
    }

    .question-icon {
        position: absolute;
        right: 10px;
        top: -8px;
        z-index: 11;
    }

    .button {
        width: 100%;
        height: 46px;
        padding: 0 20px;
    }


    .card {
        background-color: #EEEDEB;
        box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
        float: left;
        width: 100%;
        margin-bottom: 0;
        padding: 20px 15px;
        padding-bottom: 48px;
        position: relative;
        min-height: calc(100vh - 125px);
    }

    .header-right {
        display: none;
    }

    .logo-icon {
        width: 87.27px;
        height: 30px;
        background-size: 87.27px;

    }

    .header-inner {
        height: 150px;
        padding-top: 28px;
        padding-left: 14px;
    }

    .page-content {
        margin-top: -41px;
        padding-bottom: 15px;
    }

    .mdl-checkbox {
        padding-top: 10px;
        padding-bottom: 10px;
    }

    .airlineCheckboxContainer {
        display: none;
    }

    .bookingButtonContainer {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        margin-top: 0;
    }

    .chip {
        height: 40px;
        padding: 0 14px;
    }

    .chip .value {
        font-size: 12px;
        line-height: 15px;
    }

    .searchContainer.active .complete-button {
        display: block;
    }

    .ui-menu {
        background: #EEECEB;
        box-shadow: none;
    }

    .ui-menu .ui-menu-item-wrapper {
        border-color: #D6D6D6;
    }


    .instructions-card {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        top: auto;
        z-index: 99;
        width: auto;
        font-size: 14px;
        line-height: 22px;
        height: 115px;
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.19);
        border-radius: 0;
        z-index: 100;
        align-items: center;
    }

    .overlay.instructionsClass {
        display: block;
        z-index: 99;
    }

    .instructions-container.active .question-icon {
        background: #fff;
    }

    .question-icon {
        height: 16px;
        width: 16px;
        line-height: 12px;
        font-size: 12px;
        color: $primaryColor;
        border: 1px solid $primaryColor;
    }

    .overflowHidden {
        overflow: hidden;
    }

    .ui-menu {
        max-height: calc(100vh - 90px);
    }
}

@media (max-width:400px) {

    /*    .mdl-checkbox{ width: 100%; margin-right: 0;}
        .logo-icon {
        width: 100px;
        height: 34px;
        background-size: 100px;
    }*/

}



.searchContainer.active {
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #EEECEB;
}

.complete-button {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: none;
}

.searchContainer.active .airlineSearch {
    border: none;
    border-bottom: 2px solid #E7E6E4;
    height: 42px;
    position: relative;
    z-index: 99;
}

.searchContainer.active .hotelSearch {
    border: none;
    border-bottom: 2px solid #E7E6E4;
    height: 42px;
    position: relative;
    z-index: 99;
}

.searchContainer.active .selected-airlines {
    padding-left: 16px;
    padding-right: 16px;
}

.searchContainer.active .selected-hotels {
    padding-left: 16px;
    padding-right: 16px;
}