<div class="card-div active shadow">
    <div class="card-div-inner">
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="text-align: left;margin-bottom:20px;">
                <img class="image11" style="display: none;" src="assets/images/employees_purple.png">
                <div class="view11"><span class="view" style="display: none;"> {{ 'dashboardWrapper.Travellers' | translate }}</span><span class="line1" style="display: none;"></span>
                    <a [routerLink]="['/admin']" [queryParams]="{type: 'employees',subType:'departments'}" routerLinkActive="active"> <span attr.data-params="tab=Departments" attr.data-track="Travelers" class="{{ viewMode2=='tab21' ? 'items':'items1'}}" (click)="TabClicked('tab21','departments')"> {{ 'employee.Departments' | translate }}</span></a>
                    <a [routerLink]="['/admin']" [queryParams]="{type: 'employees',subType:'companyTravelers'}" routerLinkActive="active"> <span attr.data-params="tab=CompanyTravelers" attr.data-track="Travelers" class="{{ viewMode2=='tab22' ? 'items':'items1'}}" (click)="TabClicked('tab22','companyTravelers')"> {{ 'employee.CompanyTravelers' | translate }}</span></a>
                    <a [routerLink]="['/admin']" [queryParams]="{type: 'employees',subType:'activeTravelers',subSubType:'today'}" routerLinkActive="active"><span *ngIf="isDutyOfCareEnabled()"
              attr.data-params="tab=ActiveTravelers" attr.data-track="Travelers" class="{{ viewMode2=='tab23' ? 'items':'items1'}}" (click)="TabClicked('tab23','activeTravelers','today')"> {{ 'activeTraveler.ActiveTravelers' | translate }}</span></a></div>
            </div>
        </div>
        <div *ngIf="viewMode2=='tab21'" class="tab-content">
            <div class="row" style="text-align: left;margin-bottom:20px;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="text-align: left;margin-left: -8px;">
                    <div class="view12">
                        <button *ngIf="canShowAddDepartment()" class="btn btn-normal" style=" font-size:14px !important;margin-top:0px !important;"><span
              style=" font-size:14px !important;margin-top:0px !important;" class="add1"
              (click)="onAddDepartment(addDepartmentModal,'');this.addDepartmentModelMode='add'">{{ 'employee.ADDDEPARTMENT' | translate}}</span></button>
                    </div>
                </div>
            </div>
            <div *ngIf="departmentArray && departmentArray.length === 0">
                <span class="error" style="margin-top:20px !important;margin-bottom:50px !important;font-size: 18px !important;color:gray !important;">
        {{ 'employee.Nodepartmenthasbeenaddedyet' | translate}}</span>
            </div>
            <div *ngIf="departmentArray && departmentArray.length > 0" class="section" style="margin-bottom:50px !important;">
                <div class="filter-row2 table-header-bg" style="white-space:nowrap;padding-top: 5px;justify-content: space-between">
                    <div class="col-auto" style="width:256px;">
                        <div *ngIf="false" class="checkbox-container1" style="margin-top:4px !important;margin-bottom:4px !important;">
                            <label class="" for="'All Dept'">
              <input type="checkbox" id="'All Dept'" style="cursor:pointer !important;" class="mdl-checkbox__input"
                value="All Dept" (change)="onChangeDepartment('All dept',$event.target.checked)"
                [checked]="isDeptChecked('All dept')">
              <span class="mdl-checkbox__label" style="padding-left: 0px !important;"></span>
            </label>

                        </div>
                        <span class="show" style="display: inline !important;padding-top: 0px !important;"> {{ 'employee.DepartmentName' | translate}}</span>
                    </div>
                    <div class="col-auto" style="width:280px;">
                        <span class="show"> {{ 'employee.DefaultApprover' | translate}}</span>
                    </div>
                    <div class="col-auto" style="width:280px;">
                        <span class="show"> {{ 'employee.PrimaryPolicy' | translate}}</span>
                    </div>
                    <div *ngIf="false" class="col-auto" style="width:220px;">
                        <span class="show"> {{ 'employee.Centralbillingmethod' | translate}}</span>
                    </div>
                    <div *ngIf="false" class="col-auto" style="width:335px;">
                        <span class="show"> {{ 'employee.Carrentalbillingnumbers' | translate}}</span>
                    </div>
                    <div class="col-auto" style="width:90px;">

                    </div>
                </div>
                <div *ngFor="let dept of departmentArray,let i=index;">
                    <div class="{{i%2==1 ? 'filter-row3':'filter-row2'}}" [ngStyle]="{'height': (isBillingNUmberExis(dept) > 2 || (dept && dept.approvers &&  dept.approvers.length >1)) ? 'max-content':'42px'}" style="justify-content: space-between">
                        <div class="col-auto" style="width:256px;padding-top:8px;cursor:pointer !important;">
                            <div *ngIf="false" class="checkbox-container1" style="margin-top:4px !important;margin-bottom:4px !important;">
                                <label class="" for="{{dept.departmentId}}">
                <input type="checkbox" style="cursor:pointer !important;" id="{{dept.departmentId}}"
                  class="mdl-checkbox__input" (change)="onChangeDepartment(dept.departmentId,$event.target.checked)"
                  [checked]="isDeptChecked(dept.departmentId)">
                <span class="mdl-checkbox__label" style="padding-left:0px;"></span>
              </label>

                            </div>
                            <span class="show1">{{dept.name}}</span>
                        </div>
                        <div class="col-auto" style="width:280px;padding-top:12px;">
                            <span *ngFor="let item1 of  dept.approvers;let i=index">
              <span class="showDeptEmail">{{item1}} </span>
                            <div style="white-space: pre-line !important;"></div>
                            </span>
                        </div>
                        <div class="col-auto" style="width:280px;padding-top:12px;">
                            <span class="show1">{{getPolicyName(dept.defaultPolicyId)}}</span>
                        </div>
                        <div *ngIf="getCardBrand(dept) !=='' && false" class="col-auto" style="width:220px;padding-top:10px;padding-left: 25px;">
                            <img class="inlineblock_m" style="width:35px;" src="https://s3.amazonaws.com/images.biztravel.ai/cc/{{getCardBrand(dept)}}.svg" />
                            <span style="font-size:12px; " class="inlineblock_m">{{getCardBrand(dept)}}-{{getCardLast4(dept)}}</span>
                        </div>
                        <div *ngIf="false" class="col-auto" style="width:180px;padding-top:15px;padding-left: 25px;">
                        </div>
                        <div *ngIf="false && !dept.discount_code" class="col-auto" style="width:335px;padding-top:10px;padding-left: 25px;">
                        </div>
                        <div *ngIf="false && dept.discount_code && dept.discount_code.length ==0" class="col-auto" style="width:335px;padding-top:10px;padding-left: 25px;">
                        </div>
                        <div *ngIf="false && dept.discount_code && dept.discount_code.length >0" class="col-auto" style="width:335px;padding-top:10px;padding-left: 25px;">
                            <span *ngFor="let item of dept.discount_code;let i=index">
              <span style="position: relative;" *ngIf="item.billingNumber"
                [ngStyle]="{'top': (isBillingNUmberExis(dept) > 2)  ? '-10px':'-14px'}">
                <div style="display: inline-block;height:20px;width:70px;margin-left: 3px;margin-right:3px;"><img
                    class="inlineblock_m" style="max-height:20px;max-width:70px;"
                    src="https://s3.amazonaws.com/images.biztravel.ai/ota/carrentals/{{item.partnerCode}}.png" /></div>
                <span class="show11" *ngIf="item.billingNumber">{{item.billingNumber}} </span>
                            <div *ngIf="(i >0  && (i%2===1))" style="white-space: pre-line;"></div>
                            </span>
                            </span>
                        </div>

                        <div class="col-auto" style="width:90px;cursor:pointer; padding-top:10px;">
                            <img src="assets/images/icon_edit.svg" style="margin-right:10px;" (click)="onEditDepartment(editDepartmentModal,i);this.addDepartmentModelMode='edit'">
                            <img *ngIf="canShowAddDepartment()" src="assets/images/ic_delete.svg" (click)="onDeleteDepartment(removeDepartmentModal,i)">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="false" class="card-div-inner">
            <div class="row">
                <div class="col-lg-8 col-md-6 col-sm-4 col-xs-4" style="text-align: left;">
                    <span class="view"> Approvers</span>

                </div>
            </div>
            <div *ngIf="this.managers && this.managers.length > 0" class="section">
                <div class="filter-row table-header-bg" style="white-space:nowrap;padding-top: 10px;">
                    <div class="col-auto" style="width:80px;">
                        <span class="show">First Name</span>
                    </div>
                    <div class="col-auto" style="width:80px;">
                        <span class="show">Last Name</span>
                    </div>
                    <div class="col-auto" style="width:118px;">
                        <span class="show">Email</span>
                    </div>
                    <div class="col-auto" style="width:95px;">
                        <span class="show">Department</span>
                    </div>
                    <div class="col-auto" style="width:95px;">
                        <span class="show">Policy</span>
                    </div>
                    <div class="col-auto" style="width:100px;">
                        <span class="show">Approver</span>
                    </div>
                    <div class="col-auto" style="width:90px;">
                        <span class="show">Payment Options</span>
                    </div>
                    <!--   <div class="col-auto"style="width:90px;">

                          </div>-->
                    <div class="col-auto" style="width:90px;">

                    </div>

                </div>
                <div *ngFor="let item of this.managers,let i=index;">
                    <div class="{{i%2==1 ? 'filter-row1':'filter-row'}}">
                        <div class="col-auto" style="width:80px; padding-top:10px;">
                            <span class="show1" style="width:80px !important ;">{{item.firstName}}</span>
                        </div>
                        <div class="col-auto" style="width:70px; padding-top:10px;">
                            <span class="show1" style="width:80px !important;">{{item.lastName}}</span>
                        </div>
                        <div class="col-auto" style="width:150px; padding-top:10px;">
                            <span class="show1" style="width:150px !important;">{{item.email}}</span>
                        </div>
                        <div class="col-auto" style="width:95px; padding-top:10px;">
                            <span class="show1" style="width:95px !important;">{{item.department}}</span>
                        </div>
                        <div class="col-auto" style="width:95px; padding-top:10px;">
                            <span class="show1" style="width:95px !important;">{{item.policy}}</span>
                        </div>
                        <div class="col-auto" style="width:100px; padding-top:10px;">
                            <span class="show1" style="width:100px !important;">{{item.Manager}}</span>
                        </div>
                        <div class="col-auto" style="width:90px; padding-top:10px;">
                            <span class="show1" style="width:90px!important;margin-right:10px">{{item.paymentOption}}</span>
                        </div>
                        <!-- <div *ngIf="item.verified"class="col-auto"style="width:90px; padding-top:10px;">
                             <span class="primary1"style="width:90px !important;">Book Travel</span>
                             </div>-->
                        <!-- <div  class="col-auto"style="width:90px; padding-top:10px;">
                              <span class="primary1"*ngIf="item.role!='ADMIN' && !item.verified"style="width:90px !important;"(click)="showDeleteModal(resendLinkModal,i)">Resend Invite</span>
                              </div>-->
                        <div class="col-auto" style="width:90px;cursor:pointer; padding-top:10px;">
                            <img src="assets/images/icon_edit.svg" style="margin-right:10px;" (click)="showModal(i,true)">
                            <img *ngIf="showDeleteEmployee(item.role)" src="assets/images/ic_delete.svg" (click)="showDeleteModal(removeEmployeeModal,i)">
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="viewMode2=='tab22'" class="tab-content">
            <div class="row">
                <div class="col-lg-5 col-md-5 col-sm-4 col-xs-4" style="text-align: left;width:800px !important;margin-left: -8px;">
                    <div class="view12"><i *ngIf="this.filterEmployee && this.filterEmployee.length > 0" class="fa fa-download" (click)="downloadEmployeeList()" aria-hidden="true" style="margin-left:35px;cursor: pointer;"></i>
                        <button class="btn btn-normal" style=" font-size:14px !important;margin-top:0px !important;"><span
              style=" font-size:14px !important;margin-top:0px !important;" class="add1"
              (click)="checkEmployeeDetail()"> {{ 'employee.ADDUSER' | translate}}</span></button>
                        <button *ngIf="this.employeeSelecttoDelete && this.employeeSelecttoDelete.length > 0" class="btn btn-normal" style=" font-size:14px !important;margin-top:0px !important;"><span
                style=" font-size:14px !important;margin-top:0px !important;" class="add1"
                (click)="deleteMultipleEmployee(removeEmployeeModal)"> {{ 'employee.DeleteSelected' | translate}}</span></button>
                    </div>
                </div>
                <div *ngIf="(this.departmentArrayForDropdwon && this.departmentArrayForDropdwon.length > 1)" class="col-lg-4 col-md-4 col-sm-4 col-xs-4">
                    <div class="input" style="overflow: hidden;">
                        <div id="selectDepartmentDivEmployee">
                            <ng-select #departmentlist appendTo="#selectDepartmentDivEmployee" style="bottom: 5px;" (click)="this.dropDownopen =true;" dropdownPosition="middle" [searchable]="true" (close)="closeDropdown()" [closeOnSelect]="false" [clearable]="false" [items]="this.departmentArrayForDropdwon"
                                [searchFn]="searchByDepartmentNameAndId" [(ngModel)]="search" [ngModelOptions]="{standalone: true}">
                                <ng-template ng-label-tmp let-item="item">
                                    <span> {{getLabelValue1() | translate}}</span>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                                    <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="{{item.departmentId}}" style="height: 36px !important;min-width: 331px !important;margin-bottom: 0px !important;">
                <input type="checkbox" id="{{item.departmentId}}" class="mdl-checkbox__input" style="cursor: pointer;"
                (change)="onChangeDepartment(item.departmentId,$event.target.checked)"
                [checked]="isDeptChecked(item.departmentId)">
                <span class="mdl-checkbox__label"
                  style="font-family: var(--globalFontfamilyr) !important;padding-left: 10px !important;font-size: 14px;top: -2px;padding-bottom: 20px;"><span
                    style="width: 300px !important;min-width: 300px !important;text-overflow: ellipsis;display: inline-block;overflow: hidden;position: relative;
                        top: 8px;">{{item.name | translate}} </span>
                 </span>
              </label>

                                </ng-template>
                            </ng-select>
                            <div class="select-overlay"></div>

                            <div *ngIf="this.temp_filter_employee.length > 0 && !this.dropDownopen" class="modalApprover" (click)="this.dropDownopen =true;filterSelectedDeptlist(this.temp_filter_employee);departmentlist.toggle()" [ngStyle]="{'top' : this.adminPanelService.employeePopOpen ? '-60px':'-80px'}">
                                <span *ngFor="let item1 of this.temp_filter_employee;let i=index">
                    <span class="shown1">{{getDepartmentName(item1) | translate}}<span *ngIf="((i !== (this.temp_filter_employee.length-1)))">,
                      </span></span>
                                </span>
                            </div>
                        </div>

                        <svg class="down-arrow1" [ngStyle]="{'top' : this.adminPanelService.employeePopOpen ? '13px':'13px'}" (click)="this.dropDownopen =true;filterSelectedDeptlist(this.temp_filter_employee);departmentlist.toggle()" width="15" height="9" viewBox="0 0 15 9"
                            fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                fill="#8936F3" />
            </svg>
                    </div>

                </div>
                <div *ngIf="(this.filterEmployee && this.filterEmployee.length > 0) || empNameSearchValue !==''" class="col-lg-3 col-md-3 col-sm-4 col-xs-4">
                    <input [(ngModel)]="empNameSearchValue" type='text' class="search-box" placeholder="{{ 'employee.Searchbyname' | translate}}" (input)="searchByNameChanged($event.target.value)" />
                </div>
            </div>
            <div *ngIf=" this.filterEmployee && this.filterEmployee.length === 0">
                <span class="error" style="margin-top:20px !important;font-size: 18px !important;color:gray !important;"> {{
        this.errormsg }}</span>
            </div>
            <app-loader *ngIf="(this.applyButton1 && this.filterEmployee) && this.filterEmployee.length === 0" [spinnerStyle]="true"></app-loader>
            <div *ngIf="this.filterEmployee && this.filterEmployee.length > 0" class="section">
                <div class="filter-row table-header-bg" style="white-space:nowrap;padding-top: 5px;">
                    <div class="col-auto" style="width:80px;">
                        <div class="checkbox-container1" style="margin-top:4px !important;margin-bottom:4px !important;">
                            <label class="" for="'All employee'">
              <input type="checkbox" id="'All employee'" style="cursor:pointer !important;" class="mdl-checkbox__input"
                value="All employee" (change)="onChangeEmployee('All employee',$event.target.checked)"
                [checked]="isDeleteEmployeeChecked('All employee')">
              <span class="mdl-checkbox__label" style="padding-left: 0px !important;"></span>
            </label>

                        </div>
                        <span class="show" style="display: inline !important;padding-top: 0px !important;"> </span>
                    </div>
                    <div class="col-auto" style="width:80px;">
                        <span class="show" style="padding-left: 5px !important;overflow:inherit;"> {{ 'employee.FirstName' | translate}}</span>
                    </div>
                    <div class="col-auto" style="width:90px;">
                        <span class="show" style="overflow:inherit;">{{ 'employee.LastName' | translate}}</span>
                    </div>
                    <div class="col-auto" style="width:165px;">
                        <span class="show" style="overflow:inherit;"> {{ 'employee.Email' | translate}}</span>
                    </div>
                    <div class="col-auto" style="width:95px;">
                        <span class="show" style="overflow:inherit;"> {{ 'employee.Department' | translate}}</span>
                    </div>
                    <div class="col-auto" style="width:100px;">
                        <span class="show" style="overflow:inherit;"> {{ 'employee.Role' | translate}}</span>
                    </div>
                    <div class="col-auto" style="width:115px;">
                        <span class="show" style="overflow:inherit;"> {{ 'employee.Policy' | translate}}</span>
                    </div>
                    <div class="col-auto" style="width:180px;">
                        <span class="show" style="overflow:inherit;"> {{ 'employee.Approver' | translate}}</span>
                    </div>
                    <div class="col-auto" style="width:90px;">
                        <span class="show" style="overflow:inherit;"> {{ 'employee.PaymentOptions' | translate}}</span>
                    </div>
                    <!--   <div class="col-auto"style="width:90px;">
  
                            </div>-->
                    <div class="col-auto" style="width:90px;">

                    </div>

                </div>
                <div *uiScroll="let item of flightsDatasource;let i = index"  class="people_user_table">
                    <div class="{{i%2==1 ? 'filter-row1':'filter-row'}}" [ngStyle]="{'height': (item.approvers && item.approvers.length >1) ? 'max-content':'42px'}">
                        <div class="col-auto" style="width:80px;padding-top:8px;cursor:pointer !important;">
                            <div *ngIf="showDeleteEmployee(item.email)" class="checkbox-container1" style="margin-top:4px !important;margin-bottom:4px !important;">
                                <label class="" for="{{item.email}}">
                <input type="checkbox" style="cursor:pointer !important;" 
                  class="mdl-checkbox__input" (change)="onChangeEmployee(item.email,$event.target.checked)"
                  [checked]="isDeleteEmployeeChecked(item.email)">
                <span class="mdl-checkbox__label" style="padding-left:0px;"></span>
              </label>

                            </div>
                        </div>
                        <div class="col-auto" style="width:80px; padding-top:10px;padding-left: 0px;">
                            <span class="show1" style="width:80px !important ;">{{item.firstName}}</span>
                            <span class="show22" *ngIf="item.firstName != ''">{{item.firstName}}</span>
                        </div>
                        <div class="col-auto" style="width:90px; padding-top:10px;padding-left: 5px;padding-right: 5px;">
                            <span class="show1" style="width:90px !important;">{{item.lastName}}</span>
                            <span class="show22" *ngIf="item.lastName != ''">{{item.lastName}}</span>
                        </div>
                        <div class="col-auto" style="width:165px; padding-top:10px;padding-left: 0px;">
                            <span class="show1" style="width:165px !important;padding-left: 22px !important;">{{item.email}}</span>
                            <span class="show22" *ngIf="item.email != ''">{{item.email}}</span>
                        </div>
                        <div class="col-auto" style="width:95px; padding-top:10px;">
                            <span class="show1" style="width:95px !important;">{{item.department}}</span>
                            <span class="show22" *ngIf="item.department.email != ''">{{item.department}}</span>
                        </div>
                        <div class="col-auto" style="width:100px; padding-top:10px;">
                            <span class="show1" style="width:100px !important;">{{getRole(item.role)}}</span>
                            <span class="show22" *ngIf="getRole(item.role) != ''">{{getRole(item.role)}}</span>
                        </div>
                        <div class="col-auto" style="width:115px; padding-top:10px;">
                            <span class="show1" style="width:115px !important;">{{item.policy}}</span>
                            <span class="show22" *ngIf="item.policy != ''">{{item.policy}}</span>
                        </div>
                        <div class="col-auto" style="width:180px; padding-top:10px;padding-left:15px;">
                            <span *ngFor="let item1 of  item.approvers;let i=index" class="people_approvers_table">
                                <span class="show111">{{item1}}</span>
                            <span class="showOnhover22" *ngIf="item1 != ''">{{item1}}</span>
                            <div style="white-space: pre-line !important;"></div>
                            </span>
                        </div>
                        <div class="col-auto" style="width:90px; padding-top:10px;">
                            <span class="show1" style="width:90px!important;margin-right:10px">{{item.paymentOption}}</span>
                            <span class="show22" *ngIf="item.paymentOption != ''">{{item.paymentOption}}</span>
                        </div>
                        <!-- <div *ngIf="item.verified"class="col-auto"style="width:90px; padding-top:10px;">
                               <span class="primary1"style="width:90px !important;">Book Travel</span>
                               </div>-->
                        <!-- <div  class="col-auto"style="width:90px; padding-top:10px;">
                                <span class="primary1"*ngIf="item.role!='ADMIN' && !item.verified"style="width:90px !important;"(click)="showDeleteModal(resendLinkModal,i)">Resend Invite</span>
                                </div>-->
                        <div class="col-auto" style="width:90px;cursor:pointer; padding-top:10px;">
                            <img src="assets/images/icon_edit.svg" style="margin-right:10px;" (click)="showModal(i)">
                            <img *ngIf="showDeleteEmployee(item.email)" src="assets/images/ic_delete.svg" (click)="showDeleteModal(removeEmployeeModal,i)">
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="viewMode2=='tab23'" class="tab-content">
            <div class="row" style="margin-top: 30px;margin-left:10px !important;">
                <div class="col-lg-9 col-md-9 col-sm-12" style="padding-right: 0px !important;text-align: end;">
                    <div class="daysSelect">
                        <div attr.data-params="tab=TODAY" attr.data-track="ActiveTravellers" (click)="getData('today')" style="padding-top: 5px !important;display:inline-block;margin-right: 10px;">
                            <span attr.data-params="tab=TODAY" attr.data-track="ActiveTravellers" class="{{this.selectDateRange ==='today' ? 'select':'unselect'}}">  {{'activeTraveler.TODAY' | translate }}</span>
                        </div>
                        <div attr.data-params="tab=NEXT7DAYS" attr.data-track="ActiveTravellers" (click)="getData('7Days')" style="padding-top: 5px !important;display:inline-block;margin-right: 10px;">
                            <span attr.data-params="tab=NEXT7DAYS" attr.data-track="ActiveTravellers" class="{{this.selectDateRange ==='7Days' ? 'select':'unselect'}}">  {{'activeTraveler.NEXT7DAYS' | translate }}</span>
                        </div>
                        <div attr.data-params="tab=NEXT30DAYS" attr.data-track="ActiveTravellers" (click)="getData('30Days')" style="padding-top: 5px !important;display:inline-block;margin-right: 10px;">
                            <span attr.data-params="tab=NEXT30DAYS" attr.data-track="ActiveTravellers" class="{{this.selectDateRange ==='30Days' ? 'select':'unselect'}}"> {{'activeTraveler.NEXT30DAYS' | translate }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-12" style="padding-left: 15px !important;padding-right:0;">
                    <div class="" style="display: flex;">
                        <label class="showPeriod" style="">{{'activeTraveler.Period' | translate }}:</label>
                        <div class="filter custom-selectbox">
                            <div class="input-box" style="">
                                <span (click)="openNgxModal('daterangeSelection1',chartDatePicker);chartDatePicker.show()" class="input-icon material-icons">event</span>
                                <input class="input1" id="daterangeSelection1" style="min-width:210px !important;" (click)="openNgxModal('daterangeSelection1',chartDatePicker);chartDatePicker.show()" readonly=""><span class="dateShow" (click)="openNgxModal('daterangeSelection1',chartDatePicker);chartDatePicker.show()">{{daterangepickerModel[0]
                      | date : 'dd MMM yyyy'}} - {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span>

                            </div>
                            <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'daterangeSelection1')" [hideDelay]="0" (onClose)="handleModalEvents('onClose', 'daterangeSelection1')" (onDismiss)="handleModalEvents('onDismiss', ' daterangeSelection1')" [closable]="false" #daterangeSelection1
                                identifier="daterangeSelection1">
                                <div class="modal-container flight-modal-container filter-modal1 modalAirportFilterInfo" [ngStyle]="changeStyle1()" (click)="$event.stopPropagation();">
                                    <div class="modal-header" style="background-color: #fff !important;padding-top:10px !important;">
                                        <div class="tab-list top-strip" style="">

                                            <ul>
                                                <li class="{{ viewMode1 == 'tab11' ? 'tab-list-item1':'tab-list-item'}}" [class.active]="viewMode1 == 'tab11'" rel="tab14" (click)="customTabClicked()">{{ 'report.Custom' | translate }}
                                                    <div *ngIf="viewMode1 == 'tab11'" class="underline"></div>
                                                </li>
                                            </ul>
                                        </div>
                                        <input class="input1" [hidden]="viewMode1=='tab12'" style="width:100% !important;position: relative;top:8px;" readonly=""><span class="dateShow1" [hidden]="viewMode1=='tab12'" style="top:23px !important;">{{daterangepickerModel[0] | date : 'dd MMM
                          yyyy'}} - {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span>
                                    </div>
                                    <hr>
                                    <div [hidden]="viewMode1=='tab12'" style="position: relative;top: -30px;">
                                        <input class="input" bsDaterangepicker #chartDatePicker="bsDaterangepicker" style="width:100% !important;visibility: hidden;" [(ngModel)]="daterangepickerModel" (bsValueChange)="setStartDate($event)" [outsideClick]="true" [minDate]="minimumDate" [bsConfig]="{ showWeekNumbers: false , showPreviousMonth: true }"
                                            (onShown)="onShowPicker($event, chartDatePicker)" (onHidden)="onHidePicker()" container="" readonly />
                                    </div>
                                    <div class="modal-body" [ngStyle]="changeStyle2()">
                                        <div [hidden]="viewMode1=='tab11'">
                                            <div *ngFor="let item of this.dateOptions1;let i=index" style="width: auto;min-height: 15px;display:flex;line-height: 3em;min-height: 3em;">
                                                <div style="font-size:14px;color:#5f6368;padding-left:22px;margin-bottom:7px;cursor: pointer;" (click)="getSelectedCurrentDate1(item.id)">{{item.value}}</div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </ngx-smart-modal>
                        </div>

                    </div>
                </div>
            </div>
            <div style="text-align: center !important;">
                <app-loader *ngIf="this.applyBtn" [spinnerStyle]="true"></app-loader>
            </div>
            <div class="row" *ngIf="!this.applyBtn">
                <div class="col-lg-9 col-md-9 col-sm-12" style="padding-right: 0px !important;">
                    <agm-map [gestureHandling]="'greedy'" [fitBounds]="this.activeTravellersList.length >0 ? true:false" [zoom]="zoom" [streetViewControl]="false" [zoomControl]="!isMobile" (boundsChange)="boundsChangedCallback($event)" (mapReady)="mapReadyCallback($event)"
                        (zoomChange)="zoomChanged($event)">
                        <agm-marker class="marker-class" *ngFor="let data of this.activeTravellersList; let i = index" [latitude]="data.latitude" [longitude]="data.longitude" [label]="{color: data.markerClicked ? 'black':'white', fontSize:'10px', fontFamily:'apercu-mono',text: getMarkerName(data)}"
                            [zIndex]="data.markerClicked?100:i" [agmFitBounds]="data.fitBounds" (markerClick)="markerClick($event,data,i)"></agm-marker>
                    </agm-map>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-12">
                    <div class="listTraveler">
                        <input [(ngModel)]="empNameSearchValue" type='text' (focus)="this.boxSelect=true;" (focusout)="this.boxSelect=false;" [ngStyle]="{'border-color': this.boxSelect ? this.searchService.darkBgColor:'#F7F7F7'}" class="search-boxACtiveTraveler" placeholder="{{'activeTraveler.Searchbyname' | translate }}"
                            (input)="searchByNameActiveChanged($event.target.value)" />
                        <div *ngIf="this.activeTravellersList && this.activeTravellersList.length===0" style="text-align:center !important;margin-top:15px !important;">
                            <div class="text1" style="font-size: 18px;">"{{resultErrorMessage1 | translate}}"</div>
                        </div>
                        <div *ngFor="let item of this.activeTravellersList; let i = index" style="display: inline-flex;text-align: center;padding-left: 7px !important;margin-bottom: 0px !important;">
                            <div (mouseover)="changeStyle11($event,i)" (mouseout)="changeStyle11($event,i)" class="{{ (this.hover && this.number===i) ? 'search-box2':'search-box1'}}" style="padding-left: 7px !important;" [ngStyle]="{'margin-bottom': (i===this.activeTravellersList.length -1) ? '20px':'none'}"
                                (click)="markerClick($event,item,i)">
                                <span class="circle"><span style="margin-top: 7px;">{{ item.shortname}}</span></span>
                                <div class="name truncate"  data-toggle="tooltip" data-placement="top" title="{{item.name}}">{{ item.name}}</div>
                                <div class="name1">{{ item.destinations }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <button [hidden]="true" #fakeOnEditDepartmentDiv class="btn btn-secondary" (click)="onEditDepartment(editDepartmentModal, undefined)"></button>

    <ng-template #removeEmployeeModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                {{ 'employee.Removinganemployee' | translate}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <div class="modal-body" style=" text-align: left !important;">
            <span class="heading">  {{ 'employee.ConfirmRemoval' | translate}}</span>
            <div class="heading1"> {{ 'employee.Areyousureyouwanttoremove' | translate}} <span *ngIf="this.employeeSelecttoDelete.length > 1">{{ 'employee.theseemployees' | translate}}</span> <span *ngIf="this.employeeSelecttoDelete.length === 1"> {{ 'employee.thisemployees' | translate}}</span>                {{ 'employee.fromcompanyaccount?' | translate}}</div>
            <div class="heading1" *ngFor="let item of this.employeeSelecttoDelete">{{item.firstName}}
                <span *ngIf="item.lastName && item.lastName !==''">{{item.lastName}},</span> {{item.department}}</div>
            <button *ngIf="!this.deleteButton" class="btn btn-secondary" (click)=deleteEmployee()><span class="add"> {{ 'employee.CONFIRMDELETE' | translate}}
         </span></button>
            <button *ngIf="this.deleteButton" class="btn btn-secondary" [disabled]="this.deleteButton"><span class="add"
          (click)=deleteEmployee()>  {{ 'employee.WAIT' | translate}}</span></button>
            <button class="btn btn-normal" (click)="onModelCancel()"><span class="add1">  {{ 'employee.Cancel' | translate}}</span></button>
            <div class="modal-form-button">
            </div>
        </div>

    </ng-template>

    <ng-template #removeDepartmentModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                {{ 'employee.Deletedepartment' | translate}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <div class="modal-body" style=" text-align: left !important;">
            <span class="heading"> {{ 'employee.ConfirmDeleteDepartment' | translate}}</span>
            <div class="heading1"> {{ 'employee.Areyousureyouwanttodelete' | translate}} {{selectedDepartment.name}} {{ 'employee.fromcompanyaccount?' | translate}}</div>
            <button *ngIf="!this.deleteDept" class="btn btn-secondary" (click)="deleteDepartment()"><span class="add">{{ 'employee.CONFIRMDELETE' | translate}}
           </span></button>
            <button *ngIf="this.deleteDept" class="btn btn-secondary" [disabled]="this.deleteDept"><span
          class="add"> {{ 'employee.WAIT' | translate}}</span></button>
            <button class="btn btn-normal" (click)="onModelCancel()"><span class="add1"> {{ 'employee.Cancel' | translate}}</span></button>
            <app-loader *ngIf="this.deleteDept" [spinnerStyle]="true"></app-loader>
            <div class="modal-form-button">
            </div>
        </div>
    </ng-template>
    <ng-template #removeAdminModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                Remove admin
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onCancelModal1()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <div class="modal-body" style=" text-align: left !important;">
            <span class="heading">Confirm Delete Admin</span>
            <div class="heading1">Are you sure you want to delete this admin from company account?</div>
            <button *ngIf="!this.deleteDept1" class="btn btn-secondary" (click)="removeDepartmentAdmin()"><span
          class="add">CONFIRM DELETE</span></button>
            <button *ngIf="this.deleteDept1" class="btn btn-secondary" [disabled]="this.deleteDept1"><span
          class="add">Wait</span></button>
            <button class="btn btn-normal" (click)="onCancelModal1()"><span class="add1">Cancel</span></button>
            <app-loader *ngIf="this.deleteDept1" [spinnerStyle]="true"></app-loader>
            <div class="modal-form-button">
            </div>
        </div>
    </ng-template>


    <!-- (click)="showModal2(inviteSendModal)" -->
    <ng-template #resendLinkModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                Resend Invite
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <div class="modal-body" style=" text-align: left !important;">
            <span class="heading">Would you like to resend the invite to {{selectEmp.email}}?</span>
            <div class="heading1">Please click "resend invite" below: </div>
            <button class="btn btn-secondary" (click)="resendInvite()"><span class="add"> RESEND INVITE</span></button>
            <button class="btn btn-normal" (click)="onModelCancel()"><span class="add1">Cancel</span></button>
            <div class="modal-form-button">

            </div>
        </div>
    </ng-template>


    <ng-template #inviteSendModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                Invite Sent
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <div class="modal-body" style=" text-align: left !important;">
            <span class="heading">Invitation has been sent</span>
            <div class="heading1">An invite for {{selectEmp.firstName}} {{selectEmp.lastName}} has been sent to {{selectEmp.email}}
            </div>
            <button class="btn btn-secondary" (click)="onModelCancel()"><span class="add">CLOSE</span></button>
            <div class="modal-form-button">

            </div>
        </div>
    </ng-template>

    <ng-template #AddanEmployee let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                Add an employee
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <div class="modal-body" style=" text-align: left !important;">
            <form [formGroup]="emailForm">
                <span class="heading">Add an employee</span>
                <div class="row" style="margin-top:12px;">
                    <div class="col-12 col-md-5">
                        <label class="name">Email</label>
                        <input type="text" class="formControl" formControlName="emailUserName" placeholder="" (input)="emailUserNameChange($event.target.value)" />
                        <div *ngIf="emailForm.controls['email'].hasError('pattern') && (emailForm.controls['email'].touched || emailForm.controls['email'].dirty)" class="error">{{'login.pleaseenteravalidemail' | translate}}
                        </div>
                        <div *ngIf="emailForm.controls['emailUserName'].hasError('required') && (emailForm.controls['emailUserName'].touched || emailForm.controls['emailUserName'].dirty)" class="error">{{'login.thisfieldisrequired' | translate}}
                        </div>
                        <div *ngIf="emailForm.controls['domain'].hasError('required') && (emailForm.controls['emailUserName'].touched || emailForm.controls['emailUserName'].dirty)" class="error">{{'Please select a domain' | translate}}
                        </div>
                    </div>
                    <div class="col-1 d-none d-md-block" style="margin-top:25px !important; margin-left:15px !important;text-align:center !important;">
                        <span class="symbol">&#64;</span>
                    </div>
                    <div class="col-xl-12 d-block d-md-none" style="">
                        <span class="symbol">&#64;</span>
                    </div>
                    <div class="col-12 col-md-5">
                        <div class="formControl1">
                            <ng-select #domain dropdownPosition="bottom" [searchable]="false" [clearable]="false" formControlName="domain" [items]="domainOptions" bindLabel="value" bindValue="value" [(ngModel)]="this.selectedOption[0].value" (change)="emailDomainChange($event)">
                                <span class="selectBox-remove"><span class="material-icons">clear</span></span>
                                <span class="fa fa-chevron-down icon"></span>
                            </ng-select>
                        </div>
                    </div>
                </div>
                <button class="btn btn-secondary" style="margin-top:15px !important;" (click)="checkEmployeeDetail()"><span
            class="add">LOOKUP OR ADD</span></button>
                <div class="modal-form-button">

                </div>
            </form>
        </div>
    </ng-template>

    <ng-template #AddanManager let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                Add Manager
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <div class="modal-body" style=" text-align: left !important;">
            <form [formGroup]="emailForm">
                <span class="heading">Add Manager</span>
                <div class="row" style="margin-top:12px;">
                    <div class="col-12 col-md-5">
                        <label class="name">Email</label>
                        <input type="text" class="formControl" formControlName="emailUserName" placeholder="" (input)="emailUserNameChange($event.target.value)" />
                        <div *ngIf="emailForm.controls['email'].hasError('pattern') && (emailForm.controls['email'].touched || emailForm.controls['email'].dirty)" class="error">{{'login.pleaseenteravalidemail' | translate}}
                        </div>
                        <div *ngIf="emailForm.controls['emailUserName'].hasError('required') && (emailForm.controls['emailUserName'].touched || emailForm.controls['emailUserName'].dirty)" class="error">{{'login.thisfieldisrequired' | translate}}
                        </div>
                        <div *ngIf="emailForm.controls['domain'].hasError('required') && (emailForm.controls['emailUserName'].touched || emailForm.controls['emailUserName'].dirty)" class="error">{{'Please select a domain' | translate}}
                        </div>
                    </div>
                    <div class="col-1 d-none d-md-block" style="margin-top:25px !important; margin-left:15px !important;text-align:center !important;">
                        <span class="symbol">&#64;</span>
                    </div>
                    <div class="col-xl-12 d-block d-md-none" style="">
                        <span class="symbol">&#64;</span>
                    </div>
                    <div class="col-12 col-md-5">
                        <div class="formControl1">
                            <ng-select #domain dropdownPosition="bottom" [searchable]="false" [clearable]="false" formControlName="domain" [items]="domainOptions" bindLabel="value" bindValue="value" [(ngModel)]="this.selectedOption[0].value" (change)="emailDomainChange($event)">
                                <span class="selectBox-remove"><span class="material-icons">clear</span></span>
                                <span class="fa fa-chevron-down icon"></span>
                            </ng-select>
                        </div>
                    </div>
                </div>
                <button class="btn btn-secondary" style="margin-top:15px !important;" (click)="checkManagerDetail()"><span
            class="add">LOOKUP OR ADD</span></button>
                <div class="modal-form-button">

                </div>
            </form>
        </div>
    </ng-template>

    <ng-template #addAdmin let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                Add Admin
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onLookUpAdminModalCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <a data-controls-modal="addAdmin" data-keyboard="false" href="#"></a>
        <div *ngIf="this.showSpinner" class="approval_request_diaglog_bg_clickhandler" (click)="$event.stopPropagation();">
        </div>
        <div class="modal-body" style=" text-align: left !important;">
            <form [formGroup]="emailForm">
                <span class="heading">Admin's email</span>
                <div class="row" style="margin-top:12px;">
                    <div class="col-12 col-md-5">
                        <label class="name">Email</label>
                        <input type="text" class="formControl" formControlName="emailUserName" placeholder="" (input)="emailUserNameChange($event.target.value)" />
                        <div *ngIf="emailForm.controls['email'].hasError('pattern') && (emailForm.controls['email'].touched || emailForm.controls['email'].dirty)" class="error">{{'login.pleaseenteravalidemail' | translate}}
                        </div>
                        <div *ngIf="emailForm.controls['emailUserName'].hasError('required') && (emailForm.controls['emailUserName'].touched || emailForm.controls['emailUserName'].dirty)" class="error">{{'login.thisfieldisrequired' | translate}}
                        </div>
                        <div *ngIf="emailForm.controls['domain'].hasError('required') && (emailForm.controls['emailUserName'].touched || emailForm.controls['emailUserName'].dirty)" class="error">{{'Please select a domain' | translate}}
                        </div>
                    </div>
                    <div class="col-1 d-none d-md-block" style="margin-top:25px !important; margin-left:15px !important;text-align:center !important;">
                        <span class="symbol">&#64;</span>
                    </div>
                    <div class="col-xl-12 d-block d-md-none" style="">
                        <span class="symbol">&#64;</span>
                    </div>
                    <div class="col-12 col-md-5">
                        <div class="formControl1">
                            <ng-select #domain dropdownPosition="bottom" [searchable]="false" [clearable]="false" formControlName="domain" [items]="domainOptions" bindLabel="value" bindValue="value" (change)="emailDomainChange($event)">
                                <span class="selectBox-remove"><span class="material-icons">clear</span></span>
                                <span class="fa fa-chevron-down icon"></span>
                            </ng-select>
                        </div>
                    </div>
                </div>
                <div *ngIf="showToBeAdminDetail" class="row" style="margin-top:12px;">
                    <div class="col-12 col-md-6">
                        <span class="name">First Name</span>
                        <!-- <div *ngIf = "isValidField('firstName')" class="formControl3">{{getValidFieldValue('firstName')}}</div> -->
                        <input type="text" class="formControl" formControlName="firstName" [attr.disabled]="isDisable()" placeholder="First Name" />
                        <div *ngIf="emailForm.controls['firstName'].hasError('required') && (emailForm.controls['firstName'].touched || emailForm.controls['firstName'].dirty)" class="error">This field is required
                        </div>
                        <div *ngIf="emailForm.controls['firstName'].hasError('pattern') && (emailForm.controls['firstName'].touched || emailForm.controls['firstName'].dirty)" class="error">Please enter valid first name
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <span class="name">Last Name</span>
                        <!-- <div *ngIf = "isValidField('lastName')" class="formControl3">{{getValidFieldValue('lastName')}}</div> -->
                        <input type="text" class="formControl" formControlName="lastName" [attr.disabled]="isDisable()" (focusout)="this.disabled=true;" placeholder="Last Name" />
                        <div *ngIf="emailForm.controls['lastName'].hasError('required') && (emailForm.controls['lastName'].touched || emailForm.controls['lastName'].dirty)" class="error">This field is required
                        </div>
                        <div *ngIf="emailForm.controls['lastName'].hasError('pattern') && (emailForm.controls['lastName'].touched || emailForm.controls['lastName'].dirty)" class="error">Please enter valid last name
                        </div>
                    </div>
                </div>
                <button *ngIf="!showToBeAdminDetail" class="btn btn-secondary" style="margin-top:15px !important;" (click)="checkAdminDetail()"><span class="add">NEXT</span></button>
                <button *ngIf="showToBeAdminDetail" class="btn btn-secondary" style="margin-top:15px !important;" (click)="addAdminToDepartment()"><span class="add">NEXT</span></button>
                <button class="btn btn-normal" (click)="onLookUpAdminModalCancel()"><span class="add1">GO BACK</span></button>
                <app-loader *ngIf="this.showSpinner" [spinnerStyle]="true"></app-loader>
                <div class="modal-form-button">

                </div>
            </form>
        </div>
    </ng-template>

    <ng-template #addDepartmentModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                {{ 'employee.AddDepartment' | translate}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onAddEditDeptNameModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <div class="modal-body" style=" text-align: left !important;">
            <form [formGroup]="addDepartmentForm">
                <div class="row" style="margin-top:5px;padding-left:15px !important;">
                    <div class="col-12 col-md-12" style="margin-bottom:22px !important">
                        <label class="name" style="font-size:20px !important;font-weight:bold !important;"> {{ 'employee.DepartmentName' | translate}}:</label>
                        <input type="text" class="formControl1" formControlName="departmentname" placeholder=" {{ 'employee.Enterdepartmentname' | translate}}" />
                        <div *ngIf="addDepartmentForm.controls['departmentname'].hasError('required') && (addDepartmentForm.controls['departmentname'].touched || addDepartmentForm.controls['departmentname'].dirty)" class="error" style="text-align: center !important;"> {{ 'employee.Thisfieldisrequired' | translate}}
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <button #addEditDepartmentDiv class="btn btn-normal" (click)="addEditDepartment()"><span class="add"> {{ 'employee.AddDepartment' | translate}}</span></button>
                </div>
            </form>

            <div class="modal-form-button">

            </div>
        </div>
    </ng-template>
    <ng-template #employeeInDept let-modal>
        <div class="modal-header">
            <h5 class="modal-title">
                {{ 'employee.Approverchange' | translate}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel1()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <div class="modal-body" style=" text-align: left !important;">
            <h4> {{ 'employee.Approversforfollowingemployeeswerechangedfromthedefaultdepartmentsettings.Wouldyouliketoapplychangestoanyofthefollowingemployeesaswell?PleaseselectandclickUpdatetoproceed' | translate}}</h4>
            <div style="margin-top: 30px;margin-bottom: 30px;margin-left: 8px;">
                <div class="row" *ngFor="let item of this.employeeInDeptWithApprover" style="height: 42px;">
                    <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="{{item}}">
            <input type="checkbox" id="{{item}}" class="mdl-checkbox__input"
              (change)="onEmployeeChangeClicked(item, $event)" [checked]="isEmployeeChecked(item)">
            <span class="mdl-checkbox__label"
              style="font-family: var(--globalFontfamilyr) !important;padding-left: 10px !important;font-size: 14px;top: -2px;"><span
                style="text-overflow: ellipsis;display: inline-block;overflow: hidden;position: relative;
              top: 8px;">{{getEmployeename(item)}}</span>
            </span>
          </label>
                </div>
            </div>
            <div class="modal-form-button" style="text-align: center;">
                <button class="btn btn-secondary" [disabled]="this.updateDept" (click)="onConfirmUpdateDepat()"><span
            class="add"> {{ 'employee.Update' | translate}}</span></button>
                <button class="btn btn-normal" [disabled]="this.updateDept" (click)="onModelCancel1()"><span
            class="add1"> {{ 'employee.Cancel' | translate}}</span></button>
            </div>
        </div>
    </ng-template>
    <ng-template #editDepartmentModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                <span *ngIf="this.addDepartmentModelMode === 'add'"> {{ 'employee.Add' | translate}}</span> <span *ngIf="this.addDepartmentModelMode === 'edit'"> {{ 'employee.Update' | translate}}</span> {{ 'employee.Department' | translate}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onUpdateDeptModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
        </div>
        <div class="text-center" style="margin-top: 20px !important;font-size:20px !important;">

        </div>
        <div *ngIf="this.updateDept" class="approval_request_diaglog_bg_clickhandler" (click)="$event.stopPropagation();">
        </div>
        <div class="{{ openUpdateModal ? 'admin':'modal-body'}}" style=" text-align: left !important;">
            <div class="showHeading" style="margin-left:20px;">
                {{ 'employee.DepartmentSettings' | translate}}
            </div>
            <div style="margin-left:20px !important;">
                <form [formGroup]="editDepartmentForm">
                    <label class="showLabel">{{ 'employee.DepartmentName' | translate}}</label>
                    <div class="row" style="margin-top:5px;display:block !important;">
                        <input class="input" maxlength="50" formControlName="departmentname">
                        <div *ngIf="editDepartmentForm.controls['departmentname'].hasError('required') && (editDepartmentForm.controls['departmentname'].touched || editDepartmentForm.controls['departmentname'].dirty)" class="error">{{'login.thisfieldisrequired' | translate}}</div>
                        <div *ngIf="editDepartmentForm.controls['departmentname'].hasError('pattern')" class="error">{{ 'employee.Pleaseentervalidtext.' | translate}}
                        </div>
                    </div>
                    <label class="showLabel"> {{ 'employee.SetDefaultPolicy' | translate}}</label>
                    <div class="row" style="margin-top:5px;">
                        <div id="policylistDiv">
                            <div class="input">
                                <ng-select #policylist appendTo="#policylistDiv" style="bottom: 5px;" dropdownPosition="middle" [searchable]="false" [clearable]="false" formControlName="policy" [items]="policyOptions" bindLabel="value" bindValue="id">
                                </ng-select>
                                <div class="select-overlay"></div>
                            </div>
                            <svg class="down-arrow" (click)="policylist.toggle()" style="top: -22px;position:relative;float:right;right:20px;" width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                  fill="#8936F3" />
              </svg>
                        </div>
                    </div>
                    <label class="showLabel"> {{ 'employee.SetDepartmentApprover' | translate}}</label>
                    <div class="row" style="margin-top:5px;display: block !important;width: 353px;">
                        <div id="selectApproverDiv">
                            <div class="input" style="overflow: hidden;">
                                <ng-select #approverlist appendTo="#selectApproverDiv" style="bottom: 5px;" (click)="this.dropDownopen =true;filterSelectedlist(this.multipleSelectedApprover);" dropdownPosition="middle" [searchable]="true" (close)="closeDropdown()" [closeOnSelect]="false"
                                    [clearable]="false" [items]="this.appproverList" [searchFn]="searchByApproverNameAndEmailChanged" [(ngModel)]="search" [ngModelOptions]="{standalone: true}">
                                    <ng-template ng-label-tmp let-item="item">
                                        <span> {{getLabelValue() | translate}}</span>
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                                        <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="{{item.email}}" style="height: 36px !important;min-width: 331px !important;margin-bottom: 0px !important;">
                      <input type="checkbox" id="{{item.email}}" class="mdl-checkbox__input"
                        (change)="onApproverChangeClicked(item.email, $event)"
                        [checked]="isApproverChecked(item.email)">
                      <span class="mdl-checkbox__label"
                        style="font-family: var(--globalFontfamilyr) !important;padding-left: 10px !important;font-size: 14px;top: -2px;padding-bottom: 20px;"><span
                          style="width: 100px !important;min-width: 100px !important;text-overflow: ellipsis;display: inline-block;overflow: hidden;position: relative;
                              top: 8px;">{{item.firstName}} {{item.lastName}}</span>
                        <span
                          style="text-align:right;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;margin-left:20px;">
                          {{item.email}}</span></span>
                    </label>

                                    </ng-template>
                                </ng-select>
                                <div class="select-overlay"></div>
                                <div *ngIf="this.multipleSelectedApprover.length > 0 && !this.dropDownopen" class="modalApprover" (click)="this.dropDownopen =true;filterSelectedlist(this.multipleSelectedApprover);approverlist.toggle()" [ngStyle]="{'top' : this.adminPanelService.employeePopOpen ? '-60px':'-80px'}">
                                    <span *ngFor="let item1 of this.multipleSelectedApprover;let i=index">
                    <span class="shown1">{{item1}}<span *ngIf="((i !== (this.multipleSelectedApprover.length-1)))">,
                      </span></span>
                                    </span>
                                </div>
                            </div>
                            <svg class="down-arrow" (click)="this.dropDownopen =true;filterSelectedlist(this.multipleSelectedApprover);approverlist.toggle()" style="top: -22px;position:relative;float:right;right:20px;" width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                  fill="#8936F3" />
              </svg>

                        </div>
                        <!--     <input class="input" id="approverDropdown" (click)="openNgxModal1('approverDropdown')"formControlName="approvername" placeholder="search" (input)="searchByApproverNameAndEmailChanged($event.target.value)">
      <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'approverDropdown')" [hideDelay]="0"(onClose)="handleModalEvents('onClose', 'approverDropdown')" (onDismiss)="handleModalEvents('onDismiss', 'approverDropdown')"[closable]="false" #approverDropdown identifier="approverDropdown">
          <div class="modal-container flight-modal-container filter-modal2 modalAirportFilterInfo"   style="min-width: 353px;" (click)="$event.stopPropagation();" >
                 <div class="modal-body" style="min-width: 343px;max-height: 250px;background: #F7F7F9;padding-left: 7px !important;padding-right:15px!important;padding-top:7px !important;padding-bottom:16px !important;" >
                    <div *ngIf="this.appproverList && this.appproverList.length >0"class="body">
                        <div *ngFor="let item of this.appproverList" class="row" style="cursor: pointer;height:40px;" (click)="setApproverEmail(item)">
                          <div class="col-4" style="text-overflow: ellipsis;display: inline-block !important;
                          white-space: nowrap;overflow: hidden;">
                            {{item.firstName}} {{item.lastName}}
                          </div>
                          <div class="col-8" style="text-overflow: ellipsis;display: inline-block !important;
                          white-space: nowrap;overflow: hidden;">
                              {{item.email}}
                            </div>
                            </div>
                      </div>
                      <div *ngIf="this.appproverList && this.appproverList.length ===0"class="body1">
                        No Approver found
                
                      </div>
                    </div>
                    
                    </div>
            </ngx-smart-modal>-->

                        <div *ngIf="this.multipleSelectedApprover.length===0 && updateDeptButtonClicked" class="error">
                            {{'login.thisfieldisrequired' | translate}}
                        </div>
                    </div>
                    <div *ngIf="this.selectedDepartment.departmentType=== 'REGULAR'" class="row" style="margin-top: 25px;" [ngStyle]="{'opacity' : !this.companySSoPolicy ? '0.3':'1'}">
                        <div class="col-9" style="padding-top:10px;">
                            <span class="showLabel" [ngStyle]="{'white-space' : getSelectedLanguage() !== 'en' ? 'initial':'nowrap'}" style="font-family:var(--globalFontfamilyr);font-weight: bold;;font-size: 18px;"> {{ 'dashboard.RequireallusersinthisdepartmenttologinwithSSO' | translate}}</span>
                        </div>
                        <div class="col-3" id="switchForDeparment" [ngStyle]="{'padding-top' : getSelectedLanguage() !== 'en' ? '25px':'10px'}" style="padding-left:20px !important;">

                            <ui-switch color="gray" [disabled]="!this.companySSoPolicy" formControlName="defaultSSoRole" (change)="removesso($event)" [checked]="this.editDepartmentForm.controls['defaultSSoRole'].value" checkedLabel="{{ 'cards.ON' | translate }}" uncheckedLabel="{{ 'cards.OFF' | translate }}"></ui-switch>

                        </div>
                    </div>
                    <div *ngIf="this.selectedDepartment.departmentType=== 'REGULAR'" class="row" style="margin-top: 25px;">
                        <div class="col-9" style="padding-top:10px;">
                            <span class="showLabel" [ngStyle]="{'white-space' : getSelectedLanguage() !== 'en' ? 'initial':'nowrap'}" style="font-family:var(--globalFontfamilyr);font-weight: bold;;font-size: 18px;"> {{ 'employee.Allowemployeestobookforothertravelers' | translate}}</span>
                        </div>
                        <div class="col-3" id="switchForDeparment" style="padding-top:10px !important;padding-left:20px !important;">
                            <ui-switch color="gray" formControlName="defaultAgentRole" [checked]="defaultAgentRole"(change)="setBFO($event)" checkedLabel="{{ 'cards.ON' | translate }}" uncheckedLabel="{{ 'cards.OFF' | translate }}">
                            </ui-switch>

                        </div>
                    </div>
                    <div *ngIf="this.selectedDepartment.departmentType=== 'REGULAR'" style="width: 80%; margin-top: 10px; margin-left: 0px;font-size: 15px;position: relative;
  left: -5px;">{{ 'employee.Whenenabled,bydefaulttheemployeesinthisdepartmentwillbeallowedtobookforothertravelers.Youcanalsoturnonoffthisabilityatemployeelevelaswell.' | translate}} </div>
                    <!-- <div class="row" style="margin-top:12px;">
        <span class="heading"
          style="font-weight:bold;padding-left: 7px;margin-top:20px !important; font-size:16px !important;"><b>Admin
            Details:</b></span>
      </div>
      <div *ngIf="selectedDepartment && selectedDepartment.adminList && selectedDepartment.adminList.length > 1"
        class="row" style="margin-top:12px;">
        <div *ngFor="let adminObj of selectedDepartment.adminList,let i=index;" class="col-12 col-md-12">
          <label class="show">Admin {{i+1}}: {{adminObj.firstName +" "+adminObj.lastName}}, {{adminObj.emailId}}</label>
          <span *ngIf="canShowAddDepartment()" class="col-auto" style="width:auto !important;cursor:pointer;">
            <img src="assets/images/icon_edit.svg" style="margin-right:10px;"(click)="showModal(i)"> 
            <img src="assets/images/ic_delete.svg" (click)="showRemoveAdmin(removeAdminModal,i)">
          </span>
        </div>
      </div>
      <div *ngIf="selectedDepartment && selectedDepartment.adminList && selectedDepartment.adminList.length == 1"
        class="row" style="margin-top:12px;">
        <div *ngFor="let adminObj of selectedDepartment.adminList,let i=index;" class="col-12 col-md-12">
          <label class="show">Admin {{i+1}}: {{adminObj.firstName +" "+adminObj.lastName}}, {{adminObj.emailId}}</label>
        </div>
      </div>
      <div *ngIf="!selectedDepartment || !selectedDepartment.adminList || selectedDepartment.adminList.length == 0"
        class="row" style="margin-top:12px;">
        <label class="show">Admin 1: {{getPrimaryDepartmentAdminName(selectedDepartment.departmentId)}}</label>
      </div>
      <a *ngIf="canShowAddDepartment()" href="javascript:void(0);" style="color:var(--hyperlink-color) !important;margin-left: 7px;"
        (click)="showAdminLookUpModal(addAdmin)">ADD ADMIN</a>-->
              
                <div *ngIf="editDepartmentForm.controls['defaultAgentRole'].value">
                    <div class="row">
                        <div class="col-5">

                       
                        <label class="showLabel" style="white-space: pre-line; line-height: 17px;width: 100%;
                        margin-top: 15px;"> {{ 'employee.Sendbookingemailsto' | translate}}</label>
                        </div>
                        <div class="col-7">
                        <div class="row" style="margin-top:20px;position: relative;width: 353px;">
                            <div id="cardlistDiv2">
                                <div class="input">
                                    <ng-select #cardlistDiv2 appendTo="#cardlistDiv2" style="bottom: 5px;"  dropdownPosition="middle" [searchable]="false" (close)="closeDropdown1()" [closeOnSelect]="false"
                                        [clearable]="false" [items]="emailDropdown" formControlName="bookingEmailConfig" bindLabel="" bindValue="value">
                                       
                                        <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                                            <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="{{item.id}}" style="height: 36px !important;min-width: 331px !important;margin-bottom: 0px !important;">
                          <input type="checkbox" id="{{item.id}}" class="mdl-checkbox__input"
                            (change)="onEmailSendNotificationChangeClicked(item.id, $event)"
                            [checked]="isEmailSendNotificationChecked(item.id)">
                          <span class="mdl-checkbox__label"
                            style="font-family: var(--globalFontfamilyr) !important;padding-left: 10px !important;font-size: 14px;top: -2px;padding-bottom: 20px;"><span
                              style="min-width: 100px !important;text-overflow: ellipsis;display: inline-block;overflow: hidden;position: relative;
                                  top: 8px;">{{item.value | translate}}</span>
                           </span>
                        </label>
    
                                        </ng-template>
                                    </ng-select>
                                    <div class="select-overlay"></div>
                                    <div *ngIf="this.bookingEmailsSentTo.length > 0 && !this.dropDownopen1" class="modalApprover" (click)="this.dropDownopen1 =true;cardlistDiv2.toggle()" [ngStyle]="{'top' : this.adminPanelService.employeePopOpen ? '-60px':'-80px'}">
                                        <span *ngFor="let item1 of this.bookingEmailsSentTo;let i=index">
                        <span class="shown1">{{getEmailNotificationLabel(item1)}}<span *ngIf="((i !== (this.bookingEmailsSentTo.length-1)))">,
                          </span></span>
                                        </span>
                                    </div>
                                    <div *ngIf="this.bookingEmailsSentTo.length === 0 && !this.dropDownopen1" class="modalApprover" (click)="this.dropDownopen1 =true;cardlistDiv2.toggle()" [ngStyle]="{'top' : this.adminPanelService.employeePopOpen ? '-60px':'-80px'}">
                                            <span >
                            <span class="shown1">{{'employee.Default' | translate}}</span>
                                            </span>
                                        </div>
                                </div>
                                <svg class="down-arrow" (click)="this.dropDownopen1 =true;cardlistDiv2.toggle()" style="top: -22px;position:relative;float:right;right:20px;" width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                      fill="#8936F3" />
                  </svg>
    </div>
                            </div>
                           
                           
                        </div>
                    </div>
                        <div *ngIf="isCustomSelectedInEmailSendNotification()"class="row" style="width: 100%;">
                                <div class="select-box" (click)="setFocusOnInput('notificationEmail')">
                                    <div  class="row" style="padding-top:0px;"><span
                                        *ngFor="let item of this.emailNotificationList;let i=index" class="input-boss"><span
                                          style="color: gray !important;display: inline-block; 
                                          white-space: nowrap;max-width: 120px;min-width: 120px;
                                          overflow: hidden !important;
                                          text-overflow: ellipsis !important;">{{item}}</span><span
                                          style="float:right;font-size:12px;position: relative;top: -2px;"><i
                                            class="material-icons" style="color: gray !important;font-size:20px !important;cursor: pointer;"
                                            (click)="unselectedEmployee(i)">close</i></span></span><input type="email" style="border:0px !important;" id="notificationEmail" class="inputLowBalance11" formControlName="email"  (focusout)="addEmailList()"  (input)="inputEmailValue($event.target.value)" (keyup)="($event.key === 'Enter' || $event.key === ',') && addEmailList()"  ></div>
                                            
                                            
                                  </div>
                                  <div
                                  *ngIf="editDepartmentForm.controls['email'].hasError('pattern') && (editDepartmentForm.controls['email'].touched || editDepartmentForm.controls['email'].dirty)"
                                  class="error" style="width: 100%">{{'personal.Pleaseenteravalidemail' | translate}}
                                </div>
                                 <div class="error" style="width: 100%" *ngIf="editDepartmentForm.controls['email'].hasError('required') && (editDepartmentForm.controls['email'].touched || editDepartmentForm.controls['email'].dirty)">
                                        {{'login.thisfieldisrequired' | translate}}
                                 </div>
                               
                                  
                               
                              </div>
                              <div class="row" *ngIf="isCustomSelectedInEmailSendNotification()">
                                    {{ 'setting.Separatemultipleemailsaddresseswithcommas' | translate}}
                                </div>
                              <div class="row">
                                    <div class="col-5">                   
                <label  class="showLabel" style="white-space: pre-line; line-height: 20px;width: 100%;
                margin-top: 25px;">
          {{ 'employee.SaveguestusersbookedasSharedProfile' | translate}}
        </label>
               </div>
               <div class="col-7">
                <div  class="row" style="margin-top:35px;width:353px;">
                    <div id="cardlistDiv">
                        <div class="input">
                            <ng-select #cardlist appendTo="#cardlistDiv" dropdownPosition="middle" style="bottom:12px !important;" formControlName="saveGuestUsers" [searchable]="false" [clearable]="false"[items]="switchDrodpdown | translateOptions" bindLabel="value" bindValue="id">

                               
                            </ng-select>
                            <div class="select-overlay"></div>
                        </div>
                        <svg class="down-arrow" (click)="cardlist.toggle()" style="top: -22px;position:relative;float:right;right:20px;" width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                fill="#8936F3" />
            </svg>

                    </div>
                </div>
                </div>
                </div>
                <div *ngIf="this.userAccountService.infantBookingAllowed" class="row">
                        <div class="col-5">
                <label  class="showLabel" style="white-space: pre-line; line-height: 20px;width: 100%;
                margin-top:30px;">
                    {{ 'employee.EnableBookingforminors' | translate}}
                  </label>
                         </div>
                         <div class="col-7">
                          <div  class="row" style="margin-top:35px;position: relative;width: 353px">
                              <div id="cardlistDiv3">
                                  <div class="input">
                                      <ng-select #cardlist1 appendTo="#cardlistDiv3" dropdownPosition="middle" style="bottom:12px !important;" [searchable]="false"  formControlName="minorBookingEnabled"[clearable]="false"  [items]="switchDrodpdown | translateOptions" bindLabel="value" bindValue="id">
          
                                         
                                      </ng-select>
                                      <div class="select-overlay"></div>
                                  </div>
                                  <svg class="down-arrow" (click)="cardlist1.toggle()" style="top: -22px;position:relative;float:right;right:20px;" width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                          fill="#8936F3" />
                      </svg>
          
                              </div>
                          </div>
                          </div>
                          </div>
            </div>
        </form>     
                <!-- <div *ngIf="!addCardMode" id="addCard" class="add-card-link"style="margin-top:5px;margin-left:15px;">
                <a class="link-primary" href="javascript:void(0);"  onclick="addCardForm(this);"
                    (click)="setCardMode()">
                    <i class="fa fa-plus link-icon"></i>
                    <span class="link-text" style="margin-left: 5px;">{{'paymentDetails.AddNewcard' | translate}}</span>
                </a>
            </div>
            <div class="add-card-div" style="display: flex;">
              <add-card-widget *ngIf="addCardMode"[loggedIn]="isLoggedIn()"  [cardButtonStyle]="true" (goBackEmitter)='handleBackFromAddCard($event)'></add-card-widget>
            </div>-->
       <!--          <label class="showHeading" style="margin-top:30px !important;">
           {{ 'employee.CarRentalSettings' | translate}}
        </label>
                <div class="row" style="text-align:left;margin-top:0px;margin-bottom:20px; margin-left:6px !important;">
                    <div cass="col-12 md-8">
                        <div class="name2">

                            <div class="row" style="text-align:left;margin-top:20px;margin-bottom:15px;display: block;">
                                <div cass="col-lg-8 col-md-8 col-sm-12 col-xs-12">
                                    <span class="name2" style="margin-bottom:15px;"> Car rental corporate discounts and billing numbers:</span>
                                    <div *ngIf="false" class="row" style="font-size: 14px;">
                                        <div class="col-4 col-lg-4" style="margin-left: 3px;">
                                            {{ 'employee.Carrentalcompany' | translate}}
                                        </div>
                                        <div class="col-3 col-lg-3" style="margin-left: 10px;">
                                            {{ 'employee.Discountcode' | translate}}
                                        </div>
                                        <div class="col-4 col-lg-4" style="margin-left: 5px;">
                                            {{ 'employee.Billingnumber' | translate}}
                                        </div>
                                    </div>
                                    <div>
                                        <form [formGroup]="settingForm">
                                            <div formArrayName="carCompanies">
                                                <div *ngFor="let car of carCompanies.controls; let i= index;" [formGroupName]="i">
                                                    <div *ngIf="car.value.edit" class="row" style="margin-top:20px !important;height: 42px;">
                                                        <div class="col-3 col-lg-3">
                                                            <div>
                                                                <img class="inlineblock_m" src="https://s3.amazonaws.com/images.biztravel.ai/ota/carrentals/{{car.value.partnerCode}}.png" />
                                                            </div>
                                                        </div>
                                                        <div class="col-4 col-lg-4" style="margin-left:3px;">
                                                            <span class="show1" style="font-size: 16px !important; "> {{car.value.billingNumber}}</span>
                                                        </div>
                                                        <div class="col-4 col-lg-4" style="margin-left:3px;margin-top: -5px;">
                                                            <img style="margin-top: 0px !important;cursor: pointer;margin-left:3px;color:#000000 !important;" src="assets/images/ic_delete.svg" (click)="onRemoveCompanies(i)">
                                                            <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
                              </svg>
                                                            <img src="assets/images/icon_edit.svg" style="cursor: pointer;margin-right:10px;color:#000000 !important;" (click)="onEditcarRental(i)">
                                                        </div>

                                                    </div>
                                                    <div *ngIf="!car.value.edit" class="row" id="carOptions" style="margin-top:5px !important;margin-bottom: 25px !important;height: 50px;">
                                                        <div id="carOptions{{i}}" class="col-4 col-lg-4">
                                                            <div class="formControl11">
                                                                <ng-select #carOptions appendTo="#carOptions{{i}}" formControlName="partnerCode" style="top: -19px !important;" dropdownPosition="bottom" [searchable]="false" [clearable]="false" (change)="onCompanyChange(i)" [items]="carRentalCompanies" bindLabel="label"
                                                                    bindValue="value">
                                                                    <span class="selectBox-remove"><span class="material-icons">clear</span></span>
                                                                </ng-select>
                                                                <svg (click)="carOptions.toggle()" style="position:relative;float:right;right:30px;left:-15px;" [ngStyle]="{'top' : this.adminPanelService.employeePopOpen ? '-48px':'-68px'}" width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                  fill="#8936F3" />
                              </svg>
                                                            </div>
                                                            <div *ngIf="isEmptyCode(i)" class="error">
                                                                {{'profileLoyalty.Thisfieldisrequired' | translate}}</div>
                                                            <span *ngIf="isDuplicateEntry(i)" class="error">
                                {{'profileLoyalty.DuplicateEntry' | translate}}</span>
                                                        </div>
                                                        <div class="col-3 col-lg-3" style="margin-left:14px;">
                                                            <input type="text" class="formControl2" maxlength="50" placeholder="{{'employee.DiscountCode' | translate}}" formControlName="discountCode" pattern="^[a-zA-Z0-9]*$" onkeypress="return /[0-9a-zA-Z]/i.test(event.key)">
                                                            <div *ngIf="isEmptyDiscountCode(i)" class="error">{{'profileLoyalty.Thisfieldisrequired' | translate}}</div>
                                                            <div *ngIf="IsOnlyAlphanumeric(i,'discountCode')" class="error">{{'profileLoyalty.InValidCharacters' | translate}}</div>
                                                        </div>
                                                        <div class="col-4 col-lg-4" style="margin-left:13px;">
                                                            <input type="text" class="formControl2" maxlength="50" placeholder=" {{'employee.BillingNumber' | translate}}" formControlName="billingNumber" pattern="^[a-zA-Z0-9]*$" onkeypress="return /[0-9a-zA-Z]/i.test(event.key)">
                                                            <img style="margin-top: 0px !important;cursor: pointer;margin-left:3px;" src="assets/images/ic_delete.svg" (click)="onRemoveCompanies(i)">
                                                            <div *ngIf="IsOnlyAlphanumeric(i,'billingNumber')" class="error">{{'profileLoyalty.InValidCharacters' | translate}}</div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                            <div *ngIf="getAdditionalReqeustControl().length > 0" class="row" style="margin-top: 20px;margin-bottom: 10px;">
                                                <div class="col-1" id="switchForDeparment" style="padding-top:3px !important;">
                                                    <ui-switch color="gray" (change)="setCarOptions($event)" [checked]="carOptionsName">
                                                    </ui-switch>
                                                    <span *ngIf="carOptionsName" (click)="setCarOptions(false)" class="styelSwitch1">{{ 'employee.on' | translate}}</span>
                                                    <span *ngIf="!carOptionsName" (click)="setCarOptions(true)" class="styelSwitch2"> {{ 'employee.off' | translate}}</span>
                                                </div>
                                                <div class="col-11" style="padding-top:10px;max-width: auto;text-align: start;">
                                                    <span class="switchLabel"> {{ 'employee.Restrictcarrentalcompaniestoabovecontractsonly.' | translate}}</span>
                                                </div>
                                            </div>
                                            <div class="link-primary" style="margin-left: 5px;margin-top: 30px !important;cursor: pointer;font-family: var(--globalFontfamilyr) !important;">
                                                <span (click)=" onAddCarCompanies()">  {{ 'employee.AddNewCarRental' | translate}}</span>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>-->
            </div>

            <div class="text-center" style="margin-top: 20px;">
                <button *ngIf="!this.updateDept" class="btn btn-secondary" (click)="updateDepartment(employeeInDept)"><span
            class="add"> {{ 'employee.Save' | translate}}</span></button>
                <button *ngIf="this.updateDept" class="btn btn-secondary" [disabled]="this.updateDept" (click)="updateDepartment()"><span class="add"> {{ 'employee.Wait' | translate}}</span></button><span *ngIf="this.updateDept" class="loaderClass">
          <loader-dots class="loaderAlign"></loader-dots>
        </span>
            </div>

            <div class="modal-form-button">

            </div>
        </div>
    </ng-template>
</div>