import { Component, OnInit, OnDestroy, Output, EventEmitter, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { BsModalService, BsModalRef, ModalDirective } from 'ngx-bootstrap/modal';
import { AddEmployeeComponent } from '../add-employee/add-employee.component';
import { Employee } from './employeeDetails';
import { AdminPanelService, CompanyDomain, CompanySettings, Department } from '../admin-panel.service';
import { UserAccountService } from '../user-account.service';
import { Subscription } from 'rxjs';
import { UserInfo } from '../entity/user-info';
import { ToastrService } from 'ngx-toastr';
import { UntypedFormGroup, UntypedFormBuilder, Validators, UntypedFormArray, FormControl, AbstractControl, ValidatorFn } from '@angular/forms';
import { Constants } from '../util/constants';
import { TranslateService } from '@ngx-translate/core';
import { UserDetails } from '../entity/userDetails';
import { Role, EmployeeInfo } from '../entity/employee-info';
import { CommonUtils } from '../util/common-utils';
import { AddCardWidgetComponent } from '../email-booking-flow/add-card-widget/add-card-widget.component';
import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { GallopAnalyticsUtil } from '../analytics.service';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { NgSelectComponent } from '@ng-select/ng-select';
import { Router, ActivatedRoute } from '@angular/router';
import { DateUtils } from '../util/date-utils';
import { Title } from '@angular/platform-browser';
import { DeviceDetailsService } from '../device-details.service';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { SearchService } from '../search.service';
import { Datasource, IDatasource } from 'ngx-ui-scroll';
declare var setFocusOnInputField: any;
@Component({
    selector: 'app-employee',
    templateUrl: './employee.component.html',
    styleUrls: ['./employee.component.scss'],
    standalone: false
})
export class EmployeeComponent implements OnInit, OnDestroy {
  bsModalRef: BsModalRef;
  temp_filter_employee = [];
  hover: boolean;
  resultErrorMessage = this.translateService.instant('activeTraveler.FetchingData');
  resultErrorMessage1 = 'activeTraveler.0travelersfound';
  boxSelect = false;
  isMobile1: boolean;
  deviceSubscription1: Subscription;
  viewMode1 = 'tab11';
  dateValue1 = '';
  marker2 = 'assets/images/map_pin.svg';
  markerImage = 'assets/images/map-marker.png';
  markerImageActive = 'assets/images/map-marker-active.png';
  markerImageBest = 'assets/images/map-marker-best.png';
  selectDateRange = 'today';
  empNameSearchValue = '';
  number = 0;
  startDate: Date = new Date();
  endDate: Date = new Date();
  minimumDate: Date = new Date();
  applyBtn = false;
  dropDownopen1 = false;
  maximumDate: Date = new Date();
  maximumDate1: Date = new Date();
  dateOptions1 = Constants.DATE_OPTIONS;
  
  origReportResponse: any;
  zoom = 0;
  isMobile: boolean;
  lat: number = 12.954517;
  long: number = 77.3507335;
  activeTravelerSubscription: Subscription;
  deviceSubscription: Subscription;
  activeTravellersList: Array<any>;
  initialBoundsSet = false;
  queryParamSubscription: Subscription;
  filterEmployee = [];
  cardOptions = [];
  emailNotificationList=[];
  viewMode2='tab21'
  search = 'Type to search';
  deleteDept = false;
  approverChange = false;
  addCardFlow = false;
  searchbyName = false;
  dropDownopen = false;
  employeeInDeptWithApprover = [];
  employeeInDeptWithApprover1 = [];
  cardValue = '';
  multipleSelectedApprover = [];
  deleteDept1 = false;
  updateDeptButtonClicked = false;
  deleteButton = false;
  disabledInput = false;
  addCardMode = false;
  updateDept = false;
  openUpdateModal = false;
  deptEmployee = [];
  bookingEmailsSentTo=[];
  emailDropdown= Constants.emailDropdown;
  switchDrodpdown=Constants.switchDropdown;
  AdminIndex: number;
  bsLookUpAdminModalRef: BsModalRef;
  bsRemoveAdminModalRef: BsModalRef;
  bsAddEditDeptNameModelRef: BsModalRef;
  bsUpdateDeptModelRef: BsModalRef;
  bsModelLoginRef: BsModalRef;
  employee = [];
  appproverList = [];
  duplicateEntryStatus: boolean = false;
  carOptions = [{ name: 'All cars', value: 'Allow all car rental companies' }, { name: 'Selected  cars', value: 'Allow only below car rental companies' }];
  carRentalCompanies = [{ "value": "TRFL_ZI", "label": "Avis" }, { "value": "TRFL_ZD", "label": "Budget" }, { "value": "TRFL_AL", "label": "Alamo" }, { "value": "TRFL_ZL", "label": "National" }, { "value": "TRFL_ET", "label": "Enterprise" }, { "value": "TRFL_ZE", "label": "Hertz" }, { "value": "TRFL_ZR", "label": "Dollar" },
  { "value": "TRFL_ZT", "label": "Thrifty" }, { "value": "TRFL_ZA", "label": "Payless" }, { "value": "TRFL_AD", "label": "Advantage" }, { "value": "TRFL_FX", "label": "Fox" }, { "value": "TRFL_SX", "label": "Sixt" }];
  managers = [];
  showSpinner = false;
  domainOptions = [];
  departmentArray = [];
  departmentArrayForDropdwon=[{ value: 'All Departments', departmentId: 'All dept',name:"fuild.AllDepartments" }];
  spinnerStyle = false;
  policyOptions = [{ value: 'Select', id: '' }];
  selectedOption = [];
  mgrNameSearchValue = '';
  userInfoList: Array<UserInfo>;
  userInfoListOrg: Array<UserInfo>;
  managerInfoList: Array<UserInfo>;
  carCompanies: UntypedFormArray;
  selectEmp=[];
  selectMgr: any;
  companySettings: CompanySettings;
  errormsg = this.translateService.instant('employee.Fetchingemployeelist')
  applyButton1 = true;
  selectedDepartment: Department;
  emailForm: UntypedFormGroup;
  carOptionsName = false;
  addDepartmentForm: UntypedFormGroup;
  selectedCardId = '';
  editDepartmentForm: UntypedFormGroup;
  settingForm: UntypedFormGroup;
  carCompanyForm: UntypedFormGroup;
  listEmployeesResponseSubscription: Subscription;
  listManagersResponseSubscription: Subscription;
  responseErrorSubscription: Subscription;
  checkEmployeeDetailResponseSubscription: Subscription;
  companySettingsSubscription: Subscription;
  daterangepickerModel = [this.startDate, this.endDate];
  @ViewChild('approverlist') ngselect: NgSelectComponent;
  @Output() addEmployeeAddedEmitter = new EventEmitter();
  @ViewChild(AddCardWidgetComponent) addCardChild: AddCardWidgetComponent;
  @ViewChild('fakeOnEditDepartmentDiv', { static: true }) fakeOnEditDepartmentDiv: ElementRef;
  constructor(private modalService: BsModalService,
    private adminPanelService: AdminPanelService,
    private toastr: ToastrService,
    public router: Router, 
   
    private deviceDetailsService: DeviceDetailsService,
    private gallopLocalStorage: GallopLocalStorageService,
    public searchService: SearchService,
    private activatedRoute: ActivatedRoute,
    public ngxSmartModalService: NgxSmartModalService,
    private ngxAnaltics:GoogleAnalyticsService,
    private cdRef: ChangeDetectorRef,
    private fb: UntypedFormBuilder,
    private titleService: Title,
    private userAccountService: UserAccountService,
    public translateService: TranslateService,) {
    // this.employee= Employee;
  }
  public flightsDatasource = new Datasource({
    get: (index, count, success) => {
      let item = [];
      let max = this.filterEmployee.length;
      const lastElementIndex = Math.min(index + count, max);
      if (index < lastElementIndex) {
        item= this.filterEmployee.slice(index, lastElementIndex);
      }
      success(item);
    },
    settings:{
      startIndex: 0,
      bufferSize: 50,
      padding: 10,
      windowViewport: true
    }
  });
  onAddEditDeptNameModelCancel() {
    this.openUpdateModal = false;
    this.bsAddEditDeptNameModelRef.hide();
  }
  onLookUpAdminModalCancel() {
    this.bsLookUpAdminModalRef.hide();
    this.openUpdateModal = false;

  }

  onUpdateDeptModelCancel() {
    this.updateDept = false;
    if (this.adminPanelService.employeeList && this.adminPanelService.employeeList.length > 0) {
      let employeeFilterList = this.adminPanelService.employeeList;
      this.appproverList = JSON.parse(JSON.stringify(this.sortList(employeeFilterList)));
      if(this.appproverList.length > 0){
        this.appproverList = this.appproverList.filter(item=> item.employeeInfo.limitedUser!==true)
      }
    }
    this.bsUpdateDeptModelRef.hide();
  }
  changeStyle1() {
    if (!this.isMobile) {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '730px' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '99vw' }
      }
    }
  }
  changeStyle2() {
    if (!this.isMobile) {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '335px', 'overflow': 'hidden' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '775px', 'overflow': 'hidden', 'width': '350px' }
      }
    }
  }



  customTabClicked() {
    this.viewMode1 = 'tab11';
  }
  presetsTabClicked() {
    this.viewMode1 = 'tab12';
  }
 
  openNgxModal(id, picker) {
   
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);

    setTimeout(() => {
      this.viewMode1 = 'tab11';
      picker.show();
    }, 200);
  }
  observable() {
    this.adminPanelService.activeTravellerResponseObservable$.subscribe((response) => {
      if (response) {
        this.initialBoundsSet = false;
        this.origReportResponse = deserialize(response);
        if (this.origReportResponse.bookingList.length > 0) {
          this.resultErrorMessage = this.translateService.instant('activeTraveler.FetchingData');;
        } else {
          this.resultErrorMessage = 'activeTraveler.0travelersfound';
          this.zoom = 2;
        }
        this.buildCompanyReportData(this.origReportResponse);
        this.applyBtn = false;
      } else {
        this.initialBoundsSet = false;
        this.resultErrorMessage = 'activeTraveler.0travelersfound';
        this.applyBtn = false;
        this.zoom = 2;
      }
    });
  }
  changeStyle11($event, i) {
    if ($event.type == 'mouseover') {
      this.hover = true;
      this.number = i;
    } else {
      this.hover = false;
      this.number = 0;
    }
  }
getDepartmentName(id){
  if(this.temp_filter_employee && this.temp_filter_employee.length > 0){
  if(id==='All dept'){
    return 'fuild.AllDepartments'
  }else{
  let departmentName = this.adminPanelService.getDepartmentName(id);  
  return departmentName;
  }
}else{
  return 'employee.Select'
}
}
  buildCompanyReportData(response) {
    this.activeTravellersList = [];
    for (let optionItem of response.bookingList) {
      const reportItem = {};
      // const [ first, last ] = response.users[optionItem.userid].userName.split(" ");
      let travellerFullName = response.users[optionItem.userid].userName;
      if (optionItem.primaryTraveller && optionItem.primaryTraveller.userName) {
        travellerFullName = optionItem.primaryTraveller.userName;
      }
      reportItem['name'] = travellerFullName;
      const [first, last] = travellerFullName.split(' ');
      reportItem['shortname'] = (first.charAt(0) + last.charAt(0));
      let departmentName = this.adminPanelService.getDepartmentName(response.users[optionItem.userid].departmentId);
      reportItem['department'] = departmentName;
      reportItem['bookingDate'] = optionItem.bookingDate;
      reportItem['type'] = optionItem.type;
      reportItem['destinations'] = optionItem.destinationCity;
      let location = optionItem.location;
      const [lat, long] = location.split(",");
      reportItem['latitude'] = Number.parseFloat(lat);
      // this.latitudeForDestination= Number.parseFloat(lat);
      reportItem['longitude'] = Number.parseFloat(long);
      // this.longitudeForDestination= Number.parseFloat(long);
      reportItem['fitBounds'] = true;
      reportItem['markerClicked'] = false;
      reportItem['ticketNumber'] = optionItem.ticketnumber ? optionItem.ticketnumber : null;
      this.activeTravellersList.push(reportItem);
    }
  }

  getMarkerName(data) {
    return data.shortname;
  }
  changeProperty() {
    this.activeTravellersList.map(function (x) {
      x.markerClicked = false;
      return x;
    });
  }
  zoomChanged($event) {
    this.zoom = $event;
  }
  mapReadyCallback($event) {
  }

  boundsChangedCallback($event) {
    if (!this.initialBoundsSet) {
      setTimeout(() => {
        if (this.zoom > 15) {
          this.zoom = 15;
        }
      }, 1500);
    }
    this.initialBoundsSet = true;
  }

  markerClick($event, data, index) {
    this.changeProperty();
    data.markerClicked = true;
    this.titleService.setTitle(this.translateService.instant('report.TransactionsReport'));
    this.adminPanelService.from = 'activeTraveller';
    this.adminPanelService.selectDateRange = this.selectDateRange;
    this.gallopLocalStorage.setItem("from", this.adminPanelService.from);
    data.isRecommanded = true;
    let bookedOption = this.origReportResponse.bookingList[index];
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          view: 'detail',
          type: 'detail',
          name: name,
          bookingType: 'upcoming',
          userEmail: bookedOption.userid,
          ticketid: bookedOption.ticketid,
          tripid: bookedOption.tripid,
          transactionid: bookedOption.option.selectTransId
        },
      }
    );
    //this.activeTravellersList = this.getDimensionsByFind($event.latitude);
  }

  setStartDate(date) {
    if (date) {
      // this.daterangepickerModel =date;
      this.startDate = date[0];
      this.endDate = date[1];
    }
    // this.minimumDate=this.startDate;
    if (this.startDate > this.endDate) {
     // this.endDate = date[1];
      this.adminPanelService.lastReportDate = this.endDate;
    }

  }
  public isDutyOfCareEnabled() {
    return (this.userAccountService.isDutyOfCareEnabled && this.searchService.mapSupprted);
  }
  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
    this.ngxSmartModalService.close('daterangeSelection1');
    this.adminPanelService.firstReportDate = this.startDate;
    this.adminPanelService.lastReportDate = this.endDate;
    this.maximumDate1 = this.endDate;
    this.adminPanelService.firstReportDate = this.startDate;
    this.adminPanelService.filterDateType = this.selectDateRange;
    this.selectDateRange = '';
    this.adminPanelService.filterDateType = this.selectDateRange;
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'employees',
            subType:'activeTravelers',
            subSubType:(this.startDate + ',' + this.endDate)
        },
        replaceUrl: false
      }
    );
    if (!this.adminPanelService.originalTravellerResponse) {
      this.subscriptionEvents();
    } else {
      this.adminPanelService.filterActiveTraveler1()
    }
  }
  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    const dayHoverHandler = event.dayHoverHandler;
    //this.dateValue1 = "CUSTOMDATE";
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        this.searchService.hoverCounter++;
        if (this.searchService.hoverCounter > 1) {
          (picker as any)._datepickerRef.instance.daySelectHandler(cell);
        }
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }
  searchByNameActiveChanged(nameString: string) {
    this.adminPanelService.filterTravellerName = nameString;
    this.adminPanelService.filterActiveTraveler1()
  }
  getTravelerData(item) {
    this.getData(item);
    if (!this.adminPanelService.originalTravellerResponse) {
      this.subscriptionEvents();
    } else {
      this.adminPanelService.filterActiveTraveler1()
    }
  }
  getData(item) {
    this.selectDateRange = item;
    this.adminPanelService.filterDateType = this.selectDateRange;
    if (item === 'today') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: 'employees',
            subType:'activeTravelers',
            subSubType:'today'
          },
          replaceUrl: false
        }
      );
    } else if (item === '7Days') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() + 1);
      this.endDate.setDate(this.endDate.getDate() + 7);
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: 'employees',
            subType:'activeTravelers',
            subSubType:'7Days'
          },
          replaceUrl: false
        }
      );
    } else if (item === '30Days') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() + 1);
      this.endDate.setDate(this.endDate.getDate() + 30);
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: 'employees',
            subType:'activeTravelers',
            subSubType:'30Days'
          },
          replaceUrl: false
        }
      );
    }
  }

  
companySSoPolicy=false;
private detectChanges(){
  try {
    this.flightsDatasource.adapter.reload(0);
    this.cdRef.reattach();
    this.cdRef.detectChanges();
  
  } catch(err) {

  }
}
  ngOnInit() {
    this.carCompanies = this.fb.array([]);
    this.settingForm = this.fb.group({
      carCompanies: this.carCompanies
    }, { validator: '' }
      // profileImage: ['', null],
    );
    this.subscribeEvents();
    this.modalService.onHide.subscribe(result => {
      if (this.searchService.networkErrorpopupHide && this.adminPanelService.updateDepartment) {
            this.updateDept=false;
            this.adminPanelService.updateDepartment=false;
      }
    });
    this.adminPanelService.fetchEmployeesList(this.userAccountService.getUserCompanyId());
    this.selectedDepartment = null;
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });
    this.queryParamSubscription = this.activatedRoute.queryParams.subscribe(params => {
      if (params['subSubType'] == 'today') {
        this.selectDateRange = 'today';
        this.viewMode2 = 'tab23';
       // this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ActiveTravellers'));
        this.adminPanelService.filterDateType = this.selectDateRange;
        this.getTravelerData('today')
      } else if (params['subSubType'] == '7Days') {
        this.selectDateRange = '7Days';
        this.viewMode2 = 'tab23';
       // this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ActiveTravellers'));
        this.adminPanelService.filterDateType = this.selectDateRange;
        this.getTravelerData('7Days');
      } else if (params['subSubType'] == '30Days') {
        this.selectDateRange = '30Days';
        this.viewMode2 = 'tab23';
       // this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ActiveTravellers'));
        this.adminPanelService.filterDateType = this.selectDateRange;
        this.getTravelerData('30Days');
      } else {
        if (params['subSubType']) {
          const [start, end] = params['subSubType'].split(',');
          this.startDate = new Date(start);
          this.endDate = new Date(end);
          this.selectDateRange = '';
          this.viewMode2 = 'tab23';
        //  this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ActiveTravellers'));
          this.adminPanelService.firstReportDate = this.startDate;
          this.adminPanelService.lastReportDate = this.endDate;
          this.adminPanelService.filterDateType = this.selectDateRange;
          this.daterangepickerModel = [this.startDate, this.endDate];
          //  this.minimumDate=this.startDate;
          if (start && end) {
            if (!this.adminPanelService.originalTravellerResponse) {
              this.subscriptionEvents();
            } else {
              this.adminPanelService.filterActiveTraveler1()
            }
          }
        }
      }
      if (params['subType'] == 'companyTravelers') {
        this.viewMode2 = 'tab22';
      }else if (params['subType'] == 'departments') {
        this.viewMode2 = 'tab21';
      }
    });
    setTimeout(() => {
      this.observable();
    }, 2000);
  }
  TabClicked(item,subType,subType1?) {
    this.viewMode2 = item;
    
    if(subType==='companyTravelers'){
     // this.temp_filter_employee=['All dept'];
      this.departmentArrayForDropdwon=[{ value: 'All Departments', departmentId: 'All dept',name:"fuild.AllDepartments" }];
        this.departmentArray = this.sortListDept(this.adminPanelService.getDepartments());
        if(this.departmentArray.length > 0){
        for(let item of this.departmentArray){
          this.departmentArrayForDropdwon.push(item);
        }
      }
      this.onChangeDepartment('All dept',true)
    }
    if(this.empNameSearchValue !==''){
    this.empNameSearchValue ='';
    this.adminPanelService.filterByEmployeeName('');
    this.selectDateRange = 'today'; 
    this.adminPanelService.filterTravellerName='';
    this.adminPanelService.filterDateType = this.selectDateRange;
     this.adminPanelService.filterActiveTraveler1();
    }
    if(subType1){
      this.applyBtn = true;
      //this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ActiveTravellers'));
    this.router.navigate(["admin"],
    {
      queryParams:
      {
        type: 'employees',
        subType: subType,
        subSubType:subType1
      },
      replaceUrl: false
    }
  );
    }else{
     
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'employees',
          subType: subType
        },
        replaceUrl: false
      }
    );
    }
  }
  ngOnDestroy() {
    this.unSubscribeEvents();
    this.queryParamSubscription.unsubscribe();
    this.errormsg = this.translateService.instant('employee.Fetchingemployeelist')
    this.applyButton1 = true;
    this.adminPanelService.applyButton1 = true;
    this.adminPanelService.errorResponseEmpty();
    this.adminPanelService.filterEmployeeListBySearchName = '';
  }
  setFocusOnInput(id){
    setFocusOnInputField(id, '');
  }
  subscribeEvents() {
    this.responseErrorSubscription = this.adminPanelService.responseErrorSubject$.subscribe(error => {
      if (error) {
        // window.alert(error);
        this.showSpinner = false;
        this.toastr.error(error);
        if (this.updateDept) {
          this.updateDept = false;
        }
      }
    });
    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {

        if (this.bsModalRef) this.bsModalRef.hide();
        // if(this.bsAddEditDeptNameModelRef)    this.bsAddEditDeptNameModelRef.hide();     
        // if(this.bsLookUpAdminModalRef)    this.bsLookUpAdminModalRef.hide();     
        if (this.bsUpdateDeptModelRef) this.bsUpdateDeptModelRef.hide();;
        // this.cardValue ='';   
        this.domainOptions = [];
        let apiDomainList: Array<CompanyDomain> = this.adminPanelService.getDomains();
        this.selectedOption.push({ value: apiDomainList[0].domain });
        this.companySettings = settings;
        this.companySSoPolicy = settings.ssoStatus
        if (this.companySettings.cardList && this.companySettings.cardList.card_list.length > 0) {
          for (let item of this.companySettings.cardList.card_list) {
            let cardObject = { id: '', value: '', brand: '', last4: '' }
            cardObject.value = item.brand + " " + item.last4;
            cardObject.brand = item.brand;
            cardObject.id = item.id;
            cardObject.last4 = item.last4;
            if (this.cardOptions.find(item => item.id === cardObject.id) === undefined) {
              this.cardOptions.push(cardObject);
            }
          }


        }
        if (this.companySettings.cardList && this.companySettings.cardList.card_list.length > 0) {
          for (let cIndex in this.companySettings.cardList.card_list) {
            if (this.companySettings.cardList.card_list[cIndex].selected) {
              this.selectedCardId = this.companySettings.cardList.card_list[cIndex].id
              // this.cardValue= this.selectedCardId;
              break;
            }
          }
        }
        this.departmentArrayForDropdwon=[{ value: 'All Departments', departmentId: 'All dept',name:"fuild.AllDepartments" }];
        this.departmentArray = this.sortListDept(this.adminPanelService.getDepartments());
        if(this.departmentArray.length > 0){
        for(let item of this.departmentArray){
          this.departmentArrayForDropdwon.push(item);
        }
      }
      //  this.departmentArrayForDropdwon = this.sortListDept(this.adminPanelService.getDepartments());
      //  
        if(this.temp_filter_employee.length === 0){
          this.onChangeDepartment('All dept', true);
        }
        if (this.companySettings.company.discount_code && this.companySettings.company.discount_code.length > 0) {
          //let control = <FormArray>this.settingForm.controls.carCompanies;
          //  this.addDiscountData(this.companySettings.company.discount_code);
        }

        for (let domainItem of apiDomainList) {
          this.domainOptions.push({ value: domainItem.domain, id: '' + domainItem.domainId });
        }
        this.policyOptions = [];
        let departmentPolicyList = this.adminPanelService.getDepartmentPolicyList(-1);
        for (let policyOption of departmentPolicyList) {
          this.policyOptions.push({ value: policyOption.policyName, id: '' + policyOption.policyId });
        }
        
       
      }
    });
    this.listEmployeesResponseSubscription = this.adminPanelService.employeeListResponseObservable$.subscribe(response => {
      if (response) {
        if (this.bsModalRef) this.bsModalRef.hide();
        if (this.bsAddEditDeptNameModelRef) this.bsAddEditDeptNameModelRef.hide();
        if (this.bsLookUpAdminModalRef) this.bsLookUpAdminModalRef.hide();
        if (this.bsUpdateDeptModelRef) this.bsUpdateDeptModelRef.hide();
        if (this.adminPanelService.applyButton1 && this.filterEmployee && this.filterEmployee.length === 0) {
          this.errormsg = this.translateService.instant('employee.Fetchingemployeelist')
          this.applyButton1 = true;
        }
        this.selectEmp=[];
        this.employeeSelecttoDelete=[];
        if (response && response.length === 0) {
          if (this.searchbyName) {
            this.errormsg = this.translateService.instant('employee.Noemployeehasbeenfound')
            this.applyButton1 = false;
          } else if (this.adminPanelService.applyButton1 && this.filterEmployee && this.filterEmployee.length === 0) {
            this.errormsg = this.translateService.instant('employee.Fetchingemployeelist')
            this.applyButton1 = true;
          } else {
            this.errormsg = this.translateService.instant('employee.Noemployeehasbeenfound')
            this.applyButton1 = false;
          }
        }
        this.userInfoListOrg = this.sortList(response);
        if (this.adminPanelService.employeeList && this.adminPanelService.employeeList.length > 0) {
          let employeeFilterList = this.adminPanelService.employeeList;
          this.appproverList = JSON.parse(JSON.stringify(this.sortList(employeeFilterList)));
          if(this.appproverList.length > 0){
            this.appproverList = this.appproverList.filter(item=> item.employeeInfo.limitedUser!==true)
          }
        }
        this.userInfoList = this.userInfoListOrg;
        if ((this.userAccountService.onBoardingTask.indexOf('employees') === -1)) {
          if (this.userInfoList && this.userInfoList.length === 2 && this.adminPanelService.addEmployee) {
            let dataEmit = { newadmin: true, show: true }
            this.addEmployeeAddedEmitter.emit(dataEmit);
          } else if (this.userInfoList && this.userInfoList.length === 2 && !this.adminPanelService.addEmployee) {
            let dataEmit = { newadmin: false, show: true }
            this.addEmployeeAddedEmitter.emit(dataEmit);
          } else if (this.userInfoList && this.userInfoList.length > 2) {
            let dataEmit = { newadmin: false, show: true }
            this.addEmployeeAddedEmitter.emit(dataEmit);
          }
        }
        // this.filterEmployee = this.buildEmployeesList(response);  
        this.filterEmployeeList(this.temp_filter_employee, true);
      }
    });

    this.listManagersResponseSubscription = this.adminPanelService.managerListResponseObservable$.subscribe(response => {
      if (response) {
        if (this.bsModalRef) this.bsModalRef.hide();
        if (this.bsAddEditDeptNameModelRef) this.bsAddEditDeptNameModelRef.hide();
        if (this.bsLookUpAdminModalRef) this.bsLookUpAdminModalRef.hide();
        if (this.bsUpdateDeptModelRef) this.bsUpdateDeptModelRef.hide();
        this.managerInfoList = this.sortList(response);
        this.managers = this.buildEmployeesList(response);;
      }
    });


    this.checkEmployeeDetailResponseSubscription = this.adminPanelService.employeeCheckDetailResponseObservable$.subscribe(response => {
      if (response && response.flowType) {
        if (response.flowType === 'addManager') {
          this.showAddManagerModal();
        } else if (response.flowType != 'addAdmin' && response.flowType != 'changeApprover') {
          this.showAddEmployeeModal();
        } else if (response.flowType === 'addAdmin') {
          this.showToBeAdminDetail = true;
          this.showSpinner = false;
          if (response.userInfo && response.userInfo.firstName) {
            this.emailForm.controls['firstName'].setValue(response.userInfo.firstName);
            this.disabledInput = true;
          } else {
            this.emailForm.controls['firstName'].setValue('');
          }
          if (response.userInfo && response.userInfo.lastName) {
            this.emailForm.controls['lastName'].setValue(response.userInfo.lastName);
            this.disabledInput = true;
          } else {
            this.emailForm.controls['lastName'].setValue('');
          }
        }
      }
    });


   
     this.subscriptionEvents();
  }
  isDisable() {
    if ((this.emailForm.controls['firstName'].value === 'FirstName' || this.emailForm.controls['firstName'].value === '') && (this.emailForm.controls['lastName'].value === 'LastName' || this.emailForm.controls['lastName'].value === '')) {
      return null;
    } else if ((this.emailForm.controls['firstName'].value !== '' && this.emailForm.controls['lastName'].value !== '') && this.disabledInput) {
      return true;
    }
  }
  subscriptionEvents(){
    this.adminPanelService.originalTravellerResponse = undefined;
    this.activeTravellersList = [];
    this.adminPanelService.filterTravellerName = '';
    this.empNameSearchValue = '';
    this.resultErrorMessage = this.translateService.instant('activeTraveler.FetchingData');;
    this.applyBtn = true;
    let endDate1 = new Date();
    endDate1.setDate(this.startDate.getDate() + 180);
    let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
    let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(endDate1);

    let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
    let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
    this.adminPanelService.navigateFrom = 'active';
    this.adminPanelService.fetchActiveTraveler(this.userAccountService.getUserCompanyId(), startDate, endDate);


    this.userAccountService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      if (userAccountInfoObj == null) {
        this.origReportResponse = null;
        this.adminPanelService.originalTravellerResponse = undefined;
        this.adminPanelService.filterTravellerName = '';
        this.adminPanelService.removeDataAfterLogging();
        this.adminPanelService.selectDateRange = 'today';
        this.adminPanelService.loginSessionChanged();
      }
    });

  }
  getCardBrand(dept) {
    if (this.cardOptions.length > 0) {
      for (let item of this.cardOptions) {
        if (item.id === dept.card_id) {
          return item.brand;
        }
      }
    } else {
      return '';
    }

  }
  isBillingNUmberExis(dept) {
    let counter = 0;
    if (dept.discount_code && dept.discount_code.length > 0) {
      for (let item of dept.discount_code) {
        if (item.billingNumber) {
          counter = counter + 1;
        }
      }
      return counter;
    }

  }
  getCardBrand1(dept) {
    if (this.cardOptions.length > 0) {
      for (let item of this.cardOptions) {
        if (item.id === dept) {
          return item.brand;
        }
      }
    } else {
      return '';
    }

  }
  changeStyle(index) {
    if (index === 0) {
      return { 'padding-right': '15px', 'text-align': 'end' }
    } else {
      return { 'padding-right': '0px', 'text-align': 'start' }
    }
  }
  getCardLast4(dept) {
    if (this.cardOptions.length > 0) {
      for (let item of this.cardOptions) {
        if (item.id === dept.card_id) {
          return item.last4;
        }
      }
    } else {
      return '';
    }

  }
  addDiscountData(discountData) {
    // this.carCompanies = this.fb.array([]);
    for (let item of discountData) {
      let carCompant = { partnerName: '', discountCode: '', billingNumber: '', partnerCode: '', edit: true }
      carCompant.discountCode = item.discountCode;
      carCompant.partnerName = item.partnerName;
      carCompant.partnerCode = item.partnerCode;
      carCompant.billingNumber = item.billingNumber;
      if (carCompant.partnerName && (carCompant.discountCode || carCompant.billingNumber)) {
        let carCompanyForm = this.fb.group({
          partnerCode: [carCompant.partnerCode],
          partnerName: [carCompant.partnerName],
          discountCode: carCompant.discountCode ? [carCompant.discountCode] : null,
          billingNumber: carCompant.billingNumber ? [carCompant.billingNumber] : null,
          edit: carCompant.edit,
        });
        if (!this.findDupliCate(item)) {
          this.carCompanies.push(carCompanyForm);
        }

        (<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['partnerCode'].setValidators(
          Validators.compose([Validators.required]));
        (<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['partnerName'].setValidators(
          Validators.compose([Validators.required]));
        if (!carCompant.discountCode) {
          (<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['billingNumber'].setValidators(
            Validators.compose([]));
        }
        if (!carCompant.billingNumber) {
          (<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['discountCode'].setValidators(
            Validators.compose([Validators.required]));
        }
      }
    }
    //this.getAdditionalReqeustControl();
    
  }
  closeDropdown() {
    this.dropDownopen = false;
  }
  closeDropdown1() {
    this.dropDownopen1 = false;
  }
  findDupliCate(item) {
    let myArray = this.carCompanies.controls;
    //
    let test = myArray.filter(data => data.value.partnerName === item.partnerName && item.partnerName != null);
    if (test.length > 0) {
      return true;
    } else {
      return false;
    }
  }
  isEmptyCode(index: number) { 
    const carCompaniesControl = (<UntypedFormGroup>this.carCompanies.controls[index]).controls;
    const partnerCode = carCompaniesControl.partnerCode;
    const billingNumber = carCompaniesControl.billingNumber;

    if(!partnerCode.touched && partnerCode.value == "" && billingNumber.touched ){
      return true;
    }else{
      if (partnerCode.value === '' && partnerCode.touched) {
        return true;
      } else {
        return false;
      };
    };
    
  }
  isEmptyDiscountCode(index: number) {
      const carCompaniesControl = (<UntypedFormGroup>this.carCompanies.controls[index]).controls;
      const discountCode = carCompaniesControl.discountCode;
      const billingNumber = carCompaniesControl.billingNumber;

      if(!discountCode.touched && discountCode.value == "" && billingNumber.touched ){
        return true;
      } else {
        if (discountCode.value == '' && discountCode.touched) {
          return true;
        } else {
          return false;
        }
      }
    
  }
  IsOnlyAlphanumeric(i:number,name:string){
    const carCompaniesControl = (<UntypedFormGroup>this.carCompanies.controls[i]).controls;
    const controlerName = carCompaniesControl[name];
    if(controlerName.value != "" && controlerName.status == "INVALID"){
      return true;
    }else{
      return false;
    };
  };
  isDuplicateEntry(index: number) {
    if (index && this.carCompanies
      && this.carCompanies.controls
      && (<UntypedFormGroup>this.carCompanies.controls[index]).controls) {
      if ((<UntypedFormGroup>this.carCompanies.controls[index]).controls['partnerCode'].value !== '') {
        let val: string = (<UntypedFormGroup>this.carCompanies.controls[index]).controls['partnerCode'].value;
        for (var counter = 0; counter < this.carCompanies.controls.length - 1; counter++) {
          if (counter !== index) {
            if (val === (<UntypedFormGroup>this.carCompanies.controls[counter]).controls['partnerCode'].value) {
              this.duplicateEntryStatus = true;
              return true;
            }
          }
        }
      }
    }
    return false;
  }
  unsetCardMode() {
    this.addCardMode = false;
  }
  private cardTokenData: any;
  public handleBackFromAddCard(data: any) {
    if (!data) {
      this.unsetCardMode();
      return;
    }


    let tokenData = JSON.parse(data);
    if (tokenData && tokenData.type === 'newCardAdded') {

      // this.userAccountInfoService.fetchUserAccountInfo(this.emailId, this.sToken);
      let cardTokens: any = tokenData.tokens;
      if (cardTokens && cardTokens.error && cardTokens.error.length > 0) {
        // this.toastr.error(cardTokens.error, 'Card Error!');
        this.addCardChild.setAddCardProgress(false);
        this.addCardChild.setErrorMessage('');
      }

      else if (cardTokens.token && cardTokens.gToken) {
        this.cardTokenData = cardTokens;
        GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
          'cardTokenCreated', 'WebSearchUI'
        );
        this.adminPanelService.requestSaveCardInfo(cardTokens.token, cardTokens.gToken).subscribe(res => {
          // this.userAccountInfoService.requestSaveCardInfo(cardTokens.token, cardTokens.gToken).subscribe(res => {
          if (res.status === 'success') {
            this.addCardFlow = true;
            this.adminPanelService.addNewCard = true;
            // this.userAccountInfoService.fetchUserAccountInfo(false);
            this.adminPanelService.processCompanySettingsRequest(this.userAccountService.getUserCompanyId());
            this.unsetCardMode();
          } else if (res.status === 'CARDERROR') {
            // this.toastr.error(res.message, 'Card Error!');
            this.addCardChild.setErrorMessage(res.message);
            this.addCardChild.setAddCardProgress(false);

          } else {
            // this.toastr.error(res.message, 'Error!');
            this.addCardChild.setErrorMessage(res.message);
            this.addCardChild.setAddCardProgress(false);
          }
        });

      } else {
        // this.addCardChild.setErrorMessage(this.translateService.instant('paymentDetails.UnknownErrorPleasetryagain').toString());
        // this.addCardChild.setAddCardProgress(false);
      }
    }
  }
  isLoggedIn(): boolean {
    return this.userAccountService.isLoggedIn();
  }
  setCardMode() {
    this.addCardMode = true;
  }
  onRemoveCompanies(index) {
    let control = <UntypedFormArray>this.settingForm.controls.carCompanies;
    control.removeAt(index);
  }
  isEmptyBillingCode(index: number) {
    if ((<UntypedFormGroup>this.carCompanies.controls[index]).controls['billingNumber'].value === '' && (<UntypedFormGroup>this.carCompanies.controls[index]).controls['discountCode'].value === '') {
      return true;
    } else {
      return false;
    }
  }
  onChangeDepartment(option, event) {
    if (event) {
      if (option == "All dept") {
        this.temp_filter_employee=[];
        this.temp_filter_employee.push('All dept');
        
      }
      else {
        this.temp_filter_employee = this.temp_filter_employee.filter(dept => {
          if (dept !== 'All dept') return true;
        });
        this.temp_filter_employee.push(option);
      }
    } else if (!event && option == "All dept") {
      this.temp_filter_employee = [];
      this.errormsg = 'No employees in selected departments.'
      this.applyButton1 = false;
    } else {
      this.temp_filter_employee = this.temp_filter_employee.filter(dept => {
        if (dept !== option && dept !== 'All dept') return true;
      });
    }
    this.isDeptChecked(option);
    this.filterEmployeeList(this.temp_filter_employee, event);
  }
  isDeptChecked(option) {
    return this.temp_filter_employee.indexOf(option) > -1;
  }
  employeeSelecttoDelete=[];
  onChangeEmployee(option, event) {
    let userid = this.userAccountService.getUserEmail();
    if (event) {
      if (option == "All employee") {
        this.selectEmp.push("All employee");
        for(let item of this.filterEmployee){
         
          if(userid!==item.email){
          this.selectEmp.push(item.email);
          this.employeeSelecttoDelete.push(item); 
          }
         
        }
        
      }
      else {
        this.selectEmp = this.selectEmp.filter(dept => {
          if (dept !== 'All employee') return true;
        });
        if(userid!==option){
          this.selectEmp.push(option);
          }
        //this.selectEmp.push(option);
        let  findIndexValue = this.filterEmployee.findIndex(item => item.email===option);
        if(findIndexValue > -1){
        this.employeeSelecttoDelete.push(this.filterEmployee[findIndexValue]); 
        }
      }
    } else if (!event && option == "All employee") {
      this.selectEmp = [];
      this.employeeSelecttoDelete=[];
      
    } else {
      this.selectEmp = this.selectEmp.filter(dept => {
        if (dept !== option && dept !== 'All employee') return true;
      });
      this.employeeSelecttoDelete = this.employeeSelecttoDelete.filter(dept => {
        if (dept.email !== option  && dept !== 'All employee') return true;
      });
    }
    this.isDeleteEmployeeChecked(option);
    //this.filterEmployeeList(this.temp_filter_employee, event);
  }
  isDeleteEmployeeChecked(option) {
    return this.selectEmp.indexOf(option) > -1;
  }
  getAdditionalReqeustControl(): AbstractControl[] {
    return (<UntypedFormArray>this.settingForm.controls['carCompanies']).controls;

  }
  onAddCarCompanies() {
    let control = <UntypedFormArray>this.settingForm.controls.carCompanies;
    if (control.length > 0) {
      control.push(
        this.fb.group({
          partnerName: ['', Validators.compose([Validators.required])],
          partnerCode: ['', Validators.compose([Validators.required])],
          billingNumber: [''],
          discountCode: ['', this.conditionalValidators((<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['billingNumber'].value, [Validators.required])],

        }));
    } else {
      control.push(
        this.fb.group({
          partnerName: ['', Validators.compose([Validators.required])],
          partnerCode: ['', Validators.compose([Validators.required])],
          billingNumber: [''],
          discountCode: [''],

        }));
    }
  }
  onCompanyChange(i: number) {
    this.duplicateEntryStatus = false;
    if ((<UntypedFormGroup>this.carCompanies.controls[i]).controls['partnerCode'].value === '') {
    } else {
      let carName: string = (<UntypedFormGroup>this.carCompanies.controls[i]).controls['partnerCode'].value;
      let selectedObject = this.carRentalCompanies.find(item => item.value == carName);
      (<UntypedFormGroup>this.carCompanies.controls[i]).controls['partnerCode'].setValue(selectedObject.value);
      (<UntypedFormGroup>this.carCompanies.controls[i]).controls['partnerName'].setValue(selectedObject.label);
      if (i === this.carCompanies.controls.length - 1) {
        //this.addFrequentFlyerEntry(undefined);
      }
    }
  }
  conditionalValidators(condition, validators: ValidatorFn[]) {
    if (condition === '') {
      return Validators.compose(validators);
    }
    return null;
  }
  filterEmployeeList(deptList, event) {
    if (!this.userInfoListOrg) return;
    this.userInfoList = JSON.parse(JSON.stringify(this.userInfoListOrg));
    this.userInfoList = this.userInfoList.filter(dept => {
      let dept1 = dept.employeeInfo.departmentId;
      for (let code of deptList) {
        if (deptList.indexOf('All dept') > -1) {
          return true;
        } else {
          if (dept1 == code) {
            return true;
          }
        }
      }
      if (deptList.length === 0 && !event) {
        let departmentObj = this.companySettings.departments.find(obj => obj.departmentId == dept1);
        if (!departmentObj)
          return true;
      }
      return false;
    });
    this.userInfoList = this.sortList(this.userInfoList);
    this.filterEmployee = this.buildEmployeesList(this.userInfoList)
    if(this.filterEmployee.length===0){
      this.errormsg = this.translateService.instant('employee.Noemployeehasbeenfound')
      this.applyButton1 = false;
    }
    this.detectChanges()
    return this.filterEmployee;
  }
getRole(item){
  if(item==='EMPLOYEE'){
    return 'TRAVELER';
  }else{
    return item;
  }
}
  sortList(data) {
    data.sort(function (a, b) {
      // 
      if (a.firstName === "" || !a.firstName) {
        return 1;
      } else if (b.firstName === "" || !b.firstName) {
        return -1;
      } else if (a.firstName < b.firstName) { return -1; }
      else if (a.firstName > b.firstName) { return 1; }
      return 0;
    })
    return data;
  }
  sortListDept(data) {
    data.sort(function (a, b) {
      if (a.name < b.name) { return -1; }
      if (a.name > b.name) { return 1; }
      return 0;
    })
    return data;
  }
  openNgxModal1(id) {
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);

  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  getEmployeePolicyName(employeeInfo: EmployeeInfo) {
    if (employeeInfo.policyId && employeeInfo.policyId > 0) {
      return this.adminPanelService.getDepartmentPolicyName(employeeInfo.policyId);
    } else if (employeeInfo.departmentId && employeeInfo.departmentId > 0) {
      return this.adminPanelService.getDefaultDepartmentPolicyName(employeeInfo.departmentId);
    } else {
      return this.adminPanelService.getDefaultPolicyName();
    }
  }
  private buildEmployeesList(userInfoList: Array<UserInfo>): any[] {
    let employeeList = [];

    for (let userInfo of userInfoList) {

      let employee = {};
      employee['firstName'] = userInfo.firstName;
      employee['lastName'] = userInfo.lastName;
      employee['email'] = userInfo.email;
      employee['role'] = userInfo.employeeInfo ? userInfo.employeeInfo.role : '';
      employee['title'] = userInfo.employeeInfo ? userInfo.employeeInfo.jobTitle : "";
      if (userInfo.employeeInfo && userInfo.employeeInfo.approvers && userInfo.employeeInfo.approvers.length > 0) {
        employee['approvers'] = [...userInfo.employeeInfo.approvers];
      } else {
        const selectedDepartmentId = userInfo.employeeInfo.departmentId;
        const selectedCompanyId = userInfo.companyId;
        employee['approvers'] = this.getEmployeeApprovers(selectedDepartmentId);
      };
      employee['employeeId'] = userInfo.employeeInfo ? userInfo.employeeInfo.employeeId : '';
      employee['departmentId'] = userInfo.employeeInfo ? userInfo.employeeInfo.departmentId : '';
      employee['department'] = userInfo.employeeInfo ? this.adminPanelService.getDepartmentName(userInfo.employeeInfo.departmentId) : '';
      employee['policy'] = userInfo.employeeInfo ? this.getEmployeePolicyName(userInfo.employeeInfo) : '';
      // if (userInfo.employeeInfo){
      //   let adminDetail : UserDetails = this.adminPanelService.getDefaultAdminDetail(userInfo.employeeInfo.departmentId);
      //   if (userInfo.employeeInfo.managerEmail){
      //     employee['Manager'] = this.getManagerName(userInfo.employeeInfo.managerEmail);
      //   }else{
      //     if (adminDetail.emailId === userInfo.email){
      //       employee['Manager'] = this.getPrimaryDepartmentAdminName(null);
      //     }else{
      //       employee['Manager'] = this.getPrimaryDepartmentAdminName(userInfo.employeeInfo.departmentId);
      //     }
      //   }
      // }else{
      //   employee['Manager'] = this.getPrimaryDepartmentAdminName(null);
      // }
      //      employee['Manager'] = userInfo.employeeInfo && userInfo.employeeInfo.managerEmail ? this.getManagerName(userInfo.employeeInfo.managerEmail) : this.getPrimaryDepartmentAdminName(userInfo.employeeInfo.departmentId);
      let managerEmail = '';
      let managerName = '';
      if (userInfo.employeeInfo && userInfo.employeeInfo.managerEmail) {
        managerEmail = userInfo.employeeInfo.managerEmail.toString();
        if (userInfo.employeeInfo.managerName) {
          managerName = userInfo.employeeInfo.managerName.toString();
        }
      }
      employee['Manager'] = userInfo.employeeInfo ? userInfo.employeeInfo.managerEmail : '';
      employee['verified'] = userInfo.employeeInfo ? userInfo.employeeInfo.verified : false;
      employee['role'] = userInfo.employeeInfo ? userInfo.employeeInfo.role : false;
      employee['paymentOption'] = userInfo.employeeInfo ? userInfo.employeeInfo.companyCardAccess ? "Bill Company" : "Personal Card" : "";
      employeeList.push(employee);
    }
    employeeList.sort(function (a, b) {
      if (a.firstName === "" || !a.firstName) {
        return 1;
      } else if (b.firstName === "" || !b.firstName) {
        return -1;
      } else if (a.firstName < b.firstName) { return -1; }
      else if (a.firstName > b.firstName) { return 1; }
      return 0;
    })
    return employeeList;
  }

  getEmployeeApprovers(employeeDepartment:any){
    
    const departmentApprovel = this.adminPanelService.companySettings.departments.find(e => employeeDepartment == e['departmentId']);
    if(departmentApprovel){
      let array = [];
      departmentApprovel.approvers.forEach((e)=>{
        const newData  = this.translateService.instant('employee.Default') + " " + "(" + e + ")";
        array.push(newData);
      })
      return array;
    }else{
      let array = [];
      const defAdmin = this.adminPanelService.companySettings.defaultAdmin.emailId;
      const newData = this.translateService.instant('employee.Default') + " " + "(" + defAdmin + ")";
      array.push(newData);
      return array;

    }
   
  }

  unSubscribeEvents() {
    this.responseErrorSubscription.unsubscribe();
    this.listEmployeesResponseSubscription.unsubscribe();
    this.checkEmployeeDetailResponseSubscription.unsubscribe();
  }
  deleteMultipleEmployee(Modal){
    this.deleteButton = false;
    this.bsModalRef = this.modalService.show(Modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
  }
  showDeleteModal(Modal, index) {
    this.deleteButton = false;
    this.employeeSelecttoDelete=[];
    this.selectEmp=[];
    this.employeeSelecttoDelete.push(this.filterEmployee[index]);
    this.selectEmp.push(this.filterEmployee[index].email);
    this.bsModalRef = this.modalService.show(Modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
  }
  downloadEmployeeList() {
    if (!this.filterEmployee || this.filterEmployee.length == 0) {
      this.toastr.error(this.translateService.instant("employee.Norecordavailabletogeneratereport"));
      return;
    }
    let reportTable = [];
    let reportRow = []
    reportRow.push('First Name');
    reportRow.push('Last Name');
    reportRow.push('Email');
    reportRow.push('Department');
    reportRow.push('Role');
    reportRow.push('Policy');
    reportRow.push('Approver');
    reportRow.push('Payment Options');
    reportTable.push(reportRow);
    for (let rowItem of this.filterEmployee) {
      let reportRow = []
      reportRow.push(rowItem['firstName']);
      reportRow.push(rowItem['lastName']);
      reportRow.push(rowItem['email']);
      reportRow.push(rowItem['department']);
      if(rowItem['role']==='EMPLOYEE'){
        reportRow.push('TRAVELER');
      }else{
      reportRow.push(rowItem['role']);
      }
      reportRow.push(rowItem['policy']);
      if(rowItem['approvers'] && rowItem['approvers'].length===1){
      reportRow.push(rowItem['approvers'][0]);
      }else{
        if(rowItem['approvers'] && rowItem['approvers'].length > 1){
        let str='';
        for(let i=0;i<rowItem['approvers'].length;i++){
          str = str +","+rowItem['approvers'][i]
        }
        reportRow.push(str);
      }
      }
      reportRow.push(rowItem['paymentOption']);
      reportTable.push(reportRow);
    }
    let filename = "EmployeeList.pdf";
    //  
    this.adminPanelService.downloadReport(reportTable, filename).subscribe(
      data => {
        // saveAs(data, filename);
        // FileSaver.saveAs(pdf, filename);
        let blob = new Blob([data], { type: 'application/xls' });

        var downloadURL = window.URL.createObjectURL(data);
        var link = document.createElement('a');
        link.href = downloadURL;
        link.download = "Employee list.xls";
        link.click();
      },
      err => {
        alert("Problem while downloading the file.");
        console.error(err);
      }
    );
  }
  onApproverChangeClicked(item, event) {
    
    this.approverChange = true;
    if (event.target.checked) {
      if (this.multipleSelectedApprover.indexOf(item) === -1) {
        this.multipleSelectedApprover.push(item);
      }

    } else {
      this.multipleSelectedApprover = this.multipleSelectedApprover.filter(item1 => item1 !== item);
      this.isApproverChecked(item);
    }
  }
  isApproverChecked(item) {
    return this.multipleSelectedApprover.indexOf(item) > -1;
  }
  onEmailSendNotificationChangeClicked(item, event) {
    if (event.target.checked) {
      if (this.bookingEmailsSentTo.indexOf(item) === -1) {
        this.bookingEmailsSentTo.push(item);
      }

    } else {
      this.bookingEmailsSentTo = this.bookingEmailsSentTo.filter(item1 => item1 !== item);
      this.isApproverChecked(item);
    }
    if(this.isCustomSelectedInEmailSendNotification()){
      this.editDepartmentForm.controls['email'].setValidators([Validators.required,Validators.pattern(Constants.RGEX_EMAIL)]);
      this.editDepartmentForm.controls['email'].updateValueAndValidity(); 
    }else{
      this.emailNotificationList=[];
      this.editDepartmentForm.controls['email'].clearValidators();
      this.editDepartmentForm.controls['email'].setValidators(Validators.pattern(Constants.RGEX_EMAIL)); 
      this.editDepartmentForm.controls['email'].updateValueAndValidity();
this.editDepartmentForm.controls['email'].setValue(null);
    }
  }
  getEmailNotificationLabel(item1){
    let label = this.emailDropdown.filter(item => item.id===item1);
    if(label && label[0]){
      return label[0].value;
    }

  }
  inputEmailValue(value){
    let val=  value.replace(',', '');
    this.editDepartmentForm.controls['email'].setValue(val);
   }
  isEmailSendNotificationChecked(item) {
    return this.bookingEmailsSentTo.indexOf(item) > -1;
  }
  setBFO(event){
    if(!event){
      this.emailNotificationList=[];
      this.editDepartmentForm.controls['bookingEmailConfig'].setValue(['DEFAULT']);
      this.editDepartmentForm.controls['guestUserDepartmentId'].setValue(null);
      this.editDepartmentForm.controls['saveGuestUsers'].setValue('DEFAULT');
      this.editDepartmentForm.controls['minorBookingEnabled'].setValue('DEFAULT');
    }
  }
  addEmailList(){
    if(this.editDepartmentForm.controls['email'].value !==null && this.editDepartmentForm.controls['email'].value !=='' && this.editDepartmentForm.controls['email'].valid){
this.emailNotificationList.push(this.editDepartmentForm.controls['email'].value);
this.editDepartmentForm.controls['email'].clearValidators();
this.editDepartmentForm.controls['email'].setValidators(Validators.pattern(Constants.RGEX_EMAIL));
this.editDepartmentForm.controls['email'].updateValueAndValidity();
this.editDepartmentForm.controls['email'].setValue(null);
    }
  }
  unselectedEmployee(index) {
   
    this.emailNotificationList.splice(index, 1);
  }
  isCustomSelectedInEmailSendNotification(){
   return this.bookingEmailsSentTo.indexOf('CUSTOM') > -1
  }
  showRemoveAdmin(modal, index) {
    this.openUpdateModal = true;
    this.AdminIndex = index;
    this.bsRemoveAdminModalRef = this.modalService.show(modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true, keyboard: false,
    });
  }
  onCancelModal1() {
    this.openUpdateModal = false;
    this.deleteDept1 = false;
    this.selectEmp=[];
    this.bsRemoveAdminModalRef.hide();
  }
  searchByNameChanged(text: string) {
    this.searchbyName = true;
    this.adminPanelService.filterByEmployeeName(text);
  }
  searchByDepartmentNameAndId(term: string, item: any) {
    term = term.toLowerCase();
    return (item.name && item.name.toLowerCase().indexOf(term) > -1)
  }
  searchByApproverNameAndEmailChanged(term: string, item: any) {
    term = term.toLowerCase();
    return (item.firstName && item.firstName.toLowerCase().indexOf(term) > -1) ||
      (item.lastName && item.lastName.toLowerCase().indexOf(term) > -1) || item.email.toLowerCase().indexOf(term) > -1;
  }
  searchMgrByNameChanged(text: string) {
    this.adminPanelService.filterByManagerName(text);
  }
  showModal2(Modal) {
    this.bsModalRef.hide();
    this.bsModalRef = this.modalService.show(Modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true, keyboard: false,
    });
  }
  showModal(index: number, manager: boolean) {
    this.adminPanelService.departmentSelect = '';
    this.adminPanelService.userChangedApprover = false;
    if (manager) {
      this.adminPanelService.employeePopOpen = true;
      this.adminPanelService.setEditEmployeeUserInfo(this.managerInfoList[index]);
    }
    else {

      this.adminPanelService.setEditEmployeeUserInfo(this.userInfoList[index]);
      this.bsModalRef = this.modalService.show(AddEmployeeComponent, {
        initialState: {
          mode: 'edit'
        }, backdrop: true, ignoreBackdropClick: true
      });
      this.adminPanelService.employeePopOpen = true;
    }
  }

  showManagerModal(index: number) {
    this.adminPanelService.setEditEmployeeUserInfo(this.managerInfoList[index]);
    this.bsModalRef = this.modalService.show(AddEmployeeComponent, {
      initialState: {
        mode: 'addManager'
      }, backdrop: true, ignoreBackdropClick: true
    });
  }

  deleteEmployee() {
    this.selectEmp = this.selectEmp.filter(item => item !=='All employee');
    this.adminPanelService.processDeleteEmployee(this.selectEmp);
    this.deleteButton = true;
    this.selectEmp=[];
  }

  resendInvite() {
    this.adminPanelService.processInviteEmployee(this.userAccountService.getUserCompanyId(), this.selectEmp[0]['email']);
    this.bsModalRef.hide();
  }

  onModelCancel() {
    this.bsModalRef.hide();
    setTimeout(() => {
      this.selectEmp =[];
      this.employeeSelecttoDelete=[];
    }, 300);
   
  }

  showAddEmployeeModal() {
    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }
    this.bsModalRef = this.modalService.show(AddEmployeeComponent, {
      initialState: {
        mode: 'add'
      }, backdrop: true, ignoreBackdropClick: true
    });
  }

  showAddManagerModal() {
    this.bsModalRef.hide();
    this.bsModalRef = this.modalService.show(AddEmployeeComponent, {
      initialState: {
        mode: 'addManager'
      }, backdrop: true, ignoreBackdropClick: true
    });
  }
  showToBeAdminDetail = false;
  showAdminLookUpModal(modal) {
    this.openUpdateModal = true;
    this.showSpinner = false;
    this.disabledInput = false;
    this.showToBeAdminDetail = false;
    this.emailForm = this.fb.group({
      emailUserName: ['', Validators.compose([Validators.required])],
      domain: [(this.domainOptions && this.domainOptions.length > 0) ? this.domainOptions[0].value : '', Validators.compose([Validators.required])],
      email: ['', Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_EMAIL)])],
      firstName: ['FirstName', Validators.compose([Validators.required])],
      lastName: ['LastName', Validators.compose([Validators.required])],
    });
    this.bsLookUpAdminModalRef = this.modalService.show(modal, {
      initialState: {}, backdrop: true, ignoreBackdropClick: true, keyboard: false,
    });;
  }

  showLookUpModal(modal) {
    this.emailForm = this.fb.group({
      emailUserName: ['', Validators.compose([Validators.required])],
      domain: ['', Validators.compose([Validators.required])],
      email: ['', Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_EMAIL)])],
    });
    this.bsModalRef = this.modalService.show(modal);
  }
  filterSelectedlist(item) {
    for (let option of item) {
      this.appproverList = this.appproverList.filter(item2 => item2.email !== option);
    }
    
    for (let option of item) {
      let originalList;
      if (this.adminPanelService.employeeList && this.adminPanelService.employeeList.length > 0) {
        let employeeFilterList = this.adminPanelService.employeeList;
        originalList = this.sortList(employeeFilterList);
      }
      if (originalList && originalList.length > 0) {
        let approver = originalList.find(item3 => item3.email === option)
        this.appproverList.unshift(approver);
      if(this.appproverList.length > 0){
        this.appproverList = this.appproverList.filter(item=> item.employeeInfo.limitedUser!==true)
      }
      }
    }
    
  }
  filterSelectedDeptlist(item) {
    this.departmentArrayForDropdwon= this.departmentArrayForDropdwon.filter(item4 => item4.departmentId !=='All dept'); 
    for (let option of item) {
      this.departmentArrayForDropdwon = this.departmentArrayForDropdwon.filter(item2 => item2.departmentId !== option);
    }
    
   
    for (let option of item) {
      let departmentArray=[];
      if(this.departmentArray.length > 0){
        for(let item of this.departmentArray){
          departmentArray.push(item);
        }
      }
     // departmentArray = this.sortListDept(this.adminPanelService.getDepartments());
      if (departmentArray && departmentArray.length > 0) {
       
        let department = departmentArray.find(item3 => item3.departmentId === option)
        if(department){
        this.departmentArrayForDropdwon.unshift(department);
        }
      }
      
    }
    this.departmentArrayForDropdwon.unshift({ value: 'All Departments', departmentId: 'All dept',name:"fuild.AllDepartments" });
    
  }
  checkEmployeeDetail() {
    this.adminPanelService.departmentSelect = '';
    this.adminPanelService.userChangedApprover = false;
    //  if(this.emailForm.valid){
    if (this.temp_filter_employee.length === 2 && this.temp_filter_employee[0] === 'All dept') {
      this.adminPanelService.departmentSelect = this.temp_filter_employee[1];
    } else if ((this.temp_filter_employee.length === 1 && this.temp_filter_employee[0] !== 'All dept')) {
      this.adminPanelService.departmentSelect = this.temp_filter_employee[0];
    } else {
      this.adminPanelService.departmentSelect = '';
    }
    this.adminPanelService.toAddEmployeeUserInfo = deserialize({}, UserInfo);
    this.bsModalRef = this.modalService.show(AddEmployeeComponent, {
      initialState: {
        mode: 'add'
      }, backdrop: true, keyboard: false, ignoreBackdropClick: true
    });
    this.adminPanelService.employeePopOpen = true;
    //}else{
    // this.emailForm.controls['emailUserName'].markAsTouched();
    //this.emailForm.controls['email'].markAsTouched();
    //this.emailForm.controls['emailUserName'].updateValueAndValidity();
    // this.emailForm.controls['email'].updateValueAndValidity();
    //}


  }

  checkManagerDetail() {
    if (this.emailForm.valid) {
      this.showSpinner = true;
      this.adminPanelService.processCheckEmployeeDetail(this.emailForm.controls['email'].value,
        this.userAccountService.getUserCompanyId(), 'addManager');
    } else {
      this.emailForm.controls['emailUserName'].markAsTouched();
      this.emailForm.controls['email'].markAsTouched();
      this.emailForm.controls['emailUserName'].updateValueAndValidity();
      this.emailForm.controls['email'].updateValueAndValidity();
    }
  }

  checkAdminDetail() {
    if (this.emailForm.valid) {
      this.showSpinner = true;
      let defaultAdmin: UserDetails = this.adminPanelService.getDefaultAdminDetail(this.selectedDepartment.departmentId);
      let adminEmail = this.emailForm.controls['email'].value;
      if (adminEmail == defaultAdmin.emailId) {
        this.showSpinner = false;
        this.toastr.error(this.translateService.instant("employee.Userwithemailid'") + adminEmail + this.translateService.instant("employee.isalreadydefaultadmin"));
        return;
      } else if (this.selectedDepartment.adminList && this.selectedDepartment.adminList.find(obj => obj.emailId == adminEmail)) {
        this.showSpinner = false;
        this.toastr.error(this.translateService.instant("employee.ApprovercannotbethesameasemployeePleasechooseadifferentapprover"));
        return;
      }
      this.adminPanelService.processCheckEmployeeDetail(this.emailForm.controls['email'].value,
        this.userAccountService.getUserCompanyId(), 'addAdmin');
    } else {
      this.emailForm.controls['emailUserName'].markAsTouched();
      this.emailForm.controls['email'].markAsTouched();
      this.emailForm.controls['emailUserName'].updateValueAndValidity();
      this.emailForm.controls['email'].updateValueAndValidity();
    }
  }
  emailUserNameChange(userName) {
    let domain = this.emailForm.controls['domain'].value;
    this.emailForm.controls['email'].setValue(userName.trim() + '@' + domain);
  }
  emailDomainChange(domain) {
    let userName = this.emailForm.controls['emailUserName'].value;
    this.emailForm.controls['email'].setValue(userName.trim() + '@' + domain.value);
  }

  canShowAddDepartment() {
    return this.userAccountService.showSettingsTab();
  }


  showDeleteEmployee(email) {
    let userid = this.userAccountService.getUserEmail();
    if (userid === email) {
      return false;
    } else {
      return true;
    }


  }
  setCarOptions(item) {
    this.carOptionsName = item;

  }
  onEditcarRental(i) {
    (<UntypedFormGroup>this.carCompanies.controls[i]).controls['edit'].setValue(false);
  }
  setApproverEmail(item) {
    this.editDepartmentForm.controls['approvername'].setValue(item.email);
    setTimeout(() => {
      this.ngxSmartModalService.close('approverDropdown');
    }, 100);
  }
  initEditDepartmentForm(departmentIndex) {
    let approverName = '';
    this.updateDeptButtonClicked = false;
    this.approverChange = false;
    this.openUpdateModal = false;
    this.dropDownopen = false;
    if (departmentIndex != undefined) {
      let department = JSON.parse(JSON.stringify(this.departmentArray[departmentIndex]));
      this.selectedDepartment = department;
      this.multipleSelectedApprover = [];
      if (department.approvers && department.approvers.length > 0) {
        this.multipleSelectedApprover = [...department.approvers];
      }
      this.bookingEmailsSentTo = [];
      if (department.deptSetting && department.deptSetting.bookingEmailConfig && department.deptSetting.bookingEmailConfig[0] !=='DEFAULT') {
        this.bookingEmailsSentTo = department.deptSetting.bookingEmailConfig;
      }
      if (department.deptSetting &&department.deptSetting.bookingEmailCustomId && department.deptSetting.bookingEmailCustomId.length > 0) {
        this.emailNotificationList = department.deptSetting.bookingEmailCustomId.split(',');
  
      }else{
        this.emailNotificationList=[];
      }
      if (this.selectedDepartment.allowOnlyForSpecificCarCorporatedId) {
        this.carOptionsName = this.selectedDepartment.allowOnlyForSpecificCarCorporatedId
      } else {
        this.carOptionsName = false;
      }
      if (this.selectedDepartment.card_id !== '') {
        this.cardValue = this.selectedDepartment.card_id;
      } else {
        if (this.selectedCardId !== '') {
          this.cardValue = this.selectedCardId;
        }
      }
      if (this.selectedDepartment.discount_code && this.selectedDepartment.discount_code.length > 0) {
        this.carCompanies = this.fb.array([]);
        this.settingForm = this.fb.group({
          carCompanies: this.carCompanies
        }, { validator: '' }
          // profileImage: ['', null],
        );
       // this.addDiscountData(this.selectedDepartment.discount_code);
      } else {
        this.carCompanies = this.fb.array([]);
        this.settingForm = this.fb.group({
          carCompanies: this.carCompanies
        }, { validator: '' }
          // profileImage: ['', null],
        );
        //this.addDiscountData(this.companySettings.company.discount_code);
      }
    } else {
      if (this.selectedCardId !== '') {
        this.cardValue = this.selectedCardId;
      }
      this.carCompanies = this.fb.array([]);
      this.settingForm = this.fb.group({
        carCompanies: this.carCompanies
      }, { validator: '' }
        // profileImage: ['', null],
      );
    }
    this.editDepartmentForm = this.fb.group({
      departmentname: [(this.selectedDepartment && this.selectedDepartment.name) ? this.selectedDepartment.name : '', Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_DEPARTMENTNAME)])],
      policy: [(this.selectedDepartment && this.selectedDepartment.defaultPolicyId !== -1) ? "" + this.selectedDepartment.defaultPolicyId : this.adminPanelService.getDefaultPolicyID()+'', Validators.compose([Validators.required])],
      defaultAgentRole: [(this.selectedDepartment && this.selectedDepartment.defaultAgentRole !== 'BOOK_FOR_OTHERS') ? false : true],
      defaultSSoRole: [(this.selectedDepartment && this.selectedDepartment.ssoPolicy!=='mandatory') ? false : true],
      bookingEmailConfig:[(this.selectedDepartment && this.selectedDepartment.deptSetting && this.selectedDepartment.deptSetting.bookingEmailConfig ? this.selectedDepartment.deptSetting.bookingEmailConfig : 'DEFAULT')],
      guestUserDepartmentId:[(this.selectedDepartment &&  this.selectedDepartment.deptSetting && this.selectedDepartment.deptSetting.guestUserDepartmentId ? this.selectedDepartment.deptSetting.guestUserDepartmentId: null)],
      minorBookingEnabled:[(this.selectedDepartment && this.selectedDepartment.deptSetting &&  this.selectedDepartment.deptSetting.minorBookingEnabled ? this.selectedDepartment.deptSetting.minorBookingEnabled : 'DEFAULT')],
      saveGuestUsers:[(this.selectedDepartment && this.selectedDepartment.deptSetting &&  this.selectedDepartment.deptSetting.saveGuestUsers ? this.selectedDepartment.deptSetting.saveGuestUsers : 'DEFAULT')],
      email: [null, Validators.compose([Validators.pattern(Constants.RGEX_EMAIL)])]
    });
    if(this.selectedDepartment && this.selectedDepartment.departmentType!== 'REGULAR'){
      this.editDepartmentForm.controls['defaultAgentRole'].setValue(false);
    }
    if(this.emailNotificationList.length ===0 && this.isCustomSelectedInEmailSendNotification()){
      this.editDepartmentForm.controls['email'].setValidators([Validators.required,Validators.pattern(Constants.RGEX_EMAIL)]);
      this.editDepartmentForm.controls['email'].updateValueAndValidity(); 
    }
  }
 
  removesso(event){
    
      this.editDepartmentForm.controls['defaultSSoRole'].setValue(event);
   
  }
  public spaceValidator(control: AbstractControl) {
    if (control.value.length > 0) {
      let isWhitespace = (control.value || '').trim().length === 0;
      let isValid = !isWhitespace;
      return isValid ? null : { 'whitespace': true }
    }
  }
  initAddDepartmentForm() {
    this.addDepartmentForm = this.fb.group({
      departmentname: ['', Validators.compose([Validators.required])],
    });

  }
  addDepartmentModelMode = "add";
  getSelectedLanguage() {
    const selectedLang = localStorage.getItem('selectedLanguage');
    if (selectedLang) {
      return selectedLang;
    } else {
      const userLang = CommonUtils.getUserPreferredLanguage(this.translateService);
      if (userLang !== this.translateService.currentLang) {
        this.translateService.use(userLang);
      }
      return userLang;
    }
  }
  onAddDepartment(Model, value) {
    this.openUpdateModal = true;
    this.initAddDepartmentForm();
    this.multipleSelectedApprover = [];
    this.carOptionsName = false;
    //  let adminDetail : UserDetails = this.adminPanelService.getDefaultAdminDetail(departmentId);
    let department = {
      allowOnlyForSpecificCarCorporatedId: false,
      card_id: this.selectedCardId,
      companyId: this.userAccountService.getUserCompanyId(),
      defaultPolicyId: -1,
      departmentId: -1,
      defaultAgentRole: 'BOOK_FOR_OTHERS',
      name: "",
      ssoPolicy:"optional",
      deptSetting:{
      bookingEmailConfig: ["DEFAULT"],
      guestUserDepartmentId:null,
    bookingEmailCustomId: null,
    saveGuestUsers: "DEFAULT",
     minorBookingEnabled: "DEFAULT"
      },
      departmentType:'REGULAR',
      discount_code: [],
      approvers: [],
      adminList: []
    }
    if(this.companySSoPolicy){
      department.ssoPolicy='mandatory'
    }else{
      department.ssoPolicy='optional'
    }
    this.selectedDepartment = department;
    this.triggerFalseClick();
  }

  onEditDepartment(Model, departmentIndex) {
    this.updateDept = false;
    this.openUpdateModal = false;
    this.initEditDepartmentForm(departmentIndex);
    this.bsUpdateDeptModelRef = this.modalService.show(Model, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
  }


  getSelectedPolicyName() {
    let policyId = this.editDepartmentForm.controls['policy'].value;
    return this.getPolicyName(policyId);
  }

  getPolicyName(policyId) {
    if (!policyId || policyId == -1 || policyId == 0) {
      return this.adminPanelService.getDefaultPolicyName();
    } else {
      return this.adminPanelService.getDepartmentPolicyName(policyId);
    }
  }
  getCardName(cardId) {
    if (cardId === '') {
      return '';
    } else {
      let selectCard = this.cardOptions.findIndex(item => item.id === cardId);
      if (selectCard !== -1) {
        let cardName = this.cardOptions[selectCard].value;
        return cardName;
      } else {
        return '';
      }

    }

  }
  getPrimaryDepartmentAdminName(departmentId) {
    let adminDetail = this.departmentArray.find(item => item.departmentId === (departmentId));
    if (adminDetail.approver && (Object.keys((adminDetail.approver)).length === 3))
      return adminDetail.approver.firstName + " " + adminDetail.approver.lastName + " (" + adminDetail.approver.emailId + ")";
    else
      return "";
  }
  addEditDepartment() {
    if (this.addDepartmentForm.valid) {
      this.openUpdateModal = false;
      let departmentName = this.addDepartmentForm.controls['departmentname'].value;
      if (!this.adminPanelService.doesDepartmentExists(departmentName)) {
        if (!this.addDepartmentModelMode || this.addDepartmentModelMode == "add") {
          this.processAddCompanyDepartmentRequest(departmentName);
        } else {
          this.editDepartmentForm.controls['departmentname'].setValue(departmentName);
          this.selectedDepartment.name = departmentName;
          this.bsAddEditDeptNameModelRef.hide();
        }
      } else {
        this.toastr.error(this.translateService.instant("employee.Department '") + departmentName + this.translateService.instant("employee.alreadyexists"));
      }
    } else {
      this.addDepartmentForm.controls['departmentname'].markAsTouched();
    }
  }

  processAddCompanyDepartmentRequest(newDepartmentName: string) {

    this.adminPanelService.addDepartmentRequest(this.userAccountService.getUserCompanyId()
      , newDepartmentName).subscribe(res => {
        if (res && res.success) {
          this.selectedDepartment = res.data;
          this.triggerFalseClick();
          if (this.bsAddEditDeptNameModelRef) this.bsAddEditDeptNameModelRef.hide();
          this.adminPanelService.processCompanySettingsRequest(this.userAccountService.getUserCompanyId());

          // this.toastr.success('Settings saved successfully !!');

        } else if (res && res.error_message) {
          this.toastr.error(res.error_message);
        } else {
          this.toastr.error(this.translateService.instant("employee.Apologiessomethingwentwrongwecouldntprocessrequest.Pleasetryagainlaterorcontactsupport"));
        }
      }, error => {
        if (error.status != 403) {
          setTimeout(() => {
            let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
            this.toastr.error(resultErrorMessage);
          }, 100);
        }
      })
  }

  onDeleteDepartment(Modal, index) {
    this.deleteDept = false;
    this.selectedDepartment = JSON.parse(JSON.stringify(this.departmentArray[index]));
    this.bsModalRef = this.modalService.show(Modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
  }

  validateCarCompanies(){
    if(this.carCompanies.controls.length > 0){

      const lastIndex = this.carCompanies.controls.length - 1;
      const carCompaniesControles = this.carCompanies.controls[lastIndex]['controls'];
      const partnerCode = carCompaniesControles.partnerCode;
      const discountCode = carCompaniesControles.discountCode;
      const billingNumber = carCompaniesControles.billingNumber;
      
      if(partnerCode.value == "" ){
        partnerCode.touched = true;
        return false;
      }else if( discountCode.value == "" && discountCode.status == "VALID"){
        discountCode.touched = true;
        return false;
      }else if(billingNumber.value !== "" && billingNumber.status == "INVALID"){
        return false;
      }else{
        return true;
      };
    }else{
      return true;
    };
  }
  updateDepartment(modal) {

    if(this.validateCarCompanies()){
      if (this.getAdditionalReqeustControl().length > 0) {
        if (this.settingForm.invalid)
          return;
        if (this.duplicateEntryStatus)
          return;
      }
      if (this.multipleSelectedApprover.length === 0) {
        this.updateDeptButtonClicked = true;
        return;
      }
      if (this.editDepartmentForm.invalid) {
        this.editDepartmentForm.controls['departmentname'].markAsTouched();
         this.editDepartmentForm.controls['email'].markAsTouched();
        return;
      }
      let updatedDepartment: Department = JSON.parse(JSON.stringify(this.selectedDepartment));
      updatedDepartment.card_id = this.cardValue;
      updatedDepartment.approvers = [...this.multipleSelectedApprover];
    if(this.selectedDepartment.ssoPolicy !=='none'){
      if (this.editDepartmentForm.controls['defaultSSoRole'].value) {
        updatedDepartment.ssoPolicy = 'mandatory';
      } else {
        updatedDepartment.ssoPolicy = 'optional';
      }
    }
    if(this.bookingEmailsSentTo && this.bookingEmailsSentTo.length > 0){
      updatedDepartment.deptSetting.bookingEmailConfig = this.bookingEmailsSentTo;
    }else{
      updatedDepartment.deptSetting.bookingEmailConfig =['DEFAULT']
    }
    if(this.emailNotificationList && this.emailNotificationList.length > 0){
      updatedDepartment.deptSetting.bookingEmailCustomId = this.emailNotificationList.toString();
    }else{
      updatedDepartment.deptSetting.bookingEmailCustomId =null;
    }
    updatedDepartment.deptSetting.saveGuestUsers= this.editDepartmentForm.controls['saveGuestUsers'].value;
    updatedDepartment.deptSetting.minorBookingEnabled= this.editDepartmentForm.controls['minorBookingEnabled'].value;
      updatedDepartment.name = this.editDepartmentForm.controls['departmentname'].value.trim();
      if (this.editDepartmentForm.controls['defaultAgentRole'].value) {
        updatedDepartment.defaultAgentRole = 'BOOK_FOR_OTHERS';
      } else {
        updatedDepartment.defaultAgentRole = 'BOOK_FOR_SELF';
      }
      if (this.departmentArray && this.departmentArray.length > 0) {
        let duplicateFound = this.departmentArray.find((item) =>
          (item.departmentId !== this.selectedDepartment.departmentId && item.name.toLowerCase() === updatedDepartment.name.toLowerCase())
        );
        if (duplicateFound) {
          this.toastr.error(this.translateService.instant("employee.Duplicatedepartment"));
          return;
        }
      }
      if (this.addDepartmentModelMode !== "add") {
        this.employeeInDeptWithApprover = [];
        this.employeeInDeptWithApprover1 = [];
        this.deptEmployee = this.filterEmployee.filter(item => item.departmentId === updatedDepartment.departmentId);
        
        this.deptEmployee = this.deptEmployee.filter(item => {
          if (item.approvers && item.approvers.length > 0)
            return true
        });
        
        if (this.deptEmployee.length > 0 && this.approverChange) {
          for (let item of this.deptEmployee) {
            this.employeeInDeptWithApprover.push(item.email);
          }
          this.employeeInDeptWithApprover1 = [...this.employeeInDeptWithApprover];
          this.openUpdateModal = true;
          this.bsLookUpAdminModalRef = this.modalService.show(modal, {
            initialState: {
            }, backdrop: true, ignoreBackdropClick: true
          });
        } else {


          updatedDepartment.allowOnlyForSpecificCarCorporatedId = this.carOptionsName;
          if (this.editDepartmentForm.controls['defaultAgentRole'].value) {
            updatedDepartment.defaultAgentRole = 'BOOK_FOR_OTHERS';
          } else {
            updatedDepartment.defaultAgentRole = 'BOOK_FOR_SELF';
          }
          updatedDepartment.discount_code = this.settingForm.controls['carCompanies'].value;
          updatedDepartment.defaultPolicyId = this.editDepartmentForm.controls['policy'].value;
          this.adminPanelService.updateDepartment =true
        this.updateDept = true;
          this.adminPanelService.processUpdateCompanyDepartmentRequest(updatedDepartment);
         // this.updateDept = true;
        }
      } else {
        updatedDepartment.allowOnlyForSpecificCarCorporatedId = this.carOptionsName;
        if (this.editDepartmentForm.controls['defaultAgentRole'].value) {
          updatedDepartment.defaultAgentRole = 'BOOK_FOR_OTHERS';
        } else {
          updatedDepartment.defaultAgentRole = 'BOOK_FOR_SELF';
        }
        updatedDepartment.discount_code = this.settingForm.controls['carCompanies'].value;
        updatedDepartment.defaultPolicyId = this.editDepartmentForm.controls['policy'].value;
        this.adminPanelService.updateDepartment =true
        this.updateDept = true;
        this.adminPanelService.processUpdateCompanyDepartmentRequest(updatedDepartment);
        
      }
    };  
  }
  onModelCancel1() {
    if (this.updateDept) {
      return;
    }
    this.bsLookUpAdminModalRef.hide();
    this.openUpdateModal = false;
  }
  getEmployeename(item) {
    let user = this.deptEmployee.find(item1 => item1.email === item);


    return (user.firstName + " " + user.lastName);
  }
  onEmployeeChangeClicked(item, event) {
    
    if (event.target.checked) {

      if (this.employeeInDeptWithApprover1.indexOf(item) === -1) {
        this.employeeInDeptWithApprover1.push(item);
      }

    } else {
      this.employeeInDeptWithApprover1 = this.employeeInDeptWithApprover1.filter(item1 => item1 !== item);
      this.isApproverChecked(item);
    }
  }
  isEmployeeChecked(item) {
    return this.employeeInDeptWithApprover1.indexOf(item) > -1;
  }
  onConfirmUpdateDepat() {
    this.adminPanelService.resetApprovers(this.employeeInDeptWithApprover1).subscribe(res => {
      if (res.success) {

      } else {
        this.toastr.error(res.error_message);
      }
    })
    let updatedDepartment: Department = JSON.parse(JSON.stringify(this.selectedDepartment));
    updatedDepartment.card_id = this.cardValue;
    updatedDepartment.approvers = [...this.multipleSelectedApprover];
    updatedDepartment.name = this.editDepartmentForm.controls['departmentname'].value.trim();
    updatedDepartment.allowOnlyForSpecificCarCorporatedId = this.carOptionsName;
    updatedDepartment.discount_code = this.settingForm.controls['carCompanies'].value;
    updatedDepartment.defaultPolicyId = this.editDepartmentForm.controls['policy'].value;
    this.adminPanelService.processUpdateCompanyDepartmentRequest(updatedDepartment);
    this.updateDept = true;
  }
  deleteDepartment() {
    this.deleteDept = true;
    let toDeleteDepartment: Department = JSON.parse(JSON.stringify(this.selectedDepartment));
  //  this.departmentArrayForDropdwon = this.departmentArrayForDropdwon.filter(item=> Number(item.departmentId) === toDeleteDepartment.departmentId)
    this.adminPanelService.processDeleteCompanyDepartmentRequest(toDeleteDepartment);
  }

  addAdminToDepartment() {
    if (this.emailForm.valid) {
      this.openUpdateModal = false;
      let addedAdminDetails: UserDetails = new UserDetails();
      addedAdminDetails.emailId = this.emailForm.controls['email'].value;
      addedAdminDetails.firstName = this.emailForm.controls['firstName'].value;
      addedAdminDetails.lastName = this.emailForm.controls['lastName'].value;
      this.selectedDepartment.adminList.push(addedAdminDetails);
      this.bsLookUpAdminModalRef.hide();
    } else {
      this.emailForm.controls['firstName'].markAsTouched();
      this.emailForm.controls['email'].markAsTouched();
      this.emailForm.controls['lastName'].markAsTouched();
      this.emailForm.controls['email'].updateValueAndValidity();
    }

  }


  removeDepartmentAdmin(adminDetail) {
    this.deleteDept1 = true;
    this.selectedDepartment.adminList.splice(this.AdminIndex, 1);
    this.onCancelModal1();
  }
  triggerFalseClick() {
    let el: HTMLElement = this.fakeOnEditDepartmentDiv.nativeElement;
    el.click();
  }
  getLabelValue1() {
    if (this.dropDownopen) {
      return 'fuild.Typetosearch';;
    }else if(this.temp_filter_employee && this.temp_filter_employee.length === 0){
      return 'employee.Select'
    }
    return '';
  }
  getLabelValue2() {
    if (this.dropDownopen) {
      return '';;
    }else {
      return 'employee.Select'
    }
  }
  getLabelValue() {
    if (this.dropDownopen) {
      return 'fuild.Typetosearch';;
    }
    return '';
  }

}
