import { Component, OnInit, EventEmitter, Output } from '@angular/core';
import { UserAccountService } from '../user-account.service';
import { Location } from '@angular/common';
import { CommonUtils } from '../util/common-utils';
import { GallopHttpClient } from '../shared/gallop-httpclient.service';
import { environment } from 'src/environments/environment';
declare var parseUrlParam: any;
@Component({
    selector: 'app-slack-msg',
    templateUrl: './slack-msg.component.html',
    styleUrls: ['./slack-msg.component.scss'],
    standalone: false
})
export class SlackMsgComponent implements OnInit {
  @Output() getOpenAccount = new EventEmitter();
  constructor(public userAccountInfoService: UserAccountService,
    private http: GallopHttpClient,
    private location: Location) { }
  errormsg = '';
  appluButton = false;
  ngOnInit(): void {
    this.subscription();
    this.location.replaceState('profile');
  }
  subscription() {
    var params = parseUrlParam();
    var code = params["code"];
    var state = params["state"];
    this.appluButton = false;
    this.userAccountInfoService.redirectFromSlack(code, state).subscribe(res => {
      
      if (res && res.success) {
        this.appluButton = true;
        this.userAccountInfoService.connectingToSlack = true;
        this.errormsg = 'Slack connected successfully.';
        CommonUtils.checkSlackStatusFromRemote(this.http, this.userAccountInfoService, environment.apiForSlackStatusCheck);
      } else {
        this.appluButton = true;
        this.userAccountInfoService.connectingToSlack = false;
        this.errormsg = res.error_message;
      }
    })
  }
  emitValue() {
    this.getOpenAccount.emit(true);
  }
  ngOnDestroy() {
    this.userAccountInfoService.slackTab = '';
  }
}
