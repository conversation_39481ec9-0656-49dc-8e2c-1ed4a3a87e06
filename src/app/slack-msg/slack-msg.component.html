<div class="card-div active shadow">
        <div class="card-div-inner" style="float: none !important;">
                <div *ngIf="!appluButton" class="col-12"
                        style="text-align: center;padding-top:0px;padding-left:15px;top: 0px;position: relative;">
                        <app-loader *ngIf="!appluButton" [spinnerStyle]="true"></app-loader>
                </div>
                <div *ngIf="appluButton" class="slack">
                        {{this.errormsg}}
                </div>
                <div>
                        <div *ngIf="appluButton" class="booking-view-button"
                                style="text-align: center;margin-bottom: 20px;">
                                <button class="btn primary-button" (click)="emitValue()">ok</button>
                        </div>
                </div>

        </div>
</div>