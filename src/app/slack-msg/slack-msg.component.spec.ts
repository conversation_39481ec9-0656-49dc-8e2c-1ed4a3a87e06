import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SlackMsgComponent } from './slack-msg.component';

describe('SlackMsgComponent', () => {
  let component: SlackMsgComponent;
  let fixture: ComponentFixture<SlackMsgComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SlackMsgComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SlackMsgComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
