@import "../../../variables.scss";

.flight-box {
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    border: 1px solid $border-color;
    border-radius: 6px;
    position: relative;
    font-size: 0.8333rem;
    background: $background-light;
    cursor: pointer;
    width: 100%;
    min-height: 145px;
    position: relative;
    padding-bottom: 35px;
    max-width: 550px;
    min-height: 160px;

    &.selected {
        border: 2px solid $accent-color;
    }

    &.can-view {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;

        .helper-text {
            color: $accent-color;
            font-size: 16px;
            line-height: 20px;
        }
    }

    &.cannot-view {
        height: unset;
        cursor: not-allowed;
        padding-top: 20px;

        .source-dest {
            color: $secondary-text-color;
        }
    }
}

.box {
    padding: 8px;
    max-width: 450px;
    margin: 0 auto;

    .icon-plane {
        color: $primary-color;
        font-size: 18px;
    }
}

.vertical {
    height: 100%;
    width: 2px;
    background: $border-light-color;
    margin: 0 4px;
}

.bottom {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 35px;
    padding: 0 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 2px solid $border-light-color;
}

.source-dest {
    display: flex;
    justify-content: space-between;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 4px;
}

.time-stops {
    display: flex;
    justify-content: space-between;
    color: $secondary-text-color;
}

.separator {
    height: 2px;
    background: $border-light-color;
    margin: 7px 0;
}

.price {
    font-size: 14px;
    color: var(--hyperlink-color);
}

.date-day {
    display: flex;
    justify-content: space-between;
    color: $secondary-text-color;
}


@media(min-width: 565px) {

    .flight-box-container {
        display: flex;
        width: 100%;
    }

    .flight-box {
        min-height: 25px;
        margin-right: 16px;
        width: 100%;
        padding-bottom: 0px;

        .separator-vertical {
            min-height: 90px;
            background: #E8E8E8;
            width: 2px;
            margin: 0 10px;
            display: block;
        }

        .source-dest {
            font-size: 12px;
        }

        .info {
            font-size: 10px;
        }

        .box {
            padding: 0 16px;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .bottom {
            font-size: 10px;
            min-height: 24px;
        }

        .date-time {
            font-size: 10px;
            display: flex;
            justify-content: space-between;
            color: $secondary-text-color;
        }
    }

    .passenger-info-right {
        font-size: 11px;
        text-align: center;
    }

    .continue-button {
        padding: 8px 24px;
    }

}

@media(max-width: 991px) {
    .flight-box {
        padding-top: 10px;

        .separator-vertical {
            display: none;
        }
    }
}