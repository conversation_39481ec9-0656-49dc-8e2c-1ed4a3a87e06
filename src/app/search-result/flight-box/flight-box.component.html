<ng-container *ngIf="isMobile">
  <div *ngIf="flight" class="flight-box" (click)="editBooking()">
    <div class="row">
      <ng-container *ngFor="let num of noOfFlightLegs">
        <ng-container *ngIf="flight.legs[num]">
          <div class="col">
            <div class="box">
              <div class="source-dest">
                <span>{{getAirportName(flight.legs[num].flightHops[0].from)}}</span>
                <span class="icon-plane"></span>
                <span>{{getAirportName(flight.legs[num].flightHops[flight.legs[num].flightHops.length-1].to)}}</span>
              </div>
              <div *ngIf="flight.legs[num]" class="time-stops">
                <span>{{getDisplayDate(flight.legs[num].flightHops[0].starts) | date:'shortTime'}}</span>
                <span>{{getDisplayDate(flight.legs[num].flightHops[flight.legs[num].flightHops.length-1].ends) |
                  date:'shortTime'}}</span>
              </div>
              <div *ngIf="flight.legs[num]" class="date-day">
                <span>{{getDisplayDate(flight.legs[num].flightHops[0].starts) | date:'EEE, MMM d'}}</span>
                <span>{{getDisplayDate(flight.legs[num].flightHops[flight.legs[num].flightHops.length-1].ends) |
                  date:'EEE, MMM d'}}</span>
              </div>
              <div class="separator"></div>
              <div class="text-center">
                <div>{{getFlightDuration(num)?.hrs + 'hrs ' + getFlightDuration(num)?.mins + 'mins'}} <span
                    class="text-secondary"></span>
                  {{flightLayoverInfoList[num]?(flightLayoverInfoList[num].length==0?'No Stops': ' with ' +
                  flightLayoverInfoList[num].length + ' Stop'):''}}</div>
                <div *ngFor="let layoverInfo of flightLayoverInfoList[num]">
                  <div><span class="text-danger">{{layoverInfo.duration.hrs>0?layoverInfo.duration.hrs+ 'h ':''}}
                      {{layoverInfo.duration.mins+ 'm' }}</span> {{'flightBox.layoverin' | translate}}
                    <b>{{layoverInfo.in}}</b></div>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="num<(noOfFlightLegs.length-1)" class="col-auto">
            <div class="vertical"></div>
          </div>
        </ng-container>
      </ng-container>
      <div class="bottom">
        <div>{{noOfPassengers>1?noOfPassengers + ('flightBox.Passengers' | translate): noOfPassengers +
          ('flightBox.Passenger' | translate)}}
          <span class="text-secondary">{{'flightBox.with' | translate}}</span> {{getClassName(class) | translate}}
          {{'flightBox.Class' | translate}}
        </div>
        <div class="price">{{flight.fareBreakup.price| currency:getCurrencySymbol(flight.currency)}}</div>
      </div>
    </div>
  </div>
</ng-container>

<ng-container *ngIf="!isMobile">
  <div class="flight-box-container" (click)="editBooking()">
    <ng-container *ngFor="let num of noOfFlightLegs">
      <ng-container *ngIf="flight && flight.legs[num]">
        <div *ngIf="flight" class="flight-box">
          <div class="row">
            <div class="col-md-12 col-lg">
              <div class="box">
                <div class="source-dest">
                  <span>{{getAirportName(flight.legs[num].flightHops[0].from)}}</span>
                  <span class="icon-plane"></span>
                  <span>{{getAirportName(flight.legs[num].flightHops[flight.legs[num].flightHops.length-1].to)}}</span>
                </div>

                <div class="date-time">
                  <span>{{getDisplayDate(flight.legs[num].flightHops[0].starts) | date:'shortTime'}}
                    {{getDisplayDate(flight.legs[num].flightHops[0].starts) | date:'EEE, MMM d'}}</span>

                  <span>{{getDisplayDate(flight.legs[num].flightHops[flight.legs[num].flightHops.length-1].ends) |
                    date:'shortTime'}}
                    {{getDisplayDate(flight.legs[num].flightHops[flight.legs[num].flightHops.length-1].ends) |
                    date:'EEE, MMM d'}}</span>
                </div>
              </div>
            </div>

            <div class="separator-vertical "></div>

            <div class="">
              <div class="box">
                <div class="info">
                  {{getFlightDuration(num)?.hrs + 'hrs ' + getFlightDuration(num)?.mins + 'mins'}}
                  <span class="text-secondary">{{'flightBox.with' | translate}}</span>
                  {{flightLayoverInfoList[num]?(flightLayoverInfoList[num].length==0?('flightBox.NoStops' | translate):
                  +
                  flightLayoverInfoList[num].length + ('flightBox.Stop' | translate)):''}}
                </div>

                <div *ngFor="let layoverInfo of flightLayoverInfoList[num]" class="info">
                  <span class="text-danger">{{layoverInfo.duration.hrs>0?layoverInfo.duration.hrs+ 'h ':''}}
                    {{layoverInfo.duration.mins+ 'm' }}</span> {{'flightBox.layoverin' | translate}} {{layoverInfo.in}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </ng-container>
  </div>
  <div class="passenger-info-right">
    <div>{{noOfPassengers>1?noOfPassengers + ('flightBox.Passengers' | translate): noOfPassengers +
      ('flightBox.Passenger' | translate)}}
      <span class="text-secondary">{{'flightBox.with' | translate}}</span> {{flight.legs[0].flightHops[0].fareClassName}}
      {{'flightBox.Class' | translate}}
    </div>
    <div class="price">{{flight.fareBreakup.price | currency:getCurrencySymbol(flight.currency)}}</div>
    <button class="btn primary-button continue-button" [disabled]="!isPaymentEnabled" type="button"
      (click)="goToPayment()">{{'flightBox.Continue' | translate}}</button>
  </div>
</ng-container>