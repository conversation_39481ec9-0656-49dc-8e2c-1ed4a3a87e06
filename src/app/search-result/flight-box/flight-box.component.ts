import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { SearchResultService } from '../../search-result.service';
import { Subscription } from 'rxjs';
import { SearchService } from '../../search.service';
import { FlightResult } from '../../entity/flight-result';
import { FlightClassType } from '../../enum/flight-class.type';
import { DateUtils } from '../../util/date-utils';
import { DeviceDetailsService } from '../../device-details.service';
import { FlightUtils } from '../../util/flight-utils';
import { deserialize } from '../../util/ta-json/src/methods/deserialize';
import { FlightSearchQueryParam } from '../../entity/flight-search-query-param';
import { Constants } from '../../util/constants';
import { GallopLocalStorageService } from 'src/app/gallop-local-storage.service';
import { CommonUtils } from 'src/app/util/common-utils';

@Component({
    selector: 'flight-box',
    templateUrl: './flight-box.component.html',
    styleUrls: ['./flight-box.component.scss'],
    standalone: false
})
export class FlightBoxComponent implements OnInit {

  @HostListener('window:load') onBeforeUnload() {
    this.updateTravelerDetailsFromLocalStorage();
  }

  @Input() noOfPassengers: number;
  @Input() class: FlightClassType;
  @Input() isPaymentEnabled: boolean;
  @Output() goToPaymentRequest = new EventEmitter();
  @Output() editBookingRequest = new EventEmitter();


  flight: FlightResult;
  flightHops: number = 0;
  currentSelectedFlightLegId: number = 0;
  selectedFlightSubscription: Subscription;
  isSelected: boolean = false;
  noOfFlightLegs: number[] = [0];
  flightLayoverInfoList: any[] = [];
  isMobile: boolean;
  classOptions = Constants.CLASS_OPTIONS;
  deviceSubscription: Subscription;

  constructor(private searchResultService: SearchResultService,
    private gallopLocalStorage: GallopLocalStorageService,
    private searchService: SearchService,
    private deviceDetailsService: DeviceDetailsService) {

  }

  ngOnInit() {
    this.selectedFlightSubscription = this.searchResultService.selectedFlight$.subscribe((flight) => {
      this.setFlight(flight);
    });

    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });

  }

  updateTravelerDetailsFromLocalStorage() {
    let flightSearchQuery: FlightSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("flightSearchRequest")), FlightSearchQueryParam);

    if (flightSearchQuery != null && flightSearchQuery.passengers && flightSearchQuery.class) {
      this.noOfPassengers = Number(flightSearchQuery.passengers);
      this.class = FlightClassType[flightSearchQuery.class];
    }
  }
  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }
  onSelect() {
    this.isSelected = true;
  }

  setFlight(flight: any) {
    this.flight = flight;
    this.setFlightLegs(this.flight.legs.length);
    this.flightLayoverInfoList = FlightUtils.getLegWiseLayoverList(this.flight);
  }

  setFlightLegs(count) {
    this.noOfFlightLegs = Array(count || 1).fill(1).map((x, i) => i)
  }

  getFlightDuration(legIndex: number) {
    return FlightUtils.getFlightLegDuration(this.flight, legIndex);
  }

  getAirportName(code): string {
    return this.searchService.getAirportName(code);
  }

  goToPayment() {
    this.goToPaymentRequest.emit();
  }

  editBooking() {
    this.editBookingRequest.emit();
  }

  getClassName(id) {

    if (!id) return;

    let className;

    this.classOptions.map(item => {
      if (item.id.toLowerCase() == id.toLowerCase()) {
        className = item.value;
      }
    });

    return className;
  }

  getDisplayDate(dateString: string): string {
    return DateUtils.getDisplayDate(dateString);
  }

  ngOnDestroy() {
    this.selectedFlightSubscription.unsubscribe();
    this.deviceSubscription.unsubscribe();
  }
}
