<email-header></email-header>
<div class="main-wrapper">
  <div class="content">
    <div class="container">
      <flight-search #searchComponent *ngIf="bookingWizardStep==0" [componentType]="'result'" (collapseToggle)="onCollapseToggle($event)" [showFilter]="isFlightListNonZero(orgFlightSearchResponse) && callComplete"(searchAgainWithFilter)="handleSelectedAirline($event)" (changeSelectedFlight)="killSearchRequest($event)" (searchRequest)="search()"></flight-search>
      <div *ngIf="((!isFlightListNonZero(this.flightSearchResponse)) && (bookingWizardStep==0)) && callComplete"
        class="no-flight-found-container">
        <img src="assets/images/no-flight-found.png" />
        <div class="text">{{resultErrorMessage}}</div>
      </div>

      <div class="flight-charts-container"
        *ngIf=" isFlightListNonZero(this.flightSearchResponse) && (bookingWizardStep==0) && callComplete">

        <div class="flight-list-sorting-container">
          <form [formGroup]="sortingForm">
            <div class="flight-list-sorting-container-inner">
              <div class="flight-list-date">
                <!-- <span>{{ 'result.Select' | translate }} {{isReturnFlightList() ? ('result.ReturnFlight' | translate) :
                  ('result.DepartureFlight' | translate)}}</span> -->
                  <span style="margin-right: 5px;"></span>
                  <div class="flight-list-sorting2" id="sortingContainer">
                    <div class="filter custom-selectbox" attr.data-track="FlightResultsSortBy"
                      (click)="sortingDropdown.toggle()">
                      <div class="select-input12">
                        <ng-select #sortingDropdown appendTo="#sortingContainer" dropdownPosition="bottom"
                          [searchable]="false" [clearable]="false" [closeOnSelect]="true"
                          [items]="sortOptions | translateOptions" bindLabel="value" bindValue="id"
                          (change)="sortOptionChanged($event)" formControlName="sortingDropdown"
                          [(ngModel)]="currSortOptionId">
                          <ng-template ng-header-tmp>
                            <div class="selectox-header">
                              <span>{{'result.Sortby' | translate}}</span>
                              <span class="selectBox-remove" (click)="sortingDropdown.toggle()"><span
                                  class="material-icons">clear</span></span>
                            </div>
                          </ng-template>
                          <span>
                              <i class="fa fa-chevron-down" style="
                              position: relative;
                              top: -5px;
                          "></i>
                          </span>
                        </ng-select>
                        <div class="select-overlay"></div>
                      </div>
                      <div class="field-value custom-selectbox-value" attr.data-track="FlightResultsSortBy">
                        {{this.sortValue | translate}}</div>
                      <span class="control-icon icon-triangle"
                        attr.data-track="FlightResultsSortBy"></span>
                    </div>
                  </div>
              </div>
              <div *ngIf="this.flightSearchResponse && (this.searchService.policyObject || this.searchService.internationalFlightPolicyObject)" class="policyReview" (click)="openPolicyModal(policyModal)">
                {{'setting.ReviewYourPolicy' | translate}}
              </div>  
            </div>
          </form>
          <div *ngIf="this.flightSearchResponse && (this.searchService.policyObject || this.searchService.internationalFlightPolicyObject)" class="policyReview1" (click)="openPolicyModal(policyModal)">
              {{'setting.ReviewYourPolicy' | translate}}
          </div> 
          <div  class="allStars" *ngIf="TripCheck()">  
            <div *ngFor="let item of this.classArray;let i=index" class="starsImg position-relative">
              <div  class="toolTip_Stars" style="width:auto;padding-left: 20px;padding-right: 20px;" [ngStyle]="getPositionCirrectOFTooltip(i)"> <span class="tooltiparrow" [ngStyle]="getPositionCirrectOFTooltipArrow(i)"></span><span class="leftSidePAnel"> <div [ngStyle]="getPaddingTooltipStyle(i)">  <img  src="{{starsSeats[i]}}"/></div>
                <div class="starsValue">{{this.starsArray[i] | translate}}</div></span><span class="rightSidepanel">{{ this.classDescArray[i] | translate}} </span></div>
            <div [ngStyle]="getPaddingStyle(i)">  <img  src="{{starsSeats[i]}}"/></div>
              <div class="starsValue">{{this.starsArray[i] | translate}}</div>
             
            </div>
         </div>
        </div>   
        <div *ngIf="this.flightSearchResponse" class="flight-result-list-container" >
          <div *uiScroll="let flight of flightsDatasource;let i = index">
            <div class="result-item" [style.minHeight] = "getItemMinHeight()">
              <flight-chart
                #result [sortingType]="getSortingType()" [algoType]="getSearchAlgoType()"
                [legType]="isReturnFlightList()?'RETURN':'OUTGOING'" [flight]="flight" [noOfPassengers]="noOfPassengers"
                [class]="class" [index]="i" (bookRequest)="onFlightSelection($event)">
              </flight-chart>
            </div>
        </div>
      </div>
        <div class="text-center"
          *ngIf="getShowMoreFlagStatus() && flightSearchResponse.flightsLists[0].flights.length > LESS_COUNT">
          <a class="change-link" href="javascript:void(0)" attr.data-track="SeeMoreFlightResults"
            (click)="showAllResults()">{{'result.SEEMOREOPTIONS' | translate}}</a>
        </div>
        <loader-dots class="loader-for-show-more" *ngIf="getLoadingForShowMoreFlag()"></loader-dots>
      </div>

      <div class="container" *ngIf="bookingWizardStep==1">
        <div class="selected-flight-wrapper">
          <flight-chart type="static" [flight]="selectedFlight" [noOfPassengers]="noOfPassengers" [class]="class"
            [index]="i" (bookRequest)="bookingWizard.buttonAction()"></flight-chart>
        </div>
      </div>

      <passenger-detail #passengerComponent *ngIf="bookingWizardStep==1" [noOfPassengers]="noOfPassengers"
        [isPassportRequired]="isPassportRequired" (goToPaymentRequest)="goToPaymentForm()"
        (enablePaymentRequest)="enablePayment($event)"></passenger-detail>
      <payment #paymentComponent *ngIf="bookingWizardStep==2" [noOfPassengers]="noOfPassengers"></payment>
    </div>
  </div>
</div>
<ng-template #content let-modal>
               <div class="modal-body" [ngStyle]="{'height':(!this.callComplete && this.searchService.searchPopMsg && this.searchService.searchPopMsg.length > 0) ? '250px':'150px'}">
                <div class="row" class="DetailsBoxLoader" style="position: relative;">
                  <app-loader
                     style="text-align:center;margin-left:  auto !important;margin-right: auto !important;"
                     [spinnerStyle]="true"></app-loader>
               </div>
               <div *ngIf="this.callComplete && this.searchService.trainFilter.length === 0" style="text-align: center;margin:auto;margin-top: 30px;">
                {{'result.FetchingmoreFlightspleasewait' | translate}}
               </div>
               <div *ngIf="!this.callComplete && this.searchService.trainFilter.length === 0" style="text-align: center;margin:auto;margin-top: 30px;">
                  {{'result.FetchingyourFlightspleasewait' | translate}}
                 </div>
                 <div *ngIf="!this.callComplete && this.searchService.searchPopMsg && this.searchService.searchPopMsg.length > 0" style="text-align: center;margin:auto;margin-top: 20px;">
                  <span class="popMsg"> {{this.searchService.currentMessage}}</span>
                 </div>
                 <div *ngIf="!this.callComplete && this.searchService.trainFilter.length > 0" style="text-align: center;margin:auto;margin-top: 20px;">
                    {{'result.FetchingyourTrainspleasewait' | translate}}
                   </div>
                   <div *ngIf="this.callComplete && this.searchService.trainFilter.length > 0" style="text-align: center;margin:auto;margin-top: 20px;">
                      {{'result.FetchingmoreTrainspleasewait' | translate}}
                     </div>
               </div>
</ng-template>

<ng-template #policyModal let-modal>
    <div class="table-view">
        <div class="table-cell-view">
            <div class="modal-dialog modal-dialog-md" role="document">
                <div class="modal-content">
                    <div *ngIf="this.disabled" class="approval_request_diaglog_bg_clickhandler"></div>
                    <div class="modal-header">
                        <span>
                            <img class="footerimage" [src]="this.searchService.footerLogo">
                          </span>
                        <button *ngIf="!this.disabled" type="button" class="close" data-dismiss="modal"
                            (click)="onCancel()">
                            <i class="material-icons">close</i>
                        </button>
                    </div>
                    <div class="modal-body" style="text-align: left;">
                        <div class="tab">

                            <div *ngIf="this.searchService.policyObject && this.searchService.internationalFlightPolicyObject" class="tab-list top-strip" style="">
                      
                              <ul style="overflow: hidden !important;">
                                <li class="{{ viewMode1 == 'tab11' ? 'tab-list-item1':'tab-list-item'}}" [class.active]="viewMode1 == 'tab11'"
                                  rel="tab11" (click)="this.viewMode1='tab11'">{{ 'policy.DOMESTIC' | translate}} </li>
                                <li class="{{ viewMode1 == 'tab12' ? 'tab-list-item1':'tab-list-item'}}" [class.active]="viewMode1 == 'tab12'"
                                  rel="tab14" (click)="this.viewMode1='tab12'">{{ 'policy.INTERNATIONAL' | translate}} </li>
                               
                              </ul>
                              </div>

                            <ul style="display: inline-block;" *ngIf="(this.searchService.policyObject && !this.searchService.internationalFlightPolicyObject) || (this.searchService.policyObject && this.searchService.internationalFlightPolicyObject && viewMode1 == 'tab11')">
                              <li *ngIf="this.searchService.policyObject.maxFlightClassAllowed" style="font-size: 18px;display: flex;">
                                  <div class="bulletPoint"> . </div>  <div> {{'setting.Maximumclassallowance' | translate}}  {{ getClaaName(this.searchService.policyObject.maxFlightClassAllowed) | translate}}</div>
                              </li>
                              <li *ngIf="this.searchService.policyObject.upperClassReason==='BASED_ON_FLIGHT_DURATION'"  style="font-size: 18px;display: flex;">
                                  <div class="bulletPoint"> . </div>  <div>    {{'setting.Maximumclassallowedonlyifflightdurationismorethan' | translate}} {{this.searchService.policyObject.flightDurationForUpperClass}} hours</div>
                              </li>
                              <li *ngIf="!this.searchService.policyObject.considerBasicEconomy"  style="font-size: 18px;display: flex;">
                                  <div class="bulletPoint"> . </div>  <div>  {{'setting.Basefaresarerestricted' | translate}}</div>
                              </li>
                            
                              <li *ngIf="this.searchService.policyObject.pricePolicyList && this.searchService.policyObject.pricePolicyList.length > 0"  style="font-size: 18px;">
                                  <div *ngFor="let item of this.searchService.policyObject.pricePolicyList">
                                      <div *ngIf="item.type==='markup'" style="display: flex;">
                                      <div class="bulletPoint"> . </div>  <div>{{'setting.Absolutepricelimitonewayairfare' | translate}} {{getCurrencySymbol(this.currency)}}{{item.benchMark}}</div>
                                      </div>
                                      <div *ngIf="item.type==='markup_over_cheapest'" style="display: flex;">
                                              <div class="bulletPoint"> . </div>  <div>{{'setting.Relativepricelimitisupto' | translate}} {{getCurrencySymbol(this.currency)}}{{item.benchMark}} {{'setting.morefromthelowestlogicalfareinaparticularcategory' | translate}}</div>
                                              </div>
                                              <div *ngIf="item.type==='percent_markup_over_cheapest'" style="display: flex;">
                                                      <div class="bulletPoint"> . </div>  <div>{{'setting.Relativepricelimitisupto' | translate}} {{item.benchMark}}% {{'setting.ofthelowestlogicalfareinaparticularcategory' | translate}}</div>
                                                      </div>
                                  </div>
                                     
                                </li>
                            </ul>
                            <ul  *ngIf="!this.searchService.policyObject && this.searchService.internationalFlightPolicyObject || (this.searchService.policyObject && this.searchService.internationalFlightPolicyObject && viewMode1 == 'tab12')"  style="font-size: 18px;display: inline-block">
                               <li *ngIf="this.searchService.internationalFlightPolicyObject.maxFlightClassAllowed" style="display: flex;display: flex;">
                                  <div class="bulletPoint"> . </div>  <div> {{'setting.Maximumclassallowance' | translate}}  {{ getClaaName(this.searchService.internationalFlightPolicyObject.maxFlightClassAllowed) | translate}}</div>
                               </li>
                               <li  style="font-size: 18px;display: flex;" *ngIf="this.searchService.internationalFlightPolicyObject.upperClassReason==='BASED_ON_FLIGHT_DURATION'">
                                  <div class="bulletPoint"> . </div>  <div>  {{'setting.Maximumclassallowedonlyifflightdurationismorethan' | translate}} {{this.searchService.internationalFlightPolicyObject.flightDurationForUpperClass}} hours</div>
                               </li>
                               <li   style="font-size: 18px;display: flex;"*ngIf="!this.searchService.internationalFlightPolicyObject.considerBasicEconomy">
                                  <div class="bulletPoint"> . </div>  <div>  {{'setting.Basefaresarerestricted' | translate}}</div>
                               </li>
                               
                               <li *ngIf="this.searchService.internationalFlightPolicyObject.pricePolicyList && this.searchService.internationalFlightPolicyObject.pricePolicyList.length > 0"  style="font-size: 18px;">
                                  <div *ngFor="let item of this.searchService.internationalFlightPolicyObject.pricePolicyList">
                                      <div *ngIf="item.type==='markup'" style="display: flex;">
                                      <div class="bulletPoint"> . </div>  <div>{{'setting.Absolutepricelimitonewayairfare' | translate}} {{getCurrencySymbol(this.currency)}}{{item.benchMark}}</div>
                                      </div>
                                      <div *ngIf="item.type==='markup_over_cheapest'" style="display: flex;">
                                              <div class="bulletPoint"> . </div>  <div>{{'setting.Relativepricelimitisupto' | translate}} {{getCurrencySymbol(this.currency)}}{{item.benchMark}} {{'setting.morefromthelowestlogicalfareinaparticularcategory' | translate}}</div>
                                              </div>
                                              <div *ngIf="item.type==='percent_markup_over_cheapest'" style="display: flex;">
                                                      <div class="bulletPoint"> . </div>  <div>{{'setting.Relativepricelimitisupto' | translate}} {{item.benchMark}}% {{'setting.ofthelowestlogicalfareinaparticularcategory' | translate}}</div>
                                                      </div>
                                  </div>
                                     
                                </li>
                               
                             </ul>
                         </div>    
                       
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<app-navigation></app-navigation>