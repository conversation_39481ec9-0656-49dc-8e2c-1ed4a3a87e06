import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { FlightSearchQueryParam } from '../entity/flight-search-query-param';
import { SearchResultComponent } from './search-result.component';

@Injectable({
  providedIn: 'root'
})
export class CanDeactivateGuard  {
  canDeactivate(
    component: SearchResultComponent,
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    let queryParams = next.queryParams;
    if (queryParams && queryParams && Object.keys(queryParams).length > 0) {

      let flightSearchQueryParam: FlightSearchQueryParam = deserialize(JSON.parse(decodeURIComponent(queryParams.query)), FlightSearchQueryParam);
      let bookingWizardStep = Number(queryParams.step);
      return true;
    }
  }
}
