@import "../../variables.scss";

:host {
    width: 100vw;
}
.tab-list-item {
    font-family: "apercu-b" !important;
    display: inline-block;
    text-align: left;
    color: gray !important;
    margin-right: 25px;
    margin-left: 25px;
    text-transform: uppercase;
    margin-bottom: 0px;
    padding-bottom: 0px;
    font-size: 15px;
    width: auto !important;
    position: relative;
    cursor: pointer;
  }
  .top-strip {
    background: transparent !important;
    float: left;
    width: 100%;
    height: 43px;
}
  .tab-list-item1.active {
    font-family: "apercu-b" !important;
    position: relative;
    text-transform: uppercase;
    font-size: 15px;
    text-decoration: underline;
    text-align: left;
    color: var(--dark-bg-color) !important;
    width: auto !important;
    display: inline-block;
    margin-right: 25px !important;
    margin-left: 25px !important;
    margin-bottom: 0px;
    padding-bottom: 0px;
  
  }
:host ::ng-deep {
    .cdk-virtual-scroll-content-wrapper{

  position: relative !important;
    }
    cdk-virtual-scroll-viewport {
        display: contents !important;
        position: relative;
        /* overflow: auto; */
        contain: block-size !important;
        transform: translateZ(0);
        will-change: scroll-position;
        -webkit-overflow-scrolling: touch;
    }
}
.bulletPoint{
    font-weight: 800;
    font-size: 28px;
    position: relative;
    top: -8px;
    margin-right: 5px;
  }
.policyReview{
    cursor: pointer;
    width: fit-content;
    display: block;
    color: var(--hyperlink-color);
    padding-left: 5px;
    padding-top: 5px;
}
.policyReview1{
    cursor: pointer;
    width: fit-content;
    display: none;
    color: var(--hyperlink-color);
}
.popMsg{
    background: #EDFAFC;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 20px;
}
.modal-body{
    padding:20px; 
}
.flight-result-list-container{
    padding-top: 46px;
}
.result-item{
    min-height: 100px;
}
.search-results {
  min-width: 100%;
  float: left;
    overflow: scroll;
}
.viewport {
    max-height: 1120px;
    overflow-y: auto;
    float: left;
    min-width: 100%;
}
.main-wrapper {
    padding-bottom: 80px;
}

.primary-button {
    padding: 19px 35px;
}

.icon-right-arrow {
    font-size: 1rem;
}

.select-header {
    background: $themeColor2;
}

.custom-selectbox {
    cursor: pointer;
    position: relative;
    display: inline-block;
    padding-right: 15px;
    margin-right: 5px;
}

.custom-selectbox .field-value {
    color: #AEAEAE;
}

.custom-selectbox-value {
    font-size: 16px !important;
    letter-spacing: -0.71px !important;
    color: #413E3B !important;
    line-height: 16px !important;
}

.custom-selectbox .control-icon {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.filter-select-container .custom-selectbox-value {
    max-width: 120px;
    text-overflow: ellipsis;
    overflow: hidden;
}

.control-icon {
    font-size: 4px;
    margin-left: 10px;
    color: var(--hyperlink-color) !important;
}

.add-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    width: 70px;
    border-radius: 6px;
    background-color: #fff;
    font-size: 1.5714rem;
    color: $accent-color;
    cursor: pointer;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
}

.switch {
    position: absolute;
    height: 35px;
    width: 35px;
    border-radius: 50%;
    right: -17.5px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 16px;
    z-index: 1; // cursor: pointer;
    border: 2px solid $border-color;
}

.text-input-wrapper .text-input .icon {
    left: 30px;
}

.text-input-wrapper .text-input .icon+input {
    padding-left: 66px;
}

.type-selector {
    margin-bottom: 16px;

    .radio-input {
        margin-right: 40px;
    }

    .radio-input [type="radio"]:checked+label {
        color: $accent-color;
    }
}

.remove {
    position: absolute;
    bottom: 100%;
    right: 0;
    font-size: 1rem;
    margin-bottom: 5px;
    color: $primary-color;
    cursor: pointer;
    margin-right: 4px;
}

.time-slider-wrapper {
    padding: 5px 100px;
    height: 100%;
    position: relative;

    .separator {
        height: 5px;
        position: absolute;
        width: 100%;
        left: 0;
        top: calc(50% - 3px);
        background: $border-dark-color;
    }

    .time-slider {
        margin: 0 22px;
    }

    .source {
        position: absolute;
        left: 0;
        white-space: nowrap;
        font-weight: 600;
        margin: 10px 5px;
        bottom: 50%;
    }

    .destination {
        position: absolute;
        right: 0;
        white-space: nowrap;
        font-weight: 600;
        margin: 10px 5px;
        top: 50%;
    }

    .time-values {
        display: flex;
        justify-content: space-between;
        margin: 20px 0;
        font-size: 12px;

        .value:before {
            content: "";
            display: inline-block;
            height: 8px;
            width: 8px;
            border-radius: 50%;
            background: #ccc;
            margin-right: 5px;
        }
    }
}

.time-slider-container {
    background: $background-light;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    padding: 0 32px;
}

.flight-box-wrapper {
    padding: 24px 32px;
    background: $background-dark;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    width: 100%;
    overflow-y: auto;

    .flight-box-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    &.fixed-top {
        box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
        padding: 16px;

        .fixed-align {
            padding: 0 32px;
        }
    }
}

.flight-charts-container {
    float: left;
    width: 100%;
    /*box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);*/
    margin-bottom: 50px;
    padding: 0 32px 16px 32px;
    /*background: $background-dark;*/
}

.fixed-bottom {
    padding: 8px;
    background: #fff;
    box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
    display: flex;
    justify-content: center;

    .primary-button {
        padding: 8px 24px;
    }
}

.no-flight-found-container {
    float: left;
    width: 100%;
    align-items: center;
    display: flex;
    height: 300px;
    justify-content: center;
    flex-direction: column;
    /*box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);*/
    background: $background-light;

    .text {
        margin-top: 20px;
    }
}

flight-box {
    flex: 1;
    display: flex;
    justify-content: space-between;
}

.selected-flight-wrapper {
    padding: 24px 32px;
    background: $background-light;
    box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
    margin-bottom: 16px;
}

.flight-list-sorting-container {
    float: left;
    width: auto;
}

.flight-list-sorting-container {
    float: left;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 21px 0 11px;
}
.tab{
    background: transparent !important;
}
.flight-list-date {
    font-size: 16px;
    letter-spacing: -0.71px;
    line-height: 16px;
}

.flight-list-sorting2 {
    position: relative;
    top: 20px;
    display: contents; // change my InCraftiv
}
.leftSidePAnel{
    min-width: 75px;
    max-width: 75px;
    margin-top: auto;
    margin-bottom: auto;
}

.DetailsBoxLoader{
    
    display: flex;
    justify-content: center;
    align-items: center;  
}
.tooltiparrow{
    content: "";
    display: block;
    position: absolute;
    border-style: solid;
    border-color: black;
    border-width: 0.1px 0 0 0.1px;
    width: 18px;
    height: 18px;
    top: -9px;
    left: 40px;
    background: inherit;
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }
.rightSidepanel{
    width: auto;
    color: #413E3B;
    font-family: var(--globalFontfamilyr);
    font-size: 12px;
    text-align: left;

}
.flight-list-sorting2 select {
    background-color: transparent;
    font-size: 16px;
    letter-spacing: -0.71px;
    line-height: 16px;
    color: #413E3B;
    padding-right: 33px;
    border: none;
    box-shadow: none;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
   
    background-repeat: no-repeat;
    background-position: right 5px center;
}

.flight-list-sorting-container {
    float: left;
    width: 100%;
    padding: 0 11px;
}

.flight-list-sorting-container form {
    float: left;
    width: 100%;
}

.flight-list-sorting-container-inner {
    //display: flex;
    justify-content: space-between;
    align-items: center;
    float: left;
    width: 100%;
    padding: 10px 0;
    margin-bottom: 10px;
}

.change-link {
    font-size: 16px;
    line-height: 12px;
    color: var(--button-font-color);
}

.loader-for-show-more {
    text-align: center;
    width: 100%;
    display: inline-block;
}

@media (max-width: 767.98px) {
    .policyReview{
        cursor: pointer;
        width: fit-content;
        white-space: nowrap;
        display: none;
        color: var(--hyperlink-color);
    }
    .tab-list-item {
        font-family: "apercu-b" !important;
        display: inline-block;
        text-align: left;
        color: gray !important;
        margin-right: 8px;
        margin-left: 8px;
        text-transform: uppercase;
        margin-bottom: 0px;
        padding-bottom: 0px;
        font-size: 15px;
        width: auto !important;
        position: relative;
        cursor: pointer;
      }
      .top-strip {
        background: transparent !important;
        float: left;
        width: 100%;
        height: 43px;
    }
      .tab-list-item1.active {
        font-family: "apercu-b" !important;
        position: relative;
        text-transform: uppercase;
        font-size: 15px;
        text-decoration: underline;
        text-align: left;
        color: var(--dark-bg-color) !important;
        width: auto !important;
        display: inline-block;
        margin-right: 8px !important;
        margin-left: 8px !important;
        margin-bottom: 0px;
        padding-bottom: 0px;
      
      }
    .policyReview1{
        cursor: pointer;
        white-space: nowrap;
        width: fit-content;
        display: block;
        color: var(--hyperlink-color);
    }
    .custom-selectbox-value {
        font-size: 14px !important;
        letter-spacing: -0.71px !important;
        color: #413E3B !important;
        line-height: 16px !important;
    }

    .flight-charts-container {
        margin: 0;
        padding: 0 8px 16px 8px;
    }

    .flight-list-date {
        font-size: 14px;
        letter-spacing: -0.62px;
        line-height: 16px;
    }

    .flight-list-sorting-container {
        padding: 0 5px;
    }

    .flight-list-sorting-container-inner {
        padding: 15px 0;
    }

    .change-link {
        font-size: 9px;
        line-height: 12px;
        color: var(--button-font-color);
    }
}


@media (max-width: 575.98px) {
    .selected-flight-wrapper {
        padding: 16px;
    }

    .flight-charts-container {
        /*margin: 0 8px;*/
        /*padding: 16px;*/
    }

    .flight-box-wrapper {
        padding: 8px;
        justify-content: center;

        &.fixed-top {
            .fixed-align {
                padding: 0;
            }
        }

        .bottom-flight {
            flex: 1;
            justify-content: space-between;
        }
    }

    .fixed-bottom {
        padding: 16px;

        .primary-button {
            width: 100%;
            padding: 18px 36px;
        }
    }

    .main-wrapper {
        padding-bottom: 110px;
    }
}

@media (max-width: 359px) {
    .flight-list-date {
        font-size: 12px;
    }
}
.allStars{
    display: flex;
    justify-content: space-around;
    margin-right: -34px;
    margin-bottom: 10px;
}
.starsImg{
 
    width: 118px;
}
.starsImg>span>img{
    width:13px;
}

@media(max-width:1350px){
    .toolTip_Stars{
        text-align: center;
        position: absolute;
        top: 35px;
        font-size: 13px;
        background-color: white;
        border: 0.1px solid black;
        padding: 10px 5px;
      //  z-index: 9;
        min-width: 350px;max-width: 350px;
        display: none;
        border-radius: 10px;
    }
}

@media (max-width:1200px){
    .allStars{
        display: none;
    }
}
.toolTip_Stars{
    text-align: center;
    position: absolute;
    top: 55px;
    z-index: 9;
    font-size: 13px;
    background-color: white;
    border: 0.1px solid black;
    padding: 10px 5px;
  
  
  //  z-index: 9;
    min-width: 300px;max-width: 300px;
    display: none;
    border-radius: 10px;
}
.starsValue{
    font-family: var(--globalFontfamilyr);font-weight: bold;;
}
.downArrow{
    position: absolute;
    top: 12px;
    transform: rotate(180deg);
    left: 20px;
    display: none;
}
.starsImg:hover  .toolTip_Stars {
    display: flex;
   // z-index: 9;
}
.starsImg:hover .downArrow{
    display: block;
     z-index: 9;
}

virtual-scroller {
    width: 100%;
    height: 800px;
}
