import { Component, OnInit, ElementRef, <PERSON>Child, ViewChildren, HostListener,ChangeDetectionStrategy, AfterViewInit, AfterContentInit, AfterViewChecked, TemplateRef } from '@angular/core';
import { trigger,state,style,animate,transition} from '@angular/animations';
import { SearchService } from '../search.service';
import { FlightSearchResponse } from '../entity/flight-search-response';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { FlightSearchQueryParam } from '../entity/flight-search-query-param';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { SearchResultService } from '../search-result.service';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { SearchActionType } from '../enum/search-action.type';
import { FlightChartComponent } from './flight-chart/flight-chart.component';
import { FlightClassType } from 'src/app/enum/flight-class.type';
import { AbstractControl, FormArray, UntypedFormBuilder, UntypedFormGroup, Validators, FormControl, Form, ValidationErrors } from '@angular/forms';
import { PassengerDetailComponent } from '../passenger-detail/passenger-detail.component';
import { FlightResult } from '../entity/flight-result';
import { DeviceDetailsService } from '../device-details.service';
import { FilterService } from '../filter.service';
import { Constants } from '../util/constants';
import { ChangeDetectorRef } from '@angular/core';
import { SearchComponent } from '../search/search.component';
import { TravelersInfo } from '../entity/travelers-info';
import { EmailQuoteOptionsService } from '../email-quote-options.service';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { searchModalBasic } from '../search-modal/search-modal.component';
import { DateUtils } from '../util/date-utils';
import { FlightSearchRequest } from '../entity/flight-search-request';
import { JourneyDate } from '../entity/journey-date';
import { IntervalType } from '../enum/interval.type';
import { DatePipe, PlatformLocation } from '@angular/common';
import { _ } from 'src/app/util/title';
import { TranslateService } from "@ngx-translate/core";
import { UserAccountService } from '../user-account.service';
import { serialize } from '../util/ta-json/src/methods/serialize';
import { ConnectionService } from 'ng-connection-service';
import { CommonUtils } from '../util/common-utils';
import { PopupComponent } from '../popup/popup.component';
import { NavigationUtil } from '../util/navigation-util';
import { BookingService } from '../booking.service';
import { Title } from '@angular/platform-browser';
import { FlightLegSearch } from '../entity/flight-leg-search';
import { FlightHopResult } from '../entity/flight-hop-result';
import { FilterType } from '../enum/filter.type';
import { AddressType } from '../enum/address.type';
import { TooltipModule } from 'ng2-tooltip-directive';
import { Datasource, IDatasource } from 'ngx-ui-scroll';
import {CollectionViewer, DataSource} from '@angular/cdk/collections';
import { constants } from 'crypto';

@Component({
    selector: 'app-search-result',
    templateUrl: './search-result.component.html',
    styleUrls: ['./search-result.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [
        trigger('flyin', [
            state('in', style({ opacity: 1 })),
            transition('void => *', [
                style({ opacity: 0 }),
                animate(500)
            ])
        ])
    ],
    standalone: false
})
export class SearchResultComponent implements OnInit, AfterViewChecked {

  @ViewChildren('result') result: any;
  @ViewChild('content',{ static: false }) content!: TemplateRef<any>;
  @ViewChild('passengerComponent') passengerComponent: PassengerDetailComponent;
  @ViewChild('searchComponent') searchComponent: SearchComponent;
  // @ViewChild('scroll') scroller: VirtualScrollerComponent;
//  @ViewChild(SearchComponent) searchComponentFilter: SearchComponent;
  currSortOptionId = 'recommended';
 // sortValue = 'Recommended'
  sortingForm: UntypedFormGroup;
  currency='USD';
  noOfFlightLegsSubscription: Subscription;
  flightSearchResponseSubscription: Subscription;
  selectedFlightSubscription: Subscription;
  quickSearchSubscription: Subscription;
  detailSearchSubscription: Subscription;
  selectedFlightUnSelectionSubscription: Subscription;

  flightSearchRequestSubscription: Subscription;
  classOptions = [{ Name: 'policy.Notdefined', value: 'NOT_DEFINED' }, { Name: 'policy.EconomyBaseandStandardfares', value: 'ECONOMY' },
  { Name: 'policy.PremiumEconomyEnhancedandPremiumfares', value: 'PREMIUM_ECONOMY' }, { Name: 'policy.BusinessPremiumfares', value: 'BUSINESS' }];
  queryParmsSubscription: Subscription;
  noOfFlightLegs: number[] = [0];
  messageIndex=0;
  userDetails: TravelersInfo;
  private intervalId: any;
  selectedFlightsMap: any[] = [{ id: '', flight: '' }];
  viewMode1='tab11';
  isCollapse: boolean = true;
  sortOptions = Constants.SORTFLIGHT_OPTIONS;
  noOfPassengers: number = 1;
  class: string = FlightClassType.ECONOMY;
  flightAmount: number;
  isPassportRequired: boolean;
  comboOutGoingSelectedFlight: FlightResult = new FlightResult();
  selectComboOutGoingSelectedFlight: FlightResult = new FlightResult();

  bookingWizardStep: number = 0;
  isMobile: boolean;
  currScreenWidth = 1024;
  isPaymentEnabled: boolean;
  isBottomReached: boolean;
  flightSearchRequest: FlightSearchQueryParam;
  flightSearchResponse: FlightSearchResponse;
  orgFlightSearchResponse: FlightSearchResponse;
  callComplete: boolean = true;
  deviceSubscription: Subscription;
  selectedFlight: FlightResult;
  LESS_COUNT: number = 0;
  brandedFareCheckBox = false;
  connectionListener: Subscription;
  items = [];
  starsSeats = Constants.starsSeatArray;
  starsArray=Constants.CLASS_starsArray;
  classArray = Constants.CLASS_NAMES_ARRAY;
  classDescArray = Constants.CLASS_NAMES_DESC_ARRAY;
  filterOffset  = -1;
  filterHeight = -1;
  sortContainerOffset = -1;
  sortContainerHeight = -1;
  initialFlightResultListPaddingTop = -1;
  
  public flightsDatasource = new Datasource({
    get: (index, count, success) => {
      let data = [];
      let max = this.flightSearchResponse.flightsLists[0].flights.length;
      if (this.filterService.showLessFlag){
        max = this.LESS_COUNT;
      }
      const lastElementIndex = Math.min(index + count, max);
      if (index < lastElementIndex) {
        data = this.flightSearchResponse.flightsLists[0].flights.slice(index, lastElementIndex);
      }
      success(data);
    },
    settings:{
      startIndex: 0,
      bufferSize: 50,
      padding: 10,
      windowViewport: true
    }
  });

  constructor(
    public searchService: SearchService,
    private activatedRoute: ActivatedRoute,
    private fb: UntypedFormBuilder,
    private gallopLocalStorage: GallopLocalStorageService,
    private searchResultService: SearchResultService,
    public progressBar: NgxUiLoaderService,
    public router: Router,
    private deviceDetailsService: DeviceDetailsService,
    private filterService: FilterService,
    private cdRef: ChangeDetectorRef,
    private modalService: BsModalService,
    public translateService: TranslateService,
    private titleService: Title,
    private userAccountInfoService: UserAccountService,
    private connectionService: ConnectionService,
    private bookingService: BookingService,
    private emailBookingFlowService: EmailQuoteOptionsService,
    private cd:ChangeDetectorRef,
    location1: PlatformLocation) {
    location1.onPopState(() => {
      this.searchService.backClick = true;
     // this.searchService.searchHeaderCliked = true;
     if (this.userAccountInfoService.showRadio) {
      this.searchService.searchHeaderCliked =false;
      }else {
        this.searchService.searchHeaderCliked =true;
      }
      if(this.bsModalRef){
        this.bsModalRef.hide();
      }
      this.unsubscribeSearch();
      if (!this.callComplete) {
        this.bsModalRef1.hide();
        this.progressBar.stop(SearchActionType.DETAIL);
      }
    });
    this.createForm();
  }
  public getItemMinHeight() {
    if (this.currScreenWidth > 768) {
      return '246px';
    } else {
      return '150px';
    }
  }
  // >990: 228, <688: 413, >688: 398
  sortValue =  this.translateService.instant('ngOption.Recommended')
  @HostListener('window:scroll')
  onScroll() {
    this.initializeVarsForStickyNess();
    if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight) {
      this.isBottomReached = true;
    } else {
      this.isBottomReached = false;
    }
    if (this.filterOffset !== -1) {
      if (window.scrollY > this.filterOffset) {
        $('.filter-container').css('position', 'fixed');
        $('.filter-container').css('top', '0');
        $('.flight-list-sorting-container').css('position', 'fixed');
        $('.flight-list-sorting-container').css('top', this.filterHeight + 'px');
     
        $('.flight-list-sorting-container').css('boxShadow', '0 0 10px 0 rgba(0, 0, 0, 0.14)');
        $('.flight-result-list-container').css('paddingTop', (this.initialFlightResultListPaddingTop + this.filterHeight) + 'px');
      } else {
        $('.filter-container').css('position', 'relative');
        $('.filter-container').css('top', '0');
        $('.flight-list-sorting-container').css('top', '0');
        $('.flight-list-sorting-container').css('position', 'relative');
       
       
        $('.flight-list-sorting-container').css('boxShadow', 'none');
        $('.flight-result-list-container').css('paddingTop', (this.initialFlightResultListPaddingTop) + 'px');
        this.filterOffset = -1;
      }
    }
  }

  initializeVarsForStickyNess(){
    if (this.filterOffset === -1 && $('.filter-container').offset()) {
      this.filterOffset = $('.filter-container').offset().top;
      this.filterHeight = $('.filter-container').height();
      this.sortContainerOffset = $('.flight-list-sorting-container').offset().top;
      this.sortContainerHeight = $('.flight-list-sorting-container').outerHeight();
      this.initialFlightResultListPaddingTop =  parseInt( $('.flight-result-list-container').css('paddingTop').replace('px', ''), 10);
      const filterWidth = $('.filter-container').width();
      const sortContainerWidth = $('.flight-list-sorting-container').outerWidth();

      $('.filter-container').css('width', filterWidth);
      $('.flight-list-sorting-container').css('width', sortContainerWidth);
      $('.filter-container').css('zIndex', '10');
      $('.flight-list-sorting-container').css('zIndex', '9');
      $('.flight-list-sorting-container').css('backgroundColor', 'var(--light-bg-color)');
    }
  }
  ngOnInit() {
    //this.titleService.setTitle('');
    this.bookingService.pageMode ='';
    if(this.searchService.comingFromSelectionPAge){
      this.bookingService.bookingData=null;
      this.searchService.multiflightQuery.pop();
      this.searchService.seatForMultipleBooking=[];
      let seatArray = JSON.parse(this.gallopLocalStorage.getItem("selectedSeat"));
      if(seatArray && seatArray.length > 0){
        //  seatArray.pop();
          this.gallopLocalStorage.removeItem("selectedSeat");
        }
      

    }else{
      if(this.searchService.searchPopMsg && this.searchService.searchPopMsg.length > 0){
        this.searchService.currentMessage = this.searchService.searchPopMsg[0];
        this.showNextMessage()
      }
    }
    if(this.searchService.multiTripBooking && this.searchService.comingFromSelectionPAge && !this.searchService.multiTripFlightDelete){
      let selectFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"));
    
      
      if(selectFlight && selectFlight.length > 0){
        selectFlight.pop();
        this.gallopLocalStorage.setItem("selectedFlight", JSON.stringify(selectFlight));
      }
      
      
      this.searchService.comingFromSelectionPAge =false;
    }
    this.titleService.setTitle(this.translateService.instant('search.FlightResults'));
    if(this.searchService.filterReset){
      this.searchService.nonComboSelectedFlight.pop();
    }
    this.bookingService.responseData = undefined;
    
    this.subscribeSearchServiceEvents();
    this.initializeQueryParamsfromRoute();
   this.subscribeSearchResultServiceEvents();
    
   
    this.deviceSubscription = this.deviceDetailsService.isMobile1().subscribe(isMobile => {
      this.isMobile = isMobile;
      this.searchService.isMobile = isMobile;
    });
    this.deviceSubscription = this.deviceDetailsService.currScreenWidth$.subscribe(screenWidth => {
      this.currScreenWidth = screenWidth;
    });

    this.searchResultService.selectedFlight$.subscribe(flight => {
      this.selectedFlight = flight;
    });
    this.orgFlightSearchResponse = this.filterService.originalFlightSearchResponse;
    NavigationUtil.setCurrentNavigationMenu(NavigationUtil.NAVIGATION_MENU_RESULTS);
  }
  createForm(): void {
    this.sortingForm = this.fb.group({
      sortingDropdown: ['recommended']
    });
  }
  getPositionCirrectOFTooltip(index){
   if(index >2){
     return {'left':'-100px'}
   }else{
    return {'left':'-30px'} 
   }
  }
  getPositionCirrectOFTooltipArrow(index){
    if(index ===3){
      return {'left':'40%'}
    }if(index ===2){
      return {'left':'55px'}
    }else{
     return {'left':'40px'} 
    }
  }
  getPaddingStyle(index){
    if(index >0){
      return {'padding-left':'15px'}
    }else{
      return {'padding-left':'5px'}
    }
  }
  getPaddingTooltipStyle(index){
    if(index >0){
      return {'padding-left':'5px'}
    }else{
      return {'padding-left':'5px'}
    }
  }
  ngAfterViewChecked() {
    this.cdRef.detectChanges();
  }
  // currentSortOptionId = 'recommended';
  getSortingType() {
    return this.searchService.currentSortOptionId
  }
  getSearchAlgoType() {
    return this.flightSearchRequest.classTypeSwitch.toString();
  }
  sortOptionChanged(sortOption,value?) {
    if (!sortOption) return;
    this.searchService.changeClick =false;
    this.searchService.currentSortOptionId = sortOption.id;
    if (sortOption.id) {
      this.sortValue = sortOption.value ? sortOption.value : this.translateService.instant('ngOption.Recommended');
    }
  //  if (this.searchService.filterReset) {
    //  this.searchService.applySorting(this.filterService.originalFlightSearchResponse);
   // } else {
     //if(!value){
    //  this.searchService.applySorting(this.flightSearchResponse);
    // }
   // }
   let airline = (this.searchService.selectedFilter1 && this.searchService.selectedFilter1.length >0) ? [...this.searchService.selectedFilter1]:[];
   let srcAirports = (this.searchService.selectedSourceAirports && this.searchService.selectedSourceAirports.length >0) ? [...this.searchService.selectedSourceAirports]:[];
   let destAirports = (this.searchService.selectedDestAirports && this.searchService.selectedDestAirports.length >0) ? [...this.searchService.selectedDestAirports]:[];
   if(this.searchService.flightSearchQueryParam){
   let queryAsJSON = JSON.parse(JSON.stringify(this.searchService.flightSearchQueryParam));
   queryAsJSON.departureDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(queryAsJSON.departureDate)) + "T00:00:00.000Z";
   queryAsJSON.arrivalDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(queryAsJSON.arrivalDate)) + "T00:00:00.000Z";
   if (queryAsJSON.flights && queryAsJSON.flights.length > 0) {
     for (let multiFlightObj of queryAsJSON.flights) {
       multiFlightObj.departureDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(multiFlightObj.departureDate)) + "T00:00:00.000Z";
       multiFlightObj.arrivalDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(multiFlightObj.arrivalDate)) + "T00:00:00.000Z";
     }
   }
   if(!value && this.searchService.flightSearchQueryParam){
    if(this.searchService.backClick){
      this.searchService.backClick = false;
   }
    if (this.searchService.flightId.length === 0) {
   this.router.navigate(["flights"],
     {
       queryParams:
       {
         query: encodeURIComponent(JSON.stringify(queryAsJSON)),
         step: 0,
         index: this.searchService.seletedIndex,
         airline:this.filterService.filter_airlines,
         sourceAirport:this.filterService.filter_airports,
         destAirport:this.filterService.filter_destination_airports,
         noofstops:this.filterService.temp_filter_stops,
         sortid :this.searchService.currentSortOptionId,
         alliance:this.filterService.temp_filter_airlineAlliances,
         departureRange:JSON.stringify(this.filterService.temp_filter_departure_range),
         arrivalRange:JSON.stringify(this.filterService.temp_filter_arrival_range),
         durationRange : JSON.stringify(this.filterService.temp_filter_duration_range),
         priceRange:JSON.stringify(this.filterService.temp_filter_price_range),
         policy: this.filterService.temp_filter_policy,
         appliedFilter:this.filterService.appliedFilterList,
         resultFound: 1
       },
       replaceUrl: true
     }
   );
    }else{
      this.router.navigate(["flights"],
     {
       queryParams:
       {
         query: encodeURIComponent(JSON.stringify(queryAsJSON)),
         step: 0,
         flight: JSON.parse(JSON.stringify(this.searchService.flightId)),
         index: this.searchService.seletedIndex,
         airline:this.filterService.filter_airlines,
         sourceAirport:this.filterService.filter_airports,
         destAirport:this.filterService.filter_destination_airports,
         noofstops:this.filterService.temp_filter_stops,
         sortid :this.searchService.currentSortOptionId,
         alliance:this.filterService.temp_filter_airlineAlliances,
         departureRange:JSON.stringify(this.filterService.temp_filter_departure_range),
         arrivalRange:JSON.stringify(this.filterService.temp_filter_arrival_range),
         durationRange : JSON.stringify(this.filterService.temp_filter_duration_range),
         priceRange:JSON.stringify(this.filterService.temp_filter_price_range),
         policy: this.filterService.temp_filter_policy,
         appliedFilter:this.filterService.appliedFilterList,
         resultFound: 1
       },
       replaceUrl: true
     }
   );
    }
    }
  }
    let params = "?ua_action=FlightResultSortdByItem&ua_item=" + sortOption.id;
    this.searchService.letsTrack(params);
  }


  subscribeSearchServiceEvents() {

    this.flightSearchRequestSubscription = this.searchService.flightRequest$.subscribe((flightSearchRequest) => {
      this.flightSearchRequest = flightSearchRequest;
      if (this.flightSearchRequest != null) {
       // this.searchService.multiflightQuery.push(this.flightSearchRequest);
        this.gallopLocalStorage.setItem("flightSearchRequest", JSON.stringify(this.flightSearchRequest));
      }
    });

    this.noOfFlightLegsSubscription = this.searchService.noOfFlightLegs$.subscribe((num) => {
      // this.setFlightLegs(num);
    });
    if (this.searchService.orgFlightResponse && this.searchService.noAvailability && this.searchService.noAvailability.length > 0) {
      for (let flightID of this.searchService.noAvailability) {
        this.searchService.orgFlightResponse.flightsLists[0].flights = this.searchService.orgFlightResponse.flightsLists[0].flights.filter(item => {
          //          return  item.legs[0].flightHops[0].flightNumber !== flightID;
          return CommonUtils.getAllFlightNumbers(item.legs) !== flightID;
        });
        this.searchService.inPolicyFlight = this.searchService.inPolicyFlight.filter(item => {
          return CommonUtils.getAllFlightNumbers(item.legs) !== flightID;
        });
      }
      this.progressBar.stop(SearchActionType.DETAIL);
      this.searchService.searchType = SearchActionType.DETAIL;
      this.orgFlightSearchResponse = this.searchService.orgFlightResponse
      this.flightSearchResponse = this.searchService.orgFlightResponse
      if (!(this.searchService.orgFlightResponse && this.searchService.orgFlightResponse.flightsLists
        && this.searchService.orgFlightResponse.flightsLists.length > 0 && this.searchService.orgFlightResponse.flightsLists[0].flights
        && this.searchService.orgFlightResponse.flightsLists[0].flights.length > 0)) return;

      this.filterService.showLessFlag = true;
      let flightResponse = JSON.parse(JSON.stringify(this.searchService.orgFlightResponse));
      this.searchService.setFlightSearchResponse(flightResponse, true);
      // this.comboOutGoingSelectedFlight = undefined;
     // this.filterService.resetAllFilters();
    }
    this.flightSearchResponseSubscription = this.searchService.flightResponseSubject.subscribe((flightSearchResponse) => {
      //this.flightSearchResponse = flightSearchResponse;
      
      if(this.searchService.priceWithTravelCreditsZero){
        this.filterService.filter_airlines = this.searchService.selectedFilter1;
        if (this.filterService.appliedFilterList.indexOf(FilterType.AIRLINE) == -1) {
          this.filterService.appliedFilterList.push(FilterType.AIRLINE);
        }
        this.flightSearchResponse = this.filterService.filterByPreferredNoncomboAirlines(this.searchService.selectedFilter1, JSON.parse(JSON.stringify(flightSearchResponse)));
        
        if(this.flightSearchResponse && this.flightSearchResponse.flightsLists.length >0 && this.flightSearchResponse.flightsLists[0].flights.length===0){
          this.flightSearchResponse = flightSearchResponse;
          // this.items = this.flightSearchResponse.flightsLists[0].flights ;
         // if(this.filterService.appliedFilterList && this.filterService.appliedFilterList.length > 0){
          setTimeout(() => {
            if(this.searchService.seletedIndex===0){
              this.searchService.outgoingSelectedFlight = undefined;
             }
            this.filterService.showLessFlag =false;
            this.flightsDatasource.adapter.reload(0);
          }, 200);
      //  }
        }
      }else{
        this.flightSearchResponse = flightSearchResponse;
     
       
        //if(this.filterService.appliedFilterList && this.filterService.appliedFilterList.length > 0 || this.searchService.seletedIndex > 0){
          setTimeout(() => {
            this.filterService.showLessFlag =false;
            if(this.searchService.seletedIndex===0){
              this.searchService.outgoingSelectedFlight = undefined;
             }
            this.flightsDatasource.adapter.reload(0);
          }, 200);
      //  }
      }
      if(this.bsModalRef1){
        this.bsModalRef1.hide();
      }
      
   // this.filterService.broadcastResetFilters();
      this.setTravelerInfo(this.flightSearchRequest);
    });
  
    if (this.bookingService.priceChanngeFlight) {
      this.updatePrice();
    }
    this.selectedFlightUnSelectionSubscription = this.searchService.selectedFlightUnSelection$.subscribe((dummyData) => {
      if(!this.searchService.backClick){
     // this.currSortOptionId = this.searchService.currentSortOptionId;
    //  this.sortOptionChanged({ 'id': this.searchService.currentSortOptionId });
      }
     
    });

  }

  subscribeSearchResultServiceEvents() {
    this.selectedFlightSubscription = this.searchResultService.selectedFlight$.subscribe((flightResult) => {
      this.setSelectedFlightInfo(flightResult);

    });
  }
  openPolicyModal(modal){
    this.bsModalRef = this.modalService.show(modal);
  }
  public onCancel(): void {
    this.bsModalRef.hide();
  }
  updatePrice() {
    for (let item of this.searchService.orgFlightResponse.flightsLists[0].flights) {
      if (CommonUtils.getAllFlightNumbers(item.legs) === CommonUtils.getAllFlightNumbers(this.bookingService.priceChanngeFlight.legs)) {
        item.price = this.bookingService.priceChanngeFlight.price;
        item.discountedPrice = this.bookingService.priceChanngeFlight.discountedPrice;
        item.fareBreakup = this.bookingService.priceChanngeFlight.fareBreakup;
        item.legs = this.bookingService.priceChanngeFlight.legs;
        if (item.legs[0].flightHighlights.hasOwnProperty("withinPolicy")) {
          item.legs[0].flightHighlights.withinPolicy = this.bookingService.priceChanngeFlight.legs[0].flightHighlights.withinPolicy
        }
      }
    }
    this.searchService.priceWithTravelCreditsZero =false;
    let flightResponse = JSON.parse(JSON.stringify(this.searchService.orgFlightResponse));
    this.searchService.seletedIndex = 0;
    this.searchService.setFlightSearchResponse(flightResponse, true);
    let flightSearchQueryParam: FlightSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("flightSearchRequest")));
    this.searchService.multipleOutgoingSelectedFlight = [];
    this.searchService.tempFlight = [];
    this.searchService.flightId = [];
  }


  unsubscribeSearchServiceEvents() {
    if (this.noOfFlightLegsSubscription) {
      this.noOfFlightLegsSubscription.unsubscribe();
    }
    if (this.flightSearchResponseSubscription) {
      this.flightSearchResponseSubscription.unsubscribe();
    }
    if (this.flightSearchRequestSubscription) {
      this.flightSearchRequestSubscription.unsubscribe();
    }
  }

  unsubscribeSearchResultServiceEvents() {
    if (this.selectedFlightSubscription) {
      this.selectedFlightSubscription.unsubscribe();
    }
    if(this.flightSearchResponseSubscription){
      this.flightSearchResponseSubscription .unsubscribe();
    }
  }
  killSearchRequest(event) {
    this.unsubscribeSearch();
  }
  initializeQueryParamsfromRoute(): void {
    this.bookingService.priceChange = false;
    this.bookingService.selectedFlight1 = undefined;
    this.bookingService.beforeRevalidation = false;
    this.gallopLocalStorage.removeItem("selectedFlightInPolicy");
    this.queryParmsSubscription = this.activatedRoute.queryParamMap.subscribe((queryParams: Params) => {

      if (queryParams && queryParams.params && Object.keys(queryParams.params).length > 0) {

        if ((queryParams.params.resultFound) && (this.searchService.backClick && !this.bookingService.priceChanngeFlight)) {
        
          if (queryParams.params.flight && Number(queryParams.params.flight.length) > 0) {
            this.selectedFlights(Number(queryParams.params.index), queryParams.params.flight);
          } else if (this.searchService.seletedIndex > 0) {
            this.selectedFlights(Number(queryParams.params.index), queryParams.params.flight);
          }
       
        }
        if (queryParams.params.policy) {
          //if(queryParams.params.airline && queryParams.params.airline.length > 0){
            this.searchService.priceWithTravelCreditsZero =false;
            if(queryParams.params.airline && queryParams.params.airline.length > 0 && Array.isArray(queryParams.params.airline)){
          this.searchService.selectedFilter1 = queryParams.params.airline;
          this.filterService.filter_airlines = queryParams.params.airline;
          let findIndex = this.searchService.selectedFilter1.findIndex(item => item==='9F');
          if(findIndex > -1){
            this.searchService.trainFilter.push('9F');
          }
            }else{
              if(queryParams.params.airline && queryParams.params.airline.length > 0 ){
              this.searchService.selectedFilter1 = queryParams.params.airline.split(',');
              this.filterService.filter_airlines = queryParams.params.airline.split(',');
              }else if(!queryParams.params.airline || queryParams.params.airline==""){
                this.searchService.selectedFilter1 = [];
                this.filterService.filter_airlines = [];
              }
            }
            if(queryParams.params.sourceAirport && queryParams.params.sourceAirport.length > 0 && Array.isArray(queryParams.params.sourceAirport)){
              this.filterService.filter_airports= queryParams.params.sourceAirport;
            }else{
              if(queryParams.params.sourceAirport && queryParams.params.sourceAirport.length > 0){
                this.filterService.filter_airports= queryParams.params.sourceAirport.split(',');
              }
            }
            if(queryParams.params.destAirport && queryParams.params.destAirport.length > 0 && Array.isArray(queryParams.params.destAirport)){
              this.filterService.filter_destination_airports= queryParams.params.destAirport;
            }else{
              if(queryParams.params.destAirport && queryParams.params.destAirport.length > 0 ){
                this.filterService.filter_destination_airports= queryParams.params.destAirport.split(',');
            }
            }
          this.filterService.temp_filter_stops  = queryParams.params.noofstops;
          this.searchService.currentSortOptionId = queryParams.params.sortid;
          if(queryParams.params.alliance && queryParams.params.alliance.length > 0 && Array.isArray(queryParams.params.alliance)){
            this.filterService.temp_filter_airlineAlliances = queryParams.params.alliance;
          }else{
            if(queryParams.params.alliance && queryParams.params.alliance.length > 0){
            this.filterService.temp_filter_airlineAlliances = queryParams.params.alliance.split(',');
            }
          }
          if(queryParams.params.departureRange){
          this.filterService.temp_filter_departure_range =  JSON.parse(queryParams.params.departureRange);
          if(this.filterService.temp_filter_departure_range){
            this.filterService.temp_filter_departure_range.starts = new Date(this.filterService.temp_filter_departure_range.starts);
            this.filterService.temp_filter_departure_range.ends = new Date(this.filterService.temp_filter_departure_range.ends);
          }
        }
        if(queryParams.params.arrivalRange){
          this.filterService.temp_filter_arrival_range =  JSON.parse(queryParams.params.arrivalRange);
          if(this.filterService.temp_filter_arrival_range){
            this.filterService.temp_filter_arrival_range.starts = new Date(this.filterService.temp_filter_arrival_range.starts);
            this.filterService.temp_filter_arrival_range.ends = new Date(this.filterService.temp_filter_arrival_range.ends);
          }
        }
        if(queryParams.params.durationRange){
          this.filterService.temp_filter_duration_range =  JSON.parse(queryParams.params.durationRange);
          if(this.filterService.temp_filter_duration_range){
            this.filterService.temp_filter_duration_range.starts = this.filterService.temp_filter_duration_range.starts;
            this.filterService.temp_filter_duration_range.ends = this.filterService.temp_filter_duration_range.ends;
          }
        }
        if(queryParams.params.priceRange){
          this.filterService.temp_filter_price_range =  JSON.parse(queryParams.params.priceRange);
          if(this.filterService.temp_filter_price_range){
            this.filterService.temp_filter_price_range.starts = this.filterService.temp_filter_price_range.starts;
            this.filterService.temp_filter_price_range.ends = this.filterService.temp_filter_price_range.ends;
          }
        }
          this.filterService.temp_filter_policy = queryParams.params.policy;
          if(queryParams.params.appliedFilter && queryParams.params.appliedFilter.length > 0 && Array.isArray(queryParams.params.appliedFilter)){
            this.filterService.appliedFilterList = queryParams.params.appliedFilter;
          }else{
            if(queryParams.params.appliedFilter && queryParams.params.appliedFilter.length > 0){
            this.filterService.appliedFilterList = queryParams.params.appliedFilter.split(',');
            }
          }
          this.currSortOptionId = this.searchService.currentSortOptionId;
         this.sortOptionChanged({ 'id': this.searchService.currentSortOptionId },'change');
          let sortvaule = this.sortOptions.filter(item => item.id === this.searchService.currentSortOptionId);
           if(sortvaule){
            this.sortValue  = sortvaule[0].value;
            }
          
           // this.flightSearchResponse = undefined;
           
              this.filterService.broadcastGetFiltersResults();  
          
         
    
   }else{
    this.searchService.currentSortOptionId = 'recommended';
    this.currSortOptionId = 'recommended';
    this.sortOptionChanged({ 'id': this.searchService.currentSortOptionId },'change');
    let sortvaule = this.sortOptions.filter(item => item.id === this.searchService.currentSortOptionId);
     if(sortvaule){
      this.sortValue  = sortvaule[0].value;
      }
   }
        if (queryParams.params.resultFound && this.flightSearchResponse
          && this.flightSearchResponse.flightsLists && this.flightSearchResponse.flightsLists.length > 0) return;
        // if(queryParams.params.resultFound) return;
        let queryParam: FlightSearchQueryParam = deserialize(JSON.parse(decodeURIComponent(queryParams.params.query)), FlightSearchQueryParam);
        this.searchService.flightSearchQueryParam = queryParam;

        this.bookingWizardStep = Number(queryParams.params.step);

        if (this.bookingWizardStep === 0) {
          this.search();
        }
      }

    });
  }
  selectedFlights(index, flightId) {
    this.searchService.seletedIndex = 0;
    this.searchService.priceChange = undefined;
   // this.filterService.broadcastResetFiltersResults();  
    this.searchService.multipleOutgoingSelectedFlight = [];
   
    this.searchService.flight = [];
    this.searchService.flightId = [];
    this.searchService.seatSelectArray1 = [];
    this.searchService.nonComboSelectedFlight=[];
    this.searchService.paymentPage = false;
    if (index === 0) {
      if (this.bookingService.priceChanngeFlight) {
        this.updatePrice();
      }
      this.searchService.resetOutgoingSelectedFlight(0);
     
    }
    for (let i = 0; i < index; i++) {
      let flightId1;
      if (index === 1) {
        flightId1 = flightId;
        this.searchService.flightId.push(flightId)
      } else {
        flightId1 = flightId[i];
        this.searchService.flightId.push(flightId[i])
      }
      let flight = this.searchService.tempFlight.find(item => {
        return item.legs[0].legId === flightId1
      });
      if (this.bookingService.priceChanngeFlight) {
        if (flight.legs[0].legId === this.bookingService.priceChanngeFlight.legs[0].legId) {
          flight.price = this.bookingService.priceChanngeFlight.price;
          flight.discountedPrice = this.bookingService.priceChanngeFlight.discountedPrice;
          flight.fareBreakup = this.bookingService.priceChanngeFlight.fareBreakup;
        }
      }
      if(this.searchService.nonCombo){
      this.searchService.nonComboSelectedFlight[this.searchService.seletedIndex] = flight
      }
      this.searchService.seletedIndex = this.searchService.seletedIndex + 1;
      
      this.searchService.setOutgoingMultipleSelectedFlight(flight, this.searchService.seletedIndex);
      this.comboOutGoingSelectedFlight = this.searchService.multipleOutgoingSelectedFlight[0];
      this.comboOutGoingSelectedFlight.price = flight.price;
      this.comboOutGoingSelectedFlight.fareBreakup = flight.fareBreakup;
      this.comboOutGoingSelectedFlight.legs[this.searchService.seletedIndex - 1] = flight.legs[0];
      if(!this.searchService.nonCombo){
      this.searchService.comboOutGoingSelectedFlight = this.comboOutGoingSelectedFlight;
      }
    }
  }
  getClaaName(id){
    let findIndex = this.classOptions.findIndex(item => item.value ===id);
    if(findIndex > -1){
      return this.classOptions[findIndex].Name;
    }
  }
  search() {
    this.callComplete = false;
    this.userAccountInfoService.userFormOpen =-1;
  //  this.filterService.broadcastgetFilters();
    this.searchService.filterReset = false;
    this.searchService.searchCallComplete =false;
    this.searchService.nonComboSelectedFlightForForwardArrow=[];
    this.searchService.apiCallafterFlightSelection = false;
    this.searchService.priceChange = undefined
    this.searchService.seatSelectArray1 = [];
    this.searchService.selectedFilter1 =[];
    this.searchService.paymentPage = false;
    this.searchService.priceWithTravelCreditsZero =false;
    this.searchService.selectedFilter = [];
    this.searchService.selectedSourceAirports = [];
    this.searchService.comboOutGoingSelectedFlight =undefined;
    this.searchService.filtereSourceAirports = [];
    this.searchService.selectedLeg = [];
    this.searchService.selectedDestAirports = [];
    this.searchService.filteredDestAirports = [];
    this.searchService.nonComboSelectedFlight=[];
    this.searchService.displayPrice =   undefined;
    this.searchService.displayCurrency =undefined;
    this.searchService.selectedFilter1 = [];
    this.searchService.filtered_airlines = [];
    if (this.searchComponent) {
      this.searchComponent.searchInProgress = true;
    }
    this.unsubscribeSearch();
   
    this.resetStopsAndAirlines();
    this.searchService.multipleOutgoingSelectedFlight = [];
    this.searchService.inPolicyFlight = [];
    this.searchService.tempFlight = [];
    this.searchService.orgFlightResponse = undefined;
    this.searchService.flightId = [];
     this.searchService.internationalFlightPolicyObject=null;
    this.searchService.policyObject=null;
    this.searchQuick(this.searchService.flightSearchQueryParam);
    this.searchDetailed(this.searchService.flightSearchQueryParam);
    this.gallopLocalStorage.removeItem("flightSearchRequestForBooking");
    if(!this.searchService.multiTripBooking){
      this.searchService.multicarQuery =[];
      this.searchService.multiflightQuery=[];
      this.searchService.multihotelQuery=[];
      this.searchService.editMytripname='false';
    this.gallopLocalStorage.removeItem("passengers");
    this.gallopLocalStorage.removeItem("cabinClassNames");
    this.searchService.seatForMultipleBooking=[];
    this.gallopLocalStorage.removeItem("passengersFormData");
    this.gallopLocalStorage.removeItem("selectedSeat");
    //  this.gallopLocalStorage.removeItem("flightSearchRequest");
    this.gallopLocalStorage.removeItem("hotelSearchRequestForBooking");
    this.gallopLocalStorage.removeItem("carSearchRequestForBooking");
    this.gallopLocalStorage.removeItem("carSearchRequest");
    }
  }

  resetStopsAndAirlines() {
    this.filterService.resetStopsAndAirlines();
  }

  showAllResults() {
    setTimeout(() => {
      this.filterService.showLessFlag = false;
      this.flightsDatasource.adapter.reload(0);
    }, 200);
  }
  ngAfterViewInit() {
    setTimeout(() => {
      if (!this.content) {
        console.error('TemplateRef "content" not available');
      } else {
        console.log('TemplateRef "content" available:', this.content);
        this.bsModalRef1 = this.modalService.show(this.content, {
          backdrop: true,
          keyboard: false,
          ignoreBackdropClick: true
        });
      }
    });
  }
  searchQuick(flightSearchQueryParam: FlightSearchQueryParam) {
    this.progressBar.start(SearchActionType.QUICK);
   if(this.searchService.searchPopMsg && this.searchService.searchPopMsg.length > 0){
     this.searchService.showNextMessage();
   }
    if(this.bsModalRef1){
      this.bsModalRef1.hide();
    }
   
    this.quickSearchSubscription = this.searchService.search(flightSearchQueryParam, SearchActionType.QUICK).subscribe(res => {
      if (this.connectionListener) {
        this.connectionListener.unsubscribe();
      }
      if ((res.status == 'success' && res.data) && (res.data.flightsLists[0].flights && res.data.flightsLists[0].flights.length > 0)) {
        this.callComplete = true;
        this.searchComponent.searchInProgress = false;
        this.bsModalRef1.hide();
        this.searchService.seletedIndex = 0;
        let flightSearchResponse: FlightSearchResponse = deserialize(res.data, FlightSearchResponse);
        if (flightSearchResponse.flightsLists && flightSearchResponse.flightsLists.length > 1) {
          this.searchService.nonCombo =true;
        }else{
          this.searchService.nonCombo =false;
        }
        this.setFlightResults(flightSearchResponse, SearchActionType.QUICK);
        //     this.router.navigate(["hotels"],
        //   {
        //     queryParams:
        //       {
        //         query: encodeURIComponent(JSON.stringify(this.hotelSearchQueryParam)),
        //         step: 0,
        //         userid: userid,
        //         sToken: sToken,
        //         resultFound: 1
        //       },
        //     replaceUrl: true
        //   }
        // );
        // this.router.navigate(["flights"], { queryParams: { ...this.activatedRoute.snapshot.queryParams, step: this.bookingWizardStep, resultFound : 1  },replaceUrl : true });
      } else if (res.status == 'error') {
        this.setFlightResults(new FlightSearchResponse(), SearchActionType.QUICK);
      } else {
        this.router.navigate(['/errors/404']);
      }
    });

  }
  
  
  showNextMessage() {
    this.searchService.currentMessage = this.searchService.searchPopMsg[this.messageIndex];

    // Update the message every 5 seconds
    this.intervalId = setInterval(() => {
      this.messageIndex = (this.messageIndex + 1) % this.searchService.searchPopMsg.length;
      this.searchService.currentMessage =this.searchService.searchPopMsg[this.messageIndex];
    }, 5000); 
  }
  private closeAllModals() {
    for (let i = this.modalService.getModalsCount(); i >= 0; i--) {
      this.modalService._hideModal(i);
    }
  }
  bsModalRef1: BsModalRef;
  bsModalRef: BsModalRef;
  resultErrorMessage = this.translateService.instant('searchResult.Noflightsfound').toString();
  handleSelectedAirline(event) {
    this.progressBar.start(SearchActionType.DETAIL);
    this.searchService.filterReset = false;
    this.searchService.searchCallComplete =false;
    this.bsModalRef= this.modalService.show(this.content, {backdrop: true, keyboard: !false, ignoreBackdropClick: true },);
    this.detailSearchSubscription = this.searchService.search(this.searchService.flightSearchQueryParam, SearchActionType.DETAIL, event,this.bookingService.previousTransactionId).subscribe(res => {
      if (this.connectionListener) {
        this.connectionListener.unsubscribe();
      }
      // this.searchComponent.searchInProgress = false;

      if (res && res.status == 'success') {
        // this.searchService.seletedIndex =0;
       // this.closeAllModals();
       if(this.bsModalRef){
        this.bsModalRef.hide();
      }
        this.progressBar.stop(SearchActionType.DETAIL);
        
        let flightSearchResponse: FlightSearchResponse = deserialize(res.data, FlightSearchResponse);
        if (!this.searchService.nonCombo && flightSearchResponse.flightsLists && flightSearchResponse.flightsLists.length > 0 && flightSearchResponse.flightsLists[0].flights && flightSearchResponse.flightsLists[0].flights.length > 0) {
          for (let flight of flightSearchResponse.flightsLists[0].flights) {
            this.searchService.orgFlightResponse.flightsLists[0].flights.push(flight);
          }
        }else{
            if (this.searchService.nonCombo && flightSearchResponse.flightsLists && flightSearchResponse.flightsLists.length > 0 && flightSearchResponse.flightsLists[0].flights && flightSearchResponse.flightsLists[0].flights.length > 0) {
              for (let i = 0; i < flightSearchResponse.flightsLists.length; i++) {
                for (let flight of flightSearchResponse.flightsLists[i].flights) {
                this.searchService.orgFlightResponse.flightsLists[i].flights.push(flight);
              }
          }
        }
      } 
          // this.flightSearchResponse = this.orgFlightSearchResponse;
          if (!(flightSearchResponse && flightSearchResponse.flightsLists
            && flightSearchResponse.flightsLists.length > 0 && flightSearchResponse.flightsLists[0].flights
            && flightSearchResponse.flightsLists[0].flights.length > 0)) return;

          this.filterService.showLessFlag = true;
          this.orgFlightSearchResponse = this.searchService.orgFlightResponse;
          
          this.searchService.selectedFilter = this.searchService.selectedFilter1;
          let flightResponse = JSON.parse(JSON.stringify(this.orgFlightSearchResponse));
          // Object.assign(this.flightSearchResponse.airports,flightResponse.airports);
          flightResponse.airports = { ...flightResponse.airports, ...flightSearchResponse.airports }
          flightResponse.airlineNames = { ...flightResponse.airlineNames, ...flightSearchResponse.airlineNames}
          // 
          this.searchService.setFlightSearchResponse(flightResponse, true, event);
          this.searchService.searchCallComplete =true;
        }else{
          if(this.bsModalRef){
            this.bsModalRef.hide();
          }
          this.searchService.searchCallComplete =true;
          this.progressBar.stop(SearchActionType.DETAIL);
        }
       
      

    }, error => {
      if (error.status != 403) {
        setTimeout(() => {
          this.searchService.searchCallComplete =true;
          this.resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          //this.setFlightResults(new FlightSearchResponse(), SearchActionType.DETAIL);
          // this.bsModalRef1.hide();
          if(this.bsModalRef){
            this.bsModalRef.hide();
          }
          this.callComplete = true;
        }, 100);
      }
    });
  }
  searchDetailed(flightSearchQueryParam: FlightSearchQueryParam) {
    this.progressBar.start(SearchActionType.DETAIL);
    // this.bsModalRef = this.modalService.show(searchModalBasic, {
    //   initialState: {

    //   }, backdrop: true, ignoreBackdropClick: true
    // });
   
    this.searchService.minPrices = undefined;
    let trainObj={};
    if(this.searchService.trainFilter.length > 0){
      trainObj['airlineFilter'] = this.searchService.trainFilter[0];
    }else{
      trainObj =null;
    }
    this.detailSearchSubscription = this.searchService.search(flightSearchQueryParam, SearchActionType.DETAIL,trainObj,this.bookingService.previousTransactionId).subscribe(res => {
      if (this.connectionListener) {
        this.connectionListener.unsubscribe();
      }
      this.callComplete = true;
      this.searchComponent.searchInProgress = false;
      this.resultErrorMessage = this.translateService.instant('searchResult.Noflightsfound').toString();
      if(this.searchService.trainFilter.length > 0 ){
        this.resultErrorMessage = this.translateService.instant('result.Notrainsfound').toString();
      }

      if (res && res.status == 'success') {
        if (this.bsModalRef1) {
          this.bsModalRef1.hide();
        }
      //  this.searchService.searchCallComplete =true;
        this.searchService.seletedIndex = 0;
        if(res.data && res.data.flightPolicy){
          this.searchService.policyObject =res.data.flightPolicy;
        }
        if(res.data && res.data.internationalFlightPolicy){
          this.searchService.internationalFlightPolicyObject =res.data.internationalFlightPolicy;
        }
       
        this.userAccountInfoService.approvalRequiredFor = res.data.approvalRequiredFor;
        this.searchService.travelPurposeMandatory = res.data.travelPurposeMandatory;
        let flightSearchResponse: FlightSearchResponse = deserialize(res.data, FlightSearchResponse);
        this.searchService.minPrices = flightSearchResponse.summary.minPrices;
        if (flightSearchResponse.flightsLists && flightSearchResponse.flightsLists.length > 1) {
          this.searchService.nonCombo =true;
        }else{
          this.searchService.nonCombo =false;
        }
        this.setFlightResults(flightSearchResponse, SearchActionType.DETAIL);
        this.filterService.broadcastResetFiltersResults();  
        this.searchService.searchCallComplete =true;
      } else {
        this.setFlightResults(new FlightSearchResponse(), SearchActionType.DETAIL);
        
        if (this.bsModalRef1) {
          this.bsModalRef1.hide();
          this.searchService.searchCallComplete =true;
          this.callComplete = true;
        }
      }

    }, error => {
      if (this.bsModalRef1) {
        this.bsModalRef1.hide();
      }
      if (error.status != 403) {
        setTimeout(() => {
          this.resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.setFlightResults(new FlightSearchResponse(), SearchActionType.DETAIL);
          this.bsModalRef1.hide();
          this.callComplete = true;
        }, 100);
      }
    });
  }


  isFlightListNonZero(flightSearchResponse: FlightSearchResponse): boolean {
    if (flightSearchResponse && flightSearchResponse.flightsLists
      && flightSearchResponse.flightsLists.length > 0 && flightSearchResponse.flightsLists[0].flights
      && flightSearchResponse.flightsLists[0].flights.length > 0) return true; else return false;
  }
  isReturnFlightList(): boolean {
    // return this.comboOutGoingSelectedFlight ? true : false;
    return this.searchService.outgoingSelectedFlight ? true : false;
  }


  getFlightLegRequestDate() {
    let searchRequest: FlightSearchRequest = this.searchService.getSearchRequest(this.searchService.getPreviousSearch());
    let dateString = undefined;
    if (this.isReturnFlightList()) {
      dateString = searchRequest.requests[1].journeyDate.intervalStart;
    } else {
      dateString = searchRequest.requests[0].journeyDate.intervalStart;
    }
    return new Date(dateString);
  }
  getRequestDepartureText() {
    // let searchRequest: FlightSearchRequest = this.searchService.getSearchRequest(this.searchService.getPreviousSearch());
    // let toFromText = undefined;
    // if(this.isReturnFlightList()){
    //   if(searchRequest.requests[1].journeyDate.intervalType.toString() === IntervalType.ARRIVAL_BASED.toString()){
    //     toFromText = ' arriving on ';
    //   }else{
    //     toFromText = '';
    //   }

    // }else{
    //   if(searchRequest.requests[0].journeyDate.intervalType.toString() === IntervalType.ARRIVAL_BASED.toString()){
    //     toFromText = ' arriving on ';
    //   }
    // }
    return '';
  }
  getCurrencySymbol(currency): string {
   
      return CommonUtils.getCurrencySymbol(currency);
    
  }
  setFlightResults(flightSearchResponse: FlightSearchResponse, searchActionType: SearchActionType) {
    this.progressBar.stop(searchActionType);
    this.searchService.searchType = searchActionType;
    this.orgFlightSearchResponse = flightSearchResponse;

    this.flightSearchResponse = flightSearchResponse;
    if (!(flightSearchResponse && flightSearchResponse.flightsLists
      && flightSearchResponse.flightsLists.length > 0 && flightSearchResponse.flightsLists[0].flights
      && flightSearchResponse.flightsLists[0].flights.length > 0)) {
      return;
      }else{
        if(flightSearchResponse.flightsLists[0].flights[0].displayCurrency){
          this.currency = flightSearchResponse.flightsLists[0].flights[0].displayCurrency;
        }else{
        this.currency = flightSearchResponse.flightsLists[0].flights[0].currency;
        }
     
        if(this.searchService.policyObject && this.searchService.policyObject.considerBasicEconomy && this.searchService.policyObject.upperClassReason!=='BASED_ON_FLIGHT_DURATION'&& this.searchService.policyObject.pricePolicyList.length ===0 && 
          !this.searchService.policyObject.maxFlightClassAllowed){
            this.searchService.policyObject =null;
          }
          if(this.searchService.internationalFlightPolicyObject && this.searchService.internationalFlightPolicyObject.upperClassReason!=='BASED_ON_FLIGHT_DURATION' &&  this.searchService.internationalFlightPolicyObject.considerBasicEconomy && this.searchService.internationalFlightPolicyObject.pricePolicyList.length ===0 && 
             !this.searchService.internationalFlightPolicyObject.maxFlightClassAllowed){
              this.searchService.internationalFlightPolicyObject =null;
            }
      }

    this.filterService.showLessFlag = true;
    let flightResponse = JSON.parse(JSON.stringify(flightSearchResponse));
    this.searchService.setFlightSearchResponse(flightResponse, true);
    // this.comboOutGoingSelectedFlight = undefined;
   // this.filterService.resetAllFilters();
    this.searchComponent.departureAirports = "";
    let airportListString = "";
    for (let airport of this.searchService.airportOptions) {
      if (airportListString.length > 0) airportListString = airportListString + ", ";
      airportListString = airportListString + airport.id;
    }
    this.searchComponent.departureAirports = airportListString;

    airportListString = "";
    for (let airport of this.searchService.airportDestinationOptions) {
      if (airportListString.length > 0) airportListString = airportListString + ", ";
      airportListString = airportListString + airport.id;
    }
    this.searchComponent.arrivalAirports = airportListString;
    // this.searchComponent.
    // flightResponse = this.filterService.filterByPreferredAirlines(this.filterService.airline ? this.filterService.airline : Constants.ANY, flightResponse);
    // flightResponse = this.filterService.optimizeFlightResponse(this.filterService.optimizeFilterArray, flightResponse);

    // if (this.filterService.numOfStops != Constants.MAX_STOPS) {
    //   flightResponse = this.filterService.filterByNoOfStops(this.filterService.numOfStops, flightResponse);
    // }
    // this.searchService.setFlightSearchResponse(flightResponse, false);

    let userid = this.userAccountInfoService.getUserEmail();
    let sToken = this.userAccountInfoService.getSToken();
    let queryAsJSON = JSON.parse(JSON.stringify(this.searchService.flightSearchQueryParam));
    // queryAsJSON.departureDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(queryAsJSON.departureDate)) + "T00:00:00.000Z";
    // queryAsJSON.arrivalDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(queryAsJSON.arrivalDate)) + "T00:00:00.000Z";
    // if (queryAsJSON.flights && queryAsJSON.flights.length > 0) {
    //   for (let multiFlightObj of queryAsJSON.flights) {
    //     multiFlightObj.departureDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(multiFlightObj.departureDate)) + "T00:00:00.000Z";
    //     multiFlightObj.arrivalDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(multiFlightObj.arrivalDate)) + "T00:00:00.000Z";
    //   }
    // }
    this.router.navigate(["flights"],
      {
        queryParams:
        {
          query: encodeURIComponent(JSON.stringify(queryAsJSON)),
          step: 0,
          index: this.searchService.seletedIndex,
          resultFound: 1
        },
        replaceUrl: true
      }
    );
  }

  setFlightLegs(count) {
    // this.noOfFlightLegs = Array(count || 1).fill(1).map((x, i) => i);
  }

  setTravelerInfo(flightSearchQueryParam: FlightSearchQueryParam) {
    if (flightSearchQueryParam) {
      this.noOfPassengers = Number.parseInt(flightSearchQueryParam.passengers);
      this.class = flightSearchQueryParam.class;
    }
  }

  searchResultsFound() {
    return this.flightSearchResponse && this.flightSearchResponse.flightsLists &&
      this.flightSearchResponse.flightsLists[0] && this.flightSearchResponse.flightsLists[0].flights
      && this.flightSearchResponse.flightsLists[0].flights.length > 0;
  }
  // comboOutGoingSelectedFlight : FlightResult = undefined;
  onFlightSelection(flightSelection: any) {
    //
    this.userAccountInfoService.userFormOpen=-1;
    this.searchService.emptyIntervalID();
    if(this.detailSearchSubscription){
      this.detailSearchSubscription.unsubscribe();
    }
    this.bookingService.proceedButton =false;
    this.userAccountInfoService.notToRefreshForm = false;
    this.userAccountInfoService.paymentPageSave = false;
    if (this.detailSearchSubscription) {
      this.detailSearchSubscription.unsubscribe();
      this.progressBar.stop(SearchActionType.DETAIL);
    }
    this.userAccountInfoService.promptUserTosaveProfile = true;
    let flight: FlightResult = flightSelection.flight;
    let index = flightSelection.index;
    let type = flightSelection.type;
    this.searchService.backClick = false;
    this.searchService.paymentPage = false;
    this.searchService.priceChange = undefined;
    if (type === 'static') return;

    if (flight.childFlightId) {
      // this.comboOutGoingSelectedFlight = flight;
     
    let selectedFlight  = JSON.parse(JSON.stringify(flight));
      this.searchService.tempFlight.push(selectedFlight);
      
      this.filterService.broadcastResetFiltersResults();  
      //  if(this.searchService.airportOptions.length >1 || this.searchService.airportDestinationOptions.length >1){
      let source = flight.legs[0].flightHops[0].from;
      let dest = flight.legs[0].flightHops[flight.legs[0].flightHops.length - 1].to;
      let start = flight.legs[0].flightHops[0].starts;
      let end = flight.legs[0].flightHops[0].ends;
      let flightNUmber = flight.legs[0].flightHops[0].flightNumber;
      let carrier = flight.legs[0].flightHops[0].carrier;
      let selectedLeg = [];
     
      if(this.searchService.nonCombo && this.searchService.seletedIndex > 0 && this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === flight.legs[0].flightHops[0].carrier){
        var creditDetails = JSON.parse(JSON.stringify(CommonUtils.getCreditValue(this.searchService.nonComboSelectedFlight[0], this.searchService.adultCount)));
        if(creditDetails && creditDetails.length >0){
        let price = CommonUtils.getPriceAfterTravelCredits(flight.price, creditDetails, this.searchService.adultCount);
        let selectedAirline= {'airlineFilter': [carrier]}
        if(price===0 && this.searchService.adultCount===1){
          this.searchService.selectedFilter1.push(carrier);
          this.searchService.priceWithTravelCreditsZero =true;
          //this.searchService.setOutgoingMultipleSelectedFlight(flight, this.searchService.seletedIndex);
        }else if(this.searchService.adultCount >1){
          for(let flight of this.searchService.nonComboSelectedFlight){
            // selectedFlightsPrice += flight.price;
            if(flight.displayPrice){
              creditDetails = JSON.parse(JSON.stringify(CommonUtils.totalLeftCredit(flight.displayPrice,creditDetails,this.searchService.adultCount)));
            }else{
             creditDetails = JSON.parse(JSON.stringify(CommonUtils.totalLeftCredit(flight.price,creditDetails,this.searchService.adultCount)));
            }
           }
           let creditleft=false;
           for(let item of creditDetails){
             if(item.creditAmount > 0){
              creditleft=true;
              break;
             }
           }
           if(creditleft){
          this.searchService.selectedFilter1.push(carrier);
          this.searchService.priceWithTravelCreditsZero =true;
           }else{
            this.searchService.priceWithTravelCreditsZero =false;
          }
        }
      }
      }else{
        this.searchService.priceWithTravelCreditsZero =false;
      }
      if (this.searchService.selectedLeg.length > 0 && this.searchService.selectedLeg.indexOf(flightNUmber) === -1) {
        // let optionEvent = { 'from': source, 'to': dest, 'starts': start, 'ends': end, 'carrier': carrier, 'flightNumber': flightNUmber };
        selectedLeg.push(flight.legs[0].flightHops);
        this.searchService.selectedLeg[this.searchService.seletedIndex - 1] = selectedLeg[0];
      } else if (this.searchService.selectedLeg.length === 0) {
        // let optionEvent = { 'from': source, 'to': dest, 'starts': start, 'ends': end, 'carrier': carrier, 'flightNumber': flightNUmber };
        selectedLeg.push(flight.legs[0].flightHops);
        this.searchService.selectedLeg[this.searchService.seletedIndex - 1] = selectedLeg[0];
      }
      while (this.searchService.selectedLeg.length > this.searchService.seletedIndex) {
        this.searchService.selectedLeg.pop();
      }
      if (selectedLeg.length > 0) {
        this.searchService.apiCallafterFlightSelection = true;
        let optionEvent = { 'selectedLeg':this.searchService.selectedLeg}
        this.handleSelectedAirline(optionEvent);
      }
      // }
      // this.searchService.setOutgoingSelectedFlight(flight);
      this.currSortOptionId = 'recommended';
     // this.sortOptionChanged({ 'id': 'recommended' });
     this.searchService.currentSortOptionId = 'recommended';
    this.sortOptionChanged({ 'id': this.searchService.currentSortOptionId },'change');
      if (this.searchService.processedFlightResults.length > 1) {
        if (this.searchService.seletedIndex !== this.searchService.processedFlightResults.length) {
          this.searchService.setOutgoingMultipleSelectedFlight(flight, this.searchService.seletedIndex);
        }
      } else {
        this.searchService.setOutgoingSelectedFlight(flight);
      }
      this.brandedFareCheckBox = flightSelection.brandedFareCheckBox;
      this.searchService.setBrandedFareCheckBoxState(this.brandedFareCheckBox);

      if (this.searchService.processedFlightResults.length > 1 && this.searchService.seletedIndex > 1) {
        this.comboOutGoingSelectedFlight = this.searchService.multipleOutgoingSelectedFlight[0];
        if(this.searchService.nonCombo){
          this.comboOutGoingSelectedFlight.price += flight.price;
          }else{
            this.comboOutGoingSelectedFlight.price = flight.price;
          }
        this.comboOutGoingSelectedFlight.fareBreakup = flight.fareBreakup;
        this.comboOutGoingSelectedFlight.legs[this.searchService.seletedIndex - 1] = flight.legs[0];
        this.selectComboOutGoingSelectedFlight = this.searchService.multipleOutgoingSelectedFlight[0];
      } else if (this.searchService.processedFlightResults.length > 1) {
        this.selectComboOutGoingSelectedFlight = this.searchService.multipleOutgoingSelectedFlight[0];
        this.comboOutGoingSelectedFlight = this.searchService.multipleOutgoingSelectedFlight[0];
        if(this.searchService.nonCombo){
          this.comboOutGoingSelectedFlight.price += flight.price;
          }else{
            this.comboOutGoingSelectedFlight.price = flight.price;
          }
        this.comboOutGoingSelectedFlight.fareBreakup = flight.fareBreakup;
        this.comboOutGoingSelectedFlight.legs[this.searchService.seletedIndex - 1] = flight.legs[0];
      }
      if (flight.legs[0].brandDetails && flight.legs[0].brandDetails.length > 0) {
        this.comboOutGoingSelectedFlight.legs[0].brandDetails[0].priceDiff = flight.legs[0].brandDetails[0].priceDiff;
        this.brandedFareCheckBox = this.searchService.brandedFareCheckBox;
      } else {
        // this.comboOutGoingSelectedFlight.legs[0].brandDetails = undefined;
        this.brandedFareCheckBox = false;
        this.searchService.setBrandedFareCheckBoxState(this.brandedFareCheckBox);
      }

//if(this.searchService.allFaresPopupShow){
this.searchService.comboOutGoingSelectedFlight =  JSON.parse(JSON.stringify(this.comboOutGoingSelectedFlight));;
//}
      this.filterService.currLegIndex = this.searchService.seletedIndex;
    //  this.filterService.broadcastgetFilters();
      this.searchService.flightId.push(flight.legs[0].legId);
      let queryAsJSON = JSON.parse(JSON.stringify(this.searchService.flightSearchQueryParam));
      queryAsJSON.departureDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(queryAsJSON.departureDate)) + "T00:00:00.000Z";
      queryAsJSON.arrivalDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(queryAsJSON.arrivalDate)) + "T00:00:00.000Z";
      if (queryAsJSON.flights && queryAsJSON.flights.length > 0) {
        for (let multiFlightObj of queryAsJSON.flights) {
          multiFlightObj.departureDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(multiFlightObj.departureDate)) + "T00:00:00.000Z";
          multiFlightObj.arrivalDate = DateUtils.getFormattedDateWithoutTimeZone(new Date(multiFlightObj.arrivalDate)) + "T00:00:00.000Z";
        }
      }
      window.scrollTo(0, 0);
      this.router.navigate(["flights"],
        {
          queryParams:
          {
            query: encodeURIComponent(JSON.stringify(queryAsJSON)),
            step: 0,
            index: this.searchService.seletedIndex,
            flight: this.searchService.flightId,
            resultFound: 1
          },
          replaceUrl: false
        }
      );
     
      NavigationUtil.setCurrentNavigationMenu(NavigationUtil.NAVIGATION_UTIL_STORAGE_KEY);
    } else if (flight.parentFlightId) {
      this.filterService.broadcastResetFiltersResults();  
      if(!this.searchService.nonCombo){
        let selectedFlight  = JSON.parse(JSON.stringify(flight));
          this.searchService.tempFlight.push(selectedFlight);
          }
   //  this.searchService.priceWithTravelCreditsZero =false;
      var selectedFlight = this.searchService.originalFlightsWithIndex.find(item => item.recommendedIdx === flight.recommendedIdx);
      // this.searchService.setOutgoingSelectedFlight(selectedFlight);
      if (this.searchService.processedFlightResults.length > 1) {
        if (this.searchService.seletedIndex !== this.searchService.processedFlightResults.length) {
          this.searchService.setOutgoingMultipleSelectedFlight(selectedFlight, this.searchService.seletedIndex);
        }
      } else {
        this.searchService.setOutgoingSelectedFlight(selectedFlight);
      }
      this.bookingService.priceChange = false;
      this.bookingService.beforeRevalidation = false;
      this.searchService.seatSelectArray1 = [];
      //   this.comboOutGoingSelectedFlight = this.searchService.outgoingSelectedFlight;
      this.selectComboOutGoingSelectedFlight = JSON.parse(JSON.stringify(this.comboOutGoingSelectedFlight));
      if(!this.searchService.nonCombo){
      for (let i = 0; i < this.searchService.processedFlightResults.length; i++) {
        let selectedFlightCombo = this.searchService.processedFlightResults[i].find(item => { return item.recommendedIdx === flight.recommendedIdx; });
       // this.selectComboOutGoingSelectedFlight.legs[i] = selectedFlightCombo.legs[0];
      }
    }else{
      for (let i = 0; i < this.searchService.nonComboSelectedFlight.length; i++) {
      //  let selectedFlightCombo = this.searchService.processedFlightResults[i].find(item => { return item.recommendedIdx === flight.recommendedIdx; });
        this.selectComboOutGoingSelectedFlight.legs[i] = this.searchService.nonComboSelectedFlight[i].legs[0];
      }
    }
      if (this.searchService.processedFlightResults.length > 1) {
        //this.comboOutGoingSelectedFlight = this.searchService.multipleOutgoingSelectedFlight[(this.searchService.seletedIndex -2)];
        if(this.searchService.nonCombo){
        this.comboOutGoingSelectedFlight.price += flight.price;
        }else{
          this.comboOutGoingSelectedFlight.price = flight.price;
        }
        this.comboOutGoingSelectedFlight.fareBreakup = flight.fareBreakup;
        this.comboOutGoingSelectedFlight.legs[this.searchService.seletedIndex - 1] = flight.legs[0];
      //  this.selectComboOutGoingSelectedFlight = JSON.parse(JSON.stringify(this.comboOutGoingSelectedFlight));
        if(this.searchService.nonCombo){
          this.selectComboOutGoingSelectedFlight.price=0;
          this.selectComboOutGoingSelectedFlight.displayPrice=0;
          for(let flight of this.searchService.nonComboSelectedFlight){
            if(flight.displayPrice){
              this.selectComboOutGoingSelectedFlight.displayPrice += flight.displayPrice; 
            }
            this.selectComboOutGoingSelectedFlight.price += flight.price;
          }
          if(flight.displayPrice){
this.searchService.displayPrice =   this.selectComboOutGoingSelectedFlight.displayPrice;
this.searchService.displayCurrency = flight.displayCurrency;
          }
         }else{
          if(flight.displayPrice){
            this.selectComboOutGoingSelectedFlight.displayPrice = flight.displayPrice; 
            this.searchService.displayPrice =   this.selectComboOutGoingSelectedFlight.displayPrice;
this.searchService.displayCurrency = flight.displayCurrency;
          }
            this.selectComboOutGoingSelectedFlight.price = flight.price;
          }
      //  this.selectComboOutGoingSelectedFlight.price = flight.price;
        this.selectComboOutGoingSelectedFlight.fareBreakup = flight.fareBreakup;

      } else {
        this.comboOutGoingSelectedFlight.price = flight.price;
        this.comboOutGoingSelectedFlight.fareBreakup = flight.fareBreakup;
        this.comboOutGoingSelectedFlight.legs.push(flight.legs[0]);
        this.selectComboOutGoingSelectedFlight.price = flight.price;
        this.selectComboOutGoingSelectedFlight.fareBreakup = flight.fareBreakup;
      }
      if(!this.searchService.nonCombo){
      this.selectComboOutGoingSelectedFlight = JSON.parse(JSON.stringify(this.searchService.comboOutGoingSelectedFlight));
      }
      if (!this.searchService.nonCombo && this.selectComboOutGoingSelectedFlight.legs[0].brandDetails && flight.legs[0].brandDetails.length > 0) {
        this.selectComboOutGoingSelectedFlight.legs[0].brandDetails[0].priceDiff = flight.legs[0].brandDetails[0].priceDiff;
        this.brandedFareCheckBox = this.searchService.brandedFareCheckBox;
      } else {
        // this.comboOutGoingSelectedFlight.legs[0].brandDetails = undefined;
        this.brandedFareCheckBox = false;
        this.searchService.setBrandedFareCheckBoxState(this.brandedFareCheckBox);
      }
      let tempComboOutGoingSelectedFlight = JSON.parse(JSON.stringify(this.selectComboOutGoingSelectedFlight));
      tempComboOutGoingSelectedFlight.childFlightId = undefined;
      tempComboOutGoingSelectedFlight.parentFlightId = undefined;
      tempComboOutGoingSelectedFlight.flightId = undefined;
      tempComboOutGoingSelectedFlight.recommendedIdx = undefined;
      if (flight.discountedPrice) {
        tempComboOutGoingSelectedFlight.discountedPrice = flight.discountedPrice;
      }
      if(this.searchService.nonCombo){
        this.searchService.nonComboSelectedFlightForForwardArrow = [...this.searchService.nonComboSelectedFlight]
      }
      if (type !== 'static') {
        this.searchResultService.broadcastSelectedFlight(tempComboOutGoingSelectedFlight, index, this.brandedFareCheckBox,this.searchService.nonCombo);
        this.bookingWizard.buttonAction()
      }
    } else {
      this.filterService.broadcastResetFiltersResults();  
      if (type !== 'static') {
        if(this.searchService.nonCombo){
          this.searchService.nonComboSelectedFlightForForwardArrow = [...this.searchService.nonComboSelectedFlight]
        }
        flight.childFlightId = undefined;
        flight.parentFlightId = undefined;
        flight.flightId = undefined;
        flight.recommendedIdx = undefined;
        if(flight.displayPrice){
          this.searchService.displayPrice =   flight.displayPrice;
          this.searchService.displayCurrency = flight.displayCurrency;
                    }
        let selectedFlightData = JSON.parse(JSON.stringify(flight));
        this.searchResultService.broadcastSelectedFlight(selectedFlightData, index, flightSelection.brandedFareCheckBox,this.searchService.nonCombo);
        this.bookingWizard.buttonAction()
      }

    }

  }

  get bookingWizard() {
    switch (this.bookingWizardStep) {
      case 0:
        return {
          buttonAction: () => {
            this.gallopLocalStorage.removeItem("uniqueAirlines");
            this.goToTravelersForm();
          },
          buttonText: this.translateService.instant('searchResult.Book'),
          show: () => {
            if (this.flightSearchResponse && this.flightSearchResponse.flightsLists && this.flightSearchResponse.flightsLists.length > 0 && this.flightSearchResponse.flightsLists[0].flights) {
              return (this.flightSearchResponse.flightsLists[0].flights.length > 0 && this.isCollapse && this.isMobile) || (this.flightSearchResponse.flightsLists[0].flights.length > 0 && this.isBottomReached && this.isMobile);
            } else {
              return false;
            }
          },
          disabled: () => false
        };

      case 1:
        return {
          buttonAction: () => {
            this.goToPaymentForm();
          },
          buttonText: this.translateService.instant('searchResult.Continue'),
          show: () => {
            return (this.isMobile);
          },
          disabled: () => {
            return this.passengerComponent ? this.passengerComponent.passengersForm.invalid : false;
          }
        };
    }
  }

  goToTravelersForm() {
    this.bookingWizardStep = 1;
    // this.navigateOnSamePage();
    window.scrollTo(0, 0);
   // this.titleService.setTitle('');
    this.titleService.setTitle(this.translateService.instant('search.TravellerDetails'));
    this.router.navigate(['/emailflow'], { relativeTo: this.activatedRoute, queryParams: { pageMode: 'WebSearch' } });

  }

  navigateOnSamePage() {
    window.scroll(0, 0);
    this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { ...this.activatedRoute.snapshot.queryParams, step: this.bookingWizardStep } });
  }

  goToPaymentForm() {
    this.bookingWizardStep = 2;
    this.passengerComponent.setFormInLocalStorage();
    this.navigateOnSamePage();
  }

  onCollapseToggle(collapse) {
    this.isCollapse = collapse;
  }

  setSelectedFlightInfo(flightResult: FlightResult) {
    if (flightResult) {
      this.flightAmount = flightResult.fareBreakup.price;
      this.isPassportRequired = flightResult.passportRequired;
      this.emailBookingFlowService.setSearchResultOptions(flightResult, null);
    }
  }

  editBooking() {
    this.bookingWizardStep = 0;
  }

  enablePayment(enable) {
    this.isPaymentEnabled = enable;
  }

  unsubscribeSearch() {
   

    if (this.quickSearchSubscription) {
      this.quickSearchSubscription.unsubscribe();
    }

    if (this.detailSearchSubscription) {
      this.detailSearchSubscription.unsubscribe();
    }
  }

  ngOnDestroy() {
    if (this.queryParmsSubscription) {
      this.queryParmsSubscription.unsubscribe();
    }
    this.unsubscribeSearchServiceEvents();
    this.unsubscribeSearchResultServiceEvents();
    this.unsubscribeSearch();
    if (this.deviceSubscription) {
      this.deviceSubscription.unsubscribe();
    }
    if (this.bsModalRef1) {
      this.bsModalRef1.hide();
    }
    this.searchService.networkPopupListener.next(null);
    if (this.connectionListener) {
      this.connectionListener.unsubscribe();
     
    }
  }
  getShowMoreFlagStatus() {
    return this.filterService.showLessFlag;
  }
  getLoadingForShowMoreFlag() {
    return this.filterService.loadingForShowMoreFlag;
  }
 
  TripCheck(){
        if(this.searchService.flightSearchQueryParam.trip){
          return true
        }else{
          return false
        }
      
  }
  consoleFunction(e){
    
  }
 
}
