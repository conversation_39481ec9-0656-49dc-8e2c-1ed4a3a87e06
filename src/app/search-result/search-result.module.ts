import { NgModule } from '@angular/core';
import { SearchResultRoutingModule } from './search-result.routing.module';
import { CommonModule } from '@angular/common';
import { SearchResultComponent } from './search-result.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ShareModule } from '../share.module';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { NgSelectModule } from '@ng-select/ng-select';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { FlightChartComponent } from './flight-chart/flight-chart.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { PassengerDetailComponent } from '../passenger-detail/passenger-detail.component';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { UiScrollModule } from 'ngx-ui-scroll';



@NgModule({
  imports: [
    CommonModule,
    SearchResultRoutingModule,
    NgbModule,
    NgxSmartModalModule,
    NgSelectModule,
    AccordionModule,
    BsDatepickerModule,
    ReactiveFormsModule,
    FormsModule,
    UiScrollModule,
    ShareModule
    
  ],
  declarations: [
    SearchResultComponent,
    FlightChartComponent,
    PassengerDetailComponent,
  ],
})
export class SearchResultModule {

}