import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FlightChartComponent } from './flight-chart.component';

describe('ResultChartComponent', () => {
  let component: FlightChartComponent;
  let fixture: ComponentFixture<FlightChartComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [FlightChartComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FlightChartComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
