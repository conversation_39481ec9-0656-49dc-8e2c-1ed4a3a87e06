@import "../../../variables.scss";
.flight-box-left {
    max-width: 46px;
}

.result-card {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.19);
    border-radius: 6px;
    margin: 16px 0;
    cursor: pointer;
    float: left;
    width: 100%;
    background: #fff;
    &.selected {
        margin: 0 -8px;
        border: 1px solid $primary-color;
    }
    &.static {
        background: #fff;
        margin: 0;
        cursor: default;
        .list-container,
        .source,
        .destination,
        .list .icon-plane {
            background-color: #FFFFFF;
        }
        .action-container {
            box-shadow: none;
        }
    }
}
.creditNotappiledReason{
    color: var(--hyperlink-color) !important;
    font-size: 12px !important;
    line-height: 12px !important;
}
.bigIconInBox {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}

.bigIconInBox:hover {
    .msgBox1 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        bottom: 0px;
        padding: 5px 10px 20px 10px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}

.bigIconInBoxerror:hover {
    .msgBox1 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        padding: 20px;
        padding-top: 5px;
        bottom: 0px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}
.bigIconInBoxerror5:hover {
    .msgBox5 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        padding: 20px;
        padding-top: 5px;
        bottom: 0px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}
.bigIconInBoxerror6:hover {
    .msgBox6 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        padding: 20px;
        padding-top: 5px;
        bottom: 0px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}
.bigIconInBoxerror {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}
.atCharge{
  
}
.bigIconInBoxerror5 {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}
.bigIconInBoxerror6 {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}
.bigIconInBox1 {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}
.bigIconInBox5 {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}
.bigIconInBox5:hover {
    .msgBox5 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        padding: 5px 10px 20px 10px;
        bottom: 0px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}
.bigIconInBox6:hover {
    .msgBox6 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        padding: 5px 10px 20px 10px;
        bottom: 0px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}
.bigIconInBox6 {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}

.bigIconInBox1:hover {
    .msgBox2 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        padding: 5px 10px 20px 10px;
        bottom: 0px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}

.bigIconInBoxerror1:hover {
    .msgBox2 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        padding: 5px 10px 20px 10px;
        border-radius: 6px;
        bottom: 0px;
        left: -80px;
        z-index: 9;
    }
}

.bigIconInBoxerror1 {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}

.bigIconInBox2 {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}

.bigIconInBox2:hover {
    .msgBox3 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        bottom: 0px;
        padding: 5px 10px 20px 10px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}

.bigIconInBoxerror2:hover {
    .msgBox3 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        bottom: 0px;
        padding: 5px 10px 20px 10px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}

.bigIconInBoxerror2 {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}

.bigIconInBox3 {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}

.bigIconInBox3:hover {
    .msgBox4 {
        display: block;
        position: absolute;
        font-size: 14px;
        bottom: 0px;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        padding: 5px 10px 20px 10px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}

.bigIconInBoxerror3:hover {
    .msgBox4 {
        display: block;
        position: absolute;
        background-color: white;
        border: 0.1px solid black;
        white-space: nowrap;
        height: 30px;
        bottom: 0px;
        padding: 5px 10px 20px 10px;
        border-radius: 6px;
        left: -80px;
        z-index: 9;
    }
}

.bigIconInBoxerror3 {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}

.msgBox1 {
    display: none;
    position: absolute;
    font-size: 14px;
    background-color: white;
    border: 0.1px solid black;
    white-space: nowrap;
    height: 30px;
    padding: 5px 10px 20px 10px;
    border-radius: 6px;
    left: -80px;
    z-index: 9;
}

.msgBox2 {
    display: none;
    position: absolute;
    font-size: 14px;
    background-color: white;
    border: 0.1px solid black;
    white-space: nowrap;
    height: 30px;
    padding: 5px 10px 20px 10px;
    border-radius: 6px;
    left: -80px;
    z-index: 9;
}
.msgBox5 {
    display: none;
    position: absolute;
    font-size: 14px;
    background-color: white;
    border: 0.1px solid black;
    white-space: nowrap;
    height: 30px;
    padding: 5px 10px 20px 10px;
    border-radius: 6px;
    left: -80px;
    z-index: 9;
}
.msgBox6 {
    display: none;
    position: absolute;
    font-size: 14px;
    background-color: white;
    border: 0.1px solid black;
    white-space: nowrap;
    height: 30px;
    padding: 5px 10px 20px 10px;
    border-radius: 6px;
    left: -80px;
    z-index: 9;
}

.msgBox3 {
    display: none;
    position: absolute;
    font-size: 14px;
    background-color: white;
    border: 0.1px solid black;
    white-space: nowrap;
    height: 30px;
    padding: 5px 10px 20px 10px;
    border-radius: 6px;
    left: -80px;
    z-index: 9;
}

.msgBox4 {
    display: none;
    position: absolute;
    font-size: 14px;
    background-color: white;
    border: 0.1px solid black;
    white-space: nowrap;
    height: 30px;
    padding: 5px 10px 20px 10px;
    border-radius: 6px;
    left: -80px;
    z-index: 9;
}

.starsValue {
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    color:#000000;
}

.notInPolicybox {
    position: absolute;
    bottom: 0;
    font-size: 12px;
    background: #F73F39;
    color: #FFFFFF;
    width: 100%;
    left: 0;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
}

.result-item {
    min-height: 100px;
}

.marketingMsg {
    color: #A6A5A4;
    font-size: 14px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    top: 6px;
    width: 100%;
    padding-right: 10px;
    padding-bottom: 10px;
}

.city {
    font-size: 14px;
    margin-bottom: 5px;
    width: 120px;
    display: inline-block;
    text-overflow: ellipsis !important;
}

.flight-trip-type11 {
    font-size: 12px;
}

.flght-chart-info-icon {
    font-size: 20px;
    color: var(--hyperlink-color);
}

.in-policy {
    color: rgb(136, 194, 94);
    font-family: "apercu-r";
    padding-left: 5px;
    text-align: center;
}

.out-policy {
    color: rgb(249, 61, 48);
    font-family: "apercu-r";
    padding-left: 5px;
    text-align: center;
}

.flight-modal-container .flight-ticket-detail>p {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    text-transform: lowercase;
    width: 100%;
}

.flight-modal-container .flight-ticket-detail>p:first-letter {
    text-transform: uppercase;
}

.fare-attr-red {
    color: rgb(249, 61, 48);
    font-size: small;
    font-weight: lighter;
    width: 10px;
}

.fare-attr-pref-airline {
    color: #27c198;
}

.fare-attr-pref-airport {
    color: #27c198;
}

.fare-attr-green {
    color: #27c198;
    font-size: xx-small;
    font-weight: lighter;
    width: 10px;
}

.checkbox-branded-fare {
    height: 25px;
    width: 25px;
    background-color: #eee;
}

.checkbox-branded-fare-container {
    position: relative;
    padding-left: 35px;
    margin-bottom: 12px;
    color: var(--hyperlink-color);
    cursor: pointer;
    font-size: 16px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    float: left;
}

.checkbox-branded-fare-container input {
    cursor: pointer;
    width: 15px;
    height: 15px;
}

.price {
    margin-right: 10px;
    font-size: 18px;
    font-weight: 600;
    color: $link-color;
}

.list-container {
    padding: 16px;
    border-radius: 6px 0 0 6px;
    &:last-child {
        border-bottom: none;
    }
}

.list {
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 1;
    .icon-plane {
        font-size: 30px;
        padding: 0 20px;
        background: #fff;
        color: $primary-color;
    }
}

.source,
.destination {
    font-size: 18px;
    font-weight: 600;
    line-height: 26px;
    background-color: #fff;
    display: inline-flex;
    align-items: center;
    .flight-icon {
        margin-right: 2px;
        height: 12px;
    }
}

.source {
    padding-right: 20px;
}

.destination {
    padding-left: 20px;
}

.info {
    font-size: 14px;
    line-height: 18px;
    color: $secondary-text-color;
    margin: 3px 0;
}

.info-content {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    position: relative;
}

.info-content:after {
    position: absolute;
    right: -8px;
    content: ', '
}

.info-content:last-child {
    margin-right: 0;
}

.info-content:last-child:after {
    display: none
}

.action-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5px 0 5px;
    height: auto;
    border-radius: 0 6px 6px 0;
    .icon-toggle {
        padding: 10px;
        cursor: pointer;
    }
}

.layover-container {
    position: relative;
    margin: 24px -24px;
    display: flex;
    justify-content: center;
    &:after {
        content: '';
        position: absolute;
        top: 50%;
        background: $border-light-color;
        left: 0;
        right: 0;
        height: 1px;
    }
    .layover {
        display: inline-block;
        padding: 3px 24px;
        border: 1px solid $border-light-color;
        position: relative;
        z-index: 1;
        background: $background-light;
    }
}

.icon-toggle {
    color: $accent-color;
}

.flight-number {
    margin-bottom: 16px;
}

.pipe {
    margin: 0 10px;
}

.separator-right {
    border-right: 1px solid $border-dark-color;
}

.flight-icon {
    margin-right: 10px;
}

.in-policy {
    color: rgb(136, 194, 94);
    font-family: "apercu-r";
    padding-left: 5px;
    text-align: center;
}

.out-policy {
    color: rgb(249, 61, 48);
    font-family: "apercu-r";
    padding-left: 5px;
    text-align: center;
}

.no-policy {
    color: rgb(136, 194, 94);
    font-family: "apercu-r";
    padding-left: 5px;
    text-align: center;
}


/*new css*/

.result-card-box {
    background: #fff;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    border-radius: 6px;
    font-family: $fontRegular;
    float: left;
    width: 100%;
    margin-bottom: 18px;
}

.flight-detail {
    display: flex;
    justify-content: space-between;
    // padding-bottom: 33px;
}

.flight-timings {
    display: flex;
    justify-content: space-between;
}

.flight-timings-section {
    position: relative;
    z-index: 2;
}

.flight-time {
    font-size: 21px;
    line-height: 20px;
    font-family: "apercu-b";
    display: inline-block !important;
    position: relative;
}

.extra-time {
    color: #f93d30;
    position: absolute;
    top: -10px;
    right: -20px;
    font-size: 65%;
}

.extra-time1 {
    color: #f93d30;
    position: absolute;
    top: -10px;
    right: -20px;
    font-size: 65%;
}

.dot {
    color: #A6A5A4;
    margin-top: 10px;
    height: 5px;
    width: 5px;
    margin-right: 5px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
}

.detailcity {
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    color: #817E7B;
    line-height: 18px;
    text-align: left;
}

.flight-timings-lineDetail {
    position: absolute;
    height: 1px;
    width: calc(100% - 105px);
    left: 100px;
    content: "";
    background: #979797;
    top: 42px;
    z-index: 1;
}

.dateDetails {
    font-size: 14px;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    line-height: 18px
}

.flight-timings-lineDetail:after {
    position: absolute;
    content: '';
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 10px solid #979797;
    right: -2px;
    top: -4px;
}

.dotBlack {
    color: black;
    margin-top: 2px;
    height: 5px;
    width: 5px;
    margin-left: 10px;
    background-color: black;
    border-radius: 50%;
    display: inline-block;
}

.flight-airport {
    font-size: 14px;
    color: #817E7B;
    margin-top: 0px;
}

.block {
    display: block;
}

.stop-box {
    display: inline-block;
    height: 18px;
    width: 18px;
    border: 1px solid #979797;
    background-color: #817E7B;
    position: relative;
    top: 5px;
    border-radius: 100%;
    z-index: 2;
}

.numOfStops {
    color: #fff;
    position: relative;
    top: -5px;
    font-size: 14px;
    left: auto;
}

.flight-duration {
    font-size: 14px;
    line-height: 12px;
    margin-top: 14px;
}

.flight-price {
    color: var(--button-font-color);
    font-size: 22px;
    letter-spacing: 1px;
    font-family: $fontBold;
    line-height: 22px;
    margin-top: 0;
}

.flight-price.high-price {
    color: #f93d30;
}

.flight-trip-type {
    font-size: 14px;
    color: #A6A5A4;
}

.flight-name-duration {
    width: 104px;
}

.flight-timings-div {
    width: calc(100% - 104px);
    position: relative;
}

.flight-timings {
    width: 100%;
    position: relative;
}

.flight-timings-line {
    position: relative;
    height: 1px;
    width: calc(100% - 250px);
    min-width: 50px;
    left: 0px;
    content: "";
    background: #979797;
    top: 10px;
    z-index: 1;
}

.flight-timings-line:after {
    position: absolute;
    content: '';
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 10px solid #979797;
    right: -2px;
    top: -4px;
}

// .flight-facilities {
//     border-top: 2px solid #E3E3E3;
//     min-height: 26px;
//     float: left;
//     width: 95%;
//     position: relative;
//     padding: 0 10px;
//     margin-left: 20px;
//     overflow: hidden;
// }
.flight-facilities {
    margin-top: 0px;
    border-top: 2px solid #E3E3E3;
    float: left;
    position: relative;
}

.mobileFeaturelist {
    display: none;
}

// .flight-facilities ul li {
//     display: inline-block;
//     margin-right: 38px;
//     padding: 15px 0 0 0;
// }
.flight-facilities ul li {
    display: inline-block;
    margin-right: 10px;
}

.flight-facilities ul li img {
    display: inline-block;
    vertical-align: middle;
}

.flight-facilities ul li span {
    font-size: 16px;
    line-height: 12px;
    color: #A6A5A4;
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
}

.flight-facilities ul li.active .green-img {
    display: inline-block;
}

.flight-facilities ul li.active .red-img {
    display: none;
}

.flight-facilities ul li.not-active .green-img {
    display: none;
}

.flight-facilities ul li.not-active .red-img {
    display: inline-block;
}

.flight-select-button button {
    height: 64px;
    width: 153.4px;
    font-size: 16px;
    letter-spacing: 1.33px;
    color: var(--button-font-color);
}

.flight-detail-container {
    padding: 25px 25px 5px;
    float: left;
    width: 100%;
}

.flight-price-div {
    float: left;
    width: 100%;
    margin-bottom: 13px;
}

.planePriceBox>.flight-select-container {
    padding: 22px 14px 20px 0;
    position: relative;
    float: left;
    width: 100%;
    display: none;
}

.planePriceBox-2>.flight-select-container {
    padding: 22px 14px 20px 0;
    position: relative;
    float: left;
    width: 100%;
}

.planePriceBox-2>.multiChartShow {
    display: none;
}

.info-div {
    display: inline-block;
    position: absolute;
    right: 8px;
    top: 8px;
    line-height: 0;
    cursor: pointer;
    padding: 6px;
    z-index: 9;
}

.result-card-box-left {
    width: calc(100% - 490px);
    float: left;
}

.result-card-box-left-2 {
    width: calc(100% - 160px);
    float: left;
}

.result-card-box-right {
    // width: 540px;
    float: right;
}

.flight-name-logo {
    line-height: 0;
    margin-right: 5px;
}

.flight-name-logo img {
    display: inline-block;
    vertical-align: top;
}

.flight-select-button {
    float: left;
    width: 100%;
}

.stop-box-container {
    position: relative;
    top: -14px;
}

.stop-box-div {
    height: 20px;
    display: inline-block;
}

.stop-div {
    position: absolute;
    left: 0;
    right: 0;
    text-align: center;
    top: 10px;
    z-index: 0;
}

.flight-modal-container {
    font-family: $fontRegular;
}

.flight-modal-container .modal-header {
    height: 10px;
    background-color:var(--hyperlink-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flight-modal-container .modal-title {
    font-size: 14px;
    color: #FFFFFF;
    width: calc(100% - 30px);
    text-align: center;
    margin-bottom: 8px;
}

.flight-modal-container .modal-body {
    padding: 0 11px;
}

.flight-modal-container .close {
    padding: 0;
    text-shadow: none;
    color: #fff;
    opacity: 1;
    margin-right: 0;
}

.flight-modal-container .date-duration {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #E3E3E3;
    padding: 7px 12px;
}

.flight-modal-container .date {
    font-size: 16px;
    font-family: $fontBold;
}

.flight-modal-container .duration {
    font-size: 16px;
}

.flight-modal-container .duration label {
    font-family: $fontRegular;
}

.flight-modal-container .duration span {
    font-family: $fontBold;
}

.flight-modal-container .flight-box {
    float: left;
    width: 100%;
    display: flex;
    font-family: $fontRegular;
    padding: 12px 15px 7px 3px;
}

.flight-modal-container .flight-modal-name-duration {
    display: flex;
    justify-content: space-between;
    float: left;
    width: 100%;
}

.flight-modal-container .flight-modal-name {
    font-size: 18px;
}

.flight-modal-container .flight-modal-duration {
    font-size: 16px;
}

.flight-modal-container .flight-ticket-detail {
    float: left;
    width: 100%;
    color: #AEAEAE;
    font-size: 13px;
    margin-bottom: 0px;
    text-align: left;
}

.flight-modal-container .flight-timing-stops ul li {
    position: relative;
    margin-bottom: 11px;
    font-size: 14px;
    display: flex;
    text-align: left;
    float: left;
    width: 100%;
    padding-left: 20px;
    line-height: normal;
}

.flight-modal-container .flight-timing-stops ul li label {
    display: inline-block;
    vertical-align: middle;
}

.flight-modal-container .flight-timing-stops ul li span {
    margin-left: 28px;
    display: inline-block;
    vertical-align: middle;
}

.flight-modal-container .flight-timing-stops ul li:before {
    height: 9px;
    width: 9px;
    border-radius: 50%;
    background-color: var(--hyperlink-color);
    position: absolute;
    left: 0;
    top: 4px;
    content: '';
}

.flight-modal-container .flight-layover {
    float: left;
    width: 100%;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #EEEDEB;
    background-color: #EFFAFC;
    height: 37px;
    font-size: 14px;
}

.flight-modal-container .flight-layover-right {
    font-size: 16px;
}

.flight-modal-container .flight-box-right {
    padding-left: 18px;
    width: 100%;
}

.flight-modal-container .flight-modal-price {
    font-size: 19px;
    font-family: $fontMedium;
    line-height: 19px;
    color: var(--button-font-color);
}

.flight-modal-container .flight-modal-trip-type {
    color: #A6A5A4;
    font-size: 11px;
    line-height: 12px;
}

.flight-modal-container .flight-modal-button {
    float: left;
    width: 100%;
    padding: 0 15px;
}

.flight-modal-container .flight-modal-button button {
    width: 100%;
    border-radius: 6px;
    font-size: 12px;
    font-weight: normal;
    letter-spacing: 1px;
    font-family: $fontBold;
    height: 46px;
}

.flight-modal-container .flight-facilities {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    align-items: center;
    height: auto;
    border-top: 1px solid #E3E3E3;
}

.flight-modal-container .flight-facilities ul li {
    margin-right: 8px;
    padding: 0;
}

.flight-modal-container .flight-facilities ul li span {
    margin-left: 4px;
}

.flight-feature-list {
    text-align: left;
    display: flex;
    justify-content: space-between;
    float: left;
    width: 100%;
    padding: 8px 25px 24px 25px;
}

.flight-feature-list ul li {
    line-height: 15px;
}

.flight-feature-list ul li img {
    display: inline-block;
    vertical-align: middle;
    width: 15px;
}

.flight-feature-list ul li span {
    color: #AEAEAE;
    font-size: 10px;
    display: inline-block;
    margin-left: 5px;
    vertical-align: middle;
    font-size: 12px;
}

.flight-modal-container .close i {
    font-size: 18px;
}

.flight-modal-container .flight-facilities ul li span {
    font-family: $fontRegular;
}

.top-recommandation-box {
    background-color: var(--button-font-color);
    border-radius: 6px;
    float: left;
    width: 100%;
    height: 50px;
    font-size: 12px;
    line-height: 15px;
    color: #fff;
    padding: 3px 17px;
    position: relative;
    z-index: 1;
    margin-bottom: -29px;
}

.result-card-box-div {
    float: left;
    width: 100%;
    position: relative;
}

.result-card-box-inner {
    position: relative;
    z-index: 2;
    background: #fff;
    float: left;
    width: 100%;
    border-radius: 6px;
}

// this is for flight Chart
.PriceDetailsBox {
    // height: 150px;
    padding: 10px;
    width: 100px;
    min-width: 100px;
    text-align: center;
    margin: 35px 12px 20px;
    border-radius: 6px;
    position: relative;
}

.PriceDetailsBox-2 {
    height: 150px;
    padding: 10px;
    border: 0.1px solid #A6A5A4;
    color: #A6A5A4;
    width: 100px;
    min-width: 100px;
    text-align: center;
    margin: 20px 12px 20px;
    display: inline-block;
    border-radius: 6px;
    box-shadow: rgb(0 0 0 / 16%) 0px 3px 6px, rgb(0 0 0 / 23%) 0px 3px 6px;
    position: relative;
}

.planePriceBox {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    overflow: visible;
    // margin-left: -110px;
    margin-top: 0px;
    // width: 622px;
    // margin-top: -200px;
    position: absolute;
    right: 0px;
    // margin-top: -250px;
}

.planePriceBox-2 {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    position: absolute;
    right: 23px;
    top: -22px;
}

.PriceDetailsBox-single {
    height: 139px;
    padding: 10px;
    // border: 0.1px solid black;
    min-width: 100px;
    width: 100px;
    text-align: center;
    margin: -3px 1px 0px;
    display: inline-block;
    border-radius: 6px;
    // box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
    position: relative;
    font-size: 10px;
}

.PriceDetailsBox-multi {
    height: 161px;
    padding: 9px 2px;
    border: 1px solid #C2C1C1;
    width: 97px;
    text-align: center;
    margin: 11px 9px;
    display: inline-block;
    border-radius: 10px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
    position: relative;
}

.ActivePriceDetailsBox-multi {
    height: 160px;
    border: 2px solid rgba(102, 104, 107, 0.88);
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    background-color: rgb(255, 255, 255);
    z-index: 3;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
    clip-path: inset(-19px -16px 0px -15px);
}

.PriceDetailsBox-multi>h4 {
    color: #0808e5;
}

.PriceDetailsBox-multi:hover {
    border: 1px solid rgba(102, 104, 107, 0.88);
    box-shadow: rgb(0 0 0 / 16%) 0px 3px 6px, rgb(0 0 0 / 23%) 0px 3px 6px;
    cursor: pointer;
}

.Updated {
    background: rgb(255, 252, 209);
}

.creditPrice {
    position: absolute;
    font-size: 11px;
    margin: 0px 40px;
    text-align: center;
    bottom: calc((100% - 360px)/3);
}

.PriceDetailsBox-multi-disabled {
    height: 161px;
    padding: 9px;
    border: 0.1px solid #5a535370;
    min-width: 97px;
    width: 97px;
    background: #F5F3F3;
    text-align: center;
    margin: 11px 9px;
    display: inline-block;
    border-radius: 10px;
    color: #8080807a;
    //  box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
    position: relative;
}

.PriceDetailsBox-multi-mobile {
    height: 150px;
    padding: 10px;
    border: 0.1px solid black;
    width: 100px;
    min-width: 100px;
    text-align: center;
    margin: 20px 12px 20px;
    display: inline-block;
    border-radius: 6px;
    box-shadow: rgb(0 0 0 / 16%) 0px 3px 6px, rgb(0 0 0 / 23%) 0px 3px 6px;
    position: relative;
}

.PriceDetailsBox-multi-mobile:hover {
    border: 1px solid #0060f5;
    box-shadow: rgb(0 0 0 / 16%) 0px 3px 6px, rgb(0 0 0 / 23%) 0px 3px 6px;
    cursor: pointer;
}

.PriceDetailsBox-multi-mobile-diseabled {
    height: 150px;
    padding: 10px;
    border: 0.1px solid #5a535370;
    color: #8080807a;
    width: 100px;
    min-width: 100px;
    text-align: center;
    margin: 20px 12px 20px;
    display: inline-block;
    border-radius: 6px;
    // box-shadow: rgb(0 0 0 / 16%) 0px 3px 6px, rgb(0 0 0 / 23%) 0px 3px 6px;
    position: relative;
}

.PriceDetailsBox-single>h4 {
    color: #0060f5;
}

.PriceDetailsBox-multi-mobile>h3 {
    color: #0060f5;
}

.PriceDetailsBox>p {
    margin: 5px 0px;
}

.PriceDetailsBox>h2 {
    color: var(--button-font-color);
}

// .PriceDetailsBox:hover {
//     cursor: pointer;
// }
.FareClassNameCss {
    color: #A6A5A4;
    font-family: apercu-r;
    font-size: 15px;
    max-height: 38px;
    overflow: hidden;
    min-height: 38px;
}

.fareclassOld:first-letter {
    text-transform: uppercase;
}

.fareclassNew {
    white-space: nowrap;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    text-transform: lowercase;
    width: 100%;
    font-size: 14px;
}

.fareclassOld {
    white-space: break-spaces;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    text-transform: lowercase;
    max-width: 80px;
    font-size: 14px;
}

.FareClassNameCss>p {
    white-space: break-spaces;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    text-transform: lowercase;
    width: 100%
}

.FareClassNameCss>p:first-letter {
    text-transform: uppercase;
}

.detailes_price_Box .FareClassNameCss {
    margin: 2px;
}

.bigIconInBox {
    width: 18px;
    height: 18px;
    margin: 0px 5px;
}

.iconInBox {
    width: 7px;
    margin-right: 5px;
}

.PriceDetailsBox-multi .centerLine {
    height: 1px;
    width: 60%;
    margin: 10% auto;
    background-color: black;
}

.centerLine {
    height: 1px;
    width: 100%;
    margin: 10% auto;
    background-color: black;
}

.popUpShow {
    padding: 10px;
    margin-top: 200px;
    background-color: #FFFFFF;
    position: relative;
}

.popUpShow .box-top-line {
    height: 1px;
    width: calc(100% - 20px);
    background-color: #C2C1C1;
    position: absolute;
    top: -15px;
    z-index: 1;
    box-shadow: black 0px -1px 8px;
    -webkit-clip-path: inset(-10px -14px 0px);
    clip-path: inset(-10px -14px 0px);
}

.popUpShow-single {
    margin-top: 0%;
}

.hide {
    display: none;
}

.CrossBtn {
    font-size: 20px;
    cursor: pointer;
    position: absolute;
    right: -14px;
    top: -28px;
}

.popUpShow-d {
    cursor: pointer;
    text-align: end;
}

.popUpShow-h {
    position: relative;
    cursor: pointer;
    // margin-top: 10px;
    text-align: end;
    margin-right: 0px;
}

.detailsCss {
    position: absolute;
    right: -151%;
    top: -5px;
    display: none;
}

.detailsCss2 {
    position: absolute;
    right: -151%;
    top: -5px;
}

.detailsCss:hover {
    color: var(--hyperlink-color);
}

.popUpShowMobile {
    // border: 2px solid black;
    padding: 10px;
    // margin-top: 10px;
}

.InPopDiv {
    display: flex;
    justify-content: space-around;
    padding: 10px 0px;
}

.result-card-box-left-2 {
    width: calc(100% - 20px);
    float: left;
}

.popUpShow .display_Property {
    display: flex;
}

.starsImg {
    position: absolute;
    width: 125px;
    top: -25px;
    left: -20px;
}

.starsImg>span>img {
    width: 13px;
}

.BoxSlected {
    width: 100%;
    max-width: 620px;
    overflow: auto;
}

.detailes_price_Box {
    width: 172px;
    min-width: 172px;
    height: 431px;
    text-align: center;
    position: relative;
    border: 1px solid #C2C1C1;
    border-radius: 5px;
    padding: 10px 0px;
}

.Slected-Box-Content {
    display: flex;
    justify-content: flex-start;
    height: 433px;
    width: 600px;
    // overflow: scroll;
    gap: 1em;
    margin: 10px;
    cursor: default;
}

.detailes_price_Box h4 {
    color: #0808e5;
    font-size: 23px;
    margin-bottom: 5px;
    font-weight: bold;
}

.detailes_price_Box .notInPolicybox {
    position: absolute;
    bottom: 0;
    font-size: 14px;
    background: #F73F39;
    color: #FFFFFF;
    width: 100%;
    left: 0;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
}

.HopeDetails {
    text-align: center;
    position: relative;
    top: 0;
    left: 0px;
    height: 250px;
    width: 170px;
    display: table-cell;
    vertical-align: middle;
    padding-left: 10px;
}

.detailes_price_Box button {
    background-color: var(--button-bg-color);
    border-radius: 6px;
    color: var(--button-font-color);
    border: none;
    width: 90%;
    height: 40px;
    cursor: pointer;
    position: absolute;
    font-weight: 600;
    font-size: 12px;
    bottom: 60px;
    left: 10px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
}

.flight_details {
    font-size: 10px;
    color: #AEAEAE;
    margin-top: 133px;
    position: absolute;
    top: 30%;
    left: 5%;
}

.BoxSlected .flight_details span {
    color: #737171;
    font-size: 11px;
}

.flight_details .hr_line {
    height: 2px;
    margin: 3px auto;
    width: 94%;
    border: 0px;
    background-color: #c0bcbc;
}

.DetailsBoxLoader {
    display: flex;
    justify-content: center;
    align-items: center;
}

.detailes_price_Box .flight-feature-list {
    text-align: left;
    display: flex;
    justify-content: left;
    float: left;
    width: 100%;
    margin: 5px auto;
    padding: 0px;
}

.detailes_price_Box .flight-feature-list li {
    margin: 1px;
}

.detailes_price_Box .flight-feature-list ul li span {
    color: #1c1a1a;
    font-size: 10px;
    display: inline-block;
    margin: 2px 10px 2px 9px;
    vertical-align: middle;
    font-size: 13px;
    text-transform: capitalize;
    font-weight: 700;
}

.detailes_price_Box hr {
    width: calc(100% - 20px);
    height: 2px;
    border: 0px;
    background-color: #c0bcbc;
    position: absolute;
    top: 70px;
    left: 10px;
}

.colSpanForRoundTrip {
    height: 400px;
    width: 100%;
    margin-top: 11px;
    border-top: 1px solid #808080d4;
    padding-top: 10px;
}

.PriceDetailsBox-multi .h-line {
    width: 100%;
}

.MultipalBox_Active {
    height: 175px !important;
    border: 1px solid #C2C1C1 !important;
    border-bottom-left-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
    background-color: #fff !important;
    border-bottom: none !important;
    z-index: 3 !important;
    box-shadow: rgba(0, 0, 0, 0.16) 0px -2px 6px 0px, rgba(0, 0, 0, 0.23) 0px -2px 6px 0px !important;
    -webkit-clip-path: inset(-19px -16px 0px -15px) !important;
    clip-path: inset(-19px -16px 0px -15px) !important;
}

.multiChartShow::-webkit-scrollbar {
    display: none;
}

.popUpShow .flight-Info-container {
    width: 40%;
    cursor: default;
    min-width: 360px;
}

.creadit_applied {
    position: absolute;
    top: -20px;
    right: 5px;
    z-index: 8;
}

.loader_position {
    text-align: center;
    margin-left: auto !important;
    margin-right: auto !important;
    position: absolute;
    top: 55%;
}

.PopUpBox_display {
    display: flex;
}

@media (min-width: 1200.98px) {
    .starsImg {
        display: none;
    }
}

@media (max-width: 1200px) {
    .multiChartShow {
        padding-top: 10px;
    }
    .planePriceBox {
        margin-left: -15px;
        margin-top: 15px;
        width: 470px;
    }
    .planePriceBox-2 {
        margin-top: 10px;
        position: inherit;
    }
    .popUpShow {
        width: 100%;
        margin-top: 259px;
    }
    .popUpShow .box-top-line {}
    .flight-timings-div {
        width: calc(100% - 0px);
    }
    .planePriceBox-2>.flight-select-container {
        padding: 6px 14px 20px 0;
        position: relative;
        float: left;
        width: 100%;
    }
    .result-card-box-left-2 {
        width: calc(100% - 144px);
    }
    .MultipalBox_Active {
        height: 210px !important;
    }
}

// this is for flight Chart 
@media (max-width: 360px) {}

@media (max-width: 1024px) {
    .marketingMsg {
        color: #A6A5A4;
        font-size: 14px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: relative;
        top: 6px;
        width: 100%;
        padding-right: 10px;
        padding-bottom: 10px;
    }
    .result-card-box-left {
        width: calc(100% - 460px);
        float: left;
    }
    .planePriceBox {
        margin-left: -15px;
        margin-top: 15px;
        width: 460px;
        margin-right: 10px
    }
    .popUpShow .box-top-line {
        top: -18px;
    }
    .MultipalBox_Active {
        height: 206px !important;
    }
}

@media (max-width: 991px) {
    .flight-name-logo {
        padding-left: 10px;
    }
    .planePriceBox {
        overflow-x: auto;
        white-space: nowrap;
        overflow: unset;
        overflow-y: hidden;
        margin-top: 0px;
        position: absolute;
        right: 0px;
    }
    .city {
        font-size: 10px !important;
        margin-bottom: 5px;
        max-height: 45px;
        min-height: 45px;
        width: 50px !important;
        white-space: break-spaces;
        display: inline-block;
        text-overflow: ellipsis !important;
    }
    .PriceDetailsBox-multi {
        height: 160px !important;
        padding: 10px;
        border: 0.1px solid black;
        min-width: 95px;
        width: 95px;
        text-align: center;
        margin: 30px 12px 20px;
        display: inline-block;
        border-radius: 6px;
        box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
        position: relative;
    }
    .MultipalBox_Active {
        height: 175px !important;
        margin-bottom: 0;
    }
    .PriceDetailsBox-multi-disabled {
        height: 160px !important;
        padding: 10px;
        border: 0.1px solid #5a535370;
        min-width: 95px;
        width: 95px;
        text-align: center;
        margin: 30px 12px 20px;
        display: inline-block;
        border-radius: 6px;
        color: #8080807a;
        //  box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
        position: relative;
    }
    .flight-detail-container {
        padding: 25px 0px 5px;
    }
    .flight-detail {
        padding-bottom: 16px;
    }
    .flight-duration {
        font-size: 12px;
        line-height: 12px;
        margin-top: 10px;
    }
    .result-card-box-left {
        width: 360px;
    }
    .result-card-box-left-2 {
        width: calc(100% - 106px);
    }
    .stop-div {
        top: 10px;
    }
    .flight-airport {
        font-size: 14px;
        margin-top: 0px;
    }
    .flight-facilities ul li {
        margin-right: 15px !important;
        padding: 5px 0 5px 0;
    }
    .flight-trip-type {
        font-size: 12px;
    }
    .flight-price {
        font-size: 14px;
        letter-spacing: 1.17px;
    }
    .flight-name-duration {
        width: 60px;
    }
    .stop-box-container {
        position: relative;
        top: -5px;
    }
    .stop-box-div {
        line-height: 12px;
        height: 14px;
    }
    .stop-box {
        top: -8px;
        height: 17px;
        width: 17px;
    }
    .numOfStops {
        top: 1px;
    }
    .flight-price-div {
        width: 100%;
        margin-bottom: 10px;
    }
    .flight-timings-div {
        width: 100%;
    }
    .flight-modal-container .flight-timing-stops ul li span {
        margin-left: 18px;
        display: inline-block;
        vertical-align: middle;
    }
    /*.info-div{right: 6px; top: 6px;}*/
    .flight-select-button button {
        height: 40px;
        width: 100.4px;
        font-size: 12px;
        letter-spacing: 1.33px;
        color: var(--button-font-color);
    }
    .popUpShow-d {
        font-size: 12px;
    }
    .popUpShow .flight-Info-container {
        width: 40%;
        cursor: default;
    }
    .planePriceBox>.result-card-box-right {
        width: 250px;
    }
    .planePriceBox-2>.result-card-box-right {}
    .planePriceBox {
        margin-left: -55px;
        margin-top: 0px;
        width: 258px;
    }
    .planePriceBox-2 {
        margin-right: 11px;
        margin-top: -249px;
    }
    .popUpShow {
        width: 100%;
        margin-top: 232px;
    }
}

@media (max-width: 768px) {
    .extra-time {
        right: -14px;
    }
    .extra-time1 {
        right: -14px;
    }
    .result-card-box-left {
        width: 100%;
    }
    .starsImg {
        position: absolute;
        width: 100%;
        top: -25px;
        left: 0;
        right: 0;
        /* margin: auto; */
    }
    .flight-timings-lineDetail {
        position: absolute;
        height: 1px;
        width: calc(100% - 105px);
        left: 90px;
        content: "";
        background: #979797;
        top: 32px;
        z-index: 1;
    }
    .notInPolicybox {
        position: absolute;
        bottom: 0;
        font-size: 14px;
        background: #F73F39;
        color: #FFFFFF;
        width: 20px;
        right: 0;
        top: 0;
        left: auto;
        border-top-right-radius: 6px;
        border-top-left-radius: 0px;
        border-bottom-right-radius: 6px;
        border-bottom-left-radius: 0px;
    }
    .policyText {
        transform: rotate(-90deg);
        transform-origin: 50% 0px;
        display: block;
        white-space: nowrap;
        margin-bottom: auto;
        position: absolute;
        top: 50%;
        left: -195%;
        width: 79px;
        max-width: 79px;
        height: 21px;
        max-height: 21px
    }
    .dateDetails {
        font-size: 12px;
        font-family: var(--globalFontfamilyr);font-weight: bold;;
        line-height: 16px;
        max-width: 100px;
    }
    .detailcity {
        font-style: normal;
        font-weight: 700;
        font-size: 12px;
        color: #817E7B;
        max-width: 100px;
        line-height: 16px;
        text-align: left;
    }
    .mobileFeaturelist {
        display: none;
    }
    .show {
        display: block;
    }
    .starsValue {
        font-family: var(--globalFontfamilyr);font-weight: bold;;
        color:#9B9B9B;
    }
    .leftSidePanelMobile {
        display: block;
    }
    .upsellFareBoxMobile {
        display: flex;
        justify-content: space-between;
    }
    .rightSidePanelMobil {
        display: block;
    }
    .mobileDetailsBox {
        margin-bottom: 15px;
        padding: 15px;
        border: 1px solid rgba(102, 104, 107, 0.88);
        border-radius: 8px;
    }
    .Slected-Box-ContentMobile {
        display: block;
        height: auto;
        width: auto;
        // overflow: scroll;
        gap: 1em;
        margin: 10px;
        cursor: default;
    }
    .mobileShow {
        margin-top: 20px !important;
    }
    .popUpShow {
        margin: 10px 0;
        background-color: #FFFFFF;
        padding: 0;
    }
    .numOfStops {
        color: #fff;
        position: relative;
        top: 0px;
        font-size: 13px;
        left: auto;
        right: 1px;
    }
    .dot {
        color: #A6A5A4;
        margin-top: 8px;
        height: 5px;
        width: 5px;
        margin-right: 5px;
        background-color: #bbb;
        border-radius: 50%;
        display: inline-block;
    }
    .flight-facilities {
        border-top: 2px solid #E3E3E3;
        min-height: 26px;
        float: left;
        width: calc(100% + 40px);
        position: relative;
        padding: 0 0px;
        margin-bottom: 0px;
        margin-left: -20px;
        margin-right: -20px;
        margin-top: 0px;
    }
    .flight-timings-div {
        width: calc(100% - 30px);
        margin-left: auto;
        margin-right: auto;
    }
    .flight-box-left {
        width: 5%;
    }
    .flight-name-logo {
        padding-left: 0px;
        max-width: 50px;
    }
    .flight-box-right {
        width: 95%;
    }
    .centerLine {
        height: 1px;
        width: 60%;
        margin: 10% auto;
        background-color: black;
    }
    .MultipalBox_Active {
        height: 205px !important;
    }
    .popUpShow .box-top-line {
        height: 2px;
        width: 100%;
        background-color: #66686be0;
        position: absolute;
        top: -57%;
        z-index: 1;
        box-shadow: rgba(0, 0, 0, 1.35) 0px -1px 8px;
        clip-path: inset(-10px -14px 0px);
    }
    .marketingMsg {
        color: #A6A5A4;
        font-size: 9px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: relative;
        top: 4px;
        width: 100%;
        padding-right: 10px;
        padding-bottom: 10px;
    }
    .starsImg {
        position: absolute;
        width: 100%;
        top: -25px;
        left: 0;
        right: 0;
        /* margin: auto; */
    }
    .h-line {
        position: absolute;
        width: 100%;
        top: 66px;
        left: 1%;
    }
    .flight-timings-line {
        width: calc(100% - 195px);
        left: -10px;
    }
}

@media (max-width: 767.98px) {
    .starsImg {
        position: absolute;
        width: 100%;
        top: -25px;
        left: 0;
        right: 0;
        /* margin: auto; */
    }
    .fareclassNew {
        white-space: nowrap;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        text-transform: lowercase;
        width: 100%;
        font-size: 14px;
        color: #000000;
    }
    
    .fareclassOld {
        white-space: break-spaces;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #000000;
        text-transform: lowercase;
        max-width: 80px;
        font-size: 14px;
    }
    .flight-timings-lineDetail {
        position: absolute;
        height: 1px;
        width: calc(100% - 105px);
        left: 90px;
        content: "";
        background: #979797;
        top: 32px;
        z-index: 1;
    }
    .notInPolicybox {
        position: absolute;
        bottom: 0;
        font-size: 10px;
        background: #F73F39;
        color: #FFFFFF;
        width: 17px;
        right: 0;
        top: 0;
        left: auto;
        border-top-right-radius: 6px;
        border-top-left-radius: 0px;
        border-bottom-right-radius: 6px;
        border-bottom-left-radius: 0px;
    }
    .policyText {
        transform: rotate(-90deg);
        transform-origin: 50% 0px;
        display: block;
        white-space: nowrap;
        margin-bottom: auto;
        position: absolute;
        top: 50%;
        left: -228%;
        width: 79px;
        max-width: 79px;
        height: 21px;
        max-height: 21px
    }
    .dateDetails {
        font-size: 12px;
        font-family: var(--globalFontfamilyr);font-weight: bold;;
        line-height: 16px;
        max-width: 100px;
    }
    .detailcity {
        font-style: normal;
        font-weight: 700;
        font-size: 12px;
        color: #817E7B;
        max-width: 100px;
        line-height: 16px;
        text-align: left;
    }
    .mobileFeaturelist {
        display: none;
    }
    .show {
        display: block;
    }
    .starsValue {
        font-family: var(--globalFontfamilyr);font-weight: bold;;
        color:#9B9B9B;
    }
    .leftSidePanelMobile {
        display: block;
    }
    .upsellFareBoxMobile {
        display: flex;
        justify-content: space-between;
    }
    .rightSidePanelMobil {
        display: block;
    }
    .mobileDetailsBox {
        margin-bottom: 15px;
        padding: 15px;
        border: 1px solid rgba(102, 104, 107, 0.88);
        border-radius: 8px;
    }
    .Slected-Box-ContentMobile {
        display: block;
        height: auto;
        width: auto;
        // overflow: scroll;
        gap: 1em;
        margin: 10px;
        cursor: default;
    }
    .mobileShow {
        margin-top: 20px !important;
    }
    .popUpShow {
        margin: 10px 0;
        background-color: #FFFFFF;
        padding: 0;
    }
    .numOfStops {
        color: #fff;
        position: relative;
        top: 0px;
        font-size: 13px;
        left: auto;
        right: 1px;
    }
    .dot {
        color: #A6A5A4;
        margin-top: 8px;
        height: 5px;
        width: 5px;
        margin-right: 5px;
        background-color: #bbb;
        border-radius: 50%;
        display: inline-block;
    }
    .flight-facilities {
        border-top: 2px solid #E3E3E3;
        min-height: 26px;
        float: left;
        width: calc(100% + 40px);
        position: relative;
        padding: 0 0px;
        margin-bottom: 0px;
        margin-left: -20px;
        margin-right: -20px;
        margin-top: 0px;
    }
    .planePriceBox {
        overflow-x: auto;
        overflow-y: hidden;
        white-space: nowrap;
        margin-top: 0px;
        position: relative;
        right: 0px;
        display: flex;
    }
    .marketingMsg {
        color: #A6A5A4;
        font-size: 9px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: relative;
        top: 4px;
        width: 100%;
        padding-right: 10px;
        padding-bottom: 10px;
    }
}

@media (min-width: 767.98px) {
    .list:after {
        content: '';
        position: absolute;
        top: 20%;
        left: 0;
        right: 0;
        height: 1px;
        background: $border-light-color;
        z-index: -1;
    }
    .flight-icon {
        margin-right: 10px;
        height: 15px;
    }
}

@media (max-width: 768px) {
    .flight-trip-type11 {
        font-size: 8px;
    }
    .flght-chart-info-icon {
        font-size: 16px;
        color: var(--hyperlink-color);
    }
    .checkbox-branded-fare-container-mobile {
        position: relative;
        padding-right: 10px;
        padding-left: 0px;
        padding-top: 2px;
        margin-bottom: 0px;
        color: var(--hyperlink-color);
        cursor: pointer;
        font-size: 9px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        float: right;
    }
    .city {
        font-size: 10px;
        margin-bottom: 5px;
        width: 50px;
        white-space: break-spaces;
        max-height: 45px;
        min-height: 45px;
        display: inline-block;
        text-overflow: ellipsis !important;
        overflow: hidden !important;
    }
    .branded-fare-label-text {
        position: relative;
        top: -2px;
    }
    .in-policy {
        color: rgb(136, 194, 94);
        font-family: "apercu-r";
        padding-right: 15px;
        display: inline-block;
        float: right;
    }
    .out-policy {
        color: rgb(249, 61, 48);
        font-family: "apercu-r";
        padding-right: 15px;
        display: inline-block;
        float: right;
    }
    .no-policy {
        color: rgb(136, 194, 94);
        font-family: "apercu-r";
        padding-right: 15px;
        display: inline-block;
        float: right;
    }
    .action-container {
        display: inline-block;
        padding: 0px 5px 0 5px;
    }
    .result-card-box {
        margin-bottom: 14px;
    }
    .result-card-box-left {
        width: 100%;
    }
    .result-card-box-left-2 {
        width: 100%;
    }
    .flight-detail-container {
        padding: 25px 0px 5px;
    }
    .flight-detail {
        padding-bottom: 7px;
    }
    .flight-duration {
        font-size: 10px;
        line-height: 12px;
        margin-top: 10px;
    }
    .flight-airport {
        font-size: 12px;
        margin-top: 1px;
        line-height: 16px;
    }
    .flight-time {
        font-size: 15px;
        line-height: 20px;
    }
    .flight-facilities ul li {
        margin-right: 10px;
        padding: 0 0 0 0;
    }
    .flight-facilities ul li span {
        font-size: 12px;
        margin-left: 4px;
        color: #27c198;
    }
    .flight-trip-type {
        font-size: 9px;
    }
    .flight-price {
        font-size: 14px;
        letter-spacing: 1.17px;
        min-width: 65px;
    }
    .flight-name-duration {
        width: 60px;
    }
    .stop-box-container {
        position: relative;
        top: -6px;
    }
    .stop-box-div {
        line-height: 12px;
        height: 12px;
    }
    .flight-price-div {
        width: auto;
        padding-left: 10px;
        margin-bottom: 0;
        margin-top: -3px;
    }
    // .flight-timings-div {
    //     width: calc(100% - 60px);
    // }
    .flight-timings-div {
        width: calc(100% - 30px);
        margin-left: 12px;
    }
    .info-div {
        top: auto;
        bottom: 2px;
        right: 10px;
    }
    .flight-modal-container .date {
        font-size: 12px;
        line-height: 14px;
    }
    .flight-modal-container .duration {
        font-size: 14px;
    }
    .flight-modal-container .flight-modal-name {
        font-size: 16px;
    }
    .flight-modal-container .flight-modal-duration {
        font-size: 14px;
    }
    .flight-modal-container .flight-ticket-detail {
        font-size: 11px;
    }
    .flight-modal-container .flight-timing-stops ul li {
        font-size: 10px;
    }
    .flight-modal-container .flight-timing-stops ul li span {
        margin-left: 15px;
    }
    .flight-modal-container .flight-facilities {
        padding: 0 8px;
    }
    .flight-modal-container .flight-modal-button {
        padding: 0 5px;
    }
    .flight-modal-container .flight-facilities ul li {
        margin-right: 10px !important;
    }
    .flight-modal-container .flight-layover-right {
        font-size: 14px;
    }
    .flight-modal-container .flight-layover {
        padding: 5 12px;
        height: 20px;
        width: 95%;
    }
    .flight-feature-list {
        padding: 12px 25px 24px 25px;
    }
    .top-recommandation-box {
        font-size: 10px;
        margin-bottom: -31px;
    }
    .flight-modal-container .flight-box {
        padding: 5px 0;
    }
    .result-card-box-right {
        width: 100%;
    }
    .planePriceBox {
        margin-left: 8px;
        margin-top: 0px;
    }
    .planePriceBox-2 {
        margin-left: 8px;
        margin-top: 0px;
    }
}

@media (max-width: 359px) {
    .flight-modal-container .flight-facilities ul li span {
        margin-left: 2px;
    }
    .flight-modal-container .flight-facilities ul li {
        margin-right: 10px !important;
    }
    .flight-right {
        text-align: right;
    }
    .box-top-line {
        width: 97%;
        position: absolute;
        top: 140px;
    }
}

@media (min-width:768.1px) {
    .FlightResult-768 {
        display: none;
    }
    .popUpShow-h {
        display: none;
    }
}

@media(max-width:380px) {
    .flight-modal-container .flight-layover {
        padding: 5 12px;
        height: 20px;
        width: 90%;
    }
}

@media (max-width: 768px) {
    .popUpShow-d {
        display: none;
    }
    .creadit_applied {
        position: absolute;
        top: -20px;
        right: 0px;
    }
    .popUpShow .display_Property {
        display: block !important;
    }
    .popUpShow .flight-Info-container {
        width: 100%;
        cursor: default;
    }
    .loader_position {
        text-align: center;
        margin-left: auto !important;
        margin-right: auto !important;
        position: absolute;
        bottom: 0%;
    }
    .ActivePriceDetailsBox-multi {
        height: 210px;
        border: 2px solid rgba(102, 104, 107, 0.88);
        // border-bottom-left-radius: 0px;
        // border-bottom-right-radius: 0px;
        background-color: rgb(255, 255, 255);
        z-index: 3;
        box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
        clip-path: inset(-19px -16px 0px -15px);
    }
    .PopUpBox_display {
        display: flex;
        flex-direction: column;
        height: 100vh;
    }
}

.Mobile-pop-UP {
    display: flex;
    justify-content: space-around;
    margin-top: 10px;
}

.Mobile-pop-UP .starsImg {
    width: 100% !important;
    margin: 0px 0px !important;
}

.Mobile-pop-UP p {
    font-size: 13px;
}

.Mobile-pop-UP h4 {
    color: var(--hyperlink-color);
}

.Mobile-pop-UP .m-CenterLine {
    width: 3px;
    background-color: #808080bf;
    margin: 15px 20px 10px 20px;
}

.MobileFligthFeatureList .iconInBox {
    width: 8px;
    margin-right: 8px;
    margin-top: -1px;
}

.MobileFligthFeatureList>ul>li {
    width: 40%;
    text-align: start;
    font-size: 12px;
    padding: 0px !important;
    margin: -2px !important;
}

.MobileFligthFeatureList>ul>li>img {
    margin-right: 4px;
}

.fare-attr-red {
    margin-right: 5px;
    display: inline-block;
}

.fare-attr-green {
    margin-right: 5px;
    display: inline;
}