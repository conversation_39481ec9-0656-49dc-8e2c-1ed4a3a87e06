import { Component, Input, OnInit, Output, EventEmitter,
  ChangeDetectorRef, ChangeDetectionStrategy, ElementRef, HostListener, ViewChild, AfterViewInit } from '@angular/core';
import { FlightResult } from '../../entity/flight-result';
import { SearchResultService } from '../../search-result.service';
import { Subscription } from 'rxjs';
import { SearchService } from '../../search.service';
import { DateUtils } from '../../util/date-utils';
import { IntervalType } from '../../enum/interval.type';
import { DeviceDetailsService } from '../../device-details.service';
import { FlightUtils } from '../../util/flight-utils';
import { Constants } from '../../util/constants';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { GallopLocalStorageService } from 'src/app/gallop-local-storage.service';
import { CommonUtils } from '../../util/common-utils';
import { Utils } from 'ngx-bootstrap/utils';
import { flightSelected } from 'src/app/flight-selected/flight-selected.component';
import { FlightSearchRequest } from 'src/app/entity/flight-search-request';
import { FareAttributeValues } from 'src/app/enum/fare-attributes-types-values';
import { FareAttributes } from 'src/app/entity/fare-attributes';
import { FlightLegResult } from 'src/app/entity/flight-leg-result';
import { TranslateService } from '@ngx-translate/core';
import { AlgoTypes } from 'src/app/enum/algo-types';
import { BaggageDetails } from 'src/app/entity/baggage-details';
import { UserAccountService } from 'src/app/user-account.service';
import { BookingService } from 'src/app/booking.service';
import { FlightLegSearch } from 'src/app/entity/flight-leg-search';
import { JourneyDate } from 'src/app/entity/journey-date';
import { DeleteCardModelComponent } from 'src/app/email-booking-flow/delete-card-model/delete-card-model.component';
import { FlightBoxComponent } from '../flight-box/flight-box.component';
import { PopupComponent } from 'src/app/popup/popup.component';
import { ConnectionService } from 'ng-connection-service';
import { FilterService } from 'src/app/filter.service';
import moment from 'moment';
import { ThemeService } from 'ng2-charts';
declare var fixSVGColor: any;
declare var getWindowWidth: any;


@Component({
    selector: 'flight-chart',
    templateUrl: './flight-chart.component.html',
    styleUrls: ['./flight-chart.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class FlightChartComponent implements OnInit, AfterViewInit {

  brandedFareCheckBox = false;
  constructor(private searchResultService: SearchResultService,
    public userAccountInfoService: UserAccountService,
    private gallopLocalStorage: GallopLocalStorageService,
    public searchService: SearchService,
    private el: ElementRef,
    private deviceDetailsService: DeviceDetailsService,
    public translateService: TranslateService,
    private bookingService: BookingService,
    private connectionService: ConnectionService,
    private cdRef: ChangeDetectorRef,
    private filterService: FilterService,
    private modalService: BsModalService) {
  }

  @Input() flight: FlightResult;
  @Input() index: number;
  @Input() noOfPassengers: number;
  @Input() class: string;
  @Input() type: string;
  @Input() legType: string; //OUTGOING OR RETURN
  @Input() sortingType: string;
  @Input() algoType: string;
  @Output() bookRequest = new EventEmitter();
  disableButton = false;
  currScreenWidth = 1024;
  noOfFlightLegsSubscription: Subscription;
  requestForAllfaresSubscription: Subscription;
  creditDetails: any;
  creditApplied: any;
  noOfFlightLegs: number[] = [0];
  flightLayoverInfoList: any[] = [];
  isMobile: boolean;
  classOptions = Constants.CLASS_OPTIONS;
  deviceSubscription: Subscription;
  isExpanded: boolean = false;
  airlines;
  selectionOption = [];
  airports;
  cabinClasses;
  starsSeats = Constants.starsSeatArray;
  bsModalRef: BsModalRef;
  connectionListener: Subscription;
  bsModalMarketingRef: BsModalRef;
  marketingMsg = '';
  FlightClass: string = "ASS";
  screenWidth: number;
  BoxCount: number = 0;
  FlagforOnce: boolean = false;
  previousIndexArray=[];
  checkMultiFare: boolean = true;
  flightToBookEmit: FlightResult;
  indexArray=[];
  tempStarsArrayFlight=[];
  classArray = Constants.CLASS_NAMES_ARRAY; 
  starsArray=Constants.CLASS_starsArray;
  classDescArray = Constants.CLASS_NAMES_DESC_ARRAY;
  classWeightMap = {'Basic economy' : 0,'Economy' : 1,'Premium economy' : 2,'Business' : 3,'First class' : 4};
  getOtherFlightFareClass = [];
  autoOtherFareCallInProgress = false;
  scrollTimer;
  viewIsAlreadyDistroyed = false;
  fetchAllAttempted = false;
  @ViewChild('container') containerRef!: ElementRef;
  getElementsToIterFilled(count) {
    return [].constructor(count);
  }
  getElementsToIterEmpty(count) {
    return [].constructor(this.classDescArray.length -(count));
  }

  ngOnInit() {
    this.bookingService.pageMode ='';
    this.noOfFlightLegsSubscription = this.searchService.noOfFlightLegs$.subscribe((num) => {

    });
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
      this.searchService.isMobile = isMobile;
    });
    if(this.isMobile){
    this.mobileStarsArray();
    }
    this.indexArray = this.findIndexOfotherAttributArray(this.flight['otherFarePriceAttributes']);
    this.previousIndexArray = this.indexArray;
    this.airlines = JSON.parse(this.gallopLocalStorage.getItem('airlineNames'));
    this.airports = JSON.parse(this.gallopLocalStorage.getItem('airports'));
    this.cabinClasses = JSON.parse(this.gallopLocalStorage.getItem('cabinClassNames'));
    this.flightLayoverInfoList = FlightUtils.getLegWiseLayoverList(this.flight);

    if (this.legType !== 'OUTGOING') {
      this.brandedFareCheckBox = (this.searchService.getBrandedFareCheckBoxState()
        && this.searchService.getOutgoingSelectedFlight()
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails.length > 0);
    }
    let orgFlightFarePrice: FlightResult = JSON.parse(JSON.stringify(this.flight));
    if (this.flight['otherFarePriceAttributes'] && this.flight['otherFarePriceAttributes'].length !== 0) {
      // this.flight['otherFarePriceAttributes'].push(orgFlightFarePrice);
      this.checkMultiFare = false;
    }
    this.deviceSubscription = this.deviceDetailsService.currScreenWidth$.subscribe(screenWidth => {
      this.currScreenWidth = screenWidth;
    });

    //Quickfix workaround for credit icon issue
    //Ideally need to refactor a lot of code related residing in getpriceForMorefare and getPriceforotherfare
    this.getPriceWithOtherFareHandling(0);
    const thisObjRef = this;
    document.addEventListener("visibilitychange", function () {
      if (document.visibilityState === 'visible') {
        thisObjRef.detectChanges();
      }
    });

  }
  ngAfterViewInit() {
    if ( this.searchService.searchCallComplete) {
      this.handleWindowScroll();
    }
  }

  @HostListener('window:scroll')
  handleWindowScroll() {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }
    this.scrollTimer = setTimeout(() => {
      this.makeFetchAllRewquestIfVisible();
    }, 300);
  }
  isAnyBoxEmpty() {
    const emptyBox = this.indexArray.find(item => item === -1);
    if (emptyBox !== undefined) {
      return true;
    }
    return false;
  }
  makeFetchAllRewquestIfVisible() {
    if(this.isElementVisible()) {
      if ( !this.autoOtherFareCallInProgress
        && !this.viewIsAlreadyDistroyed
        && this.isAnyBoxEmpty()
      ) {
        this.makeDefaultFetchAllRequest();
      }
    }
  }
  isElementVisible(): boolean {
    const containerElement: HTMLElement = this.containerRef.nativeElement;
    const rect = containerElement.getBoundingClientRect() as DOMRect;
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.right <= ((window.innerWidth || document.documentElement.clientWidth) ) &&
      rect.bottom <= ((window.innerHeight || document.documentElement.clientHeight))
  );
  }
  makeDefaultFetchAllRequest() {
    this.fetchAllAttempted = true;
    if (!this.autoOtherFareCallInProgress
      && !this.flight['allUpsellFares']
      ) {
      this.autoOtherFareCallInProgress = true;
      this.detectChanges();
       return this.requestForAllfares(-1);
    }
  }

  getAllLayoverTimeHour(layover){
    let time=0;
   
    for(let item of layover){
      for(let item1 of item){
        time =  time + (item1.duration.hrs*60) + (item1.duration.mins);
      }

    }
    time = (time/60);
    return (~~time)+'h';
  }
  getAllLayoverTime(layover){
    let time=0; 
    for(let item of layover){
      for(let item1 of item){
        time = time+ (item1.duration.hrs*60)+(item1.duration.mins)
      }

    }
    time = (time % 60);
    return time;
  }
  getPaddingStyle(index){
    if(index >0){
      return {'padding-left':'15px'}
    }else{
      return {'padding-left':'5px'}
    }
  }
  findIndexOfotherAttributArray(otherFaresArray) {
    const indexTempArray=[];
    for(const item of this.classArray) {
      const findIndex = this.isOtherFareAvailable(item, otherFaresArray);
      indexTempArray.push(findIndex);
    }
    return indexTempArray;
  }
  afterStarArrayChange(index,index2){
    if(this.previousIndexArray[index]===index2){
      return {'background': '#fff'};
    }else   if(index2 !==-1  && this.previousIndexArray[index]===-1){
      setTimeout(() => {
      this.previousIndexArray = this.indexArray;
      this.detectChanges();
      }, 3000);
      return {'background': 'rgb(255,252,209)'};
    }
  }
  public getItemMinHeight() {
    if (this.currScreenWidth > 991) {
      return '228px';
    }
    if (this.currScreenWidth > 768) {
      return '239px';
    }
    if (this.currScreenWidth === 768) {
      return '128px';
    }
    if (this.currScreenWidth >= 688) {
      return '128px';;
    }
    if (this.currScreenWidth < 688) {
      return '128px';
    }

  }

  getflightLayoverInfoList(item, num, i) {
    let flightLayoverInfoLis = FlightUtils.getLegWiseLayoverList(item);
    if (flightLayoverInfoLis[num][i]) {
      return flightLayoverInfoLis[num][i]
    }
    return false;
  }
  ngOnChanges() {
    this.flightLayoverInfoList = FlightUtils.getLegWiseLayoverList(this.flight);
    // 
  }


  mobileSelect() {
    if (this.isMobile) {
      this.bookFlight();
    }
  }

  onCancel() {
    if (this.disableButton) {
      return;
    }
    this.bsModalRef.hide();
  }
  setFlightLegs(count) {
    // this.noOfFlightLegs = Array(count||1).fill(1).map((x, i)=>i)
  }

  onSelect() {

    this.bookRequest.emit({ 'flight': this.flightToBookEmit, 'index': this.index, 'type': this.type, 'brandedFareCheckBox': this.brandedFareCheckBox });
  }

  showFlightDetailModal(flightDetail) {

    if (this.searchService.seletedIndex === (this.searchService.processedFlightResults.length - 1)) {
      this.bsModalRef = this.modalService.show(flightDetail, { class: 'modalSearchFlightInfo' });
    }
  }

  getFlightDuration(num) {
    return FlightUtils.getFlightLegDuration(this.flight, num);
  }
  getFlightDurationForMoreOptions(item, num) {
    return FlightUtils.getFlightLegDuration(item, num);
  }

  getFlightIconURL(flightCode: string) {
    return CommonUtils.getAirlineImageUrl(flightCode);

    return this.searchResultService.getFlightIconURL(flightCode);
  }
  openMarketingMSgModal(item, modal) {
    if (item && item !== '') {
      this.marketingMsg = item.replace(/(?:\r\n|\r|\n)/g, '<br>');
      this.bsModalMarketingRef = this.modalService.show(DeleteCardModelComponent, {
        initialState: {
          title: this.translateService.instant('flightChart.Details'),
          // message: This payment method will not be displayed in your list of payment options',
          message: this.marketingMsg,
          flightChart: true
        }, backdrop: true, keyboard: false, ignoreBackdropClick: true
      });
    }
  }

  getPrice() {
    return this.getPriceWithOtherFareHandling(-1);
  }
  getOtherFarePrice(index, flight) {
    // return flight['otherFarePriceAttributes'][index].price
    return this.getPriceWithOtherFareHandling(index);
  }


  getPriceWithOtherFareHandling(otherFarePriceIndex: number) {
    let effectivePriceObject = this.flight;
    let upsellPriceDiff = 0;
    let selectedUpsellPrice = 0;
    if (this.userAccountInfoService.typeOfBooking === 'BOOK_FOR_SELF') {
      var creditDetails = CommonUtils.getCreditValue(effectivePriceObject, this.searchService.adultCount);
      this.creditDetails = JSON.parse(JSON.stringify(creditDetails));
      if(this.creditDetails && this.creditDetails.length >0){
        if (!this.searchService.selectedCreditAirline) {
          this.searchService.selectedCreditAirline = {};
        }
        this.searchService.selectedCreditAirline[effectivePriceObject.legs[0].flightHops[0].carrier]= true;
      }
    } else if (this.searchService.nonCombo && this.searchService.seletedIndex > 0 && this.searchService.nonComboSelectedFlight.length > 0 && this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === this.flight.legs[0].flightHops[0].carrier) {
      var creditDetails11 = CommonUtils.getCreditValue(this.searchService.nonComboSelectedFlight[0], this.searchService.adultCount);
      if (creditDetails11 && creditDetails11.length > 0) {
        let price = 0;
        for (let flight of this.searchService.nonComboSelectedFlight) {
          if (this.flight.displayPrice) {
            price += flight.displayPrice;
          } else {
            price += flight.price;
          }
        }
        price = CommonUtils.getPriceAfterTravelCredits(price, creditDetails11, this.searchService.adultCount,this.searchService.nonComboSelectedFlight[0]);
        this.creditApplied = CommonUtils.getAppliedTravelCredits(price, creditDetails11, this.searchService.adultCount);
        if (price === 0 && this.noOfPassengers === 1) {
          this.creditDetails = JSON.parse(JSON.stringify(creditDetails11));
        } else if (price > 0 && this.noOfPassengers === 1) {
          this.searchService.creditsFinished = true;
        }
        else if (this.noOfPassengers > 1) {
          if (this.flight.displayPrice) {
            let price = CommonUtils.getPriceAfterNonComboTravelCredits(this.flight.displayPrice, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
            this.creditApplied = CommonUtils.getAppliedCreditValueNonCombo(this.flight.displayPrice, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
            if (price !== this.flight.displayPrice) {
              this.creditDetails = JSON.parse(JSON.stringify(creditDetails11));
              this.searchService.creditsFinished = false;
            } else {
              this.searchService.creditsFinished = true;
            }
          } else {
            let price = CommonUtils.getPriceAfterNonComboTravelCredits(this.flight.price, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
            this.creditApplied = CommonUtils.getAppliedCreditValueNonCombo(this.flight.displayPrice, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
            if (price !== this.flight.price) {
              this.creditDetails = JSON.parse(JSON.stringify(creditDetails11));
              this.searchService.creditsFinished = false;
            } else {
              this.searchService.creditsFinished = true;
            }
          }
        }
      }
    }
    if (otherFarePriceIndex >= 0 && this.flight.otherFarePriceAttributes
      && this.flight.otherFarePriceAttributes[otherFarePriceIndex]) {
      effectivePriceObject = this.flight.otherFarePriceAttributes[otherFarePriceIndex];
    } else {
      effectivePriceObject = this.flight.otherFarePriceAttributes[0];
    }
    if (this.searchService.brandedFareCheckBox) {
      if (effectivePriceObject.legs[0].brandDetails
        && this.searchService.getOutgoingSelectedFlight()
        && effectivePriceObject.legs[0].brandDetails.length > 0) {
        upsellPriceDiff = effectivePriceObject.legs[0].brandDetails[0].priceDiff;
      }
      if (this.searchService.getOutgoingSelectedFlight()
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails.length > 0) {
        selectedUpsellPrice = this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails[0].priceDiff;
      }
    }
    if (effectivePriceObject.displayPrice) {
      if (this.searchService.nonCombo && this.searchService.seletedIndex > 0) {
        if (this.searchService.nonComboSelectedFlight.length > 0 && this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === this.flight.legs[0].flightHops[0].carrier && this.creditDetails && this.creditDetails.length > 0) {
          this.creditApplied = CommonUtils.getAppliedCreditValueNonCombo(this.flight.displayPrice, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
          return CommonUtils.getPriceAfterNonComboTravelCredits(this.flight.displayPrice, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
        } else {
          return effectivePriceObject.displayPrice;
        }
      } else if (this.creditDetails && this.creditDetails.length > 0) {
        if (effectivePriceObject.discountedPrice) {
          if (this.legType === 'OUTGOING') {
            this.creditApplied = CommonUtils.getAppliedTravelCredits(effectivePriceObject.discountedPrice, this.creditDetails, this.searchService.adultCount);
            return CommonUtils.getPriceAfterTravelCredits(effectivePriceObject.discountedPrice, this.creditDetails, this.searchService.adultCount,effectivePriceObject)

          } else {
            let price = (effectivePriceObject.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice);
            this.creditApplied = CommonUtils.getAppliedTravelCredits(price, this.creditDetails, this.noOfPassengers);
            return CommonUtils.getPriceAfterTravelCredits(price, this.creditDetails, this.noOfPassengers,effectivePriceObject)
          }

        } else {
          if (this.legType === 'OUTGOING') {
            this.creditApplied = CommonUtils.getAppliedTravelCredits(effectivePriceObject.displayPrice, this.creditDetails, this.searchService.adultCount);
            return CommonUtils.getPriceAfterTravelCredits(effectivePriceObject.displayPrice, this.creditDetails, this.searchService.adultCount,effectivePriceObject)

          } else {
            let price = (effectivePriceObject.displayPrice + upsellPriceDiff - selectedUpsellPrice);
            this.creditApplied = CommonUtils.getAppliedTravelCredits(price, this.creditDetails, this.searchService.adultCount);
            return CommonUtils.getPriceAfterTravelCredits(price, this.creditDetails, this.searchService.adultCount,effectivePriceObject)
          }
        }
      } else {

        if (effectivePriceObject.discountedPrice) {
          if (this.legType === 'OUTGOING') {
            if (creditDetails && (effectivePriceObject.discountedPrice > creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (effectivePriceObject.discountedPrice - creditDetails.creditAmount)
            } else if (creditDetails && (effectivePriceObject.discountedPrice <= creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (0);
            } else {
              return effectivePriceObject.discountedPrice;
            }
          } else {
            if (creditDetails && (effectivePriceObject.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice > creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (effectivePriceObject.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice - creditDetails.creditAmount)
            } else if (creditDetails && (effectivePriceObject.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice <= creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (0);
            } else {
              return effectivePriceObject.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice;
            }
          }
        } else {
          if (this.legType === 'OUTGOING') {
            if (creditDetails && (effectivePriceObject.price > creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (effectivePriceObject.displayPrice - creditDetails.creditAmount)
            } else if (creditDetails && (effectivePriceObject.displayPrice <= creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (0);
            } else {
              return effectivePriceObject.displayPrice
            }
          } else {
            if (creditDetails && (effectivePriceObject.displayPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price > creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (effectivePriceObject.displayPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price - creditDetails.creditAmount)
            } else if (creditDetails && (effectivePriceObject.displayPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price <= creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (0);
            } else {
              return effectivePriceObject.displayPrice + upsellPriceDiff - selectedUpsellPrice
            }
          }
        }
      }
    }
    else {
      if (this.searchService.nonCombo && this.searchService.seletedIndex > 0) {
        if (this.searchService.nonComboSelectedFlight.length > 0 && this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === this.flight.legs[0].flightHops[0].carrier && this.creditDetails && this.creditDetails.length > 0) {
          this.creditApplied = CommonUtils.getAppliedCreditValueNonCombo(this.flight.price, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
          return CommonUtils.getPriceAfterNonComboTravelCredits(this.flight.price, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
        } else {
          return this.flight.price;
        }
      } else if (this.creditDetails && this.creditDetails.length > 0) {
        if (effectivePriceObject.discountedPrice) {
          if (this.legType === 'OUTGOING') {
            this.creditApplied = CommonUtils.getAppliedTravelCredits(effectivePriceObject.discountedPrice, this.creditDetails, this.searchService.adultCount);
            return CommonUtils.getPriceAfterTravelCredits(effectivePriceObject.discountedPrice, this.creditDetails, this.searchService.adultCount,effectivePriceObject)

          } else {
            let price = (effectivePriceObject.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice);
            this.creditApplied = CommonUtils.getAppliedTravelCredits(price, this.creditDetails, this.searchService.adultCount);
            return CommonUtils.getPriceAfterTravelCredits(price, this.creditDetails, this.searchService.adultCount,effectivePriceObject)
          }

        } else {
          if (this.legType === 'OUTGOING') {
            this.creditApplied = CommonUtils.getAppliedTravelCredits(effectivePriceObject.price, this.creditDetails, this.searchService.adultCount);
            return CommonUtils.getPriceAfterTravelCredits(effectivePriceObject.price, this.creditDetails, this.searchService.adultCount,effectivePriceObject)

          } else {
            let price = (effectivePriceObject.price + upsellPriceDiff - selectedUpsellPrice);
            this.creditApplied = CommonUtils.getAppliedTravelCredits(price, this.creditDetails, this.searchService.adultCount);
            return CommonUtils.getPriceAfterTravelCredits(price, this.creditDetails, this.searchService.adultCount,effectivePriceObject)
          }
        }
      } else {

        if (effectivePriceObject.discountedPrice) {
          if (this.legType === 'OUTGOING') {
            if (creditDetails && (effectivePriceObject.discountedPrice > creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (effectivePriceObject.discountedPrice - creditDetails.creditAmount)
            } else if (creditDetails && (effectivePriceObject.discountedPrice <= creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (0);
            } else {
              return effectivePriceObject.discountedPrice;
            }
          } else {
            if (creditDetails && (effectivePriceObject.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice > creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (effectivePriceObject.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice - creditDetails.creditAmount)
            } else if (creditDetails && (effectivePriceObject.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice <= creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (0);
            } else {
              return effectivePriceObject.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice;
            }
          }
        } else {
          if (this.legType === 'OUTGOING') {
            if (creditDetails && (effectivePriceObject.price > creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (effectivePriceObject.price - creditDetails.creditAmount)
            } else if (creditDetails && (effectivePriceObject.price <= creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (0);
            } else {
              return effectivePriceObject.price
            }
          } else {
            if (creditDetails && (effectivePriceObject.price + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price > creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (effectivePriceObject.price + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price - creditDetails.creditAmount)
            } else if (creditDetails && (effectivePriceObject.price + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price <= creditDetails.creditAmount)) {
              this.creditApplied = creditDetails.creditAmount;
              return (0);
            } else {
              return effectivePriceObject.price + upsellPriceDiff - selectedUpsellPrice 
            }
          }
        }
      }
    }
  }
  getnoOfFlightLegs(item) {
    let noOflegs = [];
    var counter = 0;
    for (let item1 of item.legs) {
      noOflegs.push(counter);
      counter++;
    }
    return noOflegs;
  }
  getPriceForMoreOptions(item) {
    let upsellPriceDiff = 0;
    let selectedUpsellPrice = 0;
    if (this.userAccountInfoService.typeOfBooking === 'BOOK_FOR_SELF') {
      var creditDetails = CommonUtils.getCreditValue(item, this.searchService.adultCount);
      this.creditDetails = JSON.parse(JSON.stringify(creditDetails));
    } else if (this.searchService.nonCombo && this.searchService.seletedIndex > 0 && this.searchService.nonComboSelectedFlight.length > 0 && this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === item.legs[0].flightHops[0].carrier) {
      var creditDetails11 = CommonUtils.getCreditValue(this.searchService.nonComboSelectedFlight[0], this.searchService.adultCount);
      if (creditDetails11 && creditDetails11.length > 0) {
        let price = 0;
        for (let flight of this.searchService.nonComboSelectedFlight) {
          if (item.displayPrice) {
            price += flight.displayPrice;
          } else {
            price += flight.price;
          }
        }
        price = CommonUtils.getPriceAfterTravelCredits(price, creditDetails11, this.searchService.adultCount,item);
        if (price === 0 && this.noOfPassengers === 1) {
          this.creditDetails = JSON.parse(JSON.stringify(creditDetails11));
        } else if (price > 0 && this.noOfPassengers === 1) {
          this.searchService.creditsFinished = true;
        }
        else if (this.noOfPassengers > 1) {
          if (item.displayPrice) {
            let price = CommonUtils.getPriceAfterNonComboTravelCredits(item.displayPrice, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
            if (price !== item.displayPrice) {
              this.creditDetails = JSON.parse(JSON.stringify(creditDetails11));
              this.searchService.creditsFinished = false;
            } else {
              this.searchService.creditsFinished = true;
            }
          } else {
            let price = CommonUtils.getPriceAfterNonComboTravelCredits(item.price, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
            if (price !== item.price) {
              this.creditDetails = JSON.parse(JSON.stringify(creditDetails11));
              this.searchService.creditsFinished = false;
            } else {
              this.searchService.creditsFinished = true;
            }
          }
        }
      }
    }
    if (this.searchService.brandedFareCheckBox) {
      if (item.legs[0].brandDetails
        && this.searchService.getOutgoingSelectedFlight()
        && item.legs[0].brandDetails.length > 0) {
        upsellPriceDiff = item.legs[0].brandDetails[0].priceDiff;
      }
      if (this.searchService.getOutgoingSelectedFlight()
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails.length > 0) {
        selectedUpsellPrice = this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails[0].priceDiff;
      }
    }
    if (item.displayPrice) {
      if (this.searchService.nonCombo && this.searchService.seletedIndex > 0) {
        if (this.searchService.nonComboSelectedFlight.length > 0 && this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === item.legs[0].flightHops[0].carrier && this.creditDetails && this.creditDetails.length > 0) {
          return CommonUtils.getPriceAfterNonComboTravelCredits(item.displayPrice, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
        } else {
          return item.displayPrice;
        }
      } else if (this.creditDetails && this.creditDetails.length > 0) {
        if (item.discountedPrice) {
          if (this.legType === 'OUTGOING') {
            return CommonUtils.getPriceAfterTravelCredits(item.discountedPrice, this.creditDetails, this.searchService.adultCount,item)

          } else {
            let price = (item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice);
            return CommonUtils.getPriceAfterTravelCredits(price, this.creditDetails, this.searchService.adultCount,item)
          }

        } else {
          if (this.legType === 'OUTGOING') {
            return CommonUtils.getPriceAfterTravelCredits(item.displayPrice, this.creditDetails, this.searchService.adultCount,item)

          } else {
            let price = (item.displayPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice);
            return CommonUtils.getPriceAfterTravelCredits(price, this.creditDetails, this.searchService.adultCount,item)
          }
        }
      } else {

        if (item.discountedPrice) {
          if (this.legType === 'OUTGOING') {
            if (creditDetails && (item.discountedPrice > creditDetails.creditAmount)) {
              return (item.discountedPrice - creditDetails.creditAmount)
            } else if (creditDetails && (item.discountedPrice <= creditDetails.creditAmount)) {
              return (0);
            } else {
              return item.discountedPrice;
            }
          } else {
            if (creditDetails && (item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice > creditDetails.creditAmount)) {
              return (item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice - creditDetails.creditAmount)
            } else if (creditDetails && (item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice <= creditDetails.creditAmount)) {
              return (0);
            } else {
              return item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice;
            }
          }
        } else {
          if (this.legType === 'OUTGOING') {
            if (creditDetails && (item.price > creditDetails.creditAmount)) {
              return (item.price - creditDetails.creditAmount)
            } else if (creditDetails && (item.displayPrice <= creditDetails.creditAmount)) {
              return (0);
            } else {
              return item.displayPrice
            }
          } else {
            if (creditDetails && (item.displayPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price > creditDetails.creditAmount)) {
              return (item.displayPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price - creditDetails.creditAmount)
            } else if (creditDetails && (item.displayPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price <= creditDetails.creditAmount)) {
              return (0);
            } else {
              return item.displayPrice + upsellPriceDiff - selectedUpsellPrice 
            }
          }
        }
      }

    } else {
      if (this.searchService.nonCombo && this.searchService.seletedIndex > 0) {
        if (this.searchService.nonComboSelectedFlight.length > 0 && this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === item.legs[0].flightHops[0].carrier && this.creditDetails && this.creditDetails.length > 0) {
          return CommonUtils.getPriceAfterNonComboTravelCredits(item.price, this.searchService.adultCount, this.searchService.nonComboSelectedFlight);
        } else {
          return item.price;
        }
      } else if (this.creditDetails && this.creditDetails.length > 0) {
        if (item.discountedPrice) {
          if (this.legType === 'OUTGOING') {
            return CommonUtils.getPriceAfterTravelCredits(item.discountedPrice, this.creditDetails, this.searchService.adultCount,item)

          } else {
            let price = (item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice);
            return CommonUtils.getPriceAfterTravelCredits(price, this.creditDetails, this.searchService.adultCount,item)
          }

        } else {
          if (this.legType === 'OUTGOING') {
            return CommonUtils.getPriceAfterTravelCredits(item.price, this.creditDetails, this.searchService.adultCount,item)

          } else {
            let price = (item.price + upsellPriceDiff - selectedUpsellPrice);
            return CommonUtils.getPriceAfterTravelCredits(price, this.creditDetails, this.searchService.adultCount,item)
          }
        }
      } else {

        if (item.discountedPrice) {
          if (this.legType === 'OUTGOING') {
            if (creditDetails && (item.discountedPrice > creditDetails.creditAmount)) {
              return (item.discountedPrice - creditDetails.creditAmount)
            } else if (creditDetails && (item.discountedPrice <= creditDetails.creditAmount)) {
              return (0);
            } else {
              return item.discountedPrice;
            }
          } else {
            if (creditDetails && (item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice > creditDetails.creditAmount)) {
              return (item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice - creditDetails.creditAmount)
            } else if (creditDetails && (item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice <= creditDetails.creditAmount)) {
              return (0);
            } else {
              return item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice;
            }
          }
        } else {
          if (this.legType === 'OUTGOING') {
            if (creditDetails && (item.price > creditDetails.creditAmount)) {
              return (item.price - creditDetails.creditAmount)
            } else if (creditDetails && (item.price <= creditDetails.creditAmount)) {
              return (0);
            } else {
              return item.price
            }
          } else {
            if (creditDetails && (item.price + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price > creditDetails.creditAmount)) {
              return (item.price + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price - creditDetails.creditAmount)
            } else if (creditDetails && (item.price + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().price <= creditDetails.creditAmount)) {
              return (0);
            } else {
              return item.price + upsellPriceDiff - selectedUpsellPrice 
            }
          }
        }
      }
    }
}
  getTraveleCreditersName() {
    var creditDetails = CommonUtils.getCreditValue(this.flight, this.searchService.adultCount);
    return CommonUtils.getCreditersName(creditDetails, this.searchService.employeeList);
  }
  getStyleColumn(option, j) {
    if (!this.isMobile) {
      if (getWindowWidth() > 1100) {
        if (option === 1 && option % 2 === 1 && j % 2 === 0) {
          return { 'padding-left': '8px', 'padding-right': '8px', 'border-left': '2px solid #E3E3E3', 'min-width': '100%' }
        } else if ((option - 1) === j && option % 2 === 1 && j % 2 === 0) {
          return { 'padding-left': '0px', 'padding-right': '8px', 'border-left': '2px solid #E3E3E3', 'min-width': '100%' }
        } else if ((option - 1) === j && j % 2 === 1) {
          return { 'padding-left': '0px', 'padding-right': '8px', 'border-right': 'none', 'min-width': '100%' }
        } else if (j % 2 === 1) {
          return { 'padding-left': '0px', 'padding-right': '0px', 'border-right': 'none', 'min-width': '100%' }
        }
        else {
          return { 'padding-right': '0px', 'border-right': '2px solid #E3E3E3', 'padding-left': '8px', 'min-width': '100%' }
        }
      } else if (getWindowWidth() > 1100 && (option - 1) === j && option % 2 === 1 && j % 2 === 0) {
        return { 'padding-left': '8px', 'padding-right': '8px', 'border-right': 'none', 'min-width': '100%' }
      } else if (j % 2 === 1) {
        return { 'padding-left': '0px', 'padding-right': '8px', 'border-right': 'none', 'min-width': '100%' }
      } else {
        return { 'padding-right': '0px', 'border-right': '2px solid #E3E3E3', 'padding-left': '8px', 'min-width': '100%' }
      }

    } else {
      return { 'padding-left': '8px', 'padding-right': '8px', 'border-right': 'none', 'min-width': '100%' }
    }
  }
  inMobileselectMoreOptions(modal) {
    if (this.isMobile) {
      this.selectMoreOptions(modal);
    } else {
      return;
    }
  }
  getPriceForMoreOptions1(item) {
    let upsellPriceDiff = 0;
    let selectedUpsellPrice = 0;
    if (this.searchService.brandedFareCheckBox) {
      if (item.legs[0].brandDetails
        && this.searchService.getOutgoingSelectedFlight()
        && item.legs[0].brandDetails.length > 0) {
        upsellPriceDiff = item.legs[0].brandDetails[0].priceDiff;
      }
      if (this.searchService.getOutgoingSelectedFlight()
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails.length > 0) {
        selectedUpsellPrice = this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails[0].priceDiff;
      }
    }
    if (item.displayPrice) {
      if (this.searchService.nonCombo) {
        //  if(this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === this.flight.legs[0].flightHops[0].carrier && this.creditDetails && this.creditDetails.length > 0){
        let price = 0
        //for(let flight of this.searchService.nonComboSelectedFlight){
        price = item.displayPrice;
        // }
        return price;
        //  }
      } else if (item.discountedPrice) {
        if (this.legType === 'OUTGOING') {
          return item.discountedPrice;
        } else {
          return item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice;
        }
      } else {
        if (this.legType === 'OUTGOING') {
          return item.displayPrice;
        } else {
          if (this.searchService.nonCombo) {
            return item.displayPrice;
          } else {
            return item.displayPrice + upsellPriceDiff - selectedUpsellPrice
          }
        }
      }
    } else {
      if (this.searchService.nonCombo) {
        //  if(this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === this.flight.legs[0].flightHops[0].carrier && this.creditDetails && this.creditDetails.length > 0){
        let price = 0
        //for(let flight of this.searchService.nonComboSelectedFlight){
        price = item.price;
        // }
        return price;
        //  }
      } else if (item.discountedPrice) {
        if (this.legType === 'OUTGOING') {
          return item.discountedPrice;
        } else {
          return item.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice;
        }
      } else {
        if (this.legType === 'OUTGOING') {
          return item.price;
        } else {
          if (this.searchService.nonCombo) {
            return item.price;
          } else {
            return item.price + upsellPriceDiff - selectedUpsellPrice
          }
        }
      }
    }
  }
  getPrice1() {
    let upsellPriceDiff = 0;
    let selectedUpsellPrice = 0;
    if (this.searchService.brandedFareCheckBox) {
      if (this.flight.legs[0].brandDetails
        && this.searchService.getOutgoingSelectedFlight()
        && this.flight.legs[0].brandDetails.length > 0) {
        upsellPriceDiff = this.flight.legs[0].brandDetails[0].priceDiff;
      }
      if (this.searchService.getOutgoingSelectedFlight()
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails.length > 0) {
        selectedUpsellPrice = this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails[0].priceDiff;
      }
    }
    if (this.flight.displayPrice) {
      if (this.searchService.nonCombo) {
        //  if(this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === this.flight.legs[0].flightHops[0].carrier && this.creditDetails && this.creditDetails.length > 0){
        let price = 0
        //for(let flight of this.searchService.nonComboSelectedFlight){
        price = this.flight.displayPrice;
        // }
        return (price);
        //  }
      } else if (this.flight.discountedPrice) {
        if (this.legType === 'OUTGOING') {
          return this.flight.discountedPrice;
        } else {
          return this.flight.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice;
        }
      } else {
        if (this.legType === 'OUTGOING') {
          return this.flight.displayPrice;
        } else {
          if (this.searchService.nonCombo) {
            return this.flight.displayPrice;
          } else {
            return this.flight.displayPrice + upsellPriceDiff - selectedUpsellPrice 
          }
        }
      }
    } else {
      if (this.searchService.nonCombo) {
        //  if(this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier === this.flight.legs[0].flightHops[0].carrier && this.creditDetails && this.creditDetails.length > 0){
        let price = 0
        //for(let flight of this.searchService.nonComboSelectedFlight){
        price = this.flight.price;
        // }
        return (price);
        //  }
      } else if (this.flight.discountedPrice) {
        if (this.legType === 'OUTGOING') {
          return this.flight.discountedPrice;
        } else {
          return this.flight.discountedPrice + upsellPriceDiff - selectedUpsellPrice - this.searchService.getOutgoingSelectedFlight().discountedPrice;
        }
      } else {
        if (this.legType === 'OUTGOING') {
          return this.flight.price;
        } else {
          if (this.searchService.nonCombo) {
            return this.flight.price;
          } else {
            return this.flight.price + upsellPriceDiff - selectedUpsellPrice
          }
        }
      }
    }
  }

  getBrandFareText() {
    if (this.legType === 'OUTGOING') {
      if (this.flight.legs[0].brandDetails
        && this.flight.legs[0].brandDetails.length > 0
        && this.flight.legs[0].brandDetails[0] != null
      ) {
        var signString = '+';
        if (this.flight.legs[0].brandDetails[0].priceDiff < 0) {
          signString = '-';
        }
        return this.flight.legs[0].brandDetails[0].name
          + ': ' + signString + this.getCurrencySymbol(this.flight)
          + Math.round(Math.abs(this.flight.legs[0].brandDetails[0].priceDiff));
      }
    } else {
      if (this.searchService.getBrandedFareCheckBoxState()) {
        let brandLabelText = this.getBrandFareLabel();
        if (brandLabelText === null) {
          return 'No Brand';
        }
        return brandLabelText;
      }

    }
    return null;
  }
  showBrandedFareCheckBox() {
    return this.legType === 'OUTGOING';
  }
  getBrandFareLabel() {
    if (this.flight.legs[0].brandDetails
      && this.flight.legs[0].brandDetails.length > 0
      && this.flight.legs[0].brandDetails[0] != null
    ) {
      return this.flight.legs[0].brandDetails[0].name;
    }
    return null;
  }
  getBrandFareValue() {
    if (this.flight.legs[0].brandDetails
      && this.flight.legs[0].brandDetails.length > 0
      && this.flight.legs[0].brandDetails[0] != null
    ) {
      var signString = '+';
      if (this.flight.legs[0].brandDetails[0].priceDiff < 0) {
        signString = '-';
      }
      return signString + this.getCurrencySymbol(this.flight)
        + Math.round(Math.abs(this.flight.legs[0].brandDetails[0].priceDiff));
    }
    return null;
  }




  onBrandedFareClick(e) {


  }
  getNoOfHops(flightLegIndex: number): number {
    if (this.flight.legs[flightLegIndex].flightHops) {
      return this.flight.legs[flightLegIndex].flightHops.length;
    }
  }
  getNoOfHopsForMoreOptions(item, flightLegIndex: number): number {
    if (item.legs[flightLegIndex].flightHops) {
      return item.legs[flightLegIndex].flightHops.length;
    }
  }


  getFlightLegSource(flightLegIndex: number): string {
    if (this.flight && this.flight.legs[flightLegIndex]) {
      let from = this.flight.legs[flightLegIndex].flightHops[0].from;
      return from;
      // return from + ', ' + this.searchService.getAirportName(from);
    }
  }
  getFlightLegSourceForMultipleOptions(item, flightLegIndex: number): string {
    if (item && item.legs[flightLegIndex]) {
      let from = item.legs[flightLegIndex].flightHops[0].from;
      return from;
      // return from + ', ' + this.searchService.getAirportName(from);
    }
  }
  getFlightLegDestinationForOptions(item, flightLegIndex: number): string {

    if (item && item.legs[flightLegIndex]) {
      let noOfHops = this.getNoOfHopsForMoreOptions(item, flightLegIndex);
      let to = item.legs[flightLegIndex].flightHops[noOfHops - 1].to;
      return to;
      //return to + ', ' + this.searchService.getAirportName(to);
    }
  }
  getFlightLegDestination(flightLegIndex: number): string {

    if (this.flight && this.flight.legs[flightLegIndex]) {
      let noOfHops = this.getNoOfHops(flightLegIndex);
      let to = this.flight.legs[flightLegIndex].flightHops[noOfHops - 1].to;
      return to;
      //return to + ', ' + this.searchService.getAirportName(to);
    }
  }

  getFlightLegConstraint(flightLegIndex: number): string {

    if (flightLegIndex == 0) {
      return this.searchService.previousSearch.constraint;
    }
    return this.searchService.previousSearch.flights[flightLegIndex - 1].constraint
  }

  isFlightLegDepartureBased(flightLegIndex: number): boolean {
    return this.getFlightLegConstraint(flightLegIndex) == IntervalType.DEPARTURE_BASED ? true : false;
  }

  isSelectedFlight(): boolean {
    return (this.index == this.searchResultService.selectedFlightSearchIndex) ? true : false;
  }
  showloader = false;
  newFlightCountZero = false;
  selectMoreOptionsFromIcon(modal, modal2) {
    if (true) {
      this.selectMoreOptions(modal);
    } else {
      this.bsModalRef = this.modalService.show(modal2);
    }
  }
  getColConditionOnMoreFarePopup() {
    return (getWindowWidth() >= 1100);
  }

  getCol6ConditionOnMoreFarePopup() {
    if (getWindowWidth() >= 1100) {
      return false;
    }
    return (this.selectionOption && this.selectionOption.length > 1);
  }
  getCol12ConditionOnMoreFarePopup(j) {
    if (getWindowWidth() >= 1100) {
      return false;
    }
    return (this.selectionOption
      && (
        (((this.selectionOption.length) % 2 === 1 && j % 2 === 0 && ((this.selectionOption.length - 1) === j)))
        || this.selectionOption.length === 1
        || this.isMobile));
  }

  selectMoreOptions(modal) {
    let proceedToBookFlight = this.selectMoreOptionsWithMultiFares(-1, modal);
    if (proceedToBookFlight == "yes") {
      this.bookFlightOtherFarePrice(-1);
    }
  }

  selectMoreOptionsWithMultiFares(otherFarePriceIndex, modal) {
    let proceedToBookFlight = "no";
    if (true) {
      if (this.requestForAllfaresSubscription) {
        this.showloader = false;
        this.detectChanges();
        this.requestForAllfaresSubscription.unsubscribe();
      }
      this.disableButton = false;
      this.newFlightCountZero = false;
      let modalWindowClass = 'app-modal-window1';
      if (getWindowWidth() < 730) {
        modalWindowClass = 'app-modal-window1';
      } else if (getWindowWidth() < 1100) {
        modalWindowClass = 'app-modal-window2';
      } else {
        modalWindowClass = 'app-modal-window3';
      }

      if (this.searchService.nonCombo || this.userAccountInfoService.showUpSellOnAllLegs) {
        this.selectionOption = [];
        // this.selectionOption.push(this.flight);
        if (otherFarePriceIndex == -2) {
          // this.bsModalRef = this.modalService.show(modal, { class: modalWindowClass,initialState: {
          // }, backdrop: true, ignoreBackdropClick: true });

        }
        this.showloader = true;
        this.requestForAllfares(otherFarePriceIndex)
      } else if (!this.searchService.nonCombo && this.flight && this.flight.parentFlightId && !this.flight.childFlightId) {
        this.selectionOption = [];
        // this.selectionOption.push(this.flight);
        if (otherFarePriceIndex == -2) {
          // this.bsModalRef = this.modalService.show(modal, { class: modalWindowClass,initialState: {
          // }, backdrop: true, ignoreBackdropClick: true });
          // 
        }
        this.showloader = true;
        this.requestForAllfares(otherFarePriceIndex)
      } else if (!this.searchService.nonCombo && this.flight && !this.flight.parentFlightId && !this.flight.childFlightId) {
        this.selectionOption = [];
        // this.selectionOption.push(this.flight);
        if (otherFarePriceIndex == -2) {
          this.bsModalRef = this.modalService.show(modal, {
            class: modalWindowClass, initialState: {
            }, backdrop: true, ignoreBackdropClick: true
          });
          this.showloader = true;
        }
        this.requestForAllfares(otherFarePriceIndex)
      } else {
        // this.bookFlight();
        // this.bookFlightOtherFarePrice(otherFarePriceIndex)
        proceedToBookFlight = "yes";
      }
    } else {
      // this.bookFlight();
      // this.bookFlightOtherFarePrice(otherFarePriceIndex)
      proceedToBookFlight = "yes";
    }
    return proceedToBookFlight;
  }
checkPolicy(item){
  if(this.showWithinPolicy() && this.isWithinPolicy(item) == false){
    return {'margin-right':'8px'};
  }else{
    return {'margin-right':'8px'};
  }
}
residualAmountCalculation(price,creditDetails1,passennger){
  const pricePerPassenger = price / passennger;
  let effectivePrice = price;
    let residualPrice;
  // 
 
   for (let counter = 0; counter < creditDetails1.length; counter++) {
     const item = creditDetails1[counter];
     if(item.displayCreditAmount){
       if (item.displayCreditAmount <= pricePerPassenger) {
         effectivePrice -= item.displayCreditAmount;
       }else{
         effectivePrice -= pricePerPassenger;
       }
       residualPrice = item.displayCreditAmount - pricePerPassenger;
     } else {
      if (item.creditAmount <= pricePerPassenger) {
        effectivePrice -= item.creditAmount;
      } else {
        effectivePrice -= pricePerPassenger;
      }
      residualPrice = item.creditAmount - pricePerPassenger;
    }
     if(residualPrice > item.maxResidual){
       effectivePrice = price;
     }
   }
   return effectivePrice;
}
isResidualAmountIsGreaterTahnMaxresidualAmount(item){
  if(item.travelCreditsInfo && item.travelCreditsInfo.length > 0){
    let price  = this.residualAmountCalculation(item.price,item.travelCreditsInfo,this.noOfPassengers);
    if(price < item.price){
      return true;
    }else{
      return false;
    }
  }

}
isResidualAmountIsGreaterTahnMaxresidualAmountForLogo(){
  if(this.creditDetails && this.creditDetails.length > 0){
    for(let item of this.flight.otherFarePriceAttributes){
    let price  = this.residualAmountCalculation(item.price,this.creditDetails,this.noOfPassengers);
    if(item.travelCreditsInfo && item.travelCreditsInfo.length > 0 && price < item.price){
      return true;
    }
  }
  }
}

showCreditAppliedIcon() {
  return (this.creditDetails && this.creditDetails.length >0 && this.isResidualAmountIsGreaterTahnMaxresidualAmountForLogo());
}
showCreditNotAppliedReasonLink() {
  if (this.searchService.selectedCreditAirline
    && this.searchService.selectedCreditAirline[this.flight.legs[0].flightHops[0].carrier] === true) {
      return (!this.showCreditAppliedIcon());
  }
  return false;
}

removeTravelCredits(){
  let selectedFlights = JSON.parse(JSON.stringify(this.searchService.multipleOutgoingSelectedFlight));
  //this.selectedFlights.splice(0,1);
  for(let item of selectedFlights){
       if(item.travelCreditsInfo){
        delete item.travelCreditsInfo;
       }
  }
  for(let item of this.searchService.tempFlight){
    if(item.travelCreditsInfo){
     delete item.travelCreditsInfo;
    }
}

  this.searchService.multipleOutgoingSelectedFlight =selectedFlights;
}
  selectFlight(j) {
    this.disableButton = true;
    if(this.isMobile){
      this.selectionOption[j] = JSON.parse(JSON.stringify(this.tempStarsArrayFlight[j]));

    }
    this.flight.price = JSON.parse(JSON.stringify(this.selectionOption[j].price));
   

    if (this.searchService.nonCombo) {
      this.flight.legs = this.selectionOption[j].legs;
    } else if (this.selectionOption[j].legs.length === 1) {
      this.flight.legs = this.selectionOption[j].legs;
    }
    this.flight.fareBreakup = this.selectionOption[j].fareBreakup;
    this.flight.fareAttributes = this.selectionOption[j].fareAttributes;
    this.flight.appliedAdditonalFees = this.selectionOption[j].appliedAdditonalFees;
    if(this.selectionOption[j].fareWithAccountCode){
      this.flight.fareWithAccountCode = this.selectionOption[j].fareWithAccountCode;
    }
    this.flight.handlerType = this.selectionOption[j].handlerType;
  
    if ( !this.searchService.comboOutGoingSelectedFlight ){

      this.searchService.comboOutGoingSelectedFlight = JSON.parse(JSON.stringify(this.flight));
      if(this.selectionOption[j].ngsStars && parseInt(this.selectionOption[j].ngsStars)===1){
        if(this.searchService.comboOutGoingSelectedFlight.travelCreditsInfo){
          delete this.searchService.comboOutGoingSelectedFlight.travelCreditsInfo;
        }
        if(this.selectionOption[j].travelCreditsInfo){
          delete this.selectionOption[j].travelCreditsInfo;
        }
       
              }else{
                if(parseInt(this.selectionOption[j].ngsStars) > 1 && !this.searchService.comboOutGoingSelectedFlight.travelCreditsInfo && this.selectionOption[j].travelCreditsInfo){
                  this.searchService.comboOutGoingSelectedFlight.travelCreditsInfo = this.selectionOption[j].travelCreditsInfo;
               }
              }
    } else {
      this.searchService.comboOutGoingSelectedFlight.legs = this.selectionOption[j].legs;
      this.searchService.comboOutGoingSelectedFlight.handlerType = this.selectionOption[j].handlerType;
      this.searchService.comboOutGoingSelectedFlight.fareBreakup = this.selectionOption[j].fareBreakup;
      this.searchService.comboOutGoingSelectedFlight.appliedAdditonalFees = this.selectionOption[j].appliedAdditonalFees;
      if(this.selectionOption[j].fareWithAccountCode){
        this.searchService.comboOutGoingSelectedFlight.fareWithAccountCode = this.selectionOption[j].fareWithAccountCode;
      }
      this.searchService.comboOutGoingSelectedFlight.fareAttributes = this.selectionOption[j].fareAttributes;
      this.searchService.comboOutGoingSelectedFlight.luggageOptions = this.flight.luggageOptions;
      if(!this.selectionOption[j].travelCreditsInfo){
if(this.searchService.comboOutGoingSelectedFlight.travelCreditsInfo){
  delete this.searchService.comboOutGoingSelectedFlight.travelCreditsInfo;
}
      }
      if(this.selectionOption[j].ngsStars && parseInt(this.selectionOption[j].ngsStars)===1 || !this.isResidualAmountIsGreaterTahnMaxresidualAmount(this.selectionOption[j])){
        if(this.searchService.comboOutGoingSelectedFlight.travelCreditsInfo){
          delete this.searchService.comboOutGoingSelectedFlight.travelCreditsInfo;
        }
        this.removeTravelCredits();
       
        
              }else{
                if(parseInt(this.selectionOption[j].ngsStars) > 1 && !this.searchService.comboOutGoingSelectedFlight.travelCreditsInfo && this.selectionOption[j].travelCreditsInfo){
                   this.searchService.comboOutGoingSelectedFlight.travelCreditsInfo = this.selectionOption[j].travelCreditsInfo;
                }
              }
      if (this.flight.displayPrice) {
        this.searchService.comboOutGoingSelectedFlight.displayPrice = JSON.parse(JSON.stringify(this.selectionOption[j].displayPrice));
      }
      this.searchService.comboOutGoingSelectedFlight.returnLuggageOptions = this.flight.returnLuggageOptions;
    }
    this.flight.legs[0] = this.selectionOption[j].legs[this.searchService.seletedIndex];

    // if (!this.searchService.nonCombo && this.flight && this.flight.parentFlightId && this.showloader) {
    //   this.searchService.comboOutGoingSelectedFlight.legs[this.searchService.seletedIndex] = this.selectionOption[j].legs[0];
    //   if (this.requestForAllfaresSubscription) {
    //     this.requestForAllfaresSubscription.unsubscribe();
    //   }
    // } else if (!this.searchService.nonCombo && this.flight && this.flight.parentFlightId && this.newFlightCountZero) {
    //   this.searchService.comboOutGoingSelectedFlight.legs[this.searchService.seletedIndex] = this.selectionOption[j].legs[this.searchService.seletedIndex];
    //   this.searchService.comboOutGoingSelectedFlight.luggageOptions = this.flight.luggageOptions;
    //   this.searchService.comboOutGoingSelectedFlight.returnLuggageOptions = this.flight.returnLuggageOptions;
    // } else if (!this.searchService.nonCombo && this.flight && this.flight.parentFlightId) {
    //   this.searchService.comboOutGoingSelectedFlight.legs[this.searchService.seletedIndex] = this.selectionOption[j].legs[this.searchService.seletedIndex];
    //   this.searchService.comboOutGoingSelectedFlight.luggageOptions = this.flight.luggageOptions;
    //   this.searchService.comboOutGoingSelectedFlight.returnLuggageOptions = this.flight.returnLuggageOptions;
    // } else {
    //   this.flight.legs[this.searchService.seletedIndex] = this.selectionOption[j].legs[this.searchService.seletedIndex];
    //   this.searchService.comboOutGoingSelectedFlight =  this.flight;
    // }
    if (this.flight.displayPrice) {
      this.flight.displayPrice = JSON.parse(JSON.stringify(this.selectionOption[j].displayPrice));
    }
    let flight =  JSON.parse(JSON.stringify(this.flight));
    if(this.selectionOption[j].ngsStars && parseInt(this.selectionOption[j].ngsStars)===1 || !this.isResidualAmountIsGreaterTahnMaxresidualAmount(flight)){
      if(flight.travelCreditsInfo){
        delete flight.travelCreditsInfo;
      }
       this.flight = JSON.parse(JSON.stringify(flight));
    }
    this.bookFlight();
  }
  makeKeyByFareClassName(option) {
    let key ='';
    for(let legCounter = 0; legCounter < option.legs.length; legCounter++) {
      for(let hopCounter = 0; hopCounter < option.legs[legCounter].flightHops.length; hopCounter++){
        key += option.legs[legCounter].flightHops[hopCounter].fareClassName;
      }
    }
    return key;
  }
  matchOption(option1, option2) {
    const key1 = this.makeKeyByFareClassName(option1);
    const key2 = this.makeKeyByFareClassName(option2);
    if (key1.toLowerCase() === key2.toLowerCase()){
      return true;
    }
    return false;
  }
  handleAllFareResponseData(allFareFlights, selectedFlight){
    if (allFareFlights && allFareFlights.length > 0) {
      const newOtherFaresMerged = [];
      for(const otheFareItem of allFareFlights) {
        const findIndex = newOtherFaresMerged.findIndex(item => this.matchOption(item, otheFareItem));
        if (findIndex === -1) {
          newOtherFaresMerged.push(otheFareItem);
        }
      }
      for(const otheFareItem of this.flight.otherFarePriceAttributes) {
        const findIndex = newOtherFaresMerged.findIndex(item => this.matchOption(item, otheFareItem));
        if (findIndex === -1) {
          newOtherFaresMerged.push(otheFareItem);
        }
      }
      this.sortListPrice(newOtherFaresMerged);
      this.flight['otherFarePriceAttributes'] = newOtherFaresMerged;
      this.indexArray = this.findIndexOfotherAttributArray(this.flight['otherFarePriceAttributes']);
      if(!this.isMobile) {
      this.OtherFlightBoxIndex = this.indexArray[selectedFlight.ngsStars - 1];
      const relevantUpsellOptions = [];
      const currOptoionStarsHop = CommonUtils.getHopForMaxStar(selectedFlight, this.searchService);
      if (currOptoionStarsHop) {
          for(const item of newOtherFaresMerged) {
          const hop = CommonUtils.getHopForMaxStar(item, this.searchService);
          if (hop) {
            if (hop.ngsStars === currOptoionStarsHop.ngsStars && item.fareBreakup.totalPrice.toFixed(2) >= selectedFlight.fareBreakup.totalPrice.toFixed(2)) {
              relevantUpsellOptions.push(item);
           }
          }
        }
        this.selectionOption = relevantUpsellOptions;
        if (relevantUpsellOptions.length > 0){
          this.flight.otherFarePriceAttributes[this.indexArray[currOptoionStarsHop.ngsStars - 1]] = relevantUpsellOptions[0];
        }
      }
    }
    }else if(!allFareFlights){
      this.flight['allUpsellFares'] =selectedFlight;
    }
    if (this.selectionOption.length === 0) {
      this.flight['allUpsellFares'] =selectedFlight;
      this.selectionOption.push(selectedFlight);
      this.newFlightCountZero = true;
    }
    if(this.selectionOption.length > 0){
      this.selectionOption = this.selectionOption.sort(function (a, b) {
        if (a.price < b.price) { return -1; }
        else if (a.price > b.price) { return 1; }
        return 0;
      });
      this.selectionOption= this.selectionOption.filter((test, index, array) =>
     index === array.findIndex((findTest) =>
        ((findTest.ngsStars === test.ngsStars &&  findTest.maxNgsStarsFareName  === test.maxNgsStarsFareName)
          ||
          (findTest.handlerType !== test.handlerType)
        )
     )
  );
    }
    if(this.isMobile){
      this.mobileStarsArray();
      }
  }
  mobileStarsArray() {
    const fareTypesInOtherfares = {};
    const fareTypesInAllUpSellFares = {};
    for(let looper = 0; looper < this.flight.otherFarePriceAttributes.length; looper++) {
      if (!(fareTypesInOtherfares[this.flight.otherFarePriceAttributes[looper].ngsStars])) {
        fareTypesInOtherfares[this.flight.otherFarePriceAttributes[looper].ngsStars] = looper;
      }
    }

    this.tempStarsArrayFlight = JSON.parse(JSON.stringify(this.flight.otherFarePriceAttributes));
  


    if(this.flight.travelCreditsInfo){
      for(const item of this.tempStarsArrayFlight) {
        if(this.flight.travelCreditsInfo){
        item['travelCreditsInfo'] = this.flight.travelCreditsInfo;
       }
      }
    }
    if(this.flight['allUpsellFares'] && this.flight['allUpsellFares'].length >0 ) {
      for(const item of this.flight['allUpsellFares']) {
        if(this.flight.travelCreditsInfo){
          item['travelCreditsInfo'] = this.flight.travelCreditsInfo;
        }
        const fareIndex = this.tempStarsArrayFlight
                  .findIndex((tempItem) => (tempItem.fareBreakup.price === item.fareBreakup.price));
        if (fareIndex === -1) {
          this.tempStarsArrayFlight.push(item);
        }
      }
    }

    let index=0;
    for (let item of this.tempStarsArrayFlight) {
      const hop = CommonUtils.getHopForMaxStar(item, this.searchService);
      if (hop) {
        if (hop.ngsStars) {
          this.tempStarsArrayFlight[index]['ngsStars'] = hop.ngsStars;
          this.tempStarsArrayFlight[index]['showFeatureInmobile']= false;
        }
      }
      index++;
    }
    this.tempStarsArrayFlight = this.sortList(this.tempStarsArrayFlight);
    let prevPrice = -1;
    const localTemp = [];
    for (const item of this.tempStarsArrayFlight) {
      if (prevPrice !== -1) {
        if (item.fareBreakup.price !== prevPrice) {
          localTemp.push(item);
        }
      } else {
        localTemp.push(item);
      }
      prevPrice = item.fareBreakup.price;
    }
    this.tempStarsArrayFlight = localTemp;
     if(this.tempStarsArrayFlight.length > 0){
      this.tempStarsArrayFlight= this.tempStarsArrayFlight.filter((test, index, array) =>
     index === array.findIndex((findTest) =>
        ((findTest.ngsStars === test.ngsStars &&  findTest.fareBreakup.price === test.fareBreakup.price)
        )
     )
  );
    }
  }
  sortList(data) {
    data.sort(function (a, b) {
      // 
       if (a.ngsStars < b.ngsStars) { return -1; }
      else if (a.ngsStars > b.ngsStars) { return 1; }
      return 0;
    })
    return data;
  }
  sortListPrice(data) {
    data.sort(function (a, b) {
      // 
       if (a.price < b.price) { return -1; }
      else if (a.price > b.price) { return 1; }
      return 0;
    })
    return data;
  }
  requestForAllfares(otherFarePriceIndex: number) {
    const selectedFlight: FlightResult = this.getFlightObjectWithSelected(otherFarePriceIndex);
    if (this.flight['allUpsellFares']) {
      this.handleAllFareResponseData(this.flight['allUpsellFares'], selectedFlight);
      this.showloader = false;
      this.detectChanges();
      this.autoOtherFareCallInProgress = false;
      return false;
    }
    //if (this.userAccountInfoService.isConnected) {
      this.showloader = true;
      this.detectChanges();
      let flights;
      if (this.searchService.nonCombo) {
        flights = selectedFlight;
      } else if (!this.searchService.nonCombo && this.flight
        && (this.userAccountInfoService.showUpSellOnAllLegs||(!this.flight.parentFlightId && !this.flight.childFlightId)) ) {

        flights = selectedFlight;
      } else if (!this.searchService.nonCombo && this.flight && this.flight.parentFlightId) {
        // this.searchService.comboOutGoingSelectedFlight.legs[this.searchService.seletedIndex ] = selectedFlight.legs[0];
        this.searchService.comboOutGoingSelectedFlight.legs = selectedFlight.legs;
        this.searchService.comboOutGoingSelectedFlight.handlerType = selectedFlight.handlerType;
        flights = this.searchService.comboOutGoingSelectedFlight;
        // flights = selectedFlight;
      }
      this.searchService.flightSearchQueryParam.combo = (!this.searchService.nonCombo) + '';
      if(this.searchService.selectedEventID){
        this.searchService.flightSearchQueryParam.group_travel_event_id = this.searchService.selectedEventID;
      }
      this.searchService.flightSearchQueryParam.travellers =
        {
          adults: +this.searchService.flightSearchQueryParam.adults,
          infants: +this.searchService.flightSearchQueryParam.infants,
          children: +this.searchService.flightSearchQueryParam.children
        };
      this.requestForAllfaresSubscription
      = this.bookingService.getAllfare(flights, this.searchService.flightSearchQueryParam).subscribe(resp => {
        this.autoOtherFareCallInProgress = false;
        if (resp && resp.status === 'success') {
          this.showloader = false;
          if (this.connectionListener) {
            this.connectionListener.unsubscribe();
          }
          const oldOtherFareCount = this.flight.otherFarePriceAttributes.length;
          this.handleAllFareResponseData(resp.allFlights.flights, selectedFlight);
        if (resp.allFlights && resp.allFlights.flights && resp.allFlights.flights.length > 0) {
            this.flight['allUpsellFares'] = resp.allFlights.flights;
            this.flight['luggageOptions'] = resp.allFlights.flights[0]['luggageOptions'];
            this.flight['returnLuggageOptions'] = resp.allFlights.flights[0]['returnLuggageOptions'];
            if(resp.allFlights.flights[0].hasOwnProperty('passportRequired') ){
              this.flight['passportRequired'] = resp.allFlights.flights[0]['passportRequired'];
            } 
            if(this.flight['allUpsellFares']
              && this.flight['allUpsellFares'].length > 0
              && this.flight.price > this.flight['allUpsellFares'][0].price) {
                
              const newFlight = this.flight;
              newFlight.price = this.flight['allUpsellFares'][0].price;
              this.flight  = newFlight;
            }
            const index = this.filterService.originalFlightSearchResponse.flightsLists[0].flights
              .findIndex(
                item => item.legs[0].legId === this.flight.legs[0].legId
              );
            if(index> -1) {
              this.filterService.originalFlightSearchResponse.flightsLists[0].flights[index] = this.flight;
            }
          }
        } else {
          this.showloader = false;
          this.newFlightCountZero = true;
          this.flight['allUpsellFares'] =selectedFlight;
          this.selectionOption.push(selectedFlight);
        }
        this.detectChanges();
      });
      return true;
   
  }
  private detectChanges(){
    try {
      this.cdRef.reattach();
      this.cdRef.detectChanges();
    
    } catch(err) {

    }
  }
  dynamicBorderRaidus(i){
  if((this.selectionOption.length ===0 ||  this.openBox!==i) || !this.colspanShowBtn){
    return {'display':'block'};
  }else{
    return {'display':'none'};
  }
}
  bookFlightOtherFarePrice(selectedOtherFareIndex) {
    if (this.bsModalRef) {
      this.bsModalRef.hide();
      setTimeout(() => {
        this.afterModalHideActionsOtherFarePrice(selectedOtherFareIndex);
      }, 500);
    } else {
      this.afterModalHideActionsOtherFarePrice(selectedOtherFareIndex);
    }
  }


  checkNoWordsGreterthanOne(item){
    return item.split(' ').length;
  }
  firstWordOfClassName(str){
    return str.substring(0, str.indexOf(' '));
  }
  lastwordsOfclassName(str){
    return str.substring(str.indexOf(' ') + 1);
  }
  getFlightObjectWithSelected(selectedOtherFareIndex: number) {
    if (selectedOtherFareIndex < 0) {
      for(let looper = 1; looper < this.indexArray.length; looper++) {
        if (this.indexArray[looper] !== -1) {
          selectedOtherFareIndex = this.indexArray[looper];
          break;
        }
      }
      if (selectedOtherFareIndex<0) {
        selectedOtherFareIndex = this.indexArray[0];
      }
    }
    return this.modifyFlightObjectWithSelected(selectedOtherFareIndex, this.flight, this.searchService.seletedIndex);
  }
  modifyFlightObjectWithSelected(selectedOtherFareIndex: number, flight: FlightResult, comboLegIndex: number) {
    if (selectedOtherFareIndex < 0) return flight;
    let selectedFlight: FlightResult = JSON.parse(JSON.stringify(flight));
    if (selectedFlight.otherFarePriceAttributes && selectedFlight.otherFarePriceAttributes.length > 0 && selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex]) {
      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].currency) {
        selectedFlight.currency = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].currency;
      }

      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].discountedPrice) {
        selectedFlight.discountedPrice = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].discountedPrice;
      }
      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].displayCurrency) {
        selectedFlight.displayCurrency = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].displayCurrency;
      }
      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].displayPrice) {
        selectedFlight.displayPrice = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].displayPrice;
      }
      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].fareAttributes) {
        selectedFlight.fareAttributes = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].fareAttributes;
      }
      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].fareBreakup) {
        selectedFlight.fareBreakup = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].fareBreakup;
      }

      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].appliedAdditonalFees) {
        selectedFlight.appliedAdditonalFees = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].appliedAdditonalFees;
      }

      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].legs) {
          selectedFlight.legs = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].legs;
      }
      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].handlerType) {
        selectedFlight.handlerType = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].handlerType;
    }
      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].price) {
        selectedFlight.price = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].price;
      }
      if (selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].ngsStars) {
        selectedFlight.ngsStars = selectedFlight.otherFarePriceAttributes[selectedOtherFareIndex].ngsStars;
      }
      return selectedFlight;
    } else {
      return this.flight;
    }
  }
  afterModalHideActionsOtherFarePrice(selectedOtherFareIndex) {
    let selectedFlight: FlightResult = this.getFlightObjectWithSelected(selectedOtherFareIndex);

    if (this.searchService.nonCombo) {

      this.searchService.nonComboSelectedFlight[this.searchService.seletedIndex] = selectedFlight;
    }
    this.searchService.seletedIndex++;
    this.flightToBookEmit = selectedFlight;
    this.onSelect();
  }
  bookFlight() {
    if (this.bsModalRef) {
      this.bsModalRef.hide();
      setTimeout(() => {
        this.afterModalHideActions();
      }, 500);
    } else {
      this.afterModalHideActions();
    }
  }
  afterModalHideActions() {
    if (this.searchService.nonCombo) {
      this.searchService.nonComboSelectedFlight[this.searchService.seletedIndex] = this.flight;
    }
    this.searchService.seletedIndex++;
    this.flightToBookEmit = this.flight;
    this.onSelect();
  }
  onlyMobileBookFlight() {
    if (this.isMobile) this.bookFlight();
  }
  getClassName(id) {
    let className;

    this.classOptions.map(item => {
      if (item.id.toLowerCase() == id.toLowerCase()) {
        className = item.value;
      }
    });

    if (className.includes("Class")) {
      return className;
    } else {
      return className + " Class";
    }
  }

  getDisplayDate(dateString: string): string {
    return DateUtils.getDisplayDate(dateString);
  }

  ngOnDestroy() {
    this.viewIsAlreadyDistroyed = true;
    if (this.noOfFlightLegsSubscription) {
      this.noOfFlightLegsSubscription.unsubscribe();
    }
    this.deviceSubscription.unsubscribe();
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }
  isBasicEconomy(leg: FlightLegResult): boolean {
    const string1 = this.getClassStringWithBrand(0, leg);
    if (string1 === 'Basic economy') {
      return true;
    } else {
      return false;
    }
  }
  isMixed(item): boolean {
    return item.legs[0].flightHighlights.mixedClass;
  }
  getHopDuration(durationInMins) {
    return DateUtils.getDurationAsHrsMinObj(durationInMins);
  }
  getClassStringWithBrand(hopIndex: number, leg: FlightLegResult, flighthop?) {
    let carrierName = null;
    if (flighthop) {
      let cabinClass = CommonUtils.classNameReturnFromMap(this.cabinClasses, flighthop.carrier, flighthop.cabinClass);
      if (cabinClass) {
        return cabinClass;
      }
    }
    return CommonUtils.getClassStringWithBrand(hopIndex, leg,
      (this.searchService.getBrandedFareCheckBoxState()
        && this.searchService.getOutgoingSelectedFlight()
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails
        && this.searchService.getOutgoingSelectedFlight().legs[0].brandDetails.length > 0)
    );
  }
  getAirlineFullName(code) {
    let airlineFullName = null;
    airlineFullName = this.airlines[code];
    return (airlineFullName === '' || airlineFullName === null || airlineFullName === undefined) ? code : airlineFullName;
  }

  getAirportCity(code) {
    let airportCity = null;
    airportCity = this.airports[code].name;
    return (airportCity === '' || airportCity === null) ? code : airportCity;
  }
  getAbsoluteNumber(input: number) {
    if (input < 0) {
      return input * -1;
    }
    return input;
  }
  get isStatic() {
    return this.type === 'static';
  }


  getInPolicyBlockVisibility() {
    if (this.flight.legs[0].flightHighlights.hasOwnProperty("withinPolicy")) {
      if (this.flight.legs[0].flightHighlights.withinPolicy) {
        return '';
      }
    }
    return 'hidden';
  }
  getOutPolicyBlockVisibility() {
    if (this.flight.legs[0].flightHighlights.hasOwnProperty("withinPolicy")) {
      if (!(this.flight.legs[0].flightHighlights.withinPolicy)) {
        return '';
      }
    }
    return 'hidden';
  }
  getNoPolicyBlockVisibility() {
    if (this.flight.legs[0].flightHighlights.hasOwnProperty("withinPolicy")) {
      return "hidden";
    }
    return '';
  }
  getCurrencySymbol(flight): string {
    if (flight && flight.displayCurrency) {
      return CommonUtils.getCurrencySymbol(flight.displayCurrency);
    } else {
      return CommonUtils.getCurrencySymbol(flight.currency);
    }
  }
  isAttributAvailable() {
    if ((this.creditDetails && this.creditDetails.length > 0) || this.isOverNightFlight() || this.isPreferredAirline() || this.isPreferredAlliance() || this.isPreferredAirport() || this.isRedEye()) {
      return true;

    } else {
      return false;
    }
  }
  getStopsUIText(legIndex: number) {
    let stopsText = this.translateService.instant('flightChart.nonstop');
    if (this.flightLayoverInfoList[legIndex].length > 0) {
      if (this.flightLayoverInfoList[legIndex].length == 1) {
        stopsText = this.translateService.instant('flightSelected.1stop');
      } else {
        stopsText = this.flightLayoverInfoList[legIndex].length + this.translateService.instant('flightSelected.spacestops');
      }

      let stopAirports = undefined;
      for (let layover of this.flightLayoverInfoList[legIndex]) {
        if (stopAirports) {
         // stopAirports = stopAirports + ', ' + layover.in;
        } else {
         // stopAirports = layover.in;
        }
      }
    //  stopsText = stopsText + ' ' + stopAirports;
    }
    return stopsText;
  }

  isPreferredAirline(item?) {
    let isPreferred = true;
    if (item) {
      for (let leg of item.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasPreferredAirline;
      }
      return this.isPreferredAlliance(item) ? false : isPreferred;
    } else {
      for (let leg of this.flight.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasPreferredAirline;
      }
      return this.isPreferredAlliance() ? false : isPreferred;
    }
  }

  isPreferredAlliance(item?) {
    let isPreferred = true;
    if (item) {
      for (let leg of item.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasPreferredAlliance;
      }
      return isPreferred;
    } else {
      for (let leg of this.flight.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasPreferredAlliance;
      }
      return isPreferred;
    }
  }

  isPreferredAirport(item?) {
    let isPreferred = true;
    if (item) {
      for (let leg of item.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasPreferredAirport;
      }
      return isPreferred;
    } else {
      for (let leg of this.flight.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasPreferredAirport;
      }
      return isPreferred;
    }
  }

  isRedEye(item?) {
    let isPreferred = false;
    if (item) {
      for (let leg of item.legs) {
        isPreferred = isPreferred || leg.flightHighlights.redEyeFlight;
      }
      return isPreferred;
    } else {
      for (let leg of this.flight.legs) {
        isPreferred = isPreferred || leg.flightHighlights.redEyeFlight;
      }
      return isPreferred;
    }
  }

  isOverNightFlight(item?) {
    let isPreferred = true;
    if (item) {
      for (let leg of item.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasOvernightLayover;
      }
      return isPreferred;
    } else {
      for (let leg of this.flight.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasOvernightLayover;
      }
      return isPreferred;
    }
  }

  isSmallLayoverFlight(item?) {
    let isPreferred = true;
    if (item) {
      for (let leg of item.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasShortLayover;
      }
      return isPreferred;
    } else {

      for (let leg of this.flight.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasShortLayover;
      }
      return isPreferred;
    }
  }

  isLongLayoverFlight(item?) {
    let isPreferred = true;
    if (item) {
      for (let leg of item.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasLongLayover;
      }
      return isPreferred;
    } else {
      for (let leg of this.flight.legs) {
        isPreferred = isPreferred && leg.flightHighlights.hasLongLayover;
      }
      return isPreferred;
    }
  }

  isChangeOfAirport(item?) {
    let isPreferred = false;
    if (item) {
      for (let leg of item.legs) {
        isPreferred = isPreferred || leg.flightHighlights.hasChangeOfAirport;
      }
      return isPreferred;
    } else {
      for (let leg of this.flight.legs) {
        isPreferred = isPreferred || leg.flightHighlights.hasChangeOfAirport;
      }
      return isPreferred;
    }
  }
  isTopRecommendedFlight() {
    return this.sortingType === 'recommended' && this.flight.legs[0].flightHighlights.topFlightReason ? true : false;
  }

  getBaggageDetailsColor(item) {
    let baggageDetails: BaggageDetails = item.legs[0].baggageAllowance;
    if (this.searchService.brandedFareCheckBox && item.legs[0].brandDetails && item.legs[0].brandDetails.length > 0) {
      baggageDetails = item.legs[0].brandDetails[0].baggageAllowance;
    }
    if (baggageDetails) {
      if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return 'allowed';
      }
      if ((baggageDetails.maxWeightAllowed && baggageDetails.maxWeightAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.maxWeightAllowed) === 0) || (!baggageDetails.maxWeightAllowed)) {
        return 'notallowed';
      } else {
        return 'allowed';
      }
    }
    return null;
  }
  getBaggageNumber(item) {
    let baggageDetails: BaggageDetails = item.legs[0].baggageAllowance;
    if (this.searchService.brandedFareCheckBox && item.legs[0].brandDetails && item.legs[0].brandDetails.length > 0) {
      baggageDetails = item.legs[0].brandDetails[0].baggageAllowance;
    }
    if (baggageDetails) {
      if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return (Number.parseInt(baggageDetails.numberOfPieceAllowed))
      }
      if ((baggageDetails.maxWeightAllowed && baggageDetails.maxWeightAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.maxWeightAllowed) === 0) || (!baggageDetails.maxWeightAllowed)) {
        return this.translateService.instant('flightChart.Nocheckedbags');
      } else {
        return (Number.parseInt(baggageDetails.maxWeightAllowed));
      }
    }
    return null;
  }
  getBaggageDetails(item) {
    let baggageDetails: BaggageDetails = item.legs[0].baggageAllowance;
    if (this.searchService.brandedFareCheckBox && item.legs[0].brandDetails && item.legs[0].brandDetails.length > 0) {
      baggageDetails = item.legs[0].brandDetails[0].baggageAllowance;
    }
    if (baggageDetails) {
      if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return (Number.parseInt(baggageDetails.numberOfPieceAllowed) === 1) ? this.translateService.instant('flightChart.1checkedbagperadult')
          : baggageDetails.numberOfPieceAllowed + ' ' + this.translateService.instant('flightChart.checkedbagsperadult');
      }
      if ((baggageDetails.maxWeightAllowed && baggageDetails.maxWeightAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.maxWeightAllowed) === 0) || (!baggageDetails.maxWeightAllowed)) {
        return this.translateService.instant('flightChart.Nocheckedbags');
      } else {
        return baggageDetails.maxWeightAllowed + ' ' + baggageDetails.weightUnit.toLowerCase() + this.translateService.instant('flightChart.peradult');
      }
    }
    return null;
  }
  getCorporateFareAttributeMoreOptions(item) {
    if(item.corporateRate){
    return item.corporateRate;
    }
  }
 isBaggageAvailable(item){
  let baggageDetails: BaggageDetails = item.legs[0].baggageAllowance;
  if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
  && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return true;
      } else {
return false;
      }
 }
  getTopRecommendedReason() {
    return this.flight.legs[0].flightHighlights.topFlightReason;
  }
  showWithinPolicy() {
    return this.algoType === AlgoTypes.PODUCTIVITY.toString() && this.flight.legs[0].flightHighlights.hasOwnProperty("withinPolicy");
  }
  isWithinPolicy(item?) {
    if (item) {
      if (item.legs[0].flightHighlights.hasOwnProperty("withinPolicy")) {
        return item.legs[0].flightHighlights.withinPolicy;
      }
    }
    if (this.flight.legs[0].flightHighlights.hasOwnProperty("withinPolicy")) {
      return this.flight.legs[0].flightHighlights.withinPolicy;
    }
  }

  getIfFareAttributeOffered(fareAttrib: FareAttributeValues) {
    if (fareAttrib !== FareAttributeValues.NOTOFFERED) {
      return true;
    }
    return false;
  }
  getCurrentFlightFareAttributes(): FareAttributes {
    if (this.brandedFareCheckBox && this.flight.legs[0].brandDetails && this.flight.legs[0].brandDetails.length > 0) {
      return this.flight.legs[0].brandDetails[0].fareAttributes;
    }
    return this.flight.fareAttributes;
  }
  getCurrentFlightFareAttributesForMoreOptions(item): FareAttributes {
    if (this.brandedFareCheckBox && item.legs[0].brandDetails && item.legs[0].brandDetails.length > 0) {
      return item.legs[0].brandDetails[0].fareAttributes;
    }
    return item.fareAttributes;
  }

  getWifiFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).wifi;
  }
  getCheckedBagFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).checkedBag;
  }
  getCarryOnBagFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).carryBag;
  }
  getRefundAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).refund;
  }
  getChangesFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).rebooking;
  }
  getSeatFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).seat;
  }
  
  getWifiAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).wifi;
  }
  getMealFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).meal;
  }
  getWifiFareAttribute() {
    return this.getCurrentFlightFareAttributes().wifi;
  }
  getCheckedBagFareAttribute() {
    return this.getCurrentFlightFareAttributes().checkedBag;
  }
  getCarryOnBagFareAttribute() {
    return this.getCurrentFlightFareAttributes().carryBag;
  }
  getRefundAttribute() {
    return this.getCurrentFlightFareAttributes().refund;
  }
  getChangesFareAttribute() {
    return this.getCurrentFlightFareAttributes().rebooking;
  }
  getSeatFareAttribute() {
    return this.getCurrentFlightFareAttributes().seat;
  }
  getWifiAttribute() {
    return this.getCurrentFlightFareAttributes().wifi;
  }
  getMealFareAttribute() {
    return this.getCurrentFlightFareAttributes().meal;
  }
  toStringFromJSON(json: any) {
    return JSON.stringify(json);
  }

  trimairportName() {
    if (getWindowWidth() > 767 && this.colspanShowBtn) {
      return { 'white-space': 'nowrap', 'overflow': 'hidden' }
    } else if (!this.colspanShowBtn) {
      return { 'white-space': 'break-spaces', 'overflow': 'revert' }
    }
  }

  getArrivalDaysDifference(): string {
    let daysDiff = 0;
    let searchRequest = this.searchService.getPreviousSearch();
    // 
    let flightEnd  = this.flight.legs[0].flightHops[this.flight.legs[0].flightHops.length - 1].ends;
   
  //  
      flightEnd = flightEnd.split('T')[0];
      if (this.legType === 'OUTGOING' && searchRequest.departureDate) {
        let requestarrival= this.getDatePartAsString(searchRequest.departureDate);
        daysDiff = DateUtils.getDaysDiff(requestarrival, flightEnd);
      } else if (this.legType === 'RETURN' && searchRequest.flights.length >0 && searchRequest.flights[this.searchService.seletedIndex-1].departureDate) {
        let requestarrival= this.getDatePartAsString(searchRequest.flights[this.searchService.seletedIndex-1].departureDate);
        daysDiff = DateUtils.getDaysDiff(requestarrival, flightEnd);
      }else if (this.legType === 'RETURN' && searchRequest.arrivalDate) {
        let requestarrival= this.getDatePartAsString(searchRequest.arrivalDate);
        daysDiff = DateUtils.getDaysDiff(requestarrival, flightEnd);
      }
  
    let diffText = '';
    if (daysDiff >= 1) {
      if (daysDiff > 0) diffText = ' +';
      diffText = diffText + daysDiff;
    }else{
      if (daysDiff  < 0){
      diffText = diffText + daysDiff;
      }
      
    }
    return diffText;
  }
  getArrivalDaysDifferenceForLayover(i): string {
    let daysDiff = 0;
    let searchRequest = this.searchService.getPreviousSearch();
    // 
    let flightEnd  = this.flight.legs[0].flightHops[i].ends;
    
      flightEnd = flightEnd.split('T')[0];
      if (this.legType === 'OUTGOING' && searchRequest.departureDate) {
        let requestarrival= this.getDatePartAsString(searchRequest.departureDate);
        daysDiff = DateUtils.getDaysDiff(requestarrival, flightEnd);
      } else if (this.legType === 'RETURN' && searchRequest.flights.length >0 && searchRequest.flights[this.searchService.seletedIndex-1].departureDate) {
        let requestarrival= this.getDatePartAsString(searchRequest.flights[this.searchService.seletedIndex-1].departureDate);
        daysDiff = DateUtils.getDaysDiff(requestarrival, flightEnd);
      }else if (this.legType === 'RETURN' && searchRequest.arrivalDate) {
        let requestarrival= this.getDatePartAsString(searchRequest.arrivalDate);
        daysDiff = DateUtils.getDaysDiff(requestarrival, flightEnd);
      }
  
    let diffText = '';
    if (daysDiff >= 1) {
      if (daysDiff > 0) diffText = ' +';
      diffText = diffText + daysDiff;
    }else{
      if (daysDiff  < 0){
      diffText = diffText + daysDiff;
      }
      
    }
    return diffText;
  }
getNumberCarriers(item){
  let carrierArray=[];
  for(let item1 of item.legs){
    for(let hop of item1.flightHops){
    if(carrierArray.indexOf(hop.carrier)===-1){
      carrierArray.push(hop.carrier)
    }
    }

  }
return (carrierArray.length - 1);
}

getDatePartAsString(input) {
  if (input instanceof Date) {
    return  moment(input).format().split('T')[0];
  } else {
    return input.split('T')[0];
  }
}
getDepartureDaysDifferenceForLayover(i): string {
  let daysDiff = 0;
  let searchRequest = this.searchService.getPreviousSearch();
  let flightStart  = this.flight.legs[0].flightHops[i].starts;
  flightStart = flightStart.split('T')[0];
  if (this.legType === 'OUTGOING' && searchRequest.departureDate) {
    let requestDeparture= this.getDatePartAsString(searchRequest.departureDate);
    daysDiff = DateUtils.getDaysDiff(requestDeparture, flightStart);
  } else if (this.legType === 'RETURN' && searchRequest.flights.length > 0 && searchRequest.flights[this.searchService.seletedIndex-1].departureDate) {
    let requestDeparture= this.getDatePartAsString(searchRequest.flights[this.searchService.seletedIndex-1].departureDate);
    daysDiff = DateUtils.getDaysDiff(requestDeparture, flightStart);
  }else  if (this.legType  === 'RETURN' && searchRequest.arrivalDate) {
    let requestDeparture= this.getDatePartAsString(searchRequest.arrivalDate);
    daysDiff = DateUtils.getDaysDiff(requestDeparture, flightStart);
  }

  let diffText = '';
  if (daysDiff >= 1) {
    if (daysDiff > 0) diffText = ' +';
    diffText = diffText + daysDiff;
  }else{
    if (daysDiff  < 0){
    diffText = diffText + daysDiff;
    }
  
  }
  return diffText;
}
  getDepartureDaysDifference(): string {
    let daysDiff = 0;
    let searchRequest = this.searchService.getPreviousSearch();
    let flightStart ;
   
      flightStart  = this.flight.legs[0].flightHops[0].starts;
   
    flightStart = flightStart.split('T')[0];
    if (this.legType === 'OUTGOING' && searchRequest.departureDate) {
      let requestDeparture= this.getDatePartAsString(searchRequest.departureDate);
      daysDiff = DateUtils.getDaysDiff(requestDeparture, flightStart);
    } else if (this.legType === 'RETURN' && searchRequest.flights.length > 0 && searchRequest.flights[this.searchService.seletedIndex-1].departureDate) {
      let requestDeparture= this.getDatePartAsString(searchRequest.flights[this.searchService.seletedIndex-1].departureDate);
      daysDiff = DateUtils.getDaysDiff(requestDeparture, flightStart);
    } else  if (this.legType  === 'RETURN' && searchRequest.arrivalDate) {
      let requestDeparture= this.getDatePartAsString(searchRequest.arrivalDate);
      daysDiff = DateUtils.getDaysDiff(requestDeparture, flightStart);
    }

    let diffText = '';
    if (daysDiff >= 1) {
      if (daysDiff > 0) diffText = ' +';
      diffText = diffText + daysDiff;
    }else{
      if (daysDiff  < 0){
      diffText = diffText + daysDiff;
      }
    
    }
    return diffText;
  }
  getLegRoomFareAttribute() {
    return this.getCurrentFlightFareAttributes().legRoom;
  }
  getLegroomFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).legRoom;
  }
  getLayoverInfoAsTooTip(num) {
    const currLeg = this.flightLayoverInfoList[num];
    let infoText = '';
    for (const info of currLeg) {
      infoText += info['duration']['hrs'] + 'h ' + info['duration']['mins'] + 'm in ' + info['in'] + '\n';
    }
    return infoText;
  }
  getFligthClass(flighthop) {
    return flighthop.fareClassName
  }
  getFirstLayoverInfo(num) {
    const currLegHopInfo = this.flightLayoverInfoList[num][0];
    if (this.flightLayoverInfoList[num].length === 1) {
      return currLegHopInfo['duration']['hrs'] + 'h ' + currLegHopInfo['duration']['mins'] + 'm';
    }
    return '';
  }
  lastActiveClass: string;
  OtherFlightBoxIndex: number = 1;
  boxShow = false;
  SingleDetailsBoxShow = false
  colspanShowBtn = false;
  ToggleHideShowButton = "Show Details"

  selectedMobileFeatureFlight = this.tempStarsArrayFlight[0];
openFareBoxForMobile(num, modal){
  if(!this.isMobile){
   return;
  }
//  if (this.userAccountInfoService.isConnected) {
    this.getOtherFlightFareClass = [];
    if (this.requestForAllfaresSubscription) {
      this.showloader = false;
      this.detectChanges();
      this.requestForAllfaresSubscription.unsubscribe();
    }
  //  this.searchService.selectedFareIndex = num;
  this.openBox=num
    this.selectedMobileFeatureFlight=this.tempStarsArrayFlight[0];
    let BoxId = '.' + 'Price_Box' + num + '_' 
    let detailsHide = '#' + 'Details' + num;
    let pop = '.' + 'popUp' + num;
    for (let item of this.flight.legs[0].flightHops) {
      this.getOtherFlightFareClass.push(item.fareClassName);
    }

    
    if ($(pop).hasClass("block") == false) {
      this.CloseBtn(num);
      this.boxShow = true;
       this.colspanShowBtn = !this.colspanShowBtn;
      this.SingleDetailsBoxShow = true;
     // if (getWindowWidth() < 768) {
      //  this.showFlightDetailModal(modal);
   //   } else {
        $(pop).addClass("block");
        $(pop).show();
        $(detailsHide).show();
     // }
     this.lastActiveClass = BoxId;
      let proceedToBookFlight = this.selectMoreOptionsWithMultiFares(-1, modal);
      
      
      if (proceedToBookFlight == "yes") {
        $(BoxId).removeClass("MultipalBox_Active");
        this.CloseBtn(num);
        this.bookFlightOtherFarePrice(num);
      }
    } else {
      $(pop).removeClass("block");
      this.openBox=-1;
      this.CloseBtn(num);
    };
  

}
checkdetailsOfFlight=false;
selectedFeatureIndex=-1

openFareBoxForMobileFlightFeature(item,num,athorflightindex){
 // if (this.userAccountInfoService.isConnected) {
    if (this.requestForAllfaresSubscription) {
      this.showloader = false;
      this.detectChanges();
      this.requestForAllfaresSubscription.unsubscribe();
    }
    for(let i=0;i< this.tempStarsArrayFlight.length;i++){
if(i!==athorflightindex){
  this.tempStarsArrayFlight[i].showFeatureInmobile = false;
}
    }
    this.tempStarsArrayFlight[athorflightindex].showFeatureInmobile = !this.tempStarsArrayFlight[athorflightindex].showFeatureInmobile;
    this.selectedFeatureIndex = athorflightindex
    this.selectedMobileFeatureFlight=item;
    this.detectChanges();
    
  
}
openBox =-1;
  OpenModal(num, athorflightindex,boxindex, modal) {
   // if (this.userAccountInfoService.isConnected) {
    this.getOtherFlightFareClass = [];
    this.searchService.selectedFareIndex =athorflightindex;
    if (this.requestForAllfaresSubscription) {
      this.showloader = false;
      this.detectChanges();
      this.requestForAllfaresSubscription.unsubscribe();
    }
   
    let BoxId = '.' + 'Price_Box' + num + '_' + boxindex;
    let detailsHide = '#' + 'Details' + num;
    let pop = '.' + 'popUp' + num;
    for (let item of this.flight.otherFarePriceAttributes[athorflightindex].legs[0].flightHops) {
      this.getOtherFlightFareClass.push(item.fareClassName);
    }

    
    if ($(BoxId).hasClass("MultipalBox_Active") == false) {
      this.CloseBtn(num);
      this.boxShow = true;
      this.colspanShowBtn = true;
      this.openBox=boxindex;
      this.OtherFlightBoxIndex = athorflightindex;
      this.SingleDetailsBoxShow = true;
      if (getWindowWidth() < 768) {
        this.showFlightDetailModal(modal);
      } else {
        $(BoxId).addClass("MultipalBox_Active");
        $(pop).show();
        $(detailsHide).show();
      }

      this.lastActiveClass = BoxId;
     
      let proceedToBookFlight = this.selectMoreOptionsWithMultiFares(athorflightindex, modal);
      if (proceedToBookFlight == "yes") {
        $(BoxId).removeClass("MultipalBox_Active");
        this.CloseBtn(num);
        this.bookFlightOtherFarePrice(athorflightindex);
      }
    } else {
      this.openBox=-1;
      this.detectChanges();
      $(BoxId).removeClass("MultipalBox_Active");
      this.CloseBtn(num);
    };
  

  }
  getNUmberOfdigitOfPrice(symbol, price) {
    let str = symbol + price;
    if (!this.isMobile) {
      if (str.length >= 8) {
        return { 'font-size': '12px' }
      } else if (str.length < 8) {
        return { 'font-size': '14px' }
      } else if (str.length <= 6) {
        return { 'font-size': '16px' }
      }
    } else {
      if (str.length > 6) {
        return { 'font-size': '10px' }
      } else if (str.length <= 6) {
        return { 'font-size': '12px' }
      } else if (str.length <= 5) {
        return { 'font-size': '14px' }
      }
    }
  }
  OpenModalForRoundTrip(num, athorflightindex, modal,) {


    if (this.ToggleHideShowButton == "Show Details") {
      if (getWindowWidth() < 768) {
        this.showFlightDetailModal(modal);
      } else {
        this.ToggleHideShowButton = "Hide Details";
        this.boxShow = true;
        this.colspanShowBtn = true
        this.SingleDetailsBoxShow = true
      };
      this.OtherFlightBoxIndex = athorflightindex

      let proceedToBookFlight = this.selectMoreOptionsWithMultiFares(athorflightindex, modal);
      if (proceedToBookFlight == "yes") {
        this.bookFlightOtherFarePrice(athorflightindex);
      }

    } else {
      this.ToggleHideShowButton = "Show Details";
      this.SingleDetailsBoxShow = false;
      this.CloseBtn(num)
    }


  }

  CloseBtn(num) {
    this.boxShow = true;
    let pop = '.' + 'popUp' + num;
    let detailsHide = '#' + 'Details' + num;
    $(this.lastActiveClass).removeClass("MultipalBox_Active");

    $(pop).hide()
    $(detailsHide).hide()
    this.colspanShowBtn = false;
    this.detectChanges();
  }

  OpenModalMobile(num) {
    let pop = '#' + 'popUpMobile' + num;
    let Mpop = '#' + 'hideDetailsMobile' + num
    let flightDetails = '#' + 'hideFlightDetails' + num
    $(flightDetails).hide()
    $(pop).show()
    $(Mpop).hide()
  }

  closeModalMobile(num) {
    let pop = '#' + 'popUpMobile' + num;
    let Mpop = '#' + 'hideDetailsMobile' + num
    let flightDetails = '#' + 'hideFlightDetails' + num
    $(flightDetails).show()
    $(pop).hide()
    $(Mpop).show()
  }
  screenWidthChange() {
    return getWindowWidth()
  }



  ChangeClass() {
    // if(this.searchService.flightSearchQueryParam.trip == 'ONEWAY'){
    if (this.checkMultiFare) {
      return 'result-card-box-left-2'
    } else {
      return 'result-card-box-left'
    }
  }
  cssChangeWithCount() {
    //  if( this.searchService.flightSearchQueryParam.trip == 'ONEWAY'){
      if(!this.isMobile){
    if (this.checkMultiFare) {
      return "result-card-box-right d-none d-md-block planePriceBox-2"
    } else {
      return "result-card-box-right d-none d-md-block planePriceBox"
    }
  }else{
    return 'result-card-box-right d-none d-md-none planePriceBox';
  }
  }




  CheckCabinClass(event) {
    // 
  }

  getMaxClassName(flightObj) {
    let maxWeight = 0;
    let maxClassName = 'Basic economy';
    for(let legCounter = 0; legCounter < flightObj.legs.length ; legCounter++) {
      for(let hopCounter = 0; hopCounter < flightObj.legs[legCounter].flightHops.length; hopCounter++) {
        if ( this.classWeightMap[flightObj.legs[legCounter].flightHops[hopCounter].cabinClass] > maxWeight){
          maxWeight = this.classWeightMap[flightObj.legs[legCounter].flightHops[hopCounter].cabinClass];
          maxClassName = flightObj.legs[legCounter].flightHops[hopCounter].cabinClass;
        }
      }
    }
    return maxClassName;
  }

  getFontSizeBasedOnBrandFareClassName(flight){
    let maxBrandName;
    let stringArray=[];
    for(let flightObj of flight){
     maxBrandName = this.getBrandNameForMaxClass(flightObj);
    stringArray.push(maxBrandName);
    }
    stringArray = stringArray.sort(function(a, b){
      // ASC  -> a.length - b.length
      // DESC -> b.length - a.length
      return b.length - a.length;
    });
    let wordArray = stringArray[0].split(' ');
    if(wordArray && wordArray.length > 3){
      return {'font-size':'8px'};
    }
    else if(wordArray && wordArray.length ===3){
      return {'font-size':'10px'};
    }else{
      return {'font-size':'12px'};
    }
  }
  getBrandNameForMaxClass(flightObj) {
    const hop = CommonUtils.getHopForMaxStar(flightObj, this.searchService);
    let maxWeight = 0;
    let maxBrandName = 'BASIC ECONOMY';
    let carrierForBrand='';
    if (hop) {
      maxBrandName = hop.fareClassName;
      carrierForBrand = hop.carrier;
    } else {
      for(let legCounter = 0; legCounter < flightObj.legs.length ; legCounter++) {
        for(let hopCounter = 0; hopCounter < flightObj.legs[legCounter].flightHops.length; hopCounter++) {
          if ( this.classWeightMap[flightObj.legs[legCounter].flightHops[hopCounter].cabinClass] >= maxWeight) {
            maxWeight = this.classWeightMap[flightObj.legs[legCounter].flightHops[hopCounter].cabinClass];
            maxBrandName = flightObj.legs[legCounter].flightHops[hopCounter].fareClassName.toUpperCase();
            carrierForBrand = flightObj.legs[legCounter].flightHops[hopCounter].carrier;
          }
        }
      }
    }
    maxBrandName = this.removeAirlineNameFromBrand(maxBrandName, carrierForBrand);
   
    carrierForBrand = this.getAirlineFullName(carrierForBrand).toUpperCase();
    
    maxBrandName = this.removeAirlineNameFromBrand(maxBrandName, carrierForBrand);

    return maxBrandName;
  }
  private removeAirlineNameFromBrand(maxBrandName, carrierForBrand) {
    if (maxBrandName.startsWith(carrierForBrand + ' ')) {
      maxBrandName = maxBrandName.substring((carrierForBrand + ' ').length);
    }
    if (maxBrandName.endsWith(' ' + carrierForBrand)) {
      maxBrandName = maxBrandName.substring(0, maxBrandName.length - (' ' + carrierForBrand ).length);
    }
    maxBrandName = maxBrandName.replace(' ' + carrierForBrand  +' ', ' ');
    return maxBrandName;
  }
  getFlightIconURLForSelectedOtherFare(legIndex, hopIndex) {
    if (this.OtherFlightBoxIndex>-1 && this.flight.otherFarePriceAttributes.length > this.OtherFlightBoxIndex
      && this.flight.otherFarePriceAttributes[this.OtherFlightBoxIndex].legs.length > legIndex
      && this.flight.otherFarePriceAttributes[this.OtherFlightBoxIndex].legs[legIndex].flightHops.length > hopIndex
      ) {
      if(this.flight.otherFarePriceAttributes[this.OtherFlightBoxIndex].legs[legIndex].flightHops[hopIndex]) {
        if(this.flight.otherFarePriceAttributes[this.OtherFlightBoxIndex].legs[legIndex].flightHops[hopIndex].carrier) {
        return this.getFlightIconURL(
          this.flight.otherFarePriceAttributes[this.OtherFlightBoxIndex].legs[legIndex].flightHops[hopIndex].carrier
          ) ;
        }
      }
    }
    return this.getFlightIconURL(this.flight.legs[legIndex].flightHops[hopIndex].carrier);
  }
  getSelectedOtherFareBrandNameForSpecificHop(legIndex, hopIndex) {
    if (this.OtherFlightBoxIndex>-1 && this.flight.otherFarePriceAttributes.length > this.OtherFlightBoxIndex) {
      if(this.flight.otherFarePriceAttributes[this.OtherFlightBoxIndex].legs[legIndex].flightHops[hopIndex] && this.flight.otherFarePriceAttributes[this.OtherFlightBoxIndex].legs[legIndex].flightHops[hopIndex].fareClassName){
      return this.flight.otherFarePriceAttributes[this.OtherFlightBoxIndex].legs[legIndex].flightHops[hopIndex].fareClassName ;
      }
    }
    return '';
  }
  public isOtherFareAvailable(fareClassName, otherFaresArray) {
    //if data related to provided fareClassName available return true else return false

    if (otherFaresArray && otherFaresArray.length !== 0) {
      for (let i = 0; i < otherFaresArray.length; i++) {
        const maxStarHop = CommonUtils.getHopForMaxStar(otherFaresArray[i], this.searchService);
        if (maxStarHop !== null) {
          const ngsStars = maxStarHop['ngsStars'];
          if (fareClassName === 'BASIC_ECONOMY' && ngsStars === '1') {
            return i;
          }
          if (fareClassName === 'ECONOMY' && ngsStars === '2') {
            return i;
          }
          if (fareClassName === 'PREMIUM_ECONOMY' && ngsStars === '3') {
            return i;
          }
          if (fareClassName === 'BUSINESS_OR_FIRST_CLASS' && ngsStars === '4') {
            return i;
          }
        } else {
          const maxClassName = this.getMaxClassName(otherFaresArray[i]);
          const brandName = this.getBrandNameForMaxClass(otherFaresArray[i]);
          if (fareClassName === 'BASIC_ECONOMY' && maxClassName === 'Basic economy') {
            return i;
          } else if (fareClassName === 'ECONOMY' && maxClassName === 'Economy') {
            return i;
          } else if (fareClassName === 'PREMIUM_ECONOMY' && maxClassName === 'Premium economy') {
            return i;
          } else if (fareClassName === 'BUSINESS_OR_FIRST_CLASS'
            && (
              maxClassName === 'Business'
              || maxClassName === 'First class'
              || 'Delta One'.toLocaleLowerCase() === brandName.toLocaleLowerCase()
              || 'Delta Premium Select'.toLocaleLowerCase() === brandName.toLocaleLowerCase()
              || 'Delta First'.toLocaleLowerCase() === brandName.toLocaleLowerCase()
            )) {
            return i;
          }
        }
      }
    }
    return -1;
  }

  checkDetailBoxSize(length) {
    if (length > 3) {
      return '740';
    } else if (length > 4) {
      return '900';
    }
  }

  CheckFareClasslenght(fareClassName) {
    let arr = fareClassName.split(' ');
    let wordLength = arr.filter(word => word !== '').length
    let newWord = " "
    if (wordLength < 5) {
      arr.forEach((e, i) => {
        if (i = 3) {
          newWord += e + " ";
        };
      });
      // 
      return newWord
    } else {

      return newWord
    }
  }
  consoleFunction(e) {
    
  }
}


