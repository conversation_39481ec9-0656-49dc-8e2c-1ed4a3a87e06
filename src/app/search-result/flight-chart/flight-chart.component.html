<!-- -<div [ngClass]="{'result-card': true, 'selected': isSelectedFlight(), 'static': isStatic}" (click)="onSelect()">
  <div class="row no-gutters">
    <div [ngClass]="{'col-md-10': !isStatic, 'col-12': isStatic}">
      <ng-container *ngFor="let num of noOfFlightLegs">
        <div class="list-container" [collapse]="isExpanded">
          <div class="list">
            <div class="text-left">
              <div class="source"><img class="flight-icon" [src]="getFlightIconURL(flight.legs[num].flightHops[0].carrier)"><span>{{getFlightLegSource(num)}}</span></div>
              <div class="info" *ngIf="!isMobile">{{getDisplayDate(flight.legs[num].flightHops[0].starts) | date:'h:mm a, EE, MMM d'}}</div>
              <div class="info" *ngIf="isMobile">
                <div>{{getDisplayDate(flight.legs[num].flightHops[0].starts) | date:'h:mm a'}}</div>
                <div>{{getDisplayDate(flight.legs[num].flightHops[0].starts) | date:'EE, MMM d'}}</div>
              </div>
            </div>

            <div class="text-center">
              <span class="icon-plane"></span>
              <div class="info">
                  <span class="info-content" *ngIf="flightLayoverInfoList[num].length==0">Non-Stop</span>
                  <span class="info-content" *ngFor="let layover of flightLayoverInfoList[num];">{{flightLayoverInfoList[num]?(flightLayoverInfoList[num].length==0?'Non Stop':(flightLayoverInfoList[num].length==1?(layover.duration.hrs + 'h ' + layover.duration.mins + 'm ' + layover.in): (layover.in))):''}}</span>
                  <span class="info-content" *ngFor="let layover of flightLayoverInfoList[num];">{{flightLayoverInfoList[num]?(flightLayoverInfoList[num].length==1?(layover.duration.hrs + 'h ' + layover.duration.mins + 'm ' + layover.in): (layover.in)):''}}</span>
              </div>
              <div class="info">Travel time {{getFlightDuration(num).hrs + 'h ' +
                    getFlightDuration(num).mins +
                    'm'}}</div>
            </div>

            <div class="text-right">
                <div class="destination"><img class="flight-icon" [src]="getFlightIconURL(flight.legs[num].flightHops[flight.legs[num].flightHops.length - 1].carrier)"><span>{{getFlightLegDestination(num)}}</span></div>
                <div class="info" *ngIf="!isMobile">{{getDisplayDate(flight.legs[num].flightHops[getNoOfHops(num)-1].ends) | date:'h:mm a, EE, MMM d'}}</div>
                <div class="info" *ngIf="isMobile">
                  <div>{{getDisplayDate(flight.legs[num].flightHops[getNoOfHops(num)-1].ends) | date:'h:mm a'}}</div>
                  <div>{{getDisplayDate(flight.legs[num].flightHops[getNoOfHops(num)-1].ends) | date:'EE, MMM d'}}</div>
                </div>
              </div>
          </div>
        </div>

        <div class="list-container" [collapse]="!isExpanded">
          <ng-container *ngFor="let flightHop of flight.legs[num].flightHops; let i=index">
            <div>
              <div class="flight-number"><img class="flight-icon" [src]="getFlightIconURL(flightHop.carrier)">{{flightHop.carrier}}{{flightHop.flightNumber}}<span class="pipe">|</span>Operated by {{getAirlineFullName(flightHop.operatingCarrier)}}</div>
              <div class="list">

                <div class="text-left">
                  <div class="source">{{flightHop.from}}</div>
                  <div class="info">{{getAirportCity(flightHop.from)}}</div>
                  <div class="info" *ngIf="!isMobile">{{getDisplayDate(flightHop.starts) | date:'h:mm a, EE, MMM d'}}</div>
                  <div class="info" *ngIf="isMobile">
                    <div>{{getDisplayDate(flightHop.starts) | date:'h:mm a'}}</div>
                    <div>{{getDisplayDate(flightHop.starts) | date:'EE, MMM d'}}</div>
                  </div>
                </div>

                <div class="text-center">
                  <span class="icon-plane"></span>
                  <div class="info">{{getHopDuration(flightHop.duration).hrs + 'h ' +  getHopDuration(flightHop.duration).mins + 'm'}},</div>
                  <div class="info">{{flightHop.cabinClass}}</div>
                </div>

                <div class="text-right">
                    <div class="destination">{{flightHop.to}}</div>
                    <div class="info">{{getAirportCity(flightHop.to)}}</div>
                    <div class="info" *ngIf="!isMobile">{{getDisplayDate(flightHop.ends) | date:'h:mm a, EE, MMM d'}}</div>
                    <div class="info" *ngIf="isMobile">
                      <div>{{getDisplayDate(flightHop.ends) | date:'h:mm a'}}</div>
                      <div>{{getDisplayDate(flightHop.ends) | date:'EE, MMM d'}}</div>
                    </div>
                  </div>
              </div>
            </div>

            <div class="layover-container" *ngIf="flightLayoverInfoList[num][i]">
              <span class="layover">{{flightLayoverInfoList[num][i].duration.hrs + 'h ' + flightLayoverInfoList[num][i].duration.mins + 'm'}} layover in {{flightLayoverInfoList[num][i].in}}</span>
            </div>
          </ng-container>
        </div>
      </ng-container>
    </div>

    <div [ngClass]="{'col-md-2': !isStatic, 'col-12': isStatic}" style="border-left: 1px solid #D4D4D4;border-top: 1px solid #D4D4D4;">
      <div class="action-container">
        <button class="link-button" (click)="bookFlight()" *ngIf="!isStatic">Book {{flight.price | currency : getCurrencySymbol(flight.currency) : 'code': '1.0' }}</button>
        <span *ngIf="isStatic" class="d-flex align-items-center"><span class="price">{{flight.fareBreakup.price  | currency : getCurrencySymbol(flight.currency) : 'code':'1.0'}}</span><span class="text-secondary">{{noOfPassengers > 1 ? noOfPassengers + ' Passengers' : noOfPassengers + ' Passenger'}} with {{flight.legs[0].flightHops[0].cabinClass}} Class</span></span>
        <span [ngClass]="{'icon-toggle': true, 'icon-down': !isExpanded, 'icon-up': isExpanded}" (click)="toggleExpand()"></span>
      </div>
      <div class="in-policy {{getInPolicyBlockVisibility()}}">WITHIN POLICY</div>
      <div class="out-policy {{getOutPolicyBlockVisibility()}}">OUTSIDE POLICY</div>
      <div class="no-policy {{getNoPolicyBlockVisibility()}}">&nbsp;</div>
    </div>
  </div>
</div> -->

<div #container class="result-card-box result-item" [style.minHeight]="getItemMinHeight()">
    <div *ngIf="isTopRecommendedFlight() && this.searchService.seletedIndex === 0" class="top-recommandation-box">
        <p>{{getTopRecommendedReason()}}</p>
    </div>
    <div class="result-card-box-div">

        <!-- <div class="info-div" *ngIf="checkMultiFare == true" (click)="showFlightDetailModal(searchModal)" >
            <i class="fa fa-info-circle flght-chart-info-icon" aria-hidden="true"></i>
         </div> -->
        <div class="result-card-box-inner">


            <div [class]="ChangeClass()">

                <div class="flight-detail-container">

                    <ng-container *ngFor="let num of noOfFlightLegs;let count = index;">
                        <div class="flight-detail" (click)="openFareBoxForMobile(index,searchModal)">
                            <!-- the place of previous air line -->
                            <!-- <div class="flight-name-duration" style="width: 25%;display: flex;position: absolute;top: -2px;justify-content: space-evenly;">
                           <div style="display:flix;">
                              <span class="flight-name-logo block"><img onerror="this.onerror = null; this.src = 'https://s3.amazonaws.com/images.biztravel.ai/template/default.gif';" [src]="getFlightIconURL(flight.legs[num].flightHops[0].carrier)" /></span>
                              <span class="flight-duration block">{{this.airlines[flight.legs[num].flightHops[0].carrier]}}</span>
                            </div>
                           <span class="flight-duration block">{{getFlightDuration(num).hrs + 'h ' + getFlightDuration(num).mins + 'm'}}</span>
                        </div> -->
                            <div class="flight-timings-div" [id]="'hideFlightDetails'+ index">
                                <!-- *ngIf="this.creditDetails && this.creditDetails.length >0" -->

                                <div class="flight-timings">
                                    <div class="row" style="width: 100%;">
                                        <div class="col-2">
                                            <!-- <span class="flight-name-logo block"><img
                                       onerror="this.onerror = null; this.src = 'https://s3.amazonaws.com/images.biztravel.ai/template/default.gif';"
                                       [src]="getFlightIconURL(flight.legs[num].flightHops[0].carrier)" /></span> -->

                                            <div style="display:flex;margin-bottom: 10px;">
                                                <span class="flight-name-logo block"><img
                                          onerror="this.onerror = null; this.src = 'https://s3.amazonaws.com/images.biztravel.ai/template/default.gif';"
                                          [src]="getFlightIconURL(flight.legs[num].flightHops[0].carrier)" /></span>

                                            </div>

                                        </div>
                                        <div class="col-10">

                                            <div class="row" style="margin-left: 0px !important;justify-content: space-between;">
                                                <span class="block flight-time">{{getDisplayDate(flight.legs[num].flightHops[0].starts)|
                              date:'h:mm a'}} <span
                              class="extra-time1">{{getDepartureDaysDifference()}}</span></span>
                                                <div class="flight-timings-line">
                                                    <div class="stop-box-container">
                                                        <ng-container *ngIf="flightLayoverInfoList[num].length > 0">
                                                            <ng-container>
                                                                <span class="block text-center stop-box-div" style="width: 100%;">
                                                   <span  class="stop-box"><span class="numOfStops">{{flightLayoverInfoList[num].length}}</span></span>
                                                                </span>
                                                            </ng-container>
                                                        </ng-container>
                                                    </div>
                                                </div>
                                                <span class="block flight-time">{{getDisplayDate(flight.legs[num].flightHops[getNoOfHops(num)-1].ends)|
                              date:'h:mm a'}}  <span class="extra-time">{{getArrivalDaysDifference()}}</span></span>

                                            </div>
                                            <div class="row" style="margin-left: 0px !important;">
                                                <span style="margin-right:10px">
                                       <span *ngIf="!this.isMobile" >
                                             <span class="block flight-airport"
                                                *ngIf="flightLayoverInfoList[num].length==0">{{'flightChart.nonstop' |
                                                translate}}</span>
                                                <span class="block flight-airport" data-toggle="tooltip" title="{{getLayoverInfoAsTooTip(num)}}" *ngIf="flightLayoverInfoList[num].length > 0">{{getStopsUIText(num) |
                                                translate}}</span>

                                                </span>
                                                <span *ngIf="this.isMobile" style="top :40px;">
                                                <span class="block flight-airport"
                                                   *ngIf="flightLayoverInfoList[num].length==0">{{'flightChart.nonstop' |
                                                   translate}}</span>
                                                <span class="block flight-airport" data-toggle="tooltip" title="{{getLayoverInfoAsTooTip(num)}}" *ngIf="flightLayoverInfoList[num].length > 0">{{getStopsUIText(num) |
                                                   translate}}</span>

                                                </span>


                                                </span>
                                                <span class="dot"></span>
                                                <span class="block flight-airport" style="margin-bottom: 0px;margin-right:10px">{{getFlightDuration(num).hrs + 'h ' +
                                    getFlightDuration(num).mins + 'm'}}</span> <span class="dot"></span>
                                                <span class="block flight-airport" [attr.translate]="'no'">{{getFlightLegSource(num)}} - </span>




                                                <span class="block flight-airport" [attr.translate]="'no'"  style="margin-right:10px">{{getFlightLegDestination(num)}}</span>
                                                <span *ngIf="flightLayoverInfoList.length >0 && flightLayoverInfoList[0][0]" class="dot"></span>
                                                <span *ngIf="flightLayoverInfoList.length >0 && flightLayoverInfoList[0][0]" class="block flight-airport"> <span >{{getAllLayoverTimeHour(flightLayoverInfoList)}}</span>
                                     {{getAllLayoverTime(flightLayoverInfoList)}}m layover</span>
                                            </div>

                                            <div class="row" style="margin-left: 0px !important;">
                                                <span class="block flight-airport" *ngFor="let hop of flight.legs[num].flightHops;let j=index" [ngStyle]="{'margin-right':flight.legs[num].flightHops.length-1 ===j ?'10px':'2px'}">
            {{hop.carrier}}{{hop.flightNumber}}<span *ngIf="flight.legs[num].flightHops.length-1 !==j">,</span>
                                                </span><span class="dot"></span>
                                                <span class="block flight-airport" style="margin-right: 4px;">{{this.airlines[flight.legs[num].flightHops[0].carrier]}}
        </span>
                                                <span *ngIf="getNumberCarriers(flight) >0" class="block flight-airport" style="margin-right: 4px;">
        + {{getNumberCarriers(flight)}} Airline
     </span>
                                                <span *ngIf="flight.legs[num].flightHops[0].operatingCarrier
        && flight.legs[num].flightHops[0].operatingCarrier !== flight.legs[num].flightHops[0].carrier" class="block flight-airport">
         operated by {{this.airlines[flight.legs[num].flightHops[0].operatingCarrier]}}
     </span>
                                            </div>
                                            <div class="flight-facilities" [ngStyle]="{'border-top': isAttributAvailable() ? 'none':'none'}">
                                                <ul>
                                                    <li *ngIf="isPreferredAirline()" [class]="active" style="flex: 0 0 auto; min-width:50px;">
                                                        <i class="fare-attr-pref-airline fa fa-plane" aria-hidden="true"></i>
                                                        <span>{{'flightChart.Airline' | translate}}</span>
                                                    </li>
                                                    <li *ngIf="isPreferredAlliance()" [class]="active" style="flex: 0 0 auto; min-width:50px;">
                                                        <i class="fare-attr-pref-airline fa fa-plane" aria-hidden="true"></i>
                                                        <span>{{'flightChart.Alliance' | translate}}</span>
                                                    </li>
                                                    <li *ngIf="getWifiAttributeMoreOptions(flight) === 'INCLUDED' || getWifiAttributeMoreOptions(flight) === 'AVAILABLEFORCHARGE'" style="flex: 0 0 auto; min-width:50px;">
                                                        <img class="green-img" *ngIf="getWifiAttributeMoreOptions(flight) === 'INCLUDED'" src="../../../assets/images/wifi-green.svg">

                                                        <img class="green-img" *ngIf="getWifiAttributeMoreOptions(flight) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/wifi-green.svg">
                                                        <span  *ngIf="getWifiAttributeMoreOptions(flight) === 'AVAILABLEFORCHARGE'" >{{'flightChart.WiFi' | translate}}<label style="margin-left: 4px;">{{'flightChart.atcost' | translate}}</label></span>
                                                        
                                                                    <span *ngIf="getWifiAttributeMoreOptions(flight) == 'INCLUDED'" >{{'flightChart.FreeWiFi' | translate}}</span>
                                                    </li>
                                                    <li *ngIf="isPreferredAirport()" [class]="active" style="flex: 0 0 auto; min-width:50px;">
                                                        <img class="green-img" src="assets/images/flight-list/airport.svg" />
                                                        <span>{{'flightChart.Airport' | translate}}</span>
                                                    </li>
                                                    <li *ngIf="this.showCreditAppliedIcon()" style="flex: 0 0 auto; min-width:50px;">
                                                        <img class="green-img" style="max-width: 15px;max-height: 15px;" src="assets/images/credit-applied-green.svg" /><span>{{'flightChart.CreditApplied' | translate}}</span>
                                                    </li>
                                                    <li *ngIf="this.showCreditNotAppliedReasonLink()" style="flex: 0 0 auto; min-width:50px;">
                                                         <a href="https://help.routespring.com/portal/en/community/topic/why-is-my-airline-credit-not-applied-to-some-of-the-flights" target="_blank">  <span class="creditNotappiledReason">{{'flightChart.whywasflightcreditnotappliedtothisflight' | translate}}</span></a>
                                                        </li>
                                                    <li *ngIf="isRedEye()" class="not-active" style="flex: 0 0 auto; min-width:50px;">
                                                        <img class="red-img" src="assets/images/flight-list/redeye.svg" />
                                                        <span style="color:#F73F39">{{'flightChart.Redeye' | translate}}</span>
                                                    </li>


                                                    <li *ngIf="isOverNightFlight()" class="not-active" style="flex: 0 0 auto; min-width:50px;">

                                                        <img class="red-img" src="assets/images/flight-list/overnight.svg" />
                                                        <span>{{'flightChart.Overnightlayover' | translate}}</span>
                                                    </li>


                                                </ul>
                                                <label *ngIf="getBrandFareText()" class="checkbox-branded-fare-container-mobile d-sm-block d-md-none">
         <input *ngIf="showBrandedFareCheckBox()" type="checkbox" [(ngModel)]="brandedFareCheckBox"
            data-md-icheck (change)="onBrandedFareClick($event)" />
         <span class="branded-fare-label-text">
            {{getBrandFareText()}}
         </span>
      </label>
                                            </div>

                                        </div>


                                    </div>

                                </div>
                                <!-- <div class="popUpShow-d detailsCss2 "   (click)=" OpenModal(index,0,searchModal)" *ngIf="checkMultiFare == false && colspanShowBtn == false" >Show Details</div> -->
                                <!-- <div class="popUpShow-d detailsCss " [id]="'Details'+ index"  (click)=" CloseBtn(index)" *ngIf="checkMultiFare == false" >Hide Details</div> 
                           <div class="popUpShow-h detailsCss" [id]="'hideDetailsMobile'+ index"  (click)="OpenModalMobile(index)" >Show Details</div> -->
                                <div *ngIf="isMobile && (!this.colspanShowBtn || this.openBox!==index)" style="text-align: center;font-size: 16px">
                                    <h5 class="addlue">Options from {{legType ==='RETURN' && getPrice() >= 0.5 ? '' : (getPrice()
                                        <=- 0.5 ? '' : '')}}{{getAbsoluteNumber(getPrice()) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }}</h5>
                                </div>
                            </div>
                            <div class="flight-price-div text-center d-block d-md-none">

                                <div class="PriceDetailsBox-single" (click)="inMobileselectMoreOptions(searchModal)" *ngIf="checkMultiFare == true ">
                                    <h3 class="addlue">{{legType ==='RETURN' && getPrice() >= 0.5 ? '' : (getPrice()
                                        <=- 0.5 ? '' : '')}}{{getAbsoluteNumber(getPrice()) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }}</h3>
                                            <div class="centerLine" style="margin-top: 20px; margin-bottom: 10px;"></div>
                                            <p style="margin-top: 20px;margin-bottom: 10px;">
                                                <span *ngIf="showWithinPolicy() && isWithinPolicy() == true">
                                       <i class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                    </span>
                                                <span *ngIf="showWithinPolicy() && isWithinPolicy() == false"><img
                                          src="../../../assets/images/flight-list/redCross.png"
                                          class="iconInBox"></span> Policy
                                            </p>
                                            <p style="margin-right:10px;">
                                                <span *ngIf="getSeatFareAttribute() == 'NOTFOUND'"><img
                                          src="../../../assets/images/flight-list/redCross.png"
                                          class="iconInBox"></span>
                                                <span *ngIf="getSeatFareAttribute() !== 'NOTFOUND'">
                                       <img *ngIf="getSeatFareAttribute() == 'INCLUDED'"
                                          src="../../../assets/images/flight-list/greenTick.png" class="iconInBox">
                                       <img *ngIf="getSeatFareAttribute() == 'NOTOFFERED'"
                                          src="../../../assets/images/flight-list/ban.png" class="iconInBox">
                                       <img *ngIf="getSeatFareAttribute() == 'AVAILABLEFORCHARGE'"
                                          src="../../../assets/images/flight-list/dollarRed.png" class="iconInBox">
                                    </span>Seat
                                            </p>
                                            <button class="btn primary-button" (click)="OpenModalForRoundTrip(index,-1,searchModal,$event)" style="margin-top: 10px;font-size: 7px;">{{ToggleHideShowButton}}</button>
                                </div>

                            </div>
                        </div>



                    </ng-container>
                    <ng-container *ngIf="isMobile &&  checkMultiFare == false">

                        <div class="popUpShow hide popUpShow-d {{'popUp'+ index}}" *ngFor="let num of noOfFlightLegs; let count = index;">
                            <div>
                                <div class="modal-container flight-modal-container flight-Info-container">
                                    <!-- <div class="modal-header">
                              <h5 class="modal-title" id="myModalLabel">
                                 {{'flightChart.Departure' | translate}} {{getFlightLegSource(0)}} -> {{getFlightLegDestination(flight.legs.length - 1)}}
                              </h5>   
                           </div> -->
                                    <!-- <button type="button" class="close" style="POSITION: ABSOLUTE;color: rgb(14, 12, 12);TOP: 212PX;right: 10px;" data-dismiss="modal" (click)=" CloseBtn(index)" ><i class="material-icons">close</i></button> -->
                                    <div class="modal-body" style="position: relative;">

                                        <ng-container *ngFor="let num of noOfFlightLegs">
                                            <div class="flight-schedule">
                                                <ng-container *ngFor="let flightHop of flight.legs[num].flightHops; let i=index">
                                                    <div class="flight-box">
                                                        <div class="flight-box-left">
                                                            <img onerror="this.onerror = null; this.src = 'https://s3.amazonaws.com/images.biztravel.ai/template/default.gif';" [src]="getFlightIconURL(flightHop.carrier)" />
                                                        </div>
                                                        <div class="flight-box-right">
                                                            <!--   <div *ngIf="checkMultiFare == false" class="flight-ticket-detail">
                                                         {{getSelectedOtherFareBrandNameForSpecificHop(num,i)}}
                                                      </div>-->

                                                            <div class="row" style="margin-top: 0px;margin-left: 0 !important;margin-right: 0 !important">
                                                                <div class="col-6" style="text-align: left;">
                                                                    <div class="date">{{flightHop.carrier}}{{flightHop.flightNumber}}</div>
                                                                    <div style="margin-top: 10px;" *ngIf="flight.legs[num].flightHops.length >1" class="block flight-time">{{getDisplayDate(flight.legs[num].flightHops[i].starts)| date:'h:mm a'}} <span class="extra-time1">{{getDepartureDaysDifferenceForLayover(i)}}</span></div>
                                                                    <div *ngIf="flight.legs[num].flightHops.length >1" class="flight-timings-lineDetail"></div>

                                                                    <div class="date">Departure</div>
                                                                    <div class="detailcity" style="text-align: left;line-height: 18px">{{flight.legs[num].flightHops[i].from}} - {{this.airports[flight.legs[num].flightHops[i].from].name}}</div>
                                                                    <div class="dateDetails" style="text-align: left;">{{getDisplayDate(flight.legs[num].flightHops[i].starts) | date:'EEEE, MMMM d, y'}}
                                                                    </div>
                                                                </div>

                                                                <div class="col-6" style="text-align: left;">
                                                                    <div class="date">{{getHopDuration(flightHop.duration).hrs + 'h ' + getHopDuration(flightHop.duration).mins + 'm'}}</div>
                                                                    <div style="margin-top: 10px;" *ngIf="flight.legs[num].flightHops.length >1" class="block flight-time">{{getDisplayDate(flight.legs[num].flightHops[i].ends)| date:'h:mm a'}} <span class="extra-time1">{{getArrivalDaysDifferenceForLayover(i)}}</span></div>


                                                                    <div class="date">Arrival</div>
                                                                    <div class="detailcity" style="line-height: 18px">{{flight.legs[num].flightHops[i].to}} - {{this.airports[flight.legs[num].flightHops[i].to].name}}</div>
                                                                    <div class="dateDetails">{{getDisplayDate(flight.legs[num].flightHops[i].ends) | date:'EEEE, MMMM d, y'}}
                                                                    </div>
                                                                </div>

                                                            </div>



                                                        </div>
                                                    </div>
                                                    <div class="flight-layover" *ngIf="flightLayoverInfoList[num][i]" style="justify-content: center;">
                                                        <span class="flight-layover-right date">{{flightLayoverInfoList[num][i].duration.hrs +
                                                'h ' + flightLayoverInfoList[num][i].duration.mins + 'm'}}</span> <span class="dotBlack" style="margin-right: 4px;margin-left: 4px;"></span>
                                                        <span class="flight-layover-left">{{'flightChart.Layoverin' | translate}}
                                                {{flightLayoverInfoList[num][i].in}}</span>

                                                    </div>
                                                </ng-container>
                                            </div>
                                        </ng-container>

                                    </div>

                                </div>
                                <div class="BoxSlected">

                                    <div class="row" class="DetailsBoxLoader" *ngIf="showloader == true" style="position: relative;margin-bottom: 50px;">
                                        <app-loader style="text-align:center;margin-left:  auto !important;margin-right: auto !important;position: absolute;top: 55%;" [spinnerStyle]="true"></app-loader>
                                    </div>

                                    <div class="Slected-Box-ContentMobile" *ngIf="boxShow == true">
                                        <div *ngFor="let item of  tempStarsArrayFlight,let j = index" class="mobileDetailsBox" style="position: relative;">
                                            <div class="row">
                                                <div class="col-8" (click)="openFareBoxForMobileFlightFeature(item,index,j)">
                                                    <div class="row">
                                                        <div class="col-7" style="text-align: left;">
                                                            <div> <img src="{{starsSeats[item.ngsStars-1]}}" style="margin-right:5px;" /> <span class="starsValue">{{this.starsArray[item.ngsStars-1] | translate}}</span></div>
                                                        </div>
                                                        <div class="col-5" style="text-align: left;">
                                                            <h4 class="addlue">{{legType ==='RETURN' && getPriceForMoreOptions(item) >= 0.5 ? '' :((getPriceForMoreOptions(item)
                                                                <=- 0.5)? '': '')}}{{getAbsoluteNumber(getPriceForMoreOptions(item)) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }}</h4>
                                                        </div>
                                                    </div>
                                                    <div class="row" style="margin-top: 10px;">
                                                        <div class="col-12">



                                                            <div style=" white-space: break-spaces;color:#9B9B9B;max-height: 40px;
                                             overflow: hidden;min-height: 40px;
                                             ">
                                                                <span style="text-align: left;
                                                   float: left;width: 100%;"> 
                                                     <span style="display: flex;justify-content: space-between;
                                                      padding-right: 0px;float: right;min-width: 80px;">
                                                          
                                                               
                                                                  <span *ngIf="getSeatFareAttributeMoreOptions(item) !== 'NOTFOUND'" style="white-space: nowrap;">
                                                                        <img
                                                                        *ngIf="getSeatFareAttributeMoreOptions(item) === 'INCLUDED'" 
                                                                        src="../../../assets/images/seat-green.svg"
                                                                        >
                                                                       
                                                                        <img
                                                                        *ngIf="getSeatFareAttributeMoreOptions(item) === 'NOTOFFERED' || getSeatFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" 
                                                                        src="../../../assets/images/seat-red.svg"
                                                                        >
                                                                    
                                                                  </span>
                                                                <span *ngIf="getCarryOnBagFareAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                        <img
                                                                        *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'INCLUDED'" 
                                                                        src="../../../assets/images/carry-on-green.svg"
                                                                        >
                                                                       
                                                                        <img
                                                                        *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'NOTOFFERED' || getCarryOnBagFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" 
                                                                        src="../../../assets/images/carry-on-red.svg"
                                                                        >
                                                                    
                                                                  </span>
                                                                <span *ngIf="getChangesFareAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                        <img style="transform: rotate(90deg);"
                                                                        *ngIf="getChangesFareAttributeMoreOptions(item) === 'INCLUDED'" 
                                                                        src="../../../assets/images/changes-green.svg"
                                                                        >
                                                                       
                                                                        <img
                                                                        *ngIf="getChangesFareAttributeMoreOptions(item) === 'NOTOFFERED' || getChangesFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" 
                                                                        src="../../../assets/images/changes-red.svg"
                                                                        >
                                                                    
                                                                  </span>
                                                                <span *ngIf="getLegroomFareAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                        <img
                                                                        *ngIf="getLegroomFareAttributeMoreOptions(item) === 'INCLUDED'" 
                                                                        src="../../../assets/images/leg-space-green.svg"
                                                                        >
                                                                       
                                                                        <img
                                                                        *ngIf="getLegroomFareAttributeMoreOptions(item) === 'NOTOFFERED' || getLegroomFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" 
                                                                        src="../../../assets/images/leg-space-red.svg"
                                                                        >
                                                                       
                                                                  </span>

                                                                </span><span *ngIf="checkNoWordsGreterthanOne(getBrandNameForMaxClass(item))===1"><p class="fareclassOld">{{ getBrandNameForMaxClass(item)}}</p></span>
                                                                <span *ngIf="checkNoWordsGreterthanOne(getBrandNameForMaxClass(item)) > 1"><p class="fareclassOld">{{firstWordOfClassName(getBrandNameForMaxClass(item))}}</p>
                                                               <p class="fareclassNew">{{lastwordsOfclassName(getBrandNameForMaxClass(item))}}</p></span>
                                                                </span>

                                                            </div>

                                                        </div>

                                                    </div>

                                                </div>
                                                <div class="col-4" style="margin-top: 20px;padding-left: 0px;">
                                                    <div style="display: block;" [ngStyle]="checkPolicy(item)">
                                                        <button class="btn primary-button" style="font-size: 9px;padding: 5px;" [disabled]="disableButton" (click)="selectFlight(j)">{{'flightChart.Select' |
                                          translate}}</button>
                                                        <div *ngIf="this.creditDetails && this.creditDetails.length >0 && item.ngsStars > 1 && isResidualAmountIsGreaterTahnMaxresidualAmount(item)" class="block flight-trip-type" style="margin-right: 5px !important;font-size: 11px;margin:0px 10px; ">
                                                            {{'flightChart.GrossFare' | translate}} {{legType ==='RETURN' && getPriceForMoreOptions1(item) >= 0.5 ? '+' : (getPriceForMoreOptions1(item)
                                                            <=- 0.5 ? '-' : '' )}} {{getAbsoluteNumber(getPriceForMoreOptions1(item)) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }} </div>
                                                                <label *ngIf="getBrandFareText()" class="checkbox-branded-fare-container">
                                                      <input *ngIf="showBrandedFareCheckBox()" type="checkbox"
                                                         [(ngModel)]="brandedFareCheckBox" data-md-icheck
                                                         (change)="onBrandedFareClick($event)" />
                                                      {{getBrandFareText()}}
                                                   </label>



                                                        </div>
                                                    </div>

                                                    <div *ngIf="!item.showFeatureInmobile && showWithinPolicy() && isWithinPolicy(item) == false" class="notInPolicybox">
                                                        <span class="policyText"> {{'flightChart.NotinPolicy' | translate}}</span>
                                                    </div>

                                                </div>


                                                <div *ngIf="item.showFeatureInmobile">
                                                    <div class="">
                                                        <div>
                                                            <div class="">

                                                                <div class="" style="position: relative;padding: 0 11px;margin-top: 10px;">

                                                                    <div class="row">
                                                                        <div style="width:100%;">
                                                                            <div class="MobileFligthFeatureList">
                                                                                <ul class="row">
                                                                                    <li *ngIf="getCorporateFareAttributeMoreOptions(this.selectedMobileFeatureFlight)" style="white-space: nowrap;">
                                                                                        <i class="fa fa-star" style="margin-right: 4px;color: #3CBF9A;"></i>

                                                                                        <span style="white-space: break-spaces;">{{'hotelResult.Corporaterate' | translate}}</span>
                                                                                    </li>
                                                                                    <li>
                                                                                        <img *ngIf="showWithinPolicy() && isWithinPolicy(this.selectedMobileFeatureFlight) == true" src="../../../assets/images/policy-green.svg">
                                                                                        <img *ngIf="showWithinPolicy() && isWithinPolicy(this.selectedMobileFeatureFlight) == false" src="../../../assets/images/policy-red.svg">




                                                                                        <span>{{'flightChart.InPolicy' | translate}}</span>

                                                                                    </li>

                                                                                    <li *ngIf="getSeatFareAttributeMoreOptions(this.selectedMobileFeatureFlight) !== 'NOTFOUND'" style="white-space: nowrap;">
                                                                                        <img *ngIf="getSeatFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED'" src="../../../assets/images/seat-green.svg">

                                                                                        <img *ngIf="getSeatFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED' || getSeatFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/seat-red.svg">
                                                                                        <span *ngIf="getSeatFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED' "style="white-space:break-spaces;">{{'flightChart.Seatchoice' | translate}}</span>
                                                                                        <span *ngIf="getSeatFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED' || getSeatFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE'" style="white-space:break-spaces;">{{'flightChart.NoSeatchoice' | translate}}</span>
                                                                                    </li>

                                                                                    <li *ngIf="isMixed(this.selectedMobileFeatureFlight)" class="not-active  " style="flex: 0 0 auto; min-width:50px;">
                                                                                        <i class="fa fa-exclamation-triangle  fare-attr-red" aria-hidden="true"></i>
                                                                                        <span style=" color: rgb(249, 61, 48);">{{'flightChart.Mixedclass' | translate}}</span>
                                                                                    </li>
                                                                                    <li *ngIf="getCarryOnBagFareAttributeMoreOptions(this.selectedMobileFeatureFlight) !== 'NOTFOUND'">
                                                                                        <img *ngIf="getCarryOnBagFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED'" src="../../../assets/images/carry-on-green.svg">

                                                                                        <img *ngIf="getCarryOnBagFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED'" src="../../../assets/images/carry-on-red.svg">
                                                                                        <img *ngIf="getCarryOnBagFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/carry-on-blue.svg">
                                                                                        <span *ngIf="getCarryOnBagFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED' ">{{'flightChart.Carryonbag' | translate}}</span>
                                                                                        <span *ngIf="getCarryOnBagFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE' " class="atCharge">{{'flightChart.Feeforcarryon' | translate}}</span>
                                                                                        <span *ngIf="getCarryOnBagFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED'">{{'flightChart.NoCarryonbag' | translate}}</span>
                                                                                    </li>

                                                                                    <li *ngIf="getBaggageDetails(this.selectedMobileFeatureFlight)" class="not-active" style="flex: 0 0 auto; min-width:50px;white-space: nowrap;">
                                                                                            <img *ngIf="getBaggageNumber(this.selectedMobileFeatureFlight) ===1 || getBaggageNumber(this.selectedMobileFeatureFlight) >3" src="../../../assets/images/checked-bag-green.svg">
                                                                                            <img *ngIf="getBaggageNumber(this.selectedMobileFeatureFlight) ===2" src="../../../assets/images/checked-bag-green2.svg">
                                                                                            <img *ngIf="getBaggageNumber(this.selectedMobileFeatureFlight) ===3" src="../../../assets/images/checked-bag-green3.svg">
                    
                                                                                            <img *ngIf="getBaggageDetailsColor(this.selectedMobileFeatureFlight)==='notallowed'" src="../../../assets/images/checked-bag-red.svg">
                                                                                       
                                                                                        <span style="white-space: break-spaces;">{{getBaggageDetails(this.selectedMobileFeatureFlight)}}</span>
                                                                                    </li>
                                                                                    <li *ngIf="getRefundAttributeMoreOptions(this.selectedMobileFeatureFlight) !== 'NOTFOUND'">
                                                                                        <img *ngIf="getRefundAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED'" src="../../../assets/images/refund-green.svg">

                                                                                        <img *ngIf="getRefundAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED'" src="../../../assets/images/refund-red.svg">
                                                                                        <img *ngIf="getRefundAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/refund-blue.svg">
                                                                                        <span *ngIf="getRefundAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED' ">{{'flightChart.Refundable' | translate}}</span>
                                                                                        <span *ngIf="getRefundAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE' " class="atCharge">{{'flightChart.Feeforrefund' | translate}}</span>
                                                                    <span *ngIf="getRefundAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED' ">{{'flightChart.NotRefundable' | translate}}</span>
                                                                                    </li>
                                                                                    <li *ngIf="getChangesFareAttributeMoreOptions(this.selectedMobileFeatureFlight) !== 'NOTFOUND'">
                                                                                        <img *ngIf="getChangesFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED'" src="../../../assets/images/changes-green.svg">

                                                                                        <img *ngIf="getChangesFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED' " src="../../../assets/images/changes-red.svg">
                                                                                        <img *ngIf="getChangesFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE' " src="../../../assets/images/changes-blue.svg">
                                                                                        <span *ngIf="getChangesFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED' ">{{'flightChart.Changes' | translate}}</span>
                                                                                        <span *ngIf="getChangesFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE' " class="atCharge">{{'flightChart.Feeforchanges' | translate}}</span>
                                                                                        <span *ngIf="getChangesFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED'">{{'flightChart.NoChanges' | translate}}</span>
                                                                                    </li>
                                                                                    <li *ngIf="getLegroomFareAttributeMoreOptions(this.selectedMobileFeatureFlight) !== 'NOTFOUND'">
                                                                                        <img *ngIf="getLegroomFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED'" src="../../../assets/images/leg-space-green.svg">

                                                                                        <img *ngIf="getLegroomFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED' || getLegroomFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/leg-space-red.svg">
                                                                                        <span *ngIf="getLegroomFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED' "style="white-space: break-spaces;">{{'flightChart.legRoom' | translate}}</span>
                                                                                        <span *ngIf="getLegroomFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED' || getLegroomFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE'" style="white-space: break-spaces;">{{'flightChart.NolegRoom' | translate}}</span>
                                                                                    </li>
                                                                                    <li *ngIf="getWifiAttributeMoreOptions(this.selectedMobileFeatureFlight) !== 'NOTFOUND'" style="flex: 0 0 auto; min-width:80px;">
                                                                                        <img *ngIf="getWifiAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED'" src="../../../assets/images/wifi-green.svg">

                                                                                        <img *ngIf="getWifiAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED' || getWifiAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/wifi-red.svg">
                                                                                        <span *ngIf="getWifiAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE'">{{'flightChart.WiFi' | translate}}<label  style="margin-left: 4px;">{{'flightChart.atcost' | translate}}</label></span>
                                                                                        <span *ngIf="getWifiAttributeMoreOptions(this.selectedMobileFeatureFlight) == 'NOTOFFERED'">{{'flightChart.NOWiFi' | translate}}</span>
                                                                                        <span *ngIf="getWifiAttributeMoreOptions(this.selectedMobileFeatureFlight) == 'INCLUDED'" >{{'flightChart.FreeWiFi' | translate}}</span>
                                                                                    </li>
                                                                                    <li *ngIf="getMealFareAttributeMoreOptions(this.selectedMobileFeatureFlight) !== 'NOTFOUND'">
                                                                                        <img *ngIf="getMealFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED'" src="../../../assets/images/meal-green.svg">

                                                                                        <img *ngIf="getMealFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED' || getMealFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/meal-red.svg">
                                                                                        <span *ngIf="getMealFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED'">{{'flightChart.freeMeal' | translate}}</span>
                                                                                        <span *ngIf="getMealFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'AVAILABLEFORCHARGE'">{{'flightChart.Meal' | translate}}</span>
                                                                                                            <span *ngIf="getMealFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'NOTOFFERED'">{{'flightChart.NOMeal' | translate}}</span>
                                                                                    </li>

                                                                                    <div class="addlue" style="font-size: 16px;float: right;" (click)="openFareBoxForMobileFlightFeature(item,index,j)">Close</div>
                                                                                </ul>

                                                                            </div>

                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>


                                        </div>


                                    </div>
                                    <div class="addlue" *ngIf="isMobile && boxShow" style="text-align: center;" (click)="$event.stopPropagation();CloseBtn(index)">
                                        <h5> Hide options</h5>
                                    </div>
                                </div>
                            </div>

                    </ng-container>



                    <!-- SingleFare colSpan -->
                    <div class="colSpanForRoundTrip" *ngIf="SingleDetailsBoxShow == true && checkMultiFare == true ">
                        <div class="popUpShow-single popUpShow-d" [id]="'popUp'+ index" *ngFor="let num of noOfFlightLegs;let count = index;">
                            <div style="display:flex">
                                <div class="modal-container flight-modal-container" style="width:40%;cursor: default;">
                                    <!-- <div class="modal-header">
                                 <h5 class="modal-title" id="myModalLabel">
                                    {{'flightChart.Departure' | translate}} {{getFlightLegSource(0)}} -> {{getFlightLegDestination(flight.legs.length - 1)}}
                                 </h5>   
                              </div> -->
                                    <!-- <button type="button" class="close" style="POSITION: ABSOLUTE;color: rgb(14, 12, 12);TOP: 212PX;right: 10px;" data-dismiss="modal" (click)=" CloseBtn(index)" ><i class="material-icons">close</i></button> -->
                                    <div class="modal-body" style="position: relative;">
                                        <!-- <div  style="height: 2px;width: 1015px;background-color: #0060f5;position: absolute;top: -57%;z-index: 1;box-shadow: rgba(0, 0, 0, 1.35) 0px -1px 8px;clip-path: inset(-10px -14px  0px );" ></div> -->
                                        <ng-container *ngFor="let num of noOfFlightLegs">
                                            <div class="date-duration">
                                                <div class="date">{{getDisplayDate(flight.legs[num].flightHops[0].starts) | date:'EE, MMM d'}}
                                                </div>
                                                <div class="duration">
                                                    <label>{{'flightChart.TotalDuration' | translate}} </label>
                                                    <span style="margin-left: 2px;"> {{getFlightDuration(num).hrs + 'h ' +
                                          getFlightDuration(num).mins + 'm'}}</span>
                                                </div>
                                            </div>
                                            <div class="flight-schedule">
                                                <ng-container *ngFor="let flightHop of flight.legs[num].flightHops; let i=index">
                                                    <div class="flight-box">
                                                        <div class="flight-box-left">
                                                            <img onerror="this.onerror = null; this.src = 'https://s3.amazonaws.com/images.biztravel.ai/template/default.gif';" [src]="getFlightIconURL(flightHop.carrier)" />
                                                        </div>
                                                        <div class="flight-box-right">
                                                            <div class="flight-modal-name-duration">
                                                                <span class="flight-modal-name">{{getAirlineFullName(flightHop.carrier)}}
                                                   {{flightHop.flightNumber}}</span>
                                                                <span class="flight-modal-duration">{{getHopDuration(flightHop.duration).hrs
                                                   + 'h ' + getHopDuration(flightHop.duration).mins + 'm'}}</span>
                                                            </div>
                                                            <div class="flight-ticket-detail">{{getClassStringWithBrand(i, flight.legs[num],flightHop)}} {{flightHop.operatingCarrier && flightHop.operatingCarrier !== flightHop.carrier ? 'Operated by ' + getAirlineFullName(flightHop.operatingCarrier)
                                                                : ''}}
                                                            </div>
                                                            <div class="flight-timing-stops">
                                                                <ul>
                                                                    <li><label>{{getDisplayDate(flightHop.starts) | date:'EEE
                                                         h:mma'}}</label> <span>{{getAirportCity(flightHop.from)}}
                                                         ({{flightHop.from}})</span>
                                                                    </li>
                                                                    <li><label>{{getDisplayDate(flightHop.ends) | date:'EEE
                                                         h:mma'}}</label> <span>{{getAirportCity(flightHop.to)}}
                                                         ({{flightHop.to}})</span>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flight-layover" *ngIf="flightLayoverInfoList[num][i]">
                                                        <span class="flight-layover-left">{{'flightChart.Layoverin' | translate}}
                                             {{flightLayoverInfoList[num][i].in}}</span>
                                                        <span class="flight-layover-right">{{flightLayoverInfoList[num][i].duration.hrs +
                                             'h ' + flightLayoverInfoList[num][i].duration.mins + 'm'}}</span>
                                                    </div>
                                                </ng-container>
                                            </div>
                                        </ng-container>

                                    </div>

                                </div>
                                <div class="BoxSlected">

                                    <div *ngIf="showloader == true" class="row" class="DetailsBoxLoader">
                                        <app-loader [spinnerStyle]="true" class="loader_position"></app-loader>
                                    </div>

                                    <div class="Slected-Box-Content" *ngIf="boxShow == true" [ngStyle]="{'width.px':checkDetailBoxSize(this.selectionOption.length)}">


                                        <div *ngFor="let item of  this.selectionOption,let j = index" class="detailes_price_Box">

                                            <h4>{{legType ==='RETURN' && getPriceForMoreOptions(item) >= 0.5 ? '' :((getPriceForMoreOptions(item)
                                                <=- 0.5)? '': '')}}{{getAbsoluteNumber(getPriceForMoreOptions(item)) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }}</h4>
                                                    <p>{{item.legs[0].flightHops[0].fareClassName}} </p>
                                                    <hr>
                                                    <div class="HopeDetails">

                                                        <div class="flight-feature-list">
                                                            <ul>
                                                                <li *ngIf="getCorporateFareAttributeMoreOptions(item)" style="white-space: nowrap;">
                                                                    <i class="fa fa-star" style="margin-right: 4px;color: #3CBF9A"></i>

                                                                    <span style="white-space: break-spaces;">{{'hotelResult.Corporaterate' | translate}}</span>
                                                                </li>
                                                                <li>

                                                                    <i *ngIf="showWithinPolicy() && isWithinPolicy(item) == false" class="fa fa-times fare-attr-red"></i>
                                                                    <i *ngIf="showWithinPolicy() && isWithinPolicy(item) == true" class="fare-attr-green fa fa-check" aria-hidden="true"></i>

                                                                    <span style="">{{'flightChart.Policy' | translate}}</span>

                                                                </li>

                                                                <li *ngIf="getSeatFareAttributeMoreOptions(item) !== 'NOTFOUND'" style="white-space: nowrap;">
                                                                    <i *ngIf="getSeatFareAttributeMoreOptions(item) == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                                                    <i *ngIf="getSeatFareAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                                                    <i *ngIf="getSeatFareAttributeMoreOptions(item) == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                                                    <span style="white-space: break-spaces;">{{'flightChart.Seatchoice' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getWifiAttributeMoreOptions(item) !== 'NOTFOUND'" style="flex: 0 0 auto; min-width:80px;">
                                                                    <i *ngIf="getWifiAttributeMoreOptions(item) == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                                                    <i *ngIf="getWifiAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                                                    <i *ngIf="getWifiAttributeMoreOptions(item) == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                                                    <span *ngIf="getWifiAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE' || getWifiAttributeMoreOptions(item) == 'NOTOFFERED'">{{'flightChart.WiFi' | translate}}</span>
                                                                    <span *ngIf="getWifiAttributeMoreOptions(item) == 'INCLUDED'" >{{'flightChart.FreeWiFi' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getChangesFareAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                    <i *ngIf="getChangesFareAttributeMoreOptions(item) == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                                                    <i *ngIf="getChangesFareAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                                                    <i *ngIf="getChangesFareAttributeMoreOptions(item) == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                                                    <span *ngIf="getChangesFareAttributeMoreOptions(item) !== 'AVAILABLEFORCHARGE'">{{'flightChart.Changes' | translate}}</span> <span *ngIf="getChangesFareAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="atCharge">{{'flightChart.Feeforchanges' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getMealFareAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                    <i *ngIf="getMealFareAttributeMoreOptions(item) == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                                                    <i *ngIf="getMealFareAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                                                    <i *ngIf="getMealFareAttributeMoreOptions(item) == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                                                    <span *ngIf="getMealFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED'">{{'flightChart.Meal' | translate}}</span>
                                                                    <span *ngIf="getMealFareAttributeMoreOptions(this.selectedMobileFeatureFlight) === 'INCLUDED'">{{'flightChart.NOMeal' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getSeatFareAttributeMoreOptions(item) !== 'NOTFOUND'" style="white-space: nowrap;">
                                                                    <i *ngIf="getSeatFareAttributeMoreOptions(item) == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                                                    <i *ngIf="getSeatFareAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                                                    <i *ngIf="getSeatFareAttributeMoreOptions(item) == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                                                    <span style="white-space: break-spaces;">{{'flightChart.Seatchoice' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getBaggageDetails(item)" class="not-active" style=" min-width:50px;white-space: nowrap;">
                                                                        <img *ngIf="getBaggageNumber(item) ===1" src="../../../assets/images/checked-bag-green.svg">
                                                                        <img *ngIf="getBaggageNumber(item) ===2" src="../../../assets/images/checked-bag-green2.svg">
                                                                        <img *ngIf="getBaggageNumber(item) ===3" src="../../../assets/images/checked-bag-green3.svg">

                                                                        <img *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'NOTOFFERED'" src="../../../assets/images/checked-bag-red.svg">
                                                                    <span style="white-space: break-spaces;">{{getBaggageDetails(item)}}</span>
                                                                </li>
                                                                <li *ngIf="getCarryOnBagFareAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                    <i *ngIf="getCarryOnBagFareAttributeMoreOptions(item) == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                                                    <i *ngIf="getCarryOnBagFareAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                                                    <i *ngIf="getCarryOnBagFareAttributeMoreOptions(item) == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                                                    <span *ngIf="getCarryOnBagFareAttributeMoreOptions(item) !== 'AVAILABLEFORCHARGE'">{{'flightChart.Carryonbag' | translate}}</span> <span *ngIf="getCarryOnBagFareAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="atCharge">{{'flightChart.Feeforcarryon' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="false">
                                                                    <i *ngIf="getCheckedBagFareAttributeMoreOptions(item) == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                                                    <i *ngIf="getCheckedBagFareAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                                                    <i *ngIf="getCheckedBagFareAttributeMoreOptions(item) == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                                                    <span>{{'flightChart.Checkedbag' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getRefundAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                    <i *ngIf="getRefundAttributeMoreOptions(item) == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                                                    <i *ngIf="getRefundAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                                                    <i *ngIf="getRefundAttributeMoreOptions(item) == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                                                    <span *ngIf="getRefundAttributeMoreOptions(item) !== 'AVAILABLEFORCHARGE'">{{'flightChart.Refundable' | translate}}</span> <span *ngIf="getRefundAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="atCharge">{{'flightChart.Feeforrefund' | translate}}</span>
                                                                </li>


                                                                <li *ngIf="getLegroomFareAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                    <i *ngIf="getLegroomFareAttributeMoreOptions(item) == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                                                    <i *ngIf="getLegroomFareAttributeMoreOptions(item) == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                                                    <i *ngIf="getLegroomFareAttributeMoreOptions(item) == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                                                    <span>{{'flightChart.legRoom' | translate}}</span>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="block flight-trip-type" style="margin-bottom:5px;" *ngIf="this.creditDetails && this.creditDetails.creditAmount">
                                                        {{getTraveleCreditersName()}} {{'flightChart.GrossFare' | translate}} {{legType ==='RETURN' && getPriceForMoreOptions1(item) >= 0.5 ? '' : (getPriceForMoreOptions1(item)
                                                        <=- 0.5 ? '' : '' )}} {{getAbsoluteNumber(getPriceForMoreOptions1(item)) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }} </div>
                                                            <div class="block flight-trip-type" style="position: absolute;margin-bottom:5px;float: left;margin-left: 15px;cursor: pointer;width: 100%;text-align: left;bottom: 28%;left: -5px;" *ngIf="item.marketingMessage && item.marketingMessage!==''" (click)="openMarketingMSgModal(item.marketingMessage,marketingmodal)">
                                                                <span class="marketingMsg">
                                                   <span style="font-family: var(--globalFontfamilyr);font-weight: bold;;"> {{'flightChart.FareDetails' |
                                                      translate}}: </span> {{item.marketingMessage}}
                                                                </span>
                                                            </div>
                                                            <!--  -->
                                                            <div *ngIf="this.creditDetails && this.creditDetails.length >0 " class="block flight-trip-type" style="position: absolute;bottom: 5px;font-size: 12px;margin: 0px 20px;">
                                                                {{'flightChart.creditappliedonthetotalfareof' | translate}} {{legType ==='RETURN' && getPriceForMoreOptions1(item) >= 0.5 ? '+' : (getPriceForMoreOptions1(item)
                                                                <=- 0.5 ? '-' : '' )}} {{getAbsoluteNumber(getPriceForMoreOptions1(item)) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }} </div>
                                                                    <label *ngIf="getBrandFareText()" class="checkbox-branded-fare-container">
                                                      <input *ngIf="showBrandedFareCheckBox()" type="checkbox"
                                                         [(ngModel)]="brandedFareCheckBox" data-md-icheck
                                                         (change)="onBrandedFareClick($event)" />
                                                      {{getBrandFareText()}}
                                                   </label>

                                                    <button  *ngIf="this.searchService.trainFilter.length > 0" class="btn primary-button" [disabled]="disableButton" (click)="selectFlight(j)">{{'flightChart.Select' |
                                                      translate}}</button>
                                                    <button  *ngIf="this.searchService.trainFilter.length === 0" class="btn primary-button" [disabled]="disableButton" (click)="selectFlight(j)">{{'flightChart.SELECTFLIGHT' |
                                                      translate}}</button>

                                                            </div>

                                                    </div>

                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>
                    <!-- Desktop layOut Starts here  -->
                    <div [class]="cssChangeWithCount()">


                        <div class="PriceDetailsBox" *ngIf="checkMultiFare == true">

                            <h2>{{legType ==='RETURN' && getPrice() >= 0.5 ? '' : (getPrice()
                                <=- 0.5 ? '' : '')}} {{getAbsoluteNumber(getPrice()) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }} </h2>
                                    <div class="centerLine"></div>
                                    <p>
                                        <i *ngIf="showWithinPolicy() && isWithinPolicy() == false" class="fa fa-times  fare-attr-red"></i>
                                        <span *ngIf="showWithinPolicy() && isWithinPolicy() == true">
                                 <i class="fare-attr-green fa fa-check" aria-hidden="true"></i>

                              </span> Policy
                                    </p>
                                    <p style="margin-right:10px;">
                                        <span *ngIf="getSeatFareAttribute() == 'NOTFOUND'"><img
                                    src="../../../assets/images/flight-list/redCross.png" class="iconInBox"></span>
                                        <span *ngIf="getSeatFareAttribute() !== 'NOTFOUND'">
                                 <img *ngIf="getSeatFareAttribute() == 'INCLUDED'"
                                    src="../../../assets/images/flight-list/greenTick.png" class="iconInBox">
                                 <img *ngIf="getSeatFareAttribute() == 'NOTOFFERED'"
                                    src="../../../assets/images/flight-list/ban.png" class="iconInBox">
                                 <img *ngIf="getSeatFareAttribute() == 'AVAILABLEFORCHARGE'"
                                    src="../../../assets/images/flight-list/dollarRed.png" class="iconInBox">
                              </span> Seat
                                    </p>
                                    <button class="btn primary-button" (click)="OpenModalForRoundTrip(index,-1,searchModal,$event)" style="margin-top:20px;font-size: 10px;">{{ToggleHideShowButton}}</button>

                        </div>

                        <div class="multiChartShow" style="display:flex" *ngIf="checkMultiFare == false">
                            <!-- First_Class -->
                            <div *ngFor="let item of this.classArray;let i=index">
                                <ng-container *ngIf="this.indexArray[i] >= 0">
                                    <div class="PriceDetailsBox-multi {{'Price_Box'+index+'_'+i}}" [ngStyle]="afterStarArrayChange(i,this.indexArray[i])" (click)="OpenModal(index,this.indexArray[i],i,searchModal)">
                                        <div class="starsImg">

                                            <div class="starsValue">{{this.starsArray[i] | translate}}</div>

                                        </div>

                                        <h4 [ngStyle]="getNUmberOfdigitOfPrice(getCurrencySymbol(flight['otherFarePriceAttributes'][this.indexArray[i]]),getOtherFarePrice(this.indexArray[i],flight))">{{getOtherFarePrice(this.indexArray[i],flight) | currency : getCurrencySymbol(flight['otherFarePriceAttributes'][this.indexArray[i]]): 'code' : '1.0-0' }}</h4>
                                        <div class="FareClassNameCss" [ngStyle]="{'max-height' :  (this.selectionOption.length === 0 || this.OtherFlightBoxIndex!==this.indexArray[i]) || !this.colspanShowBtn  ? '31px':'fit-content'}">
                                            <p>{{ getBrandNameForMaxClass( flight['otherFarePriceAttributes'][this.indexArray[i]])}}
                                            </p>
                                        </div>
                                        <div class="h-line" *ngIf="(!this.colspanShowBtn || this.openBox!==i)">
                                            <div class="centerLine"></div>

                                            <p style="margin-right:0px;display: block; white-space: break-spaces;">
                                               
                                                <label class="bigIconInBox" *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.seat == 'INCLUDED'">
                                          <img
                                            
                                             src="../../../assets/images/seat-green.svg"
                                             >
                                             <span class="msgBox1">
                                                {{'flightChart.Seatchoiceincluded' | translate }}
                                             </span>
                                          </label>
                                                <label class="bigIconInBoxerror" *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.seat == 'NOTOFFERED' || flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.seat == 'AVAILABLEFORCHARGE'">
                                          <img
                                             
                                             src="../../../assets/images/seat-red.svg"
                                            > 
                                             <span class="msgBox1">
                                                {{'flightChart.Seatchoice' | translate }}<span *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.seat == 'NOTOFFERED'">{{'flightChart.notincluded' | translate }}</span><span *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.seat == 'AVAILABLEFORCHARGE'">{{'flightChart.atcost' | translate }}</span>
                                             </span>
                                          </label>
                                                <label class="bigIconInBox1" *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.carryBag === 'INCLUDED'">
                                          <img
                                            
                                             src="../../../assets/images/carry-on-green.svg"
                                            >
                                             <span class="msgBox2">
                                                {{'flightChart.Carrybagincluded' | translate }}
                                             </span>
                                          </label>
                                                <label class="bigIconInBoxerror1" *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.carryBag == 'NOTOFFERED' || flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.carryBag === 'AVAILABLEFORCHARGE'">
                                          <img
                                            
                                             src="../../../assets/images/carry-on-red.svg"
                                             >
                                             <span class="msgBox2">
                                                {{'flightChart.Carryonbag' | translate }}<span *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.carryBag == 'NOTOFFERED'">{{'flightChart.notincluded' | translate }}</span><span *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.carryBag == 'AVAILABLEFORCHARGE'">{{'flightChart.atcost' | translate }}</span>
                                             </span>
                                          </label>
                                                <label class="bigIconInBox2" *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.rebooking == 'INCLUDED'">
                                             <img  style="transform: rotate(90deg);"
                                            
                                             src="../../../assets/images/changes-green.svg"
                                             >
                                             <span class="msgBox3">
                                                {{'flightChart.Changesincluded' | translate }}
                                             </span>
                                          </label>
                                          <label class="bigIconInBox5" *ngIf="getBaggageDetailsColor(flight['otherFarePriceAttributes'][this.indexArray[i]])==='allowed'">
                                                <img *ngIf="getBaggageNumber(flight['otherFarePriceAttributes'][this.indexArray[i]]) ===1 || getBaggageNumber(flight['otherFarePriceAttributes'][this.indexArray[i]]) > 3" src="../../../assets/images/checked-bag-green.svg">
                                                <img *ngIf="getBaggageNumber(flight['otherFarePriceAttributes'][this.indexArray[i]]) ===2" src="../../../assets/images/checked-bag-green2.svg">
                                                <img *ngIf="getBaggageNumber(flight['otherFarePriceAttributes'][this.indexArray[i]]) ===3" src="../../../assets/images/checked-bag-green3.svg">
                                                   <span class="msgBox5">
                                                      {{getBaggageDetails(flight['otherFarePriceAttributes'][this.indexArray[i]]) }}
                                                   </span>
                                                </label>
                                                      <label class="bigIconInBoxerror5" *ngIf="getBaggageDetailsColor(flight['otherFarePriceAttributes'][this.indexArray[i]])==='notallowed'">
                                          <img
                                             
                                                   src="../../../assets/images/checked-bag-red.svg"
                                             >
                                                   <span class="msgBox5">
                                                        {{getBaggageDetails(flight['otherFarePriceAttributes'][this.indexArray[i]])}}
                                                   </span>
                                                </label>
                                                <label class="bigIconInBox6" *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.refund == 'INCLUDED'">
                                                        <img
                                                          
                                                           src="../../../assets/images/refund-green.svg"
                                                           >
                                                           <span class="msgBox6">
                                                              {{'flightChart.Refundable' | translate }}
                                                           </span>
                                                        </label>
                                                              <label class="bigIconInBoxerror6" *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.refund == 'NOTOFFERED' || flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.refund == 'AVAILABLEFORCHARGE'">
                                                        <img
                                                           
                                                           src="../../../assets/images/refund-red.svg"
                                                          > 
                                                           <span class="msgBox6">
                                                              {{'flightChart.Refundable' | translate }}<span *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.refund == 'NOTOFFERED'">{{'flightChart.notincluded' | translate }}</span><span *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.refund == 'AVAILABLEFORCHARGE'">{{'flightChart.atcost' | translate }}</span>
                                             </span>
                                          </label>
                                                <label class="bigIconInBox3" *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].fareAttributes.legRoom == 'INCLUDED'">
                                             <img
                                            
                                             src="../../../assets/images/leg-space-green.svg"
                                             >
                                             <span class="msgBox4">
                                                {{'flightChart.Legroomincluded' | translate }}
                                             </span>
                                          </label>

                                            </p>
                                        </div>
                                        <div *ngIf="flight['otherFarePriceAttributes'][this.indexArray[i]].legs[0].flightHighlights.withinPolicy == false" class="notInPolicybox" [ngStyle]="dynamicBorderRaidus(i)">
                                            <span> {{'flightChart.NotinPolicy' | translate}}</span>
                                        </div>
                                    </div>
                                </ng-container>

                                <ng-container *ngIf="this.indexArray[i] == -1">
                                    <div class="PriceDetailsBox-multi-disabled">
                                        <div class="starsImg">

                                            <div class="starsValue">{{this.starsArray[i] | translate}}</div>
                                        </div>
                                        <div *ngIf="this.showloader" class="row">
                                                <app-loader [spinnerStyle]="true" class="loader_position" style="top: 40%;"></app-loader>
                                            </div>
                                        <h6 *ngIf="!this.showloader && this.fetchAllAttempted" style="margin-top: 50%; white-space: break-spaces;">{{'flightChart.NooptionsAvailable' | translate}}</h6>
                                    </div>
                                </ng-container>

                            </div>

                        </div>
                    </div>

                    <!-- screenWidthChange() > 768 && -->

                    <ng-container *ngIf="!isMobile &&  checkMultiFare == false">

                        <div class="popUpShow hide popUpShow-d {{'popUp'+ index}}" *ngFor="let num of noOfFlightLegs; let count = index;">
                            <div class="display_Property">
                                <div class="box-top-line"></div>
                                <div class="modal-container flight-modal-container flight-Info-container">
                                    <!-- <div class="modal-header">
                           <h5 class="modal-title" id="myModalLabel">
                              {{'flightChart.Departure' | translate}} {{getFlightLegSource(0)}} -> {{getFlightLegDestination(flight.legs.length - 1)}}
                           </h5>   
                        </div> -->
                                    <!-- <button type="button" class="close" style="POSITION: ABSOLUTE;color: rgb(14, 12, 12);TOP: 212PX;right: 10px;" data-dismiss="modal" (click)=" CloseBtn(index)" ><i class="material-icons">close</i></button> -->
                                    <div class="modal-body" style="position: relative;">
                                        <ng-container *ngFor="let num of noOfFlightLegs">
                                            <div class="date-duration" style="justify-content: unset;">
                                                <label class="date" style="margin-right: 4px;">{{'flightChart.TotalDuration' | translate}} </label>
                                                <span class="date"> {{getFlightDuration(num).hrs + 'h ' +
                                             getFlightDuration(num).mins + 'm'}}</span>
                                                <span class="dotBlack"></span>
                                                <span class="block flight-airport" style="margin-top: 0px;margin-left: 10px;color: black" *ngIf="flightLayoverInfoList[num].length==0">{{'flightChart.nonstop' |
                                             translate}}</span>
                                                <span class="block flight-airport" style="margin-top: 0px;margin-left: 10px;color: black" data-toggle="tooltip" title="{{getLayoverInfoAsTooTip(num)}}" *ngIf="flightLayoverInfoList[num].length > 0">{{getStopsUIText(num) |
                                             translate}}</span>


                                            </div>
                                            <div class="flight-schedule">
                                                <ng-container *ngFor="let flightHop of flight.legs[num].flightHops; let i=index">
                                                    <div class="flight-box">
                                                        <div class="flight-box-left">
                                                            <img onerror="this.onerror = null; this.src = 'https://s3.amazonaws.com/images.biztravel.ai/template/default.gif';" [src]="getFlightIconURL(flightHop.carrier)" />
                                                        </div>
                                                        <div class="flight-box-right">
                                                            <div *ngIf="checkMultiFare == false" class="flight-ticket-detail" style="color:#817E7B;">
                                                                <p style="display: none !important;"> {{getSelectedOtherFareBrandNameForSpecificHop(num,i)}}</p>
                                                            </div>

                                                            <div class="row" style="margin-top: 10px;">
                                                                <div class="col-6" style="text-align: left;">
                                                                    <div class="date">{{flightHop.carrier}}{{flightHop.flightNumber}}</div>
                                                                    <div style="margin-top: 10px;" class="block flight-time">{{getDisplayDate(flight.legs[num].flightHops[i].starts)| date:'h:mm a'}} <span class="extra-time1">{{getDepartureDaysDifferenceForLayover(i)}}</span></div>
                                                                    <div class="flight-timings-lineDetail"></div>
                                                                    <div class="date" style="font-size: 14px;margin-top: 10px;">Departure</div>
                                                                    <div class="detailcity" style="text-align: left;line-height: 18px">{{flight.legs[num].flightHops[i].from}} - {{this.airports[flight.legs[num].flightHops[i].from].name}}</div>
                                                                    <div class="dateDetails" style="text-align: left;">{{getDisplayDate(flight.legs[num].flightHops[i].starts) | date:'EEEE, MMMM d, y'}}
                                                                    </div>
                                                                </div>

                                                                <div class="col-6" style="text-align: left;">
                                                                    <div class="date">{{getHopDuration(flightHop.duration).hrs + 'h ' + getHopDuration(flightHop.duration).mins + 'm'}}</div>
                                                                    <div style="margin-top: 10px;" class="block flight-time">{{getDisplayDate(flight.legs[num].flightHops[i].ends)| date:'h:mm a'}} <span class="extra-time">{{getArrivalDaysDifferenceForLayover(i)}}</span></div>
                                                                    <div class="date" style="font-size: 14px;margin-top: 10px;">Arrival</div>
                                                                    <div class="detailcity" style="line-height: 18px">{{flight.legs[num].flightHops[i].to}} - {{this.airports[flight.legs[num].flightHops[i].to].name}}</div>
                                                                    <div class="dateDetails">{{getDisplayDate(flight.legs[num].flightHops[i].ends) | date:'EEEE, MMMM d, y'}}
                                                                    </div>
                                                                </div>

                                                            </div>



                                                        </div>
                                                    </div>
                                                    <div class="flight-layover" *ngIf="flightLayoverInfoList[num][i]" style="justify-content: center;">
                                                        <span class="flight-layover-right date">{{flightLayoverInfoList[num][i].duration.hrs +
                                             'h ' + flightLayoverInfoList[num][i].duration.mins + 'm'}}</span> <span class="dotBlack" style="margin-right: 4px;margin-left: 4px;"></span>
                                                        <span class="flight-layover-left">{{'flightChart.Layoverin' | translate}}
                                             {{flightLayoverInfoList[num][i].in}}</span>

                                                    </div>
                                                </ng-container>
                                            </div>
                                        </ng-container>

                                    </div>

                                </div>
                                <div class="BoxSlected">

                                    <div class="row" class="DetailsBoxLoader" *ngIf="showloader == true">
                                        <app-loader style="text-align:center;margin-left:  auto !important;margin-right: auto !important;position: absolute;top: 55%;" [spinnerStyle]="true"></app-loader>
                                    </div>

                                    <div class="Slected-Box-Content" *ngIf="boxShow == true" [ngStyle]="{'width.px':checkDetailBoxSize(this.selectionOption.length)}">


                                        <div *ngFor="let item of  this.selectionOption,let j = index" class="detailes_price_Box">
                                            <h4>{{legType ==='RETURN' && getPriceForMoreOptions(item) >= 0.5 ? '' :((getPriceForMoreOptions(item)
                                                <=- 0.5)? '': '')}}{{getAbsoluteNumber(getPriceForMoreOptions(item)) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }}</h4>
                                                    <div class="FareClassNameCss" style="color: #9B9B9B">
                                                        <p>{{ getBrandNameForMaxClass(item)}} </p>
                                                    </div>
                                                    <hr>
                                                    <div class="HopeDetails">

                                                        <div class="flight-feature-list">

                                                            <ul>
                                                                <li *ngIf="getCorporateFareAttributeMoreOptions(item)" style="white-space: nowrap;">
                                                                    <i class="fa fa-star" style="margin-right: 4px;color: #3CBF9A"></i>

                                                                    <span style="white-space: break-spaces;">{{'hotelResult.Corporaterate' | translate}}</span>
                                                                </li>

                                                                <li *ngIf="getSeatFareAttributeMoreOptions(item) !== 'NOTFOUND'" style="white-space: nowrap;">
                                                                    <img *ngIf="getSeatFareAttributeMoreOptions(item) === 'INCLUDED'" src="../../../assets/images/seat-green.svg">

                                                                    <img *ngIf="getSeatFareAttributeMoreOptions(item) === 'NOTOFFERED' || getSeatFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/seat-red.svg">
                                                                    <span *ngIf="getSeatFareAttributeMoreOptions(item) === 'INCLUDED' "style="white-space:break-spaces;">{{'flightChart.Seatchoice' | translate}}</span>
                                                                    <span *ngIf="getSeatFareAttributeMoreOptions(item) === 'NOTOFFERED' || getSeatFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" style="white-space:break-spaces;">{{'flightChart.NoSeatchoice' | translate}}</span>
                                                                </li>

                                                                <li *ngIf="isMixed(item)" class="not-active  " style="flex: 0 0 auto; min-width:50px;">
                                                                    <i class="fa fa-exclamation-triangle  fare-attr-red" aria-hidden="true"></i>
                                                                    <span style=" color: rgb(249, 61, 48);">{{'flightChart.Mixedclass' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getCarryOnBagFareAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                    <img *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'INCLUDED'" src="../../../assets/images/carry-on-green.svg">

                                                                    <img *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'NOTOFFERED'" src="../../../assets/images/carry-on-red.svg">
                                                                    <img *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/carry-on-blue.svg">
                                                                    <span *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'INCLUDED' ">{{'flightChart.Carryonbag' | translate}}</span>
                                                                    <span *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE' " class="atCharge">{{'flightChart.Feeforcarryon' | translate}}</span>
                                                                    <span *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'NOTOFFERED'">{{'flightChart.NoCarryonbag' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getChangesFareAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                    <img style="transform: rotate(90deg);" *ngIf="getChangesFareAttributeMoreOptions(item) === 'INCLUDED'" src="../../../assets/images/changes-green.svg">

                                                                    <img *ngIf="getChangesFareAttributeMoreOptions(item) === 'NOTOFFERED'" src="../../../assets/images/changes-red.svg">
                                                                    <img *ngIf="getChangesFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/changes-blue.svg">
                                                                    <span *ngIf="getChangesFareAttributeMoreOptions(item) === 'INCLUDED' ">{{'flightChart.Changes' | translate}}</span>
                                                                    <span *ngIf="getChangesFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE' " class="atCharge">{{'flightChart.Feeforchanges' | translate}}</span>
                                                                    <span *ngIf="getChangesFareAttributeMoreOptions(item) === 'NOTOFFERED'">{{'flightChart.NoChanges' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="showWithinPolicy() && isWithinPolicy(item) == true">
                                                                    <img src="../../../assets/images/policy-green.svg">




                                                                    <span>{{'flightChart.InPolicy' | translate}}</span>

                                                                </li>
                                                                <li *ngIf="false">
                                                                    <img *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'INCLUDED'" src="../../../assets/images/checked-bag-green.svg">

                                                                    <img *ngIf="getCarryOnBagFareAttributeMoreOptions(item) === 'NOTOFFERED'" src="../../../assets/images/checked-bag-red.svg">
                                                                    <span>{{'flightChart.Checkedbag' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getBaggageDetails(item)" class="not-active" style="flex: 0 0 auto; min-width:50px;white-space: nowrap;">
                                                                        <img *ngIf="getBaggageNumber(item) ===1 || getBaggageNumber(item) >3" src="../../../assets/images/checked-bag-green.svg">
                                                                        <img *ngIf="getBaggageNumber(item) ===2" src="../../../assets/images/checked-bag-green2.svg">
                                                                        <img *ngIf="getBaggageNumber(item) ===3" src="../../../assets/images/checked-bag-green3.svg">

                                                                        <img *ngIf="getBaggageDetailsColor(item)==='notallowed'" src="../../../assets/images/checked-bag-red.svg">
                                                                    <!--<i class="fa fa-suitcase  fare-attr-red" aria-hidden="true" [ngStyle]="{'color': isBaggageAvailable(item) ? '#27c198':'#9B9B9B'}"></i>-->
                                                                    <span style="white-space: break-spaces;">{{getBaggageDetails(item)}}</span>
                                                                </li>
                                                                <li *ngIf="getRefundAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                    <img *ngIf="getRefundAttributeMoreOptions(item) === 'INCLUDED'" src="../../../assets/images/refund-green.svg">

                                                                    <img *ngIf="getRefundAttributeMoreOptions(item) === 'NOTOFFERED'" src="../../../assets/images/refund-red.svg">
                                                                    <img *ngIf="getRefundAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/refund-blue.svg">
                                                                    <span *ngIf="getRefundAttributeMoreOptions(item) === 'INCLUDED' ">{{'flightChart.Refundable' | translate}}</span>
                                                                    <span *ngIf="getRefundAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE' " class="atCharge">{{'flightChart.Feeforrefund' | translate}}</span>
                                                                    <span *ngIf="getRefundAttributeMoreOptions(item) === 'NOTOFFERED'">{{'flightChart.NotRefundable' | translate}}</span>
                                                                </li>


                                                                <li *ngIf="getWifiAttributeMoreOptions(item) !== 'NOTFOUND'" style="flex: 0 0 auto; min-width:80px;">
                                                                    <img *ngIf="getWifiAttributeMoreOptions(item) === 'INCLUDED'" src="../../../assets/images/wifi-green.svg">

                                                                    <img *ngIf="getWifiAttributeMoreOptions(item) === 'NOTOFFERED' || getWifiAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/wifi-red.svg">
                                                                    <span *ngIf="getWifiAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'">{{'flightChart.WiFi' | translate}}<label  style="margin-left: 4px;">{{'flightChart.atcost' | translate}}</label></span>
                                                                    <span *ngIf="getWifiAttributeMoreOptions(item) == 'NOTOFFERED'">{{'flightChart.NOWiFi' | translate}}</span>
                                                                    <span *ngIf="getWifiAttributeMoreOptions(item) == 'INCLUDED'" >{{'flightChart.FreeWiFi' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getMealFareAttributeMoreOptions(item) !== 'NOTFOUND'">
                                                                    <img *ngIf="getMealFareAttributeMoreOptions(item) === 'INCLUDED'" src="../../../assets/images/meal-green.svg">

                                                                    <img *ngIf="getMealFareAttributeMoreOptions(item) === 'NOTOFFERED' || getMealFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/meal-red.svg">
                                                                    <span *ngIf="getMealFareAttributeMoreOptions(item) === 'INCLUDED'">{{'flightChart.freeMeal' | translate}}</span>
                                                                    <span *ngIf="getMealFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'">{{'flightChart.Meal' | translate}}</span>
                                                                                        <span *ngIf="getMealFareAttributeMoreOptions(item) === 'NOTOFFERED'">{{'flightChart.NOMeal' | translate}}</span>
                                                                </li>
                                                                <li *ngIf="getLegroomFareAttributeMoreOptions(item) !== 'NOTFOUND'" style="flex: 0 0 auto; min-width:50px;white-space: nowrap;">
                                                                    <img *ngIf="getLegroomFareAttributeMoreOptions(item) === 'INCLUDED'" src="../../../assets/images/leg-space-green.svg">

                                                                    <img *ngIf="getLegroomFareAttributeMoreOptions(item) === 'NOTOFFERED' || getLegroomFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" src="../../../assets/images/leg-space-red.svg">
                                                                    <span *ngIf="getLegroomFareAttributeMoreOptions(item) === 'INCLUDED' "style="white-space: break-spaces;">{{'flightChart.legRoom' | translate}}</span>
                                                                    <span *ngIf="getLegroomFareAttributeMoreOptions(item) === 'NOTOFFERED' || getLegroomFareAttributeMoreOptions(item) === 'AVAILABLEFORCHARGE'" style="white-space: break-spaces;">{{'flightChart.NolegRoom' | translate}}</span>
                                                                </li>


                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <!-- <div class="block flight-trip-type" style="margin-bottom:5px;" *ngIf="this.creditDetails && this.creditDetails.creditAmount">
                                 {{getTraveleCreditersName()}} {{'flightChart.creditappliedonthetotalfareof' | translate}} {{legType ==='RETURN' && getPriceForMoreOptions1(item) >= 0.5 ? '+' : (getPriceForMoreOptions1(item)<=- 0.5 ? '-' : '')}} {{getAbsoluteNumber(getPriceForMoreOptions1(item)) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }} 
                              </div> -->
                                                    <div class="block flight-trip-type" style="position: absolute;margin-bottom:5px;float: left;margin-left: 15px;cursor: pointer;width: 100%;text-align: left;bottom: 28%;left: -5px;" *ngIf="item.marketingMessage && item.marketingMessage!==''" (click)="openMarketingMSgModal(item.marketingMessage,marketingmodal)">
                                                        <span class="marketingMsg">
                                             <span style="font-family: var(--globalFontfamilyr);font-weight: bold;;"> {{'flightChart.FareDetails' |
                                                translate}}: </span> {{item.marketingMessage}}
                                                        </span>
                                                    </div>

                                                    <button *ngIf="this.searchService.trainFilter.length > 0" class="btn primary-button" [disabled]="disableButton" (click)="selectFlight(j)">{{'flightChart.Select' |
                                                translate}}</button>

                                                    <button *ngIf="this.searchService.trainFilter.length === 0" class="btn primary-button" [disabled]="disableButton" (click)="selectFlight(j)">{{'flightChart.SELECTFLIGHT' |
                                                translate}}</button>
                                                    <div *ngIf="showWithinPolicy() && isWithinPolicy(item) == false" class="notInPolicybox">
                                                        <span> {{'flightChart.NotinPolicy' | translate}}</span>
                                                    </div>

                                                    <div *ngIf="this.creditDetails && this.creditDetails.length >0 && item.ngsStars > 1 && isResidualAmountIsGreaterTahnMaxresidualAmount(item)" class="block flight-trip-type creditPrice">
                                                        {{'flightChart.GrossFare' | translate}} {{legType ==='RETURN' && getPriceForMoreOptions1(item) >= 0.5 ? '+' : (getPriceForMoreOptions1(item)
                                                        <=- 0.5 ? '-' : '' )}} {{getAbsoluteNumber(getPriceForMoreOptions1(item)) | currency : getCurrencySymbol(flight) : 'code' : '1.0-0' }} </div>
                                                            <label *ngIf="getBrandFareText()" class="checkbox-branded-fare-container">
                                                   <input *ngIf="showBrandedFareCheckBox()" type="checkbox"
                                                      [(ngModel)]="brandedFareCheckBox" data-md-icheck
                                                      (change)="onBrandedFareClick($event)" />
                                                   {{getBrandFareText()}}
                                                </label>
                                                    </div>



                                        </div>

                                    </div>
                                </div>
                            </div>

                    </ng-container>



                    <!-- ColSpan for Desktop Starts Ends-->
                    <div class="flight-facilities d-none d-md-block" *ngIf="false">
                        <ul style="display:flex !important;    flex-direction: row; overflow: hidden; max-height: 40px;flex-wrap: wrap;">
                            <li *ngIf="isPreferredAirline()" [class]="active" style="flex: 0 0 auto; min-width:50px;">
                                <i class="fare-attr-pref-airline fa fa-plane" aria-hidden="true"></i>
                                <span>{{'flightChart.Airline' | translate}}</span>
                            </li>
                            <li *ngIf="isPreferredAlliance()" [class]="active" style="flex: 0 0 auto; min-width:50px;">
                                <i class="fare-attr-pref-airline fa fa-plane" aria-hidden="true"></i>
                                <span>{{'flightChart.Alliance' | translate}}</span>
                            </li>
                            <!-- <li *ngIf="showWithinPolicy()" [class]="isWithinPolicy()?'active':'not-active'" style="flex: 0 0 auto; min-width:50px;">
                            <img class="green-img" src="assets/images/flight-list/policy.svg" />
                            <img class="red-img" src="assets/images/flight-list/policy-red.svg" />
                            <span>{{'flightChart.Policy' | translate}}</span>
                        </li> -->
                            <li *ngIf="isPreferredAirport()" [class]="active" style="flex: 0 0 auto; min-width:50px;">
                                <img class="green-img" src="assets/images/flight-list/airport.svg" />
                                <!-- <img class="red-img" src="assets/images/flight-list/airport-red.png" /> -->
                                <span>{{'flightChart.Airport' | translate}}</span>
                            </li>
                            <li *ngIf="isRedEye()" class="not-active" style="flex: 0 0 auto; min-width:50px;">
                                <!-- <img class="green-img" src="assets/images/flight-list/airport.png" /> -->
                                <img class="red-img" src="assets/images/flight-list/redeye.svg" />
                                <span style="color:#F73F39">{{'flightChart.Redeye' | translate}}</span>
                            </li>
                            <li *ngIf="isSmallLayoverFlight()" class="not-active" style="flex: 0 0 auto; min-width:50px;">
                                <!-- <img class="green-img" src="assets/images/flight-list/airport.png" /> -->
                                <img class="red-img" src="assets/images/flight-list/gen-negative.svg" />
                                <span style="color:#F73F39">{{'flightChart.Shortlayover' | translate}}</span>
                            </li>
                            <li *ngIf="!isSmallLayoverFlight && isLongLayoverFlight()" class="not-active" style="flex: 0 0 auto; min-width:50px;">
                                <!-- <img class="green-img" src="assets/images/flight-list/airport.png" /> -->
                                <img class="red-img" src="assets/images/flight-list/gen-negative.svg" />
                                <span>{{'flightChart.Longlayover' | translate}}</span>
                            </li>
                            <li *ngIf="isChangeOfAirport()" class="not-active" style="flex: 0 0 auto; min-width:50px;">
                                <!-- <img class="green-img" src="assets/images/flight-list/airport.png" /> -->
                                <img class="red-img" src="assets/images/flight-list/gen-negative.svg" />
                                <span>{{'flightChart.Changeofairport' | translate}}</span>
                            </li>
                            <li *ngIf="isBasicEconomy(flight.legs[0])" class="not-active" style="color:red !important; flex: 0 0 auto; min-width:50px;">
                                <!-- <img class="green-img" src="assets/images/flight-list/airport.png" /> -->
                                <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                                <span style="color:red !important;">{{'flightChart.BasicEconomy' | translate}}</span>
                            </li>

                            <li *ngIf="isOverNightFlight()" class="not-active" style="flex: 0 0 auto; min-width:50px;">
                                <!-- <img class="green-img" src="assets/images/flight-list/airport.png" /> -->
                                <img class="red-img" src="assets/images/flight-list/overnight.svg" />
                                <span>{{'flightChart.Overnightlayover' | translate}}</span>
                            </li>

                            <!--
                            <span *ngIf="getSeatFareAttribute() !== 'NOTFOUND' || getRefundAttribute() !== 'NOTFOUND' || getChangesFareAttribute() !== 'NOTFOUND' || getCarryOnBagFareAttribute() !== 'NOTFOUND' || getMealFareAttribute() !== 'NOTFOUND'">
                                <svg style="margin-left:15px;margin-right:15px;" width="1" height="30"
                            viewBox="0 0 1 47" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="0.5" y1="2.18557e-08" x2="0.499998" y2="47" stroke="black" />
                        </svg>
                    </span>
                        -->
                            <!-- <li *ngIf="getSeatFareAttribute() !== 'NOTFOUND'" style="flex: 0 0 auto; min-width:80px;">
                        <i *ngIf="getSeatFareAttribute() == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                        <i *ngIf="getSeatFareAttribute() == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                        <i *ngIf="getSeatFareAttribute() == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                    <span>{{'flightChart.Seatchoice' | translate}}</span>
                        </li> -->
                            <li *ngIf="getWifiAttribute() !== 'NOTFOUND'" style="flex: 0 0 auto; min-width:80px;">
                                <i *ngIf="getWifiAttribute() == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                <i *ngIf="getWifiAttribute() == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                <i *ngIf="getWifiAttribute() == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                <span>{{'flightChart.WiFi' | translate}}</span>
                            </li>
                            <li *ngIf="getRefundAttribute() !== 'NOTFOUND'" style="flex: 0 0 auto; min-width:80px;">
                                <i *ngIf="getRefundAttribute() == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                <i *ngIf="getRefundAttribute() == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                <i *ngIf="getRefundAttribute() == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                <span>{{'flightChart.Refundable' | translate}}</span>
                            </li>
                            <li *ngIf="getChangesFareAttribute() !== 'NOTFOUND'" style="flex: 0 0 auto; min-width:80px;">
                                <i *ngIf="getChangesFareAttribute() == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                <i *ngIf="getChangesFareAttribute() == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                <i *ngIf="getChangesFareAttribute() == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                <span>{{'flightChart.Changes' | translate}}</span>
                            </li>

                            <li *ngIf="getCarryOnBagFareAttribute() !== 'NOTFOUND'" style="flex: 0 0 auto; min-width:80px;">
                                <i *ngIf="getCarryOnBagFareAttribute() == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                <i *ngIf="getCarryOnBagFareAttribute() == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                <i *ngIf="getCarryOnBagFareAttribute() == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                <span>{{'flightChart.Carryonbag' | translate}}</span>
                            </li>
                            <li *ngIf="getMealFareAttribute() !== 'NOTFOUND'" style="flex: 0 0 auto; min-width:80px;">
                                <i *ngIf="getMealFareAttribute() == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                <i *ngIf="getMealFareAttribute() == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                <i *ngIf="getMealFareAttribute() == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                <span *ngIf="getMealFareAttributeMoreOptions() === 'INCLUDED'">{{'flightChart.freeMeal' | translate}}</span>
                                                                    <span *ngIf="getMealFareAttributeMoreOptions() === 'AVAILABLEFORCHARGE'">{{'flightChart.Meal' | translate}}</span>
                                                                                        <span *ngIf="getMealFareAttributeMoreOptions() === 'NOTOFFERED'">{{'flightChart.NOMeal' | translate}}</span>
                            </li>
                            <li *ngIf="getLegRoomFareAttribute() !== 'NOTFOUND'">
                                <i *ngIf="getLegRoomFareAttribute() == 'NOTOFFERED'" class="fare-attr-red fa fa-ban" aria-hidden="true"></i>
                                <i *ngIf="getLegRoomFareAttribute() == 'AVAILABLEFORCHARGE'" class="fare-attr-red fa fa-usd" aria-hidden="true"></i>
                                <i *ngIf="getLegRoomFareAttribute() == 'INCLUDED'" class="fare-attr-green fa fa-check" aria-hidden="true"></i>
                                <span>{{'flightChart.legRoom' | translate}}</span>
                            </li>
                        </ul>
                        <label *ngIf="getBrandFareText()" class="checkbox-branded-fare-container-mobile d-sm-block d-md-none">
                     <input *ngIf="showBrandedFareCheckBox()" type="checkbox" [(ngModel)]="brandedFareCheckBox"
                        data-md-icheck (change)="onBrandedFareClick($event)" />
                     <span class="branded-fare-label-text">
                        {{getBrandFareText()}}
                     </span>
                  </label>
                    </div>
                    </div>



                    <ng-template #searchModal let-modal>

                    </ng-template>





                    </div>
                </div>