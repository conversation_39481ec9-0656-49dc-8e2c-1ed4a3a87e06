@import "../../variables.scss";

.field-input {
    margin-bottom: 28px;
    position: relative;
    font-size: 14px;

    .on-right {
        position: relative;

        .icon {
            right: 14px;
        }
    }

    .with-prefix {
        position: relative;

        .prefix {
            position: absolute;
            top: 14px;
            left: 14px;
        }

        .field-control {
            padding-left: 60px;
        }
    }



    .icon {
        position: absolute;
        top: 17px;
    }

    .field-control {
        padding: 12px 16px;
        border: 2px solid $border-light-color;
        background: #fff;
        outline: none;
        width: 100%;
        line-height: 21px;
    }

    .error {
        margin: 2px 0;
        color: $danger;
    }
}

.label {
    color: $secondary-text-color;
    font-size: 12px;
    margin-bottom: 4px;
}

.toggle-button {
    padding: 12px 16px;
    border: 2px solid $border-light-color;
    background: #fff;
    outline: none;
    width: 100%;
    color: $secondary-text-color;
    cursor: pointer;

    &.selected {
        background: $accent-color;
        color: $link-color;
    }
}

.check-or-input {
    display: flex;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);

    &.checked {
        .field-control {
            flex: 0;
            padding: 0;
        }

        .check-input {
            flex-basis: auto;
            justify-content: flex-start;
            flex: 1;
        }
    }

    .field-control {
        box-shadow: none;
        border: none;
    }

    .check-input {
        height: 49px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 16px;
        flex-basis: 200px;
        cursor: pointer;
    }
}

.heading {
    font-size: 16px;
}

.sub-heading {
    font-size: 16px;
    margin: 24px 0 16px 0;
}

.circle {
    min-height: 26px;
    min-width: 26px;
    border: 2px solid $link-color;
    font-size: 14px;
    border-radius: 50%;
    position: relative;
    margin-right: 16px;
    display: inline-block;

    .number {
        position: absolute;
        top: 1px;
        left: 7px;
    }
}

.card-heading {
    display: flex;
    align-items: center;
}

.icon-calendar {
    color: $accent-color;
}

.flex-wrapper {
    display: flex;
    justify-content: space-between;

    .phone-number {
        width: 175px;

        .icon-country-flag {
            height: 15px;
            width: 20px;
            margin-top: 8px;
        }

        .country-name {
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

        }
    }
}

.button-wrapper {
    text-align: right;

    button {
        padding: 8px 24px;
        margin-right: 28px;
    }
}

.triangle {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 0 20px 20px;
    border-color: transparent transparent $link-color transparent;
    position: absolute;
    right: 0;
    bottom: 0;
}

.flag-label {
    height: 15px;
    width: 20px;
    margin-right: 8px;
    margin-top: -2px;
}