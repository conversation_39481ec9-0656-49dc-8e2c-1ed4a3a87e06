import { Component, HostListener, Input, OnInit, Output, EventEmitter } from '@angular/core';
import {
  AbstractControl, AsyncValidatorFn, UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, ValidationErrors, Validator, ValidatorFn,
  Validators
} from '@angular/forms';
import { Constants } from '../util/constants';
import { SearchService } from '../search.service';
import { SearchResultService } from '../search-result.service';
import { Observable, Subscription } from 'rxjs';
import { FlightResult } from '../entity/flight-result';
import { CommonUtils } from '../util/common-utils';
import { Router } from '@angular/router';
import { BookingService } from '../booking.service';
import { FormUtils } from '../util/form-utils';
import { countries } from '../util/countries';
import { DeviceDetailsService } from '../device-details.service';
import { DateUtils } from '../util/date-utils';
import { GeoLocationService } from '../geo-location.service';
import { catchError, map } from 'rxjs/operators';
import { TravelersInfo } from '../entity/travelers-info';
import { FrequentFlyerInfo } from "../entity/frequent-flyer-info";
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'passenger-detail',
    templateUrl: './passenger-detail.component.html',
    styleUrls: ['./passenger-detail.component.scss'],
    standalone: false
})
export class PassengerDetailComponent implements OnInit {

  @HostListener('window:beforeunload') onBeforeUnload() {
    this.setFormInLocalStorage();
  }

  @Input() noOfPassengers: number;
  @Input() isPassportRequired: boolean;

  @Output() goToPaymentRequest = new EventEmitter();
  @Output() enablePaymentRequest = new EventEmitter<boolean>();

  passengersForm: UntypedFormGroup;
  maxDoBDate: Date = new Date();
  minPassportDate: Date = new Date();
  userDetails: TravelersInfo;

  selectedFlightSubscription: Subscription;

  titleOptions = ['Mr', 'Sir', 'Lord', 'Mrs', 'Lady', 'Miss', 'Master', 'Inf', 'Ms'];

  uniqueAirlines = [];

  countries = [];
  dialCode = "+1";
  isMobile: boolean;
  userId: string = null;
  sToken: string = null;
  deviceSubscription: Subscription;
  userDetailsSubscription: Subscription;
  constructor(private fb: UntypedFormBuilder,
    private gallopLocalStorage: GallopLocalStorageService,
    private searchResultService: SearchResultService,
    private searchService: SearchService,
    public router: Router,
    private bookingService: BookingService,
    private deviceDetailsService: DeviceDetailsService,
    private geoLocationService: GeoLocationService,
  ) {
    // this.userId = this.userDetailsService.getUserId()?this.userDetailsService.getUserId():null;
    // this.sToken = this.userDetailsService.getStoken()?this.userDetailsService.getStoken():null;
  }

  ngOnInit() {
    // this.userDetailsSubscription = this.userDetailsService.userDetails$.subscribe(details => this.userDetails = details);
    this.subscribeEvents();
    this.setUniqueAirlinesFromLocalStorage();
    this.setNumOfPassengersFromLocalStorage();
    this.createForm();
    this.loadExistingFormFromLocalStorage();
    this.countries = countries;

    this.
      passengersForm.
      statusChanges.
      subscribe(status => {
        status === 'VALID' ? this.enablePaymentRequest.emit(true) : this.enablePaymentRequest.emit(false);
      });

    setTimeout(() => {
      this.passengersForm.valid ? this.enablePaymentRequest.emit(true) : this.enablePaymentRequest.emit(false);
    }, 500);

    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });

    this.setKTNValidator();
    this.setFFNValidator();
  }


  setUniqueAirlinesFromLocalStorage() {
    if (this.gallopLocalStorage.getItem("uniqueAirlines")) {
      this.uniqueAirlines = JSON.parse(this.gallopLocalStorage.getItem("uniqueAirlines"));
    }
  }

  setNumOfPassengersFromLocalStorage() {
    if (this.gallopLocalStorage.getItem("passengers")) {
      let value = JSON.parse(this.gallopLocalStorage.getItem("passengers"));
      if (value.passengers != null) {
        this.noOfPassengers = value.passengers.length;
      }
    }
  }

  loadExistingFormFromLocalStorage() {
    if (this.gallopLocalStorage.getItem("passengers")) {
      let value = JSON.parse(this.gallopLocalStorage.getItem("passengers"));

      value.passengers.forEach((p) => {

        p.dateOfBirth ? (p.dateOfBirth = new Date(p.dateOfBirth)) : null;
        p.passportExpiryDate ? (p.passportExpiryDate = new Date(p.passportExpiryDate)) : null;

      });

      this.passengersForm.patchValue(value);
      FormUtils.markFormGroupTouched(this.passengersForm);
      this.updateFormValueInBookingService();
    }
  }

  subscribeEvents() {
    this.selectedFlightSubscription = this.searchResultService.selectedFlight$.subscribe((flight) => {
      this.setUniqueAirlines(flight);
    });
  }


  setKTNValidator() {
    for (let i = 0; i < (this.numOfFormPassengers); i++) {
      let passenger = this.getFormArrayPassengerObject(i);
      let ktnAvailable = passenger.get('ktnAvailable').value;
      passenger.get('knownTravellerNumber')
        .setValidators(this.conditionalValidators(ktnAvailable,
          [
            Validators.required,
            Validators.pattern(Constants.RGEX_ALPHANUMERIC),
            Validators.minLength(8),
            Validators.maxLength(environment.maxKTNFieldLength)]));
    }
  }

  setFFNValidator() {
    for (let i = 0; i < (this.numOfFormPassengers); i++) {
      let passenger = this.getFormArrayPassengerObject(i);
      let frequentFlyerInfo = (<UntypedFormArray>passenger.get('frequentFlyerInfo'));

      for (let j = 0; j < frequentFlyerInfo.length; j++) {
        let ffnAvailable = (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[i]).controls['frequentFlyerInfo']).at(j).get('ffnAvailable').value;
        (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[i]).controls['frequentFlyerInfo']).at(j).get('frequent_flyer_number').setValidators(this.conditionalValidators(ffnAvailable, [Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
      }

    }
  }

  setUniqueAirlines(flight: FlightResult) {
    if (flight) {
      flight.legs.forEach((leg) => {

        let numOfHops = leg.flightHops.length;

        for (let i = 0; i < numOfHops; i++) {

          var airlineCode = leg.flightHops[i].carrier;
          let airlineInfo = { 'code': '', 'name': '' };

          airlineInfo.code = airlineCode;
          airlineInfo.name = this.searchService.getAirlineName(airlineCode);

          if (!CommonUtils.containsObject(airlineInfo, this.uniqueAirlines)) {
            this.uniqueAirlines.push(airlineInfo);
          }
        }
      })

      this.gallopLocalStorage.setItem("uniqueAirlines", JSON.stringify(this.uniqueAirlines));
    }
  }

  checkIfPrecedingPassengersInvalid(index: number): boolean {
    if (index === 0) return false;
    if ((<UntypedFormArray>this.passengersForm.controls['passengers']).at(index - 1).invalid) {
      return true;
    }

    return false;
  }

  markPreviousPassengersTouched(index: number) {
    if (index === 0) return;

    FormUtils.markFormGroupTouched(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).at(index - 1));
  }

  createForm(): void {
    let ffnArray = [];
    if (this.userDetails !== null && this.userDetails.frequentFlyerInfo !== null) {
      //ffnArray = this.userDetails.frequentFlyerInfo;
      ffnArray = this.userDetails.frequentFlyerInfo.filter((x) => x.name === this.uniqueAirlines[0].name);
      //ffnArray.push(tempArray?tempArray:[]);
    }

    let newFfnArray = ffnArray.length === 0 ? this.uniqueAirlines : ffnArray;
    this.passengersForm = this.fb.group(
      {
        passengers: this.fb.array([])
      }, { validator: '' });

    for (let i = 0; i < this.noOfPassengers; i++) {
      this.addPassenger(i);
      if (i === 0) {
        newFfnArray = ffnArray.length === 0 || ffnArray === undefined ? this.uniqueAirlines : ffnArray
      }
      else {
        newFfnArray = this.uniqueAirlines;
      }
      for (let j = 0; j < newFfnArray.length; j++) {
        this.addFFNDetails(i, newFfnArray[j]);
      }
    }
  }
  getDefaultPassengerForm(i) {
    return this.fb.group({
      title: [null, Validators.compose([Validators.required])],
      firstName: [null, Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)])],
      middleName: [null, Validators.compose([Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)])],
      lastName: [null, Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)])],
      gender: [null, Validators.compose([Validators.required])],
      dateOfBirth: [null, Validators.compose([Validators.required, this.minAgeValidator])],
      email: [null, this.conditionalValidators((i == 0), [Validators.required, Validators.pattern(Constants.RGEX_EMAIL)])],
      phoneNumber: [null, this.conditionalValidators((i == 0), [Validators.required, Validators.pattern(Constants.RGEX_ONLY_DIGITS)])],
      address: [null, this.conditionalValidators((i == 0), [Validators.required])],
      zipCode: new UntypedFormControl(null, {
        validators: this.conditionalValidators((i == 0), [Validators.required])
      }),
      ktnAvailable: [true],
      knownTravellerNumber: [null],
      dialCode: ["+1", Validators.compose([Validators.required])],
      passportNumber: [null, this.conditionalValidators(this.isPassportRequired, [Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)])],
      passportCountry: [null, this.conditionalValidators(this.isPassportRequired, [Validators.required])],
      passportNationality: [null, this.conditionalValidators(this.isPassportRequired, [Validators.required])],
      passportExpiryDate: [null, this.conditionalValidators(this.isPassportRequired, [Validators.required])],
      openFormOnUI: [false],
      frequentFlyerInfo: this.fb.array([])
    });
  }
  addPassenger(i) {
    let newPassengerForm;
    if (this.userDetails !== null && this.userId !== null && this.sToken !== null && i === 0) {
      let newPhoneNumber = this.userDetails.phoneNumber.slice(-10);
      let newDialCode = this.userDetails.phoneNumber.slice(0, (this.userDetails.phoneNumber.indexOf(newPhoneNumber)));
      let passportCountryInitialObject = { "code": "", "dial_code": "", "name": "", "nationality": "" };
      passportCountryInitialObject.code = (this.userDetails.passportDetails.passportCountryCode && this.userDetails.passportDetails.passportCountryCode.length > 0)
        ? this.userDetails.passportDetails.passportCountryCode : "";
      passportCountryInitialObject.dial_code = passportCountryInitialObject.code;
      passportCountryInitialObject.name = passportCountryInitialObject.code;
      passportCountryInitialObject.nationality = passportCountryInitialObject.code;

      let passportNationalityInitialObject = { "code": "", "dial_code": "", "name": "", "nationality": "" };
      passportNationalityInitialObject.code = (this.userDetails.nationality && this.userDetails.nationality.length > 0)
        ? this.userDetails.passportDetails.passportCountryCode : "";
      passportNationalityInitialObject.dial_code = passportNationalityInitialObject.code;
      passportNationalityInitialObject.name = passportNationalityInitialObject.code;
      passportNationalityInitialObject.nationality = passportNationalityInitialObject.code;


      try {
        newPassengerForm = this.fb.group({
          title: [this.userDetails.title, Validators.compose([Validators.required])],
          firstName: [this.userDetails.firstName, Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA)])],
          middleName: [this.userDetails.middleName, Validators.compose([Validators.pattern(Constants.RGEX_ONLY_ALPHA)])],
          lastName: [this.userDetails.lastName, Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA)])],
          gender: [this.userDetails.gender, Validators.compose([Validators.required])],
          dateOfBirth: [this.userDetails.dateOfBirth, Validators.compose([Validators.required, this.minAgeValidator])],
          email: [this.userDetails.email, this.conditionalValidators((i == 0), [Validators.required, Validators.pattern(Constants.RGEX_EMAIL)])],
          phoneNumber: [newPhoneNumber, this.conditionalValidators((i == 0), [Validators.required, Validators.pattern(Constants.RGEX_ONLY_DIGITS)])],
          address: [this.userDetails.address, this.conditionalValidators((i == 0), [Validators.required])],
          zipCode: new UntypedFormControl(this.userDetails.zipCode, {
            validators: this.conditionalValidators((i == 0), [Validators.required])
          }),
          ktnAvailable: [true],
          knownTravellerNumber: [this.userDetails.knownTravellerNumber],
          dialCode: [newDialCode, Validators.compose([Validators.required])],
          passportNumber: [this.userDetails.passportDetails.passportNumber, this.conditionalValidators(this.isPassportRequired, [Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)])],
          passportCountry: [passportCountryInitialObject, this.conditionalValidators(this.isPassportRequired, [Validators.required])],
          passportNationality: [passportNationalityInitialObject, this.conditionalValidators(this.isPassportRequired, [Validators.required])],
          passportExpiryDate: [this.userDetails.passportDetails.passportExpiryDate, this.conditionalValidators(this.isPassportRequired, [Validators.required])],
          openFormOnUI: [false],
          frequentFlyerInfo: this.fb.array([])
        });
      } catch (errorDetails) {
        newPassengerForm = this.getDefaultPassengerForm(i);
      }
    }

    else {
      newPassengerForm = this.getDefaultPassengerForm(i);
    }
    (<UntypedFormArray>this.passengersForm.controls['passengers']).push(newPassengerForm);
  }

  conditionalValidators(condition, validators: ValidatorFn[]) {
    if (condition) {
      return Validators.compose(validators);
    }
    return null;
  }

  conditionalAsyncValidators(condition, validators: AsyncValidatorFn[]) {
    if (condition) {
      return Validators.composeAsync(validators);
    }
    return null;
  }

  minAgeValidator(control: AbstractControl): ValidationErrors | null {
    let result = null;

    if (control.value && control.valid) {
      let yearsDiff = DateUtils.getYearsDiffFromNow(control.value);
      if (yearsDiff < Constants.MIN_DOB_AGE) {
        result = { 'lessThanMinAge': true };
      }
    }
    return result;
  }

  zipCodeValidator(control: AbstractControl): Promise<ValidationErrors> | null | Observable<ValidationErrors> {

    return this.geoLocationService.getLocationByZipCode(control.value).pipe(
      map(response => ((response && response.results.length == 0 || response.status != 'OK') ? { invalidZipCode: true } : null)),
      catchError(() => null)
    );
  }

  addFFNDetails(passengerFormIndex: number, airlineObj: any) {
    let ffnForm;

    if (passengerFormIndex === 0 && this.sToken != null && this.userId !== null) {
      ffnForm = this.fb.group({
        frequent_flyer_number: [airlineObj ? airlineObj.frequent_flyer_number : null],
        airlineCode: [airlineObj.airline_code],
        name: [airlineObj.name],
        ffnAvailable: [true]
      });
    }
    else {
      ffnForm = this.fb.group({
        frequent_flyer_number: [null],
        airlineCode: [airlineObj.airline_code],
        name: [airlineObj.name],
        ffnAvailable: [true]
      });
    }
    (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[passengerFormIndex]).controls['frequentFlyerInfo']).push(ffnForm);
  }

  setGender(formArrayIndex: number, value: string) {
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    passenger.get('gender').setValue(value);
    this.passengersForm.patchValue({ ...this.passengersForm });
  }

  isSelectedGender(formArrayIndex: number, value: string): boolean {
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let val = passenger.get('gender').value;
    return (val == value);
  }

  checkTravelerNumNotAvailableCheckBox(formArrayIndex: number, field: string, element, innerArrayIndex?: number) {
    let val = element.checked;
    let fieldName = (field == 'FFN') ? 'ffnAvailable' : 'ktnAvailable';
    let passenger: AbstractControl = this.getFormArrayPassengerObject(formArrayIndex);

    if (field == 'FFN') {
      (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get(fieldName).setValue(val);
    } else {
      passenger.get(fieldName).setValue(val);
    }

    if (!val) {

      if (field == 'FFN') {
        (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').setValidators(null);
        (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').setValue(null);
        (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').setErrors(null);
      }
      else {
        passenger.get('knownTravellerNumber').setValidators(null);
        passenger.get('knownTravellerNumber').setValue(null);
        passenger.get('knownTravellerNumber').setErrors(null);
      }
    }
    else {
      if (field == 'FFN') {
        (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').setValidators(Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
        (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').setValue('');

      } else {
        passenger.get('knownTravellerNumber').setValidators(Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC), Validators.minLength(9), Validators.maxLength(9)]));
        (<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['knownTravellerNumber'].setValue('');

      }

    }
  }

  isTravelerNumAvailable(formArrayIndex: number, field: string, innerArrayIndex?: number): boolean {
    let fieldName = (field == 'FFN') ? 'ffnAvailable' : 'ktnAvailable';
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let val;

    if (field == 'FFN') {
      val = (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get(fieldName).value;
    } else {
      val = passenger.get(fieldName).value;
    }
    return (val);
  }


  getFormArrayPassengerObject(formArrayIndex: number): AbstractControl {
    return (<UntypedFormArray>this.passengersForm.controls['passengers']).at(formArrayIndex);
  }

  onAccordianClick(formArrayIndex: number) {
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let val = passenger.get('openFormOnUI').value;
    passenger.get('openFormOnUI').setValue(!val);

    if (!val) {

      for (let i = 0; i < (this.numOfFormPassengers); i++) {

        if (i != formArrayIndex) {
          let p = this.getFormArrayPassengerObject(i);
          p.get('openFormOnUI').setValue(false);
        }
      }
    }

    this.passengersForm.patchValue({ ...this.passengersForm });
  }

  isFormOpen(formArrayIndex: number): boolean {
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let val = passenger.get('openFormOnUI').value;
    return val;
  }

  setFormInLocalStorage() {
    this.gallopLocalStorage.setItem("passengers", JSON.stringify(this.passengersForm.value));
    this.updateFormValueInBookingService();
  }

  updateFormValueInBookingService() {
    this.bookingService.passengersFormValue = this.passengersForm.value;
  }

  onSelectPassportCountry(formArrayIndex: number, event: any) {
    let val = event.target.value.split(":")[1];
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    passenger.get('passportCountry').setValue(val.toUpperCase().trim());
    this.passengersForm.patchValue({ ...this.passengersForm });
  }

  getPassengerFormTitle(passengerFormArrayIndex: number): string {
    let passenger = this.getFormArrayPassengerObject(passengerFormArrayIndex);

    if (passenger.get('title').value &&
      passenger.get('firstName').value &&
      passenger.get('lastName').value
    ) {
      let name = passenger.get('title').value + '. ';
      name += passenger.get('firstName').value + ' ';

      if (passenger.get('middleName').value) {
        name += passenger.get('middleName').value + ' ';
      }

      name += passenger.get('lastName').value;
      return name;
    }

    return "Passenger Details";
  }
  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
  }

  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    const dayHoverHandler = event.dayHoverHandler;
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        (picker as any)._datepickerRef.instance.daySelectHandler(cell);
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }

  goToPayment() {
    this.goToPaymentRequest.emit();
  }

  preventEntry(event) {
  }

  handleAddressChange(index, address) {
    (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[index]).controls['address']).setValue(address.formatted_address);

    this.autoFillZipCode(index, address);
  }

  autoFillZipCode(index, address) {
    let zipCodeControl = (<UntypedFormArray>(<UntypedFormGroup>(<UntypedFormArray>this.passengersForm.controls['passengers']).controls[index]).controls['zipCode']);
    if (zipCodeControl.pristine === true) {
      address.address_components.map(item => {
        if (item.types.includes("postal_code")) {
          zipCodeControl.setValue(item.short_name);
        }
      })
    }
  }

  get numOfFormPassengers(): number {
    return (<UntypedFormArray>this.passengersForm.get('passengers')).length;
  }

  searchByNameOrCode(term: string, item) {
    term = term.toLocaleLowerCase();
    if (item.dial_code) {
      return item['name'].toLocaleLowerCase().indexOf(term) > -1 || item['dial_code'].toLocaleLowerCase().indexOf(term) > -1;
    }
  }

  getCountryCode(code) {
    let countryCode;

    this.countries.map(item => {
      if (item.dial_code == code) {
        countryCode = item.code;
      }
    });
    if (countryCode) {
      return countryCode.toLowerCase();
    } else {
      return "";
    }
  }

  unsubscribeEvents() {
    if (this.selectedFlightSubscription) {
      this.selectedFlightSubscription.unsubscribe();
    }
    this.deviceSubscription.unsubscribe();
    this.userDetailsSubscription.unsubscribe();
  }

  ngOnDestroy() {
    this.unsubscribeEvents();
  }

  getPassengerFormArrayControl(): AbstractControl[] {
    return (<UntypedFormArray>this.passengersForm.controls['passengers']).controls;
  }

  preventCheck(event) {
    event.preventDefault();
  }
}
