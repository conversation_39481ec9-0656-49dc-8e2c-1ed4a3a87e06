<form [formGroup]="passengersForm">
  <div class="container">
    <ng-container formArrayName="passengers">

      <accordion [closeOthers]="true">
        <accordion-group [isOpen]="i == 0"
          *ngFor="let passenger of getPassengerFormArrayControl(); let i= index; let last = last" [formGroupName]="i"
          [isDisabled]="passengersForm.controls['passengers'].controls.length === 1 || checkIfPrecedingPassengersInvalid(i)"
          [ngClass]="{'disabled': passengersForm.controls['passengers'].controls.length === 1 && i === 0}"
          (click)="markPreviousPassengersTouched(i)">

          <div class="card-heading" accordion-heading>
            <span class="circle">
              <span class="number">{{i+1}}</span>
            </span>

            <span class="heading"> {{getPassengerFormTitle(i)}}</span>
            <span class="triangle"></span>

          </div>

          <div>
            <div class="row">
              <div class="col-6 col-md-4">
                <div class="field-input">
                  <div class="label">{{'passenger.Title' | translate}}
                  </div>
                  <ng-select id="title-passenger" [tabIndex]="1 * (i + 1)" dropdownPosition="bottom"
                    placeholder="{{'passenger.Title' | translate}}" [searchable]="false" [clearable]="false"
                    [items]="titleOptions" formControlName="title"></ng-select>
                  <div
                    *ngIf="passenger.controls['title'].hasError('required') && (passenger.controls['title'].touched || passenger.controls['title'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>

              <div class="w-100"></div>

              <div class="col-12">
                <div class="label">{{'passenger.Name' | translate}}
                </div>
              </div>

              <div class="col-12 col-md-4">
                <div class="field-input">
                  <input type="text" tabindex="{{2 * (i + 1)}}" class="field-control"
                    placeholder="{{'passenger.firstName' | translate}}" formControlName="firstName" />
                  <div
                    *ngIf="passenger.controls['firstName'].hasError('pattern') && (passenger.controls['firstName'].touched || passenger.controls['firstName'].dirty)"
                    class="error">{{'passenger.PleaseenteravalidfirstnameNumericorspecialcharactersarenotallowed' |
                    translate}}

                  </div>
                  <div
                    *ngIf="passenger.controls['firstName'].hasError('required') && (passenger.controls['firstName'].touched || passenger.controls['firstName'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>

              <div class="col-12 col-md-4">
                <div class="field-input">
                  <input type="text" tabindex="{{3 * (i + 1)}}" class="field-control" placeholder="{{'passenger.MiddlenameifshownonID' | translate}}
                  " formControlName="middleName" />
                  <div
                    *ngIf="passenger.controls['middleName'].hasError('pattern') && (passenger.controls['middleName'].touched || passenger.controls['middleName'].dirty)"
                    class="error">{{'passenger.PleaseenteravalidmiddlenameNumericorspecialcharactersarenotallowed' |
                    translate}}
                  </div>
                </div>
              </div>

              <div class="col-12 col-md-4">
                <div class="field-input">
                  <input type="text" tabindex="{{4 * (i + 1)}}" class="field-control"
                    placeholder="{{'passenger.LastName' | translate}}" formControlName="lastName" />
                  <div
                    *ngIf="passenger.controls['lastName'].hasError('pattern') && (passenger.controls['lastName'].touched || passenger.controls['firstName'].dirty)"
                    class="error">{{'passenger.PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed' |
                    translate}}
                  </div>
                  <div
                    *ngIf="passenger.controls['lastName'].hasError('required') && (passenger.controls['lastName'].touched || passenger.controls['lastName'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>

              <div class="w-100"></div>

              <div class="col-12 col-md-6 col-lg-4">
                <div class="field-input">
                  <div class="label">{{'passenger.Gender' | translate}}</div>
                  <div class="row">
                    <div class="col">
                      <div tabindex="{{5 * (i + 1)}}" class="toggle-button" (keyup.enter)="setGender(i,'M')"
                        [ngClass]="{'selected': isSelectedGender(i,'M')}" (click)="setGender(i,'M')">{{'passenger.Male'
                        | translate}}</div>
                    </div>

                    <div class="col">
                      <div tabindex="{{6 * (i + 1)}}" class="toggle-button" (keyup.enter)="setGender(i,'F')"
                        [ngClass]="{'selected': isSelectedGender(i,'F')}" (click)="setGender(i,'F')">
                        {{'passenger.Female' | translate}}</div>
                    </div>

                    <div class="col" style="display:none;">
                      <div tabindex="{{7 * (i + 1)}}" class="toggle-button" (keyup.enter)="setGender(i,'O')"
                        [ngClass]="{'selected': isSelectedGender(i,'O')}" (click)="setGender(i,'O')">{{'passenger.Other'
                        | translate}}</div>
                    </div>
                  </div>
                  <div
                    *ngIf="passenger.controls['gender'].hasError('required') && (passenger.controls['gender'].touched || passenger.controls['gender'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>

              <div class="col-12 col-md-6 col-lg-4">
                <div class="field-input">
                  <div class="label">{{'passenger.DateofBirth' | translate}}
                  </div>
                  <div class="on-right">
                    <input tabindex="{{8 * (i + 1)}}" class="field-control" bsDatepicker #dobDatePicker="bsDatepicker"
                      [outsideClick]="true" [bsConfig]="{showWeekNumbers: false}"
                      placeholder="{{'passenger.MMDDYY' | translate}}" [maxDate]="maxDoBDate"
                      formControlName="dateOfBirth" (keydown)="preventEntry($event)"
                      (onShown)="onShowPicker($event, dobDatePicker)" (onHidden)="onHidePicker()" readonly />
                    <span class="icon icon-calendar" (click)="dobDatePicker.toggle()"></span>
                  </div>
                  <div
                    *ngIf="passenger.controls['dateOfBirth'].hasError('required') && (passenger.controls['dateOfBirth'].touched || passenger.controls['dateOfBirth'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                  <div
                    *ngIf="passenger.controls['dateOfBirth'].hasError('lessThanMinAge') && (passenger.controls['dateOfBirth'].touched || passenger.controls['dateOfBirth'].dirty)"
                    class="error">{{'passenger.Agecannotbelessthan12years' | translate}}

                  </div>
                </div>
              </div>

              <div class="w-100"></div>

              <div class="col-12 col-md-6 col-lg-4">
                <div class="field-input">
                  <div class="label">{{'passenger.Email' | translate}}</div>
                  <input tabindex="{{9 * (i + 1)}}" type="email" class="field-control"
                    (keydown.space)="$event.preventDefault()" placeholder="{{'passenger.Email' | translate}}"
                    formControlName="email" />
                  <div
                    *ngIf="passenger.controls['email'].hasError('pattern') && (passenger.controls['email'].touched || passenger.controls['email'].dirty)"
                    class="error">{{'passenger.Pleaseenteravalidemail' | translate}}
                  </div>
                  <div
                    *ngIf="passenger.controls['email'].hasError('required') && (passenger.controls['email'].touched || passenger.controls['email'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>

              <div class="col-12 col-md-6 col-lg-4">
                <div class="field-input">
                  <div class="label">{{'passenger.PhoneNumber' | translate}} </div>
                  <div class="flex-wrapper">
                    <ng-select tabindex="{{9 * (i + 1)}}" class="phone-number" dropdownPosition="bottom"
                      [items]="countries" formControlName="dialCode" bindValue="dial_code"
                      [searchFn]="searchByNameOrCode">
                      <ng-template ng-label-tmp let-item="item">
                        <img class="flag-label"
                          src="assets/flags/{{getCountryCode(passenger.controls['dialCode'].value)}}.png" />
                        <span>{{passenger.controls['dialCode'].value}}</span>
                      </ng-template>
                      <ng-template ng-option-tmp let-item="item">
                        <span class="country-name" title="{{item.name}}">{{item.name}}</span>
                        <img class="icon-country-flag" src="assets/flags/{{item.code | lowercase}}.png" />
                      </ng-template>
                    </ng-select>
                    <div
                      *ngIf="passenger.controls['dialCode'].hasError('required') && (passenger.controls['phoneNumber'].touched || passenger.controls['phoneNumber'].dirty)"
                      class="error">{{'passenger.Thisfieldisrequired' | translate}}
                    </div>

                    <div class="flex-fill">
                      <input tabindex="{{10 * (i + 1)}}" type="tel" mask="(*************" class="field-control"
                        placeholder="(*************" formControlName="phoneNumber" />

                      <div
                        *ngIf="passenger.controls['phoneNumber'].hasError('pattern') && (passenger.controls['phoneNumber'].touched || passenger.controls['phoneNumber'].dirty)"
                        class="error">{{'passenger.Pleaseenteravalidphonenumber' | translate}}
                      </div>
                      <div
                        *ngIf="passenger.controls['phoneNumber'].hasError('required') && (passenger.controls['phoneNumber'].touched || passenger.controls['phoneNumber'].dirty)"
                        class="error">{{'passenger.Thisfieldisrequired' | translate}}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="w-100"></div>

              <div class="col-12 col-md-8">
                <div class="field-input">
                  <div class="label">{{'passenger.Address' | translate}}</div>
                  <input tabindex="{{11 * (i + 1)}}" ngx-google-places-autocomplete
                    (onAddressChange)="handleAddressChange(i, $event)" type="text" class="field-control"
                    placeholder="{{'passenger.Writeyourhomeaddresshere' | translate}}" formControlName="address" />
                  <div
                    *ngIf="passenger.controls['address'].hasError('required') && (passenger.controls['address'].touched || passenger.controls['address'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>

              <div class="col-12 col-md-4">
                <div class="field-input">
                  <div class="label">{{'passenger.ZipCode' | translate}}</div>
                  <input tabindex="{{12 * (i + 1)}}" type="text" class="field-control"
                    placeholder="{{'passenger.AreaZipCode' | translate}}" formControlName="zipCode" />
                  <div
                    *ngIf="passenger.controls['zipCode'].hasError('pattern') && (passenger.controls['zipCode'].touched || passenger.controls['zipCode'].dirty)"
                    class="error">{{'passenger.Pleaseenteravalidzipcode' | translate}}
                  </div>
                  <div
                    *ngIf="passenger.controls['zipCode'].hasError('required') && (passenger.controls['zipCode'].touched || passenger.controls['zipCode'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>

              <div class="w-100"></div>

              <div class="col-12 col-md-6 col-xl-4" formArrayName="frequentFlyerInfo">
                <div class="field-input"
                  *ngFor="let ffn of passenger.controls['frequentFlyerInfo'].controls; let j=index;"
                  [formGroupName]="j">
                  <div style="white-space: unset;" class="label">{{'passenger.FrequentFlyerNumber' |
                    translate}}({{ffn.controls['name'].value}})</div>
                  <div class="check-or-input" [ngClass]="{'checked': !isTravelerNumAvailable(i,'FFN',j)}">
                    <input tabindex="{{13 * (i + 1)}}" type="text" class="field-control"
                      placeholder="{{'passenger.FlyerNumber' | translate}}" formControlName="frequent_flyer_number" />
                    <div class="check-input" (click)="checkTravelerNumNotAvailableCheckBox(i,'FFN', ffnref, j)">
                      <input tabindex="{{14 * (i + 1)}}" type="checkbox" [checked]="!isTravelerNumAvailable(i,'FFN',j)"
                        id="{{'ffn-' + i}}" (click)="preventCheck($event)" #ffnref />
                      <label for="{{'ffn-' + i}}">{{'passenger.NotAvailable' | translate}}</label>
                    </div>
                  </div>
                  <div
                    *ngIf="ffn.controls['frequent_flyer_number'].hasError('required') && (ffn.controls['frequent_flyer_number'].touched || ffn.controls['frequent_flyer_number'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                  <div
                    *ngIf="ffn.controls['frequent_flyer_number'].hasError('pattern') && (ffn.controls['frequent_flyer_number'].touched || ffn.controls['frequent_flyer_number'].dirty)"
                    class="error">{{'passenger.PleaseenteravalidFrequentFlyerNumber' | translate}}
                  </div>

                </div>
              </div>

              <div class="col-12 col-md-6 col-xl-4">
                <div class="field-input">
                  <div class="label">{{'passenger.KnownTravelerNumber' | translate}}</div>
                  <div class="check-or-input" [ngClass]="{'checked': !isTravelerNumAvailable(i,'KTN')}">
                    <input tabindex="{{15 * (i + 1)}}" type="text" class="field-control"
                      placeholder="{{'passenger.KTNNumber' | translate}}" formControlName="knownTravellerNumber"
                      maxlength="12" />
                    <div class="check-input" (click)="checkTravelerNumNotAvailableCheckBox(i,'KTN', ktnref)">
                      <input tabindex="{{16 * (i + 1)}}" type="checkbox" [checked]="!isTravelerNumAvailable(i,'KTN')"
                        id="{{ 'ktn-' + i}}" (click)="preventCheck($event)" #ktnref />
                      <label for="{{ 'ktn-' + i}}">{{'passenger.NotAvailable' | translate}}</label>
                    </div>
                  </div>
                  <div
                    *ngIf="passenger.controls['knownTravellerNumber'].hasError('minlength') && !passenger.controls['knownTravellerNumber'].hasError('pattern') && (passenger.controls['knownTravellerNumber'].touched || passenger.controls['knownTravellerNumber'].dirty)"
                    class="error">{{'passenger.Thisfieldcannotbelessthan9characters' | translate}}
                  </div>
                  <div
                    *ngIf="passenger.controls['knownTravellerNumber'].hasError('required') && (passenger.controls['knownTravellerNumber'].touched || passenger.controls['knownTravellerNumber'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                  <div
                    *ngIf="passenger.controls['knownTravellerNumber'].hasError('pattern') && (passenger.controls['knownTravellerNumber'].touched || passenger.controls['knownTravellerNumber'].dirty)"
                    class="error">{{'passenger.PleaseenteravalidKnownTravelerNumber' | translate}}
                  </div>
                </div>
              </div>
            </div>

            <div class="sub-heading" *ngIf="isPassportRequired">Passport Information</div>

            <div class="row" *ngIf="isPassportRequired">
              <div class="col-12 col-md-4">
                <div class="field-input">
                  <div class="label">{{'passenger.PassportNumber' | translate}}</div>

                  <input tabindex="{{17 * (i + 1)}}" type="text" class="field-control"
                    placeholder="{{'passenger.EnterPassportNumber' | translate}}" formControlName="passportNumber" />
                  <div
                    *ngIf="passenger.controls['passportNumber'].hasError('pattern') && (passenger.controls['passportNumber'].touched || passenger.controls['passportNumber'].dirty)"
                    class="error">{{'passenger.Pleaseenteravalidpassportnumber' | translate}}
                  </div>
                  <div
                    *ngIf="passenger.controls['passportNumber'].hasError('required') && (passenger.controls['passportNumber'].touched || passenger.controls['passportNumber'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>

              <div class="col-12 col-md-4">
                <div class="field-input">
                  <div class="label">{{'passenger.IssuanceCountry' | translate}}</div>
                  <ng-select tabindex="{{18 * (i + 1)}}" dropdownPosition="bottom"
                    placeholder="{{'passenger.SelectIssuanceCountry' | translate}}" [items]="countries" bindLabel="name"
                    formControlName="passportCountry"></ng-select>
                  <!-- <i18n-country-select [(iso3166Alpha2)]="country.isocode" size="sm" placeholder="Select Issuance Country" (click)="onSelectPassportCountry(i,$event)"></i18n-country-select> -->

                  <div
                    *ngIf="passenger.controls['passportCountry'].hasError('required') && (passenger.controls['passportCountry'].touched || passenger.controls['passportCountry'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>

              <div class="col-12 col-md-4">
                <div class="field-input">
                  <div class="label">{{'passenger.Nationality' | translate}}</div>
                  <ng-select tabindex="{{19 * (i + 1)}}" dropdownPosition="bottom"
                    placeholder="{{'passenger.SelectNationality' | translate}}" [items]="countries"
                    bindLabel="nationality" formControlName="passportNationality"></ng-select>
                  <div
                    *ngIf="passenger.controls['passportNationality'].hasError('required') && (passenger.controls['passportNationality'].touched || passenger.controls['passportNationality'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>

              <div class="col-6 col-md-4">
                <div class="field-input">
                  <div class="label">{{'passenger.ExpiryDate' | translate}}</div>
                  <div class="on-right">
                    <input tabindex="{{20 * (i + 1)}}" class="field-control" bsDatepicker
                      #expiryDatePicker="bsDatepicker" [outsideClick]="true" [bsConfig]="{showWeekNumbers: false}"
                      placeholder="{{'passenger.MMDDYY' | translate}}" [minDate]="minPassportDate"
                      formControlName="passportExpiryDate" (keydown)="$event.preventDefault()" placement="top"
                      (onShown)="onShowPicker($event, expiryDatePicker)" (onHidden)="onHidePicker()" readonly />
                    <span class="icon icon-calendar" (click)="expiryDatePicker.toggle()"></span>
                  </div>

                  <div
                    *ngIf="passenger.controls['passportExpiryDate'].hasError('required') && (passenger.controls['passportExpiryDate'].touched || passenger.controls['passportExpiryDate'].dirty)"
                    class="error">{{'passenger.Thisfieldisrequired' | translate}}
                  </div>
                </div>
              </div>
            </div>
            <div class="button-wrapper" *ngIf="last && !isMobile"><button tabindex="{{21 * (i + 1)}}"
                [disabled]="passengersForm.invalid" (click)="goToPayment()" class="btn primary-button continue-button"
                type="button">Continue</button>
            </div>
          </div>
        </accordion-group>
      </accordion>
    </ng-container>
  </div>
</form>