import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { UserProfileTablistComponent } from './user-profile-tablist.component';

describe('UserProfileTablistComponent', () => {
  let component: UserProfileTablistComponent;
  let fixture: ComponentFixture<UserProfileTablistComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [UserProfileTablistComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UserProfileTablistComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
