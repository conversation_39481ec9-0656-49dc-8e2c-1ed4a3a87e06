import { Component, OnInit } from '@angular/core';
import { ChartType } from 'chart.js';
import { Chart } from 'chart.js';
//import { MultiDataSet, Label } from 'ng2-charts';
import { AdminPanelService, CompanySettings, Department } from '../admin-panel.service';
import { Constants } from '../util/constants';
import { DataType, ColorType } from '../entity/email-flow/barChart';
import { Subscription } from 'rxjs';
import { SearchService } from '../search.service';
import { CommonUtils } from '../util/common-utils';
import { DateUtils } from '../util/date-utils';
import { UserAccountService } from '../user-account.service';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { DatePipe } from '@angular/common';
import { ClientConfiguration } from '../client-config.service';
import { environment } from 'src/environments/environment';
import { NgxSmartModalService } from 'ngx-smart-modal';
@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.scss'],
    standalone: false
})
export class DashboardComponent implements OnInit {

  changeStyle() {
    if (this.viewMode1 === 'tab12') {
      return { 'min-width': '230px' };
    } else {
      return { 'min-width': '730px' }
    }
  }
  changeStyle1() {
    if (this.viewMode1 === 'tab12') {
      return { 'height': '300px', 'overflow': 'auto' };
    } else {
      return { 'height': '335px', 'overflow': 'hidden' }
    }
  }
  datalabelConfig: any = {
    anchor: 'end',
    align: 'end',
    clamp: true,
    offset: 0,
    color: 'black',
    'font-weight': 'bolder',
    display: 'auto',
    formatter: this.formatterFn
  };
  verticalLabelConfig = {
    anchor: 'end',
    align: 'end',
    clamp: true,
    offset: -2,
    color: 'black',
    'font-weight': 'bolder',
    display: 'auto',
    rotation: 0,
    formatter: this.formatterFn
  };
  layoutPaddingOption: any = {
    padding: {
      top: 25,
      right: 25, left: 20, bottom: 0
    }
  };
  layoutPaddingOptionWrapper: any = {
    layout: this.layoutPaddingOption
  }
  barChartOptions: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    layout: this.layoutPaddingOption,
    maintainAspectRatio: false
  };
  doughnutChartOptions: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    maintainAspectRatio: false
  };
  pieChartOptions: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    maintainAspectRatio: false
  };
  pieChartOptions1: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    maintainAspectRatio: false
  };
  labelName = [];
  labelName1 = [];
  labelName2 = [];
  empName1 = []
  empName2 = []
  dateRange = []
  default = true;
  disableDateType = false;
  msg = '';
  msg1 = '';
  msg11 = '';
  viewMode1 = 'tab11';
  msg12 = '';
  bookingTable = '';
  totalApprove = 0;
  averagemsg = '';
  currency = 'USD';
  barChartOptions1: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    layout: this.layoutPaddingOption,
  };
  barChartOptions4: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    layout: this.layoutPaddingOption,
  };
  barChartOptions5: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    layout: this.layoutPaddingOption,
  };
  barChartOptions2: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    layout: this.layoutPaddingOption,
  };
  barChartOptions3: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    layout: this.layoutPaddingOption,
  };
  disableDate = true;
  disableDate1 = true;
  maximumDate1: Date = new Date();
  select = 'MONTHS';
  totalSpend = 0;
  totalBooking = 0;
  compliance = 0;
  travellers = 0;
  averagePrice = 0;
  averagePricePerce = [];
  startDate: Date = new Date();
  endDate: Date = new Date();
  companyId: number;
  companySettings: CompanySettings;
  companySettingsSubscription: Subscription;
  applyBtn = false;
  bookingValue = 'ALL';
  deptValue = 'All Departments';
  dateOptions = Constants.DATE_OPTIONS1;
  defaultQuerySelection = 'CURRMONTH';
  dateValue = this.defaultQuerySelection;
  departmentOptions = [{ value: 'All Departments', id: '' }];
  bookingOptions = [{ id: 'ALL', value: 'All' }, { id: 'CAR', value: 'Car Spend' }, { id: 'FLIGHT', value: 'Flight Spend' }, { id: 'HOTEL', value: 'Hotel Spend' }];
  barChartLabels: string[] = [];
  barChartLabels1: string[] = [];
  barChartLabels2: string[] = [];
  barChartLabels3: string[] = [];
  barChartLabels4: string[] = [];
  barChartLabels5: string[] = [];
  weeklyTraveller = [];
  barChartType = 'bar';
  pieChartType = 'pie'
  barChartType1 = 'horizontalBar';
  resultErrorMessage = 'No data'
  barChartLegend = true;
  public doughnutChartLabels: any[] = ['Air', 'Cars', 'Hotels'];
  public pieChartLabels: any[] = ['Outside policy', 'Within policy'];
  public pieChartData = [];
  public pieChartData1 = [];
  public pieChartLabels1: any[] = ['Disapproved', 'Approved', 'Expired'];
  public doughnutChartData = [];
  public doughnutChartType: ChartType = 'doughnut';
  barChartData: DataType[] = [
    { options: this.layoutPaddingOptionWrapper, data: [], label: '', datalabels: this.datalabelConfig },
  ];
  barChartData4: DataType[] = [
    { options: this.layoutPaddingOptionWrapper, data: [], label: '', datalabels: this.verticalLabelConfig },
  ];
  barChartData5: DataType[] = [
    { options: this.layoutPaddingOptionWrapper, data: [], label: '', datalabels: this.verticalLabelConfig },
  ];
  barChartData1: DataType[] = [
    { options: this.layoutPaddingOptionWrapper, data: [], label: 'Top employees by $ spend', datalabels: this.verticalLabelConfig },
  ];
  barChartData3: DataType[] = [
    { options: this.layoutPaddingOptionWrapper, data: [], label: '', datalabels: this.verticalLabelConfig },
  ];
  barChartData2: DataType[] = [
    { options: this.layoutPaddingOptionWrapper, data: [], label: 'Top employees by # bookings', datalabels: this.verticalLabelConfig },
  ];

  public chartColors: Array<any> = [
    {
      backgroundColor: ['rgba(255,192,203 ,1 )'], borderColor: ['rgba(255,192,203 ,1 )'], borderWidth: 0.5,
    }
  ];

  public chartColors11: Array<any> = [
    {
      backgroundColor: ['#127DBE'], borderColor: ['#127DBE'], borderWidth: 0.5,
    }
  ];
  public chartColors21: Array<any> = [
    {
      backgroundColor: ['#A76B3D'], borderColor: ['#A76B3D'], borderWidth: 0.5,
    }
  ];
  public chartColors22: Array<any> = [
    {
      backgroundColor: ['#266666'], borderColor: ['#266666'], borderWidth: 0.5,
    }
  ];
  public chartColors3: Array<any> = [
    {
      backgroundColor: ['rgba(255,192,203 ,1 )'], borderColor: ['rgba(255,192,203 ,1 )'], borderWidth: 0.5,
    }
  ];
  public chartColors31: Array<any> = [
    {
      backgroundColor: ['#FB6786'], borderColor: ['#FB6786'], borderWidth: 0.5,
    }
  ];
  public chartColors32: Array<any> = [
    {
      backgroundColor: ['#F99D53'], borderColor: ['#F99D53'], borderWidth: 0.5,
    }
  ];
  public chartColors33: Array<any> = [
    {
      backgroundColor: ['#43A4E5'], borderColor: ['#43A4E5'], borderWidth: 0.5,
    }
  ];
  public chartColors1: Array<any> = [
    {
      backgroundColor: ['rgba(137, 196, 244, 1)'], borderColor: ['rgba(137, 196, 244, 1)'], borderWidth: 0.5,
    }
  ];
  public chartColors2: Array<any> = [
    {
      backgroundColor: ['rgba(78, 205, 196, 1)'], borderColor: ['rgba(78, 205, 196, 1)'], borderWidth: 0.5,
    }
  ];
  colors = [
    {
      backgroundColor: ['rgba(255,192,203 ,1 )', 'rgba(137, 196, 244, 1)'], borderColor: ['rgba(255,192,203 ,1 )', 'rgba(137, 196, 244, 1)'], borderWidth: 2,
    }];
  colors11 = [
    {
      backgroundColor: ['#ff6c5f', '#2dde98'], borderColor: ['#ff6c5f', '#2dde98', '#1cc7d0'], borderWidth: 2,
    }];

  colors12 = [
    {
      backgroundColor: ['#ffc168', '#1cc7d0', '#2dde98'], borderColor: ['#ffc168', '#1cc7d0', '#2dde98'], borderWidth: 2,
    }];
  colors1 = [
    {
      backgroundColor: ['rgba(78, 205, 196, 1)', 'yellow'], borderColor: ['rgba(78, 205, 196, 1)', 'yellow'], borderWidth: 2,
    }];

  public donutColors = [
    {
      backgroundColor: [
        '#FB6786',
        '#F99D53',
        '#43A4E5'
      ]
    }
  ];
  daterangepickerModel = [this.startDate, this.endDate];
  chartHovered(e: any): void {
  }
  constructor(
    private adminPanelService: AdminPanelService,
    private searchService: SearchService,
    private userAccountService: UserAccountService,
    public ngxSmartModalService: NgxSmartModalService,
    private clientConfig: ClientConfiguration
  ) {
    // this.daterangepickerModel =[this.startDate,this.endDate];
  }

  ngOnInit() {
    this.defaultQuerySelection = 'CURRMONTH';
    this.dateValue = this.defaultQuerySelection;
    // if (this.clientConfig.classicReports){
    //   this.defaultQuerySelection = 'CURRMONTH';
    //   this.dateValue = this.defaultQuerySelection;
    // }else{
    //   if (environment.production){
    //     this.defaultQuerySelection = 'Last 12 months';
    //   }else{
    //     this.defaultQuerySelection = 'CURRMONTH';
    //   }
    //   this.dateValue = this.defaultQuerySelection;
    // }
   // Chart.defaults.global.defaultFontColor = 'black';
    //Chart.defaults.global.defaultFontFamily = 'Apercu-r';
  //  Chart.defaults.global.defaultFontStyle = 'bold';
  //  Chart.plugins.unregister(ChartDataLabels);
    this.subscribe();
    this.companyId = this.userAccountService.getAccountInfo().userInfo.companyId;
    this.currency = this.userAccountService.getAccountInfo().userInfo.currency;
    this.getSelectedCurrentDate(this.defaultQuerySelection);
    // this.getChart();
  }
  ngOnDestroy() {
    this.companySettingsSubscription.unsubscribe();
    this.adminPanelService.previousDate = '';
    this.adminPanelService.previousDept = '';
    this.adminPanelService.previousBookingType = '';
  }
  subscribe() {
    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {
        this.companySettings = settings;
        this.companyId = this.companySettings.company.company_id;
        this.currency = this.companySettings.company.currency;
        this.getDepartments();
      }
    });
  }
  getSelectedBookingType(id: string) {
    let bookingObj = this.bookingOptions.find(obj => obj.id === id);
    if (bookingObj) {
      return bookingObj.value;
    }
  }

  getSelectedDepartmentNamed(id: string) {
    let departmentObj = this.departmentOptions.find(obj => obj.value === id);
    // if (!id || id === '0' || id === 'All Departments'){
    // departmentObj = this.departmentOptions.find(obj=> obj.value === this.adminPanelService.filterAppliedDepartmentId);
    //}
    if (departmentObj) {
      return departmentObj.value;
    } else {
      return 'All Departments';
    }
  }
  customTabClicked() {
    this.viewMode1 = 'tab11';
  }
  presetsTabClicked() {
    this.viewMode1 = 'tab12';
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  openNgxModal(id, picker) {
    if (this.disableDateType) {
      return;
    }
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);

    setTimeout(() => {
      this.viewMode1 = 'tab11';
      picker.show();
    }, 200);
  }
  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    // picker._config.value = this.daterangepickerModel;
    const dayHoverHandler = event.dayHoverHandler;
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        (picker as any)._datepickerRef.instance.daySelectHandler(cell);
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }
  getStartDate(picker) {
    picker._bsValue = this.startDate;
    return this.startDate;
  }

  getEndDate(picker) {
    picker._bsValue = this.endDate;
    return this.endDate;
  }
  chartClicked(e: any): void {
    if (e.active.length > 0) {
      if (e.active[0] && e.active[0]._index === 0) {
        this.bookingValue = 'FLIGHT';
        this.getChart();
      } else if (e.active[0] && e.active[0]._index === 1) {
        this.bookingValue = 'CAR';
        this.getChart();
      } else if (e.active[0] && e.active[0]._index === 2) {
        this.bookingValue = 'HOTEL';
        this.getChart();
      }
    }
  }
  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
    this.ngxSmartModalService.close('daterangeSelection');
  }
  getQuarter(date) {
    var m = 0;
    var n = ((date.getMonth() / 3));
    var decimal = ((date.getMonth() % 3))
    if (n >= 0 && n < 1) {
      m = 1;
    } else if (n >= 1 && n < 2) {
      m = 2;
    } else if (n >= 2 && n < 3) {
      m = 3;
    } else if (n >= 3 && n < 4) {
      m = 4;
    }
    var quarter;
    if (m > 4) {
      quarter = (m - 4);
    } else {
      quarter = m;
    }
    if (m && m === 1) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 0, 1);
    } else if (m && m === 2) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 3, 1);
    }
    else if (m && m === 3) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 6, 1);
    }
    else if (m && m === 4) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 9, 1);
    }
  }
  getSelectedCurrentDate(id: string) {
    if (id === 'CURRMONTH') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(1);
      this.startDate.setMonth(this.startDate.getMonth());
      this.disableDate = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.disableDate1 = true;
    } else if (id === 'Last 12 months') {
      this.startDate = new Date();
      this.endDate = new Date();
      for (var i = 0; i < 12; i++) {
        this.startDate.setMonth(this.startDate.getMonth() - 1);
      }
      this.disableDate = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.disableDate1 = true;
    }
    else if (id === 'Currentquarter') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.getQuarter(this.startDate);
      this.disableDate = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.disableDate1 = true;
    } else if (id === 'Currentyear') {
      this.endDate = new Date();
      this.startDate = new Date();
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 0, 1);
      this.disableDate = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.disableDate1 = true;
    }
    else if (id === 'LAST7DAYS') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 7);
      this.disableDate = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.disableDate1 = true;
    } else if (id === 'LAST30DAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 30);
      this.disableDate = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.disableDate1 = true;
    } else if (id === 'LAST90DAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 90);
      this.disableDate = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.disableDate1 = true;
    } else if (id === 'LASTMONTH') {
      this.startDate = new Date();
      this.startDate.setDate(1);
      this.startDate.setMonth(this.startDate.getMonth() - 1);
      this.endDate = new Date(this.startDate.getFullYear(), this.startDate.getMonth() + 1, 0);
      this.disableDate = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.disableDate1 = true;
    } else if (id === 'CUSTOMDATE') {
      this.disableDate = false;
      this.disableDate1 = false;
      this.daterangepickerModel = [this.startDate, this.endDate];
    }
    let dateObj = this.dateOptions.find(obj => obj.id === id);
    if (dateObj) {
      return dateObj.value;
    }
  }
  setDefaultGrouping() {
    const oneDay = 24 * 60 * 60 * 1000;
    var diff = Math.round(Math.abs((this.startDate.getTime() - this.endDate.getTime()) / oneDay));
    if (diff > 60) {
      this.select = 'MONTHS';
    } else if (diff <= 60) {
      this.select = 'DAYS';
    }
  }

  getChart() {
    this.resetValue();
    //if(this.default){
    this.setDefaultGrouping();
    //}
    let startDate = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
    let endDate = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);
    this.totalSpend = 0;
    this.totalBooking = 0;
    this.weeklyTraveller = [];
    this.compliance = 0;
    this.travellers = 0;
    this.averagePrice = 0;
    this.disableDateType = true;
    this.adminPanelService.getChartData(endDate, startDate, this.companyId, this.deptValue, this.bookingValue, this.select, this.dateValue, this.userAccountService.getUserEmail()).subscribe(res => {
      for (let key in res.data.aggDateTypeData) {
        if (key === this.select) {
          res.data.aggSpendDataByDateType = res.data.aggDateTypeData[key];
          break;
        }
      }
      this.getGraphData(res);
      this.doughnutChartData = [];
      let startDate = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
      let endDate = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);
      this.adminPanelService.previousDate = startDate + 'T' + endDate;
      this.adminPanelService.previousDept = this.deptValue;
      this.adminPanelService.previousBookingType = this.bookingValue;
      this.averagePricePerce = [];
      this.adminPanelService.origDashBoardResponse = res;
      if (res && res.success) {
        this.applyBtn = false;
        this.disableDateType = false;
        if (this.bookingValue === 'ALL') {
          var totalSpend = (res.data.totalSpendHotel + res.data.totalSpendFlight + res.data.totalSpendCar);
          this.totalSpend = totalSpend;
          var totalBooking = (res.data.totalbookingFlight + res.data.totalbookingCar + res.data.totalbookingHotel);
          this.totalBooking = totalBooking;
          this.averagePricePerce[0] = ((res.data.totalSpendFlight * 100) / this.totalSpend).toFixed(2);
          this.averagePricePerce[1] = ((res.data.totalSpendCar * 100) / this.totalSpend).toFixed(2);
          this.averagePricePerce[2] = ((res.data.totalSpendHotel * 100) / this.totalSpend).toFixed(2);
          this.travellers = res.data.travellerCount ? res.data.travellerCount : 0;
          this.compliance = res.data.compliancePerc ? res.data.compliancePerc : 0;
          var average = (res.data.totalSpendFlight / res.data.totalbookingFlight)
          if ((res.data.totalSpendFlight === 0 && res.data.totalSpendCar === 0) && res.data.totalSpendHotel == 0) {
            this.doughnutChartData = [0, 0, 0];
            this.resultErrorMessage = 'No data'
          } else {
            this.doughnutChartData.push(res.data.totalSpendFlight);
            this.doughnutChartData.push(res.data.totalSpendCar);
            this.doughnutChartData.push(res.data.totalSpendHotel);
            let self3 = this;
            this.doughnutChartOptions = {
              legend: {
                display: true,
                responsive: false
              },
              tooltips: {
                callbacks: {
                  label: function (tooltipItem, data) {
                    let value = self3.doughnutChartLabels[tooltipItem.index];
                    let range = self3.doughnutChartData[tooltipItem.index];
                    let average = self3.averagePricePerce[tooltipItem.index];
                    return (value + '( ' + self3.getCurrencySymbol(self3.currency) + range + ", " + average + '%' + ' )');
                  }
                }
              }
            }
          }
        }
      }
    });
  }
  getGraphData(res) {
    if (res && res.success) {
      this.applyBtn = false;
      this.disableDateType = false;
      this.weeklyTraveller = [];
      if ((res.data.totalSpendFlight === 0 && res.data.totalSpendCar === 0) && res.data.totalSpendHotel == 0) {
        this.resultErrorMessage = 'No data'
        if (this.bookingValue === 'FLIGHT') {
          this.msg1 = 'Air spend'
        } else if (this.bookingValue === 'CAR') {
          this.msg1 = 'Car spend'
        } else if (this.bookingValue === 'HOTEL') {
          this.msg1 = 'Hotel spend'
        }
        this.pieChartData = [];
        this.pieChartData1 = [];
        this.pieChartOptions = {
          legend: {
            display: false,
            responsive: true
          },
        }
        this.pieChartOptions1 = {
          legend: {
            display: false,
            responsive: true
          },
        }
        this.barChartOptions = {
          layout: this.layoutPaddingOption,
          scales: {
            yAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Total Spend',
              },
              ticks: {
                beginAtZero: true,
                precision: 0,
                callback: function (label, index, labels) {
                  if (label >= 1000) {
                    return ((label / 1000).toFixed(1)) + 'k';
                  } else {
                    return label;
                  }
                }
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
        }
        this.barChartOptions2 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Total Spend',
              },
              ticks: {
                beginAtZero: true,
                precision: 0,

              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
        }
        this.barChartOptions3 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Number of bookings',
              },
              ticks: {
                beginAtZero: true,
                precision: 0,

              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          }
        }
        this.barChartOptions1 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Number of bookings',
              },
              ticks: {
                beginAtZero: true,
                precision: 0
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          }
        }
        this.barChartOptions4 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Number of bookings',
              },
              ticks: {
                beginAtZero: true,
                precision: 0
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          }
        }
        this.barChartOptions5 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Number of bookings',
              },
              ticks: {
                beginAtZero: true,
                precision: 0
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          }
        }
      }
      if (this.bookingValue === 'ALL') {
        if ((res.data.totalSpendFlight === 0 && res.data.totalSpendCar === 0) && res.data.totalSpendHotel == 0) {
          this.resultErrorMessage = 'No data'
          this.pieChartData = [];
          this.pieChartData1 = [];
          this.msg1 = 'Total spend'
        } else {
          if (res.data.aggSpendDataByDateType && res.data.aggSpendDataByDateType.length > 0) {
            if (res.data.weeklyNumbers && res.data.weeklyNumbers.length > 0) {
              for (let item of res.data.weeklyNumbers) {
                this.bookingTable = 'flights';
                let weektraveller = {};
                weektraveller['flightNUmber'] = item.flightTravellers;
                weektraveller['carNUmber'] = item.carTravellers;
                weektraveller['hotelNUmber'] = item.hotelTravellers;
                weektraveller['label'] = item.label;
                weektraveller['totalTraveller'] = item.totalTravellers;
                let average1 = '-';
                if (item.totalTravellers !== 0) {
                  average1 = ((item.flightTravellers * 100) / item.totalTravellers).toFixed(2);
                  average1 = average1 + '%';
                }
                weektraveller['perc'] = average1;
                this.weeklyTraveller.push(weektraveller);
              }
            }
            for (let i = 0; i < res.data.aggSpendDataByDateType.length; i++) {
              //const [date , month ] = res.data.aggSpendDataByDateType[i].label.split(",");
              if (this.select === "DAYS") {
                let date1 = new Date(res.data.aggSpendDataByDateType[i].label);
                let datePipe = new DatePipe('en-US');
                let label1 = datePipe.transform(new Date(date1), 'MMM d, EEE');;
                this.barChartLabels.push(label1);
              } else {
                this.barChartLabels.push(res.data.aggSpendDataByDateType[i].label);
              }
              this.barChartData[0].data.push(res.data.aggSpendDataByDateType[i].totalSpend)
              this.barChartData[0].label = 'Total Spend';
              this.msg1 = 'Total Spend';
              this.averagemsg = 'per booking';
              if (this.select !== "DAYS") {
                var range = res.data.aggSpendDataByDateType[i].startDate + ' - ' + res.data.aggSpendDataByDateType[i].endDate;
                this.dateRange.push(range);
              } else {
                this.dateRange.push(res.data.aggSpendDataByDateType[i].startDate);
              }
              this.msg1 = 'Total spend';
              if (res.data.totalbookingFlight > 0) {
                var average = (res.data.totalSpendFlight / res.data.totalbookingFlight);
                this.averagePrice = average;
              } else {
                this.averagePrice = 0;
              }
              this.travellers = res.data.travellerCount;
              this.pieChartData1 = [res.data.numberOfDisapprovals, res.data.numberOfApprovals, res.data.numberOfExpirations],
                this.totalApprove = res.data.numberOfDisapprovals + res.data.numberOfApprovals + res.data.numberOfExpirations;
              this.getColor()
            }
            for (let i = 0; i < res.data.aggSpendDataByDateTypeUserSpend.length; i++) {
              // const [date , month ] = res.data.aggSpendDataByDateTypeUserSpend[i].label.split(",");
              this.barChartLabels1.push(res.data.aggSpendDataByDateTypeUserSpend[i].label);
              this.empName1.push(res.data.aggSpendDataByDateTypeUserSpend[i].employeeName);
              this.barChartData1[0].data.push(res.data.aggSpendDataByDateTypeUserSpend[i].totalSpend)
              this.getColor()
            }
            this.compliance = res.data.compliancePerc.toFixed(2);
            var outsidePolicy = ((100 - this.compliance).toFixed(2));
            if (this.barChartData1[0].data.length > 0) {
              this.pieChartData = [outsidePolicy, this.compliance];
            } else {
              this.pieChartData = [];
            }
            let self4 = this;
            this.pieChartOptions = {
              legend: {
                display: false,
                responsive: true
              },
              plugins: {
                maintainAspectRatio: true,
                datalabels: {
                  formatter: (value, ctx) => {
                    let percentage = Math.round(value) + "%";
                    if (value > 0) {
                      return percentage;
                    } else {
                      return '';
                    }
                  },
                  color: '#000000',
                }
              },
              tooltips: {
                callbacks: {
                  label: function (tooltipItem, data) {
                    let value = self4.pieChartData[tooltipItem.index];
                    return (value + '%');
                  }
                }
              }
            }
            this.pieChartOptions1 = {
              legend: {
                display: false,
                responsive: true
              },
              plugins: {
                datalabels: {
                  formatter: (value, ctx) => {
                    let percentage = value;
                    if (percentage > 0) {
                      return percentage;
                    } else {
                      return '';
                    }
                  },
                  color: '#000000',
                }
              },
              tooltips: {
                callbacks: {
                  label: function (tooltipItem, data) {
                    let value = self4.pieChartData1[tooltipItem.index];
                    let average = ((value * 100) / self4.totalApprove).toFixed(2);
                    return (value + ' of ' + self4.totalApprove + ', ' + average + '%');
                  }
                }
              }
            }
            //  let self2 = this;
            this.barChartOptions = {
              layout: this.layoutPaddingOption,
              scales: {
                yAxes: [{
                  scaleLabel: {
                    display: true,
                    labelString: 'Total Spend',
                  },
                  ticks: {
                    beginAtZero: true,
                    precision: 0,
                    callback: function (label, index, labels) {
                      if (label >= 1000) {
                        return ((label / 1000).toFixed(1)) + 'k';
                      } else {
                        return label;
                      }
                    }
                  }
                }],
              },
              legend: {
                display: false,
                responsive: false
              },
              tooltips: {
                callbacks: {
                  label: function (tooltipItem, data) {
                    let range = self4.dateRange[tooltipItem.index];
                    return (range + '(' + self4.getCurrencySymbol(self4.currency) + tooltipItem.value + ')');
                  }
                }
              }
            }
            this.barChartOptions2 = {
              layout: this.layoutPaddingOption,
              scales: {
                xAxes: [{
                  scaleLabel: {
                    display: true,
                    labelString: 'Total Spend',
                  }
                }],
              },
              legend: {
                display: false,
                responsive: false
              },
              tooltips: {
                callbacks: {
                  label: function (tooltipItem, data) {
                    let empName = self4.empName1[tooltipItem.index];
                    return (empName + '(' + self4.getCurrencySymbol(self4.currency) + tooltipItem.xLabel + ')');
                  }
                }
              }
            }
            for (let i = 0; i < res.data.aggSpendDataByDateTypeUserBookingCount.length; i++) {
              // const [date , month ] = res.data.aggSpendDataByDateTypeUserBookingCount[i].label.split(",");
              this.barChartLabels2.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].label);
              this.empName2.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].employeeName);
              this.barChartData2[0].data.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].numberOfbooking)
              this.getColor()
            }
            let self = this;
            this.barChartOptions3 = {
              layout: this.layoutPaddingOption,
              scales: {
                xAxes: [{
                  scaleLabel: {
                    display: true,
                    labelString: 'Number of bookings',
                  },
                  ticks: {
                    beginAtZero: true,
                    precision: 0,

                  }
                }],
              },
              legend: {
                display: false,
                responsive: false
              },
              tooltips: {
                callbacks: {
                  label: function (tooltipItem, data) {
                    let empName = self.empName2[tooltipItem.index];
                    return (empName + '(' + tooltipItem.xLabel + ')');
                  }
                }
              }
            }
            for (let i = 0; i < res.data.aggData.FLIGHT.length; i++) {
              // const [date , month ] = res.data.aggSpendDataByDateTypeOther[i].label.split(",");
              this.barChartLabels3.push(res.data.aggData.FLIGHT[i].label);
              this.barChartData3[0].data.push(res.data.aggData.FLIGHT[i].numberOfbooking)
              this.barChartData3[0].label = 'Top Airlines';
              this.labelName.push(res.data.aggData.FLIGHT[i].airlineName);
              this.getColor();
              this.msg = 'Airlines';
            }
            let that = this;
            this.barChartOptions1 = {
              layout: this.layoutPaddingOption,
              scales: {
                xAxes: [{
                  scaleLabel: {
                    display: true,
                    labelString: 'Number of bookings',
                  },
                  ticks: {
                    beginAtZero: true,
                    precision: 0
                  }
                }],
              },
              legend: {
                display: false,
                responsive: false
              },
              tooltips: {
                callbacks: {
                  label: function (tooltipItem, data) {
                    let airline = that.labelName[tooltipItem.index];
                    return (airline + '(' + tooltipItem.xLabel + ')');
                  }
                }
              }
            }
            for (let i = 0; i < res.data.aggData.CAR.length; i++) {
              // const [date , month ] = res.data.aggSpendDataByDateTypeOther[i].label.split(",");
              this.barChartLabels4.push(res.data.aggData.CAR[i].label);
              this.barChartData4[0].data.push(res.data.aggData.CAR[i].numberOfbooking)
              this.barChartData4[0].label = 'Top car rental companies';
              this.labelName1.push(res.data.aggData.CAR[i].car);
              this.getColor();
              this.msg11 = 'Top car rental companies';
            }
            let that2 = this;
            this.barChartOptions4 = {
              layout: this.layoutPaddingOption,
              scales: {
                xAxes: [{
                  scaleLabel: {
                    display: true,
                    labelString: 'Number of bookings',
                  },
                  ticks: {
                    beginAtZero: true,
                    precision: 0
                  }
                }],
              },
              legend: {
                display: false,
                responsive: false
              },
              tooltips: {
                callbacks: {
                  label: function (tooltipItem, data) {
                    let airline = that2.labelName1[tooltipItem.index];
                    return (airline + '(' + tooltipItem.xLabel + ')');
                  }
                }
              }
            }
            for (let i = 0; i < res.data.aggData.HOTEL.length; i++) {
              // const [date , month ] = res.data.aggSpendDataByDateTypeOther[i].label.split(",");
              this.barChartLabels5.push(res.data.aggData.HOTEL[i].label);
              this.barChartData5[0].data.push(res.data.aggData.HOTEL[i].numberOfbooking)
              this.barChartData5[0].label = 'Top Hotel Chain';
              this.labelName2.push(res.data.aggData.HOTEL[i].chain);
              this.getColor();
              this.msg12 = 'Top Hotel Chain';
            }
            let that1 = this;
            this.barChartOptions5 = {
              layout: this.layoutPaddingOption,
              scales: {
                xAxes: [{
                  scaleLabel: {
                    display: true,
                    labelString: 'Number of bookings',
                  },
                  ticks: {
                    beginAtZero: true,
                    precision: 0
                  }
                }],
              },
              legend: {
                display: false,
                responsive: false
              },
              tooltips: {
                callbacks: {
                  label: function (tooltipItem, data) {
                    let airline = that1.labelName2[tooltipItem.index];
                    return (airline + '(' + tooltipItem.xLabel + ')');
                  }
                }
              }
            }
          }
        }
      } else if (this.bookingValue === 'FLIGHT' && (res.data.aggSpendDataByDateType && res.data.aggSpendDataByDateType.length > 0)) {
        if (res.data.weeklyNumbers && res.data.weeklyNumbers.length > 0) {
          for (let item of res.data.weeklyNumbers) {
            this.bookingTable = 'flights';
            let weektraveller = {};
            weektraveller['flightNUmber'] = item.flightTravellers;
            weektraveller['label'] = item.label;
            weektraveller['totalTraveller'] = item.totalTravellers;
            let average1 = '-';
            if (item.totalTravellers !== 0) {
              average1 = ((item.flightTravellers * 100) / item.totalTravellers).toFixed(2);
              average1 = average1 + '%';
            }
            weektraveller['perc'] = average1;
            this.weeklyTraveller.push(weektraveller);
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateType.length; i++) {
          //const [date , month ] = res.data.aggSpendDataByDateType[i].label.split(",");
          if (this.select === "DAYS") {
            let date1 = new Date(res.data.aggSpendDataByDateType[i].label);
            let datePipe = new DatePipe('en-US');
            let label1 = datePipe.transform(new Date(date1), 'MMM d, EEE');;
            this.barChartLabels.push(label1);
          } else {
            this.barChartLabels.push(res.data.aggSpendDataByDateType[i].label);
          }
          this.totalBooking = res.data.totalbookingFlight;
          this.totalSpend = res.data.totalSpendFlight;
          this.barChartData[0].data.push(res.data.aggSpendDataByDateType[i].totalSpend)
          this.barChartData[0].label = 'Air Spend';
          this.msg1 = 'Air Spend';
          this.averagemsg = 'per booking';
          if ((this.totalSpend === 0 && this.totalBooking === 0)) {
            this.doughnutChartData = [];
            this.resultErrorMessage = 'No data'
          }
          if (this.select !== "DAYS") {
            var range = res.data.aggSpendDataByDateType[i].startDate + ' - ' + res.data.aggSpendDataByDateType[i].endDate;
            this.dateRange.push(range);
          } else {
            this.dateRange.push(res.data.aggSpendDataByDateType[i].startDate);
          }
          this.msg1 = 'Air spend';
          if (res.data.totalbookingFlight > 0) {
            var average = (res.data.totalSpendFlight / res.data.totalbookingFlight);
            this.averagePrice = average;
          } else {
            this.averagePrice = 0;
          }
          this.travellers = res.data.travellerCount;
          this.pieChartData1 = [res.data.numberOfDisapprovals, res.data.numberOfApprovals, res.data.numberOfExpirations]
          this.totalApprove = res.data.numberOfDisapprovals + res.data.numberOfApprovals + res.data.numberOfExpirations;
          this.getColor()
        }
        let self4 = this;
        self4.pieChartOptions = {
          legend: {
            display: false,
            responsive: true
          },
          plugins: {
            maintainAspectRatio: true,
            datalabels: {
              formatter: (value, ctx) => {
                let percentage = Math.round(value) + "%";
                if (value > 0) {
                  return percentage;
                } else {
                  return '';
                }
              },
              color: '#000000',
            }
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let value = self4.pieChartData[tooltipItem.index];
                return (value + '%');
              }
            }
          }
        }
        self4.pieChartOptions1 = {
          legend: {
            display: false,
            responsive: true
          },
          plugins: {
            datalabels: {
              formatter: (value, ctx) => {
                let percentage = value;
                if (percentage > 0) {
                  return percentage;
                } else {
                  return '';
                }
              },
              color: '#000000',
            }
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let value = self4.pieChartData1[tooltipItem.index];
                let average = ((value * 100) / self4.totalApprove).toFixed(2);
                return (value + ' of ' + self4.totalApprove + ', ' + average + '%');
              }
            }
          }
        }
        //  let self2 = this;
        self4.barChartOptions = {
          layout: this.layoutPaddingOption,
          scales: {
            yAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Total Spend',
              },
              ticks: {
                beginAtZero: true,
                precision: 0,
                callback: function (label, index, labels) {
                  if (label >= 1000) {
                    return ((label / 1000).toFixed(1)) + 'k';
                  } else {
                    return label;
                  }
                }
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let range = self4.dateRange[tooltipItem.index];
                return (range + '(' + self4.getCurrencySymbol(self4.currency) + tooltipItem.value + ')');
              }
            }
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateTypeUserSpend.length; i++) {
          // const [date , month ] = res.data.aggSpendDataByDateTypeUserSpend[i].label.split(",");
          this.barChartLabels1.push(res.data.aggSpendDataByDateTypeUserSpend[i].label);
          this.empName1.push(res.data.aggSpendDataByDateTypeUserSpend[i].employeeName);
          this.barChartData1[0].data.push(res.data.aggSpendDataByDateTypeUserSpend[i].totalSpend)
          this.getColor()
        }
        this.compliance = res.data.compliancePerc.toFixed(2);
        var outsidePolicy = ((100 - this.compliance).toFixed(2));
        if (this.barChartData1[0].data.length > 0) {
          this.pieChartData = [outsidePolicy, this.compliance];
        } else {
          this.pieChartData = [];
        }
        let self1 = this;
        this.barChartOptions2 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Total Spend',
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let empName = self1.empName1[tooltipItem.index];
                return (empName + '(' + self1.getCurrencySymbol(self1.currency) + tooltipItem.xLabel + ')');
              }
            }
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateTypeUserBookingCount.length; i++) {
          // const [date , month ] = res.data.aggSpendDataByDateTypeUserBookingCount[i].label.split(",");
          this.barChartLabels2.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].label);
          this.empName2.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].employeeName);
          this.barChartData2[0].data.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].numberOfbooking)
          this.getColor()
        }
        let self = this;
        this.barChartOptions3 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Number of bookings',
              },
              ticks: {
                beginAtZero: true,
                precision: 0
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let empName = self.empName2[tooltipItem.index];
                return (empName + '(' + tooltipItem.xLabel + ')');
              }
            }
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateTypeOther.length; i++) {
          // const [date , month ] = res.data.aggSpendDataByDateTypeOther[i].label.split(",");
          this.barChartLabels3.push(res.data.aggSpendDataByDateTypeOther[i].label);
          this.barChartData3[0].data.push(res.data.aggSpendDataByDateTypeOther[i].numberOfbooking)
          this.barChartData3[0].label = 'Top Airlines';
          this.labelName.push(res.data.aggSpendDataByDateTypeOther[i].airlineName);
          this.getColor();
          this.msg = 'Airlines';
        }
        let that = this;
        this.barChartOptions1 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Number of bookings',
              },
              ticks: {
                beginAtZero: true,
                precision: 0
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let airline = that.labelName[tooltipItem.index];
                return (airline + '(' + tooltipItem.xLabel + ')');
              }
            }
          }
        }
      } else if (this.bookingValue === 'CAR' && (res.data.aggSpendDataByDateType && res.data.aggSpendDataByDateType.length > 0)) {
        if (res.data.weeklyNumbers && res.data.weeklyNumbers.length > 0) {
          for (let item of res.data.weeklyNumbers) {
            this.bookingTable = 'car rentals';
            let weektraveller = {};
            weektraveller['flightNUmber'] = item.carTravellers;
            weektraveller['label'] = item.label;
            weektraveller['totalTraveller'] = item.totalTravellers;
            let average1 = '-';
            if (item.totalTravellers !== 0) {
              average1 = ((item.carTravellers * 100) / item.totalTravellers).toFixed(2);
              average1 = average1 + '%';
            }
            weektraveller['perc'] = average1;
            this.weeklyTraveller.push(weektraveller);
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateType.length; i++) {
          //const [date , month ] = res.data.aggSpendDataByDateType[i].label.split(",");
          if (this.select === "DAYS") {
            let date1 = new Date(res.data.aggSpendDataByDateType[i].label);
            let datePipe = new DatePipe('en-US');
            let label1 = datePipe.transform(new Date(date1), 'MMM d, EEE');;
            this.barChartLabels.push(label1);
          } else {
            this.barChartLabels.push(res.data.aggSpendDataByDateType[i].label);
          }
          this.totalBooking = res.data.totalbookingCar;
          this.totalSpend = res.data.totalSpendCar;
          this.barChartData[0].data.push(res.data.aggSpendDataByDateType[i].totalSpend)
          this.barChartData[0].label = 'Car Spend';
          this.msg1 = 'Car Spend';
          this.averagemsg = 'per day';
          if ((this.totalSpend === 0 && this.totalBooking === 0)) {
            this.doughnutChartData = [];
            this.resultErrorMessage = 'No data'
          }
          if (this.select !== "DAYS") {
            var range = res.data.aggSpendDataByDateType[i].startDate + ' - ' + res.data.aggSpendDataByDateType[i].endDate;
            this.dateRange.push(range);
          } else {
            this.dateRange.push(res.data.aggSpendDataByDateType[i].startDate);
          }
          this.msg1 = 'Car spend';
          if (res.data.numberOfDayOrNight > 0) {
            var average = (res.data.totalSpendCar / res.data.numberOfDayOrNight);
            this.averagePrice = average;
          } else {
            this.averagePrice = 0;
          }
          this.travellers = res.data.travellerCount;

          this.pieChartData1 = [res.data.numberOfDisapprovals, res.data.numberOfApprovals, res.data.numberOfExpirations]
          this.totalApprove = res.data.numberOfDisapprovals + res.data.numberOfApprovals + res.data.numberOfExpirations;
          this.getColor();
        }
        let self4 = this;
        this.pieChartOptions = {
          legend: {
            display: false,
            responsive: true
          },
          plugins: {
            maintainAspectRatio: true,
            datalabels: {
              formatter: (value, ctx) => {
                let percentage = Math.round(value) + "%";
                if (value > 0) {
                  return percentage;
                } else {
                  return '';
                }
              },
              color: '#000000',
            }
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let value = self4.pieChartData[tooltipItem.index];
                return (value + '%');
              }
            }
          }
        }
        self4.pieChartOptions1 = {
          legend: {
            display: false,
            responsive: true
          },
          plugins: {
            datalabels: {
              formatter: (value, ctx) => {
                let percentage = value;
                if (percentage > 0) {
                  return percentage;
                } else {
                  return '';
                }
              },
              color: '#000000',
            }
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let value = self4.pieChartData1[tooltipItem.index];
                let average = ((value * 100) / self4.totalApprove).toFixed(2);
                return (value + ' of ' + self4.totalApprove + ', ' + average + '%');
              }
            }
          }
        }
        this.barChartOptions = {
          layout: this.layoutPaddingOption,
          scales: {
            yAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Total Spend',
              },
              ticks: {
                beginAtZero: true,
                precision: 0,
                callback: function (label, index, labels) {
                  if (label >= 1000) {
                    return ((label / 1000).toFixed(1)) + 'k';
                  } else {
                    return label;
                  }
                }
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let range = self4.dateRange[tooltipItem.index];
                return (range + '(' + self4.getCurrencySymbol(self4.currency) + tooltipItem.value + ')');
              }
            }
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateTypeUserSpend.length; i++) {
          // const [date , month ] = res.data.aggSpendDataByDateTypeUserSpend[i].label.split(",");
          this.barChartLabels1.push(res.data.aggSpendDataByDateTypeUserSpend[i].label);
          this.empName1.push(res.data.aggSpendDataByDateTypeUserSpend[i].employeeName);
          this.barChartData1[0].data.push(res.data.aggSpendDataByDateTypeUserSpend[i].totalSpend)
          this.getColor()
        }
        this.compliance = res.data.compliancePerc.toFixed(2);
        var outsidePolicy = ((100 - this.compliance).toFixed(2));
        if (this.barChartData1[0].data.length > 0) {
          this.pieChartData = [outsidePolicy, this.compliance];
        } else {
          this.pieChartData = [];
        }
        let self1 = this;
        this.barChartOptions2 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Total Spend',
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let empName = self1.empName1[tooltipItem.index];
                return (empName + '(' + self1.getCurrencySymbol(self1.currency) + tooltipItem.xLabel + ')');
              }
            }
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateTypeUserBookingCount.length; i++) {
          //  const [date , month ] = res.data.aggSpendDataByDateTypeUserBookingCount[i].label.split(",");
          this.barChartLabels2.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].label);
          this.empName2.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].employeeName);
          this.barChartData2[0].data.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].numberOfbooking)
          this.getColor()
        }
        let self = this;
        this.barChartOptions3 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Number of bookings',
              },
              ticks: {
                beginAtZero: true,
                precision: 0
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let empName = self.empName2[tooltipItem.index];
                return (empName + '(' + tooltipItem.xLabel + ')');
              }
            }
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateTypeOther.length; i++) {
          //  const [date , month ] = res.data.aggSpendDataByDateTypeOther[i].label.split(",");
          this.barChartLabels4.push(res.data.aggSpendDataByDateTypeOther[i].label);
          this.barChartData4[0].data.push(res.data.aggSpendDataByDateTypeOther[i].numberOfbooking)
          this.labelName.push(res.data.aggSpendDataByDateTypeOther[i].car);
          this.barChartData4[0].label = 'Top car rental companies';
          this.getColor();
          this.msg = 'Rental company';
        }
        let that = this;
        this.barChartOptions4 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Number of bookings',
              },
              ticks: {
                beginAtZero: true,
                precision: 0
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let airline = that.labelName[tooltipItem.index];
                return (airline + '(' + tooltipItem.xLabel + ')');
              }
            }
          }
        }
      } else if (this.bookingValue === 'HOTEL' && (res.data.aggSpendDataByDateType && res.data.aggSpendDataByDateType.length > 0)) {
        if (res.data.weeklyNumbers && res.data.weeklyNumbers.length > 0) {
          for (let item of res.data.weeklyNumbers) {
            this.bookingTable = 'hotels';
            let weektraveller = {};
            weektraveller['flightNUmber'] = item.hotelTravellers;
            weektraveller['label'] = item.label;
            weektraveller['totalTraveller'] = item.totalTravellers;
            let average1 = '-';
            if (item.totalTravellers !== 0) {
              average1 = ((item.hotelTravellers * 100) / item.totalTravellers).toFixed(2);
              average1 = average1 + '%';
            }
            weektraveller['perc'] = average1;
            this.weeklyTraveller.push(weektraveller);
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateType.length; i++) {
          // const [date , month ] = res.data.aggSpendDataByDateType[i].label.split(",");
          if (this.select === "DAYS") {
            let date1 = new Date(res.data.aggSpendDataByDateType[i].label);
            let datePipe = new DatePipe('en-US');
            let label1 = datePipe.transform(new Date(date1), 'MMM d, EEE');;
            this.barChartLabels.push(label1);
          } else {
            this.barChartLabels.push(res.data.aggSpendDataByDateType[i].label);
          }
          this.barChartData[0].data.push(res.data.aggSpendDataByDateType[i].totalSpend)
          this.barChartData[0].label = 'Hotel Spend';
          this.msg1 = 'Hotel Spend';
          this.averagemsg = 'per night';
          this.totalBooking = res.data.totalbookingHotel;
          this.totalSpend = res.data.totalSpendHotel;
          if ((this.totalSpend === 0 && this.totalBooking === 0)) {
            this.doughnutChartData = [];
            this.resultErrorMessage = 'No data'
          }
          if (this.select !== "DAYS") {
            var range = res.data.aggSpendDataByDateType[i].startDate + ' - ' + res.data.aggSpendDataByDateType[i].endDate;
            this.dateRange.push(range);
          } else {
            this.dateRange.push(res.data.aggSpendDataByDateType[i].startDate);
          }
          this.msg1 = 'Hotel spend';
          if (res.data.numberOfDayOrNight > 0) {
            var average = (res.data.totalSpendHotel / res.data.numberOfDayOrNight);
            this.averagePrice = average;
          } else {
            this.averagePrice = 0;
          }
          this.travellers = res.data.travellerCount;
          this.pieChartData1 = [res.data.numberOfDisapprovals, res.data.numberOfApprovals, res.data.numberOfExpirations]
          this.totalApprove = (res.data.numberOfDisapprovals + res.data.numberOfApprovals + res.data.numberOfExpirations);
          this.getColor()
        }
        let self4 = this;
        this.pieChartOptions = {
          legend: {
            display: false,
            responsive: true
          },
          plugins: {
            datalabels: {
              maintainAspectRatio: true,
              formatter: (value, ctx) => {
                let percentage = Math.round(value) + "%";
                if (value > 0) {
                  return percentage;
                } else {
                  return '';
                }
              },
              color: '#000000',
            }
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let value = self4.pieChartData[tooltipItem.index];
                return (value + '%');
              }
            }
          }
        }
        self4.pieChartOptions1 = {
          legend: {
            display: false,
            responsive: true
          },
          plugins: {
            datalabels: {
              formatter: (value, ctx) => {
                let percentage = value;
                if (percentage > 0) {
                  return percentage;
                } else {
                  return '';
                }
              },
              color: '#000000',
            }
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let value = self4.pieChartData1[tooltipItem.index];
                let average = ((value * 100) / self4.totalApprove).toFixed(2);
                return (value + ' of ' + self4.totalApprove + ', ' + average + '%');
              }
            }
          }
        }

        this.barChartOptions = {
          layout: this.layoutPaddingOption,
          scales: {
            yAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Total Spend',
              },
              ticks: {
                beginAtZero: true,
                precision: 0,
                callback: function (label, index, labels) {
                  if (label >= 1000) {
                    return ((label / 1000).toFixed(1)) + 'k';
                  } else {
                    return label;
                  }
                }
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let range = self4.dateRange[tooltipItem.index];
                return (range + '(' + self4.getCurrencySymbol(self4.currency) + tooltipItem.value + ')');
              }
            }
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateTypeUserSpend.length; i++) {
          // const [date , month ] = res.data.aggSpendDataByDateTypeUserSpend[i].label.split(",");
          this.barChartLabels1.push(res.data.aggSpendDataByDateTypeUserSpend[i].label);
          this.empName1.push(res.data.aggSpendDataByDateTypeUserSpend[i].employeeName);
          this.barChartData1[0].data.push(res.data.aggSpendDataByDateTypeUserSpend[i].totalSpend)
          this.getColor()
        }
        this.compliance = res.data.compliancePerc.toFixed(2);
        var outsidePolicy = ((100 - this.compliance).toFixed(2));
        if (this.barChartData1[0].data.length > 0) {
          this.pieChartData = [outsidePolicy, this.compliance];
        } else {
          this.pieChartData = [];
        }
        let self1 = this;
        this.barChartOptions2 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Total Spend',
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let empName = self1.empName1[tooltipItem.index];
                return (empName + '(' + self1.getCurrencySymbol(self1.currency) + tooltipItem.xLabel + ')');
              }
            }
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateTypeUserBookingCount.length; i++) {
          //const [date , month ] = res.data.aggSpendDataByDateTypeUserBookingCount[i].label.split(",");
          this.barChartLabels2.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].label);
          this.empName2.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].employeeName);
          this.barChartData2[0].data.push(res.data.aggSpendDataByDateTypeUserBookingCount[i].numberOfbooking)
          this.getColor()
        }
        let self = this;
        this.barChartOptions3 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Number of bookings',
              },
              ticks: {
                beginAtZero: true,
                precision: 0
              }
            }],
          }, legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let empName = self.empName2[tooltipItem.index];
                return (empName + '(' + tooltipItem.xLabel + ')');
              }
            }
          }
        }
        for (let i = 0; i < res.data.aggSpendDataByDateTypeOther.length; i++) {
          // const [date , month ] = res.data.aggSpendDataByDateTypeOther[i].label.split(",");
          this.barChartLabels5.push(res.data.aggSpendDataByDateTypeOther[i].label);
          this.barChartData5[0].data.push(res.data.aggSpendDataByDateTypeOther[i].numberOfbooking)
          this.labelName.push(res.data.aggSpendDataByDateTypeOther[i].chain);
          this.barChartData5[0].label = 'Top Hotel Chain';
          this.getColor();
          this.msg = 'Hotel chain';
        }
        let that = this;
        this.barChartOptions5 = {
          layout: this.layoutPaddingOption,
          scales: {
            xAxes: [{
              scaleLabel: {
                display: true,
                labelString: 'Number of bookings',
              }, ticks: {
                beginAtZero: true,
                precision: 0
              }
            }],
          },
          legend: {
            display: false,
            responsive: false
          },
          tooltips: {
            callbacks: {
              label: function (tooltipItem, data) {
                let airline = that.labelName[tooltipItem.index];
                return (airline + '(' + tooltipItem.xLabel + ')');
              }
            }
          }
        }
      } else {
        this.applyBtn = false;
        this.resultErrorMessage = 'No data'
      }
    } else {
      this.applyBtn = false;
      this.resultErrorMessage = 'No data'
    }
  }
  getColor() {
    for (let i = 0; i < this.barChartData[0].data.length; i++) {
      this.chartColors[0].backgroundColor.push('rgba(255,192,203 ,1 )');
      this.chartColors[0].borderColor.push('rgba(255,192,203 ,1 )');
      this.chartColors1[0].backgroundColor.push('rgba(137, 196, 244, 1)');
      this.chartColors1[0].borderColor.push('rgba(137, 196, 244, 1)');
      this.chartColors2[0].backgroundColor.push('rgba(78, 205, 196, 1)');
      this.chartColors2[0].borderColor.push('rgba(78, 205, 196, 1)');
      this.chartColors3[0].backgroundColor.push('rgba(137, 196, 244, 1)');
      this.chartColors3[0].borderColor.push('rgba(137, 196, 244, 1)');
      this.chartColors21[0].backgroundColor.push('#A76B3D');
      this.chartColors22[0].backgroundColor.push('#266666');
      this.chartColors21[0].borderColor.push('#A76B3D');
      this.chartColors22[0].borderColor.push('#266666');

      this.chartColors11[0].backgroundColor.push('#127DBE');
      this.chartColors31[0].backgroundColor.push('#FB6786');
      this.chartColors32[0].backgroundColor.push('#F99D53');
      this.chartColors33[0].backgroundColor.push('#43A4E5');
      this.chartColors11[0].borderColor.push('#127DBE');
      this.chartColors31[0].borderColor.push('#FB6786');
      this.chartColors32[0].borderColor.push('#F99D53');
      this.chartColors33[0].borderColor.push('#43A4E5');
    }
  }
  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }
  resetValue() {
    this.applyBtn = true;
    this.labelName = [];
    this.labelName1 = [];
    this.labelName2 = [];
    this.empName1 = [];
    this.empName2 = [];
    this.dateRange = [];
    this.resultErrorMessage = 'Fetching data'
    this.chartColors1 = [{ backgroundColor: [], borderColor: [], borderWidth: 0.5, }];
    this.chartColors2 = [{ backgroundColor: [], borderColor: [], borderWidth: 0.5, }];
    this.chartColors3 = [{ backgroundColor: [], borderColor: [], borderWidth: 0.5, }];
    this.barChartData = [{ options: this.layoutPaddingOptionWrapper, data: [], label: '', datalabels: this.datalabelConfig }];
    this.barChartData4 = [{ options: this.layoutPaddingOptionWrapper, data: [], label: '', datalabels: this.verticalLabelConfig },];
    this.barChartData5 = [{ options: this.layoutPaddingOptionWrapper, data: [], label: '', datalabels: this.verticalLabelConfig },];
    this.barChartData1 = [{ options: this.layoutPaddingOptionWrapper, data: [], label: 'Top employees by $ spend', datalabels: this.verticalLabelConfig },];
    this.barChartData2 = [{ options: this.layoutPaddingOptionWrapper, data: [], label: 'Top employees by # bookings', datalabels: this.verticalLabelConfig },];
    this.barChartData3 = [{ options: this.layoutPaddingOptionWrapper, data: [], label: '', datalabels: this.verticalLabelConfig },];
    this.barChartLabels = [];
    this.barChartLabels1 = [];
    this.barChartLabels2 = [];
    this.barChartLabels3 = [];
    this.barChartLabels4 = [];
    this.barChartLabels5 = [];
  }
  setStartDate(date) {
    //
    if (date) {
      // this.daterangepickerModel =date;
      this.startDate = date[0];
      this.endDate = date[1];
      if (this.startDate > this.endDate) {
        this.endDate = date;
      }
      this.getChart();
    }
  }
  getSelectString(item) {
    this.select = item;
    this.default = false;
    let startDate = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
    let endDate = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);
    let searchDates = startDate + 'T' + endDate;
    this.resetValue();
    if (this.default) {
      this.setDefaultGrouping();
    }
    if ((this.adminPanelService.previousDate === searchDates && this.adminPanelService.previousDept === this.deptValue) && (this.adminPanelService.previousBookingType === this.bookingValue)) {
      let currentDataResponse = this.adminPanelService.origDashBoardResponse;
      // currentDataResponse = this.adminPanelService.origDashBoardResponse;
      for (let key in this.adminPanelService.origDashBoardResponse.data.aggDateTypeData) {
        if (key === this.select) {
          currentDataResponse.data.aggSpendDataByDateType = this.adminPanelService.origDashBoardResponse.data.aggDateTypeData[key];
          break;
        }
      }
      this.getGraphData(currentDataResponse);
    } else {
      this.getChart();
    }
  }
  showDateChanged(id) {
    if (id === 'CURRMONTH') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(1);
      this.startDate.setMonth(this.startDate.getMonth());
      this.disableDate = true;
      this.disableDate1 = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'Last 12 months') {
      this.startDate = new Date();
      this.endDate = new Date();
      for (var i = 0; i < 12; i++) {
        this.startDate.setMonth(this.startDate.getMonth() - 1);
      }
      this.disableDate = true;
      this.disableDate1 = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
    }
    else if (id === 'Currentquarter') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.getQuarter(this.startDate);
      this.disableDate = true;
      this.disableDate1 = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'Currentyear') {
      this.endDate = new Date();
      this.startDate = new Date();
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 0, 1);
      this.disableDate = true;
      this.disableDate1 = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
    }
    else if (id === 'LAST7DAYS') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 7);
      this.disableDate = true;
      this.disableDate1 = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'LAST30DAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 30);
      this.disableDate = true;
      this.disableDate1 = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'LAST90DAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 90);
      this.disableDate = true;
      this.disableDate1 = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'LASTMONTH') {
      this.startDate = new Date();
      this.startDate.setDate(1);
      this.startDate.setMonth(this.startDate.getMonth() - 1);
      this.endDate = new Date(this.startDate.getFullYear(), this.startDate.getMonth() + 1, 0);
      this.disableDate = true;
      this.disableDate1 = true;
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'CUSTOMDATE') {
      this.disableDate = false;
      this.disableDate1 = false;

    }
    this.ngxSmartModalService.close('daterangeSelection');
    this.viewMode1 = 'tab11';
    // this.getChart();
  }
  showDepartmentChanged($event) {
    this.getChart();

  }
  showBookingTypeChanged($event) {
    this.getChart();
  }
  getDepartments() {
    this.departmentOptions = [{ value: 'All Departments', id: 'all' }];
    if (this.companySettings && this.companySettings.departments) {
      let apiDepartmentList: Array<Department> = this.companySettings.departments;
      for (let department of apiDepartmentList) {
        this.departmentOptions.push({ value: department.name, id: '' + department.departmentId });
      }
    }
  }
  getPlugins() {
    return ChartDataLabels;
  }
  formatterFn(value, context) {
    if (context && context.dataset && context.dataset.data && context.dataset.data.length <= 62) {
      let retVal = value;
      if (value > 1000000) {
        retVal = Math.round(value / 1000000) + 'M';
      } else if (value > 1000) {
        retVal = Math.round(value / 1000) + 'K';
      } else {
        retVal = Math.round(value);
      }
      if (retVal === 0) {
        return '';
      }
      return retVal;
    } else {
      return '';
    }
  }

}
