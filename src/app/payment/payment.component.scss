@import "../../variables.scss";

.StripeElement {
  background-color: white;
  height: 40px;
  padding: 10px 12px;
  border-radius: 4px;

  border: 1px solid transparent;
  box-shadow: 0 1px 3px 0 #e6ebf1;
  -webkit-transition: box-shadow 150ms ease;
  transition: box-shadow 150ms ease;
}

.StripeElement--focus {
  box-shadow: 0 1px 3px 0 #cfd7df;
}

.StripeElement--invalid {
  border-color: #fa755a;
}

.StripeElement--webkit-autofill {
  background-color: #fefde5 !important;
}

.card {
  width: 94%;
  background-color: #EEEDEB;
  box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
  margin: 20px auto 0;

  .payment-header {
    margin-top: 16px;

    .heading {
      color: $primary-text-color;
      font-size: 16px;
      letter-spacing: -0.71px;
      line-height: 26px;
      margin: 16px;
    }

    .price {
      background: $primary-color;
      color: #fff;
      padding: 6px 12px;
      margin-right: 8px;
      font-size: 16px;
    }

    .icon-customer {
      font-size: 14px;
      margin-right: 3px;
    }

    .passengers {
      font-size: 16px;
      letter-spacing: -0.87px;
    }
  }

  .form {
    margin: 10px;

    .content-wrapper {
      height: autoss;
      width: 100%;
      margin-bottom: 16px;

      input {
        padding-left: 16px;
      }

      .name-card {
        height: 40px;
        width: 100%;
        border: none;
        outline: none;
      }
    }

    .field {
      margin-top: 18px;
    }
  }

  .error-color {
    color: $danger;
  }

  .card-number-wrapper {
    position: relative;

    .card-icon {
      position: absolute;
      top: 13px;
      left: 13px;
      font-size: 16px;
      color: $primary-color;
    }
  }

  #card-number {
    padding-left: 46px;
  }
}

.button-wrapper {
  padding: 0px;
  width: 100%;
  text-align: center;
  margin-top: 24px;

  button {
    width: 321px;
    border-radius: 5px;
    background-color: var(--button-bg-color) !important;
    border: none;
    color: $link-color;
    padding: 10px 0 10px;
    font-size: 12px;
    font-weight: bold;
    letter-spacing: 1px;
    line-height: 18px;
    text-align: center;
  }

}


@media (min-width : 575.99px) {
  .card {
    width: 595px;
    padding: 60px;

    .payment-header {
      .heading {
        margin-left: 0px;
      }
    }

    .form {
      width: 100%;
      margin: 18px auto;
    }

    .button-wrapper {
      margin-top: 17px;

      button {
        width: 98.5%;
        border-radius: 5px;
        cursor: pointer;
      }
    }

  }

}