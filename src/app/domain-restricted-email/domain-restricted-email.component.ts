import { Component, OnInit } from '@angular/core';
import { AdminPanelService, CompanyDomain } from '../admin-panel.service';
import { UserAccountService } from '../user-account.service';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Constants } from '../util/constants';

@Component({
    selector: 'app-domain-restricted-email',
    templateUrl: './domain-restricted-email.component.html',
    styleUrls: ['./domain-restricted-email.component.scss'],
    standalone: false
})
export class DomainRestrictedEmailComponent implements OnInit {

  domainOptions = [];
  selectedOption = [];
  emailForm: UntypedFormGroup;
  constructor(private adminPanelService: AdminPanelService,
    private userAccountService: UserAccountService,
    private fb: UntypedFormBuilder,
    public router: Router,) { }

  ngOnInit() {
    let apiDomainList: Array<CompanyDomain> = this.adminPanelService.getDomains();
    this.selectedOption.push({ value: apiDomainList[0].domain });
    for (let domainItem of apiDomainList) {
      this.domainOptions.push({ value: domainItem.domain, id: '' + domainItem.domainId });
    }

    this.emailForm = this.fb.group({
      emailUserName: ['', Validators.compose([Validators.required])],
      domain: ['', Validators.compose([Validators.required])],
      email: ['', Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_EMAIL)])],
    });
  }

  emailUserNameChange(userName) {
    let domain = this.emailForm.controls['domain'].value;
    this.emailForm.controls['email'].setValue(userName.trim() + '@' + domain);
  }
  emailDomainChange(domain) {
    let userName = this.emailForm.controls['emailUserName'].value;
    this.emailForm.controls['email'].setValue(userName.trim() + '@' + domain.value);
  }

}
