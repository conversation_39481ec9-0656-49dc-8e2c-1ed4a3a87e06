import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { HotelHistoryModalComponent } from './hotel-history-modal.component';

describe('HotelHistoryModalComponent', () => {
  let component: HotelHistoryModalComponent;
  let fixture: ComponentFixture<HotelHistoryModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [HotelHistoryModalComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HotelHistoryModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
