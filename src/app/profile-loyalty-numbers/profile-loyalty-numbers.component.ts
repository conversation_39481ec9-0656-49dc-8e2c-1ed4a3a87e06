import { Component, OnInit } from '@angular/core';
import { ALL_AIRLINES, ALL_TRAINLINES } from '../util/airlines';
import { UntypedFormGroup, UntypedFormBuilder, UntypedFormArray, Validators, UntypedFormControl } from '@angular/forms';
import { Constants } from '../util/constants';
import { FrequentFlyerInfo } from '../entity/frequent-flyer-info';
import { UserAccountService } from '../user-account.service';
import { Subscription } from 'rxjs';
import { ToastrService, ActiveToast } from 'ngx-toastr';
import { UserInfo } from '../entity/user-info';
import { UserAccountInfo } from '../entity/user-account-info';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { ALL_HOTELCHAINS } from '../util/hotel-chains';
import { ALL_CARBRANDS } from '../util/car-brands';
import { _ } from 'src/app/util/title';
import { TranslateService } from "@ngx-translate/core";

@Component({
    selector: 'app-profile-loyalty-numbers',
    templateUrl: './profile-loyalty-numbers.component.html',
    styleUrls: ['./profile-loyalty-numbers.component.scss'],
    standalone: false
})
export class ProfileLoyaltyNumbersComponent implements OnInit {

  all_airlines = [];
  all_trainLines = [];
  trainLineFound=false;
  all_carbrands = [];
  frequentFlyerForm: UntypedFormGroup;
  frequentFlyerFormArray: UntypedFormArray;
  trainLoyalityForm: UntypedFormGroup;
  trainLoyalityFormArray: UntypedFormArray;
  duplicateEntryStatus: boolean = false;
  duplicateHotelEntryStatus: boolean = false;
  duplicateCarStatus: boolean = false;
  duplicateTrainEntryStatus = false;
  all_hotelChains = [];
  frequentGuestForm: UntypedFormGroup;
  frequentGuestFormArray: UntypedFormArray;
  frequentCarLoyalityForm: UntypedFormGroup;
  frequentCarLoyalityFormArray: UntypedFormArray;
  fetchAccountInfoSubscription: Subscription;
  activeToast: ActiveToast<any>;
  isRequestInProgress: boolean;
  constructor(private fb: UntypedFormBuilder,
    private userAccountInfoService: UserAccountService,
    public translateService: TranslateService,
    private toastr: ToastrService,) {
    this.all_airlines = ALL_AIRLINES;
    this.all_trainLines = ALL_TRAINLINES;
    this.all_hotelChains = ALL_HOTELCHAINS;
    this.all_carbrands = ALL_CARBRANDS;
    this.initForms();

  }

  private initForms() {
    this.frequentFlyerFormArray = this.fb.array([]);
    this.frequentFlyerForm = this.fb.group(
      {
        frequentFlyerFormArray: this.frequentFlyerFormArray
      }, { validator: '' });

      this.trainLoyalityFormArray = this.fb.array([]);
      this.trainLoyalityForm = this.fb.group(
        {
          trainLoyalityFormArray: this.trainLoyalityFormArray
        }, { validator: '' });
  
    this.frequentGuestFormArray = this.fb.array([]);
    this.frequentGuestForm = this.fb.group(
      {
        frequentGuestFormArray: this.frequentGuestFormArray
      }, { validator: '' });
    this.
      frequentCarLoyalityFormArray = this.fb.array([]);
    this.frequentCarLoyalityForm = this.fb.group(
      {
        frequentCarLoyalityFormArray: this.frequentCarLoyalityFormArray
      }, { validator: '' });
  }

  ngOnInit() {
    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      let userAccountInfo = this.userAccountInfoService.getAccountInfo();
      this.initForms();
      if (userAccountInfo && userAccountInfo.userInfo && userAccountInfo.userInfo.ffnMapping) {
        for (let ffnMap of userAccountInfo.userInfo.ffnMapping) {
          if(ffnMap.airline_code!=='9F' && ffnMap.airline_code!=='2V'){
          this.addFrequentFlyerEntry(ffnMap);
          }
        else if(ffnMap.airline_code==='9F' || ffnMap.airline_code==='2V'){
          this.addTrainLineLoyaltyEntry(ffnMap);
        }
      }
      }
      this.addFrequentFlyerEntry(undefined);
      this.addTrainLineLoyaltyEntry(undefined);
      if (userAccountInfo && userAccountInfo.userInfo && userAccountInfo.userInfo.loyalityCards) {
        for (let glnMap of userAccountInfo.userInfo.loyalityCards) {
          this.addFrequentGuestEntry(glnMap);
        }
      }
      this.addFrequentGuestEntry(undefined);
      if (userAccountInfo && userAccountInfo.userInfo && userAccountInfo.userInfo.carLoyaltyNumbers) {
        for (let clnMap of userAccountInfo.userInfo.carLoyaltyNumbers) {
          this.addCarLoyalityEntry(clnMap);
        }
      }
      this.addCarLoyalityEntry(undefined);
    });
  }
  addTrainLineLoyaltyEntry(ffnAirlineMap: FrequentFlyerInfo){
    let newFFNForm = this.fb.group({
      name: [ffnAirlineMap ? ffnAirlineMap.name : '', null],
      airline: [ffnAirlineMap ? ffnAirlineMap.airline_code : '', null],
      flyerNumber: [ffnAirlineMap ? ffnAirlineMap.frequent_flyer_number : '', null],
    });
    this.trainLoyalityFormArray.push(newFFNForm);
    
    if (ffnAirlineMap && ffnAirlineMap.airline_code && ffnAirlineMap.airline_code.trim() !== ''){
      (<UntypedFormGroup>this.trainLoyalityFormArray.controls[this.trainLoyalityFormArray.controls.length - 1]).controls['flyerNumber'].setValidators(
        Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
    }
  }
  addFrequentFlyerEntry(ffnAirlineMap: FrequentFlyerInfo) {
    let newFFNForm = this.fb.group({
      name: [ffnAirlineMap ? ffnAirlineMap.name : '', null],
      airline: [ffnAirlineMap ? ffnAirlineMap.airline_code : '', null],
      flyerNumber: [ffnAirlineMap ? ffnAirlineMap.frequent_flyer_number : '', null],
    });
    this.frequentFlyerFormArray.push(newFFNForm);
    if (ffnAirlineMap && ffnAirlineMap.airline_code && ffnAirlineMap.airline_code.trim() !== ''){
      (<UntypedFormGroup>this.frequentFlyerFormArray.controls[this.frequentFlyerFormArray.controls.length - 1]).controls['flyerNumber'].setValidators(
        Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
    }
  }
  addCarLoyalityEntry(clnMap: any) {
    let newCLNForm = this.fb.group({
      name: [clnMap ? clnMap.rentalCarName : '', null],
      loyalityNumber: [clnMap ? clnMap.rentalCarLoyaltyNumber : '', null],
      carCode: [clnMap ? clnMap.rentalCarCode : '', null]
    });
    this.frequentCarLoyalityFormArray.push(newCLNForm);
    if (clnMap && clnMap.airline_code && clnMap.airline_code.trim() !== '')  {
      (<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[this.frequentCarLoyalityFormArray.controls.length - 1]).controls['loyalityNumber'].setValidators(
        Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
    }
  }

  addFrequentGuestEntry(glnHotelChainMap: any) {
    if(glnHotelChainMap && glnHotelChainMap.chain){
    let findndex = this.all_hotelChains.findIndex(item => item.chainCode ===glnHotelChainMap.chain);
    if(findndex===-1){
      let tempHotel ={
        lowerName: glnHotelChainMap.hotel_name,
        key: "Hotel",
        value: glnHotelChainMap.hotel_name,
        chainCode: glnHotelChainMap.chain,
        chainName: glnHotelChainMap.hotel_name
      }
      this.all_hotelChains.push(tempHotel);
    }
  }
    let newGLNForm = this.fb.group({
      name: [glnHotelChainMap ? glnHotelChainMap.hotel_name : '', null],
      hotelChain: [glnHotelChainMap ? glnHotelChainMap.chain : '', null],
      guestNumber: [glnHotelChainMap ? glnHotelChainMap.number : '', null],
    });
    this.frequentGuestFormArray.push(newGLNForm);
    if (glnHotelChainMap && glnHotelChainMap.airline_code && glnHotelChainMap.airline_code.trim() !== ''){
      (<UntypedFormGroup>this.frequentGuestFormArray.controls[this.frequentGuestFormArray.controls.length - 1]).controls['guestNumber'].setValidators(
        Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
    }
  }

  removeAirline(i: number, val) {
    this.frequentFlyerFormArray.removeAt(i);
    for (var counter = 0; counter < this.frequentFlyerFormArray.controls.length; counter++) {
      if (this.isDuplicateEntry(counter)) {
        this.duplicateEntryStatus = true;
        return;
      }
    }
    this.duplicateEntryStatus = false;
  }

  removeTrainline(i: number, val) {
    this.trainLoyalityFormArray.removeAt(i);
    for (var counter = 0; counter < this.trainLoyalityFormArray.controls.length; counter++) {
      if (this.isDuplicateTarinlineEntry(counter)) {
        this.duplicateTrainEntryStatus = true;
       true;
        return;
      }
    }
    this.duplicateTrainEntryStatus  = false;
  }

  removeHotelChain(i: number) {
    this.frequentGuestFormArray.removeAt(i);
    for (var counter = 0; counter < this.frequentGuestFormArray.controls.length; counter++) {
      if (this.isDuplicateHotelChainEntry(counter)) {
        this.duplicateHotelEntryStatus = true;
        return;
      }
    }
    this.duplicateHotelEntryStatus = false;
  }
  removeCarLoyalityNumber(i: number) {
    this.frequentCarLoyalityFormArray.removeAt(i);
    for (var counter = 0; counter < this.frequentCarLoyalityFormArray.controls.length; counter++) {
      if (this.isDuplicateCarLoyalityEntry(counter)) {
        this.duplicateCarStatus = true;
        return;
      }
    }
    this.duplicateCarStatus = false;
  }
  isEmptyHGNumber(index: number) {
    if ((<UntypedFormGroup>this.frequentGuestFormArray.controls[index]).controls.hotelChain.value === '') {
      return false;
    }
    if ((<UntypedFormGroup>this.frequentGuestFormArray.controls[index]).controls['guestNumber'].value === '') {
      return true;
    }
    return false;
  }
  isEmptyCLNumber(index: number) {
    if ((<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[index]).controls.carCode.value === '') {
      return false;
    }
    if ((<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[index]).controls['loyalityNumber'].value === '') {
      return true;
    }
    return false;
  }
  isEmptyFFNumber(index: number) {
    if ((<UntypedFormGroup>this.frequentFlyerFormArray.controls[index]).controls.airline.value === '') {
      return false;
    }
    if ((<UntypedFormGroup>this.frequentFlyerFormArray.controls[index]).controls['flyerNumber'].value === '') {
      return true;
    }
    return false;
  }
  isEmptyTrainlineumber(index: number) {
    if ((<UntypedFormGroup>this.trainLoyalityFormArray.controls[index]).controls.airline.value === '') {
      return false;
    }
    if ((<UntypedFormGroup>this.trainLoyalityFormArray.controls[index]).controls['flyerNumber'].value === '') {
      return true;
    }
    return false;
  }
  isDuplicateHotelChainEntry(index: number) {
    if (index && this.frequentGuestFormArray
      && this.frequentGuestFormArray.controls
      && (<UntypedFormGroup>this.frequentGuestFormArray.controls[index]).controls) {
      if ((<UntypedFormGroup>this.frequentGuestFormArray.controls[index]).controls['hotelChain'].value !== '') {
        let val: string = (<UntypedFormGroup>this.frequentGuestFormArray.controls[index]).controls['hotelChain'].value;
        for (var counter = 0; counter < this.frequentGuestFormArray.controls.length - 1; counter++) {
          if (counter !== index) {
            if (val === (<UntypedFormGroup>this.frequentGuestFormArray.controls[counter]).controls['hotelChain'].value) {
              this.duplicateHotelEntryStatus = true;
              return true;
            }
          }
        }
      }
    }
    return false;
  }
  isDuplicateCarLoyalityEntry(index: number) {
    if (index && this.frequentCarLoyalityFormArray
      && this.frequentCarLoyalityFormArray.controls
      && (<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[index]).controls) {
      if ((<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[index]).controls['carCode'].value !== '') {
        let val: string = (<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[index]).controls['carCode'].value;
        for (var counter = 0; counter < this.frequentCarLoyalityFormArray.controls.length - 1; counter++) {
          if (counter !== index) {
            if (val === (<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[counter]).controls['carCode'].value) {
              this.duplicateCarStatus = true;
              return true;
            }
          }
        }
      }
    }
    return false;
  }
  isDuplicateEntry(index: number) {
    if (index && this.frequentFlyerFormArray
      && this.frequentFlyerFormArray.controls
      && (<UntypedFormGroup>this.frequentFlyerFormArray.controls[index]).controls) {
      if ((<UntypedFormGroup>this.frequentFlyerFormArray.controls[index]).controls['airline'].value !== '') {
        let val: string = (<UntypedFormGroup>this.frequentFlyerFormArray.controls[index]).controls['airline'].value;
        for (var counter = 0; counter < this.frequentFlyerFormArray.controls.length - 1; counter++) {
          if (counter !== index) {
            if (val === (<UntypedFormGroup>this.frequentFlyerFormArray.controls[counter]).controls['airline'].value) {
              this.duplicateTrainEntryStatus = true;
              return true;
            }
          }
        }
      }
    }
    return false;
  }
  isDuplicateTarinlineEntry(index: number) {
    if (index && this.trainLoyalityFormArray
      && this.trainLoyalityFormArray.controls
      && (<UntypedFormGroup>this.trainLoyalityFormArray.controls[index]).controls) {
      if ((<UntypedFormGroup>this.trainLoyalityFormArray.controls[index]).controls['airline'].value !== '') {
        let val: string = (<UntypedFormGroup>this.trainLoyalityFormArray.controls[index]).controls['airline'].value;
        for (var counter = 0; counter < this.trainLoyalityFormArray.controls.length - 1; counter++) {
          if (counter !== index) {
            if (val === (<UntypedFormGroup>this.trainLoyalityFormArray.controls[counter]).controls['airline'].value) {
              this.duplicateTrainEntryStatus = true;
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  onAirlineChange(i: number) {
    this.duplicateEntryStatus = false;
    if ((<UntypedFormGroup>this.frequentFlyerFormArray.controls[i]).controls['airline'].value === '') {
      (<UntypedFormGroup>this.frequentFlyerFormArray.controls[i]).controls['flyerNumber'].setValidators(null);
      (<UntypedFormGroup>this.frequentFlyerFormArray.controls[i]).controls['flyerNumber'].setErrors(null);
      (<UntypedFormGroup>this.frequentFlyerFormArray.controls[i]).controls['flyerNumber'].setValue('');
    } else {
      let airlineCode: string = (<UntypedFormGroup>this.frequentFlyerFormArray.controls[i]).controls['airline'].value;
      let selectedObject = ALL_AIRLINES.find(item => item.id == airlineCode);
      (<UntypedFormGroup>this.frequentFlyerFormArray.controls[i]).controls['name'].setValue(selectedObject.value);
       (<UntypedFormGroup>this.frequentFlyerFormArray.controls[i]).controls['flyerNumber'].setValidators(
        Validators.compose([Validators.required,Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
      if (i === this.frequentFlyerFormArray.controls.length - 1) {
        this.addFrequentFlyerEntry(undefined);
      }
    }
  }

  onTrainChange(i: number) {
    this.duplicateEntryStatus = false;
    if ((<UntypedFormGroup>this.trainLoyalityFormArray.controls[i]).controls['airline'].value === '') {
      (<UntypedFormGroup>this.trainLoyalityFormArray.controls[i]).controls['flyerNumber'].setValidators(null);
      (<UntypedFormGroup>this.trainLoyalityFormArray.controls[i]).controls['flyerNumber'].setErrors(null);
      (<UntypedFormGroup>this.trainLoyalityFormArray.controls[i]).controls['flyerNumber'].setValue('');
    } else {
      let airlineCode: string = (<UntypedFormGroup>this.trainLoyalityFormArray.controls[i]).controls['airline'].value;
      let selectedObject = ALL_TRAINLINES.find(item => item.id == airlineCode);
      (<UntypedFormGroup>this.trainLoyalityFormArray.controls[i]).controls['name'].setValue(selectedObject.value);
       (<UntypedFormGroup>this.trainLoyalityFormArray.controls[i]).controls['flyerNumber'].setValidators(
        Validators.compose([Validators.required,Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
      if (i === this.trainLoyalityFormArray.controls.length - 1) {
        this.addTrainLineLoyaltyEntry(undefined);
      }
    }
  }

  onHotelChainChange(i: number) {
    this.duplicateHotelEntryStatus = false;
    if ((<UntypedFormGroup>this.frequentGuestFormArray.controls[i]).controls['hotelChain'].value === '') {
      (<UntypedFormGroup>this.frequentGuestFormArray.controls[i]).controls['guestNumber'].setValidators(null);
      (<UntypedFormGroup>this.frequentGuestFormArray.controls[i]).controls['guestNumber'].setErrors(null);
      (<UntypedFormGroup>this.frequentGuestFormArray.controls[i]).controls['guestNumber'].setValue('');
    } else {
      let hotelChainCode = (<UntypedFormGroup>this.frequentGuestFormArray.controls[i]).controls['hotelChain'].value;
      let selectedHotelObject = ALL_HOTELCHAINS.find(item => item.chainCode == hotelChainCode);
      (<UntypedFormGroup>this.frequentGuestFormArray.controls[i]).controls['name'].setValue(selectedHotelObject.value);
      (<UntypedFormGroup>this.frequentGuestFormArray.controls[i]).controls['guestNumber'].setValidators(
        Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
      if (i === this.frequentGuestFormArray.controls.length - 1) {
        this.addFrequentGuestEntry(undefined);
      }
    }
  }

  private getUpdatedFrequentFlyerData(): UserInfo {
    let p = this.frequentFlyerForm.value;
    let p1 = this.trainLoyalityForm.value;
    let userInfoDTO = this.userAccountInfoService.getAccountInfo().userInfo;
    // p.frequentFlyerFormArray.splice(-1,1);
    let ffnMapping = Array<FrequentFlyerInfo>();
    for (let formFfn of p.frequentFlyerFormArray) {
      if (formFfn.airline === '' || !formFfn.airline) continue;
      let ffnMap = new FrequentFlyerInfo();
      ffnMap.name = formFfn.name;
      ffnMap.airline_code = formFfn.airline;
      ffnMap.frequent_flyer_number = formFfn.flyerNumber;
      ffnMapping.push(ffnMap);
    }
    for (let formFfn of p1.trainLoyalityFormArray) {
      if (formFfn.airline === '' || !formFfn.airline) continue;
      let ffnMap = new FrequentFlyerInfo();
      ffnMap.name = formFfn.name;
      ffnMap.airline_code = formFfn.airline;
      ffnMap.frequent_flyer_number = formFfn.flyerNumber;
      ffnMapping.push(ffnMap);
    }
    userInfoDTO.ffnMapping = ffnMapping;

    return userInfoDTO;
  }
 

  private getUpdatedFrequentGuestData(): UserInfo {
    let p = this.frequentGuestForm.value;
    let userInfoDTO = this.userAccountInfoService.getAccountInfo().userInfo;
    // p.frequentGuestFormArray.splice(-1,1);
    let glnMapping = Array<any>();
    for (let formGln of p.frequentGuestFormArray) {
      if (formGln.hotelChain === '' || !formGln.hotelChain) continue;
      let glnMap: any = {};
      glnMap.hotel_name = formGln.name;
      glnMap.chain = formGln.hotelChain;
      glnMap.number = formGln.guestNumber;
      glnMapping.push(glnMap);
    }
    userInfoDTO.loyalityCards = glnMapping;

    return userInfoDTO;
  }

  saveFrequentFlyerNumbers() {
    this.validateAllFormFields(this.frequentFlyerForm);
    for (var counter = 0; counter < this.frequentFlyerFormArray.length; counter++) {
      if (this.isEmptyFFNumber(counter)) {
        return;
      }
    }

    if (!this.duplicateEntryStatus && this.frequentFlyerForm.valid) {
      this.saveAndUpdateProfileInfo(this.getUpdatedFrequentFlyerData(), 'savePersonalInfoBtn');
    }
  }
  saveTrainLineNumbers() {
    this.validateAllFormFields(this.trainLoyalityForm);
    for (var counter = 0; counter < this.trainLoyalityFormArray.length; counter++) {
      if (this.isEmptyTrainlineumber(counter)) {
        return;
      }
    }

    if (!this.duplicateTrainEntryStatus && this.trainLoyalityForm.valid) {
      this.saveAndUpdateProfileInfo(this.getUpdatedFrequentFlyerData(), 'savePersonalInfoBtn');
    }
  }

  saveFrequentCarLoyalityNumbers() {
    this.validateAllFormFields(this.frequentCarLoyalityForm);

    for (var counter = 0; counter < this.frequentCarLoyalityFormArray.length; counter++) {
      if (this.isEmptyCLNumber(counter)) {
        return;
      }
    }
    if (!this.duplicateHotelEntryStatus && this.frequentCarLoyalityForm.valid) {
      this.saveAndUpdateProfileInfo(this.getUpdatedFrequenCarLoyalityData(), 'savePersonalInfoBtn');
    }
  }
  private getUpdatedFrequenCarLoyalityData(): UserInfo {
    let p = this.frequentCarLoyalityForm.value;
    let userInfoDTO = this.userAccountInfoService.getAccountInfo().userInfo;
    // p.frequentGuestFormArray.splice(-1,1);
    let clnMapping = Array<any>();
    for (let formCln of p.frequentCarLoyalityFormArray) {
      if (formCln.carCode === '' || !formCln.carCode) continue;
      let clnMap: any = {};
      clnMap.rentalCarName = formCln.name;
      clnMap.rentalCarCode = formCln.carCode;
      clnMap.rentalCarLoyaltyNumber = formCln.loyalityNumber;
      clnMapping.push(clnMap);
    }
    userInfoDTO.carLoyaltyNumbers = clnMapping;

    return userInfoDTO;
  }
  saveFrequentGuestNumbers() {
    this.validateAllFormFields(this.frequentGuestForm);

    for (var counter = 0; counter < this.frequentGuestFormArray.length; counter++) {
      if (this.isEmptyHGNumber(counter)) {
        return;
      }
    }
    if (!this.duplicateHotelEntryStatus && this.frequentGuestForm.valid) {
      this.saveAndUpdateProfileInfo(this.getUpdatedFrequentGuestData(), 'savePersonalInfoBtn');
    }
  }
  validateAllFormFields(formGroup: UntypedFormGroup) {         //{1}
    Object.keys(formGroup.controls).forEach(field => {  //{2}
      const control = formGroup.get(field);             //{3}
      if (control instanceof UntypedFormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof UntypedFormGroup) {        //{5}
        this.validateAllFormFields(control);            //{6}
      }
    });
  }


  private saveAndUpdateProfileInfo(userInfoDTO: UserInfo, saveButtonId: string) {
    this.isRequestInProgress = true;
    this.showProgress();
    this.fetchAccountInfoSubscription = this.userAccountInfoService.saveAccountInfo(
      userInfoDTO).subscribe(res => {
        this.isRequestInProgress = false;
        if (res.success == true) {
          this.showSuccess();
          //   this.isChangePasswordMode = false;
          if (res.data) {
            let userAccountInfoObj: UserAccountInfo = deserialize(res.data, UserAccountInfo);
            this.userAccountInfoService.setAccountInfo(userAccountInfoObj);
          }
        } else {
          this.showError();
        }
      }, error => {
        this.showError();
      });
  }

  showSuccess() {
    this.toastr.remove(this.activeToast.toastId);
    this.activeToast = this.toastr.success(this.translateService.instant('profilePage.Profilesavedsuccessfully').toString());
  }

  showProgress() {
    if (this.activeToast) {
      this.toastr.remove(this.activeToast.toastId);
    }
    this.activeToast = this.toastr.info(this.translateService.instant('profilePage.Pleasewait').toString(), this.translateService.instant('profilePage.Saving').toString());
  }

  showError() {
    if (this.activeToast) {
      this.toastr.remove(this.activeToast.toastId);
    }
    this.activeToast = this.toastr.success('Error!', 'Unknown error while saving profile');
  }

  ffnShortInFO(array) {
    let templateArray = [];
    let template = "";
    // for(let name of array.value){
    //     if(name.name !="" && name.flyerNumber != ""){
    //         templateArray.push(`${name.name} | ${name.flyerNumber}`);
    //     }
    // }
    // template = templateArray.join(' , ');
    // return template;
    for (let counter: number = 0; counter < this.frequentFlyerFormArray.controls.length; counter++) {
      let currObj: any = (<UntypedFormGroup>this.frequentFlyerFormArray.controls[counter]).controls;
      if (currObj['name'].value !== '' && currObj['flyerNumber'].value !== '') {
        templateArray.push(`${currObj['name'].value} | ${currObj['flyerNumber'].value}`);
      }
    }
    template = templateArray.join(' , ');
    return template;
  }

  trainShortInFO(array) {
    let templateArray = [];
    let template = "";
    // for(let name of array.value){
    //     if(name.name !="" && name.flyerNumber != ""){
    //         templateArray.push(`${name.name} | ${name.flyerNumber}`);
    //     }
    // }
    // template = templateArray.join(' , ');
    // return template;
    for (let counter: number = 0; counter < this.trainLoyalityFormArray.controls.length; counter++) {
      let currObj: any = (<UntypedFormGroup>this.trainLoyalityFormArray.controls[counter]).controls;
      if (currObj['name'].value !== '' && currObj['flyerNumber'].value !== '') {
        templateArray.push(`${currObj['name'].value} | ${currObj['flyerNumber'].value}`);
      }
    }
    template = templateArray.join(' , ');
    return template;
  }

  fgnShortInFO(array) {
    let templateArray = [];
    let template = "";
    for (let name of array.value) {
      if (name.name != "" && name.guestNumber != "") {
        templateArray.push(`${name.name} | ${name.guestNumber}`);
      }
    }
    template = templateArray.join(' , ');
    return template;
  }
  clnShortInfo(array) {
    let templateArray = [];
    let template = "";
    for (let name of array.value) {
      if (name.name != "" && name.loyalityNumber != "") {
        templateArray.push(`${name.name} | ${name.loyalityNumber}`);
      }
    }
    template = templateArray.join(' , ');
    return template;
  }
  onCarLoyalityNumberchange(i) {
    this.duplicateCarStatus = false;
    if ((<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[i]).controls['carCode'].value === '') {
      (<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[i]).controls['loyalityNumber'].setValidators(null);
      (<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[i]).controls['loyalityNumber'].setErrors(null);
      (<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[i]).controls['loyalityNumber'].setValue('');
    } else {
      let carBrandCode = (<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[i]).controls['carCode'].value;
      let selectedCarObject = ALL_CARBRANDS.find(item => item.carcode == carBrandCode);
      (<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[i]).controls['name'].setValue(selectedCarObject.value);
      (<UntypedFormGroup>this.frequentCarLoyalityFormArray.controls[i]).controls['loyalityNumber'].setValidators(
        Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
      if (i === this.frequentCarLoyalityFormArray.controls.length - 1) {
        this.addCarLoyalityEntry(undefined);
      }
    }
  }


}
