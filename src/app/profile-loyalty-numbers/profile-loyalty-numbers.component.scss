.card-div-inner {
  width: 100% !important;
}

.loaderClass {
  display: flex;
  margin-top: 0px;
}

:host ::ng-deep {

  .ng-select.ng-select-single .ng-select-container .ng-value-container,
  .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
    overflow: visible !important;
    position: relative !important;
    top: 0px !important;
  }

}

@media (max-width: 767px) {
  .loaderClass {
    display: flex;
    margin-top: 10px;
  }

  .loaderAlign {
    position: relative;
    top: -5px;
  }
}