<div class="card-div active shadow" id="frequentFlyerCard">
    <div class="card-div-inner">
        <div class="traveller-form">
            <div class="card-div-header" onclick="openCardEdit(this);">
                <h3>{{'profileLoyalty.FrequentFlyerNumber' | translate}}</h3>
                <div class="card-edit">
                    <span class="edit-text">Edit</span>
                    <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                </div>
                <div class="traveller-short-info">
                    <div class="traveller-short-info-inner">
                        {{ffnShortInFO(frequentFlyerFormArray)}}
                    </div>
                </div>
            </div>
            <div class="card-div-body">
                <form method="post" id="frequentFlyerForm" class="frequentFlyerForm" [formGroup]="frequentFlyerForm">
                    <div class="adderFlyers"></div>
                    <div class="row d-none d-md-flex">
                        <div class="col-lg-4 col-md-5  col-sm-12 col-xs-12">
                            <label class="input-label">{{'profileLoyalty.Airline' | translate}}</label>
                        </div>
                        <div class="col-lg-4  col-md-5  col-sm-12 col-xs-12">
                            <label class="input-label">{{'profileLoyalty.Flyernumber' | translate}} </label>
                        </div>
                    </div>
                    <div class="airlineFlyerNumberDiv-container" formArrayName="frequentFlyerFormArray">
                        <div *ngFor="let ffnEntry of frequentFlyerFormArray.controls; let i= index;"
                            class="row airlineFlyerNumberDiv" id="airlineFlyerNumberDiv" [formGroupName]="i">
                            <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12">
                                <div class="input-box">
                                    <label class="input-label d-block d-md-none">{{'profileLoyalty.Airline' |
                                        translate}}</label>
                                    <div class="select-box select-dropdown searchable-dropdown">
                                        <!-- <select formControlName="airline" onchange="selectAirlineChange(this);" class="input-textfield airlineOptions js-example-basic-single" id="airlineOptions" name="airlineOptions">
                                            <option *ngFor="let aItem of all_airlines" value="aItem.id">{{aItem.name}}</option>
                                        </select> -->
                                        <ng-select formControlName="airline"
                                            class="input-textfield airlineOptions js-example-basic-single"
                                            (change)="onAirlineChange(i)" [searchable]="true" [clearable]="false"
                                            [items]="all_airlines | translateOptions" bindLabel="value" bindValue="id"></ng-select>
                                        <span *ngIf="isDuplicateEntry(i)" class="error">
                                            {{'profileLoyalty.DuplicateEntry' | translate}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4  col-md-5 col-sm-12 col-xs-12">
                                <div class="input-box">
                                    <label class="input-label d-block d-md-none">{{'profileLoyalty.Flyernumber' |
                                        translate}}</label>
                                    <input formControlName="flyerNumber" maxlength="20"
                                        class="input-textfield disabledInput flyerNumber" type="text" placeholder=""
                                        [attr.disabled]="ffnEntry.controls['airline'].value === '' ? '' : null" />
                                    <span *ngIf="isEmptyFFNumber(i)" class="error">
                                        {{'profileLoyalty.Thisfieldisrequired' | translate}}</span>
                                    <span
                                        *ngIf="ffnEntry.controls['flyerNumber'].hasError('pattern') && (true || ffnEntry.controls['flyerNumber'].touched || ffnEntry.controls['flyerNumber'].dirty)"
                                        class="error">
                                        {{'profileLoyalty.Pleaseenteravalidfrequentflyernumberonlyalphanumericcharactorsallowed'
                                        | translate}}</span>
                                </div>
                            </div>
                            <div class="removeFlyerNumberDiv"
                                [class]="i === frequentFlyerFormArray.controls.length - 1 ? 'hide': ''">
                                <a href="javascript:void(0);"
                                    (click)="removeAirline(i,frequentFlyerFormArray.controls[i].controls['airline'].value);">{{'profileLoyalty.remove'
                                    | translate}}</a>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"
                                    (click)="saveFrequentFlyerNumbers()">{{'profileLoyalty.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profileLoyalty.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                                <!-- <button id="airlineDisabledButton" type="button" class="button button-text d-inline-block d-md-none airlineDisabledButton" onclick="addOtherFFN(this);">Add Another FFN</button> -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="card-div" >
        <div class="card-div-inner">
            <div class="traveller-form">
                <div class="card-div-header" onclick="openCardEdit(this);">
                    <h3>{{'profileLoyalty.TrainLoyaltyPrograms' | translate}}</h3>
                    <div class="card-edit">
                        <span class="edit-text">Edit</span>
                        <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                    </div>
                    <div class="traveller-short-info">
                        <div class="traveller-short-info-inner">
                            {{trainShortInFO(trainLoyalityFormArray)}}
                        </div>
                    </div>
                </div>
                <div class="card-div-body" style="display: none;">
                    <form method="post" id="trainLoyalityForm" class="frequentFlyerForm" [formGroup]="trainLoyalityForm">
                        <div class="adderFlyers"></div>
                        <div class="row d-none d-md-flex">
                            <div class="col-lg-4 col-md-5  col-sm-12 col-xs-12">
                                <label class="input-label">{{'profileLoyalty.Trainline' | translate}}</label>
                            </div>
                            <div class="col-lg-4  col-md-5  col-sm-12 col-xs-12">
                                <label class="input-label">{{'profileLoyalty.LoyaltyNumber' | translate}} </label>
                            </div>
                        </div>
                        <div class="airlineFlyerNumberDiv-container" formArrayName="trainLoyalityFormArray">
                            <div *ngFor="let ffnEntry of trainLoyalityFormArray.controls; let i= index;"
                                class="row airlineFlyerNumberDiv" id="trainLoyalityDiv" [formGroupName]="i">
                                <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12">
                                    <div class="input-box">
                                        <label class="input-label d-block d-md-none">{{'profileLoyalty.Trainline' |
                                            translate}}</label>
                                        <div class="select-box select-dropdown searchable-dropdown">
                                            <!-- <select formControlName="airline" onchange="selectAirlineChange(this);" class="input-textfield airlineOptions js-example-basic-single" id="airlineOptions" name="airlineOptions">
                                                <option *ngFor="let aItem of all_airlines" value="aItem.id">{{aItem.name}}</option>
                                            </select> -->
                                            <ng-select formControlName="airline"
                                                class="input-textfield airlineOptions js-example-basic-single"
                                                (change)="onTrainChange(i)" [searchable]="true" [clearable]="false"
                                                [items]="all_trainLines | translateOptions" bindLabel="value" bindValue="id"></ng-select>
                                            <span *ngIf="isDuplicateTarinlineEntry(i)" class="error">
                                                {{'profileLoyalty.DuplicateEntry' | translate}}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4  col-md-5 col-sm-12 col-xs-12">
                                    <div class="input-box">
                                        <label class="input-label d-block d-md-none">{{'profileLoyalty.LoyaltyNumber' |
                                            translate}}</label>
                                        <input formControlName="flyerNumber" maxlength="20"
                                            class="input-textfield disabledInput flyerNumber" type="text" placeholder=""
                                            [attr.disabled]="ffnEntry.controls['airline'].value === '' ? '' : null" />
                                        <span *ngIf="isEmptyTrainlineumber(i)" class="error">
                                            {{'profileLoyalty.Thisfieldisrequired' | translate}}</span>
                                        <span
                                            *ngIf="ffnEntry.controls['flyerNumber'].hasError('pattern') && (true || ffnEntry.controls['flyerNumber'].touched || ffnEntry.controls['flyerNumber'].dirty)"
                                            class="error">
                                            {{'profileLoyalty.PleaseenteravalidTainLoyalitynlyalphanumericcharactorsallowed'
                                            | translate}}</span>
                                    </div>
                                </div>
                                <div class="removeFlyerNumberDiv"
                                    [class]="i === trainLoyalityFormArray.controls.length - 1 ? 'hide': ''">
                                    <a href="javascript:void(0);"
                                        (click)="removeTrainline(i,trainLoyalityFormArray.controls[i].controls['airline'].value);">{{'profileLoyalty.remove'
                                        | translate}}</a>
                                </div>
                            </div>
                        </div>
    
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <div class="card-div-footer">
                                    <button *ngIf="!isRequestInProgress" type="submit"
                                        class="button button-primary saveButton"
                                        (click)="saveTrainLineNumbers()">{{'profileLoyalty.Save' | translate}}</button>
                                    <button *ngIf="isRequestInProgress" type="submit"
                                        class="button button-primary saveButton"> <span
                                            class="loaderClass">{{'profileLoyalty.PleaseWait' | translate}}<loader-dots
                                                class="loaderAlign"></loader-dots></span></button>
                                    <!-- <button id="airlineDisabledButton" type="button" class="button button-text d-inline-block d-md-none airlineDisabledButton" onclick="addOtherFFN(this);">Add Another FFN</button> -->
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            </div>
        </div>
<div class="card-div">
    <div class="card-div-inner">
        <div class="traveller-form">
            <div class="card-div-header" onclick="openCardEdit(this);">
                <h3>{{'profileLoyalty.FrequentGuestNumber' | translate}}</h3>
                <div class="card-edit">
                    <span class="edit-text">Edit</span>
                    <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                </div>
                <div class="traveller-short-info">
                    <div class="traveller-short-info-inner">
                        {{fgnShortInFO(frequentGuestFormArray)}}
                    </div>
                </div>
            </div>
            <div class="card-div-body" style="display: none;">
                <form method="post" id="frequentGuestForm" class="frequentGuestForm" [formGroup]="frequentGuestForm">
                    <div class="adderGuest"></div>
                    <div class="row d-none d-md-flex">
                        <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12">
                            <label class="input-label">{{'profileLoyalty.Hotels' | translate}}</label>
                        </div>
                        <div class="col-lg-4  col-md-5 col-sm-12 col-xs-12">
                            <label class="input-label">{{'profileLoyalty.GuestNumber' | translate}}</label>
                        </div>
                    </div>
                    <div class="hotelFlyerNumberDiv-container" formArrayName="frequentGuestFormArray">
                        <div *ngFor="let hlnEntry of frequentGuestFormArray.controls; let i= index;"
                            class="row hotelFlyerNumberDiv" [formGroupName]="i">
                            <div class="col-lg-4 col-md-5  col-sm-12 col-xs-12">
                                <div class="input-box">
                                    <label class="input-label d-block d-md-none">{{'profileLoyalty.Hotels' |
                                        translate}}</label>
                                    <div class="select-box select-dropdown searchable-dropdown">
                                        <!-- <select onchange="selectHotelChange(this);" class="input-textfield hotelOptions js-example-basic-single" id="hotelOptions" name="hotelOptions"></select> -->
                                        <ng-select formControlName="hotelChain"
                                            class="input-textfield hotelOptions js-example-basic-single"
                                            (change)="onHotelChainChange(i)" [searchable]="true" [clearable]="false"
                                            [items]="all_hotelChains | translateOptions" bindLabel="value" bindValue="chainCode">
                                        </ng-select>
                                        <span *ngIf="isDuplicateHotelChainEntry(i)" class="error">
                                            {{'profileLoyalty.DuplicateEntry' | translate}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4  col-md-5  col-sm-12 col-xs-12">
                                <div class="input-box">
                                    <label class="input-label d-block d-md-none">{{'profileLoyalty.GuestNumber' |
                                        translate}}</label>
                                    <!-- <input class="input-textfield disabledInput guestNumber" type="text" id="guestNumber" name="guestNumber" placeholder="" disabled="" /> -->
                                    <input formControlName="guestNumber" maxlength="20"
                                        class="input-textfield disabledInput guestNumber" type="text" placeholder=""
                                        [attr.disabled]="hlnEntry.controls['hotelChain'].value === '' ? '' : null" />
                                    <span *ngIf="isEmptyHGNumber(i)" class="error">
                                        {{'profileLoyalty.Thisfieldisrequired' | translate}}</span>
                                    <span
                                        *ngIf="hlnEntry.controls['guestNumber'].hasError('pattern') && (true || hlnEntry.controls['guestNumber'].touched || hlnEntry.controls['guestNumber'].dirty)"
                                        class="error">
                                        {{'profileLoyalty.Pleaseenteravalidfrequentflyernumberonlyalphanumericcharactorsallowed'
                                        | translate}}</span>
                                </div>
                            </div>
                            <div class="removeFlyerNumberDiv"
                                [class]="i === frequentGuestFormArray.controls.length - 1 ? 'hide': ''">
                                <a href="javascript:void(0);" (click)="removeHotelChain(i);">{{'profileLoyalty.remove' |
                                    translate}}</a>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <!-- <button type="submit" class="button button-primary saveButton" onclick="return submitFrequentGuestForm(this);">Save</button> -->
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"
                                    (click)="saveFrequentGuestNumbers()">{{'profileLoyalty.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profileLoyalty.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                                <!-- <button id="hotelDisabledButton" type="button" class="button button-text disabled d-inline-block d-md-none hotelDisabledButton" onclick="addOtherFGN(this);">Add Another FGN</button> -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="card-div">
    <div class="card-div-inner">
        <div class="traveller-form">
            <div class="card-div-header" onclick="openCardEdit(this);">
                <h3>{{'profileLoyalty.CarLoyaltyNumber' | translate}}</h3>
                <div class="card-edit">
                    <span class="edit-text">Edit</span>
                    <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                </div>
                <div class="traveller-short-info">
                    <div class="traveller-short-info-inner">
                        {{clnShortInfo(frequentCarLoyalityFormArray)}}
                    </div>
                </div>
            </div>
            <div class="card-div-body" style="display: none;">
                <form method="post" id="frequentCarLoyalityForm" class="frequentCarLoyalityForm"
                    [formGroup]="frequentCarLoyalityForm">
                    <div class="adderGuest"></div>
                    <div class="row d-none d-md-flex">
                        <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12">
                            <label class="input-label">{{'profileLoyalty.Cars' | translate}}</label>
                        </div>
                        <div class="col-lg-4  col-md-5 col-sm-12 col-xs-12">
                            <label class="input-label">{{'profileLoyalty.LoyaltyNumber' | translate}}
                            </label>
                        </div>
                    </div>
                    <div class="hotelFlyerNumberDiv-container" formArrayName="frequentCarLoyalityFormArray">
                        <div *ngFor="let hlnEntry of frequentCarLoyalityFormArray.controls; let i= index;"
                            class="row hotelFlyerNumberDiv" [formGroupName]="i">
                            <div class="col-lg-4 col-md-5  col-sm-12 col-xs-12">
                                <div class="input-box">
                                    <label class="input-label d-block d-md-none">{{'profileLoyalty.Cars' |
                                        translate}}</label>
                                    <div class="select-box select-dropdown searchable-dropdown">
                                        <!-- <select onchange="selectHotelChange(this);" class="input-textfield hotelOptions js-example-basic-single" id="hotelOptions" name="hotelOptions"></select> -->
                                        <ng-select formControlName="carCode"
                                            class="input-textfield hotelOptions js-example-basic-single"
                                            (change)="onCarLoyalityNumberchange(i)" [searchable]="true"
                                            [clearable]="false" [items]="all_carbrands | translateOptions" bindLabel="value"
                                            bindValue="carcode"></ng-select>
                                        <span *ngIf="isDuplicateCarLoyalityEntry(i)" class="error">
                                            {{'profileLoyalty.DuplicateEntry' | translate}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4  col-md-5  col-sm-12 col-xs-12">
                                <div class="input-box">
                                    <label class="input-label d-block d-md-none">{{'profileLoyalty.CarLoyaltyNumber' |
                                        translate}}</label>
                                    <!-- <input class="input-textfield disabledInput guestNumber" type="text" id="guestNumber" name="guestNumber" placeholder="" disabled="" /> -->
                                    <input formControlName="loyalityNumber" maxlength="20"
                                        class="input-textfield disabledInput guestNumber" type="text" placeholder=""
                                        [attr.disabled]="hlnEntry.controls['name'].value === '' ? '' : null" />
                                    <span *ngIf="isEmptyCLNumber(i)" class="error">
                                        {{'profileLoyalty.Thisfieldisrequired' | translate}}</span>
                                    <span
                                        *ngIf="hlnEntry.controls['loyalityNumber'].hasError('pattern') && (true || hlnEntry.controls['loyalityNumber'].touched || hlnEntry.controls['loyalityNumber'].dirty)"
                                        class="error">
                                        {{'profileLoyalty.Pleaseenteravalidloyaltynumberonlyalphanumericcharactorsallowed'
                                        | translate}}</span>
                                </div>
                            </div>
                            <div class="removeFlyerNumberDiv"
                                [class]="i === frequentCarLoyalityFormArray.controls.length - 1 ? 'hide': ''">
                                <a href="javascript:void(0);"
                                    (click)="removeCarLoyalityNumber(i);">{{'profileLoyalty.remove' | translate}}</a>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <!-- <button type="submit" class="button button-primary saveButton" onclick="return submitFrequentGuestForm(this);">Save</button> -->
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"
                                    (click)="saveFrequentCarLoyalityNumbers()">{{'profileLoyalty.Save' |
                                    translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profileLoyalty.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                                <!-- <button id="hotelDisabledButton" type="button" class="button button-text disabled d-inline-block d-md-none hotelDisabledButton" onclick="addOtherFGN(this);">Add Another FGN</button> -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>