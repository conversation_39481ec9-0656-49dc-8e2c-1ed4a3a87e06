@import "../../variables.scss";
:host {
    width: 100vw;
}

.input-textfield-lg {
    width: 100% !important;
}
.font-bold {
    font-family: $fontBold;
}
.font-bold1 {
    font-family: $fontBold;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.custom-selectbox {
    cursor: pointer;
    position: relative;
    display: inline-block;
    padding-right: 15px;
    margin-right: 5px;
    top: -20px;
}

.labelTime {
    position: relative;
    top: 0px;
    margin-right: 5px;
}

.labelBookingType {
    position: relative;
    top: 10px;
    margin-right: 5px;
}

.down-Arrow {
    position: absolute;
    left: 220px;
    top: 12px;
}

.down-arrow {
    left: 225px;
    top: -28px;
    position: relative;
}

.dateShow {
    position: absolute;
    white-space: nowrap;
    left: 13%;
    font-size: 16px;
    top: 4px;
    max-width: 200px;
    overflow: hidden;
    // text-overflow: ellipsis;
}

.filter-row {
    display: flex;
    justify-content: space-between;
}

.input {
    cursor: pointer;
    text-align: center;
    padding-top: 5px;
    width: 265px;
    padding-left: 25px;
    background: #FCFCFC;
    border: 1px solid var(--dark-bg-color);
    border-radius: 8px;
    height: 32px;
    text-align: left;
    font-size: 14px;
}

.input-box {
    height: 33px;
    cursor: pointer;
    border: none;
    border-radius: 6px;
    background-color: #fff;
    font-size: 14px;
}

.custom-selectbox .field-value {
    color: var(--hyperlink-color);
    font-size: 14px;
    margin-left: 50px !important;
}

.custom-selectbox .control-icon {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.top-strip {
    background: $themeColor2;
    float: left;
    width: 100%;
    height: 43px;
}

:host ::ng-deep {
    .ng-dropdown-panel {
        min-width: auto !important;
        top: auto !important;
        left: 10px !important;
        right: auto !important;
        bottom: auto !important;
        width: 265px !important;
    }
    .ng-option.ng-option-selected {}
    .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
        font-size: 16px !important;
        font-family: var(--globalFontfamilyr) !important;
        // max-height: 32px !important;
        line-height: 2em !important;
        padding-left: 25px !important;
        border-radius: 8px !important;
    }
    .ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {
        font-size: 16px !important;
    }
    .ng-select .ng-select-container {
        font-size: 16px !important;
        bottom: 4px !important;
        left: -15px !important;
    }
    .ng-clear-wrapper {
        display: none !important;
    }
    .ng-select span {
        box-sizing: border-box;
    }
    .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
        display: none !important;
    }
    .ng-select.ng-select-container.ng-value-container.ng-input {
        position: fixed !important;
        top: -14px;
    }
    .ng-select.ng-select-single .ng-select-container .ng-value-container,
    .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
        position: relative !important;
        top: -9px !important;
        white-space: nowrap;
        text-overflow: ellipsis !important;
        overflow: visible;
        max-width: 210px;
    }
    .ng-dropdown-panel.ng-select-bottom {
        border: 1px solid var(--dark-bg-color) !important;
    }
    .ng-select-container {
        border: none !important;
        &:after {
            display: none;
        }
    }
}

.showNumber {
    top: auto;
    position: absolute;
    left: auto;
    font-size: 12px;
    color: #A7A7A7;
    right: 10px;
    z-index: 10;
}

.search-box {
    width: 320px;
    padding: 0px 10px;
    font-size: 12px;
    line-height: 15px;
    text-align: left;
    height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    border: 1px solid #E4E4E4;
    border-radius: 6px;
    padding-right: 30px;
}

.modalAirportFilterInfo .modal-header::-webkit-scrollbar {
    display: none;
}

.dateShow1 {
    position: absolute;
    white-space: nowrap;
    font-size: 16px;
    right: calc((100% - 650px)/2);
    top: 6px;
    color: gray;
}

.select {
    font-family: "apercu-b" !important;
    position: relative;
    font-size: 20px;
    text-align: left;
    line-height: 45px;
    color: var(--dark-bg-color)!important;
    width: auto !important;
    display: inline-block;
    margin-right: 15px !important;
    font-weight: 700;
    margin-left: 15px !important;
    margin-bottom: 0px;
    padding-bottom: 0px;
    cursor: pointer;
}

.unselect {
    font-family: "apercu-r" !important;
    display: inline-block;
    text-align: left;
    color: var(--dark-bg-color)!important;
    margin-right: 15px;
    margin-left: 15px;
    margin-bottom: 0px;
    padding-bottom: 0px;
    font-size: 20px;
    width: auto !important;
    line-height: 30px;
    font-weight: 400;
    position: relative;
    cursor: pointer;
}

.modalAirportFilterInfo {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modalAirportFilterInfo .modal-content {
    border: none;
    border-radius: 6px;
}

.modalAirportFilterInfo .close {
    text-shadow: none;
    color: #fff;
    opacity: 1;
}

.modalAirportFilterInfo .modal-header {
    background-color: var(--hyperlink-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px;
    border-bottom: none;
}

.modalAirportFilterInfo .modal-header h5 {
    font-size: 14px;
    width: 100%;
}

.modalAirportFilterInfo .modal-footer {
    padding: 0;
    border-top: none;
    float: left;
    width: 100%;
}

.modalAirportFilterInfo .close i {
    font-size: 17px;
}

.modalAirportFilterInfo .close:hover {
    color: #fff !important;
    opacity: 1 !important;
}

.statusDrop {
    margin-top: 5px;
    margin-left: 0px;
    width: 253px;
}

.filter-modal1 {
    width: 253px;
    position: absolute;
    z-index: 1000;
    background: #fff;
    border-radius: 8px;
    left: calc((100% - 270px)/2);
    top: 32px;
    border: 1px solid var(--dark-bg-color);
}

.tab-content {
    float: left;
    width: 100%;
    padding: 0 20px;
}

.tab-list-item {
    display: inline-block;
    margin-right: 60px;
    padding-bottom: 0;
    margin-bottom: 0;
}

.tab-list-item a {
    cursor: pointer;
    position: relative;
    display: inline-block;
    margin-left: 20px;
    color: #FFFFFF;
    font-size: 12px;
    opacity: 0.6;
    line-height: 15px;
    padding: 10px 5px 13px 5px;
}

.tab-list-item a img {
    margin-right: 10px;
}

.tab-list-item.active a {
    opacity: 1;
}

.tab-list-item.active a::after {
    position: absolute;
    content: "";
    bottom: 2px;
    background: #fff;
    left: 0;
    right: 0;
    width: auto;
    height: 3px;
}

.showUserDetail {
    display: block;
    overflow: hidden;
    display: inline-block;
    width: 100%;
    text-overflow: ellipsis;
}

.showinUSerdetails {
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    width: 100%;
    text-align: left !important;
    vertical-align: middle;
    padding-right: 0px;
}

.tab-list-item12 {
    font-family: "apercu-r" !important;
    display: inline-block;
    text-align: left;
    color: #595959 !important;
    margin-right: 15px;
    margin-left: 15px;
    margin-bottom: 0px;
    padding-bottom: 0px;
    text-transform: uppercase;
    font-size: 18px;
    width: auto !important;
    line-height: 30px;
    font-weight: 400;
    cursor: pointer;
}

.tab-list-item12.active a {
    opacity: 1;
}

.tab-list-item11 {
    font-family: "apercu-b" !important;
    position: relative;
    text-transform: uppercase;
    font-size: 18px;
    text-align: left;
    line-height: 30px;
    color: var(--dark-bg-color) !important;
    text-decoration: underline;
    width: auto !important;
    display: inline-block;
    margin-right: 15px !important;
    font-weight: 700;
    margin-left: 15px !important;
    margin-bottom: 0px;
    padding-bottom: 0px;
    cursor: pointer;
}

.readonly-thumbs {
    width: 25px;
    height: 25px;
    margin-left: 25px;
}

.expense-image {
    height: 30px;
    width: auto;
    padding-bottom: 7px;
}

.bed-image {
    height: 60px;
    width: 60px;
}

.feature {
    text-align: left;
    padding-left: 0px;
    width: 100%;
}

.feature1 {
    padding-left: 0px;
    margin-top: 15px;
    margin-bottom: 15px;
    display: inline-block;
}

.admin-name {
    font-size: 15px;
    color: var(--hyperlink-color) !important;
    padding-right: 12px;
    font-family: "apercu-r";
}

.tab-content-item {
    float: left;
    width: 100%;
}

.result-card-box {
    position: relative;
    cursor: pointer;
    background: #fff;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.34);
    border-radius: 6px;
    font-family: $fontRegular;
    float: left;
    width: 100%;
    margin-bottom: 16.37px;
}
.result-card-box-inner::after{
    content: "";
    background: #E3E3E3;
    position: absolute;
    bottom: 0;
    left: 4px;
    height: 1px;
    width: 99%;
}
.header-left {
    text-align: right;
    width: 50% !important;
}

.header-right {
    text-align: left;
    width: 50% !important;
}

.header-inner {
    padding: 0px 0px 0px 0px;
    margin-bottom: 0px;
    height: 30px;
}

.mdl-radio__label {
    font-size: 14px;
    font-weight: normal;
    font-family: $fontRegular;
    text-align: left
}

.mdl-radio__outer-circle {
    position: relative !important;
    border: 1px solid #979797;
    background-color: #FFFFFF;
}

.mdl-radio.is-checked .mdl-radio__outer-circle {
    border: 1px solid var(--button-bg-color);
}

.radiostyle1 {
    margin-right: 1px;
    width: 20px;
}

.result-card-box-inner {
    position: relative;
    z-index: 2;
    background: #fff;
    float: left;
    width: 100%;
    border-radius: 6px;
    padding: 12px 32px 16px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: space-between;
    height: 105.63px;
    font-size: 14px;
}

.origin-destination.flight {
    // justify-content: space-between;
}

.origin-destination.hotel {
    // justify-content: space-between;
}

.origin-destination1.hotel {
    // justify-content: space-between;
}

.booking-details {
    display: flex;
    // border-right: 1px solid #e7e6e4;
    width: 200px;
    height: 100%;
    align-items: center;
}

.origin-destination {
    // width: calc(100% - 338px);
    display: flex;
    padding: 0 20px;
    padding: 10px 20px;
    padding-bottom: 0px;
}

.origin-destination1 {
    // width: calc(100% - 438px);
    display: flex;
    padding: 0 20px;
    //padding-bottom: 0px;
}

.booking-view-button {
    width: 123px !important;
    white-space: nowrap;
}

.primary-button {
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}

.booking-img {
    line-height: 12px;
    margin-right: 20px;
    width: 40px;
}

.origin-destination-section {
    width: calc(100% - 200px);
}

.origin-destination-switch {
    height: 28px;
    width: 28px;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    border-radius: 50%;
    margin: 0 30px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.origin-destination-switch span {
    font-size: 10px;
    color: rgba(0, 0, 0, 0.3);
}

.booking-view-button button {
    height: 43px;
    width: 123px;
    font-size: 12px;
    letter-spacing: 1px;
    line-height: 18px;
}



.hotel-nights-count {
    font-family: $fontBold;
    padding-top: 10px;
    max-width: 60px;
    min-width: 60px;
}

.booking-container {
    float: left;
    width: 100%;
    padding: 21px 0;
}

.booking-history-detail {
    width: 100%;
    margin: auto;
}

.booking-history-detail-heading {
    float: left;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 2px 30px;
}

.booking-history-detail-heading h4 {
    color: #fff;
    font-size: 12px;
    letter-spacing: 0.51px;
    line-height: 15px;
    margin: 0;
    padding: 0;
}

.booking-history-heading-link {
    line-height: 10px;
    margin-right: 18px;
}

.booking-details-inner {
    display: flex;
    align-items: center;
}

.booking-history-detail-content {
    float: left;
    width: 100%;
    padding-bottom: 50px;
    box-shadow: 2px 2px 2px 0px whitesmoke;
}

.booking-history-detail-content-inner {
    // display: flex;
}

.booking-history-detail-content-left {
    float: left;
    width: 100%;
    // max-width: 429.3px;
    font-family: $fontRegular;
    font-size: 14px;
    margin-right: 51.7px;
}

.booking-history-detail-content-right {
    padding-top: 60px;
}

.booking-detail-data {
    background: #fff;
    float: left;
    width: 100%;
    padding: 15px 30px;
    // border-radius: 6px;
    // box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
}

.booking-detail-header {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 16px;
    padding: 17px 8px 10px 8px;
}

.date-duration {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #E3E3E3;
    padding: 11px 12px;
}

.date {
    font-family: $fontBold;
}


/*.duration{font-size: 16px;}*/

.duration label {
    font-family: $fontRegular;
    font-size: 12px;
}

.duration span {
    font-family: $fontBold;
}

.flight-box {
    float: left;
    width: 100%;
    display: flex;
    font-family: $fontRegular;
    padding: 10px 15px 12px 15px;
}

.flight-modal-name-duration {
    display: flex;
    justify-content: space-between;
    float: left;
    width: 100%;
}

.flight-modal-name {
    font-size: 16px;
}

.flight-modal-duration {
    font-size: 14px;
}

.flight-ticket-detail {
    float: left;
    width: 100%;
    color: #AEAEAE;
    font-size: 11px;
    margin-bottom: 6px;
}

.flight-timing-stops ul li {
    position: relative;
    margin-bottom: 7px;
    font-size: 12px;
    float: left;
    width: 100%;
    padding-left: 20px;
    line-height: normal;
}

.flight-timing-stops ul li label {
    display: inline-block;
    vertical-align: middle;
}

.flight-timing-stops ul li span {
    margin-left: 0px;
    display: block;
}

.flight-timing-stops ul li:before {
    height: 9px;
    width: 9px;
    border-radius: 50%;
    background-color: var(--hyperlink-color);
    position: absolute;
    left: 0;
    top: 4px;
    content: '';
}

.flight-layover {
    float: left;
    width: 100%;
    padding: 0 10px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #EEEDEB;
    background-color: #EFFAFC;
    height: 37px;
    font-size: 14px;
}

.flight-layover-right {
    font-size: 14px;
}

.flight-box-right {
    padding-left: 18px;
}

.flight-modal-price {
    font-size: 19px;
    font-family: $fontMedium;
    line-height: 19px;
    color: var(--button-font-color);
}

.flight-modal-trip-type {
    color: #A6A5A4;
    font-size: 11px;
    line-height: 12px;
}

.seat-no {
    font-size: 12px;
    line-height: 14px;
    color: var(--button-font-color);
    font-family: $fontBold;
}

.flight-box-left {
    padding-top: 8px;
}

.price-section {
    float: left;
    width: 100%;
}

.price-section-content {
    float: left;
    width: 100%;
    border-top: 1px solid #E3E3E3;
    border-bottom: 1px solid #E3E3E3;
    padding: 5px 0;
}

.price-row {
    border-top: 1px solid #D8D8D8;
    padding-top: 10px;
    float: right;
    width: 100%;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 20px;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding-top: 20px;
    padding-right: 10px;
    
}

.price-row label {
    font-size: 18px;
    width: 134px;
    font-weight: 700;
    margin-right: 20px;
}

.price-row span {
    font-size: 14px;
    font-family: $fontMedium;
    width: 90px;
}

.price-section-total .price-row span {
    color: var(--button-font-color);
    font-size: 19px;
}

.price-section-total {
    float: left;
    width: 100%;
    padding: 6px 0;
}

.button-white {
    height: 44px;
    border: 1px solid #e9e9ed;
    background: #fff;
    border-radius: 6px;
    font-size: 15px;
    letter-spacing: 0.65px;
    line-height: 13px;
    color: var(--button-font-color);
    font-family: $fontMedium;
    width: 176px;
    display: -ms-flexbox;
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 5px 0 9px;
    cursor: pointer;
}

.button-white img {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
}

.button-white span {
    display: inline-block;
    vertical-align: middle;
}

.button-text {
    white-space: nowrap;
    font-size: 12px;
    letter-spacing: 1px;
    line-height: 13px;
    color: var(--button-font-color);
    font-family: $fontBold;
}

.button-text {
    font-size: 12px;
    letter-spacing: 1px;
    line-height: 13px;
    color: var(--button-font-color);
    font-family: $fontBold;
    background: none;
    border: none;
    padding: 0;
    white-space: nowrap;
}

.booking-button-text-container {
    border-top: 1px solid #E3E3E3;
    padding: 13px 30px;
    width: 240px;
}

.booking-button-text-container1 {
    border-top: 1px solid #E3E3E3;
    padding: 13px 30px;
    width: 130%;
}

.booking-history-detail-content-inner {
    // max-width: 720px;
    padding: 10px;
    margin: 0 auto;
}

.button-text {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.button-text img {
    margin-right: 13px;
}

.booking-button-container {
    margin-bottom: 18px;
    text-align: center;
}

.button-white-img {
    margin-right: 6px;
    width: 26px;
}

.marginTop {
    margin-top: 17px;
}

.input-textfield {
    padding: 5px 15px;
    color: #000000;
    font-size: 12px;
    width: 100%;
}

.modal-textarea {
    height: 111.46px;
    border: 1px solid #E4E4E4;
    border-radius: 6px;
    background-color: #F7F7F7;
    resize: none;
}

.footer-button {}

.booking-div-header h4 {
    font-size: 16px;
    padding-top: 20px;
    margin: 0;
    padding-bottom: 10px;
    font-family: $fontBold;
}

.modal-title {
    display: inline-block;
    float: left;
    width: 25% !important;
}

.booking-details {
    width: 200px;
}

.input-style {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
}

.input-control {
    margin-top: 0px;
    cursor: pointer;
}

.checkbox_input {
    padding-left: 3px;
    margin-top: 10px;
    margin-bottom: 10px;
    border: 1px var(--button-font-color);
}

.mdl-checkbox__input {
    position: absolute;
    left: -9999px;
}

.mdl-checkbox__input:checked+.mdl-checkbox__label:after {
    background-image: url(/../../assets/images/check-icon.png);
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center center;
}

.label1 {
    padding-top: 2px;
    padding-left: 10px;
    font-size: 14px;
    font-weight: bold;
    margin-top: 2px;
    margin-left: 5px;
}

.historyModal {
    min-width: 324px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modal-content {
    border: none;
    border-radius: 6px;
}

.close {
    text-shadow: none;
    color: #fff;
    opacity: 1;
}

.modal-header {
    background-color: var(--hyperlink-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px !important;
    border-bottom: none;
}

.modal-header h5 {
    font-size: 14px;
}

.modal-body {
    padding: 28px 25px 47px 25px;
    border-radius: 0 0 5px 5px;
}

.modal-footer {
    padding: 0;
    border-top: none;
}

.close i {
    font-size: 17px;
}

.close:hover {
    color: #fff !important;
    opacity: 1 !important;
}

.input-field {
    float: left;
    width: 100%;
    padding: 0;
}

.input-field label {
    float: left;
    width: 100%;
    font-size: 13px;
    color: #000000;
    line-height: 16px;
    margin-bottom: 14px;
}

.input-field input {
    float: none !important;
    width: 60%;
    border: 1px solid #E4E4E4;
    border-radius: 6px;
    background-color: #F7F7F7;
    height: 41px;
    font-size: 12px;
}

.modal-form-button {
    float: left;
    width: 100%;
    margin-top: 18.5px;
    padding-left: 12px;
}

.modal-form-button button {
    float: left;
    width: 100%;
    height: 46px;
    font-size: 12px;
    letter-spacing: 1px;
    font-weight: normal;
    font-family: $fontBold;
}

.modal-footer {
    background-color: #EFFAFC;
    border-radius: 0 0 6px 6px;
}

.modal-footer p {
    font-size: 12px;
    line-height: 19px;
    font-family: $fontRegular;
    padding: 18px 20px 33px 30px;
}

.modal-footer p span {
    font-family: $fontBold;
}

.requestModalBody.modal-body {
    padding-bottom: 15px;
}

.mdl-checkbox {
    float: none !important;
    width: 18px;
    font-weight: normal;
    cursor: pointer;
    margin-right: 8px;
    text-align: right;
    margin-bottom: 6px;
}

.empty-container {
    float: left;
    width: 100%;
    text-align: center;
    padding: 43px 0 43px 0;
}

.empty-img {
    float: left;
    width: 100%;
}

.empty-text {
    float: left;
    width: 100%;
    font-size: 18px;
    line-height: 22px;
    padding: 38px 0 50px;
    color: #18CFDD;
}

.empty-button {
    float: left;
    width: 100%;
}

.empty-button button {
    height: 46px;
    width: 296px;
    border-radius: 6px;
    max-width: 100%;
}

.button-text-img {
    width: 20px;
    margin-right: 12px;
}

.option-group {
    text-align: left;
    background: #fff;
    padding: 13px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    font-family: $fontRegular;
    border-radius: 8px;
}

.option-group-list-item {
    float: left;
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.option-group-list-item:last-child {
    margin-bottom: 0;
}

.option-group-list-item-label {
    font-family: $fontBold;
}

.option-group-list-item-img {
    width: 20px;
    margin-right: 15px;
    display: inline-block;
}

.option-group-list-item-label span {
    font-size: 12px;
    font-style: italic;
    font-family: $fontLight;
    color: #aeaeae;
    padding-left: 3px;
}

.hotel-booking-detail {
    display: flex;
    margin-top: 27px;
    margin-bottom: 8px;
    padding: 0 15px;
}

.door {
    width: 15px !important;
    height: 15px !important;
}

.hotel-booking-img {
    width: 71px;
    height: 71px;
    border-radius: 6px;
}

.hotel-booking-img img {
    border-radius: 6px;
}

.hotel-booking-content {
    width: calc(100% - 71px);
    padding-left: 11px;
    display: flex;
    flex-wrap: wrap;
}

.hotel-contact {
    float: left;
    width: 100%;
    padding: 0 15px;
    font-size: 12px;
}

.book-data {
    float: left;
    width: 50%;
    line-height: 16px;
    margin-bottom: 10px;
}

.book-data label {
    font-size: 12px;
    display: block;
}

.book-data span {
    font-size: 14px;
    font-family: $fontBold;
    display: block;
}

.hotel-about-detail-text {
    float: left;
    width: 100%;
    font-size: 12px;
    line-height: 16px;
    color: #AEAEAE;
    text-transform: uppercase;
    border-top: 1px solid #e3e3e3;
    padding: 10px 15px 13px 15px;
    margin-top: 15px;
}

.contact-icon {
    width: 18px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 2px;
}

.contact-text {
    display: inline;
    vertical-align: middle;
}

.footer-button {
    float: left;
    width: 100%;
    text-align: center;
    padding: 20px 10px;
    position: fixed;
    bottom: 0;
    left: 0;
}

.footer-button button {
    border-radius: 6px;
    width: 277px;
    height: 46px;
    background-color: var(--button-bg-color) !important;
}

.booking-div-container {
    float: left;
    width: 100%;
    border-bottom: 2px solid #000;
}

.gallopCash {
    color: rgb(117, 0, 128);
    letter-spacing: 0.65px;
    font-family: $fontMedium;
    font-weight: bold;
}

.booking-container .trip-search-div{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 0px 10px;
}

// new ui css 
.trip-search-div .trips-count{
    font-size: 12px;
    color: #AEAEAE;
    display: flex;
    align-items: center;
}

.trip-search-div .search-input-div{
    width: 300px;
}

.trip-search-div .search-input{
    width: 320px;
    border-radius: 5px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    border: 1px solid rgba(0, 0, 0, 0.14);
    padding: 0px 10px;
    height: 33px;
}

.show-destination{
    width: 80%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0px 20px;
}

.show-destination .destination-devider{
    margin: 0px 5px;
}

.flight-timing{
    display: flex;
    align-items: center;

    // background-color: #000;
}


.flight-timing span{
    margin: 0px 5px;
}
.view-details-div{
    padding: 10px 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f7f7f7;
}

.booking-history-upper-header{
    margin: 15px 20px;
}

.flight-arrow{
    height: 1px;
    width: 35%;
    top: 15px;
    right: 15%;
    position: absolute;
    background-color: #000;
}
.flight-arrow span{
    position: absolute;
    content: "";
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 10px solid #979797;
    right: -2px;
    top: -4px;
}

.flight-start-timing{
    width: 230px;
}
.flight-start-timing div:nth-child(1n){
color: #000;
font-size: 22px;
font-weight: 700;

}
.flight-start-timing div:nth-child(2n){
    color: #000;
    font-size: 14px;
    font-weight: 700;
}
.booking-history-upper-header div:nth-child(2n){
display: flex;
align-items: center;
}
.booking-history-upper-header .pancel-img{
    width: 18px;
    margin-left: 10px;
    margin-bottom: 5px;
}
.booking-history-upper-header select{
margin-left: 5px;
border: none;
font-weight: 800;
background-color: transparent;
}
.booking-details-icons{
    height:80px;
    display: flex;
    border-top: 1px solid #e3e3e3;
    
}
.booking-details-icons .item{
    width: 200px;
    color: black;
    flex-direction: column;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    word-wrap: break-word;
    text-align: center;
}
.booking-details-icons .item::after{
    content: "";
    position: absolute;
    width: 1px;
    height: 75%;
    background-color: #E3E3E3;
    right: 0px;
}
// .booking-details-icons .dividing-line{
//     width: 1px;
//     height: 80%;
//     background-color: #E3E3E3;
//     margin: auto;

// }

.company-img{
margin: 0px 25px;
width: 70px;
height: 70px;

}

.see-details-button{
    color: #1211F5;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
}

.price-show{
color: #8936F3;
text-align: center;
font-size: 18px;
font-weight: 700;
margin-bottom:5px;
}

.policy-show{
color: #8BBF6B;
text-align: center;
font-size: 16px;
font-weight: 500;
text-transform: uppercase;
margin-bottom:5px;
}
.confirmation-text{
    color: #000;
text-align: center;
font-size: 14px;
font-style: normal;
font-weight: 500;
max-width: 155px;
}

.booking-details-MainContainer{
display: flex;
}
.details-box{
border-radius: 6px;
width: 95%;
box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
float: right;
}
.details-and-price-box{
    display: flex;
    justify-content: space-between;
}

.price-continer-box{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #E3E3E3;
    width: 190px;
    padding: 10px;
}

.price-continer-box .text-elm{
 text-align: center;
}
.item-name{
    display: none;
}
.img-details-holder{
    display: flex;
    margin-top:20px;
}
.payment-details{
    margin-top: 10px;
}
.total-price-box{
    width: 100%;
    background-color: white;
    padding: 0px 40px;
    border-top: 1px solid #80808078;
}
.total-price-box-inner-div{
    width: 100%;background-color: white;display: flex;padding: 20px 0px;justify-content: flex-end;
}
.total-price-label{
    color: #06020b;font-size: 22px;font-style: normal;font-weight: 700;margin-right: 10px;
}
.price-div{
    color: #8936F3;font-size: 22px;font-style: normal;font-weight: 700;
}
.trip-tag-box{
width: 100%;background-color: white;padding: 0px 40px;
}
.single-trip-box{
    // display: flex;
    // flex-wrap: wrap;
    width: 100%;
    margin: 20px 0px;
    padding-top: 15px;
    display: inline-block;
}
.trip-tag-box .trip-tag-name-box{
width: 250px;
    cursor: pointer;
    text-align: center;
    padding-top: 5px;
    padding-left: 15px;
    background: #F7F7F7;
    border: 1px solid rgb(138, 39, 247);
    border-radius: 8px;
    height: 35px;
    text-align: left;
    font-size: 14px;
}
.payment-details span{
  color: black;font-size: 15px;

}
.result-card-box-inner .booking-description{
    word-wrap: normal;
    text-align: start;
    display: flex;
    align-items: center;
}
.result-card-box-inner .booking-details , .origin-destination,.flight-timing, .booking-description {
    width: 80%;
}
.origin-destination1{
    width: 40%;
}
.deviding-container{
    display: flex;
    width: 100%;
    align-items: center;
}
.inner-view-details-div{
    // display: flex;
    // width: 70%;
    // flex-wrap: wrap;
    // justify-content: space-between;
}
.result-card-box-inner .booking-description{
    width: 100%;
}
.payment-details{
    word-wrap: break-word;
    width: 80%;
}
// .result-card-box-inner .deviding-container .booking-description{
// margin-top: 10px;
// }
@media (max-width: 1200px) {
    .statusDrop {
        margin-top: 12px;
        margin-left: 0px;
        width: 253px;
    }
    .filter-row {
        display: block;
    }
    .trip-search-div{
        display: flex !important;
    }
    .origin-destination {
        // width: calc(100% - 313px);
        padding: 0 30px;
    }
    .origin-destination1 {
        padding: 0 30px;
    }
    .origin-destination-section {
        width: calc(100% - 200px);
    }
    .origin-destination-switch {
        margin: 0 20px;
    }
}
@media (max-width:992px){
    :host::ng-deep{ .bs-datepicker .bs-media-container {
        flex-direction: row !important;
    }
}
}
@media (max-width: 991px) {
        :host::ng-deep{ 
            .bs-datepicker .bs-media-container {
            flex-direction: column !important;
        }

    }
    .result-card-box-inner {
        padding: 12px 16px 16px;
        font-size: 13px;
    }

    .booking-img {
        margin-right: 8px;
    }
    .booking-view-button {
        width: 90px;
        white-space: nowrap;
    }
    .origin-destination1 {
        width: 90px;
        padding: 0 10px;
    }
    .origin-destination-section {
        width: calc(100% - 200px);
    }
    .origin-destination1 {
        width: calc(100% - 370px);
        display: flex;
        padding: 0 10px;
    }
    .details-and-price-box{
       flex-direction: column;
    }
    .price-continer-box{
        width: 100%;
        display: block;
        padding: 5px 20px;

    }
    .price-continer-box .text-elm{
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: row-reverse;
    }

    .booking-details-icons{
        flex-wrap:wrap;
        height: auto;
    }

    .booking-details-icons .item {
        width: 50%;
        height: 80px;
      }
     .booking-details-icons .dividing-line {
        margin: 0px;

     } 
     .result-card-box-inner .deviding-container .booking-description{
        margin-top: 10px;
        }
}

@media(max-width:1024px) {
    .booking-view-button {
        // display: none;
    }

    .booking-details {
        border-right: none;
        height: auto;
    }
    .result-card-box-inner {
        font-size: 12px;
        padding: 8px 10px 12px;
        height: auto;
    }
    .origin-destination1 {
        width: 100%;
        padding: 0 0px;
        padding-top: 10px;
    }
    .text-left {
        margin-left: 10px;
    }
    .footer-button {
        display: block;
    }
    .origin-destination {
        width: 100%;
        padding: 0;
    }
    .origin-destination-section {
        width: 130px;
    }
}

@media (max-width: 768px) {
     :host ::ng-deep {
        .ng-dropdown-panel {
            position: absolute !important;
            max-width: 253px !important;
            top: 32px !important;
            left: auto !important;
            background: #F7F7F9 !important;
            right: auto !important;
            -ms-transform: translate(0%);
            transform: translate(-6%);
            bottom: auto !important;
            min-width: 100px !important;
            width: calc(100% - 40px) !important;
        }
        #bookingDropdown .ng-dropdown-panel {
            position: absolute !important;
            max-width: 253px !important;
            top: 33px !important;
            left: auto !important;
            background: #F7F7F9 !important;
            right: auto !important;
            -ms-transform: translate(0%);
            transform: translate(-6%);
            bottom: auto !important;
            min-width: 100px !important;
            width: calc(100% - 40px) !important;
        }
    }
    .price-section {
        float: left;
        width: auto;
    }
    .hotel-about-detail-text {
        font-size: 9px;
        line-height: 15px;
    }
    .booking-button-text-container1 {
        border-top: 1px solid #E3E3E3;
        padding: 13px 0px;
        width: 110%;
    }
    .hotel-booking-detail {
        padding: 0 5px;
    }
    .book-data1 {
        width: 55%;
    }
    .book-data2 {
        width: 45%;
    }
    .hotel-booking-content {
        padding-left: 8px;
    }
    .tab-list-item {
        width: 50%;
        float: left;
        display: block;
        text-align: center;
        margin-right: 0;
    }
    .tab-list-item a {
        margin-left: 0;
    }
    .result-card-box-inner {
        flex-wrap: flex;
        font-size: 12px;
        padding: 8px 10px 12px;
        height: auto;
    }
    .booking-view-button {
        // display: none;
    }
    .booking-details {
        border-right: none;
        height: auto;
    }
    .origin-destination1 {
        width: 10%;
        padding: 0 0px;
        padding-top: 10px;
    }
    .text-left {
        margin-left: 10px;
    }
    .footer-button {
        display: block;
    }
    .origin-destination {
        width: 100%;
        padding: 0;
    }
    .origin-destination-section {
        width: 130px;
    }
    .origin-destination-switch {
        margin: 0 12px;
    }
    .tab-content {
        padding: 0 15px;
        overflow: auto;
    }
    .booking-img {
        /*margin-top: -3px;*/
        margin-right: 10px;
    }
    /*.booking-img img{width: 24px;}*/

    .booking-history-detail-content-inner {
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0 18px;
    }
    .booking-history-detail-content-left {
        margin-right: 0;
    }
    .booking-history-detail-content-right {
        padding-top: 24px;
        padding-bottom: 50px;
    }
    .booking-detail-data {
        padding: 0 8px 31px 8px;
    }
    .price-row {
        justify-content: flex-end;
    }
    .hotel-nights-count {
        margin-top: -20px;
        padding-top: 0;
        word-wrap: nowrap;
        max-width: 60px;
        min-width: 60px;
    }
    .primary-text {
        font-size: 13px;
    }
    .vertical_dotted_line{
        display: none;
    }
    .booking-details-MainContainer{
        display: block;
    }
    .details-box{
        width: 97%;
        margin: 15px auto;
        float: none;
    }
    .item-name{
        display: block;
    }
    .booking-detail-data .flight-start-timing{
        width: 200px;
    }

    .booking-detail-data .flight-start-timing .div{
        font-size: 20px;
        font-weight: 600;
    }

    .booking-detail-data .booking-img{
        display: flex;
        align-items: center;
        font-size: 20px;
        text-transform: uppercase;
        font-weight: 600;
        font-family: 'ApercuPro';
    }

    .booking-detail-data .booking-img img{
        margin: 10px 10px;
    }

    .img-details-holder{
        margin: 0px;
        margin-top: 5px;
        justify-content: space-between;
    }
    .booking-details-holder{
        width: 100%;
    }
    .company-img{
        flex: 0 0 auto;
        width: 84px;
        height: 59px;
        text-align: center;
        margin: 0px 10px;
    }

    .trip-search-div{
        flex-wrap: wrap;
    }
    .flight-start-timing div:nth-child(1n){
        color: #000;
        font-size: 18px;
        font-weight: 700;    
    }
    .flight-start-timing div:nth-child(2n){
        color: #000;
        font-size: 11px;
        font-weight: 700;
    }
    .result-card-box-inner .deviding-container .booking-description{
        margin-top: 0px;
        justify-content: flex-end;
    }
}

@media (max-width: 991px) {
    .input-textfield {
        max-width: 100% !important;
    }
}

@media(max-width:767px) {
    .origin-destination1 {
        width: 50%;
    }
    .loaderMobile{
        margin-top: 35px;
    }
    .search-box{
        width: 250px;
        padding: 0px 10px;
        font-size: 12px;
        line-height: 15px;
        text-align: left;
        height: 40px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        border: 1px solid #E4E4E4;
        border-radius: 6px;
        padding-right: 30px;
        background-color: #F7F7F7;
    }
     :host ::ng-deep {
        .ng-dropdown-panel {
            position: absolute !important;
            max-width: 253px !important;
            top: 32px !important;
            left: auto !important;
            background: #F7F7F9 !important;
            right: auto !important;
            -ms-transform: translate(0%);
            transform: translate(-6%);
            bottom: auto !important;
            min-width: 100px !important;
            width: calc(100% - 40px) !important;
        }
        #bookingDropdown .ng-dropdown-panel {
            position: absolute !important;
            max-width: 253px !important;
            top: 33px !important;
            left: auto !important;
            background: #F7F7F9 !important;
            right: auto !important;
            -ms-transform: translate(0%);
            transform: translate(-6%);
            bottom: auto !important;
            min-width: 100px !important;
            width: calc(100% - 40px) !important;
        }
    }
    .booking-img{
        width: 30px;
    }
    .labelTime {
        position: relative;
        top: 0px;
        margin-right: 5px;
    }
    .dateShow {
        position: absolute;
        white-space: nowrap;
        left: 13%;
        font-size: 16px;
        top: 6px;
        max-width: 195px;
        overflow: hidden;
        // text-overflow: ellipsis;
    }
    .input {
        cursor: pointer;
        text-align: center;
        padding-top: 5px;
        width: 249px;
        padding-left: 25px;
        background: #FCFCFC;
        border: 1px solid var(--dark-bg-color);
        border-radius: 8px;
        height: 32px;
        text-align: left;
        font-size: 14px;
    }
    .down-arrow {
        left: 225px;
        top: -28px;
        position: relative;
    }
    .down-Arrow {
        position: absolute;
        left: 190px;
        top: 12px;
    }
    .dateShow1 {
        position: absolute;
        white-space: nowrap;
        right: calc((100% - 346px)/2);
        top: 6px;
        font-size: 14px;
        color: gray;
    }
   
    .flight-timing-stops ul li span {
        margin-left: 0px;
        display: block;
    }
    .showUserDetail {
        display: block;
    }
    .showinUSerdetails {
        line-height: 15px;
        font-family: var(--globalFontfamilyr);font-weight: bold;;
        width: 100%;
        padding-right: 5px;
        vertical-align: middle;
        text-align: left !important;
    }
    .tab-list-item11 {
        font-family: "apercu-b" !important;
        position: relative;
        text-transform: uppercase;
        font-size: 14px;
        text-align: left;
        line-height: 30px;
        color: var(--dark-bg-color) !important;
        text-decoration: underline;
        width: auto !important;
        display: inline-block;
        margin-right: 10px !important;
        font-weight: 700;
        margin-left: 10px !important;
        margin-bottom: 0px;
        padding-bottom: 0px;
        cursor: pointer;
        white-space: nowrap;
    }
    .tab-list-item12 {
        font-family: "apercu-r" !important;
        display: inline-block;
        text-align: left;
        color: #595959 !important;
        margin-right: 10px;
        margin-left: 10px;
        margin-bottom: 0px;
        padding-bottom: 0px;
        text-transform: uppercase;
        font-size: 14px;
        white-space: nowrap;
        width: auto !important;
        line-height: 30px;
        font-weight: 400;
        cursor: pointer;
    }

    .deviding-container{
        display: block;
    }
    .result-card-box-inner .booking-description , .result-card-box-inner .origin-destination1 , .result-card-box-inner .showinUSerdetails{
        text-align: end !important;
    }
    .booking-details ,.show-destination,.booking-description,.flight-timing,.origin-destination1,.origin-destination{
        margin-top: 5px;
    }
    .flight-timing,.origin-destination1 {
        justify-content: flex-end;
        width: 100%;
        text-align: end;
    }
    .show-destination{
        justify-content: flex-start;
        margin-left: 20px;
    }
    .origin-destination1{
        padding: 0px;
    }
    .destination-hotel{
        margin-left: 41px;
    }
    
    .view-details-div div{
        width: 100%;
    }
    .booking-view-button{
        margin: 0px auto;
        margin-top: 5px;
    }
}

@media(max-width:550px) {
    .labelTime {
        position: relative;
        top: -10px;
        margin-right: 5px;
    }
    .flight-arrow {
        top: 10px;
        width: 36%;
    }
}

@media (max-width: 499px) {
    
    .booking-detail-data .booking-img img{
        width: 30px;
    }
    .origin-destination-section {
        width: 110px;
    }
    .labelTime {
        position: relative;
        top: 0px;
        margin-right: 5px;
    }
    .origin-destination1-section {
        width: 110px;
    }
    .totalPrice {
        font-size: 16px !important;
    }
    .company-img {
        flex: 0 0 auto;
        width: 50px;
        height: 50px;
        text-align: center;
        margin: 0px 10px;
    }
    .details-box{
        width: 100%;
        margin: 0px 0px;
        float: none;
    }
    .flight-arrow{
        top: 10px;
        width: 25%;
    }
    .flight-start-timing div:nth-child(1n){
        font-size: 16px;
    }
    .flight-start-timing div:nth-child(2n){
        font-size: 10px;
        font-weight: 500;
    }
    .booking-history-detail-content-left{
        font-size: 12px;
    }
    .flight-start-timing{
        width: 100%;
    }
    .payment-details{
        width: 80%;
        word-wrap: normal;
    }
    .payment-details span{
        font-size: 13px;
    }
    .booking-description{
        word-wrap: normal;
    }
    .confirmation-text{
        font-size: 12px;
    }
    .policy-show{
        font-size: 12px;
    }
    .price-show{
        font-size: 16px;
    }
    .view-details-div{
        flex-wrap: wrap;
    }
    .trip-search-div .search-input{
        margin-top: 20px;
        width: 249px;
    }
    .result-card-box-inner .booking-details{
       width:100%
    }
    .booking-text{
        width: 100%;
    }
    .show-destination{
        margin-left: 13px;
    }
    .destination-hotel{
        margin-left: 32px;
    }

}
.vertical_dotted_line
{
    border-left: 1px dotted black;
    height: 100%;
    margin: 0px 28%;
    position: absolute;
}
.icon-parent-div{
    position: relative;
}
.blocked,.flight-time{    margin-top: 10px;
font-size: large;
font-weight: bolder;
color: black;
}
.underline{
    border-top: 4px solid var(--dark-bg-color);
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
 }
 @media(max-width:414px) {
    .tab-content {
        padding: 0 20px;
        overflow: auto;
    }
     .filter-modal1 {
         width: 100%;
         position: absolute;
         z-index: 1000;
         background: #fff;
         border-radius: 8px;
         // left: calc((100% - 315px)) !important;
         top: 32px;
         border: 1px solid var(--dark-bg-color);
         overflow: hidden;
     }
 }

 @media(max-width:767px) {
    .tab-content1{
        float: left;
        width: 100%;
        background-color: #FFFFFF;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
        padding: 0 6px;
    }
    .tab-content{
        float: left;
       position: relative;
        width: 100%;
        background-color: #FFFFFF;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
        padding: 0 6px;
    }
    .filter-modal1 {
        width: 100%;
        position: absolute;
        z-index: 1000;
        background: #fff;
        border-radius: 8px;
      //  left: calc((100% - 310px)/2) !important;
        top: 32px;
        border: 1px solid var(--dark-bg-color);
        overflow: hidden;
    }
    .modal-body {
        padding: 28px 15px 47px 15px;
        border-radius: 0 0 5px 5px;
    }

 }
 @media(max-width:397px){
    .tab-content1{
        padding: 0 15px !important;
    }
 }
    