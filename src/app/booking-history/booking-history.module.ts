import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { BookingHistoryRoutingModule } from './booking-history.routing.module';
import { CommonModule } from '@angular/common';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ShareModule } from '../share.module';
import { TooltipModule } from 'ng2-tooltip-directive';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { NgSelectModule } from '@ng-select/ng-select';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiScrollModule } from 'ngx-ui-scroll';



@NgModule({
  imports: [
    CommonModule,
    BookingHistoryRoutingModule,
    NgbModule,
    FormsModule,
    ReactiveFormsModule,
    TooltipModule,
    NgxSmartModalModule,
    NgSelectModule,
    BsDatepickerModule,
     UiScrollModule,
     ShareModule
    
  ],
  declarations: [],
})
export class BookingHistoryModule {

}