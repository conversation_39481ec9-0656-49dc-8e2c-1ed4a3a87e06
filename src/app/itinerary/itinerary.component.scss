@import "../../variables.scss";

:host {
    width: 100vw;
}

.itinerary-container {
    padding: 40px;
    box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
    background-color: #EEEDEB;
}

.itinerary-card {
    border-radius: 6px;
    background-color: #FFFFFF;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.19);
    padding: 40px;
}

.card-heading {
    margin-bottom: 30px;
    letter-spacing: 2.28px;
}

.card-heading1 {
    margin-bottom: 0px !important;
    letter-spacing: 2.28px;
}

.logo {
    text-align: center !important;
    margin-top: 10px;
    margin-bottom: 10px !important;
}

.text1 {
    font-weight: bold;
    word-wrap: break-word !important;
}

.text2 {
    font-size: 0.75em;
    color: rgb(108, 104, 101) !important;
    line-height: 1.5em;
    word-wrap: break-word !important;
}

.heading {
    font-size: 1.2857rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.text-wrapper {
    font-size: 14px;
}

.text4 {
    color: rgb(108, 104, 101) !important;
}

.text3 {
    margin-top: 8px;
    padding: 10px 5px 10px 0 px;
    color: rgb(108, 104, 101);
    font-size: 0.625em;
}

.image {
    fill: rgb(138, 40, 247) !important;
    font-size: 0.75em;
    font-weight: bold;
    width: 1.688em;
}

.hotel-card-wrapper {
    margin-top: 10px;
    margin-bottom: 10px;
}

.pipe {
    display: inline-block;
    margin: 0 10px;
    color: $secondary-text-color;
}

.hotel-card {
    padding: 15px;
    border: 1px solid #D8EBEF;
    background-color: #EFFAFC;
}

.flight-card {
    border: 1px solid #D8EBEF;
}

.flight-detail-wrapper {
    padding: 40px 25px;
    background: #effafc;
    position: relative;
}

.flight-detail,
.time-detail {
    display: flex;
    justify-content: space-between;
}

.flight-detail {
    position: relative;
    z-index: 1;
    margin-bottom: 28px;

    &:after {
        position: absolute;
        content: '';
        height: 1px;
        background: #E3E3E3;
        left: 0;
        right: 0;
        top: 15px;
        z-index: -1;
    }

    .icon-plane {
        font-size: 28px;
        background: #effafc;
        padding: 0 15px;
        color: $primary-color;
    }
}

.source,
.destination {
    font-size: 1.2857rem;
    font-weight: 600;
    line-height: 26px;
    display: inline-block;
    background: #effafc;
}

.source {
    padding-right: 20px;
}

.destination {
    padding-left: 20px;
}

.info {
    font-size: 12px;
    color: #6C6865;
}

.layover-container {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
}

.layover {
    display: inline-block;
    padding: 3px 54px;
    border: 1px solid $border-color;
    position: relative;
    z-index: 1;
    background: #fff;
    margin-bottom: -15px;
}

.airline-detail,
.payment-detail {
    padding: 0 20px;
}

.airline-detail {
    line-height: 1.3;
}

.flight-card-wrapper {
    margin: 10px 0 20px;
}

.booking-card {
    border: 1px solid #EBEBEB;
}

.booking-detail {
    padding: 45px 25px;
    background-color: #F8F8F8;
}

.list-item {
    margin-bottom: 12px;

    &:last-child {
        margin-bottom: 0;
    }
}

.price {
    color: $primary-color;
    font-size: 18px;
    font-weight: 600;
    margin-top: 12px;
}

.sub-text {
    font-size: 12px;
}

.separator {
    border: 1px solid $border-color;
    margin: 40px 0;
}

.link-button {
    font-weight: 600;
}

.operating-carrier-detail {
    display: flex;
    align-items: center;

    .flight-icon {
        height: 12px;
    }
}

@media (max-width: 575.98px) {
    .itinerary-card {
        padding: 16px;
    }

    .card-heading {
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid $border-color;
        font-weight: 600;
    }

    .flight-icon {
        margin-right: 8px;
    }

    .detail-header-on-mobile {
        display: flex;
        margin: 15px 0 10px;
        align-items: flex-end;
        justify-content: space-between;
    }

    .flight-detail:after {
        display: none;
    }

    .flight-detail-wrapper {
        margin: 20px 0;
        padding: 16px;
    }

    .layover-container {
        position: static;
        margin-bottom: 20px;
    }

    .layover {
        position: static;
        margin: 0;
        background: $background-light;
    }

    .payment-detail {
        padding: 16px;
        background-color: #F8F8F8;
        border: 1px solid #EBEBEB;
        border-radius: 6px;
        margin: 20px 0;
    }

    .booking-detail {
        padding: 0;
        background: none;
    }

    .list-item {
        margin: 0;
        padding: 12px 0;
        border-bottom: 1px solid $border-color;
        display: flex;
        justify-content: space-between;
    }

    .book-another {
        margin: 40px 0 30px;
    }

    .flight-detail-wrapper {
        border: 1px solid #D8EBEF;
    }
}