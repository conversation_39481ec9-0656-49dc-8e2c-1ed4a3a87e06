.spinner {
  margin: 0px auto 0 auto;
  width: 20px;
  height: 20px;
  text-align: center;
  display: inline-block;
}

.spinner>div {
  background-color: var(--dark-bg-color);
  height: 100%;
  margin-right: 2px;
  width: 2px;
  display: inline-block;
  margin: 0 2px;
  -webkit-animation: sk-stretchdelay .8s infinite ease-in-out;
  animation: sk-stretchdelay 0.8s infinite ease-in-out;
}

.spinner .rect1 {
  -webkit-animation-delay: -1.2s;
  animation-delay: -1.2s;
}

.spinner .rect2 {
  -webkit-animation-delay: -.8s;
  animation-delay: -.8s;
}

.spinner12 {
  margin: 0px auto 0 auto;
  width: 80px;
  height: 40px;
  text-align: center;
}

.spinner12>div {
  background-color: #fff;
  height: 100%;
  width: 2px;
  margin: 0 2px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay .8s infinite ease-in-out;
  animation: sk-stretchdelay 0.8s infinite ease-in-out;
}


.spinner12 .rect1 {
  -webkit-animation-delay: -1.2s;
  animation-delay: -1.2s;
}

.spinner12 .rect2 {
  -webkit-animation-delay: -.8s;
  animation-delay: -.8s;
}

@-webkit-keyframes sk-stretchdelay {

  0%,
  80%,
  100% {
    -webkit-transform: scaleY(0.4);
    opacity: 1
  }

  40% {
    -webkit-transform: scaleY(1.0);
    opacity: .4
  }
}

@keyframes sk-stretchdelay {

  0%,
  80%,
  100% {
    transform: scaleY(0.4);
    -webkit-transform: scaleY(0.4);
    opacity: 1
  }

  40% {
    transform: scaleY(1.0);
    -webkit-transform: scaleY(1.0);
    opacity: .4
  }
}