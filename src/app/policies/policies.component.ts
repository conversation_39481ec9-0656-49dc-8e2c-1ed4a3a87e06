import { Component, OnInit, ViewEncapsulation, Output, EventEmitter } from '@angular/core';
import { UserAccountService } from '../user-account.service';
import { AbstractControl, UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators, FormControl, Form, ValidationErrors, ValidatorFn } from '@angular/forms';
import { Subscription } from 'rxjs';
import { UserAccountInfo } from '../entity/user-account-info';
import { AdminPanelService, CompanySettings } from '../admin-panel.service';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtils } from '../util/common-utils';
import { BookingService } from '../booking.service';
import { Constants } from '../util/constants';
import { countries } from '../util/countries';
import { Currency } from '../util/currency';
import { SearchService } from '../search.service';
@Component({
    selector: 'app-policies',
    templateUrl: './policies.component.html',
    styleUrls: ['./policies.component.scss'],
    standalone: false
})
export class PoliciesComponent implements OnInit {
  private userAccountInfoObj: UserAccountInfo;
  @Output() goBackPolicyEmitter = new EventEmitter();
  bsModalRef: BsModalRef;
  viewMode1 = 'tab11';
  dollar = false;
  countries = countries;
  applyBtn = false;
  currencyList = Currency;
  cardButtonStyle=true;
  addDiemPolicyProgress=false;
  maxLength=2;
  flightClass: string;
  flightClass1: string;
  index: number;
  percentage = false;
  hotelStar = 0;
  maxClass = false;
  perDiemPolicyList=[];
  deletepolicy = false;
  approvalProcessEnabled = false;
  travelPurposeMandatory= false;
  approvalRequiredFor = "";
  price = false;
  percentagePrice = false;
  showAllPolicies = false;
  duration = false;
  viewMode2 = 'tab21'
  dollar1 = false;
  percentage1 = false;
  refundable = false;
  duration1 = false;
  maxClass1 = false;
  price1 = false;
  percentagePrice1 = false;
  dollar2 = false;
  percentage2 = false;
  maxClass2 = false;
  price2 = false;
  percentagePrice2 = false;
  dollar3 = false;
  percentage3 = false;
  automaticallyReimburse=false
  maxClass3 = false;
  price3 = false;
  paymenntType = false;
  percentagePrice3 = false;
  url = "assets/images/check.png";
  policy = '';
  addPolicy = true;
  resultErrorMessage = 'Fetching policy';
  policyOptions = [{ value: 'Select', id: '' }];
  isDefaultCheckBoxState = false;
  alternatePolicyOptions = [{ value: 'Select', id: '' }];
  saveFlightSetting = false;
  saveHotelSetting = false;
  saveCarSetting = false;
  saveTrainSetting = false;
  approvalOptions = [{ Name: 'policy.Onlyforoutofpolicytravelbookings', value: 'OUT_OF_POLICY_BOOKING' }, { Name: 'policy.Foralltravelbookings', value: 'ALL_BOOKINGS' },];
  classOptions = [{ Name: 'policy.Notdefined', value: 'NOT_DEFINED' }, { Name: 'policy.EconomyBaseandStandardfares', value: 'ECONOMY' },
  { Name: 'policy.PremiumEconomyEnhancedandPremiumfares', value: 'PREMIUM_ECONOMY' }, { Name: 'policy.BusinessPremiumfares', value: 'BUSINESS' }];
  upperClassOptions = [{ Name: 'policy.Always', value: 'ALWAYS' },
  { Name: 'policy.BasedOnFlightDurationexcludinglayover', value: 'BASED_ON_FLIGHT_DURATION' }, { Name: 'policy.Asperpricerestrictionssetbelow', value: 'AS_PER_PRICE_CAPS' }]
  carClassOptions = [{ Name: 'policy.Notdefined', value: 'NOT_DEFINED' }, { Name: 'policy.MiniEconomyorCompact', value: 'Economy' }, { Name: 'policy.Intermediate', value: 'Intermediate' }, { Name: 'policy.Standard', value: 'Standard' }, { Name: 'policy.FullSize', value: 'Fullsize' }, { Name: 'policy.Premium', value: 'Premium' }, { Name: 'policy.Luxury', value: 'Luxury' }];
  trainClassOptions = [{ Name: 'policy.Notdefined', value: 'NOT_DEFINED' }, { Name: 'policy.Coach', value: 'Coach' }, { Name: 'policy.Business', value: 'Business' }, { Name: 'First', value: 'First' }];
  starOptions = [{ Name: 'Not defined', value: 'NOT_DEFINED' }, { Name: '2 stars', value: 2.0 }, { Name: '3 stars', value: 3.0 }, { Name: '4 stars', value: 4.0 }, { Name: '5 stars', value: 5.0 }];
  paymentOption = [{ name: 'prepaid', value: false, label: 'policy.Prepaidroomsonly' }, { name: 'postpaid', value: false, label: 'policy.Postpaidroomsonly' }];
  refundOption = [{ name: 'refundable', value: 'Refundable' }, { name: 'any', value: 'Any' }];
  basicEconomy = [{ Name: 'policy.Yes', value: 'yes' }, { Name: 'policy.No', value: 'no' }];
  policyNameForm: UntypedFormGroup;
  diemPolicyForm:UntypedFormGroup;
  internatinalFlightForm: UntypedFormGroup;
  policyForm: UntypedFormGroup;
  hotelPolicyForm: UntypedFormGroup;
  carPolicyForm: UntypedFormGroup;
  trainPolicyForm: UntypedFormGroup;
  item = [];
  defaultPolicy: any;
  companySettingsSubscription: Subscription;
  item1 = [];
  companyId: number;
  flightPolicy = {};
  internationalFlightPolicy =
    {
      'internatinalClas': 'BUSINESS',
      'internatinalUpperClas': 'ALWAYS',
      'internatinalConsiderBasicEconomy': false,
      'internatinalPrice': 2000,
      'internatinalTerms1': 'internatinalCheapPercentagePrice',
      'internatinalCheapPricePercentage': 40

    };
  carPolicy = {};
  trainPolicy = {};
  hotelPolicy = {};
  policyDetail = {};
  
  previousPolicyDetail = {};
  private selectedDepartment = null;
  selectedDepartmentPolicy = null;
  private toBeActedDepartmentPolicy = null;
  departmentPolicyList = [];
  private fetchAccountInfoSubscription: Subscription;
  companySettings: CompanySettings;
  currency = 'USD';
  showPolicyDetailForm = false;
  addNewPolicy = false;
  editPolicy = false;
  allowedCarTypesFormArray: UntypedFormArray;
  carTypeOptions = [{ val: 'Car', checked: false }, { val: 'Pickup', checked: false }, { val: 'SUV', checked: false }, { val: 'Van', checked: false }, { val: 'Convertible', checked: false }];
  constructor(private fb: UntypedFormBuilder,
    private userAccountInfoService: UserAccountService,
    private adminPanelService: AdminPanelService,
    public translateService: TranslateService,
    private bookingService: BookingService,
    public searchService: SearchService,
    private modalService: BsModalService,
    private adminService: AdminPanelService, private toastr: ToastrService,) { }

  onAddPolicy() {
    //    this.selectedDepartmentPolicy.default = false;
    this.addPolicy = false;
    this.showPolicyDetailForm = true;
    this.addNewPolicy = true;
    this.toBeActedDepartmentPolicy = null;
    // return this.companySettings.departmentPolicies.find(obj=> obj.default);
    let defaultCompanyPolicy = JSON.parse(JSON.stringify(this.departmentPolicyList.find(obj => obj.default)));
    this.policyFormInit(defaultCompanyPolicy.policy, '', false);
  }

  onEditPolicy(index) {
    this.showPolicyDetailForm = true;
    this.editPolicy = true;
    this.addPolicy = false;
    // return this.companySettings.departmentPolicies.find(obj=> obj.default);

    this.toBeActedDepartmentPolicy = JSON.parse(JSON.stringify(this.departmentPolicyList[index]));
    this.departmentPolicySelected(this.departmentPolicyList[index], this.index);
    this.policyFormInit(this.toBeActedDepartmentPolicy.policy, this.toBeActedDepartmentPolicy.policyName,
      this.toBeActedDepartmentPolicy.default);
  }
  searchByAirlineCode(term: string, item: any) {
    term = term.toLowerCase();
    return (item.code && item.code.toLowerCase().indexOf(term) > -1 || item.name && item.name.toLowerCase().indexOf(term) > -1);
  }
  openAddDestinationodal(modal){
    this.diemPolicyForm = this.fb.group({
      destination: ['', Validators.compose([Validators.required])],
      amount : ['', Validators.compose([Validators.required])],
    }); 
    this.bsModalRef = this.modalService.show(modal);
  }
  onDiemModelCancel1(){
    if(this.addDiemPolicyProgress){
      return;
    }
    this.bsModalRef.hide();
  }
  addDiemPolicy(){
    if(this.diemPolicyForm.invalid){
      this.diemPolicyForm.markAllAsTouched();
      return;
    }
    let diemPolicy={};
    diemPolicy['countryCode'] = this.diemPolicyForm.controls['destination'].value;
    diemPolicy['amount'] = this.diemPolicyForm.controls['amount'].value;
    diemPolicy['currency'] = this.currencyList[0][this.diemPolicyForm.controls['destination'].value];
    this.perDiemPolicyList.push(diemPolicy);
    this.addDiemPolicyProgress=true;
    this.savePolicy();
  }
  
  confirmDeleteDiemPolicy(item2){
    this.perDiemPolicyList = this.perDiemPolicyList.filter(item=>item.currency!==item2.currency);
    this.savePolicy();
  }
  getLabelValueUatp(){
    if (this.diemPolicyForm.controls['destination'].value !=='') {
      let findValue = this.countries.findIndex(item => item.code === this.diemPolicyForm.controls['destination'].value);
      let cardString= this.countries[findValue].name;
      return  cardString;
    } else  {
      return this.translateService.instant('policy.SelectCountry');
    } 
  }
  currencyStyle(item,item2){
    if(item){
    if (item2 && item2==='USD') {
      return { 'color': '#000000', 'border-color': this.searchService.darkBgColor,'padding-left':'18px' };
    } else {
      return { 'color': '#000000', 'border-color': this.searchService.darkBgColor ,'padding-left':'0px'}
    }
  }else{
    if (item2 && item2==='USD') {
      return { 'color': 'gray', 'border-color': '#E7E6E4','padding-left':'18px' };
    } else {
      return { 'color': 'gray', 'border-color': '#E7E6E4' ,'padding-left':'0px'}
    }
  }
  }
  changeStyle(item) {
    if (item) {
      return { 'color': '#000000', 'border-color': this.searchService.darkBgColor };
    } else {
      return { 'color': 'gray', 'border-color': '#E7E6E4' }
    }
  }
  onAddCancel() {
    if (this.addNewPolicy) {
      if (this.defaultPolicy.policyId === this.selectedDepartmentPolicy.policyId) {
        this.selectedDepartmentPolicy.default = true;
      }
      this.addPolicy = true;
      this.addNewPolicy = false;
    }
    this.editPolicy = false;
  }
  public handleStarFilterClick(isRatingAny, isRated) {

    if (this.maxClass3) {
      this.hotelPolicyForm.controls['Star'].setValue(isRated);
      this.hotelStar = isRated;
    }

  }
  public getStarFilterCheckStatus(isRated, isRatingAny) {
    if (this.maxClass3) {
      return (this.hotelPolicyForm.controls['Star'].value === isRated)
    }
  }
  departmentPolicySelected(target, index) {
    // alert(target);
    this.index = index;
    this.policySaved =false;
    this.selectedDepartmentPolicy = target;
    this.toBeActedDepartmentPolicy = target;
    this.policyFormInit(this.selectedDepartmentPolicy.policy, this.selectedDepartmentPolicy.policyName,
      this.selectedDepartmentPolicy.default);
      
    this.showPolicyDetailForm = true;
  }
  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }
  getDepartmentOptions() {
    let departmentOptions = [{ "departmentId": -1, "name": "SELECT" }];
    if (this.adminService.getDepartments()) departmentOptions = this.adminService.getDepartments();
    // for (let option of this.adminService.getDepartments()) {
    //   departmentOptions.push(option);
    // }
    return departmentOptions;
  }
  getSelectedDepartmentName() {
    if (this.getDepartmentOptions() && this.selectedDepartment) {
      let departmentObj = this.getDepartmentOptions().find(obj => obj.departmentId === this.selectedDepartment.departmentId);
      if (departmentObj) {
        return departmentObj.name;
      }
    }
    return "SELECT";
  }
  getCountryName(code){
    let country  = this.countries.find(item=> item.code===code);
    if(country && country){
      return country.name;
    }
  }
 
  getSelectedPolicyName() {
    if (this.selectedDepartmentPolicy) {
      return this.selectedDepartmentPolicy.policyName;
    }
    return "SELECT";
  }
  getUrl(option) {
    if (option.default) {
      this.url = "assets/images/check.png";
    } else {
      this.url = "assets/images/check-mark.png";
    }
    return this.url;
  }
  ngOnInit() {
    this.applyBtn = true;
    this.getDepartmentPolicyList();
    this.subscription();

  }

  subscription() {
    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {
        this.companySettings = settings;
      //  this.showAllPolicies=false;
        this.currency = this.companySettings.company.currency;
      }
    });
  
    this.userAccountInfoService.userAccountInitObjObserver$.subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.userAccountInfoService.initResponseReceived = true;
        if (this.userAccountInfoService.userIsDeptAdmin) {
          if ((this.userAccountInfoService.onBoardingTask.indexOf('policy') === -1)) {
            this.goBackPolicyEmitter.emit(true);
          }
        }
      }
    });
  }
  policyFormInit(policyDetail, policyName, isDefault) {
    this.isDefaultCheckBoxState = isDefault;
    if (this.addNewPolicy || this.editPolicy || this.showAllPolicies) {
      this.policyNameForm = this.fb.group({
        name: [policyName, Validators.compose([Validators.required,Validators.pattern(Constants.RGEX_POLICYNAME)])]
      });
    }
   
    this.policyForm = this.fb.group({

      Clas: ['', Validators.compose([Validators.required])],
      upperClas: ['',],
      duration: [1],
      price: [1],
      terms1: [false],
      cheapPricePercentage: [1],
      cheapPrice: [1],
      considerBasicEconomy: [true, Validators.compose([Validators.required])]
    });
    this.internatinalFlightForm = this.fb.group({

      internatinalClas: ['NOT_DEFINED', Validators.compose([Validators.required])],
      internatinalUpperClas: ['',],
      internatinalDuration: [1],
      internatinalPrice: [1],
      internatinalTerms1: [false],
      internatinalCheapPricePercentage: [0],
      internatinalCheapPrice: [0],
      internatinalConsiderBasicEconomy: [true, Validators.compose([Validators.required])]
    });
    this.hotelPolicyForm = this.fb.group({
      Star: ['', Validators.compose([Validators.required])],
      paymentType: [''],
      guranteeType: [''],
      roomPrice: [1],
      roomTerms1: [false],
      cheapRoomPricePercentage: [1],
      cheapRoomPrice: [1]
    });
    this.allowedCarTypesFormArray = this.fb.array([]);
    this.carPolicyForm = this.fb.group({
      carClass: ['', Validators.compose([Validators.required])],
      carRentalPrice: [1],
      carRentalTerms1: [false],
      carRentalPricePercentage: [1],
      cheapCarRentalPrice: [1],
      allowedCarTypes: this.allowedCarTypesFormArray
    });
    for (const allowedCarTypeOptionVar of this.carTypeOptions) {
      this.allowedCarTypesFormArray
        .push(this.fb.group({ allowedCarTypeVal: [] }));
    }
    this.trainPolicyForm = this.fb.group({
      trainClass: ['', Validators.compose([Validators.required])],
      trainTerm: [null, Validators.compose([Validators.required])],
      trainPrice: ['', Validators.compose([Validators.min(0)])],
      trainTerms1: ['', Validators.compose([Validators.required])],
      trainPricePercentage: [0, Validators.compose([Validators.required, Validators.min(0)])],
      cheapTrainPrice: [0, Validators.compose([Validators.required, Validators.min(0)])]
    });
    this.getPolicyDetails(policyDetail);

  }

  getDepartmentPolicyList() {
    this.adminService.getDepartmentPolicies("-1").subscribe(resp => {
      if (resp.success === true && resp.data) {
        let inputList = JSON.parse(JSON.stringify(resp.data));
        this.applyBtn = false;

        this.defaultPolicy = inputList.find(obj => obj.default);
        if (!this.defaultPolicy) {
          this.defaultPolicy = inputList[0];
          this.defaultPolicy.default = true;
        }
        if (!this.selectedDepartmentPolicy) {
          this.departmentPolicySelected(this.defaultPolicy, this.index)
        }
        this.policyOptions = [];
        for (let policyOption of inputList) {
          this.policyOptions.push({ value: policyOption.policyName, id: '' + policyOption.policyId });
        }
        this.departmentPolicyList = inputList;
        if(this.adminPanelService.addEventpolicy){
          window.scrollTo(0, 0);
          this.showAllPolicies =true;
          this.onAddPolicy();
          this.adminPanelService.addEventpolicy=false;
        }
      }
    });
  }
  getPolicyName(policyId) {
    if (!policyId || policyId == -1 || policyId == 0) {
      return this.adminPanelService.getDefaultPolicyName();
    } else {
      return this.adminPanelService.getDepartmentPolicyName(policyId);
    }
  }
  getPolicyDetails(policyDetail) {
    if (policyDetail) {
      this.policyDetail = JSON.parse(JSON.stringify(policyDetail));
     
     // 
      this.buildFormData(this.policyDetail);
      this.policyForm.patchValue({ ...this.flightPolicy });
      this.hotelPolicyForm.patchValue({ ...this.hotelPolicy });
      this.carPolicyForm.patchValue({ ...this.carPolicy });
      this.trainPolicyForm.patchValue({ ...this.trainPolicy });
      this.internatinalFlightForm.patchValue({ ...this.internationalFlightPolicy });
      this.saveFlightPolicy();
      this.saveInternationalPolicy();
      this.saveHotelPolicy();
      this.saveCarPolicy();
      this.saveApprovalPolicy();
      this.previousPolicyDetail = JSON.parse(JSON.stringify(this.policyDetail));
    }
  }

  canUserAddEditPolicies() {
    return this.userAccountInfoService.canUserAddEditPolicies();
  }
  alternatePolicy = null;
  alternatePolicyList = null;
  alternatePolicySelected(event) {
    this.alternatePolicy = event;
  }
  getAlternatePolicyName() {
    return this.alternatePolicy.value;
  }
  onDeletePolicy(Modal, index,modal2?) {
    if (index > -1) {
      this.alternatePolicy = [];
      this.toBeActedDepartmentPolicy = this.departmentPolicyList[index];
      this.alternatePolicyList = this.departmentPolicyList.filter(policy => policy.policyId != this.toBeActedDepartmentPolicy.policyId);
      this.alternatePolicyOptions = [];
      //let defaultCompanyPolicy = JSON.parse(JSON.stringify(this.alternatePolicyList.find(obj=> obj.default)));
      for (let policyOption of this.alternatePolicyList) {
        this.alternatePolicyOptions.push({ value: policyOption.policyName, id: '' + policyOption.policyId });
      }
      var defaultPolicy = [];
      defaultPolicy.push({ value: this.defaultPolicy.policyName, id: '' + this.defaultPolicy.policyId });
      this.alternatePolicy = defaultPolicy[0];
    }
    if(this.getNumberOfAffectedEmployeesByPolicy() ===0 && modal2){
      if (this.bsModalRef) this.bsModalRef.hide();
      this.bsModalRef = this.modalService.show(modal2, {
        initialState: {
        }, backdrop: true, keyboard: false, ignoreBackdropClick: false
      });
    }else{
    if (this.bsModalRef) this.bsModalRef.hide();
    this.bsModalRef = this.modalService.show(Modal, {
      initialState: {
      }, backdrop: true, keyboard: false, ignoreBackdropClick: false
    });
  }
  }

  onModelCancel() {
    this.bsModalRef.hide();
  }

  deletePolicyConfirmed() {
    let replacingPolicyId = "";
    this.deletepolicy = true;
    //if (this.adminService.getNumberOfEmployeesWithPolicy(this.toBeActedDepartmentPolicy.policyId) > 0) {
      replacingPolicyId = this.alternatePolicy.id;
   // }
    this.adminService.deleteCompanyPolicyRequest("-1", this.toBeActedDepartmentPolicy.policyId, replacingPolicyId).subscribe(resp => {
      // this.bsModalRef.hide();
      if (resp.success === true && resp.data) {
        this.deletepolicy = false;
        if(this.bsModalRef){
        this.bsModalRef.hide();
        }
        this.saveFlightSetting = false;
        this.saveHotelSetting = false;
        this.saveCarSetting = false;
        this.saveTrainSetting = false;
        this.addNewPolicy = false;
        this.toastr.success(this.translateService.instant('policy.Policydeletedsuccessfully'))
        if (this.selectedDepartmentPolicy.policyId === this.toBeActedDepartmentPolicy.policyId) {
          this.selectedDepartmentPolicy = null;
          //this.defaultPolicy = null;
        }
        this.getDepartmentPolicyList();
        this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
      } else {
        this.saveFlightSetting = false;
        this.saveHotelSetting = false;
        this.deletepolicy = false;
        this.bsModalRef.hide();
        this.saveCarSetting = false;
        this.saveTrainSetting = false;
        this.addNewPolicy = false;
      }
    });
  }


  showAddEditPolicyForm() {
    return (this.addNewPolicy || this.editPolicy || this.showAllPolicies);
  }
  handleAddEditPolicyResponse(resp) {
    if (resp.success === true && resp.data) {
      this.saveFlightSetting = false;
      if(this.bsModalRef){
        this.bsModalRef.hide();
      }
      this.addDiemPolicyProgress =false;
      this.saveHotelSetting = false;
      this.saveCarSetting = false;
       this.policySaved =true;
      this.saveTrainSetting = false;
      this.addPolicy = true;
      let departmentPolicy = JSON.parse(JSON.stringify(resp.data));
      this.departmentPolicyList.push(departmentPolicy);
      this.policyDetail = departmentPolicy.policy;
      this.selectedDepartmentPolicy = departmentPolicy;
      if (this.addNewPolicy) {
        this.toastr.success(this.translateService.instant('policy.Policyaddedsuccessfully'))
        this.addNewPolicy = false;
        this.toBeActedDepartmentPolicy = departmentPolicy;
        this.showAllPolicies = true;
      } else if (this.showAllPolicies) {
        this.toBeActedDepartmentPolicy = departmentPolicy;
        this.toastr.success(this.translateService.instant('policy.Policyupdatedsuccessfully'))
      }
      this.buildFormData(this.policyDetail);
      this.policyForm.patchValue({ ...this.flightPolicy });
      this.hotelPolicyForm.patchValue({ ...this.hotelPolicy });
      this.carPolicyForm.patchValue({ ...this.carPolicy });
      this.trainPolicyForm.patchValue({ ...this.trainPolicy });
      this.internatinalFlightForm.patchValue({ ...this.internationalFlightPolicy });
      this.getDepartmentPolicyList();
      this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
    } else {
      this.saveFlightSetting = false;
      if(this.bsModalRef){
        this.bsModalRef.hide();
      }
      this.addDiemPolicyProgress =false;
      this.saveHotelSetting = false;
      this.saveCarSetting = false;
      this.saveTrainSetting = false;
      this.addNewPolicy = false;
    }
  }
  flightBasicEconomy(type) {
    this.policyForm.controls['considerBasicEconomy'].setValue(type);
  }
  interFlightBasicEconomy(type) {
    this.internatinalFlightForm.controls['internatinalConsiderBasicEconomy'].setValue(type);
  }
  policySaved =false;
  processSavePolicyRequest() {
    if (this.addNewPolicy || this.editPolicy || this.showAllPolicies) {
      let policyName = this.policyNameForm.controls['name'].value;
      let errMessage = undefined;
      if (!policyName || policyName.trim().length == 0) {
        errMessage = this.translateService.instant("policy.Pleaseenterpolicyname");
      }
      let allowEditWithSameName = false;
      if (this.toBeActedDepartmentPolicy) {
        allowEditWithSameName = policyName.toLowerCase() == this.toBeActedDepartmentPolicy.policyName.toLowerCase();
      }
      let sameNamePolicy = this.departmentPolicyList.find(obj => obj.policyName.toLowerCase() == policyName.toLowerCase())
      if (!allowEditWithSameName && sameNamePolicy) {
        errMessage = this.translateService.instant("policy.Policywithsamenamealreadyexists");
      }

      if (errMessage) {
        this.saveFlightSetting = false;
        this.saveHotelSetting = false;
        this.saveCarSetting = false;
        this.saveTrainSetting = false;
        this.toastr.error(errMessage);
        return;
      }
      if (this.addNewPolicy) {
        this.adminService.addCompanyPolicyRequest(this.policyDetail, '-1', policyName, this.isDefaultCheckBoxState).subscribe(resp => {
          this.handleAddEditPolicyResponse(resp);
        });
      }
      else if (this.showAllPolicies) {
        this.adminService.saveCompanyPolicyRequest(policyName, this.policyDetail, '-1',
          this.toBeActedDepartmentPolicy.policyId, this.isDefaultCheckBoxState).subscribe(resp => {
            this.handleAddEditPolicyResponse(resp);
          });
      }


    } else {
      this.adminService.saveCompanyPolicyRequest(this.selectedDepartmentPolicy.policyName, this.policyDetail, '-1',
        this.selectedDepartmentPolicy.policyId, this.selectedDepartmentPolicy.default).subscribe(resp => {
          if (resp.success === true && resp.data) {
            this.saveFlightSetting = false;
            this.saveHotelSetting = false;
            this.saveCarSetting = false;
            this.policySaved =true;
            this.saveTrainSetting = false;
            let departmentPolicy = JSON.parse(JSON.stringify(resp.data));
            //this.departmentPolicyList.push(departmentPolicy);
            // this.policyDetail =departmentPolicy.policy;
            this.selectedDepartmentPolicy = departmentPolicy;
            this.policyDetail = JSON.parse(JSON.stringify(resp.data)).policy;
            this.toastr.success(this.translateService.instant('policy.Policysavedsuccessfully'))
            this.buildFormData(this.policyDetail);
            this.policyForm.patchValue({ ...this.flightPolicy });
            this.hotelPolicyForm.patchValue({ ...this.hotelPolicy });
            this.carPolicyForm.patchValue({ ...this.carPolicy });
            this.trainPolicyForm.patchValue({ ...this.trainPolicy });
            this.internatinalFlightForm.patchValue({ ...this.internationalFlightPolicy });
            this.getDepartmentPolicyList();
            this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
          } else {
            this.saveFlightSetting = false;
            this.saveHotelSetting = false;
            this.saveCarSetting = false;
            this.saveTrainSetting = false;
          }
        });
    }

  }
  onModelCancel1(){
    this.bsModalRef.hide();
    this.showAllPolicies = false;
    this.addNewPolicy = false;
  }
  backTolist(modal) {
    let equal;
    this.saveFlightPolicy();
      this.saveInternationalPolicy();
      this.saveHotelPolicy();
      this.saveCarPolicy();
      this.saveApprovalPolicy();
      
    equal = this.bookingService.equals(this.policyDetail, this.previousPolicyDetail);
    if(!equal && !this.policySaved){
  this.bsModalRef = this.modalService.show(modal, {
    initialState: {
    }, backdrop: true, ignoreBackdropClick: true
  });
    }else{
    this.showAllPolicies = false;
    this.addNewPolicy = false;
    }
  }
  buildFormData(policy) {
    this.approvalProcessEnabled = policy.approvalProcessEnabled;
    this.travelPurposeMandatory = policy.travelPurposeMandatory;
    this.approvalRequiredFor = policy.approvalRequiredFor;
    if (policy.flight) {
      if (policy.flight.maxFlightClassAllowed) {
        this.flightPolicy['Clas'] = policy.flight.maxFlightClassAllowed;
        this.policyForm.get('Clas').enable();
        this.flightClass = policy.flight.maxFlightClassAllowed;
        this.maxClass = true;
      } else {
        this.flightPolicy['Clas'] = 'NOT_DEFINED';
        this.flightClass = 'BUSINESS'
        this.policyForm.get('Clas').disable();
        this.maxClass = false;
      }
      this.flightPolicy['upperClas'] = policy.flight.upperClassReason;
      if (this.flightPolicy['upperClas'] === 'BASED_ON_FLIGHT_DURATION') {
        this.policyForm.controls['duration'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
        this.policyForm.controls['duration'].updateValueAndValidity();
        this.duration = true;
      } else {
        this.policyForm.controls['duration'].setValidators(null);
        this.policyForm.controls['duration'].updateValueAndValidity();
        this.duration = false;
      }
      this.flightPolicy['duration'] = (policy.flight.flightDurationForUpperClass > 0) ? policy.flight.flightDurationForUpperClass : 1;
      if (policy.flight.considerBasicEconomy) {
        this.flightPolicy['considerBasicEconomy'] = false;
      } else {
        this.flightPolicy['considerBasicEconomy'] = true;
      }
      if (policy.flight.pricePolicyList.length > 0) {
        for (let item of policy.flight.pricePolicyList) {
          if (policy.flight.pricePolicyList && policy.flight.pricePolicyList.length > 0 && policy.flight.pricePolicyList.findIndex(item => item.type === 'markup') !== -1) {
            this.flightPolicy['price'] = policy.flight.pricePolicyList[0].benchMark;
            this.price = true;
            this.policyForm.controls['price'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.policyForm.controls['price'].updateValueAndValidity();
          } else {
            this.price = false;
          }
          if (item.type == 'percent_markup_over_cheapest' && item.benchMark > 0) {
            this.flightPolicy['terms1'] = 'cheapPercentagePrice';
            this.flightPolicy['cheapPricePercentage'] = item.benchMark;
            this.dollar = false;
            this.percentage = true;
            this.percentagePrice = true;
            this.policyForm.controls['cheapPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.policyForm.controls['cheapPricePercentage'].updateValueAndValidity();
          }
          else if (item.type == 'markup_over_cheapest' && item.benchMark > 0) {
            this.flightPolicy['terms1'] = 'cheapPrice';
            this.dollar = true;
            this.percentage = false;
            this.percentagePrice = true;
            this.flightPolicy['cheapPrice'] = item.benchMark;
            this.flightPolicy['cheapPricePercentage'] = item.benchMark;
            this.policyForm.controls['cheapPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.policyForm.controls['cheapPricePercentage'].updateValueAndValidity();
          } else {
            this.flightPolicy['terms1'] = '';
            this.dollar = false;
            this.percentage = false;
            this.percentagePrice = false;
            this.flightPolicy['cheapPrice'] = 1;
            this.flightPolicy['cheapPricePercentage'] = 1;
            this.policyForm.controls['cheapPricePercentage'].setValidators(null);
            this.policyForm.controls['cheapPricePercentage'].updateValueAndValidity();
          }
        }
      } else {
        this.flightPolicy['terms1'] = '';
        this.flightPolicy['price'] = 1;
        this.policyForm.controls['price'].setValidators(null);
        this.policyForm.controls['price'].updateValueAndValidity();
        this.dollar = false;
        this.percentage = false;
        this.price = false;
        this.percentagePrice = false;
        this.policyForm.controls['cheapPricePercentage'].setValidators(null);
        this.policyForm.controls['cheapPricePercentage'].updateValueAndValidity();
        this.flightPolicy['cheapPrice'] = 1;
        this.flightPolicy['cheapPricePercentage'] = 1;
      }
    }
  if(policy.perDiemPolicy && policy.perDiemPolicy.items && policy.perDiemPolicy.items.length > 0){
    this.perDiemPolicyList = policy.perDiemPolicy.items;
    this.automaticallyReimburse = policy.perDiemPolicy.automaticallyReimburse

  }else{
    this.perDiemPolicyList =[];
  }
    if (policy.internationalFlight) {
      if (policy.internationalFlight.maxFlightClassAllowed) {
        this.internationalFlightPolicy['internatinalClas'] = policy.internationalFlight.maxFlightClassAllowed;
        this.maxClass1 = true;
        this.internatinalFlightForm.get('internatinalClas').enable();
        this.flightClass1 = policy.internationalFlight.maxFlightClassAllowed;
      } else {
        this.internationalFlightPolicy['internatinalClas'] = 'NOT_DEFINED';
        this.maxClass1 = false;
        this.flightClass1 = 'BUSINESS'
        this.internatinalFlightForm.get('internatinalClas').disable();
      }
      this.internationalFlightPolicy['internatinalUpperClas'] = policy.internationalFlight.upperClassReason;
      if (this.internationalFlightPolicy['internatinalUpperClas'] === 'BASED_ON_FLIGHT_DURATION') {
        this.duration1 = true;
        this.internatinalFlightForm.controls['internatinalDuration'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
        this.internatinalFlightForm.controls['internatinalDuration'].updateValueAndValidity();
      } else {
        this.duration1 = false;
        this.internatinalFlightForm.controls['internatinalDuration'].setValidators(null);
        this.internatinalFlightForm.controls['internatinalDuration'].updateValueAndValidity();
      }

      this.internationalFlightPolicy['internatinalDuration'] = (policy.internationalFlight.flightDurationForUpperClass > 0) ? policy.internationalFlight.flightDurationForUpperClass : 1;
      if (policy.internationalFlight.considerBasicEconomy) {
        this.internationalFlightPolicy['internatinalConsiderBasicEconomy'] = false;
      } else {
        this.internationalFlightPolicy['internatinalConsiderBasicEconomy'] = true;
      }
      if (policy.internationalFlight.pricePolicyList.length > 0) {
        for (let item of policy.internationalFlight.pricePolicyList) {
          if (policy.internationalFlight.pricePolicyList && policy.internationalFlight.pricePolicyList.length > 0 && policy.internationalFlight.pricePolicyList.findIndex(item => item.type === 'markup') !== -1) {
            this.internationalFlightPolicy['internatinalPrice'] = policy.internationalFlight.pricePolicyList[0].benchMark;
            this.price1 = true;
            this.internatinalFlightForm.controls['internatinalPrice'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.internatinalFlightForm.controls['internatinalPrice'].updateValueAndValidity();
          } else {
            this.price1 = false;
          }
          if (item.type == 'percent_markup_over_cheapest' && item.benchMark > 0) {
            this.internationalFlightPolicy['internatinalTerms1'] = 'internatinalCheapPercentagePrice';
            this.internationalFlightPolicy['internatinalCheapPricePercentage'] = item.benchMark;
            this.dollar1 = false;
            this.percentage1 = true;
            this.percentagePrice1 = true;
            this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].updateValueAndValidity();
          }
          else if (item.type == 'markup_over_cheapest' && item.benchMark > 0) {
            this.internationalFlightPolicy['internatinalTerms1'] = 'internatinalCheapPrice';
            this.internationalFlightPolicy['internatinalCheapPrice'] = item.benchMark;
            this.internationalFlightPolicy['internatinalCheapPricePercentage'] = item.benchMark;
            this.dollar1 = true;
            this.percentage1 = false;
            this.percentagePrice1 = true;
            this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].updateValueAndValidity();
          } else {
            this.dollar1 = false;
            this.percentage1 = false;
            this.percentagePrice1 = false;
            this.internationalFlightPolicy['internatinalTerms1'] = '';
            this.internationalFlightPolicy['internatinalCheapPrice'] = 1;
            this.internationalFlightPolicy['internatinalCheapPricePercentage'] = 1;
          }
        }
      } else {
        this.dollar1 = false;
        this.percentage1 = false;
        this.price1 = false;
        this.internatinalFlightForm.controls['internatinalPrice'].setValidators(null);
        this.internatinalFlightForm.controls['internatinalPrice'].updateValueAndValidity();
        this.internationalFlightPolicy['internatinalPrice'] = 1;
        this.percentagePrice1 = false;
        this.internationalFlightPolicy['internatinalTerms1'] = '';
        this.internationalFlightPolicy['internatinalCheapPrice'] = 1;
        this.internationalFlightPolicy['internatinalCheapPricePercentage'] = 1;
        this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].setValidators(null);
        this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].updateValueAndValidity();
      }
    }
    if (policy.hotel) {
      if (policy.hotel.maxHotelRatingAllowed < 10) {
        this.hotelPolicy['Star'] = policy.hotel.maxHotelRatingAllowed;
        this.hotelStar = policy.hotel.maxHotelRatingAllowed;
        this.maxClass3 = true;
      } else {
        this.hotelPolicy['Star'] = 0;
        this.maxClass3 = false;
        this.hotelStar = 4;
      }
      if (policy.hotel.paymentType && policy.hotel.paymentType.length) {
        if (policy.hotel.paymentType[0] === 'nonrefundable' || policy.hotel.paymentType.length > 1) {
          this.hotelPolicy['guranteeType'] = 'any';
          this.refundable = false;
        } else {
          this.hotelPolicy['guranteeType'] = 'refundable';
          this.refundable = true;
        }
      }
      if (policy.hotel.guaranteeType) {
        if (policy.hotel.guaranteeType.length > 1) {
          this.hotelPolicy['paymentType'] = 'any';
          this.paymenntType = false;
          this.hotelPolicyForm.controls['paymentType'].clearValidators();
          this.hotelPolicyForm.controls['paymentType'].updateValueAndValidity();
        } else if (policy.hotel.guaranteeType.length > 0 && policy.hotel.guaranteeType.length < 2) {
          this.hotelPolicy['paymentType'] = policy.hotel.guaranteeType[0];
          this.paymenntType = true;
          this.hotelPolicyForm.controls['paymentType'].updateValueAndValidity();
          this.hotelPolicyForm.controls['paymentType'].markAsTouched();
        } else {
          this.paymenntType = false;
          this.hotelPolicyForm.controls['paymentType'].clearValidators();
          this.hotelPolicyForm.controls['paymentType'].updateValueAndValidity();
        }
      }
      if (policy.hotel.pricePolicyList && policy.hotel.pricePolicyList.length > 0) {
        for (let item of policy.hotel.pricePolicyList) {
          if (policy.hotel.pricePolicyList && policy.hotel.pricePolicyList.length > 0 && policy.hotel.pricePolicyList.findIndex(item => item.type === 'markup') !== -1) {
            this.hotelPolicy['roomTerm'] = true;
            this.hotelPolicy['roomPrice'] = policy.hotel.pricePolicyList[0].benchMark;
            this.price3 = true;
            this.hotelPolicyForm.controls['roomPrice'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.hotelPolicyForm.controls['roomPrice'].updateValueAndValidity();
          } else {
            this.price3 = false;
          }
          if (item.type == 'percent_markup_over_cheapest' && item.benchMark > 0) {
            this.hotelPolicy['roomTerms1'] = 'cheapPercentagePrice';
            this.hotelPolicy['cheapRoomPricePercentage'] = item.benchMark;
            this.dollar3 = false;
            this.percentage3 = true;
            this.percentagePrice3 = true;
            this.hotelPolicyForm.controls['cheapRoomPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.hotelPolicyForm.controls['cheapRoomPricePercentage'].updateValueAndValidity();
          }
          else if (item.type == 'markup_over_cheapest' && item.benchMark > 0) {
            this.hotelPolicy['roomTerms1'] = 'cheapPrice';
            this.hotelPolicy['cheapRoomPrice'] = item.benchMark;
            this.hotelPolicy['cheapRoomPricePercentage'] = item.benchMark;
            this.dollar3 = true;
            this.percentage3 = false;
            this.percentagePrice3 = true;
            this.hotelPolicyForm.controls['cheapRoomPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.hotelPolicyForm.controls['cheapRoomPricePercentage'].updateValueAndValidity();
          } else {
            this.dollar3 = false;
            this.percentage3 = false;
            this.percentagePrice3 = false;
            this.hotelPolicy['roomTerms1'] = '';
            this.hotelPolicy['cheapRoomPricePercentage'] = 1;
            this.hotelPolicy['cheapRoomPrice'] = 1;
            this.hotelPolicyForm.controls['cheapRoomPricePercentage'].setValidators(null);
            this.hotelPolicyForm.controls['cheapRoomPricePercentage'].updateValueAndValidity();
          }
        }
      } else {
        this.dollar3 = false;
        this.price3 = false;
        this.hotelPolicyForm.controls['roomPrice'].setValidators(null);
        this.hotelPolicyForm.controls['roomPrice'].updateValueAndValidity();
        this.percentage3 = false;
        this.percentagePrice3 = false;
        this.hotelPolicy['roomPrice'] = 1;
        this.hotelPolicy['roomTerms1'] = '';
        this.hotelPolicy['cheapRoomPricePercentage'] = 1;
        this.hotelPolicy['cheapRoomPrice'] = 1;
        this.hotelPolicyForm.controls['cheapRoomPricePercentage'].setValidators(null);
        this.hotelPolicyForm.controls['cheapRoomPricePercentage'].updateValueAndValidity();
      }
    }
    if (policy.carPolicy) {
      if (policy.carPolicy.maxCarClassAllowed) {
        this.carPolicy['carClass'] = policy.carPolicy.maxCarClassAllowed;
        this.maxClass2 = true;
        this.carPolicyForm.get('carClass').enable();
      } else {
        this.carPolicy['carClass'] = 'NOT_DEFINED';
        this.maxClass2 = false;
        this.carPolicyForm.get('carClass').disable();
      }
      for (const allowedCarTypeOptionPlaceHolder of this.carTypeOptions) {
        allowedCarTypeOptionPlaceHolder.checked = false;
      }
      if (policy.carPolicy.allowedTypes && policy.carPolicy.allowedTypes.length > 0) {
        for (const incomingAllowedType of policy.carPolicy.allowedTypes) {
          let allowedCarTypeOption = this.carTypeOptions.find(item => {
            return item.val == incomingAllowedType;
          });
          if (allowedCarTypeOption) {
            allowedCarTypeOption.checked = true;
          }
        }
      }
      if (policy.carPolicy.pricePolicyList && policy.carPolicy.pricePolicyList.length > 0) {
        for (let item of policy.carPolicy.pricePolicyList) {
          if (policy.carPolicy.pricePolicyList && policy.carPolicy.pricePolicyList.length > 0 && policy.carPolicy.pricePolicyList.findIndex(item => item.type === 'markup') !== -1) {
            this.carPolicy['carRentalTerm'] = true;
            this.carPolicy['carRentalPrice'] = policy.carPolicy.pricePolicyList[0].benchMark;
            this.price2 = true;
            this.carPolicyForm.controls['carRentalPrice'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.carPolicyForm.controls['carRentalPrice'].updateValueAndValidity();
          } else {
            this.price2 = false;
          }
          if (item.type == 'percent_markup_over_cheapest' && item.benchMark > 0) {
            this.carPolicy['carRentalTerms1'] = 'cheapPercentagePrice';
            this.carPolicy['carRentalPricePercentage'] = item.benchMark;
            this.dollar2 = false;
            this.percentage2 = true;
            this.percentagePrice2 = true;
            this.carPolicyForm.controls['carRentalPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.carPolicyForm.controls['carRentalPricePercentage'].updateValueAndValidity();
          }
          else if (item.type == 'markup_over_cheapest' && item.benchMark > 0) {
            this.carPolicy['carRentalTerms1'] = 'cheapPrice';
            this.carPolicy['cheapCarRentalPrice'] = item.benchMark;
            this.carPolicy['carRentalPricePercentage'] = item.benchMark;
            this.dollar2 = true;
            this.percentage2 = false;
            this.percentagePrice2 = true;
            this.carPolicyForm.controls['carRentalPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
            this.carPolicyForm.controls['carRentalPricePercentage'].updateValueAndValidity();
          } else {
            this.dollar2 = false;
            this.percentage2 = false;
            this.percentagePrice2 = false;
            this.carPolicy['carRentalTerms1'] = '';
            this.carPolicy['carRentalPricePercentage'] = 1;
            this.carPolicy['cheapCarRentalPrice'] = 1;
          }
        }
      } else {
        this.dollar2 = false;
        this.price2 = false;
        this.carPolicyForm.controls['carRentalPrice'].setValidators(null);
        this.carPolicyForm.controls['carRentalPrice'].updateValueAndValidity();
        this.percentage2 = false;
        this.carPolicyForm.controls['carRentalPricePercentage'].setValidators(null);
        this.carPolicyForm.controls['carRentalPricePercentage'].updateValueAndValidity();
        this.carPolicy['carRentalPrice'] = 1;
        this.percentagePrice2 = false;
        this.carPolicy['carRentalTerms1'] = '';
        this.carPolicy['carRentalPricePercentage'] = 1;
        this.carPolicy['cheapCarRentalPrice'] = 1;
      }
    }
    if (policy.trainPolicy) {
      if (policy.trainPolicy.maxTrainClassAllowed) {
        this.trainPolicy['trainClass'] = policy.trainPolicy.maxTrainClassAllowed;
      } else {
        this.trainPolicy['trainClass'] = 'NOT_DEFINED';
      }
      if (policy.trainPolicy.pricePolicyList && policy.trainPolicy.pricePolicyList.length > 0) {
        for (let item of policy.trainPolicy.pricePolicyList) {
          if (item.type == 'markup') {
            this.trainPolicy['trainterm'] = true;
            this.trainPolicy['trainPrice'] = item.benchMark;
          }
          if (item.type == 'percent_markup_over_cheapest' && item.benchMark > 0) {
            this.trainPolicy['trainTerms1'] = 'cheapPercentagePrice';
            this.trainPolicy['trainPricePercentage'] = item.benchMark;
          }
          else if (item.type == 'markup_over_cheapest' && item.benchMark > 0) {
            this.trainPolicy['trainTerms1'] = 'cheapPrice';
            this.trainPolicy['cheapTrainPrice'] = item.benchMark;
          } else {
            this.trainPolicy['trainTerms1'] = '';
            this.trainPolicy['trainPricePercentage'] = 0;
            this.trainPolicy['cheapTrainPrice'] = 0;
          }
        }
      }
    }
  }
  trainTabClicked() {
    this.viewMode1 = 'tab16';
  }
  hotelTabClicked() {
    this.viewMode1 = 'tab12';
  }
  carTabClicked() {
    this.viewMode1 = 'tab13';
  }
  approvalTabClicked() {
    this.viewMode1 = 'tab15';
  }
  flightTabClicked() {
    this.viewMode1 = 'tab11';
  }
  flightTabClicked1() {
    this.viewMode1 = 'tab14';
  }
  interntionalTabClicked() {
    this.viewMode2 = 'tab22';
  }
  domesticTabClicked() {
    this.viewMode2 = 'tab21';
  }
  onPaymentTypeClicked(option) {
    this.hotelPolicyForm.get('paymentType').setValue(option);
  }


  flightUpperClassChange(selectedOption) {

    if (selectedOption.value == 'BUSINESS' || selectedOption.value == 'PREMIUM_ECONOMY') {
      if (!this.duration) {
        this.policyForm.controls['upperClas'].setValue('ALWAYS');
        this.policyForm.controls['upperClas'].setValidators(Validators.compose([Validators.required]));
      } else {
        this.policyForm.controls['upperClas'].setValue('BASED_ON_FLIGHT_DURATION');
        this.policyForm.controls['upperClas'].setValidators(Validators.compose([Validators.required]));
      }
    } else {
      this.policyForm.controls['upperClas'].setValidators(null);
      this.policyForm.controls['upperClas'].setValue(null);
      this.policyForm.controls['duration'].setValidators(null);
      this.policyForm.controls['duration'].setValue(null);
    }
  }
  flightUpperClassChange2(selectedOption) {

    if (selectedOption.value == 'BUSINESS' || selectedOption.value == 'PREMIUM_ECONOMY') {
      if (!this.duration1) {
        this.internatinalFlightForm.controls['internatinalUpperClas'].setValue('ALWAYS');
        this.internatinalFlightForm.controls['internatinalUpperClas'].setValidators(Validators.compose([Validators.required]));
      } else {
        this.internatinalFlightForm.controls['internatinalUpperClas'].setValue('BASED_ON_FLIGHT_DURATION');
        this.internatinalFlightForm.controls['internatinalUpperClas'].setValidators(Validators.compose([Validators.required]));
      }
    } else {
      this.internatinalFlightForm.controls['internatinalUpperClas'].setValidators(null);
      this.internatinalFlightForm.controls['internatinalUpperClas'].setValue(null);
      this.internatinalFlightForm.controls['duration1'].setValidators(null);
      this.internatinalFlightForm.controls['duration1'].setValue(null);
    }
  }
  savePolicyFromModal(){
    if(this.bsModalRef){
      this.bsModalRef.hide();
    }
    this.processSavePolicyRequest();
  }
  savePerDiemPolicy(){
    this.policyDetail['perDiemPolicy'] ={items:[],automaticallyReimburse:false}
    this.policyDetail['perDiemPolicy'] ['automaticallyReimburse'] =this.automaticallyReimburse;
    this.policyDetail['perDiemPolicy']['items'] = this.perDiemPolicyList;
    if(this.perDiemPolicyList && this.perDiemPolicyList.length === 0 ){
      this.policyDetail['perDiemPolicy'] =null
 }
    
  }
  changeReimbursePolicy(event){
    this.automaticallyReimburse = event;
  }
  savePolicy() {
    //if (this.viewMode1 === 'tab11') {
      this.saveFlightPolicy();
   // } else if (this.viewMode1 === 'tab14') {
      this.saveInternationalPolicy();
    //} else if (this.viewMode1 === 'tab12') {
      this.saveHotelPolicy();
    //} else if (this.viewMode1 === 'tab13') {
      this.saveCarPolicy();
    //} else if (this.viewMode1 === 'tab15') {
      this.saveApprovalPolicy();
      this.savePerDiemPolicy();
   // }
   this.processSavePolicyRequest();

  }
  validForm() {
    if(this.policyNameForm.invalid){
      return true;
    }
    if (this.viewMode1 === 'tab11') {
      if (this.policyForm.invalid) {
        return true;
      } else {
        return false;
      }
    } else if (this.viewMode1 === 'tab14') {
      if (this.internatinalFlightForm.invalid) {
        return true;
      } else {
        return false;
      }
    } else if (this.viewMode1 === 'tab12') {
      if (this.hotelPolicyForm.invalid) {
        return true;
      } else {
        return false;
      }
    } else if (this.viewMode1 === 'tab13') {
      if (this.carPolicyForm.invalid) {
        return true;
      } else {
        return false;
      }
    }
  }
  upperClassReasonChange2(event) {
    if (event) {
      this.internatinalFlightForm.controls['internatinalUpperClas'].setValue('BASED_ON_FLIGHT_DURATION');
      this.internatinalFlightForm.controls['internatinalDuration'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
      this.internatinalFlightForm.controls['internatinalDuration'].updateValueAndValidity();
      this.duration1 = true;
    } else {
      this.internatinalFlightForm.controls['internatinalUpperClas'].setValue('ALWAYS');
      this.duration1 = false;
      this.internatinalFlightForm.controls['internatinalDuration'].setValidators(null);
      this.internatinalFlightForm.controls['internatinalDuration'].updateValueAndValidity();

    }
  }
  pricePercChange(event) {
    if (event) {
      this.percentagePrice = true;
      this.policyForm.controls['cheapPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
      this.policyForm.controls['cheapPricePercentage'].updateValueAndValidity();
      if (!this.dollar) {
        this.percentage = true;
      }
    } else {
      this.percentagePrice = false;
      this.policyForm.controls['cheapPricePercentage'].setValidators(null);
      this.policyForm.controls['cheapPricePercentage'].updateValueAndValidity();
      this.percentage = false;
      this.dollar = false;
    }
  }
  approvalBehaviourChange(event) {
    this.approvalProcessEnabled = event;
    if (event) {
      this.approvalRequiredFor = 'OUT_OF_POLICY_BOOKING';
    }
  }
  travelPurposeMandatoryChange(event) {
    this.travelPurposeMandatory = event;
   
  }
  maxClassChange(event) {
    if (event) {
      this.maxClass = true;
      this.policyForm.get('Clas').enable();
      this.policyForm.controls['Clas'].setValue(this.flightClass);
    } else {
      this.maxClass = false;
      this.flightClass = this.policyForm.controls['Clas'].value;
      this.policyForm.get('Clas').disable();
      this.duration = false;
    }
  }
  priceChange(event) {
    if (event) {
      this.price = true;
      this.policyForm.controls['price'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
      this.policyForm.controls['price'].updateValueAndValidity();
    } else {
      this.price = false;
      this.policyForm.controls['price'].setValidators(null);
      this.policyForm.controls['price'].updateValueAndValidity();
    }
  }
  pricePercChange1(event) {
    if (event) {
      this.percentagePrice1 = true;
      this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
      this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].updateValueAndValidity();
      if (!this.dollar1) {
        this.percentage1 = true;
      }
    } else {
      this.percentagePrice1 = false;
      this.percentage1 = false;
      this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].setValidators(null);
      this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].updateValueAndValidity();
      this.dollar1 = false;
    }
  }

  maxClassChange1(event) {
    if (event) {
      this.maxClass1 = true;
      this.internatinalFlightForm.get('internatinalClas').enable();
      this.internatinalFlightForm.controls['internatinalClas'].setValue(this.flightClass1);
    } else {
      this.maxClass1 = false;
      this.duration1 = false;
      this.flightClass1 = this.internatinalFlightForm.controls['internatinalClas'].value;
      this.internatinalFlightForm.get('internatinalClas').disable();
    }
  }
  priceChange1(event) {
    if (event) {
      this.price1 = true;
      this.internatinalFlightForm.controls['internatinalPrice'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
      this.internatinalFlightForm.controls['internatinalPrice'].updateValueAndValidity();
    } else {
      this.price1 = false;
      this.internatinalFlightForm.controls['internatinalPrice'].setValidators(null);
      this.internatinalFlightForm.controls['internatinalPrice'].updateValueAndValidity();
    }
  }
  getToggleCheck(type) {
    if (!this.percentagePrice) {
      return;
    }
    if (type === 'dollar') {
      this.dollar = true;
      this.percentage = false;
    } else {
      this.dollar = false;
      this.percentage = true;
    }
  }
  pricePercChange2(event) {
    if (event) {
      this.percentagePrice2 = true;
      this.carPolicyForm.controls['carRentalPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
      this.carPolicyForm.controls['carRentalPricePercentage'].updateValueAndValidity();
      if (!this.dollar2) {
        this.percentage2 = true;
      }
    } else {
      this.percentagePrice2 = false;
      this.percentage2 = false;
      this.carPolicyForm.controls['carRentalPricePercentage'].setValidators(null);
      this.carPolicyForm.controls['carRentalPricePercentage'].updateValueAndValidity();
      this.dollar2 = false;
    }
  }

  maxClassChange2(event) {
    if (event) {
      this.maxClass2 = true;
      this.carPolicyForm.get('carClass').enable();
    } else {
      this.maxClass2 = false;
      this.carPolicyForm.get('carClass').disable();
      this.carPolicyForm.controls['carClass'].setValue('NOT_DEFINED');
    }
  }
  priceChange2(event) {
    if (event) {
      this.price2 = true;
      this.carPolicyForm.controls['carRentalPrice'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
      this.carPolicyForm.controls['carRentalPrice'].updateValueAndValidity();
    } else {
      this.price2 = false;
      this.carPolicyForm.controls['carRentalPrice'].setValidators(null);
      this.carPolicyForm.controls['carRentalPrice'].updateValueAndValidity();
    }
  }
  getToggleCheck2(type) {
    if (!this.percentagePrice2) {
      return;
    }
    if (type === 'dollar') {
      this.dollar2 = true;
      this.percentage2 = false;
    } else {
      this.dollar2 = false;
      this.percentage2 = true;
    }
  }
  getToggleCheck1(type) {
    if (!this.percentagePrice1) {
      return;
    }
    if (type === 'dollar') {
      this.dollar1 = true;
      this.percentage1 = false;
    } else {
      this.dollar1 = false;
      this.percentage1 = true;
    }
  }
  upperClassReasonChange(event) {
    if (event) {
      this.policyForm.controls['upperClas'].setValue('BASED_ON_FLIGHT_DURATION');
      this.policyForm.controls['duration'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
      this.policyForm.controls['duration'].updateValueAndValidity();
      this.duration = true;
    } else {
      this.policyForm.controls['upperClas'].setValue('ALWAYS');
      this.policyForm.controls['duration'].setValidators(null);
      this.policyForm.controls['duration'].updateValueAndValidity();
      this.duration = false;
    }
  }
  saveInternationalPolicy() {
    if (this.internatinalFlightForm.invalid) {
      return;
    }
    this.saveFlightSetting = true;
    let internationalFlightPolicyObj = {};
    internationalFlightPolicyObj['pricePolicyList'] = [];
    internationalFlightPolicyObj['internationalPricePolicyList'] = [];
    if (this.internatinalFlightForm.controls['internatinalClas'].value != null && this.maxClass1
      && this.internatinalFlightForm.controls['internatinalClas'].value != undefined && this.internatinalFlightForm.controls['internatinalClas'].value != 'NOT_DEFINED') {
      internationalFlightPolicyObj['maxFlightClassAllowed'] = this.internatinalFlightForm.controls['internatinalClas'].value;
    }

    if (this.internatinalFlightForm.controls['internatinalConsiderBasicEconomy'].value != undefined) {
      if (this.internatinalFlightForm.controls['internatinalConsiderBasicEconomy'].value === true) {
        internationalFlightPolicyObj['considerBasicEconomy'] = false;
      } else {
        internationalFlightPolicyObj['considerBasicEconomy'] = true;
      }
    }
    if (this.internatinalFlightForm.controls['internatinalPrice'].value != null && this.price1
      && this.internatinalFlightForm.controls['internatinalPrice'].value != undefined
      && this.internatinalFlightForm.controls['internatinalPrice'].value > 0) {
      let absolutePricePolicyObj = {};
      absolutePricePolicyObj['type'] = 'markup';
      absolutePricePolicyObj['benchMark'] = this.internatinalFlightForm.controls['internatinalPrice'].value;
      internationalFlightPolicyObj['pricePolicyList'].push(absolutePricePolicyObj);
    }

    if (this.percentagePrice1
      && this.dollar1 &&
      this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].value > 0) {
      let cheapPricePolicyObj = {};
      cheapPricePolicyObj['type'] = 'markup_over_cheapest';
      cheapPricePolicyObj['benchMark'] = this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].value;
      internationalFlightPolicyObj['pricePolicyList'].push(cheapPricePolicyObj);
    } else if (this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].value != null && this.percentagePrice1 && this.percentage1
      && this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].value != undefined
      && this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].value > 0) {
      let percetnPricePolicyObj = {};
      percetnPricePolicyObj['type'] = 'percent_markup_over_cheapest';
      percetnPricePolicyObj['benchMark'] = this.internatinalFlightForm.controls['internatinalCheapPricePercentage'].value;
      internationalFlightPolicyObj['pricePolicyList'].push(percetnPricePolicyObj);
    }
    if (this.internatinalFlightForm.controls['internatinalUpperClas'].value != null
      && this.internatinalFlightForm.controls['internatinalUpperClas'].value != undefined
    ) {
      internationalFlightPolicyObj['upperClassReason'] = this.internatinalFlightForm.controls['internatinalUpperClas'].value;
    }

    if (this.internatinalFlightForm.controls['internatinalDuration'].value != null
      && this.internatinalFlightForm.controls['internatinalDuration'].value != undefined
      && this.internatinalFlightForm.controls['internatinalDuration'].value > 0) {
      internationalFlightPolicyObj['flightDurationForUpperClass'] = this.internatinalFlightForm.controls['internatinalDuration'].value;
    }
    this.policyDetail['internationalFlight'] = internationalFlightPolicyObj;
   // this.processSavePolicyRequest();
  }
  saveFlightPolicy() {
    if (this.policyForm.invalid) {
      return;
    }
    this.saveFlightSetting = true;
    let flightPolicyObj = {};
    flightPolicyObj['pricePolicyList'] = [];
    flightPolicyObj['internationalPricePolicyList'] = [];

    if (this.policyForm.controls['Clas'].value != null && this.maxClass
      && this.policyForm.controls['Clas'].value != undefined && this.policyForm.controls['Clas'].value != 'NOT_DEFINED') {
      flightPolicyObj['maxFlightClassAllowed'] = this.policyForm.controls['Clas'].value;
    }

    if (this.policyForm.controls['considerBasicEconomy'].value != undefined) {
      if (this.policyForm.controls['considerBasicEconomy'].value === true) {
        flightPolicyObj['considerBasicEconomy'] = false;
      } else {
        flightPolicyObj['considerBasicEconomy'] = true;
      }
    }

    if (this.policyForm.controls['price'].value != null && this.price
      && this.policyForm.controls['price'].value != undefined
      && this.policyForm.controls['price'].value > 0) {
      let absolutePricePolicyObj = {};
      absolutePricePolicyObj['type'] = 'markup';
      absolutePricePolicyObj['benchMark'] = this.policyForm.controls['price'].value;
      flightPolicyObj['pricePolicyList'].push(absolutePricePolicyObj);
    }



    if (this.percentagePrice && this.dollar
      && this.policyForm.controls['cheapPricePercentage'].value > 0) {
      let cheapPricePolicyObj = {};
      cheapPricePolicyObj['type'] = 'markup_over_cheapest';
      cheapPricePolicyObj['benchMark'] = this.policyForm.controls['cheapPricePercentage'].value;
      flightPolicyObj['pricePolicyList'].push(cheapPricePolicyObj);
    } else if (this.policyForm.controls['cheapPricePercentage'].value != null && this.percentagePrice && this.percentage
      && this.policyForm.controls['cheapPricePercentage'].value != undefined
      && this.policyForm.controls['cheapPricePercentage'].value > 0) {
      let percetnPricePolicyObj = {};
      percetnPricePolicyObj['type'] = 'percent_markup_over_cheapest';
      percetnPricePolicyObj['benchMark'] = this.policyForm.controls['cheapPricePercentage'].value;
      flightPolicyObj['pricePolicyList'].push(percetnPricePolicyObj);
    }



    if (this.policyForm.controls['upperClas'].value != null
      && this.policyForm.controls['upperClas'].value != undefined
    ) {
      flightPolicyObj['upperClassReason'] = this.policyForm.controls['upperClas'].value;
    }
    if (this.policyForm.controls['duration'].value != null
      && this.policyForm.controls['duration'].value != undefined
      && this.policyForm.controls['duration'].value > 0) {
      flightPolicyObj['flightDurationForUpperClass'] = this.policyForm.controls['duration'].value;
    }

    this.policyDetail['flight'] = flightPolicyObj;
   // this.processSavePolicyRequest();
  }

  pricePercChange3(event) {
    if (event) {
      this.percentagePrice3 = true;
      this.hotelPolicyForm.controls['cheapRoomPricePercentage'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
      this.hotelPolicyForm.controls['cheapRoomPricePercentage'].updateValueAndValidity();
      if (!this.dollar3) {
        this.percentage3 = true
      }
    } else {
      this.percentagePrice3 = false;
      this.percentage3 = false;
      this.hotelPolicyForm.controls['cheapRoomPricePercentage'].setValidators(null);
      this.hotelPolicyForm.controls['cheapRoomPricePercentage'].updateValueAndValidity();
      this.dollar3 = false;
    }
  }

  maxClassChange3(event) {
    if (event) {
      this.maxClass3 = true;
      // this.hotelPolicyForm.controls['star'].setValue(4);
      this.hotelPolicyForm.controls['Star'].setValue(this.hotelStar);
    } else {
      this.maxClass3 = false;
      this.hotelPolicyForm.controls['Star'].setValue(0);
    }
  }
  priceChange3(event) {
    if (event) {
      this.price3 = true;
      this.hotelPolicyForm.controls['roomPrice'].setValidators(Validators.compose([Validators.required, Validators.min(1)]));
      this.hotelPolicyForm.controls['roomPrice'].updateValueAndValidity();
    } else {
      this.price3 = false;
      this.hotelPolicyForm.controls['roomPrice'].setValidators(null);
      this.hotelPolicyForm.controls['roomPrice'].updateValueAndValidity();
    }
  }
  getToggleCheck3(type) {
    if (!this.percentagePrice3) {
      return;
    }
    if (type === 'dollar') {
      this.dollar3 = true;
      this.percentage3 = false;
    } else {
      this.dollar3 = false;
      this.percentage3 = true;
    }
  }
  isRefundable(event) {
    if (event) {
      this.hotelPolicyForm.controls['guranteeType'].setValue('nonrefundable')
      this.refundable = true;
    } else {
      this.hotelPolicyForm.controls['guranteeType'].setValue('any')
      this.refundable = false;
    }
  }
  isPaymentType(event) {
    if (event) {
      this.hotelPolicyForm.controls['paymentType'].setValue('prepaid')
      this.hotelPolicyForm.controls['paymentType'].updateValueAndValidity();
      this.hotelPolicyForm.controls['paymentType'].markAsTouched();
      this.paymenntType = true;
    } else {
      this.hotelPolicyForm.controls['paymentType'].setValue('any')
      this.hotelPolicyForm.controls['paymentType'].clearValidators();
      this.hotelPolicyForm.controls['paymentType'].updateValueAndValidity();
      this.paymenntType = false;
    }
  }
  saveHotelPolicy() {
    if (this.hotelPolicyForm.invalid) {
      return;
    }
    this.saveFlightSetting = true;
    let hotelPolicyObj = {};
    hotelPolicyObj['pricePolicyList'] = [];
    hotelPolicyObj['internationalPricePolicyList'] = [];
    hotelPolicyObj['guaranteeType'] = [];
    hotelPolicyObj['paymentType'] = [];
    if (this.hotelPolicyForm.controls['Star'].value != null && this.hotelPolicyForm.controls['Star'].value != 'NOT_DEFINED' && this.maxClass3) {
      hotelPolicyObj['maxHotelRatingAllowed'] = this.hotelPolicyForm.controls['Star'].value;
    }
    // (<FormArray>this.passengersForm.controls['passengers'])
    if (this.hotelPolicyForm.controls['paymentType'].value) {
      if (this.hotelPolicyForm.controls['paymentType'].value === 'any') {
        hotelPolicyObj['guaranteeType'].push('prepaid');
        hotelPolicyObj['guaranteeType'].push('postpaid');
      } else {
        hotelPolicyObj['guaranteeType'].push(this.hotelPolicyForm.controls['paymentType'].value);
      }
    }

    // for(let gType of (<FormArray>this.hotelPolicyForm.controls['paymentType']).controls){
    if (this.hotelPolicyForm.controls['guranteeType'].value) {
      if (this.hotelPolicyForm.controls['guranteeType'].value === 'any') {
        hotelPolicyObj['paymentType'].push('refundable');
        hotelPolicyObj['paymentType'].push('nonrefundable');
      } else {
        hotelPolicyObj['paymentType'].push('refundable');
      }
    }
    // }
    if (this.hotelPolicyForm.controls['roomPrice'].value != null && this.price3
      && this.hotelPolicyForm.controls['roomPrice'].value != undefined
      && this.hotelPolicyForm.controls['roomPrice'].value > 0) {
      let absolutePricePolicyObj = {};
      absolutePricePolicyObj['type'] = 'markup';
      absolutePricePolicyObj['benchMark'] = this.hotelPolicyForm.controls['roomPrice'].value;
      hotelPolicyObj['pricePolicyList'].push(absolutePricePolicyObj);
    }

    if (this.percentagePrice3 && this.dollar3
      && this.hotelPolicyForm.controls['cheapRoomPricePercentage'].value > 0) {
      let cheapPricePolicyObj = {};
      cheapPricePolicyObj['type'] = 'markup_over_cheapest';
      cheapPricePolicyObj['benchMark'] = this.hotelPolicyForm.controls['cheapRoomPricePercentage'].value;
      hotelPolicyObj['pricePolicyList'].push(cheapPricePolicyObj);
    } else if (this.hotelPolicyForm.controls['cheapRoomPricePercentage'].value != null && this.percentagePrice3 && this.percentage3
      && this.hotelPolicyForm.controls['cheapRoomPricePercentage'].value != undefined
      && this.hotelPolicyForm.controls['cheapRoomPricePercentage'].value > 0) {
      let percetnPricePolicyObj = {};
      percetnPricePolicyObj['type'] = 'percent_markup_over_cheapest';
      percetnPricePolicyObj['benchMark'] = this.hotelPolicyForm.controls['cheapRoomPricePercentage'].value;
      hotelPolicyObj['pricePolicyList'].push(percetnPricePolicyObj);
    }
    // // guranteeType: this.fb.array([]),
    // paymentType: ['', Validators.compose([Validators.required])],
    // roomPrice: ['', Validators.compose([Validators.required, Validators.min(0)])],
    // roomTerms1: [false, Validators.compose([Validators.required])],
    // cheapRoomPricePercentage: ['', Validators.compose([Validators.required, Validators.min(0)])],
    // cheapRoomPrice: ['', Validators.compose([Validators.required, Validators.min(0)])]
    // 
    this.policyDetail['hotel'] = hotelPolicyObj;
   //this.processSavePolicyRequest();
  }
  saveApprovalPolicy() {
    this.policyDetail['approvalProcessEnabled'] = this.approvalProcessEnabled;
    this.policyDetail['travelPurposeMandatory'] = this.travelPurposeMandatory;
    this.policyDetail['approvalRequiredFor'] = this.approvalRequiredFor;
   // this.processSavePolicyRequest();
  }
  saveCarPolicy() {
    if (this.carPolicyForm.invalid) {
      return;
    }
    this.saveFlightSetting = true;
    let carPolicyObj = {};
    carPolicyObj['pricePolicyList'] = [];
    carPolicyObj['internationalPricePolicyList'] = [];
    carPolicyObj['allowedTypes'] = [];

    if (this.carPolicyForm.controls['carClass'].value != null && this.carPolicyForm.controls['carClass'].value != 'NOT_DEFINED' && this.maxClass2) {
      carPolicyObj['maxCarClassAllowed'] = this.carPolicyForm.controls['carClass'].value;
    }
    for (const currCarType of this.carTypeOptions) {
      if (currCarType.checked) {
        carPolicyObj['allowedTypes'].push(currCarType.val);
      }
    }
    if (this.price2
      && this.carPolicyForm.controls['carRentalPrice'].value != undefined
      && this.carPolicyForm.controls['carRentalPrice'].value > 0) {
      let absolutePricePolicyObj = {};
      absolutePricePolicyObj['type'] = 'markup';
      absolutePricePolicyObj['benchMark'] = this.carPolicyForm.controls['carRentalPrice'].value;
      carPolicyObj['pricePolicyList'].push(absolutePricePolicyObj);
    }

    if (this.percentagePrice2
      && this.dollar2
      && this.carPolicyForm.controls['carRentalPricePercentage'].value > 0) {
      let cheapPricePolicyObj = {};
      cheapPricePolicyObj['type'] = 'markup_over_cheapest';
      cheapPricePolicyObj['benchMark'] = this.carPolicyForm.controls['carRentalPricePercentage'].value;
      carPolicyObj['pricePolicyList'].push(cheapPricePolicyObj);
    } else if (this.carPolicyForm.controls['carRentalPricePercentage'].value != null && this.percentagePrice2 && this.percentage2
      && this.carPolicyForm.controls['carRentalPricePercentage'].value != undefined
      && this.carPolicyForm.controls['carRentalPricePercentage'].value > 0) {
      let percetnPricePolicyObj = {};
      percetnPricePolicyObj['type'] = 'percent_markup_over_cheapest';
      percetnPricePolicyObj['benchMark'] = this.carPolicyForm.controls['carRentalPricePercentage'].value;
      carPolicyObj['pricePolicyList'].push(percetnPricePolicyObj);
    }

    this.policyDetail['carPolicy'] = carPolicyObj;
   // this.processSavePolicyRequest();
  }

  saveTrainPolicy() {
    this.saveTrainSetting = true;
    let trainPolicyObj = {};
    trainPolicyObj['pricePolicyList'] = [];
    trainPolicyObj['internationalPricePolicyList'] = [];
    if (this.trainPolicyForm.controls['trainClass'].value != null && this.trainPolicyForm.controls['trainClass'].value != 'NOT_DEFINED') {
      trainPolicyObj['maxTrainClassAllowed'] = this.trainPolicyForm.controls['trainClass'].value;
    }
    if (this.trainPolicyForm.controls['trainPrice'].value != null
      && this.trainPolicyForm.controls['trainPrice'].value != undefined
      && this.trainPolicyForm.controls['trainPrice'].value > 0) {
      let absolutePricePolicyObj = {};
      absolutePricePolicyObj['type'] = 'markup';
      absolutePricePolicyObj['benchMark'] = this.trainPolicyForm.controls['trainPrice'].value;
      trainPolicyObj['pricePolicyList'].push(absolutePricePolicyObj);
    }

    if (this.trainPolicyForm.controls['cheapTrainPrice'].value != null
      && this.trainPolicyForm.controls['cheapTrainPrice'].value != undefined
      && this.trainPolicyForm.controls['cheapTrainPrice'].value > 0) {
      let cheapPricePolicyObj = {};
      cheapPricePolicyObj['type'] = 'markup_over_cheapest';
      cheapPricePolicyObj['benchMark'] = this.trainPolicyForm.controls['cheapTrainPrice'].value;
      trainPolicyObj['pricePolicyList'].push(cheapPricePolicyObj);
    } else if (this.trainPolicyForm.controls['trainPricePercentage'].value != null
      && this.trainPolicyForm.controls['trainPricePercentage'].value != undefined
      && this.trainPolicyForm.controls['trainPricePercentage'].value > 0) {
      let percetnPricePolicyObj = {};
      percetnPricePolicyObj['type'] = 'percent_markup_over_cheapest';
      percetnPricePolicyObj['benchMark'] = this.trainPolicyForm.controls['trainPricePercentage'].value;
      trainPolicyObj['pricePolicyList'].push(percetnPricePolicyObj);
    }


    this.policyDetail['trainPolicy'] = trainPolicyObj;
   // this.processSavePolicyRequest();
  }

  canEditPolicies() {
    return this.canUserAddEditPolicies();
  }

  setPolicyAsDefault(event, option) {
    if (this.canUserAddEditPolicies()) {
      this.departmentPolicySelected(option, this.index)
      this.isDefaultCheckBoxState = event;
      if (!this.addNewPolicy) {
        if (event && this.selectedDepartmentPolicy) {
          for (let policy of this.departmentPolicyList) {
            policy.default = false;
          }
          option.default = true;
          this.selectedDepartmentPolicy = option;
        } else {
          this.selectedDepartmentPolicy.default = false;
          option.default = false;
          this.selectedDepartmentPolicy = this.defaultPolicy;
        }
        this.adminService.saveCompanyPolicyRequest(this.selectedDepartmentPolicy.policyName, this.policyDetail, '-1',
          this.selectedDepartmentPolicy.policyId, this.selectedDepartmentPolicy.default).subscribe(resp => {
            if (resp.success === true && resp.data) {
              this.saveFlightSetting = false;
              this.saveHotelSetting = false;
              this.saveCarSetting = false;
              this.saveTrainSetting = false;
              let departmentPolicy = JSON.parse(JSON.stringify(resp.data));
              //this.departmentPolicyList.push(departmentPolicy);
              // this.policyDetail =departmentPolicy.policy;
              this.selectedDepartmentPolicy = departmentPolicy;
              this.policyDetail = JSON.parse(JSON.stringify(resp.data)).policy;
              this.toastr.success(this.translateService.instant('policy.Policysavedsuccessfully'))
              this.buildFormData(this.policyDetail);
              this.policyForm.patchValue({ ...this.flightPolicy });
              this.hotelPolicyForm.patchValue({ ...this.hotelPolicy });
              this.carPolicyForm.patchValue({ ...this.carPolicy });
              this.trainPolicyForm.patchValue({ ...this.trainPolicy });
              this.internatinalFlightForm.patchValue({ ...this.internationalFlightPolicy });
              this.getDepartmentPolicyList();
              this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
            } else {
              this.saveFlightSetting = false;
              this.saveHotelSetting = false;
              this.saveCarSetting = false;
              this.saveTrainSetting = false;
            }
          });
      }
    }
  }
  isDisabled() {
    if (this.addNewPolicy) {
      return false;
    }
    if (this.selectedDepartmentPolicy) {
      if (this.defaultPolicy.policyId === this.selectedDepartmentPolicy.policyId && this.selectedDepartmentPolicy.default) {
        return true;
      }
    }
    return false;
    // if((this.addPolicy) &&( this.defaultPolicy.default && this.selectedDepartmentPolicy.default)){
    //   return true;
    // }else{
    //   return null;
    // }
  }
  isSelectedPolicyDefault(index) {
    if (this.addNewPolicy) {
      return this.isDefaultCheckBoxState;
    }
    return this.departmentPolicyList[index].default;
  }

  getNumberOfAffectedEmployeesByPolicy() {
    return this.adminPanelService.getNumberOfEmployeesWithPolicy(this.toBeActedDepartmentPolicy.policyId);
  }
  isPolicyRadioSelected(option) {
    if (this.selectedDepartmentPolicy) {
      return this.selectedDepartmentPolicy.policyId === option.policyId;
    }
    return false;
  }
  isGivenPolicyDefault(option) {
    if (this.addNewPolicy) {
      return this.isDefaultCheckBoxState;
    }
    return (this.selectedDepartmentPolicy.policyId === option.policyId);
  }
  public allowedCarTypesClicked(currCarTypeOptionVal) {
    const currCarTypeOption = this.carTypeOptions.find(term => {
      return term.val == currCarTypeOptionVal;
    });
    if (currCarTypeOption) {
      currCarTypeOption.checked = (!currCarTypeOption.checked);
    }
  }
}
