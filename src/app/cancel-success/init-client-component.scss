@import "../../variables.scss";

.modal-content-heading {
  margin-top: 45px;
  margin-bottom: 50px;
}

.modal-dialog {
  margin-top: 13px;

  .modal-content {
    border: none;

    .modal-header {
      border-bottom: none;

      .modal-search-from {
        font-size: 23px;
        color: $primary-color;
        padding-top: 2px;
      }

      .cross-button {
        opacity: 1;
        padding-top: 8px;

        .modal-cross {
          font-size: 10px;
          color: #000;

          &:hover {
            color: #000;
          }
        }
      }
    }

    .modal-body {
      margin-top: 21px;
      position: relative;
      top: -92px;

      .modal-logo {
        text-align: center;
        display: block;
        position: absolute;
        top: 36%;
        left: 35.3%;
      }

      .logo-border {
        display: block;
        height: 48px;
        width: 48px;
        border-radius: 50%;
        position: relative;
        margin: 0 auto;
      }

      .checkmark {
        border: 3px solid #1EBD97;
        background-color: #DDFFF7;

        .icon-checkmark {
          color: #1EBD97;
          font-size: 19px;
          left: 29%;
          top: 23%;
        }
      }

      .cross-bold {
        border: 3px solid #f93d30;
        background-color: #FFE8EB;

        .icon-cross-bold {
          color: #f93d30;
        }
      }

      .fare-changed {
        border: 3px solid #F5A623;
        background-color: #FFEED1;

        .icon-fare-changed {
          color: #F5A623;
          font-size: 20px;
          left: 20%;
          top: 30%;
        }
      }

      .no-flight {
        border: 3px solid #F5A623;
        background-color: #FFEED1;

        .icon-no-flight {
          color: #F5A623;
          left: 28%;
        }
      }

      .path1 {
        top: 4px;
        position: absolute;
        left: 16px;
      }

      .title-msg {
        color: $primary-text-color;
        font-size: 16px;
        letter-spacing: -0.71px;
        line-height: 23px;
        text-align: center;
        margin: 18.5px 0;
        width: 100%;

      }

      .email-text-content {
        max-width: 65%;
        margin: 0 auto;
        color: $primary-text-color;
        font-size: 12px;
        line-height: 19px;
        text-align: center;
        margin-top: 24px;
        white-space: pre-wrap;
      }

      .link {
        color: $link-color;
        font-size: 12px;
        font-weight: bold;
        letter-spacing: 1px;
        line-height: 18px;
        text-align: center;
        display: block;
        text-decoration: none;
        margin-top: 44px;
        padding-bottom: 28px;
      }

      .modal-button-wrapper {
        text-align: center;

        .modal-button {
          height: 46px;
          width: 248px;
          border-radius: 6px;
          background-color: var(--button-bg-color);
          color: var(--button-font-color);
          box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
        }
      }
    }
  }

  .bold {
    font-weight: bold;
  }
}

.modal-backdrop.show {
  opacity: -0.1;
}

@media(max-width:767px) {
  .modal-dialog {
    margin-top: 13px;

    .modal-content {
      border: none;

      .modal-body {
        .modal-button-wrapper {
          text-align: center;

          .modal-button {
            height: 40px;
            width: 148px !important;
            border-radius: 6px;
            background-color: var(--button-bg-color);
            color: var(--button-font-color);
              box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
          }
        }
      }
    }
  }
}