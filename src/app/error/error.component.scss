:host {
    width: 100vw;
}

.errorPage {
    overflow: hidden;
    padding: 20px;

    &__bgImg {

        &--Img {
            width: 775.11px;
            background-size: cover;
        }
    }

    &--textImg {
        padding-left: 80px;
    }

    &__textImg {

        &--Img {
            margin-top: 200px;
        }
    }
}

.heading {

    &--primary {
        font-size: 32px;
        font-weight: bold;
        letter-spacing: 1px;
        line-height: 40px;
        text-transform: uppercase;
        display: block;
    }

    &--secondary {
        font-size: 20px;
        font-weight: bold;
        letter-spacing: 1px;
        line-height: 25px;
        display: block;
    }
}

/////////MEDIA QUERY
@media only screen and (max-width: 900px) {
    .errorPage {

        &__bgImg {

            &--Img {
                width: 100%;
            }
        }

        &--textImg {
            padding-left: 0;
        }

        &__textImg {

            &--Img {
                width: 100%;
                margin-top: 10px;
            }
        }
    }

    .heading {
        text-align: center;

        &--primary {
            font-size: 24px;
        }

        &--secondary {
            font-size: 16px;
        }
    }
}