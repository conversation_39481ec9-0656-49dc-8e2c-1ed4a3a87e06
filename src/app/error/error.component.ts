import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { _ } from 'src/app/util/title';
import { TranslateService } from "@ngx-translate/core";
import { ClientConfiguration } from '../client-config.service';
import { UserAccountService } from '../user-account.service';


@Component({
    selector: 'app-error',
    templateUrl: './error.component.html',
    styleUrls: ['./error.component.scss'],
    standalone: false
})
export class ErrorComponent implements OnInit {

  private errorMsgMap: any = {
    '404': { 'title': 'errorComponent.404Title', 'message': 'errorComponent.404Message' },
    '403': { 'title': 'errorComponent.403Title', 'message': 'errorComponent.403Message' },
    'unknown': { 'title': 'errorComponent.unknownErrorTitle', 'message': 'errorComponent.unknownErrorMessage' },
    'alreadyConfirmed': { 'title': 'errorComponent.alreadyConfirmedTitle', 'message': 'errorComponent.alreadyConfirmedMessage' },

  }
  showHomeLink = false;
  userid = '';
  sToken = '';
  errorCode: number = 404;
  constructor(public translateService: TranslateService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private userAccountService: UserAccountService,
    private clientConfig: ClientConfiguration
  ) {
    this.activatedRoute.queryParams.subscribe(params => {

      if (params['errorCode'])
        this.errorCode = params['errorCode'];
      if (this.errorCode === 404) {
        this.showHomeLink = true;
        this.userid = params['userid'];
        this.sToken = params['sToken']
      }
    });

  }

  ngOnInit() {

  }

  ngOnChanges() {

  }

  public getErrorTitle() {
    return this.translateService.instant(
      this.errorMsgMap[this.errorCode]['title'], { 'GALLOP_HELP_EMAIL': this.clientConfig.agentEmailId });
  }

  public getErrorMessage() {
    return this.translateService.instant(
      this.errorMsgMap[this.errorCode]['message'], { 'GALLOP_HELP_EMAIL': this.clientConfig.agentEmailId });
  }
  goToHomePage() {
    this.router.navigate([this.userAccountService.getDefaultRoutePath()], { queryParams: { userid: this.userid, sToken: this.sToken }, replaceUrl: true });
  }
  public display404() {
    return this.errorCode === 404;
  }

}
