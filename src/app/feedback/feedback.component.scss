@import "../../variables.scss";

.modal-header {
  background-color: var(--hyperlink-color);
  color: #FFFFFF;
  font-size: 14px;
  height: 40px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 6px 6px 0 0;
  padding: 0 8px 0 22px !important;
  border-bottom: none;
}

.modal-header h5 {
  font-size: 18px;
  font-family: "apercu-r";
  width: 100%;
}

.modal-body {
  padding: 28px 25px 47px 25px;
  border-radius: 0 0 5px 5px;
}

.modal-footer {
  padding: 0;
  border-top: none;
}

.modal-content {
  text-align: left !important;
}

.modal-backdrop.show {
  opacity: -0.1;
}

.show {
  padding-bottom: 50px;
}

.bed-image {
  height: 60px;
  width: 60px;
}

.feature {
  text-align: left;
  padding-left: 0px;
  width: 100%;
}

.feature1 {
  text-align: center;
  padding-left: 0px;
  width: 100%;
  margin-top: 15px;
  margin-bottom: 15px;
}

.box11 {
  font-size: 14px;
  cursor: pointer;
  padding-top: 6px;
}

.filter-row {
  height: 40px;
  margin: 10px 0;
  display: flex;
}

.negative-reason {
  border: 2px solid gray;
  cursor: pointer;
  margin: 0 5px;
}

.negative-reason-selected {
  border: 2px solid var(--button-bg-color);
  cursor: pointer;
  background: #e9fffd;
  margin: 0 5px;
}

.blank-reason {
  margin: 0 5px;
}

.btn-secondary {
  height: 54px;
  width: 200px;
  border-radius: 0px !important;
  letter-spacing: 1px;
  background-color: var(--button-bg-color) !important;
  box-shadow: none;
  margin-top: 15px;
  border: none;
}

.add {
  height: 20px;
  width: 188px;
  color: #2D57FA;
  font-family: "apercu-r";
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 1px;
  line-height: 25px;
}

@media(max-width:767px) {
  .filter-row {
    height: 30;
    display: flex;
    margin: 5px 0;
  }

  .box11 {
    font-size: 10px;
    cursor: pointer;
    padding-top: 10px;
  }

  .modal-dialog {
    margin-top: 13px;

    .modal-content {
      border: none;

      .modal-body {
        .modal-button-wrapper {
          text-align: center;

          .modal-button {
            height: 40px;
            width: 148px !important;
            border-radius: 6px;
            background-color: var(--button-bg-color);
            color: var(--button-font-color);
              box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
          }
        }
      }
    }
  }
}