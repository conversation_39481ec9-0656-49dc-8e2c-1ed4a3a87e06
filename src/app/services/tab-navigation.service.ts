import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TabNavigationService {
  private isInitialized = false;
  private mutationObserver: MutationObserver | null = null;
  private debounceTimer: any = null;

  constructor() {}

  public initializeTabNavigation(): void {
    if (this.isInitialized) {
      return;
    }

    // Different initialization strategy for production vs development
    if (environment.production) {
      this.initializeForProduction();
    } else {
      this.initializeForDevelopment();
    }

    this.isInitialized = true;
  }

  private initializeForProduction(): void {
    // In production builds, scripts may load in different order
    // Wait for DOM to be fully ready and all scripts to load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => this.setupTabNavigation(), 200);
      });
    } else {
      // DOM is already loaded, but wait a bit for scripts to settle
      setTimeout(() => this.setupTabNavigation(), 200);
    }
  }

  private initializeForDevelopment(): void {
    // In development, we can set up immediately
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupTabNavigation());
    } else {
      this.setupTabNavigation();
    }
  }

  private setupTabNavigation(): void {
    // Ensure all focusable elements are properly configured
    this.ensureFocusableElements();
    
    // Set up mutation observer for dynamic content
    this.setupMutationObserver();
    
    // Fix any existing tab navigation issues
    this.fixExistingTabIssues();
  }

  private ensureFocusableElements(): void {
    const focusableSelectors = [
      'input:not([disabled]):not([tabindex="-1"])',
      'button:not([disabled]):not([tabindex="-1"])',
      'select:not([disabled]):not([tabindex="-1"])',
      'textarea:not([disabled]):not([tabindex="-1"])',
      'a[href]:not([tabindex="-1"])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable]:not([tabindex="-1"])'
    ];

    const elements = document.querySelectorAll(focusableSelectors.join(','));
    
    elements.forEach((element: HTMLElement) => {
      // Ensure element is properly focusable
      if (!element.hasAttribute('tabindex') && this.shouldBeFocusable(element)) {
        element.setAttribute('tabindex', '0');
      }
      
      // Remove any event listeners that might be preventing tab navigation
      this.fixElementTabBehavior(element);
    });
  }

  private shouldBeFocusable(element: HTMLElement): boolean {
    // Check if element should be focusable based on its type and visibility
    const tagName = element.tagName.toLowerCase();
    const focusableTags = ['input', 'button', 'select', 'textarea', 'a'];
    
    if (focusableTags.includes(tagName)) {
      const style = window.getComputedStyle(element);
      return style.display !== 'none' && 
             style.visibility !== 'hidden' && 
             !element.hasAttribute('disabled');
    }
    
    return false;
  }

  private fixElementTabBehavior(element: HTMLElement): void {
    // Remove any keydown listeners that might be preventing tab navigation
    const newElement = element.cloneNode(true) as HTMLElement;
    
    // Only replace if the element has problematic event listeners
    if (this.hasProblematicEventListeners(element)) {
      element.parentNode?.replaceChild(newElement, element);
      
      // Re-add necessary event listeners without tab interference
      this.addSafeEventListeners(newElement);
    }
  }

  private hasProblematicEventListeners(element: HTMLElement): boolean {
    // Check if element has event listeners that might interfere with tab navigation
    // This is a heuristic approach since we can't directly inspect event listeners
    
    // Check for common problematic patterns
    const onkeydown = element.getAttribute('onkeydown');
    if (onkeydown && onkeydown.includes('preventDefault')) {
      return true;
    }
    
    // Check for jQuery event data (if jQuery is available)
    if ((window as any).$ && (window as any).$(element).data('events')) {
      const events = (window as any).$(element).data('events');
      if (events && events.keydown) {
        return true;
      }
    }
    
    return false;
  }

  private addSafeEventListeners(element: HTMLElement): void {
    // Add back necessary event listeners that don't interfere with tab navigation
    element.addEventListener('keydown', (event: KeyboardEvent) => {
      // Allow tab navigation to work normally
      if (event.key === 'Tab') {
        // Don't prevent default for tab key
        return;
      }
      
      // Handle other keys as needed
      // Add specific logic here if needed for other keys
    });
  }

  private setupMutationObserver(): void {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }

    this.mutationObserver = new MutationObserver((mutations) => {
      let shouldUpdate = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (this.containsFocusableElements(element)) {
                shouldUpdate = true;
              }
            }
          });
        }
      });
      
      if (shouldUpdate) {
        this.debouncedUpdate();
      }
    });

    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  private containsFocusableElements(element: Element): boolean {
    const focusableSelectors = [
      'input', 'button', 'select', 'textarea', 'a[href]', '[tabindex]', '[contenteditable]'
    ];
    
    return focusableSelectors.some(selector => 
      element.matches(selector) || element.querySelector(selector)
    );
  }

  private debouncedUpdate(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    
    this.debounceTimer = setTimeout(() => {
      this.ensureFocusableElements();
    }, 100);
  }

  private fixExistingTabIssues(): void {
    // Fix common tab navigation issues in production builds
    
    // 1. Ensure no global event listeners are preventing tab navigation
    this.fixGlobalTabPrevention();
    
    // 2. Fix issues with third-party libraries
    this.fixThirdPartyLibraryIssues();
  }

  private fixGlobalTabPrevention(): void {
    // Override any global event listeners that might be preventing tab navigation
    const originalAddEventListener = document.addEventListener;
    
    document.addEventListener = function(type: string, listener: any, options?: any) {
      if (type === 'keydown' && typeof listener === 'function') {
        // Wrap the listener to ensure tab navigation isn't prevented
        const wrappedListener = function(event: KeyboardEvent) {
          if (event.key === 'Tab') {
            // Allow tab navigation to proceed
            const result = listener.call(this, event);
            // If the original listener prevented default, we need to check if it should
            if (event.defaultPrevented && !TabNavigationService.shouldPreventTabDefault(event)) {
              // Create a new event and dispatch it to continue tab navigation
              const newEvent = new KeyboardEvent('keydown', {
                key: event.key,
                code: event.code,
                shiftKey: event.shiftKey,
                ctrlKey: event.ctrlKey,
                altKey: event.altKey,
                metaKey: event.metaKey,
                bubbles: true,
                cancelable: true
              });
              event.target?.dispatchEvent(newEvent);
            }
            return result;
          } else {
            return listener.call(this, event);
          }
        };
        return originalAddEventListener.call(this, type, wrappedListener, options);
      } else {
        return originalAddEventListener.call(this, type, listener, options);
      }
    };
  }

  private static shouldPreventTabDefault(event: KeyboardEvent): boolean {
    // Determine if tab default should be prevented based on context
    const target = event.target as HTMLElement;
    
    // Don't prevent tab in form elements unless specifically needed
    if (target && ['INPUT', 'TEXTAREA', 'SELECT'].includes(target.tagName)) {
      return false;
    }
    
    // Don't prevent tab in contenteditable elements
    if (target && target.contentEditable === 'true') {
      return false;
    }
    
    return false;
  }

  private fixThirdPartyLibraryIssues(): void {
    // Fix issues with specific third-party libraries that might interfere with tab navigation
    
    // Fix jQuery UI issues
    if ((window as any).$ && (window as any).$.ui) {
      this.fixJQueryUITabIssues();
    }
    
    // Fix Bootstrap issues
    this.fixBootstrapTabIssues();
    
    // Fix other library issues as needed
  }

  private fixJQueryUITabIssues(): void {
    // jQuery UI sometimes interferes with tab navigation
    // Override problematic behaviors
  }

  private fixBootstrapTabIssues(): void {
    // Bootstrap modals and other components can interfere with tab navigation
    // Ensure tab navigation works within modals
  }

  public destroy(): void {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }
    
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
    
    this.isInitialized = false;
  }
}
