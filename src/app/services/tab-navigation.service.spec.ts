import { TestBed } from '@angular/core/testing';
import { TabNavigationService } from './tab-navigation.service';

describe('TabNavigationService', () => {
  let service: TabNavigationService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(TabNavigationService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize tab navigation', () => {
    // Create some test elements
    const input = document.createElement('input');
    const button = document.createElement('button');
    const select = document.createElement('select');
    
    document.body.appendChild(input);
    document.body.appendChild(button);
    document.body.appendChild(select);

    // Initialize the service
    service.initializeTabNavigation();

    // Check that elements have proper tabindex
    expect(input.getAttribute('tabindex')).toBe('0');
    expect(button.getAttribute('tabindex')).toBe('0');
    expect(select.getAttribute('tabindex')).toBe('0');

    // Clean up
    document.body.removeChild(input);
    document.body.removeChild(button);
    document.body.removeChild(select);
  });

  it('should handle tab key events', () => {
    // Create test elements
    const input1 = document.createElement('input');
    const input2 = document.createElement('input');
    
    input1.setAttribute('tabindex', '0');
    input2.setAttribute('tabindex', '0');
    
    document.body.appendChild(input1);
    document.body.appendChild(input2);

    // Initialize the service
    service.initializeTabNavigation();

    // Focus the first input
    input1.focus();
    expect(document.activeElement).toBe(input1);

    // Simulate tab key press
    const tabEvent = new KeyboardEvent('keydown', {
      key: 'Tab',
      bubbles: true,
      cancelable: true
    });

    document.dispatchEvent(tabEvent);

    // Clean up
    document.body.removeChild(input1);
    document.body.removeChild(input2);
  });

  it('should destroy properly', () => {
    service.initializeTabNavigation();
    expect(() => service.destroy()).not.toThrow();
  });
});
