<div class="card-div active shadow">
  <div class="card-div-inner" style="width:100% !important;">
    <div *ngFor="let item of this.faqData;let i=index">
      <label class="title" style="margin-top:30px;margin-bottom:15px ;">{{item.title}}</label>
      <div *ngFor="let item2 of item.questions;let j=index">
        <label style="margin-bottom: 5px;"><span *ngIf="!this.booleanValues[i][j]" (click)="showAnswers(i,j)"
            class="boxAdd">+</span><span *ngIf="this.booleanValues[i][j]" (click)="showAnswers(i,j)"
            class="boxAdd">-</span> <span class="question">{{item2.question}}</span>
        </label>
        <div [hidden]="!this.booleanValues[i][j]" [innerHTML]="item2.answer" class="answer"></div>
      </div>
    </div>

  </div>
</div>