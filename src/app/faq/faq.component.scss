.title {
    font-size: 22px;
    font-family: "apercu-mono";
}

.question {
    font-family: "apercu-b";
    font-size: 18px;
    color: #444444;
    padding: 10px;
    padding-left: 10px;
    margin: 0;
    font-weight: bold;

}

.answer {
    font-family: "apercu-r";
    font-size: 14px;
    padding: 10px;
    padding-left: 40px;
    user-select: none;
    outline-style: none;
}

.boxAdd {
    background-color: #f7f7f7d2;
    display: inline-flex;
    width: 18px !important;
    height: 24px;
    margin-bottom: 3px;
    text-align: center;
    border: 1px solid #E7E6E4;
    padding-left: 3px;
    cursor: pointer;
}

@media(max-width:1100px) {
    .question {
        font-family: "apercu-b";
        font-size: 14px;
        color: #444444;
        padding: 10px;
        padding-left: 10px;
        margin: 0;
        font-weight: bold;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        outline-style: none;
    }

    .answer {
        font-family: "apercu-r";
        font-size: 12px;
        padding: 10px;
        padding-left: 40px;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        outline-style: none;
    }

}

@media(max-width:768px) {
    .question {
        font-family: "apercu-b";
        font-size: 14px;
        color: #444444;
        padding: 10px;
        padding-left: 10px;
        margin: 0;
        font-weight: bold;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        outline-style: none;
    }

    .answer {
        font-family: "apercu-r";
        font-size: 12px;
        padding: 10px;
        padding-left: 40px;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        outline-style: none;
    }

}

@media(max-width:500px) {
    .question {
        font-family: "apercu-b";
        font-size: 12px;
        color: #444444;
        padding: 10px;
        padding-left: 5px;
        margin: 0;
        font-weight: bold;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        outline-style: none;
    }

    .answer {
        font-family: "apercu-r";
        font-size: 10px;
        padding: 10px;
        padding-left: 40px;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        outline-style: none;
    }
}