import { Component, OnInit } from '@angular/core';
import { SearchService } from '../search.service';

@Component({
    selector: 'app-faq',
    templateUrl: './faq.component.html',
    styleUrls: ['./faq.component.scss'],
    standalone: false
})
export class FaqComponent implements OnInit {
  faqData = [];
  str = '+';
  booleanValues: Array<boolean>[];
  constructor(
    private searchService: SearchService,
  ) { }

  ngOnInit() {
    this.getFaqData();
  }

  getFaqData() {
    this.searchService.getFaqResults().subscribe(res => {
      if (res && res.status === 'success') {
        var response = JSON.parse(res.data);
        this.faqData = [...response.data];
        this.booleanValues = new Array(this.faqData.length).fill(null).map(_ => []);
        // 
        for (let counter = 0; counter < this.faqData.length; counter++) {
          for (let item2 of this.faqData[counter].questions) {
            this.booleanValues[counter].push(false);
          }
        }
        // 
      }

    });
  }
  showAnswers(i, j) {
    this.booleanValues[i][j] = !this.booleanValues[i][j];
  }
}
