import { Component, OnInit, Input, ElementRef, EventEmitter, Output } from '@angular/core';
import { AdminPanelService } from '../admin-panel.service';
import { Subscription } from 'rxjs';
import { CommonUtils } from '../util/common-utils';
import { Router } from '@angular/router';
import { ClientConfiguration } from '../client-config.service';
import { UserAccountService } from '../user-account.service';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { SearchService } from '../search.service';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';

declare var gtag: any;


@Component({
    selector: 'app-onboarding',
    templateUrl: './onboarding.component.html',
    styleUrls: ['./onboarding.component.scss'],
    standalone: false
})
export class OnboardingComponent implements OnInit {
  @Input() popData: any;
  @Output() goBackMSg = new EventEmitter();
  onboardSubscription: Subscription;
  bsModalRef: BsModalRef;
  constructor(private el: ElementRef, private searchService: SearchService, public translateService: TranslateService, private toastr: ToastrService,
    private modalService: BsModalService, private adminPanelService: AdminPanelService, private userAccountService: UserAccountService, public router: Router, public clientConfig: ClientConfiguration,) { }
  url = "assets/images/check.png";
  ngOnInit(): void {
    setTimeout(() => {
      this.expandOnboardingPopup();
    }, 1000);
  }
  getUrl(option) {
    if (option.status !== "not_clicked") {
      this.url = "assets/images/check.png";
    } else {
      this.url = "assets/images/check-mark.png";
    }
    return this.url;
  }
  updateItems(item) {
    if (item.status === "clicked") {
      return;
    }
    gtag('event', 'onboarding_popup_item', { 'item': item.label });
    this.onboardSubscription = this.adminPanelService.updateOnboardingList(item.onboardingPopupItemId).subscribe(resp => {
      if (resp && resp.status === 'success') {
        let findIndex = this.popData.findIndex(item1 => item1.onboardingPopupItemId === item.onboardingPopupItemId);
        if (findIndex > -1) {
          this.popData[findIndex].status = "clicked";
        }
        
        
      }
    })
    if (item.type === 'external') {
      if (item.passLoginToken) {
        let userid = this.userAccountService.getUserEmail();
        let sToken = this.userAccountService.getSToken();
        let str = item.target;
        if(str.indexOf( "?" ) ===-1){
          item.target = (item.target + "?userid=" + userid + "&sToken=" + sToken)
          }else{
            item.target = (item.target + "&userid=" + userid + "&sToken=" + sToken)
          }
      }
      window.open(item.target, "_blank");
    } else if (item.type === 'internal') {
      let userid = this.userAccountService.getUserEmail();
      let sToken = this.userAccountService.getSToken();
      if ((CommonUtils.doesPathContain(item.target, 'admin'))) {
        let path = item.target.split('?')[1];
        path = path.split('=')[1];
        this.router.navigateByUrl(item.target);
      } else if ((CommonUtils.doesPathContain(item.target, 'search'))) {
        window.location.href = (this.clientConfig.bookingAppBaseUrl + '/search' + "?userid=" + item.userId + "&sToken=" + sToken);
      }
      else if ((CommonUtils.doesPathContain(item.target, 'profile'))) {
        window.location.href = (this.clientConfig.bookingAppBaseUrl + '/profile' + "?userid=" + item.userId + "&sToken=" + sToken);
      }
    }
  }
  expandOnboardingPopup() {
    let myTag = this.el.nativeElement.querySelector(".down-arrow")
    if (!myTag.classList.contains('removeTransform')) {
      myTag.classList.add('removeTransform');
    } else {
      myTag.classList.remove('removeTransform');
    }
    $('.openPopup').slideToggle();
  }
  removePopup(modal){
    if(this.bsModalRef){
      this.bsModalRef.hide();
    }
this.bsModalRef = this.modalService.show(modal);
  
  }
  onModelCancel(){
    this.bsModalRef.hide();
  }
  onPopupCanceCancel(){
    this.bsModalRef.hide();
    this.adminPanelService.updateOnboardingList(0).subscribe(resp =>{
      if(resp && resp.status==='success'){
       
     //   this.toastr.success(this.translateService.instant("employee.Popupclosesuccessfully"));
        this.goBackMSg.emit(false);
      }
    })
  }
  ngOnDestroy() {
    if (this.onboardSubscription) {
      this.onboardSubscription.unsubscribe();
    }
  }
}
