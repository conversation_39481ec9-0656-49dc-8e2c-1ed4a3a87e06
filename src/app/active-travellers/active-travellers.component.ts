import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { AdminPanelService } from '../admin-panel.service';
import { UserAccountService } from '../user-account.service';
import { DateUtils } from '../util/date-utils';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { Subscription } from 'rxjs';
import { DeviceDetailsService } from '../device-details.service';
import { SearchService } from '../search.service';
import { UserInfoBasic } from '../entity/company-report-response';
import { Router, ActivatedRoute } from '@angular/router';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { CommonUtils } from '../util/common-utils';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { Constants } from '../util/constants';
import { TranslateService } from '@ngx-translate/core';
import { Title } from '@angular/platform-browser';

@Component({
    selector: 'app-active-travellers',
    templateUrl: './active-travellers.component.html',
    styleUrls: ['./active-travellers.component.scss'],
    standalone: false
})
export class ActiveTravellersComponent implements OnInit {
  hover: boolean;
  boxSelect = false;
  isMobile1: boolean;
  deviceSubscription1: Subscription;
  viewMode1 = 'tab11';
  dateValue1 = '';
  marker2 = 'assets/images/map_pin.svg';
  markerImage = 'assets/images/map-marker.png';
  markerImageActive = 'assets/images/map-marker-active.png';
  markerImageBest = 'assets/images/map-marker-best.png';
  selectDateRange = 'today';
  empNameSearchValue = '';
  number = 0;
  startDate: Date = new Date();
  endDate: Date = new Date();
  minimumDate: Date = new Date();
  applyBtn = false;
  maximumDate: Date = new Date();
  maximumDate1: Date = new Date();
  dateOptions1 = Constants.DATE_OPTIONS;
  
  origReportResponse: any;
  zoom = 0;
  isMobile: boolean;
  lat: number = 12.954517;
  long: number = 77.3507335;
  activeTravelerSubscription: Subscription;
  deviceSubscription: Subscription;
  activeTravellersList: Array<any>;
  initialBoundsSet = false;
  queryParamSubscription: Subscription;
  daterangepickerModel = [];
  constructor(private adminPanelService: AdminPanelService,
    private userAccountInfoService: UserAccountService,
    private titleService: Title,
    public ngxSmartModalService: NgxSmartModalService,
    private deviceDetailsService: DeviceDetailsService,
    private gallopLocalStorage: GallopLocalStorageService,
    public searchService: SearchService,
    private activatedRoute: ActivatedRoute,
    public translateService: TranslateService,
    private cdRef: ChangeDetectorRef,
    public router: Router,) { }
    resultErrorMessage = this.translateService.instant('activeTraveler.FetchingData');
    resultErrorMessage1 = 'activeTraveler.0travelersfound';
  ngOnInit() {
    this.applyBtn = true;
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });
    this.queryParamSubscription = this.activatedRoute.queryParams.subscribe(params => {
      if (params['subType'] == 'today') {
        this.selectDateRange = 'today';
        this.adminPanelService.filterDateType = this.selectDateRange;
        this.getTravelerData('today')
      } else if (params['subType'] == '7Days') {
        this.selectDateRange = '7Days';
        this.adminPanelService.filterDateType = this.selectDateRange;
        this.getTravelerData('7Days');
      } else if (params['subType'] == '30Days') {
        this.selectDateRange = '30Days';
        this.adminPanelService.filterDateType = this.selectDateRange;
        this.getTravelerData('30Days');
      } else {
        if (params['subType']) {
          const [start, end] = params['subType'].split(',');
          this.startDate = new Date(start);
          this.endDate = new Date(end);
          this.selectDateRange = '';
          this.adminPanelService.firstReportDate = this.startDate;
          this.adminPanelService.lastReportDate = this.endDate;
          this.adminPanelService.filterDateType = this.selectDateRange;
          this.daterangepickerModel = [this.startDate, this.endDate];
          //  this.minimumDate=this.startDate;
          if (start && end) {
            if (!this.adminPanelService.originalTravellerResponse) {
              this.subscriptionEvents();
            } else {
              this.adminPanelService.filterActiveTraveler1()
            }
          }
        }
      }
    });
    setTimeout(() => {
      this.observable();
    }, 2000);

  }
  changeStyle1() {
    if (!this.isMobile) {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '730px' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '99vw' }
      }
    }
  }
  changeStyle2() {
    if (!this.isMobile) {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '335px', 'overflow': 'hidden' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '775px', 'overflow': 'hidden', 'width': '350px' }
      }
    }
  }



  customTabClicked() {
    this.viewMode1 = 'tab11';
  }
  presetsTabClicked() {
    this.viewMode1 = 'tab12';
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  openNgxModal(id, picker) {
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);

    setTimeout(() => {
      this.viewMode1 = 'tab11';
      picker.show();
    }, 200);
  }
  subscriptionEvents() {
    this.adminPanelService.originalTravellerResponse = undefined;
    this.activeTravellersList = [];
    this.adminPanelService.filterTravellerName = '';
    this.empNameSearchValue = '';
    this.resultErrorMessage = this.translateService.instant('activeTraveler.FetchingData');;
    this.applyBtn = true;
    let endDate1 = new Date();
    endDate1.setDate(this.startDate.getDate() + 180);
    let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
    let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(endDate1);

    let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
    let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
    this.adminPanelService.navigateFrom = 'active';
    this.adminPanelService.fetchActiveTraveler(this.userAccountInfoService.getUserCompanyId(), startDate, endDate);


    this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      if (userAccountInfoObj == null) {
        this.origReportResponse = null;
        this.adminPanelService.originalTravellerResponse = undefined;
        this.adminPanelService.filterTravellerName = '';
        this.adminPanelService.removeDataAfterLogging();
        this.adminPanelService.selectDateRange = 'today';
        this.adminPanelService.loginSessionChanged();
      }
    });
  }
  observable() {
    this.adminPanelService.activeTravellerResponseObservable$.subscribe((response) => {
      if (response) {
        this.initialBoundsSet = false;
        this.origReportResponse = deserialize(response);
        if (this.origReportResponse.bookingList.length > 0) {
          this.resultErrorMessage = this.translateService.instant('activeTraveler.FetchingData');;
        } else {
          this.resultErrorMessage = 'activeTraveler.0travelersfound';
          this.zoom = 2;
        }
        this.buildCompanyReportData(this.origReportResponse);
        this.applyBtn = false;
      } else {
        this.initialBoundsSet = false;
        this.resultErrorMessage = 'activeTraveler.0travelersfound';
        this.applyBtn = false;
        this.zoom = 2;
      }
    });
  }
  changeStyle($event, i) {
    if ($event.type == 'mouseover') {
      this.hover = true;
      this.number = i;
    } else {
      this.hover = false;
      this.number = 0;
    }
  }

  buildCompanyReportData(response) {
    this.activeTravellersList = [];
    for (let optionItem of response.bookingList) {
      const reportItem = {};
      // const [ first, last ] = response.users[optionItem.userid].userName.split(" ");
      let travellerFullName = response.users[optionItem.userid].userName;
      if (optionItem.primaryTraveller && optionItem.primaryTraveller.userName) {
        travellerFullName = optionItem.primaryTraveller.userName;
      }
      reportItem['name'] = travellerFullName;
      const [first, last] = travellerFullName.split(' ');
      reportItem['shortname'] = (first.charAt(0) + last.charAt(0));
      let departmentName = this.adminPanelService.getDepartmentName(response.users[optionItem.userid].departmentId);
      reportItem['department'] = departmentName;
      reportItem['bookingDate'] = optionItem.bookingDate;
      reportItem['type'] = optionItem.type;
      reportItem['destinations'] = optionItem.destinationCity;
      let location = optionItem.location;
      const [lat, long] = location.split(",");
      reportItem['latitude'] = Number.parseFloat(lat);
      // this.latitudeForDestination= Number.parseFloat(lat);
      reportItem['longitude'] = Number.parseFloat(long);
      // this.longitudeForDestination= Number.parseFloat(long);
      reportItem['fitBounds'] = true;
      reportItem['markerClicked'] = false;
      reportItem['ticketNumber'] = optionItem.ticketnumber ? optionItem.ticketnumber : null;
      this.activeTravellersList.push(reportItem);
    }
  }

  getMarkerName(data) {
    return data.shortname;
  }
  changeProperty() {
    this.activeTravellersList.map(function (x) {
      x.markerClicked = false;
      return x;
    });
  }
  zoomChanged($event) {
    this.zoom = $event;
  }
  mapReadyCallback($event) {
  }

  boundsChangedCallback($event) {
    if (!this.initialBoundsSet) {
      setTimeout(() => {
        if (this.zoom > 15) {
          this.zoom = 15;
        }
      }, 1500);
    }
    this.initialBoundsSet = true;
  }

  markerClick($event, data, index) {
    this.changeProperty();
    data.markerClicked = true;
    this.titleService.setTitle(this.translateService.instant('report.TransactionsReport'));
    this.adminPanelService.from = 'activeTraveller';
    this.adminPanelService.selectDateRange = this.selectDateRange;
    this.gallopLocalStorage.setItem("from", this.adminPanelService.from);
    data.isRecommanded = true;
    let bookedOption = this.origReportResponse.bookingList[index];
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          view: 'detail',
          type: 'detail',
          name: name,
          bookingType: 'upcoming',
          userEmail: bookedOption.userid,
          ticketid: bookedOption.ticketid,
          tripid: bookedOption.tripid,
          transactionid: bookedOption.option.selectTransId
        },
      }
    );
    //this.activeTravellersList = this.getDimensionsByFind($event.latitude);
  }

  setStartDate(date) {
    if (date) {
      // this.daterangepickerModel =date;
      this.startDate = date[0];
      this.endDate = date[1];
    }
    // this.minimumDate=this.startDate;
    if (this.startDate > this.endDate) {
      this.endDate = date[1];
      this.adminPanelService.lastReportDate = this.endDate;
    }

  }
  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
    this.ngxSmartModalService.close('daterangeSelection1');
    this.adminPanelService.firstReportDate = this.startDate;
    this.adminPanelService.lastReportDate = this.endDate;
    this.maximumDate1 = this.endDate;
    this.adminPanelService.firstReportDate = this.startDate;
    this.adminPanelService.filterDateType = this.selectDateRange;
    this.selectDateRange = '';
    this.adminPanelService.filterDateType = this.selectDateRange;
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'activeTravellers',
          subType: (this.startDate + ',' + this.endDate)
        },
        replaceUrl: false
      }
    );
    if (!this.adminPanelService.originalTravellerResponse) {
      this.subscriptionEvents();
    } else {
      this.adminPanelService.filterActiveTraveler1()
    }
  }
  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    const dayHoverHandler = event.dayHoverHandler;
    //this.dateValue1 = "CUSTOMDATE";
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        this.searchService.hoverCounter++;
        if (this.searchService.hoverCounter > 1) {
          (picker as any)._datepickerRef.instance.daySelectHandler(cell);
        }
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }
  searchByNameChanged(nameString: string) {
    this.adminPanelService.filterTravellerName = nameString;
    this.adminPanelService.filterActiveTraveler1()
  }
  getTravelerData(item) {
    this.getData(item);
    if (!this.adminPanelService.originalTravellerResponse) {
      this.subscriptionEvents();
    } else {
      this.adminPanelService.filterActiveTraveler1()
    }
  }
  getData(item) {
    this.selectDateRange = item;
    this.adminPanelService.filterDateType = this.selectDateRange;
    if (item === 'today') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: 'activeTravellers',
            subType: 'today'
          },
          replaceUrl: false
        }
      );
    } else if (item === '7Days') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() + 1);
      this.endDate.setDate(this.endDate.getDate() + 7);
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: 'activeTravellers',
            subType: '7Days'
          },
          replaceUrl: false
        }
      );
    } else if (item === '30Days') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() + 1);
      this.endDate.setDate(this.endDate.getDate() + 30);
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: 'activeTravellers',
            subType: '30Days'
          },
          replaceUrl: false
        }
      );
    }
  }

  ngOnDestroy() {
    this.queryParamSubscription.unsubscribe();
    // this.adminPanelService.companySaveReport = undefined;
  }


}
