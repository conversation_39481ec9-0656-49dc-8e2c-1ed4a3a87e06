import { Component, OnInit, ViewChild, HostListener } from '@angular/core';
import { CompanySettings, AdminPanelService } from '../admin-panel.service';
import { Subscription, Subject } from 'rxjs';
import { UserAccountService } from '../user-account.service';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { TranslateService } from '@ngx-translate/core';
import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { GallopAnalyticsUtil } from 'src/app/analytics.service';
import { ToastrService } from 'ngx-toastr';
import { UserAccountInfo } from '../entity/user-account-info';
import { CardInfo } from '../entity/card-info';
import { AddCardWidgetComponent } from '../email-booking-flow/add-card-widget/add-card-widget.component';
import { CommonUtils } from '../util/common-utils';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { environment } from '../../environments/environment';
import { TouchSequence } from 'selenium-webdriver';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Title, DomSanitizer } from '@angular/platform-browser';
import { Constants } from '../util/constants';
import { DeviceDetailsService } from '../device-details.service';
import { SearchService } from '../search.service';
declare var setFocusOnInputField: any;
declare var getCurrentlyOpenNgxSmartModalIds: any;
declare var setNgxSmartModalOpenStateClosed: any;
declare var updateTravelCheckBoxes: any;
declare var intiatePlaid: any;
declare var addCardForm: any;
declare var accountSuccesfullyMsg: any;
@Component({
    selector: 'app-admin-payment-method',
    templateUrl: './admin-payment-method.component.html',
    styleUrls: ['./admin-payment-method.component.scss'],
    standalone: false
})
export class AdminPaymentMethodComponent implements OnInit {
  companySettings: CompanySettings;
  companySettingsSubscription: Subscription;
  companyName: '';
  viewMode2 = 'tab21';
  url = "assets/images/check.png";
  tooltipOptions = {
    'placement': 'left',
    'show-delay': '500',
    'tooltip-class': 'new-tooltip-class'
  };
  changingValue: Subject<any> = new Subject();
  primarycardValue = '';
  backupcardValue = '';
  defaultCreditCardForAllDept = false;
  addTnplForm: UntypedFormGroup;
  carRentalForm:UntypedFormGroup;
  addBalanceLowForm: UntypedFormGroup;
  updatingPaymentMethodPrimary1 = [];
  updatingPaymentMethodBackup1 = [];
  addCardProgress=false;
  updatingPaymentMethodPrimary = false;
  cardButtonStyle=true;
  cardAddForUapa = false;
  uploadingDocuments = false;
  selectedDept = [];
  selectedId = '';
  selectedType:any;
  currency = 'USD';
  updatingPaymentMethodBackup = false;
  travelNowpayLater = false;
  configure = '-1';
  defaultCreditCArdDept = [];
  configureItems = [{ value: 'for entire company', id: 'Entirecompany' }, { value: 'by departemnt', id: 'ByDepartment' }]
  settingSaveProcessing = false;
  userAccountInfoObj: UserAccountInfo;
  existingDepartments: Array<any>;
  departmentArrayForDropdwon: Array<any>;
  existingDepartmentsForDropdwon:Array<any>;
  fetchAccountInfoSubscription: Subscription;
  paymentOptionsSubscription: Subscription;
  hideGoBackButton = false;
  show = false;
  cardIndex: number;
  bsModalRef: BsModalRef;
  selectedCardIndex: number = -1;
  deleteCard = false;
  companyConfigure: any;
  addCardFlow = false;
  departmentCardList = []
  addCardMode = false;
  switchon = false;
  cardOptions = [];
  companyCards = [];
  carRentalList=[];
  departmentValue=[];
  uatpCards = []
  selectDeptcard: any;
  applyButton = false;
  alldeptInfo = [];
  tempAlldeptInfo = [];
  cardOptions1 = [];
  uploadedBank = [];
  carRentalCompanies = Constants.carRentalCompanies;
  originalCarRentalCompanies = Constants.carRentalCompanies;
  uploadedDocuments = [];
  tempdepartmentArray = [];
  updatingPaymentMethodPrepay = false;
  uploading = false;
  bankLodershow = false;
  updatingPaymentMethod = false;
  resultErrorMsg = this.translateService.instant("fuild.Fetchingdata");
  resultErrorMsg1 = this.translateService.instant("setting.Nopaymentmethodselectedforanydepartment");
  departmentArray = [{ value: 'All departments', id: '', primaryCardId: '', backupCardId: '' }]
  paymentMethod: string;
  @ViewChild(AddCardWidgetComponent, { static: true }) addCardChild: AddCardWidgetComponent;
  constructor(public userAccountInfoService: UserAccountService,
    public adminPanelService: AdminPanelService,
    private fb: UntypedFormBuilder,
    private sanitizer: DomSanitizer,
    private gallopLocalStorage: GallopLocalStorageService,
    public translateService: TranslateService,
    public ngxSmartModalService: NgxSmartModalService,
    private ngxAnaltics:GoogleAnalyticsService,
    public deviceDetailsService: DeviceDetailsService,
    public router: Router,
    public searchService: SearchService,
    private titleService: Title,
    private modalService: BsModalService,
    private toastr: ToastrService,) { }

  setResponseData(resp) {
    this.tempAlldeptInfo = [];
    this.updatingPaymentMethod = false;
    this.updatingPaymentMethodBackup = false;
    this.updatingPaymentMethodPrimary = false;
    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }
    this.deleteCard = false;
    this.stopLoader();
    if (resp.data && resp.data.companyCards) {
      this.companyCards = resp.data.companyCards;
    }
    if (resp && resp.data.bankAccounts) {
      this.uploadedBank = resp.data.bankAccounts;
    }
    if (resp.data && resp.data.companyPaymentConfigs) {
      this.companyConfigure = resp.data.companyPaymentConfigs;
      //   this.companyCards =[]
      if (resp.data.companyPaymentConfigs.departmentConfigs && resp.data.companyPaymentConfigs.departmentConfigs.length > 0) {
        this.tempdepartmentArray = []
        let index = 0;
        for (let item of resp.data.companyPaymentConfigs.departmentConfigs) {
          let deptItem = { value: '', id: '', primaryCardId: '', backupCardId: '' };
          deptItem.value = item.departmentName
          deptItem.id = item.departmentId;
          if (item.config.corprateCards && item.config.corprateCards.primary) {
            deptItem.primaryCardId = item.config.corprateCards.primary.id;
          }
          if (item.config.corprateCards && item.config.corprateCards.backup) {
            deptItem.backupCardId = item.config.corprateCards.backup.id;
          }
          if (index === 1) {
            this.tempdepartmentArray.push({ value: 'All departments', id: '', primaryCardId: '', backupCardId: '' });
          }
          this.tempdepartmentArray.push(deptItem);
          let allDeptDeatils = {};

          if (item.config) {
            if (item.config.corprateCards && item.config.corprateCards.primarySelected) {

              allDeptDeatils['corprateCards'] = item.config.corprateCards;
              allDeptDeatils['deaprtmentName'] = item.departmentName;
              allDeptDeatils['corprateCardsPrimary'] = true;
              allDeptDeatils['deaprtmentId'] = item.departmentId;
              this.primarycardValue = item.config.corprateCards.primary.id;
              this.backupcardValue = item.config.corprateCards.backup.id;
              //this.alldeptInfo.push(allDeptDeatils);
            }
            if (item.config.corprateCards && item.config.corprateCards.backupSelected) {

              allDeptDeatils['corprateCards'] = item.config.corprateCards;
              allDeptDeatils['deaprtmentName'] = item.departmentName;
              allDeptDeatils['deaprtmentId'] = item.departmentId;
              allDeptDeatils['corprateCardsBackup'] = true;
              this.primarycardValue = (item.config.corprateCards.primary && item.config.corprateCards.primary.id) ? item.config.corprateCards.primary.id : null;
              this.backupcardValue = item.config.corprateCards.backup.id;
              //this.alldeptInfo.push(allDeptDeatils);
            }
            if (item.config.dailyDebit && item.config.dailyDebit.primarySelected) {

              allDeptDeatils['dailyDebit'] = item.config.dailyDebit;
              allDeptDeatils['deaprtmentId'] = item.departmentId;
              allDeptDeatils['dailyDebitPrimary'] = true;
              allDeptDeatils['deaprtmentName'] = item.departmentName;
              // this.alldeptInfo.push(allDeptDeatils);
            }
            if (item.config.dailyDebit && item.config.dailyDebit.backupSelected) {

              allDeptDeatils['dailyDebit'] = item.config.dailyDebit;
              allDeptDeatils['deaprtmentId'] = item.departmentId;
              allDeptDeatils['dailyDebitBackup'] = true;
              allDeptDeatils['deaprtmentName'] = item.departmentName;
              // this.alldeptInfo.push(allDeptDeatils);
            }
            if (item.config.prepay && item.config.prepay.primarySelected) {

              allDeptDeatils['prepay'] = item.config.prepay;
              allDeptDeatils['deaprtmentId'] = item.departmentId;
              allDeptDeatils['prepayPrimary'] = true;
              allDeptDeatils['deaprtmentName'] = item.departmentName;
              // this.alldeptInfo.push(allDeptDeatils);
            }
            if (item.config.prepay && item.config.prepay.backupSelected) {

              allDeptDeatils['prepay'] = item.config.prepay;
              allDeptDeatils['deaprtmentId'] = item.departmentId;
              allDeptDeatils['prepayBackup'] = true;
              allDeptDeatils['deaprtmentName'] = item.departmentName;
              //this.alldeptInfo.push(allDeptDeatils);
            }
            if (item.config.travelNowPayLater && item.config.travelNowPayLater.primarySelected) {

              allDeptDeatils['travelNowPayLater'] = item.config.travelNowPayLater;
              allDeptDeatils['deaprtmentId'] = item.departmentId;
              allDeptDeatils['deaprtmentName'] = item.departmentName;
              allDeptDeatils['travelNowPayLaterPrimary'] = true;
              // this.alldeptInfo.push(allDeptDeatils);
            }



            if (item.config.travelNowPayLater && item.config.travelNowPayLater.backupSelected) {
              allDeptDeatils['travelNowPayLater'] = item.config.travelNowPayLater;
              allDeptDeatils['deaprtmentId'] = item.departmentId;
              allDeptDeatils['travelNowPayLaterBackup'] = true;
              allDeptDeatils['deaprtmentName'] = item.departmentName;

            }
          }
          if ((Object.keys((allDeptDeatils)).length > 0)) {
            if ((Object.keys((allDeptDeatils)).length > 4)) {
              this.tempAlldeptInfo.push(allDeptDeatils);
            } else {
              allDeptDeatils = this.keyMissing(allDeptDeatils);
              this.tempAlldeptInfo.push(allDeptDeatils);
            }
          }
          index++
        }
        this.alldeptInfo = this.tempAlldeptInfo;
        for (let counter = 0; counter < this.alldeptInfo.length; counter++) {
          this.updatingPaymentMethodBackup1.push(false);
          this.updatingPaymentMethodPrimary1.push(false)
        }
        this.departmentArray = this.tempdepartmentArray;
        if (this.departmentArray && this.departmentArray.length === 2) {
          this.departmentArray = this.departmentArray.filter(item => item.id !== '')
        }
      }
    }
    if (this.configure !== '') {
      this.showConfigureTypeChanged(this.configure);
    }
  }

  stopLoader() {
    for (let counter = 0; counter < this.updatingPaymentMethodPrimary1.length; counter++) {
      this.updatingPaymentMethodPrimary1[counter] = false;
    }
    for (let counter = 0; counter < this.updatingPaymentMethodBackup1.length; counter++) {
      this.updatingPaymentMethodBackup1[counter] = false;
    }
  }
  selectedCardTypeDropdown = false;
  getStyleToolTip(item){
    
    let dept  =this.getDepartments(item);
    if(dept && dept.length > 140){
      return {top:'-140px'}
    }else if(dept && dept.length > 110){
      return {top:'-80px'}
    }else{
      return {top:'-55px'} 
    }
  }
  updatePaymentMethodForDepartmentPrepay(department, type1, type2) {
    let primary = false;
    if (type2 === 'primary') {
      if (this.selectDeptcard.config.prepay.primarySelected) {
        return;
      }
      primary = true;
      if (this.selectDeptcard.config.corprateCards && this.selectDeptcard.config.corprateCards.primarySelected) {
        this.selectDeptcard.config.corprateCards.primarySelected = false;
      }
      this.selectDeptcard.config.prepay.primarySelected = true;
    } else if (type2 === 'backup') {
      if (this.selectDeptcard.config.prepay.backupSelected) {
        return;
      }
      primary = false;;
      if (this.selectDeptcard.config.corprateCards && this.selectDeptcard.config.corprateCards.backupSelected) {
        this.selectDeptcard.config.corprateCards.backupSelected = false;
      }
      this.selectDeptcard.config.prepay.backupSelected = true;
    }
    let deaptVaue = {}
    deaptVaue = { departmentId: department, type: type1, primary: primary };
    this.updatingPaymentMethodPrepay = true;
    this.updatepaymentRequestApi(deaptVaue);
  }
  updatepaymentRequestApi(deptvalue) {
    this.adminPanelService.getRoutespringUpdateCreditCardDropdown(deptvalue).subscribe(resp => {
      if (resp && resp.status === 'success') {
        

        this.setResponseData(resp);
        // this.updatingPaymentMethod= false;
        this.updatingPaymentMethodPrepay = false;
        this.toastr.success(this.translateService.instant("setting.PaymentMethodUpdatedSucessfull"))
      } else {
        if (resp) {
          this.updatingPaymentMethod = false;
          // this.ngxSmartModalService.getModal(modal).close();
        }
      }
    })
  }
  updatePaymentMethodForDepartment(id, type1, type2, modal, modal1, index?) {
    if (this.updatingPaymentMethod || this.updatingPaymentMethodPrepay) {
      this.toastr.warning(this.translateService.instant("setting.PaymentMethodUpdating"))
      return;
    }
    if (this.ngxSmartModalService.getModal(modal)) {
      this.ngxSmartModalService.getModal(modal).close();
    }
    let primarycardValue;
    let findIndex;
    let selectDeptcard
    if (this.configure === '' && this.alldeptInfo[index]) {

      selectDeptcard = this.alldeptInfo[index];
      if (selectDeptcard && selectDeptcard.corprateCards && type2 === 'primary') {
        primarycardValue = selectDeptcard.corprateCards.primary.id;
      } else {
        primarycardValue = (selectDeptcard.corprateCards && selectDeptcard.corprateCards.backup.id) ? selectDeptcard.corprateCards.backup.id : null;
      }
      if (id === primarycardValue) {
        return;
      }

    } else {
      findIndex = this.companyConfigure.departmentConfigs.findIndex(item => item.departmentId === this.configure);
      selectDeptcard = this.companyConfigure.departmentConfigs[findIndex];
      if (selectDeptcard && selectDeptcard.config.corprateCards && type2 === 'primary') {
        if (selectDeptcard.config.corprateCards.primary) {
          primarycardValue = selectDeptcard.config.corprateCards.primary.id;
        } else {
          primarycardValue = '';
        }
      } else {
        if (selectDeptcard.config.corprateCards.backup) {
          primarycardValue = selectDeptcard.config.corprateCards.backup.id;
        } else {
          primarycardValue = '';
        }
      }
      if (id === primarycardValue) {
        return;
      }
    }

    if (this.configure === '-1' && !index && index !== 0 || (this.configure === '' && index === 0 && this.alldeptInfo[index].deaprtmentId === '-1')) {
      this.selectedId = id;
      this.defaultCreditCArdDept = [];


      for (let deptItem of this.departmentArray) {
        if (type2 === 'primary') {
          if (deptItem.id !== '-1' && deptItem.id !== '' && deptItem.primaryCardId && deptItem.primaryCardId === primarycardValue) {

            this.defaultCreditCArdDept.push(deptItem);
          }
        } else {
          if (deptItem.id !== '-1' && deptItem.id !== '' && deptItem.backupCardId && deptItem.backupCardId === primarycardValue) {

            this.defaultCreditCArdDept.push(deptItem);
          }
        }
      }
      this.selectedDept = [];
      this.showDeaprtmentList = false;
      this.defaultCreditCardForAllDept = false;
      if (type2 === 'primary') {
        this.selectedCardTypeDropdown = true;
      } else {
        this.selectedCardTypeDropdown = false;
      }
      this.bsModalRef = this.modalService.show(modal1, {
        initialState: {
        }, backdrop: true, ignoreBackdropClick: true
      });
    }
    else {
      let deaptVaue = {}
      let primary;
      if (!index && index !== 0) {
        if (type2 === 'primary') {
          this.primarycardValue = id;
          this.updatingPaymentMethodPrimary = true;
          primary = true;
        } else {
          primary = false;
          this.updatingPaymentMethodBackup = true;
          this.backupcardValue = id;
        }
        deaptVaue = { departmentId: this.configure, type: type1, methodId: id, primary: primary };
      } else {
        if (type2 === 'primary') {
          this.primarycardValue = id;
          this.updatingPaymentMethodPrimary1[index] = true;
          primary = true;
        } else {
          primary = false;
          this.updatingPaymentMethodBackup1[index] = true;
          this.backupcardValue = id;
        }
        deaptVaue = { departmentId: this.alldeptInfo[index].deaprtmentId, type: type1, methodId: id, primary: primary };
      }
      this.updatingPaymentMethod = true;
      // deaptVaue={departmentId:this.configure,type:type1,methodId:id,primary:primary};
      this.updatepaymentRequestApi(deaptVaue);
    }
  }
  showDeaprtmentList = false;
  onConfirmUpdate() {
    this.deleteCard = true;
    let findIndex1 = this.companyCards.findIndex(item => item.id === this.selectedId);

    let deaptVaue = { departmentId: '-1', type: this.companyCards[findIndex1].type, methodId: this.selectedId, primary: this.selectedCardTypeDropdown, departments: this.selectedDept };
    this.adminPanelService.getRoutespringUpdateCreditCardDropdown(deaptVaue).subscribe(resp => {
      if (resp && resp.status === 'success') {
        

        this.setResponseData(resp);
        // this.updatingPaymentMethod= false;
        this.toastr.success(this.translateService.instant("setting.PaymentMethodUpdatedSucessfull"))
      } else {
        if (resp) {
          this.updatingPaymentMethod = false;
          // this.ngxSmartModalService.getModal(modal).close();
        }
      }
    })
  }
  SelectToggleButton(item) {
    if (item) {
      this.showDeaprtmentList = false;
      for (let item1 of this.defaultCreditCArdDept) {
        this.selectedDept.push(item1.id)
      }
    } else {
      this.selectedDept = [];
      this.showDeaprtmentList = true;
    }
  }
  onModelUpdateDeafultDepatCancel1() {
    if (this.deleteCard) {
      return;
    }
    this.bsModalRef.hide();




  }

  onChangeDepartment(option, event) {
    if (event) {
      if (this.selectedDept.indexOf(option) === -1) {
        this.selectedDept.push(option)
      }

    } else {
      this.selectedDept = this.selectedDept.filter(item => item !== option);
    }
    this.isDeptChecked(option);
  }
  isDeptChecked(option) {
    return this.selectedDept.indexOf(option) > -1;
  }
  getPaymentOptions() {

    this.tempdepartmentArray = [];
    // this.uploadedBank=[];
    this.resultErrorMsg = this.translateService.instant("fuild.Fetchingdata");

    this.paymentOptionsSubscription = this.adminPanelService.getPaymentOptions().subscribe(resp => {
      if (resp.status === 'success') {
        if (this.bsModalRef) {
          this.bsModalRef.hide();
        }
        this.applyButton = false;
        this.deleteCard = false;
        this.userAccountInfoService.deletingCard = false;
        this.setResponseData(resp);
        
      } else if (resp) {
        this.applyButton = false;
        if (this.bsModalRef) {
          this.bsModalRef.hide();
        }
        this.resultErrorMsg = this.translateService.instant("fuild.Nodata");
      }
    })
  }
  openLowBalanceModal(modal){
    this.bsModalRef = this.modalService.show(modal);

  }
  loadeToastr(item, thisObj, data) {
    thisObj.bankLodershow = false;
    if (item) {
      thisObj.toastr.success(thisObj.translateService.instant("fuild.Accountlinkedsuccessfully"));
      thisObj.getPaymentOptions();
    }else if(data && data !==''){
      thisObj.toastr.error(thisObj.translateService.instant(data));
    }
  }
  keyMissing(item) {
    let keys1 = ['travelNowPayLaterBackup', 'prepayBackup', 'dailyDebitBackup', 'corprateCardsBackup'];
    let found = 0;
    for (let item1 of keys1) {
      if (item.hasOwnProperty(item1)) {
        found = 1;
      }
    }
    if (found === 1) {
      item['primary'] = true;
    } else {
      item['backup'] = true;
    }
    return item;
  }
  getUploadedDocuments() {
    this.adminPanelService.getUploadedDocumentsForPAylater().subscribe(resp => {
      if (resp && resp.status === 'success') {
        if (resp.data && resp.data.length > 0) {
          this.uploadedDocuments = resp.data;
        }
        this.uploading = false;
        
      } else if (resp) {
        this.uploading = false;
      }
    })
  }

  bankUploading() {
    const userid = this.userAccountInfoService.getUserEmail();
    const stoken = this.userAccountInfoService.getSToken();
    this.bankLodershow = true;
    // this.bankLodershow =true;
    intiatePlaid(environment.plaidInitUrl, environment.plaidIssuingUrl, userid, environment.plaidEnvironment,stoken)
  }
  clickOnLearnMore() {
    window.open('https://help.routespring.com/portal/en/kb/articles/centralized-payment-methods-overview/', '_blank');
  }
  uploadBank(modal) {
    this.bsModalRef = this.modalService.show(modal);
  }
  uploadDocuments(modal) {
    this.bsModalRef = this.modalService.show(modal);
  }
  selectedFile = null;
  imageSrc: any;
  readURL(event, cardNumber, i): void {
    if (event && event[0]) {
      const file = event[0];
      this.selectedFile = file;
      // this.uploading =true;
      this.renderPostUploadFile();
    }
  }
  renderPostUploadFile() {
    const reader = new FileReader();
    const file = this.selectedFile;
    reader.onload = e => this.imageSrc = reader.result;
    reader.readAsDataURL(file);
    this.uploadingDocuments = true;
    const filename = this.selectedFile.name
    this.adminPanelService.uploadedDocumentsForPaylater(this.selectedFile).subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.uploading = false;
        const fileObject = { fileName: '', url: '' };
        fileObject.fileName = filename;
        fileObject.url = resp.data.url;
        this.uploadedDocuments.push(fileObject);
        this.uploadingDocuments = false;
        
        this.toastr.success(this.translateService.instant('setting.ileuploadedsuccessfully'));
      } else {
        if (resp.errorMessage && resp.errorMessage.length > 0) {
          this.toastr.error(resp.errorMessage[0]);
          this.uploading = false;
          this.uploadingDocuments = false;
        } else {
          this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
          this.uploading = false;
          this.uploadingDocuments = false;
        }
      }
    });
  }
  getTnplDetails() {
    this.adminPanelService.getTnplDetails().subscribe(resp => {
      if (resp && resp.status === 'success') {
        
        if (!resp.data) {
          this.travelNowpayLater = false;
          this.addTnplForm = this.fb.group({
            legalName: ['', Validators.compose([Validators.required])],
            address: ['', Validators.compose([Validators.required])],
            ein: ['', Validators.compose([Validators.required])]
          });
        } else {
          this.travelNowpayLater = true;
          this.addTnplForm = this.fb.group({
            legalName: [resp.data.legalName, Validators.compose([Validators.required])],
            address: [resp.data.address, Validators.compose([Validators.required])],
            ein: [resp.data.ein, Validators.compose([Validators.required])]
          });
        }
      }
    })
  }
  setFocusOnInput(id){
    setFocusOnInputField(id, '');
  }
  submitTnplApplication() {
    if (this.addTnplForm.invalid) {
      this.addTnplForm.controls['ein'].markAsTouched();
      this.addTnplForm.controls['address'].markAsTouched();
      this.addTnplForm.controls['legalName'].markAsTouched();
      return
    }
    this.uploading = true;
    let tnplObj = { legalName: this.addTnplForm.controls['legalName'].value, address: this.addTnplForm.controls['address'].value, ein: this.addTnplForm.controls['ein'].value };
    this.adminPanelService.updateTnplDetails(tnplObj).subscribe(resp => {
      if (resp && resp.status) {
        this.toastr.success(this.translateService.instant('setting.ApplicationSubmitSuccessfully'));
        this.uploading = false;
        this.travelNowpayLater = true;
        // this.bsModalRef.hide();
      } else {
        if (resp) {
          this.uploading = false;
          // this.bsModalRef.hide();
        }
      }
    });
  }
  showQucikBookButtonWithCompanyName = false;
  uuidKey = '';
  private generateUUID(): string {
    var text = "";
    var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    for (var i = 0; i < 5; i++)
      text += possible.charAt(Math.floor(Math.random() * possible.length));

    return text;
  }
  qboCards = [];
  applyButton1 = false
  all_airlines = Constants.uatpAirlines;
  edituatpCard = false;
  getAirlineName(item) {
    let airlineName = this.all_airlines.filter(item1 => item1.id === item);
    return airlineName[0].value;
  }
  getCarRentalComapnyName(item){
    let airlineName = this.originalCarRentalCompanies.filter(item1 => item1.value === item);
    return airlineName[0].label;
  }
  getDepartments(item) {
    let itemArray = item.split(",");

    if (itemArray.length === 1 && itemArray[0] === '-1') {
      return "fuild.AllDepartments"
    } else if (itemArray.length > 0) {
      let departments = '';
      let index = 0;
      for (let dept of itemArray) {
        let deptId = +dept;
        departments += this.adminPanelService.getDepartmentName(deptId);
        if (index < (itemArray.length - 1)) {
          departments = departments + ", ";
        }
        index = index + 1;
      }
      return departments;
    }

  }
  selectedAirlines = [];
  maintainUAtpList(resp) {
    this.selectedAirlines = [];
    if (resp && resp.status === 'success') {
      

      if (this.bsModalRef) {
        this.bsModalRef.hide();
      }

      if (resp && resp.data) {
        this.uatpCards = resp.data;
      }
      if (this.uatpCards.length > 0) {
        for (let item of this.uatpCards) {
          this.selectedAirlines.push(item.airline);
        }
      }
      this.applyButton1 = false
      setTimeout(() => {
        this.cardAddForUapa = false;
        this.edituatpCard = false;
       // this.changingValue.next({ value: '', value1: false });
        this.unsetCardMode();
      }, 300);
    } else {
      if (this.bsModalRef) {
        this.bsModalRef.hide();
      }
      this.applyButton1 = false
      setTimeout(() => {
        this.cardAddForUapa = false;
       // this.changingValue.next({ value: '', value1: false });
        this.edituatpCard = false;
        this.unsetCardMode();
      }, 300);
    }
  }
  srclink:any;
  getUatpList() {
    this.selectedAirlines = [];
    this.adminPanelService.getUatpList().subscribe(resp => {
      this.maintainUAtpList(resp);
    })
  }
  isMobile1 = false;
  deviceSubscription1: Subscription;
  ngOnInit(): void {
    this.applyButton = true;
    this.applyButton1 = true;
    this.addBalanceLowForm = this.fb.group({
      amount: ['', Validators.compose([Validators.required])],
      email: ['', Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_EMAIL)])]
     
    });
    this.deviceSubscription1 = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
   this.onChangeDepartmentForCarRental('All dept', true);
    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      this.userAccountInfoObj = userAccountInfoObj;
      this.applyButton = true;
      this.unsetCardMode();
      this.getPaymentOptions();
      this.getUploadedDocuments();
      this.getTnplDetails();
      this.getCarDiscountList();
      this.getBalanceStatus();
      this.getUatpList();
    });
    accountSuccesfullyMsg(this.loadeToastr, this);
    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {
        this.companySettings = settings;
        //


        this.currency = this.companySettings.company.currency;
        if (this.companySettings.cardList && this.companySettings.cardList.card_list.length > 0) {
          this.cardOptions = [];
          this.cardOptions1 = [];
          for (let item of this.companySettings.cardList.card_list) {
            let cardObject = { id: '', value: '', brand: '', last4: '' }
            cardObject.value = item.brand + " " + item.last4;
            cardObject.brand = item.brand;
            cardObject.id = item.id;
            cardObject.last4 = item.last4;
            if (this.cardOptions.find(item => item.id === cardObject.id) === undefined) {
              this.cardOptions.push(cardObject);
            }
            if (this.cardOptions1.find(item => item.id === cardObject.id) === undefined) {
              this.cardOptions1.push(cardObject);
            }
          }


        }
        if (this.bsModalRef) {
          this.bsModalRef.hide();
          this.deleteCard = false;
          this.userAccountInfoService.deletingCard = false;
        }



        if (this.companySettings.cardList && this.companySettings.cardList.card_list.length > 0) {
          for (let cIndex in this.companySettings.cardList.card_list) {
            if (this.companySettings.cardList.card_list[cIndex].selected) {
              this.selectedCardIndex = parseInt('' + cIndex);

              break;
            }
          }
          if (this.selectedCardIndex == -1) this.selectedCardIndex = 0;
        }
        if ((this.userAccountInfoService.onBoardingTask.indexOf('billing') === -1)) {
          if (this.companySettings.cardList && this.companySettings.cardList.card_list.length === 1 && this.adminPanelService.addNewCard) {
            let dataEmit = { newadmin: true, show: true }
            // this.getImageUrl.emit(dataEmit);
          } else if (this.companySettings.cardList && this.companySettings.cardList.card_list.length === 1 && !this.adminPanelService.addNewCard) {
            let dataEmit = { newadmin: false, show: true }
            // this.getImageUrl.emit(dataEmit);
          } else if (this.companySettings.cardList && this.companySettings.cardList.card_list.length > 1) {
            let dataEmit = { newadmin: false, show: true }
            // this.getImageUrl.emit(dataEmit);
          }
        }
        if (!this.companySettings.cardList) {
          // this.setCardMode();
          //this.hideGoBackButton = true;
          // let object1 = document.getElementById("addCard");
          // addCardForm(object1);

        } else {
          this.hideGoBackButton = false;
        }
        this.companyName = this.companySettings.company.name;
        if (this.companySettings.departments) {
          this.existingDepartments = [{ value: 'All Departments', departmentId: 'All dept', name: "fuild.AllDepartments" }];
          for (let department of this.companySettings.departments) {
            //   (<FormArray>this.settingForm.controls['Department']).push(new FormControl(department.name));
            this.existingDepartments.push(department);
          }
            this.departmentArrayForDropdwon = this.existingDepartments;
            this.existingDepartmentsForDropdwon = this.existingDepartments;
        }
      }
    });
    this.uuidKey = this.generateUUID();
    this.adminPanelService.getQuickBookdetails(this.uuidKey).subscribe(resp => {
      if (resp.success) {
        this.showQucikBookButtonWithCompanyName = true;
        if (resp.data && resp.data.accounts && resp.data.accounts.length > 0) {
          this.qboCards = resp.data.accounts;
        }

        this.companyName = resp.data.qboCompanyName;
      } else {
        this.showQucikBookButtonWithCompanyName = false;
      }
    })
  }
  TabClicked(item, type) {
    this.viewMode2 = item;
  }
  onModelCancel() {
   this.bsModalRef.hide();

  }
  getCarDiscountList(){
    this.adminPanelService.getCarRentalList().subscribe(resp => {
      if(resp && resp.success){
        if(resp.data && resp.data.length > 0){
        this.carRentalList = resp.data;
        for(let item1 of this.carRentalList){
          this.carRentalCompanies = this.carRentalCompanies.filter(item => item.value!==item1.partnerCode)
        }
        }else{
          this.carRentalCompanies =   Constants.carRentalCompanies;
        
          this.carRentalList=[];
        }
        
      }
    })
  }
  onModelCancel1() {
    if (this.deleteCard) {
      return;
    }

    this.updatingPaymentMethodBackup = false;
    this.updatingPaymentMethodPrimary = false;
    this.stopLoader();
    this.bsModalRef.hide();
    setTimeout(() => {
      if (this.addCardMode) {
        if (this.cardUpdating) {
          this.cardUpdating = false;
        }
        this.cardAddForUapa = false;
        this.unsetCardMode();
        //  this.addCardMode =false;
      }
    }, 300);

    // this.cardOptions1 = [...this.cardOptions];

  }
  addingLowBalanceDetails=false;
  emailNotificationList=[];
  addLowBalanceSetting(){
    if(this.addBalanceLowForm.invalid){
      this.addBalanceLowForm.markAllAsTouched();
      this.addBalanceLowForm.controls['email'].updateValueAndValidity(); 
      return;
    }
    let balanceObject:any={};
    let email = this.addBalanceLowForm.controls['email'].value;
    if(email && this.emailNotificationList.find(email)===undefined){
      this.emailNotificationList.push(email);
    }
    balanceObject['threashold'] = this.addBalanceLowForm.controls['amount'].value;
    balanceObject['currency'] = this.currency;
    balanceObject['contacts'] = this.emailNotificationList;
    this.addingLowBalanceDetails=true;
    this.adminPanelService.postStatusBalanceOfCard(balanceObject).subscribe(resp=>{
      if(resp && resp.success){
       // 
        if(this.bsModalRef){
          this.bsModalRef.hide();
        }
        this.addingLowBalanceDetails=false;
        this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"));
        this.getBalanceStatus();
      }else{
        this.addingLowBalanceDetails=false;
      }
    })
  }
  unselectedEmployee(index) {
   
    this.emailNotificationList.splice(index, 1);
    if(this.emailNotificationList.length===0){
      this.addBalanceLowForm.controls['email'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_EMAIL)]);
      this.addBalanceLowForm.markAllAsTouched();
      this.addBalanceLowForm.controls['email'].updateValueAndValidity(); 
    }
  }
  addEmailList(){
    if(this.addBalanceLowForm.controls['email'].value!==null && this.addBalanceLowForm.controls['email'].value !=='' && this.addBalanceLowForm.controls['email'].valid){
this.emailNotificationList.push(this.addBalanceLowForm.controls['email'].value);
this.addBalanceLowForm.controls['email'].clearValidators();
this.addBalanceLowForm.controls['email'].setValidators(Validators.pattern(Constants.RGEX_EMAIL));
this.addBalanceLowForm.controls['email'].updateValueAndValidity();
this.addBalanceLowForm.controls['email'].setValue(null);
    }
  }
  currencyStyle(item2){
  if (item2 && item2==='USD') {
      return { 'padding-left':'20px' };
    } else {
      return { 'padding-left':'30px'}
    }
  
  
  }
  inputEmailValue(value){
    let val=  value.replace(',', '');
    this.addBalanceLowForm.controls['email'].setValue(val);
   }
  getBalanceStatus(){
  
    this.adminPanelService.getStatusBalanceOfCard().subscribe(resp=>{
      if(resp && resp.success){
        if(resp.data && resp.data.threashold){
          this.addBalanceLowForm.controls['amount'].setValue(resp.data.threashold);
          this.emailNotificationList = resp.data.contacts;
          if(this.emailNotificationList && this.emailNotificationList.length>0){
            this.addBalanceLowForm.controls['email'].clearValidators();
this.addBalanceLowForm.controls['email'].setValidators(Validators.pattern(Constants.RGEX_EMAIL));
this.addBalanceLowForm.controls['email'].updateValueAndValidity();
          }
        }
        
      }
    })
  }
  getCurrencySymbol(item): string {
    return CommonUtils.getCurrencySymbol(item);

  }
  getDepartmentName(item) {
    for (let item1 of this.departmentArray) {
      if (item1.id === item) {
        return item1.value;
      }
    }
  }
  showCardChanged(event) {


  }
  showbackupCardChanged(event) {

  }
  selectCard(event, i, modal) {
    this.departmentCardList[i].cardId = event;
    if (this.ngxSmartModalService.getModal(modal)) {
      this.ngxSmartModalService.getModal(modal).close();
    }
  }
  openTansactionPdf(modal){
    this.bsModalRef = this.modalService.show(modal);
  }
  clicked() {
    if (this.ngxSmartModalService.getOpenedModals() &&
      this.ngxSmartModalService.getOpenedModals().length > 0
    ) {
      let modals = this.ngxSmartModalService.getOpenedModals();
      for (let index = 0; index < modals.length; index++) {
        var currModalTimeStamps = getCurrentlyOpenNgxSmartModalIds();
        let helpModalAlreadyOpen = false;
        if (currModalTimeStamps[modals[index].id] &&
          new Date().getTime() - currModalTimeStamps[modals[index].id] > 500
        ) {
          if (modals[index].id !== 'helpModal') {
            this.ngxSmartModalService.getModal(modals[index].id).close();
            setNgxSmartModalOpenStateClosed(modals[index].id);
          } else {
            if ($('.modalAirportFilterInfo').css('display') !== 'none') {
              $('.modalAirportFilterInfo').slideToggle();
              setNgxSmartModalOpenStateClosed(modals[index].id);
            }
          }
        }
      }
    }
  }

  getCardDetails() {
    let card1: any = this.companyCards.filter(item => item.id === this.previousUpdatingCard);
    if (card1 && card1.length > 0) {
      return card1[0].nameOnCard + "-" + " " + card1[0].brand + "-" + card1[0].last4
    }
  }
  SelectToggleButtonOfDeletePopUp(item) {
    if (item) {
      for (let counter = 1; counter < this.departmentCardList.length; counter++) {
        this.departmentCardList[counter].cardId = this.selectedId;
      }
    } else {
      for (let counter = 1; counter < this.departmentCardList.length; counter++) {
        this.departmentCardList[counter].cardId = '';
      }
    }
    this.defaultCreditCardForAllDept = item;
  }
  selectDefaultCard(event, modal) {
    this.departmentCardList[0].cardId = event;
    this.selectedId = event;
    if (this.defaultCreditCardForAllDept) {
      for (let counter = 1; counter < this.departmentCardList.length; counter++) {
        this.departmentCardList[counter].cardId = this.selectedId;
      }
    }
    if (this.ngxSmartModalService.getModal(modal)) {
      this.ngxSmartModalService.getModal(modal).close();
    }
  }
  defaultCardDeleting = false;
  selectedForDeletioncard: any;
  public confirmCardDeleteModel(modal, cardIndex: number) {
    this.departmentCardList = [];
    this.cardOptions1 = this.companyCards;
    this.defaultCreditCardForAllDept = false;
    let defaultCardDeleting = false;
    this.selectedForDeletioncard = this.companyCards[cardIndex];
    this.cardOptions1 = this.cardOptions1.filter(item => item.id !== this.companyCards[cardIndex].id);
    for (let item of this.alldeptInfo) {
      if (item.corprateCards && ((item.corprateCards.primary && item.corprateCards.primary.id === this.companyCards[cardIndex].id) || (item.corprateCards.backup && item.corprateCards.backup.id === this.companyCards[cardIndex].id))) {
        let departList = { deptId: '', cardId: null };
        departList.deptId = item.deaprtmentId;
        if (departList.deptId === '-1') {
          defaultCardDeleting = true;
        } else {
          this.departmentCardList.push(departList);
        }
      }
    }

    if (!defaultCardDeleting) {
      this.cardOptions = this.cardOptions1;
      let findIndex = this.alldeptInfo.findIndex(item1 => item1.deaprtmentId === '-1')
      if (findIndex > -1) {
        this.selectedId = this.alldeptInfo[findIndex].corprateCards.primary && this.alldeptInfo[findIndex].corprateCards.primary.id ? this.alldeptInfo[findIndex].corprateCards.primary.id : this.alldeptInfo[findIndex].corprateCards.backup.id;
        let option = { deptId: '-1', cardId: this.selectedId };
        this.departmentCardList.unshift(option);
      }
    } else {
      this.selectedId = null;
      let option = { deptId: '-1', cardId: '' };
      this.departmentCardList.unshift(option);
      this.cardOptions = this.cardOptions1;
    }
    if (this.departmentCardList.length === 1 && defaultCardDeleting) {
      this.defaultCardDeleting = true;
    } else {
      this.defaultCardDeleting = false;
    }
    this.bsModalRef = this.modalService.show(modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
    this.cardIndex = cardIndex;
    //this.bsModalRef.content.onClose.subscribe(result => {

    // if(result){ this.markCardDeleted(cardIndex);}
    // });
  }

  getUrl(option, item, type) {
    if (type === 'primary') {
      if (option === '') {
        this.url = "assets/images/check.png";
      } else if (item === 'corporate') {
        if (this.selectDeptcard.config) {
          if (this.selectDeptcard.config.corprateCards && this.selectDeptcard.config.corprateCards.primarySelected) {
            this.url = "assets/images/check.png";
          } else {
            this.url = "assets/images/check-mark.png";
          }
        }
      } else if (item === 'travelNowPayLater') {
        if (this.selectDeptcard.config) {
          if (this.selectDeptcard.config.travelNowPayLater && this.selectDeptcard.config.travelNowPayLater.primarySelected) {
            this.url = "assets/images/check.png";
          } else {
            this.url = "assets/images/check-mark.png";
          }
        }
      } else if (item === 'dailyDebit') {
        if (this.selectDeptcard.config) {
          if (this.selectDeptcard.config.dailyDebit && this.selectDeptcard.config.dailyDebit.primarySelected) {
            this.url = "assets/images/check.png";
          } else {
            this.url = "assets/images/check-mark.png";
          }
        }
      } else if (item === 'prepay') {
        if (this.selectDeptcard.config) {
          if (this.selectDeptcard.config.prepay && this.selectDeptcard.config.prepay.primarySelected) {
            this.url = "assets/images/check.png";
          } else {
            this.url = "assets/images/check-mark.png";
          }
        }
      }
    } else if (type === 'backup') {
      if (item === 'corporate') {
        if (this.selectDeptcard.config) {
          if (this.selectDeptcard.config.corprateCards && this.selectDeptcard.config.corprateCards.backupSelected) {
            this.url = "assets/images/check.png";
          } else {
            this.url = "assets/images/check-mark.png";
          }
        }
      } else if (item === 'travelNowPayLater') {
        if (this.selectDeptcard.config) {
          if (this.selectDeptcard.config.travelNowPayLater && this.selectDeptcard.config.travelNowPayLater.backupSelected) {
            this.url = "assets/images/check.png";
          } else {
            this.url = "assets/images/check-mark.png";
          }
        }
      } else if (item === 'dailyDebit') {
        if (this.selectDeptcard.config) {
          if (this.selectDeptcard.config.dailyDebit && this.selectDeptcard.config.dailyDebit.backupSelected) {
            this.url = "assets/images/check.png";
          } else {
            this.url = "assets/images/check-mark.png";
          }
        }
      } else if (item === 'prepay') {
        if (this.selectDeptcard.config) {
          if (this.selectDeptcard.config.prepay && this.selectDeptcard.config.prepay.backupSelected) {
            this.url = "assets/images/check.png";
          } else {
            this.url = "assets/images/check-mark.png";
          }
        }
      }
    }
    return this.url;
  }
  openNgxModal(id) {
    if (this.userAccountInfoService.deletingCard) {
      return;
    }
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);
  }
  openNgxModal1(id) {
    if (this.defaultCreditCardForAllDept || this.userAccountInfoService.deletingCard) {
      return;
    }
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);
  }
  onEditDepartment(deaprtmentId) {
    this.configure = deaprtmentId;
    this.showConfigureTypeChanged(deaprtmentId);
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  getCardName(id) {
    let findIndex = this.companyCards.findIndex(item => item.id === id);
    if (findIndex > -1) {
      return this.companyCards[findIndex].nameOnCard;
    } else {
      return this.translateService.instant('setting.SelectCard')
    }
  }
  getBrandName(id) {
    let findIndex = this.companyCards.findIndex(item => item.id === id);
    if (findIndex > -1) {
      return this.companyCards[findIndex].brand;
    } else {
      return '';
    }
  }
  getLastFour(id) {
    let findIndex = this.companyCards.findIndex(item => item.id === id);
    if (findIndex > -1) {
      return this.companyCards[findIndex].last4;
    }
  }
  showConfigureTypeChanged(event) {
    if (event !== '') {
      let findIndex = this.companyConfigure.departmentConfigs.findIndex(item => item.departmentId === event);
      this.selectDeptcard = this.companyConfigure.departmentConfigs[findIndex];
      if (this.selectDeptcard.config) {
        if (this.selectDeptcard.config.corprateCards) {
          this.primarycardValue = (this.selectDeptcard.config.corprateCards.primary && this.selectDeptcard.config.corprateCards.primary.id) ? this.selectDeptcard.config.corprateCards.primary.id : null;
          this.backupcardValue = this.selectDeptcard.config.corprateCards.backup.id
        }
      }
      if (this.selectDeptcard.config && !this.selectDeptcard.config.corprateCards && !this.selectDeptcard.config.dailyDebit && !this.selectDeptcard.config.travelNowPayLater
        && !this.selectDeptcard.config.prepay) {
        let findIndex = this.companyConfigure.departmentConfigs.findIndex(item => item.departmentId === '-1');
        this.selectDeptcard = this.companyConfigure.departmentConfigs[findIndex];
        if (this.selectDeptcard.config.corprateCards) {
          this.primarycardValue = (this.selectDeptcard.config.corprateCards.primary && this.selectDeptcard.config.corprateCards.primary.id) ? this.selectDeptcard.config.corprateCards.primary.id : null;
          this.backupcardValue = this.selectDeptcard.config.corprateCards.backup.id
        }
      }
    } else {
      this.selectDeptcard = null;
    }
  }
  onConfirm() {

    let map = new Map();
    for (let item of this.departmentCardList) {
      if (!item.cardId || item.cardId === '') {
        this.toastr.error(this.translateService.instant("setting.Pleaseselectcreditcardforalldepartments"))
        return;
      }
      map.set(item.deptId, item.cardId);
    }
    this.deleteCard = true;
    this.userAccountInfoService.deletingCard = true;
    this.markCardDeleted(this.cardIndex, map)
  }
  public markCardDeleted(cardIndex: number, map) {


    let findIndex = this.companyConfigure.departmentConfigs.findIndex(item => item.departmentId === '-1');
    let selectDeptcard = this.companyConfigure.departmentConfigs[findIndex];
    let primarycardValue;
    if (selectDeptcard && selectDeptcard.config.corprateCards && selectDeptcard.config.corprateCards.primary) {
      primarycardValue = selectDeptcard.config.corprateCards.primary.id;
    } else if (selectDeptcard && selectDeptcard.config.corprateCards && selectDeptcard.config.corprateCards.backup) {
      primarycardValue = selectDeptcard.config.corprateCards.backup.id;
    }
    if (this.companyCards && this.companyCards) {
      this.adminPanelService.requestDeleteCard(this.companyCards[cardIndex].id,
        primarycardValue, map).subscribe(res => {

          if (res.status === 'success') {
            this.companyCards = this.cardOptions1;
            this.getPaymentOptions();

            // this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
          } else {
            this.bsModalRef.hide();
            this.userAccountInfoService.deletingCard = false;
            // this.userAccountInfoService.deletingCard=false;
            //  this.deleteCard =false;
            this.deleteCard = false;
            this.toastr.error(res.message, 'Attention!');
          }
        });
    }
  }
  public getCardList(): Array<CardInfo> {
    if (this.companySettings && this.companySettings.cardList != null) {
      this.hideGoBackButton = false;
      return this.companySettings.cardList.card_list;
    }
  }

  public setCardIndex(index: number) {
    this.selectedCardIndex = index;
    this.paymentMethod = 'PERSONAL_CARD';
    this.switchon = false;
    this.addSetting();
  }
  ngOnDestroy() {
    this.departmentArray = [];
    this.edituatpCard = false;
    this.cardAddForUapa = false;
    if (this.deviceSubscription1) {
      this.deviceSubscription1.unsubscribe();
    }
    if (this.companySettingsSubscription) {
      this.companySettingsSubscription.unsubscribe();
    }
    if (this.paymentOptionsSubscription) {
      this.paymentOptionsSubscription.unsubscribe();

    }
    if (this.fetchAccountInfoSubscription) {
      this.fetchAccountInfoSubscription.unsubscribe();
    }
  }
  addSetting() {
    // if(this.settingForm.valid){
    this.settingSaveProcessing = true;
    this.processSaveCompanySettings();
  }

  unsetCardMode() {
    this.addCardMode = false;
    this.show = true;

  }
  cardUpdating = false;
  previousUpdatingCard: any;

  updateCardMode(modal, id, modalid) {
    this.addCardMode = true;
    this.cardUpdating = true;
    this.edituatpCard = false;
    this.cardAddForUapa = false;
    this.previousUpdatingCard = id;
    this.show = false;
    if (!this.isMobile1) {
      this.bsModalRef = this.modalService.show(modal, {
        initialState: {
        }, backdrop: true, ignoreBackdropClick: true
      });
    }
    setTimeout(() => {
      this.ngxSmartModalService.getModal(modalid).close()
    }, 100);
  }
  routeToQuickbooks() {
    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }
    this.adminPanelService.routeToQuickBooks = true;
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Integerations'));
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'setting',
          subType:'integrations',
          subType2: 'accounting',
          product:'quickbooks'
        },
        replaceUrl: false
      }
    );
  }
  openaddcardModal() {
    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }
    this.bsModalRef = this.modalService.show(this.currentModal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
  }
  currentModal: any;
  selectedUapaCard: any;
  selectedForDeletionCarDiscountCode:any;
  confirmDeleteCarDiscountCode(index, modal){
    this.selectedForDeletionCarDiscountCode = this.carRentalList[index];
    
    this.bsModalRef = this.modalService.show(modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
  }
  confirmDelete(index, modal) {
    this.selectedUapaCard = this.uatpCards[index];
    if (modal) {
      this.currentModal = modal;
    }
    this.bsModalRef = this.modalService.show(modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
  }
  deletingUatpCard = false;
  deleteUatpCard() {
    this.deletingUatpCard = true;
    this.adminPanelService.deleteUaptCard(this.selectedUapaCard.airline).subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.deletingUatpCard = false;

        this.maintainUAtpList(resp);

      } else {
        this.deletingUatpCard = false;
      }
    })
  }
 
  onEditUatpCard(index, modal) {
    this.addCardMode = true;
    this.edituatpCard = true;
    this.selectedUapaCard = this.uatpCards[index];
    this.cardAddForUapa = true
    if (modal) {
      this.currentModal = modal;
    }
    if (!this.isMobile1) {
      this.bsModalRef = this.modalService.show(modal, {
        initialState: {
        }, backdrop: true, ignoreBackdropClick: true
      });
    }
  }
  dropDownopen = false;
  getDepartmentName1(id){
    if(this.departmentValue && this.departmentValue.length > 0){
    if(id==='All dept'){
      return 'fuild.AllDepartments'
    }else{
    let departmentName = this.adminPanelService.getDepartmentName(id);  
    return departmentName;
    }
  }else{
    return 'employee.Select'
  }
  }
  searchByAirlineCode(term: string, item: any) {
    term = term.toLowerCase();
    return (item.id && item.id.toLowerCase().indexOf(term) > -1 || item.value && item.value.toLowerCase().indexOf(term) > -1);
  }
  getLabelValueUatp(){
    if (this.carRentalForm.controls['partnerCode'].value !=='') {
      let findValue = this.carRentalCompanies.findIndex(item => item.value === this.carRentalForm.controls['partnerCode'].value);
      let cardString= this.carRentalCompanies[findValue].label;
      return  cardString;
    } else  {
      return this.translateService.instant('setting.SelectCarRentalCompany');
    } 
  }
  filterSelectedDeptlist(item) {
    this.departmentArrayForDropdwon= this.departmentArrayForDropdwon.filter(item4 => item4.departmentId !=='All dept'); 
    this.existingDepartmentsForDropdwon = this.existingDepartmentsForDropdwon.filter(item4 => item4.departmentId !=='All dept');
    for (let option of item) {
      this.departmentArrayForDropdwon = this.departmentArrayForDropdwon.filter(item2 => item2.departmentId !== option);
    }
    
   
    for (let option of item) {
      let departmentArray=[];
      if(this.existingDepartmentsForDropdwon.length > 0){
        for(let item of this.existingDepartmentsForDropdwon){
          departmentArray.push(item);
        }
      }
     // departmentArray = this.sortListDept(this.adminPanelService.getDepartments());
      if (departmentArray && departmentArray.length > 0) {
       
        let department = departmentArray.find(item3 => item3.departmentId === option)
        if(department){
        this.departmentArrayForDropdwon.unshift(department);
        }
      }
      
    }
    this.departmentArrayForDropdwon.unshift({ value: 'All Departments', departmentId: 'All dept',name:"fuild.AllDepartments" });
    
  }
  getLabelValueDepartment(){
    if (this.dropDownopen) {
      return 'fuild.Typetosearch';;
    }else if(this.departmentValue && this.departmentValue.length === 0){
      return this.translateService.instant('employee.SelectDepartment');
    } else  {
      return '';
    }   
  }
  closeDropdown() {
    this.dropDownopen = false;
  }
  searchByDepartmentNameAndId(term: string, item: any) {
    term = term.toLowerCase();
    return (item.name && item.name.toLowerCase().indexOf(term) > -1)
  }
  goBack(){
    this.bsModalRef.hide();
  }
   isDeptCheckedForCarrental(option) {
    return this.departmentValue.indexOf(option) > -1;
  }
  deleteCarRental(){
    let carRenatlObj = {};
    carRenatlObj['partnerCode'] =  this.selectedForDeletionCarDiscountCode.partnerCode;
    this.deletingUatpCard =true;
    this.adminPanelService.addCarRental(carRenatlObj).subscribe(resp=>{
      if(resp && resp.success){
        this.deletingUatpCard =false;
        if(resp.data && resp.data.length >0){
          this.carRentalList = resp.data; 
        this.carRentalCompanies =   Constants.carRentalCompanies;
          for(let item1 of this.carRentalList){
            this.carRentalCompanies = this.carRentalCompanies.filter(item => item.value!==item1.partnerCode)
          }
        }else{
          this.carRentalCompanies =   Constants.carRentalCompanies;
          this.carRentalList=[];
        }
       
          this.bsModalRef.hide();
       
          
        this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"));
      }else if (resp && resp.error_message) {
        this.deletingUatpCard =false;
        this.bsModalRef.hide();
        this.toastr.error(resp.error_message);
      } else {
        this.deletingUatpCard =false;
        this.bsModalRef.hide();
        this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntprocessrequest.Pleasetryagainlaterorcontactsupport"));
      }
    }) 
  }
  addCarRental(msg){
    if(this.carRentalForm.invalid){
      this.carRentalForm.markAllAsTouched();
      return;
    }
    let carRenatlObj = {};
    carRenatlObj['departments'] = this.departmentValue.toString() ;
    if(this.departmentValue && this.departmentValue.length===1 && this.departmentValue[0]==='All dept'){
      carRenatlObj['departments'] ='-1';
    }
    carRenatlObj['partnerCode'] = this.carRentalForm.controls['partnerCode'].value;
    carRenatlObj['discountCode'] = this.carRentalForm.controls['discountCode'].value;
    carRenatlObj['billingNumber'] = this.carRentalForm.controls['billingNumber'].value;
    this.addCardProgress = true;
    
  this.adminPanelService.addCarRental(carRenatlObj).subscribe(resp=>{
    if(resp && resp.success){
      this.addCardProgress = false;
      if(resp.data && resp.data.length >0){
        this.carRentalList = resp.data;
        for(let item1 of this.carRentalList){
          this.carRentalCompanies = this.carRentalCompanies.filter(item => item.value!==item1.partnerCode)
        } 
      }
      if(msg==='addonlyone'){
        this.bsModalRef.hide();
      }else{
        this.departmentValue =[];
        this.departmentValue.push('All dept');
        this.carRentalForm = this.fb.group({
          partnerCode: ['', Validators.compose([Validators.required])],
          discountCode : ['', Validators.compose([Validators.required])],
          billingNumber : ['', ],
        }); 
      }
      this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"));
    }else if (resp && resp.error_message) {
      this.addCardProgress = false;
      this.bsModalRef.hide();
      this.toastr.error(resp.error_message);
    } else {
      this.addCardProgress = false;
      this.bsModalRef.hide();
      this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntprocessrequest.Pleasetryagainlaterorcontactsupport"));
    }
  })
  }
  onChangeDepartmentForCarRental(option, event) {
    if (event) {
      if (option == "All dept") {
        this.departmentValue=[];
        this.departmentValue.push('All dept');
        
      }
      else {
        this.departmentValue = this.departmentValue.filter(dept => {
          if (dept !== 'All dept') return true;
        });
        this.departmentValue.push(option);
      }
    } else if (!event && option == "All dept") {
      this.departmentValue = [];
    } else {
      this.departmentValue = this.departmentValue.filter(dept => {
        if (dept !== option && dept !== 'All dept') return true;
      });
    }
    this.isDeptCheckedForCarrental(option);
  }
  onEditRentalCard(index, modal){
    this.edituatpCard = true;
    this.carRentalCompanies =   Constants.carRentalCompanies;
    this.departmentValue =[];
    this.departmentArrayForDropdwon = this.existingDepartments;
    this.existingDepartmentsForDropdwon = this.existingDepartments;
    let departmentValue  = this.carRentalList[index].departments.split(',');
    
   
    for(let dept of departmentValue){
      if(dept==='-1'){
        this.onChangeDepartmentForCarRental('All dept', true)
      }else{
        let deptId = +dept
        this.onChangeDepartmentForCarRental(deptId, true)
      }
    }
    this.carRentalForm = this.fb.group({
      partnerCode: [this.carRentalList[index].partnerCode, Validators.compose([Validators.required])],
      discountCode : [this.carRentalList[index].discountCode, Validators.compose([Validators.required])],
      billingNumber : [this.carRentalList[index].billingNumber, ],
    });
    this.carRentalForm.controls['partnerCode'].disable();
    this.bsModalRef = this.modalService.show(modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
  }
  openCarRentalModal(modal){
    this.edituatpCard =false;
    this.departmentValue =[];
    for(let item1 of this.carRentalList){
      this.carRentalCompanies = this.carRentalCompanies.filter(item => item.value!==item1.partnerCode)
    } 
    this.departmentValue.push('All dept');
    this.departmentArrayForDropdwon = this.existingDepartments;
    this.existingDepartmentsForDropdwon = this.existingDepartments;
    this.carRentalForm = this.fb.group({
      partnerCode: ['', Validators.compose([Validators.required])],
      discountCode : ['', Validators.compose([Validators.required])],
      billingNumber : ['', ],
    });
    this.bsModalRef = this.modalService.show(modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
  }
  setCardForUapa(modal,type) {
    this.addCardMode = true;
    this.edituatpCard = false;
    if (modal) {
      this.currentModal = modal;
    }
    this.cardAddForUapa = true;
    if (!this.isMobile1) {
      this.bsModalRef = this.modalService.show(modal, {
        initialState: {
        }, backdrop: true, ignoreBackdropClick: true
      });
    }
  
  }
  setCardMode(modal?, modal1?) {
    this.addCardMode = true;
    this.edituatpCard = false;
    this.cardAddForUapa = false;
    this.show = false;
    if (modal) {
      this.currentModal = modal;
    }
   
      if (!this.isMobile1) {
        this.bsModalRef = this.modalService.show(modal, {
          initialState: {
          }, backdrop: true, ignoreBackdropClick: true
        });
      } else {
        setTimeout(() => {
          this.clicked();
        }, 100);
      
    }
  }
  private cardTokenData: any;
  public handleBackFromAddCard(data: any) {
    if (!data) {
      if (this.bsModalRef) {
        this.bsModalRef.hide();
      }
      this.updatingPaymentMethodBackup = false;
      this.updatingPaymentMethodPrimary = false;
      this.stopLoader()
      setTimeout(() => {
        if (this.cardUpdating) {
          this.cardUpdating = false;
        }
        this.unsetCardMode();
      }, 300);


      return;
    }


    let tokenData = JSON.parse(data);
    if (this.cardAddForUapa && this.edituatpCard && tokenData && tokenData.process === 'editUapaCard') {
      this.edituatpCard = false;

    }
    if (tokenData && !tokenData.type && this.cardAddForUapa && this.edituatpCard) {
      this.adminPanelService.editUatpCard(data).subscribe(resp => {
        if (resp && resp.status === 'success') {
          
          this.getUatpList();

        }
      })
      return;
    }
    if (tokenData && tokenData.type === 'newCardAdded') {

      // this.userAccountInfoService.fetchUserAccountInfo(this.emailId, this.sToken);
      let cardTokens: any = tokenData.tokens;
      if (this.cardAddForUapa && !this.edituatpCard) {
        if (tokenData && tokenData.type === 'newCardAdded' && cardTokens.gToken) {
          let item = {
            gallopTokenId: tokenData.result.gallopTokenId, name: tokenData.result.name, last4: tokenData.result.last4,

            expMonth: tokenData.result.exp_month,
            expYear: tokenData.result.exp_year,

            departmentIds: this.adminPanelService.uatpDept,

            airline: this.adminPanelService.uatpAirline,
            accountCode: this.adminPanelService.uatpPPaccount
          }
          if (this.cardAddForUapa && !this.edituatpCard && tokenData.process === 'editUapaCard') {
            this.deletingUatpCard = true;
            this.adminPanelService.deleteUaptCard(this.selectedUapaCard.airline).subscribe(resp => {
              if (resp && resp.status === 'success') {
                this.deletingUatpCard = false;
                this.adminPanelService.addUatpCard(item).subscribe(resp => {
                  if (resp && resp.status === 'success') {
                    
                    this.maintainUAtpList(resp);

                  }
                })
              } else {
                this.deletingUatpCard = false;
              }
            })

          } else {

            this.adminPanelService.addUatpCard(item).subscribe(resp => {
              if (resp && resp.status === 'success') {
                
                this.maintainUAtpList(resp);

              }
            })
          }
        } else if (cardTokens && cardTokens.error && cardTokens.error.length > 0) {
          // this.toastr.error(cardTokens.error, 'Card Error!');
          this.changingValue.next({ value: '', value1: false });
          // this.addCardChild.setAddCardProgress(false);
          //this.addCardChild.setErrorMessage('');
        }
      }

      if (!this.cardAddForUapa) {
        if (cardTokens && cardTokens.error && cardTokens.error.length > 0) {
          // this.toastr.error(cardTokens.error, 'Card Error!');
          this.changingValue.next({ value: '', value1: false });
          // this.addCardChild.setAddCardProgress(false);
          //this.addCardChild.setErrorMessage('');
        }

        else if (cardTokens.token && cardTokens.gToken) {
          this.cardTokenData = cardTokens;
          GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
            'cardTokenCreated', 'WebSearchUI'
          );
          if (!this.cardAddForUapa) {
            this.adminPanelService.requestSaveCardInfo(cardTokens.token, cardTokens.gToken).subscribe(res => {
              // this.userAccountInfoService.requestSaveCardInfo(cardTokens.token, cardTokens.gToken).subscribe(res => {
              if (res.status === 'success') {
                this.addCardFlow = true;
                this.adminPanelService.addNewCard = true;
                this.updatingPaymentMethod = true;
                // this.userAccountInfoService.fetchUserAccountInfo(false);
                if (!this.cardUpdating) {
                  this.getPaymentOptions();
                }
                if (res.cards && res.cards.Item && res.cards.Item.preferences && res.cards.Item.preferences.payment.stripe_card && this.adminPanelService.selectedqboCardvalue !== '') {
                  this.adminPanelService.getQboCardUpload(this.adminPanelService.selectedqboCardvalue, res.cards.Item.preferences.payment.stripe_card.id).subscribe(resp => {
                    if (resp && resp.status === 'success') {
                      this.adminPanelService.selectedqboCardvalue = '';
                      // 
                    }
                  })
                }
                if (this.bsModalRef) {
                  this.bsModalRef.hide();
                }
                if (this.cardUpdating) {
                  if (res.cards && res.cards.Item && res.cards.Item.preferences && res.cards.Item.preferences.payment.stripe_card) {

                    this.adminPanelService.getRoutespringUpdateCreditCard(this.previousUpdatingCard, res.cards.Item.preferences.payment.stripe_card.id).subscribe(resp => {
                      if (resp && resp.status === 'success') {

                        this.getPaymentOptions();
                        // 
                      }
                    })

                  }
                }
                setTimeout(() => {
                  this.cardUpdating = false;
                  this.unsetCardMode();
                }, 300);


              } else if (res.status === 'CARDERROR') {
                // this.toastr.error(res.message, 'Card Error!');
                // this.changingValue.next('cardError');
                this.changingValue.next({ value: res.message, value1: false });
                // this.addCardChild.setErrorMessage(res.message);
                //this.addCardChild.setAddCardProgress(false);

              } else {
                // this.toastr.error(res.message, 'Error!');
                this.changingValue.next({ value: res.message, value1: false });
                // this.addCardChild.setErrorMessage(res.message);
                // this.addCardChild.setAddCardProgress(false);
              }
            });
          }
        } else {
          // this.changingValue.next({ value: this.translateService.instant('paymentDetails.UnknownErrorPleasetryagain').toString(), value1: false });
          //this.addCardChild.setErrorMessage(this.translateService.instant('paymentDetails.UnknownErrorPleasetryagain').toString());
          //    this.addCardChild.setAddCardProgress(false);
        }
      }
    }
  }
  isLoggedIn(): boolean {
    return this.userAccountInfoService.isLoggedIn();
  }
  processSaveCompanySettings() {
    let companySettings: any = {};
    companySettings['companyName'] = this.companyName ? this.companyName : '';
    if (this.selectedCardIndex > -1) {
      companySettings['defaultCardId'] = this.getCardList()[this.selectedCardIndex].id;
    }

    this.adminPanelService.saveCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId()
      , companySettings).subscribe(res => {
        if (res && res.success) {
          this.settingSaveProcessing = false;
          this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
          if (this.switchon) {
            this.toastr.warning(this.translateService.instant("setting.Settingssavedsuccessfully"));
          } else {
            this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"));
          }
        } else if (res && res.error_message) {
          this.settingSaveProcessing = false;
          this.toastr.error(res.error_message);
        } else {
          this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntprocessrequest.Pleasetryagainlaterorcontactsupport"));
        }
      }, error => {
        if (error.status != 403) {
          setTimeout(() => {
            this.settingSaveProcessing = false;
            let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
            this.toastr.error(resultErrorMessage);
          }, 100);
        }
      })
  }
  isCardsSupported(){
    return this.userAccountInfoService && this.userAccountInfoService.userhascard ;
  }
}
