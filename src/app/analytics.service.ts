import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';

export class GallopAnalyticsUtil {
  constructor() {
  }
  
    static trackAction(ngxAnalytics: GoogleAnalyticsService, action: string) {
      ngxAnalytics.event(action);
    }
  
    static trackActionWithCategory(ngxAnalytics: GoogleAnalyticsService, action: string, category: string) {
      ngxAnalytics.event(action, { category });
    }
  
    static trackActionWithCategoryAndLabel(ngxAnalytics: GoogleAnalyticsService, action: string, category: string, label: string) {
      ngxAnalytics.event(action, { category, label });
    }

}
