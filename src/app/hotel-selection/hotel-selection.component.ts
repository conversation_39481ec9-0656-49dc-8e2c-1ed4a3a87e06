import { Component, OnInit, Input } from '@angular/core';
import { AbstractControl, FormArray, UntypedFormBuilder, UntypedFormGroup, Validators, FormControl, Form, ValidationErrors } from '@angular/forms';
import { NavigationExtras, Router, ActivatedRoute } from '@angular/router';
import { SearchService } from '../search.service';
import { FilterService } from '../filter.service';
import { Constants } from '../util/constants';
import { DeviceDetailsService } from '../device-details.service';
import { Subscription } from 'rxjs';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { NgxGalleryOptions, NgxGalleryImage, NgxGalleryAnimation } from '@kolkov/ngx-gallery';
import { HotelDetailResult } from '../entity/hotel-detail-result';
import { SearchResultService } from '../search-result.service';
import { HotelResult } from '../entity/hotel-result';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { HotelSearchService } from '../hotel-search.service';
import { Location } from '@angular/common';
import { CommonUtils } from '../util/common-utils';
import { EmailQuoteOptionsService } from '../email-quote-options.service';
import { DateUtils } from '../util/date-utils';
import { TranslateService } from '@ngx-translate/core';
import { HandlerTypes } from '../enum/handler.type.';
import { ConnectionService } from 'ng-connection-service';
import { PopupComponent } from '../popup/popup.component';
import { HotelQueryParam } from '../entity/hotel-query-param';
import { environment } from 'src/environments/environment';
import { BookingService } from '../booking.service';
import { UserAccountService } from '../user-account.service';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';

@Component({
    selector: 'hotel-selection-component',
    templateUrl: './hotel-selection.component.html',
    styleUrls: ['./hotel-selection.component.scss'],
    standalone: false
})


export class hotelSelectionComponent implements OnInit {
    HOTEL_RATINGS = [1.0, 2.0, 3.0, 4.0, 5.0];
    policiesForm: UntypedFormGroup;
    isCollapsed = true;
    isMobile: boolean;
    sortValue = this.translateService.instant('ngOption.Recommended');
    lat: number = 51.678418;
    lng: number = 7.809007;
    isSeenComplete = false;
    showGovernmentRate = true;
    showLoyalitySwitch=false;
    loyalityRoomToggle=false;
    showErrorForAAAMsg=false;
    showAAARate = true;
    showAAASwitch= false;
    showSwitch = false;
    truncating = true;
    extraRateInfoTrucating = true;
    extraRateInfoIndex = -1;
    sortingDropdownValue: 'recommended';
    deviceSubscription: Subscription;
    text = 'Nestled in the lower Garden District, across from the St. Charles Street Car line, this nonsmoking hotel in New Orleans, Louisiana offers a free continental breakfast and free WiFi.If the description.';
    hotelBrandOptions = Constants.HOTEL_BRAND_OPTIONS;
    amenitiesOptions = Constants.AMENITIES_OPTIONS;
    sortOptionsHotel = Constants.SORT_OPTIONS_HOTEL;
    ratingOptions = [1];
    hotelElementCount = 5;
    cancellationPoliciesDropdownItems = Constants.CANCELLATION_ITEMS;
    galleryOptions: NgxGalleryOptions[];
    galleryImages: NgxGalleryImage[];
    galleryRoomsImages: NgxGalleryImage[];
    selectedHotelResult: HotelResult;
    hotelDataObj: any;
    hotelRoomDetails: any;
    revalidateSubscription: Subscription;
    roomRulesAndRateDescSubscription: Subscription;
    loadingInProgress: boolean;
    revalidateResponseStatus: string = 'NONE';
    bsModalRef: BsModalRef;
    connectionListener: Subscription;
    visibleHotelCounter = 0;
    buttonDisabled=[];
    totalVisibleHotels = 0;
    constructor(private fb: UntypedFormBuilder,
        public  searchService: HotelSearchService,
        public searchService1: SearchService,
        private titleService: Title,
        private bookingService: BookingService,
        public userAccountService: UserAccountService,
        private flightSearchService: SearchService,
        private searchResultService: SearchResultService,
        private gallopLocalStorage: GallopLocalStorageService,
        private activatedRoute: ActivatedRoute,
        private deviceDetailsService: DeviceDetailsService,
        private location: Location,
        private router: Router,
        private filterService: FilterService,
        private connectionService: ConnectionService,
        private modalService: BsModalService,
        private toastr: ToastrService,
        public translateService: TranslateService) {
        this.createForm();
        //        this.getNights();

    }


    ngOnInit(): void {
        this.sortOptionsHotel = this.sortOptionsHotel.filter(item=> item.id!=='distance');
        if(this.searchService1.comingFromSelectionPAge){
            if(this.searchService1.multihotelQuery && this.searchService1.multihotelQuery.length >0){
            this.searchService1.multihotelQuery.pop();
            }
            if(this.bookingService.cardAllowed && this.bookingService.cardAllowed.length >0){
                            this.bookingService.cardAllowed.pop();
            }
            let seatArray = JSON.parse(this.gallopLocalStorage.getItem("selectedSeat"));
      if(seatArray && seatArray.length > 0){
        //  seatArray.pop();
          this.gallopLocalStorage.removeItem("selectedSeat");
        }
            this.bookingService.bookingData=null;
            }
        if(this.searchService1.multiTripBooking && this.searchService1.comingFromSelectionPAge && !this.searchService1.multiTripHotelDelete){
            let selectHotel = JSON.parse(this.gallopLocalStorage.getItem("selectedHotel"));
            let selectedHotelDetailsObj: any = JSON.parse(this.gallopLocalStorage.getItem("selectedHotelDetailedObj"));
            if(selectHotel  && selectHotel.length > 0){
              selectHotel.pop();
            }
            if(selectedHotelDetailsObj  && selectedHotelDetailsObj.length > 0){
                selectedHotelDetailsObj.pop();
              }
              this.gallopLocalStorage.setItem("selectedHotelDetailedObj", JSON.stringify(selectedHotelDetailsObj));
            this.gallopLocalStorage.setItem("selectedHotel", JSON.stringify(selectHotel));
            this.searchService1.comingFromSelectionPAge =false;
          }
        this.hotelDataObj = this.searchResultService.selectedHotelDetailObj;
        let selectedHotelResult = this.searchService.selectedHotel;
        this.selectedHotelResult  =selectedHotelResult;
        this.deviceSubscription = this.deviceDetailsService.isMobile1().subscribe(isMobile => {
            this.isMobile = isMobile;


        });
        this.galleryOptions = [
            {
                width: '474px',
                height: '364px',
                thumbnailsColumns: 11,
                thumbnailsMargin: 15,
                thumbnailSize: '40',
                thumbnailsPercent: 15,
                thumbnailMargin: 3,
                imageAnimation: NgxGalleryAnimation.Slide,
                arrowPrevIcon: 'fa fa-angle-left',
                arrowNextIcon: 'fa fa-angle-right',
                preview: false,
                imageInfinityMove: true
            }
        ];

        this.galleryImages = [
            {
                small: 'assets/images/hotel/hotel.png',
                medium: 'assets/images/hotel/hotel-m.png',
                big: 'assets/images/hotel/hotel-b.png'
            }
        ];


        this.requestHotelDetails(this.selectedHotelResult);
        this.updateImagesFromLocal();
        // this.updateImages();
    }

    createForm(): void {
        this.policiesForm = this.fb.group({
            cancellationPoliciesDropdown: ['ALL']
        });
    }
    getRoomPolicyStatus(roomIndex: number) {
        if (this.hotelDataObj
            && "hotelRateList" in this.hotelDataObj
            && this.hotelDataObj.roomPolicyStatus
            && this.hotelDataObj.roomPolicyStatus.length > 0) {
            return this.hotelDataObj.roomPolicyStatus[roomIndex];
        }
        return true;
    }
    getRoomPriceTotalFee(item) {
        let hotelPrice = 0;
        if(item != null && this.selectedHotelResult.displayCurrency){
            hotelPrice = (item.approximateTotal == null) ? item.displayTotal
           
            :item.approximateTotal
                .substring(3);
        let hotelSearchQueryParam: HotelQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));
        var resortFee = item.resortFee ? Number.parseFloat(item.resortFee.substring(3)) : null;
        hotelPrice = (hotelPrice * parseInt(hotelSearchQueryParam.roomCount)) + resortFee;
        }else{
        if (item != null) {
            hotelPrice = (item.approximateTotal == null || !item.approximateTotal) ? item.total
                .substring(3)
                : item.approximateTotal
                    .substring(3);
            let hotelSearchQueryParam: HotelQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));
            var resortFee = item.resortFee ? Number.parseFloat(item.resortFee.substring(3)) : null;
            hotelPrice = (hotelPrice * parseInt(hotelSearchQueryParam.roomCount)) + resortFee;
        }
    }
        return hotelPrice;
    }
    sortOptionChanged(sortOption) {
        if (!sortOption) return;
        if (sortOption.id) {
          this.sortValue = sortOption.value ? sortOption.value : this.translateService.instant('ngOption.Recommended');
        }
           this.applySorting(this.hotelDataObj.hotelRateList, this.sortValue);
      }
      public applySorting(hotelSearchResponse, currentSortOptionId) {
        setTimeout(() => {
          if (currentSortOptionId === 'Price') {
            this.hotelDataObj.hotelRateList=    this.sortResultsByPrice(hotelSearchResponse);
          }  else if (currentSortOptionId === 'Recommended') {
            this.hotelDataObj.hotelRateList=  this.sortResultsAsRecommended(hotelSearchResponse);
          }
        }, 200);
      }
      sortResultsAsRecommended(hotelSearchResponse){
        hotelSearchResponse = hotelSearchResponse.sort(function (a, b) {
            if (a.rsRank < b.rsRank) return -1;
            else if (a.rsRank > b.rsRank) return 1;
            else return 0;
          });  
          return hotelSearchResponse;
      }
      sortResultsByPrice(hotelSearchResponse){
          const self = this;
        hotelSearchResponse = hotelSearchResponse.sort(function (a, b) {
            const priceA = self.getBasePriceFromGivenRate(a);
            const priceB = self.getBasePriceFromGivenRate(b);
            if (priceA < priceB) {return -1; }
            if (priceA > priceB) {return 1; }
            return 0;
          });
          return hotelSearchResponse;
      }

    selectRoom(l: number,hotelRoomRateObj ?) {
        if(this.disableSelectionIfCancellationPolicyNotAvailable(l,hotelRoomRateObj)){
            this.toastr.error("Fetching cancellation policy, please wait.");
            return;
        }
        this.selectedHotelResult.price = this.getRoomPriceForBooking(l,false);
        this.searchService1.displayCurrency = this.selectedHotelResult.displayCurrency;
        this.searchService1.displayPrice = this.getRoomPriceForBooking(l,true);
        this.userAccountService.notToRefreshForm = false;
        this.selectedHotelResult['checkInDate'] = this.hotelDataObj.checkInDate;
        this.selectedHotelResult['checkOutDate'] = this.hotelDataObj.checkOutDate;
        this.userAccountService.paymentPageSave = false;
        this.userAccountService.promptUserTosaveProfile = true;
        this.searchResultService.broadcastSelectedHotel(this.selectedHotelResult, 0);
        this.gallopLocalStorage.setItem("selectedHotelRoom", JSON.stringify(this.getRoomTypeName(l)));
        const hotelSelectionData = JSON.parse(JSON.stringify(this.hotelDataObj));
        hotelSelectionData.hotelOutOfPolicyReason = hotelRoomRateObj.roomOutOfPolicyReason ;
        this.gallopLocalStorage.setItem("selectedHotelPolicyReason", JSON.stringify(hotelSelectionData.hotelOutOfPolicyReason));
        hotelSelectionData.inPolicy = hotelRoomRateObj.roomOutOfPolicyReason.reason === 'WITHIN_POLICY' ;
        hotelSelectionData.minPrice = this.selectedHotelResult.price;
        hotelSelectionData.amenities = this.selectedHotelResult.amenities;
        hotelSelectionData.originalPrice = this.selectedHotelResult.price;
        hotelSelectionData.hotelRateList = [];
        hotelSelectionData.distanceMile = this.selectedHotelResult.distanceMile;
        hotelSelectionData.thumbnailImage = this.selectedHotelResult.thumbnailImage;
        hotelSelectionData.hotelRateList.push(this.hotelDataObj.hotelRateList[l]);
        hotelSelectionData.hotelRateDetail = this.hotelDataObj.hotelRateList[l];
        hotelSelectionData.roomsRules = [];
        hotelSelectionData.checkInDate = DateUtils.getFormattedDateWithoutTimeZoneFromFormat(this.hotelDataObj.checkInDate, 'yyyy-MM-dd');
        hotelSelectionData.checkOutDate = DateUtils.getFormattedDateWithoutTimeZoneFromFormat(this.hotelDataObj.checkOutDate, 'yyyy-MM-dd');

        let policies;
        let hotelRoomPolicies = [];
        if (false && this.hotelDataObj.hotelRateList[l].handleType === 'TravelPort') {
            //policies = this.getRoomCancellationRules(l);
        } else {
            policies = this.getCancellationPolicyText(l);
        }
        for (let policy of policies) {
            hotelRoomPolicies.push({ cancellationRule: policy });
        }
        hotelSelectionData.hotelRooms[0].hotelRates[0].cancellationPolicies = hotelRoomPolicies;

        hotelSelectionData.hotelRooms[0].hotelRoomName = this.getRoomTypeName(l).name;

        hotelSelectionData.fee = this.hotelDataObj.hotelRateList[l].fee ? this.hotelDataObj.hotelRateList[l].fee : null;
        hotelSelectionData.resortFee = this.hotelDataObj.hotelRateList[l].resortFee ? this.hotelDataObj.hotelRateList[l].resortFee : null;
        hotelSelectionData.tax = this.getTaxesAndFees(l,false);
        hotelSelectionData.resortFee = this.hotelDataObj.hotelRateList[l].resortFee ? this.hotelDataObj.hotelRateList[l].resortFee : null;
        hotelSelectionData.displayTax = this.getTaxesAndFees(l,true);
        hotelSelectionData.displayResortFee = this.hotelDataObj.hotelRateList[l].displayResortFee ? this.hotelDataObj.hotelRateList[l].displayResortFee : 0;
        hotelSelectionData.addPolicy = this.hotelDataObj.hotelRateList[l].policy;


        if (this.hotelDataObj.handlerToHotelInfoCommon && this.hotelDataObj.hotelRateList
            &&
            this.hotelDataObj.handlerToHotelInfoCommon[this.hotelDataObj.hotelRateList[l].handleType]) {
            hotelSelectionData.hotelChain
                = this.hotelDataObj.handlerToHotelInfoCommon[this.hotelDataObj.hotelRateList[l].handleType].hotelChain
            hotelSelectionData.hotelName
                = this.hotelDataObj.handlerToHotelInfoCommon[this.hotelDataObj.hotelRateList[l].handleType].hotelName;
            hotelSelectionData.hotelCode
                = this.hotelDataObj.handlerToHotelInfoCommon[this.hotelDataObj.hotelRateList[l].handleType].hotelCode;
            hotelSelectionData.address
                = this.hotelDataObj.handlerToHotelInfoCommon[this.hotelDataObj.hotelRateList[l].handleType].address;

        } else {
            hotelSelectionData.hotelChain = this.hotelDataObj.hotelChain;
            hotelSelectionData.hotelName = this.hotelDataObj.hotelName;
            hotelSelectionData.hotelCode = this.hotelDataObj.hotelCode;
            hotelSelectionData.address = this.hotelDataObj.address;
        }
        hotelSelectionData.source = this.hotelDataObj.hotelRateList[l].handleType;
        hotelSelectionData.handleType = this.hotelDataObj.hotelRateList[l].handleType;
        hotelSelectionData.hotelChainName = this.selectedHotelResult.hotelChainName
        hotelSelectionData.distanceInMilesFromMeeting = this.selectedHotelResult.distanceMile;

        hotelSelectionData.amenities = this.selectedHotelResult.amenities;
        hotelSelectionData.traflaRating =  Number(this.selectedHotelResult.starRating);
        if (isNaN(hotelSelectionData.traflaRating)) {
            hotelSelectionData.traflaRating = 3.0;
        }
        hotelSelectionData.rateId = this.selectedHotelResult.rateId;
        hotelSelectionData.distance = this.selectedHotelResult.drivingTime;
        hotelSelectionData.walkingTime = this.selectedHotelResult.walkingTime;
        //        hotelSelectionData.withinPolicy = this.getRoomPolicyStatus(l); //this.selectedHotelResult.withinPolicy;
        hotelSelectionData.latitude = this.selectedHotelResult.latitude;
        hotelSelectionData.longitude = this.selectedHotelResult.longitude;
        // hotelSelectionData.handleType =this.hotelDataObj.hotelRateList.handleType
        hotelSelectionData.prepay = EmailQuoteOptionsService.getPrePayNonRefundableFlag(hotelSelectionData).isPrePay;
        hotelSelectionData.nonRefundableStayIndicator = EmailQuoteOptionsService.getPrePayNonRefundableFlag(hotelSelectionData).isNonRefundable;
        // hotelSelectionData.source = this.selectedHotelResult.handleType;
        hotelSelectionData.serviceFee = this.hotelDataObj.hotelRateList[l].serviceFee;
        hotelSelectionData.isZipCodeRequired = this.hotelDataObj.hotelRateList[l].isZipCodeRequired;
        if(this.searchService1.multiTripBooking){
            let selectHotel = JSON.parse(this.gallopLocalStorage.getItem("selectedHotelDetailedObj"));
            if(selectHotel){
              selectHotel.push(hotelSelectionData);
            }else{
              selectHotel =[];
              selectHotel.push(hotelSelectionData)
            }
            this.gallopLocalStorage.setItem("selectedHotelDetailedObj", JSON.stringify(selectHotel));
          }else{
            this.gallopLocalStorage.removeItem("selectedHotelDetailedObj");
            let  selectHotel =[];
            selectHotel.push(hotelSelectionData)
          this.gallopLocalStorage.setItem("selectedHotelDetailedObj", JSON.stringify(selectHotel));
          }
       // this.gallopLocalStorage.setItem("selectedHotelDetailedObj", JSON.stringify(hotelSelectionData));
        this.goToTravelersForm();
    }

    roomSelectionFromGallery(index: number, hotelRoomRateObj) {
        this.selectRoom(index, hotelRoomRateObj);
    }
    getPerNightPrice(l: number) {
        if (this.hotelDataObj.stay > 1) {
            return (this.getBasePrice(l)) / this.hotelDataObj.stay;
        }
        return (this.getBasePrice(l));
    }
    getBasePrice(index) {
        if (this.hotelDataObj
            && 'hotelRateList' in this.hotelDataObj 
            && this.hotelDataObj.hotelRateList[index] != null ) {
                return this.getBasePriceFromGivenRate(this.hotelDataObj.hotelRateList[index]);
            }
        return 0;
    }
    getBasePriceFromGivenRate(hotelRateDetail){
        if (hotelRateDetail.displayBase) {
            return hotelRateDetail.displayBase;
        }else{
            return hotelRateDetail.base.substring(3);
        }
    }
    getRatingStarsMap(hotelStars) {
        // let hStar = Number.parseFloat(hotelStars);
        // let starMap = [];

        // for(let i = 1;i <= 5;i++){
        //   if(hStar >= i) starMap.push('full');
        //   else if(hStar > (i-1) && hStar < i) starMap.push('half');
        //   else starMap.push('none');
        // }
        // return starMap;
        return EmailQuoteOptionsService.getHotelRatingStarsMap(hotelStars);
    }

    widthWhenNoMap(item){
        if(item && item==='parent'){
            if(!this.isMobile && !this.searchService1.mapSupprted){
                return {'width':'100%'}
            }  
        }else{
        if(!this.isMobile && !this.searchService1.mapSupprted){
            return {'max-width':'100%'}
        }
    }
    }
    getHotelCurrency() {
        if(this.selectedHotelResult.displayCurrency){
            return this.selectedHotelResult.displayCurrency
        }else{
        return this.selectedHotelResult.currency;
        }
    }
    getCurrencySymbol(currencyCode: string): string {
        return CommonUtils.getCurrencySymbol(currencyCode);
    }
    selectRoomMobile(l, event, hotelRoomRateObj) {
        if (this.isMobile && event.target.id !== 'showMoreLink' && event.target.id !== 'showLessLink') {
            this.selectRoom(l, hotelRoomRateObj);
        }
    }
    goToHotelResult() {
        let hotelSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));
        this.router.navigate(["hotels"],
            {
                queryParams:
                {
                    query: encodeURIComponent(JSON.stringify(hotelSearchQueryParam)),
                    step: 0,
                    resultFound: 1
                },
                replaceUrl: true
            });
    }
    loadAllItems() {
        this.isSeenComplete = true;
    }
    goToTravelersForm() {
        window.scrollTo(0, 0);
        //this.titleService.setTitle('Traveller Details');
        this.titleService.setTitle(this.translateService.instant('search.TravellerDetails'));
        this.router.navigate(['/emailflow'], { relativeTo: this.activatedRoute, queryParams: { pageMode: 'WebSearch', } });

    }

    buildGalaryOption(imageInputObj: any) {
        let tempGalleryImages = [];
        let tempSizeKeyMap = { B: [], M: [], S: [] };
        for (let key in imageInputObj) {
            // if (this.hotelDataObj.hotelImages.imagesByHotelTypes.hasOwnProperty(key)) {
            let imageList = imageInputObj[key];
            for (let imageObj of imageList) {
                if (imageObj.sizeCode === 'B' || imageObj.sizeCode === 'M' || imageObj.sizeCode === 'S') {
                    tempSizeKeyMap[imageObj.sizeCode].push(imageObj.url);
                }
            }
            // }
        }

        for (let i = 0; i < tempSizeKeyMap['S'].length; i++) {
            let galleryObj = { small: "", medium: "", big: "" };
            if (i < tempGalleryImages.length) {
                galleryObj = tempGalleryImages[i];
                galleryObj.small = tempSizeKeyMap['S'][i];
            } else {
                galleryObj.small = tempSizeKeyMap['S'][i];
                galleryObj.medium = tempSizeKeyMap['S'][i];
                galleryObj.big = tempSizeKeyMap['S'][i];
                tempGalleryImages.push(galleryObj);
            }
        }
        for (let i = 0; i < tempSizeKeyMap['M'].length; i++) {
            let galleryObj = { small: "", medium: "", big: "" };
            if (i < tempGalleryImages.length) {
                galleryObj = tempGalleryImages[i];
                galleryObj.medium = tempSizeKeyMap['M'][i];
            } else {
                galleryObj.small = tempSizeKeyMap['M'][i];
                galleryObj.medium = tempSizeKeyMap['M'][i];
                galleryObj.big = tempSizeKeyMap['M'][i];
                tempGalleryImages.push(galleryObj);
            }
        }
        for (let i = 0; i < tempSizeKeyMap['B'].length; i++) {
            let galleryObj = { small: "", medium: "", big: "" };
            if (i < tempGalleryImages.length) {
                galleryObj = tempGalleryImages[i];
                galleryObj.big = tempSizeKeyMap['B'][i];
            } else {
                galleryObj.small = tempSizeKeyMap['B'][i];
                galleryObj.medium = tempSizeKeyMap['B'][i];
                galleryObj.big = tempSizeKeyMap['B'][i];
                tempGalleryImages.push(galleryObj);
            }
        }
        return tempGalleryImages;
    }
    updateImagesFromLocal() {
        if (this.selectedHotelResult.handleType === HandlerTypes.HOTELBEDS || this.selectedHotelResult.handleType === HandlerTypes.PRICELINE) {
            if (this.selectedHotelResult && 'hotelImages' in this.selectedHotelResult.hotelInfo && 'imagesByHotelTypes' in this.selectedHotelResult.hotelInfo.hotelImages) {
                this.galleryImages = this.buildGalaryOption(this.selectedHotelResult.hotelInfo.hotelImages.imagesByHotelTypes);
            }

            if (this.selectedHotelResult && 'hotelImages' in this.selectedHotelResult.hotelInfo && 'imageByRoomTypes' in this.selectedHotelResult.hotelInfo.hotelImages) {
                this.galleryRoomsImages = this.buildGalaryOption(this.selectedHotelResult.hotelInfo.hotelImages.imageByRoomTypes);
            }
        } else {
            if (this.selectedHotelResult && 'hotelImages' in this.selectedHotelResult && 'imagesByHotelTypes' in this.selectedHotelResult.hotelImages) {
                this.galleryImages = this.buildGalaryOption(this.selectedHotelResult.hotelImages.imagesByHotelTypes);
            }

            if (this.selectedHotelResult && 'hotelImages' in this.selectedHotelResult && 'imageByRoomTypes' in this.selectedHotelResult.hotelImages) {
                this.galleryRoomsImages = this.buildGalaryOption(this.selectedHotelResult.hotelImages.imageByRoomTypes);
            }
        }
    }
    updateImages() {
        if (this.hotelDataObj && 'hotelImages' in this.hotelDataObj && 'imagesByHotelTypes' in this.hotelDataObj.hotelImages) {
            if (this.hotelDataObj.hotelImages.imagesByHotelTypes && Object.keys(this.hotelDataObj.hotelImages.imagesByHotelTypes).length > 0) {
                this.galleryImages = this.buildGalaryOption(this.hotelDataObj.hotelImages.imagesByHotelTypes);
            }
        }

        if (this.hotelDataObj && 'hotelImages' in this.hotelDataObj && 'imageByRoomTypes' in this.hotelDataObj.hotelImages) {
            this.galleryRoomsImages = this.buildGalaryOption(this.hotelDataObj.hotelImages.imageByRoomTypes);
        }
    }
    isRoomTypePrePay(roomIndex: number) {
        if (this.hotelDataObj
            && "hotelRateList" in this.hotelDataObj
            && this.hotelDataObj.hotelRateList[roomIndex] != null
        ) {
            return this.hotelDataObj.hotelRateList[roomIndex].prePay;
        }
    }

    getTaxesAndFees(roomIndex: number,item?) {
        if (this.hotelDataObj
            && "hotelRateList" in this.hotelDataObj
            && this.hotelDataObj.hotelRateList[roomIndex] != null && this.hotelDataObj.hotelRateList[roomIndex].displayBase
        ) {
            let hotelSearchQueryParam: HotelQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));
            var taxAmount = CommonUtils.getTaxAmountFromRateDetail(this.hotelDataObj.hotelRateList[roomIndex], hotelSearchQueryParam.roomCount,true);
            return taxAmount;
        }else{
            if (this.hotelDataObj
                && "hotelRateList" in this.hotelDataObj
                && this.hotelDataObj.hotelRateList[roomIndex] != null
            ) {
                let hotelSearchQueryParam: HotelQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));
                var taxAmount = CommonUtils.getTaxAmountFromRateDetail(this.hotelDataObj.hotelRateList[roomIndex], hotelSearchQueryParam.roomCount,false);
                return taxAmount;
            }  
        }
        return 0;
    }
    getresortFee(roomIndex) {
        if (this.hotelDataObj
            && "hotelRateList" in this.hotelDataObj
            && this.hotelDataObj.hotelRateList[roomIndex] != null &&  this.hotelDataObj.hotelRateList[roomIndex].displayResortFee
        ){
            var resortFee = this.hotelDataObj.hotelRateList[roomIndex].displayResortFee ? Number.parseFloat(this.hotelDataObj.hotelRateList[roomIndex].displayResortFee) : null;
            return resortFee;
        } 
        else{
            var resortFee = this.hotelDataObj.hotelRateList[roomIndex].resortFee ? Number.parseFloat(this.hotelDataObj.hotelRateList[roomIndex].resortFee.substring(3)) : null;
            return resortFee;
        }
        return 0;
    }
    getRoomPriceForBooking(l: number,item ?){
        let hotelPrice = 0;
        if(item){
            if(this.hotelDataObj && "hotelRateList" in this.hotelDataObj && this.hotelDataObj.hotelRateList[l] != null && this.selectedHotelResult.displayCurrency ){
                hotelPrice = (this.hotelDataObj.hotelRateList[l].approximateTotal == null) ? this.hotelDataObj.hotelRateList[l].displayTotal
               
                : this.hotelDataObj.hotelRateList[l].approximateTotal
                    .substring(3);
            let hotelSearchQueryParam: HotelQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));
           // var resortFee = this.hotelDataObj.hotelRateList[l].resortFee ? Number.parseFloat(this.hotelDataObj.hotelRateList[l].resortFee.substring(3)) : null;
            hotelPrice = (hotelPrice * parseInt(hotelSearchQueryParam.roomCount)) 
            }
        }else{
            if(this.hotelDataObj && "hotelRateList" in this.hotelDataObj && this.hotelDataObj.hotelRateList[l] != null) {
                hotelPrice = (this.hotelDataObj.hotelRateList[l].approximateTotal == null) ? this.hotelDataObj.hotelRateList[l].total
                    .substring(3)
                    : this.hotelDataObj.hotelRateList[l].approximateTotal
                        .substring(3);
                let hotelSearchQueryParam: HotelQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));
                // var resortFee = this.hotelDataObj.hotelRateList[l].resortFee ? Number.parseFloat(this.hotelDataObj.hotelRateList[l].resortFee.substring(3)): null;
                hotelPrice = (hotelPrice * parseInt(hotelSearchQueryParam.roomCount));  
        }
        }
        return hotelPrice;
    }
    getRoomPrice(l: number,item ?) {
        let hotelPrice = 0;
        
            if(this.hotelDataObj && "hotelRateList" in this.hotelDataObj && this.hotelDataObj.hotelRateList[l] != null && this.selectedHotelResult.displayCurrency ){
                hotelPrice = (this.hotelDataObj.hotelRateList[l].approximateTotal == null) ? this.hotelDataObj.hotelRateList[l].displayTotal
               
                : this.hotelDataObj.hotelRateList[l].approximateTotal
                    .substring(3);
            let hotelSearchQueryParam: HotelQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));
           // var resortFee = this.hotelDataObj.hotelRateList[l].resortFee ? Number.parseFloat(this.hotelDataObj.hotelRateList[l].resortFee.substring(3)) : null;
            hotelPrice = (hotelPrice * parseInt(hotelSearchQueryParam.roomCount)) 
            }
            else if(this.hotelDataObj && "hotelRateList" in this.hotelDataObj && this.hotelDataObj.hotelRateList[l] != null) {
                hotelPrice = (this.hotelDataObj.hotelRateList[l].approximateTotal == null) ? this.hotelDataObj.hotelRateList[l].total
                    .substring(3)
                    : this.hotelDataObj.hotelRateList[l].approximateTotal
                        .substring(3);
                let hotelSearchQueryParam: HotelQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));
                // var resortFee = this.hotelDataObj.hotelRateList[l].resortFee ? Number.parseFloat(this.hotelDataObj.hotelRateList[l].resortFee.substring(3)): null;
                hotelPrice = (hotelPrice * parseInt(hotelSearchQueryParam.roomCount));  
        }
    
        return hotelPrice;
    }
    getRoomPriceWithoutResortFee(l: number) {
        if(this.hotelDataObj && "hotelRateList" in this.hotelDataObj && this.hotelDataObj.hotelRateList[l] != null && this.selectedHotelResult.displayCurrency){
            return this.hotelDataObj.hotelRateList[l].displayTotal;
        }
        return this.hotelDataObj.hotelRateList[l].total.substring(3);
    }

    getRoomBedType(l: number) {
        let bedTypeName = "";
        if (this.hotelDataObj && "hotelRateList" in this.hotelDataObj && this.hotelDataObj.hotelRateList[l] != null) {
            if (this.hotelDataObj.hotelRateList[l].inclusions && this.hotelDataObj.hotelRateList[l].inclusions.bedTypes[0].code !== 248) {
                let bedType = Constants.HOTEL_BEDTYPES.find(obj => obj.Code === this.hotelDataObj.hotelRateList[l].inclusions.bedTypes[0].code);
                if (bedType) {
                    bedTypeName = bedType.Name;
                }
            }

            if (bedTypeName.length == 0) {
                let bedtypeStringSearchInput = "";
                for (let k = 0; k < this.hotelDataObj.hotelRateList[l].roomRateDescription.length; k++) {
                    for (let t = 0; t < this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text.length; t++) {
                        let tempRoomName: string = this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text[t];
                        bedtypeStringSearchInput = bedtypeStringSearchInput + " " + tempRoomName;
                    }
                }
                bedtypeStringSearchInput = bedtypeStringSearchInput.replace(' ', '').toLowerCase();
                for (let bedName of Constants.POSSIBLE_BED_TYPES_TEXTS) {
                    if (bedtypeStringSearchInput.indexOf(bedName + 'bed') >= 0
                        || bedtypeStringSearchInput.indexOf(bedName + '-bed') >= 0) {
                        bedTypeName = bedTypeName + (bedTypeName.length > 0 ? ', ' + bedName : bedName);
                    }
                }
            }
        }
        return bedTypeName;
    }

    private buildRoomRateDetailsMap(hotelRateDetailObj: any): any {
        var roomRateDetailsMap = {};
        var textOfRoomElement = [''];
        var rateName = '';
        for (let k = 0; k < hotelRateDetailObj.roomRateDescription.length; k++) {
            var roomText = '';
            for (let t = 0; t < hotelRateDetailObj.roomRateDescription[k].text.length; t++) {
                roomText += ' ' + hotelRateDetailObj.roomRateDescription[k].text[t];
            }
            if (hotelRateDetailObj.roomRateDescription[k].name === 'Room') {
                var lastIndexOfDot = roomText.lastIndexOf('.');
                if (lastIndexOfDot > 50) {
                    roomText = roomText.split('.')[0];
                }
                textOfRoomElement = hotelRateDetailObj.roomRateDescription[k].text;
            }
            if (hotelRateDetailObj.roomRateDescription[k].name === 'Rate'
                && hotelRateDetailObj.roomRateDescription[k].text
                && hotelRateDetailObj.roomRateDescription[k].text.length > 0
            ) {
                rateName = hotelRateDetailObj.roomRateDescription[k].text[0];
            }
            if (hotelRateDetailObj.roomRateDescription[k].name === 'Room rate') {
                var textArrLen = hotelRateDetailObj.roomRateDescription[k].text.length;
                if (hotelRateDetailObj.roomRateDescription[k].text[0] === 'COMMISSIONABLE'
                    && textOfRoomElement && textOfRoomElement.length > 0
                    && textOfRoomElement[0].toLocaleLowerCase()
                        .indexOf(hotelRateDetailObj.roomRateDescription[k].text[textArrLen - 1]
                            .toLocaleLowerCase()) != -1) {
                    var tmpRoomText = hotelRateDetailObj.roomRateDescription[k].text[textArrLen - 1];
                    roomRateDetailsMap['Room'] = tmpRoomText.trim();
                }
            }
            roomRateDetailsMap[hotelRateDetailObj.roomRateDescription[k].name] =
                roomText.trim();
        }
        if (rateName && rateName.trim().length > 0) {
            if (roomRateDetailsMap['Room'] && roomRateDetailsMap['Room'].trim().length > 0) {
                roomRateDetailsMap['Room'] = rateName.trim() + ', ' + roomRateDetailsMap['Room'];
            } else {
                roomRateDetailsMap['Room'] = rateName.trim();
            }
        }
        return roomRateDetailsMap;
    }
    getHotelRoomDetails() {
        var hotelRateDetails = {};
        if (this.hotelDataObj
            && 'hotelRateList' in this.hotelDataObj
            && this.hotelDataObj.hotelRateList != null
            && this.hotelDataObj.hotelRateList.length > 0) {
            for (var rateCounter = 0; rateCounter < this.hotelDataObj.hotelRateList.length; rateCounter++) {
                if (this.hotelDataObj
                    && 'hotelRateList' in this.hotelDataObj
                    && this.hotelDataObj.hotelRateList[rateCounter] != null) {
                    hotelRateDetails[rateCounter] = this.buildRoomRateDetailsMap(this.hotelDataObj.hotelRateList[rateCounter]);
                    if (this.hotelDataObj.hotelRateList[rateCounter].handleType === 'TravelPort') {
                        this.buttonDisabled[rateCounter]=true;
                        this.requestRoomRulesAndRateDescription(rateCounter);
                    }
                }
            }
        }
        return hotelRateDetails;
    }

    getRoomTextKeys(roomIndex: number) {
        let keys = '';
        for (let rateDesc of this.hotelDataObj.hotelRateList[roomIndex].roomRateDescription) {
            keys = keys + ',' + rateDesc.name;
        }
        return keys;
    }
    getRoomTypeName(roomIndex: number) {
        // if (this.hotelRoomDetails == null){
        //     this.hotelRoomDetails = this.getHotelRoomDetails();
        // }
        let hotelRoomDetailsLocal = this.buildRoomRateDetailsMap(this.hotelDataObj.hotelRateList[roomIndex]);
        var roomNameText = '';
        var roomDetailsText = null;
        if (hotelRoomDetailsLocal
            && hotelRoomDetailsLocal['Room'] && hotelRoomDetailsLocal['Room'].length > 0) {
            roomNameText = hotelRoomDetailsLocal['Room'];
        } else
            if (hotelRoomDetailsLocal
                && hotelRoomDetailsLocal['Description'] && hotelRoomDetailsLocal['Description'].length > 0) {
                roomNameText = hotelRoomDetailsLocal['Description'];
            } else
                if (hotelRoomDetailsLocal
                    && hotelRoomDetailsLocal['Rate'] && hotelRoomDetailsLocal['Rate'].length > 0) {
                    roomNameText = hotelRoomDetailsLocal['Rate'];
                } else
                    if (hotelRoomDetailsLocal
                        && hotelRoomDetailsLocal['Rate description'] && hotelRoomDetailsLocal['Rate description'].length > 0) {
                        roomNameText = hotelRoomDetailsLocal['Rate description'];
                    } else
                        if (hotelRoomDetailsLocal
                            && hotelRoomDetailsLocal['Room detail'] && hotelRoomDetailsLocal['Room detail'].length > 0) {
                            roomNameText = hotelRoomDetailsLocal['Room detail'];
                        }
        if (hotelRoomDetailsLocal
            && hotelRoomDetailsLocal['RoomMoreDetails'] && hotelRoomDetailsLocal['RoomMoreDetails'].length > 0) {
            roomDetailsText = hotelRoomDetailsLocal['RoomMoreDetails'];
        }
        return { 'name': roomNameText, 'roomDetailsText': roomDetailsText };
    }

    getRoomTypeNameBackup(roomIndex: number) {
        // if (this.hotelRoomDetails == null){
        //     this.hotelRoomDetails = this.getHotelRoomDetails();
        // }
        let hotelRoomDetailsLocal = this.buildRoomRateDetailsMap(roomIndex);
        var roomNameText = '';
        if (this.hotelRoomDetails[roomIndex]
            && this.hotelRoomDetails[roomIndex]['Room'] && this.hotelRoomDetails[roomIndex]['Room'].length > 0) {
            roomNameText = this.hotelRoomDetails[roomIndex]['Room'];
        } else
            if (this.hotelRoomDetails[roomIndex]
                && this.hotelRoomDetails[roomIndex]['Description'] && this.hotelRoomDetails[roomIndex]['Description'].length > 0) {
                roomNameText = this.hotelRoomDetails[roomIndex]['Description'];
            } else
                if (this.hotelRoomDetails[roomIndex]
                    && this.hotelRoomDetails[roomIndex]['Rate'] && this.hotelRoomDetails[roomIndex]['Rate'].length > 0) {
                    roomNameText = this.hotelRoomDetails[roomIndex]['Rate'];
                } else
                    if (this.hotelRoomDetails[roomIndex]
                        && this.hotelRoomDetails[roomIndex]['Rate description'] && this.hotelRoomDetails[roomIndex]['Rate description'].length > 0) {
                        roomNameText = this.hotelRoomDetails[roomIndex]['Rate description'];
                    } else
                        if (this.hotelRoomDetails[roomIndex]
                            && this.hotelRoomDetails[roomIndex]['Room detail'] && this.hotelRoomDetails[roomIndex]['Room detail'].length > 0) {
                            roomNameText = this.hotelRoomDetails[roomIndex]['Room detail'];
                        }
        return { 'name': roomNameText, 'subName': roomNameText };
    }
    getRoomTypeNameOld(l: number) {
        let priceRecheckRoomName = "";
        let ratePlanRoomName = "";
        if (this.hotelDataObj && "hotelRateList" in this.hotelDataObj && this.hotelDataObj.hotelRateList[l] != null) {
            for (let k = 0; k < this.hotelDataObj.hotelRateList[l].roomRateDescription.length; k++) {
                if (this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name == 'Room'
                    || this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name == 'Description') {
                    for (let t = 0; t < this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text.length; t++) {
                        let tempRoomName: string = this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text[t];
                        priceRecheckRoomName = priceRecheckRoomName + (t == 0 ? '' : ', ') + tempRoomName;
                    }
                    break;
                }
            }
            if (!priceRecheckRoomName || priceRecheckRoomName.length == 0) {
                for (let k = 0; k < this.hotelDataObj.hotelRateList[l].roomRateDescription.length; k++) {

                    if (this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name == 'Rate') {
                        for (let t = 0; t < this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text.length; t++) {
                            let tempRoomName: string = this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text[t];
                            priceRecheckRoomName = priceRecheckRoomName + (t == 0 ? '' : ', ') + tempRoomName;
                        }
                    }
                }
            }
        }
        return { name: ratePlanRoomName.length > 0 ? ratePlanRoomName : priceRecheckRoomName, subName: priceRecheckRoomName };
    }

    getRoomTypeDescriptionShort(l: number) {
        let rateText = ""
        if (this.hotelDataObj && "hotelRateList" in this.hotelDataObj && this.hotelDataObj.hotelRateList[l] != null) {

            for (let k = 0; k < this.hotelDataObj.hotelRateList[l].roomRateDescription.length; k++) {
                if (this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name === "Rate description"
                    // || this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name === "Rate"
                    || this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name === "Description") {
                    for (let t = 0; t < this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text.length; t++) {
                        if (this.getRoomTypeName(l).name.indexOf(this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text[t]) == -1) {
                            let tempText: string = this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text[t];
                            if (t == 0 && tempText.indexOf(':') == 6) tempText = tempText.substring(7);
                            rateText = rateText + (t == 0 ? '' : ", ") + tempText;
                        }
                    }
                }
            }

        }
        return rateText;
    }
    getRoomTypeDescription(l: number) {
        let descriptionObj = [];
        let roomDescText = "";
        if (this.hotelDataObj && "hotelRateList" in this.hotelDataObj && this.hotelDataObj.hotelRateList
            && this.hotelDataObj.hotelRateList.length > l && this.hotelDataObj.hotelRateList[l] != null) {

            for (let k = 0; k < this.hotelDataObj.hotelRateList[l].roomRateDescription.length; k++) {
                if (this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name.toLowerCase() !== "Room".toLowerCase()
                    && this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name.toLowerCase() !== "Room detail".toLowerCase()
                    && this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name.toLowerCase() !== "Room Rate".toLowerCase()
                    && this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name.toLowerCase() !== "Rate".toLowerCase()) {
                    let rateText = "";
                    for (let t = 0; t < this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text.length; t++) {
                        let tempText: string = this.hotelDataObj.hotelRateList[l].roomRateDescription[k].text[t];
                        if (t == 0 && tempText.indexOf(':') == 6) tempText = tempText.substring(7);
                        if (this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name.toLowerCase() === "Rate Description".toLowerCase()) {
                            roomDescText = roomDescText + (t == 0 ? '' : ", ") + tempText;
                        } else {
                            rateText = rateText + (t == 0 ? '' : ", ") + tempText;
                        }

                    }
                    if (this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name.toLowerCase() !== "Rate Description".toLowerCase())
                        descriptionObj.push({ caption: this.hotelDataObj.hotelRateList[l].roomRateDescription[k].name, descText: rateText });
                }
            }
        }
        let finalDescObj = [];
        if (roomDescText.length > 0)
            finalDescObj.push({ caption: 'Room Description', descText: roomDescText });
        for (let descObj of descriptionObj) {
            finalDescObj.push(descObj);
        }
        return finalDescObj;
    }

    getRoomCancellationRulesBackup(l: number) {
        let policies = [];
        if (this.hotelDataObj && "hotelRateList" in this.hotelDataObj
            && this.hotelDataObj.roomsRules
            && this.hotelDataObj.roomsRules.length > l
            && this.hotelDataObj.roomsRules[l] != null
            && this.hotelDataObj.roomsRules[l].Cancellation) {

            for (let text of this.hotelDataObj.roomsRules[l].Cancellation.text) {
                policies.push(text);
            }
        }
        return policies;
    }
    isLloyaltyPointsSupported(l: number) {
        if (this.hotelDataObj && "hotelRateList" in this.hotelDataObj
            && this.hotelDataObj.hotelRateList.length > l
            ) {
                return this.hotelDataObj.hotelRateList[l].loyaltyPointsSupported;
        }
        return false;
    }

    disableSelectionIfCancellationPolicyNotAvailable(l:number,hotelRoomRateObj){
        if( hotelRoomRateObj && hotelRoomRateObj.handleType != 'TravelPort') return false;
        if (hotelRoomRateObj 
            && hotelRoomRateObj.cancelInfo
            && hotelRoomRateObj.cancelInfo.text
            && hotelRoomRateObj.cancelInfo.text.length > 0
        ) {
            return false;
        } else {
            return true;
        }
    }
    getRoomCancellationRules(l: number) {
        return this.    getCancellationPolicyText(l);
    }
    getCancellationPolicyText(l: number) {
        let hotelOption = this.hotelDataObj;
        let policies = [];
        if (hotelOption && hotelOption.hotelRateList &&
            hotelOption.hotelRateList.length > l && hotelOption.hotelRateList[l]
            && hotelOption.hotelRateList[l].cancelInfo
            && hotelOption.hotelRateList[l].cancelInfo.text
        ) {
            for (let text of hotelOption.hotelRateList[l].cancelInfo.text) {
                policies.push(text);
            }
        }
        return policies;
    }
    getHotelName() {
        return this.selectedHotelResult.hotelName;
    }

    getMileDistance() {
        return this.selectedHotelResult.distanceMile;
    }

    getHotelAddress() {
        return this.selectedHotelResult.address;
    }
    getFeedUp() {
        if (this.selectedHotelResult.hotelInfo.upVotes) {
            return this.selectedHotelResult.hotelInfo.upVotes;
        } else {
            return null;
        }
    }
    getFeedDown() {
        if (this.selectedHotelResult.hotelInfo.downVotes) {
            return this.selectedHotelResult.hotelInfo.downVotes;
        } else {
            return null;
        }
    }

    getWalkingMin() {
        return this.selectedHotelResult.walkingTime;
    }

    getDrivingMin() {
        return this.selectedHotelResult.drivingTime;
    }
    isPolicySet() {
        if (this.filterService && this.filterService.originalHotelSearchResponse
            && this.filterService.originalHotelSearchResponse.policySet) {
            return true;
        }
        return false;
    }

    isWithinPolicy(roomIndex: number) {
        if (this.hotelDataObj 
            && "hotelRateList" in this.hotelDataObj
            && this.hotelDataObj.hotelRateList.length > 0) {
                return this.hotelDataObj.hotelRateList[roomIndex].roomPolicyStatus;
            }
        return false;
    }
    getMarketingText() {
        return this.hotelDataObj.hotelDescription;
    }
    getCheckCorporaterate(hotelDataObj){
        if(hotelDataObj.corporateRate){
          return hotelDataObj.corporateRate
        }
      return false;
      }
    isAmenityAvailable(type: string) {
        if (this.selectedHotelResult && this.selectedHotelResult.amenities) {
            return this.selectedHotelResult.amenities[type];
        }
        return false;
    }

    openExtraRateInfo(l: number) {
        this.extraRateInfoTrucating = false;
        this.extraRateInfoIndex = l;
    }

    // getRoomImages(l:number){
    //     // let roomName = this.getRoomTypeName(l).name;
    //     if(this.hotelDataObj && 'hotelImages' in this.hotelDataObj && 'imageByRoomTypes' in this.hotelDataObj.hotelImages){
    //         return this.buildGalaryOption(this.hotelDataObj.hotelImages.imageByRoomTypes);
    //     }else{
    //         return [];
    //     }
    // }
    showErrorMsg = false;
    requestHotelDetails(hotel: HotelResult) {
        this.searchResultService.selectedHotelDetailObj = undefined;
        // this.searchResultService.broadcastSelectedHotel(undefined);
        this.hotelDataObj = undefined;
        this.loadingInProgress = true;
        //if(hotel.handleType=== HandlerTypes.HOTELBEDS){
        //this.loadingInProgress = false;
        //this.hotelDataObj=hotel.hotelInfo;
        //this.searchResultService.selectedHotelDetailObj = this.hotelDataObj;
        //this.revalidateResponseStatus = "SUCCESS";
        //this.hotelRoomDetails = this.getHotelRoomDetails();
        //} else{

     
        if(this.searchService.hotelSelectFromDropDown){
            this.revalidateSubscription = this.searchService.searchHotelFromDrodown(hotel).subscribe(res => {
                setTimeout(() => {
                    if (this.connectionListener) {
                        this.connectionListener.unsubscribe();
                    }
                }, 5000);
               this.hotelRevalidationResponse(res);
    
            }, error => {
                setTimeout(() => {
                    this.hotelDataObj = {};
                    this.searchResultService.selectedHotelDetailObj = this.hotelDataObj;
                    this.revalidateResponseStatus = "Please check your internet.";
                    this.loadingInProgress = false;
    
            //        CommonUtils.showNetworkErrorPopupModal(this.flightSearchService, this.modalService,
              //          PopupComponent.POPUP_ID_NETWORK_ERROR_ON_ROOMLIST,this.translateService);
    
                    // this.setHotelResults(new HotelSearchResponse(), SearchActionType.DETAIL);
                    // this.bsModalRef.hide();
                    // this.callComplete = true;
                }, 100);
    
            });

        } else {
            let isRetry = false;
            if (this.searchService.retryHotelFailedBooking) {
                isRetry = true;
            }
            this.revalidateSubscription = this.searchService.revalidateHotel(hotel, isRetry).subscribe(res => {
        // this.revalidateSubscription = this.searchService.readLocalFile("reCheckHotelPriceFiveRooms.json").subscribe(res => {
            setTimeout(() => {
                if (this.connectionListener) {
                    this.connectionListener.unsubscribe();
                }
            }, 5000);
           this.hotelRevalidationResponse(res);

        }, error => {
            setTimeout(() => {
                this.hotelDataObj = {};
                this.searchResultService.selectedHotelDetailObj = this.hotelDataObj;
                this.revalidateResponseStatus = "Please check your internet.";
                this.loadingInProgress = false;

          //      CommonUtils.showNetworkErrorPopupModal(this.flightSearchService, this.modalService,
            //        PopupComponent.POPUP_ID_NETWORK_ERROR_ON_ROOMLIST,this.translateService);

                // this.setHotelResults(new HotelSearchResponse(), SearchActionType.DETAIL);
                // this.bsModalRef.hide();
                // this.callComplete = true;
            }, 100);

        });
    }
        // }
    }

    // hotelRoomRateDetailObj.rsRank = res.data.hotelRateDetail.rsRank;
    // this.hotelDataObj.roomPolicyStatus[originalIndex] = res.data.roomPolicyStatus[0];
    // this.hotelDataObj.outOfPolicyReasonList[originalIndex] = res.data.outOfPolicyReasonList[0];
    // this.allRoomRules[originalIndex] = res.data.roomsRules[0];



    private initializeRoomIndex(rooms){
        for(let index =0; index < rooms.length; index++) {
            rooms[index]['roomPolicyStatus'] = this.hotelDataObj.roomPolicyStatus[index];
            this.buttonDisabled[index]=false;
            rooms[index]['roomOutOfPolicyReason'] = this.hotelDataObj.outOfPolicyReasonList[index];
        }
    }
    hotelRevalidationResponse(res){
        setTimeout(() => {
            this.loadingInProgress = false;
            let failedRatesStr: any = this.gallopLocalStorage.getItem('failedRates');
            let failedRates = {};
            if (failedRatesStr && failedRatesStr !== '') {
                failedRates = JSON.parse(failedRatesStr);
            }
            if (res && res.success === true && res.data && Object.keys(failedRates).length < 4) {
                this.hotelDataObj = res.data;
                if(this.hotelDataObj && this.hotelDataObj.hotelRateList && this.hotelDataObj.hotelRateList.length > 0){
                    this.initializeRoomIndex(this.hotelDataObj.hotelRateList);
                    this.hotelDataObj.hotelRateList = this.sortResultsAsRecommended(this.hotelDataObj.hotelRateList);
                }
                if(this.hotelDataObj && this.searchService.hotelSelectFromDropDown){
                    this.selectedHotelResult['amenities'] = this.hotelDataObj.amenities;
                    this.selectedHotelResult['starRating'] = this.hotelDataObj.rating;
                    this.selectedHotelResult['drivingTime'] = this.hotelDataObj.drivingTime;
                    this.selectedHotelResult['distanceMile'] = this.hotelDataObj.distanceMile;
                    this.selectedHotelResult['latitude'] = Number.parseFloat(this.hotelDataObj.latitude);
                    this.selectedHotelResult['longitude'] = Number.parseFloat(this.hotelDataObj.longitude);
                     this.selectedHotelResult['address'] = this.hotelDataObj.address;
                     this.selectedHotelResult['price'] = this.hotelDataObj.optimumPrice;
                }
                const currEmployeeArr
                    = this.searchService1.employeeList.filter(item1 => item1.email === this.searchService1.employeeEmail[0].email) ;
                let currEmployee = this.userAccountService.getAccountInfo().userInfo.employeeInfo;
                if (currEmployeeArr && currEmployeeArr.length > 0){
                    const selectedTraveller = this.searchService1.employeeList.filter(item1 => item1.email === currEmployeeArr[0].email) ;
                    if (selectedTraveller && selectedTraveller.length > 0 && selectedTraveller[0].employeeInfo){
                    currEmployee = currEmployeeArr[0].employeeInfo;
                }
                }
                if (this.hotelDataObj && this.hotelDataObj.hotelRateList && this.hotelDataObj.hotelRateList.length > 0) {
                    const findObjIndex = this.hotelDataObj.hotelRateList.findIndex(item => item.govtRate === true);
                    if (findObjIndex > -1) {
                        this.showSwitch = true;
                        this.showErrorMsg = false;
                        this.showGovernmentRate = true;
                    } else {
                        if (currEmployee.showGovtRates){
                            this.showErrorMsg = true;
                        } else {
                            this.showErrorMsg = false;
                        }
                        this.showSwitch = false;
                        this.showGovernmentRate = false;
                    }
                    const findLoyalityIndex = this.hotelDataObj.hotelRateList.findIndex(item => item.loyaltyPointsSupported === true);
                    const findLoyalitySecondIndex = this.hotelDataObj.hotelRateList.findIndex(item => item.loyaltyPointsSupported === false);
                    if (findLoyalityIndex > -1 && findLoyalitySecondIndex > -1) {
                        this.showLoyalitySwitch = true;
                        this.showErrorMsg = false;
                        this.loyalityRoomToggle = false;
                    } else {
                        
                        this.showLoyalitySwitch = false;
                        this.loyalityRoomToggle = false;
                    }
                    const findAAAIndex = this.hotelDataObj.hotelRateList.findIndex(item => item.aaarate === true);
                    if (findAAAIndex > -1) {
                        this.showAAASwitch = true;
                        this.showErrorForAAAMsg = false;
                        this.showAAARate = true;
                    } else {
                        if (currEmployee.showAAARates){
                            this.showErrorForAAAMsg = true;
                        } else {
                            this.showErrorForAAAMsg = false;
                        }
                        this.showAAASwitch = false;
                        this.showAAARate = false;
                    }
                    this.totalVisibleHotels = this.getVisibleHotelCountWithCurrentFilters();
                }
                if (!this.showGovernmentRate
                    && !this.showAAARate
                    && this.hotelDataObj
                    && this.hotelDataObj.hotelRateList && this.hotelDataObj.hotelRateList.length > this.hotelElementCount) {
                    if (this.isSeenComplete) {
                        this.isSeenComplete = false;
                    }
                }
                if (this.hotelDataObj && this.hotelDataObj.hotelRateDetail) {
                    this.bookingService.cardAllowed.push(this.hotelDataObj.hotelRateDetail.allowedCredit);
                }
                let price = this.getRoomPrice(0);
                if (price > this.selectedHotelResult.price) {
                    let selectedRoom: any = {};
                    selectedRoom.hotelCode = this.hotelDataObj.hotelCode;
                    selectedRoom.price = price;
                    selectedRoom.inPolicy = this.hotelDataObj.roomPolicyStatus[0];
                    // 
                    this.searchService.roomPriceChange.push(selectedRoom);
                    // 
                }
              
                this.updateImages();
                if (this.hotelDataObj.status === "APIERROR" || !this.hotelDataObj.hotelRateList.length) {
                    this.searchService.hotelCodeHasNoRoom.push(this.selectedHotelResult.hotelCode);
                }
                let newRateList = [];
                for (let respRateIter = 0; respRateIter < this.hotelDataObj.hotelRateList.length; respRateIter++) {
                    let currRateId = this.hotelDataObj.hotelRateList[respRateIter].ratePlanTypePPN;
                    if (!currRateId) {
                        currRateId = this.hotelDataObj.hotelRateList[respRateIter].ratePlanType;
                    }
                    if (!failedRates[currRateId]) {
                        newRateList.push(this.hotelDataObj.hotelRateList[respRateIter]);
                    }
                }
                this.hotelDataObj.hotelRateList = newRateList;
                if (newRateList.length > 0) {
                    this.hotelDataObj.hotelRateDetail = newRateList[0];
                } else {
                    this.searchService.hotelCodeHasNoRoom.push(this.selectedHotelResult.hotelCode);
                }
                this.searchResultService.selectedHotelDetailObj = this.hotelDataObj;
                this.revalidateResponseStatus = "SUCCESS";
                this.hotelRoomDetails = this.getHotelRoomDetails();
                // let hotelSearchResponse: HotelSearchResponse = deserialize(res.data, HotelSearchResponse);
                // this.setHotelResults(hotelSearchResponse, SearchActionType.DETAIL);
            } else {
                this.searchService.hotelCodeHasNoRoom.push(this.selectedHotelResult.hotelCode);
                this.hotelDataObj = {};
                this.searchResultService.selectedHotelDetailObj = this.hotelDataObj;
                this.revalidateResponseStatus = "Please try again later <NAME_EMAIL>";
                if (Object.keys(failedRates).length >= 2) {
                    this.gallopLocalStorage.setItem('failedRates', '{}');
                }
                // this.setHotelResults(new HotelSearchResponse(), SearchActionType.DETAIL);
            }

        }, 100);

    }
    loyaityOptionSelected(event){
        this.loyalityRoomToggle = event;
        this.isSeenComplete = false;
        this.totalVisibleHotels = this.getVisibleHotelCountWithCurrentFilters();  
    }
    aaarateOptionSelected(event) {
        this.showAAARate = event;
        this.isSeenComplete = false;
        this.totalVisibleHotels = this.getVisibleHotelCountWithCurrentFilters();
    }
    rateOptionSelected(event) {
        this.showGovernmentRate = event;
        this.isSeenComplete = false;
        this.totalVisibleHotels = this.getVisibleHotelCountWithCurrentFilters();
    }
    getRoomRulesCount() {
        if (this.hotelDataObj && this.hotelDataObj.roomsRules) {
            return this.hotelDataObj.roomsRules;
        } else {
            return 'NOTFOUND';
        }

    }
    private requestRoomRulesAndRateDescription(roomIndex: number) {
        let hotelRoomRateDetailObj: any = this.hotelDataObj.hotelRateList[roomIndex];
        let hotelcode;
        let is_corporate_rate;
        let hotelchain;
        let price = hotelRoomRateDetailObj.total.substring(3);
        if (hotelRoomRateDetailObj.displayTotal) {
            price = hotelRoomRateDetailObj.displayTotal.toFixed(2);
        }
        if (this.hotelDataObj.handlerToHotelInfoCommon && this.hotelDataObj.hotelRateList && this.hotelDataObj.handlerToHotelInfoCommon.TravelPort) {
            if (this.hotelDataObj.hotelRateList[roomIndex].handleType && this.hotelDataObj.hotelRateList[roomIndex].handleType === 'TravelPort') {
                hotelcode = this.hotelDataObj.handlerToHotelInfoCommon.TravelPort.hotelCode;
                hotelchain = this.hotelDataObj.handlerToHotelInfoCommon.TravelPort.hotelChain;
            } else {
                hotelcode = this.hotelDataObj.handlerToHotelInfoCommon.HotelBeds.hotelCode;
                hotelchain = this.hotelDataObj.handlerToHotelInfoCommon.HotelBeds.hotelChain;
            }
        } else {
            hotelcode = this.hotelDataObj.hotelCode;
            hotelchain = this.hotelDataObj.hotelChain;
        }
        is_corporate_rate = this.getCheckCorporaterate(this.hotelDataObj.hotelRateList[roomIndex]);
        const policyStatus = hotelRoomRateDetailObj.roomPolicyStatus;
        this.roomRulesAndRateDescSubscription = this.searchService.fetchRoomRulesAndRateDesc(this.selectedHotelResult, hotelRoomRateDetailObj.ratePlanType,
            hotelcode, hotelchain,is_corporate_rate, price, policyStatus).subscribe(res => {
            if (res && res.success === true && res.data) {
                // 
                if (res.data.hotelRateDetail && res.data.hotelRateDetail.roomRateDescription) {
                    for (let rateDesc of res.data.hotelRateDetail.roomRateDescription) {
                        hotelRoomRateDetailObj.roomRateDescription.push(rateDesc);
                    }
                }
                if (res.data.hotelRateDetail && res.data.hotelRateDetail.tax){
                    hotelRoomRateDetailObj.tax = res.data.hotelRateDetail.tax;
                }
                if (res.data.hotelRateDetail && res.data.hotelRateDetail.base){
                    hotelRoomRateDetailObj.base = res.data.hotelRateDetail.base;
                }
                if (res.data.hotelRateDetail && res.data.hotelRateDetail.displayTax){
                    hotelRoomRateDetailObj.displayTax = res.data.hotelRateDetail.displayTax;
                }
                if (res.data.hotelRateDetail && res.data.hotelRateDetail.displayBase){
                    hotelRoomRateDetailObj.displayBase = res.data.hotelRateDetail.displayBase;
                }

                if (policyStatus
                    && res.data.roomPolicyStatus
                    && res.data.roomPolicyStatus.length > 0
                    && res.data.roomPolicyStatus[0] === false) {
                    hotelRoomRateDetailObj.roomPolicyStatus = false;
                    hotelRoomRateDetailObj.roomOutOfPolicyReason.reason = res.data.outOfPolicyReasonList[0].reason;
                    hotelRoomRateDetailObj.rsRank = res.data.hotelRateDetail.rsRank;
                    this.hotelDataObj.hotelRateList=  this.sortResultsAsRecommended(this.hotelDataObj.hotelRateList);
                }
                hotelRoomRateDetailObj.cancelInfo = res.data.roomsRules[0].Cancellation;
                this.buttonDisabled[roomIndex]=false;
            } else {
                hotelRoomRateDetailObj.cancelInfo  = {'text':['CXL: Non Refundable'],'name':'Cancellation'};
                this.buttonDisabled[roomIndex]=false;
                
            }
        }, error => {
            hotelRoomRateDetailObj.cancelInfo  = {'text':['CXL: Non Refundable'],'name':'Cancellation'};
            this.buttonDisabled[roomIndex]=false;
            
        });
    }
    isMapSupported() {
       
        if (environment.platform.toLowerCase() === 'ios' && environment.appStoreBuildNumber >= 27) {
            return true;
        }
        if (environment.platform.toLowerCase() === 'android') {
            return true;
        }
        return false;
    }
    mapsSelector(lat, long) {
        if (environment.platform.toLowerCase() === 'ios' && environment.appStoreBuildNumber >= 27) {
            window.location.href = 'maps://maps.google.com/maps?daddr=' + lat + ',' + long + '&dummy=/TripItAPI/';
        } else {
            window.open("https://maps.google.com/maps?daddr=" + lat + ',' + long);
        }
    }

    ngOnDestroy() {
        if (this.connectionListener) {
            this.connectionListener.unsubscribe();
        }
        if (this.revalidateSubscription) {
            this.revalidateSubscription.unsubscribe();
        }
    }
    showSeeMoreLink(){
       return !this.isSeenComplete &&  this.totalVisibleHotels > this.hotelElementCount ;
    }
    private isHotelVisibleWithCUrrentFilters(hotelRoomObj){
        let showThisHotel = false;
        if (this.showGovernmentRate) {
            if (hotelRoomObj.govtRate === true) {
                showThisHotel = true;
            }
        }
        if (this.showAAARate) {
            if (hotelRoomObj.aaarate === true) {
                showThisHotel = true;
            }
        }
        if (this.loyalityRoomToggle) {
            if(this.showGovernmentRate && !this.showAAARate){
            if (hotelRoomObj.loyaltyPointsSupported === true && hotelRoomObj.govtRate === true) {
                showThisHotel = true;
            }
        }else  if (this.showAAARate && !this.showGovernmentRate) {
            
            if (hotelRoomObj.loyaltyPointsSupported === true && hotelRoomObj.aaarate === true) {
                showThisHotel = true;
            }
            
        }else  if (this.showAAARate && this.showGovernmentRate) {
            
            if (hotelRoomObj.loyaltyPointsSupported === true && hotelRoomObj.aaarate === true && hotelRoomObj.govtRate === true) {
                showThisHotel = true;
            }
            
        }else{
            if (hotelRoomObj.loyaltyPointsSupported === true) {
                showThisHotel = true;
            }   
        }
        }
        if (!this.showAAARate && !this.showGovernmentRate && !this.loyalityRoomToggle) {
            showThisHotel = true;
        }
        return showThisHotel;
    }


    showCurrentHotel(hotelRoomObj, index) {
        if (index === 0) {
            this.visibleHotelCounter =0;
        }
        const showThisHotel = this.isHotelVisibleWithCUrrentFilters(hotelRoomObj);
        if (showThisHotel) {
            if (this.isSeenComplete || this.visibleHotelCounter < this.hotelElementCount){
                this.visibleHotelCounter++;
                return true;
            }
            this.visibleHotelCounter++;
        }
        return false;
    }
    private getVisibleHotelCountWithCurrentFilters() {
        let counter = 0;
        for (let i = 0; i < this.hotelDataObj.hotelRateList.length; i++) {
            if (this.isHotelVisibleWithCUrrentFilters(this.hotelDataObj.hotelRateList[i])){
                counter++;
            }
        }
        return counter;
    }
    isPayAtHotel(hotelRoomRateObj) {
        return (
                (
                    hotelRoomRateObj.handleType === 'TravelPort' 
                    || hotelRoomRateObj.handleType === 'Sabre'
                    )
                && !this.userAccountService.confermaStatus);
    }
}
