import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { hotelSelectionComponent } from './hotel-selection.component';
import { HotelSelectionRoutingModule } from './hotel-selection.routing.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ShareModule } from '../share.module';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxGalleryModule } from '@kolkov/ngx-gallery';
import { roomGalleryModal } from '../room-gallery-modal/room-gallery-modal.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { UiSwitchModule } from 'ngx-ui-switch';


@NgModule({
  imports: [
    CommonModule,
    HotelSelectionRoutingModule,
    NgbModule,
    ShareModule,
  //  AgmCoreModule.forRoot({ apiKey: 'AIzaSyA8z-JfNhr9cQgPz58usOioKHIXWSJsvy0' }),
    NgxGalleryModule,
    NgxSmartModalModule,
    NgSelectModule,
    UiSwitchModule,
    ReactiveFormsModule,
    FormsModule,
  ],
  declarations: [
    hotelSelectionComponent,
    roomGalleryModal,

  ],
})
export class HotelSelectionModule {

}