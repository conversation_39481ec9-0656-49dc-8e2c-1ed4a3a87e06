<email-header></email-header>
<div class="main-wrapper">
    <div class="content">
        <div class="container">
            <hotel-request-view [backToSearchSteps]="2"></hotel-request-view>
            <div class="hotel-selection-container">
                <div class="hotel-heading">
                    <a class="hotel-heading-link" *ngIf=" !this.searchService.retryHotelFailedBooking" href="javascript:void(0)" attr.data-track="Back"
                        attr.data-params="page=HotelRooms" (click)="goToHotelResult()">
                        <img attr.data-track="Back" attr.data-params="page=HotelRooms"
                            src="assets/images/hotel/backarrow.svg" alt="">
                    </a>
                    <h4>{{getHotelName()}}</h4>
                </div>

                <div class="hotel-details">
                    <div class="hotel-details-inner">
                        <div class="hotal-carosal">
                            <div *ngIf="loadingInProgress" class="hotal-carosal-loader">
                                <loader-dots></loader-dots>
                            </div>
                            <!-- <div *ngIf="!hotelDataObj || (!hotelDataObj.hotelImages && !loadingInProgress)" class="hotel-carosal-placeholder-iamge">
                                <img src="assets/images/hotel/hotel.png" />
                            </div> -->
                            <ngx-gallery [options]="galleryOptions" [images]="galleryImages"></ngx-gallery>
                        </div>
                        <div class="address-duration">
                            <div class="inlineblock_m">
                                <div class="hotel-distance">{{getMileDistance() | number : '.2-2'}}
                                    {{'hotelSelect.milesfromyourdestination' | translate}}</div>
                                <div class="hotel-address">{{getHotelAddress()}}</div>
                            </div>
                            <div class="hotel-duration">
                                <div class="distance-car">
                                    <img class="inlineblock_m" src="assets/images/car.svg" alt="" />
                                    <label class="inlineblock_m">{{getDrivingMin()}} {{'hotelSelect.min' |
                                        translate}}</label>
                                </div>
                                <div *ngIf="getWalkingMin() < 30 " class="distance-walk">
                                    <img class="inlineblock_m" src="assets/images/walk.svg" alt="" />
                                    <label class="inlineblock_m">{{getWalkingMin()}} {{'hotelSelect.min' |
                                        translate}}</label>
                                </div>
                                <span *ngIf="getFeedUp() && !this.isMobile" style="margin-left:8px;">
                                    <span style="color:green;font-size:14px;margin-right:10px;"><img
                                            class="feedbackImage"
                                            src="assets/images/Untitled_design-2_copy.png">{{getFeedUp()}}</span>
                                </span>
                                <span *ngIf="getFeedDown() && !this.isMobile" style="margin-left:4px;">
                                    <span style="color:red;font-size:14px;margin-right:0px;"><img class="feedbackImage"
                                            src="assets/images/Untitled_design_copy.png">{{ getFeedDown()}}</span>
                                </span>
                            </div>

                        </div>
                        <div class="d-block d-md-none">
                            <span *ngIf="getFeedUp()" style="margin-left:0px;">
                                <span style="color:green;font-size:14px;margin-right:10px;"><img class="feedbackImage"
                                        src="assets/images/Untitled_design-2_copy.png">{{getFeedUp()}}</span>
                            </span>
                            <span *ngIf="getFeedDown()">
                                <span style="color:red;font-size:14px;margin-right:0px;"><img class="feedbackImage"
                                        src="assets/images/Untitled_design_copy.png">{{ getFeedDown()}}</span>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="room-selection">
                    <div *ngIf="hotelDataObj && hotelDataObj.hotelRateList" class="description-heading">
                      

                            <div class="description-heading-inner">
                                
                                <div class="policies-dropdown simple-dropdown"
                                    id="cancellationPoliciesDropdownContainer">
                                    <!-- <ng-select #cancellationPoliciesDropdown appendTo="#cancellationPoliciesDropdownContainer" value="recommanded" dropdownPosition="bottom" [searchable]="false" [clearable]="false" [items]="cancellationPoliciesDropdownItems" bindLabel="value" bindValue="id" formControlName="cancellationPoliciesDropdown">
                                        <ng-template ng-header-tmp>
                                            <div class="selectox-header">
                                                <span>Sort by</span>
                                                <span class="selectBox-remove" (click)="cancellationPoliciesDropdown.toggle()"><span class="material-icons">clear</span></span>
                                            </div>
                                        </ng-template>
                                    </ng-select> -->
                                    <!-- <div class="select-overlay"></div> -->
                                </div>
                                <div class="flight-list-sorting" id="sortingContainer1" style="position: relative;">
                                        <div class="filter custom-selectbox" attr.data-track="HotelResultsSortBy"
                                            (click)="sortingDropdown.toggle()">
                                            <div class="select-input12">
                                                <ng-select #sortingDropdown appendTo="#sortingContainer1"
                                                    value="recommanded" dropdownPosition="bottom"
                                                    [searchable]="false" [clearable]="false"
                                                    [items]="sortOptionsHotel | translateOptions" bindLabel="value"
                                                    bindValue="id" [(ngModel)]="sortingDropdownValue"
                                                    (change)="sortOptionChanged($event)">
                                                    <ng-template ng-header-tmp>
                                                        <div class="selectox-header">
                                                            <span>{{'hotelResult.Sortby' | translate}}</span>
                                                            <span class="selectBox-remove"
                                                                (click)="sortingDropdown.toggle()"><span
                                                                    class="material-icons">clear</span></span>
                                                        </div>
                                                    </ng-template>
                                                </ng-select>
                                                <div class="select-overlay"></div>
                                            </div>
                                            <div class="field-value custom-selectbox-value"
                                                attr.data-track="HotelResultsSortBy">{{this.sortValue | translate}} <span class="control-icon icon-triangle"
                                               
                                                attr.data-track="HotelResultsSortBy"></span></div>
                                            
                                        </div>
                                    </div>
                            </div>


                       
                    </div>
                    <div *ngIf="loadingInProgress" class="loader-text-container">
                        <div class="text">{{'hotelSelect.RetrivingRoomsList' | translate}}</div>
                        <loader-dots></loader-dots>
                    </div>

                    <div *ngIf="!loadingInProgress && (!hotelDataObj ||  !hotelDataObj.hotelRateList || !hotelDataObj.hotelRateList.length)"
                        class="no-flight-found-container text-center">
                        <img style="margin-top:20px; " src="assets/images/no-flight-found.png" />
                        <div class="text1 text-danger" style="font-size: 18px;">{{'hotelSelect.Noroomsavailable.' | translate}}</div>
                    </div>
                    <div style="display: grid;float: left;">
                            <div *ngIf="showLoyalitySwitch" style="float: left;margin-bottom: 20px;">
                                    <tr style="display: flex;justify-content: space-between;margin-top:10px;">
                                        <td class="text-left" Style="white-space: nowrap;font-family: var(--globalFontfamilyr);font-weight: bold;;font-size: 16px;">
                                            {{'hotelSelect.Hiderateswithnoloyalitybenefits' | translate}}</td>
                                        <td class="switch-ui" style="">
                                            <ui-switch color="gray" [(ngModel)]="loyalityRoomToggle" [checked]="loyalityRoomToggle"
                                                (change)="loyaityOptionSelected($event)" checkedLabel="{{ 'cards.ON' | translate }}" uncheckedLabel="{{ 'cards.OFF' | translate }}">
                                            </ui-switch>
            
                                        </td>
                                    </tr>
                                </div>
                    <div>    
                    <div *ngIf="showSwitch" style="float: left;margin-bottom: 20px;">
                        <tr style="display: flex;justify-content: space-between;margin-top:10px;">
                            <td class="text-left" Style="white-space: nowrap;font-family: var(--globalFontfamilyr);font-weight: bold;;font-size: 16px;">
                                {{'hotelSelect.ShowGovernmentratesonly' | translate}}</td>
                            <td class="switch-ui" style="">
                                <ui-switch color="gray" [(ngModel)]="showGovernmentRate" [checked]="showGovernmentRate"
                                    (change)="rateOptionSelected($event)" checkedLabel="{{ 'cards.ON' | translate }}" uncheckedLabel="{{ 'cards.OFF' | translate }}">
                                </ui-switch>

                            </td>
                        </tr>
                    </div>
                   
                    <div *ngIf="showErrorMsg" style="float: left;margin-bottom: 20px;">
                        {{'hotelSelect.Noroomswithgovernment/militaryrateswerefound' | translate}}
                    </div>
                    </div>
                   
                    <div>
                    <div *ngIf="showAAASwitch" style="float: left;margin-bottom: 20px;">
                        <tr style="display: flex;justify-content: space-between;margin-top:10px;">
                            <td class="text-left" Style="white-space: nowrap;font-family: var(--globalFontfamilyr);font-weight: bold;;font-size: 16px;">
                                {{'hotelSelect.ShowAAAratesonly' | translate}}</td>
                            <td class="switch-ui" style="">
                                <ui-switch color="gray" [(ngModel)]="showAAARate" [checked]="showAAARate"
                                    (change)="aaarateOptionSelected($event)" checkedLabel="{{ 'cards.ON' | translate }}" uncheckedLabel="{{ 'cards.OFF' | translate }}">
                                </ui-switch>

                            </td>
                        </tr>
                    </div>
                    <div *ngIf="showErrorForAAAMsg" style="float: left;margin-bottom: 20px;">
                        {{'hotelSelect.Noroomswithaaarateswerefound' | translate}}
                    </div>
                    </div>
                    </div>
                    <div *ngIf="!loadingInProgress && hotelDataObj && hotelDataObj.hotelRateList"
                        class="room-selection-inner">
                        <div *ngFor="let hotelRoomRateObj of hotelDataObj.hotelRateList; let l = index;"
                            class="room-item">
                            <div class="result-card-box" *ngIf="showCurrentHotel(hotelRoomRateObj, l)">
                                <div class="result-card-box-inner">
                                    <div class="result-card-left">
                                        <div class="result-card-left-inner">
                                            <img onerror="this.onerror=null;this.src='assets/images/hotel/room.png'"
                                                class="room-placeholder-img" src="assets/images/hotel/room.png" />
                                            <img *ngIf="galleryRoomsImages && galleryRoomsImages.length > 0"
                                                class="room-img" src="{{galleryRoomsImages[0].medium}}" />
                                            <ng-container *ngIf="galleryRoomsImages && galleryRoomsImages.length > 0">
                                                <room-gallery-modal [hotelRoomIndex]="l"
                                                    [galleryImages]="galleryRoomsImages"
                                                    (hotelRoomSelectedEvent)="roomSelectionFromGallery($event, hotelRoomRateObj)">
                                                </room-gallery-modal>
                                            </ng-container>
                                        </div>
                                    </div>
                                    <div class="result-card-middle" (click)="selectRoomMobile(l,$event, hotelRoomRateObj)">
                                        <div class="result-card-middle-top">
                                            <div class="result-card-middle-top-left">
                                                <div class="hotel-name">{{getRoomTypeName(l).name | titlecase}}</div>
                                                <div *ngIf="getRoomTypeName(l).roomDetailsText"
                                                    class="duration-section car-duration">
                                                    <!-- <img class="inlineblock icon-margin" src="assets/images/hotel/hotel.svg" /> -->
                                                    <span
                                                        class="inlineblock">{{getRoomTypeName(l).roomDetailsText}}</span>
                                                </div>
                                                <div *ngIf="getCheckCorporaterate(hotelRoomRateObj)" class="corporateRate" style="white-space: nowrap;"><i class="fa fa-star" style="margin-right: 4px;"></i> {{'hotelResult.Corporaterate' | translate}}</div>
                                                <div class="duration-section walk-duration">
                                                      
                                                </div>
                                            </div>
                                            <div class="result-card-middle-top-right">
                                                  
                                                <div class="hotel-price d-block d-md-none">
                                                        {{getPerNightPrice(l) | currency :
                                                            getCurrencySymbol(getHotelCurrency()) : 'code': '1.0-0' }}/<span class="lowercase">{{'bookingHistory.Night' | translate}}</span></div>
                                                <div 
                                                    class="hotels-taxes-fees d-block d-md-none">
                                                    {{ getRoomPriceWithoutResortFee(l) | currency :
                                                        getCurrencySymbol(getHotelCurrency()) : 'code': '1.0-0' }}   {{'setting.Totalperroom' | translate}} 
                                                </div>
                                                <div *ngIf="getresortFee(l) > 0"
                                                    class="hotels-taxes-fees d-block d-md-none">
                                                    {{'hotelSelect.resortfee' | translate}} : {{getresortFee(l) | currency :
                                                    getCurrencySymbol(getHotelCurrency()) : 'code': '1.0-0' }}
                                                </div>
                                                <div *ngIf="isPayAtHotel(hotelRoomRateObj)"
                                                    class="hotel-name d-block d-md-none">Pay at hotel</div>
                                                <div class="night-count">{{hotelDataObj.stay}}-{{hotelDataObj.stay > 1 ?
                                                    ('hotelSelect.Nights' | translate) :('hotelSelect.Night' |
                                                    translate)}}</div>
                                                
                                                 
                                            </div>
                                           
                                        </div>
                                        
                                        <span *ngIf="!this.isLloyaltyPointsSupported(l)" style="color:#f93d30;"> <img
                                                src="assets/images/flight-list/gen-negative.svg">{{'option.Noloyaltyrewards' | translate}}</span>
                                        <!-- <span  *ngIf="hotelRoomRateObj.handleType !== 'TravelPort'" style="color:#f93d30;"> <img src="assets/images/flight-list/gen-negative.svg"> No loyalty rewards</span> -->
                                        <div class="result-card-middle-bottom">
                                            <div class="result-card-middle-bottom-left">
                                                <p *ngIf="hotelRoomRateObj.handleType !== 'TravelPort'"
                                                    class="small-discription"> {{'hotelSelect.CancellationPolicy' | translate}}:<span
                                                        *ngFor="let rule of getCancellationPolicyText(l)">{{rule+'. '}}
                                                    </span></p>
                                                <p *ngIf="hotelRoomRateObj.handleType === 'TravelPort'"
                                                    class="small-discription"><span
                                                        *ngFor="let rule of getRoomCancellationRules(l)">{{rule+'. '}}
                                                    </span></p>
                                            </div>
                                            <div class="result-card-middle-bottom-right">
                                                <div *ngIf="isPolicySet()" class="policy-container">
                                                    <img class="inlineblock icon-margin"
                                                        src="assets/images/hotel/policy{{isWithinPolicy(l) ? '-active' : ''}}.svg" />
                                                    <span class="inlineblock">{{'hotelSelect.Policy' |
                                                        translate}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="result-card-right d-none d-md-block">
                                        <div>
                                            <div class="hotel-price">{{getPerNightPrice(l) | currency :
                                                    getCurrencySymbol(getHotelCurrency()) : 'code': '1.0-0' }}/<span class="lowercase">{{'bookingHistory.Night' | translate}}</span>
                                            </div>
                                            <div  class="hotels-taxes-fees">
                                                    {{ getRoomPriceWithoutResortFee(l) | currency :
                                                        getCurrencySymbol(getHotelCurrency()) : 'code': '1.0-0' }}   {{'setting.Totalperroom' | translate}} 
                                            </div>
                                            <div *ngIf="getresortFee(l) > 0" class="hotels-taxes-fees">
                                                    {{'hotelSelect.resortfee' | translate}}: {{getresortFee(l) | currency :
                                                getCurrencySymbol(getHotelCurrency()) : 'code': '1.0-0' }}
                                            </div>
                                        </div>
                                        <div class="hotel-select-button">
                                            <button class="btn primary-button" [disabled]="buttonDisabled[l]" [ngStyle]="{'opacity': this.disableSelectionIfCancellationPolicyNotAvailable(l,hotelRoomRateObj) ? '0.5':'1'}"  (click)="selectRoom(l,hotelRoomRateObj)">
                                                {{'hotelSelect.Select' | translate}}</button>
                                        </div>
                                        <div *ngIf="isPayAtHotel(hotelRoomRateObj)"> {{'hotelSelect.Payathotel' | translate}}</div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="seeMoreLink" attr.data-track="SeeMoreHotelRooms" *ngIf="showSeeMoreLink()">
                                <a href="javascript:void(0)" (click)="loadAllItems()">
                                    <span>{{'hotelResult.SEEMOREOPTIONS' | translate}}</span>
                                    <span><i class="fa fa-chevron-down" style="
                                        position: relative;
                                        top: -5px;
                                    "></i></span>
                                </a>
                            </div>
                        </div>
                        <!-- <div class="room-item">
                            <div class="result-card-box">
                                <div class="result-card-box-inner">
                                    <div class="result-card-left">
                                        <div class="result-card-left-inner">
                                            <img src="https://travelport.leonardocontentcloud.com/imageRepo/3/0/75/137/341/NYCMF_33154505_P.jpg" />
                                            <room-gallery-modal></room-gallery-modal>
                                        </div>
                                    </div>
                                    <div class="result-card-middle" (click)="selectRoom()">
                                        <div class="result-card-middle-top">
                                            <div class="result-card-middle-top-left">
                                                <div class="hotel-name">King</div>
                                                <div class="duration-section car-duration">
                                                    <img class="inlineblock icon-margin" src="assets/images/hotel/hotel.svg" />
                                                    <span class="inlineblock">1 King Bed</span>
                                                </div>
                                                <div class="duration-section walk-duration">
                                                    <img class="inlineblock icon-margin" src="assets/images/hotel/sleeps.svg" />
                                                    <span class="inlineblock">Sleeps 2</span>
                                                </div>

                                            </div>
                                            <div class="result-card-middle-top-right">
                                                <div class="hotel-price d-block d-md-none">$657</div>
                                                <div class="night-count">4-Nights</div>
                                                <div class="room-price">$164 ave/night</div>
                                            </div>
                                        </div>
                                        <div class="result-card-middle-bottom">
                                            <div class="result-card-middle-bottom-left">
                                                <p class="small-discription">276.53 USD CXL FEE PER RM CANCELLATION UP TO 2DAYS BEFORE ARRIVAL</p>
                                            </div>
                                            <div class="result-card-middle-bottom-right">
                                                <div class="policy-container">
                                                    <img class="inlineblock icon-margin" src="assets/images/hotel/policy-active.svg" />
                                                    <span class="inlineblock">Policy</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="result-card-right d-none d-md-block">
                                        <div class="hotel-price">$657</div>
                                        <div class="hotel-select-button">
                                            <button class="btn primary-button" (click)="goToSelectRoom()">Select</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> -->

                    </div>
                </div>


                <div class="hotel-description">
                    <div class="description-heading loader-text-container">
                        <h4>{{'hotelSelect.AboutHotel' | translate}}</h4>
                        <span *ngIf="loadingInProgress">
                            <loader-dots></loader-dots>
                        </span>

                    </div>


                    <div class="hotel-description-inner" *ngIf="!loadingInProgress">
                        <div class="hotel-description-left" [ngStyle]="widthWhenNoMap('parent')">
                            <div *ngIf="truncating" class="hotel-description-text" [class.collapsed]="isCollapsed">
                                <p [ngStyle]="widthWhenNoMap()">
                                    <span>{{getMarketingText()| truncate : 150}}</span>
                                    <a href="javascript:void(0);" class="showMoreLess"
                                        attr.data-track="ShowMoreHotelDescription"
                                        (click)="truncating = false">{{'hotelSelect.Showmore' | translate}}</a>
                                </p>

                            </div>

                            <div *ngIf="!truncating" class="hotel-description-text" [class.collapsed]="isCollapsed">
                                <p [ngStyle]="widthWhenNoMap()">
                                    <span [innerHTML]="getMarketingText()"></span>
                                    <a href="javascript:void(0);" class="showMoreLess"
                                        attr.data-track="ShowLessHotelDescription"
                                        (click)="truncating = true">{{'hotelSelect.Showless' | translate}}</a>
                                </p>
                            </div>
                            <div class="hotel-description-rating d-none d-md-inline-block">
                                <div class="inlineblock_m hotel-rating">
                                    {{selectedHotelResult.starRating}}-{{'hotelSelect.StarHotel' | translate}}</div>
                                <div class="inlineblock_m hotel-rating-stars">
                                    <span
                                        *ngFor="let starType of getRatingStarsMap(selectedHotelResult.starRating); let i = index;"
                                        class="star">
                                        <img *ngIf="starType == 'full'" src="assets/images/hotel/star-filled.svg" />
                                        <img *ngIf="starType == 'half'" src="assets/images/hotel/halfstar.png" />
                                        <img *ngIf="starType == 'none'" src="assets/images/hotel/star.svg" />
                                    </span>
                                    <!-- <span><img src="assets/images/hotel/star-filled.svg" alt="star-filled" /></span>
                                    <span><img src="assets/images/hotel/star-filled.svg" alt="star-filled" /></span>
                                    <span><img src="assets/images/hotel/star-filled.svg" alt="star-filled" /></span>
                                    <span><img src="assets/images/hotel/star.svg" alt="star" /></span>
                                    <span><img src="assets/images/hotel/star.svg" alt="star" /></span> -->
                                </div>

                            </div>

                            <div class="hotel-amenities">
                                <div class="hotel-amenities-header">
                                    <h5>{{'hotelSelect.Amenities' | translate}}</h5>
                                    <div class="hotel-description-rating d-inline-block d-md-none">
                                        <div class="inlineblock_m hotel-rating">
                                            {{selectedHotelResult.starRating}}-{{'hotelSelect.StarHotel' | translate}}
                                        </div>
                                        <div class="inlineblock_m hotel-rating-stars">
                                            <span *ngFor="let rating of HOTEL_RATINGS; let i = index;">
                                                <span *ngIf="selectedHotelResult.starRating >= rating"><img
                                                        src="assets/images/hotel/star-filled.svg"
                                                        alt="star-filled" /></span>
                                                <span *ngIf="selectedHotelResult.starRating < rating"><img
                                                        src="assets/images/hotel/star.svg" alt="star-filled" /></span>
                                            </span>
                                            <!-- <span><img src="assets/images/hotel/star-filled.svg" alt="star-filled" /></span>
                                            <span><img src="assets/images/hotel/star-filled.svg" alt="star-filled" /></span>
                                            <span><img src="assets/images/hotel/star-filled.svg" alt="star-filled" /></span>
                                            <span><img src="assets/images/hotel/star.svg" alt="star" /></span>
                                            <span><img src="assets/images/hotel/star.svg" alt="star" /></span> -->
                                        </div>
                                    </div>
                                </div>
                                <ul>
                                    <li>
                                        <img class="inlineblock_m"
                                            src="assets/images/hotel/local_cafe{{isAmenityAvailable('AMENITY_TYPE_BKFAST') ? '-active' : ''}}.svg"
                                            alt="" />
                                        <span class="inlineblock_m">{{'hotelSelect.Breakfast' | translate}}</span>
                                    </li>

                                    <li>
                                        <img class="inlineblock_m"
                                            src="assets/images/hotel/wifi{{isAmenityAvailable('AMENITY_TYPE_WIFI') ? '-active' : ''}}.svg"
                                            alt="" />
                                        <span class="inlineblock_m">{{'hotelSelect.FreeWiFi' | translate}}</span>
                                    </li>

                                    <li>
                                        <img class="inlineblock_m"
                                            src="assets/images/hotel/fitness-dumbbell{{isAmenityAvailable('AMENITY_TYPE_GYM') ? '-active' : ''}}.svg"
                                            alt="" />
                                        <span class="inlineblock_m">{{'hotelSelect.Gym' | translate}}</span>
                                    </li>

                                    <li>
                                        <img class="inlineblock_m"
                                            src="assets/images/hotel/local_restaurant{{isAmenityAvailable('AMENITY_TYPE_RESTAURANT') ? '-active' : ''}}.svg"
                                            alt="" />
                                        <span class="inlineblock_m">{{'hotelSelect.Restaurant' | translate}}</span>
                                    </li>

                                    <li>
                                        <img class="inlineblock_m"
                                            src="assets/images/hotel/bell-call{{isAmenityAvailable('AMENITY_TYPE_ROOMSERVICE') ? '-active' : ''}}.svg"
                                            alt="" />
                                        <span class="inlineblock_m">{{'hotelSelect.Concierge' | translate}}</span>
                                    </li>

                                    <li>
                                        <img class="inlineblock_m"
                                            src="assets/images/hotel/swimming-pool-person{{isAmenityAvailable('AMENITY_TYPE_POOL') ? '-active' : ''}}.svg"
                                            alt="" />
                                        <span class="inlineblock_m">{{'hotelSelect.Pool' | translate}}</span>
                                    </li>

                                    <li>
                                        <img class="inlineblock_m"
                                            src="assets/images/hotel/bar{{isAmenityAvailable('AMENITY_TYPE_BAR') ? '-active' : ''}}.svg"
                                            alt="" />
                                        <span class="inlineblock_m">{{'hotelSelect.Bar' | translate}}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <ng-container *ngIf="isMapSupported()">
                            <a (click)="mapsSelector(selectedHotelResult.latitude,selectedHotelResult.longitude)"
                            class="addlue" style="margin-left:8px;cursor:pointer;white-space: nowrap;">{{'hotelSelect.OPENMAP' | translate}}</a>
                        </ng-container>
                        <div  *ngIf="this.searchService1.mapSupprted" class="hotel-description-right"
                            (click)="mapsSelector(selectedHotelResult.latitude,selectedHotelResult.longitude)"
                            style="cursor:pointer;">
                            <google-map
                            class="sqs-block-map"
                            [center]="{ lat: selectedHotelResult.latitude, lng: selectedHotelResult.longitude }"
                            [zoom]="13"
                            [options]="mapOptions"
                            height="400px"
                            width="100%"
                          >
                            <map-marker
                              [position]="{ lat: selectedHotelResult.latitude, lng: selectedHotelResult.longitude }"
                            ></map-marker>
                          </google-map>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <app-navigation></app-navigation>