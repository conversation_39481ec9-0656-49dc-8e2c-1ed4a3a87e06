@import "../../variables.scss";

:host {
    width: 100vw;
}

.summery-view-container {
    float: left;
    width: 100%;
    background: #fff;
}

.summary-view {
    float: left;
    width: 100%;
    background: #fff;
}
.corporateRate{
    font-size: 14px;
    color: var(--hyperlink-color);
    display: inline-block;
    vertical-align: bottom;
}
.custom-selectbox {
    cursor: pointer;
    position: relative;
    display: inline-block;
    padding-right: 15px;
    margin-right: 5px;
}

.custom-selectbox .field-value {
    color: #AEAEAE;
}

.custom-selectbox-value {
    font-size: 16px !important;
    letter-spacing: -0.71px !important;
    color: #413E3B !important;
    line-height: 16px !important;
}

.custom-selectbox .control-icon {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.filter-select-container .custom-selectbox-value {
    max-width: 120px;
    text-overflow: ellipsis;
    overflow: hidden;
}

.control-icon {
    font-size: 4px;
    margin-left: 10px;
    color: $link-color;
}


/*box css*/

.summary-view-inner {
    float: left;
    width: 100%;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -ms-flex-align: center;
    align-items: center;
    padding: 20px 40px;
}

.source-dest {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    letter-spacing: 0.42px;
    line-height: 17px;
    color: #000000;
}

.summary-view-middle {
    display: inline-flex;
    flex-direction: column;
    font-size: 12px;
    line-height: 15px;
    color: #AEAEAE;
}

.summary-view-middle .date {
    letter-spacing: -0.6px;
    margin-bottom: 5px;
}

.summery-room-details {
    display: flex;
    margin-bottom: 6px;
}

.edit-summery-view {
    display: inline-flex;
}

.edit-summery-view .icon-edit {
    background: #fff;
    height: 32px;
    width: 32px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    color: var(--secondarybutton-font-color);
    cursor: pointer;
}

.summery-room {
    margin-right: 20px;
}

.no-flight-found-container {
    float: left;
    width: 100%;
}

.summery-view-container {
    float: left;
    width: 100%;
    background: #fff;
}

.tab-content-item {
    float: left;
    width: 100%;
}

.selected-flight-container {
    float: left;
    width: 100%;
    padding: 5px 20px 13px;
}

.summery-person {
    margin-right: 20px;
}

.summery-person .container-icon {
    margin-right: 8px;
}

.policy-container {
    text-align: right;
}

.hotels-taxes-fees {
    font-size: 13px;
    color:#A9A9A9;
    line-height: 15px;
    margin-bottom: 4px;
}

;

.seeMoreLink {
    float: left;
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    padding-top: 6px;
}

.seeMoreLink a {
    font-size: 14px;
    letter-spacing: 1.17px;
    line-height: 18px;
    font-family: $fontBold;
}

.seeMoreLink a span {
    display: block;
}

.switch-ui {
    position: relative;
    left: 10px;
    top: -6px;
    text-align: right;
}

:host ::ng-deep.switch-medium.checked small {
    height: 22px !important;
    width: 22px !important;
    margin-top: 1px !important;
    background-color: var(--hyperlink-color) !important;
    border: none !important;
    box-sizing: none !important;
    left: 37px !important;
}

:host ::ng-deep.switch.switch-medium.checked {
    border: 1px solid var(--dark-bg-color) !important;
}

:host ::ng-deep.switch {
    height: 24px !important;
    margin-top: 5px !important;
    width: 60px !important;
    border-radius: 30px !important;
    background-color: gray !important;
}

:host ::ng-deep.switch-medium small {
    height: 22px !important;
    width: 22px !important;
    box-sizing: border-box;
    margin-top: 1px !important;
    border: 1px solid #979797;
}

:host ::ng-deep.switch.switch-medium>.switch-pane .switch-label-unchecked {
    padding-left: 35px;
    padding-right: 15px;
    color: white;
    line-height: 23px;
    font-size: 13px;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
}

:host ::ng-deep.switch.switch-medium>.switch-pane .switch-label-checked {
    padding-left: 15px;
    padding-right: 35px;
    color: white;
    line-height: 23px;
    font-size: 13px;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
}

:host ::ng-deep.switch.switch-medium small {
    width: 30px;
    height: 30px;
    right: calc(100% - 20px);
}

.feedbackImage {
    width: 30px;
    height: 28px;
}

@media (max-width: 991px) {
    .summary-view-inner {
        flex-wrap: wrap;
        padding: 9px 20px 17px;
    }

    .source-dest {
        order: 1;
    }

    .summary-view-middle {
        order: 3;
        width: 100%;

    }

    .edit-summery-view {
        order: 2;
        position: relative;
        top: 10px;
    }

    .hotel-price {
        font-size: 20px;
        line-height: 21px;
    }
}

/*new css for new design*/
.hotel-details {
    float: left;
    width: 100%;
    padding: 0 8px;
}

.hotel-details-inner {
    float: left;
    width: 100%;
    border-radius: 6px;
    background-color: #FFFFFF;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    margin-top: 25px;
    padding: 18px 20px 16px 20px;
}

.hotel-heading {
    float: left;
    width: 100%;
    background: $themeColor2;
    padding: 0 15px;
    height: 43px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.hotel-heading-link {
    line-height: 0;
}

.hotel-heading h4 {
    font-size: 16px;
    letter-spacing: 0.69px;
    padding-left: 14px;
    line-height: 20px;
    font-family: $fontRegular;
    text-transform: uppercase;
    color: #fff;
    margin-bottom: 0;
    font-weight: regular;
}

.hotal-carosal {
    display: flex;
    min-height: 100px;
    justify-content: center;
    float: left;
    width: 100%;
    position: relative;
}

.hotal-carosal-loader {
    float: left;
    width: 100%;
    text-align: center;
    z-index: 99;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 0;
}

.address-duration {
    float: left;
    width: 100%;
    font-size: 14px;
    line-height: 18px;
    font-family: $fontRegular;
    text-align: center;
    margin-top: 12px;
}

.hotel-distance,
.hotel-address,
.hotel-duration,
.distance-car,
.distance-walk {
    display: inline-block;
}

.distance-car,
.distance-walk {
    font-size: 13px;
}

.hotel-distance {
    font-family: $fontBold;
}

.hotel-address {
    margin-left: 15px;
    margin-right: 50px;
}

.distance-car img,
.distance-walk img {
    margin-right: 10px;
}

.distance-car {
    margin-right: 30px;
}

.hotel-description {
    float: left;
    width: 100%;
    margin-bottom: 248px;
}

.hotel-description-inner {
    float: left;
    width: 100%;
    background-color: #FFFFFF;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    border-radius: 6px;
    display: flex;
    font-family: $fontRegular;
}

.hotel-description-left {
    width: 50%;
    padding: 35px 40px 39px 40px;
}

.hotel-description-right {
    width: 50%;
}

.hotel-description-right iframe {
    border-radius: 0 6px 6px 0;
    width: 100%;
    height: 356px;
    border: none;
}

.sqs-block-map {
    pointer-events: none;
}

.hotel-description-text {
    float: left;
    width: 100%;
    margin-bottom: 18px;
}

.hotel-description-rating {
    float: left;
    width: 100%;
    padding-bottom: 20px;
    border-bottom: 1px solid #E3E3E3;
}

.hotel-amenities {
    float: left;
    width: 100%;
    padding-top: 14px;
}

.hotel-amenities-header {
    float: left;
    width: 100%;
    margin-bottom: 18px;
    display: flex;
    justify-content: space-between;
}

.hotel-amenities-header h5 {
    font-size: 15px;
    line-height: 15px;
}

.hotel-amenities ul {
    float: left;
    width: 100%;
    max-width: 330px;
}

.hotel-amenities ul li {
    float: left;
    width: 33.33%;
    font-size: 12px;
    line-height: 15px;
    margin-bottom: 9px;
}

.hotel-amenities ul li img {
    margin-right: 10px;
}

.description-heading {
    padding: 33px 43px 21px 10px;
    float: left;
    width: 100%;
}

.description-heading h4 {
    font-size: 16px;
    letter-spacing: -0.71px;
    line-height: 16px;
    margin: 0;
}

.hotel-description-text p {
    font-size: 16px;
    line-height: 20px;
    max-width: 404px;
}

.hotel-rating {
    margin-right: 10px;
    font-size: 15px;
    line-height: 15px;
}

.hotel-rating-stars {
    margin-top: -8px;
}


.inlineblock {
    display: inline-block;
    vertical-align: middle;
}

.result-card-box {
    position: relative;
    background: #fff;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    border-radius: 6px;
    font-family: $fontRegular;
    float: left;
    width: 100%;
    margin-bottom: 11px;
}

.block {
    display: block;
}

.result-card-box-inner {
    position: relative;
    z-index: 2;
    background: #fff;
    float: left;
    width: 100%;
    border-radius: 6px;
    padding: 8px;
    display: flex;
    align-items: center;
}

.result-card-left {
    width: 125px;
    padding-right: 20px;
}

.result-card-left img {
    height: 105px;
    width: 105px;
    border-radius: 6px;
}

.hotel-name {
    font-size: 18px;
    font-family: $fontRegular;
    line-height: 23px;
}

.hotel-distance-address {
    font-size: 14px;
    line-height: 15px;
    padding-top: 10px;
}

.hotel-address {
    position: relative;
    padding-left: 6px;
    margin-left: 8px;
}

.result-card-middle-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-bottom: 11px;
    padding-left: 7px;
    padding-right: 10px;
}

.result-card-right {
    width: 200px;
    text-align: center;
}

.result-card-middle {
    width: calc(100% - 325px);
}

.duration-section {
    display: inline-block;
    vertical-align: middle;
    margin-right: 32px;
    margin-top: 7px;
    margin-bottom: 2px;
}

.duration-section:last-child {
    margin-right: 0;
}

.duration-section span {
    font-size: 12px;
    line-height: 15px;
}

.result-card-middle-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.result-card-middle-bottom-left {
    display: flex;
    align-items: center;
    width: calc(100% - 100px);
}

.result-card-middle-bottom {
    border-top: 2px solid #e3e3e3;
    float: left;
    width: 100%;
    padding-top: 4px;
    padding-left: 7px;
    padding-right: 10px;
}

.icon-margin {
    margin-right: 5px;
}

.duration-section .icon-margin {
    margin-right: 12px;
}
.lowercase {
    text-transform: lowercase;
}
.hotel-price {
    font-size: 24px;
    font-family: $fontBold;
    margin-bottom: 4px;
    float: left;
    width: 100%;
    color: var(--hyperlink-color);
    letter-spacing: .5px;
    line-height: 24px;
    display: contents;
}

.hotel-select-button {
    float: left;
    width: 100%;
}

.hotel-select-button button {
    width: 153px;
    border-radius: 6px;
    height: 43px;
    font-size: 16px;
    letter-spacing: 1.33px;
    font-family: $fontBold;
    font-weight: normal;
}

.policy-container span {
    font-size: 12px;
    line-height: 12px;
    color: #AEAEAE;
}

.result-card-middle-top-right {}


.small-discription {
    font-size: 12px;
    line-height: 16px;
    color: #AEAEAE;
    text-transform: uppercase;
}

.night-count {
    color: #AEAEAE;
    font-size: 12px;
    line-height: 16px;
    min-width: 55px;
    text-align: right;
}

.room-price {
    font-size: 12px;
    line-height: 15px;
    font-family: $fontBold;
}

.policies-dropdown {
    position: relative;
}

.description-heading-inner {
    float: left;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.result-card-middle-bottom-right {
    width: 105px;
}

.result-card-left-inner {
    position: relative;
    width: 100%;
    height: 105px;
}

.room-placeholder-img {
    position: absolute;
    z-index: 1;
}

.room-img {
    position: relative;
    z-index: 2;
}

.showMoreLess {
    color: var(--button-font-color);
    border: none;
    background: none;
    padding: 0;
    cursor: pointer;
    white-space: nowrap;
}

.loader-text-container {
    display: flex;
    float: left;
    width: 100%;
    padding: 10px 10px;
}

.hotel-carosal-placeholder-iamge {
    float: left;
    width: 100%;
    text-align: center;
}

.image {
    padding-top: 70px;
    padding-left: 250px;
}

.text1 {
    font-size: 18px;
    white-space: nowrap;
}

.room-selection {
    float: left;
    width: 100%;
}

.room-selection-inner {
    float: left;
    width: 100%;
}

.heightClass {
    height: 46px;
    overflow: hidden;
}

@media (max-width: 991px) {
    .hotel-description-inner {
        flex-wrap: wrap;
    }

    .hotel-description-left {
        width: 100%;
    }

    .hotel-description-right {
        width: 100%;
    }

    .hotel-description-text p {
        max-width: none;
    }

    .duration-section {
        margin-right: 20px;
    }

    .duration-section .icon-margin {
        margin-right: 5px;
    }

    .hotel-amenities ul {
        max-width: none;
    }

    .result-card-right {
        min-width: 160px;
    }

    .result-card-middle {
        width: calc(100% - 285px);
    }

    .hotel-select-button button {
        width: 120px;
    }

    .result-card-middle-top-right {
        text-align: right;
    }

    .result-card-middle-bottom-left {
        width: calc(100% - 60px);
    }

    .result-card-middle-bottom-right {
        width: 60px;
    }
}

@media (max-width: 767px) {
    .switch-ui {
        position: relative;
        left: 10px;
        top: -6px;
        text-align: right;
    }
    .corporateRate{
        font-size: 14px;
        color: var(--hyperlink-color);
        display: inline-block;
        vertical-align: bottom;
    }
    .address-duration {
        font-size: 12px;
        line-height: 18px;
        text-align: left;
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
    }

    .text1 {
        font-size: 10px !important;
        white-space: nowrap;
    }

    .hotel-address {
        padding: 0;
        margin: 0;
    }

    .distance-car,
    .distance-walk {
        font-size: 11px;
        float: left;
        width: 100%;
        margin-right: 0;
        text-align: right;
    }

    .hotel-distance {
        float: left;
        width: 100%;
        text-align: left;
    }

    hotel-address {
        float: left;
        width: 100%;
        text-align: left;
    }

    /*.hotel-duration{display: flex;}*/

    .result-card-left {
        width: 79px;
        padding-right: 8px;
    }

    .result-card-left img {
        height: 71px;
        width: 71px;
        border-radius: 6px;
    }

    .result-card-middle {
        width: calc(100% - 81px);
    }

    .hotel-name {
        font-size: 13px;
    }

    .duration-section span {
        font-size: 9px;
        width: calc(100% - 27px);
    }

    .hotel-price {
        font-size: 14px;
        margin-bottom: 4px;
        letter-spacing: 1.17px;
        line-height: 15px;
    }

    .hotels-taxes-fees {
        font-size: 11px;
        line-height: 12px;
    }

    ;

    .night-count {
        font-size: 9px;
        line-height: 11px;
    }

    .room-price {
        line-height: 14px;
    }

    .small-discription {
        font-size: 9px;
        line-height: 15px;
    }

    .policy-container {
        text-align: right;
    }

    .policy-container img {
        width: 10px;
    }

    .policy-container span {
        font-size: 9px;
        line-height: 12px;
    }

    .result-card-middle-bottom {
        border-top: 1px solid #e3e3e3;
        padding-top: 5px;
        padding-left: 5px;
        padding-right: 8px;
    }

    .result-card-box-inner {
        padding: 6px;
    }

    .duration-section {
        float: left;
        width: 100%;
        margin-right: 0;
        margin-top: 0;
        margin-bottom: 0;
        line-height: 16px;
        display: flex;
        align-items: flex-start;
    }

    .result-card-middle-bottom-left {
        width: calc(100% - 60px);
    }

    .result-card-middle-top-right {
        text-align: right;
    }

    .room-selection {
        padding: 0 8px;
    }

    .hotel-description {
        padding: 0 8px;
    }

    .hotel-description {
        margin-bottom: 28px;
    }

    .hotel-description-inner {
        flex-wrap: wrap;
    }

    .hotel-description-left {
        width: 100%;
        padding: 22px 16px 22px 16px;
    }

    .hotel-description-right {
        width: 100%;
    }

    .hotel-description-text p {
        font-size: 13px;
        line-height: 15px;
        max-width: 404px;
    }

    .hotel-description-rating {
        padding-bottom: 0px;
        border-bottom: none;
        width: auto;
        line-height: 12px;
    }

    .hotel-amenities-header h5 {
        font-size: 13px;
        line-height: 15px;
    }

    .hotel-rating {
        font-size: 9px;
    }

    .hotel-rating-stars span img {
        width: 15px;
    }

    .result-card-middle-top {
        padding-bottom: 7px;
        padding-left: 5px;
        padding-right: 5px;
        justify-content: space-between;
    }

    .hotel-amenities {

        border-top: 1px solid #E3E3E3;
        padding-top: 16px;
    }

    .hotel-description-text {
        margin-bottom: 25px;
    }

    .hotel-rating-stars {

        margin-top: -3px;

    }

    .hotel-amenities ul li {

        font-size: 9px;

    }

    .hotel-amenities-header {
        margin-bottom: 11px;
    }

    .hotel-amenities ul li {
        margin-bottom: 6px;
    }

    .hotel-description-right iframe {
        height: 105px;
        margin-bottom: 2px;
    }

    .result-card-box {
        margin-bottom: 13px;
    }

    .description-heading {
        padding: 16px 5px 15px 10px;
    }

    .description-heading h4 {
        font-size: 14px;
        letter-spacing: -0.62px;
    }

    .room-item:last-child .result-card-box {
        margin-bottom: 0;
    }

    .hotel-details-inner {
        margin-top: 16px;
        padding: 6px 20px 16px 20px;
    }

    .duration-section .icon-margin {
        margin-right: 8px;
    }

    .hotel-heading h4 {
        font-size: 12px;
        letter-spacing: 0.51px;
        line-height: 15px;
    }


    .container {
        padding-left: 0;
        padding-right: 0;
    }

    .result-card-left-inner {
        height: 71px;
    }

    .result-card-middle-top-right {
        overflow-wrap: break-word;
    }
}