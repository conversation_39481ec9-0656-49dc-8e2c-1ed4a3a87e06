<email-header></email-header>
<div class="main-wrapper">
  <div class="content">
    <div class="container">
      <app-car-view></app-car-view>
      <div *ngIf="(isSearchResultsCount(orgCarSearchResponse) === 0 && callComplete)"
        class="no-flight-found-container text-center" style="display: block;">
        <img style="margin-top: 20px;" src="assets/images/no-flight-found.png" />
        <div class="text1 text-danger" style="font-size: 18px;">{{resultErrorMessage}}</div>
        <div class="text1" style="font-size: 18px;">{{reason}}</div>
      </div>
      <div class="hotel-list-container" style="display: block;">
        <div class="filter-container" style="position: sticky;top:0;z-index: 10;"
          *ngIf="(isSearchResultsCount(this.carBookingService.originalCarSearchResponse) > 0) && callComplete">
          <div class="filter-strip">
            <div class="">
              <div class="filter-strip-inner">
                <ul>
                  <li class="filter-item">
                    <span class="filter-item-link" href="javascript:void(0)">
                      <span class="icon icon-setting"></span>
                    </span>
                  </li>
                  <li class="filter-item" id="filter-car-type">
                    <ng-select class="filter-select-box flightTimeTemplate" groupBy="Type" [closeOnSelect]="false"
                      [multiple]="true" #carType appendTo="#filter-car-type" dropdownPosition="bottom"
                      [searchable]="false" [clearable]="false" [items]="cars" bindLabel="name" bindValue="value">
                      <ng-template ng-header-tmp>
                        <div class="selectox-header">
                          <span>{{'carresult.VehicleType' | translate }}</span>
                          <span class="selectBox-remove" (click)="carType.toggle()"><span
                              class="material-icons">clear</span></span>
                        </div>
                      </ng-template>
                      <ng-template ng-option-tmp let-option="item">
                        <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="{{option.Name}}">
                          <input type="checkbox" id="{{option.Name}}" class="mdl-checkbox__input"
                            (change)="onCarTypeClicked(option.Name, $event.target.checked)"
                            [checked]="isCarTypeChecked(option.Name)">
                          <span class="mdl-checkbox__label">{{option.value}}</span>
                        </label>
                      </ng-template>
                      <ng-template ng-footer-tmp>
                        <div class="selectBox-footer-filter-button">
                          <button class="btn primary-button"
                            (click)="[applyCarTypeFilter(),carType.toggle()]">{{'hotelResult.Apply' | translate}}
                          </button>
                        </div>
                      </ng-template>
                    </ng-select>
                    <a class="filter-item-link" href="javascript:void(0)" attr.data-track="CarResultsFilter"
                      attr.data-params="filter=Car Type" (click)="carType.toggle()">{{'carresult.VehicleType' | translate }}</a>
                  </li>
                  <li *ngIf="this.carBookingService.brandValue !==1" class="filter-item" id="filter-car-brand">
                    <ng-select class="filter-select-box flightTimeTemplate" groupBy="Type" [closeOnSelect]="false"
                      [multiple]="true" #carBrand appendTo="#filter-car-brand" dropdownPosition="bottom"
                      [searchable]="false" [clearable]="false" [items]="preDefinedCarBrands" bindLabel="label"
                      bindValue="value">
                      <ng-template ng-header-tmp>
                        <div class="selectox-header">
                          <span>{{'carresult.RentalCompany' | translate }}</span>
                          <span class="selectBox-remove" (click)="carBrand.toggle()"><span
                              class="material-icons">clear</span></span>
                        </div>
                      </ng-template>
                      <ng-template ng-option-tmp let-option="item">
                        <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="{{option.value}}">
                          <input type="checkbox" id="{{option.value}}" class="mdl-checkbox__input"
                            (change)="onCarBrandClicked(option.value, $event.target.checked)"
                            [checked]="isCarBrandChecked(option.value)">
                          <span class="mdl-checkbox__label">{{option.label}}</span>
                        </label>
                      </ng-template>
                      <ng-template ng-footer-tmp>
                        <div class="selectBox-footer-filter-button">
                          <button class="btn primary-button"
                            (click)=[applyCarBrandFilter(),carBrand.toggle()]>{{'hotelResult.Apply' | translate}}
                          </button>
                        </div>
                      </ng-template>
                    </ng-select>
                    <a class="filter-item-link" href="javascript:void(0)" attr.data-track="CarResultsFilter"
                      attr.data-params="filter=Car Brand" (click)="carBrand.toggle()">{{'carresult.RentalCompany' | translate }}</a>
                  </li>
                  <li *ngIf="this.carBookingService.noFilterRequired > 0" class="filter-item"
                    id="filter-car-specification">
                    <ng-select class="filter-select-box flightTimeTemplate" groupBy="Type" [closeOnSelect]="false"
                      [multiple]="true" #carSpecification appendTo="#filter-car-specification" dropdownPosition="bottom"
                      [searchable]="false" [clearable]="false" [items]="Specification" bindLabel="name"
                      bindValue="value">
                      <ng-template ng-header-tmp>
                        <div class="selectox-header">
                          <span>{{'carresult.CarSpecification' | translate }}</span>
                          <span class="selectBox-remove" (click)="carSpecification.toggle()"><span
                              class="material-icons">clear</span></span>
                        </div>
                      </ng-template>
                      <ng-template ng-option-tmp let-option="item">
                        <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="{{option.Name}}">
                          <input type="checkbox" id="{{option.value}}" class="mdl-checkbox__input"
                            (change)="onCarSpecificationClicked(option.value, $event.target.checked)"
                            [checked]="isCarSpecificationChecked(option.value)">
                          <span class="mdl-checkbox__label">{{option.Name}}</span>
                        </label>
                      </ng-template>
                      <ng-template ng-footer-tmp>
                        <div class="selectBox-footer-filter-button">
                          <button class="btn primary-button"
                            (click)=[applyCarSpecificationFilter(),carSpecification.toggle()]>{{'hotelResult.Apply' |
                            translate}}
                          </button>
                        </div>
                      </ng-template>
                    </ng-select>
                    <a class="filter-item-link" href="javascript:void(0)" attr.data-track="CarResultsFilter"
                      attr.data-params="filter=Car Specification" (click)="carSpecification.toggle()"> {{'carresult.CarSpecification' | translate }}</a>
                  </li>
                  <li *ngIf="this.carBookingService.locationFound > 0 " class="filter-item" id="filter-car-location">
                    <ng-select class="filter-select-box flightTimeTemplate" groupBy="Type" [closeOnSelect]="false"
                      [multiple]="true" #carLocation appendTo="#filter-car-location" dropdownPosition="bottom"
                      [searchable]="false" [clearable]="false" [items]="carsLocation" bindLabel="Name"
                      bindValue="value">
                      <ng-template ng-header-tmp>
                        <div class="selectox-header">
                          <span>{{'carresult.CarLocation' | translate }}</span>
                          <span class="selectBox-remove" (click)="carLocation.toggle()"><span
                              class="material-icons">clear</span></span>
                        </div>
                      </ng-template>
                      <ng-template ng-option-tmp let-option="item">
                        <label class="radio-button-label" for="{{option.Name}}">
                          <input type="radio" id="{{option.Name}}" class="radio-button1"
                            (click)="onCarLocationClicked(option)" [checked]="isCarLocationChecked(option.Name)">
                          <span *ngFor="let item1 of this.getName(option.Name);let i=index"><span class="label12"
                              [ngStyle]="{'color':(i===0 && item1!=='All')? 'gray':'black'}" style=""><i *ngIf="i===0"
                                class="{{getIconText(item1)}}"
                                style="color:gray !important;font-size: 16px;margin-right:6px;"
                                aria-hidden="true"></i>{{item1}}</span><br></span>
                        </label>
                      </ng-template>
                      <ng-template ng-footer-tmp>
                        <div class="selectBox-footer-filter-button">
                          <button class="btn primary-button"
                            (click)=[applyCarLocationFilter(),carLocation.toggle()]>{{'hotelResult.Apply' | translate}}
                          </button>
                        </div>
                      </ng-template>
                    </ng-select>
                    <a class="filter-item-link" href="javascript:void(0)" attr.data-track="CarResultsFilter"
                      attr.data-params="filter=Car Location" (click)="carLocation.toggle()"> {{'carresult.CarLocation' | translate }}</a>
                  </li>
                  <li *ngIf="this.carBookingService.locationTypeFound > 0 " class="filter-item"
                    id="filter-car-locationType">
                    <ng-select class="filter-select-box flightTimeTemplate" groupBy="Type" [closeOnSelect]="false"
                      [multiple]="true" #carLocationType appendTo="#filter-car-locationType" dropdownPosition="bottom"
                      [searchable]="false" [clearable]="false" [items]="carsLocationType" bindLabel="name"
                      bindValue="value">
                      <ng-template ng-header-tmp>
                        <div class="selectox-header">
                          <span>{{'carresult.LocationType' | translate }}</span>
                          <span class="selectBox-remove" (click)="carLocationType.toggle()"><span
                              class="material-icons">clear</span></span>
                        </div>
                      </ng-template>
                      <ng-template ng-option-tmp let-option="item">
                        <label class="radio-button-label" for="{{option.value}}">
                          <input type="radio" id="{{option.value}}" class="radio-button1"
                            (change)="onCarLocationTypeClicked(option.value)"
                            [checked]="isCarLocationTypeChecked(option.value)">
                          <span class="label13" style="margin-right:0px;"> <i class="{{getIconText(option.value)}}"
                              style="color:gray !important;font-size: 16px;position: relative;top: 2px;margin-right: 10px;"
                              aria-hidden="true"></i>{{option.Name}}</span>
                        </label>
                      </ng-template>
                      <ng-template ng-footer-tmp>
                        <div class="selectBox-footer-filter-button">
                          <button class="btn primary-button"
                            (click)="[applyCarLocationTypeFilter(),carLocationType.toggle()]">{{'hotelResult.Apply' |
                            translate}}
                          </button>
                        </div>
                      </ng-template>
                    </ng-select>
                    <a class="filter-item-link" href="javascript:void(0)" (click)="carLocationType.toggle()">
                       {{'carresult.LocationType' | translate }}</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="flight-list-sorting-container" style="background: var(--light-bg-color);">
            <div class="row" style="margin-top: 20px;">
              <div class="col-12" style="padding-left: 12px !important;">
                <div class="filter-search-box">
                  <img src="assets/images/ic_search.svg" />
                  <input type='text' (focus)="this.boxSelect=true;" (focusout)="this.boxSelect=false;"
                    [ngStyle]="{'border-color': this.boxSelect ? this.searchService.darkBgColor:'#F7F7F7'}"
                    placeholder="{{ 'carresult.Searchbylocationaddress' | translate}}" (input)="onSearchTextChange($event.target.value)" />
                </div>
              </div>
            </div>
            <form [formGroup]="carResultForm">
              <div class="flight-list-sorting-container-inner">
                <div *ngIf="this.carBookingService.pickUpType!=='AIRPORT'" class="flight-list-sorting"
                  id="sortingContainer2">
                  <div class="filter custom-selectbox" attr.data-track="CarResultsSortBy"
                    (click)="sortingDropdown1.toggle()">
                    <div class="select-input12">
                      <ng-select #sortingDropdown1 appendTo="#sortingContainer2" value="Sort by"
                        dropdownPosition="bottom" [searchable]="false" [clearable]="false"
                        [items]="sortOptionsHotel | translateOptions" bindLabel="value" bindValue="id"
                        formControlName="sortingDropdown" (change)="sortOptionChanged($event)">
                        <ng-template ng-header-tmp>
                          <div class="selectox-header">
                            <span>{{'hotelResult.Sortby' | translate}}</span>
                            <span class="selectBox-remove" (click)="sortingDropdown1.toggle()"><span
                                class="material-icons">clear</span></span>
                          </div>
                        </ng-template>
                      </ng-select>
                      <div class="select-overlay"></div>
                    </div>
                    <div class="field-value custom-selectbox-value" attr.data-track="CarResultsSortBy">
                      {{this.sortValue | translate}}</div>
                    <span class="control-icon icon-triangle"
                      attr.data-track="CarResultsSortBy"></span>
                  </div>
                </div>
                <div *ngIf="this.carBookingService.policyObject"  class="policyReview" (click)="openPolicyModal(policyModal)">
                    {{'setting.ReviewYourPolicy' | translate}}
                </div> 
              </div>
            </form>
            <div *ngIf="(isSearchResultsCount(carSearchResponse) === 0  && callComplete)"
            class="no-flight-found-container text-center">
            <img style="margin-top: 20px;" src="assets/images/no-flight-found.png" />
            <div class="text1 text-danger" style="font-size: 18px;">{{resultErrorMessage}}</div>

            <!--  <div style="margin-top:15px">
        <a (click)="clearAll()"style="color:var(--hyperlink-color);cursor:pointer;">Clear All Filter</a></div>
   -->
          </div>
          </div>
        
          </div>
          <div *ngIf="isSearchResultsCount(carSearchResponse) > 0" class="hotel-list">
            <div *ngFor="let carItem of carArray(); let i = index;" class="hotel-list-item">
              <div class="result-card-box">
                <div *ngIf="getRecommendedText(carItem)!==''" class="top-recommandation-box">
                  <p>{{getRecommendedText(carItem)}}</p>
                </div>
                <div class="result-card-box-inner" (click)="onCarSelectionMobile(i)">
                  <div class="result-card-left">
                    <img *ngIf="carUrl(carItem)" src="{{carUrl(carItem)}}"
                    onerror="this.src='assets/images/cars/vehicle_notavailable_S.jpg' " />
                    <img style="margin-top:5px;height:auto!important;width:auto !important;border-radius: 6px"
                      *ngIf="carItem.partnerLogo" src="{{ carItem.partnerLogo }}" />
                  </div>
                  <div class="result-card-middle">
                    <div class="result-card-middle-top">
                      <div class="result-card-middle-top-left">
                        <div class="hotel-name">
                          {{getDescription(carItem.description,carItem.carType)}}
                        </div>
                        <div class="hotel-distance-address">
                          <span>{{carItem.carMake}}</span>
                        </div>
                        <div *ngIf="carItem.milage" class="hotel-distance-address">
                          <i class="fa fa-tachometer" aria-hidden="true"></i><span style="margin-left:4px;"> {{'carresult.Unlimitedmileage' | translate}}</span>
                        </div>
                        <div *ngIf="carItem.automaticTransmission" class="hotel-distance-address">
                          <span class="inlineblock icon-margin "><img class="door"
                              src="assets/images/transmission_icon.png"></span><span style="margin-left:4px;"> {{'carresult.Automatictransmission' | translate}}</span>
                        </div>
                        <div class="hotel-distance-address">
                          <span *ngIf="carItem.pickUpLocationType && carItem.pickUpLocationType!=='Airport'"
                            class="hotel-distance">{{carItem.distance | number : '.2-2'}}
                            {{'hotelResult.miles' | translate}}</span>
                          <div class="hotel-address1"><i *ngIf="carItem.pickUpLocationType"
                              style="color:#AEAEAE !important;margin-right:4px;"
                              class="{{getIconText(carItem.pickUpLocationType)}}" aria-hidden="true"></i><b>{{
                              getLocationText1(carItem.pickUpLocationType)}} {{'carresult.Pick-up' | translate }} :</b>{{carItem.pickUpLocation}}
                            <span style="" class="addlue"
                              *ngIf="carItem.locationInformation && carItem.locationInformation!==''">({{
                              getLocationType(carItem.locationInformation) }})</span></div>
                          <div class="hotel-address1"><i *ngIf="carItem.dropLocationType"
                              style="color:#AEAEAE !important;margin-right:2px;"
                              class="{{getIconText(carItem.dropLocationType)}}" aria-hidden="true"></i><b> {{
                              getLocationText1(carItem.dropLocationType) }} {{'carresult.Drop-off' | translate }} : </b><span
                              *ngIf="carItem.pickUpLocation !== carItem.dropOffLocation">{{carItem.dropOffLocation}}</span><span
                              *ngIf="carItem.pickUpLocation === carItem.dropOffLocation">{{'carresult.sameaspick-up' | translate }}</span></div>
                        </div>
                      </div>
                      <div class="result-card-middle-top-right">
                        <div class="hotel-price d-block d-md-none">
                          {{getAbsoluteNumber(carItem) | currency : getCurrencySymbol(carItem) : 'code' :
                          "1.0-0" }}
                        </div>
                        <div>
                          <div *ngIf="carItem.doors" class="duration-section car-duration">
                            <span class="inlineblock icon-margin "><img class="door"
                                src="assets/images/car_gate.svg"></span>
                            <span class="inlineblock">{{carItem.doors}}</span>
                          </div>

                          <div *ngIf="carItem.passengers" class="duration-section car-duration">
                            <span class="inlineblock icon-margin "><i class="fa fa-user" aria-hidden="true"></i></span>
                            <span class="inlineblock">{{carItem.passengers}}</span>
                          </div>

                          <div *ngIf="carItem.bags" class="duration-section walk-duration">
                            <span class="inlineblock icon-margin"><i class="fa fa-suitcase"
                                aria-hidden="true"></i></span>
                            <span class="inlineblock ">{{carItem.bags}}</span>
                          </div>
                          <!--   <div class="duration-section walk-duration">
                                                              <span class="inlineblock icon-margin" ></span>
                                                              <span class="inlineblock ">{{hotelItem.doors}}</span>
                                                          </div>-->
                        </div>

                      </div>
                    </div>
                    <div class="result-card-middle-bottom">
                      <div class="result-card-middle-bottom-left">
                        <span class="perDay"> {{getPerDayPrice(carItem) | currency : getCurrencySymbol(carItem)
                          : 'code' : "1.0-0"}} {{'carresult.avgday' | translate }}
                        </span><span *ngIf="carItem.freeCancellation" class="free">{{'carresult.FreeCancellation' | translate }}</span>
                      </div>
                      <div class="result-card-middle-bottom-right">
                        <div *ngIf="isPolicySet(carItem)" class="policy-container">
                          <img class="inlineblock icon-margin"
                            src="assets/images/hotel/policy{{carItem.policy ? '-active' : ''}}.svg" />
                          <span class="inlineblock">{{'hotelResult.Policy' | translate}}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="result-card-right d-none d-md-block">
                    <div class="hotel-price">
                      {{getAbsoluteNumber(carItem) | currency : getCurrencySymbol(carItem) : 'code':
                      '1.0-0' }}
                    </div>
                    <div class="hotel-select-button">
                      <button class="btn primary-button" (click)="selectCar(i)">{{'carresult.Select' | translate }}</button>

                      <div *ngIf="carItem.handlerType.toLowerCase() == 'travelport'" class="tax1">{{'carresult.Includingtaxandfees' | translate }}
                      </div>
                      <div *ngIf="carItem.handlerType.toLowerCase() == 'priceline'" class="tax1">{{'carresult.Includingtaxandfees' | translate }}
                      </div>


                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="resultLen">
              <div class="seeMoreLink" *ngIf=" !isSeenComplete">
                <a href="javascript:void(0)" attr.data-track="SeeMoreCarResults" (click)="loadAllItems()">
                  <span attr.data-track="SeeMoreCarResults">{{'hotelResult.SEEMOREOPTIONS' | translate}}</span>
                  <span><i class="fa fa-chevron-down" style="
                    position: relative;
                    top: -5px;
                "></i></span>
                </a>
              </div>
            </div>
          </div>
          <ng-template #policyModal let-modal>
              <div class="table-view">
                  <div class="table-cell-view">
                      <div class="modal-dialog modal-dialog-md" role="document">
                          <div class="modal-content">
                              <div *ngIf="this.disabled" class="approval_request_diaglog_bg_clickhandler"></div>
                              <div class="modal-header">
                                  <span>
                                      <img class="footerimage" [src]="this.searchService.footerLogo">
                                    </span>
                                  <button *ngIf="!this.disabled" type="button" class="close" data-dismiss="modal"
                                      (click)="onCancel()">
                                      <i class="material-icons">close</i>
                                  </button>
                              </div>
                              <div class="modal-body">
                                  <div  style="position: relative;text-align: left;display: flex;">
          
                                      <ul >
                                        <li *ngIf="this.carBookingService.policyObject.maxCarClassAllowed" style="font-size: 18px;display: flex;">
                                            <div class="bulletPoint"> . </div>  <div>{{'setting.Maximumclassallowance' | translate}}  {{ getClaaName(this.carBookingService.policyObject.maxCarClassAllowed) | translate}}</div>

                                        </li>
                                        <li *ngIf="this.carBookingService.policyObject.allowedTypes && this.carBookingService.policyObject.allowedTypes.length > 0"  style="font-size: 18px;display: flex">
                                            <div class="bulletPoint"> . </div>  <div> {{'setting.Vehicletypeallowance' | translate}} <span *ngFor="let item of this.carBookingService.policyObject.allowedTypes;let i=index">{{item}}<span *ngIf="i!==(this.carBookingService.policyObject.allowedTypes.length-1)">, </span></span></div>
                                        </li>
                                        <li *ngIf="this.carBookingService.policyObject.pricePolicyList && this.carBookingService.policyObject.pricePolicyList.length > 0"  style="font-size: 18px;">
                                            <div *ngFor="let item of this.carBookingService.policyObject.pricePolicyList">
                                                <div *ngIf="item.type==='markup'" style="display: flex;">
                                                <div class="bulletPoint"> . </div>  <div>{{'setting.Absolutepricelimitperdayincludingtaxesfees' | translate}} {{getCurrencySymbol1(this.currency)}}{{item.benchMark}}</div>
                                                </div>
                                                <div *ngIf="item.type==='markup_over_cheapest'" style="display: flex;">
                                                        <div class="bulletPoint"> . </div>  <div>{{'setting.Relativepricelimitisupto' | translate}} {{getCurrencySymbol1(this.currency)}}{{item.benchMark}} {{'setting.morefromthelowestlogicalfareinaparticularcategory' | translate}}</div>
                                                        </div>
                                                        <div *ngIf="item.type==='percent_markup_over_cheapest'" style="display: flex;">
                                                                <div class="bulletPoint"> . </div>  <div>{{'setting.Relativepricelimitisupto' | translate}} {{item.benchMark}}% {{'setting.ofthelowestlogicalfareinaparticularcategory' | translate}}</div>
                                                                </div>
                                            </div>
                                               
                                          </li>
                                      </ul>
                                      
                                   </div>    
                                 
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </ng-template>
        <app-navigation></app-navigation>