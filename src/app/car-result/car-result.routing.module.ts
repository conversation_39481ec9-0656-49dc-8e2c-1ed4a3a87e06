import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { CarResultComponent } from '../car-result/car-result.component';


const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: CarResultComponent,
    data: { title: 'Car Results' }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class CarResultRoutingModule {

}