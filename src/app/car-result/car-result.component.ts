import { Component, OnInit, HostListener } from '@angular/core';
import { Constants } from '../util/constants';
import { CarResult } from '../entity/carResult';
import { TranslateService } from "@ngx-translate/core";
import { AbstractControl, FormArray, UntypedFormBuilder, UntypedFormGroup, Validators, FormControl, Form, ValidationErrors } from '@angular/forms';
import { CommonUtils } from '../util/common-utils';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { Subscription, Observable } from 'rxjs';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ConnectionService } from 'ng-connection-service';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { CarBookingService } from '../car-booking.service';
import { SearchActionType } from '../enum/search-action.type';
//import { google } from '@agm/core/services/google-maps-types';
import { CarModalComponent } from '../car-modal/car-modal.component';
import { PopupComponent } from '../popup/popup.component';
import { SearchService } from '../search.service';
import { UserAccountService } from '../user-account.service';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { FilterType } from '../enum/filter.type';
import { DeviceDetailsService } from '../device-details.service';
import { PlatformLocation } from '@angular/common';
import { NavigationUtil } from '../util/navigation-util';
import { Title } from '@angular/platform-browser';
import { BookingService } from '../booking.service';

declare var addNavigationComponentPadding: any;
declare var addingHeader:any;

@Component({
    selector: 'app-car-result',
    templateUrl: './car-result.component.html',
    styleUrls: ['./car-result.component.scss'],
    standalone: false
})
export class CarResultComponent implements OnInit {
  cars: any;
  carElementCount = 5;
  currency='USD';
  boxSelect = false;
  carResultForm: UntypedFormGroup;
  queryParmsSubscription: Subscription;
  connectionListener: Subscription;
  isSeenComplete: boolean = false;
  resultLen = true;
  isMobile: boolean;
  preDefinedCarBrands: any;
  carSearchRequestSubscription: Subscription;
  deviceSubscription: Subscription;
  carSearchRequest: any;
  carSearchResponse: any;
  carSearchResponseSubscription: Subscription;
  Specification: any;
  carsLocation: any;
  carsLocationType: any;
  quickSearchSubscription: Subscription;
  filter_carType = [];
  filter_carLocationType = 'Any';
  filter_carBrand = [];
  filter_carSpecification = [];
  filter_Location = 'All';
  temp_filter_carType = this.filter_carType;
  temp_filter_carLocationType = this.filter_carLocationType;
  temp_filter_carBrand = this.filter_carBrand;
  temp_filter_carSpecification = this.filter_carSpecification;
  temp_filter_Location = this.filter_Location;
  temp_filter_Location1 = 'All';
  carSearchQueryParam: any;
  appliedFilterList: any = [];
  bookingWizardStep: number;
  callComplete: boolean = true;
  orgCarSearchResponse: any;
  sortOptionsHotel = Constants.SORT_OPTIONS_CAR;
  bsModalRef1: BsModalRef;
  resultErrorMessage = '';
  carClassOptions = [{ Name: 'policy.Notdefined', value: 'NOT_DEFINED' }, { Name: 'policy.MiniEconomyorCompact', value: 'Economy' }, { Name: 'policy.Intermediate', value: 'Intermediate' }, { Name: 'policy.Standard', value: 'Standard' }, { Name: 'policy.FullSize', value: 'Fullsize' }, { Name: 'policy.Premium', value: 'Premium' }, { Name: 'policy.Luxury', value: 'Luxury' }];
  reason = '';
  constructor(private fb: UntypedFormBuilder,
    public translateService: TranslateService,
    private activatedRoute: ActivatedRoute,
    private connectionService: ConnectionService,
    private modalService: BsModalService,
    public progressBar: NgxUiLoaderService,
    private bookingService: BookingService,
    public searchService: SearchService,
    private titleService: Title,
    public carBookingService: CarBookingService,
    private deviceDetailsService: DeviceDetailsService,
    private flightSearchService: SearchService,
    private userAccountInfoService: UserAccountService,
    private gallopLocalStorage: GallopLocalStorageService,
    public router: Router,
    location1: PlatformLocation) {
    location1.onPopState(() => {
    //  this.searchService.searchHeaderCliked = true;
    if (this.userAccountInfoService.showRadio) {
      this.searchService.searchHeaderCliked =false;
      }else {
        this.searchService.searchHeaderCliked =true;
      }
      if (!this.callComplete) {
        this.bsModalRef1.hide();
        if (this.quickSearchSubscription) {
          this.quickSearchSubscription.unsubscribe();
        }
        this.progressBar.stop(SearchActionType.DETAIL);
      }
    });
    this.createForm();
    if (this.userAccountInfoService 
      && this.userAccountInfoService.getAccountInfo()
      && this.userAccountInfoService.getAccountInfo().userInfo
      && this.userAccountInfoService.getAccountInfo().userInfo.currency
    ) {
      this.currency = this.userAccountInfoService.getAccountInfo().userInfo.currency;
    }
  }
 
  createForm(): void {


    this.carResultForm = this.fb.group({
      sortingDropdown: [this.carBookingService.pickUpType === 'AIRPORT' ? 'price' : 'distance']
    });

  }
  sortValue =  this.translateService.instant('ngOption.Distance')
  private initFilters() {
    this.filter_carType = [];
    this.filter_carBrand = [];
    this.filter_carSpecification = [];
    this.filter_Location = 'All';
    this.filter_carLocationType = 'Any';
    this.initTempFilters();
  }

  private initTempFilters() {
    this.temp_filter_carType = this.filter_carType;
    this.temp_filter_carBrand = this.filter_carBrand;
    this.temp_filter_carLocationType = this.filter_carLocationType;
    this.temp_filter_carSpecification = this.filter_carSpecification;
    this.temp_filter_Location = this.filter_Location;
  }
  ngOnInit() {
   // this.titleService.setTitle('Car Results');
   if(this.searchService.comingFromSelectionPAge){
   this.searchService.multicarQuery.pop();
   let seatArray = JSON.parse(this.gallopLocalStorage.getItem("selectedSeat"));
      if(seatArray && seatArray.length > 0){
        //  seatArray.pop();
          this.gallopLocalStorage.removeItem("selectedSeat");
        }
   }
   if(this.searchService.multiTripBooking && this.searchService.comingFromSelectionPAge && !this.searchService.multiTripCarDelete){
    let selectFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
    if(selectFlight && selectFlight.length > 0){
      selectFlight.pop();
    }
    this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(selectFlight));
    this.searchService.comingFromSelectionPAge =false;
  }
  
  
   addingHeader();
    this.titleService.setTitle(this.translateService.instant('search.CarResults'));
    this.cars = Constants.CARS_OPTIONS;
    this.preDefinedCarBrands = Constants.CARS_BRANDS;
    this.Specification = Constants.CAR_SPECIFICATION;
   
    this.subscribeSearchServiceEvents();
    this.initializeQueryParamsfromRoute();
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
      if (isMobile) {
        addNavigationComponentPadding();
      }
    });
    this.orgCarSearchResponse = this.carBookingService.originalCarSearchResponse;
    this.carSearchQueryParam = this.gallopLocalStorage.getItem("carSearchRequest") ? deserialize(JSON.parse(this.gallopLocalStorage.getItem("carSearchRequest"))) : undefined;
    // this.carSearchResponse = this.carBookingService.lastBroadcastedSearchResponse;
    this.carSearchRequest = this.gallopLocalStorage.getItem("carSearchRequest") ? JSON.parse(this.gallopLocalStorage.getItem("carSearchRequest")) : undefined;
    this.filterResults();
    NavigationUtil.setCurrentNavigationMenu(NavigationUtil.NAVIGATION_MENU_RESULTS);
  }
  getClaaName(id){
    let findIndex = this.carClassOptions.findIndex(item => item.value ===id);
    if(findIndex > -1){
      return this.carClassOptions[findIndex].Name;
    }
  }
  HOTEL_RECOMMENDED_TEXT_MAP = { 'BEST_OPTION': this.translateService.instant('hotelResult.BestOption'), 'PREFERRED_BRAND': this.translateService.instant('hotelResult.Preferrredhotelbrand'), 'PREFERRED_RATING': this.translateService.instant('hotelResult.Preferredstarshotel') };
  getRecommendedText(car) {
    if (car.selectionReason && this.carBookingService.currentSortOptionId !== 'price') {
      return car.selectionReason;
    } else
      return '';


  }
  carUrl(car) {
    if (car.images) {
      return car.images.S;
    }
  }
  subscribeSearchServiceEvents() {
    this.carSearchRequestSubscription = this.carBookingService.carRequest$.subscribe((carSearchRequest) => {
      this.carSearchRequest = carSearchRequest;
      if (this.carSearchRequest != null) {
       
        this.gallopLocalStorage.setItem("carSearchRequest", JSON.stringify(this.carSearchRequest));
      }
    });
   
    this.carSearchResponseSubscription = this.carBookingService.carResponseSubject.subscribe((carSearchResponse) => {
      if (this.carBookingService.originalCarSearchResponse && this.carBookingService.originalCarSearchResponse.length > 0) {
        this.updateFilter(this.carBookingService.originalCarSearchResponse)
      }
     
    
      if (carSearchResponse && carSearchResponse.length > this.carElementCount) {
        this.resultLen = true;
        if (this.isSeenComplete) {
          this.isSeenComplete = false;
        }
      } else {
        this.resultLen = false;

      }
      if(this.bsModalRef1 && this.callComplete){
        this.bsModalRef1.hide();
      }
      this.carSearchResponse = carSearchResponse;
      this.checkAndRemoveFailedCarRates(carSearchResponse);
    });
  }
  openPolicyModal(modal){
    this.bsModalRef1 = this.modalService.show(modal);
  }
  public onCancel(): void {
    this.bsModalRef1.hide();
  }
  updateFilter(carSearchResponse) {
   
      if(this.searchService.filterReset){
        this.cars = this.carBookingService.getCarType(this.carBookingService.originalCarSearchResponse);
        this.preDefinedCarBrands = this.carBookingService.getCarBrand(this.carBookingService.originalCarSearchResponse);
        this.Specification = this.carBookingService.getCarSpecification(this.carBookingService.originalCarSearchResponse);
        this.carsLocation = this.carBookingService.getCarAddressType(this.carBookingService.originalCarSearchResponse);
        this.carsLocationType = this.carBookingService.getCarLocationType(this.carBookingService.originalCarSearchResponse);
        this.appliedFilterList = this.carBookingService.carAppliedFilters;
      if(this.carBookingService.temp_filter_carType.length > 0){
        this.filter_carType = this.carBookingService.temp_filter_carType;
      //  this.temp_filter_carType = this.carBookingService.temp_filter_carType;
        for(let item of this.carBookingService.temp_filter_carType){
          let event =true;
          this.onCarTypeClicked(item,event);
        }
      }
      if(this.carBookingService.temp_filter_carSpecification.length > 0){
        this.filter_carSpecification = this.carBookingService.temp_filter_carSpecification;
      //  this.temp_filter_carType = this.carBookingService.temp_filter_carType;
        for(let item of this.carBookingService.temp_filter_carSpecification){
          let event =true;
          this.onCarSpecificationClicked(item,event);
        }
      }
      if(this.carBookingService.temp_filter_carBrand.length > 0){
        this.filter_carBrand = this.carBookingService.temp_filter_carBrand;
      //  this.temp_filter_carType = this.carBookingService.temp_filter_carType;
        for(let item of this.carBookingService.temp_filter_carBrand){
          let event =true;
          this.onCarBrandClicked(item,event);
        }
      }
      if(this.carBookingService.temp_filter_Location){
        this.filter_Location = this.carBookingService.temp_filter_Location;
          let carTypeArray = { Name: this.carBookingService.temp_filter_Location1, value: this.carBookingService.temp_filter_Location };
          this.onCarLocationClicked(carTypeArray);
       // }
      }
      if(this.carBookingService.temp_filter_Location){
        this.filter_carLocationType = this.carBookingService.temp_filter_carLocationType;
       //   let carTypeArray = { Name: this.carBookingService.temp_filter_Location1, value: this.carBookingService.temp_filter_Location };
          this.onCarLocationTypeClicked(this.filter_carLocationType );
       // }
      }
      if(!this.carBookingService.currentSortOptionId){
    
        this.carBookingService.currentSortOptionId = 'recommended';
        }else{
          let sortvaule = this.sortOptionsHotel.filter(item => item.id === this.carBookingService.currentSortOptionId);
          if(sortvaule){
          this.sortValue  = sortvaule[0].value;
          this.carResultForm.controls['sortingDropdown'].setValue(this.carBookingService.currentSortOptionId);
          }
        }
      this.filterResults()
      this.searchService.filterReset =false;
     
  
    }
  }
  sortOptionChanged(sortOption) {
    if (!sortOption) return;
    this.carBookingService.currentSortOptionId = sortOption.id;
    if (sortOption.id) {
      this.sortValue = sortOption.value ? sortOption.value : this.translateService.instant('ngOption.Distance');
    }
    this.carBookingService.applySorting(this.carSearchResponse);
    let params = "?ua_action=CartResultSortdByItem&ua_item=" + sortOption.id;
    this.searchService.letsTrack(params);
  }
  initializeQueryParamsfromRoute(): void {
    this.queryParmsSubscription = this.activatedRoute.queryParamMap.subscribe((queryParams: Params) => {

      if (queryParams && queryParams.params && Object.keys(queryParams.params).length > 0) {
        if (queryParams.params.resultFound && this.carSearchResponse
          && this.carSearchResponse.length > 0) return;
      //  this.initFilters();
        let queryParam = deserialize(JSON.parse(decodeURIComponent(queryParams.params.query)));
        this.carSearchQueryParam = queryParam;
        this.bookingWizardStep = Number(queryParams.params.step);

        if (this.bookingWizardStep === 0) {
          this.searchCar();
        }
      }

    });
  }
  searchCar() {
    this.callComplete = false;
    this.carBookingService.carPolicy = false;
    this.searchService.priceChange = undefined
    this.carBookingService.policyObject=null;
    this.searchService.displayPrice =   undefined;
    this.searchService.displayCurrency =undefined;
    this.searchService.setPreviousSearch(null);
    this.carBookingService.brandValue = 0;
    this.searchService.seatSelectArray1 = [];
    this.carBookingService.dropOff = 'same';
    this.carBookingService.filterByName = '';
    // this.carBookingService.originalCarSearchResponse=undefined;
    this.carBookingService.noFilterRequired = 0;
    this.carBookingService.locationFound = 0;
    this.carBookingService.locationTypeFound = 0
    // this.orgCarSearchResponse=undefined;
    this.carBookingService.broadCastCarResponse(undefined);
    this.unsubscribeSearch();
    this.searchCarDetailed(this.carSearchQueryParam);
    this.gallopLocalStorage.removeItem("passengers");
    this.gallopLocalStorage.removeItem("passengersFormData");
    this.gallopLocalStorage.removeItem("carSearchRequestForBooking");
    if(!this.searchService.multiTripBooking){
      this.searchService.multicarQuery =[];
      this.searchService.multiflightQuery=[];
      this.searchService.multihotelQuery=[];
      this.searchService.editMytripname='false';
    this.gallopLocalStorage.removeItem("flightSearchRequest");
    this.gallopLocalStorage.removeItem("flightSearchRequestForBooking");
    this.gallopLocalStorage.removeItem("hotelSearchRequestForBooking");
   
    }
  }

  unsubscribeSearch() {

   
    if (this.quickSearchSubscription) {
      this.quickSearchSubscription.unsubscribe();
    }
  }
  internetavailable =false;
  onImgError(event){
    event.target.src = 'assets/images/carnotavailable_S.jpg'
   //Do other stuff with the event.target
   }
  searchCarDetailed(carSearchQueryParam) {
    this.progressBar.start(SearchActionType.DETAIL);
    if(this.bsModalRef1){
      this.bsModalRef1.hide();
    }
    if(this.searchService.searchPopMsg && this.searchService.searchPopMsg.length > 0){
      this.searchService.currentMessage = this.searchService.searchPopMsg[0];
      this.searchService.showNextMessage();
    }
    this.bsModalRef1 = this.modalService.show(CarModalComponent, {
      initialState: {

      }, backdrop: true, keyboard: false, ignoreBackdropClick: true
    });
   
   

    this.quickSearchSubscription = this.carBookingService.searchCar(carSearchQueryParam, SearchActionType.DETAIL).subscribe(res => {
      this.callComplete = true;
      if (this.connectionListener) {
        this.connectionListener.unsubscribe();
      }
     // setTimeout(() => {
        this.resultErrorMessage =  this.translateService.instant('carresult.Nocarsfound');
        this.gallopLocalStorage.removeItem("failedCarRates");
        if ((res && res.success === true) && (res.data.carInfo && res.data.carInfo.length > 0)) {
          this.cars = this.carBookingService.getCarType(res.data.carInfo);
          this.bsModalRef1.hide();
          if(res.data && res.data.destination && res.data.destination.city){
          this.searchService.carDestinationCity = res.data.destination.city;
          }
          if(res.data && res.data.carPolicy){
            this.carBookingService.policyObject = res.data.carPolicy;
            
          }
          this.userAccountInfoService.approvalRequiredFor = res.data.approvalRequiredFor;
          this.searchService.travelPurposeMandatory = res.data.travelPurposeMandatory;
          if (this.carBookingService.pickUpType === 'AIRPORT') {
            this.sortOptionsHotel = this.sortOptionsHotel.filter(item => item.id !== 'distance');
            this.sortValue = this.translateService.instant('ngOption.Price');
          }
          this.preDefinedCarBrands = this.carBookingService.getCarBrand(res.data.carInfo);
          this.Specification = this.carBookingService.getCarSpecification(res.data.carInfo);
          this.carsLocation = this.carBookingService.getCarAddressType(res.data.carInfo);
          this.carsLocationType = this.carBookingService.getCarLocationType(res.data.carInfo);
          this.carBookingService.carPolicy = res.policySet;
          var event = true;
          this.onCarTypeClicked('Any', event);
          this.onCarBrandClicked('Any', event);
          this.onCarSpecificationClicked('Any', event);
          this.onCarLocationTypeClicked('Any');
          let carTypeArray = { Name: "All", value: 'All' };
          this.onCarLocationClicked(carTypeArray);
          let carSearchResponse: CarResult = deserialize(res.data.carInfo, CarResult);
          // this.isSearchResultsCount(carSearchResponse)
          this.carBookingService.applySorting(carSearchResponse);
          this.setCarResults(carSearchResponse, SearchActionType.DETAIL);
          this.carBookingService.setCarSearchResponse(carSearchResponse, false);
        } else {
          if (res && res.error_message) {
            this.reason = res.error_message.split(')')[0];
          }
          this.bsModalRef1.hide();
          this.progressBar.stop(SearchActionType.DETAIL);
          // this.carBookingService.setCarSearchResponse(new CarResult(), false);
          //this.setCarResults(new CarResult(), SearchActionType.DETAIL);
        }
    //  }, 100);
    }, error => {
    //  this.bsModalRef1.hide();
    if (this.bsModalRef1) {
      this.bsModalRef1.hide();
    }
      if (error.status != 403) {
        setTimeout(() => {
          this.callComplete = true;
          this.resultErrorMessage = this.translateService.instant('hotelResult.Pleasecheckyourinternet').toString();
          this.setCarResults(new CarResult(), SearchActionType.DETAIL,'Offline');
          if(this.bsModalRef1){
          this.bsModalRef1.hide();
          }
         
        }, 100);
      }
    });
  }
  setCarResults(carSearchResponse, searchActionType,internet?) {
    this.progressBar.stop(searchActionType);
    this.carBookingService.searchType = searchActionType;
    this.orgCarSearchResponse = carSearchResponse;
    let carResponse = JSON.parse(JSON.stringify(carSearchResponse));
    if(carResponse && carResponse[0].displayCurrency){
      this.currency = carResponse[0].displayCurrency;
    }else{
    this.currency = carResponse  ? carResponse[0].currency:'USD';
    }
  
    if(this.carBookingService.policyObject && this.carBookingService.policyObject.allowedTypes && this.carBookingService.policyObject.allowedTypes.length===0 &&
      this.carBookingService.policyObject.internationalPricePolicyList.length===0 && this.carBookingService.policyObject.pricePolicyList.length ===0 && 
      !this.carBookingService.policyObject.maxCarClassAllowed){
        this.carBookingService.policyObject =null;
      }
    this.carBookingService.setCarSearchResponse(carResponse, true);
    let userid = this.userAccountInfoService.getUserEmail();
    let sToken = this.userAccountInfoService.getSToken();
if(!internet){
    this.router.navigate(["/carResult"],
      {
        queryParams:
        {
          query: encodeURIComponent(JSON.stringify(this.carSearchQueryParam)),
          step: 0,
          resultFound: 1
        },
        replaceUrl: true
      }
    
    );
  }
  
  }
  onCarSelectionMobile(l) {
    if (this.isMobile) {
      this.selectCar(l);
    }
  }
  getPerDayPrice(car) {
    if(car.displayPrice){
      var subTotal = (car.displayPrice / car.numberOfDay);
      return subTotal;
    }else{
    var subTotal = (car.price / car.numberOfDay);
    return subTotal;
    }
  }

  onSearchTextChange(text) {
    if (text && text.length > 0) {
      this.carBookingService.filterByName = text;
      if (this.appliedFilterList.indexOf(FilterType.CAR_NAME) === -1) {
        this.appliedFilterList.push(FilterType.CAR_NAME);
      }
    } else {
      this.appliedFilterList = this.appliedFilterList.filter(filter => {
        if (filter.toString() !== FilterType.CAR_NAME.toString()) { return true; }
      });
    }

    this.filterResults();
  }
  getLocationType(item) {
    if (item && item !== '') {
      if (item === 'Terminal') {
        return 'At airport terminal'
      } else {
        return 'Outside airport - shuttle from airport terminal'
      }
    }
  }
  getDescription(description, carType) {
    if (description === 'Other' && carType !== 'Other') {
      return carType;
    } else {
      return description;
    }
  }
  selectCar(l) {
    let carSelectionData = JSON.parse(JSON.stringify(this.carSearchResponse[l]));
    if(this.searchService.overlapBookingsList.length >0){
      carSelectionData.duplicateBooking = this.searchService.overlapBookingsList;
  }

    this.userAccountInfoService.notToRefreshForm = false;
    this.userAccountInfoService.promptUserTosaveProfile = true;
    this.userAccountInfoService.paymentPageSave = false;
    if(carSelectionData.displayPrice){
    this.searchService.displayCurrency = carSelectionData.displayCurrency;
    this.searchService.displayPrice = carSelectionData.displayPrice;
    }
    let carArray = [];
    carArray.push(carSelectionData);
    this.searchService.emptyIntervalID();
    if(this.searchService.multiTripBooking){
      let selectCar = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
      if(selectCar){
        selectCar.push(carSelectionData);
      }else{
        selectCar =[];
        selectCar.push(carSelectionData)
      }
      this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(selectCar));
    }else{
      this.gallopLocalStorage.removeItem("selectedCar");
    this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(carArray));
    }
    if(!this.searchService.multiTripBooking){
    this.gallopLocalStorage.removeItem("selectedHotel");
    this.gallopLocalStorage.removeItem("selectedHotelDetailedObj");
    this.gallopLocalStorage.removeItem("selectedFlight");
    }
    this.carBookingService.temp_filter_carType = this.temp_filter_carType;
    this.carBookingService.temp_filter_carBrand = this.temp_filter_carBrand;
    this.carBookingService.temp_filter_carLocationType = this.temp_filter_carLocationType;
    this.carBookingService.temp_filter_carSpecification = this.temp_filter_carSpecification;
    this.carBookingService.temp_filter_Location= this.temp_filter_Location;
    this.carBookingService.temp_filter_Location1 = this.temp_filter_Location1;
   // this.temp_filter_carType = [];
   // this.temp_filter_carBrand = [];
   // this.temp_filter_carSpecification = [];
    //this.temp_filter_Location = 'All';
    //this.carBookingService.filterByName = '';
    this.goToTravelersForm();
  }
  goToTravelersForm() {
    window.scrollTo(0, 0);
   // this.titleService.setTitle('');
    this.titleService.setTitle(this.translateService.instant('search.TravellerDetails'));
    this.router.navigate(['/emailflow'], { relativeTo: this.activatedRoute, queryParams: { pageMode: 'WebSearch', } });

  }
  isSearchResultsCount(carSearchResponse) {
    if (carSearchResponse !== undefined && carSearchResponse.length > 0) {
      return carSearchResponse.length;
    } else {
      return 0;
    }
  }
  loadAllItems() {
    this.isSeenComplete = true;
  }
  getAbsoluteNumber(input) {
    if(input.displayPrice){
      if (input.displayPrice && input.displayPrice < 0) {
        return input.displayPrice * -1;
      }else{
        return (Number.parseFloat(input.displayPrice));
      }
    }else{
       if(input.price < 0 ){
        return input.price * -1;
      }else{
        return (Number.parseFloat(input.price));
      }
    }
  }
  getCurrencySymbol(item): string {
    if(item.displayCurrency){
      return CommonUtils.getCurrencySymbol(item.displayCurrency);
    }else{
    return CommonUtils.getCurrencySymbol(item.currency);
    }
  }
  getCurrencySymbol1(item): string {
   
    return CommonUtils.getCurrencySymbol(item);
    
  }
  isPolicySet(car) {
    if (this.carBookingService.carPolicy) {
      if (car.policy) {
        return car.policy;
      } else {
        return true;
      }
    } else {
      return false;
    }
  }
  onCarTypeClicked(option, event) {
    if (event) {
      if (option == "Any") {
        this.temp_filter_carType.push('Any');
        for (let item of this.cars) {
          this.temp_filter_carType.push(item.Name);
        }
      }
      else {
        this.temp_filter_carType = this.temp_filter_carType.filter(car => {
          if (car !== 'Any') return true;
        });
        this.temp_filter_carType.push(option);
      }
    } else if (!event && option == "Any") {
      this.temp_filter_carType = [];
    }
    else {
      this.temp_filter_carType = this.temp_filter_carType.filter(car => {
        if (car !== option && car !== 'Any') return true;
      });
    }
    if ((this.temp_filter_carType.indexOf('Any') === -1) && this.temp_filter_carType.length === (this.cars.length - 1)) {
      this.temp_filter_carType.push('Any');
    }
    this.isCarTypeChecked(option);
  }

  isCarTypeChecked(car) {
    return this.temp_filter_carType.indexOf(car) > -1;
  }

  onCarBrandClicked(option, event) {
    if (event) {
      if (option == "Any") {
        this.temp_filter_carBrand.push('Any');
        for (let item of this.preDefinedCarBrands) {
          this.temp_filter_carBrand.push(item.value);
        }
      }
      else {
        this.temp_filter_carBrand = this.temp_filter_carBrand.filter(car => {
          if (car !== 'Any') return true;
        });
        this.temp_filter_carBrand.push(option);
      }
    } else if (!event && option == "Any") {
      this.temp_filter_carBrand = [];
    } else {
      this.temp_filter_carBrand = this.temp_filter_carBrand.filter(car => {
        if (car !== option && car !== 'Any') return true;
      });
    }
    if ((this.temp_filter_carBrand.indexOf('Any') === -1) && this.temp_filter_carBrand.length === (this.preDefinedCarBrands.length - 1)) {
      this.temp_filter_carBrand.push('Any');
    }
    this.isCarBrandChecked(option);
  }

  isCarBrandChecked(car) {
    return this.temp_filter_carBrand.indexOf(car) > -1;
  }

  onCarSpecificationClicked(option, event) {
    if (event) {
      if (option == "Any") {
        this.temp_filter_carSpecification.push('Any');
        for (let item of this.Specification) {
          this.temp_filter_carSpecification.push(item.value);
        }
      } else {
        this.temp_filter_carSpecification = this.temp_filter_carSpecification.filter(car => {
          if (car !== 'Any') return true;
        });
        this.temp_filter_carSpecification.push(option);
      }
    } else if (!event && option == "Any") {
      this.temp_filter_carSpecification = [];
    } else {
      this.temp_filter_carSpecification = this.temp_filter_carSpecification.filter(car => {
        if (car !== option && car !== 'Any') return true;
      });
    }
    if ((this.temp_filter_carSpecification.indexOf('Any') === -1) && this.temp_filter_carSpecification.length === (this.Specification.length - 1)) {
      this.temp_filter_carSpecification.push('Any');
    }
    this.isCarSpecificationChecked(option);
  }

  isCarSpecificationChecked(car) {
    return this.temp_filter_carSpecification.indexOf(car) > -1;
  }
  getLocationText(text) {
    if (['Airport', 'Shuttle'].includes(text)) {
      return 'Airport location';
    } else if (text === 'Railway Station') {
      return 'Train station location'
    } else if (text === 'Port Or Ferry') {
      return 'Port location'
    } else if (text === 'City Location') {
      return 'City location'
    }
  }
  getLocationText1(text) {
    if (text) {
      if (text.toLowerCase().search(' ') !== -1) {
        let text1 = text.split(" ");
        return text1[0];
      } else {
        return text;
      }
    }
  }
  getIconText(text) {
    if (text.toLowerCase().search(',') !== -1) {
      let text1 = text.split(',', 2);
      text = text1[1];
    }
    if (['Airport', 'Shuttle'].includes(text)) {
      return 'fa fa-plane';
    } else if (text === 'Railway Station') {
      return 'fa fa-train'
    } else if (text === 'Port Or Ferry') {
      return 'fa fa-ship'
    } else if (text === 'City Location') {
      return 'fa fa-building'
    }
  }
  onCarLocationTypeClicked(option) {
    this.temp_filter_carLocationType = option;
    this.isCarLocationTypeChecked(option);
  }
  isCarLocationTypeChecked(car) {
    return this.temp_filter_carLocationType === car;
  }
  onCarLocationClicked(option) {

    this.temp_filter_Location = option.value;
    this.temp_filter_Location1 = option.Name;

    this.isCarLocationChecked(option.Name);
  }
  getName(value) {
    var formattedString = value.split(",", 1000);
    return formattedString;
  }
  isCarLocationChecked(car) {
    return this.temp_filter_Location1 === car;
  }
  carArray() {
    let newArray = [];
    let maxToReturn = this.carElementCount;
    if (this.isSeenComplete) {
      maxToReturn = this.carSearchResponse.length;
    }
    for (let i = 0; i < maxToReturn && i < this.carSearchResponse.length; i++) {
      newArray.push(this.carSearchResponse[i]);
    }

    return newArray;
  }
  ngOnDestroy() {
    if (this.queryParmsSubscription) {
      this.queryParmsSubscription.unsubscribe();
    }
    if (!this.callComplete) {
      this.bsModalRef1.hide();
      if (this.quickSearchSubscription) {
        this.quickSearchSubscription.unsubscribe();
      }
    }
   if(this.carSearchResponseSubscription){
    this.carSearchResponseSubscription.unsubscribe();
   }
    this.searchService.networkPopupListener.next(null);
    if (this.connectionListener) {
      this.connectionListener.unsubscribe();
     
    }
  }
  filterResultsAsync() {
    if (!this.carBookingService.originalCarSearchResponse) return;

    let carSearchRes = JSON.parse(JSON.stringify(this.carBookingService.originalCarSearchResponse));
    for (let filter of this.appliedFilterList) {
      if (filter === FilterType.CAR_TYPE) {
        carSearchRes = this.carBookingService.filterByCarType(this.filter_carType, carSearchRes);
      } else if (filter === FilterType.CAR_BRAND) {
        carSearchRes = this.carBookingService.filterByCarBrand(this.filter_carBrand, carSearchRes);
      } else if (filter === FilterType.CAR_SPECIFICATION) {
        carSearchRes = this.carBookingService.filterByCarSpecification(this.filter_carSpecification, carSearchRes);
      }
      else if (filter === FilterType.CAR_NAME) {
        carSearchRes = this.carBookingService.filterCarByName(this.carBookingService.filterByName, carSearchRes);
      }
      else if (filter === FilterType.CAR_LOCATION) {
        carSearchRes = this.carBookingService.filterByCarLocation(this.filter_Location, carSearchRes);
      }
      else if (filter === FilterType.CAR_LOCATION_TYPE) {
        carSearchRes = this.carBookingService.filterByCarLcationType(this.filter_carLocationType, carSearchRes);
      }

    }
    if (carSearchRes.length < this.carElementCount && carSearchRes.length > 0) {
      this.resultLen = false;
    } else {
      this.resultLen = true;
      if (this.isSeenComplete) {
        this.isSeenComplete = false;
      }
    }
    this.carBookingService.applySorting(carSearchRes);
    this.carBookingService.carAppliedFilters = this.appliedFilterList;
  }

  applyCarTypeFilter() {
    let params;
    if (this.temp_filter_carType.length == 0) {
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Type&ua_type=Any";
      this.appliedFilterList = this.appliedFilterList.filter(filter => {
        if (filter.toString() !== FilterType.CAR_TYPE.toString()) return true;
      });
    } else if (this.appliedFilterList.indexOf(FilterType.CAR_TYPE) == -1) {
      this.appliedFilterList.push(FilterType.CAR_TYPE);
    }
    if (this.temp_filter_carType.length > 0 && (this.temp_filter_carType.indexOf('Any') !== -1)) {
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Type&ua_type=Any";
    } else if (this.temp_filter_carType.length > 0) {
      var filteredArray = Array.from(new Set(this.temp_filter_carType));
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Type&ua_type=" + filteredArray;
    }
    this.searchService.letsTrack(params);
    this.filter_carType = this.temp_filter_carType;

    this.filterResults();
  }
  applyCarSpecificationFilter() {
    let params;
    if (this.temp_filter_carSpecification.length == 0) {
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Specification&ua_specification=Any";
      this.appliedFilterList = this.appliedFilterList.filter(filter => {
        if (filter.toString() !== FilterType.CAR_SPECIFICATION.toString()) return true;
      });
    } else if (this.appliedFilterList.indexOf(FilterType.CAR_SPECIFICATION) == -1) {
      this.appliedFilterList.push(FilterType.CAR_SPECIFICATION);
    }
    if (this.temp_filter_carSpecification.length > 0 && (this.temp_filter_carSpecification.indexOf('Any') !== -1)) {
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Specification&ua_specification=Any";
    } else if (this.temp_filter_carSpecification.length > 0) {
      var filteredArray = Array.from(new Set(this.temp_filter_carSpecification));
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Specification&ua_specification=" + filteredArray;
    }
    this.searchService.letsTrack(params);
    this.filter_carSpecification = this.temp_filter_carSpecification;

    this.filterResults();
  }
  applyCarLocationTypeFilter() {
    let params;
    if (this.temp_filter_carLocationType.length == 0) {
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Specification&ua_specification=Any";
      this.appliedFilterList = this.appliedFilterList.filter(filter => {
        if (filter.toString() !== FilterType.CAR_LOCATION_TYPE.toString()) return true;
      });
    } else if (this.appliedFilterList.indexOf(FilterType.CAR_LOCATION_TYPE) == -1) {
      this.appliedFilterList.push(FilterType.CAR_LOCATION_TYPE);
    }
    if (this.temp_filter_carLocationType.length > 0 && (this.temp_filter_carLocationType.indexOf('Any') !== -1)) {
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Specification&ua_specification=Any";
    } else if (this.temp_filter_carLocationType.length > 0) {
      var filteredArray = Array.from(new Set(this.temp_filter_carLocationType));
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Specification&ua_specification=" + filteredArray;
    }
    this.searchService.letsTrack(params);
    this.filter_carLocationType = this.temp_filter_carLocationType;

    this.filterResults();
  }
  applyCarLocationFilter() {
    let params;
    if (this.temp_filter_Location.length == 0) {
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Location&ua_location=Any";
      this.appliedFilterList = this.appliedFilterList.filter(filter => {
        if (filter.toString() !== FilterType.CAR_LOCATION.toString()) return true;
      });
    } else if (this.appliedFilterList.indexOf(FilterType.CAR_LOCATION) == -1) {
      this.appliedFilterList.push(FilterType.CAR_LOCATION);
    }
    if (this.temp_filter_Location.length > 0 && this.temp_filter_Location == 'All') {
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Location&ua_location=Any";
    } else if (this.temp_filter_Location.length > 0) {
      var filteredArray = this.temp_filter_Location;
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Location&ua_location=" + filteredArray;
    }
    this.searchService.letsTrack(params);
    this.filter_Location = this.temp_filter_Location;

    this.filterResults();
  }
  applyCarBrandFilter() {
    let params;
    if (this.temp_filter_carBrand.length == 0) {
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Brand&ua_brand=Any";
      this.appliedFilterList = this.appliedFilterList.filter(filter => {
        if (filter.toString() !== FilterType.CAR_BRAND.toString()) return true;
      });
    } else if (this.appliedFilterList.indexOf(FilterType.CAR_BRAND) == -1) {
      this.appliedFilterList.push(FilterType.CAR_BRAND);
    }
    if (this.temp_filter_carBrand.length > 0 && (this.temp_filter_carBrand.indexOf('Any') !== -1)) {
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Brand&ua_brand=Any";
    } else if (this.temp_filter_carBrand.length > 0) {
      var filteredArray = Array.from(new Set(this.temp_filter_carBrand));
      params = "?ua_action=CarResultFilterItem&ua_filter=Car Brand&ua_brand=" + filteredArray;
    }
    this.searchService.letsTrack(params);
    this.filter_carBrand = this.temp_filter_carBrand;

    this.filterResults();
  }
  filterResults() {
    setTimeout(() => {
      this.filterResultsAsync();
    }, 200);
  }
  checkAndRemoveFailedCarRates(carSearchResponse){
    if(!carSearchResponse || carSearchResponse == null || carSearchResponse == undefined || carSearchResponse.length == 0){
      return;
    }
    
    const failedRates = JSON.parse(this.gallopLocalStorage.getItem('failedCarRates'));
    if(!failedRates || failedRates === null || failedRates === undefined || (failedRates && failedRates.length <= 0)) {
      return ;
    }
    for(let i = 0 ; i<failedRates.length; i++){
      let failedCarResponse =  carSearchResponse.filter((car)=>{
        return car.carClass === failedRates[i].carClass 
        &&  car.carType === failedRates[i].carType
        && car.carMake === failedRates[i].carMake
        && car.doors === failedRates[i].doors
        && car.traflaPartnerCode === failedRates[i].traflaPartnerCode
      })
      if(failedCarResponse && failedCarResponse.length != 0){
        let index = carSearchResponse.indexOf(failedCarResponse[0]);
        if(index  != -1){
          this.carSearchResponse.splice(index,1);
        }
      }
    }
    
  }

}
