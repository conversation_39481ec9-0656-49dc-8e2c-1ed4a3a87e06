import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CarResultRoutingModule } from './car-result.routing.module';
import { CarResultComponent } from './car-result.component';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CarViewComponent } from '../car-view/car-view.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ShareModule } from '../share.module';
import { NgSelectModule } from '@ng-select/ng-select';



@NgModule({
  imports: [
    CommonModule,
    CarResultRoutingModule,
    NgbModule,
    ShareModule,
    NgxSmartModalModule,
    NgSelectModule,
    ReactiveFormsModule,
    FormsModule,
  ],
  declarations: [
    CarResultComponent,
    CarViewComponent,

  ],
})
export class CarResultModule {

}