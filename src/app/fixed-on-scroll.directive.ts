import { Directive, ElementRef, HostListener, Renderer2, Inject } from '@angular/core';

@Directive({
    selector: '[appFixedOnScroll]',
    standalone: false
})
export class FixedOnScrollDirective {

  element: ElementRef;
  renderer: Renderer2;

  constructor(el: ElementRef, private ren: Renderer2) {
    this.element = el;
    this.renderer = ren;
  }

  @HostListener('window:scroll') onMouseEnter() {
    if (this.element.nativeElement.getBoundingClientRect().top < 10) {
      this.renderer.addClass(this.element.nativeElement, 'fixed-top');
    }
    if (window.pageYOffset < 10) {
      this.renderer.removeClass(this.element.nativeElement, 'fixed-top');
    }
  }

}
