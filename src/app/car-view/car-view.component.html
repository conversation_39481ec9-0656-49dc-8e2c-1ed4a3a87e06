<div class="summary-view">
    <div class="container">
        <div class="summary-view-inner">
            <div class="source-dest">
                <div class="source-dest-place" [attr.translate]="'no'">{{carSearchDetails.pickUp}} </div>
                <div *ngIf="showDropOff()" class="switch2">
                    <span class="icon-arrow"></span>
                </div>
                <div *ngIf="showDropOff()" class="source-dest-place" [attr.translate]="'no'">{{carSearchDetails.dropOff}} </div>
            </div>
            <div class="summary-view-middle">
                <div style="width:300px !important;"> {{'carview.Pick-update' | translate }}: {{(carSearchDetails.pickUpDate | date:'EEE MMM d')}}
                    &#64; {{carSearchDetails.pickTime}}
                </div>
                <div class="summary-view-middle">
                    <div style="width:300px !important;"> {{'carview.Drop-offdate' | translate }}: {{(carSearchDetails.dropOffDate| date:'EEE MMM
                        d')}} &#64; {{carSearchDetails.dropTime}}
                    </div>
                </div>
            </div>
            <div class="edit-summery-view">
                <span class="icon-edit" attr.data-track="EditCarSearch" attr.data-params="page=CarResults"
                    (click)="backToSearch()"></span>
            </div>
        </div>
    </div>
</div>