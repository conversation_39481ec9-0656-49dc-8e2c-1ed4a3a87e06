@import "../../variables.scss";
$color-overlay: rgba(0, 0, 0, .7);
$dialog-position-top: 20%;

//@import "ngx-smart-modal/ngx-smart-modal";

.airport-search-dropdown {
    opacity: 0.5;
    padding-left: 5px;
}

.checkbox {
    width: 15px;
    cursor: pointer;
    position: absolute;
    border: 0.5px solid;
    top: 4px;
    left: 10px;
    height: 15px;
    background-image: url(/../../assets/images/check-icon.png);
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center center;
}

.checkbox1 {
    background-color: #fff !important;
    width: 15px;
    height: 15px;
    cursor: pointer;
    position: absolute;
    border: 0.5px solid;
    left: 10px;
    top: 4px;
}

.name {
    font-size: 21px !important;
    color: #2c2D3c !important;
    margin-bottom: 10px !important;
}

.box {
    height: 1px;
    width: 3px;
    border: none;
    text-align: center;
    background-color: #fff;
    color: #fff;
}

.name2 {
    font-size: 17px !important;
    margin-bottom: 15px !important;
}

.checkboxLabel {
    color: #AEAEAE !important;
    font-size: 14px;
    white-space: nowrap;
}

.hotel-search-input-container .x {
    position: absolute;
    right: 10px;
    margin: auto;
    cursor: pointer;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    background: #f7f7f7 !important;
}

.hotel-search-input-container .x i {
    font-size: 28px;
    vertical-align: middle;
}

.shadow {
    padding: 10px 0;
    text-align: center !important;
    background: #fff;
    margin-top: 20px !important;
    border-radius: 6px;
    box-shadow: 0 0 20px 0 #EDFAFC;
    padding-right: 10px;
    padding-left: 10px;
}

@media(max-width:767px) {
    .shadow {
        background: #fff;
        box-shadow: 0 0 20px 0 #EDFAFC;
        padding-right: 0px;
        padding-left: 0px;
    }
}

@media(max-width:414px) {
    .shadow {
        background: #fff;
        box-shadow: 0 0 20px 0 #EDFAFC;
    }
}

@media(max-width:375px) {
    .shadow {
        background: #fff;
        box-shadow: 0 0 20px 0 #EDFAFC;
        padding-right: 0px;
        padding-left: 0px;
    }
}

@media(max-width:367px) {
    .shadow {
        background: #fff;
        box-shadow: 0 0 20px 0 #EDFAFC;
        padding-right: 0px;
        padding-left: 0px;
    }
}

@media(max-width:320px) {
    .shadow {
        background: #fff;
        box-shadow: 0 0 20px 0 #EDFAFC;
        padding-right: 0px;
        padding-left: 0px;
    }
}

.button-normal {
    width: 80px;
    height: 80px;
    background-color: transparent !important;
    color: #fff;
    border: none;
    border-radius: 6px;
    text-align: center;
    font-weight: normal !important;
}

.view {
    text-align: center;
    font-weight: normal !important;
    color: #000000;
    font-size: 12px;
    font-family: "apercu-r";
    letter-spacing: 1px;
    text-transform: uppercase;
}

.search-wrapper {
    background-color: $background-dark;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    padding: 32px 0;
    border-radius: 0;
    float: left;
    width: 100%;

    &.result {
        background-color: #fff;
    }
}

::-webkit-scrollbar {
    width: 0px;
    background: transparent;
    /* make scrollbar transparent */
}

.date-input {
    margin: 0px;
}

.controls {
    font-size: 1rem;
    margin: 14px 0 34px;
}

.control {
    display: flex;
    margin-right: 30px;

    &.selected {
        .value {
            color: $primary-text-color;
        }
    }

    .value {
        margin-left: 10px;
        color: $secondary-text-color;
        cursor: pointer;
    }

    .select-input .value {
        margin-left: 0;
    }
}

.control-icon {
    font-size: 4px;
    margin-left: 10px;
    color: $link-color;
}

.primary-button {
    padding: 19px 35px;
}

.icon-right-arrow {
    font-size: 1rem;
}

.add-more,
.remove {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background-color: #fff;
    color: $link-color;
    font-size: 14px;
    cursor: pointer;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    border: none;

    &[disabled] {
        opacity: 0.6;
        cursor: not-allowed;
    }

    span {
        font-size: 14px;
        margin-right: 5px;
    }
}

.add-more {
    width: 70px;
    height: 60px;
}

.switch2 {
    position: absolute;
    height: 35px;
    width: 35px;
    border-radius: 50%;
    right: -17.5px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 16px;
    z-index: 13;
    border: 2px solid #E7E6E4;
}

.switch1 {
    position: absolute;
    height: 35px;
    width: 35px;
    border-radius: 50%;
    right: -17.5px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 16px;
    z-index: 11;
    border: 2px solid #E7E6E4;
}

.text-input .icon {
    left: 30px;
}

.text-input .icon+input {
    padding-left: 66px;
}

.text-input input {
    padding-left: 36px;
    letter-spacing: 1px;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
}

.type-selector {
    margin-bottom: 16px;

    .radio-input {
        margin-right: 40px;
    }

    .radio-input [type="radio"]:checked+label {
        color: $accent-color;
    }
}

.remove {
    width: 60px;
    height: 50px;
}

.search-button {
    margin: 0 auto;
    padding-right: 74px;
}

.collapse-control-container {
    height: 2px;
    background: $border-color;
    position: relative;
    top: 0;
    margin-top: 24px;

    .collapse-control {
        position: absolute;
        right: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 1px solid $link-color;
        top: -15px;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
        background: #fff;
        cursor: pointer;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
    }
}

.icon-search-from,
.icon-search-destination,
.icon-arrow,
.icon-up,
.icon-down {
    color: $icon-color;
}

.filter-container {
    /*padding: 32px 0 0;*/
    float: left;
    width: 100%;
}

.option-title {
    margin-right: 48px;
}

.primary-button {
    letter-spacing: 2px;
}

.server-side-filters {
    margin-bottom: 11px;

    .filter {
        margin-right: 24px;
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .field-value {
        color: $secondary-text-color;
    }
}

.icon-customer {
    color: $secondary-text-color;
    font-size: 11px;
    margin-right: 8px;
}

.fix-align {
    margin-right: 74px;
}

.search-button-container {
    display: flex;
    margin-bottom: 20px;

}

/*new scss for new design*/
.search-wrapper {
    box-shadow: none !important;
    background: transparent !important;
}

.top-strip {
    background: $themeColor2;
    float: left;
    width: 100%;
    height: 43px;
    margin-top: 5px;
}

.tab-content {
    float: left;
    width: 100%;
    background-color: #FFFFFF;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    padding: 0 20px;
}

.tab-list-item {
    display: inline-block;
    margin-right: 60px;
    margin-bottom: 0;
    padding-bottom: 0;
    padding-top: 5px;
}

.tab-list-item a {
    cursor: pointer;
    position: relative;
    display: inline-block;
    margin-left: 20px;
    color: #FFFFFF;
    font-size: 12px;
    opacity: 0.6;
    line-height: 15px;
    padding: 10px 5px 13px 5px;
}

.tab-list-item a img {
    margin-right: 10px;
}

.tab-list-item.active a {
    opacity: 1;
}

.tab-list-item.active a::after {
    position: absolute;
    content: "";
    bottom: 2px;
    background: #fff;
    left: 0;
    right: 0;
    width: auto;
    height: 3px;
}

.currentLocation {
    cursor: pointer;
    padding-top: 7px;
    border: 1px solid #e4e4e4;
    background: #fff !important;
    height: 35px;
    font-size: 14px;
    letter-spacing: 0.42px;
    line-height: 17px;
    position: absolute;
    padding-left: 15px;
    word-break: break-word;
    z-index: 12;
    width: 100%;
}

.locate {
    position: absolute;
    /* float: left; */
    left: 0;
    padding-top: 20px;
    padding-left: 20px;
}

.select-header {
    background: $themeColor2;
}

.text-input input {
    position: static;
}

.custom-selectbox {
    cursor: pointer;
    position: relative;
    display: inline-block;
    padding-right: 15px;
    margin-right: 5px;
}

.custom-selectbox .field-value {
    color: #AEAEAE;
}

.custom-selectbox .control-icon {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.filter-row {
    display: flex;
    justify-content: space-between;
}

.filter-row-right {
    display: flex;
}

.input-icon {
    position: absolute;
    left: 13px;
    z-index: 9;
    height: 100%;
    display: flex;
    align-items: center;
    color: #adadad;
}

.textfield-with-icon {
    padding-left: 47px !important;
}

.text-input input {
    border: 1px solid #e4e4e4;
    background: #f7f7f7 !important;
    height: 65px;
    font-size: 14px;
    letter-spacing: 0.42px;
    line-height: 17px;
}

.search-wrapper .row {
    margin-left: -5px !important;
    margin-right: -5px !important;
}

.search-wrapper [class^="col-"],
.search-wrapper [class^=" col-"] {
    padding-right: 5px;
    padding-left: 5px;
}

.searchButton button {
    height: 65px !important;
}

.constraint-time,
.time-type {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    margin-top: 9px;
}

.custom-selectbox-icon {
    width: 10px;
}

/*.time-type{margin-top: 9px;}*/


.switch-toggle-label {
    outline: none !important;
    cursor: pointer;
}

.class-toggle-switch {
    width: 212px;
    display: inline-block;
}

.switch-toggle.switch-candy,
.switch-light.switch-candy>span {
    color: #AEAEAE;
    border: 1px solid #E4E4E4;
    border-radius: 7px;
    background-color: #F7F7F7;
    box-shadow: none
}

.switch-light.switch-candy span span,
.switch-light.switch-candy input:checked~span span:first-child,
.switch-toggle.switch-candy label {
    color: #AEAEAE;
    font-size: 14px;
    line-height: 24px;
    text-shadow: none;
    height: 24px;
    font-weight: normal;
}

.switch-light.switch-candy input~span span:first-child,
.switch-light.switch-candy input:checked~span span:nth-child(2),
.switch-candy input:checked+label {
    border: none;
    border-radius: 6px;
    background-color: #transparent;
    color: #fff;
    text-shadow: none;
}

.switch-candy a {
    background-color: #aeaeae;
    border: 1px solid #E4E4E4;
    box-shadow: none;
    border-radius: 6px;
    background-image: none;
}

.switch-toggle a {
    position: absolute;
    top: 0;
    left: 0;
    padding: 0;
    z-index: 1;
    width: 50%;
    height: 100%;
    border: none;
}

.summary-view {
    float: left;
    width: 100%;
    background: #fff;
}

.summary-view-inner {
    float: left;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
}

.source-dest {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    letter-spacing: 0.42px;
    line-height: 17px;
    color: #000000;

    .source-dest-place {
        max-width: 150px;
    }

    .switch {
        position: static;
        margin: 0 50px;
    }

    .switch2 {
        position: static;
        margin: 0 50px;
    }

}

.widthAdjust {
    width: 32px !important;
    display: inline-flex;
}

.summary-view-middle {
    display: inline-flex;
    flex-direction: column;
    font-size: 12px;
    line-height: 15px;
    color: #AEAEAE;
}

.summary-view-middle .date {
    letter-spacing: -0.6px;
    margin-bottom: 5px;
}

.class-passenger {
    display: flex;
}

.edit-summery-view {
    display: inline-flex;
}

.edit-summery-view .icon-edit {
    background: #fff;
    height: 32px;
    width: 32px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    color: var(--secondarybutton-font-color);
    cursor: pointer;
}

.summery-class {
    margin-right: 20px;
}

.no-flight-found-container {
    float: left;
    width: 100%;
}

.summery-view-container {
    float: left;
    width: 100%;
    background: #fff;
}

.tab-content-item {
    float: left;
    width: 100%;
}

/*.tab-content-item.active{display: block;}*/
.selected-flight-container {
    float: left;
    width: 100%;
    padding: 5px 20px 13px;
}


.hotel-search-form {
    float: left;
    width: 100%;
}

.hotel-filters-left {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 32px;
    margin-bottom: 16px;
    padding-left: 21px;
    padding-right: 14px;
}

.hotel-filters-right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 32px;
    margin-bottom: 16px;
    padding-left: 0;
    padding-right: 4px;
}

.hotel-label {
    color: #AEAEAE;
    font-size: 14px;
    line-height: 17px;
}

.hotel-check-label {
    color: #AEAEAE;
    font-size: 14px;
    line-height: 17px;
    padding-top: 13px;
    padding-left: 10px;
    padding-right: 10px;
}

.container-icon {
    margin-right: 8px;
    display: inline-block;
    vertical-align: top;
    margin-top: -1px;
}

.mobile-vs {
    float: left;
    width: 100%;
}

.mobile-vs .searchButton {
    margin-top: 0;
}

.filter-select-container {
    position: relative;
}

.hotel-filters-right-section1 {
    display: flex;
}

.room-container {
    margin-right: 40px;
}

.filter-select-container .custom-selectbox-value {
    max-width: 120px;
    text-overflow: ellipsis;
    overflow: hidden;
}

.constraint-time .control {
    margin-right: 0 !important;
    display: inline-block !important;
}

.filter-modal {
    width: 100%;
    float: left;
    right: 0px;
    position: absolute;
    z-index: 1000;
    background: #fff;
    border-radius: 8px;
}

.leizure {
    font-size: 14px;
    font-weight: bold;
    padding-left: 15px !important;
    margin-right: 22px;
    text-align: right;
}

.text-danger {
    font-size: 12px;
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.leizure2 {
    font-size: 13px;
    padding-left: 15px !important;
    margin-right: 22px;
    text-align: right;
    color: gray;
}

.leizure3 {
    font-size: 16px;
    font-weight: bold;
    position: absolute;
    margin-top: 12px;
    right: 94px;
}

.leizure1 {
    font-weight: bold;
    text-align: left;
    padding-right: 15px;
}

.header-inner {
    height: 55px;
    margin-bottom: 20px;
    padding-bottom: 0px;
    padding-top: 0px;
}

.btn-default {
    padding: 4px 16px;
    border-radius: 4px;
    color: #808080;
    background: #f7f7f7;
    font-size: 25px;
    margin-right: 7px;
    box-shadow: none;
    border: 1px solid #eeeeee;
    border-radius: 4px;
    background-color: #e7e7e7;
}

.box {
    height: 18px;
    width: 30px;
    border: none;
    text-align: center;
    background-color: #fff;
    color: #fff;
}

.btn-default1 {
    padding: 4px 16px;
    color: var(--button-font-color);
    font-size: 25px;
    margin-left: 7px;
    box-shadow: none;
    border: 1px solid #eeeeee;
    border-radius: 4px;
    background-color: #EDFAFC;
}

.modal-footer {
    display: block !important;
    align-items: center;
    background-color: #EDFAFC;
    height: 88px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, .14)
}

.primary1-button {
    width: 50%;
    height: 46px;
    margin-bottom: 6px;
    margin-top: 20px;
    padding-top: 12px;
    background-color: var(--button-bg-color) !important;
    color: var(--button-font-color);
    font-size: 16px;
}

.modal-title {
    text-align: left;
    font-family: $fontMono;
}

.modal-body {
    margin-top: 0px;
    padding-bottom: 0px;
}

.modalAirportFilterInfo {
    min-width: 324px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modalAirportFilterInfo .modal-content {
    border: none;
    border-radius: 6px;
}

.modalAirportFilterInfo .close {
    text-shadow: none;
    color: #fff;
    opacity: 1;
}

.modalAirportFilterInfo .modal-header {
    background-color: var(--hyperlink-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px !important;
    border-bottom: none;
}

.modalAirportFilterInfo .modal-header h5 {
    font-size: 14px;
    width: 100%;
}

.modalAirportFilterInfo .modal-footer {
    padding: 0;
    border-top: none;
    float: left;
    width: 100%;
}

.modalAirportFilterInfo .close i {
    font-size: 17px;
}

.modalAirportFilterInfo .close:hover {
    color: #fff !important;
    opacity: 1 !important;
}

@media (max-width: 1200px) {
    .text-input {
        margin-bottom: 18px;
    }

    .searchButton {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .constraint-time,
    .time-type {
        margin-top: 0;
    }

    /*.time-type{margin-top: 0;}*/
    .hotel-check-label {
        padding-top: 0;
    }

    .hotal-filters-row {
        flex-direction: column-reverse;
    }

}



@media (max-width: 991px) {
    .summary-view-inner {
        flex-wrap: wrap;
    }

    .source-dest {
        float: left;
        width: 100%;
        margin-bottom: 20px;
    }

    .summary-view-middle {
        float: left;
        width: 80%;
    }

    .edit-summery-view {
        float: left;
        width: 20%;
        justify-content: flex-end;
    }
}


// For Tablet
@media (max-width: 767.98px) {
    .custom-selectbox {
        padding-right: 10px;
    }

    .switch2 {
        top: 2px;
    }

    .locate {
        position: absolute;
        /* float: left; */
        left: 0;
        padding-top: 10px !important;
        padding-left: 20px;
    }

    .textfield-with-icon {
        padding-left: 40px !important;
        padding-right: 2px;
    }

    .search-wrapper {
        padding: 16px 0 30px 0 !important;
    }

    .text-input {
        margin-bottom: 11px;
    }

    .text-input input {
        height: 41px;
        font-size: 12px;
        letter-spacing: normal;
        line-height: 15px;
        padding-top: 0;
        padding-bottom: 0;
    }

    .switch {
        height: 28px;
        width: 28px;
        right: -13px;
        top: 7px;
    }

    .switch .icon-arrow {
        font-size: 8px;
    }

    .switch1 .icon-arrow {
        font-size: 8px;
    }

    .switch1 {
        height: 28px;
        width: 28px;
        right: -13px;
        top: 7px;
    }

    .switch2 .icon-arrow {
        font-size: 8px;
    }

    .switch2 {
        height: 28px;
        width: 28px;
        right: -13px;
        top: 7px;
    }

    .search-button {
        margin: 0;
    }

    .separator {
        border-bottom: 2px solid $border-dark-color;
        margin-bottom: 24px;
    }

    .search-container {
        padding: 0;
    }

    .search-wrapper .row {
        margin-left: -3px !important;
        margin-right: -3px !important;
    }

    .search-wrapper [class^="col-"],
    .search-wrapper [class^=" col-"] {
        padding-right: 3px;
        padding-left: 3px;
    }

    .custom-selectbox-value {
        font-size: 12px !important;
    }

    .searchButton button {
        height: 46px !important;
        line-height: normal;
        padding-top: 0;
        padding-bottom: 0;
    }

    .switch-div {
        width: 100%;
        text-align: center;
        position: fixed;
        top: 420px;
        max-width: none;
        min-width: 1px;
        left: 0;
    }

    .tab-list-item {
        max-width: 50%;
        display: inline-block;
        text-align: center;
        margin-right: 30px;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .server-side-filters {
        margin-bottom: 25px;

        .filter {
            margin-right: 12px;
            padding-right: 15px;
        }
    }

    .class-toggle-switch {
        margin-top: 15px;
    }

    .summary-view-inner {
        flex-wrap: wrap;
        padding: 20px 20px;
    }

    .source-dest {
        float: left;
        width: 100%;
        margin-bottom: 13px;
        justify-content: space-between;

        .source-dest-place {
            max-width: none;
            width: calc(50% - 26px);
            display: inline-block;
            vertical-align: middle;
        }

        .switch {
            position: static;
            margin: 0 12px;
            display: inline-flex;
            vertical-align: middle;
        }
    }

    .summary-view-middle {
        float: left;
        width: 80%;
    }

    .edit-summery-view {
        float: left;
        width: 20%;
        justify-content: flex-end;
    }

    .source-dest {
        font-size: 12px;
        line-height: 15px;
    }

    .selected-flight-container {
        padding: 0 8px;
    }

    .hotel-check-label {
        display: none;
    }

    /*.hotel-label{display: none;}*/
    .hotel-search-form {
        display: flex;
        flex-direction: column-reverse;
    }

    .hotel-filters-right-section1 {
        width: 50%;
    }

    .hotel-filters-right-section2 {
        width: 50%;
    }

    /*.hotal-filters{ margin-top: 0px;}*/

    .room-container {
        margin-right: 15px;
    }

    .hotel-filters-right-section2 {
        padding-left: 10px;
    }

    .tab-list-item a {
        margin-left: 5px;
    }

    .primary1-button {
        width: 40% !important;
        height: auto;
        margin-bottom: 5px;
        padding-bottom: 5px;
    }

    .filter-modal {
        right: -15px;
    }

    .header-inner {
        height: 70px;
        padding-bottom: 0px;
        padding-top: 0px;
        margin-bottom: 7px;
    }

    .leizure {
        padding-left: 15px !important;
    }

    .modal-body {
        margin-top: 0px;
        padding-bottom: 0px;
    }

    .modal-title {
        text-align: left;
        font-family: $fontMono;
    }
}

// For Mobile
@media (max-width: 575.98px) {

    .search-wrapper,
    .no-flight-found-container {
        padding: 16px 16px;
        /*margin: 0 8px;*/
    }

    .date-input {
        /*margin: 0 4px 0 2px;*/
    }

    .select-input {
        margin: 0;

    }

    .text-input .icon {
        left: 14px;
    }

    .text-input .icon+input {
        padding-left: 46px;
    }

    .text-input input {
        padding-left: 16px;
    }

    .primary-button {
        padding: 14px 35px;
        letter-spacing: 2px;
        width: 100%;
    }

    .add-more {
        width: 100%;
        margin-bottom: 24px;
    }
}

@media (max-width:400px) {
    .primary1-button {
        width: 40% !important;
        height: auto;
        margin-bottom: 5px;
    }

    .filter-modal {
        right: -15px;
    }

    .modal-body {
        margin-top: 0px;
        padding-bottom: 0px;
    }

    .header-inner {
        height: 70px;
        padding-bottom: 0px;
        padding-top: 0px;
        margin-bottom: 7px;
    }

    .leizure {
        padding-left: 15px !important;
    }

    .modal-title {
        text-align: left;
        font-family: $fontMono;
    }
}


@media (max-width: 450px) {
    .switch-div {
        top: 440px;
    }

    .switch1-div {
        top: 440px;
    }

    .switch2-div {
        top: 440px;
    }

    .currentLocation {
        cursor: pointer;
        padding-top: 7px;
        border: 1px solid #e4e4e4;
        background: #fff !important;
        height: 30px;
        font-size: 12px;
        letter-spacing: 0.42px;
        line-height: 17px;
        position: absolute;
        padding-left: 15px;
        word-break: break-word;
        z-index: 12;
        width: 100%;
    }

}

@media (max-width:320px) {
    .filter-modal {
        right: -20px !important;
    }

    .modalAirportFilterInfo {
        min-width: 300px !important;
    }
}