import { Component, OnInit } from '@angular/core';
import { CarBookingService } from '../car-booking.service';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { Title } from '@angular/platform-browser';
import { SearchService } from '../search.service';
import { TranslateService } from '@ngx-translate/core';
import { UserAccountService } from '../user-account.service';

@Component({
    selector: 'app-car-view',
    templateUrl: './car-view.component.html',
    styleUrls: ['./car-view.component.scss'],
    standalone: false
})
export class CarViewComponent implements OnInit {
  carSearchDetails: any;
  searchRequestSubscription: Subscription;
  constructor(private carBookingService: CarBookingService,
    public translateService: TranslateService,
    private titleService: Title,
    private searchService: SearchService,
    private userAccountService: UserAccountService,
    private router: Router,) { }

  ngOnInit() {
    this.searchRequestSubscription = this.carBookingService.carRequest$.subscribe(request => {
      if (request) {
        this.carSearchDetails = this.carBookingService.getSearchRequest(request);
      }
    });
  }
  showDropOff() {
    if (this.carSearchDetails.pickUp === this.carSearchDetails.dropOff || this.carSearchDetails.dropOff === '') {
      return false;
    } else {
      return true;
    }
  }
  backToSearch() {
   // this.titleService.setTitle('Car Search');
    this.titleService.setTitle(this.translateService.instant('search.CarSearch'));
    this.searchService.searchHeaderCliked = true;
    this.router.navigate([this.userAccountService.getDefaultRoutePath()],
      {
        queryParams:
        {
          type: 'car',
        },
        replaceUrl: true
      }
    );
  }
}
