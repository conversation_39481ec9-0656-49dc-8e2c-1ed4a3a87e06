import { Route, PreloadingStrategy } from '@angular/router';
import { Observable, of } from 'rxjs';
import { Injectable } from "@angular/core";

@Injectable()
export class CustomPreloadingStrategy implements PreloadingStrategy {
  preload(route: Route, load: () => Observable<any>): Observable<any> {
    const shouldPreload = route.data && route.data['preload'];
    return shouldPreload ? load() : of(null);
  }
}