import { Component, OnInit, ElementRef, HostListener, ViewChild, ChangeDetectorRef } from '@angular/core';
import { UntypedFormGroup, Validators, UntypedFormBuilder } from '@angular/forms';
import { SearchService } from '../search.service';
import { AdminPanelService } from '../admin-panel.service';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { DateUtils } from '../util/date-utils';
import { IntervalType } from '../enum/interval.type';
import { Constants } from '../util/constants';
import { Subscription, Subject } from 'rxjs';
import { UserAccountService } from '../user-account.service';
import { GallopAnalyticsUtil } from '../analytics.service';
import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { DeleteCardModelComponent } from '../email-booking-flow/delete-card-model/delete-card-model.component';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { CommonUtils } from '../util/common-utils';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { DeviceDetailsService } from '../device-details.service';
import { threadId } from 'worker_threads';
import { DatePipe } from '@angular/common';
import { Address } from 'ngx-google-places-autocomplete/objects/address';
declare var getCurrentlyOpenNgxSmartModalIds: any;
declare var setNgxSmartModalOpenStateClosed:any;
@Component({
    selector: 'app-event',
    templateUrl: './event.component.html',
    styleUrls: ['./event.component.scss'],
    standalone: false
})
export class EventComponent implements OnInit {
eventCreateForm:UntypedFormGroup;
fullEventCreateForm :UntypedFormGroup;
@ViewChild('addressInput',{ static: false }) input!: ElementRef<HTMLInputElement>;
  autocomplete!: google.maps.places.Autocomplete;
bsModalRef:BsModalRef
minimumDate1: Date = new Date();
minimumDate6: Date = new Date();
minimumDate2: Date = new Date();
minimumDate4: Date = new Date();
minimumDate3: Date = new Date();
minimumDate5: Date = new Date();
maximumDate: Date = new Date();
viewMode2='tab22';
viewSubMode2='tab11';
deletionProgress=false;
addingPolicy=false;
showStartingLoader=false;
addDeleteProgress=false;
invitationProgress=false;
selectMultipleAttendeeList=[];
search = 'Type to search';
changingValue: Subject<any> = new Subject();
timeOptions: any[] =Constants.CONSTRAINT_TIME_OPTIONS_FOR_EVENT;
selectedAirports=[];
updateDeptButtonClicked=false;
policyOptions = [{ value: 'Select', id: '' }];
selectedEvent=-1;
employeeList=[];
originalEmployeelist=[];
addingEmployee =false;;
employeeLocation=[];
selectedEmployeeList=[];
originalSelectedEmployeeList=[];
policyCheck=false;
dropDownopen=false;
cardValue:any;
paymentCheck=false;
approverCheck=false;
njoySpecificBuild: boolean;
addCardMode=false;
appproverList =[];
airports=[];
multipleSelectedApprover=[];
addEventProgress=false;
departureTimeCheck=false;
arrivalTimeCheck=false;
selectedCardIndex=0;
companySettings:any;
card_list=[];
eventList=[];
eventBookingList =[];
autoCompleteOptions = {
  bounds: undefined,
  fields: ['formatted_address', 'place_id', 'types', 'address_components', 'opening_hours','utc_offset_minutes'],
  strictBounds: false,
  types: undefined,
  origin: undefined,
  componentRestrictions: undefined
};
companySettingsSubscription: Subscription;
listEmployeesResponseSubscription: Subscription;
  constructor(public userAccountInfoService: UserAccountService,
    private fb: UntypedFormBuilder,
    private modalService:BsModalService,
    public  searchService: SearchService,
    public ngxSmartModalService: NgxSmartModalService,
    public router: Router,
    private cdRef: ChangeDetectorRef,
    private ngxAnaltics:GoogleAnalyticsService,
    public deviceDetailsService: DeviceDetailsService,
    public adminPanelService: AdminPanelService,
    public translateService: TranslateService,
    private toastr: ToastrService,
    private el: ElementRef,
  ) {

   }
   isMobile1 = false;
   deviceSubscription1:Subscription;
   @HostListener('document:visibilitychange', ['$event'])
   visibilitychange() {
     if (document.visibilityState === 'visible') {
     this.updatePolicy();
     }
   }
  ngOnInit(): void {
    this.deviceSubscription1 = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
    this.njoySpecificBuild = this.userAccountInfoService.isItNjoyBuild();
    this.eventCreateForm = this.fb.group({
      groupTravelEventName: [''],
      location : ['', Validators.compose([Validators.required])],
      start : [null,  Validators.compose([Validators.required])],
      end: [null, Validators.compose([Validators.required]) ],
    });
   
    this.subscription();
  }
  getCityFromAddress(address: Address): string | null {
    for (const component of address.address_components) {
      if (component.types.includes('locality')) {
        return component.long_name; // or component.short_name depending on your need
      }
    }
    return null;
  }
  currentLocation:any;
  eventLocationChange(event,item){
  
  this.currentLocation = this.getCityFromAddress(event);
  this.eventCreateForm.controls['location'].setValue(item);
  }
  updatePolicy(){
    this.addingPolicy=true;
    this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  openNgxModal(id) {
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);
  }
  getCardName(cardValue) {
    if(cardValue){
    let findIndex = this.card_list.findIndex(item => item.id === cardValue.id);
    if (findIndex > -1) {
      return this.card_list[findIndex].name;
    }
    if(cardValue==='UserConfig'){
      return this.card_list[0].name;
    }
    else {
      return this.translateService.instant('setting.SelectCard')
    }
   } else {
      return this.translateService.instant('setting.SelectCard')
    }
  }
  getBrandName(id) {
    let findIndex = this.card_list.findIndex(item => item.id === id);
    if (findIndex > -1) {
      return this.card_list[findIndex].brand;
    } else {
      return '';
    }
  }
  getLastFour(id) {
    let findIndex = this.card_list.findIndex(item => item.id === id);
    if (findIndex > -1) {
      return this.card_list[findIndex].last4;
    }
  }
  initAutocomplete(){
    this.autocomplete = new google.maps.places.Autocomplete(this.input.nativeElement, {
      types: ['address'],
      componentRestrictions: { country: 'us' },
    });
    this.autocomplete.addListener('place_changed', () => {
      const place = this.autocomplete.getPlace();
      console.log('Selected Place:', place.formatted_address);
    });
  }
  ngAfterViewInit() {
    console.log('Input element:', this.input); //
    if (this.input && !this.autocomplete) {
      this.initAutocomplete();
    }

    this.cdRef.detectChanges();

  }
  subscription(){
  this.getListOfEvents();
 
  this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
    if (settings) {
      this.addingPolicy=false;
      this.companySettings=settings; 
      if (this.companySettings.cardList && this.companySettings.cardList.card_list.length > 0) {
        this.card_list=[];
        for (let cIndex in this.companySettings.cardList.card_list) {
          this.card_list = JSON.parse(JSON.stringify(this.companySettings.cardList.card_list));
       
        }
        if(this.viewMode2==='tab22'){
          this.card_list.unshift({ name: 'User’s payment method', id:  -1 ,brand:'',last4:''});
          this.cardValue= this.card_list[0];
          this.selectedCardIndex=0;
        }
      }else{
        this.card_list.push({ name: 'User’s payment method', id:  -1 ,brand:'',last4:''});
      }
      
     
      this.policyOptions = [];
      let departmentPolicyList = this.adminPanelService.getDepartmentPolicyList(-1);
      for (let policyOption of departmentPolicyList) {
        this.policyOptions.push({ value: policyOption.policyName, id: '' + policyOption.policyId });
      }
      this.policyOptions.unshift({ value: 'User’s travel policy', id: '' + -1 });
     
    }
  });
  this.listEmployeesResponseSubscription = this.adminPanelService.employeeListResponseObservable$.subscribe(response => {
    if (response) {

      if(this.viewMode2==='tab22'){
        this.appproverList=[];
      if (this.adminPanelService.employeeList && this.adminPanelService.employeeList.length > 0) {
        let employeeFilterList = this.adminPanelService.employeeList;
        this.appproverList = JSON.parse(JSON.stringify(this.sortList(employeeFilterList)));
        if(this.appproverList.length > 0){
          this.appproverList = this.appproverList.filter(item=> item.employeeInfo.limitedUser!==true)
        }
        this.employeeList =  JSON.parse(JSON.stringify(this.sortList(employeeFilterList)));
        if(this.employeeList.length > 0){
          this.employeeList = this.employeeList.filter(item=> item.employeeInfo.limitedUser!==true)
        }
        this.originalEmployeelist = JSON.parse(JSON.stringify(this.sortList(employeeFilterList)));;
        if(this.originalEmployeelist.length > 0){
          this.originalEmployeelist =  this.originalEmployeelist.filter(item=> item.employeeInfo.limitedUser!==true)
        }
        this.appproverList.unshift({ firstName: 'User’s approver',lastName: '', email: '-1',employeeInfo:{limitedUser:false} });
        this.multipleSelectedApprover=['-1'];
     //   
      }
    }
     
    }
  });
  
  }

 
  deleteEmployeeOfEvent(email,j){
   this.deletionProgress=true;
   this.selectedEmployeeList=  this.selectedEmployeeList.filter(item => item.userid!==email);
   $('.deletionLink'+j).show();
   this.adminPanelService.deleteEventEmployeeList(this.eventList[this.selectedEvent].id,email).subscribe(resp =>{
     if(resp && resp.success){
       if(resp && resp.data && resp.data.length > 0){
this.selectedEmployeeList=resp.data
       }else{
        this.selectedEmployeeList=[];
       }
       this.deletionProgress=false;
       $('.deletionLink'+j).hide();
       this.toastr.success(this.translateService.instant("setting.Attendeedeletedsucessfully"))
       this.employeeList = this.originalEmployeelist;
       for(let item of this.selectedEmployeeList){
        this.employeeLocation.push('');
        this.employeeList = this.employeeList.filter(item1 => item1.email!==item.userid);
      }
      
     }else{
       //this.addingEmployee =false;;
       $('.deletionLink'+j).hide();
       this.deletionProgress=false;
     }
   })
  }
  employeeLocationChange(i,loc){
    this.employeeLocation[i]=loc;
    this.editAtendeeDetails(i, this.employeeLocation[i]);
  }
  inviteAttendee(item,j){
    let itemArray=[item.userid];
    this.invitationProgress =true;
    $('.invitationLink'+j).show();
    this.adminPanelService.sendInvitationToAttendee(this.eventList[this.selectedEvent].id,itemArray).subscribe(resp =>{
      if(resp && resp.success){
       // this.addingEmployee =false;
        this.invitationProgress =false;
        if(resp && resp.data && resp.data.length > 0){
 this.selectedEmployeeList=resp.data
        }
        $('.invitationLink'+j).hide();
        this.toastr.success(this.translateService.instant("setting.Attendeeinvitedsucessfully"))
        
      }else{
       // this.addingEmployee =false;;
       $('.invitationLink'+j).hide();
       this.invitationProgress =false;
      }
    })
  }
  editAtendeeDetails(j,loc){
    let attendeObj=this.selectedEmployeeList[j];
    attendeObj['startLocation'] =loc
    this.adminPanelService.addEventEmployeeList(this.eventList[this.selectedEvent].id, attendeObj).subscribe(resp =>{
      if(resp && resp.success){
        this.addingEmployee =false;
        if(resp && resp.data && resp.data.length > 0){
 this.selectedEmployeeList=resp.data
        }
        this.toastr.success(this.translateService.instant("setting.Attendeeupdatedsucessfully"))
        
        //this.employeeList = this.employeeList.filter(item => item.email!==email);
      }else{
        this.addingEmployee =false;;
      }
    })
  }
  getNameOFAttendee(email){
    let employee = this.originalEmployeelist.find(item => item.email===email);
    if(employee && employee){
      return employee.firstName + " " + employee .lastName;
    }
  }
  getDepartmentName(email){
    let employee = this.originalEmployeelist.find(item => item.email===email);
    if(employee && employee.employeeInfo && employee.employeeInfo.departmentId){
      return this.adminPanelService.getDepartmentName(employee.employeeInfo.departmentId);
    }
    return "";
  }
  addEmployeeToEvent(){
    let employeeArray=[];
    for(let item of this.selectMultipleAttendeeList){
     let employeeObj = {userid:item, status: "NOT_INVITED"};
     employeeArray.push(employeeObj);
    }
     this.addingEmployee =true;
    this.adminPanelService.addEventEmployeeList(this.eventList[this.selectedEvent].id,employeeArray).subscribe(resp =>{
      if(resp && resp.success){
        this.addingEmployee =false;
        if(resp && resp.data && resp.data.length > 0){
 this.selectedEmployeeList=resp.data
        }
        this.toastr.success(this.translateService.instant("setting.Attendeeaddedsucessfully"))
        for(let item of this.selectedEmployeeList){
          this.employeeLocation.push('');
        }
        for(let item1 of this.selectMultipleAttendeeList){
        this.employeeList = this.employeeList.filter(item => item.email!==item1);
        }
        this.selectMultipleAttendeeList=[];
      }else{
        this.addingEmployee =false;;
      }
    })
    
  }
  getEventEmployeeList(item){
    this.employeeList = this.originalEmployeelist;
    this.selectedEmployeeList=[];
     this.employeeLocation=[];
    this.adminPanelService.getEventEmployeeList(item).subscribe(resp =>{
      if(resp && resp.success){
        if(resp && resp.data && resp.data.length > 0){
          this.selectedEmployeeList=resp.data
          for(let item of this.selectedEmployeeList){
            this.employeeLocation.push('');
            this.employeeList = this.employeeList.filter(item1=> item1.email !== item.userid);
          }
                 }else{
                  this.selectedEmployeeList=[];
                  this.employeeLocation=[];
                 }
      }
    })
  }
  clicked() {
    if (this.ngxSmartModalService.getOpenedModals() &&
      this.ngxSmartModalService.getOpenedModals().length > 0
    ) {
      let modals = this.ngxSmartModalService.getOpenedModals();
      for (let index = 0; index < modals.length; index++) {
        var currModalTimeStamps = getCurrentlyOpenNgxSmartModalIds();
        let helpModalAlreadyOpen = false;
        if (currModalTimeStamps[modals[index].id] &&
          new Date().getTime() - currModalTimeStamps[modals[index].id] > 500
        ) {
          if (modals[index].id !== 'helpModal') {
            this.ngxSmartModalService.getModal(modals[index].id).close();
            setNgxSmartModalOpenStateClosed(modals[index].id);
          } else {
            if ($('.modalAirportFilterInfo').css('display') !== 'none') {
              $('.modalAirportFilterInfo').slideToggle();
              setNgxSmartModalOpenStateClosed(modals[index].id);
            }
          }
        }
      }
    }
  }
  searchByApproverNameAndEmailChanged(term: string, item: any) {
    term = term.toLowerCase();
    return (item.firstName && item.firstName.toLowerCase().indexOf(term) > -1) ||
      (item.lastName && item.lastName.toLowerCase().indexOf(term) > -1) || item.email.toLowerCase().indexOf(term) > -1;
  }
  public handleBackFromAddCard(data: any) {
    if (!data) {
      if (this.bsModalRef) {
        this.bsModalRef.hide();
      }
      setTimeout(() => {
        this.unsetCardMode();
      }, 300);


      return;
    }


    let tokenData = JSON.parse(data);
    
  
    if (tokenData && tokenData.type === 'newCardAdded') {

      // this.userAccountInfoService.fetchUserAccountInfo(this.emailId, this.sToken);
      let cardTokens: any = tokenData.tokens;
   

      if (true) {
        if (cardTokens && cardTokens.error && cardTokens.error.length > 0) {
         
          this.changingValue.next({ value: '', value1: false });
         
        }

        else if (cardTokens.token && cardTokens.gToken) {
          GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
            'cardTokenCreated', 'WebSearchUI'
          );
          if (true) {
            this.adminPanelService.requestSaveCardInfo(cardTokens.token, cardTokens.gToken).subscribe(res => {
             
              if (res.status === 'success') {
                this.adminPanelService.addNewCard = true;
                
                this.updatePolicy();
              
                if (this.bsModalRef) {
                  this.bsModalRef.hide();
                }
               
                setTimeout(() => {
                
                  this.unsetCardMode();
                }, 300);


              } else if (res.status === 'CARDERROR') {
               
                this.changingValue.next({ value: res.message, value1: false });
               

              } else {
                
                this.changingValue.next({ value: res.message, value1: false });
               
              }
            });
          }
        } else {
          
        }
      }
    }
  }
  isdropDownOpen() {
    this.dropDownopen = true;
    this.search = this.translateService.instant('search.AddTraveler');
  }
  routeToPolicy(){
    let url = "/admin?type=policies&subtype=true"
   window.open(url,"_blank");
  }
  subTabClicked(item){
    this.viewSubMode2=item;
    if(item == 'tab13'){
      this.getBookingsList();
    }
    }
    public setCardIndex(index: number) {
      this.selectedCardIndex = index;
    }
    setCardMode(modal?, modal1?) {
      this.addCardMode = true;
     
      
        if (!this.isMobile1) {
          this.bsModalRef = this.modalService.show(modal, {
            initialState: {
            }, backdrop: true, ignoreBackdropClick: true
          });
        } else {
          setTimeout(() => {
            this.clicked();
          }, 100);
        }
      
    }
  
    unsetCardMode() {
      this.addCardMode = false;
    }
    onModelCancel1() {
     
      this.bsModalRef.hide();
      setTimeout(() => {
        if (this.addCardMode) {
          this.unsetCardMode();
        }
      }, 300);
  
      // this.cardOptions1 = [...this.cardOptions];
  
    }
  filterSelectedlist(item) {
    for (let option of item) {
      this.appproverList = this.appproverList.filter(item2 => item2.email !== option);
    }
    for (let option of item) {
      let originalList;
      if (this.adminPanelService.employeeList && this.adminPanelService.employeeList.length > 0) {
        let employeeFilterList = this.adminPanelService.employeeList;
        originalList = this.sortList(employeeFilterList);
      }
      if (originalList && originalList.length > 0) {
        let approver = originalList.find(item3 => item3.email === option)
        this.appproverList.unshift(approver);
      }
    }
    if(this.appproverList[0]===undefined){
    this.appproverList[0] = { firstName: 'User’s approver',lastName: '', email: '-1' ,employeeInfo:{limitedUser:false}};
    }else{
      this.appproverList= this.appproverList.filter(item2 => item2.email !== '-1');
      this.appproverList.unshift({ firstName: 'User’s approver',lastName: '', email: '-1',employeeInfo:{limitedUser:false} });
    }
    if(this.appproverList.length > 0){
      this.appproverList = this.appproverList.filter(item=> item.employeeInfo.limitedUser!==true)
    }
  //  
  }
  onApproverChangeClicked(item, event) {
    
    if (event.target.checked) {
      if (this.multipleSelectedApprover.indexOf(item) === -1 && item ==='-1') {
        this.multipleSelectedApprover=[];
        this.multipleSelectedApprover.push(item);
      }else if (this.multipleSelectedApprover.indexOf(item) === -1 && this.multipleSelectedApprover.indexOf('-1') ===-1) {
        this.multipleSelectedApprover.push(item);
      }else if(this.multipleSelectedApprover.indexOf('-1') !==-1 && item!=='-1'){
        this.multipleSelectedApprover=[];
        this.multipleSelectedApprover.push(item);
      }
      

    } else {
      this.multipleSelectedApprover = this.multipleSelectedApprover.filter(item1 => item1 !== item);
      this.isApproverChecked(item);
    }
    this.updateDeptButtonClicked = false;
  }
  isApproverChecked(item) {
    return this.multipleSelectedApprover.indexOf(item) > -1;
  }
  onAttendeeChangeClicked(item, event) {
    
    if (event.target.checked) {
      if (this.selectMultipleAttendeeList.indexOf(item) === -1) {
        this.selectMultipleAttendeeList.push(item);
      }

    } else {
      this.selectMultipleAttendeeList = this.selectMultipleAttendeeList.filter(item1 => item1 !== item);
      this.isApproverChecked(item);
    }
  }
  isAttendeeChecked(item) {
    return this.selectMultipleAttendeeList.indexOf(item) > -1;
  }
  sortList(data) {
    data.sort(function (a, b) {
      // 
      if (a.firstName === "" || !a.firstName) {
        return 1;
      } else if (b.firstName === "" || !b.firstName) {
        return -1;
      } else if (a.firstName < b.firstName) { return -1; }
      else if (a.firstName > b.firstName) { return 1; }
      return 0;
    })
    return data;
  }
  getDepartureDateDate() {
    return this.eventCreateForm.controls['start'].value;
  }
  getListOfEvents(){
    this.showStartingLoader=true;
    this.adminPanelService.getEventList().subscribe(resp=>{
      if(resp  && resp.success){
        
        this.showStartingLoader=false;;
        if(resp && resp.data && resp.data.length > 0){
          this.eventList = resp.data;
        }
      }else{
        this.showStartingLoader=false;
      }
    })
  }
  changeArrivalTime(event,type){ 
    if(event && type==='departure'){
      this.departureTimeCheck = event;
      this.fullEventCreateForm.controls['suggestedEnd'].enable();
      this.fullEventCreateForm.controls['departureTimeFilter'].enable();
      this.fullEventCreateForm.controls['suggestedEnd'].setValue(new Date(this.eventList[this.selectedEvent].end));
  
    }else if(event && type==='arrival'){
      this.fullEventCreateForm.controls['suggestedStart'].enable();
      this.arrivalTimeCheck = event;
      this.fullEventCreateForm.controls['arrivalTimeFilter'].enable();
      this.fullEventCreateForm.controls['suggestedStart'].setValue(new Date(this.eventList[this.selectedEvent].start));
    }else if(!event && type==='departure'){
      this.fullEventCreateForm.controls['suggestedEnd'].setValue(null);
      this.departureTimeCheck = event;
      this.fullEventCreateForm.controls['suggestedEnd'].disable();
      this.fullEventCreateForm.controls['departureTimeFilter'].disable();
    }else if(!event && type==='arrival'){  
      this.arrivalTimeCheck = event;
      this.fullEventCreateForm.controls['suggestedStart'].setValue(null);
      this.fullEventCreateForm.controls['suggestedStart'].disable();
      this.fullEventCreateForm.controls['arrivalTimeFilter'].disable();
    }

  }
  getTimeOptionValue(option) {

    if (option.title == 'Anytime') {
      return "";
    }

    let startAMPM = (option.hours.start < 12 || option.hours.start == 24) ? this.translateService.instant('search.AM') : this.translateService.instant('search.PM');
    let endAMPM = (option.hours.end < 12 || option.hours.end == 24) ? this.translateService.instant('search.AM') : this.translateService.instant('search.PM');

    let start = option.hours.start > 12 ? option.hours.start - 12 : option.hours.start;
    let end = option.hours.end > 12 ? option.hours.end - 12 : option.hours.end;
    start = (start == 0) ? 0 : start;

    return `${start} ${startAMPM}`;
  }

  getTimeOptionName(id, item) {
    if (item) {
      this.getTimeOptions(item);
    }
    let timeOptionName;
    if (!this.timeOptions) {
      return 'ngOption.Anytime';
    }
    this.timeOptions.map(item => {
      if (item.titleID.toLowerCase() == id.toLowerCase()
        || item.value.toLowerCase() == id.toLowerCase()) {
        timeOptionName = item.value;
        // return;
      }
    });

    return timeOptionName;
  }
  getTimeOptions(timeConstraintType: string) {
    let time: number;
    let today = DateUtils.getCurrentDateAsUTCDate();
    let constraintDate;
    if (timeConstraintType == IntervalType.DEPARTURE_BASED) {
      this.timeOptions = Constants.CONSTRAINT_TIME_OPTIONS_FOR_EVENT;
    } else {
      this.timeOptions = Constants.CONSTRAINT_TIME_OPTIONS_FOR_EVENT;
    }
  }
  getLabelValueTimeFilter(){

  }
  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
  }
  updatePaymentMethod(id,modal){
    let card = this.card_list.filter(item =>item.id===id);
    if(card && card[0] && id!==-1){
      this.cardValue =card[0];
    }else{
      this.cardValue='UserConfig'
    }
    if (this.ngxSmartModalService.getModal(modal)) {
      this.ngxSmartModalService.getModal(modal).close();
    }
  }
  changePolcies(item){
    this.policyCheck=item;
    if(item){
      if(this.policyOptions && this.policyOptions.length > 0){
        this.fullEventCreateForm.controls['policyId'].setValue(this.policyOptions[0].id);
      }
     
    }else{
      this.fullEventCreateForm.controls['policyId'].setValue(null);
    }
  }
  changeApprover(item){
    this.approverCheck=item;
    if(item){
     this.dropDownopen=false;
      this.fullEventCreateForm.controls['approvers'].setValidators(Validators.required);
      this.fullEventCreateForm.updateValueAndValidity();
      this.multipleSelectedApprover=[];
    }else{
      this.fullEventCreateForm.controls['approvers'].clearValidators();
      this.fullEventCreateForm.updateValueAndValidity();
      this.multipleSelectedApprover=[];
    }
  }
  changePayment(item){
    this.paymentCheck=item;
    if(item){
      if (this.companySettings.cardList && this.companySettings.cardList.card_list.length > 0) {
        for (let cIndex in this.companySettings.cardList.card_list) {
        if (this.companySettings.cardList.card_list[cIndex].selected) {
          this.selectedCardIndex = parseInt(cIndex);
           this.cardValue = this.companySettings.cardList.card_list[cIndex];
          }
        }
      }
      this.fullEventCreateForm.controls['payment'].setValidators(Validators.required);
      this.fullEventCreateForm.updateValueAndValidity();
    }else{
      this.cardValue ='UserConfig';
      this.fullEventCreateForm.controls['payment'].clearValidators();
      this.fullEventCreateForm.updateValueAndValidity();
    }
   
  }
  setDepartureDate(date){
    if(date){
      this.minimumDate2 = new Date(date);
    
      if (this.eventCreateForm) {
        let leg1DateControl = this.eventCreateForm.controls['start'];
        leg1DateControl.setValue(date);
      }
    }
  }
  setArrivalDate(date){
    if(date){
    
      if (this.eventCreateForm) {
        let leg1DateControl = this.eventCreateForm.controls['end'];
        leg1DateControl.setValue(date);
      }
    }
  }
  setItemTravelStart(date){
    if(date){
    this.minimumDate3 =date;
      if (this.fullEventCreateForm) {
        let leg1DateControl = this.fullEventCreateForm.controls['itemTravelStart'];
        leg1DateControl.setValue(date);
      }
    } 
  }
  setItemTravelEnd(date){
    if(date){
    
      if (this.fullEventCreateForm) {
        let leg1DateControl = this.fullEventCreateForm.controls['itemTravelEnd'];
        leg1DateControl.setValue(date);
      }
    } 
  }
  setsuggestedStart(date){
    if(date){
    this.minimumDate4=date;
      if (this.fullEventCreateForm) {
        let leg1DateControl = this.fullEventCreateForm.controls['suggestedStart'];
        leg1DateControl.setValue(date);
      }
    }
  }
  setsuggestedEnd(date){
    if(date){
    
      if (this.fullEventCreateForm) {
        let leg1DateControl = this.fullEventCreateForm.controls['suggestedEnd'];
        leg1DateControl.setValue(date);
      }
    }
  }
  closeDropdown() {
    this.dropDownopen = false;
    if(this.selectMultipleAttendeeList.length >0){
    this.addEmployeeToEvent();
    }
  }
  TabClicked(item){
if(item!=='tab22'){
  this.selectedEvent = item;
  this.editEventForm();
  this.getBookingsList();
}else{
  this.eventCreateForm.reset();
}
this.viewMode2 =item;
  }
  getLabelValue() {
    if (this.dropDownopen) {
      return 'fuild.Typetosearch';;
    }else{
    return 'setting.selectapprover';
    }
  }
  
 
  editEventForm(){
    this.getEventEmployeeList(this.eventList[this.selectedEvent].id);
    this.updateDeptButtonClicked = false;
   this.eventCreateForm.controls['groupTravelEventName'].setValue(this.eventList[this.selectedEvent].groupTravelEventName);
   this.eventCreateForm.controls['location'].setValue(this.eventList[this.selectedEvent].location);
   this.eventCreateForm.controls['start'].setValue(new Date(this.eventList[this.selectedEvent].start));
   this.eventCreateForm.controls['end'].setValue(new Date(this.eventList[this.selectedEvent].end));
   this.fullEventCreateForm = this.fb.group({
    arrivalTimeFilter: [this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].suggestedStart ? this.convertToAmPm(this.eventList[this.selectedEvent].items[0].suggestedStart):'8am'],
    departureTimeFilter: [this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].suggestedEnd ? this.convertToAmPm(this.eventList[this.selectedEvent].items[0].suggestedEnd):'4pm'],
    suggestedStart:[null],
    suggestedEnd:[null],
    payment:[this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].allowedPaymentMethods? this.eventList[this.selectedEvent].items[0].allowedPaymentMethods:-1],
    approvers:[this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].approvers.length > 0 ? this.eventList[this.selectedEvent].items[0].approvers:''],
    destinationAirports:[this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].destinationAirports && this.eventList[this.selectedEvent].items[0].destinationAirports.length > 0 ? this.eventList[this.selectedEvent].items[0].destinationAirports:null],
    itemTravelStart:[this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].itemTravelStart ? new Date(this.eventList[this.selectedEvent].items[0].itemTravelStart):new Date(this.eventList[this.selectedEvent].start)],
    itemTravelEnd:[this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].itemTravelEnd ? new Date(this.eventList[this.selectedEvent].items[0].itemTravelEnd):new Date(this.eventList[this.selectedEvent].end)],
    policyId: [this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].policyId ? this.eventList[this.selectedEvent].items[0].policyId:'-1']
  });
  if(this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].suggestedEnd){
    this.fullEventCreateForm.controls['suggestedEnd'].enable();
    this.fullEventCreateForm.controls['departureTimeFilter'].enable();
  this.fullEventCreateForm.controls['suggestedEnd'].setValue(new Date(this.eventList[this.selectedEvent].items[0].suggestedEnd));
  this.departureTimeCheck=true;
  }else{
    this.departureTimeCheck=false;;
    this.fullEventCreateForm.controls['suggestedEnd'].disable();
    this.fullEventCreateForm.controls['departureTimeFilter'].disable();
  }
  if(this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].suggestedStart){
    this.fullEventCreateForm.controls['suggestedStart'].enable();
  this.fullEventCreateForm.controls['arrivalTimeFilter'].enable();
  this.fullEventCreateForm.controls['suggestedStart'].setValue(new Date(this.eventList[this.selectedEvent].items[0].suggestedStart));
  this.arrivalTimeCheck=true;
  this.minimumDate4=this.fullEventCreateForm.controls['suggestedStart'].value;
  }else{
    this.arrivalTimeCheck=false;
    this.fullEventCreateForm.controls['suggestedStart'].disable();
    this.fullEventCreateForm.controls['arrivalTimeFilter'].disable();
    
  }
  if(this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].destinationAirports){
    this.selectedAirports = this.eventList[this.selectedEvent].items[0].destinationAirports;
  }else{
    this.selectedAirports =[];
  }
  if(this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].approvers && this.eventList[this.selectedEvent].items[0].approvers.length > 0){
       this.multipleSelectedApprover = this.eventList[this.selectedEvent].items[0].approvers;
       this.approverCheck=true;
  }else{
    this.multipleSelectedApprover=['-1'];
    this.approverCheck=false;
  }
  if(this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].policyId){
    this.policyCheck=true;
}else{
  this.policyCheck=false;
}
if(this.eventList[this.selectedEvent].items && this.eventList[this.selectedEvent].items.length> 0 && this.eventList[this.selectedEvent].items[0].customConfigCardId){
this.cardValue = this.card_list.find(item=> item.id===this.eventList[this.selectedEvent].items[0].customConfigCardId);
  this.paymentCheck=true;
}else{
  this.cardValue ='UserConfig'
  this.paymentCheck=false;
}
  this.fullEventCreateForm.controls['destinationAirports'].setValidators(Validators.required);
   this.fullEventCreateForm.updateValueAndValidity();
  
  
   this.viewSubMode2='tab11';
   
   this.getAirports(this.eventList[this.selectedEvent].location);
  }
  getAirports(loc){
    this.adminPanelService.getLocationAirportList(loc).subscribe(resp=>{
      if(resp && resp.data){
        let airports=JSON.parse(resp.data);
        
        this.airports =airports;
      }
    })
  }
  openCheckinCalender(checkinDatePicker){
   
      checkinDatePicker.toggle();
       
  }
  setSelectedHomeAirport(item,option){
    if(item){
      if(this.selectedAirports.indexOf(option)===-1){
        this.selectedAirports.push(option);
      }
    }else{
      this.selectedAirports =  this.selectedAirports.filter(item1 => item1!==option);
    }
    
    this.isAirportChecked(option);
  }
  isAirportChecked(code){
   return  this.selectedAirports.indexOf(code) > -1;
  }
  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    this.searchService.previousDate = false;
    const dayHoverHandler = event.dayHoverHandler;
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        (picker as any)._datepickerRef.instance.daySelectHandler(cell);
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }
  cancelEvent(){
    this.eventCreateForm.reset();

  }
  addEventEmployee(){

  }
  addEvent(){
    if(this.eventCreateForm.invalid){
      this.eventCreateForm.markAllAsTouched();
      return;
    }
    this.addEventProgress=true;
    if((!this.eventCreateForm.controls['groupTravelEventName'].value || this.eventCreateForm.controls['groupTravelEventName'].value==='') && this.currentLocation){
      let datePipe = new DatePipe('en-US');
      
      let val = this.currentLocation +' '+ datePipe.transform(new Date(this.eventCreateForm.controls['start'].value), 'MMM yyyy');
      this.eventCreateForm.controls['groupTravelEventName'].setValue(val);
    }
    let fullEventObj=this.eventCreateForm.value;
    fullEventObj['start'] =  this.dateFprmatting(fullEventObj['start']);
    fullEventObj['end'] =  this.dateFprmatting(fullEventObj['end']);
    this.adminPanelService.createEvent(fullEventObj).subscribe(resp=>{
      if(resp  && resp.success){
        
        if(resp && resp.data && resp.data.length > 0){
          this.eventList = resp.data;
          let findIndex = this.eventList.findIndex(item => item.groupTravelEventName===fullEventObj['groupTravelEventName']);
        if(findIndex !== -1){
        this.TabClicked(findIndex)
        }
        }
        this.toastr.success(this.translateService.instant("setting.EventaddedSucessfull"))
        this.addEventProgress=false;
      } else {
        if (resp.errorMessage && resp.errorMessage.length > 0) {
          this.toastr.error(resp.errorMessage[0]);
          this.addEventProgress=false;
         
        } else {
          this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
          this.addEventProgress=false;
         
        }
      }
    })
  }
  scrollTo(el: Element): void {
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }
  private scrollToFirstInvalidControl() {
    const firstInvalidControl: HTMLElement = this.el.nativeElement.querySelector(".error");
    this.scrollTo(firstInvalidControl);
  }

  updateEvent(){
    if(this.addDeleteProgress){
      return;
    }
    if(this.eventCreateForm.invalid){
      this.eventCreateForm.markAllAsTouched();
      setTimeout(() => {
        this.scrollToFirstInvalidControl(); 
      }, 200);
     
      return;
    }
    if(this.fullEventCreateForm.invalid){
      this.fullEventCreateForm.markAllAsTouched();
      setTimeout(() => {
        this.scrollToFirstInvalidControl(); 
      }, 200);
      return;
    }
    let fullEventObj=this.eventCreateForm.value;
    let itemObj:any=this.fullEventCreateForm.value;
    if(this.multipleSelectedApprover && this.multipleSelectedApprover[0]==='-1'){
      itemObj['approvers'] = [];
    }else{
    itemObj['approvers'] = this.multipleSelectedApprover;
    }
    if(itemObj['policy']==='-1'){
      itemObj['policy']=null;
    }
    fullEventObj['id']= this.eventList[this.selectedEvent].id;
    itemObj['destinationAirports'] = this.selectedAirports;
    itemObj['type']='flights';
    fullEventObj['start'] =  this.dateFprmatting(fullEventObj['start']);
    fullEventObj['end'] =  this.dateFprmatting(fullEventObj['end']);
    itemObj['itemTravelStart'] =  this.dateFprmatting(itemObj['itemTravelStart']);
    itemObj['itemTravelEnd'] =  this.dateFprmatting(itemObj['itemTravelEnd']);
    if(itemObj['suggestedStart']){
    itemObj['suggestedStart'] = this.dateFprmatting(itemObj['suggestedStart']).split('T')[0] +'T'+ this.convertToIso(itemObj['arrivalTimeFilter']);
    }else{
      itemObj['suggestedStart']=null;
    }
    if(itemObj['suggestedEnd']){
    itemObj['suggestedEnd'] = this.dateFprmatting(itemObj['suggestedEnd']).split('T')[0] +'T'+  this.convertToIso(itemObj['departureTimeFilter'])
    }else{
      itemObj['suggestedEnd']=null;
    }
    if(this.cardValue && this.cardValue.id){
      itemObj['allowedPaymentMethods']='CustomConfig';
      itemObj["customConfigCardId"]= this.cardValue.id;
    
    }else{
      itemObj['allowedPaymentMethods']=this.cardValue;
    }
    delete itemObj['departureTimeFilter'];
    delete itemObj['arrivalTimeFilter'];
    delete itemObj['payment'];
    fullEventObj['items']=[];
    fullEventObj.items.push(itemObj);
    this.addEventProgress=true;
    this.adminPanelService.updateEvent(fullEventObj).subscribe(resp=>{
      if(resp  && resp.success){
        
        if(resp && resp.data && resp.data.length > 0){
          this.eventList = resp.data;
        }
        this.toastr.success(this.translateService.instant("setting.EventupdatedSucessfull"))
        this.addEventProgress=false;
      } else {
        if (resp.errorMessage && resp.errorMessage.length > 0) {
          this.toastr.error(resp.errorMessage[0]);
          this.addEventProgress=false;
         
        } else {
          this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
          this.addEventProgress=false;
         
        }
      }
    })
  }
  deleteEvent(){
    let eveObj={};
    eveObj['id'] = this.eventList[this.selectedEvent].id;
    this.addDeleteProgress=true;
    this.adminPanelService.removeEvent(eveObj).subscribe(resp =>{
      if(resp && resp.success){
        this.addDeleteProgress=false;
        this.eventList = this.eventList.filter(item => item.id!==eveObj['id']);
        this.toastr.success(this.translateService.instant("setting.EventdeletedSucessfull"))
        this.viewMode2='tab22';
        window.scrollTo(0, 0);
        this.eventCreateForm.reset();
      }else {
        if (resp.errorMessage && resp.errorMessage.length > 0) {
          this.toastr.error(resp.errorMessage[0]);
          this.addDeleteProgress=false;
         
        } else {
          this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
          this.addDeleteProgress=false;
         
        }
      }
    })
  }
  convertToAmPm(timeStr: string): string {
    const date = new Date(timeStr);
    let hours = date.getUTCHours();
    const minutes = date.getUTCMinutes();
    const ampm = hours >= 12 ? 'pm' : 'am';

    hours = hours % 12;
    hours = hours ? hours : 12; // The hour '0' should be '12'

    const minutesString = minutes < 10 ? '0' + minutes : minutes;
    return `${hours}${ampm}`;
  }
  
  convertToIso(timeStr: string): string {
    // Convert the input to lowercase and extract hours and period (am/pm)
    timeStr = timeStr.toLowerCase();
    const match = timeStr.match(/^(\d{1,2})(am|pm)$/);
  
  
    let hours = parseInt(match[1], 10);
    const period = match[2];
  
    // Convert to 24-hour format
    if (period === 'pm' && hours !== 12) {
      hours += 12;
    } else if (period === 'am' && hours === 12) {
      hours = 0;
    }
  
    // Format hours, minutes, and seconds in ISO format
    const isoTime = `${hours.toString().padStart(2, '0')}:00:00.000Z`;
  
    return isoTime;
  }
 dateFprmatting(date){
  let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(date);
  let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
  return startDate;
 }
 getCurrencySymbol(item1): string {
  if (item1.option && item1.option.flight_option  && item1.option.flight_option.displayCurrency) {
    return CommonUtils.getCurrencySymbol(item1.option.flight_option.displayCurrency);
  } else {
    return CommonUtils.getCurrencySymbol(item1.option.flight_option.currency);
  }
}
 getBookingsList (){
  this.adminPanelService.getEventBookingsList(this.eventList[this.selectedEvent].id).subscribe(resp =>{
    if(resp && resp.data && resp.data.bookingList){
      this.eventBookingList = resp.data.bookingList;
    }
    else{
      this.eventBookingList = [];
    }
  }
  )
 }
 getDisplayDate(dateString: string): string {
  if (!dateString || dateString.trim() === ''){
    return '';
  }
  return DateUtils.getDisplayDate(dateString);
}
openItinerary(item) {
  const url = '/admin?view=detail&type=detail&transactionid=' + item.option.selectTransId;
  window.open(url, '_blank');
}
}
