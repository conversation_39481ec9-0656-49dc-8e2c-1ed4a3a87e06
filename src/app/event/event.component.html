<div class="card-div active shadow" style="padding-bottom: 350px;">
        <div class="card-div-inner" >
                <div class="section" style="display: grid;">
                        <div *ngIf="this.eventList.length > 0" class="row" style="margin-bottom: 40px;">
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                                        style="text-align: left;margin-bottom:20px;">

                                        <div class="view11">
                                                <span *ngFor="let item of this.eventList;let i=index">
                                                        <span (click)="TabClicked(i)"
                                                                style="cursor: pointer !important;"
                                                                class="{{ viewMode2==i ? 'items':'items1'}}">
                                                                {{ item.groupTravelEventName }}
                                                        </span>
                                                        <span class="line1"></span>
                                                </span>
                                                <span *ngIf="this.eventList.length > 0"
                                                        style="cursor: pointer !important;"
                                                        (click)="TabClicked('tab22')"
                                                        class="{{ viewMode2=='tab22' ? 'items':'items1'}}">
                                                        {{ 'setting.addEvents' | translate }}
                                                </span>

                                        </div>
                                </div>
                        </div>
                        <div *ngIf="this.showStartingLoader"  style="margin-top: 20px;text-align: center;">
                                        <app-loader  [spinnerStyle]="true"></app-loader>
                                </div>
                        <div *ngIf="viewMode2=='tab22' || this.eventList.length===0" class="tab-content">
                                <div class="row">
                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                                                style="text-align: left;margin-bottom:10px;margin-top: 20px;">

                                                <h3> {{ 'setting.Createanevent' | translate }}</h3>
                                        </div>
                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                                                style="text-align: left;margin-bottom:20px;">

                                                <span class="subHeading">
                                                        {{ 'setting.Organizegrouptravelformeetingsoffsitesoreventswewillsendinvitestoyourtravelersshowyoutheirbookingprogressandkeepyouinformedofestimateandactualbookingcost' | translate }}</span>
                                        </div>
                                </div>

                                <form [formGroup]="this.eventCreateForm">
                                        <div class="input-field">
                                                <label> {{'setting.EventName' | translate}}</label>
                                                <input type="text" class="inputForAirlines" maxlength="50"
                                                        formControlName="groupTravelEventName">

                                                
                                        </div>

                                        <div class="row">
                                                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                                                        <div class="input-field">
                                                                <label> {{'setting.EventLocation' | translate}}</label>
                                                                <input type="text" #addressInput
                                                                        [options]="[autoCompleteOptions]"
                                                                        (keydown.enter)="$event.preventDefault()"
                                                                        (click)="$event.stopPropagation();"
                                                                        (onAddressChange)="eventLocationChange($event,addressInput.value)"
                                                                        class="inputForAirlines" 
                                                                        formControlName="location" >

                                                                <div *ngIf="this.eventCreateForm.controls['location'].hasError('required') && (this.eventCreateForm.controls['location'].touched || this.eventCreateForm.controls['location'].dirty)"
                                                                        class="error">
                                                                        {{'login.thisfieldisrequired' | translate}}
                                                                </div>
                                                                <div style="font-size: 14px;">
                                                                        {{'setting.ifyouknowthehotelwhereeveryonewillstay orshouldstaythenpleaseentertheexactlocationofthehotel' | translate}}     
                                                                </div>
                                                        </div>
                                                </div>

                                                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12"
                                                        style="display: flex;">
                                                        <div class="col-6">
                                                                <div class="input-field">
                                                                        <label>
                                                                                {{'setting.Startdate' | translate}}</label>
                                                                        <span class="input-icon material-icons"
                                                                                (click)="openCheckinCalender(checkinDatePicker)">event</span>
                                                                               

                                                                        <input bsDatepicker
                                                                                #checkinDatePicker="bsDatepicker"
                                                                                class="inputForAirlines"
                                                                                formControlName="start"
                                                                                [minDate]="minimumDate1"
                                                                               
                                                                                [outsideClick]="true"
                                                                                [bsConfig]="{showWeekNumbers: false, dateInputFormat: 'DD MMM YYYY'}"
                                                                                (onShown)="onShowPicker($event, checkinDatePicker)"
                                                                                (bsValueChange)="setDepartureDate($event)"
                                                                                (onHidden)="onHidePicker()" readonly />
                                                                        <div *ngIf="this.eventCreateForm.controls['start'].hasError('required') && (this.eventCreateForm.controls['start'].touched || this.eventCreateForm.controls['start'].dirty)"
                                                                                class="error">
                                                                                {{'login.thisfieldisrequired' | translate}}
                                                                        </div>
                                                                </div>
                                                        </div>
                                                        <div class="col-6">
                                                                <div class="input-field">
                                                                        <label>
                                                                                {{'setting.Enddate' | translate}}</label>
                                                                        <span class="input-icon material-icons"
                                                                                (click)="openCheckinCalender(checkoutDatePicker)">event</span>

                                                                        <input bsDatepicker
                                                                                #checkoutDatePicker="bsDatepicker"
                                                                                class="inputForAirlines"
                                                                                formControlName="end"
                                                                                [minDate]="minimumDate2"
                                                                                [outsideClick]="true"
                                                                                [bsConfig]="{showWeekNumbers: false, dateInputFormat: 'DD MMM YYYY'}"
                                                                                (onShown)="onShowPicker($event, checkoutDatePicker)"
                                                                                (bsValueChange)="setArrivalDate($event)"
                                                                                (onHidden)="onHidePicker()" readonly />

                                                                        <div *ngIf="this.eventCreateForm.controls['end'].hasError('required') && (this.eventCreateForm.controls['end'].touched || this.eventCreateForm.controls['end'].dirty)"
                                                                                class="error">
                                                                                {{'login.thisfieldisrequired' | translate}}
                                                                        </div>
                                                                </div>
                                                        </div>
                                                </div>
                                        </div>
                                </form>
                                <div class="rentalButtonConatiner">
                                        <button *ngIf="!addEventProgress" class="btn button-primary" style="z-index: 9;position: relative;"
                                                (click)="addEvent()">{{
                                            ('setting.Saveandcontinue' | translate)}}</button>

                                        <button *ngIf="addEventProgress" class="button button-primary">{{'addCard.wait' |
                                            translate}}  <span *ngIf="addEventProgress" class="loaderCarRental">
                                                        <loader-dots class="loaderAlign"></loader-dots>
                                                </span></button>
                                      
                                                
                                                <button *ngIf="!addEventProgress" class="text-button link-primary"
                                                        (click)="cancelEvent()">{{'setting.Cancel' | translate}}</button>
                                        

                                </div>
                        </div>
                        <div *ngIf="viewMode2!=='tab22'" class="tab-content">
                                <div class="row" style="margin-bottom: 40px;margin-top: 20px;">
                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                                                style="text-align: left;margin-bottom:20px;">

                                                <div class="view11">
                                                        <span>
                                                                <span (click)="subTabClicked('tab11')"
                                                                        style="cursor: pointer !important;"
                                                                        class="{{ viewSubMode2=='tab11' ? 'items':'items1'}}">
                                                                        {{ 'setting.Eventsetting' | translate }}
                                                                </span>
                                                        </span>
                                                        <span class="line1"></span>
                                                        <span style="cursor: pointer !important;"
                                                                (click)="subTabClicked('tab12')"
                                                                class="{{ viewSubMode2=='tab12' ? 'items':'items1'}}">
                                                                {{ 'setting.participants' | translate }}
                                                        </span>
                                                        <span class="line1"></span>
                                                        <span style="cursor: pointer !important;"
                                                                (click)="subTabClicked('tab13')"
                                                                class="{{ viewSubMode2=='tab13' ? 'items':'items1'}}">
                                                                 {{ 'setting.Bookings' | translate }}
                                                        </span>

                                                </div>
                                        </div>
                                </div>
                                <div *ngIf="viewSubMode2==='tab11'" class="tab-content">
                                        <form [formGroup]="this.eventCreateForm">
                                                <hr>
                                                <div class="input-field">
                                                        <label> {{'setting.EventName' | translate}}</label>
                                                        <input type="text" class="inputForAirlines" maxlength="50"
                                                                formControlName="groupTravelEventName">

                                                      
                                                </div>

                                                <div class="row">
                                                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                                                                <div class="input-field">
                                                                        <label>
                                                                                {{'setting.EventLocation' | translate}}</label>
                                                                        <input type="text"
                                                                        #addressInput
                                                                                [options]="[autoCompleteOptions]"
                                                                                (keydown.enter)="$event.preventDefault()"
                                                                                (click)="$event.stopPropagation();"
                                                                                (onAddressChange)="eventLocationChange($event,addressInput.value)"
                                                                                class="inputForAirlines" 
                                                                                formControlName="location"
                                                                                >

                                                                        <div *ngIf="this.eventCreateForm.controls['location'].hasError('required') && (this.eventCreateForm.controls['location'].touched || this.eventCreateForm.controls['location'].dirty)"
                                                                                class="error">
                                                                                {{'login.thisfieldisrequired' | translate}}
                                                                        </div>
                                                                        <div style="font-size: 14px;">
                                                                                {{'setting.ifyouknowthehotelwhereeveryonewillstay orshouldstaythenpleaseentertheexactlocationofthehotel' | translate}}     
                                                                        </div>
                                                                </div>
                                                        </div>

                                                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12"
                                                                style="display: flex;">
                                                                <div class="col-6">
                                                                        <div class="input-field">
                                                                                <label>
                                                                                        {{'setting.Startdate' | translate}}</label>
                                                                                <span class="input-icon material-icons"
                                                                                        (click)="openCheckinCalender(checkinDatePicker)">event</span>

                                                                                <input bsDatepicker
                                                                                        #checkinDatePicker="bsDatepicker"
                                                                                        class="inputForAirlines"
                                                                                        formControlName="start"
                                                                                        [minDate]="minimumDate1"
                                                                                       
                                                                                        [outsideClick]="true"
                                                                                        [bsConfig]="{showWeekNumbers: false, dateInputFormat: 'DD MMM YYYY'}"
                                                                                        (onShown)="onShowPicker($event, checkinDatePicker)"
                                                                                        (bsValueChange)="setDepartureDate($event)"
                                                                                        (onHidden)="onHidePicker()"
                                                                                        readonly />
                                                                                <div *ngIf="this.eventCreateForm.controls['start'].hasError('required') && (this.eventCreateForm.controls['start'].touched || this.eventCreateForm.controls['start'].dirty)"
                                                                                        class="error">
                                                                                        {{'login.thisfieldisrequired' | translate}}
                                                                                </div>
                                                                        </div>
                                                                </div>
                                                                <div class="col-6">
                                                                        <div class="input-field">
                                                                                <label>
                                                                                        {{'setting.Enddate' | translate}}</label>
                                                                                <span class="input-icon material-icons"
                                                                                        (click)="openCheckinCalender(checkoutDatePicker)">event</span>

                                                                                <input bsDatepicker
                                                                                        #checkoutDatePicker="bsDatepicker"
                                                                                        class="inputForAirlines"
                                                                                        formControlName="end"
                                                                                      
                                                                                        [minDate]="minimumDate1"
                                                                                        [outsideClick]="true"
                                                                                        [bsConfig]="{showWeekNumbers: false, dateInputFormat: 'DD MMM YYYY'}"
                                                                                        (onShown)="onShowPicker($event, checkoutDatePicker)"
                                                                                        (bsValueChange)="setArrivalDate($event)"
                                                                                        (onHidden)="onHidePicker()"
                                                                                        readonly />

                                                                                <div *ngIf="this.eventCreateForm.controls['end'].hasError('required') && (this.eventCreateForm.controls['end'].touched || this.eventCreateForm.controls['end'].dirty)"
                                                                                        class="error">
                                                                                        {{'login.thisfieldisrequired' | translate}}
                                                                                </div>
                                                                        </div>
                                                                </div>
                                                        </div>
                                                </div>

</form>
                      <form [formGroup]="fullEventCreateForm" style="margin-top: 40px;display: grid;">
                        <hr>
                        <div class="heading" style="margin-top: 0px;">
                                                        {{'setting.Travelwindow' | translate}}
                                                </div>
                                                <div class="input-label primary-label" style="font-size: 18px;">
                                                                {{'setting.Setarrivalandreturndatesforparticipantsandsuggestarrivalandreturntimes' | translate}}
                                                        </div>
                                                <div class="rentalButtonConatiner">
                                                        <label class="input-label primary-label" style="margin-bottom: -5px;">{{'setting.AirTravelDates' | translate}}
                                                        </label>
                                                        <div class="row">

                                                                <div class="col-6">
                                                                        <div class="input-field">
                                                                                <label>
                                                                                        {{'setting.Departure' | translate}}</label>
                                                                                <span class="input-icon material-icons"
                                                                                        (click)="openCheckinCalender(checkinDatePicker)">event</span>

                                                                                <input bsDatepicker
                                                                                        #checkinDatePicker="bsDatepicker"
                                                                                        class="inputForAirlines"
                                                                                        formControlName="itemTravelStart"
                                                                                        [minDate]="minimumDate1"
                                                                                        
                                                                                        [outsideClick]="true"
                                                                                        [bsConfig]="{showWeekNumbers: false, dateInputFormat: 'DD MMM YYYY'}"
                                                                                        (onShown)="onShowPicker($event, checkinDatePicker)"
                                                                                        
                                                                                        (onHidden)="onHidePicker()"
                                                                                        readonly />
                                                                                
                                                                        </div>
                                                                </div>
                                                                <div class="col-6">
                                                                        <div class="input-field">
                                                                                <label>
                                                                                        {{'setting.Arrival' | translate}}</label>
                                                                                <span class="input-icon material-icons"
                                                                                        (click)="openCheckinCalender(checkoutDatePicker)">event</span>

                                                                                <input bsDatepicker
                                                                                        #checkoutDatePicker="bsDatepicker"
                                                                                        class="inputForAirlines"
                                                                                        formControlName="itemTravelEnd"
                                                                                        [minDate]="minimumDate3"
                                                                                     
                                                                                        [outsideClick]="true"
                                                                                        [bsConfig]="{showWeekNumbers: false, dateInputFormat: 'DD MMM YYYY'}"
                                                                                        (onShown)="onShowPicker($event, checkoutDatePicker)"
                                                                                        (bsValueChange)="setItemTravelEnd($event)"
                                                                                        (onHidden)="onHidePicker()"
                                                                                        readonly />

                                                                               
                                                                        </div>
                                                                </div>
                                                        </div>
                                                </div>
                                                <div class="rentalButtonConatiner">
                                                        <div class="row">
                                                                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

                                                                        <div class="checkbox-container1"
                                                                                style="margin-bottom:0px !important; margin-top: 0 !important;">
                                                                                <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect"
                                                                                        for="arrivalTimeCheck">
                                                                                        <input type="checkbox"
                                                                                                id="arrivalTimeCheck"
                                                                                                class="mdl-checkbox__input"
                                                                                                value="arrivalTimeCheck"
                                                                                                (change)="changeArrivalTime($event.target.checked,'arrival')"
                                                                                                [checked]="arrivalTimeCheck">
                                                                                        <span class="mdl-checkbox__label"
                                                                                                style="font-family: var(--globalFontfamilyr);">{{'setting.Suggestlatestarrivaltime' | translate}}</span>
                                                                                </label>
                                                                        </div>

                                                                </div>
                                                                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12"
                                                                        style="display: flex;">
                                                                        <div class="col-6">
                                                                                <div id="addAirlineList"
                                                                                        style="width: 100%;height: 40px;margin-bottom: 20px;">
                                                                                        <div class="inputForAirlines"
                                                                                                style="background: #F7F7F9 !important;width: 100% !important;"
                                                                                                [ngStyle]="{'border-color':  !arrivalTimeCheck ? '#E7E6E4':this.searchService.darkBgColor}">
                                                                                                <ng-select #airlineListt
                                                                                                        appendTo="#addAirlineList"
                                                                                                        dropdownPosition="bottom"
                                                                                                        [searchable]="false"
                                                                                                        [clearable]="false"
                                                                                                        [items]="timeOptions | translateOptions"
                                                                                                        bindValue="titleID"
                                                                                                        bindLabel="title"
                                                                                                        (open)="getTimeOptions()"
                                                                                                        formControlName="arrivalTimeFilter">
                                                                                                        <ng-template
                                                                                                                ng-label-tmp
                                                                                                                let-item="item">
                                                                                                                <span class="cardName"
                                                                                                                        style="top:-2px;max-width: 300px;">{{ getTimeOptionValue(item) | translate}}</span>
                                                                                                        </ng-template>
                                                                                                        <ng-template
                                                                                                                ng-header-tmp>
                                                                                                                <div
                                                                                                                        class="selectox-header">
                                                                                                                        <span>{{'search.SelectTime' | translate }}</span>
                                                                                                                        <span class="selectBox-remove"
                                                                                                                                ><span
                                                                                                                                        class="material-icons">clear</span></span>
                                                                                                                </div>
                                                                                                        </ng-template>
                                                                                                        <ng-template
                                                                                                                ng-option-tmp
                                                                                                                let-option="item">
                                                                                                                <span
                                                                                                                        class="option-title">{{option.title |
                                                                                                                    translate}}</span>

                                                                                                        </ng-template>
                                                                                                </ng-select>
                                                                                                <div
                                                                                                        class="select-overlay">
                                                                                                </div>
                                                                                        </div>
                                                                                        <svg class="down-arrow11" *ngIf="!this.arrivalTimeCheck" (click)="airlineListt.toggle()" style="left: 175px;top: -28px;" width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                                                                d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                                                                                fill="gray" /></svg>
                                                                                        <svg class="down-arrow11" *ngIf="this.arrivalTimeCheck"
                                                                                                (click)="airlineListt.toggle()"
                                                                                                style="left: 175px;top: -28px;"
                                                                                                width="15" height="9"
                                                                                                viewBox="0 0 15 9"
                                                                                                fill="none"
                                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                                <path fill-rule="evenodd"
                                                                                                        clip-rule="evenodd"
                                                                                                        d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                                                                                        fill="#8936F3" />
                                                                                        </svg>

                                                                                </div>
                                                                        </div>
                                                                        <div class="col-6">
                                                                                <div class="input-field">

                                                                                        <span class="input-icon material-icons"
                                                                                                (click)="openCheckinCalender(checkoutDatePicker)">event</span>

                                                                                        <input bsDatepicker
                                                                                                #checkoutDatePicker="bsDatepicker"
                                                                                                [ngStyle]="{'border-color':  !this.arrivalTimeCheck ? '#E7E6E4':this.searchService.darkBgColor}"
                                                                                                class="inputForAirlines"
                                                                                                formControlName="suggestedStart"
                                                                                                [minDate]="minimumDate1"
                                                                                                
                                                                                                [outsideClick]="true"
                                                                                                [bsConfig]="{showWeekNumbers: false, dateInputFormat: 'DD MMM YYYY'}"
                                                                                                (onShown)="onShowPicker($event, checkoutDatePicker)"
                                                                                                (bsValueChange)="setsuggestedStart($event)"
                                                                                                (onHidden)="onHidePicker()"
                                                                                                readonly />

                                                                                       
                                                                                </div>
                                                                        </div>
                                                                </div>

                                                        </div>
                                                </div>
                                                <div class="rentalButtonConatiner">
                                                        <div class="row">
                                                                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">

                                                                        <div class="checkbox-container1"
                                                                                style="margin-bottom:0px !important; margin-top: 0 !important;">
                                                                                <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect"
                                                                                        for="departureTimeCheck">
                                                                                        <input type="checkbox"
                                                                                                id="departureTimeCheck"
                                                                                                class="mdl-checkbox__input"
                                                                                                value="departureTimeCheck"
                                                                                                (change)="changeArrivalTime($event.target.checked,'departure')"
                                                                                                [checked]="departureTimeCheck">
                                                                                        <span class="mdl-checkbox__label"
                                                                                                style="font-family: var(--globalFontfamilyr);">{{'setting.Suggestearliestreturningtime' | translate}}</span>
                                                                                </label>
                                                                        </div>

                                                                </div>
                                                                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12"
                                                                        style="display: flex;">
                                                                        <div class="col-6">
                                                                                <div id="addDepartmentList"
                                                                                        style="width: 100%;height: 40px;margin-bottom: 20px;">
                                                                                        <div class="inputForAirlines"
                                                                                                style="background: #F7F7F9 !important;width: 100% !important;"
                                                                                                [ngStyle]="{'border-color':  !this.departureTimeCheck ? '#E7E6E4':this.searchService.darkBgColor}">
                                                                                                <ng-select #airlineListt
                                                                                                        appendTo="#addDepartmentList"
                                                                                                        dropdownPosition="bottom"
                                                                                                        [searchable]="false"
                                                                                                        [clearable]="false"
                                                                                                        [items]="timeOptions | translateOptions"
                                                                                                        bindValue="titleID"
                                                                                                        bindLabel="title"
                                                                                                        (open)="getTimeOptions()"
                                                                                                        formControlName="departureTimeFilter">
                                                                                                        <ng-template
                                                                                                                ng-label-tmp
                                                                                                                let-item="item">
                                                                                                                <span class="cardName"
                                                                                                                        style="top:-2px;max-width: 300px;">{{ getTimeOptionValue(item) | translate}}</span>
                                                                                                        </ng-template>
                                                                                                        <ng-template
                                                                                                                ng-header-tmp>
                                                                                                                <div
                                                                                                                        class="selectox-header">
                                                                                                                        <span>{{'search.SelectTime' | translate }}</span>
                                                                                                                        <span class="selectBox-remove"
                                                                                                                               ><span
                                                                                                                                        class="material-icons">clear</span></span>
                                                                                                                </div>
                                                                                                        </ng-template>
                                                                                                        <ng-template
                                                                                                                ng-option-tmp
                                                                                                                let-option="item">
                                                                                                                <span
                                                                                                                        class="option-title">{{option.title |
                                                                                                             translate}}</span>

                                                                                                        </ng-template>
                                                                                                </ng-select>
                                                                                                <div
                                                                                                        class="select-overlay">
                                                                                                </div>
                                                                                        </div>
                                                                                        <svg class="down-arrow11" *ngIf="!this.departureTimeCheck" (click)="airlineListt.toggle()" style="left: 175px;top: -28px;" width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                                                                d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                                                                                fill="gray" /></svg>
                                                                                        <svg class="down-arrow11" *ngIf="this.departureTimeCheck"
                                                                                                (click)="airlineListt.toggle()"
                                                                                                style="left: 175px;top: -28px;"
                                                                                                width="15" height="9"
                                                                                                viewBox="0 0 15 9"
                                                                                                fill="none"
                                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                                <path fill-rule="evenodd"
                                                                                                        clip-rule="evenodd"
                                                                                                        d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                                                                                        fill="#8936F3" />
                                                                                        </svg>

                                                                                </div>
                                                                        </div>
                                                                        <div class="col-6">
                                                                                <div class="input-field">

                                                                                        <span class="input-icon material-icons"
                                                                                                (click)="openCheckinCalender(checkoutDatePicker)">event</span>

                                                                                        <input bsDatepicker
                                                                                                #checkoutDatePicker="bsDatepicker"
                                                                                                [ngStyle]="{'border-color':  !this.departureTimeCheck ? '#E7E6E4':this.searchService.darkBgColor}"
                                                                                                class="inputForAirlines"
                                                                                                formControlName="suggestedEnd"
                                                                                                [minDate]="minimumDate4"
                                                                                               
                                                                                                [outsideClick]="true"
                                                                                                [bsConfig]="{showWeekNumbers: false, dateInputFormat: 'DD MMM YYYY'}"
                                                                                                (onShown)="onShowPicker($event, checkoutDatePicker)"
                                                                                                (bsValueChange)="setsuggestedEnd($event)"
                                                                                                (onHidden)="onHidePicker()"
                                                                                                readonly />

                                                                                        <div *ngIf="this.eventCreateForm.controls['end'].hasError('required') && (this.eventCreateForm.controls['end'].touched || this.eventCreateForm.controls['end'].dirty)"
                                                                                                class="error">
                                                                                                {{'login.thisfieldisrequired' | translate}}
                                                                                        </div>
                                                                                </div>
                                                                        </div>
                                                                </div>


                                                        </div>
                                                </div>
                                                <div class="rentalButtonConatiner" 
                                                *ngIf="this.airports && this.airports.length > 0">
                                                <label class="input-label primary-label">{{'setting.DestinationAirport' | translate}}
                                                </label>
                                                <div>
                                                <div *ngFor="let item of this.airports">
                                                        <label class="mdl-radio mdl-js-radio selection-checkbox checkbox2 mdl-js-ripple-effect is-checked"
                                                                [ngClass]="{'is-checked':isAirportChecked(item.code)}"
                                                                for="{{item.code}}">
                                                                <input value="{{item.code}}" type="checkbox"
                                                                        style="display: none;"
                                                                        id="{{item.code}}"
                                                                        formControlName="destinationAirports"
                                                                        class="mdl-radio__button"
                                                                        (change)="setSelectedHomeAirport($event.target.checked,item.code)"
                                                                        [checked]="isAirportChecked(item.code)">
                                                                <span
                                                                        class="mdl-radio__label">{{item.code}}</span>
                                                        </label>
                                                </div>
                                                <br>
                                                <div *ngIf="this.fullEventCreateForm.controls['destinationAirports'].hasError('required') && (this.fullEventCreateForm.controls['destinationAirports'].touched || this.fullEventCreateForm.controls['destinationAirports'].dirty)"
                                                        class="error" style="width: fit-content;">
                                                        {{'login.thisfieldisrequired' | translate}}</div>
                                                        </div>
                                        </div>
                                                <div class="rentalButtonConatiner" style="">
                                                        <hr>
                                                        <div class="heading">
                                                                {{'setting.Othersetting' | translate}}
                                                        </div>

                                                        <div class="input-label primary-label">
                                                                {{'setting.Travelpolicy' | translate}}
                                                        </div>

                                                       
                                                        <div class="row"  style="padding-left: 20px;">
                                                                <div id="policylistDiv" style="min-width: 353px;">
                                                                        <div class="inputForAirlines"
                                                                                style="background: #F7F7F9 !important;width: 100% !important;"
                                                                                [ngStyle]="{'border-color':  false ? '#E7E6E4':this.searchService.darkBgColor}">
                                                                                <ng-select #policylist
                                                                                        appendTo="#policylistDiv"
                                                                                        style="bottom: 5px;"
                                                                                        dropdownPosition="middle"
                                                                                        [searchable]="false"
                                                                                        [clearable]="false"
                                                                                        formControlName="policyId"
                                                                                        [items]="policyOptions"
                                                                                        bindLabel="value"
                                                                                        bindValue="id">
                                                                                </ng-select>
                                                                                <div class="select-overlay"></div>
                                                                        </div>
                                                                        <svg class="down-arrow11"
                                                                                (click)="policylist.toggle()"
                                                                                style="top: -22px;position:relative;float:right;right:20px;"
                                                                                width="15" height="9" viewBox="0 0 15 9"
                                                                                fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path fill-rule="evenodd"
                                                                                        clip-rule="evenodd"
                                                                                        d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                                                                        fill="#8936F3" />
                                                                        </svg>
                                                                </div>
                                                               
                                                               
                                                        </div>
                                                        <div  style="padding-left: 20px;z-index: 9;position: relative;" class="row" >
                                                                        <button class="btn btn-normal" (click)="routeToPolicy()"
                                                                        style="width:270px !important;text-align: left !important;margin-top: 0px;height: 40px;"><span class="link-text addlue"
                                                                          style="font-weight: bold !important;margin-left: 0px;"
                                                                          >  {{ 'policy.AddNewPolicy' | translate}}</span></button>
                                                        </div>
                                                        <div class="input-label primary-label"
                                                                style="margin-top: 10px;">
                                                                {{'navigation.Approvals' | translate}}
                                                        </div>

                                                        
                                                        <div class="row" 
                                                                style="margin-top:5px;padding-left: 20px;">
                                                                <div id="selectApproverDiv" style="width: 353px;">
                                                                        <div class="inputForAirlines"
                                                                                style="background: #F7F7F9 !important;width: 100% !important;"
                                                                                [ngStyle]="{'border-color':  false ? '#E7E6E4':this.searchService.darkBgColor}">
                                                                                <ng-select #approverlist
                                                                                        appendTo="#selectApproverDiv"
                                                                                        style="bottom: 5px;"
                                                                                        (click)="this.dropDownopen =true;filterSelectedlist(this.multipleSelectedApprover);"
                                                                                        dropdownPosition="middle"
                                                                                        [searchable]="true"
                                                                                        (close)="closeDropdown()"
                                                                                        [closeOnSelect]="false"
                                                                                        bindValue=""
                                                                                        bindLabel=""
                                                                                        formControlName="approvers"
                                                                                        [clearable]="false"
                                                                                        [items]="this.appproverList"
                                                                                        [searchFn]="searchByApproverNameAndEmailChanged"
                                                                                       
                                                                                       >
                                                                                        <ng-template ng-label-tmp let-item="item" *ngIf="this.multipleSelectedApprover.length ===0">
                                                                                                <span>
                                                                                                        {{getLabelValue() | translate}}</span>
                                                                                        </ng-template>
                                                                                        <ng-template ng-option-tmp
                                                                                                let-item="item"
                                                                                                let-search="searchTerm"
                                                                                                let-index="index">
                                                                                                <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect"
                                                                                                        for="{{item.email}}"
                                                                                                        style="height: 36px !important;min-width: 331px !important;margin-bottom: 0px !important;">
                                                                                                        <input type="checkbox"
                                                                                                                id="{{item.email}}"
                                                                                                               
                                                                                                                class="mdl-checkbox__input"
                                                                                                                (change)="onApproverChangeClicked(item.email, $event)"
                                                                                                                
                                                                                                                [checked]="isApproverChecked(item.email)"
                                                                                                                >
                                                                                                        <span   class="mdl-checkbox__label"
                                                                                                                style="font-family: var(--globalFontfamilyr) !important;padding-left: 10px !important;font-size: 14px;top: -2px;padding-bottom: 20px;"><span
                                                                                                                        style="width: 100px !important;min-width: 100px !important;text-overflow: ellipsis;display: inline-block;overflow: hidden;position: relative;
                                                                                              top: 2px; left: 20px;;">{{item.firstName}}
                                                                                                                        {{item.lastName}}</span>
                                                                                                                <span *ngIf="item.email !=='-1'"
                                                                                                                        style="text-align:right;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;margin-left:20px;">
                                                                                                                        {{item.email}}</span></span>
                                                                                                </label>

                                                                                        </ng-template>
                                                                                </ng-select>
                                                                                <div class="select-overlay"></div>
                                                                                <div *ngIf="this.multipleSelectedApprover.length > 0 && !this.dropDownopen" class="modalApprover" (click)="approverlist.toggle();this.dropDownopen =true;filterSelectedlist(this.multipleSelectedApprover);" [ngStyle]="{'top' : this.adminPanelService.employeePopOpen ? '-60px':'-80px'}">
                                                                                                <span *ngFor="let item1 of this.multipleSelectedApprover;let i=index">
                                                                                <span *ngIf="item1!='-1'"class="shown1">{{item1}}<span *ngIf="((i !== (this.multipleSelectedApprover.length-1)))">,
                                                                                        </span></span> <span *ngIf="item1==='-1'"class="shown1">{{this.appproverList[0].firstName}}</span>
                                                                                                </span>
                                                                                            </div>
                                                                               
                                                                        </div>
                                                                        <svg class="down-arrow11"
                                                                                (click)="approverlist.toggle();this.dropDownopen =true;filterSelectedlist(this.multipleSelectedApprover);"
                                                                                style="top: -22px;position:relative;float:right;right:20px;"
                                                                                width="15" height="9" viewBox="0 0 15 9"
                                                                                fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path fill-rule="evenodd"
                                                                                        clip-rule="evenodd"
                                                                                        d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                                                                        fill="#8936F3" />
                                                                        </svg>

                                                                </div>
                                                                <div *ngIf="this.fullEventCreateForm.controls['approvers'].hasError('required') && (this.fullEventCreateForm.controls['approvers'].touched || this.fullEventCreateForm.controls['approvers'].dirty)"
                                                                class="error">
                                                                {{'login.thisfieldisrequired' | translate}}</div>
                                                              
                                                        </div>
                                                        <div class="input-label primary-label"
                                                                style="margin-top: 10px;">
                                                                {{'dashboardWrapper.PaymentMethods' | translate}}
                                                        </div>

                                                      
                                                        <div class="row" 
                                                                style="margin-top:5px;width: 353px;position: relative;">
                                                                <div id="cardList" (click)="openNgxModal('cardList');" style="width: 353px;margin-top: 5px;margin-left: 18px;">
                                                                                <div class="inputForAirlines" style="background: #F7F7F9 !important;">
                                                                                  <div style="position:relative;">
                                                                                        <input type="text" style="display: none" formControName="payment">
                                                                                                <span class="cardName"  style="top:3px;max-width: 100%;">{{getCardName(this.cardValue) | translate}}</span>
                                                                                                <div *ngIf="this.cardValue && this.cardValue.id" class="brand-last4"> <img class="inlineblock_m" *ngIf="this.cardValue && this.cardValue.id  && this.cardValue.id !==-1&& getBrandName(this.cardValue.id)!==''" style="width:35px;position: relative;
                                                                                      top: -8px;" src="https://s3.amazonaws.com/images.biztravel.ai/cc/{{getBrandName(this.cardValue.id)}}.svg" />
                                                                                                    <span style="position: relative;top: -6px;margin-right: 6px;">{{getBrandName(this.cardValue.id)}}</span><span class="cardName" style="position: relative;top: 0px;max-width:100px !important;">{{getLastFour(this.cardValue.id)}}</span></div>
                                                                                   </div>
                                                                                </div>
                                                                                
                                                                                        <svg class="down-arrow11" style="left: -15px !important;top: -28px;" width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                                  <path fill-rule="evenodd" clip-rule="evenodd"
                                                                                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                                                                    fill="#8936F3" />
                                                                                </svg>
                                                                              </div>
                                                                                    <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'cardList')" [hideDelay]="0" (onClose)="handleModalEvents('onClose', 'cardList')" (onDismiss)="handleModalEvents('onDismiss', 'cardList')" [closable]="false" #cardList identifier="cardList">
                                                                                        <div class="modal-container flight-modal-container filter-modal1 modalAirportFilterInfo" (click)="$event.stopPropagation();">
                                                                                <div class="modal-header" style=" background: #fff !important;padding-top:10px !important;">
                                                                                    <div *ngIf="!addCardMode" id="addCard" class="add-card-link" style="margin-top:5px ;">
                                                                                                    <a class="link-primary" href="javascript:void(0);" onclick="addCardForm(this);" (click)="setCardMode(showAddCardModal);">
                                                                                          <i class="fa fa-plus link-icon"></i>
                                                                                          <span class="link-text" style="margin-left: 5px;">{{'paymentDetails.AddNewcard' | translate}}</span>
                                                                                        </a>
                                                                                      </div>
                                                                                </div>
                                                                              
                                                                                <div class="modal-body1">
                                                                                    <div   *ngFor="let item of this.card_list,let rIndex = index;">
                                                                                      <div  class="{{(rIndex%2==0) ? 'row2':'row1'}}">
                                                                                       <div  (click)="updatePaymentMethod(item.id,'cardList')" style="cursor: pointer;"> <span class="cardName1" style="top:0px;">{{item.name | translate}}</span> </div> 
                                                                                                        <div style="cursor: pointer;display: flex;justify-content: space-between;"><span style="width: 90%;" (click)="updatePaymentMethod(item.id,'cardList')"><img  *ngIf="item.id !==-1"class="inlineblock_m" style="width:35px;position: relative;top: -5px;
                                                                                                      " src="https://s3.amazonaws.com/images.biztravel.ai/cc/{{item.brand}}.svg" />
                                                                                                        <span style="margin-right: 6px;" class="cardName1">{{item.brand}}</span>{{item.last4}}</span>
                                                                                                           </div> 
                                                                                                        
                                                                                      <div>
                                                                                        </div>
                                                                
                                                                                      </div>
                                                                                   
                                                                                  </div>
                                                                                  </div>
                                                                              </div>
                                                                            </ngx-smart-modal>
                                                                            <div *ngIf="this.fullEventCreateForm.controls['payment'].hasError('required') && (this.fullEventCreateForm.controls['payment'].touched || this.fullEventCreateForm.controls['payment'].dirty)"
                                                                class="error">
                                                                {{'login.thisfieldisrequired' | translate}}</div>
                                                        </div>


                                                </div>
                                                <div class="add-card-div">
                                                                <add-card-widget *ngIf="addCardMode && this.isMobile1" [changing]="changingValue" 
                                                                     [loggedIn]="isLoggedIn()" [hideGoBackButton]="false" [cardButtonStyle]="true" (goBackEmitter)='handleBackFromAddCard($event)'></add-card-widget>
                                                                </div>
                                        </form>
                                        <div class="rentalButtonConatiner">
                                                <button *ngIf="!addEventProgress" class="btn button-primary" style="z-index: 9;position: relative;"
                                                        (click)="updateEvent()">{{'setting.Saveandcontinue' | translate}}</button>

                                                <button *ngIf="addEventProgress" class="button button-primary">{{'addCard.wait' |
                                                                                    translate}} <span class="loaderCarRental">
                                                                                                <loader-dots class="loaderAlign"></loader-dots>
                                                                                        </span></button>
                                               
                                                <button *ngIf="!addDeleteProgress" class="btn button-primaryDelete"
                                                (click)="deleteEvent()">{{'setting.DeleteEvent' | translate}}</button>
                                                <button *ngIf="addDeleteProgress" class="button button-primaryDelete">{{'addCard.wait' |
                                                                translate}}</button>
                                                <span *ngIf="addDeleteProgress" class="loaderCarRental">
                                                                <loader-dots class="loaderAlign"></loader-dots>
                                                        </span>

                                        </div>
                                </div>
                                <div *ngIf="viewSubMode2==='tab12'" class="tab-content">
                                                <div class="row">
                                                <div class="col-6">
                                                                <div id="addAirlineList"
                                                                        style="width: 100%;height: 40px;margin-bottom: 20px;">
                                                                        <div class="inputForAirlines"
                                                                                style="background: #F7F7F9 !important;width: 100% !important;"
                                                                                [ngStyle]="{'border-color':  false ? '#E7E6E4':this.searchService.darkBgColor}">
                                                                                <ng-select #airlineListt
                                                                                        appendTo="#addAirlineList"
                                                                                        dropdownPosition="bottom"
                                                                                        [searchable]="true"
                                                                                        [clearable]="false"
                                                                                        (click)="isdropDownOpen()"
                                                                                        (close)="closeDropdown()"
                                                                                        [closeOnSelect]="false"
                                                                                        [items]="this.employeeList"
                                                                                        [searchFn]="searchByApproverNameAndEmailChanged"
                                                                                        bindValue=""
                                                                                        bindLabel=""
                                                                                        [(ngModel)]="search"
                                                                                        
                                                                                        >
                                                                                        <ng-template *ngIf="!this.dropDownopen" ng-label-tmp let-item="item">
                                                                                                        <span style="top: -5px !important; position: relative;"> {{ 'search.AddTraveler' | translate }} </span>
                                                                                                    </ng-template>
                                                                                                    <ng-template ng-option-tmp
                                                                                                    let-item="item"
                                                                                                    let-search="searchTerm"
                                                                                                    let-index="index">
                                                                                                    <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect"
                                                                                                            for="{{item.email}}"
                                                                                                            style="height: 36px !important;min-width: 331px !important;margin-bottom: 0px !important;">
                                                                                                            <input type="checkbox"
                                                                                                                    id="{{item.email}}"
                                                                                                                    class="mdl-checkbox__input"
                                                                                                                    (change)="onAttendeeChangeClicked(item.email, $event)"
                                                                                                                    [checked]="isAttendeeChecked(item.email)">
                                                                                                            <span class="mdl-checkbox__label"
                                                                                                                    style="font-family: var(--globalFontfamilyr) !important;padding-left: 10px !important;font-size: 14px;top: -2px;padding-bottom: 20px;"><span
                                                                                                                            style="max-width: 150px !important;min-width: 150px !important;text-overflow: ellipsis;display: inline-block;overflow: hidden;position: relative;
                                                                                                  top: 2px; left: 20px;;">{{item.firstName}}
                                                                                                                            {{item.lastName}}</span> <span
                                                                                                                            style="max-width:230px;min-width:230px;text-align:left;text-overflow: ellipsis;display: inline-block;white-space: nowrap;overflow: hidden;margin-left:20px;">
                                                                                                                            {{item.email}}</span>
                                                                                                                            <span *ngIf="item.employeeInfo && item.employeeInfo.employeeIdentifier"
                                                                                                                            style="max-width:120px;min-width:120px;text-align:left;text-overflow: ellipsis;display: inline-block;white-space: nowrap;overflow: hidden;margin-left:10px;">
                                                                                                                            {{item.employeeInfo.employeeIdentifier}}</span>
                                                                                                                 </span>
                                                                                                    </label>
    
                                                                                            </ng-template>
                                                                                       
                                                                                <div class="travelerAddIcon" (click)="airlineListt.toggle();"><i class="fa fa-user-plus"
                                                                                        aria-hidden="true"></i></div>     
                                                                                </ng-select>
                                                                                <div
                                                                                        class="select-overlay">
                                                                                </div>
                                                                        </div>
                                                                        <svg class="down-arrow11"
                                                                                (click)="airlineListt.toggle()"
                                                                                style="left: 175px;top: -28px;"
                                                                                width="15" height="9"
                                                                                viewBox="0 0 15 9"
                                                                                fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path fill-rule="evenodd"
                                                                                        clip-rule="evenodd"
                                                                                        d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                                                                        fill="#8936F3" />
                                                                        </svg>

                                                                </div>
                                                        </div>
                                                        </div>
                                                        <div *ngIf="this.selectedEmployeeList.length > 0" class="section">
                                                                        <div class="heading">
                                                                                        {{'setting.Atendeelist' | translate}}
                                                                                </div>
                                                                        <div class="section" >
                                                                                        <div class="filter-row" style="justify-content: space-between !important;background-color: #DBDBDB !important;padding-top:10px;height:42px;">
                                                                                        <div class="col-auto" style="width:300px;margin-right:4px !important;">
                                                                                          <span class="show2"> {{ 'setting.participant' | translate}}</span>
                                                                                        </div>
                                                                                       
                                                                                        <div class="col-auto" style="width:400px;margin-right:4px !important;">
                                                                                            <span class="show2"> {{ 'setting.Status' | translate}}</span>
                                                                                          </div>
                                                                                         
                                                                                          
                                                                                              <div class="col-auto" style="width:60px;margin-right:4px !important;">
                                                                                                  <span class="show2"> </span>
                                                                                                </div>
                                                                                        </div>
                                                                    
                                                                                 
                                                                                   
                                                                                    </div>
                                                                                    <div class="section" >
                                                                                        <div *ngFor="let item1 of this.selectedEmployeeList,let j=index" class="{{j%2==0 ? 'filter-row4':'filter-row3'}}">
                                                                                            <div class="col-auto" style="width:300px;margin-right:4px !important;">
                                                                                                <span class="show1" style="width:300px;" > {{getNameOFAttendee(item1.userid)}}
                                                                                                               </span>
                                                                                              </div>
                                                                                              
                                                                                             
                                                                                                <div class="col-auto" style="width:400px;margin-right:4px !important;">
                                                                                                                <span class="show1"  *ngIf="item1.status==='NOT_INVITED'" >{{ 'setting.Invitationpending' | translate}}</span>
                                                                                                                <span class="show1"  *ngIf="item1.status==='INIVITATION_ACCEPTED'"  style="width:200px;max-width: 200px;" > {{ 'setting.Invitationaccepted' | translate}}</span>
                                                                                                                <span class="show1"  *ngIf="item1.status==='PARTIALLY_BOOKED'"  style="width:200px;max-width: 200px;" > {{ 'setting.Partiallybooked' | translate}}</span>
                                                                                                                <span class="show1"  *ngIf="item1.status==='BOOKED'"  style="width:200px;max-width: 200px;" > {{ 'setting.Booked' | translate}}</span>
                                                                                                    <span class="show1 addlue"  *ngIf="item1.status==='NOT_INVITED'" style="width:200px;max-width: 200px;cursor: pointer;" (click)="inviteAttendee(item1,j)"> {{ 'setting.Invite' | translate}}</span>
                                                                                                    <span class="show1 addlue"  *ngIf="item1.status==='INVITED'" style="width:200px;max-width: 200px;cursor: pointer;" (click)="inviteAttendee(item1,j)"> {{ 'setting.Reinvite' | translate}}</span>
                                                                                                    <span class="show1"   *ngIf="item1.status==='INIVITATION_DECLINED'" style="width:200px;max-width: 200px;"> {{ 'setting.Decline' | translate}}</span>
                                                                                                    <span class="show1"   *ngIf="item1.status==='BOOKED_ALTERNATE'" style="width:auto;"> {{ 'setting.DeclineIvebookedtraveloutsideRoutespring' | translate}}</span>
                                                                                                    <span  class="invitationLink{{j}}" style="margin-left: 5px;display: none;"class="loaderCarRental">
                                                                                                                <loader-dots class="loaderAlign"></loader-dots>
                                                                                                        </span>
                                                                                                  </div>
                                                                                               
                                                                                                    <div class="col-auto" style="width:60px;margin-right:4px !important;">
                                                                                                       
                                                                                                        <img src="assets/images/ic_delete.svg" (click)="deleteEmployeeOfEvent(item1.userid,j)" style="cursor: pointer;">  
                                                                                                                        <span class="deletionLink{{j}}" style="margin-left: 5px;display: none;" class="loaderCarRental">
                                                                                                                                <loader-dots class="loaderAlign"></loader-dots>
                                                                                                                        </span>
                                                                                                                        
                                                                                                      </div>
                                                                                          </div>
                                                                                   
                                                                                    </div>
                                                        </div>
                                                        <div style="margin-top: 20px;text-align: center;">
                                                                <app-loader *ngIf="this.addingEmployee" [spinnerStyle]="true"></app-loader>
                                                        </div>

                                                        
                                </div>
                                <div *ngIf="viewSubMode2==='tab13'" class="tab-content">
                                        <div class="section"  *ngIf="this.eventBookingList.length === 0">
                                                 {{ 'setting.NobookingsFound' | translate }}
                                        </div>
                                                <div *ngIf="this.eventBookingList.length > 0" class="section">
                                                        <div  class="heading">
                                                                {{ 'setting.BookingList' | translate }}  
                                                        </div>
                                                        <div class="section">
                                                                <div class="filter-row"
                                                                        style="background-color: #DBDBDB !important;padding-top:10px;height:auto;">
                                                                        <div class="col-auto" style="width:180px !important;margin-right:4px !important;">
                                                                                <span class="show2"> {{ 'setting.participant' | translate}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:150px !important;margin-right:4px !important;">
                                                                                <span class="show2"> {{ 'setting.Department' | translate}} </span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:150px !important;margin-right:4px !important;">
                                                                                <span class="show2">{{ 'setting.Email' | translate}} </span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:170px !important ;margin-right:4px !important;">
                                                                                <span class="show2" style="max-width: 170px;white-space: pre-line !important;"> {{ 'setting.OutgoingArrivalDate' | translate}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:170px !important;margin-right:4px !important;">
                                                                                <span class="show2" style="max-width: 170px;white-space: pre-line !important;"> {{ 'setting.OutgoingArrivalAirport' | translate}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:170px !important;margin-right:4px !important;">
                                                                                <span class="show2" style="max-width: 170px;white-space: pre-line !important;"> {{ 'setting.Outgoingarrivalflightnumber' | translate}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:180px !important;margin-right:4px !important;">
                                                                                <span class="show2" style="max-width: 180px;white-space: pre-line !important;">{{ 'setting.ReturningDepartureDate' | translate}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:170px !important;margin-right:4px !important;">
                                                                                <span class="show2" style="max-width: 170px;white-space: pre-line !important;"> {{ 'setting.Returningdepartureairport' | translate}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:170px !important;margin-right:4px !important;">
                                                                                <span class="show2" style="max-width: 170px;white-space: pre-line !important;"> {{ 'setting.Returningdepartureflightnumber' | translate}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:100px !important;margin-right:4px !important;">
                                                                                <span class="show2" style="max-width: 100px;white-space: pre-line !important;">{{ 'setting.EventStatus' | translate}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:120px !important;margin-right:4px !important;">
                                                                                <span class="show2"style="max-width: 120px;white-space: pre-line !important;">{{ 'setting.CostofBooking' | translate}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:60px !important;margin-right:4px !important;">
                                                                                <span class="show2"style="max-width: 60px;white-space: pre-line !important;"></span>
                                                                        </div>
                                                                </div>
                                                
                                                
                                                
                                                        </div>
                                                        <div class="section">
                                                                <div *ngFor="let item1 of this.eventBookingList,let j=index"
                                                                        class="{{j%2==0 ? 'filter-row4':'filter-row3'}}">
                                                                        <div class="col-auto" style="width:180px;margin-right:4px !important;">
                                                                                <span class="show1" style="width:180px;"> {{getNameOFAttendee(item1.userid)}}
                                                                                </span>
                                                                        </div>
                                                
                                                                        <div class="col-auto" style="width:150px !important;margin-right:4px !important;">
                                                                                
                                                                                        <span  class="showDept" style="width:150px;">
                                                                                                <div class="labelMSg" >
                                                                                                                <span class="tooltiparrow"></span><span >
                                                                                                                        {{getDepartmentName(item1.userid)}}
                                                                                                                </span>
                                                                                                              </div>
                                                                                                              {{getDepartmentName(item1.userid)}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:150px !important;margin-right:4px !important;">
                                                                                        <span  class="showDept" style="width:150px;">
                                                                                                <div class="labelMSg" >
                                                                                                                <span class="tooltiparrow"></span><span style="position: relative;
                                                                                                                top: -8px;">
                                                                                                                        {{item1.userid}}
                                                                                                                </span>
                                                                                                              </div>
                                                                                                              {{item1.userid}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:170px !important;margin-right:4px !important;">
                                                                                <span   class="show1" style="width:170px;">
                                                                                        {{ this.getDisplayDate(item1.outgoingArrivalTime)  | date: 'dd MMM yyyy hh:mm a' }}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:170px !important;margin-right:4px !important;">
                                                                                <span  class="show1" style="width:170px;">
                                                                                        {{item1.outgoingArrivalAirport}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:170px !important;margin-right:4px !important;">
                                                                                <span  class="show1" style="width:170px;">
                                                                                        {{item1.outgoingArrivalFlightNumber}}</span>
                                                                                </div>
                                                                        <div class="col-auto" style="width:170px !important;margin-right:4px !important;">
                                                                                <span  class="show1" style="width:170px;">
                                                                                        {{this.getDisplayDate(item1.returnDepartureTime)  | date: 'dd MMM yyyy hh:mm a' }}   </span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:180px !important;margin-right:4px !important;">
                                                                                <span  class="show1" style="width:170px;">
                                                                                        {{item1.returnDepartureAirport}}  </span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:170px !important;margin-right:4px !important;">
                                                                                <span  class="show1" style="width:170px;">
                                                                                        {{item1.returnDepartureFlightNumber}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:100px !important;margin-right:4px !important;">
                                                                                <span *ngIf="item1.bookingInviteStatus==='PARTIALLY_BOOKED'" class="showDept" style="width:100px;">
                                                                                                <div class="labelMSg" >
                                                                                                                <span class="tooltiparrow"></span><span>
                                                                                                                        {{ 'setting.Partiallybooked' | translate}}
                                                                                                                </span>
                                                                                                              </div>
                                                                                                              {{ 'setting.Partiallybooked' | translate}}</span>
                                                                                                              <span *ngIf="item1.bookingInviteStatus==='BOOKED'"  class="showDept" style="width:100px;">
                                                                                                                <div class="labelMSg" >
                                                                                                                                <span class="tooltiparrow"></span><span>
                                                                                                                                        {{ 'setting.Booked' | translate}}
                                                                                                                                </span>
                                                                                                                              </div>
                                                                                                                              {{ 'setting.Booked' | translate}}</span>
                                                                                        
                                                                        </div>
                                                                        
                                                                        <div class="col-auto" style="width:120px !important;margin-right:4px !important;">
                                                                                <span  class="show1" style="width:120px;">
                                                                                        {{item1.bookingAmount | currency : getCurrencySymbol(item1) : 'code' : '1.0-0'}}</span>
                                                                        </div>
                                                                        <div class="col-auto" style="width:60px !important;margin-right:4px !important;">
                                                                                <span  class="show1" style="width:60px;cursor: pointer;text-decoration: underline;" 
                                                                                        (click)="openItinerary(item1)">
                                                                                        {{item1.ticketnumber}}
                                                                                </span>
                                                                        </div>

                                                                </div>
                                                
                                                        </div>
                                                </div>
                                                <div style="margin-top: 20px;text-align: center;">
                                                        <app-loader *ngIf="this.addingEmployee" [spinnerStyle]="true"></app-loader>
                                                </div>

                                                
                        </div>

                        </div>

                </div>
        </div>
</div>

<ng-template #showAddCardModal let-modal>
                <div class="modal-header">
                  
                  <h5  class="modal-title">
                      {{'setting.AddNewcard' | translate}}
                  </h5>
                  
                  <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel1()">
                    <i class="material-icons" style="color:#fff;">close</i>
                  </button>
                </div>
                <div class="modal-body" style=" text-align: left !important;margin-left: auto;
                margin-right: auto;">
                
               
                    <div class="add-card-div">
        <add-card-widget *ngIf="addCardMode" [changing]="changingValue" 
             [loggedIn]="true" [hideGoBackButton]="false" [cardButtonStyle]="true" (goBackEmitter)='handleBackFromAddCard($event)'></add-card-widget>
                      </div>
                  <div class="modal-form-button" style="margin-bottom: 0px;margin-top: 20px;text-align: center;">
                    
                  </div>
                </div>
              </ng-template>