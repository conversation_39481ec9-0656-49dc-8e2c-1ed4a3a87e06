:host {
  width: 100vw;
}

.container1 {
  width: auto;
  height: 100% !important;
  margin-left: 35px;
}

.container {
  background-color: #FCFCFC;
  width: 100%;
}

.heading {
  margin-left: 100px;
  font-family: "apercu-mono";
  font-size: 30px;
}
.passwordConstraints{
  text-align: start;
  padding-left: 65px;
  
    position: absolute;
      top: -105px;
      font-size: 13px;
      background-color: white;
      border: 0.1px solid black;
      padding: 10px 5px;
      min-width: 350px;
      max-width: 350px;
      /* display: none; */
      border-radius: 10px;
    //padding-left: 65px;
  
}
.constraints{
  font-size: 14px;
  display: flex;
  align-items: center;
  font-family: var(--globalFontfamilyr);font-weight: bold;
}
.map-image {
  text-align: left;
  margin-top: 5px;
  padding-left: 47px;
}

.linkstyle {
  cursor: pointer;
  color: var(--hyperlink-color);
  font-family: "apercu-mono";
}

.text-primary {
  cursor: pointer;
  font-family: "apercu-b";
  text-transform: none !important;
}

.login-container .text-primary {
  font-size: 14px !important;
}

.terms {
  color: var(--button-font-color);
  cursor: pointer;
  font-family: "apercu-b";
  text-transform: none !important;
  font-size: 14px !important;
}

.mdl-checkbox__box-outline {
  display: none !important;
}

.mdl-checkbox {
  height: auto;
  left: 50px !important;
  margin-top: 10px;
}

.mdl-checkbox__input {
  position: absolute;
  left: -9999px;
}

.mdl-checkbox__label {
  padding-left: 30px;
  position: relative;
  display: inline-block;
}

.mdl-checkbox__label:before,
.mdl-checkbox__label:after {
  height: 20px;
  width: 20px;
  position: absolute;
  left: 0;
  content: '';
  top: 0;
}

.mdl-checkbox__label:before {
  border: 1px solid #aeaeae;
  border-radius: 0px;
  background-color: #F7F7F7;
}

.mdl-checkbox__input:checked+.mdl-checkbox__label:after {
  background-image: url(/../../assets/images/check-icon.png);
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: center center;
}

.mdl-checkbox__label {
  font-size: 18px;
  line-height: normal;
  font-family: "apercu-r";
  margin-left: 0px;
  margin-top: 10px;
  padding-bottom: 8px;
  font-size: 16px;
}

.checkbox-container1 {
  float: left;
  width: 100%;
  margin-bottom: 25px;
  padding-left: 0px;
  margin-top: 25px;
}

.checkbox-container1 .checkboxlabel {
  color: #413E3B;
  font-family: "apercu-r";
  margin-left: 10px;
  font-size: 18px;
  line-height: 17px;
}

.input-box {
  float: left;
  width: 100%;
  margin-bottom: 10px;
  position: relative;
  margin-top: 10px;
}

.input-box input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: 50px;
  border: 2px solid #E7E6E4;
  font-family: "apercu-mono";
  background-color: #FFFFFF;
  width: 80%;
  padding: 0 17px 0 17px;
  font-size: 16px;
  line-height: 20px;
}

.button-container {
  margin-bottom: 30px !important;
}

.input-box-with-icon input {
  padding-left: 43px;
}

.input-box-icon-container {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  left: calc(12% + 10px) !important;
  height: 50px;
}

.button-container .button {
  margin-bottom: 16px;
}

.button-container .button:last-child {
  margin-bottom: auto;
}

.button-with-static-icon {
  position: relative;
  padding: 0 20px
}

.button-with-static-icon img {
  position: absolute;
  left: 5px;
  width: 30px;
  top: calc((100% - 30px)/2);
}

.button-container {
  width: 100%;
}

.button {
  cursor: pointer;
  border-radius: 2px !important;
  width: 80%;
  height: 50px;
  padding: 0 20px;
  border: none;
  text-transform: uppercase;
  font-size: 16px;
  letter-spacing: 0.86px;
}

.button-primary {
  color: var(--hyperlink-color);
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
}

.button-secondary {
  background: transparent;
  border: 2px solid #E7E6E4;
}

.button-white {
  border: 1px solid #E6E6E6;
  border-radius: 4px !important;
  background-color: #F5F5F5;
}

.form-image {
  width: calc(100% - 0px);
}

.redirect-div {
  text-align: right;
  width: 80%;
  font-family: "apercu-mono";
  margin: 10px 0;
  display: inline-block;
  font-size: 12px
}

.checkbox-container {
  float: left;
  width: 100%;
  margin-bottom: 25px;
  padding-left: 47px;
}

.checkbox-container .mdl-checkbox__label {
  font-size: 12px;
  line-height: 17px;
  margin-left: 18px;
}

.mdl-checkbox.is-checked .mdl-checkbox__box-outline {
  border: 1px solid var(--button-bg-color);
  ;
}

.mdl-checkbox.is-checked .mdl-checkbox__tick-outline {
  background-color: var(--button-bg-color) !important;
  ;
}

.mdl-checkbox {
  text-align: left;
  height: 100%;
  left: 20px !important;
}

.input-box-icon1-container {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  left: 55px !important;
  height: 50px;
}

@media (max-width:991px) {
  .heading {
    margin-left: 80px;
    font-family: "apercu-mono";
    font-size: 25px;
  }
}

@media (max-width:767px) {
  .mdl-checkbox__label {
    line-height: normal;
    font-family: "apercu-r";
    margin-left: 0px;
    margin-top: 10px;
    padding-bottom: 8px;
    font-size: 12px;
  }
  .passwordConstraints{
    text-align: start;
    padding-left: 65px;
    
      position: absolute;
        top: -105px;
        font-size: 13px;
        background-color: white;
        border: 0.1px solid black;
        padding: 10px 5px;
        min-width: 300px;
        max-width: 300px;
        /* display: none; */
        border-radius: 10px;
      //padding-left: 65px;
    
  }
  .mdl-checkbox__label:before,
  .mdl-checkbox__label:after {
    height: 16px;
    width: 16px;
    position: absolute;
    left: 0;
    content: '';
    top: 0;
  }

  .heading {
    margin-left: 30px;
    font-family: "apercu-mono";
    font-size: 18px;
  }

  header.inner {
    height: 90px;
  }

  .button-with-static-icon {
    padding: 0 40px;
  }

  .button-with-static-icon img {
    left: 10px;
  }

  .button-with-static-icon img {
    position: absolute;
    left: 5px;
    width: 30px;
    top: calc((100% - 30px)/2);
  }

  .redirect-div {
    cursor: pointer !important;
    text-transform: none !important;
    text-align: center;
    width: 80%;
    font-size: 12px;
    font-family: "apercu-mono";
    margin: 10px 10px;
    display: inline-block;
  }

  .mdl-checkbox {
    text-align: left;
    height: 100%;
  }

  .button {
    cursor: pointer;
    border-radius: 2px !important;
    width: 80%;
    height: 50px;
    padding: 0 20px;
    border: none;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.86px;
  }
}

@media (max-width:450px) {
  .mdl-checkbox__label {
    line-height: normal;
    font-family: "apercu-r";
    margin-left: 0px;
    margin-top: 10px;
    padding-bottom: 8px;
    font-size: 12px;
  }

  .checkbox-container1 {
    float: left;
    width: 84%;
    margin-bottom: 25px;
    padding-left: 35px;
    margin-top: 25px;
  }

  .mdl-checkbox__label:before,
  .mdl-checkbox__label:after {
    height: 16px;
    width: 16px;
    position: absolute;
    left: 0;
    content: '';
    top: 0;
  }
}

@media (max-width:414px) {
  .mdl-checkbox__label {
    line-height: normal;
    font-family: "apercu-r";
    margin-left: 0px;
    margin-top: 10px;
    padding-bottom: 8px;
    font-size: 12px;
  }

  .mdl-checkbox__label:before,
  .mdl-checkbox__label:after {
    height: 16px;
    width: 16px;
    position: absolute;
    left: 0;
    content: '';
    top: 0;
  }

  .heading {
    margin-left: 5px;
    font-family: "apercu-mono";
    font-size: 15px;
  }

  header.inner {
    height: 90px;
  }

  .button-with-static-icon {
    padding: 0 40px;
  }

  .button-with-static-icon img {
    left: 10px;
  }

  .button {
    cursor: pointer;
    border-radius: 2px !important;
    width: 80%;
    height: 50px;
    padding: 0 20px;
    border: none;
    text-transform: uppercase;
    font-size: 10px !important;
    letter-spacing: 0.86px;
  }

  .button-with-static-icon img {
    position: absolute;
    left: 5px;
    width: 25px !important;
    top: calc((100% - 25px)/2);
  }

  .checkbox-container {
    float: left;
    width: 100%;
    margin-bottom: 10px;
    left: 0px !important;
    padding-left: 0px !important;
  }

  .mdl-checkbox {
    text-align: left;
    height: 100%;
    left: none !important;
  }

  .redirect-div {
    text-align: center;
    cursor: pointer !important;
    text-transform: none !important;
    width: 80%;
    font-size: 8px;
    font-family: "apercu-mono";
    margin: 10px 10px;
    display: inline-block;
  }
}

@media (max-width:350px) {
  .mdl-checkbox__label {
    line-height: normal;
    font-family: "apercu-r";
    margin-left: 0px;
    margin-top: 10px;
    padding-bottom: 8px;
    font-size: 12px;
  }

  .mdl-checkbox__label:before,
  .mdl-checkbox__label:after {
    height: 16px;
    width: 16px;
    position: absolute;
    left: 0;
    content: '';
    top: 0;
  }
}