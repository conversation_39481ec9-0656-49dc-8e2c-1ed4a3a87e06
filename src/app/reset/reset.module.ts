import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ResetRoutingModule } from './reset.routing.module';
import { ResetComponent } from './reset.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ShareModule } from '../share.module';
import { NgSelectModule } from '@ng-select/ng-select';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';


@NgModule({
  imports: [
    CommonModule,
    ResetRoutingModule,
    NgbModule,
    ShareModule,
    NgSelectModule,
    ReactiveFormsModule,
    FormsModule,
  ],
  declarations: [
    ResetComponent,

  ],
})
export class ResetModule {

}