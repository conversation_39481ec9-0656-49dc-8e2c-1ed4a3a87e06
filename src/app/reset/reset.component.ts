import { Component, OnInit, ChangeDetectorRef, ɵConsole } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators, ValidatorFn, AbstractControl, FormControl } from '@angular/forms';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { LoginService } from '../login.service';
import { ActivatedRoute, Router } from '@angular/router';
import { UserAccountService } from '../user-account.service';
import { UserAccountInfo } from '../entity/user-account-info';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { ClientConfiguration } from '../client-config.service';
import { Title } from '@angular/platform-browser';
import { Constants } from '../util/constants';
import { ValidationResult } from 'json-schema';

@Component({
    selector: 'app-reset',
    templateUrl: './reset.component.html',
    styleUrls: ['./reset.component.scss'],
    standalone: false
})
export class ResetComponent implements OnInit {

  loadModal = false;
  agreePolicy = false;
  showFullPage = false;
  showError = false;
  ResetForm: UntypedFormGroup;
  bsModalRef: BsModalRef;
  resetOnWelcomeFlow = false; // if true show welcome screen, else show normal reset screen
  userid: string;
  constructor(private fb: UntypedFormBuilder,
    private modalService: BsModalService,
    private loginService: LoginService,
    private router: Router,
    private cdRef: ChangeDetectorRef,
    private toastr: ToastrService,
    private titleService: Title,
    public translateService: TranslateService,
    private userAccountService: UserAccountService,
    private activatedRoute: ActivatedRoute,
    private clientConfig: ClientConfiguration) {
    this.titleService.setTitle('Reset Password');
  }
  ngOnInit() {
    this.titleService.setTitle('Reset Password');
    this.userid = this.activatedRoute.snapshot.queryParams["userid"];
    this.ResetForm = this.fb.group({
      password: ['', [Validators.required,this.strong()]],
      confirmPassword: ['', [Validators.required,this.strong()]]
    });
    this.loginService.signInSubject$.subscribe(res => {
      if (res && res === 'emailVerificationSuccess') {
        this.showFullPage = true;
        setTimeout(() => {

          let sToken = this.activatedRoute.snapshot.queryParams["loginToken"];
          this.router.navigate(['/search'], { queryParams: { userid: this.userid, sToken: sToken } });
        }, 3000);
      } else if (res && res === 'resetPasswordSuccess') {
        if (this.bsModalRef) this.bsModalRef.hide();
      }
    });


  }
  hidePasswordRequirementPopup(){
  if ($('.passwordConstraints').css('display') !== 'none') {
    $('.passwordConstraints').hide();
    }
}
isPasswordValid(item){
  if(item && item.length >0 ){
    if(this.lowercharMissing || this.uppercharMissing || this.numbercharMissing || this.specialcharMissing || item.length <8){
      if ($('.passwordConstraints').css('display') == 'none') {
        $('.passwordConstraints').show();
        }
    }else{
      if ($('.passwordConstraints').css('display') !== 'none') {
        $('.passwordConstraints').hide();
        }
    }
  }else if(item && item.length >=8){
    if(!this.lowercharMissing && !this.uppercharMissing && !this.numbercharMissing && !this.specialcharMissing){
      if ($('.passwordConstraints').css('display') !== 'none') {
        $('.passwordConstraints').hide();
        }
    }
  }

}
lowercharMissing=false;
numbercharMissing=false;
uppercharMissing=false;
specialcharMissing=false;
private detectChanges(){
  try {
    this.cdRef.reattach();
    this.cdRef.detectChanges();
  
  } catch(err) {

  }
}
  public  strong(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: boolean } | null => {
    let hasNumber = /\d/.test(control.value);
    let hasUpper = /[A-Z]/.test(control.value);
    let hasLower = /[a-z]/.test(control.value);
    let hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(control.value);
  //   
    
    const valid = (hasNumber && hasUpper && hasLower && hasSpecial && (control.value.length>=8));
    if(!hasLower){
      this.lowercharMissing =true;
    }else{
      this.lowercharMissing =false;
    }

    if(!hasUpper){
      this.uppercharMissing =true;
    }else{
      this.uppercharMissing =false;
    }

    if(!hasNumber){
      this.numbercharMissing =true;
    }else{
      this.numbercharMissing =false; 
    }

    if(!hasSpecial){
      this.specialcharMissing =true;
    }else{
      this.specialcharMissing =false;
    }
    if (!valid) {
        return { 'pattern': true };
    }
   
  //  
    return null;
  }
}
  // passwordValidator(): ValidatorFn {
  //   return (control: AbstractControl): { [key: string]: boolean } | null => {
  //     if(this.ResetForm.controls['password'].value !== this.ResetForm.controls['confirmPassword'].value){
  //       return { 'passwordMismatch': true };
  //     }else{
  //       return null;
  //     }
  //   };
  // }


  getMessage() {
    return this.translateService.instant('reset.Thankyou!Youremailhasbeensuccessfullyverified').toString();
  }
  ngAfterViewInit() {

    if (this.activatedRoute.snapshot.queryParams['userid']
      && this.activatedRoute.snapshot.queryParams['loginToken']) {
      if (this.activatedRoute.snapshot.queryParams['action']
        && this.activatedRoute.snapshot.queryParams['action'] === 'resetPassword') {
        this.loadModal = true;
      } else if (this.activatedRoute.snapshot.queryParams['action']
        && this.activatedRoute.snapshot.queryParams['action'] === 'welcome') {
        this.loadModal = true;
        this.resetOnWelcomeFlow = true;
      } else if (this.activatedRoute.snapshot.queryParams['action']
        && this.activatedRoute.snapshot.queryParams['action'] === 'verifyEmail') {
        // this.showFullPage = true;
        let userid = this.activatedRoute.snapshot.queryParams["userid"];
        let sToken = this.activatedRoute.snapshot.queryParams["loginToken"];
        let userType = this.activatedRoute.snapshot.queryParams["usertype"];
        this.loginService.userVerifyEmail(userid, sToken, userType);
      }
    } else {
      setTimeout(() => {
        this.router.navigate(['/errors'], { queryParams: { errorCode: '403' } });
      }, 1000);

    }
  }
  checkBoxChange(event) {
    if (event) {
      this.showError = false;
      this.agreePolicy = true;
    } else {
      this.agreePolicy = false;
    }
  }
  showModal(modelName) {
    this.loadModal = false;
    setTimeout(() => {
      this.bsModalRef = this.modalService.show(modelName, {
        backdrop: true, keyboard: false, ignoreBackdropClick: true
      });
    }, 1000);
  }


  requestResetPassword() {
    if (!this.agreePolicy && this.resetOnWelcomeFlow) {
      this.showError = true;
      return;
    }
    if (this.ResetForm.valid) {
      if (this.ResetForm.controls['confirmPassword'].value === this.ResetForm.controls['password'].value) {
        let userid = this.activatedRoute.snapshot.queryParams["userid"];
        let sToken = this.activatedRoute.snapshot.queryParams["loginToken"];
        this.titleService.setTitle('Profile');
        this.loginService.userResetPasswordRequest(userid, sToken, this.ResetForm.controls['confirmPassword'].value);
      } else {

        this.toastr.error(this.translateService.instant('reset.PasswordandConfirmPassworddonotmatch').toString());
      }

    } else {
      this.ResetForm.controls['confirmPassword'].markAsTouched();
      this.ResetForm.controls['password'].markAsTouched();
      this.ResetForm.updateValueAndValidity();
    }
  }
  goToSignIn() {
    if (this.bsModalRef) this.bsModalRef.hide();
    this.titleService.setTitle('Login');
    this.router.navigate(['/login'], { queryParams: {} });
  }
  getBrandName() {
    return this.clientConfig.brandName;
  }
}
