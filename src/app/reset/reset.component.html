<div *ngIf="loadModal">{{showModal(resetPasswordModal)}}</div>
<div class="container1" *ngIf="showFullPage">
  <div class="row">
    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
      <div class="header-inner">
        <div class="header-left">
          <div class="logo">
            <a href="javascript:void(0)">
              <img src="assets/images/logo.png" />
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="heading">
      {{getMessage()}}
    </div>
  </div>
</div>


<ng-template #resetPasswordModal let-modal>
  <div class="container">
    <div class="row">
      <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
        <div class="header-inner">
          <div class="header-left">
            <div class="logo">
              <a href="javascript:void(0)">
                <img src="assets/images/logo.png" />
              </a>
            </div>
          </div>
        </div>
      </div>
      <div class="form-content login-container">
        <form [formGroup]="ResetForm">
          <ng-container *ngIf="resetOnWelcomeFlow">
            <h3 style="padding: 10px 0;
                text-align: left !important;
                width: 80%;
                margin: 0 auto;">{{'reset.Thankyouforjoining' | translate}} {{getBrandName()}} </h3>
            <p style="padding: 10px 0;
                text-align: left !important;
                width: 80%;
                margin: 0 auto;">{{'reset.Pleasesetuppasswordtoyouraccountwithemail' | translate}}: {{userid}}</p>
          </ng-container>
          <ng-container *ngIf="!resetOnWelcomeFlow">
            <h3>{{'reset.Forgotpassword' | translate}}</h3>
            <span style="padding-bottom:10px;">{{'reset.Enteranewpasswordforyouraccount' | translate}}</span>
          </ng-container>
          <div  class="passwordConstraints" style="display: none">
            <div >
              {{'reset.Passwordrequirements' | translate}}:
            </div>
            <div class="constraints" >
                    <span *ngIf="!this.uppercharMissing" style="min-width:20px;max-width:20px;color:green;margin-right:5px;font-size:15px;padding-left: 0px;"
                    [ngStyle]="{'float': this.isMobile1 ? 'right' : 'none'}">&#10004;</span>
                <span *ngIf="this.uppercharMissing" style="min-width:20px;max-width:20px;color:red;margin-right:5px;font-size:15px;">
                  &times;</span>
             {{'reset.UppercaselettersAZ' | translate}} 
           </div>
           <div class="constraints">
                <span *ngIf="!this.lowercharMissing" style="min-width:20px;max-width:20px;color:green;margin-right:5px;font-size:15px;padding-left: 0px;"
                    [ngStyle]="{'float': this.isMobile1 ? 'right' : 'none'}">&#10004;</span>
                <span *ngIf="this.lowercharMissing" style="min-width:20px;max-width:20px;color:red;margin-right:5px;font-size:15px;">
                  &times;</span>
             {{'reset.Lowercaselettersaz' | translate}}  
           </div>
           <div class="constraints">
                <span *ngIf="!this.numbercharMissing" style="min-width:20px;max-width:20px;color:green;margin-right:5px;font-size:15px;padding-left: 0px;"
                    [ngStyle]="{'float': this.isMobile1 ? 'right' : 'none'}">&#10004;</span>
                <span *ngIf="this.numbercharMissing" style="min-width:20px;max-width:20px;color:red;margin-right:5px;font-size:15px;">
                  &times;</span>
             {{'reset.Numbers09' | translate}}  
           </div>
           <div class="constraints">
                <span *ngIf="!this.specialcharMissing" style="min-width:20px;max-width:20px;color:green;margin-right:5px;font-size:15px;padding-left: 0px;"
                    [ngStyle]="{'float': this.isMobile1 ? 'right' : 'none'}">&#10004;</span>
                <span *ngIf="this.specialcharMissing" style="min-width:20px;max-width:20px;color:red;margin-right:5px;font-size:15px;">&times;</span>
             {{'reset.Specialcharacters' | translate}} 
           </div>
           </div>
          <div class="input-box input-box-with-icon">
            <input type="password" maxlength="20"formControlName="password" (input)="isPasswordValid($event.target.value)" (focusout)="hidePasswordRequirementPopup()"placeholder=" {{'reset.Enternewpassword' | translate}}" />
            <span class="input-box-icon-container">
              <img class="input-box-icon" src="assets/images/lock.png" alt="" />
            </span>
            <div
              *ngIf="ResetForm.controls['password'].hasError('required') && (ResetForm.controls['password'].touched || ResetForm.controls['password'].dirty)"
              class="error">{{'reset.thisfieldisrequired' | translate}}
            </div>
            <div
            *ngIf="ResetForm.controls['password'].hasError('pattern') && (ResetForm.controls['password'].touched || ResetForm.controls['password'].dirty)"
            class="error">{{'reset.pleaseenteravalidpassword' | translate}}
          </div>
          </div>
          <div class="input-box input-box-with-icon">
            <input type="password" maxlength="20" formControlName="confirmPassword" placeholder=" {{'reset.Confirmnewpassword' | translate}}" />
            <span class="input-box-icon-container">
              <img class="input-box-icon" src="assets/images/lock.png" alt="" />
            </span>
            <div
              *ngIf="ResetForm.controls['confirmPassword'].hasError('required') && (ResetForm.controls['confirmPassword'].touched || ResetForm.controls['confirmPassword'].dirty)"
              class="error">{{'login.thisfieldisrequired' | translate}}
            </div>
            
            <div
              *ngIf="ResetForm.controls['confirmPassword'].hasError('passwordMismatch') && (ResetForm.controls['confirmPassword'].touched || ResetForm.controls['confirmPassword'].dirty)"
              class="error">{{'reset.passwordMismatch' | translate}}
            </div>
          </div>
          <div *ngIf="resetOnWelcomeFlow" class="checkbox-container1" style="margin-top:0px !important;">
            <label style="width:80%; text-align:left;" for="agreePolicy">
              <input type="checkbox" id="agreePolicy" value="agreePolicy"
                (change)="checkBoxChange($event.target.checked)" class="mdl-checkbox__input">
              <span class="mdl-checkbox__label">{{'reset.Iagreeto' | translate}} <a
                  style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:16px !important;"
                  href="https://routespring.com/tos.html?&dummy=/TripItAPI/" target="_blank">{{'reset.Termsofuse' | translate}}</a>  {{'reset.and' | translate}}<a
                  style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:16px !important;"
                  href="https://routespring.com/privacy.html?&dummy=/TripItAPI/" target="_blank"> {{'reset.PrivacyPolicy' | translate}}
                  </a></span>
            </label>
            <div *ngIf="showError" style="margin-top: 10px; float: left; margin-left: 62px;" class="error">
              {{'reset.Pleaseaccepttheterms' | translate}}
            </div>
          </div>
          <div *ngIf="!resetOnWelcomeFlow" class="button-container">
            <button type="button" (click)="requestResetPassword();" class="button button-primary">{{'reset.RESET' |
              translate}}</button>
          </div>
          <div *ngIf="resetOnWelcomeFlow" class="button-container">
            <button type="button" (click)="requestResetPassword();" class="button button-primary">{{'reset.save&gotoprofile' | translate}}</button>
          </div>
        </form>
      </div>
      <div *ngIf="resetOnWelcomeFlow" style="width:100%; margin: 0 10%;text-align: left;padding: 0 0 20px 0;">
        <!--     <div>Already have a password? <a class="terms" (click)="goToSignIn();"><span style="color:var(--hyperlink-color)"> {{'login.Signin' | translate}}</span></a></div>-->
      </div>

    </div>
  </div>
</ng-template>