<section class=" user-summery">
  <div *ngIf="isUserLoggedIn()" class="container">
    <div class="row">
      <div class="col-lg-12">
        <div class="user-summery-inner card-div" style="margin-top:20px !important;">
          <div class="user-summery-left">
            <div class="user-detail">
              <div class="user-img" style="cursor:pointer;padding-top: 30px;"
                onclick="document.getElementById('getFile').click()">
                <input type="file" #fileInput id="getFile" accept=".png,.jpg,.jpeg,.svg,.gif" (click)="fileInput.value = null"
                  value="" (change)="readURL($event.target.files);" style="display:none;" single>
                <img class="pImage" src="{{getUserImage()}}" alt="" /><span class="pencil"><i class="fa fa-pencil"
                    aria-hidden="true"></i></span>
              </div>
              <span *ngIf="imageExists()" class="delete-profile" (click)="showModal(profileImageRemovePopup)"><i
                  aria-hidden="true" class="fa fa-trash-o"></i></span>
              <div class="user-content">
                <span class="user-name">{{getUserName()}}</span>
                <span class="user-email">{{getUserEmail()}}</span>
                <span class="user-phone">{{getUserPhoneNumber()}}</span>
              </div>
              <div *ngIf="njoySpecificBuild">
                <div class="user-summery-card-heading">
                  <a (click)="signOut()"> <span class="sign-out"> {{'profileUser.Signout' | translate}}</span></a>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="showGallopCash" class="user-summery-right">
            <div class="card-div shadow2 user-summery-card gallop-cash-div">
              <div class="user-summery-card-heading">
                <img src="assets/images/gallop-cash-icon.png">
                <span>{{'profileUser.GallopCash' | translate}}</span>
              </div>
              <div class="user-summery-card-content">({{getGallopCash() |
                currency: getCurrencySymbol(currencyCode): "code" :'1.2'}})</div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</section>


<ng-template #profileImageRemovePopup let-modal>
  <div class="modal-header">
    <h5 class="modal-title" id="myModalLabel">
      {{'profileUser.Removeprofileimage' | translate}}
    </h5>
    <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
      <i class="material-icons" style="color:#fff;">close</i>
    </button>
  </div>
  <div class="modal-body">
    <div class="remove-popup-heading"> {{'profileUser.Removeprofileimage?' | translate}}</div>
    <button *ngIf="!requestInProgress" class="btn btn-secondary" (click)="removeConfirm()"><span
        class="add"> {{'profileUser.Remove' | translate}}</span></button>
    <button *ngIf="requestInProgress" class="btn btn-secondary" [disabled]="requestInProgress"><span
        class="add"> {{'profileUser.Removing' | translate}}</span></button>
    <button class="btn btn-normal" (click)="onModelCancel()"><span class="add1"> {{'profileUser.Cancel' | translate}}</span></button>
    <div class="modal-form-button">

    </div>
  </div>
</ng-template>