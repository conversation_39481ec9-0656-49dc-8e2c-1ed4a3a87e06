.user-detail {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  overflow: auto;
}

.modal-dialog {
  max-width: 710px;
  margin-top: 200px !important;
}

.modal-content {
  border-radius: 0px;
  box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
  border: none;
  text-align: center;
  max-width: 710px;
  margin-top: 200px !important;
  width: calc(100% - 48px);
}

.modal-title {
  height: 26px;
  width: 266px;
  color: #fff;
  font-family: "apercu-mono";
  font-size: 22px;
  letter-spacing: -0.98px;
  line-height: 26px;
  text-align: left;
}

.modal-header {
  background-color: var(--hyperlink-color);
  color: #FFFFFF;
  font-size: 14px;
  height: 40px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 6px 6px 0 0;
  padding: 0 8px 0 22px !important;
  border-bottom: none;
}

.modal-header h5 {
  font-size: 14px;
  font-family: "apercu-mono";
}

.modal-body {
  padding: 28px 25px 47px 25px;
  border-radius: 0 0 5px 5px;
}

.modal-footer {
  padding: 0;
  border-top: none;
}

.remove-popup-heading {
  padding-bottom: 20px;
  ;
}

.btn-secondary {
  background-color: var(--button-bg-color) !important;
  border: none;
  margin: 0 10px;
  height: 64px;
  width: 256px;
  border-radius: 0px !important;
  box-shadow: none;
}

.btn-normal {
  height: 64px;
  width: 200px;
  border: none;
  margin: 0 10px;
  border-radius: 0px !important;
  box-shadow: none;
}

.delete-profile {
  position: relative;
  top: -40px;
  font-size: 20px;
  cursor: pointer;
  padding-right: 10px;
}

.add1 {
  height: 24px;
  width: 68px;
  color: var(--hyperlink-color);
  font-family: "apercu-r";
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 1px;
  line-height: 25px;
}

.add {
  height: 24px;
  width: 188px;
  color: var(--button-font-color);
  font-family: "apercu-r";
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 1px;
  line-height: 25px;
}



.card-div-inner {
  width: 100% !important;
}

.sign-out {
  margin-right: 25px !important;
  color: var(--hyperlink-color) !important;
  font-size: 20px !important;
  cursor: pointer !important;
  margin-left: 72px;
}

.pImage {
  width: 90px;
  height: 90px;
}

.pencil {
  position: relative;
  top: -100px;
  left: 75px;
  font-size: 20px;
  background: rgba(255, 255, 255, 0.5);
  padding: 0px;
}

@media(max-width:767px) {
  .user-name {
    text-align: left;
  }

  .delete-profile {
    top: -20px;
    font-size: 16px;
  }

  .pencil {
    position: relative;
    top: -60px;
    left: 40px;
    font-size: 16px;
  }

  .pImage {
    width: 50px;
    height: 50px;
    min-width: 50px;
  }

  .user-summery-card-heading span {
    margin-left: 18px !important;
    margin-bottom: 20px !important;
  }

  .sign-out {
    margin-right: 15px !important;
    color: var(--hyperlink-color) !important;
    cursor: pointer !important;
    white-space: nowrap;
    font-size: 12px !important;
    margin-left: 7px !important;
  }
}

@media(max-width:340px) {
  .pencil {
    position: relative;
    top: -60px;
    left: 30px;
    font-size: 16px;
  }
}