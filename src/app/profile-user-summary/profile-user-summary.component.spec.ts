import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ProfileUserSummaryComponent } from './profile-user-summary.component';

describe('ProfileUserSummaryComponent', () => {
  let component: ProfileUserSummaryComponent;
  let fixture: ComponentFixture<ProfileUserSummaryComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ProfileUserSummaryComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProfileUserSummaryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
