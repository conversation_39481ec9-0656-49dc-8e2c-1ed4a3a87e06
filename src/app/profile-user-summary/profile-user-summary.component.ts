import { Component, OnInit } from '@angular/core';
import { UserAccountService } from '../user-account.service';
import { Subscription } from 'rxjs';
import { SubscriptionPlan } from '../entity/subscription-plan';
import { UserAccountInfo } from '../entity/user-account-info';
import { CommonUtils } from '../util/common-utils';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { UserInfo } from '../entity/user-info';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Constants } from '../util/constants';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { LoginService } from '../login.service';
import { SearchService } from '../search.service';
import { CarBookingService } from '../car-booking.service';
import { HotelSearchService } from '../hotel-search.service';
import { AdminPanelService } from '../admin-panel.service';
import { TranslateService } from '@ngx-translate/core';
import { DateUtils } from '../util/date-utils';

@Component({
    selector: 'app-profile-user-summary',
    templateUrl: './profile-user-summary.component.html',
    styleUrls: ['./profile-user-summary.component.scss'],
    standalone: false
})
export class ProfileUserSummaryComponent implements OnInit {

  private userName = "";
  private userEmail = "";
  private userImage = "";
  private userPhoneNumber = "";
  imageSrc: any;
  requestInProgress = false;
  njoySpecificBuild: boolean;
  userAccountInfoObj: UserAccountInfo;
  companyApprovalSubscription:Subscription
  currencyCode: string;
  fetchAccountInfoSubscription: Subscription;
  showGallopCash: boolean;
  bsModalRef: BsModalRef;
  constructor(private userAccountInfoService: UserAccountService,
    public router: Router, private toastr: ToastrService,
    private carBookingService: CarBookingService,
    private adminPanelService: AdminPanelService,
    private searchHotelService: HotelSearchService,
    private gallopLocalStorage: GallopLocalStorageService,
    private loginService: LoginService,
    public translateService: TranslateService,
    private searchService: SearchService,
    private titleService: Title, private modalService: BsModalService,) { }

  ngOnInit() {
    this.njoySpecificBuild = this.userAccountInfoService.isItNjoyBuild();
    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      // this.fillTravelerProfileData();
      // this.userName = this.userAccountInfoService.getAccountInfo().userInfo.username;
      // this.userEmail = this.userAccountInfoService.getUserEmail();
      // this.userPhoneNumber = this.getUserPhoneNumber();
      this.userAccountInfoObj = userAccountInfoObj;
      if (this.userAccountInfoObj && this.userAccountInfoObj.gallopCash) {
        this.currencyCode = this.userAccountInfoObj.gallopCash.currency;

        if (this.userAccountInfoObj.gallopCash.amount > 0) {
          this.showGallopCash = true;
        } else {
          this.showGallopCash = false;
        }
        if (this.userAccountInfoObj && this.userAccountInfoService.njoySpecificBuild) {

          let startDate1: Date = new Date();
          let endDate1: Date = new Date();
          endDate1.setDate(endDate1.getDate() + 1);
          startDate1.setDate(startDate1.getDate() -1);
          let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(startDate1);
          let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(endDate1);
          let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
          let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
          this.adminPanelService.fetchCompanyApprovals(this.userAccountInfoService.getUserCompanyId(), startDate, endDate, 'all');
        }
      }
    });
    this.companyApprovalSubscription = this.adminPanelService.companyApprovalResponseObservable$.subscribe((reportResponse) => {
      if (reportResponse) {
          if (reportResponse.pendingApprovals.length > 0) {
            this.searchService.showApprovalaInMenu =true;
          } else {
            this.searchService.showApprovalaInMenu =false;
          }
    
      } else {
        this.searchService.showApprovalaInMenu =false;
      }
    });
  }
  ngOnDestroy(){
    if(this.companyApprovalSubscription){
      this.companyApprovalSubscription.unsubscribe();
    }
  }
  signOut() {
    this.searchService.previousSearch = null;
    this.searchHotelService.previousSearch = null;
    this.adminPanelService.emptyEmployeeList();
    this.searchService.employeeOptions = new Array(1).fill(null).map(_ => []);
    this.searchService.originalEmployeeOptions = new Array(1).fill(null).map(_ => []);
    this.carBookingService.carSearchDetail = null; this.searchService.employeeEmail = [];
    this.searchService.employeeList = [];
    this.searchService.previousSearch = undefined;
    this.searchService.emptytravelList();
    this.adminPanelService.emptyEmployeeList();
    this.searchHotelService.previousSearch = null;
    this.carBookingService.carSearchDetail = null;
    CommonUtils.signout(this.userAccountInfoService, this.gallopLocalStorage, this.loginService);

    this.userAccountInfoService.signOut();
    localStorage.setItem('loginSession', null);
    this.router.navigate(['/login']);
   // this.titleService.setTitle('Login');
    this.titleService.setTitle(this.translateService.instant('login.Login'));
  }
  isUserLoggedIn() {
    return this.userAccountInfoService.isLoggedIn();
  }
  getUserEmail() {
    return this.userAccountInfoService.getUserEmail();
  }

  getUserName() {
    return this.userAccountInfoService.getUserName();
  }
  selectedFile = null;
  readURL(event): void {
    if (event && event[0]) {
      const file = event[0];
      this.selectedFile = file;
      this.renderPostUploadFile();
      this.processImageUpload(this.selectedFile);
    }
  }
  renderPostUploadFile() {
    const reader = new FileReader();
    const file = this.selectedFile;
    reader.onload = e => this.imageSrc = reader.result;

    reader.readAsDataURL(file);
  }
  processImageUpload(imageFile) {
    this.userAccountInfoService.uploadProfileImageRequest(imageFile).subscribe(res => {
      if (res && res.success) {
        this.imageSrc = res.data[0];
        this.renderPostUploadFile();
        let userAccountObj = this.userAccountInfoService.getAccountInfo().userInfo;
        let googleImge: any = { image: { url: '' } };
        googleImge.image.url = this.imageSrc;
        //userAccountObj.userEntityData.google_info.image.url ='';
        userAccountObj.userEntityData.google_info = JSON.stringify(googleImge);
        this.saveAndUpdateProfileInfo(userAccountObj, '123');
      } else if (res && res.error_message) {
        this.toastr.error(res.error_message);
      } else {
        this.toastr.error(this.translateService.instant('search.Apologies!somethingwentwrong,wecouldntretriveemployeeslist.Pleasetryagainlaterorcontactsupport'));
      }
    }, error => {
      setTimeout(() => {
        this.toastr.error(this.translateService.instant('search.Pleasecheckyourinternet'));
      }, 100);

    })

  }
  private saveAndUpdateProfileInfo(userInfoDTO, saveButtonId: string) {
    this.fetchAccountInfoSubscription = this.userAccountInfoService.saveAccountInfo(
      userInfoDTO).subscribe(res => {
        if (res.success == true) {
          this.requestInProgress = false;
          if (res.data) {
            let userAccountInfoObj: UserAccountInfo = deserialize(res.data, UserAccountInfo);
            this.userAccountInfoService.setAccountInfo(userAccountInfoObj);
            this.selectedFile = false;
          }
        } else if (res.status == 'error') {
          this.requestInProgress = false;
        } else {
          this.requestInProgress = false;
        }
      }, error => {
        //this.showError();
      });
  }
  getUserPhoneNumber() {
    return this.userAccountInfoService.getUserPhoneNumber();
  }

  getUserImage() {
    if (this.selectedFile) {
      return this.imageSrc;
    }
    return this.userAccountInfoService.getUserImage();
  }

  isPaidSubscription() {
    let plan: SubscriptionPlan = this.userAccountInfoService.getSubscriptionPlan();
    if (plan && plan != null && plan.id.toString() !== 'subscription_free') {
      return true;
    } else {
      return false;
    }
  }

  getSubscriptionPlanName() {
    let plan: SubscriptionPlan = this.userAccountInfoService.getSubscriptionPlan();
    if (plan && plan != null && plan.id.toString() !== 'subscription_free') {
      return plan.name;
    } else {
      return "Free";
    }
  }

  getSubscriptionPlanDesc() {
    let plan: SubscriptionPlan = this.userAccountInfoService.getSubscriptionPlan();
    if (plan && plan != null && plan.id.toString() !== 'subscription_free') {
      return plan.name;
    } else {
      return "Free";
    }
  }
  public getGallopCash(): number {
    if (this.userAccountInfoObj && this.userAccountInfoObj.gallopCash) {
      return this.userAccountInfoObj.gallopCash.amount;
    } else {
      return 0.00;
    }
  }
  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }

  showModal(Modal) {
    this.bsModalRef = this.modalService.show(Modal, {
      initialState: {
      }, backdrop: true, keyboard: false, ignoreBackdropClick: true
    });
  }
  onModelCancel() {
    this.bsModalRef.hide();
  }
  removeConfirm() {
    const userAccountObj = this.userAccountInfoService.getAccountInfo().userInfo;
    this.requestInProgress = true;
    userAccountObj.userEntityData.google_info = '{"image" : {}}';
    this.saveAndUpdateProfileInfo(userAccountObj, '123');
    setTimeout(() => {
      this.bsModalRef.hide();
    }, 1000);
  }
  imageExists() {
    return (Constants.DEFAULT_PROFILE_IMAGE !== this.getUserImage());
  }
}
