import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { AdminPanelService } from '../admin-panel.service';
import { Subscription } from 'rxjs';
import { CompanyReportResponse, UserInfoBasic } from '../entity/company-report-response';
import { SearchActionType } from '../enum/search-action.type';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { DateUtils } from '../util/date-utils';
import { Router, ActivatedRoute } from '@angular/router';
import { UserAccountService } from '../user-account.service';
import { SearchService } from '../search.service';
import { LoginService } from '../login.service';
import { CommonUtils } from '../util/common-utils';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { DatePipe } from '@angular/common';
import { Title } from '@angular/platform-browser';
import { DeviceDetailsService } from '../device-details.service';
import { TranslateService } from '@ngx-translate/core';
@Component({
    selector: 'app-approval',
    templateUrl: './approval.component.html',
    styleUrls: ['./approval.component.scss'],
    standalone: false
})
export class ApprovalComponent implements OnInit {
  @Output() goBackToAdmin = new EventEmitter<boolean>();
  changeStyle() {
    if (!this.isMobile1) {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '730px' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '99vw' }
      }
    }
  }
  changeStyle1() {
    if (!this.isMobile1) {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '335px', 'overflow': 'hidden' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '700px', 'overflow': 'hidden' }
      }
    }
  }
  isMobile1: boolean;
  viewMode='tab11';
  deviceSubscription1: Subscription;
  showPenndingApprovals = false;
  airports;
  minimumDate: Date = new Date();
  maximumDate: Date = new Date();
  maxDoBDate: Date = new Date();
  previousDate = '';
  viewMode2 = 'tab21';
  approvalUsernname = [];
  originalApprovalsList = [];
  viewMode1 = 'tab11';
  employeeOptions1 = [{ id: '', value: 'All Employees',name:"approval.AllEmployees" }];
  empValue = '';
  empValue1 = '';
  deptValue = '';
  approvers: any;
  applyButton = false;
  subType = 'All Employees';
  firstDate: Date;
  lastDate: Date = new Date(new Date().toISOString().split('T')[0]);
  resultErrorMessage = this.translateService.instant('report.FetchingData');
  resultPendingErrorMessage = '';
  showApprovalDetails = false;
  queryParamSubscription: Subscription;
  companyReport: Array<any>;
  historyList: Array<any>;
  type='Pending';
  companyApprovalSubscription: Subscription;
  startDate: Date = new Date();
  endDate: Date = new Date();
  apiReportResponse: CompanyReportResponse;
  loginServiceSubscription: Subscription;
  daterangepickerModel = [this.startDate, this.endDate];
  constructor(private adminPanelService: AdminPanelService,
    private userAccountInfoService: UserAccountService,
    public translateService: TranslateService,
    private searchService: SearchService,
    public progressBar: NgxUiLoaderService,
    public deviceDetailsService: DeviceDetailsService,
    private activatedRoute: ActivatedRoute,
    public ngxSmartModalService: NgxSmartModalService,
    private titleService: Title,
    private loginService: LoginService,
    public router: Router,) {
  }
  ngOnInit() {
    this.deviceSubscription1 = this.deviceDetailsService.isMobile1().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
    this.searchService.showApprovalaInMenu =false;
    this.queryParamSubscription = this.activatedRoute.queryParams.subscribe(params => {
      if (params['subType'] == 'pending') {
        this.viewMode2 = 'tab21';
        this.type = 'Pending';
        this.titleService.setTitle(this.translateService.instant('dashboardWrapper.PendingApprovals'));
        this.getApprovals(false, 'all');
        //this.resultPendingErrorMessage = this.translateService.instant('approval.Yay');
    //this.resultErrorMessage =this.translateService.instant('report.FetchingData');
      }else if(params['view'] && params['view']==='approval'){
        this.viewMode2 = 'tab22';
        this.type = params['bookingType'];
        if (this.type === 'pending' || this.type === 'upcoming') {
          this.type = 'Pending';
          this.viewMode='tab11';
        }else{
          this.type = 'History';
          this.viewMode='tab12';
        }
      }
    });
    this.lastDate = new Date();
    var curryear = this.lastDate.getFullYear();
    var currmonth = this.lastDate.getMonth();
    this.firstDate = new Date(curryear, currmonth, 1);
    this.maximumDate = this.lastDate;
    this.startDate = this.firstDate;
    this.endDate = this.lastDate;
    this.daterangepickerModel = [this.startDate, this.endDate];
    this.previousDate = '';
    this.deptValue = this.adminPanelService.filterAppliedDepartmentId;
    this.empValue1 = this.adminPanelService.filterAppliedApprovalEmployeeId;
    // this.empValue = this.adminPanelService.filterAppliedEmployeeId;
    this.loginServiceSubscription = this.loginService.signInSubject$.subscribe(res => {
      if (res && res === 'success') {
        this.empValue1 = '';
        this.adminPanelService.filterAppliedApprovalEmployeeId = '';
        this.adminPanelService.usersInApproval = [{ id: '', value: 'All Employees',name:"approval.AllEmployees" }];
        this.companyReport = [];
        this.historyList = [];
        this.subType = 'All Employees';
        this.adminPanelService.removeDataAfterLogging();
      }
    });
   
      //  this.daterangepickerModel=[this.startDate,this.endDate];
     
      setTimeout(() => {
        
        this.applyButton = false;
        this.companyApprovalSubscription = this.adminPanelService.companyApprovalResponseObservable$.subscribe((reportResponse) => {
          this.progressBar.stop(SearchActionType.DETAIL);
          if (reportResponse) {
            this.applyButton = true;
            //  this.originalApprovalsList=[];
            if (this.viewMode2 === 'tab21') {
              if (reportResponse.pendingApprovals.length > 0) {
                this.resultErrorMessage = this.translateService.instant('report.FetchingData');
                this.resultPendingErrorMessage = '';
              } else {
                this.resultPendingErrorMessage = 'approval.Yay';
    this.resultErrorMessage ='approval.Youdonthaveanypendingapprovals';
              }
            } else if (this.viewMode2 === 'tab22') {
              this.resultPendingErrorMessage = '';
              if (reportResponse.approvalHistory.length > 0) {
                this.resultErrorMessage = this.translateService.instant('report.FetchingData');
              } else {
                this.resultErrorMessage = 'approval.Lookslikeyoumanagedeverythingwithinpolicy'
              }
            }
            this.apiReportResponse = reportResponse;
          //  this.showPenndingApprovals = this.adminPanelService.showPenndingApprovals;
            this.buildCompanyReportData(reportResponse);
          
           this.buildCompanyReportDataForMObile(reportResponse);
          // this.allowAllAdminApprovals(this.adminPanelService.showPenndingApprovals) 
          } else {
            this.applyButton = true;
          }
        });
      }, 2000);
    }
    approvallist=[]
 private buildCompanyReportDataForMObile(reportResponse: CompanyReportResponse) {
    this.approvallist = [];
    this.originalApprovalsList = [];
    if(reportResponse.airports){
    this.airports = reportResponse.airports
    }
    for (let optionItem of reportResponse.pendingApprovals) {
      let reportItem = {};
      this.approvers = reportResponse.approvers;
      reportItem['ticketid'] = optionItem.ticketid;
      reportItem['tripid'] = optionItem.tripid;
      reportItem['option'] = optionItem.option;
      reportItem['groupTravelEventId'] = optionItem.groupTravelEventId;
      reportItem['selectTransId'] = optionItem.option.selectTransId;
      reportItem['name'] = reportResponse.users[optionItem.userid].userName;
      reportItem['bookingDate'] = optionItem.bookingDate;
      reportItem['type'] = optionItem.type;
      reportItem['userid'] = optionItem.userid;
      reportItem['approvers'] =  this.approvalUsers(reportResponse.users, optionItem.userid,reportItem['groupTravelEventId']);
      if(optionItem['tripSessionId'] != undefined){
        reportItem['tripSessionId'] =  optionItem['tripSessionId']
      };
      if (optionItem.type === 'flight') {
        reportItem['departure'] = optionItem.option.flight_option.legs[0].hops[0].starts;
        let firstLeg = optionItem.option.flight_option.legs[0];
        reportItem['price'] = optionItem.option.flight_option.price;
        let destination = firstLeg.hops[firstLeg.hops.length - 1].to;
        if (reportResponse.airports[destination]) {
          destination = reportResponse.airports[destination].city;
        }
        // let destination = optionItem.option.flight_option.legs[0].hops[0].from;
        for (let i = 1; i < optionItem.option.flight_option.legs.length - 1; i++) {
          let thisleg = optionItem.option.flight_option.legs[i];
          let lastHop = thisleg.hops[thisleg.hops.length - 1];
          if (reportResponse.airports[lastHop.to]) {
            destination = destination + ", " + reportResponse.airports[lastHop.to].city;
          } else {
            destination = destination + ", " + optionItem.option.flight_option.legs[i].hops[thisleg.hops.length - 1].to;
          }
          // destination = destination+ ", " +lastHop.to;
        }
        reportItem['destinations'] = destination;
      } else if (optionItem.type === 'hotel') {
        reportItem['departure'] = optionItem.option.hotel_option.checkInDate;
        reportItem['return'] = optionItem.option.hotel_option.checkOutDate;
        reportItem['price'] = optionItem.option.hotel_option.originalPrice

        reportItem['destinations'] = optionItem.destinationCity;
      } else if (optionItem.type === 'cars') {
        if (optionItem.option.car_option && optionItem.option.car_option.pickUpDate) {
          reportItem['departure'] = optionItem.option.car_option.pickUpDate;
        }
        reportItem['price'] = optionItem.option.car_option.price;
        if (optionItem.option.car_option && optionItem.option.car_option.dropOffDate) {
          reportItem['return'] = optionItem.option.car_option.dropOffDate;
        }
        reportItem['destinations'] = optionItem.destinationCity;

      }
      this.originalApprovalsList.push(reportItem);
    }
    let uniqueUserIds = {};
   
    if (this.originalApprovalsList.length > 0) {
      this.approvallist = this.filterSelfApprovalList(reportResponse.approvers);
      this.approvallist = this.filterbyTripSessionId(this.approvallist);
      
      if (this.approvallist.length === 0) {
        this.resultErrorMessage= this.translateService.instant('report.NoData');
      }
    }
   
  }
  getApprovals(onload: boolean, item) {
    this.companyReport = [];
    this.historyList = [];
    this.approvallist=[];
    this.adminPanelService.showPenndingApprovals = false;
        this.showPenndingApprovals = false;
    this.startDate.setDate(this.startDate.getDate() -1);
    let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
    let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);
    let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
    let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
    this.resultErrorMessage = this.translateService.instant('report.FetchingData');
    this.adminPanelService.firstApprovalDate1 = this.startDate;
    this.adminPanelService.lastApprovalDate1 = this.endDate;
    this.adminPanelService.filterAppliedApprovalEmployeeId = this.empValue1;
    let searchDates = startDate + 'T' + endDate;
   
    // this.adminPanelService.filterAppliedEmployeeId = this.empValue;
    if (this.previousDate === searchDates) {
     // this.showPenndingApprovals = this.adminPanelService.showPenndingApprovals;
      this.adminPanelService.filterApprovalByEmployee(item);
    } else {
      this.showPenndingApprovals =false;
      this.applyButton =false
      this.adminPanelService.fetchCompanyApprovals(this.userAccountInfoService.getUserCompanyId(), startDate, endDate, item);
    }
    this.previousDate = startDate + 'T' + endDate;
  }
  getCurrencySymbol(item): string {
    if(item.displayCurrency){
      return CommonUtils.getCurrencySymbol(item.displayCurrency);
    }else{
    return CommonUtils.getCurrencySymbol(item.currency);
    }
  }
  
  historyTabClicked() {
    this.viewMode2 = 'tab22';
    this.resultPendingErrorMessage = '';
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ApprovalsHistory'));
    this.resultErrorMessage = 'approval.Lookslikeyoumanagedeverythingwithinpolicy'
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'approvals',
          subType: 'history'
        },
        replaceUrl: false
      }
    );
  }
  pendingTabClicked() {
    this.viewMode2 = 'tab21';
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.PendingApprovals'));
    this.resultPendingErrorMessage = 'approval.Yay';
    this.resultErrorMessage ='approval.Youdonthaveanypendingapprovals';
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'approvals',
          subType: 'pending'
        },
        replaceUrl: false
      }
    );
  }
  
  getDisplayDateTimeForFlights(dateString: string, format: string): string {
    return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }
  getAirportCity(code) {

    return code && this.airports[code] && this.airports[code]['name'] ? this.airports[code].name : code;
  }
  channgeAlignment() {
    if (this.isMobile1) {
     
        return { 'text-align': 'left' };
     
    }
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  openNgxModal(id, picker) {
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);

    setTimeout(() => {
      this.viewMode1 = 'tab11';
      picker.show();
    }, 200);
  }
  setStartDate(date) {
    if (date) {
      // this.daterangepickerModel =date;
      this.startDate = date[0];
      this.endDate = date[1];
    }
  }
  setEndDate(date) {
    this.endDate = date;
  }
  //getSelectedEmployeeName(id:string){
  //let employeeObj = this.employeeOptions1.find(obj=> obj.id === id);
  //if(employeeObj){
  //return employeeObj.value;}

  //}
  getSelectedEmployeeNameFromHistory(id: string) {
    let employeeObj = this.employeeOptions1.find(obj => obj.id === id);
    if (employeeObj) {
      this.subType = employeeObj.value;
      if(employeeObj.value==='All Employees'){
       return ('approval.AllEmployees')
      }else{
      return employeeObj.value;
      }
    }

  }
  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
    this.ngxSmartModalService.close('daterangeSelection1');
  }
  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    const dayHoverHandler = event.dayHoverHandler;
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        this.searchService.hoverCounter++;
        if (this.searchService.hoverCounter > 1) {
          (picker as any)._datepickerRef.instance.daySelectHandler(cell);
        }
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }
  allowAllAdminApprovals(event) {
    if (event) {
      this.showPenndingApprovals = event;
      this.adminPanelService.showPenndingApprovals = event;
      this.companyReport = [];
      this.approvallist=[];
      this.approvallist = [...this.originalApprovalsList];
      this.approvallist = this.filterbyTripSessionId(this.approvallist);
      this.companyReport = [...this.originalApprovalsList];
    } else {
      this.showPenndingApprovals = event;
      this.adminPanelService.showPenndingApprovals = event;
      this.companyReport = [];
      this.approvallist = this.filterSelfApprovalList(this.approvers);
      this.approvallist = this.filterbyTripSessionId(this.approvallist);
      this.companyReport = this.filterSelfApprovalList(this.approvers);
      if (this.companyReport.length === 0 && this.approvallist.length===0) {
        this.resultErrorMessage = this.translateService.instant('report.NoData');

      }
    }
  }
  private buildCompanyReportData(reportResponse: CompanyReportResponse) {
    this.companyReport = [];
    this.originalApprovalsList = [];
    if(reportResponse.airports){
      this.airports = reportResponse.airports
      }
    this.historyList = [];
    for (let optionItem of reportResponse.pendingApprovals) {
      let reportItem = {};
      this.approvers = reportResponse.approvers;
      reportItem['ticketid'] = optionItem.ticketid;
      reportItem['tripid'] = optionItem.tripid;
      reportItem['groupTravelEventId'] = optionItem.groupTravelEventId;
      reportItem['selectTransId'] = optionItem.option.selectTransId;
      reportItem['name'] = reportResponse.users[optionItem.userid].userName;
      reportItem['bookingDate'] = optionItem.bookingDate;
      reportItem['type'] = optionItem.type;
      reportItem['userid'] = optionItem.userid;
      reportItem['approvers'] = this.approvalUsers(reportResponse.users, optionItem.userid,reportItem['groupTravelEventId']);
      if(optionItem["tripSessionId"] != undefined){
        reportItem['tripSessionId'] = optionItem["tripSessionId"];
      }
      if (optionItem.type === 'flight') {
        reportItem['departure'] = optionItem.option.flight_option.legs[0].hops[0].starts;
        let firstLeg = optionItem.option.flight_option.legs[0];
        let destination = firstLeg.hops[firstLeg.hops.length - 1].to;
        if (reportResponse.airports[destination]) {
          destination = reportResponse.airports[destination].city;
        }
        // let destination = optionItem.option.flight_option.legs[0].hops[0].from;
        for (let i = 1; i < optionItem.option.flight_option.legs.length - 1; i++) {
          let thisleg = optionItem.option.flight_option.legs[i];
          let lastHop = thisleg.hops[thisleg.hops.length - 1];
          if (reportResponse.airports[lastHop.to]) {
            destination = destination + ", " + reportResponse.airports[lastHop.to].city;
          } else {
            destination = destination + ", " + optionItem.option.flight_option.legs[i].hops[thisleg.hops.length - 1].to;
          }
          // destination = destination+ ", " +lastHop.to;
        }
        reportItem['destinations'] = destination;
      } else if (optionItem.type === 'hotel') {
        reportItem['departure'] = optionItem.option.hotel_option.checkInDate;
        reportItem['return'] = optionItem.option.hotel_option.checkOutDate;
        reportItem['destinations'] = optionItem.destinationCity;
      } else if (optionItem.type === 'cars') {
        if (optionItem.option.car_option && optionItem.option.car_option.pickUpDate) {
          reportItem['departure'] = optionItem.option.car_option.pickUpDate;
        }
        if (optionItem.option.car_option && optionItem.option.car_option.dropOffDate) {
          reportItem['return'] = optionItem.option.car_option.dropOffDate;
        }
        reportItem['destinations'] = optionItem.destinationCity;

      }
      this.originalApprovalsList.push(reportItem);
    }
    let uniqueUserIds = {};
    this.employeeOptions1 = [{ id: '', value: 'All Employees',name:"approval.AllEmployees" }];
    for (let optionItem of reportResponse.approvalHistory) {
      if (this.empValue1 === '' && this.subType === 'All Employees') {
        if (!uniqueUserIds[optionItem.userid]) {
          let user: UserInfoBasic = reportResponse.users[optionItem.userid];
          if (user && user.userName) {
            this.employeeOptions1.push({ value: user.userName, id: '' + user.userId,name:user.userName });
            if (this.adminPanelService.usersInApproval.length < this.employeeOptions1.length) {
              this.adminPanelService.usersInApproval.push({ value: user.userName, id: '' + user.userId ,name:user.userName});
            }
            uniqueUserIds[optionItem.userid] = user.userName;
          }
        }
      } else {
        if (this.adminPanelService.usersInApproval.length > this.employeeOptions1.length) {
          this.employeeOptions1 = this.adminPanelService.usersInApproval;
        }
      }
      let historyItem = {};

      if (reportResponse.users[optionItem.userid] && reportResponse.users[optionItem.userid].userName) {
        historyItem['name'] = reportResponse.users[optionItem.userid].userName;
      } else {
        historyItem['name'] = optionItem.userid;
      }
      historyItem['bookingDate'] = optionItem.bookingDate;
      historyItem['type'] = optionItem.type;
      if (optionItem.type === 'flight') {
        historyItem['departure'] = optionItem.option.flight_option.legs[0].hops[0].starts;
        let firstLeg = optionItem.option.flight_option.legs[0];
        let destination = firstLeg.hops[firstLeg.hops.length - 1].to;
        if (reportResponse.airports[destination]) {
          destination = reportResponse.airports[destination].city;
        }
        // let destination = optionItem.option.flight_option.legs[0].hops[0].from;
        for (let i = 1; i < optionItem.option.flight_option.legs.length - 1; i++) {
          let thisleg = optionItem.option.flight_option.legs[i];
          let lastHop = thisleg.hops[thisleg.hops.length - 1];
          if (reportResponse.airports[lastHop.to]) {
            destination = destination + ", " + reportResponse.airports[lastHop.to].city;
          } else {
            destination = destination + ", " + optionItem.option.flight_option.legs[i].hops[thisleg.hops.length - 1].to;
          }
          // destination = destination+ ", " +lastHop.to;
        }
        historyItem['destinations'] = destination;
      } else if (optionItem.type === 'hotel') {
        historyItem['departure'] = optionItem.option.hotel_option.checkInDate;
        historyItem['return'] = optionItem.option.hotel_option.checkOutDate;
        historyItem['destinations'] = optionItem.destinationCity;
      }
      else if (optionItem.type === 'cars') {
        if (optionItem.option.car_option && optionItem.option.car_option.pickUpDate) {
          historyItem['departure'] = optionItem.option.car_option.pickUpDate;
        }
        if (optionItem.option.car_option && optionItem.option.car_option.dropOffDate) {
          historyItem['return'] = optionItem.option.car_option.dropOffDate;
        }
        historyItem['destinations'] = optionItem.destinationCity;
      }
      historyItem['reviewedOn'] = optionItem.reviewedOn;
      historyItem['reviewedBy'] = optionItem.reviewedBy;
      historyItem['adminNote'] = optionItem.adminNote;
      historyItem['approved'] = optionItem.approvalStatus === 'approved' ? 'Yes' : 'No';
      this.historyList.push(historyItem);
    }
    //if(this.empValue1===''&& this.subType==='All Employees'){
    // this.adminPanelService.usersInApproval=[...this.employeeOptions1];
    //}

    if (this.originalApprovalsList.length > 0) {
      this.companyReport = this.filterSelfApprovalList(reportResponse.approvers);
      if (this.companyReport.length === 0) {
        this.resultErrorMessage = this.translateService.instant('report.NoData');
      }
    }
    this.employeeOptions1.sort(function (a, b) {
      if (a.id !== '' && b.id !== '') {
        if (a.value < b.value) { return -1; }
        if (a.value > b.value) { return 1; }
      }
      return 0;
    })
  }
  approvalUsers(users, userid,eventid) {
    let approvers = [];
    for (let key in this.approvers) {
      if(eventid && eventid===key){
        let approverList = this.approvers[key];
        for (let item of approverList) {
          for (let key1 in users) {
            let approverdetail = users[key1];
            if (key1 === item) {
              approvers.push(approverdetail.userName);
            }
          }
        }
      }else if (!eventid && userid === key) {
        let approverList = this.approvers[key];
        for (let item of approverList) {
          for (let key1 in users) {
            let approverdetail = users[key1];
            if (key1 === item) {
              approvers.push(approverdetail.userName);
            }
          }
        }
      }

    }
    return approvers;
  }
  filterSelfApprovalList(approver) {
    let companyReport = this.originalApprovalsList.filter(item => {
      let userid = item.userid;
      let approverid = this.userAccountInfoService.getUserEmail();
      for (let key in approver) {
        if (userid === key) {
          let approverList = approver[key];
          for (let approver of approverList) {
            if (approver === approverid) {
              return true;
            }
          }
        }
      }
    })
    return companyReport;
  }
  showEmployeeChanged(employee) {
    if (employee.id.length > 0) {
      this.deptValue = '' + this.apiReportResponse.users[employee.id].departmentId;
    }
  }
  getTooltip(item): boolean {
    if (item.adminNote) {
      return true;
    } else {
      return false;
    }
  }
  getDisplayDate(dateString: string, format: string): string {
    if (dateString) {
      let datePipe = new DatePipe('en-US');
      return datePipe.transform(new Date(dateString), format);
      //    return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
    } else {
      return '-';
    }
  }
  getDisplayDateForFlightAndHotelAndCar(itemType: string, dateString: string, format: string): string {
    if ('flight' === itemType.toLowerCase()) {
      return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
    } else if ('hotel' === itemType.toLowerCase()) {
      return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
    } else {
      return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
    }
  }
  
  getApprovalDetails(index) {
    let bookedOption;
    let name ;
    if(!this.isMobile1){
   bookedOption = this.companyReport[index]
     name = this.companyReport[index].name;
    }else{
       bookedOption = this.approvallist[index]
       name = this.approvallist[index].name;
    }
    this.showApprovalDetails = true;
    this.previousDate='';
    this.adminPanelService.gotoDetail = true;
    
    if(bookedOption.tripSessionId != undefined){
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            view: 'approval',
            type: 'list',
            name: name,
            bookingType: 'pending',
            userEmail: bookedOption.userid,
            ticketid: bookedOption.ticketid,
            tripid: bookedOption.tripid,
            transactionid: bookedOption.selectTransId,
            tripSessionId:bookedOption.tripSessionId
          },
          replaceUrl: true
        }
      );
    }else{
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            view: 'approval',
            type: 'list',
            name: name,
            bookingType: 'pending',
            userEmail: bookedOption.userid,
            ticketid: bookedOption.ticketid,
            tripid: bookedOption.tripid,
            transactionid: bookedOption.selectTransId
          },
          replaceUrl: true
        }
      );
    }
  }
  getApprovers(item){
    let approvers = item.join(', ');
    return approvers;
  }
  getApprovalHistoryDetails(index) {
    let bookedOption = this.apiReportResponse.approvalHistory[index];
    this.showApprovalDetails = true;
    this.adminPanelService.gotoDetail = true;
    let name = this.historyList[index].name;
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          view: 'approval',
          type: 'list',
          name: name,
          bookingType: 'history',
          userEmail: bookedOption.userid,
          ticketid: bookedOption.ticketid,
          tripid: bookedOption.tripid,
          transactionid: bookedOption.option.selectTransId
        },
        replaceUrl: true
      }
    );
  }
  ngOnDestroy() {
    this.queryParamSubscription.unsubscribe();
    // this.adminPanelService.origApprovalResponse=undefined;
    // this.adminPanelService.companySaveReport = undefined;
  }

  filterbyTripSessionId(item){
    let getBysession = [];
    
    item.forEach((a,i)=>{
      if(a.tripSessionId){
        let find = getBysession.find((e) => a.tripSessionId == e.tripSessionId);
  
        if(find == undefined || i == 0 ){
          let obj = {"data":[],"tripSessionId":""};
  
         item.forEach((b) =>{
            if(obj.data.length > 0){
              const ticketIdFind = obj.data.find(a => a.ticketed == b.ticketId );
              if(obj.data[0].tripSessionId == b.tripSessionId && ticketIdFind == undefined){
                if(a.tripSessionId == b.tripSessionId){
                  let findDummy = obj.data.find((c) => b.ticketid == c.ticketid);
                  if(findDummy == undefined){
                    obj.data.push(b);
                  };
                };
              }else{
                const ticketIdFind = obj.data.find(a => a.ticketed == b.ticketId );
                if(obj.tripSessionId == b.tripSessionId && ticketIdFind){
                  // 
                  obj.data.push(b);
                }
              }
            }else{
              if(getBysession.length > 0){
                let newfind = getBysession.find((e) => b.tripSessionId == e.tripSessionId);
                if(newfind == undefined){
                  obj.data.push(b);
                  obj.tripSessionId = b.tripSessionId;
                };
              }else{
                obj.data.push(b);
                  obj.tripSessionId = b.tripSessionId;
              }
            };
          });
          // 
          getBysession.push(obj);
        }else{
          // 
        }
      }else{
        let obj = {"data":[a]};
        getBysession.push(obj);
      }
    });
    // 
    return getBysession;
  }
}
