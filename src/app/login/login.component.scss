@import "../../variables.scss";

:host {
  width: 100%;
  display: grid;
}

.header1-inner {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: center;
  align-items: center;
  height: 90px;
  margin-bottom: 39px;
  padding-top: 30px;
}

.loaderAlign {
  position: relative;
  top: 5px;
  left: 5px;
}
.mdl-checkbox__box-outline {
  display: none !important;
}

.mdl-checkbox {
  height: auto;
  left: 50px !important;
  margin-top: 10px;
}

.mdl-checkbox__input {
  position: absolute;
  left: -9999px;
}
.agreePolicy{
  width: 60%;text-align: left;margin-left: 0px;
}
.mdl-checkbox__label {
  padding-left: 30px;
  position: relative;
  display: inline-block;
}

.mdl-checkbox__label:before,
.mdl-checkbox__label:after {
  height: 20px;
  width: 20px;
  position: absolute;
  left: 0;
  content: '';
  top: 0;
}

.mdl-checkbox__label:before {
    border: 1px solid var(--dark-bg-color);
  border-radius: 0px;
  background-color: #F7F7F7;
}

.mdl-checkbox__input:checked+.mdl-checkbox__label:after {
  background-image: url(/../../assets/images/check-icon.png);
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: center center;
}

.mdl-checkbox__label {
  font-size: 18px;
  line-height: normal;
  font-family: "apercu-r";
  margin-left: 0px;
  margin-top: 10px;
  padding-bottom: 8px;
  font-size: 16px;
}

.checkbox-container1 {
  float: left;
  width: 100%;
  margin-bottom: 25px;
  padding-left: 0px;
  margin-top: 25px;
}

.checkbox-container1 .checkboxlabel {
  color: #413E3B;
  font-family: "apercu-r";
  margin-left: 10px;
  font-size: 18px;
  line-height: 17px;
}
.microsoftMsg {
  width: 60%;
  text-align: left;
  margin: auto;
}

.modal-header1 {
  background-color: var(--hyperlink-color);
  color: #FFFFFF;
  font-size: 14px;
  height: 40px;
  display: -ms-flexbox;
  display: inline-block;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 6px 6px 0 0;
  padding: 10px 18px 0 22px !important;
  border-bottom: none;
}

.modal-header1 h5 {
  font-size: 14px;
  white-space: nowrap;
  font-family: "apercu-mono";
}

.modal-body1 {
  padding: 28px 25px 47px 25px;
  border-radius: 0 0 5px 5px;
  background-color: #FCFCFC !important;
}

.container1 {
  background-color: #FCFCFC;
  width: auto !important;
  height: auto !important;
  text-align: center !important;
  align-items: center !important;
  margin-left: 0px !important;
  margin-right: 0px !important;
  margin-right: 0px !important;
  max-width: none !important;
  padding-bottom: 100px;
}

.profileTab .tab-list-item {
  margin-right: 20px;
  margin-left: 95px;
  margin-bottom: 0;
  padding-bottom: 0;
  float: left;
}

.top-strip21 {
  background: $themeColor2;
  float: left;
  width: 100%;
  height: 43px;
}

.container {
  width: auto !important;
  height: auto !important;
  text-align: center !important;
  align-items: center !important;
  margin-left: 0px !important;
  margin-right: 0px !important;
  max-width: none !important;
  padding-bottom: 100px;
}

.map-image {
  text-align: left;
  margin-top: 5px;
  padding-left: 47px;
}
.changeEmail{
  color: var(--hyperlink-color);
  cursor: pointer;
  left: calc(75% + 0px) !important;
  top: 10px;
  position: absolute;
}
.linkstyle {
  cursor: pointer;
  color: var(--hyperlink-color);
  font-family: "apercu-mono";
}

.text-primary {
  cursor: pointer;
  font-family: "apercu-b";
  text-transform: none !important;
}

.login-container1 {
  background-color: #EDFAFC;
}

.login-container1 .button {
  font-size: 14px !important;
}

.login-container .text-primary {
  font-size: 14px !important;

}

.login-container .button {
  font-size: 14px !important;
}

.terms {
  color: var(--button-font-color);
  cursor: pointer;
  font-family: "apercu-b";
  text-transform: none !important;
  font-size: 12px !important;
}

.input-box {
  float: left;
  width: 100%;
  margin-bottom: 16px;
  position: relative;
  margin-top: 10px;
  margin-left: auto !important;
  margin-right: auto !important;
}

.input-box input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: 50px;
  border: 1px solid var(--dark-bg-color);
  font-family: "apercu-mono";
  background-color: #FFFFFF;
  border-radius: 8px;
  width: 60%;
  padding: 0 17px 0 17px;
  font-size: 16px;
  line-height: 20px;
}

.input-box-with-icon input {
  padding-left: 43px;
}

.input-box-icon-container {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  left: calc(20% + 10px) !important;
  height: 50px;
}

.button-container .button {
  margin-bottom: 16px;
}

.button-container .button:last-child {
  margin-bottom: 0;
}

.button-with-static-icon {
  position: relative;
  padding: 0 20px
}

.button-with-static-icon img {
  position: absolute;
  left: 5px;
  width: 30px;
  top: calc((100% - 30px)/2);
}

.button-container {
  width: 100%;
  text-align: center;
  align-items: center;
  margin-left: auto !important;
  margin-right: auto !important;
}

.button {
  cursor: pointer;
  border-radius: 2px !important;
  width: 60%;
  height: 50px;
  padding: 0 20px;
  border: none;
  text-transform: uppercase;
  font-size: 16px;
  letter-spacing: 0.86px;
}

.button-primary {
  color: var(--hyperlink-color);
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
}

.button-secondary {
  background: transparent;
  border: 2px solid #E7E6E4;
}

.button-white {
  border: 1px solid #E6E6E6;
  border-radius: 4px !important;
  background-color: #F5F5F5;
}

.form-image {
  width: calc(100% - 0px);
}

.redirect {
  width: auto !important;
  font-family: "apercu-mono";
  margin: 10px 0;
  font-size: 12px;
  margin-right: 10px;
}

.checkbox-container {
  float: left;
  width: 100%;
  margin-bottom: 25px;
  padding-left: 47px;
}

.checkbox-container .mdl-checkbox__label {
  font-size: 12px;
  line-height: 17px;
  margin-left: 18px;
}

.mdl-checkbox__box-outline {
  float: left;
  width: 17px !important;
  height: 17px !important;
  border: 1px solid #979797 !important;
  border-radius: 0;
}

.mdl-checkbox.is-checked .mdl-checkbox__box-outline {
  border: 1px solid var(--button-bg-color);
  ;
}

.mdl-checkbox.is-checked .mdl-checkbox__tick-outline {
  background-color: var(--button-bg-color) !important;
  ;
}

.mdl-checkbox {
  text-align: left;
  height: 100%;
  left: 20px !important;
}

.input-box-icon1-container {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  left: 155px !important;
  height: 50px;
}

@media (max-width:768px) {
  .login-container1 .text-primary {
    font-size: 14px !important;
    float: left;
    margin-left: 83px;
    margin-top: 20px;
  }

  .modal-body1 {
    background-color: #FCFCFC !important;
  }

  .container1 {
    background-color: var(--light-bg-color);
    width: auto !important;
    height: auto !important;
    text-align: center !important;
    align-items: center !important;
    margin-left: 0px !important;
    margin-right: 0px !important;
    max-width: none !important;
  }

  .container {
    background-color: var(--light-bg-color);
    width: auto !important;
    height: auto !important;
    text-align: center !important;
    align-items: center !important;
    margin-left: 0px !important;
    margin-right: 0px !important;
    max-width: none !important;
  }

  .input-box {
    float: left;
    width: 100%;
    margin-bottom: 16px;
    position: relative;
    margin-top: 10px;
    margin-left: auto !important;
    margin-right: auto !important;
  }

  .input-box input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 50px;
    border: 1px solid var(--dark-bg-color);
    ;
    font-family: "apercu-mono";
    background-color: #FFFFFF;
    border-radius: 8px;
    width: 80%;
    padding: 0 17px 0 17px;
    font-size: 16px;
    line-height: 20px;
  }

  .input-box-with-icon input {
    padding-left: 43px;
  }

  .input-box-icon1-container {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    left: 80px !important;
    height: 50px;
  }

  .input-box-icon-container {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    left: calc(10% + 10px) !important;
    height: 50px;
  }

  .button-container {
    width: 100%;
    text-align: center;
    align-items: center;
    margin-left: auto !important;
    margin-right: auto !important;
  }

  .button {
    cursor: pointer;
    border-radius: 2px !important;
    width: 78%;
    height: 40px;
    padding: 0 20px;
    border: none;
    text-transform: uppercase;
    font-size: 16px;
    letter-spacing: 0.86px;
  }

  header.inner {
    height: 90px;
  }

  .button-with-static-icon {
    padding: 0 40px;
  }

  .button-with-static-icon img {
    left: 10px;
  }

  .button-with-static-icon img {
    position: absolute;
    left: 5px;
    width: 30px;
    top: calc((100% - 30px)/2);
  }

  .redirect {
    cursor: pointer !important;
    text-transform: none !important;
    text-align: center;
    width: auto !important;
    font-size: 12px;
    font-family: "apercu-mono";
    margin: 10px 10px !important;
  }

  .redirect1 {
    cursor: pointer !important;
    text-transform: none !important;
    float: left;
    margin-left: 73px;
    margin-top: 20px;
    width: auto !important;
    font-size: 12px;
    font-family: "apercu-mono";
    margin: 10px 10px !important;
  }

  .mdl-checkbox {
    text-align: left;
    height: 100%;
  }

  .button {
    cursor: pointer;
    border-radius: 2px !important;
    width: 78%;
    height: 50px;
    padding: 0 20px;
    border: none;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.86px;
  }

  //.input-box-icon-container{ position: absolute; top: 0; display: flex; align-items: center;left: 55px !important; height: 50px;}
}

.tab-list-item a {
  cursor: pointer;
  position: relative;
  display: inline-block;
  color: #FFFFFF;
  font-size: 15px;
  line-height: 21px;
  padding: 10px 5px 13px 5px;
}
@media(max-width:1024px){ 
  .changeEmail{
    color: var(--hyperlink-color);
    cursor: pointer;
    left: calc(70% + 0px) !important;
    top: 15px;
    position: absolute;
    font-size: 12px;
}
}
@media(max-width:768px){
  .agreePolicy{
    width: 60%;
    text-align: left;
    margin-left: 80px;
    float: left;
  }
  .inputEmail{
    width: 70%;
    display: inline-block !important;
  white-space: nowrap;
  font-family: "apercu-r";
  overflow: hidden;
  text-overflow: ellipsis;
  }
  .changeEmail{
    color: var(--hyperlink-color);
    cursor: pointer;
    left: calc(75% + 0px) !important;
    top:15px;
    position: absolute;
    font-size: 12px;
}
}
@media(max-width:767px) {
  .loaderClass {
    position: relative;
    top: -5px;
  }
  
  .input-box-with-icon input {
    padding-left: 43px;
    padding-right: 55px;
  }
  .agreePolicy{
    width: 60%;
    text-align: left;
    margin-left: 60px;
    float: left;
  }
  .changeEmail{
    color: var(--hyperlink-color);
    cursor: pointer;
    left: calc(75% + 0px) !important;
    top: 15px;
    position: absolute;
    font-size: 12px;
}
  .microsoftMsg {
    width: 75%;
    text-align: left;
    margin: auto;
  }
.autocomplete{
  z-index: -1;
}
  .input-box-icon1-container {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    left: 70px !important;
    height: 50px;
  }

}
@media (max-width:600px) {
  .agreePolicy{
    width: 80%;
    text-align: left;
    margin-left: 60px;
    float: left;
  }
  .input-box-with-icon input {
    padding-left: 43px;
    padding-right: 75px;
  }
}
@media (max-width:414px) {
  .microsoftMsg {
    width: 75%;
    text-align: left;
    margin: auto;
  }
  .agreePolicy{
    width: 80%;
    text-align: left;
    margin-left: 40px;
    float: left;
  }
  .top-strip21 {
    background: $themeColor2;
    float: left;
    width: 100% !important;
    height: 43px;
  }

  header.inner {
    height: 90px;
  }

  .terms {
    font-size: 8px !important;
  }

  .login-container1 .text-primary {
    font-size: 14px !important;
    float: left;
    margin-left: 45px;
    margin-top: 20px;
  }

  .input-box-icon1-container {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    left: 60px !important;
    height: 50px;
  }

  .text-primary {
    font-size: 8px !important;
  }

  .checkbox-container .mdl-checkbox__label {
    font-size: 10px;
    line-height: 17px;
    margin-left: 18px;
  }

  .button-with-static-icon {
    padding: 0 40px;
  }

  .button-with-static-icon img {
    left: 10px;
  }

  .button {
    cursor: pointer;
    border-radius: 2px !important;
    width: 78%;
    height: 40px;
    padding: 0 20px;
    border: none;
    text-transform: uppercase;
    font-size: 10px !important;
    letter-spacing: 0.86px;
  }

  .button-with-static-icon img {
    position: absolute;
    left: 5px;
    width: 25px !important;
    top: calc((100% - 25px)/2);
  }

  .checkbox-container {
    float: left;
    width: 100%;
    margin-bottom: 10px;
    left: 0px !important;
    padding-left: 0px !important;
  }

  .mdl-checkbox {
    text-align: left;
    height: 100%;
    left: none !important;
  }

  .redirect {
    text-align: center;
    cursor: pointer !important;
    text-transform: none !important;
    width: auto !important;
    font-size: 8px;
    font-family: "apercu-mono";
    margin: 10px 10px !important;
  }

  .redirect1 {
    cursor: pointer !important;
    text-transform: none !important;
    float: left;
    margin-left: 39px;
    margin-top: 20px;
    width: auto !important;
    font-size: 12px;
    font-family: "apercu-mono";
    margin: 10px 10px !important;
  }

  .map-image {
    text-align: left;
    margin-top: 5px;
    padding-left: 17px !important;
  }

  .input-box-icon-container {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    height: 50px;
  }
}

@media (max-width:380px) {
  .login-container1 .text-primary {
    font-size: 14px !important;
    float: left;
    margin-left: 38px;
    margin-top: 20px;
  }

  .top-strip21 {
    background: $themeColor2;
    float: left;
    width: 100% !important;
    height: 43px;
  }

  .redirect1 {
    cursor: pointer !important;
    text-transform: none !important;
    float: left;
    margin-left: 33px;
    margin-top: 20px;
    width: auto !important;
    font-size: 12px;
    font-family: "apercu-mono";
    margin: 10px 10px !important;
  }
}

@media (max-width:320px) {
  .terms {
    font-size: 8px !important;
  }

  .checkbox-container .mdl-checkbox__label {
    font-size: 8px;
    line-height: 17px;
    margin-left: 18px;
  }

  .redirect {
    font-size: 8px !important;
  }

  .button-with-static-icon {
    text-align: right;
  }
}