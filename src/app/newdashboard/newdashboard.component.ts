import { Component, OnInit, ViewChild, ElementRef, QueryList } from '@angular/core';
import { SearchService } from '../search.service';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { CommonUtils } from '../util/common-utils';
import { DateUtils } from '../util/date-utils';
import { Constants } from '../util/constants';
import { DatePipe } from '@angular/common';

import ChartDataLabels from 'chartjs-plugin-datalabels';
import { CompanySettings, AdminPanelService, Department } from '../admin-panel.service';
import { Subscription } from 'rxjs';
import { UserAccountService } from '../user-account.service';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { Router } from '@angular/router';
import {  ChartDataset,  } from 'chart.js';
import 'chartjs-plugin-labels';
import { DataType } from '../entity/email-flow/barChart';
import { DeviceDetailsService } from '../device-details.service';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { BsDatepickerDirective, BsDaterangepickerDirective } from 'ngx-bootstrap/datepicker';
import { TranslateService } from '@ngx-translate/core';
import { Title } from '@angular/platform-browser';
import { MapInfoWindow } from '@angular/google-maps';
import {
  ChartData,
  ChartOptions,
  ChartType,
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend
} from 'chart.js';

ChartJS.register(ArcElement, Tooltip, Legend);
declare var convertDataToJSON1: any;
@Component({
    selector: 'app-newdashboard',
    templateUrl: './newdashboard.component.html',
    styleUrls: ['./newdashboard.component.scss'],
    standalone: false
})
export class NewdashboardComponent implements OnInit {
  @ViewChild('setHeightOfUniqueTravelerOnMobile') setHeightOfUniqueTravelerOnMobile: ElementRef;
  @ViewChild('chartDatePicker')
  private _picker: BsDaterangepickerDirective;
  @ViewChild('infoWindow') infoWindows!: QueryList<MapInfoWindow>;
  

// center is optional if using [center] directly on the template
center: google.maps.LatLngLiteral = {
  lat: 41.399115,
  lng: 2.160962
};
  activeTravellersList: Array<any> = [];
  public lineChartLabels = [];
  public lineChartData : ChartData<'line'> = {
    labels: [], 
    datasets: [
      {
        data: [],
        label: '',
        tension: 0.0,
        borderColor: '#8936F3',
        backgroundColor: 'transparent',
        fill: false,
      },
      {
        data: [],
        label: '',
        tension: 0.0,
        borderColor: '#BFBFBF',
        backgroundColor: 'transparent',
        fill: false,
      },
    ]
  };
  monthNames = ["January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];
  public lineChartOptions: any = {
    responsive: true,
  };
  public lineChartColors: any[] = [
    {
      borderColor: '#8936F3',
      backgroundColor: 'transparent',
    },
    {
      borderColor: '#BFBFBF',
      backgroundColor: 'transparent',
    },
  ];
  colors12 = [
    {
      backgroundColor: ['#29EED8', '#8936F3', '#F3A536'], borderColor: ['#29EED8', '#8936F3', '#F3A536'], borderWidth: 2,
    }];
    public pieChartData1: ChartData<'pie'> = {
      labels: [this.translateService.instant('dashboard.Disapproved'), this.translateService.instant('dashboard.Approved'), this.translateService.instant('dashboard.Expired')],
      datasets: [
        {
          data: [],
          backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],
          borderWidth: 1
        }
      ]
    };
  public pieChartLabels1: any[] = [this.translateService.instant('dashboard.Disapproved'), this.translateService.instant('dashboard.Approved'), this.translateService.instant('dashboard.Expired')];
  public lineChartLegend = true;
  public lineChartType = 'line';
  pieChartType = 'pie';
  public lineChartPlugins = [];
  pieChartOptions1: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    maintainAspectRatio: false
  };
  verticalLabelConfig = {
    anchor: 'end',
    align: 'end',
    clamp: true,
    offset: -2,
    color: 'black',
    'font-weight': 'bolder',
    display: 'auto',
    rotation: 0,
    formatter: this.formatterFn
  };
  layoutPaddingOption: any = {
    padding: {
      top: 25,
      right: 25, left: 20, bottom: 0
    }
  };
  layoutPaddingOptionWrapper: any = {
    layout: this.layoutPaddingOption
  }
  barChartOptions5: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    layout: this.layoutPaddingOption,
  };
  barChartLabels1: string[] = [];
  barChartLabels5: string[] = [];
  barChartData5: ChartData<'bar'> = {
    labels: [],
    datasets: [
      {
        data: [],
        label: '',
        backgroundColor:  [] as string[]
      }
    ]
  };
  barChartData1: ChartData<'bar'> = {
    labels: [],
    datasets: [
      {
        data: [],
        label: 'Top employees by # bookings',
        backgroundColor:  [] as string[]
      }
    ]
  };
  public chartColors22: Array<any> = [
    {
      backgroundColor: ['#29EED8'], borderColor: ['#29EED8'], borderWidth: 0.5,
    }
  ];
  public chartColors33: Array<any> = [
    {
      backgroundColor: ['#6663EA'], borderColor: ['#6663EA'], borderWidth: 0.5,
    }
  ];
  barChartType1 = 'bar';
  barChartOptions2: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    layout: this.layoutPaddingOption,
  };
  initialBoundsSet = false;
  originalResponse = [];
  previousOriginalResponse = [];
  originalNonApprovedResponse = [];
  originalResponseWithDepartmentFilter = [];
  previousOriginalResponseWithDepartmentFilter = [];
  originalNonApprovedResponseWithDepartmentFilter = [];
  maximumDate1: Date = new Date();
  zoom = 0.5;
  applyBtn = false;
  getActiveTraveler = false;
  viewMode1 = 'tab11';
  dateRangeSeleted = false;
  selectApprovalIndex = -1;
  selectBookingValue = 'all';
  averagePricePerce = [];
  weeklyData = [];
  totalSpend = 0;
  totalCompliance = '0%';
  empName1 = [];
  totalTraveler = 0;
  totalBooking = 0;
  avgPriceDay = 0;
  grandTotalSpend = 0;
  expiredCount = 0;
  grandPreviousTotalSpend = 0;
  totalPreviousSpend = 0;
  totalPreviousCompliance = '0%';
  totalPreviousTraveler = 0;
  totalPreviousBooking = 0;
  avgPreviousPriceDay = 0;
  applyBtn1 = true;
  pendingApproval = [];
  secondLabel = [];
  markerImage = 'assets/images/Ellipse.png';
  selectPreviousDate = "Previous period"
  selectPreviousDate1 = "dashboard.Previousperiod"
  startDate: Date = new Date();
  endDate: Date = new Date();
  previousStartDate: Date = new Date();
  previousEndDate: Date = new Date();
  startDate1: Date = new Date();
  endDate1: Date = new Date();
  activeTravelerStartDate: Date = new Date();
  activeTravelerEndDate: Date = new Date();
  departmentOptions = [{ value: 'All Departments', id: '',name:"fuild.AllDepartments" }];
  disableDateType = false;
  selectDateRange = 'today';
  bookingValue = 'ALL';
  deptValue = 'All Departments';
  dateOptions = Constants.DATE_OPTIONS1;
  companyApprovalSubscription: Subscription;
  defaultQuerySelection = 'CURRMONTH';
  previousOptions = [];
  select = '';
  VehicleMsg = '';
  origReportResponse: any;
  currency = 'USD';
  resultErrorMessage = 'There are no active travelers for today.';
  dateValue = this.defaultQuerySelection;
  companyId: number;
  companySettings: CompanySettings;
  companySettingsSubscription: Subscription;
  isMobile1: boolean;
  deviceSubscription1: Subscription;
  daterangepickerModel = [this.startDate, this.endDate];
  daterangepickerModel1 = [this.startDate, this.endDate];
  public doughnutChartData: ChartData<'doughnut'> ={
    labels: [this.translateService.instant('dashboard.Cars'), this.translateService.instant('dashboard.Flights'),this.translateService.instant('dashboard.Hotels')],
    datasets: [
      {
        data: [], // update as needed
        backgroundColor: ['#853BF2', '#29EED8', '#4B8AE5'],
        borderWidth: 1
      }
    ]
  };
  public doughnutChartType: ChartType = 'doughnut';
  doughnutChartOptions: any = {
    scaleShowVerticalLines: false,
    responsive: true,
    maintainAspectRatio: false
  };
  public donutColors = [
    {
      backgroundColor: [
        '#853BF2', '#29EED8',
        '#4B8AE5'
      ]
    }
  ];
  constructor(
    private adminPanelService: AdminPanelService,
    private gallopLocalStorage: GallopLocalStorageService,
    private titleService: Title,
    public deviceDetailsService: DeviceDetailsService,
    public router: Router,
   
    private searchService: SearchService,
    private userAccountService: UserAccountService,
    public ngxSmartModalService: NgxSmartModalService,
    public translateService: TranslateService,
  ) { }
  public doughnutChartLabels: any[] = [this.translateService.instant('dashboard.Cars'), this.translateService.instant('dashboard.Flights'),this.translateService.instant('dashboard.Hotels')];
  public darkStyle = [
    { elementType: "geometry", stylers: [{ color: "#ebe3cd" }] },
    { elementType: "labels.text.fill", stylers: [{ color: "#523735" }] },
    { elementType: "labels.text.stroke", stylers: [{ color: "#f5f1e6" }] },
    {
      featureType: "administrative",
      elementType: "geometry.stroke",
      stylers: [{ color: "#c9b2a6" }],
    },
    {
      featureType: "administrative.land_parcel",
      elementType: "geometry.stroke",
      stylers: [{ color: "#AEAEAE" }],
    },
    {
      featureType: "administrative.land_parcel",
      elementType: "labels.text.fill",
      stylers: [{ color: "#ae9e90" }],
    },
    {
      featureType: "landscape.natural",
      elementType: "geometry",
      stylers: [{ color: "#AEAEAE" }],
    },
    {
      featureType: "poi",
      elementType: "geometry",
      stylers: [{ color: "#AEAEAE" }],
    },
    {
      featureType: "poi",
      elementType: "labels.text.fill",
      stylers: [{ color: "#93817c" }],
    },
    {
      featureType: "poi.park",
      elementType: "geometry.fill",
      stylers: [{ color: "#fff" }],
    },
    {
      featureType: "poi.park",
      elementType: "labels.text.fill",
      stylers: [{ color: "#447530" }],
    },
    {
      featureType: "road",
      elementType: "geometry",
      stylers: [{ color: "#f5f1e6" }],
    },
    {
      featureType: "road.arterial",
      elementType: "geometry",
      stylers: [{ color: "#fdfcf8" }],
    },
    {
      featureType: "road.highway",
      elementType: "geometry",
      stylers: [{ color: "#f8c967" }],
    },
    {
      featureType: "road.highway",
      elementType: "geometry.stroke",
      stylers: [{ color: "#e9bc62" }],
    },
    {
      featureType: "road.highway.controlled_access",
      elementType: "geometry",
      stylers: [{ color: "#e98d58" }],
    },
    {
      featureType: "road.highway.controlled_access",
      elementType: "geometry.stroke",
      stylers: [{ color: "#db8555" }],
    },
    {
      featureType: "road.local",
      elementType: "labels.text.fill",
      stylers: [{ color: "#806b63" }],
    },
    {
      featureType: "transit.line",
      elementType: "geometry",
      stylers: [{ color: "#dfd2ae" }],
    },
    {
      featureType: "transit.line",
      elementType: "labels.text.fill",
      stylers: [{ color: "#8f7d77" }],
    },
    {
      featureType: "transit.line",
      elementType: "labels.text.stroke",
      stylers: [{ color: "#ebe3cd" }],
    },
    {
      featureType: "transit.station",
      elementType: "geometry",
      stylers: [{ color: "#dfd2ae" }],
    },
    {
      featureType: "water",
      elementType: "geometry.fill",
      stylers: [{ color: "#FFFFFF" }],
    },
    {
      featureType: "water",
      elementType: "labels.text.fill",
      stylers: [{ color: "#92998d" }],
    },
  ];
  mapOptions: google.maps.MapOptions = {
    gestureHandling: 'greedy',
    streetViewControl: false,
    zoomControl: true,
    styles: this.darkStyle, // your darkStyle object
  };
  mapReadyCallback(map: google.maps.Map) {
    this.fitMapToActiveTravellers(map);
  }
  ngOnInit() {
    //  this.getPreviousTimeData();
    let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
        let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);
        let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
        let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
    this.adminPanelService.fetchCompanyApprovals(this.userAccountService.getUserCompanyId(), startDate, endDate, 'all');
    this.subscribe();
    this.getData('today');

    setTimeout(() => {
      this.observable();
    }, 2000);

    this.showDateChanged('CURRMONTH', true);
  }
  fitMapToActiveTravellers(map: google.maps.Map) {
    const bounds = new google.maps.LatLngBounds();
    this.activeTravellersList.forEach(data => {
      if (data.fitBounds) {
        bounds.extend(new google.maps.LatLng(data.latitude, data.longitude));
      }
    });
    if (!bounds.isEmpty()) {
      map.fitBounds(bounds);
    }
  }
  formatter(value) {
    let retVal = value;
    if (value > 1000000) {
      retVal = Math.round(value / 1000000) + 'M';
    } else if (value > 1000) {
      retVal = Math.round(value / 1000) + 'K';
    } else {
      retVal = Math.round(value);
    }
    if (retVal === 0) {
      return '';
    }
    return retVal;
  }
  formatterFn(value, context) {
    if (context && context.dataset && context.dataset.data && context.dataset.data.length <= 62) {
      let retVal = value;
      if (value > 1000000) {
        retVal = Math.round(value / 1000000) + 'M';
      } else if (value > 1000) {
        retVal = Math.round(value / 1000) + 'K';
      } else {
        retVal = Math.round(value);
      }
      if (retVal === 0) {
        return '';
      }
      return retVal;
    } else {
      return '';
    }
  }
  zoomChanged($event) {
    //this.zoom = $event;
  }
 
  ngOnDestroy() {
if(this.deviceSubscription1){
  this.deviceSubscription1.unsubscribe();
}
if(this.companySettingsSubscription){
  this.companySettingsSubscription.unsubscribe();
}
if(this.companyApprovalSubscription){
  this.companyApprovalSubscription.unsubscribe();
}
  }
  subscribe() {
    this.deviceSubscription1 = this.deviceDetailsService.isMobile1().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
    if (this.isMobile1) {
      this.zoom = 0.2;
    } else {
      this.zoom = 0.3;
    }
    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {
        this.companySettings = settings;
        this.companyId = this.companySettings.company.company_id;
        this.currency = this.companySettings.company.currency;
        this.getDepartments();
      }
    });
    this.companyApprovalSubscription = this.adminPanelService.companyApprovalResponseObservable$.subscribe((reportResponse) => {
      if (reportResponse) {
        if (reportResponse.pendingApprovals.length > 0) {
          this.pendingApproval=[];
          for (let optionItem of reportResponse.pendingApprovals) {
            let reportItem = {};
            reportItem['name'] = reportResponse.users[optionItem.userid].userName;
            reportItem['expiryTime'] = optionItem.approvalExpiryTime;
            this.pendingApproval.push(reportItem);
          }
        } else {

        }
      } else {

      }
    });
  }
  activeUrl=this.decodeQueryParam('activeTravellers'+'&subSubType='+'30days');
  decodeQueryParam(p) {
    return decodeURIComponent(p.replace(/\+/g, " "));
  }
  boundsChangedCallback($event) {
    if (!this.initialBoundsSet) {
      setTimeout(() => {
        if (this.zoom > 15) {
          this.zoom = 15;
        }
      }, 1500);
    }
    this.initialBoundsSet = true;
  }
  getMarkerName(data) {
    return data.shortname;
  }
  changeProperty() {
    this.activeTravellersList.map(function (x) {
      x.markerClicked = false;
      return x;
    });
  }
  subscriptionEvents() {
    this.adminPanelService.originalTravellerResponse = undefined;
    this.activeTravellersList = [];
    this.adminPanelService.filterTravellerName = '';
    // this.resultErrorMessage='Fetching data';
    let endDate1 = new Date();
    endDate1.setDate(this.startDate.getDate() + 180);
    let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.activeTravelerStartDate);
    let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(endDate1);

    let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
    let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
    this.adminPanelService.navigateFrom = 'active';
    this.adminPanelService.fetchActiveTraveler(this.userAccountService.getUserCompanyId(), startDate, endDate);
  }
  getData(item) {
    this.selectDateRange = item;
    this.adminPanelService.filterDateType = this.selectDateRange;
    if (item === 'today') {
      this.activeTravelerStartDate = new Date();
      this.resultErrorMessage = 'dashboard.Therearenoactivetravelersfortoday';
      this.activeTravelerEndDate = new Date();
    } else if (item === '7Days') {
      this.activeTravelerStartDate = new Date();
      this.resultErrorMessage = 'dashboard.Therearenoactivetravelersforthenextsevendays';
      this.activeTravelerStartDate.setDate(this.activeTravelerStartDate.getDate() + 1);
      this.activeTravelerEndDate = new Date();
      this.activeTravelerEndDate.setDate(this.activeTravelerEndDate.getDate() + 7);
    }
    this.getActiveTraveler = false;
    if (!this.adminPanelService.originalTravellerResponse) {
      this.subscriptionEvents();
    } else {
      this.adminPanelService.filterActiveTraveler1()
    }
  }
  observable() {
    this.adminPanelService.activeTravellerResponseObservable$.subscribe((response) => {
      if (response) {
        this.initialBoundsSet = false;
        this.getActiveTraveler = true;
        this.origReportResponse = deserialize(response);
        if (this.origReportResponse.bookingList.length > 0) {
          // this.resultErrorMessage='Fetching data';
        } else {
          // this.resultErrorMessage='0 travelers found';
          //this.zoom=1;
        }
        this.buildCompanyReportData(this.origReportResponse);
        //this.applyBtn=false;
      } else {
        this.initialBoundsSet = false;
        this.getActiveTraveler = true;
        //this.resultErrorMessage='0 travelers found';
        //  this.applyBtn=false;
        // this.zoom=1;
      }
    });
  }
  markerClick($event, data, index) {
    this.changeProperty();
    data.markerClicked = true;
    this.titleService.setTitle(this.translateService.instant('report.TransactionsReport'));
    this.adminPanelService.from = 'dashboard';
    this.adminPanelService.selectDateRange = this.selectDateRange;
    this.gallopLocalStorage.setItem("from", this.adminPanelService.from);
    data.isRecommanded = true;
    let bookedOption = this.origReportResponse.bookingList[index];
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          view: 'detail',
          type: 'detail',
          name: name,
          bookingType: 'upcoming',
          userEmail: bookedOption.userid,
          ticketid: bookedOption.ticketid,
          tripid: bookedOption.tripid,
          transactionid: bookedOption.option.selectTransId
        },
      }
    );
    //this.activeTravellersList = this.getDimensionsByFind($event.latitude);
  }
  routeToActiveTravellers(type) {
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'employees',
          subType:'activeTravelers',
          subSubType:'30Days'
        },
        replaceUrl: false
      }
    );
  }
  getDisplayDateTimeForFlights(dateString: string, format: string): string {
    return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }
  getDisplayDate(dateString: string, format: string, type: string): string {
    if (type && type.toLowerCase() === 'flight') {
      return this.getDisplayDateTimeForFlights(dateString, format);
    }
    else if (type && type.toLowerCase() === 'cars') {
      return this.getDisplayDateTimeForFlights(dateString, format);
    } else {
      return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
    }
  }
  routeToApprovals(type, type11, item) {
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
          subType: type11
        },
        replaceUrl: false
      }
    );
  }
  selectApproval(index) {
    this.selectApprovalIndex = index;
  }
  getTimeDiffernece(time) {
    let bookingTime = new Date(time);
    let currentTime = new Date();
    let durationInMins = DateUtils.getDateDiffInMinutes(bookingTime, currentTime);
    //);
    return DateUtils.getDurationAsHrsMinObj(durationInMins);
  }
  gettime(value) {
    if (!value) { return ''; }
    var hours = new Date(value).getHours();
    var minutes = new Date(value).getMinutes();
    var ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    hours = hours > 0 ? hours : 12;
    let minutes1 = minutes < 10 ? '0' + minutes : minutes;
    var strTime = hours + ':' + minutes1 + ' ' + ampm;
    return strTime;
  }
  hideAfterExpire(time, index) {
    if (this.getTimeDiffernece(time).mins > -1) {
      return true;
    } else {
      this.expiredCount = this.expiredCount + 1;
      if (this.expiredCount === this.pendingApproval.length) {
        this.adminPanelService.approvalFinished = true;
        this.pendingApproval = [];
      }
      return false;
    }

  }
  buildCompanyReportData(response) {
    this.activeTravellersList = [];
    for (let optionItem of response.bookingList) {
      const reportItem = {};
      // const [ first, last ] = response.users[optionItem.userid].userName.split(" ");
      let travellerFullName = response.users[optionItem.userid].userName;
      if (optionItem.primaryTraveller && optionItem.primaryTraveller.userName) {
        travellerFullName = optionItem.primaryTraveller.userName;
      }
      reportItem['name'] = travellerFullName;
      const [first, last] = travellerFullName.split(' ');
      reportItem['shortname'] = (first.charAt(0) + last.charAt(0));
      let departmentName = this.adminPanelService.getDepartmentName(response.users[optionItem.userid].departmentId);
      reportItem['department'] = departmentName;
      reportItem['bookingDate'] = optionItem.bookingDate;
      reportItem['type'] = optionItem.type;
      reportItem['destinations'] = optionItem.destinationCity;
      let location = optionItem.location;
      const [lat, long] = location.split(",");
      reportItem['latitude'] = Number.parseFloat(lat);
      // this.latitudeForDestination= Number.parseFloat(lat);
      reportItem['longitude'] = Number.parseFloat(long);
      // this.longitudeForDestination= Number.parseFloat(long);
      reportItem['fitBounds'] = true;
      if (optionItem.type === 'flight') {
        reportItem['departure'] = optionItem.option.flight_option.legs[0].hops[0].starts;
        let lastLeg = optionItem.option.flight_option.legs[optionItem.option.flight_option.legs.length - 1];
        let firstHop = lastLeg.hops[0];
        let lastHop = lastLeg.hops[lastLeg.hops.length - 1];

        if (lastHop.to == optionItem.option.flight_option.legs[0].hops[0].from) {
          reportItem['return'] = firstHop.starts;
        } else {
          reportItem['return'] = firstHop.starts;
        }
      } else if (optionItem.type === 'hotel') {
        reportItem['departure'] = optionItem.option.hotel_option.checkInDate;
        reportItem['return'] = optionItem.option.hotel_option.checkOutDate;
      } else if (optionItem.type === 'cars') {
        if (optionItem.option.car_option && optionItem.option.car_option.pickUpDate) {
          reportItem['departure'] = optionItem.option.car_option.pickUpDate;
        }
        if (optionItem.option.car_option && optionItem.option.car_option.dropOffDate) {
          reportItem['return'] = optionItem.option.car_option.dropOffDate;
        }
      }
      reportItem['markerClicked'] = false;
      reportItem['ticketNumber'] = optionItem.ticketnumber ? optionItem.ticketnumber : null;
      this.activeTravellersList.push(reportItem);
    }
  }
  customTabClicked() {
    this.viewMode1 = 'tab11';
  }
  presetsTabClicked() {
    this.viewMode1 = 'tab12';
  }
  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
    this.dateRangeSeleted = false;
    this.ngxSmartModalService.close('daterangeSelection');
  }
  getPreviousDropdownStyle(item) {
    if (this.selectPreviousDate === item.peroid) {
      return { 'background': '#D3CCDE' }
    } else {
      return { 'background': '#F7F7F9' }
    }
  }
  selectPreviousDateRange(item) {
    this.selectPreviousDate = item.peroid;
    if(item.peroid === 'Previous period'){
      this.selectPreviousDate1 = 'dashboard.Previousperiod'
    }else  if(item.peroid === 'Previous month'){
      this.selectPreviousDate1 = 'dashboard.Previousmonth'
    }else  if(item.peroid === 'Previous quarter'){
      this.selectPreviousDate1 = 'dashboard.Previousquarter'
    }else  if(item.peroid === 'Previous year'){
      this.selectPreviousDate1 = 'dashboard.Previousyear'
    }
    const [star, end] = item.date.split('-');
    this.previousStartDate = new Date(star);
    this.previousEndDate = new Date(end);
    // 
    this.dateRangeSeleted = true;
    if(this.ngxSmartModalService.getModal('daterangeSelection1')){
      this.ngxSmartModalService.getModal('daterangeSelection1').close();
      }
   // this.ngxSmartModalService.close('daterangeSelection1');
    this.getPreviousChart();
  }
  setStartDate(date, item) {
    //
    if (date) {
      // this.daterangepickerModel =date;
      this.startDate = date[0];
      this.endDate = date[1];
      // this.dateRangeSeleted =false;
      if (this.startDate > this.endDate) {
        this.endDate = date;
      }
      this.getPreviousTimeData();
      setTimeout(() => {
        this.getCurrentChart();
        if (this.selectPreviousDate === 'Previous period') {
          this.selectPreviousDateRange(this.previousOptions[0]);
        } else if (this.selectPreviousDate === 'Previous month') {
          this.selectPreviousDateRange(this.previousOptions[1]);
        } else if (this.selectPreviousDate === 'Previous quarter') {
          this.selectPreviousDateRange(this.previousOptions[2]);
        } else if (this.selectPreviousDate === 'Previous year') {
          this.selectPreviousDateRange(this.previousOptions[3]);
        }
      }, 200);
    }
  }
  changeStyle() {
    if (!this.isMobile1) {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '730px' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '99vw' }
      }
    }
  }
  changeStyle1() {
    if (!this.isMobile1) {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '335px', 'overflow': 'hidden' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '700px', 'overflow': 'hidden' }
      }
    }
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  openNgxModal(id, picker) {
    if (this.disableDateType) {
      return;
    }
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);

    setTimeout(() => {
      this.viewMode1 = 'tab11';
      picker.show();
    }, 200);
  }
  openNgxModal1(id) {
    if (this.disableDateType) {
      return;
    }
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);

  }
  onMouseOver(infoWindow, $event: MouseEvent) {
    infoWindow.open();
  }

  onMouseOut(infoWindow, $event: MouseEvent) {
    infoWindow.close();
  }
  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    const dayHoverHandler = event.dayHoverHandler;
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        this.searchService.hoverCounter++;
        if (this.searchService.hoverCounter > 1) {
          (this._picker as any)._datepickerRef.instance.daySelectHandler(cell);
        }
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }

  showDateChanged(id, item) {
    this.dateRangeSeleted = item;
    if (id === 'CURRMONTH') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(1);
      this.startDate.setMonth(this.startDate.getMonth());
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    }else if (id === 'TODAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    }else if (id === 'YESTERDAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 1);
      this.endDate.setDate(this.endDate.getDate() - 1);
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    }else if (id === 'Last 12 months') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.endDate.setDate(this.endDate.getDate() - 1);
      for (var i = 0; i < 12; i++) {
        this.startDate.setMonth(this.startDate.getMonth() - 1);
      }
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    }
    else if (id === 'Currentquarter') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.getQuarter(this.startDate);
      this.daterangepickerModel = [this.startDate, this.endDate];
      //  this.getPreviousTimeData();
    } else if (id === 'Currentyear') {
      this.endDate = new Date();
      this.startDate = new Date();
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 0, 1);
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    }
    else if (id === 'LAST7DAYS') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 6);
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    } else if (id === 'LAST30DAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 29);
      this.daterangepickerModel = [this.startDate, this.endDate];
      this.getPreviousTimeData();
    } else if (id === 'LAST90DAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 89);
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    } else if (id === 'LASTMONTH') {
      this.startDate = new Date();
      this.startDate.setDate(1);
      this.startDate.setMonth(this.startDate.getMonth() - 1);
      this.endDate = new Date(this.startDate.getFullYear(), this.startDate.getMonth() + 1, 0);
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    } else if (id === 'CUSTOMDATE') {

    }
    // this.getCurrentChart();
    //  this.getPreviousChart();
    if (this.ngxSmartModalService.getOpenedModals() &&
      (this.ngxSmartModalService.getOpenedModals().length > 0)
    ) {
      let modals = this.ngxSmartModalService.getOpenedModals();
      for (let index = 0; index < modals.length; index++) {
        if (modals[index].id === 'daterangeSelection') {
          this.ngxSmartModalService.close('daterangeSelection');
        }
      }
    }
    this.viewMode1 = 'tab11';
    // this.getChart();
  }
  getQuarter(date) {
    var m = 0;
    var n = ((date.getMonth() / 3));
    var decimal = ((date.getMonth() % 3))
    if (n >= 0 && n < 1) {
      m = 1;
    } else if (n >= 1 && n < 2) {
      m = 2;
    } else if (n >= 2 && n < 3) {
      m = 3;
    } else if (n >= 3 && n < 4) {
      m = 4;
    }
    var quarter;
    if (m > 4) {
      quarter = (m - 4);
    } else {
      quarter = m;
    }
    if (m && m === 1) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 0, 1);
    } else if (m && m === 2) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 3, 1);
    }
    else if (m && m === 3) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 6, 1);
    }
    else if (m && m === 4) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 9, 1);
    }
  }
  getDepartments() {
    this.departmentOptions = [];
    if (this.companySettings && this.companySettings.departments) {
      let apiDepartmentList: Array<Department> = this.companySettings.departments;
      for (let department of apiDepartmentList) {
        this.departmentOptions.push({ value: department.name, id: '' + department.departmentId ,name:department.name});
      }
    }
    if (this.departmentOptions && this.departmentOptions.length > 0) {
      this.departmentOptions = this.sortListDept(this.departmentOptions);
      this.departmentOptions.unshift({ value: 'All Departments', id: 'all',name:"fuild.AllDepartments" });
    }
    // 
  }
  sortListDept(data) {
    data.sort(function (a, b) {
      if (a.value < b.value) { return -1; }
      if (a.value > b.value) { return 1; }
      return 0;
    })
    return data;
  }
  showDepartmentChanged(event) {
    
    if (event !== 'All Departments') {
      this.originalResponseWithDepartmentFilter = this.originalResponse.filter(item => item['Department'] === event);
      this.originalNonApprovedResponseWithDepartmentFilter = this.originalNonApprovedResponse.filter(item => item['Department'] === event);
      this.previousOriginalResponseWithDepartmentFilter = this.previousOriginalResponse.filter(item => item['Department'] === event);
      this.getCurrentGrandTotal(this.originalResponseWithDepartmentFilter);
      this.getPreviousGrandTotal(this.previousOriginalResponseWithDepartmentFilter);
      this.createWeekData(this.originalResponseWithDepartmentFilter);
      this.getSelectedGraph(this.originalResponseWithDepartmentFilter, this.originalNonApprovedResponseWithDepartmentFilter, this.previousOriginalResponseWithDepartmentFilter, this.selectBookingValue, true);
    } else {
      this.originalResponseWithDepartmentFilter = this.originalResponse;
      this.originalNonApprovedResponseWithDepartmentFilter = this.originalNonApprovedResponse;
      this.previousOriginalResponseWithDepartmentFilter = this.previousOriginalResponse;
      this.getCurrentGrandTotal(this.originalResponseWithDepartmentFilter);
      this.getPreviousGrandTotal(this.previousOriginalResponseWithDepartmentFilter);
      this.createWeekData(this.originalResponseWithDepartmentFilter);
      this.getSelectedGraph(this.originalResponseWithDepartmentFilter, this.originalNonApprovedResponseWithDepartmentFilter, this.previousOriginalResponseWithDepartmentFilter, this.selectBookingValue, true);
    }

  }
  getSelectedGraph(resp, resp2, resp3, item, item2) {
    if (item === 'all') {
      this.createChartData(resp, resp2, item2, 'currentData');
      this.createChartData(resp3, [], item2, 'previousData');
      // this.createPreviousChartData(resp3,item2);
    } else if (item == 'flight') {
      this.VehicleMsg = this.translateService.instant("dashboard.TopAirlines")
      let response = resp.filter(item => (item['Booking Type'] === 'Air'));
      let response2 = resp2.filter(item => (item['Booking Type'] === 'Air'));
      let response3 = resp3.filter(item => (item['Booking Type'] === 'Air'));
      this.createChartData(response, response2, item2, 'currentData');
      // this.createPreviousChartData(response3,item2);
      this.createChartData(response3, [], item2, 'previousData');
    } else if (item == 'hotel') {
      this.VehicleMsg = this.translateService.instant("dashboard.TopHotelChains")
      let response = resp.filter(item => (item['Booking Type'] === 'Hotels'));
      let response2 = resp2.filter(item => (item['Booking Type'] === 'Hotels'));
      this.createChartData(response, response2, item2, 'currentData');
      let response3 = resp3.filter(item => (item['Booking Type'] === 'Hotels'));
      //  this.createPreviousChartData(response3,item2);
      this.createChartData(response3, [], item2, 'previousData');
    } else if (item == 'car') {
      this.VehicleMsg = this.translateService.instant("dashboard.TopVehicleTypes")
      let response = resp.filter(item => (item['Booking Type'] === 'Cars'));
      let response2 = resp2.filter(item => (item['Booking Type'] === 'Cars'));
      this.createChartData(response, response2, item2, 'currentData');
      let response3 = resp3.filter(item => (item['Booking Type'] === 'Cars'));
      //this.createPreviousChartData(response3,item2);
      this.createChartData(response3, [], item2, 'previousData');
    }
  }
  getselectedbookingValue(item) {
    if (this.disableDateType) {
      return;
    }
    this.selectBookingValue = item;
    if (this.deptValue === 'All Departments') {
      this.originalResponseWithDepartmentFilter = this.originalResponse;
      this.originalNonApprovedResponseWithDepartmentFilter = this.originalNonApprovedResponse;
      this.previousOriginalResponseWithDepartmentFilter = this.previousOriginalResponse;
      this.getSelectedGraph(this.originalResponseWithDepartmentFilter, this.originalNonApprovedResponseWithDepartmentFilter, this.previousOriginalResponseWithDepartmentFilter, item, false);
    } else {
      this.showDepartmentChanged(this.deptValue);
    }

  }
  getPreviousTimeData() {
    for (let counter = 0; counter < 4; counter++) {
      let previousDate = { peroid: '', date: '',peroid1:'' }
      if (counter == 0) {
        this.endDate1 = new Date(this.startDate);
        this.endDate1.setDate((this.endDate1.getDate() - 1));
        const oneDay = 24 * 60 * 60 * 1000;
        var diff = Math.round(Math.abs((this.startDate.getTime() - this.endDate.getTime()) / oneDay));
        if (diff > 0) {
          this.startDate1 = new Date(this.endDate1.getFullYear(), (this.endDate1.getMonth()), (this.endDate1.getDate() - diff))
        } else {
          this.startDate1 = new Date(this.endDate1.getFullYear(), (this.endDate1.getMonth()), (this.endDate1.getDate() - diff));
        }
        this.daterangepickerModel1 = [this.startDate1, this.endDate1];
        let datePipe = new DatePipe('en-US');
       previousDate.peroid = 'Previous period'
        previousDate.peroid1 = 'dashboard.Previousperiod';
        previousDate.date = datePipe.transform(new Date(this.daterangepickerModel1[0]), 'dd MMM yyyy') + ' - ' + datePipe.transform(new Date(this.daterangepickerModel1[1]), 'dd MMM yyyy');
        this.previousOptions[counter] = previousDate;
      } else if (counter == 1) {
        // ,(this.endDate.getMonth()),(this.endDate.getDate()))
        let firtsSelectedMonth = (this.endDate.getMonth() - 1) >= 0 ? (this.endDate.getMonth() - 1) : ((12 + this.endDate.getMonth()) - 1);
        let endDate12 = new Date(this.endDate.getFullYear(), (this.endDate.getMonth() - 1), (this.endDate.getDate()));
        let secondSelectedMonth = (endDate12.getMonth())
        if (firtsSelectedMonth === secondSelectedMonth) {
          this.endDate1 = new Date(this.endDate.getFullYear(), (this.endDate.getMonth() - 1), (this.endDate.getDate()));
        } else {
          this.endDate1 = new Date(this.endDate.getFullYear(), this.endDate.getMonth(), 0);
        }
        this.startDate1 = new Date(this.startDate.getFullYear(), (this.startDate.getMonth() - 1), (this.startDate.getDate()));
        this.daterangepickerModel1 = [this.startDate1, this.endDate1];
        let datePipe = new DatePipe('en-US');
        previousDate.peroid = 'Previous month'
        previousDate.date = datePipe.transform(new Date(this.daterangepickerModel1[0]), 'dd MMM yyyy') + ' - ' + datePipe.transform(new Date(this.daterangepickerModel1[1]), 'dd MMM yyyy');
        previousDate.peroid1 = 'dashboard.Previousmonth',
        this.previousOptions[counter] = previousDate;
      } else if (counter == 2) {
        // ,(this.endDate.getMonth()),(this.endDate.getDate())) if()
        let firtsSelectedMonth = (this.endDate.getMonth() - 3) >= 0 ? (this.endDate.getMonth() - 3) : ((12 + this.endDate.getMonth()) - 3);
        let endDate12 = new Date(this.endDate.getFullYear(), (this.endDate.getMonth() - 3), (this.endDate.getDate()));
        let secondSelectedMonth = (endDate12.getMonth())
        if (firtsSelectedMonth === secondSelectedMonth) {
          this.endDate1 = new Date(this.endDate.getFullYear(), (this.endDate.getMonth() - 3), (this.endDate.getDate()));
        } else {
          this.endDate1 = new Date(this.endDate.getFullYear(), this.endDate.getMonth() - 2, 0);
        }
        this.startDate1 = new Date(this.startDate.getFullYear(), (this.startDate.getMonth() - 3), (this.startDate.getDate()));
        this.daterangepickerModel1 = [this.startDate1, this.endDate1];
        let datePipe = new DatePipe('en-US');
        previousDate.peroid = 'Previous quarter';
        previousDate.peroid1  = 'dashboard.Previousquarter'
        previousDate.date = datePipe.transform(new Date(this.daterangepickerModel1[0]), 'dd MMM yyyy') + ' - ' + datePipe.transform(new Date(this.daterangepickerModel1[1]), 'dd MMM yyyy');
        this.previousOptions[counter] = previousDate;
      } else if (counter == 3) {
        // ,(this.endDate.getMonth()),(this.endDate.getDate()))
        var previousYear = (this.startDate.getFullYear() - 1);
        var previousEndYear = (this.endDate.getFullYear() - 1);
        this.startDate1 = new Date(previousYear, this.startDate.getMonth(), (this.startDate.getDate()));
        this.endDate1 = new Date((this.endDate.getFullYear() - 1), this.endDate.getMonth(), (this.endDate.getDate()));
        this.daterangepickerModel1 = [this.startDate1, this.endDate1];
        let datePipe = new DatePipe('en-US');
        previousDate.peroid = 'Previous year',
        previousDate.peroid1  = 'dashboard.Previousyear'
        previousDate.date = datePipe.transform(new Date(this.daterangepickerModel1[0]), 'dd MMM yyyy') + ' - ' + datePipe.transform(new Date(this.daterangepickerModel1[1]), 'dd MMM yyyy');
        this.previousOptions[counter] = previousDate;
      }

    }
  }
  PreviousTimeSelected(event) {

  }
  setDefaultGrouping() {
    const oneDay = 24 * 60 * 60 * 1000;
    var diff = Math.round(Math.abs((this.startDate.getTime() - this.endDate.getTime()) / oneDay));
    if (diff > 2700) {
      this.select = 'year';
    } else if (diff > 90) {
      this.select = 'month';
    } else if (diff <= 90) {
      this.select = 'days';
    }
  }
  getCurrentChart() {
    this.doughnutChartData = {
      labels: [this.translateService.instant('dashboard.Cars'), this.translateService.instant('dashboard.Flights'),this.translateService.instant('dashboard.Hotels')],
      datasets: [
        {
          data: [0, 0, 0], // update as needed
          backgroundColor: ['#853BF2', '#29EED8', '#4B8AE5'],
          borderWidth: 1
        }
      ]
    };
    this.originalNonApprovedResponse = [];
    this.originalResponse = [];
    this.previousOriginalResponse = [];
    // this.resetValue();
    this.disableDateType = true;
    this.applyBtn = true;
    this.applyBtn1 = true;
    this.lineChartData={
      labels: [], 
      datasets: [
        {
          data: [],
          label: '',
          tension: 0.0,
          borderColor: '#8936F3',
          backgroundColor: 'transparent',
          fill: false,
        },
        {
          data: [],
          label: '',
          tension: 0.0,
          borderColor: '#BFBFBF',
          backgroundColor: 'transparent',
          fill: false,
        },
      ]
    };;
    // this.lineChartLabels = [];
    let startDate = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
    let endDate = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);
    this.setDefaultGrouping();
    this.adminPanelService.getChartData(endDate, startDate, this.companyId, this.deptValue, this.bookingValue, this.select, this.dateValue, this.userAccountService.getUserEmail()).subscribe(res => {
      if (res) {
        //  
        let response = convertDataToJSON1(res.data.approved, ';');
        let response2 = convertDataToJSON1(res.data.notApproved, ';');
        let originalresponse2 = response2.slice(1);
        let originalresponse = response.slice(1);
        this.originalResponse = originalresponse;
        if (this.originalResponse.length !== 0) {
          this.lineChartLabels = [];
        }
        this.originalNonApprovedResponse = originalresponse2;
        this.originalResponseWithDepartmentFilter = originalresponse;
        this.originalNonApprovedResponseWithDepartmentFilter = originalresponse2;
        
        
        if (this.deptValue !== 'All Departments') {
          if (this.originalResponse.length > 0) {
            let originalResponseWithDepartmentFilter1 = this.originalResponse.filter(item => item['Department'] === this.deptValue);
            this.getCurrentGrandTotal(originalResponseWithDepartmentFilter1);
            this.createWeekData(originalResponseWithDepartmentFilter1);
          }
          if (this.selectBookingValue === 'all') {
            if (this.originalResponse.length > 0) {
              this.originalResponseWithDepartmentFilter = this.originalResponse.filter(item => item['Department'] === this.deptValue);
            }
            if (this.originalNonApprovedResponse.length > 0) {
              this.originalNonApprovedResponseWithDepartmentFilter = this.originalNonApprovedResponse.filter(item => item['Department'] === this.deptValue);
            }
          } else {
            let selectType;
            if (this.selectBookingValue === 'flight') {
              selectType = 'Air';
            } else if (this.selectBookingValue === 'car') {
              selectType = 'Cars';
            } else if (this.selectBookingValue === 'hotel') {
              selectType = 'Hotels';
            }
            if (this.originalResponse.length > 0) {
              this.originalResponseWithDepartmentFilter = this.originalResponse.filter(item => (item['Department'] === this.deptValue && item['Booking Type'] === selectType));
            }
            if (this.originalNonApprovedResponse.length > 0) {
              this.originalNonApprovedResponseWithDepartmentFilter = this.originalNonApprovedResponse.filter(item => (item['Department'] === this.deptValue && item['Booking Type'] === selectType));
            }
          }
        } else {
          this.getCurrentGrandTotal(this.originalResponse);
          this.createWeekData(this.originalResponse);
          if (this.selectBookingValue === 'all') {
            this.originalResponseWithDepartmentFilter = this.originalResponse;
            this.originalNonApprovedResponseWithDepartmentFilter = this.originalNonApprovedResponse;
          } else {
            let selectType;
            if (this.selectBookingValue === 'flight') {
              selectType = 'Air';
            } else if (this.selectBookingValue === 'car') {
              selectType = 'Cars';
            } else if (this.selectBookingValue === 'hotel') {
              selectType = 'Hotels';
            }
            if (this.originalResponse.length > 0) {
              this.originalResponseWithDepartmentFilter = this.originalResponse.filter(item => (item['Booking Type'] === selectType));
            }
            if (this.originalNonApprovedResponse.length > 0) {
              this.originalNonApprovedResponseWithDepartmentFilter = this.originalNonApprovedResponse.filter(item => (item['Booking Type'] === selectType));
            }
          }
        }
        this.createChartData(this.originalResponseWithDepartmentFilter, this.originalNonApprovedResponseWithDepartmentFilter, false, 'currentData');
      }


    });
  }
 getLocalMidNightDate(isoDateString){
    const d=new Date(isoDateString);
    let date = '' + (d.getDate());
    let month = '' + (d.getMonth()+1);
    const year = d.getFullYear();
    if (parseInt(month) < 10) {
      month = '0' + month;
    }
    if (parseInt(date) < 10) {
      date = '0' + date;
    }
    return (new Date(year + '-' + month + '-'  + date + ' 00:00:00'));
}
  groupByTimePeriod(obj, timestamp, period) {
    var objPeriod = {};
    var oneDay = 24 * 60 * 60 * 1000;
    for (var i = 0; i < obj.length; i++) {
      var d = new Date(obj[i][timestamp]);
          
      
      let index1;
      if (period == 'days') {
        index1 = Math.floor((this.getLocalMidNightDate(obj[i][timestamp]).getTime())/ (24 * 60 * 60 * 1000));
      } else if (period == 'week') {
        //  / (oneDay * 7)),d.getTime(),period);
        index1 = Math.round((this.getLocalMidNightDate(obj[i][timestamp]).getTime()) / (oneDay * 7));
        index1 = this.getWeekRangeForGivenDate(d);
      } else if (period == 'month') {
        index1 = (d.getFullYear() - 1970) * 12 + d.getMonth();
      } else if (period == 'year') {
        index1 = d.getFullYear();
      }
      objPeriod[index1] = objPeriod[index1] || [];
      objPeriod[index1].push(obj[i]);
    }
    return objPeriod;
  }
  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }
  resetValue(resp) {
    this.pieChartData1 = {
      labels: [this.translateService.instant('dashboard.Disapproved'), this.translateService.instant('dashboard.Approved'), this.translateService.instant('dashboard.Expired')],
      datasets: [
        {
          data: [],
          backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],
          borderWidth: 1
        }
      ]
    };;
    this.lineChartData ={
      labels: [], 
      datasets: [
        {
          data: [],
          label: '',
          tension: 0.0,
          borderColor: '#8936F3',
          backgroundColor: 'transparent',
          fill: false,
        },
        {
          data: [],
          label: '',
          tension: 0.0,
          borderColor: '#BFBFBF',
          backgroundColor: 'transparent',
          fill: false,
        },
      ]
    };
    this.avgPriceDay = 0;
    this.doughnutChartData = {
      labels: [this.translateService.instant('dashboard.Cars'), this.translateService.instant('dashboard.Flights'),this.translateService.instant('dashboard.Hotels')],
      datasets: [
        {
          data: [], // update as needed
          backgroundColor: ['#853BF2', '#29EED8', '#4B8AE5'],
          borderWidth: 1
        }
      ]
    };
    this.empName1 = [];
    this.barChartData1 = {
      labels: [],
      datasets: [
        {
          data: [],
          label: 'Top employees by # bookings',
          backgroundColor: [] as string[]
        }
      ]
    };;
    this.barChartLabels1 = [];
    this.barChartData5 = {
      labels: [],
      datasets: [
        {
          data: [],
          label: '',
          backgroundColor:  [] as string[]
        }
      ]
    };;
    this.barChartLabels5 = [];
    if (resp && resp.length > 0) {
      this.lineChartLabels = [];
    }
    this.totalCompliance = '0%';
    this.totalSpend = 0;
    this.totalTraveler = 0;
    this.totalBooking = 0;
  }
  changeUniqueStyle(index) {
    if (index > 1 && (index !== this.weeklyData.length - 1)) {
      return { 'margin-top': '0px', 'margin-bottom': '0px' }
    } else if ((index === this.weeklyData.length - 1)) {
      return { 'margin-top': '0px', 'margin-bottom': '0px' }
    } else {
      return { 'margin-top': '0px', 'margin-bottom': '0px' }
    }
  }
  resetPreviousValue() {
    this.lineChartData={
      labels: [], 
      datasets: [
        {
          data: [],
          label: '',
          tension: 0.0,
          borderColor: '#8936F3',
          backgroundColor: 'transparent',
          fill: false,
        },
        {
          data: [],
          label: '',
          tension: 0.0,
          borderColor: '#BFBFBF',
          backgroundColor: 'transparent',
          fill: false,
        },
      ]
    };;
    if (!this.applyBtn1 && this.originalResponse.length == 0) {
      this.lineChartLabels = [];
      this.lineChartData={
        labels: [], 
        datasets: [
          {
            data: [],
            label: '',
            tension: 0.0,
            borderColor: '#8936F3',
            backgroundColor: 'transparent',
            fill: false,
          },
          {
            data: [],
            label: '',
            tension: 0.0,
            borderColor: '#BFBFBF',
            backgroundColor: 'transparent',
            fill: false,
          },
        ]
      };;
    }
    this.secondLabel = [];
    this.totalPreviousCompliance = '0%';
    this.avgPreviousPriceDay = 0;
    this.totalPreviousSpend = 0;
    this.totalPreviousTraveler = 0;
    this.totalPreviousBooking = 0;
  }
  getCurrentGrandTotal(resp) {
    let totalFlightSpend = 0;
    this.grandTotalSpend = 0;
    let totalCarSpend = 0;
    let totalHotelSpend = 0;
    for (let item of resp) {
      if (item['Booking Type'] === 'Air') {
        totalFlightSpend = totalFlightSpend + Number(item['-Spend']);
      } else if (item['Booking Type'] === 'Cars') {
        totalCarSpend = totalCarSpend + Number(item['-Spend']);
      } else if (item['Booking Type'] === 'Hotels') {
        totalHotelSpend = totalHotelSpend + Number(item['-Spend']);
      }
    }


    this.grandTotalSpend = totalFlightSpend + totalCarSpend + totalHotelSpend;
  }
  getPreviousGrandTotal(resp) {
    let totalFlightSpend = 0;
    this.grandPreviousTotalSpend = 0;
    let totalCarSpend = 0;
    let totalHotelSpend = 0;
    for (let item of resp) {
      if (item['Booking Type'] === 'Air') {
        totalFlightSpend = totalFlightSpend + Number(item['-Spend']);
      } else if (item['Booking Type'] === 'Cars') {
        totalCarSpend = totalCarSpend + Number(item['-Spend']);
      } else if (item['Booking Type'] === 'Hotels') {
        totalHotelSpend = totalHotelSpend + Number(item['-Spend']);
      }
    }


    this.grandPreviousTotalSpend = totalFlightSpend + totalCarSpend + totalHotelSpend;
  }
  createDoughNutGraph(resp) {
    this.totalCompliance = '0%';
    let calculateValues = {
      totalFlightSpend: 0, totalCarSpend: 0, totalHotelSpend: 0, totalBooking: 0, statusCancelled: 0, travelerNumber: [],
      carCounter: 0, hotelCounter: 0, flightCounter: 0, compliance: 0, employeeNumber: []
    }
    calculateValues = this.calculateBookingSpendApproval(resp)

    this.doughnutChartData.datasets[0].data = [
      calculateValues.totalCarSpend,
      calculateValues.totalFlightSpend,
      calculateValues.totalHotelSpend
    ];

    console.log("doughnut data",this.doughnutChartData);
    if (this.selectBookingValue === 'all') {
      this.donutColors = [
        {
          backgroundColor: [
            '#853BF2', '#29EED8',
            '#4B8AE5'
          ]
        }
      ];
    } else if (this.selectBookingValue === 'car') {
      this.donutColors = [
        {
          backgroundColor: [
            '#853BF2', '#BFBFBF',
            '#BFBFBF'
          ]
        }
      ];
    } else if (this.selectBookingValue === 'flight') {
      this.donutColors = [
        {
          backgroundColor: [
            '#BFBFBF', '#29EED8',
            '#BFBFBF'
          ]
        }
      ];
    } else if (this.selectBookingValue === 'hotel') {
      this.donutColors = [
        {
          backgroundColor: [
            '#BFBFBF', '#BFBFBF',
            '#4B8AE5'
          ]
        }
      ];
    }
    if (calculateValues.totalCarSpend === 0 && calculateValues.totalFlightSpend == 0 && calculateValues.totalHotelSpend == 0) {
      this.doughnutChartData = {
        labels: [this.translateService.instant('dashboard.Cars'), this.translateService.instant('dashboard.Flights'),this.translateService.instant('dashboard.Hotels')],
        datasets: [
          {
            data: [0, 0, 0], // update as needed
            backgroundColor: ['#853BF2', '#29EED8', '#4B8AE5'],
            borderWidth: 1
          }
        ]
      };
    }
    this.averagePricePerce[0] = ((calculateValues.totalCarSpend * 100) / this.grandTotalSpend).toFixed(2);
    this.averagePricePerce[1] = ((calculateValues.totalFlightSpend * 100) / this.grandTotalSpend).toFixed(2);
    this.averagePricePerce[2] = ((calculateValues.totalHotelSpend * 100) / this.grandTotalSpend).toFixed(2);
    let self3 = this;
    this.doughnutChartOptions = {
      cutoutPercentage: 20,
      legend: {
        display: false,
        responsive: true
      },
      maintainAspectRatio: true,
      plugins: {
        labels: {
          render: function (args) {
            return ' \n' + args.label + " " + self3.getCurrencySymbol(self3.currency) + self3.formatter(args.value) + '\n' + "(" + args.percentage + "%" + ")";
          },
          position: 'outside',
          arc: false,
          fontSize: 11,
          showZero: false,
          fontFamily: 'apercu-r',
          textShadow: true,
          shadowColor: '#E8D7FD',
          fontColor: "black",
          overlap: true,
          outsidePadding: 20,
          shadowBlur: 10
        }
        ,
        maintainAspectRatio: true,
        datalabels: {
          display: false
        }
      },
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            let value = self3.doughnutChartLabels[tooltipItem.index];
            let range = self3.doughnutChartData.datasets[0].data[tooltipItem.index];
            let average = self3.averagePricePerce[tooltipItem.index];
            return (value + '( ' + self3.getCurrencySymbol(self3.currency) + Math.round(range).toLocaleString() + ", " + average + '%' + ' )');
          }
        }
      }
    }
  }
  dateMissingFromBegining(date1) {
    let startDate = this.startDate;
    date1 = new Date(date1);
    if (date1.getTime() > startDate.getTime()) {
      const oneDay = 24 * 60 * 60 * 1000;
      var currntDate = Math.floor(Math.abs((this.getLocalMidNightDate(date1).getTime() - this.getLocalMidNightDate(startDate).getTime()) / oneDay));
      for (let counter = 0; counter < currntDate; counter++) {
        let prevDateObj = new Date(startDate);
        prevDateObj.setDate(prevDateObj.getDate() + counter);
        let missingDate = new DatePipe('en-US').transform(prevDateObj,
          'MMM d, EEE');
        
       if(this.lineChartLabels.find( item =>item ===missingDate) === undefined){
        this.lineChartLabels.push(missingDate);
        this.lineChartData.datasets[0].data.push(0);
        this.lineChartData.labels.push(missingDate);
       }
       
      }
    }
  }
  monthDiff(d1, d2) {
    var months;
    months = (d2.getFullYear() - d1.getFullYear()) * 12;
    months -= d1.getMonth();
    months += d2.getMonth();
    return months <= 0 ? 0 : months;
  }
  MonthMissingFromBegining(date1) {
    let startDate = this.startDate; // new Date(DateUtils.getLocalDateAsYYYYMMDD(this.startDate, 'yyyy-MM-dd'));;
    date1 = new Date(date1); //new Date(DateUtils.getLocalDateAsYYYYMMDD(date1, 'yyyy-MM-dd'));;
    var monthDiff = this.monthDiff(startDate, date1)
    // 
    if (monthDiff !== 0) {
      var currntDate = date1.getMonth() - startDate.getMonth();
      for (let counter = 0; counter < monthDiff; counter++) {
        let prevDateObj = new Date(startDate);
        prevDateObj.setMonth(prevDateObj.getMonth() + counter);
        
        this.lineChartLabels.push(this.monthNames[prevDateObj.getMonth()]);
        this.lineChartData.datasets[0].data.push(0);
        this.lineChartData.labels.push(this.monthNames[prevDateObj.getMonth()]);
      }
    }
  }
  MonthMissingFromEnd(date1) {
    let endDate = this.endDate; //new Date(DateUtils.getLocalDateAsYYYYMMDD(this.endDate, 'yyyy-MM-dd'));
    date1 = new Date(date1); //new Date(DateUtils.getLocalDateAsYYYYMMDD(date1, 'yyyy-MM-dd'));;
    //, endDate.getMonth());
    var monthDiff = this.monthDiff(date1, endDate)
    
    if (monthDiff !== 0) {
      var currntDate = endDate.getMonth() - date1.getMonth();
      for (let counter = 0; counter < monthDiff; counter++) {
        let prevDateObj = new Date(date1);
  
        this.lineChartLabels.push(this.monthNames[prevDateObj.getMonth()]);
        this.lineChartData.datasets[0].data.push(0);
        this.lineChartData.labels.push(this.monthNames[prevDateObj.getMonth()]);
      }
    }
  }
  YearMissingFromBegining(date1) {
    let startDate = new Date(DateUtils.getLocalDateAsYYYYMMDD(this.startDate, 'yyyy-MM-dd'));;
    date1 = new Date(DateUtils.getLocalDateAsYYYYMMDD(date1, 'yyyy-MM-dd'));;
    if (date1.getFullYear() > startDate.getFullYear()) {
      var currntDate = date1.getFullYear(); - startDate.getFullYear();
      for (let counter = 0; counter < currntDate; counter++) {
        let prevDateObj = new Date(date1);
        prevDateObj.setFullYear(prevDateObj.getFullYear() + (counter + 1));
        
        this.lineChartLabels.push(prevDateObj.getFullYear());
        this.lineChartData.datasets[0].data.push(0);
        this.lineChartData.labels.push(prevDateObj.getFullYear());
      }
    }
  }
  YearMissingFromEnd(date1) {
    let endDate = new Date(DateUtils.getLocalDateAsYYYYMMDD(this.endDate, 'yyyy-MM-dd'));
    date1 = new Date(DateUtils.getLocalDateAsYYYYMMDD(date1, 'yyyy-MM-dd'));;
    if (date1.getFullYear() < endDate.getFullYear()) {
      var currntDate = endDate.getFullYear() - date1.getFullYear();
      for (let counter = 0; counter < currntDate; counter++) {
        let prevDateObj = new Date(date1);
        prevDateObj.setFullYear(prevDateObj.getFullYear() + (counter + 1));
        
        this.lineChartLabels.push(prevDateObj.getFullYear());
        this.lineChartData.datasets[0].data.push(0);
        this.lineChartData.labels.push(prevDateObj.getFullYear());
      }
    }
  }
  dateMissingFromEnd(date1) {
    let endDate = this.endDate;
    date1 = new Date(date1);
    if (date1.getTime() < endDate.getTime()) {
      const oneDay = 24 * 60 * 60 * 1000;
      var currntDate = Math.floor(Math.abs((this.getLocalMidNightDate(endDate).getTime() - this.getLocalMidNightDate(date1).getTime()) / oneDay));
      for (let counter = 0; counter < currntDate; counter++) {
        let prevDateObj = new Date(date1);
        prevDateObj.setDate(prevDateObj.getDate() + (counter + 1));
        let missingDate = new DatePipe('en-US').transform(prevDateObj,
          'MMM d, EEE');
        
        if(this.lineChartLabels.find( item =>item === missingDate) === undefined){
        this.lineChartLabels.push(missingDate);
        this.lineChartData.datasets[0].data.push(0);
        this.lineChartData.labels.push(missingDate);
        }
        
      }
    }
  }
  yearMissingFromBegining1(date1, item) {
    let startDate = new Date(DateUtils.getLocalDateAsYYYYMMDD(this.previousStartDate, 'yyyy-MM-dd'));;
    date1 = new Date(DateUtils.getLocalDateAsYYYYMMDD(date1, 'yyyy-MM-dd'));;
    if (date1.getFullYear() > startDate.getFullYear()) {
      var currntDate = date1.getFullYear() - startDate.getFullYear();
      for (let counter = 0; counter < currntDate; counter++) {
        let prevDateObj = new Date(startDate);
        prevDateObj.setMonth(prevDateObj.getMonth() + counter);
       // 
        if (item) {
          this.lineChartLabels.push(prevDateObj.getFullYear())
        }
        this.secondLabel.push(prevDateObj.getFullYear());
        this.lineChartData.datasets[1].data.push(0);
        this.lineChartData.labels.push(prevDateObj.getFullYear());
        // this.lineChartData[0].data.push(0);
      }
    }
  }
  yearMissingFromEnd1(date1, item) {
    let endDate = new Date(DateUtils.getLocalDateAsYYYYMMDD(this.previousEndDate, 'yyyy-MM-dd'));
    date1 = new Date(DateUtils.getLocalDateAsYYYYMMDD(date1, 'yyyy-MM-dd'));;
    if (date1.getFullYear() < endDate.getFullYear()) {
      var currntDate = endDate.getFullYear() - date1.getFullYear();
      for (let counter = 0; counter < currntDate; counter++) {
        let prevDateObj = new Date(date1);
        prevDateObj.setFullYear(prevDateObj.getFullYear() + (counter + 1));
        
        if (item) {
          this.lineChartLabels.push(prevDateObj.getFullYear())
        }
        this.secondLabel.push(prevDateObj.getFullYear());
        this.lineChartData.datasets[1].data.push(0);
        this.lineChartData.labels.push(prevDateObj.getFullYear());
      }
    }
  }
  MonthMissingFromBegining1(date1, item) {
    let startDate = this.previousStartDate; //new Date(DateUtils.getLocalDateAsYYYYMMDD(this.previousStartDate, 'yyyy-MM-dd'));;
    // date1 = new Date(DateUtils.getLocalDateAsYYYYMMDD(date1, 'yyyy-MM-dd'));;
    date1 = new Date(date1);
    var monthDiff = this.monthDiff(startDate, date1);
    // 
    if (monthDiff !== 0) {
      var currntDate = date1.getMonth() - startDate.getMonth();
      for (let counter = 0; counter < monthDiff; counter++) {
        let prevDateObj = new Date(startDate);
        prevDateObj.setMonth(prevDateObj.getMonth() + counter);
        
        if (item) {
          this.lineChartLabels.push(this.monthNames[prevDateObj.getMonth()])
        }
        this.secondLabel.push(this.monthNames[prevDateObj.getMonth()]);
        this.lineChartData.datasets[1].data.push(0);
        this.lineChartData.labels.push(this.monthNames[prevDateObj.getMonth()]);
        // this.lineChartData[0].data.push(0);
      }
    }
  }
  MonthMissingFromEnd1(date1, item) {
    let endDate = this.previousEndDate; //new Date(DateUtils.getLocalDateAsYYYYMMDD(this.previousEndDate, 'yyyy-MM-dd'));
    date1 = new Date(date1); //new Date(DateUtils.getLocalDateAsYYYYMMDD(date1, 'yyyy-MM-dd'));;
    var monthDiff = this.monthDiff(date1, endDate);
    // 
    if (monthDiff !== 0) {
      var currntDate = (endDate.getMonth() + 1);
      for (let counter = 0; counter < monthDiff; counter++) {
        let prevDateObj = new Date(date1);
        prevDateObj.setMonth(prevDateObj.getMonth() + (counter + 1));
        
        if (item) {
          this.lineChartLabels.push(this.monthNames[prevDateObj.getMonth()])
        }
        this.secondLabel.push(this.monthNames[prevDateObj.getMonth()]);
        this.lineChartData.datasets[1].data.push(0);
        this.lineChartData.labels.push(this.monthNames[prevDateObj.getMonth()]);
      }
    }

  }

  dateMissingFromBegining1(date1, item) {
    let startDate = this.previousStartDate;
    date1 = new Date(date1)
    if (date1.getTime() > startDate.getTime()) {
      const oneDay = 24 * 60 * 60 * 1000;
      var currntDate = Math.floor(Math.abs((this.getLocalMidNightDate(date1).getTime() - this.getLocalMidNightDate(startDate).getTime()) / oneDay));
      for (let counter = 0; counter < currntDate; counter++) {
        let prevDateObj = new Date(startDate);
        prevDateObj.setDate(prevDateObj.getDate() + counter);
        let missingDate = new DatePipe('en-US').transform(prevDateObj,
          'MMM d, EEE');
        if (item) {
          if(this.lineChartLabels.find( item1 =>item1 ===missingDate) === undefined){
          this.lineChartLabels.push(missingDate);
          this.lineChartData.datasets[1].data.push(0);
          this.lineChartData.labels.push(missingDate);
          }
        }
        if(this.secondLabel.find( item1 =>item1 ===missingDate) === undefined){
          this.secondLabel.push(missingDate);
          this.lineChartData.datasets[1].data.push(0);
          this.lineChartData.labels.push(missingDate);
          }
       
      }
    }
  }
  dateMissingFromEnd1(date1, item) {
    let endDate = this.previousEndDate;
    date1 = new Date(date1)
    if (date1.getTime() < endDate.getTime()) {
      const oneDay = 24 * 60 * 60 * 1000;
      var currntDate = Math.floor(Math.abs((this.getLocalMidNightDate(endDate).getTime() - this.getLocalMidNightDate(date1).getTime()) / oneDay));
      for (let counter = 0; counter < currntDate; counter++) {
        let prevDateObj = new Date(date1);
        prevDateObj.setDate(prevDateObj.getDate() + (counter + 1));
        let missingDate = new DatePipe('en-US').transform(prevDateObj,
          'MMM d, EEE');
        
        if (item) {
          if(this.lineChartLabels.find( item1 =>item1 ===missingDate) === undefined){
          this.lineChartLabels.push(missingDate)
          this.lineChartData.datasets[1].data.push(0);
          this.lineChartData.labels.push(missingDate);
          }
        }
        if(this.secondLabel.find( item1 =>item1 ===missingDate) === undefined){
        this.secondLabel.push(missingDate);
        }
       
      }
    }
  }
  getTotalSpendChart(resp) {
    var objPeriodMonth = this.groupByTimePeriod(resp, 'ds+Transaction Date', this.select);
    
    let prevKey = -1;
    let prevDate = null;
    let i = 0
    for (let key in objPeriodMonth) {
      let totalSpend = 0;
      let dataList = objPeriodMonth[key];
      for (let item of dataList) {
        totalSpend = totalSpend + Number(item['-Spend']);
      }
      if (this.select === 'days') {
        if (i == 0) {
          this.dateMissingFromBegining(dataList[0]['ds+Transaction Date'])
        }

        if (prevKey != -1) {
          if (parseInt(key) - prevKey > 1) {
            for (let counter = prevKey; counter < parseInt(key) - 1; counter++) {
              let prevDateObj = new Date(Date.parse(prevDate));
              prevDateObj.setDate(prevDateObj.getDate() + (counter - prevKey) + 1);
              let missingDate = new DatePipe('en-US').transform(prevDateObj,
                'MMM d, EEE');
                
                if(this.lineChartLabels.find(item1 =>item1 ===missingDate) === undefined){
              this.lineChartLabels.push(missingDate);
            
              this.lineChartData.datasets[0].data.push(0);
              this.lineChartData.labels.push(missingDate);
                }
              
            }
          }
        }
        let date1 = new DatePipe('en-US').transform(dataList[0]['ds+Transaction Date'],
          'MMM d, EEE');
        let label1 = date1;
       // if(this.lineChartLabels.find( item1 =>item1 ===label1) === undefined){
        this.lineChartLabels.push(label1)
       // }

        prevKey = parseInt(key);
        prevDate = dataList[0]['ds+Transaction Date'];
      } else if (this.select === 'month') {
        if (i == 0) {
          this.MonthMissingFromBegining(dataList[0]['ds+Transaction Date'])
        }
        if (prevKey != -1) {
          if (parseInt(key) - prevKey > 1) {
            for (let counter = prevKey; counter < parseInt(key) - 1; counter++) {
              let prevDateObj = new Date(Date.parse(prevDate));
              prevDateObj.setMonth(prevDateObj.getMonth() + (counter - prevKey) + 1);
              this.lineChartLabels.push(this.monthNames[prevDateObj.getMonth()]);
              this.lineChartData.datasets[0].data.push(0);
              this.lineChartData.labels.push(this.monthNames[prevDateObj.getMonth()]);
            }
          }
        }
        let date1 = new Date(dataList[0]['ds+Transaction Date']);
        this.lineChartLabels.push(this.monthNames[date1.getMonth()])
        prevKey = parseInt(key);
        prevDate = date1;

      } else if (this.select === 'year') {
        if (i == 0) {
          this.YearMissingFromBegining(dataList[0]['ds+Transaction Date'])
        }
        if (prevKey != -1) {
          if (parseInt(key) - prevKey > 1) {
            for (let counter = prevKey; counter < parseInt(key) - 1; counter++) {
              let prevDateObj = new Date(Date.parse(prevDate));
              prevDateObj.setFullYear(prevDateObj.getFullYear() + (counter - prevKey) + 1);
              this.lineChartLabels.push(prevDateObj.getFullYear());
              this.lineChartData.datasets[0].data.push(0);
              this.lineChartData.labels.push(prevDateObj.getFullYear());
              
            }
          }
        }
        let date1 = new Date(dataList[0]['ds+Transaction Date']);
        this.lineChartLabels.push(date1.getFullYear());
        prevKey = parseInt(key);
        prevDate = date1;
      }
     
      this.lineChartData.datasets[0].data.push(totalSpend);
       if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'days') {
        this.dateMissingFromEnd(dataList[0]['ds+Transaction Date'])
      }
      if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'month') {
        this.MonthMissingFromEnd(dataList[0]['ds+Transaction Date'])
      }
      if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'year') {
        this.YearMissingFromEnd(dataList[0]['ds+Transaction Date'])
      }
      i++;
    }
    // 
    console.log("line chart dtaa",this.lineChartData);
    this.setTotalSpendchartOptions();
  }
  getApprovalChart(resp, resp2) {
    let itemsRejected = 0;
    let itemsExpired = 0
    let approvedByAdmin = 0;
    if (resp && resp.length > 0) {
      for (const item of resp) {
        if (item['Status'] !== 'refund') {
          if (item['Approval Status'] === 'approved') {
            approvedByAdmin++;
          }
        }
      }
    }
    if (resp2 && resp2.length > 0) {
      for (const item of resp2) {
        if (item['Status'] !== 'refund') {
          if (item['Approval Status'] === 'expired') {
            itemsExpired++;
          } else if (item['Approval Status'] === 'rejected') {
            itemsRejected++;
          }
        }
      }
    }
    if ((itemsRejected + itemsExpired + approvedByAdmin) == 0) {
      this.pieChartData1 = {
        labels: [this.translateService.instant('dashboard.Disapproved'), this.translateService.instant('dashboard.Approved'), this.translateService.instant('dashboard.Expired')],
        datasets: [
          {
            data: [],
            backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],
            borderWidth: 1
          }
        ]
      };;
    } else {
      this.pieChartData1.datasets[0].data= [itemsRejected, approvedByAdmin, itemsExpired];
    }
    const self4 = this;
    this.pieChartOptions1 = {
      legend: {
        display: false,
        responsive: true,
      },
      maintainAspectRatio: true,
      plugins: {
        labels: {
          render: function (args) {
            return ' \n' + args.label + '\n' + args.percentage + "%";
          },
          position: 'outside',
          arc: false,
          fontSize: 12,
          showZero: false,
          fontFamily: 'apercu-r',
          textShadow: true,
          shadowColor: '#E8D7FD',
          fontColor: "black",
          overlap: true,
          outsidePadding: 20,
          shadowBlur: 10
        }
        ,
        datalabels: {
          display: false,
          formatter: (value, ctx) => {
            let percentage = value;
            if (percentage > 0) {
              return percentage;
            } else {
              return '';
            }
          },
          color: '#000000',
        }
      },
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            let totalApprove = 0;
            for (let i = 0; i < self4.pieChartData1.datasets[0].data.length; i++) {
              totalApprove += self4.pieChartData1.datasets[0].data[i];
            }
            const value = self4.pieChartData1.datasets[0].data[tooltipItem.index];
            const average = ((value * 100) / totalApprove).toFixed(2);
            return (value + ' of ' + totalApprove + ', ' + average + '%');
          }
        }
      }
    }
  
  }
  getTopSpenderChart(resp) {
    this.totalCompliance = '0%';
    let calculateValues = {
      totalFlightSpend: 0, totalCarSpend: 0, totalHotelSpend: 0, totalBooking: 0, statusCancelled: 0, travelerNumber: [],
      carCounter: 0, hotelCounter: 0, flightCounter: 0, compliance: 0, employeeNumber: []
    }
    calculateValues = this.calculateBookingSpendApproval(resp)
    this.totalSpend = calculateValues.totalFlightSpend + calculateValues.totalCarSpend + calculateValues.totalHotelSpend;
    this.totalTraveler = calculateValues.travelerNumber.length;
    this.totalBooking = calculateValues.totalBooking;
    if (calculateValues.compliance > 0) {
      this.totalCompliance = ((calculateValues.compliance * 100) / ((calculateValues.totalBooking + calculateValues.statusCancelled))).toFixed(2) + "%";
    }
    let chaLabels1 = [];
    let chartData = [];
    for (let item of calculateValues.travelerNumber) {
      let totalEmployeeSpend = 0;
      for (let item1 of resp) {
        if (item === item1['Traveler']) {
          totalEmployeeSpend = totalEmployeeSpend + Number(item1['-Spend']);
        }
      }
      chaLabels1.push(item);
      let chartData1 = { name: '', spend: 0 }
      chartData1.name = item;
      chartData1.spend = totalEmployeeSpend;
      chartData.push(chartData1);
    }
    if (chartData.length > 5) {
      let descending: any = chartData.sort((a, b) => (a.spend > b.spend ? -1 : 1))
      descending = descending.slice(0, 5);
      for (let item of descending) {
        this.barChartData1.datasets[0].data.push(item.spend);
        this.barChartData1.labels.push(item.name);
       // this.barChartLabels1.push(item.name);
        this.empName1.push(item.name);
        this.getColor()
      }
    } else {
      chartData = chartData.sort((a, b) => (a.spend > b.spend ? -1 : 1));
      for (let item of chartData) {
        this.barChartData1.datasets[0].data.push(item.spend);
        this.barChartData1.labels.push(item.name);
        this.empName1.push(item.name);
        this.getColor()
      }
    }
    if (this.selectBookingValue === 'car') {
      if (calculateValues.carCounter > 0) {
        this.avgPriceDay = calculateValues.totalCarSpend / calculateValues.carCounter;
      } else {
        this.avgPriceDay = 0;
      }
    } else if (this.selectBookingValue === 'flight') {
      if (calculateValues.flightCounter > 0) {
        this.avgPriceDay = calculateValues.totalFlightSpend / calculateValues.flightCounter;
      } else {
        this.avgPriceDay = 0;
      }
    } else if (this.selectBookingValue === 'hotel') {
      if (calculateValues.hotelCounter > 0) {
        this.avgPriceDay = calculateValues.totalHotelSpend / calculateValues.hotelCounter;
      } else {
        this.avgPriceDay = 0;
      }
    }
    let self3 = this;
    this.barChartOptions2 = {
      indexAxis: 'y', 
      responsive: true,
      maintainAspectRatio: true,
      layout: {
        padding: {
          top: 25,
          right: 25,
          left: 20,
          bottom: 0
        }
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Total Spend'
          }
        },
        y: {
          beginAtZero: true
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: (tooltipItem) => {
              const index = tooltipItem.dataIndex;
              const value = tooltipItem.raw;
              const empName = self3.empName1[index] || 'Employee';
              return `${empName} (${self3.getCurrencySymbol(self3.currency)}${Number(value).toLocaleString()})`;
            }
          }
        }
      }
    };
  }
  getTopVendorChart(resp) {
    let flightVendor = [];
    for (let item of resp) {
      if (item['Booking Type'] === 'Air') {
        if (flightVendor.indexOf(item['Vendor Chain']) === -1) {
          flightVendor.push(item['Vendor Chain']);
          this.VehicleMsg = this.translateService.instant("dashboard.TopAirlines")
        }
      } else if (item['Booking Type'] === 'Cars') {
        if (flightVendor.indexOf(item['Class Of Service']) === -1) {
          flightVendor.push(item['Class Of Service']);
          this.VehicleMsg = this.translateService.instant("dashboard.TopVehicleTypes")
        }
      } else if (item['Booking Type'] === 'Hotels') {
        if (flightVendor.indexOf(item['Vendor Chain']) === -1) {
          flightVendor.push(item['Vendor Chain']);
          this.VehicleMsg = this.translateService.instant("dashboard.TopHotelChains")
        }
      }
    }
    let chaLabels1 = [];
    let chartData = [];
    if (this.selectBookingValue === 'car') {
      for (let item of flightVendor) {
        let totalBooking = 0;
        for (let item1 of resp) {
          if (item === item1['Class Of Service'] && item1['Status'] !== 'refund') {
            totalBooking = totalBooking + 1;
          }
        }
        chaLabels1.push(item);
        let chartData1 = { name: '', spend: 0 }
        chartData1.name = item;
        chartData1.spend = totalBooking;
        chartData.push(chartData1);
      }
    } else {
      for (let item of flightVendor) {
        let totalBooking = 0;
        for (let item1 of resp) {
          if (item === item1['Vendor Chain'] && item1['Status'] !== 'refund') {
            totalBooking = totalBooking + 1;
          }
        }
        chaLabels1.push(item);
        let chartData1 = { name: '', spend: 0 }
        if (item === 'None ') {
          item = 'Others'
        }
        chartData1.name = item;
        chartData1.spend = totalBooking;
        chartData.push(chartData1);
      }
    }
    if (chartData.length > 5) {
      let descending: any = chartData.sort((a, b) => (a.spend > b.spend ? -1 : 1))
      descending = descending.slice(0, 5);
      for (let item of descending) {
        this.barChartData5.datasets[0].data.push(item.spend);
        this.barChartData5.labels.push(item.name);
        this.barChartLabels5.push(item.name);
        this.empName1.push(item.name);
        this.getColor1()
      }
    } else {
      chartData = chartData.sort((a, b) => (a.spend > b.spend ? -1 : 1));
      for (let item of chartData) {
        this.barChartData5.datasets[0].data.push(item.spend);
        this.barChartData5.labels.push(item.name);
        this.barChartLabels5.push(item.name);
        this.empName1.push(item.name);
        this.getColor1()
      }
    }
    let that1 = this;
    this.barChartOptions5 = {
      indexAxis: 'y', 
      layout: this.layoutPaddingOption,
      scales: {
        x: {
          title: {
            display: true,
            text: 'Total Spend'
          }
        },
        y: {
          beginAtZero: true
        }
      },
      legend: {
        display: false,
        responsive: true,
      },
      maintainAspectRatio: true,
      tooltips: {
        callbacks: {
          label: function (tooltipItem, data) {
            let airline = that1.barChartLabels5[tooltipItem.index];
            return (airline + '(' + tooltipItem.xLabel + ')');
          }
        }
      }
    }
  }
  createWeekData(resp) {
    this.weeklyData = [];
    var objPeriodMonth1 = this.groupByTimePeriod(resp, 'ds+Transaction Date', 'week');
    
    for (let key in objPeriodMonth1) {
      let travelerTotalNumber = [];
      let travelerCarNumber = [];
      let travelerFlightNumber = [];
      let travelerHotelNumber = [];
      let travelerCarBooking = 0;
      let travelerFlightBooking = 0;
      let travelerHotelBooking = 0;
      let weekData = { week: '', totalTraveler: 0, filghtTra: 0, flightPerc: 0, carTra: 0, carPerc: 0, hotelTra: 0, hotelPerc: 0 }
      let dataList = objPeriodMonth1[key];
      for (let item of dataList) {
        if (travelerTotalNumber.indexOf(item['Traveler']) === -1) {
          travelerTotalNumber.push(item['Traveler']);
        }
        if (item['Booking Type'] === 'Air') {
          if (travelerFlightNumber.indexOf(item['Traveler']) === -1) {
            travelerFlightNumber.push(item['Traveler']);
          }
          travelerFlightBooking = travelerFlightBooking + 1;
        } else if (item['Booking Type'] === 'Cars') {
          if (travelerCarNumber.indexOf(item['Traveler']) === -1) {
            travelerCarNumber.push(item['Traveler']);
          }
          travelerCarBooking = travelerCarBooking + 1;
        } else if (item['Booking Type'] === 'Hotels') {
          if (travelerHotelNumber.indexOf(item['Traveler']) === -1) {
            travelerHotelNumber.push(item['Traveler']);
          }
          travelerHotelBooking = travelerHotelBooking + 1;
        }
      }
      weekData.filghtTra = travelerFlightNumber.length;
      weekData.carTra = travelerCarNumber.length;
      weekData.hotelTra = travelerHotelNumber.length;
      weekData.totalTraveler = travelerTotalNumber.length;
      weekData.flightPerc = Math.round((weekData.filghtTra * 100) / weekData.totalTraveler);
      weekData.carPerc = Math.round((weekData.carTra * 100) / weekData.totalTraveler);
      weekData.hotelPerc = Math.round((weekData.hotelTra * 100) / weekData.totalTraveler);
      weekData.week = key;
      this.weeklyData.push(weekData);
    }
    
  }
  getWeekRangeForGivenDate(givenDate) {
    const oneDay = 24 * 60 * 60 * 1000;
    const absWeekNum = Math.round((givenDate.getTime() - 4 * oneDay) / (oneDay * 7));
    const firstDay = (absWeekNum * 7 - 4);
    const lastDay = (absWeekNum * 7 + 2);
    const datePipe = new DatePipe('en-US');
    return datePipe.transform(this.getMonday(givenDate), 'dd MMM yy')
      + ' - ' + datePipe.transform(new Date(this.getMonday(givenDate).getTime() + 6 * oneDay), 'dd MMM yy');
  }
  getMonday(d) {
    d = new Date(d);
    const day = d.getDay();
    const diff = d.getDate() - day + (day == 0 ? -6 : 1); // adjust when day is sunday
    return new Date(d.setDate(diff));
  }


  createChartData(resp, resp2, item, dataType) {
    if (dataType === 'currentData') {
      this.resetValue(resp);
      if (item && this.selectBookingValue === 'all') {
        this.getCurrentGrandTotal(resp);
        this.createWeekData(resp);
      }
      this.doughnutChartData =  {
        labels: [this.translateService.instant('dashboard.Cars'), this.translateService.instant('dashboard.Flights'),this.translateService.instant('dashboard.Hotels')],
        datasets: [
          {
            data: [0, 0, 0], // update as needed
            backgroundColor: ['#853BF2', '#29EED8', '#4B8AE5'],
            borderWidth: 1
          }
        ]
      };
      this.createDoughNutGraph(resp);
      this.getTotalSpendChart(resp);
      this.getTopSpenderChart(resp);
      this.getApprovalChart(resp, resp2);
      if (this.selectBookingValue !== 'all' && resp.length > 0) {
        this.getTopVendorChart(resp);
      }

      this.applyBtn1 = false;
    } else if (dataType === 'previousData') {
      if (item && this.selectBookingValue === 'all') {
        this.getPreviousGrandTotal(resp);
      }
      this.resetPreviousValue();
      let calculateValues = {
        totalFlightSpend: 0, totalCarSpend: 0, totalHotelSpend: 0, totalBooking: 0, statusCancelled: 0, travelerNumber: [],
        carCounter: 0, hotelCounter: 0, flightCounter: 0, compliance: 0, employeeNumber: []
      }
      if (resp.length > 0) {
        calculateValues = this.calculateBookingSpendApproval(resp)
        this.totalPreviousBooking = calculateValues.totalBooking;
        this.totalPreviousTraveler = calculateValues.travelerNumber.length;
        if (calculateValues.compliance > 0) {
          this.totalPreviousCompliance = ((calculateValues.compliance * 100) / ((calculateValues.totalBooking + calculateValues.statusCancelled))).toFixed(2) + "%";
        }
        this.totalPreviousSpend = calculateValues.totalFlightSpend + calculateValues.totalCarSpend + calculateValues.totalHotelSpend;
        if (this.selectBookingValue === 'car') {
          if (calculateValues.carCounter > 0) {
            this.avgPreviousPriceDay = calculateValues.totalCarSpend / calculateValues.carCounter;
          } else {
            this.avgPreviousPriceDay = 0;
          }
        } else if (this.selectBookingValue === 'flight') {
          if (calculateValues.flightCounter > 0) {
            this.avgPreviousPriceDay = calculateValues.totalFlightSpend / calculateValues.flightCounter;
          } else {
            this.avgPreviousPriceDay = 0;
          }
        } else if (this.selectBookingValue === 'hotel') {
          if (calculateValues.hotelCounter > 0) {
            this.avgPreviousPriceDay = calculateValues.totalHotelSpend / calculateValues.hotelCounter;
          } else {
            this.avgPreviousPriceDay = 0;
          }
        }
        var objPeriodMonth = this.groupByTimePeriod(resp, 'ds+Transaction Date', this.select);
        let prevKey = -1;
        let prevDate = null;
        let i = 0;
        
        for (let key in objPeriodMonth) {
          let totalSpend = 0;

          let dataList = objPeriodMonth[key];
          for (let item of dataList) {
            totalSpend = totalSpend + Number(item['-Spend']);
          }
          if (this.originalResponse.length == 0) {
            if (this.select === 'days') {
              if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'days') {
                if (this.startDate.getTime() === this.endDate.getTime()) {
                  let prevDateObj = new Date(this.startDate);
                  prevDateObj.setDate(prevDateObj.getDate());
                  let missingDate = new DatePipe('en-US').transform(prevDateObj,
                    'MMM d, EEE');
                  // 
                  this.lineChartLabels.push(missingDate);
                //  this.lineChartData[0].data.push(0);
                  this.lineChartData.datasets[0].data.push(0);
                  this.lineChartData.labels.push(missingDate);
                } else {
                  let prevDateObj = new Date(this.startDate);
                  prevDateObj.setDate(prevDateObj.getDate());
                  let missingDate = new DatePipe('en-US').transform(prevDateObj,
                    'MMM d, EEE');
                  // 
                  this.lineChartLabels.push(missingDate);
                 // this.lineChartData[0].data.push(0);
                 this.lineChartData.datasets[0].data.push(0);
                 this.lineChartData.labels.push(missingDate);
                  this.dateMissingFromEnd(this.startDate)
                }
              }

              if (i == 0) {
                this.dateMissingFromBegining1(dataList[0]['ds+Transaction Date'], false)
              }

              if (prevKey != -1) {
                if (parseInt(key) - prevKey > 1) {
                  for (let counter = prevKey; counter < parseInt(key) - 1; counter++) {
                    let prevDateObj = new Date(Date.parse(prevDate));
                    prevDateObj.setDate(prevDateObj.getDate() + (counter - prevKey) + 1);
                    let missingDate = new DatePipe('en-US').transform(prevDateObj,
                      'MMM d, EEE');
                    // this.lineChartLabels.push(missingDate);
                    this.secondLabel.push(missingDate);
                    this.lineChartData.datasets[1].data.push(0);
                    
                  }
                }
              }
              let date1 = new Date(dataList[0]['ds+Transaction Date']);
              let datePipe = new DatePipe('en-US');
              let label1 = datePipe.transform(new Date(date1), 'MMM d, EEE');
              prevKey = parseInt(key);
              prevDate = dataList[0]['ds+Transaction Date'];
              // this.lineChartLabels.push(label1);
              if (this.secondLabel.indexOf(label1) === -1) {
                this.secondLabel.push(label1);
                this.lineChartData.datasets[1].data.push(0);
               
              }

            } else if (this.select === 'month') {
              if (i == 0) {
                this.MonthMissingFromBegining1(dataList[0]['ds+Transaction Date'], false)
              }
              if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'month') {
                if (this.startDate.getTime() === this.endDate.getTime()) {
                  let prevDateObj = new Date(this.startDate);
                  prevDateObj.setMonth(prevDateObj.getMonth());
                  // 
                  this.lineChartLabels.push(this.monthNames[prevDateObj.getMonth()]);
                //  this.lineChartData[0].data.push(0);
                  this.lineChartData.datasets[0].data.push(0);
                  this.lineChartData.labels.push(this.monthNames[prevDateObj.getMonth()]);
                } else {
                  let prevDateObj = new Date(this.startDate);
                  prevDateObj.setMonth(prevDateObj.getMonth());
                  // 
                  this.lineChartLabels.push(this.monthNames[prevDateObj.getMonth()]);
                 // this.lineChartData[0].data.push(0);
                 this.lineChartData.datasets[0].data.push(0);
                 this.lineChartData.labels.push(this.monthNames[prevDateObj.getMonth()]);
                  this.MonthMissingFromEnd(this.startDate)
                }
              }
              if (prevKey != -1) {
                if (parseInt(key) - prevKey > 1) {
                  for (let counter = prevKey; counter < parseInt(key) - 1; counter++) {
                    let prevDateObj = new Date(Date.parse(prevDate));
                    prevDateObj.setMonth(prevDateObj.getMonth() + (counter - prevKey) + 1);
                    // this.lineChartLabels.push(this.monthNames[prevDateObj.getMonth()]);
                    this.secondLabel.push(this.monthNames[prevDateObj.getMonth()]);
                   // this.lineChartData[1].data.push(0);
                   this.lineChartData.datasets[1].data.push(0);
                  }
                }
              }
              let date1 = new Date(dataList[0]['ds+Transaction Date']);
              //  this.lineChartLabels.push(this.monthNames[date1.getMonth()])
              this.secondLabel.push(this.monthNames[date1.getMonth()]);
              prevKey = parseInt(key);
              prevDate = date1;
            } else if (this.select === 'year') {
              if (i == 0) {
                this.yearMissingFromBegining1(dataList[0]['ds+Transaction Date'], false)
              }
              if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'year') {
                if (this.startDate.getTime() === this.endDate.getTime()) {
                  let prevDateObj = new Date(this.startDate);
                  prevDateObj.setFullYear(prevDateObj.getFullYear());
                  //  
                  this.lineChartLabels.push(prevDateObj.getFullYear());
                  this.lineChartData.datasets[0].data.push(0);
                  this.lineChartData.labels.push(prevDateObj.getFullYear());
                  //this.lineChartData[0].data.push(0);
                } else {
                  let prevDateObj = new Date(this.startDate);
                  prevDateObj.setFullYear(prevDateObj.getFullYear());
                  //  
                  this.lineChartLabels.push(prevDateObj.getFullYear());
                  this.lineChartData.datasets[0].data.push(0);
                  this.lineChartData.labels.push(prevDateObj.getFullYear());
                  this.YearMissingFromEnd(this.startDate);
                }
              }
              if (prevKey != -1) {
                if (parseInt(key) - prevKey > 1) {
                  for (let counter = prevKey; counter < parseInt(key) - 1; counter++) {
                    let prevDateObj = new Date(Date.parse(prevDate));
                    prevDateObj.setFullYear(prevDateObj.getFullYear() + (counter - prevKey) + 1);
                    //  this.lineChartLabels.push(prevDateObj.getFullYear());
                    this.secondLabel.push(prevDateObj.getFullYear());
                    //this.lineChartData[1].data.push(0);
                    this.lineChartData.datasets[1].data.push(0);
                   
                  }
                }
              }
              let date1 = new Date(dataList[0]['ds+Transaction Date']);
              // this.lineChartLabels.push(date1.getFullYear());
              prevKey = parseInt(key);
              prevDate = date1;
              // if (this.secondLabel.indexOf(date1.getFullYear()) === -1) {
              this.secondLabel.push(date1.getFullYear())
              // }
              // this.lineChartLabels.push(date1.getFullYear())
            }
          //  this.lineChartData[1].data.push(totalSpend);
            this.lineChartData.datasets[1].data.push(totalSpend);
          //  this.lineChartData.labels.push(prevDateObj.getFullYear());
            if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'days') {
              this.dateMissingFromEnd1(dataList[0]['ds+Transaction Date'], false)
            }
            if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'month') {
              this.MonthMissingFromEnd1(dataList[0]['ds+Transaction Date'], false)
            }
            if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'year') {
              this.yearMissingFromEnd1(dataList[0]['ds+Transaction Date'], false)
            }
            i++;
          }
          else {
            if (this.select === 'days') {
              if (i == 0) {
                this.dateMissingFromBegining1(dataList[0]['ds+Transaction Date'], false)
              }
              if (prevKey != -1) {

                if (parseInt(key) - prevKey > 1) {
                  for (let counter = prevKey; counter < parseInt(key) - 1; counter++) {
                    let prevDateObj = new Date(Date.parse(prevDate));
                    prevDateObj.setDate(prevDateObj.getDate() + (counter - prevKey) + 1);
                    let missingDate = new DatePipe('en-US').transform(prevDateObj,
                      'MMM d, EEE');
                    // this.lineChartLabels.push(missingDate);
                    this.secondLabel.push(missingDate);
                   // this.lineChartData[1].data.push(0);
                    this.lineChartData.datasets[1].data.push(0);
                   // this.lineChartData.labels.push(prevDateObj.getFullYear());
                  }
                }
              }
              let date1 = new DatePipe('en-US').transform(dataList[0]['ds+Transaction Date'], 'MMM d, EEE');
              let label1 = date1;
              if (this.secondLabel.indexOf(label1) === -1) {
                this.secondLabel.push(label1)
              }
              prevKey = parseInt(key);
              prevDate = dataList[0]['ds+Transaction Date'];
            } else if (this.select === 'month') {
              if (i == 0) {
                this.MonthMissingFromBegining1(dataList[0]['ds+Transaction Date'], false)
              }
              if (prevKey != -1) {
                if (parseInt(key) - prevKey > 1) {
                  for (let counter = prevKey; counter < parseInt(key) - 1; counter++) {
                    let prevDateObj = new Date(Date.parse(prevDate));
                    prevDateObj.setMonth(prevDateObj.getMonth() + (counter - prevKey) + 1);
                    // this.lineChartLabels.push(this.monthNames[prevDateObj.getMonth()]);
                    this.secondLabel.push(this.monthNames[prevDateObj.getMonth()]);
                   // this.lineChartData[1].data.push(0);
                    this.lineChartData.datasets[1].data.push(0);
                  //  this.lineChartData.labels.push(prevDateObj.getFullYear());
                  }
                }
              }
              let date1 = new Date(dataList[0]['ds+Transaction Date']);
              // this.lineChartLabels.push(this.monthNames[date1.getMonth()])
              prevKey = parseInt(key);
              prevDate = date1;
              // if (this.secondLabel.indexOf(this.monthNames[date1.getMonth()]) === -1) {
              this.secondLabel.push(this.monthNames[date1.getMonth()])
              // }
            } else if (this.select === 'year') {
              if (i == 0) {
                this.yearMissingFromBegining1(dataList[0]['ds+Transaction Date'], false)
              }
              if (prevKey != -1) {
                if (parseInt(key) - prevKey > 1) {
                  for (let counter = prevKey; counter < parseInt(key) - 1; counter++) {
                    let prevDateObj = new Date(Date.parse(prevDate));
                    prevDateObj.setFullYear(prevDateObj.getFullYear() + (counter - prevKey) + 1);
                    this.secondLabel.push(prevDateObj.getFullYear());
                  //  this.lineChartData[1].data.push(0);
                    this.lineChartData.datasets[1].data.push(0);
                   // this.lineChartData.labels.push(prevDateObj.getFullYear());
                  }
                }
              }
              let date1 = new Date(dataList[0]['ds+Transaction Date']);
              //this.lineChartLabels.push(date1.getFullYear());
              prevKey = parseInt(key);
              prevDate = date1;
              //  if (this.secondLabel.indexOf(date1.getFullYear()) === -1) {
              this.secondLabel.push(date1.getFullYear())
              // }
            }
           // this.lineChartData[1].data.push(totalSpend);
            this.lineChartData.datasets[1].data.push(totalSpend);
          //  this.lineChartData.labels.push(prevDateObj.getFullYear());
            if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'days') {
              this.dateMissingFromEnd1(dataList[0]['ds+Transaction Date'], false)
            }
            if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'month') {
              this.MonthMissingFromEnd1(dataList[0]['ds+Transaction Date'], false)
            }
            if ((Object.keys((objPeriodMonth)).length - 1) === i && this.select === 'year') {
              this.yearMissingFromEnd1(dataList[0]['ds+Transaction Date'], false)
            }
            i++;
          }
        }
        
        this.setTotalSpendchartOptions();
      }

      this.disableDateType = false;
      this.applyBtn = false;
    }
  }
  setTotalSpendchartOptions() {
    let self3 = this;
    this.lineChartOptions = {
      layout: this.layoutPaddingOption,
      legend: {
        display: false,
        responsive: true
      },
      maintainAspectRatio: true,
      plugins: {
        maintainAspectRatio: true,
        datalabels: {
          display: false
        }
      },
      tooltips: {
        enabled: true,
        mode: 'single',
        callbacks: {
          title: function (tooltipItem, data) {
            if (tooltipItem[0].datasetIndex === 1) {
              tooltipItem[0].label = self3.secondLabel[tooltipItem[0].index];
              tooltipItem[0].xLabel = self3.secondLabel[tooltipItem[0].index];
              return tooltipItem[0].xLabel;
            } else if (tooltipItem[0].datasetIndex === 0) {
              return tooltipItem[0].xLabel;
            }
          },
          label: function (tooltipItem, data) {
            return self3.getCurrencySymbol(self3.currency) + (Number(tooltipItem.yLabel.toFixed(2))).toLocaleString();
          }
        }
      }
    }
  }
  calculateBookingSpendApproval(resp) {
    let calculateValues = {
      totalFlightSpend: 0, totalCarSpend: 0, totalHotelSpend: 0, totalBooking: 0, statusCancelled: 0, travelerNumber: [],
      carCounter: 0, hotelCounter: 0, flightCounter: 0, compliance: 0, employeeNumber: []
    }
    for (let item of resp) {
      if (item['Booking Type'] === 'Air') {
        calculateValues.totalFlightSpend = calculateValues.totalFlightSpend + Number(item['-Spend']);
        if (item['Status'] !== 'refund' && item['Status'] !== 'cancelled' && item['Status'] !== 'voided') {
          calculateValues.flightCounter++;
        }
      } else if (item['Booking Type'] === 'Cars') {
        calculateValues.totalCarSpend = calculateValues.totalCarSpend + Number(item['-Spend']);
        if (item['Status'] !== 'refund' && item['Status'] !== 'cancelled') {
          calculateValues.carCounter += Number(item['-Spend']) / Number(item['-Average Spend']);
        }
      } else if (item['Booking Type'] === 'Hotels') {
        calculateValues.totalHotelSpend = calculateValues.totalHotelSpend + Number(item['-Spend']);
        if (item['Status'] !== 'refund' && item['Status'] !== 'cancelled') {
          calculateValues.hotelCounter += Number(item['-Spend']) / Number(item['-Average Spend']);
        }
      }
      if (item['Status'] === 'booked' || item['Status'] === 'Under review' || (item['Status'] === 'CNCL under review')) {
        calculateValues.totalBooking = calculateValues.totalBooking + 1;
      }
      if (item['Status'] === 'voided' || item['Status'] === 'cancelled') {
        calculateValues.statusCancelled = calculateValues.statusCancelled + 1;
      }
      if (item['Status'] !== 'refund') {
        if (item['Policy Status'] === 'Y') {
          calculateValues.compliance = calculateValues.compliance + 1;
        }
      }
      if (calculateValues.travelerNumber.indexOf(item['Traveler']) === -1) {
        calculateValues.travelerNumber.push(item['Traveler']);
      }
      if (calculateValues.employeeNumber.indexOf(item['Booked By']) === -1) {
        calculateValues.employeeNumber.push(item['Booked By']);
      }
    }
    return calculateValues;
  }
  getWeekDate(date) {
    var curr = new Date(date);
    var first = curr.getDate() - curr.getDay() + 1;

    var firstday = new Date(curr.setDate(first));
    var lastday = new Date(curr.setDate(firstday.getDate() + 6));
    let datePipe = new DatePipe('en-US');

    let weekDate = datePipe.transform(new Date(firstday), 'dd MMM yy') + ' - ' + datePipe.transform(new Date(lastday), 'dd MMM yy');
    return weekDate;
  }
  getColor() {
    for (let i = 0; i < this.barChartData1.datasets[0].data.length; i++) {
      this.chartColors22[0].backgroundColor.push('#29EED8');
      this.barChartData1.datasets[0].backgroundColor=['#29EED8'];
     
      this.chartColors22[0].borderColor.push('#29EED8');
    }
    console.log("barchartDta",this.barChartData1);
  }
  getColor1() {
    for (let i = 0; i < this.barChartData5.datasets[0].data.length; i++) {
      this.barChartData5.datasets[0].backgroundColor =['#6663EA']
      this.chartColors33[0].borderColor.push('#6663EA');
    }
  }
  getPreviousChart() {
    this.resetPreviousValue();
    let startDate = DateUtils.getFormattedDateWithoutTimeZone(this.previousStartDate);
    let endDate = DateUtils.getFormattedDateWithoutTimeZone(this.previousEndDate);
    // this.lineChartData[1].data = [];
    this.setDefaultGrouping();
    this.adminPanelService.getChartData(endDate, startDate, this.companyId, this.deptValue, this.bookingValue, this.select, this.dateValue, this.userAccountService.getUserEmail()).subscribe(res => {
      if (res) {
        let response = convertDataToJSON1(res.data.approved, ';');

        response = response.slice(1);
        this.previousOriginalResponse = response;
        this.previousOriginalResponseWithDepartmentFilter = response;
        
        // this.showDepartmentChanged(this.deptValue);
        if (this.deptValue !== 'All Departments') {
          if (this.previousOriginalResponse.length > 0) {
            let originalResponseWithDepartmentFilter1 = this.previousOriginalResponse.filter(item => item['Department'] === this.deptValue);
            this.getPreviousGrandTotal(originalResponseWithDepartmentFilter1);
          }
          if (this.selectBookingValue === 'all') {
            if (this.previousOriginalResponse.length > 0) {
              this.previousOriginalResponseWithDepartmentFilter = this.previousOriginalResponse.filter(item => item['Department'] === this.deptValue);
            }
          } else {
            let selectType;
            if (this.selectBookingValue === 'flight') {
              selectType = 'Air';
            } else if (this.selectBookingValue === 'car') {
              selectType = 'Cars';
            } else if (this.selectBookingValue === 'hotel') {
              selectType = 'Hotels';
            }
            if (this.previousOriginalResponse.length > 0) {
              this.previousOriginalResponseWithDepartmentFilter = this.previousOriginalResponse.filter(item => (item['Department'] === this.deptValue && item['Booking Type'] === selectType));
            }
          }

        } else {
          this.getPreviousGrandTotal(this.previousOriginalResponse);
          if (this.selectBookingValue === 'all') {
            this.previousOriginalResponseWithDepartmentFilter = this.previousOriginalResponse;
          } else {
            let selectType;
            if (this.selectBookingValue === 'flight') {
              selectType = 'Air';
            } else if (this.selectBookingValue === 'car') {
              selectType = 'Cars';
            } else if (this.selectBookingValue === 'hotel') {
              selectType = 'Hotels';
            }
            if (this.previousOriginalResponse.length > 0) {
              this.previousOriginalResponseWithDepartmentFilter = this.previousOriginalResponse.filter(item => (item['Booking Type'] === selectType));
            }
          }
        }
        this.createChartData(this.previousOriginalResponseWithDepartmentFilter, [], false, 'previousData');
      }


    });
  }

  percentageDiff(item1, item2) {
    item1 = parseFloat((item1));
    item2 = parseFloat((item2));
    let diffValue = { growth: '', value: 0 }
    let growthDiff = 0;
    if (item1 > item2 || item1 === item2) {
      diffValue.growth = 'positive';
      growthDiff = item1 - item2;
    } else if (item2 > item1) {
      diffValue.growth = 'negative';
      growthDiff = item2 - item1;
    }
    let total = item2;
    let percValue = 0;
    if (total > 0) {
      percValue = Math.round((growthDiff * 100) / total);
      diffValue.value = percValue;
    } else {
      diffValue.value = 100;
    }

    return diffValue.value;
  }
  showChangeReport(item1, item2) {
    item1 = parseFloat((item1));
    item2 = parseFloat((item2));
    let diffValue = { growth: '', value: 0 }
    if (item1 > item2 || item1 === item2) {
      diffValue.growth = 'positive';
      return diffValue.growth;
    } else if (item2 > item1) {
      diffValue.growth = 'negative';
      return diffValue.growth;
    }
  }
  getPlugins() {
    return ChartDataLabels;
  }
  triggerFalseClick() {
    let el: HTMLElement = this.setHeightOfUniqueTravelerOnMobile.nativeElement;
    el.click();
  }
  setHeight() {
    this.triggerFalseClick();
  }

  public isDutyOfCareEnabled() {
    return (this.userAccountService.isDutyOfCareEnabled && this.searchService.mapSupprted);
  }
}
