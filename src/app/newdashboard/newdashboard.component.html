<div *ngIf="!isMobile1" class="card-div shadow">
  <div class="card-div-inner">
    <div style="float:left;display: grid;">
      <div style="display: inline-block;">
        <div class="row">
          <div *ngIf="isDutyOfCareEnabled()" class="col-auto" style="margin-right:5px;">
            <label class="name"> {{'activeTraveler.ActiveTravelers' | translate }}</label>
            <div class="first-box">
              <div class="map-box">
                <google-map [center]="{ lat: 41.399115, lng: 2.160962 }" [zoom]="zoom" [options]="mapOptions"
                  height="400px" width="100%" class="map-class" (mapReady)="mapReadyCallback($event)"
                  (boundsChanged)="boundsChangedCallback($event)" (zoomChanged)="zoomChanged($event)">
                  <map-marker *ngFor="let data of activeTravellersList; let i = index"
                    [position]="{ lat: data.latitude, lng: data.longitude }" [zIndex]="data.markerClicked ? 100 : i"
                    [icon]="markerImage" (mapMouseover)="onMouseOver(infoWindows[i], $event)"
                    (mapMouseout)="onMouseOut(infoWindows[i], $event)" #marker="mapMarker">
                    <map-info-window #infoWindow>
                      <div>
                        <div class="mapName">{{ data.name }}</div>
                        <div class="mapName">{{ data.destinations }}</div>
                        <div class="mapName">
                          {{ getDisplayDate(data.departure, 'dd MMM', data.type) }} -
                          {{ getDisplayDate(data.return, 'dd MMM', data.type) }}
                        </div>
                      </div>
                    </map-info-window>
                  </map-marker>
                </google-map>

              </div>
              <div class="tab11">
                <div class="top-strip" style="margin-bottom: 10px !important;">
                  <ul>
                    <li (click)="getData('today')">
                      <span class="{{this.selectDateRange ==='today' ? 'select1':'unselect1'}}">
                        {{'activeTraveler.TODAY' | translate }}<div *ngIf="this.selectDateRange ==='today'"
                          class="underline"></div></span>
                    </li>
                    <li (click)="getData('7Days')">
                      <span class="{{this.selectDateRange ==='7Days' ? 'select1':'unselect1'}}">
                        {{'activeTraveler.NEXT7DAYS' | translate }} <div *ngIf="this.selectDateRange ==='7Days'"
                          class="underline"></div></span>
                    </li>
                  </ul>
                  <a [routerLink]="['/admin']"
                    [queryParams]="{type: 'employees',subType:'activeTravelers',subSubType:'30Days'}"
                    routerLinkActive="active">
                    <span class="alltravelers"> {{'dashboard.SeeAllTravelers' | translate }}<img
                        style="margin-left:10px;" src="assets/images/Line11.png"></span></a>
                </div>
                <div *ngIf="this.activeTravellersList.length==0 && getActiveTraveler" class="nodata">
                  {{this.resultErrorMessage | translate}}
                </div>
                <div *ngIf="!getActiveTraveler" class="col-7"
                  style="text-align: center;padding-top:20px;padding-left:15px;top: 40px;position: relative;left: 70px;">
                  <app-loader *ngIf="!getActiveTraveler" [spinnerStyle]="true"></app-loader>
                </div>
                <div *ngIf="this.activeTravellersList.length > 0"
                  style="padding-left: 20px;margin-top: 20px; overflow: auto;height:170px;width:462px">
                  <div *ngFor="let item of this.activeTravellersList; let i = index"
                    style="display: inline-flex;text-align: center;padding-left: 7px !important;margin-bottom: 0px !important;">
                    <div class="travelerDetailsBox" style="padding-left: 7px !important;"
                      [ngStyle]="{'margin-bottom': (i===this.activeTravellersList.length -1) ? '20px':'none'}"
                      (click)="markerClick($event,item,i)">
                      <div class="name11">{{ item.name}}</div>
                      <div class="name11">{{ item.destinations }}</div>
                      <div class="name11">{{ getDisplayDate(item.departure,'dd MMM',item.type)}} - {{
                        getDisplayDate(item.return,'dd MMM',item.type)}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-auto"
            [ngStyle]="{'margin' : isDutyOfCareEnabled()?'0':'0 auto','width': isDutyOfCareEnabled()?'auto':'100%'}">
            <label class="name"> {{'dashboard.PendingApprovals' | translate }}</label>
            <div class="approval-box" [ngStyle]="{ 'width': isDutyOfCareEnabled()?'267px':'100%'}">
              <a [routerLink]="['/admin']" [queryParams]="{type: 'approvals',subType:'pending'}"
                routerLinkActive="active"> <span class="alltravelers"
                  style="float: right;margin-top: 18px;cursor:pointer;margin-right:27px !important;">
                  {{'dashboard.ManageAll' | translate }}<img style="margin-left:10px;"
                    src="assets/images/Line11.png"></span></a>
              <div *ngIf="this.pendingApproval.length==0" class="nodata">
                {{'dashboard.Therearenopendingapprovalsatthistime.' | translate }}
              </div>
              <div *ngIf="this.pendingApproval.length > 0" [ngStyle]="{ 'width': isDutyOfCareEnabled()?'260px':'95%'}"
                style="padding-left: 5px;margin-top: 20px;overflow: auto;height:170px;overflow-x: hidden;">
                <div *ngFor="let item of this.pendingApproval; let i = index"
                  style="padding-left: 7px !important;margin-bottom: 0px !important;">
                  <div *ngIf="hideAfterExpire(item.expiryTime,i)"
                    [ngStyle]="{ 'width': isDutyOfCareEnabled()?'236px':'100%'}"
                    class="{{ selectApprovalIndex===i ?'pendingtravelerDetailsBox1':'pendingtravelerDetailsBox'}}"
                    style="padding-left: 17px !important;"
                    [ngStyle]="{'margin-bottom': (i===this.pendingApproval.length -1) ? '0px':'0px'}"
                    (click)="selectApproval(i)">
                    <div class="{{ selectApprovalIndex===i ?'name1111':'name111'}}">{{ item.name}}</div>
                    <div *ngIf="selectApprovalIndex===i" class=""
                      [ngStyle]="{ 'width': isDutyOfCareEnabled()?'auto':'100%'}" style="color: #fff;font-size: 12px;white-space: nowrap;
                          padding-top: 2px;text-align: right;padding-right: 46px;"> {{'dashboard.expiresin' | translate
                      }}<span
                        *ngIf="getTimeDiffernece(item.expiryTime)?.hrs > 0">{{getTimeDiffernece(item.expiryTime)?.hrs +
                        'h' }}</span> {{ getTimeDiffernece(item.expiryTime)?.mins + 'm'}}</div>
                  </div>

                  <div *ngIf="selectApprovalIndex===i && hideAfterExpire(item.expiryTime,i)" class="name111"
                    style="font-size:12px !important;float:right;width:145px;color:#757575;E !important;">
                    {{'dashboard.expiresat' | translate }} {{gettime(item.expiryTime)}}
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="divideline">
        <span class="name" style="position:relative ;top: 26px;">{{'dashboard.Overview'| translate }}</span>
        <svg width="1128" height="23" viewBox="0 0 1128 23" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="192" y="6" width="936" height="8" rx="4" fill="#8936F3" />
          <path fill="#8936F3" />
        </svg>

      </div>
      <div style="display: inline-block;min-width: 1141px;">
        <div class="row">
          <div class="col-5">
            <div class="left-panel">
              <label class="show"> {{'dashboard.Period' | translate }}</label>
              <div style="margin-top: 2px;margin-left:5px;">
                <div class="filter custom-selectbox">
                  <div class="input-box" style="margin-right:5px;position: relative; top: 3px;">
                    <input class="input" id="daterangeSelection"
                      (click)="openNgxModal('daterangeSelection',chartDatePicker);chartDatePicker.show()"
                      style="background: #F7F7F9 !important;" readonly=""><span class="dateShow"
                      (click)="openNgxModal('daterangeSelection',chartDatePicker);chartDatePicker.show()">{{daterangepickerModel[0]
                      | date : 'dd MMM yyyy'}} - {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span><svg
                      class="down-arrow"
                      (click)="openNgxModal('daterangeSelection',chartDatePicker);chartDatePicker.show()" width="15"
                      height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                        fill="#8936F3" />
                    </svg>

                  </div>
                  <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'daterangeSelection')" [hideDelay]="0"
                    (onClose)="handleModalEvents('onClose', 'daterangeSelection')"
                    (onDismiss)="handleModalEvents('onDismiss', ' daterangeSelection')" [closable]="false"
                    #daterangeSelection identifier="daterangeSelection">
                    <div class="modal-container flight-modal-container filter-modal1 modalAirportFilterInfo"
                      [ngStyle]="changeStyle()" (click)="$event.stopPropagation();">
                      <div class="modal-header"
                        style="background-color: #fff !important;padding-top:10px !important;overflow:visible;">
                        <div class="tab-list top-strip" style="">

                          <ul style="overflow: visible !important;">
                            <li class="{{ viewMode1 == 'tab12' ? 'select':'unselect'}}"
                              [class.active]="viewMode1 == 'tab12'" rel="tab11" (click)="presetsTabClicked()"
                              style="margin-left:0px !important;margin-right:0px !important;">{{ 'report.Presets' |
                              translate }}
                              <div *ngIf="viewMode1 == 'tab12'" class="underline"></div>
                            </li>
                            <li class="{{ viewMode1 == 'tab11' ? 'select':'unselect'}}"
                              [class.active]="viewMode1 == 'tab11'" rel="tab14" (click)="customTabClicked()">{{
                              'report.Custom' | translate }}
                              <div *ngIf="viewMode1 == 'tab11'" class="underline"></div>
                            </li>
                          </ul>
                        </div>
                        <input class="input" [hidden]="viewMode1=='tab12'"
                          style="width:100% !important;position: relative;top:8px;" readonly=""><span class="dateShow1"
                          [hidden]="viewMode1=='tab12'" style="top:24px !important;">{{daterangepickerModel[0] | date :
                          'dd MMM yyyy'}} - {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span>
                      </div>
                      <hr>
                      <div [hidden]="viewMode1=='tab12'" style="position: relative;top: -30px;">
                        <input class="input" bsDaterangepicker #chartDatePicker="bsDaterangepicker"
                          style="width:100% !important;visibility: hidden;" [(ngModel)]="daterangepickerModel"
                          (bsValueChange)="setStartDate($event,false)" [outsideClick]="true" [maxDate]="maximumDate1"
                          [bsConfig]="{showWeekNumbers: false , showPreviousMonth: true}"
                          (onShown)="onShowPicker($event, chartDatePicker)" (onHidden)="onHidePicker()" container=""
                          readonly />
                      </div>
                      <div class="modal-body" [ngStyle]="changeStyle1()">
                        <div [hidden]="viewMode1=='tab11'">
                          <div *ngFor="let item of this.dateOptions;let i=index"
                            style="width: auto;min-height: 15px;display:flex;line-height: 3em;min-height: 3em;">
                            <div
                              style="font-size:14px;color:#5f6368;padding-left:22px;margin-bottom:7px;cursor: pointer;"
                              (click)="showDateChanged(item.id,true)">{{item.name | translate}}</div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </ngx-smart-modal>
                </div>

              </div>
              <label class="show">{{'dashboard.Compareto' | translate }}</label>
              <div style="margin-top: 2px;margin-left:5px;">
                <div class="filter custom-selectbox">
                  <div class="input-box" style="margin-right:5px;position: relative; top: 3px;">
                    <input class="input" id="daterangeSelection1" (click)="openNgxModal1('daterangeSelection1')"
                      style="background: #F7F7F9 !important;" readonly=""><span class="dateShow"
                      (click)="openNgxModal1('daterangeSelection1')">{{selectPreviousDate1 | translate}}</span>
                    <svg class="down-arrow" (click)="openNgxModal1('daterangeSelection1')" width="15" height="9"
                      viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                        fill="#8936F3" />
                    </svg>

                  </div>
                  <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'daterangeSelection1')" [hideDelay]="0"
                    (onClose)="handleModalEvents('onClose', 'daterangeSelection1')"
                    (onDismiss)="handleModalEvents('onDismiss', ' daterangeSelection1')" [closable]="false"
                    #daterangeSelection1 identifier="daterangeSelection1">
                    <div class="modal-container flight-modal-container filter-modal2 modalAirportFilterInfo"
                      style="min-width: 444px;" (click)="$event.stopPropagation();">
                      <div class="modal-body"
                        style="min-width: 450px;max-height: 250px;background: #F7F7F9;padding-left: 7px !important;padding-right:15px!important;padding-top:7px !important;padding-bottom:16px !important;">
                        <div class="presetSelect" *ngFor="let item of this.previousOptions;let i=index"
                          [ngStyle]="getPreviousDropdownStyle(item)" (click)="selectPreviousDateRange(item)">
                          <div class="row" style="padding-right: 8px;">
                            <div class="col-4" style="font-size:18px;color:#000000;white-space: nowrap;">{{item.peroid1
                              | translate}}
                            </div>
                            <div class="col-8"
                              style="padding-left:70px!important;font-size:16px;color:#716F74;white-space: nowrap;">
                              {{item.date}}</div>
                          </div>
                        </div>

                      </div>
                    </div>
                  </ngx-smart-modal>
                </div>

              </div>
              <label class="show">{{'dashboard.Department' | translate }}</label>
              <div id="chartDepartment" style="margin-top: 5px;margin-left: 5px;">
                <div class="input" style="background: #F7F7F9 !important;">
                  <ng-select #chartDepartment [disabled]="this.disableDateType" dropdownPosition="bottom"
                    [searchable]="false" [clearable]="false" [(ngModel)]="deptValue" [items]="departmentOptions"
                    bindLabel="value" bindValue="value" (change)="showDepartmentChanged($event.value)">
                    <ng-template ng-option-tmp let-option="item">
                      <span class="option-title">{{option.name |
                        translate}}</span>

                    </ng-template>
                    <ng-template ng-label-tmp let-item="item">
                      <span> {{item.name | translate}}</span>
                    </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg class="down-arrow" (click)="chartDepartment.toggle()" style="left: 225px;top: -28px;" width="15"
                  height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
              </div>
            </div>
          </div>
          <div *ngIf="!this.applyBtn && !this.applyBtn1" class="col-7">
            <div class="right-panel" style="margin-left:-20px !important;">
              <div class="row">
                <div class="col-4">
                  <div class="{{(this.totalBooking >0 || this.totalPreviousBooking>0) ? 'dollar1':'dollar' }}">
                    <span *ngIf="this.grandTotalSpend >0">
                      {{this.grandTotalSpend | currency : getCurrencySymbol(this.currency) : 'code' : "1.2-2"}}</span>
                    <span *ngIf="this.grandTotalSpend===0">
                      {{getCurrencySymbol(this.currency)}} 0</span>
                  </div>
                  <div *ngIf="this.totalBooking >0 || this.totalPreviousBooking>0" class="change">
                    <img *ngIf="showChangeReport(this.grandTotalSpend,this.grandPreviousTotalSpend)==='positive'"
                      style="margin-right:3px;position: relative;top: -1px;" src="assets/images/positive.png">
                    <img *ngIf="showChangeReport(this.grandTotalSpend,this.grandPreviousTotalSpend)==='negative'"
                      style="margin-right:3px;position: relative;top: -1px;" src="assets/images/negative.png"> <span
                      class="{{(this.totalBooking >0 || this.totalPreviousBooking>0) ? 'show3':'show2' }}"
                      style="margin-left:0px !important;font-size:20px !important;font-family:var(--globalFontfamilyr) !important;font-weight:300 !important;">
                      {{percentageDiff(this.grandTotalSpend,this.grandPreviousTotalSpend)}}%</span><span
                      style="margin-left: 20px;"
                      class="{{(this.totalBooking >0 || this.totalPreviousBooking>0) ? 'show4':'show1' }}">
                      {{'dashboard.change' | translate }}</span>
                  </div>
                  <div *ngIf="this.totalBooking ===0 && this.totalPreviousBooking===0" class="change">
                    <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png">
                    <span class="{{(this.totalBooking >0 || this.totalPreviousBooking>0) ? 'show3':'show2' }}"
                      style="margin-left:0px !important;font-size:20px !important;font-family:var(--globalFontfamilyr) !important;font-weight:300 !important;">
                      0%</span><span style="margin-left: 20px;"
                      class="{{(this.totalBooking >0 || this.totalPreviousBooking>0) ? 'show4':'show1' }}">
                      {{'dashboard.change' | translate }}</span>
                  </div>

                </div>
                <div class="col-8">
                  <svg *ngIf="this.doughnutChartData.length==0" width="341" height="204" viewBox="0 0 341 204"
                    fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M228.188 25.8249L238.542 14.2971L228.188 25.8249Z" fill="#AEAEAE" />
                    <path
                      d="M253.528 10.064C252.952 7.952 251.384 6.672 249 6.672C246.296 6.672 243.816 8.608 243.816 12.4C243.816 16.192 246.296 18.128 249 18.128C251.384 18.128 252.952 16.848 253.528 14.736L251.864 14.32C251.304 15.744 250.52 16.464 249 16.464C247.112 16.464 245.608 15.136 245.608 12.4C245.608 9.664 247.112 8.336 249 8.336C250.52 8.336 251.304 9.056 251.864 10.48L253.528 10.064ZM254.829 15.68C254.829 17.36 255.965 18.128 257.309 18.128C258.445 18.128 259.277 17.68 259.805 16.944L259.965 18H261.373V12.992C261.373 10.624 260.029 9.808 258.157 9.808C256.605 9.808 255.453 10.672 255.117 12.032L256.637 12.352C256.909 11.632 257.485 11.28 258.141 11.28C258.989 11.28 259.709 11.68 259.709 12.688V13.04H258.317C256.253 13.04 254.829 14.016 254.829 15.68ZM256.589 15.68C256.589 14.912 257.229 14.432 258.253 14.432H259.709V14.608C259.709 16 258.621 16.656 257.597 16.656C256.893 16.656 256.589 16.224 256.589 15.68ZM265.087 18V15.008C265.087 12.464 266.191 11.28 267.263 11.28C267.599 11.28 267.951 11.36 268.207 11.52L268.495 10C268.255 9.904 267.903 9.808 267.471 9.808C266.607 9.808 265.647 10.32 265.087 11.28V9.936H263.423V18H265.087ZM271.932 9.808C270.476 9.808 269.452 10.848 269.452 12.096C269.452 13.328 270.252 13.92 271.18 14.304C272.124 14.704 273.1 14.896 273.1 15.6C273.1 16.336 272.652 16.656 272.012 16.656C271.116 16.656 270.556 15.92 270.236 15.2L268.828 15.968C269.243 16.976 270.284 18.128 271.948 18.128C273.676 18.128 274.748 16.96 274.748 15.568C274.748 14.128 273.58 13.648 272.492 13.232C271.66 12.912 271.052 12.656 271.052 12.08C271.052 11.552 271.436 11.28 271.852 11.28C272.364 11.28 272.86 11.504 273.26 12.016L274.316 11.088C273.804 10.288 272.908 9.808 271.932 9.808ZM286.876 8.56C286.412 7.728 285.708 7.04 284.7 6.784V5.36H283.1V6.752C281.804 7.04 280.86 8.176 280.86 9.616C280.86 13.072 285.628 12.592 285.628 14.784C285.628 15.776 284.924 16.464 283.916 16.464C282.636 16.464 281.98 15.344 281.66 14.096L280.22 15.008C280.556 16.544 281.58 17.744 283.1 18.048V19.44H284.7V18.048C286.3 17.76 287.42 16.496 287.42 14.72C287.42 11.264 282.62 11.696 282.62 9.488C282.62 8.832 283.068 8.272 283.788 8.272C284.444 8.272 285.004 8.656 285.452 9.504L286.876 8.56ZM293.15 6.672C290.51 6.672 289.118 8.864 289.118 12.4C289.118 15.936 290.51 18.128 293.15 18.128C295.79 18.128 297.182 15.936 297.182 12.4C297.182 8.864 295.79 6.672 293.15 6.672ZM293.15 8.208C294.734 8.208 295.486 9.776 295.486 12.4C295.486 15.024 294.734 16.592 293.15 16.592C291.566 16.592 290.814 15.024 290.814 12.4C290.814 9.776 291.566 8.208 293.15 8.208ZM300.971 6C300.059 7.536 299.275 9.264 299.275 12.4C299.275 15.536 300.059 17.264 300.971 18.8H302.635C301.515 17.072 300.939 15.296 300.939 12.4C300.939 9.504 301.515 7.728 302.635 6H300.971ZM308.119 6.672C305.479 6.672 304.087 8.864 304.087 12.4C304.087 15.936 305.479 18.128 308.119 18.128C310.759 18.128 312.151 15.936 312.151 12.4C312.151 8.864 310.759 6.672 308.119 6.672ZM308.119 8.208C309.703 8.208 310.455 9.776 310.455 12.4C310.455 15.024 309.703 16.592 308.119 16.592C306.535 16.592 305.783 15.024 305.783 12.4C305.783 9.776 306.535 8.208 308.119 8.208ZM314.052 9.232C314.052 10.656 315.188 11.792 316.644 11.792C318.1 11.792 319.236 10.656 319.236 9.232C319.236 7.808 318.1 6.672 316.644 6.672C315.188 6.672 314.052 7.808 314.052 9.232ZM315.46 9.232C315.46 8.528 315.908 8 316.644 8C317.38 8 317.828 8.528 317.828 9.232C317.828 9.936 317.38 10.464 316.644 10.464C315.908 10.464 315.46 9.936 315.46 9.232ZM320.548 15.568C320.548 16.992 321.684 18.128 323.14 18.128C324.596 18.128 325.732 16.992 325.732 15.568C325.732 14.144 324.596 13.008 323.14 13.008C321.684 13.008 320.548 14.144 320.548 15.568ZM321.956 15.568C321.956 14.864 322.404 14.336 323.14 14.336C323.876 14.336 324.324 14.864 324.324 15.568C324.324 16.272 323.876 16.8 323.14 16.8C322.404 16.8 321.956 16.272 321.956 15.568ZM323.844 6.272L315.22 17.792L316.148 18.512L324.772 6.992L323.844 6.272ZM327.041 6C328.161 7.728 328.737 9.504 328.737 12.4C328.737 15.296 328.161 17.072 327.041 18.8H328.705C329.617 17.264 330.401 15.536 330.401 12.4C330.401 9.264 329.617 7.536 328.705 6H327.041Z"
                      fill="#AEAEAE" />
                    <path d="M120.638 67.8311L105.629 63.9813L120.638 67.8311Z" fill="#AEAEAE" />
                    <path
                      d="M1.504 73H3.264V67.848H7.808V73H9.568V61.8H7.808V66.184H3.264V61.8H1.504V73ZM11.5028 68.968C11.5028 71.24 12.9748 73.128 15.4708 73.128C17.9668 73.128 19.4388 71.24 19.4388 68.968C19.4388 66.696 17.9668 64.808 15.4708 64.808C12.9748 64.808 11.5028 66.696 11.5028 68.968ZM13.2628 68.968C13.2628 67.608 14.0148 66.28 15.4708 66.28C16.9268 66.28 17.6788 67.608 17.6788 68.968C17.6788 70.328 16.9268 71.656 15.4708 71.656C14.0148 71.656 13.2628 70.328 13.2628 68.968ZM20.1798 66.408H21.5238V70.248C21.5238 72.232 22.6438 73.128 24.1638 73.128C24.6118 73.128 25.0278 73.048 25.4598 72.84L25.0278 71.432C24.7398 71.608 24.5477 71.656 24.2598 71.656C23.5238 71.656 23.1878 71.256 23.1878 70.344V66.408H25.2838V64.936H23.1878V61.672L21.5238 62.712V64.936H20.1798V66.408ZM30.3565 66.28C31.5565 66.28 32.1645 67.144 32.3085 68.04H28.3885C28.5645 67.032 29.2525 66.28 30.3565 66.28ZM34.0685 68.712C34.0685 66.904 32.9325 64.808 30.3565 64.808C27.7005 64.808 26.5965 67.048 26.5965 68.968C26.5965 70.888 27.7005 73.128 30.3565 73.128C32.1965 73.128 33.1085 72.376 33.8925 70.856L32.4685 70.296C32.0525 71.176 31.2205 71.656 30.3565 71.656C29.0445 71.656 28.3565 70.616 28.3565 69.432H34.0685V68.712ZM35.8753 61.672V73H37.5393V61.672H35.8753ZM42.244 64.808C40.788 64.808 39.764 65.848 39.764 67.096C39.764 68.328 40.564 68.92 41.492 69.304C42.436 69.704 43.412 69.896 43.412 70.6C43.412 71.336 42.964 71.656 42.324 71.656C41.428 71.656 40.868 70.92 40.548 70.2L39.14 70.968C39.556 71.976 40.596 73.128 42.26 73.128C43.988 73.128 45.06 71.96 45.06 70.568C45.06 69.128 43.892 68.648 42.804 68.232C41.972 67.912 41.364 67.656 41.364 67.08C41.364 66.552 41.748 66.28 42.164 66.28C42.676 66.28 43.172 66.504 43.572 67.016L44.628 66.088C44.116 65.288 43.22 64.808 42.244 64.808ZM57.1881 63.56C56.7241 62.728 56.0201 62.04 55.0121 61.784V60.36H53.4121V61.752C52.1161 62.04 51.1721 63.176 51.1721 64.616C51.1721 68.072 55.9401 67.592 55.9401 69.784C55.9401 70.776 55.2361 71.464 54.2281 71.464C52.9481 71.464 52.2921 70.344 51.9721 69.096L50.5321 70.008C50.8681 71.544 51.8921 72.744 53.4121 73.048V74.44H55.0121V73.048C56.6121 72.76 57.7321 71.496 57.7321 69.72C57.7321 66.264 52.9321 66.696 52.9321 64.488C52.9321 63.832 53.3801 63.272 54.1001 63.272C54.7561 63.272 55.3161 63.656 55.7641 64.504L57.1881 63.56ZM63.4623 61.672C60.8223 61.672 59.4303 63.864 59.4303 67.4C59.4303 70.936 60.8223 73.128 63.4623 73.128C66.1023 73.128 67.4943 70.936 67.4943 67.4C67.4943 63.864 66.1023 61.672 63.4623 61.672ZM63.4623 63.208C65.0463 63.208 65.7983 64.776 65.7983 67.4C65.7983 70.024 65.0463 71.592 63.4623 71.592C61.8783 71.592 61.1263 70.024 61.1263 67.4C61.1263 64.776 61.8783 63.208 63.4623 63.208ZM71.2836 61C70.3716 62.536 69.5876 64.264 69.5876 67.4C69.5876 70.536 70.3716 72.264 71.2836 73.8H72.9476C71.8276 72.072 71.2516 70.296 71.2516 67.4C71.2516 64.504 71.8276 62.728 72.9476 61H71.2836ZM78.431 61.672C75.791 61.672 74.399 63.864 74.399 67.4C74.399 70.936 75.791 73.128 78.431 73.128C81.071 73.128 82.463 70.936 82.463 67.4C82.463 63.864 81.071 61.672 78.431 61.672ZM78.431 63.208C80.015 63.208 80.767 64.776 80.767 67.4C80.767 70.024 80.015 71.592 78.431 71.592C76.847 71.592 76.095 70.024 76.095 67.4C76.095 64.776 76.847 63.208 78.431 63.208ZM84.3644 64.232C84.3644 65.656 85.5004 66.792 86.9564 66.792C88.4124 66.792 89.5484 65.656 89.5484 64.232C89.5484 62.808 88.4124 61.672 86.9564 61.672C85.5004 61.672 84.3644 62.808 84.3644 64.232ZM85.7724 64.232C85.7724 63.528 86.2204 63 86.9564 63C87.6924 63 88.1404 63.528 88.1404 64.232C88.1404 64.936 87.6924 65.464 86.9564 65.464C86.2204 65.464 85.7724 64.936 85.7724 64.232ZM90.8604 70.568C90.8604 71.992 91.9964 73.128 93.4524 73.128C94.9084 73.128 96.0444 71.992 96.0444 70.568C96.0444 69.144 94.9084 68.008 93.4524 68.008C91.9964 68.008 90.8604 69.144 90.8604 70.568ZM92.2684 70.568C92.2684 69.864 92.7164 69.336 93.4524 69.336C94.1884 69.336 94.6364 69.864 94.6364 70.568C94.6364 71.272 94.1884 71.8 93.4524 71.8C92.7164 71.8 92.2684 71.272 92.2684 70.568ZM94.1564 61.272L85.5324 72.792L86.4604 73.512L95.0844 61.992L94.1564 61.272ZM97.3539 61C98.4739 62.728 99.0499 64.504 99.0499 67.4C99.0499 70.296 98.4739 72.072 97.3539 73.8H99.0179C99.9299 72.264 100.714 70.536 100.714 67.4C100.714 64.264 99.9299 62.536 99.0179 61H97.3539Z"
                      fill="#AEAEAE" />
                    <path d="M208.403 148.895L224.862 162.634L208.403 148.895Z" fill="#AEAEAE" />
                    <path
                      d="M221.504 150.8V162H223.264V157.024H227.312V155.36H223.264V152.464H228.288V150.8H221.504ZM230.125 150.672V162H231.789V150.672H230.125ZM233.57 151.664C233.57 152.304 234.066 152.8 234.722 152.8C235.378 152.8 235.874 152.304 235.874 151.664C235.874 151.024 235.378 150.496 234.722 150.496C234.066 150.496 233.57 151.024 233.57 151.664ZM233.89 153.936V162H235.554V153.936H233.89ZM237.814 156.512C237.814 157.2 238.006 157.776 238.358 158.208C237.766 158.544 237.382 159.12 237.382 159.84C237.382 160.4 237.558 160.816 237.846 161.104C237.43 161.488 237.158 162.048 237.158 162.736C237.158 164.496 238.678 165.392 240.71 165.392C242.518 165.392 244.246 164.608 244.246 162.608C244.246 161.088 243.158 160.304 241.574 160.304H239.798C239.238 160.304 238.966 160.112 238.966 159.68C238.966 159.376 239.11 159.136 239.398 158.976C239.766 159.12 240.214 159.216 240.694 159.216C242.326 159.216 243.59 158.208 243.59 156.512C243.59 156 243.462 155.552 243.238 155.168L244.326 154.56L243.654 153.36L242.166 154.16C241.734 153.936 241.238 153.808 240.694 153.808C239.062 153.808 237.814 154.832 237.814 156.512ZM241.222 161.712C242.006 161.712 242.55 161.968 242.55 162.736C242.55 163.52 241.862 163.952 240.71 163.952C239.478 163.952 238.758 163.44 238.758 162.64C238.758 162.224 238.87 161.92 239.11 161.664C239.366 161.696 239.638 161.712 239.926 161.712H241.222ZM239.43 156.512C239.43 155.712 239.958 155.2 240.694 155.2C241.446 155.2 241.974 155.712 241.974 156.512C241.974 157.312 241.446 157.824 240.694 157.824C239.958 157.824 239.43 157.312 239.43 156.512ZM245.798 150.672V162H247.462V158.592C247.462 156.448 248.726 155.28 249.638 155.28C250.39 155.28 250.902 155.52 250.902 156.752V162H252.566V156.32C252.566 154.608 251.67 153.808 250.182 153.808C248.966 153.808 248.038 154.48 247.462 155.312V150.672H245.798ZM253.961 155.408H255.305V159.248C255.305 161.232 256.425 162.128 257.945 162.128C258.393 162.128 258.809 162.048 259.241 161.84L258.809 160.432C258.521 160.608 258.329 160.656 258.041 160.656C257.305 160.656 256.969 160.256 256.969 159.344V155.408H259.065V153.936H256.969V150.672L255.305 151.712V153.936H253.961V155.408ZM263.385 153.808C261.929 153.808 260.905 154.848 260.905 156.096C260.905 157.328 261.705 157.92 262.633 158.304C263.577 158.704 264.553 158.896 264.553 159.6C264.553 160.336 264.105 160.656 263.465 160.656C262.569 160.656 262.009 159.92 261.689 159.2L260.281 159.968C260.697 160.976 261.737 162.128 263.401 162.128C265.129 162.128 266.201 160.96 266.201 159.568C266.201 158.128 265.033 157.648 263.945 157.232C263.113 156.912 262.505 156.656 262.505 156.08C262.505 155.552 262.889 155.28 263.305 155.28C263.817 155.28 264.313 155.504 264.713 156.016L265.769 155.088C265.257 154.288 264.361 153.808 263.385 153.808ZM278.329 152.56C277.865 151.728 277.161 151.04 276.153 150.784V149.36H274.553V150.752C273.257 151.04 272.313 152.176 272.313 153.616C272.313 157.072 277.081 156.592 277.081 158.784C277.081 159.776 276.377 160.464 275.369 160.464C274.089 160.464 273.433 159.344 273.113 158.096L271.673 159.008C272.009 160.544 273.033 161.744 274.553 162.048V163.44H276.153V162.048C277.753 161.76 278.873 160.496 278.873 158.72C278.873 155.264 274.073 155.696 274.073 153.488C274.073 152.832 274.521 152.272 275.241 152.272C275.897 152.272 276.457 152.656 276.905 153.504L278.329 152.56ZM284.603 150.672C281.963 150.672 280.571 152.864 280.571 156.4C280.571 159.936 281.963 162.128 284.603 162.128C287.243 162.128 288.635 159.936 288.635 156.4C288.635 152.864 287.243 150.672 284.603 150.672ZM284.603 152.208C286.187 152.208 286.939 153.776 286.939 156.4C286.939 159.024 286.187 160.592 284.603 160.592C283.019 160.592 282.267 159.024 282.267 156.4C282.267 153.776 283.019 152.208 284.603 152.208ZM296.549 150C295.637 151.536 294.853 153.264 294.853 156.4C294.853 159.536 295.637 161.264 296.549 162.8H298.213C297.093 161.072 296.517 159.296 296.517 156.4C296.517 153.504 297.093 151.728 298.213 150H296.549ZM303.697 150.672C301.057 150.672 299.665 152.864 299.665 156.4C299.665 159.936 301.057 162.128 303.697 162.128C306.337 162.128 307.729 159.936 307.729 156.4C307.729 152.864 306.337 150.672 303.697 150.672ZM303.697 152.208C305.281 152.208 306.033 153.776 306.033 156.4C306.033 159.024 305.281 160.592 303.697 160.592C302.113 160.592 301.361 159.024 301.361 156.4C301.361 153.776 302.113 152.208 303.697 152.208ZM309.63 153.232C309.63 154.656 310.766 155.792 312.222 155.792C313.678 155.792 314.814 154.656 314.814 153.232C314.814 151.808 313.678 150.672 312.222 150.672C310.766 150.672 309.63 151.808 309.63 153.232ZM311.038 153.232C311.038 152.528 311.486 152 312.222 152C312.958 152 313.406 152.528 313.406 153.232C313.406 153.936 312.958 154.464 312.222 154.464C311.486 154.464 311.038 153.936 311.038 153.232ZM316.126 159.568C316.126 160.992 317.262 162.128 318.718 162.128C320.174 162.128 321.31 160.992 321.31 159.568C321.31 158.144 320.174 157.008 318.718 157.008C317.262 157.008 316.126 158.144 316.126 159.568ZM317.534 159.568C317.534 158.864 317.982 158.336 318.718 158.336C319.454 158.336 319.902 158.864 319.902 159.568C319.902 160.272 319.454 160.8 318.718 160.8C317.982 160.8 317.534 160.272 317.534 159.568ZM319.422 150.272L310.798 161.792L311.726 162.512L320.35 150.992L319.422 150.272ZM322.62 150C323.74 151.728 324.316 153.504 324.316 156.4C324.316 159.296 323.74 161.072 322.62 162.8H324.284C325.196 161.264 325.98 159.536 325.98 156.4C325.98 153.264 325.196 151.536 324.284 150H322.62Z"
                      fill="#AEAEAE" />
                    <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M248 91C248 81.4565 245.991 72.0199 242.104 63.3039C238.217 54.5878 232.539 46.7876 225.439 40.4105C218.339 34.0333 209.976 29.2221 200.894 26.2897C191.813 23.3572 182.215 22.3693 172.726 23.3901L172.726 23.39C158.957 24.8713 145.969 30.5212 135.498 39.5841C125.027 48.647 117.573 60.6909 114.132 74.1051C110.692 87.5192 111.429 101.664 116.245 114.648C121.061 127.632 129.726 138.836 141.082 146.762L141.082 146.762C151.276 153.876 163.225 158.061 175.63 158.859C188.036 159.658 200.423 157.041 211.445 151.293C222.467 145.544 231.702 136.884 238.147 126.254C244.592 115.624 248 103.431 248 90.9999L248 91ZM197.859 91C197.859 88.4936 197.331 86.0153 196.31 83.7262C195.289 81.4372 193.798 79.3886 191.933 77.7138C190.069 76.039 187.873 74.7755 185.487 74.0053C183.102 73.2352 180.582 72.9757 178.09 73.2438L178.09 73.2437C174.474 73.6327 171.063 75.1166 168.313 77.4967C165.563 79.8769 163.605 83.0399 162.701 86.5628C161.798 90.0857 161.991 93.8006 163.256 97.2105C164.521 100.62 166.797 103.563 169.779 105.644L169.779 105.644C172.456 107.513 175.594 108.612 178.852 108.822C182.11 109.031 185.363 108.344 188.258 106.834C191.153 105.325 193.578 103.05 195.271 100.259C196.964 97.4669 197.858 94.2647 197.858 90.9999L197.859 91Z"
                      fill="#AEAEAE" fill-opacity="0.2" />
                    <path
                      d="M248 91L247.95 91.4975L248.5 91.5524V91H248ZM225.439 40.4105L225.773 40.0385L225.439 40.4105ZM200.894 26.2897L200.741 26.7655L200.894 26.2897ZM172.726 23.3901L172.272 23.5983L172.422 23.9258L172.78 23.8873L172.726 23.3901ZM172.726 23.39L173.181 23.1819L173.031 22.8544L172.673 22.8929L172.726 23.39ZM135.498 39.5841L135.825 39.9622L135.498 39.5841ZM114.132 74.1051L113.648 73.9808L114.132 74.1051ZM116.245 114.648L116.713 114.474L116.245 114.648ZM141.082 146.762L141.482 147.062L141.793 146.648L141.368 146.352L141.082 146.762ZM141.082 146.762L140.682 146.462L140.372 146.876L140.796 147.172L141.082 146.762ZM175.63 158.859L175.598 159.358L175.63 158.859ZM238.147 126.254L237.72 125.995H237.72L238.147 126.254ZM248 90.9999L248.05 90.5024L247.5 90.4474V90.9999H248ZM197.859 91L197.803 91.4969L198.359 91.5586V91H197.859ZM196.31 83.7262L195.854 83.9299L196.31 83.7262ZM185.487 74.0053L185.641 73.5295L185.487 74.0053ZM178.09 73.2438L177.632 73.4442L177.779 73.7802L178.143 73.741L178.09 73.2438ZM178.09 73.2437L178.548 73.0433L178.401 72.7074L178.036 72.7466L178.09 73.2437ZM162.701 86.5628L162.217 86.4386L162.701 86.5628ZM163.256 97.2105L162.787 97.3844L163.256 97.2105ZM169.779 105.644L170.179 105.944L170.49 105.53L170.065 105.234L169.779 105.644ZM169.779 105.644L169.379 105.344L169.069 105.758L169.493 106.054L169.779 105.644ZM178.852 108.822L178.82 109.321L178.852 108.822ZM188.258 106.834L188.027 106.391L188.258 106.834ZM195.271 100.259L194.843 99.9994H194.843L195.271 100.259ZM197.858 90.9999L197.914 90.503L197.358 90.4413V90.9999H197.858ZM241.647 63.5075C245.506 72.1594 247.5 81.5266 247.5 91H248.5C248.5 81.3863 246.476 71.8803 242.561 63.1002L241.647 63.5075ZM225.105 40.7824C232.153 47.1127 237.789 54.8556 241.647 63.5075L242.561 63.1002C238.645 54.3201 232.925 46.4625 225.773 40.0385L225.105 40.7824ZM200.741 26.7655C209.756 29.6763 218.057 34.4522 225.105 40.7824L225.773 40.0385C218.621 33.6144 210.197 28.7678 201.048 25.8139L200.741 26.7655ZM172.78 23.8873C182.199 22.874 191.726 23.8546 200.741 26.7655L201.048 25.8139C191.899 22.8599 182.231 21.8647 172.673 22.893L172.78 23.8873ZM172.272 23.5982L172.272 23.5983L173.181 23.182L173.181 23.1819L172.272 23.5982ZM135.825 39.9622C146.219 30.9659 159.112 25.3575 172.78 23.8871L172.673 22.8929C158.803 24.3851 145.719 30.0765 135.171 39.2061L135.825 39.9622ZM114.617 74.2293C118.032 60.9138 125.431 48.9585 135.825 39.9622L135.171 39.2061C124.623 48.3356 117.114 60.4681 113.648 73.9808L114.617 74.2293ZM116.713 114.474C111.933 101.586 111.201 87.5448 114.617 74.2293L113.648 73.9808C110.182 87.4936 110.924 101.743 115.776 114.822L116.713 114.474ZM141.368 146.352C130.096 138.484 121.494 127.363 116.713 114.474L115.776 114.822C120.627 127.901 129.356 139.188 140.796 147.172L141.368 146.352ZM140.682 146.462L140.682 146.462L141.482 147.062L141.482 147.062L140.682 146.462ZM140.796 147.172C151.065 154.339 163.102 158.554 175.598 159.358L175.663 158.36C163.348 157.567 151.487 153.414 141.368 146.352L140.796 147.172ZM175.598 159.358C188.095 160.163 200.573 157.527 211.676 151.736L211.214 150.849C200.273 156.555 187.977 159.153 175.663 158.36L175.598 159.358ZM211.676 151.736C222.779 145.945 232.083 137.222 238.575 126.513L237.72 125.995C231.322 136.547 222.155 145.143 211.214 150.849L211.676 151.736ZM238.575 126.513C245.067 115.805 248.5 103.522 248.5 90.9999H247.5C247.5 103.34 244.117 115.443 237.72 125.995L238.575 126.513ZM248.05 90.5024L248.05 90.5024L247.95 91.4975L247.95 91.4975L248.05 90.5024ZM198.359 91C198.359 88.4234 197.816 85.8757 196.767 83.5226L195.854 83.9299C196.846 86.1548 197.359 88.5638 197.359 91H198.359ZM196.767 83.5226C195.717 81.1694 194.184 79.0635 192.268 77.3418L191.599 78.0858C193.412 79.7137 194.861 81.7049 195.854 83.9299L196.767 83.5226ZM192.268 77.3418C190.351 75.6201 188.093 74.3212 185.641 73.5295L185.334 74.4811C187.652 75.2297 189.787 76.4579 191.599 78.0858L192.268 77.3418ZM185.641 73.5295C183.189 72.7378 180.598 72.4711 178.036 72.7467L178.143 73.741C180.565 73.4804 183.015 73.7326 185.334 74.4811L185.641 73.5295ZM178.548 73.0434L178.548 73.0433L177.632 73.4441L177.632 73.4442L178.548 73.0434ZM178.036 72.7466C174.319 73.1465 170.812 74.6719 167.985 77.1187L168.64 77.8748C171.313 75.5612 174.628 74.119 178.143 73.7408L178.036 72.7466ZM167.985 77.1187C165.158 79.5655 163.146 82.8171 162.217 86.4386L163.186 86.687C164.064 83.2628 165.967 80.1883 168.64 77.8748L167.985 77.1187ZM162.217 86.4386C161.288 90.0601 161.487 93.879 162.787 97.3844L163.725 97.0366C162.495 93.7221 162.307 90.1113 163.186 86.687L162.217 86.4386ZM162.787 97.3844C164.088 100.89 166.427 103.915 169.493 106.054L170.065 105.234C167.166 103.211 164.954 100.351 163.725 97.0366L162.787 97.3844ZM169.379 105.344L169.379 105.344L170.179 105.944L170.179 105.944L169.379 105.344ZM178.884 108.323C175.718 108.119 172.667 107.051 170.065 105.234L169.493 106.054C172.245 107.975 175.471 109.105 178.82 109.321L178.884 108.323ZM188.027 106.391C185.213 107.858 182.051 108.527 178.884 108.323L178.82 109.321C182.169 109.536 185.514 108.83 188.489 107.278L188.027 106.391ZM194.843 99.9994C193.198 102.713 190.841 104.924 188.027 106.391L188.489 107.278C191.465 105.726 193.959 103.388 195.698 100.518L194.843 99.9994ZM197.358 90.9999C197.358 94.1733 196.489 97.2859 194.843 99.9994L195.698 100.518C197.438 97.648 198.358 94.3561 198.358 90.9999H197.358ZM197.914 90.503L197.914 90.503L197.803 91.4969L197.803 91.4969L197.914 90.503Z"
                      fill="white" />
                  </svg>
                  <div *ngIf="this.doughnutChartData.datasets[0].data.length > 0" class="chart-container" style="position: relative;">
                    <canvas baseChart class="doughnutChart" [data]="doughnutChartData" 
                      [type]="doughnutChartType" [options]="doughnutChartOptions">
                    </canvas>
                  </div>
                </div>
              </div>

            </div>
          </div>
          <div *ngIf="this.applyBtn || this.applyBtn1" class="col-7" style="text-align: center;padding-top:30px;">
            <app-loader *ngIf="this.applyBtn || this.applyBtn1" [spinnerStyle]="true"></app-loader>
          </div>
        </div>
      </div>
      <div *ngIf="this.grandTotalSpend === 0 && this.grandPreviousTotalSpend === 0" class="top-strip"
        style="padding-top: 0px;margin-top: 30px;">
        <ul>
          <li (click)="getselectedbookingValue('all')">
            <svg width="24" height="24" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M25 13C25 11.3159 24.6455 9.65057 23.9596 8.11245C23.2736 6.57432 22.2716 5.19781 21.0187 4.07243C19.7658 2.94706 18.29 2.09801 16.6873 1.58053C15.0847 1.06304 13.391 0.888703 11.7166 1.06885L12.663 9.86656C13.1028 9.81925 13.5476 9.86504 13.9685 10.0009C14.3894 10.1368 14.7769 10.3598 15.106 10.6554C15.4351 10.9509 15.6982 11.3124 15.8784 11.7164C16.0585 12.1203 16.1516 12.5577 16.1516 13H25Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#AEAEAE'}" stroke="white" />
              <path
                d="M11.7163 1.06885C9.28655 1.33025 6.99449 2.32729 5.14669 3.92663C3.29889 5.52597 1.98344 7.65136 1.37627 10.0186C0.769099 12.3858 0.89915 14.8819 1.74903 17.1732C2.59891 19.4645 4.1281 21.4417 6.1321 22.8403L11.1962 15.5843C10.6699 15.217 10.2683 14.6977 10.0451 14.096C9.82192 13.4942 9.78777 12.8387 9.94723 12.217C10.1067 11.5953 10.4522 11.0371 10.9374 10.6171C11.4227 10.1971 12.0247 9.93521 12.6628 9.86656L11.7163 1.06885Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#AEAEAE'}" stroke="white" />
              <path
                d="M6.13208 22.8403C7.93098 24.0959 10.0396 24.8342 12.2288 24.9752C14.418 25.1162 16.6039 24.6543 18.549 23.6399C20.494 22.6254 22.1238 21.0972 23.2611 19.2213C24.3984 17.3455 24.9998 15.1937 24.9998 13H16.1514C16.1514 13.5761 15.9934 14.1412 15.6948 14.6339C15.3961 15.1265 14.968 15.5279 14.4572 15.7943C13.9464 16.0607 13.3723 16.182 12.7974 16.145C12.2224 16.108 11.6687 15.9141 11.1962 15.5843L6.13208 22.8403Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#AEAEAE'}" stroke="white" />
            </svg>
            <span class="{{this.selectBookingValue ==='all' ? 'select':'unselect'}}"> {{'dashboard.Total' | translate }}
              <div *ngIf="this.selectBookingValue ==='all'" class="underline"></div></span>
          </li>
          <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
          </svg>
          <li (click)="getselectedbookingValue('flight')">
            <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M20.4999 7H5.53491L3.83191 4.445C3.64591 4.167 3.33391 4 2.99991 4H0.999911C0.691911 4 0.400911 4.142 0.211911 4.385C0.022911 4.628 -0.045089 4.944 0.029911 5.243L2.02991 13.243C2.14191 13.688 2.54091 14 2.99991 14H11.1319L8.16791 18.445C7.96391 18.752 7.94391 19.146 8.11791 19.471C8.29191 19.796 8.63091 20 8.99991 20H12.9999C13.3339 20 13.6459 19.833 13.8319 19.555L17.5349 14H20.4999C22.4299 14 23.9999 12.43 23.9999 10.5C23.9999 8.57 22.4299 7 20.4999 7Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='flight' ?'#F3A536':'#AEAEAE'}" />
              <path
                d="M10.2719 5H16.6169L13.8529 0.479C13.6719 0.181 13.3489 0 12.9999 0H8.99992C8.63892 0 8.30492 0.195 8.12792 0.511C7.95092 0.827 7.95792 1.213 8.14692 1.522L10.2719 5Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='flight' ?'#F3A536':'#AEAEAE'}" />
            </svg>
            <span class="{{this.selectBookingValue ==='flight' ? 'select':'unselect'}}"> {{'dashboard.Flights' |
              translate }} <div *ngIf="this.selectBookingValue ==='flight'" class="underline"></div></span>
          </li>
          <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
          </svg>
          <li (click)="getselectedbookingValue('hotel')">
            <svg width="15" height="24" viewBox="0 0 15 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M0.609375 17.625V23.25C0.609375 23.3495 0.643945 23.4448 0.705481 23.5152C0.767016 23.5855 0.850476 23.625 0.9375 23.625H6.1875V19.5C6.1875 19.4005 6.22207 19.3052 6.28361 19.2348C6.34514 19.1645 6.4286 19.125 6.51562 19.125H8.48438C8.5714 19.125 8.65486 19.1645 8.71639 19.2348C8.77793 19.3052 8.8125 19.4005 8.8125 19.5V23.625H14.0625C14.1495 23.625 14.233 23.5855 14.2945 23.5152C14.3561 23.4448 14.3906 23.3495 14.3906 23.25V17.625H0.609375Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='hotel' ?'#F3A536':'#AEAEAE'}" />
              <path
                d="M14.1419 2.26126L7.57941 0.38626C7.52732 0.370891 7.47268 0.370891 7.42059 0.38626L0.858094 2.26126C0.787077 2.2815 0.724021 2.3283 0.678954 2.39421C0.633886 2.46012 0.609395 2.54136 0.609375 2.62501V16.875H14.3906V2.62501C14.3906 2.54136 14.3661 2.46012 14.321 2.39421C14.276 2.3283 14.2129 2.2815 14.1419 2.26126ZM3.89062 15C3.89062 15.0995 3.85606 15.1948 3.79452 15.2652C3.73298 15.3355 3.64952 15.375 3.5625 15.375H2.57812C2.4911 15.375 2.40764 15.3355 2.34611 15.2652C2.28457 15.1948 2.25 15.0995 2.25 15V13.125C2.25 13.0256 2.28457 12.9302 2.34611 12.8598C2.40764 12.7895 2.4911 12.75 2.57812 12.75H3.5625C3.64952 12.75 3.73298 12.7895 3.79452 12.8598C3.85606 12.9302 3.89062 13.0256 3.89062 13.125V15ZM3.89062 10.875C3.89062 10.9745 3.85606 11.0698 3.79452 11.1402C3.73298 11.2105 3.64952 11.25 3.5625 11.25H2.57812C2.4911 11.25 2.40764 11.2105 2.34611 11.1402C2.28457 11.0698 2.25 10.9745 2.25 10.875V9.00001C2.25 8.90055 2.28457 8.80517 2.34611 8.73484C2.40764 8.66452 2.4911 8.62501 2.57812 8.62501H3.5625C3.64952 8.62501 3.73298 8.66452 3.79452 8.73484C3.85606 8.80517 3.89062 8.90055 3.89062 9.00001V10.875ZM3.89062 6.75001C3.89062 6.84947 3.85606 6.94485 3.79452 7.01517C3.73298 7.0855 3.64952 7.12501 3.5625 7.12501H2.57812C2.4911 7.12501 2.40764 7.0855 2.34611 7.01517C2.28457 6.94485 2.25 6.84947 2.25 6.75001V4.87501C2.25 4.77555 2.28457 4.68017 2.34611 4.60984C2.40764 4.53952 2.4911 4.50001 2.57812 4.50001H3.5625C3.64952 4.50001 3.73298 4.53952 3.79452 4.60984C3.85606 4.68017 3.89062 4.77555 3.89062 4.87501V6.75001ZM6.84375 15C6.84375 15.0995 6.80918 15.1948 6.74764 15.2652C6.68611 15.3355 6.60265 15.375 6.51562 15.375H5.53125C5.44423 15.375 5.36077 15.3355 5.29923 15.2652C5.23769 15.1948 5.20312 15.0995 5.20312 15V13.125C5.20312 13.0256 5.23769 12.9302 5.29923 12.8598C5.36077 12.7895 5.44423 12.75 5.53125 12.75H6.51562C6.60265 12.75 6.68611 12.7895 6.74764 12.8598C6.80918 12.9302 6.84375 13.0256 6.84375 13.125V15ZM6.84375 10.875C6.84375 10.9745 6.80918 11.0698 6.74764 11.1402C6.68611 11.2105 6.60265 11.25 6.51562 11.25H5.53125C5.44423 11.25 5.36077 11.2105 5.29923 11.1402C5.23769 11.0698 5.20312 10.9745 5.20312 10.875V9.00001C5.20312 8.90055 5.23769 8.80517 5.29923 8.73484C5.36077 8.66452 5.44423 8.62501 5.53125 8.62501H6.51562C6.60265 8.62501 6.68611 8.66452 6.74764 8.73484C6.80918 8.80517 6.84375 8.90055 6.84375 9.00001V10.875ZM6.84375 6.75001C6.84375 6.84947 6.80918 6.94485 6.74764 7.01517C6.68611 7.0855 6.60265 7.12501 6.51562 7.12501H5.53125C5.44423 7.12501 5.36077 7.0855 5.29923 7.01517C5.23769 6.94485 5.20312 6.84947 5.20312 6.75001V4.87501C5.20312 4.77555 5.23769 4.68017 5.29923 4.60984C5.36077 4.53952 5.44423 4.50001 5.53125 4.50001H6.51562C6.60265 4.50001 6.68611 4.53952 6.74764 4.60984C6.80918 4.68017 6.84375 4.77555 6.84375 4.87501V6.75001ZM9.79688 15C9.79688 15.0995 9.76231 15.1948 9.70077 15.2652C9.63923 15.3355 9.55577 15.375 9.46875 15.375H8.48438C8.39735 15.375 8.31389 15.3355 8.25236 15.2652C8.19082 15.1948 8.15625 15.0995 8.15625 15V13.125C8.15625 13.0256 8.19082 12.9302 8.25236 12.8598C8.31389 12.7895 8.39735 12.75 8.48438 12.75H9.46875C9.55577 12.75 9.63923 12.7895 9.70077 12.8598C9.76231 12.9302 9.79688 13.0256 9.79688 13.125V15ZM9.79688 10.875C9.79688 10.9745 9.76231 11.0698 9.70077 11.1402C9.63923 11.2105 9.55577 11.25 9.46875 11.25H8.48438C8.39735 11.25 8.31389 11.2105 8.25236 11.1402C8.19082 11.0698 8.15625 10.9745 8.15625 10.875V9.00001C8.15625 8.90055 8.19082 8.80517 8.25236 8.73484C8.31389 8.66452 8.39735 8.62501 8.48438 8.62501H9.46875C9.55577 8.62501 9.63923 8.66452 9.70077 8.73484C9.76231 8.80517 9.79688 8.90055 9.79688 9.00001V10.875ZM9.79688 6.75001C9.79688 6.84947 9.76231 6.94485 9.70077 7.01517C9.63923 7.0855 9.55577 7.12501 9.46875 7.12501H8.48438C8.39735 7.12501 8.31389 7.0855 8.25236 7.01517C8.19082 6.94485 8.15625 6.84947 8.15625 6.75001V4.87501C8.15625 4.77555 8.19082 4.68017 8.25236 4.60984C8.31389 4.53952 8.39735 4.50001 8.48438 4.50001H9.46875C9.55577 4.50001 9.63923 4.53952 9.70077 4.60984C9.76231 4.68017 9.79688 4.77555 9.79688 4.87501V6.75001ZM12.75 15C12.75 15.0995 12.7154 15.1948 12.6539 15.2652C12.5924 15.3355 12.5089 15.375 12.4219 15.375H11.4375C11.3505 15.375 11.267 15.3355 11.2055 15.2652C11.1439 15.1948 11.1094 15.0995 11.1094 15V13.125C11.1094 13.0256 11.1439 12.9302 11.2055 12.8598C11.267 12.7895 11.3505 12.75 11.4375 12.75H12.4219C12.5089 12.75 12.5924 12.7895 12.6539 12.8598C12.7154 12.9302 12.75 13.0256 12.75 13.125V15ZM12.75 10.875C12.75 10.9745 12.7154 11.0698 12.6539 11.1402C12.5924 11.2105 12.5089 11.25 12.4219 11.25H11.4375C11.3505 11.25 11.267 11.2105 11.2055 11.1402C11.1439 11.0698 11.1094 10.9745 11.1094 10.875V9.00001C11.1094 8.90055 11.1439 8.80517 11.2055 8.73484C11.267 8.66452 11.3505 8.62501 11.4375 8.62501H12.4219C12.5089 8.62501 12.5924 8.66452 12.6539 8.73484C12.7154 8.80517 12.75 8.90055 12.75 9.00001V10.875ZM12.75 6.75001C12.75 6.84947 12.7154 6.94485 12.6539 7.01517C12.5924 7.0855 12.5089 7.12501 12.4219 7.12501H11.4375C11.3505 7.12501 11.267 7.0855 11.2055 7.01517C11.1439 6.94485 11.1094 6.84947 11.1094 6.75001V4.87501C11.1094 4.77555 11.1439 4.68017 11.2055 4.60984C11.267 4.53952 11.3505 4.50001 11.4375 4.50001H12.4219C12.5089 4.50001 12.5924 4.53952 12.6539 4.60984C12.7154 4.68017 12.75 4.77555 12.75 4.87501V6.75001Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='hotel' ?'#F3A536':'#AEAEAE'}" />
            </svg>
            <span class="{{this.selectBookingValue ==='hotel' ? 'select':'unselect'}}"> {{'dashboard.Hotels' | translate
              }}<div *ngIf="this.selectBookingValue ==='hotel'" class="underline"></div></span>
          </li>
          <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
          </svg>
          <li (click)="getselectedbookingValue('car')">
            <svg width="37" height="19" viewBox="0 0 37 19" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M0 14.2188V14.7969C0 14.9502 0.0609094 15.0973 0.169329 15.2057C0.277748 15.3141 0.424797 15.375 0.578125 15.375H4.04688C4.04688 14.7804 4.16213 14.1914 4.38623 13.6406H0.578125C0.424797 13.6406 0.277748 13.7015 0.169329 13.81C0.0609094 13.9184 0 14.0654 0 14.2188H0Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#AEAEAE'}" />
              <path
                d="M8.67188 18.8437C7.98582 18.8437 7.31518 18.6403 6.74474 18.2592C6.17431 17.878 5.72971 17.3363 5.46717 16.7024C5.20463 16.0686 5.13594 15.3712 5.26978 14.6983C5.40362 14.0254 5.73399 13.4073 6.2191 12.9222C6.70421 12.4371 7.32229 12.1067 7.99516 11.9729C8.66803 11.8391 9.36548 11.9078 9.99931 12.1703C10.6331 12.4328 11.1749 12.8774 11.556 13.4479C11.9372 14.0183 12.1406 14.6889 12.1406 15.375C12.1397 16.2947 11.774 17.1764 11.1236 17.8268C10.4733 18.4771 9.59156 18.8428 8.67188 18.8437ZM8.67188 13.0625C8.21451 13.0625 7.76741 13.1981 7.38712 13.4522C7.00683 13.7063 6.71043 14.0675 6.5354 14.49C6.36038 14.9126 6.31458 15.3776 6.40381 15.8261C6.49304 16.2747 6.71328 16.6868 7.03669 17.0102C7.3601 17.3336 7.77215 17.5538 8.22073 17.6431C8.66931 17.7323 9.13428 17.6865 9.55683 17.5115C9.97939 17.3364 10.3405 17.04 10.5946 16.6598C10.8488 16.2795 10.9844 15.8324 10.9844 15.375C10.9844 14.7617 10.7407 14.1735 10.3071 13.7398C9.87338 13.3061 9.28519 13.0625 8.67188 13.0625Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#AEAEAE'}" />
              <path
                d="M29.4844 18.8437C28.7983 18.8437 28.1277 18.6403 27.5572 18.2592C26.9868 17.878 26.5422 17.3363 26.2797 16.7024C26.0171 16.0686 25.9484 15.3712 26.0823 14.6983C26.2161 14.0254 26.5465 13.4073 27.0316 12.9222C27.5167 12.4371 28.1348 12.1067 28.8077 11.9729C29.4805 11.8391 30.178 11.9078 30.8118 12.1703C31.4456 12.4328 31.9874 12.8774 32.3685 13.4479C32.7497 14.0183 32.9531 14.6889 32.9531 15.375C32.9522 16.2947 32.5865 17.1764 31.9361 17.8268C31.2858 18.4771 30.4041 18.8428 29.4844 18.8437ZM29.4844 13.0625C29.027 13.0625 28.5799 13.1981 28.1996 13.4522C27.8193 13.7063 27.5229 14.0675 27.3479 14.49C27.1729 14.9126 27.1271 15.3776 27.2163 15.8261C27.3055 16.2747 27.5258 16.6868 27.8492 17.0102C28.1726 17.3336 28.5847 17.5538 29.0332 17.6431C29.4818 17.7323 29.9468 17.6865 30.3693 17.5115C30.7919 17.3364 31.1531 17.04 31.4072 16.6598C31.6613 16.2795 31.7969 15.8324 31.7969 15.375C31.7969 14.7617 31.5532 14.1735 31.1196 13.7398C30.6859 13.3061 30.0977 13.0625 29.4844 13.0625Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#AEAEAE'}" />
              <path
                d="M36.4219 13.6406H33.77C33.9941 14.1914 34.1094 14.7804 34.1094 15.375H36.4219C36.5752 15.375 36.7223 15.3141 36.8307 15.2057C36.9391 15.0973 37 14.9502 37 14.7969V14.2188C37 14.0654 36.9391 13.9184 36.8307 13.81C36.7223 13.7015 36.5752 13.6406 36.4219 13.6406Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#AEAEAE'}" />
              <path
                d="M35.5079 9.51108C35.4171 8.88792 35.126 8.31123 34.6786 7.86808C34.2312 7.42494 33.6517 7.13941 33.0277 7.05463L26.9984 6.25045L23.6222 0.624144C23.5418 0.495701 23.4142 0.403946 23.2669 0.368627C23.1195 0.333307 22.9642 0.357246 22.8343 0.435292C22.7045 0.513338 22.6104 0.639251 22.5724 0.785936C22.5345 0.932621 22.5556 1.08835 22.6313 1.21961L25.5728 6.125H15.0312C14.4179 6.125 13.8297 5.88136 13.3961 5.44769C12.9624 5.01401 12.7188 4.42582 12.7188 3.8125V2.94532C12.7188 2.56199 12.5665 2.19437 12.2954 1.92332C12.0244 1.65228 11.6568 1.5 11.2734 1.5C10.8901 1.5 10.5225 1.65228 10.2514 1.92332C9.9804 2.19437 9.82812 2.56199 9.82812 2.94532V4.98032C9.80094 4.97638 9.7735 4.97444 9.74603 4.97453L3.63814 5.8475C2.94928 5.9459 2.31903 6.28948 1.86312 6.81517C1.4072 7.34086 1.15622 8.01337 1.15625 8.70922V12.7289H4.88169C5.44529 11.923 6.25113 11.3177 7.18218 11.0009C8.11323 10.6841 9.12102 10.6724 10.0592 10.9674C10.9974 11.2624 11.8171 11.8488 12.3993 12.6414C12.9815 13.434 13.2959 14.3915 13.2969 15.375H24.8594C24.8601 14.4175 25.158 13.4838 25.712 12.7028C26.266 11.9218 27.0487 11.332 27.9521 11.0148C28.8556 10.6976 29.8352 10.6686 30.7558 10.9318C31.6765 11.195 32.4927 11.7375 33.0919 12.4844H35.6975L35.5079 9.51108Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#AEAEAE'}" />
            </svg>
            <span class="{{this.selectBookingValue ==='car' ? 'select':'unselect'}}"> {{'dashboard.Cars' | translate }}
              <div *ngIf="this.selectBookingValue ==='car'" class="underline"></div></span>
          </li>
        </ul>
      </div>
      <div
        *ngIf="(this.grandTotalSpend > 0 || this.grandPreviousTotalSpend > 0 )|| (this.grandTotalSpend < 0 || this.grandPreviousTotalSpend < 0)"
        class="top-strip" style="padding-top: 0px;margin-top: 30px;">
        <ul>
          <li (click)="getselectedbookingValue('all')">
            <svg width="24" height="24" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M25 13C25 11.3159 24.6455 9.65057 23.9596 8.11245C23.2736 6.57432 22.2716 5.19781 21.0187 4.07243C19.7658 2.94706 18.29 2.09801 16.6873 1.58053C15.0847 1.06304 13.391 0.888703 11.7166 1.06885L12.663 9.86656C13.1028 9.81925 13.5476 9.86504 13.9685 10.0009C14.3894 10.1368 14.7769 10.3598 15.106 10.6554C15.4351 10.9509 15.6982 11.3124 15.8784 11.7164C16.0585 12.1203 16.1516 12.5577 16.1516 13H25Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#47494F'}" stroke="white" />
              <path
                d="M11.7163 1.06885C9.28655 1.33025 6.99449 2.32729 5.14669 3.92663C3.29889 5.52597 1.98344 7.65136 1.37627 10.0186C0.769099 12.3858 0.89915 14.8819 1.74903 17.1732C2.59891 19.4645 4.1281 21.4417 6.1321 22.8403L11.1962 15.5843C10.6699 15.217 10.2683 14.6977 10.0451 14.096C9.82192 13.4942 9.78777 12.8387 9.94723 12.217C10.1067 11.5953 10.4522 11.0371 10.9374 10.6171C11.4227 10.1971 12.0247 9.93521 12.6628 9.86656L11.7163 1.06885Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#47494F'}" stroke="white" />
              <path
                d="M6.13208 22.8403C7.93098 24.0959 10.0396 24.8342 12.2288 24.9752C14.418 25.1162 16.6039 24.6543 18.549 23.6399C20.494 22.6254 22.1238 21.0972 23.2611 19.2213C24.3984 17.3455 24.9998 15.1937 24.9998 13H16.1514C16.1514 13.5761 15.9934 14.1412 15.6948 14.6339C15.3961 15.1265 14.968 15.5279 14.4572 15.7943C13.9464 16.0607 13.3723 16.182 12.7974 16.145C12.2224 16.108 11.6687 15.9141 11.1962 15.5843L6.13208 22.8403Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#47494F'}" stroke="white" />
            </svg>
            <span class="{{this.selectBookingValue ==='all' ? 'select':'unselect'}}"> {{'dashboard.Total' | translate }}
              <div *ngIf="this.selectBookingValue ==='all'" class="underline"></div></span>
          </li>
          <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
          </svg>
          <li (click)="getselectedbookingValue('flight')">
            <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M20.4999 7H5.53491L3.83191 4.445C3.64591 4.167 3.33391 4 2.99991 4H0.999911C0.691911 4 0.400911 4.142 0.211911 4.385C0.022911 4.628 -0.045089 4.944 0.029911 5.243L2.02991 13.243C2.14191 13.688 2.54091 14 2.99991 14H11.1319L8.16791 18.445C7.96391 18.752 7.94391 19.146 8.11791 19.471C8.29191 19.796 8.63091 20 8.99991 20H12.9999C13.3339 20 13.6459 19.833 13.8319 19.555L17.5349 14H20.4999C22.4299 14 23.9999 12.43 23.9999 10.5C23.9999 8.57 22.4299 7 20.4999 7Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='flight' ?'#F3A536':'#47494F'}" />
              <path
                d="M10.2719 5H16.6169L13.8529 0.479C13.6719 0.181 13.3489 0 12.9999 0H8.99992C8.63892 0 8.30492 0.195 8.12792 0.511C7.95092 0.827 7.95792 1.213 8.14692 1.522L10.2719 5Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='flight' ?'#F3A536':'#47494F'}" />
            </svg>
            <span class="{{this.selectBookingValue ==='flight' ? 'select':'unselect'}}"> {{'dashboard.Flights' |
              translate }}<div *ngIf="this.selectBookingValue ==='flight'" class="underline"></div></span>
          </li>
          <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
          </svg>
          <li (click)="getselectedbookingValue('hotel')">
            <svg width="15" height="24" viewBox="0 0 15 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M0.609375 17.625V23.25C0.609375 23.3495 0.643945 23.4448 0.705481 23.5152C0.767016 23.5855 0.850476 23.625 0.9375 23.625H6.1875V19.5C6.1875 19.4005 6.22207 19.3052 6.28361 19.2348C6.34514 19.1645 6.4286 19.125 6.51562 19.125H8.48438C8.5714 19.125 8.65486 19.1645 8.71639 19.2348C8.77793 19.3052 8.8125 19.4005 8.8125 19.5V23.625H14.0625C14.1495 23.625 14.233 23.5855 14.2945 23.5152C14.3561 23.4448 14.3906 23.3495 14.3906 23.25V17.625H0.609375Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='hotel' ?'#F3A536':'#47494F'}" />
              <path
                d="M14.1419 2.26126L7.57941 0.38626C7.52732 0.370891 7.47268 0.370891 7.42059 0.38626L0.858094 2.26126C0.787077 2.2815 0.724021 2.3283 0.678954 2.39421C0.633886 2.46012 0.609395 2.54136 0.609375 2.62501V16.875H14.3906V2.62501C14.3906 2.54136 14.3661 2.46012 14.321 2.39421C14.276 2.3283 14.2129 2.2815 14.1419 2.26126ZM3.89062 15C3.89062 15.0995 3.85606 15.1948 3.79452 15.2652C3.73298 15.3355 3.64952 15.375 3.5625 15.375H2.57812C2.4911 15.375 2.40764 15.3355 2.34611 15.2652C2.28457 15.1948 2.25 15.0995 2.25 15V13.125C2.25 13.0256 2.28457 12.9302 2.34611 12.8598C2.40764 12.7895 2.4911 12.75 2.57812 12.75H3.5625C3.64952 12.75 3.73298 12.7895 3.79452 12.8598C3.85606 12.9302 3.89062 13.0256 3.89062 13.125V15ZM3.89062 10.875C3.89062 10.9745 3.85606 11.0698 3.79452 11.1402C3.73298 11.2105 3.64952 11.25 3.5625 11.25H2.57812C2.4911 11.25 2.40764 11.2105 2.34611 11.1402C2.28457 11.0698 2.25 10.9745 2.25 10.875V9.00001C2.25 8.90055 2.28457 8.80517 2.34611 8.73484C2.40764 8.66452 2.4911 8.62501 2.57812 8.62501H3.5625C3.64952 8.62501 3.73298 8.66452 3.79452 8.73484C3.85606 8.80517 3.89062 8.90055 3.89062 9.00001V10.875ZM3.89062 6.75001C3.89062 6.84947 3.85606 6.94485 3.79452 7.01517C3.73298 7.0855 3.64952 7.12501 3.5625 7.12501H2.57812C2.4911 7.12501 2.40764 7.0855 2.34611 7.01517C2.28457 6.94485 2.25 6.84947 2.25 6.75001V4.87501C2.25 4.77555 2.28457 4.68017 2.34611 4.60984C2.40764 4.53952 2.4911 4.50001 2.57812 4.50001H3.5625C3.64952 4.50001 3.73298 4.53952 3.79452 4.60984C3.85606 4.68017 3.89062 4.77555 3.89062 4.87501V6.75001ZM6.84375 15C6.84375 15.0995 6.80918 15.1948 6.74764 15.2652C6.68611 15.3355 6.60265 15.375 6.51562 15.375H5.53125C5.44423 15.375 5.36077 15.3355 5.29923 15.2652C5.23769 15.1948 5.20312 15.0995 5.20312 15V13.125C5.20312 13.0256 5.23769 12.9302 5.29923 12.8598C5.36077 12.7895 5.44423 12.75 5.53125 12.75H6.51562C6.60265 12.75 6.68611 12.7895 6.74764 12.8598C6.80918 12.9302 6.84375 13.0256 6.84375 13.125V15ZM6.84375 10.875C6.84375 10.9745 6.80918 11.0698 6.74764 11.1402C6.68611 11.2105 6.60265 11.25 6.51562 11.25H5.53125C5.44423 11.25 5.36077 11.2105 5.29923 11.1402C5.23769 11.0698 5.20312 10.9745 5.20312 10.875V9.00001C5.20312 8.90055 5.23769 8.80517 5.29923 8.73484C5.36077 8.66452 5.44423 8.62501 5.53125 8.62501H6.51562C6.60265 8.62501 6.68611 8.66452 6.74764 8.73484C6.80918 8.80517 6.84375 8.90055 6.84375 9.00001V10.875ZM6.84375 6.75001C6.84375 6.84947 6.80918 6.94485 6.74764 7.01517C6.68611 7.0855 6.60265 7.12501 6.51562 7.12501H5.53125C5.44423 7.12501 5.36077 7.0855 5.29923 7.01517C5.23769 6.94485 5.20312 6.84947 5.20312 6.75001V4.87501C5.20312 4.77555 5.23769 4.68017 5.29923 4.60984C5.36077 4.53952 5.44423 4.50001 5.53125 4.50001H6.51562C6.60265 4.50001 6.68611 4.53952 6.74764 4.60984C6.80918 4.68017 6.84375 4.77555 6.84375 4.87501V6.75001ZM9.79688 15C9.79688 15.0995 9.76231 15.1948 9.70077 15.2652C9.63923 15.3355 9.55577 15.375 9.46875 15.375H8.48438C8.39735 15.375 8.31389 15.3355 8.25236 15.2652C8.19082 15.1948 8.15625 15.0995 8.15625 15V13.125C8.15625 13.0256 8.19082 12.9302 8.25236 12.8598C8.31389 12.7895 8.39735 12.75 8.48438 12.75H9.46875C9.55577 12.75 9.63923 12.7895 9.70077 12.8598C9.76231 12.9302 9.79688 13.0256 9.79688 13.125V15ZM9.79688 10.875C9.79688 10.9745 9.76231 11.0698 9.70077 11.1402C9.63923 11.2105 9.55577 11.25 9.46875 11.25H8.48438C8.39735 11.25 8.31389 11.2105 8.25236 11.1402C8.19082 11.0698 8.15625 10.9745 8.15625 10.875V9.00001C8.15625 8.90055 8.19082 8.80517 8.25236 8.73484C8.31389 8.66452 8.39735 8.62501 8.48438 8.62501H9.46875C9.55577 8.62501 9.63923 8.66452 9.70077 8.73484C9.76231 8.80517 9.79688 8.90055 9.79688 9.00001V10.875ZM9.79688 6.75001C9.79688 6.84947 9.76231 6.94485 9.70077 7.01517C9.63923 7.0855 9.55577 7.12501 9.46875 7.12501H8.48438C8.39735 7.12501 8.31389 7.0855 8.25236 7.01517C8.19082 6.94485 8.15625 6.84947 8.15625 6.75001V4.87501C8.15625 4.77555 8.19082 4.68017 8.25236 4.60984C8.31389 4.53952 8.39735 4.50001 8.48438 4.50001H9.46875C9.55577 4.50001 9.63923 4.53952 9.70077 4.60984C9.76231 4.68017 9.79688 4.77555 9.79688 4.87501V6.75001ZM12.75 15C12.75 15.0995 12.7154 15.1948 12.6539 15.2652C12.5924 15.3355 12.5089 15.375 12.4219 15.375H11.4375C11.3505 15.375 11.267 15.3355 11.2055 15.2652C11.1439 15.1948 11.1094 15.0995 11.1094 15V13.125C11.1094 13.0256 11.1439 12.9302 11.2055 12.8598C11.267 12.7895 11.3505 12.75 11.4375 12.75H12.4219C12.5089 12.75 12.5924 12.7895 12.6539 12.8598C12.7154 12.9302 12.75 13.0256 12.75 13.125V15ZM12.75 10.875C12.75 10.9745 12.7154 11.0698 12.6539 11.1402C12.5924 11.2105 12.5089 11.25 12.4219 11.25H11.4375C11.3505 11.25 11.267 11.2105 11.2055 11.1402C11.1439 11.0698 11.1094 10.9745 11.1094 10.875V9.00001C11.1094 8.90055 11.1439 8.80517 11.2055 8.73484C11.267 8.66452 11.3505 8.62501 11.4375 8.62501H12.4219C12.5089 8.62501 12.5924 8.66452 12.6539 8.73484C12.7154 8.80517 12.75 8.90055 12.75 9.00001V10.875ZM12.75 6.75001C12.75 6.84947 12.7154 6.94485 12.6539 7.01517C12.5924 7.0855 12.5089 7.12501 12.4219 7.12501H11.4375C11.3505 7.12501 11.267 7.0855 11.2055 7.01517C11.1439 6.94485 11.1094 6.84947 11.1094 6.75001V4.87501C11.1094 4.77555 11.1439 4.68017 11.2055 4.60984C11.267 4.53952 11.3505 4.50001 11.4375 4.50001H12.4219C12.5089 4.50001 12.5924 4.53952 12.6539 4.60984C12.7154 4.68017 12.75 4.77555 12.75 4.87501V6.75001Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='hotel' ?'#F3A536':'#47494F'}" />
            </svg>
            <span class="{{this.selectBookingValue ==='hotel' ? 'select':'unselect'}}">{{'dashboard.Hotels' | translate
              }} <div *ngIf="this.selectBookingValue ==='hotel'" class="underline"></div></span>
          </li>
          <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
          </svg>
          <li (click)="getselectedbookingValue('car')">
            <svg width="37" height="19" viewBox="0 0 37 19" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M0 14.2188V14.7969C0 14.9502 0.0609094 15.0973 0.169329 15.2057C0.277748 15.3141 0.424797 15.375 0.578125 15.375H4.04688C4.04688 14.7804 4.16213 14.1914 4.38623 13.6406H0.578125C0.424797 13.6406 0.277748 13.7015 0.169329 13.81C0.0609094 13.9184 0 14.0654 0 14.2188H0Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#47494F'}" />
              <path
                d="M8.67188 18.8437C7.98582 18.8437 7.31518 18.6403 6.74474 18.2592C6.17431 17.878 5.72971 17.3363 5.46717 16.7024C5.20463 16.0686 5.13594 15.3712 5.26978 14.6983C5.40362 14.0254 5.73399 13.4073 6.2191 12.9222C6.70421 12.4371 7.32229 12.1067 7.99516 11.9729C8.66803 11.8391 9.36548 11.9078 9.99931 12.1703C10.6331 12.4328 11.1749 12.8774 11.556 13.4479C11.9372 14.0183 12.1406 14.6889 12.1406 15.375C12.1397 16.2947 11.774 17.1764 11.1236 17.8268C10.4733 18.4771 9.59156 18.8428 8.67188 18.8437ZM8.67188 13.0625C8.21451 13.0625 7.76741 13.1981 7.38712 13.4522C7.00683 13.7063 6.71043 14.0675 6.5354 14.49C6.36038 14.9126 6.31458 15.3776 6.40381 15.8261C6.49304 16.2747 6.71328 16.6868 7.03669 17.0102C7.3601 17.3336 7.77215 17.5538 8.22073 17.6431C8.66931 17.7323 9.13428 17.6865 9.55683 17.5115C9.97939 17.3364 10.3405 17.04 10.5946 16.6598C10.8488 16.2795 10.9844 15.8324 10.9844 15.375C10.9844 14.7617 10.7407 14.1735 10.3071 13.7398C9.87338 13.3061 9.28519 13.0625 8.67188 13.0625Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#47494F'}" />
              <path
                d="M29.4844 18.8437C28.7983 18.8437 28.1277 18.6403 27.5572 18.2592C26.9868 17.878 26.5422 17.3363 26.2797 16.7024C26.0171 16.0686 25.9484 15.3712 26.0823 14.6983C26.2161 14.0254 26.5465 13.4073 27.0316 12.9222C27.5167 12.4371 28.1348 12.1067 28.8077 11.9729C29.4805 11.8391 30.178 11.9078 30.8118 12.1703C31.4456 12.4328 31.9874 12.8774 32.3685 13.4479C32.7497 14.0183 32.9531 14.6889 32.9531 15.375C32.9522 16.2947 32.5865 17.1764 31.9361 17.8268C31.2858 18.4771 30.4041 18.8428 29.4844 18.8437ZM29.4844 13.0625C29.027 13.0625 28.5799 13.1981 28.1996 13.4522C27.8193 13.7063 27.5229 14.0675 27.3479 14.49C27.1729 14.9126 27.1271 15.3776 27.2163 15.8261C27.3055 16.2747 27.5258 16.6868 27.8492 17.0102C28.1726 17.3336 28.5847 17.5538 29.0332 17.6431C29.4818 17.7323 29.9468 17.6865 30.3693 17.5115C30.7919 17.3364 31.1531 17.04 31.4072 16.6598C31.6613 16.2795 31.7969 15.8324 31.7969 15.375C31.7969 14.7617 31.5532 14.1735 31.1196 13.7398C30.6859 13.3061 30.0977 13.0625 29.4844 13.0625Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#47494F'}" />
              <path
                d="M36.4219 13.6406H33.77C33.9941 14.1914 34.1094 14.7804 34.1094 15.375H36.4219C36.5752 15.375 36.7223 15.3141 36.8307 15.2057C36.9391 15.0973 37 14.9502 37 14.7969V14.2188C37 14.0654 36.9391 13.9184 36.8307 13.81C36.7223 13.7015 36.5752 13.6406 36.4219 13.6406Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#47494F'}" />
              <path
                d="M35.5079 9.51108C35.4171 8.88792 35.126 8.31123 34.6786 7.86808C34.2312 7.42494 33.6517 7.13941 33.0277 7.05463L26.9984 6.25045L23.6222 0.624144C23.5418 0.495701 23.4142 0.403946 23.2669 0.368627C23.1195 0.333307 22.9642 0.357246 22.8343 0.435292C22.7045 0.513338 22.6104 0.639251 22.5724 0.785936C22.5345 0.932621 22.5556 1.08835 22.6313 1.21961L25.5728 6.125H15.0312C14.4179 6.125 13.8297 5.88136 13.3961 5.44769C12.9624 5.01401 12.7188 4.42582 12.7188 3.8125V2.94532C12.7188 2.56199 12.5665 2.19437 12.2954 1.92332C12.0244 1.65228 11.6568 1.5 11.2734 1.5C10.8901 1.5 10.5225 1.65228 10.2514 1.92332C9.9804 2.19437 9.82812 2.56199 9.82812 2.94532V4.98032C9.80094 4.97638 9.7735 4.97444 9.74603 4.97453L3.63814 5.8475C2.94928 5.9459 2.31903 6.28948 1.86312 6.81517C1.4072 7.34086 1.15622 8.01337 1.15625 8.70922V12.7289H4.88169C5.44529 11.923 6.25113 11.3177 7.18218 11.0009C8.11323 10.6841 9.12102 10.6724 10.0592 10.9674C10.9974 11.2624 11.8171 11.8488 12.3993 12.6414C12.9815 13.434 13.2959 14.3915 13.2969 15.375H24.8594C24.8601 14.4175 25.158 13.4838 25.712 12.7028C26.266 11.9218 27.0487 11.332 27.9521 11.0148C28.8556 10.6976 29.8352 10.6686 30.7558 10.9318C31.6765 11.195 32.4927 11.7375 33.0919 12.4844H35.6975L35.5079 9.51108Z"
                [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#47494F'}" />
            </svg>
            <span class="{{this.selectBookingValue ==='car' ? 'select':'unselect'}}"> {{'dashboard.Cars' | translate }}
              <div *ngIf="this.selectBookingValue ==='car'" class="underline"></div></span>
          </li>
        </ul>
      </div>
      <div *ngIf="this.applyBtn || this.applyBtn1" class="col-12" style="text-align: center;padding-top:30px;">
        <app-loader *ngIf="this.applyBtn || this.applyBtn1" [spinnerStyle]="true"></app-loader>
      </div>
      <div *ngIf="!this.applyBtn && !this.applyBtn1">
        <div class="graph">
          <div class="row" style="padding-left: 50px;">
            <div class="col-auto" style="text-align: center;margin-right:50px;margin-left: -50px;">
              <span class="showTitle"> {{'dashboard.Total' | translate }} <span *ngIf="this.selectBookingValue!=='all'"
                  style="text-transform: uppercase;"><span *ngIf="this.selectBookingValue==='flight'"> {{
                    'dashboard.flight' | translate }}</span> <span *ngIf="this.selectBookingValue==='hotel'"> {{
                    'dashboard.hotel' | translate }}</span> <span *ngIf="this.selectBookingValue==='car'"> {{
                    'dashboard.car' | translate }}</span></span> {{'dashboard.Spend' | translate }}</span>
              <div
                *ngIf="this.lineChartData.datasets[0].data && this.lineChartData.datasets[0].data.length ===0 && this.lineChartData.datasets[1].data && this.lineChartData.datasets[1].data.length ===0"
                class="left-panel1">
                <svg style="position: relative;top: 20px;" width="395" height="131" viewBox="0 0 395 131" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M2.94727 9.13903L80.5797 54.4796L158.212 3.78735L235.844 126.281L313.477 108.888L391.109 89.4142"
                    stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M2.9473 11.9264C4.02277 11.9264 4.89461 10.6785 4.89461 9.13904C4.89461 7.59963 4.02277 6.35168 2.9473 6.35168C1.87184 6.35168 1 7.59963 1 9.13904C1 10.6785 1.87184 11.9264 2.9473 11.9264Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M80.5796 57.2669C81.6551 57.2669 82.5269 56.019 82.5269 54.4796C82.5269 52.9401 81.6551 51.6922 80.5796 51.6922C79.5042 51.6922 78.6323 52.9401 78.6323 54.4796C78.6323 56.019 79.5042 57.2669 80.5796 57.2669Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M158.212 6.57465C159.288 6.57465 160.159 5.32672 160.159 3.78732C160.159 2.24793 159.288 1 158.212 1C157.137 1 156.265 2.24793 156.265 3.78732C156.265 5.32672 157.137 6.57465 158.212 6.57465Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M235.844 129.069C236.92 129.069 237.792 127.821 237.792 126.281C237.792 124.742 236.92 123.494 235.844 123.494C234.769 123.494 233.897 124.742 233.897 126.281C233.897 127.821 234.769 129.069 235.844 129.069Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M313.477 111.676C314.552 111.676 315.424 110.428 315.424 108.888C315.424 107.349 314.552 106.101 313.477 106.101C312.401 106.101 311.53 107.349 311.53 108.888C311.53 110.428 312.401 111.676 313.477 111.676Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M391.109 92.2014C392.185 92.2014 393.057 90.9535 393.057 89.4141C393.057 87.8747 392.185 86.6268 391.109 86.6268C390.034 86.6268 389.162 87.8747 389.162 89.4141C389.162 90.9535 390.034 92.2014 391.109 92.2014Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M2.94727 46.1548L80.5797 55.8176L158.212 91.0494L235.844 40.3572L313.477 88.6709L391.109 62.0612"
                    stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M2.9473 48.9421C4.02277 48.9421 4.89461 47.6942 4.89461 46.1548C4.89461 44.6154 4.02277 43.3675 2.9473 43.3675C1.87184 43.3675 1 44.6154 1 46.1548C1 47.6942 1.87184 48.9421 2.9473 48.9421Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M80.5796 58.6048C81.6551 58.6048 82.5269 57.3569 82.5269 55.8175C82.5269 54.2781 81.6551 53.0302 80.5796 53.0302C79.5042 53.0302 78.6323 54.2781 78.6323 55.8175C78.6323 57.3569 79.5042 58.6048 80.5796 58.6048Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M158.212 93.8367C159.287 93.8367 160.159 92.5887 160.159 91.0493C160.159 89.51 159.287 88.262 158.212 88.262C157.136 88.262 156.265 89.51 156.265 91.0493C156.265 92.5887 157.136 93.8367 158.212 93.8367Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M235.844 43.1445C236.92 43.1445 237.792 41.8965 237.792 40.3571C237.792 38.8177 236.92 37.5698 235.844 37.5698C234.769 37.5698 233.897 38.8177 233.897 40.3571C233.897 41.8965 234.769 43.1445 235.844 43.1445Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M313.477 91.4583C314.552 91.4583 315.424 90.2103 315.424 88.6709C315.424 87.1314 314.552 85.8835 313.477 85.8835C312.401 85.8835 311.53 87.1314 311.53 88.6709C311.53 90.2103 312.401 91.4583 313.477 91.4583Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                  <path
                    d="M391.109 64.8485C392.185 64.8485 393.057 63.6006 393.057 62.0612C393.057 60.5217 392.185 59.2738 391.109 59.2738C390.034 59.2738 389.162 60.5217 389.162 62.0612C389.162 63.6006 390.034 64.8485 391.109 64.8485Z"
                    fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                </svg>
              </div>
              <div
                *ngIf="this.lineChartData.datasets[0].data && this.lineChartData.datasets[0].data.length >0 || this.lineChartData.datasets[1].data && this.lineChartData.datasets[1].data.length >0"
                class="left-panel1">
                <div style="display: block;">
                  <canvas baseChart class="linechart" [data]="lineChartData" 
                    [options]="lineChartOptions" 
                    [type]="lineChartType" >
                  </canvas>
                </div>
              </div>
            </div>
            <div class="col-auto" style="margin-left:50px;">
              <span class="showTitle" style="margin-left:15px;">{{daterangepickerModel[0] | date : 'dd MMM yyyy'}} -
                {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span>
              <span class="showTitle" style="margin-right:60px;float:right;"><span
                  *ngIf="this.totalBooking >0 || this.totalPreviousBooking>0">%</span> {{'dashboard.change' | translate
                }}</span>
              <div class="left-panel11" [ngStyle]="{'height': this.selectBookingValue!=='all' ? '264px':'224px'}">
                <div *ngIf="this.totalBooking===0 && this.totalPreviousBooking===0 " class="row">
                  <div class="col-9">
                    <div class="bookingdetails">
                      <svg width="16" height="24" viewBox="0 0 16 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M9.12498 10.3973V5.16675C10.1216 5.22579 11.1065 5.41309 12.0552 5.724L13.4427 6.27825L14.5565 3.49275L13.163 2.9355C11.8621 2.48027 10.5018 2.2175 9.12498 2.1555V0H6.87498V2.184C3.78648 2.51325 0.830732 4.209 0.830732 7.51725C0.830732 11.0325 4.03773 12.1245 6.87498 12.8962V18.3795C5.54415 18.3128 4.23429 18.0206 3.00123 17.5155L1.65948 16.8442L0.318481 19.5278L1.65948 20.199C3.30987 20.9126 5.07824 21.3142 6.87498 21.3832V24H9.12498V21.3638C12.8307 21.0263 15.1692 19.008 15.1692 16.0215C15.1692 12.3885 11.9067 11.175 9.12498 10.3973ZM3.83073 7.51725C3.83073 6.03675 5.53698 5.427 6.87498 5.21625V9.7725C4.82373 9.1605 3.83073 8.613 3.83073 7.51725ZM9.12498 18.3495V13.5292C11.174 14.1645 12.1692 14.7765 12.1692 16.0215C12.1692 17.6655 10.4292 18.1882 9.12498 18.3495Z"
                          fill="#AEAEAE" />
                      </svg>
                      <span class="show1" style=" font-size: 17px !important;"><span
                          *ngIf="this.selectBookingValue==='all'" style="text-transform: capitalize;">
                          {{'dashboard.Total' | translate }}</span><span *ngIf="this.selectBookingValue!=='all'"
                          style="text-transform: capitalize;">
                          <span *ngIf="this.selectBookingValue==='flight'"> {{ 'dashboard.flight' | translate }}</span>
                          <span *ngIf="this.selectBookingValue==='hotel'"> {{ 'dashboard.hotel' | translate }}</span>
                          <span *ngIf="this.selectBookingValue==='car'"> {{ 'dashboard.car' | translate }}</span>
                        </span> {{'dashboard.Spend' | translate }}:</span>
                      <span class="show2" style=" font-size: 16px !important;">{{'dashboard.na' | translate }}</span>
                    </div>
                    <div class="bookingdetails">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M18.75 13.5C17.7117 13.5 16.6966 13.8079 15.8333 14.3848C14.9699 14.9617 14.297 15.7816 13.8996 16.7409C13.5023 17.7002 13.3983 18.7558 13.6009 19.7742C13.8035 20.7926 14.3035 21.7281 15.0377 22.4623C15.7719 23.1965 16.7074 23.6966 17.7258 23.8991C18.7442 24.1017 19.7998 23.9977 20.7591 23.6004C21.7184 23.203 22.5383 22.5301 23.1152 21.6667C23.6921 20.8034 24 19.7884 24 18.75C24 17.3576 23.4469 16.0223 22.4623 15.0377C21.4777 14.0531 20.1424 13.5 18.75 13.5ZM18.5303 20.7803C18.3896 20.9209 18.1989 20.9998 18 20.9998C17.8011 20.9998 17.6104 20.9209 17.4698 20.7803L15.4395 18.75L16.5 17.6895L18 19.1895L21 16.1895L22.0605 17.25L18.5303 20.7803Z"
                          fill="#AEAEAE" />
                        <path
                          d="M12 21.75H3C2.80109 21.75 2.61032 21.671 2.46967 21.5303C2.32902 21.3897 2.25 21.1989 2.25 21V7.5C2.25 7.30109 2.32902 7.11032 2.46967 6.96967C2.61032 6.82902 2.80109 6.75 3 6.75H21C21.1989 6.75 21.3897 6.82902 21.5303 6.96967C21.671 7.11032 21.75 7.30109 21.75 7.5V12H23.25V4.5C23.25 4.10218 23.092 3.72064 22.8107 3.43934C22.5294 3.15804 22.1478 3 21.75 3H18V0.75C18 0.551088 17.921 0.360322 17.7803 0.21967C17.6397 0.0790176 17.4489 0 17.25 0C17.0511 0 16.8603 0.0790176 16.7197 0.21967C16.579 0.360322 16.5 0.551088 16.5 0.75V3H7.5V0.75C7.5 0.551088 7.42098 0.360322 7.28033 0.21967C7.13968 0.0790176 6.94891 0 6.75 0C6.55109 0 6.36032 0.0790176 6.21967 0.21967C6.07902 0.360322 6 0.551088 6 0.75V3H2.25C1.85218 3 1.47064 3.15804 1.18934 3.43934C0.908035 3.72064 0.75 4.10218 0.75 4.5V21.75C0.75 22.1478 0.908035 22.5294 1.18934 22.8107C1.47064 23.092 1.85218 23.25 2.25 23.25H12V21.75Z"
                          fill="#AEAEAE" />
                      </svg>
                      <span class="show1" style=" font-size: 17px !important;"><span
                          *ngIf="this.selectBookingValue==='all'"
                          style="text-transform: capitalize;">{{'dashboard.Total' | translate }} </span>
                        <span *ngIf="this.selectBookingValue!=='all'" style="text-transform: capitalize;">
                          <span *ngIf="this.selectBookingValue==='flight'"> {{ 'dashboard.flight' | translate }}</span>
                          <span *ngIf="this.selectBookingValue==='hotel'"> {{ 'dashboard.hotel' | translate }}</span>
                          <span *ngIf="this.selectBookingValue==='car'"> {{ 'dashboard.car' | translate }}</span>
                        </span>{{'dashboard.bookings' | translate }}: </span>
                      <span class="show2" style=" font-size: 16px !important;">{{'dashboard.na' | translate }}</span>
                    </div>
                    <div class="bookingdetails">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M2.32505 5.77499C2.17505 5.39999 1.65005 5.32499 1.35005 5.47499C0.975051 5.69999 0.825051 6.14999 1.05005 6.52499L10.7251 23.25C10.8751 23.475 11.1001 23.625 11.4001 23.625C11.5501 23.625 11.6251 23.625 11.7751 23.55C12.1501 23.325 12.2251 22.875 12.0751 22.5L2.32505 5.77499Z"
                          fill="#AEAEAE" />
                        <path
                          d="M17.5501 1.64999C17.4751 1.49999 17.2501 1.34999 17.0251 1.34999C16.8001 1.34999 16.5751 1.34999 16.4251 1.57499C15.0001 2.84999 13.5001 2.99999 11.9251 2.99999H10.7251C8.5501 2.99999 6.3001 3.22499 4.6501 6.14999C4.5001 6.37499 4.5001 6.67499 4.6501 6.89999L9.2251 14.85C9.3751 15.075 9.6001 15.225 9.9001 15.225C10.2001 15.225 10.4251 15.075 10.5751 14.85C11.8501 12.6 13.3501 12.45 15.4501 12.45H16.4251C18.2251 12.45 20.1751 12.3 21.9751 10.725C22.2751 10.5 22.2751 10.125 22.1251 9.82499L17.5501 1.64999Z"
                          fill="#AEAEAE" />
                      </svg>

                      <span class="show1" style=" font-size: 17px !important;"><span
                          *ngIf="this.selectBookingValue==='all'"
                          style="text-transform: capitalize;">{{'dashboard.Overall' | translate }}</span>
                        {{'dashboard.Compliance' | translate }}: </span>
                      <span class="show2" style=" font-size: 16px !important;">{{'dashboard.na' | translate }}</span>
                    </div>
                    <div class="bookingdetails">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M14.8094 5.25148C16.1556 5.25148 17.2469 4.16017 17.2469 2.81398C17.2469 1.46779 16.1556 0.37648 14.8094 0.37648C13.4633 0.37648 12.3719 1.46779 12.3719 2.81398C12.3719 4.16017 13.4633 5.25148 14.8094 5.25148Z"
                          fill="#AEAEAE" />
                        <path
                          d="M17.625 17.1652L14.787 14.6152L13.9425 17.4622L15.747 18.5025V22.125C15.747 22.4233 15.8655 22.7095 16.0765 22.9205C16.2875 23.1314 16.5736 23.25 16.872 23.25C17.1704 23.25 17.4565 23.1314 17.6675 22.9205C17.8785 22.7095 17.997 22.4233 17.997 22.125V18C17.9969 17.8425 17.9637 17.6869 17.8996 17.5431C17.8355 17.3993 17.742 17.2706 17.625 17.1652Z"
                          fill="#AEAEAE" />
                        <path
                          d="M9.38921 10.4535L10.5348 10.3492L10.9098 7.99423C10.9384 7.81604 10.984 7.64099 11.046 7.47148L8.48546 8.07598C8.30261 8.11518 8.1324 8.19935 7.99026 8.32088C7.84811 8.4424 7.7385 8.59744 7.67134 8.77198L6.44996 11.9464C6.39331 12.0893 6.36566 12.242 6.36862 12.3957C6.37159 12.5494 6.4051 12.701 6.46722 12.8416C6.52934 12.9822 6.61882 13.1091 6.73045 13.2148C6.84208 13.3205 6.97362 13.4029 7.11742 13.4573C7.26122 13.5116 7.41439 13.5368 7.56803 13.5314C7.72166 13.526 7.87268 13.4901 8.01229 13.4257C8.15189 13.3613 8.2773 13.2698 8.3812 13.1565C8.4851 13.0432 8.56541 12.9104 8.61746 12.7657L8.63096 12.7282L9.38921 10.4535Z"
                          fill="#AEAEAE" />
                        <path
                          d="M22.4625 9.85348C22.4266 9.71015 22.3629 9.57528 22.275 9.45657C22.187 9.33786 22.0766 9.23764 21.9499 9.16162C21.8232 9.0856 21.6828 9.03528 21.5367 9.01353C21.3905 8.99178 21.2415 8.99903 21.0982 9.03486L18.5021 9.68398L14.9801 6.83398C14.6994 6.60648 14.3637 6.45684 14.0069 6.40009C13.65 6.34334 13.2845 6.38147 12.9471 6.51066C12.6096 6.63985 12.3121 6.85555 12.0844 7.1361C11.8566 7.41665 11.7067 7.75217 11.6497 8.10898L9.39972 22.2412C9.38459 22.3273 9.37507 22.4142 9.37122 22.5015C9.37157 22.7793 9.4747 23.0472 9.66076 23.2535C9.84682 23.4598 10.1026 23.59 10.3789 23.6189C10.6552 23.6479 10.9325 23.5736 11.1573 23.4103C11.3821 23.2471 11.5385 23.0064 11.5965 22.7347L15.2913 10.275L17.7071 11.7461C17.8763 11.8483 18.0641 11.916 18.2596 11.9453C18.4552 11.9747 18.6545 11.9651 18.8463 11.9171L21.6446 11.2174C21.9336 11.1448 22.182 10.9606 22.3353 10.7051C22.4887 10.4496 22.5344 10.1438 22.4625 9.85461V9.85348Z"
                          fill="#AEAEAE" />
                        <path
                          d="M4.12495 21.375C4.41421 21.3753 4.6971 21.4599 4.93908 21.6184L7.39421 14.2522C7.21132 14.2409 7.03106 14.203 6.85908 14.1397C6.7948 14.1123 6.73219 14.0812 6.67158 14.0464L6.11808 15.7065L4.16808 15.0562C4.02788 15.0095 3.87985 14.9909 3.73244 15.0014C3.58503 15.0119 3.44114 15.0513 3.30898 15.1175C3.17683 15.1836 3.05899 15.2751 2.96221 15.3868C2.86544 15.4985 2.79161 15.6281 2.74495 15.7684L0.96858 21.0326C0.874978 21.3158 0.897405 21.6245 1.03095 21.8912C1.16449 22.1578 1.39827 22.3607 1.68108 22.4554L2.63583 22.7734C2.66089 22.3951 2.82853 22.0404 3.10491 21.7808C3.38128 21.5213 3.74582 21.3763 4.12495 21.375Z"
                          fill="#AEAEAE" />
                        <path
                          d="M4.125 23.625C4.53921 23.625 4.875 23.2892 4.875 22.875C4.875 22.4608 4.53921 22.125 4.125 22.125C3.71079 22.125 3.375 22.4608 3.375 22.875C3.375 23.2892 3.71079 23.625 4.125 23.625Z"
                          fill="#AEAEAE" />
                      </svg>

                      <span class="show1" style=" font-size: 17px !important;">{{'dashboard.TotalTravelers' | translate
                        }}:</span>
                      <span class="show2" style=" font-size: 16px !important;">{{'dashboard.na' | translate }}</span>
                    </div>
                    <div *ngIf="this.selectBookingValue!=='all'" class="bookingdetails">
                      <svg width="18" height="19" viewBox="0 0 25 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M3.37597 4.58984C5.11811 6.8466 9.55643 10.3035 13.3726 6.07692C17.1888 1.85035 21.1242 5.23241 22.6148 7.45176"
                          stroke="#AEAEAE" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                        <path
                          d="M2.38525 11.2495C4.12739 13.5062 8.56571 16.9631 12.3819 12.7366C16.1981 8.50999 20.1334 11.892 21.6241 14.1114"
                          stroke="#AEAEAE" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>

                      <span class="show1" style=" font-size: 17px !important;">{{'dashboard.Avg.Price' | translate
                        }}<span *ngIf="this.selectBookingValue==='car'">{{'dashboard.Day' | translate }}</span><span
                          *ngIf="this.selectBookingValue==='hotel'">{{'dashboard.Night' | translate }}</span><span
                          *ngIf="this.selectBookingValue==='flight'">{{'dashboard.Booking' | translate }}</span>:
                      </span>
                      <span class="show2" style=" font-size: 16px !important;">{{'dashboard.na' | translate }}</span>
                    </div>
                  </div>
                  <div class="col-3" style="text-align: end;">
                    <div class="bookingdetails">
                      <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png"> <span
                        class="show1" style=" font-size: 17px !important;"> 0%</span>
                    </div>
                    <div class="bookingdetails">
                      <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png"> <span
                        class="show1" style=" font-size: 17px !important;"> 0%</span>
                    </div>
                    <div class="bookingdetails">
                      <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png"> <span
                        class="show1" style=" font-size: 17px !important;"> 0%</span>
                    </div>
                    <div class="bookingdetails">
                      <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png"> <span
                        class="show1" style=" font-size: 17px !important;"> 0%</span>
                    </div>
                    <div *ngIf="this.selectBookingValue!=='all'" class="bookingdetails">
                      <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png"> <span
                        class="show1" style=" font-size: 17px !important;"> 0%</span>
                    </div>
                  </div>
                </div>
                <div *ngIf="this.totalBooking > 0 || this.totalPreviousBooking > 0" class="row">
                  <div class="col-9">
                    <div class="bookingdetails">
                      <svg width="16" height="24" viewBox="0 0 16 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M9.12486 10.3973V5.16675C10.1215 5.22579 11.1064 5.41309 12.0551 5.724L13.4426 6.27825L14.5564 3.49275L13.1629 2.9355C11.862 2.48027 10.5017 2.2175 9.12486 2.1555V0H6.87486V2.184C3.78636 2.51325 0.830609 4.209 0.830609 7.51725C0.830609 11.0325 4.03761 12.1245 6.87486 12.8962V18.3795C5.54402 18.3128 4.23417 18.0206 3.00111 17.5155L1.65936 16.8442L0.318359 19.5278L1.65936 20.199C3.30975 20.9126 5.07812 21.3142 6.87486 21.3832V24H9.12486V21.3638C12.8306 21.0263 15.1691 19.008 15.1691 16.0215C15.1691 12.3885 11.9066 11.175 9.12486 10.3973ZM3.83061 7.51725C3.83061 6.03675 5.53686 5.427 6.87486 5.21625V9.7725C4.82361 9.1605 3.83061 8.613 3.83061 7.51725ZM9.12486 18.3495V13.5292C11.1739 14.1645 12.1691 14.7765 12.1691 16.0215C12.1691 17.6655 10.4291 18.1882 9.12486 18.3495Z"
                          fill="#8936F3" />
                      </svg>
                      <span class="show3"><span *ngIf="this.selectBookingValue==='all'"
                          style="text-transform: capitalize;"> {{'dashboard.Total' | translate }}</span><span
                          *ngIf="this.selectBookingValue!=='all'" style="text-transform: capitalize;"> <span
                            *ngIf="this.selectBookingValue==='flight'"> {{ 'dashboard.flight' | translate }}</span>
                          <span *ngIf="this.selectBookingValue==='hotel'"> {{ 'dashboard.hotel' | translate }}</span>
                          <span *ngIf="this.selectBookingValue==='car'"> {{ 'dashboard.car' | translate }}</span>
                        </span> {{'dashboard.Spend'
                        | translate }}:</span>
                      <span class="show4" style=" font-size: 17px !important;"
                        *ngIf="this.totalSpend >0">{{this.totalSpend | currency : getCurrencySymbol(this.currency) :
                        'code' : "1.2-2" }}</span>
                      <span class="show4" style=" font-size: 17px !important;" *ngIf="this.totalSpend ===0">
                        {{getCurrencySymbol(this.currency)}}0</span>
                    </div>
                    <div class="bookingdetails">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M18.75 13.5C17.7117 13.5 16.6966 13.8079 15.8333 14.3848C14.9699 14.9617 14.297 15.7816 13.8996 16.7409C13.5023 17.7002 13.3983 18.7558 13.6009 19.7742C13.8035 20.7926 14.3035 21.7281 15.0377 22.4623C15.7719 23.1965 16.7074 23.6966 17.7258 23.8991C18.7442 24.1017 19.7998 23.9977 20.7591 23.6004C21.7184 23.203 22.5383 22.5301 23.1152 21.6667C23.6921 20.8034 24 19.7884 24 18.75C24 17.3576 23.4469 16.0223 22.4623 15.0377C21.4777 14.0531 20.1424 13.5 18.75 13.5ZM18.5303 20.7803C18.3896 20.9209 18.1989 20.9998 18 20.9998C17.8011 20.9998 17.6104 20.9209 17.4698 20.7803L15.4395 18.75L16.5 17.6895L18 19.1895L21 16.1895L22.0605 17.25L18.5303 20.7803Z"
                          fill="#8936F3" />
                        <path
                          d="M12 21.75H3C2.80109 21.75 2.61032 21.671 2.46967 21.5303C2.32902 21.3897 2.25 21.1989 2.25 21V7.5C2.25 7.30109 2.32902 7.11032 2.46967 6.96967C2.61032 6.82902 2.80109 6.75 3 6.75H21C21.1989 6.75 21.3897 6.82902 21.5303 6.96967C21.671 7.11032 21.75 7.30109 21.75 7.5V12H23.25V4.5C23.25 4.10218 23.092 3.72064 22.8107 3.43934C22.5294 3.15804 22.1478 3 21.75 3H18V0.75C18 0.551088 17.921 0.360322 17.7803 0.21967C17.6397 0.0790176 17.4489 0 17.25 0C17.0511 0 16.8603 0.0790176 16.7197 0.21967C16.579 0.360322 16.5 0.551088 16.5 0.75V3H7.5V0.75C7.5 0.551088 7.42098 0.360322 7.28033 0.21967C7.13968 0.0790176 6.94891 0 6.75 0C6.55109 0 6.36032 0.0790176 6.21967 0.21967C6.07902 0.360322 6 0.551088 6 0.75V3H2.25C1.85218 3 1.47064 3.15804 1.18934 3.43934C0.908035 3.72064 0.75 4.10218 0.75 4.5V21.75C0.75 22.1478 0.908035 22.5294 1.18934 22.8107C1.47064 23.092 1.85218 23.25 2.25 23.25H12V21.75Z"
                          fill="#8936F3" />
                      </svg>

                      <span class="show3"><span *ngIf="this.selectBookingValue==='all'"
                          style="text-transform: capitalize;"> {{'dashboard.Total' | translate }}</span> <span
                          *ngIf="this.selectBookingValue!=='all'" style="text-transform: capitalize;">
                          <span *ngIf="this.selectBookingValue==='flight'"> {{ 'dashboard.flight' | translate }}</span>
                          <span *ngIf="this.selectBookingValue==='hotel'"> {{ 'dashboard.hotel' | translate }}</span>
                          <span *ngIf="this.selectBookingValue==='car'"> {{ 'dashboard.car' | translate }}</span>
                        </span>{{'dashboard.bookings' | translate }} : </span>
                      <span class="show4" style=" font-size: 17px !important;">{{this.totalBooking}}</span>
                    </div>
                    <div class="bookingdetails">
                      <svg width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M2.32505 4.77499C2.17505 4.39999 1.65005 4.32499 1.35005 4.47499C0.975051 4.69999 0.825051 5.14999 1.05005 5.52499L10.7251 22.25C10.8751 22.475 11.1001 22.625 11.4001 22.625C11.5501 22.625 11.6251 22.625 11.7751 22.55C12.1501 22.325 12.2251 21.875 12.0751 21.5L2.32505 4.77499Z"
                          fill="#8936F3" />
                        <path
                          d="M17.5501 0.649991C17.4751 0.499991 17.2501 0.349991 17.0251 0.349991C16.8001 0.349991 16.5751 0.349991 16.4251 0.574991C15.0001 1.84999 13.5001 1.99999 11.9251 1.99999H10.7251C8.5501 1.99999 6.3001 2.22499 4.6501 5.14999C4.5001 5.37499 4.5001 5.67499 4.6501 5.89999L9.2251 13.85C9.3751 14.075 9.6001 14.225 9.9001 14.225C10.2001 14.225 10.4251 14.075 10.5751 13.85C11.8501 11.6 13.3501 11.45 15.4501 11.45H16.4251C18.2251 11.45 20.1751 11.3 21.9751 9.72499C22.2751 9.49999 22.2751 9.12499 22.1251 8.82499L17.5501 0.649991Z"
                          fill="#8936F3" />
                      </svg>
                      <span class="show3"><span *ngIf="this.selectBookingValue==='all'"
                          style="text-transform: capitalize;">{{'dashboard.Overall' | translate }}</span>
                        {{'dashboard.Compliance' | translate }}: </span>
                      <span class="show4" style=" font-size: 17px !important;">{{this.totalCompliance}}</span>
                    </div>
                    <div class="bookingdetails">
                      <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M14.8093 5.25148C16.1555 5.25148 17.2468 4.16017 17.2468 2.81398C17.2468 1.46779 16.1555 0.37648 14.8093 0.37648C13.4631 0.37648 12.3718 1.46779 12.3718 2.81398C12.3718 4.16017 13.4631 5.25148 14.8093 5.25148Z"
                          fill="#8936F3" />
                        <path
                          d="M17.6249 17.1652L14.7869 14.6152L13.9424 17.4622L15.7469 18.5025V22.125C15.7469 22.4233 15.8654 22.7095 16.0764 22.9205C16.2874 23.1314 16.5735 23.25 16.8719 23.25C17.1703 23.25 17.4564 23.1314 17.6674 22.9205C17.8784 22.7095 17.9969 22.4233 17.9969 22.125V18C17.9967 17.8425 17.9635 17.6869 17.8995 17.5431C17.8354 17.3993 17.7418 17.2706 17.6249 17.1652Z"
                          fill="#8936F3" />
                        <path
                          d="M9.38921 10.4535L10.5348 10.3492L10.9098 7.99423C10.9384 7.81604 10.984 7.64099 11.046 7.47148L8.48546 8.07598C8.30261 8.11518 8.1324 8.19935 7.99026 8.32088C7.84811 8.4424 7.7385 8.59744 7.67134 8.77198L6.44996 11.9464C6.39331 12.0893 6.36566 12.242 6.36862 12.3957C6.37159 12.5494 6.4051 12.701 6.46722 12.8416C6.52934 12.9822 6.61882 13.1091 6.73045 13.2148C6.84208 13.3205 6.97362 13.4029 7.11742 13.4573C7.26122 13.5116 7.41439 13.5368 7.56803 13.5314C7.72166 13.526 7.87268 13.4901 8.01229 13.4257C8.15189 13.3613 8.2773 13.2698 8.3812 13.1565C8.4851 13.0432 8.56541 12.9104 8.61746 12.7657L8.63096 12.7282L9.38921 10.4535Z"
                          fill="#8936F3" />
                        <path
                          d="M22.4626 9.85348C22.4268 9.71015 22.3631 9.57528 22.2751 9.45657C22.1872 9.33786 22.0767 9.23764 21.95 9.16162C21.8233 9.0856 21.6829 9.03528 21.5368 9.01353C21.3906 8.99178 21.2417 8.99903 21.0983 9.03486L18.5022 9.68398L14.9802 6.83398C14.6995 6.60648 14.3638 6.45684 14.007 6.40009C13.6501 6.34334 13.2846 6.38147 12.9472 6.51066C12.6097 6.63985 12.3122 6.85555 12.0845 7.1361C11.8568 7.41665 11.7069 7.75217 11.6498 8.10898L9.39984 22.2412C9.38471 22.3273 9.37519 22.4142 9.37134 22.5015C9.37169 22.7793 9.47483 23.0472 9.66088 23.2535C9.84694 23.4598 10.1028 23.59 10.3791 23.6189C10.6554 23.6479 10.9326 23.5736 11.1574 23.4103C11.3822 23.2471 11.5386 23.0064 11.5966 22.7347L15.2915 10.275L17.7072 11.7461C17.8765 11.8483 18.0643 11.916 18.2598 11.9453C18.4553 11.9747 18.6547 11.9651 18.8465 11.9171L21.6447 11.2174C21.9337 11.1448 22.1821 10.9606 22.3355 10.7051C22.4888 10.4496 22.5345 10.1438 22.4626 9.85461V9.85348Z"
                          fill="#8936F3" />
                        <path
                          d="M4.12495 21.375C4.41421 21.3753 4.6971 21.4599 4.93908 21.6184L7.39421 14.2522C7.21132 14.2409 7.03106 14.203 6.85908 14.1397C6.7948 14.1123 6.73219 14.0812 6.67158 14.0464L6.11808 15.7065L4.16808 15.0562C4.02788 15.0095 3.87985 14.9909 3.73244 15.0014C3.58503 15.0119 3.44114 15.0513 3.30898 15.1175C3.17683 15.1836 3.05899 15.2751 2.96221 15.3868C2.86544 15.4985 2.79161 15.6281 2.74495 15.7684L0.96858 21.0326C0.874978 21.3158 0.897405 21.6245 1.03095 21.8912C1.16449 22.1578 1.39827 22.3607 1.68108 22.4554L2.63583 22.7734C2.66089 22.3951 2.82853 22.0404 3.10491 21.7808C3.38128 21.5213 3.74582 21.3763 4.12495 21.375Z"
                          fill="#8936F3" />
                        <path
                          d="M4.125 23.625C4.53921 23.625 4.875 23.2892 4.875 22.875C4.875 22.4608 4.53921 22.125 4.125 22.125C3.71079 22.125 3.375 22.4608 3.375 22.875C3.375 23.2892 3.71079 23.625 4.125 23.625Z"
                          fill="#8936F3" />
                      </svg>
                      <span class="show3"> {{'dashboard.Total' | translate }}<span
                          *ngIf="this.selectBookingValue!=='all'" style="text-transform: capitalize;"><span
                            *ngIf="this.selectBookingValue==='flight'"> {{ 'dashboard.flight' | translate }}</span>
                          <span *ngIf="this.selectBookingValue==='hotel'"> {{ 'dashboard.hotel' | translate }}</span>
                          <span *ngIf="this.selectBookingValue==='car'"> {{ 'dashboard.car' | translate }}</span></span>
                        {{'dashboard.Travelers' | translate }}: </span>
                      <span class="show4" style=" font-size: 17px !important;">{{this.totalTraveler}}</span>
                    </div>
                    <div *ngIf="this.selectBookingValue!=='all'" class="bookingdetails">
                      <svg width="25" height="19" viewBox="0 0 25 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M3.37597 4.58984C5.11811 6.8466 9.55643 10.3035 13.3726 6.07692C17.1888 1.85035 21.1242 5.23241 22.6148 7.45176"
                          stroke="#8936F3" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                        <path
                          d="M2.38525 11.2495C4.12739 13.5062 8.56571 16.9631 12.3819 12.7366C16.1981 8.50999 20.1334 11.892 21.6241 14.1114"
                          stroke="#8936F3" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>

                      <span class="show3">{{'dashboard.Avg.Price' | translate }}<span
                          *ngIf="this.selectBookingValue==='car'">{{'dashboard.Day' | translate }}</span><span
                          *ngIf="this.selectBookingValue==='hotel'">{{'dashboard.Night' | translate }}</span>
                        <span *ngIf="this.selectBookingValue==='flight'">{{'dashboard.Booking' | translate }}</span>:
                      </span>
                      <span class="show4" style=" font-size: 17px !important;"
                        *ngIf="this.avgPriceDay >0 ">{{this.avgPriceDay | currency : getCurrencySymbol(this.currency) :
                        'code' : "1.2-2"}}</span>
                      <span class="show4" style=" font-size: 17px !important;" *ngIf="this.avgPriceDay ===0 ">
                        {{getCurrencySymbol(this.currency)}}0</span>
                    </div>

                  </div>
                  <div class="col-3" style="text-align: end;">
                    <div class="bookingdetails">
                      <div class="row">
                        <div class="col-6">
                          <img *ngIf="showChangeReport(this.totalSpend,this.totalPreviousSpend)==='positive'"
                            style="margin-right:3px;position: relative;top: -4px;" src="assets/images/positive.png">
                          <img *ngIf="showChangeReport(this.totalSpend,this.totalPreviousSpend)==='negative'"
                            style="margin-right:3px;position: relative;top: -4px;" src="assets/images/negative.png">
                        </div>
                        <div class="col-6"> <span class="show3">
                            {{percentageDiff(this.totalSpend,this.totalPreviousSpend)}}%</span></div>
                      </div>
                    </div>
                    <div class="bookingdetails">
                      <div class="row">
                        <div class="col-6">
                          <img *ngIf="showChangeReport(this.totalBooking,this.totalPreviousBooking)==='positive'"
                            style="margin-right:3px;position: relative;top: -4px;" src="assets/images/positive.png">
                          <img *ngIf="showChangeReport(this.totalBooking,this.totalPreviousBooking)==='negative'"
                            style="margin-right:3px;position: relative;top: -4px;" src="assets/images/negative.png">
                        </div>
                        <div class="col-6"> <span class="show3">
                            {{percentageDiff(this.totalBooking,this.totalPreviousBooking)}}%</span></div>
                      </div>
                    </div>
                    <div class="bookingdetails">
                      <div class="row">
                        <div class="col-6">
                          <img *ngIf="showChangeReport(this.totalCompliance,this.totalPreviousCompliance)==='positive'"
                            style="margin-right:3px;position: relative;top: -4px;" src="assets/images/positive.png">
                          <img *ngIf="showChangeReport(this.totalCompliance,this.totalPreviousCompliance)==='negative'"
                            style="margin-right:3px;position: relative;top: -4px;" src="assets/images/negative.png">
                        </div>
                        <div class="col-6"><span class="show3">
                            {{percentageDiff(this.totalCompliance,this.totalPreviousCompliance)}}%</span></div>
                      </div>
                    </div>
                    <div class="bookingdetails">
                      <div class="row">
                        <div class="col-6">
                          <img *ngIf="showChangeReport(this.totalTraveler,this.totalPreviousTraveler)==='positive'"
                            style="margin-right:3px;position: relative;top: -4px;" src="assets/images/positive.png">
                          <img *ngIf="showChangeReport(this.totalTraveler,this.totalPreviousTraveler)==='negative'"
                            style="margin-right:3px;position: relative;top: -4px;" src="assets/images/negative.png">
                        </div>
                        <div class="col-6"> <span class="show3">
                            {{percentageDiff(this.totalTraveler,this.totalPreviousTraveler)}}%</span></div>
                      </div>
                    </div>
                    <div *ngIf="this.selectBookingValue!=='all'" class="bookingdetails">
                      <div class="row">
                        <div class="col-6">
                          <img *ngIf="showChangeReport(this.avgPriceDay,this.avgPreviousPriceDay)==='positive'"
                            style="margin-right:3px;position: relative;top: -4px;" src="assets/images/positive.png">
                          <img *ngIf="showChangeReport(this.avgPriceDay,this.avgPreviousPriceDay)==='negative'"
                            style="margin-right:3px;position: relative;top: -4px;" src="assets/images/negative.png">
                        </div>
                        <div class="col-6"> <span class="show3">
                            {{percentageDiff(this.avgPriceDay,this.avgPreviousPriceDay)}}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row" style="margin-top: 20px;">
            <div class="col-auto" style="text-align: center;margin-right:10px;">
              <span class="showTitle">{{'dashboard.approvals' | translate }}</span>
              <div *ngIf="this.pieChartData1.datasets[0].data.length === 0" class="approvalGraph">
                <svg width="151" height="150" viewBox="0 0 151 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M132.448 57.8895C127.978 43.6503 118.317 31.5743 105.372 24.0466C92.4264 16.5188 77.1389 14.0865 62.528 17.2299C47.9172 20.3733 35.0452 28.864 26.4542 41.0251C17.8632 53.1863 14.1778 68.1337 16.1257 82.9156L75.3979 75.4255L132.448 57.8895Z"
                    fill="#AEAEAE" fill-opacity="0.2" stroke="white" />
                  <path
                    d="M16.0257 82.8818C16.797 88.7422 18.4382 94.4596 20.8955 99.8461L75.3223 75.379L16.0257 82.8818Z"
                    fill="#AEAEAE" fill-opacity="0.2" stroke="white" />
                  <path
                    d="M20.8747 99.6975C27.1814 113.513 38.5292 124.438 52.6063 130.245C66.6834 136.053 82.4307 136.307 96.6397 130.956C110.849 125.604 122.451 115.05 129.082 101.443C135.713 87.8357 136.874 72.2 132.328 57.721L75.2775 75.2571L20.8747 99.6975Z"
                    fill="#AEAEAE" fill-opacity="0.2" stroke="white" />
                </svg>

              </div>
              <div *ngIf="this.pieChartData1.datasets[0].data.length > 0" class="approvalGraph">
                <div class="chart-container approvalGraph-chart-container">
                  <canvas baseChart class="piechart" [data]="pieChartData1" 
                    [options]="pieChartOptions1" 
                    [type]="pieChartType"></canvas>
                </div>
              </div>
            </div>
            <div class="col-auto" style="text-align: center;margin-right:10px;">
              <span class="showTitle"> {{'dashboard.Topspenders' | translate }}</span>
              <div *ngIf="this.barChartData1.datasets[0].data.length === 0" class="employeeGraph">
                <svg width="239" height="136" viewBox="0 0 239 136" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g opacity="0.2">
                    <path
                      d="M117.827 84.9081L117.827 107.109L-6.20188e-05 107.109L-6.10352e-05 84.9081L117.827 84.9081Z"
                      fill="#AEAEAE" />
                    <path d="M239 0L239 22.0493L0.168502 22.0493L0.168503 -1.03693e-05L239 0Z" fill="#AEAEAE" />
                    <path d="M101.213 113.288L101.213 135.338L0.168578 135.338L0.168579 113.288L101.213 113.288Z"
                      fill="#AEAEAE" />
                    <path d="M212.71 28.4143L212.71 50.615L-9.83648e-07 50.615L0 28.4143L212.71 28.4143Z"
                      fill="#AEAEAE" />
                    <path d="M189.243 56.2638L189.243 79.0736L0.168456 79.0736L0.168457 56.2638L189.243 56.2638Z"
                      fill="#AEAEAE" />
                  </g>
                </svg>

              </div>
              <div *ngIf="this.barChartData1.datasets[0].data.length > 0" class="employeeGraph">
                <div class="chart-container" style="position: relative;left:-10px;bottom:20px;">
                  <canvas baseChart class="barChart" [data]="barChartData1" 
                  [options]="barChartOptions2" 
                   [type]="barChartType1"></canvas>
                </div>
              </div>
            </div>
            <div *ngIf="this.selectBookingValue!=='all'" class="col-auto" style="text-align: center;margin-right:10px;">
              <span class="showTitle" style="text-transform:uppercase;">{{this.VehicleMsg}}</span>
              <div *ngIf="this.barChartData5.datasets[0].data.length === 0" class="employeeGraph">
                <svg width="239" height="136" viewBox="0 0 239 136" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g opacity="0.2">
                    <path
                      d="M117.827 84.9081L117.827 107.109L-6.20188e-05 107.109L-6.10352e-05 84.9081L117.827 84.9081Z"
                      fill="#AEAEAE" />
                    <path d="M239 0L239 22.0493L0.168502 22.0493L0.168503 -1.03693e-05L239 0Z" fill="#AEAEAE" />
                    <path d="M101.213 113.288L101.213 135.338L0.168578 135.338L0.168579 113.288L101.213 113.288Z"
                      fill="#AEAEAE" />
                    <path d="M212.71 28.4143L212.71 50.615L-9.83648e-07 50.615L0 28.4143L212.71 28.4143Z"
                      fill="#AEAEAE" />
                    <path d="M189.243 56.2638L189.243 79.0736L0.168456 79.0736L0.168457 56.2638L189.243 56.2638Z"
                      fill="#AEAEAE" />
                  </g>
                </svg>

              </div>
              <div *ngIf="this.barChartData5.datasets[0].data.length > 0" class="employeeGraph">
                <div class="chart-container" style="position: relative;left:-10px;bottom:20px;">
                  <canvas baseChart class="barChart" [data]="barChartData5"  [options]="barChartOptions5"  [type]="barChartType1"></canvas>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="divideline">
          <span class="name" style="position: relative;
          top: 26px;">{{'dashboard.Uniquetravelers'| translate }}</span>
          <svg width="1128" height="23" viewBox="0 0 1128 23" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="192" y="6" width="936" height="8" rx="4" fill="#8936F3" />
            <path fill="#8936F3" />
          </svg>

        </div>
        <div class="row" style="padding:18px 0;">
          <div class="col-12">
            <div class="uniquetravelerbox" [ngStyle]="{'height': this.weeklyData.length > 1 ?'285px':'162px'}">
              <div class="row" style="padding-top: 10px;">
                <div>
                  <div class="date-box-header">
                    <span class="show5"
                      style="margin-left:5px !important;font-size: 16px !important;">{{daterangepickerModel[0] | date :
                      'dd MMM yyyy'}} - {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span><br>
                    <span class="" style="text-align: center;"> <svg width="24" height="24" viewBox="0 0 24 24"
                        fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M6.75 0.75C6.75 0.551088 6.67098 0.360322 6.53033 0.21967C6.38968 0.0790176 6.19891 0 6 0C5.80109 0 5.61032 0.0790176 5.46967 0.21967C5.32902 0.360322 5.25 0.551088 5.25 0.75V3H6.75V0.75Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path
                          d="M18.75 0.75C18.75 0.551088 18.671 0.360322 18.5303 0.21967C18.3897 0.0790176 18.1989 0 18 0C17.8011 0 17.6103 0.0790176 17.4697 0.21967C17.329 0.360322 17.25 0.551088 17.25 0.75V3H18.75V0.75Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path d="M8.25 15.75H3.75V19.5H8.25V15.75Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path d="M8.25 10.5H3.75V14.25H8.25V10.5Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path d="M14.25 15.75H9.75V19.5H14.25V15.75Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path d="M14.25 10.5H9.75V14.25H14.25V10.5Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path d="M20.25 10.5H15.75V14.25H20.25V10.5Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path
                          d="M20.25 3.75H3.75C2.95435 3.75 2.19129 4.06607 1.62868 4.62868C1.06607 5.19129 0.75 5.95435 0.75 6.75V20.25C0.75 21.0456 1.06607 21.8087 1.62868 22.3713C2.19129 22.9339 2.95435 23.25 3.75 23.25H20.25C21.0456 23.25 21.8087 22.9339 22.3713 22.3713C22.9339 21.8087 23.25 21.0456 23.25 20.25V6.75C23.25 5.95435 22.9339 5.19129 22.3713 4.62868C21.8087 4.06607 21.0456 3.75 20.25 3.75ZM21.75 20.25C21.75 20.6478 21.592 21.0294 21.3107 21.3107C21.0294 21.592 20.6478 21.75 20.25 21.75H3.75C3.35218 21.75 2.97064 21.592 2.68934 21.3107C2.40804 21.0294 2.25 20.6478 2.25 20.25V8.25H21.75V20.25Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                      </svg>

                    </span>
                  </div>

                </div>
                <div class="">
                  <div class="traveler-box" [ngStyle]="{'height': this.weeklyData.length >0 ? '96px':'156px'}">
                    <div style="text-align: center;">
                      <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M25 13C25 11.3159 24.6455 9.65057 23.9596 8.11245C23.2736 6.57432 22.2716 5.19781 21.0187 4.07243C19.7658 2.94706 18.29 2.09801 16.6873 1.58053C15.0847 1.06304 13.391 0.888703 11.7166 1.06885L12.663 9.86656C13.1028 9.81925 13.5476 9.86504 13.9685 10.0009C14.3894 10.1368 14.7769 10.3598 15.106 10.6554C15.4351 10.9509 15.6982 11.3124 15.8784 11.7164C16.0585 12.1203 16.1516 12.5577 16.1516 13H25Z"
                          fill="#47494F" stroke="white" />
                        <path
                          d="M11.7163 1.06885C9.28655 1.33025 6.99449 2.32729 5.14669 3.92663C3.29889 5.52597 1.98344 7.65136 1.37627 10.0186C0.769099 12.3858 0.89915 14.8819 1.74903 17.1732C2.59891 19.4645 4.1281 21.4417 6.1321 22.8403L11.1962 15.5843C10.6699 15.217 10.2683 14.6977 10.0451 14.096C9.82192 13.4942 9.78777 12.8387 9.94723 12.217C10.1067 11.5953 10.4522 11.0371 10.9374 10.6171C11.4227 10.1971 12.0247 9.93521 12.6628 9.86656L11.7163 1.06885Z"
                          fill="#47494F" stroke="white" />
                        <path
                          d="M6.13208 22.8403C7.93098 24.0959 10.0396 24.8342 12.2288 24.9752C14.418 25.1162 16.6039 24.6543 18.549 23.6399C20.494 22.6254 22.1238 21.0972 23.2611 19.2213C24.3984 17.3455 24.9998 15.1937 24.9998 13H16.1514C16.1514 13.5761 15.9934 14.1412 15.6948 14.6339C15.3961 15.1265 14.968 15.5279 14.4572 15.7943C13.9464 16.0607 13.3723 16.182 12.7974 16.145C12.2224 16.108 11.6687 15.9141 11.1962 15.5843L6.13208 22.8403Z"
                          fill="#47494F" stroke="white" />
                      </svg> <span class="show5">{{'dashboard.Total' | translate }}</span>
                    </div>
                    <div class="row" class="traveler11">
                      <div class="col-12" style="text-align: center;margin-top: 20px;">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M14.8094 5.25148C16.1556 5.25148 17.2469 4.16017 17.2469 2.81398C17.2469 1.46779 16.1556 0.37648 14.8094 0.37648C13.4633 0.37648 12.3719 1.46779 12.3719 2.81398C12.3719 4.16017 13.4633 5.25148 14.8094 5.25148Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M17.625 17.1652L14.787 14.6152L13.9425 17.4622L15.747 18.5025V22.125C15.747 22.4233 15.8655 22.7095 16.0765 22.9205C16.2875 23.1314 16.5736 23.25 16.872 23.25C17.1704 23.25 17.4565 23.1314 17.6675 22.9205C17.8785 22.7095 17.997 22.4233 17.997 22.125V18C17.9969 17.8425 17.9637 17.6869 17.8996 17.5431C17.8355 17.3993 17.742 17.2706 17.625 17.1652Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M9.38921 10.4535L10.5348 10.3492L10.9098 7.99423C10.9384 7.81604 10.984 7.64099 11.046 7.47148L8.48546 8.07598C8.30261 8.11518 8.1324 8.19935 7.99026 8.32088C7.84811 8.4424 7.7385 8.59744 7.67134 8.77198L6.44996 11.9464C6.39331 12.0893 6.36566 12.242 6.36862 12.3957C6.37159 12.5494 6.4051 12.701 6.46722 12.8416C6.52934 12.9822 6.61882 13.1091 6.73045 13.2148C6.84208 13.3205 6.97362 13.4029 7.11742 13.4573C7.26122 13.5116 7.41439 13.5368 7.56803 13.5314C7.72166 13.526 7.87268 13.4901 8.01229 13.4257C8.15189 13.3613 8.2773 13.2698 8.3812 13.1565C8.4851 13.0432 8.56541 12.9104 8.61746 12.7657L8.63096 12.7282L9.38921 10.4535Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M22.4625 9.85348C22.4266 9.71015 22.3629 9.57528 22.275 9.45657C22.187 9.33786 22.0766 9.23764 21.9499 9.16162C21.8232 9.0856 21.6828 9.03528 21.5367 9.01353C21.3905 8.99178 21.2415 8.99903 21.0982 9.03486L18.5021 9.68398L14.9801 6.83398C14.6994 6.60648 14.3637 6.45684 14.0069 6.40009C13.65 6.34334 13.2845 6.38147 12.9471 6.51066C12.6096 6.63985 12.3121 6.85555 12.0844 7.1361C11.8566 7.41665 11.7067 7.75217 11.6497 8.10898L9.39972 22.2412C9.38459 22.3273 9.37507 22.4142 9.37122 22.5015C9.37157 22.7793 9.4747 23.0472 9.66076 23.2535C9.84682 23.4598 10.1026 23.59 10.3789 23.6189C10.6552 23.6479 10.9325 23.5736 11.1573 23.4103C11.3821 23.2471 11.5385 23.0064 11.5965 22.7347L15.2913 10.275L17.7071 11.7461C17.8763 11.8483 18.0641 11.916 18.2596 11.9453C18.4552 11.9747 18.6545 11.9651 18.8463 11.9171L21.6446 11.2174C21.9336 11.1448 22.182 10.9606 22.3353 10.7051C22.4887 10.4496 22.5344 10.1438 22.4625 9.85461V9.85348Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M4.12495 21.375C4.41421 21.3753 4.6971 21.4599 4.93908 21.6184L7.39421 14.2522C7.21132 14.2409 7.03106 14.203 6.85908 14.1397C6.7948 14.1123 6.73219 14.0812 6.67158 14.0464L6.11808 15.7065L4.16808 15.0562C4.02788 15.0095 3.87985 14.9909 3.73244 15.0014C3.58503 15.0119 3.44114 15.0513 3.30898 15.1175C3.17683 15.1836 3.05899 15.2751 2.96221 15.3868C2.86544 15.4985 2.79161 15.6281 2.74495 15.7684L0.96858 21.0326C0.874978 21.3158 0.897405 21.6245 1.03095 21.8912C1.16449 22.1578 1.39827 22.3607 1.68108 22.4554L2.63583 22.7734C2.66089 22.3951 2.82853 22.0404 3.10491 21.7808C3.38128 21.5213 3.74582 21.3763 4.12495 21.375Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M4.125 23.625C4.53921 23.625 4.875 23.2892 4.875 22.875C4.875 22.4608 4.53921 22.125 4.125 22.125C3.71079 22.125 3.375 22.4608 3.375 22.875C3.375 23.2892 3.71079 23.625 4.125 23.625Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        </svg>
                        <div class="showHover"><span class="show22">{{'dashboard.TotalTravelers' | translate }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="">
                  <div class="flight-box" [ngStyle]="{'height': this.weeklyData.length >0 ? '96px':'156px'}">
                    <div style="text-align: center;">
                      <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M20.4999 7H5.53491L3.83191 4.445C3.64591 4.167 3.33391 4 2.99991 4H0.999911C0.691911 4 0.400911 4.142 0.211911 4.385C0.022911 4.628 -0.045089 4.944 0.029911 5.243L2.02991 13.243C2.14191 13.688 2.54091 14 2.99991 14H11.1319L8.16791 18.445C7.96391 18.752 7.94391 19.146 8.11791 19.471C8.29191 19.796 8.63091 20 8.99991 20H12.9999C13.3339 20 13.6459 19.833 13.8319 19.555L17.5349 14H20.4999C22.4299 14 23.9999 12.43 23.9999 10.5C23.9999 8.57 22.4299 7 20.4999 7Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path
                          d="M10.2719 5H16.6169L13.8529 0.479C13.6719 0.181 13.3489 0 12.9999 0H8.99992C8.63892 0 8.30492 0.195 8.12792 0.511C7.95092 0.827 7.95792 1.213 8.14692 1.522L10.2719 5Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                      </svg> <span class="show5">{{'dashboard.Flights' | translate }}</span>
                    </div>
                    <div class="row" style="margin-top: 20px;">
                      <div class="col-auto" class="traveler12" style="margin-left:10px;width:100px; ">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M14.8094 5.25148C16.1556 5.25148 17.2469 4.16017 17.2469 2.81398C17.2469 1.46779 16.1556 0.37648 14.8094 0.37648C13.4633 0.37648 12.3719 1.46779 12.3719 2.81398C12.3719 4.16017 13.4633 5.25148 14.8094 5.25148Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M17.625 17.1652L14.787 14.6152L13.9425 17.4622L15.747 18.5025V22.125C15.747 22.4233 15.8655 22.7095 16.0765 22.9205C16.2875 23.1314 16.5736 23.25 16.872 23.25C17.1704 23.25 17.4565 23.1314 17.6675 22.9205C17.8785 22.7095 17.997 22.4233 17.997 22.125V18C17.9969 17.8425 17.9637 17.6869 17.8996 17.5431C17.8355 17.3993 17.742 17.2706 17.625 17.1652Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M9.38921 10.4535L10.5348 10.3492L10.9098 7.99423C10.9384 7.81604 10.984 7.64099 11.046 7.47148L8.48546 8.07598C8.30261 8.11518 8.1324 8.19935 7.99026 8.32088C7.84811 8.4424 7.7385 8.59744 7.67134 8.77198L6.44996 11.9464C6.39331 12.0893 6.36566 12.242 6.36862 12.3957C6.37159 12.5494 6.4051 12.701 6.46722 12.8416C6.52934 12.9822 6.61882 13.1091 6.73045 13.2148C6.84208 13.3205 6.97362 13.4029 7.11742 13.4573C7.26122 13.5116 7.41439 13.5368 7.56803 13.5314C7.72166 13.526 7.87268 13.4901 8.01229 13.4257C8.15189 13.3613 8.2773 13.2698 8.3812 13.1565C8.4851 13.0432 8.56541 12.9104 8.61746 12.7657L8.63096 12.7282L9.38921 10.4535Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M22.4625 9.85348C22.4266 9.71015 22.3629 9.57528 22.275 9.45657C22.187 9.33786 22.0766 9.23764 21.9499 9.16162C21.8232 9.0856 21.6828 9.03528 21.5367 9.01353C21.3905 8.99178 21.2415 8.99903 21.0982 9.03486L18.5021 9.68398L14.9801 6.83398C14.6994 6.60648 14.3637 6.45684 14.0069 6.40009C13.65 6.34334 13.2845 6.38147 12.9471 6.51066C12.6096 6.63985 12.3121 6.85555 12.0844 7.1361C11.8566 7.41665 11.7067 7.75217 11.6497 8.10898L9.39972 22.2412C9.38459 22.3273 9.37507 22.4142 9.37122 22.5015C9.37157 22.7793 9.4747 23.0472 9.66076 23.2535C9.84682 23.4598 10.1026 23.59 10.3789 23.6189C10.6552 23.6479 10.9325 23.5736 11.1573 23.4103C11.3821 23.2471 11.5385 23.0064 11.5965 22.7347L15.2913 10.275L17.7071 11.7461C17.8763 11.8483 18.0641 11.916 18.2596 11.9453C18.4552 11.9747 18.6545 11.9651 18.8463 11.9171L21.6446 11.2174C21.9336 11.1448 22.182 10.9606 22.3353 10.7051C22.4887 10.4496 22.5344 10.1438 22.4625 9.85461V9.85348Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M4.12495 21.375C4.41421 21.3753 4.6971 21.4599 4.93908 21.6184L7.39421 14.2522C7.21132 14.2409 7.03106 14.203 6.85908 14.1397C6.7948 14.1123 6.73219 14.0812 6.67158 14.0464L6.11808 15.7065L4.16808 15.0562C4.02788 15.0095 3.87985 14.9909 3.73244 15.0014C3.58503 15.0119 3.44114 15.0513 3.30898 15.1175C3.17683 15.1836 3.05899 15.2751 2.96221 15.3868C2.86544 15.4985 2.79161 15.6281 2.74495 15.7684L0.96858 21.0326C0.874978 21.3158 0.897405 21.6245 1.03095 21.8912C1.16449 22.1578 1.39827 22.3607 1.68108 22.4554L2.63583 22.7734C2.66089 22.3951 2.82853 22.0404 3.10491 21.7808C3.38128 21.5213 3.74582 21.3763 4.12495 21.375Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M4.125 23.625C4.53921 23.625 4.875 23.2892 4.875 22.875C4.875 22.4608 4.53921 22.125 4.125 22.125C3.71079 22.125 3.375 22.4608 3.375 22.875C3.375 23.2892 3.71079 23.625 4.125 23.625Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        </svg>
                        <div class="showHover"><span class="show22">{{'dashboard.TotalTravelers' | translate }}</span>
                        </div>
                      </div>
                      <div class="col-auto" class="perc12"
                        style="margin-left:16px;font-size:22px;width:70px;text-align: center;"
                        [ngStyle]="{'color': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}">
                        %
                        <div class="showHover"><span class="show22" style="position:relative;bottom: 4px !important;">%
                            {{'dashboard.Travelers' | translate }}</span></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="">
                  <div class="traveler-box" [ngStyle]="{'height': this.weeklyData.length >0 ? '96px':'156px'}">
                    <div style="text-align: center;"> <svg width="15" height="24" viewBox="0 0 15 24" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M0.609375 17.625V23.25C0.609375 23.3495 0.643945 23.4448 0.705481 23.5152C0.767016 23.5855 0.850476 23.625 0.9375 23.625H6.1875V19.5C6.1875 19.4005 6.22207 19.3052 6.28361 19.2348C6.34514 19.1645 6.4286 19.125 6.51562 19.125H8.48438C8.5714 19.125 8.65486 19.1645 8.71639 19.2348C8.77793 19.3052 8.8125 19.4005 8.8125 19.5V23.625H14.0625C14.1495 23.625 14.233 23.5855 14.2945 23.5152C14.3561 23.4448 14.3906 23.3495 14.3906 23.25V17.625H0.609375Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path
                          d="M14.1419 2.26126L7.57941 0.38626C7.52732 0.370891 7.47268 0.370891 7.42059 0.38626L0.858094 2.26126C0.787077 2.2815 0.724021 2.3283 0.678954 2.39421C0.633886 2.46012 0.609395 2.54136 0.609375 2.62501V16.875H14.3906V2.62501C14.3906 2.54136 14.3661 2.46012 14.321 2.39421C14.276 2.3283 14.2129 2.2815 14.1419 2.26126ZM3.89062 15C3.89062 15.0995 3.85606 15.1948 3.79452 15.2652C3.73298 15.3355 3.64952 15.375 3.5625 15.375H2.57812C2.4911 15.375 2.40764 15.3355 2.34611 15.2652C2.28457 15.1948 2.25 15.0995 2.25 15V13.125C2.25 13.0256 2.28457 12.9302 2.34611 12.8598C2.40764 12.7895 2.4911 12.75 2.57812 12.75H3.5625C3.64952 12.75 3.73298 12.7895 3.79452 12.8598C3.85606 12.9302 3.89062 13.0256 3.89062 13.125V15ZM3.89062 10.875C3.89062 10.9745 3.85606 11.0698 3.79452 11.1402C3.73298 11.2105 3.64952 11.25 3.5625 11.25H2.57812C2.4911 11.25 2.40764 11.2105 2.34611 11.1402C2.28457 11.0698 2.25 10.9745 2.25 10.875V9.00001C2.25 8.90055 2.28457 8.80517 2.34611 8.73484C2.40764 8.66452 2.4911 8.62501 2.57812 8.62501H3.5625C3.64952 8.62501 3.73298 8.66452 3.79452 8.73484C3.85606 8.80517 3.89062 8.90055 3.89062 9.00001V10.875ZM3.89062 6.75001C3.89062 6.84947 3.85606 6.94485 3.79452 7.01517C3.73298 7.0855 3.64952 7.12501 3.5625 7.12501H2.57812C2.4911 7.12501 2.40764 7.0855 2.34611 7.01517C2.28457 6.94485 2.25 6.84947 2.25 6.75001V4.87501C2.25 4.77555 2.28457 4.68017 2.34611 4.60984C2.40764 4.53952 2.4911 4.50001 2.57812 4.50001H3.5625C3.64952 4.50001 3.73298 4.53952 3.79452 4.60984C3.85606 4.68017 3.89062 4.77555 3.89062 4.87501V6.75001ZM6.84375 15C6.84375 15.0995 6.80918 15.1948 6.74764 15.2652C6.68611 15.3355 6.60265 15.375 6.51562 15.375H5.53125C5.44423 15.375 5.36077 15.3355 5.29923 15.2652C5.23769 15.1948 5.20312 15.0995 5.20312 15V13.125C5.20312 13.0256 5.23769 12.9302 5.29923 12.8598C5.36077 12.7895 5.44423 12.75 5.53125 12.75H6.51562C6.60265 12.75 6.68611 12.7895 6.74764 12.8598C6.80918 12.9302 6.84375 13.0256 6.84375 13.125V15ZM6.84375 10.875C6.84375 10.9745 6.80918 11.0698 6.74764 11.1402C6.68611 11.2105 6.60265 11.25 6.51562 11.25H5.53125C5.44423 11.25 5.36077 11.2105 5.29923 11.1402C5.23769 11.0698 5.20312 10.9745 5.20312 10.875V9.00001C5.20312 8.90055 5.23769 8.80517 5.29923 8.73484C5.36077 8.66452 5.44423 8.62501 5.53125 8.62501H6.51562C6.60265 8.62501 6.68611 8.66452 6.74764 8.73484C6.80918 8.80517 6.84375 8.90055 6.84375 9.00001V10.875ZM6.84375 6.75001C6.84375 6.84947 6.80918 6.94485 6.74764 7.01517C6.68611 7.0855 6.60265 7.12501 6.51562 7.12501H5.53125C5.44423 7.12501 5.36077 7.0855 5.29923 7.01517C5.23769 6.94485 5.20312 6.84947 5.20312 6.75001V4.87501C5.20312 4.77555 5.23769 4.68017 5.29923 4.60984C5.36077 4.53952 5.44423 4.50001 5.53125 4.50001H6.51562C6.60265 4.50001 6.68611 4.53952 6.74764 4.60984C6.80918 4.68017 6.84375 4.77555 6.84375 4.87501V6.75001ZM9.79688 15C9.79688 15.0995 9.76231 15.1948 9.70077 15.2652C9.63923 15.3355 9.55577 15.375 9.46875 15.375H8.48438C8.39735 15.375 8.31389 15.3355 8.25236 15.2652C8.19082 15.1948 8.15625 15.0995 8.15625 15V13.125C8.15625 13.0256 8.19082 12.9302 8.25236 12.8598C8.31389 12.7895 8.39735 12.75 8.48438 12.75H9.46875C9.55577 12.75 9.63923 12.7895 9.70077 12.8598C9.76231 12.9302 9.79688 13.0256 9.79688 13.125V15ZM9.79688 10.875C9.79688 10.9745 9.76231 11.0698 9.70077 11.1402C9.63923 11.2105 9.55577 11.25 9.46875 11.25H8.48438C8.39735 11.25 8.31389 11.2105 8.25236 11.1402C8.19082 11.0698 8.15625 10.9745 8.15625 10.875V9.00001C8.15625 8.90055 8.19082 8.80517 8.25236 8.73484C8.31389 8.66452 8.39735 8.62501 8.48438 8.62501H9.46875C9.55577 8.62501 9.63923 8.66452 9.70077 8.73484C9.76231 8.80517 9.79688 8.90055 9.79688 9.00001V10.875ZM9.79688 6.75001C9.79688 6.84947 9.76231 6.94485 9.70077 7.01517C9.63923 7.0855 9.55577 7.12501 9.46875 7.12501H8.48438C8.39735 7.12501 8.31389 7.0855 8.25236 7.01517C8.19082 6.94485 8.15625 6.84947 8.15625 6.75001V4.87501C8.15625 4.77555 8.19082 4.68017 8.25236 4.60984C8.31389 4.53952 8.39735 4.50001 8.48438 4.50001H9.46875C9.55577 4.50001 9.63923 4.53952 9.70077 4.60984C9.76231 4.68017 9.79688 4.77555 9.79688 4.87501V6.75001ZM12.75 15C12.75 15.0995 12.7154 15.1948 12.6539 15.2652C12.5924 15.3355 12.5089 15.375 12.4219 15.375H11.4375C11.3505 15.375 11.267 15.3355 11.2055 15.2652C11.1439 15.1948 11.1094 15.0995 11.1094 15V13.125C11.1094 13.0256 11.1439 12.9302 11.2055 12.8598C11.267 12.7895 11.3505 12.75 11.4375 12.75H12.4219C12.5089 12.75 12.5924 12.7895 12.6539 12.8598C12.7154 12.9302 12.75 13.0256 12.75 13.125V15ZM12.75 10.875C12.75 10.9745 12.7154 11.0698 12.6539 11.1402C12.5924 11.2105 12.5089 11.25 12.4219 11.25H11.4375C11.3505 11.25 11.267 11.2105 11.2055 11.1402C11.1439 11.0698 11.1094 10.9745 11.1094 10.875V9.00001C11.1094 8.90055 11.1439 8.80517 11.2055 8.73484C11.267 8.66452 11.3505 8.62501 11.4375 8.62501H12.4219C12.5089 8.62501 12.5924 8.66452 12.6539 8.73484C12.7154 8.80517 12.75 8.90055 12.75 9.00001V10.875ZM12.75 6.75001C12.75 6.84947 12.7154 6.94485 12.6539 7.01517C12.5924 7.0855 12.5089 7.12501 12.4219 7.12501H11.4375C11.3505 7.12501 11.267 7.0855 11.2055 7.01517C11.1439 6.94485 11.1094 6.84947 11.1094 6.75001V4.87501C11.1094 4.77555 11.1439 4.68017 11.2055 4.60984C11.267 4.53952 11.3505 4.50001 11.4375 4.50001H12.4219C12.5089 4.50001 12.5924 4.53952 12.6539 4.60984C12.7154 4.68017 12.75 4.77555 12.75 4.87501V6.75001Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                      </svg> <span class="show5">{{'dashboard.Hotels' | translate }}</span></div>
                    <div class="row" style="margin-top: 20px;">
                      <div class="col-auto" class="traveler13" style="margin-left:10px;width:100px; ">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M14.8094 5.25148C16.1556 5.25148 17.2469 4.16017 17.2469 2.81398C17.2469 1.46779 16.1556 0.37648 14.8094 0.37648C13.4633 0.37648 12.3719 1.46779 12.3719 2.81398C12.3719 4.16017 13.4633 5.25148 14.8094 5.25148Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M17.625 17.1652L14.787 14.6152L13.9425 17.4622L15.747 18.5025V22.125C15.747 22.4233 15.8655 22.7095 16.0765 22.9205C16.2875 23.1314 16.5736 23.25 16.872 23.25C17.1704 23.25 17.4565 23.1314 17.6675 22.9205C17.8785 22.7095 17.997 22.4233 17.997 22.125V18C17.9969 17.8425 17.9637 17.6869 17.8996 17.5431C17.8355 17.3993 17.742 17.2706 17.625 17.1652Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M9.38921 10.4535L10.5348 10.3492L10.9098 7.99423C10.9384 7.81604 10.984 7.64099 11.046 7.47148L8.48546 8.07598C8.30261 8.11518 8.1324 8.19935 7.99026 8.32088C7.84811 8.4424 7.7385 8.59744 7.67134 8.77198L6.44996 11.9464C6.39331 12.0893 6.36566 12.242 6.36862 12.3957C6.37159 12.5494 6.4051 12.701 6.46722 12.8416C6.52934 12.9822 6.61882 13.1091 6.73045 13.2148C6.84208 13.3205 6.97362 13.4029 7.11742 13.4573C7.26122 13.5116 7.41439 13.5368 7.56803 13.5314C7.72166 13.526 7.87268 13.4901 8.01229 13.4257C8.15189 13.3613 8.2773 13.2698 8.3812 13.1565C8.4851 13.0432 8.56541 12.9104 8.61746 12.7657L8.63096 12.7282L9.38921 10.4535Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M22.4625 9.85348C22.4266 9.71015 22.3629 9.57528 22.275 9.45657C22.187 9.33786 22.0766 9.23764 21.9499 9.16162C21.8232 9.0856 21.6828 9.03528 21.5367 9.01353C21.3905 8.99178 21.2415 8.99903 21.0982 9.03486L18.5021 9.68398L14.9801 6.83398C14.6994 6.60648 14.3637 6.45684 14.0069 6.40009C13.65 6.34334 13.2845 6.38147 12.9471 6.51066C12.6096 6.63985 12.3121 6.85555 12.0844 7.1361C11.8566 7.41665 11.7067 7.75217 11.6497 8.10898L9.39972 22.2412C9.38459 22.3273 9.37507 22.4142 9.37122 22.5015C9.37157 22.7793 9.4747 23.0472 9.66076 23.2535C9.84682 23.4598 10.1026 23.59 10.3789 23.6189C10.6552 23.6479 10.9325 23.5736 11.1573 23.4103C11.3821 23.2471 11.5385 23.0064 11.5965 22.7347L15.2913 10.275L17.7071 11.7461C17.8763 11.8483 18.0641 11.916 18.2596 11.9453C18.4552 11.9747 18.6545 11.9651 18.8463 11.9171L21.6446 11.2174C21.9336 11.1448 22.182 10.9606 22.3353 10.7051C22.4887 10.4496 22.5344 10.1438 22.4625 9.85461V9.85348Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M4.12495 21.375C4.41421 21.3753 4.6971 21.4599 4.93908 21.6184L7.39421 14.2522C7.21132 14.2409 7.03106 14.203 6.85908 14.1397C6.7948 14.1123 6.73219 14.0812 6.67158 14.0464L6.11808 15.7065L4.16808 15.0562C4.02788 15.0095 3.87985 14.9909 3.73244 15.0014C3.58503 15.0119 3.44114 15.0513 3.30898 15.1175C3.17683 15.1836 3.05899 15.2751 2.96221 15.3868C2.86544 15.4985 2.79161 15.6281 2.74495 15.7684L0.96858 21.0326C0.874978 21.3158 0.897405 21.6245 1.03095 21.8912C1.16449 22.1578 1.39827 22.3607 1.68108 22.4554L2.63583 22.7734C2.66089 22.3951 2.82853 22.0404 3.10491 21.7808C3.38128 21.5213 3.74582 21.3763 4.12495 21.375Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M4.125 23.625C4.53921 23.625 4.875 23.2892 4.875 22.875C4.875 22.4608 4.53921 22.125 4.125 22.125C3.71079 22.125 3.375 22.4608 3.375 22.875C3.375 23.2892 3.71079 23.625 4.125 23.625Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        </svg>
                        <div class="showHover"><span class="show22">{{'dashboard.TotalTravelers' | translate }}</span>
                        </div>
                      </div>
                      <div class="col-auto" class="perc14"
                        style="margin-left:16px;font-size:22px;width:70px;text-align: center;"
                        [ngStyle]="{'color': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}">
                        %
                        <div class="showHover"><span class="show22" style="position:relative;bottom: 4px !important;">%
                            {{'dashboard.Travelers' | translate }}</span></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="">
                  <div class="flight-box" [ngStyle]="{'height': this.weeklyData.length >0 ? '96px':'156px'}">
                    <div style="text-align: center;">
                      <svg width="37" height="19" viewBox="0 0 37 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M0 14.2188V14.7969C0 14.9502 0.0609094 15.0973 0.169329 15.2057C0.277748 15.3141 0.424797 15.375 0.578125 15.375H4.04688C4.04688 14.7804 4.16213 14.1914 4.38623 13.6406H0.578125C0.424797 13.6406 0.277748 13.7015 0.169329 13.81C0.0609094 13.9184 0 14.0654 0 14.2188H0Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path
                          d="M8.67188 18.8437C7.98582 18.8437 7.31518 18.6403 6.74474 18.2592C6.17431 17.878 5.72971 17.3363 5.46717 16.7024C5.20463 16.0686 5.13594 15.3712 5.26978 14.6983C5.40362 14.0254 5.73399 13.4073 6.2191 12.9222C6.70421 12.4371 7.32229 12.1067 7.99516 11.9729C8.66803 11.8391 9.36548 11.9078 9.99931 12.1703C10.6331 12.4328 11.1749 12.8774 11.556 13.4479C11.9372 14.0183 12.1406 14.6889 12.1406 15.375C12.1397 16.2947 11.774 17.1764 11.1236 17.8268C10.4733 18.4771 9.59156 18.8428 8.67188 18.8437ZM8.67188 13.0625C8.21451 13.0625 7.76741 13.1981 7.38712 13.4522C7.00683 13.7063 6.71043 14.0675 6.5354 14.49C6.36038 14.9126 6.31458 15.3776 6.40381 15.8261C6.49304 16.2747 6.71328 16.6868 7.03669 17.0102C7.3601 17.3336 7.77215 17.5538 8.22073 17.6431C8.66931 17.7323 9.13428 17.6865 9.55683 17.5115C9.97939 17.3364 10.3405 17.04 10.5946 16.6598C10.8488 16.2795 10.9844 15.8324 10.9844 15.375C10.9844 14.7617 10.7407 14.1735 10.3071 13.7398C9.87338 13.3061 9.28519 13.0625 8.67188 13.0625Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path
                          d="M29.4844 18.8437C28.7983 18.8437 28.1277 18.6403 27.5572 18.2592C26.9868 17.878 26.5422 17.3363 26.2797 16.7024C26.0171 16.0686 25.9484 15.3712 26.0823 14.6983C26.2161 14.0254 26.5465 13.4073 27.0316 12.9222C27.5167 12.4371 28.1348 12.1067 28.8077 11.9729C29.4805 11.8391 30.178 11.9078 30.8118 12.1703C31.4456 12.4328 31.9874 12.8774 32.3685 13.4479C32.7497 14.0183 32.9531 14.6889 32.9531 15.375C32.9522 16.2947 32.5865 17.1764 31.9361 17.8268C31.2858 18.4771 30.4041 18.8428 29.4844 18.8437ZM29.4844 13.0625C29.027 13.0625 28.5799 13.1981 28.1996 13.4522C27.8193 13.7063 27.5229 14.0675 27.3479 14.49C27.1729 14.9126 27.1271 15.3776 27.2163 15.8261C27.3055 16.2747 27.5258 16.6868 27.8492 17.0102C28.1726 17.3336 28.5847 17.5538 29.0332 17.6431C29.4818 17.7323 29.9468 17.6865 30.3693 17.5115C30.7919 17.3364 31.1531 17.04 31.4072 16.6598C31.6613 16.2795 31.7969 15.8324 31.7969 15.375C31.7969 14.7617 31.5532 14.1735 31.1196 13.7398C30.6859 13.3061 30.0977 13.0625 29.4844 13.0625Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path
                          d="M36.4219 13.6406H33.77C33.9941 14.1914 34.1094 14.7804 34.1094 15.375H36.4219C36.5752 15.375 36.7223 15.3141 36.8307 15.2057C36.9391 15.0973 37 14.9502 37 14.7969V14.2188C37 14.0654 36.9391 13.9184 36.8307 13.81C36.7223 13.7015 36.5752 13.6406 36.4219 13.6406Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        <path
                          d="M35.5079 9.51108C35.4171 8.88792 35.126 8.31123 34.6786 7.86808C34.2312 7.42494 33.6517 7.13941 33.0277 7.05463L26.9984 6.25045L23.6222 0.624144C23.5418 0.495701 23.4142 0.403946 23.2669 0.368627C23.1195 0.333307 22.9642 0.357246 22.8343 0.435292C22.7045 0.513338 22.6104 0.639251 22.5724 0.785936C22.5345 0.932621 22.5556 1.08835 22.6313 1.21961L25.5728 6.125H15.0312C14.4179 6.125 13.8297 5.88136 13.3961 5.44769C12.9624 5.01401 12.7188 4.42582 12.7188 3.8125V2.94532C12.7188 2.56199 12.5665 2.19437 12.2954 1.92332C12.0244 1.65228 11.6568 1.5 11.2734 1.5C10.8901 1.5 10.5225 1.65228 10.2514 1.92332C9.9804 2.19437 9.82812 2.56199 9.82812 2.94532V4.98032C9.80094 4.97638 9.7735 4.97444 9.74603 4.97453L3.63814 5.8475C2.94928 5.9459 2.31903 6.28948 1.86312 6.81517C1.4072 7.34086 1.15622 8.01337 1.15625 8.70922V12.7289H4.88169C5.44529 11.923 6.25113 11.3177 7.18218 11.0009C8.11323 10.6841 9.12102 10.6724 10.0592 10.9674C10.9974 11.2624 11.8171 11.8488 12.3993 12.6414C12.9815 13.434 13.2959 14.3915 13.2969 15.375H24.8594C24.8601 14.4175 25.158 13.4838 25.712 12.7028C26.266 11.9218 27.0487 11.332 27.9521 11.0148C28.8556 10.6976 29.8352 10.6686 30.7558 10.9318C31.6765 11.195 32.4927 11.7375 33.0919 12.4844H35.6975L35.5079 9.51108Z"
                          [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                      </svg> <span class="show5">{{'dashboard.Cars' | translate }}</span>
                    </div>
                    <div class="row" style="margin-top: 20px;">
                      <div class="col-auto" class="traveler14" style="margin-left:10px;width:100px; ">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M14.8094 5.25148C16.1556 5.25148 17.2469 4.16017 17.2469 2.81398C17.2469 1.46779 16.1556 0.37648 14.8094 0.37648C13.4633 0.37648 12.3719 1.46779 12.3719 2.81398C12.3719 4.16017 13.4633 5.25148 14.8094 5.25148Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M17.625 17.1652L14.787 14.6152L13.9425 17.4622L15.747 18.5025V22.125C15.747 22.4233 15.8655 22.7095 16.0765 22.9205C16.2875 23.1314 16.5736 23.25 16.872 23.25C17.1704 23.25 17.4565 23.1314 17.6675 22.9205C17.8785 22.7095 17.997 22.4233 17.997 22.125V18C17.9969 17.8425 17.9637 17.6869 17.8996 17.5431C17.8355 17.3993 17.742 17.2706 17.625 17.1652Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M9.38921 10.4535L10.5348 10.3492L10.9098 7.99423C10.9384 7.81604 10.984 7.64099 11.046 7.47148L8.48546 8.07598C8.30261 8.11518 8.1324 8.19935 7.99026 8.32088C7.84811 8.4424 7.7385 8.59744 7.67134 8.77198L6.44996 11.9464C6.39331 12.0893 6.36566 12.242 6.36862 12.3957C6.37159 12.5494 6.4051 12.701 6.46722 12.8416C6.52934 12.9822 6.61882 13.1091 6.73045 13.2148C6.84208 13.3205 6.97362 13.4029 7.11742 13.4573C7.26122 13.5116 7.41439 13.5368 7.56803 13.5314C7.72166 13.526 7.87268 13.4901 8.01229 13.4257C8.15189 13.3613 8.2773 13.2698 8.3812 13.1565C8.4851 13.0432 8.56541 12.9104 8.61746 12.7657L8.63096 12.7282L9.38921 10.4535Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M22.4625 9.85348C22.4266 9.71015 22.3629 9.57528 22.275 9.45657C22.187 9.33786 22.0766 9.23764 21.9499 9.16162C21.8232 9.0856 21.6828 9.03528 21.5367 9.01353C21.3905 8.99178 21.2415 8.99903 21.0982 9.03486L18.5021 9.68398L14.9801 6.83398C14.6994 6.60648 14.3637 6.45684 14.0069 6.40009C13.65 6.34334 13.2845 6.38147 12.9471 6.51066C12.6096 6.63985 12.3121 6.85555 12.0844 7.1361C11.8566 7.41665 11.7067 7.75217 11.6497 8.10898L9.39972 22.2412C9.38459 22.3273 9.37507 22.4142 9.37122 22.5015C9.37157 22.7793 9.4747 23.0472 9.66076 23.2535C9.84682 23.4598 10.1026 23.59 10.3789 23.6189C10.6552 23.6479 10.9325 23.5736 11.1573 23.4103C11.3821 23.2471 11.5385 23.0064 11.5965 22.7347L15.2913 10.275L17.7071 11.7461C17.8763 11.8483 18.0641 11.916 18.2596 11.9453C18.4552 11.9747 18.6545 11.9651 18.8463 11.9171L21.6446 11.2174C21.9336 11.1448 22.182 10.9606 22.3353 10.7051C22.4887 10.4496 22.5344 10.1438 22.4625 9.85461V9.85348Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M4.12495 21.375C4.41421 21.3753 4.6971 21.4599 4.93908 21.6184L7.39421 14.2522C7.21132 14.2409 7.03106 14.203 6.85908 14.1397C6.7948 14.1123 6.73219 14.0812 6.67158 14.0464L6.11808 15.7065L4.16808 15.0562C4.02788 15.0095 3.87985 14.9909 3.73244 15.0014C3.58503 15.0119 3.44114 15.0513 3.30898 15.1175C3.17683 15.1836 3.05899 15.2751 2.96221 15.3868C2.86544 15.4985 2.79161 15.6281 2.74495 15.7684L0.96858 21.0326C0.874978 21.3158 0.897405 21.6245 1.03095 21.8912C1.16449 22.1578 1.39827 22.3607 1.68108 22.4554L2.63583 22.7734C2.66089 22.3951 2.82853 22.0404 3.10491 21.7808C3.38128 21.5213 3.74582 21.3763 4.12495 21.375Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                          <path
                            d="M4.125 23.625C4.53921 23.625 4.875 23.2892 4.875 22.875C4.875 22.4608 4.53921 22.125 4.125 22.125C3.71079 22.125 3.375 22.4608 3.375 22.875C3.375 23.2892 3.71079 23.625 4.125 23.625Z"
                            [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                        </svg>
                        <div class="showHover"><span class="show22">{{'dashboard.TotalTravelers' | translate }}</span>
                        </div>
                      </div>
                      <div class="col-auto" class="perc13"
                        style="margin-left:16px;font-size:22px;width:70px;text-align: center;"
                        [ngStyle]="{'color': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}">
                        %
                        <div class="showHover" style="right:15px !important;"><span class="show22"
                            style="position:relative;bottom: 4px !important;">% {{'dashboard.Travelers' | translate
                            }}</span></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="this.weeklyData.length > 5" class="drop-arrow col-auto">
                <div class="">
                  <svg onclick="openUniqueBox(this);" class="" style="cursor:pointer;" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M23.5 12C23.5 18.3513 18.3513 23.5 12 23.5C5.64873 23.5 0.5 18.3513 0.5 12C0.5 5.64873 5.64873 0.5 12 0.5C18.3513 0.5 23.5 5.64873 23.5 12Z"
                      fill="white" stroke="#8936F3" />
                  </svg>

                  <svg class="userdownArrow" onclick="openUniqueBox(this);" style="cursor:pointer;" width="15"
                    height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M6.31799 8.43958L0.439309 2.5609C-0.146477 1.97512 -0.146477 1.02537 0.439309 0.439584C1.0251 -0.146202 1.97484 -0.146202 2.56063 0.439585L7.37865 5.25761L12.1967 0.439586C12.7825 -0.1462 13.7322 -0.1462 14.318 0.439586C14.9038 1.02537 14.9038 1.97512 14.318 2.56091L8.43931 8.43959C7.85352 9.02537 6.90377 9.02537 6.31799 8.43958Z"
                      fill="#8936F3" />
                  </svg>
                </div>
              </div>
              <div *ngIf="this.weeklyData.length > 0" id="travelerDetails"
                style="overflow: auto;height:170px;overflow-x: hidden;width:1140px;">
                <div class="row" *ngFor="let item of this.weeklyData;let i=index ;even as isEven" style="height:34px;"
                  [ngStyle]="changeUniqueStyle(i)">
                  <div class="flight-box1-bg">
                    <div class="{{ isEven? 'date-box':'date-box date-box-transparent'}}">
                      <span class="" style="color: #656566;font-size:16px;">{{item.week}}</span>
                    </div>

                  </div>
                  <div class="traveler-box1-bg">
                    <div class="{{ isEven? 'traveler-box1':'traveler-box1 date-box-transparent'}}"
                      style="text-align: center !important;">
                      <span style="color: #656566;font-size:16px;">{{item.totalTraveler}}</span>
                    </div>
                  </div>
                  <div class="flight-box1-bg">
                    <div class="{{ isEven? 'flight-box1':'flight-box1 date-box-transparent'}}">
                      <div class="row" style="margin-top: 0px;">
                        <div class="col-auto" style="margin-left:10px;width:100px; ">
                          <span style="color: #656566;font-size:16px;">{{item.filghtTra}}</span>
                        </div>
                        <div class="col-auto" style="margin-left:15px;width:100px;color: #AEAEAE;text-align: center;">
                          <span style="color: #656566;font-size:16px;">{{item.flightPerc}}% </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="traveler-box1-bg">
                    <div class="{{ isEven? 'traveler-box1':'traveler-box1 date-box-transparent'}}">
                      <div class="row" style="margin-top: 0px;">
                        <div class="col-auto" style="margin-left:10px;width:100px; ">
                          <span style="color: #656566;font-size:16px;"> {{item.hotelTra}} </span>
                        </div>
                        <div class="col-auto"
                          style="margin-left:15px;color: #656566;font-size:16px;width:100px;text-align: center;">
                          {{item.hotelPerc}}%
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="flight-box1-bg">
                    <div class="{{ isEven? 'flight-box1':'flight-box1 date-box-transparent'}}">
                      <div class="row" style="margin-top: 0px;">
                        <div class="col-auto" style="margin-left:10px;width:100px; ">
                          <span style="color: #656566;font-size:16px;"> {{item.carTra}}</span>
                        </div>
                        <div class="col-auto"
                          style="margin-left:15px;color: #656566;font-size:16px;width:100px;text-align: center;">
                          {{item.carPerc}}%
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div *ngIf="isMobile1" class="card-div shadow">
  <div class="card-div-inner">
    <div class="topHeading">
      <span class="topHeadingMiddle">{{'dashboardWrapper.Dashboard' | translate }}</span>
    </div>
    <div *ngIf="isDutyOfCareEnabled()" class="divideline">
      <div>
        <div class="subHeading">{{'activeTraveler.ActiveTravelers' | translate }}</div>
        <div class="underline2"></div>
      </div>
    </div>
    <div *ngIf="isDutyOfCareEnabled()" class="map-box">
      <google-map
      #map
      [center]="{ lat: 41.399115, lng: 2.160962 }"
      [zoom]="zoom"
      [options]="mapOptions"
      class="map-class"
      height="400px"
      width="100%"
      (mapReady)="mapReadyCallback($event)"
      (boundsChanged)="boundsChangedCallback($event)"
      (zoomChanged)="zoomChanged($event)"
    >
      <map-marker
        *ngFor="let data of activeTravellersList; let i = index"
        [position]="{ lat: data.latitude, lng: data.longitude }"
        [zIndex]="data.markerClicked ? 100 : i"
        [icon]="markerImage"
      >
      </map-marker>
    </google-map>
    </div>
    <div *ngIf="isDutyOfCareEnabled()" class="first-box">
      <div class="tab11" style="width:99vw;">
        <div class="top-strip" style="margin-bottom: 10px !important;">
          <ul>
            <li (click)="getData('today')">
              <span class="{{this.selectDateRange ==='today' ? 'select1':'unselect1'}}"> {{'activeTraveler.Today' |
                translate }}<div *ngIf="this.selectDateRange ==='today'" class="underline"></div></span>
            </li>
            <li (click)="getData('7Days')">
              <span class="{{this.selectDateRange ==='7Days' ? 'select1':'unselect1'}}"> {{'activeTraveler.NEXT7DAYS' |
                translate }}<div *ngIf="this.selectDateRange ==='7Days'" class="underline"></div></span>
            </li>
          </ul>
          <span class="alltravelers" (click)="routeToActiveTravellers('activeTravellers')"
            style="position: relative;top: 4px;"> {{'dashboard.SeeAllTravelers' | translate }}<img
              style="margin-left:10px;" src="assets/images/Line11.png"></span>
        </div>
        <div *ngIf="this.activeTravellersList.length==0 && getActiveTraveler" class="nodata">
          {{this.resultErrorMessage | translate}}
        </div>
        <div *ngIf="!getActiveTraveler" class="col-12" style="text-align: center;padding-top:30px;">
          <app-loader *ngIf="!getActiveTraveler" [spinnerStyle]="true"></app-loader>
        </div>
        <div *ngIf="this.activeTravellersList.length > 0"
          style="padding-left: 20px;margin-top: 20px; overflow: auto;height:170px;width:370px">
          <div *ngFor="let item of this.activeTravellersList; let i = index"
            style="display: inline-flex;text-align: center;padding-left: 7px !important;margin-bottom: 0px !important;">
            <div class="travelerDetailsBox" style="padding-left: 7px !important;"
              [ngStyle]="{'margin-bottom': (i===this.activeTravellersList.length -1) ? '20px':'none'}"
              (click)="markerClick($event,item,i)">
              <div class="name11">{{ item.name}}</div>
              <div class="name11">{{ item.destinations }}</div>
              <div class="name11"> {{ getDisplayDate(item.departure,'dd MMM',item.type)}} - {{
                getDisplayDate(item.return,'dd MMM',item.type)}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="divideline">
      <div>
        <div class="subHeading">{{'dashboard.PendingApprovals' | translate }}</div>
        <div class="underline2"></div>
      </div>

    </div>
    <div class="approval-box">
      <span class="alltravelers" (click)="routeToApprovals('approvals','pending',false)"
        style="float: right;margin-top: 18px;cursor:pointer;margin-right:10px !important;">{{'dashboard.ManageAll' |
        translate }}<img style="margin-left:10px;" src="assets/images/Line11.png"></span>
      <div *ngIf="this.pendingApproval.length==0" class="nodata">
        {{'dashboard.Therearenopendingapprovalsatthistime.' | translate }}
      </div>
      <div *ngIf="this.pendingApproval.length > 0"
        style="padding-left: 20px;margin-top: 20px;overflow: auto;height:170px;width:380px;overflow-x: hidden;">
        <div *ngFor="let item of this.pendingApproval; let i = index"
          style="padding-left: 7px !important;margin-bottom: 0px !important;">
          <div *ngIf="hideAfterExpire(item.expiryTime,i)"
            class="{{ selectApprovalIndex===i ?'pendingtravelerDetailsBox1':'pendingtravelerDetailsBox'}}"
            style="padding-left: 17px !important;"
            [ngStyle]="{'margin-bottom': (i===this.pendingApproval.length -1) ? '0px':'none'}"
            (click)="selectApproval(i)">
            <div class="{{ selectApprovalIndex===i ?'name1111':'name111'}}">{{ item.name}}</div>
            <div *ngIf="selectApprovalIndex===i" class="" style="color: #fff;font-size: 12px;white-space: nowrap;
                              padding-top: 2px; width: calc(100% - 140px);text-align: right;padding-right: 35px;">
              {{'dashboard.expiresin' | translate }} <span
                *ngIf="getTimeDiffernece(item.expiryTime)?.hrs > 0">{{getTimeDiffernece(item.expiryTime)?.hrs + 'h '
                }}</span> {{ getTimeDiffernece(item.expiryTime)?.mins + 'm'}}</div>
          </div>

          <div *ngIf="selectApprovalIndex===i && hideAfterExpire(item.expiryTime,i)" class="name111"
            style="font-size:12px !important;float:right;width:145px;color:#757575; !important;margin-right:25px;">
            {{'dashboard.expiresat' | translate }} {{gettime(item.expiryTime)}}</div>
        </div>
      </div>
    </div>
    <div class="divideline">
      <div>
        <div class="subHeading">{{'dashboard.Overview' | translate }}</div>
        <div class="underline1"></div>
      </div>
    </div>

    <div class="row">
      <div class="col-12">
        <div class="left-panel">
          <label class="show"> {{'dashboard.Period' | translate }}</label>
          <div style="margin-top: 2px;margin-left:5px;">
            <div class="filter custom-selectbox">
              <div class="input-box" style="margin-right:5px;position: relative; top: 3px;">
                <input class="input" id="daterangeSelection"
                  (click)="openNgxModal('daterangeSelection',chartDatePicker);chartDatePicker.show()" readonly=""><span
                  class="dateShow"
                  (click)="openNgxModal('daterangeSelection',chartDatePicker);chartDatePicker.show()">{{daterangepickerModel[0]
                  | date : 'dd MMM yyyy'}} - {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span><svg
                  class="down-arrow" (click)="openNgxModal('daterangeSelection',chartDatePicker);chartDatePicker.show()"
                  width="15" height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>

              </div>
              <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'daterangeSelection')" [hideDelay]="0"
                (onClose)="handleModalEvents('onClose', 'daterangeSelection')"
                (onDismiss)="handleModalEvents('onDismiss', ' daterangeSelection')" [closable]="false"
                #daterangeSelection identifier="daterangeSelection">
                <div class="modal-container flight-modal-container filter-modal1 modalAirportFilterInfo"
                  [ngStyle]="changeStyle()" (click)="$event.stopPropagation();">
                  <div class="modal-header" style="background-color: #fff !important;padding-top:10px !important;">
                    <div class="tab-list top-strip" style="padding-top:15px !important;">

                      <ul style="overflow: visible !important;">
                        <li class="{{ viewMode1 == 'tab12' ? 'select1':'unselect1'}}"
                          [class.active]="viewMode1 == 'tab12'" rel="tab11" (click)="presetsTabClicked()"
                          style="margin-left:0px !important;margin-right:0px !important;">{{ 'report.Presets' |
                          translate }}
                          <div *ngIf="viewMode1 == 'tab12'" class="underline"></div>
                        </li>
                        <li class="{{ viewMode1 == 'tab11' ? 'select1':'unselect1'}}"
                          [class.active]="viewMode1 == 'tab11'" rel="tab14" (click)="customTabClicked()">{{
                          'report.Custom' | translate }}
                          <div *ngIf="viewMode1 == 'tab11'" class="underline"></div>
                        </li>
                      </ul>
                    </div>
                    <input class="input" [hidden]="viewMode1=='tab12'"
                      style="width:100% !important;position: relative;top:8px;" readonly=""><span class="dateShow1"
                      [hidden]="viewMode1=='tab12'" style="top:24px !important;">{{daterangepickerModel[0] | date : 'dd
                      MMM yyyy'}} - {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span>
                  </div>
                  <hr>
                  <div [hidden]="viewMode1=='tab12'" style="position: relative;top: -30px;">
                    <input class="input" bsDaterangepicker #chartDatePicker="bsDaterangepicker"
                      style="width:100% !important;visibility: hidden;" [(ngModel)]="daterangepickerModel"
                      (bsValueChange)="setStartDate($event,false)" [outsideClick]="true" [maxDate]="maximumDate1"
                      [bsConfig]="{showWeekNumbers: false , showPreviousMonth: true}"
                      (onShown)="onShowPicker($event, chartDatePicker)" (onHidden)="onHidePicker()" container=""
                      readonly />
                  </div>
                  <div class="modal-body" [ngStyle]="changeStyle1()">
                    <div [hidden]="viewMode1=='tab11'">
                      <div *ngFor="let item of this.dateOptions;let i=index"
                        style="width: auto;min-height: 15px;display:flex;line-height: 3em;min-height: 3em;">
                        <div style="font-size:14px;color:#5f6368;padding-left:22px;margin-bottom:7px;cursor: pointer;"
                          (click)="showDateChanged(item.id,true)">{{item.name | translate}}</div>
                      </div>

                    </div>
                  </div>
                </div>
              </ngx-smart-modal>
            </div>

          </div>
          <label class="show"> {{'dashboard.Compareto' | translate }}</label>
          <div style="margin-top: 2px;margin-left:5px;">
            <div class="filter custom-selectbox">
              <div class="input-box" style="margin-right:5px;position: relative; top: 3px;">
                <input class="input" id="daterangeSelection1" (click)="openNgxModal1('daterangeSelection1')"
                  readonly=""><span class="dateShow"
                  (click)="openNgxModal1('daterangeSelection1')">{{selectPreviousDate1 | translate}}</span>
                <svg class="down-arrow" (click)="openNgxModal1('daterangeSelection1')" width="15" height="9"
                  viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>

              </div>
              <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'daterangeSelection1')" [hideDelay]="0"
                (onClose)="handleModalEvents('onClose', 'daterangeSelection1')"
                (onDismiss)="handleModalEvents('onDismiss', ' daterangeSelection1')" [closable]="false"
                #daterangeSelection1 identifier="daterangeSelection1">
                <div class="modal-container flight-modal-container filter-modal2 modalAirportFilterInfo"
                  style="min-width: 99vw;" (click)="$event.stopPropagation();">
                  <div class="modal-body" style="">
                    <div class="presetSelect" *ngFor="let item of this.previousOptions;let i=index"
                      [ngStyle]="getPreviousDropdownStyle(item)" (click)="selectPreviousDateRange(item)">
                      <div class="row" style="padding-right: 8px;">
                        <div class="col-4" style="font-size:18px;color:#000000;white-space: nowrap;">{{item.peroid1 |
                          translate}}
                        </div>
                        <div class="col-8"
                          style="padding-left:45px!important;font-size:16px;color:#716F74;white-space: nowrap;">
                          {{item.date}}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </ngx-smart-modal>
            </div>

          </div>
          <label class="show">{{'dashboard.Department' | translate }}</label>
          <div id="chartDepartment" style="margin-top: 5px;margin-left: 5px;">
            <div class="input">
              <ng-select #chartDepartment [disabled]="this.disableDateType" dropdownPosition="bottom"
                [searchable]="false" [clearable]="false" [(ngModel)]="deptValue" [items]="departmentOptions"
                bindLabel="value" bindValue="value" (change)="showDepartmentChanged($event.value)">
                <ng-template ng-option-tmp let-option="item">
                  <span class="option-title">{{option.name |
                    translate}}</span>

                </ng-template>
                <ng-template ng-label-tmp let-item="item">
                  <span> {{item.name | translate}}</span>
                </ng-template>
              </ng-select>
              <div class="select-overlay"></div>
            </div>
            <svg class="down-arrow" (click)="chartDepartment.toggle()" style="left: 225px;top: -28px;" width="15"
              height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                fill="#8936F3" />
            </svg>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="this.grandTotalSpend > 0 || this.grandPreviousTotalSpend > 0" class="top-strip1">
      <span (click)="getselectedbookingValue('all')">
        <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M25 13C25 11.3159 24.6455 9.65057 23.9596 8.11245C23.2736 6.57432 22.2716 5.19781 21.0187 4.07243C19.7658 2.94706 18.29 2.09801 16.6873 1.58053C15.0847 1.06304 13.391 0.888703 11.7166 1.06885L12.663 9.86656C13.1028 9.81925 13.5476 9.86504 13.9685 10.0009C14.3894 10.1368 14.7769 10.3598 15.106 10.6554C15.4351 10.9509 15.6982 11.3124 15.8784 11.7164C16.0585 12.1203 16.1516 12.5577 16.1516 13H25Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#47494F'}" stroke="white" />
          <path
            d="M11.7163 1.06885C9.28655 1.33025 6.99449 2.32729 5.14669 3.92663C3.29889 5.52597 1.98344 7.65136 1.37627 10.0186C0.769099 12.3858 0.89915 14.8819 1.74903 17.1732C2.59891 19.4645 4.1281 21.4417 6.1321 22.8403L11.1962 15.5843C10.6699 15.217 10.2683 14.6977 10.0451 14.096C9.82192 13.4942 9.78777 12.8387 9.94723 12.217C10.1067 11.5953 10.4522 11.0371 10.9374 10.6171C11.4227 10.1971 12.0247 9.93521 12.6628 9.86656L11.7163 1.06885Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#47494F'}" stroke="white" />
          <path
            d="M6.13208 22.8403C7.93098 24.0959 10.0396 24.8342 12.2288 24.9752C14.418 25.1162 16.6039 24.6543 18.549 23.6399C20.494 22.6254 22.1238 21.0972 23.2611 19.2213C24.3984 17.3455 24.9998 15.1937 24.9998 13H16.1514C16.1514 13.5761 15.9934 14.1412 15.6948 14.6339C15.3961 15.1265 14.968 15.5279 14.4572 15.7943C13.9464 16.0607 13.3723 16.182 12.7974 16.145C12.2224 16.108 11.6687 15.9141 11.1962 15.5843L6.13208 22.8403Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#47494F'}" stroke="white" />
        </svg>
        <span class="{{this.selectBookingValue ==='all' ? 'select':'unselect'}}"> {{'dashboard.Total' | translate }}<div
            *ngIf="this.selectBookingValue ==='all'" class="underline"></div></span>
      </span>
      <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
      </svg>
      <span (click)="getselectedbookingValue('flight')">
        <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M20.5 7H5.535L3.832 4.445C3.646 4.167 3.334 4 3 4H1C0.692003 4 0.401003 4.142 0.212003 4.385C0.0230026 4.628 -0.0449974 4.944 0.0300026 5.243L2.03 13.243C2.142 13.688 2.541 14 3 14H11.132L8.168 18.445C7.964 18.752 7.944 19.146 8.118 19.471C8.292 19.796 8.631 20 9 20H13C13.334 20 13.646 19.833 13.832 19.555L17.535 14H20.5C22.43 14 24 12.43 24 10.5C24 8.57 22.43 7 20.5 7Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='flight' ?'#F3A536':'#47494F'}" />
          <path
            d="M10.272 5H16.617L13.853 0.479C13.672 0.181 13.349 0 13 0H9.00001C8.63901 0 8.30501 0.195 8.12801 0.511C7.95101 0.827 7.95801 1.213 8.14701 1.522L10.272 5Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='flight' ?'#F3A536':'#47494F'}" />
        </svg>
        <span class="{{this.selectBookingValue ==='flight' ? 'select':'unselect'}}">{{'dashboard.Flights' | translate }}
          <div *ngIf="this.selectBookingValue ==='flight'" class="underline"></div></span>
      </span>

    </div>
    <div *ngIf="this.grandTotalSpend > 0 || this.grandPreviousTotalSpend > 0" class="top-strip1"
      style="margin-top:10px !important;">
      <span (click)="getselectedbookingValue('hotel')">
        <svg width="15" height="24" viewBox="0 0 15 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M0.609375 17.625V23.25C0.609375 23.3495 0.643945 23.4448 0.705481 23.5152C0.767016 23.5855 0.850476 23.625 0.9375 23.625H6.1875V19.5C6.1875 19.4005 6.22207 19.3052 6.28361 19.2348C6.34514 19.1645 6.4286 19.125 6.51562 19.125H8.48438C8.5714 19.125 8.65486 19.1645 8.71639 19.2348C8.77793 19.3052 8.8125 19.4005 8.8125 19.5V23.625H14.0625C14.1495 23.625 14.233 23.5855 14.2945 23.5152C14.3561 23.4448 14.3906 23.3495 14.3906 23.25V17.625H0.609375Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='hotel' ?'#F3A536':'#47494F'}" />
          <path
            d="M14.1419 2.26125L7.57941 0.386252C7.52732 0.370883 7.47268 0.370883 7.42059 0.386252L0.858094 2.26125C0.787077 2.2815 0.724021 2.32829 0.678954 2.3942C0.633886 2.46011 0.609395 2.54135 0.609375 2.625V16.875H14.3906V2.625C14.3906 2.54135 14.3661 2.46011 14.321 2.3942C14.276 2.32829 14.2129 2.2815 14.1419 2.26125ZM3.89062 15C3.89062 15.0995 3.85606 15.1948 3.79452 15.2652C3.73298 15.3355 3.64952 15.375 3.5625 15.375H2.57812C2.4911 15.375 2.40764 15.3355 2.34611 15.2652C2.28457 15.1948 2.25 15.0995 2.25 15V13.125C2.25 13.0255 2.28457 12.9302 2.34611 12.8598C2.40764 12.7895 2.4911 12.75 2.57812 12.75H3.5625C3.64952 12.75 3.73298 12.7895 3.79452 12.8598C3.85606 12.9302 3.89062 13.0255 3.89062 13.125V15ZM3.89062 10.875C3.89062 10.9745 3.85606 11.0698 3.79452 11.1402C3.73298 11.2105 3.64952 11.25 3.5625 11.25H2.57812C2.4911 11.25 2.40764 11.2105 2.34611 11.1402C2.28457 11.0698 2.25 10.9745 2.25 10.875V9C2.25 8.90055 2.28457 8.80516 2.34611 8.73484C2.40764 8.66451 2.4911 8.625 2.57812 8.625H3.5625C3.64952 8.625 3.73298 8.66451 3.79452 8.73484C3.85606 8.80516 3.89062 8.90055 3.89062 9V10.875ZM3.89062 6.75C3.89062 6.84946 3.85606 6.94484 3.79452 7.01517C3.73298 7.08549 3.64952 7.125 3.5625 7.125H2.57812C2.4911 7.125 2.40764 7.08549 2.34611 7.01517C2.28457 6.94484 2.25 6.84946 2.25 6.75V4.875C2.25 4.77555 2.28457 4.68016 2.34611 4.60984C2.40764 4.53951 2.4911 4.5 2.57812 4.5H3.5625C3.64952 4.5 3.73298 4.53951 3.79452 4.60984C3.85606 4.68016 3.89062 4.77555 3.89062 4.875V6.75ZM6.84375 15C6.84375 15.0995 6.80918 15.1948 6.74764 15.2652C6.68611 15.3355 6.60265 15.375 6.51562 15.375H5.53125C5.44423 15.375 5.36077 15.3355 5.29923 15.2652C5.23769 15.1948 5.20312 15.0995 5.20312 15V13.125C5.20312 13.0255 5.23769 12.9302 5.29923 12.8598C5.36077 12.7895 5.44423 12.75 5.53125 12.75H6.51562C6.60265 12.75 6.68611 12.7895 6.74764 12.8598C6.80918 12.9302 6.84375 13.0255 6.84375 13.125V15ZM6.84375 10.875C6.84375 10.9745 6.80918 11.0698 6.74764 11.1402C6.68611 11.2105 6.60265 11.25 6.51562 11.25H5.53125C5.44423 11.25 5.36077 11.2105 5.29923 11.1402C5.23769 11.0698 5.20312 10.9745 5.20312 10.875V9C5.20312 8.90055 5.23769 8.80516 5.29923 8.73484C5.36077 8.66451 5.44423 8.625 5.53125 8.625H6.51562C6.60265 8.625 6.68611 8.66451 6.74764 8.73484C6.80918 8.80516 6.84375 8.90055 6.84375 9V10.875ZM6.84375 6.75C6.84375 6.84946 6.80918 6.94484 6.74764 7.01517C6.68611 7.08549 6.60265 7.125 6.51562 7.125H5.53125C5.44423 7.125 5.36077 7.08549 5.29923 7.01517C5.23769 6.94484 5.20312 6.84946 5.20312 6.75V4.875C5.20312 4.77555 5.23769 4.68016 5.29923 4.60984C5.36077 4.53951 5.44423 4.5 5.53125 4.5H6.51562C6.60265 4.5 6.68611 4.53951 6.74764 4.60984C6.80918 4.68016 6.84375 4.77555 6.84375 4.875V6.75ZM9.79688 15C9.79688 15.0995 9.76231 15.1948 9.70077 15.2652C9.63923 15.3355 9.55577 15.375 9.46875 15.375H8.48438C8.39735 15.375 8.31389 15.3355 8.25236 15.2652C8.19082 15.1948 8.15625 15.0995 8.15625 15V13.125C8.15625 13.0255 8.19082 12.9302 8.25236 12.8598C8.31389 12.7895 8.39735 12.75 8.48438 12.75H9.46875C9.55577 12.75 9.63923 12.7895 9.70077 12.8598C9.76231 12.9302 9.79688 13.0255 9.79688 13.125V15ZM9.79688 10.875C9.79688 10.9745 9.76231 11.0698 9.70077 11.1402C9.63923 11.2105 9.55577 11.25 9.46875 11.25H8.48438C8.39735 11.25 8.31389 11.2105 8.25236 11.1402C8.19082 11.0698 8.15625 10.9745 8.15625 10.875V9C8.15625 8.90055 8.19082 8.80516 8.25236 8.73484C8.31389 8.66451 8.39735 8.625 8.48438 8.625H9.46875C9.55577 8.625 9.63923 8.66451 9.70077 8.73484C9.76231 8.80516 9.79688 8.90055 9.79688 9V10.875ZM9.79688 6.75C9.79688 6.84946 9.76231 6.94484 9.70077 7.01517C9.63923 7.08549 9.55577 7.125 9.46875 7.125H8.48438C8.39735 7.125 8.31389 7.08549 8.25236 7.01517C8.19082 6.94484 8.15625 6.84946 8.15625 6.75V4.875C8.15625 4.77555 8.19082 4.68016 8.25236 4.60984C8.31389 4.53951 8.39735 4.5 8.48438 4.5H9.46875C9.55577 4.5 9.63923 4.53951 9.70077 4.60984C9.76231 4.68016 9.79688 4.77555 9.79688 4.875V6.75ZM12.75 15C12.75 15.0995 12.7154 15.1948 12.6539 15.2652C12.5924 15.3355 12.5089 15.375 12.4219 15.375H11.4375C11.3505 15.375 11.267 15.3355 11.2055 15.2652C11.1439 15.1948 11.1094 15.0995 11.1094 15V13.125C11.1094 13.0255 11.1439 12.9302 11.2055 12.8598C11.267 12.7895 11.3505 12.75 11.4375 12.75H12.4219C12.5089 12.75 12.5924 12.7895 12.6539 12.8598C12.7154 12.9302 12.75 13.0255 12.75 13.125V15ZM12.75 10.875C12.75 10.9745 12.7154 11.0698 12.6539 11.1402C12.5924 11.2105 12.5089 11.25 12.4219 11.25H11.4375C11.3505 11.25 11.267 11.2105 11.2055 11.1402C11.1439 11.0698 11.1094 10.9745 11.1094 10.875V9C11.1094 8.90055 11.1439 8.80516 11.2055 8.73484C11.267 8.66451 11.3505 8.625 11.4375 8.625H12.4219C12.5089 8.625 12.5924 8.66451 12.6539 8.73484C12.7154 8.80516 12.75 8.90055 12.75 9V10.875ZM12.75 6.75C12.75 6.84946 12.7154 6.94484 12.6539 7.01517C12.5924 7.08549 12.5089 7.125 12.4219 7.125H11.4375C11.3505 7.125 11.267 7.08549 11.2055 7.01517C11.1439 6.94484 11.1094 6.84946 11.1094 6.75V4.875C11.1094 4.77555 11.1439 4.68016 11.2055 4.60984C11.267 4.53951 11.3505 4.5 11.4375 4.5H12.4219C12.5089 4.5 12.5924 4.53951 12.6539 4.60984C12.7154 4.68016 12.75 4.77555 12.75 4.875V6.75Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='hotel' ?'#F3A536':'#47494F'}" />
        </svg>
        <span class="{{this.selectBookingValue ==='hotel' ? 'select':'unselect'}}"> {{'dashboard.Hotels' | translate }}
          <div *ngIf="this.selectBookingValue ==='hotel'" class="underline"></div></span>
      </span>
      <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
      </svg>
      <span (click)="getselectedbookingValue('car')">
        <svg width="37" height="19" viewBox="0 0 37 19" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M0 14.2188V14.7969C0 14.9502 0.0609094 15.0973 0.169329 15.2057C0.277748 15.3141 0.424797 15.375 0.578125 15.375H4.04688C4.04688 14.7804 4.16213 14.1914 4.38623 13.6406H0.578125C0.424797 13.6406 0.277748 13.7015 0.169329 13.81C0.0609094 13.9184 0 14.0654 0 14.2188H0Z"
            fill="#47494F" />
          <path
            d="M8.67188 18.8437C7.98582 18.8437 7.31518 18.6403 6.74474 18.2592C6.17431 17.878 5.72971 17.3363 5.46717 16.7024C5.20463 16.0686 5.13594 15.3712 5.26978 14.6983C5.40362 14.0254 5.73399 13.4073 6.2191 12.9222C6.70421 12.4371 7.32229 12.1067 7.99516 11.9729C8.66803 11.8391 9.36548 11.9078 9.99931 12.1703C10.6331 12.4328 11.1749 12.8774 11.556 13.4479C11.9372 14.0183 12.1406 14.6889 12.1406 15.375C12.1397 16.2947 11.774 17.1764 11.1236 17.8268C10.4733 18.4771 9.59156 18.8428 8.67188 18.8437ZM8.67188 13.0625C8.21451 13.0625 7.76741 13.1981 7.38712 13.4522C7.00683 13.7063 6.71043 14.0675 6.5354 14.49C6.36038 14.9126 6.31458 15.3776 6.40381 15.8261C6.49304 16.2747 6.71328 16.6868 7.03669 17.0102C7.3601 17.3336 7.77215 17.5538 8.22073 17.6431C8.66931 17.7323 9.13428 17.6865 9.55683 17.5115C9.97939 17.3364 10.3405 17.04 10.5946 16.6598C10.8488 16.2795 10.9844 15.8324 10.9844 15.375C10.9844 14.7617 10.7407 14.1735 10.3071 13.7398C9.87338 13.3061 9.28519 13.0625 8.67188 13.0625Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#47494F'}" />
          <path
            d="M29.4844 18.8437C28.7983 18.8437 28.1277 18.6403 27.5572 18.2592C26.9868 17.878 26.5422 17.3363 26.2797 16.7024C26.0171 16.0686 25.9484 15.3712 26.0823 14.6983C26.2161 14.0254 26.5465 13.4073 27.0316 12.9222C27.5167 12.4371 28.1348 12.1067 28.8077 11.9729C29.4805 11.8391 30.178 11.9078 30.8118 12.1703C31.4456 12.4328 31.9874 12.8774 32.3685 13.4479C32.7497 14.0183 32.9531 14.6889 32.9531 15.375C32.9522 16.2947 32.5865 17.1764 31.9361 17.8268C31.2858 18.4771 30.4041 18.8428 29.4844 18.8437ZM29.4844 13.0625C29.027 13.0625 28.5799 13.1981 28.1996 13.4522C27.8193 13.7063 27.5229 14.0675 27.3479 14.49C27.1729 14.9126 27.1271 15.3776 27.2163 15.8261C27.3055 16.2747 27.5258 16.6868 27.8492 17.0102C28.1726 17.3336 28.5847 17.5538 29.0332 17.6431C29.4818 17.7323 29.9468 17.6865 30.3693 17.5115C30.7919 17.3364 31.1531 17.04 31.4072 16.6598C31.6613 16.2795 31.7969 15.8324 31.7969 15.375C31.7969 14.7617 31.5532 14.1735 31.1196 13.7398C30.6859 13.3061 30.0977 13.0625 29.4844 13.0625Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#47494F'}" />
          <path
            d="M36.4219 13.6406H33.77C33.9941 14.1914 34.1094 14.7804 34.1094 15.375H36.4219C36.5752 15.375 36.7223 15.3141 36.8307 15.2057C36.9391 15.0973 37 14.9502 37 14.7969V14.2188C37 14.0654 36.9391 13.9184 36.8307 13.81C36.7223 13.7015 36.5752 13.6406 36.4219 13.6406Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#47494F'}" />
          <path
            d="M35.5079 9.51107C35.4171 8.88792 35.126 8.31122 34.6786 7.86807C34.2312 7.42493 33.6517 7.1394 33.0277 7.05462L26.9984 6.25045L23.6222 0.624137C23.5418 0.495693 23.4142 0.403939 23.2669 0.368619C23.1195 0.3333 22.9642 0.357239 22.8343 0.435284C22.7045 0.51333 22.6104 0.639244 22.5724 0.785928C22.5345 0.932613 22.5556 1.08834 22.6313 1.2196L25.5728 6.12499H15.0312C14.4179 6.12499 13.8297 5.88136 13.3961 5.44768C12.9624 5.014 12.7188 4.42581 12.7188 3.8125V2.94531C12.7188 2.56199 12.5665 2.19437 12.2954 1.92332C12.0244 1.65227 11.6568 1.5 11.2734 1.5C10.8901 1.5 10.5225 1.65227 10.2514 1.92332C9.9804 2.19437 9.82812 2.56199 9.82812 2.94531V4.98031C9.80094 4.97637 9.7735 4.97444 9.74603 4.97452L3.63814 5.8475C2.94928 5.94589 2.31903 6.28947 1.86312 6.81516C1.4072 7.34085 1.15622 8.01336 1.15625 8.70921V12.7289H4.88169C5.44529 11.923 6.25113 11.3177 7.18218 11.0009C8.11323 10.6841 9.12102 10.6724 10.0592 10.9674C10.9974 11.2624 11.8171 11.8488 12.3993 12.6414C12.9815 13.434 13.2959 14.3915 13.2969 15.375H24.8594C24.8601 14.4175 25.158 13.4838 25.712 12.7028C26.266 11.9218 27.0487 11.332 27.9521 11.0148C28.8556 10.6976 29.8352 10.6686 30.7558 10.9318C31.6765 11.195 32.4927 11.7375 33.0919 12.4844H35.6975L35.5079 9.51107Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#47494F'}" />
        </svg>
        <span class="{{this.selectBookingValue ==='car' ? 'select':'unselect'}}"> {{'dashboard.Cars' | translate }} <div
            *ngIf="this.selectBookingValue ==='car'" class="underline"></div></span>
      </span>
    </div>
    <div *ngIf="this.grandTotalSpend === 0 && this.grandPreviousTotalSpend === 0" class="top-strip1">
      <span (click)="getselectedbookingValue('all')">
        <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M25 13C25 11.3159 24.6455 9.65057 23.9596 8.11245C23.2736 6.57432 22.2716 5.19781 21.0187 4.07243C19.7658 2.94706 18.29 2.09801 16.6873 1.58053C15.0847 1.06304 13.391 0.888703 11.7166 1.06885L12.663 9.86656C13.1028 9.81925 13.5476 9.86504 13.9685 10.0009C14.3894 10.1368 14.7769 10.3598 15.106 10.6554C15.4351 10.9509 15.6982 11.3124 15.8784 11.7164C16.0585 12.1203 16.1516 12.5577 16.1516 13H25Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#AEAEAE'}" stroke="white" />
          <path
            d="M11.7163 1.06885C9.28655 1.33025 6.99449 2.32729 5.14669 3.92663C3.29889 5.52597 1.98344 7.65136 1.37627 10.0186C0.769099 12.3858 0.89915 14.8819 1.74903 17.1732C2.59891 19.4645 4.1281 21.4417 6.1321 22.8403L11.1962 15.5843C10.6699 15.217 10.2683 14.6977 10.0451 14.096C9.82192 13.4942 9.78777 12.8387 9.94723 12.217C10.1067 11.5953 10.4522 11.0371 10.9374 10.6171C11.4227 10.1971 12.0247 9.93521 12.6628 9.86656L11.7163 1.06885Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#AEAEAE'}" stroke="white" />
          <path
            d="M6.13208 22.8403C7.93098 24.0959 10.0396 24.8342 12.2288 24.9752C14.418 25.1162 16.6039 24.6543 18.549 23.6399C20.494 22.6254 22.1238 21.0972 23.2611 19.2213C24.3984 17.3455 24.9998 15.1937 24.9998 13H16.1514C16.1514 13.5761 15.9934 14.1412 15.6948 14.6339C15.3961 15.1265 14.968 15.5279 14.4572 15.7943C13.9464 16.0607 13.3723 16.182 12.7974 16.145C12.2224 16.108 11.6687 15.9141 11.1962 15.5843L6.13208 22.8403Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='all' ?'#F3A536':'#AEAEAE'}" stroke="white" />
        </svg>
        <span class="{{this.selectBookingValue ==='all' ? 'select':'unselect'}}"> {{'dashboard.Total' | translate }}<div
            *ngIf="this.selectBookingValue ==='all'" class="underline"></div></span>
      </span>
      <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
      </svg>
      <span (click)="getselectedbookingValue('flight')">
        <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M20.5 7H5.535L3.832 4.445C3.646 4.167 3.334 4 3 4H1C0.692003 4 0.401003 4.142 0.212003 4.385C0.0230026 4.628 -0.0449974 4.944 0.0300026 5.243L2.03 13.243C2.142 13.688 2.541 14 3 14H11.132L8.168 18.445C7.964 18.752 7.944 19.146 8.118 19.471C8.292 19.796 8.631 20 9 20H13C13.334 20 13.646 19.833 13.832 19.555L17.535 14H20.5C22.43 14 24 12.43 24 10.5C24 8.57 22.43 7 20.5 7Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='flight' ?'#F3A536':'#AEAEAE'}" />
          <path
            d="M10.272 5H16.617L13.853 0.479C13.672 0.181 13.349 0 13 0H9.00001C8.63901 0 8.30501 0.195 8.12801 0.511C7.95101 0.827 7.95801 1.213 8.14701 1.522L10.272 5Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='flight' ?'#F3A536':'#AEAEAE'}" />
        </svg>
        <span class="{{this.selectBookingValue ==='flight' ? 'select':'unselect'}}"> {{'dashboard.Flights' | translate
          }} <div *ngIf="this.selectBookingValue ==='flight'" class="underline"></div></span>
      </span>

    </div>
    <div *ngIf="this.grandTotalSpend === 0 && this.grandPreviousTotalSpend === 0" class="top-strip1"
      style="margin-top:10px !important;">
      <span (click)="getselectedbookingValue('hotel')">
        <svg width="15" height="24" viewBox="0 0 15 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M0.609375 17.625V23.25C0.609375 23.3495 0.643945 23.4448 0.705481 23.5152C0.767016 23.5855 0.850476 23.625 0.9375 23.625H6.1875V19.5C6.1875 19.4005 6.22207 19.3052 6.28361 19.2348C6.34514 19.1645 6.4286 19.125 6.51562 19.125H8.48438C8.5714 19.125 8.65486 19.1645 8.71639 19.2348C8.77793 19.3052 8.8125 19.4005 8.8125 19.5V23.625H14.0625C14.1495 23.625 14.233 23.5855 14.2945 23.5152C14.3561 23.4448 14.3906 23.3495 14.3906 23.25V17.625H0.609375Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='hotel' ?'#F3A536':'#AEAEAE'}" />
          <path
            d="M14.1419 2.26125L7.57941 0.386252C7.52732 0.370883 7.47268 0.370883 7.42059 0.386252L0.858094 2.26125C0.787077 2.2815 0.724021 2.32829 0.678954 2.3942C0.633886 2.46011 0.609395 2.54135 0.609375 2.625V16.875H14.3906V2.625C14.3906 2.54135 14.3661 2.46011 14.321 2.3942C14.276 2.32829 14.2129 2.2815 14.1419 2.26125ZM3.89062 15C3.89062 15.0995 3.85606 15.1948 3.79452 15.2652C3.73298 15.3355 3.64952 15.375 3.5625 15.375H2.57812C2.4911 15.375 2.40764 15.3355 2.34611 15.2652C2.28457 15.1948 2.25 15.0995 2.25 15V13.125C2.25 13.0255 2.28457 12.9302 2.34611 12.8598C2.40764 12.7895 2.4911 12.75 2.57812 12.75H3.5625C3.64952 12.75 3.73298 12.7895 3.79452 12.8598C3.85606 12.9302 3.89062 13.0255 3.89062 13.125V15ZM3.89062 10.875C3.89062 10.9745 3.85606 11.0698 3.79452 11.1402C3.73298 11.2105 3.64952 11.25 3.5625 11.25H2.57812C2.4911 11.25 2.40764 11.2105 2.34611 11.1402C2.28457 11.0698 2.25 10.9745 2.25 10.875V9C2.25 8.90055 2.28457 8.80516 2.34611 8.73484C2.40764 8.66451 2.4911 8.625 2.57812 8.625H3.5625C3.64952 8.625 3.73298 8.66451 3.79452 8.73484C3.85606 8.80516 3.89062 8.90055 3.89062 9V10.875ZM3.89062 6.75C3.89062 6.84946 3.85606 6.94484 3.79452 7.01517C3.73298 7.08549 3.64952 7.125 3.5625 7.125H2.57812C2.4911 7.125 2.40764 7.08549 2.34611 7.01517C2.28457 6.94484 2.25 6.84946 2.25 6.75V4.875C2.25 4.77555 2.28457 4.68016 2.34611 4.60984C2.40764 4.53951 2.4911 4.5 2.57812 4.5H3.5625C3.64952 4.5 3.73298 4.53951 3.79452 4.60984C3.85606 4.68016 3.89062 4.77555 3.89062 4.875V6.75ZM6.84375 15C6.84375 15.0995 6.80918 15.1948 6.74764 15.2652C6.68611 15.3355 6.60265 15.375 6.51562 15.375H5.53125C5.44423 15.375 5.36077 15.3355 5.29923 15.2652C5.23769 15.1948 5.20312 15.0995 5.20312 15V13.125C5.20312 13.0255 5.23769 12.9302 5.29923 12.8598C5.36077 12.7895 5.44423 12.75 5.53125 12.75H6.51562C6.60265 12.75 6.68611 12.7895 6.74764 12.8598C6.80918 12.9302 6.84375 13.0255 6.84375 13.125V15ZM6.84375 10.875C6.84375 10.9745 6.80918 11.0698 6.74764 11.1402C6.68611 11.2105 6.60265 11.25 6.51562 11.25H5.53125C5.44423 11.25 5.36077 11.2105 5.29923 11.1402C5.23769 11.0698 5.20312 10.9745 5.20312 10.875V9C5.20312 8.90055 5.23769 8.80516 5.29923 8.73484C5.36077 8.66451 5.44423 8.625 5.53125 8.625H6.51562C6.60265 8.625 6.68611 8.66451 6.74764 8.73484C6.80918 8.80516 6.84375 8.90055 6.84375 9V10.875ZM6.84375 6.75C6.84375 6.84946 6.80918 6.94484 6.74764 7.01517C6.68611 7.08549 6.60265 7.125 6.51562 7.125H5.53125C5.44423 7.125 5.36077 7.08549 5.29923 7.01517C5.23769 6.94484 5.20312 6.84946 5.20312 6.75V4.875C5.20312 4.77555 5.23769 4.68016 5.29923 4.60984C5.36077 4.53951 5.44423 4.5 5.53125 4.5H6.51562C6.60265 4.5 6.68611 4.53951 6.74764 4.60984C6.80918 4.68016 6.84375 4.77555 6.84375 4.875V6.75ZM9.79688 15C9.79688 15.0995 9.76231 15.1948 9.70077 15.2652C9.63923 15.3355 9.55577 15.375 9.46875 15.375H8.48438C8.39735 15.375 8.31389 15.3355 8.25236 15.2652C8.19082 15.1948 8.15625 15.0995 8.15625 15V13.125C8.15625 13.0255 8.19082 12.9302 8.25236 12.8598C8.31389 12.7895 8.39735 12.75 8.48438 12.75H9.46875C9.55577 12.75 9.63923 12.7895 9.70077 12.8598C9.76231 12.9302 9.79688 13.0255 9.79688 13.125V15ZM9.79688 10.875C9.79688 10.9745 9.76231 11.0698 9.70077 11.1402C9.63923 11.2105 9.55577 11.25 9.46875 11.25H8.48438C8.39735 11.25 8.31389 11.2105 8.25236 11.1402C8.19082 11.0698 8.15625 10.9745 8.15625 10.875V9C8.15625 8.90055 8.19082 8.80516 8.25236 8.73484C8.31389 8.66451 8.39735 8.625 8.48438 8.625H9.46875C9.55577 8.625 9.63923 8.66451 9.70077 8.73484C9.76231 8.80516 9.79688 8.90055 9.79688 9V10.875ZM9.79688 6.75C9.79688 6.84946 9.76231 6.94484 9.70077 7.01517C9.63923 7.08549 9.55577 7.125 9.46875 7.125H8.48438C8.39735 7.125 8.31389 7.08549 8.25236 7.01517C8.19082 6.94484 8.15625 6.84946 8.15625 6.75V4.875C8.15625 4.77555 8.19082 4.68016 8.25236 4.60984C8.31389 4.53951 8.39735 4.5 8.48438 4.5H9.46875C9.55577 4.5 9.63923 4.53951 9.70077 4.60984C9.76231 4.68016 9.79688 4.77555 9.79688 4.875V6.75ZM12.75 15C12.75 15.0995 12.7154 15.1948 12.6539 15.2652C12.5924 15.3355 12.5089 15.375 12.4219 15.375H11.4375C11.3505 15.375 11.267 15.3355 11.2055 15.2652C11.1439 15.1948 11.1094 15.0995 11.1094 15V13.125C11.1094 13.0255 11.1439 12.9302 11.2055 12.8598C11.267 12.7895 11.3505 12.75 11.4375 12.75H12.4219C12.5089 12.75 12.5924 12.7895 12.6539 12.8598C12.7154 12.9302 12.75 13.0255 12.75 13.125V15ZM12.75 10.875C12.75 10.9745 12.7154 11.0698 12.6539 11.1402C12.5924 11.2105 12.5089 11.25 12.4219 11.25H11.4375C11.3505 11.25 11.267 11.2105 11.2055 11.1402C11.1439 11.0698 11.1094 10.9745 11.1094 10.875V9C11.1094 8.90055 11.1439 8.80516 11.2055 8.73484C11.267 8.66451 11.3505 8.625 11.4375 8.625H12.4219C12.5089 8.625 12.5924 8.66451 12.6539 8.73484C12.7154 8.80516 12.75 8.90055 12.75 9V10.875ZM12.75 6.75C12.75 6.84946 12.7154 6.94484 12.6539 7.01517C12.5924 7.08549 12.5089 7.125 12.4219 7.125H11.4375C11.3505 7.125 11.267 7.08549 11.2055 7.01517C11.1439 6.94484 11.1094 6.84946 11.1094 6.75V4.875C11.1094 4.77555 11.1439 4.68016 11.2055 4.60984C11.267 4.53951 11.3505 4.5 11.4375 4.5H12.4219C12.5089 4.5 12.5924 4.53951 12.6539 4.60984C12.7154 4.68016 12.75 4.77555 12.75 4.875V6.75Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='hotel' ?'#F3A536':'#AEAEAE'}" />
        </svg>
        <span class="{{this.selectBookingValue ==='hotel' ? 'select':'unselect'}}"> {{'dashboard.Hotels' | translate }}
          <div *ngIf="this.selectBookingValue ==='hotel'" class="underline"></div></span>
      </span>
      <svg class="line11" width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="#47494F" />
      </svg>
      <span (click)="getselectedbookingValue('car')">
        <svg width="37" height="19" viewBox="0 0 37 19" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M0 14.2188V14.7969C0 14.9502 0.0609094 15.0973 0.169329 15.2057C0.277748 15.3141 0.424797 15.375 0.578125 15.375H4.04688C4.04688 14.7804 4.16213 14.1914 4.38623 13.6406H0.578125C0.424797 13.6406 0.277748 13.7015 0.169329 13.81C0.0609094 13.9184 0 14.0654 0 14.2188H0Z"
            fill="#47494F" />
          <path
            d="M8.67188 18.8437C7.98582 18.8437 7.31518 18.6403 6.74474 18.2592C6.17431 17.878 5.72971 17.3363 5.46717 16.7024C5.20463 16.0686 5.13594 15.3712 5.26978 14.6983C5.40362 14.0254 5.73399 13.4073 6.2191 12.9222C6.70421 12.4371 7.32229 12.1067 7.99516 11.9729C8.66803 11.8391 9.36548 11.9078 9.99931 12.1703C10.6331 12.4328 11.1749 12.8774 11.556 13.4479C11.9372 14.0183 12.1406 14.6889 12.1406 15.375C12.1397 16.2947 11.774 17.1764 11.1236 17.8268C10.4733 18.4771 9.59156 18.8428 8.67188 18.8437ZM8.67188 13.0625C8.21451 13.0625 7.76741 13.1981 7.38712 13.4522C7.00683 13.7063 6.71043 14.0675 6.5354 14.49C6.36038 14.9126 6.31458 15.3776 6.40381 15.8261C6.49304 16.2747 6.71328 16.6868 7.03669 17.0102C7.3601 17.3336 7.77215 17.5538 8.22073 17.6431C8.66931 17.7323 9.13428 17.6865 9.55683 17.5115C9.97939 17.3364 10.3405 17.04 10.5946 16.6598C10.8488 16.2795 10.9844 15.8324 10.9844 15.375C10.9844 14.7617 10.7407 14.1735 10.3071 13.7398C9.87338 13.3061 9.28519 13.0625 8.67188 13.0625Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#AEAEAE'}" />
          <path
            d="M29.4844 18.8437C28.7983 18.8437 28.1277 18.6403 27.5572 18.2592C26.9868 17.878 26.5422 17.3363 26.2797 16.7024C26.0171 16.0686 25.9484 15.3712 26.0823 14.6983C26.2161 14.0254 26.5465 13.4073 27.0316 12.9222C27.5167 12.4371 28.1348 12.1067 28.8077 11.9729C29.4805 11.8391 30.178 11.9078 30.8118 12.1703C31.4456 12.4328 31.9874 12.8774 32.3685 13.4479C32.7497 14.0183 32.9531 14.6889 32.9531 15.375C32.9522 16.2947 32.5865 17.1764 31.9361 17.8268C31.2858 18.4771 30.4041 18.8428 29.4844 18.8437ZM29.4844 13.0625C29.027 13.0625 28.5799 13.1981 28.1996 13.4522C27.8193 13.7063 27.5229 14.0675 27.3479 14.49C27.1729 14.9126 27.1271 15.3776 27.2163 15.8261C27.3055 16.2747 27.5258 16.6868 27.8492 17.0102C28.1726 17.3336 28.5847 17.5538 29.0332 17.6431C29.4818 17.7323 29.9468 17.6865 30.3693 17.5115C30.7919 17.3364 31.1531 17.04 31.4072 16.6598C31.6613 16.2795 31.7969 15.8324 31.7969 15.375C31.7969 14.7617 31.5532 14.1735 31.1196 13.7398C30.6859 13.3061 30.0977 13.0625 29.4844 13.0625Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#AEAEAE'}" />
          <path
            d="M36.4219 13.6406H33.77C33.9941 14.1914 34.1094 14.7804 34.1094 15.375H36.4219C36.5752 15.375 36.7223 15.3141 36.8307 15.2057C36.9391 15.0973 37 14.9502 37 14.7969V14.2188C37 14.0654 36.9391 13.9184 36.8307 13.81C36.7223 13.7015 36.5752 13.6406 36.4219 13.6406Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#AEAEAE'}" />
          <path
            d="M35.5079 9.51107C35.4171 8.88792 35.126 8.31122 34.6786 7.86807C34.2312 7.42493 33.6517 7.1394 33.0277 7.05462L26.9984 6.25045L23.6222 0.624137C23.5418 0.495693 23.4142 0.403939 23.2669 0.368619C23.1195 0.3333 22.9642 0.357239 22.8343 0.435284C22.7045 0.51333 22.6104 0.639244 22.5724 0.785928C22.5345 0.932613 22.5556 1.08834 22.6313 1.2196L25.5728 6.12499H15.0312C14.4179 6.12499 13.8297 5.88136 13.3961 5.44768C12.9624 5.014 12.7188 4.42581 12.7188 3.8125V2.94531C12.7188 2.56199 12.5665 2.19437 12.2954 1.92332C12.0244 1.65227 11.6568 1.5 11.2734 1.5C10.8901 1.5 10.5225 1.65227 10.2514 1.92332C9.9804 2.19437 9.82812 2.56199 9.82812 2.94531V4.98031C9.80094 4.97637 9.7735 4.97444 9.74603 4.97452L3.63814 5.8475C2.94928 5.94589 2.31903 6.28947 1.86312 6.81516C1.4072 7.34085 1.15622 8.01336 1.15625 8.70921V12.7289H4.88169C5.44529 11.923 6.25113 11.3177 7.18218 11.0009C8.11323 10.6841 9.12102 10.6724 10.0592 10.9674C10.9974 11.2624 11.8171 11.8488 12.3993 12.6414C12.9815 13.434 13.2959 14.3915 13.2969 15.375H24.8594C24.8601 14.4175 25.158 13.4838 25.712 12.7028C26.266 11.9218 27.0487 11.332 27.9521 11.0148C28.8556 10.6976 29.8352 10.6686 30.7558 10.9318C31.6765 11.195 32.4927 11.7375 33.0919 12.4844H35.6975L35.5079 9.51107Z"
            [ngStyle]="{'fill': this.selectBookingValue ==='car' ?'#F3A536':'#AEAEAE'}" />
        </svg>
        <span class="{{this.selectBookingValue ==='car' ? 'select':'unselect'}}"> {{'dashboard.Cars' | translate }}<div
            *ngIf="this.selectBookingValue ==='car'" class="underline"></div></span>
      </span>
    </div>
    <div *ngIf=" !this.applyBtn1 && !this.applyBtn">
      <div class="right-panel">
        <div class="row">
          <div class="col-12" style="text-align: center;">
            <div class="{{(this.totalBooking >0 || this.totalPreviousBooking>0) ? 'dollar1':'dollar' }}">
              <span *ngIf="this.grandTotalSpend >0">
                {{this.grandTotalSpend | currency : getCurrencySymbol(this.currency) : 'code' : "1.2-2"}}</span>
              <span *ngIf="this.grandTotalSpend===0">
                {{getCurrencySymbol(this.currency)}}0</span>
            </div>
            <div *ngIf="this.totalBooking >0 || this.totalPreviousBooking > 0" class="change">
              <img *ngIf="showChangeReport(this.grandTotalSpend,this.grandPreviousTotalSpend)==='positive'"
                style="margin-right:3px;position: relative;top: -4px;" src="assets/images/positive.png">
              <img *ngIf="showChangeReport(this.grandTotalSpend,this.grandPreviousTotalSpend)==='negative'"
                style="margin-right:3px;position: relative;top: -4px;" src="assets/images/negative.png"> <span
                class="{{(this.totalBooking >0 || this.totalPreviousBooking>0) ? 'show3':'show2' }}">
                {{percentageDiff(this.grandTotalSpend,this.grandPreviousTotalSpend)}}%</span><span
                style="margin-left: 20px;"
                class="{{(this.totalBooking >0 || this.totalPreviousBooking>0) ? 'show4':'show1' }}">{{'dashboard.change'
                | translate }}</span>
            </div>
            <div *ngIf="this.totalBooking ===0 && this.totalPreviousBooking===0" class="change">
              <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png">
              <span class="{{(this.totalBooking >0 || this.totalPreviousBooking>0) ? 'show3':'show2' }}"
                style="margin-left:0px !important;font-family:var(--globalFontfamilyr) !important;font-weight:300 !important;">
                0%</span><span style="margin-left: 20px;"
                class="{{(this.totalBooking >0 || this.totalPreviousBooking>0) ? 'show4':'show1' }}">{{'dashboard.change'
                | translate }}</span>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <svg *ngIf="this.doughnutChartData.length==0" style="position: relative;left: -10px;" width="341"
              height="204" viewBox="0 0 341 204" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M228.188 25.8249L238.542 14.2971L228.188 25.8249Z" fill="#AEAEAE" />
              <path
                d="M253.528 10.064C252.952 7.952 251.384 6.672 249 6.672C246.296 6.672 243.816 8.608 243.816 12.4C243.816 16.192 246.296 18.128 249 18.128C251.384 18.128 252.952 16.848 253.528 14.736L251.864 14.32C251.304 15.744 250.52 16.464 249 16.464C247.112 16.464 245.608 15.136 245.608 12.4C245.608 9.664 247.112 8.336 249 8.336C250.52 8.336 251.304 9.056 251.864 10.48L253.528 10.064ZM254.829 15.68C254.829 17.36 255.965 18.128 257.309 18.128C258.445 18.128 259.277 17.68 259.805 16.944L259.965 18H261.373V12.992C261.373 10.624 260.029 9.808 258.157 9.808C256.605 9.808 255.453 10.672 255.117 12.032L256.637 12.352C256.909 11.632 257.485 11.28 258.141 11.28C258.989 11.28 259.709 11.68 259.709 12.688V13.04H258.317C256.253 13.04 254.829 14.016 254.829 15.68ZM256.589 15.68C256.589 14.912 257.229 14.432 258.253 14.432H259.709V14.608C259.709 16 258.621 16.656 257.597 16.656C256.893 16.656 256.589 16.224 256.589 15.68ZM265.087 18V15.008C265.087 12.464 266.191 11.28 267.263 11.28C267.599 11.28 267.951 11.36 268.207 11.52L268.495 10C268.255 9.904 267.903 9.808 267.471 9.808C266.607 9.808 265.647 10.32 265.087 11.28V9.936H263.423V18H265.087ZM271.932 9.808C270.476 9.808 269.452 10.848 269.452 12.096C269.452 13.328 270.252 13.92 271.18 14.304C272.124 14.704 273.1 14.896 273.1 15.6C273.1 16.336 272.652 16.656 272.012 16.656C271.116 16.656 270.556 15.92 270.236 15.2L268.828 15.968C269.243 16.976 270.284 18.128 271.948 18.128C273.676 18.128 274.748 16.96 274.748 15.568C274.748 14.128 273.58 13.648 272.492 13.232C271.66 12.912 271.052 12.656 271.052 12.08C271.052 11.552 271.436 11.28 271.852 11.28C272.364 11.28 272.86 11.504 273.26 12.016L274.316 11.088C273.804 10.288 272.908 9.808 271.932 9.808ZM286.876 8.56C286.412 7.728 285.708 7.04 284.7 6.784V5.36H283.1V6.752C281.804 7.04 280.86 8.176 280.86 9.616C280.86 13.072 285.628 12.592 285.628 14.784C285.628 15.776 284.924 16.464 283.916 16.464C282.636 16.464 281.98 15.344 281.66 14.096L280.22 15.008C280.556 16.544 281.58 17.744 283.1 18.048V19.44H284.7V18.048C286.3 17.76 287.42 16.496 287.42 14.72C287.42 11.264 282.62 11.696 282.62 9.488C282.62 8.832 283.068 8.272 283.788 8.272C284.444 8.272 285.004 8.656 285.452 9.504L286.876 8.56ZM293.15 6.672C290.51 6.672 289.118 8.864 289.118 12.4C289.118 15.936 290.51 18.128 293.15 18.128C295.79 18.128 297.182 15.936 297.182 12.4C297.182 8.864 295.79 6.672 293.15 6.672ZM293.15 8.208C294.734 8.208 295.486 9.776 295.486 12.4C295.486 15.024 294.734 16.592 293.15 16.592C291.566 16.592 290.814 15.024 290.814 12.4C290.814 9.776 291.566 8.208 293.15 8.208ZM300.971 6C300.059 7.536 299.275 9.264 299.275 12.4C299.275 15.536 300.059 17.264 300.971 18.8H302.635C301.515 17.072 300.939 15.296 300.939 12.4C300.939 9.504 301.515 7.728 302.635 6H300.971ZM308.119 6.672C305.479 6.672 304.087 8.864 304.087 12.4C304.087 15.936 305.479 18.128 308.119 18.128C310.759 18.128 312.151 15.936 312.151 12.4C312.151 8.864 310.759 6.672 308.119 6.672ZM308.119 8.208C309.703 8.208 310.455 9.776 310.455 12.4C310.455 15.024 309.703 16.592 308.119 16.592C306.535 16.592 305.783 15.024 305.783 12.4C305.783 9.776 306.535 8.208 308.119 8.208ZM314.052 9.232C314.052 10.656 315.188 11.792 316.644 11.792C318.1 11.792 319.236 10.656 319.236 9.232C319.236 7.808 318.1 6.672 316.644 6.672C315.188 6.672 314.052 7.808 314.052 9.232ZM315.46 9.232C315.46 8.528 315.908 8 316.644 8C317.38 8 317.828 8.528 317.828 9.232C317.828 9.936 317.38 10.464 316.644 10.464C315.908 10.464 315.46 9.936 315.46 9.232ZM320.548 15.568C320.548 16.992 321.684 18.128 323.14 18.128C324.596 18.128 325.732 16.992 325.732 15.568C325.732 14.144 324.596 13.008 323.14 13.008C321.684 13.008 320.548 14.144 320.548 15.568ZM321.956 15.568C321.956 14.864 322.404 14.336 323.14 14.336C323.876 14.336 324.324 14.864 324.324 15.568C324.324 16.272 323.876 16.8 323.14 16.8C322.404 16.8 321.956 16.272 321.956 15.568ZM323.844 6.272L315.22 17.792L316.148 18.512L324.772 6.992L323.844 6.272ZM327.041 6C328.161 7.728 328.737 9.504 328.737 12.4C328.737 15.296 328.161 17.072 327.041 18.8H328.705C329.617 17.264 330.401 15.536 330.401 12.4C330.401 9.264 329.617 7.536 328.705 6H327.041Z"
                fill="#AEAEAE" />
              <path d="M120.638 67.8311L105.629 63.9813L120.638 67.8311Z" fill="#AEAEAE" />
              <path
                d="M1.504 73H3.264V67.848H7.808V73H9.568V61.8H7.808V66.184H3.264V61.8H1.504V73ZM11.5028 68.968C11.5028 71.24 12.9748 73.128 15.4708 73.128C17.9668 73.128 19.4388 71.24 19.4388 68.968C19.4388 66.696 17.9668 64.808 15.4708 64.808C12.9748 64.808 11.5028 66.696 11.5028 68.968ZM13.2628 68.968C13.2628 67.608 14.0148 66.28 15.4708 66.28C16.9268 66.28 17.6788 67.608 17.6788 68.968C17.6788 70.328 16.9268 71.656 15.4708 71.656C14.0148 71.656 13.2628 70.328 13.2628 68.968ZM20.1798 66.408H21.5238V70.248C21.5238 72.232 22.6438 73.128 24.1638 73.128C24.6118 73.128 25.0278 73.048 25.4598 72.84L25.0278 71.432C24.7398 71.608 24.5477 71.656 24.2598 71.656C23.5238 71.656 23.1878 71.256 23.1878 70.344V66.408H25.2838V64.936H23.1878V61.672L21.5238 62.712V64.936H20.1798V66.408ZM30.3565 66.28C31.5565 66.28 32.1645 67.144 32.3085 68.04H28.3885C28.5645 67.032 29.2525 66.28 30.3565 66.28ZM34.0685 68.712C34.0685 66.904 32.9325 64.808 30.3565 64.808C27.7005 64.808 26.5965 67.048 26.5965 68.968C26.5965 70.888 27.7005 73.128 30.3565 73.128C32.1965 73.128 33.1085 72.376 33.8925 70.856L32.4685 70.296C32.0525 71.176 31.2205 71.656 30.3565 71.656C29.0445 71.656 28.3565 70.616 28.3565 69.432H34.0685V68.712ZM35.8753 61.672V73H37.5393V61.672H35.8753ZM42.244 64.808C40.788 64.808 39.764 65.848 39.764 67.096C39.764 68.328 40.564 68.92 41.492 69.304C42.436 69.704 43.412 69.896 43.412 70.6C43.412 71.336 42.964 71.656 42.324 71.656C41.428 71.656 40.868 70.92 40.548 70.2L39.14 70.968C39.556 71.976 40.596 73.128 42.26 73.128C43.988 73.128 45.06 71.96 45.06 70.568C45.06 69.128 43.892 68.648 42.804 68.232C41.972 67.912 41.364 67.656 41.364 67.08C41.364 66.552 41.748 66.28 42.164 66.28C42.676 66.28 43.172 66.504 43.572 67.016L44.628 66.088C44.116 65.288 43.22 64.808 42.244 64.808ZM57.1881 63.56C56.7241 62.728 56.0201 62.04 55.0121 61.784V60.36H53.4121V61.752C52.1161 62.04 51.1721 63.176 51.1721 64.616C51.1721 68.072 55.9401 67.592 55.9401 69.784C55.9401 70.776 55.2361 71.464 54.2281 71.464C52.9481 71.464 52.2921 70.344 51.9721 69.096L50.5321 70.008C50.8681 71.544 51.8921 72.744 53.4121 73.048V74.44H55.0121V73.048C56.6121 72.76 57.7321 71.496 57.7321 69.72C57.7321 66.264 52.9321 66.696 52.9321 64.488C52.9321 63.832 53.3801 63.272 54.1001 63.272C54.7561 63.272 55.3161 63.656 55.7641 64.504L57.1881 63.56ZM63.4623 61.672C60.8223 61.672 59.4303 63.864 59.4303 67.4C59.4303 70.936 60.8223 73.128 63.4623 73.128C66.1023 73.128 67.4943 70.936 67.4943 67.4C67.4943 63.864 66.1023 61.672 63.4623 61.672ZM63.4623 63.208C65.0463 63.208 65.7983 64.776 65.7983 67.4C65.7983 70.024 65.0463 71.592 63.4623 71.592C61.8783 71.592 61.1263 70.024 61.1263 67.4C61.1263 64.776 61.8783 63.208 63.4623 63.208ZM71.2836 61C70.3716 62.536 69.5876 64.264 69.5876 67.4C69.5876 70.536 70.3716 72.264 71.2836 73.8H72.9476C71.8276 72.072 71.2516 70.296 71.2516 67.4C71.2516 64.504 71.8276 62.728 72.9476 61H71.2836ZM78.431 61.672C75.791 61.672 74.399 63.864 74.399 67.4C74.399 70.936 75.791 73.128 78.431 73.128C81.071 73.128 82.463 70.936 82.463 67.4C82.463 63.864 81.071 61.672 78.431 61.672ZM78.431 63.208C80.015 63.208 80.767 64.776 80.767 67.4C80.767 70.024 80.015 71.592 78.431 71.592C76.847 71.592 76.095 70.024 76.095 67.4C76.095 64.776 76.847 63.208 78.431 63.208ZM84.3644 64.232C84.3644 65.656 85.5004 66.792 86.9564 66.792C88.4124 66.792 89.5484 65.656 89.5484 64.232C89.5484 62.808 88.4124 61.672 86.9564 61.672C85.5004 61.672 84.3644 62.808 84.3644 64.232ZM85.7724 64.232C85.7724 63.528 86.2204 63 86.9564 63C87.6924 63 88.1404 63.528 88.1404 64.232C88.1404 64.936 87.6924 65.464 86.9564 65.464C86.2204 65.464 85.7724 64.936 85.7724 64.232ZM90.8604 70.568C90.8604 71.992 91.9964 73.128 93.4524 73.128C94.9084 73.128 96.0444 71.992 96.0444 70.568C96.0444 69.144 94.9084 68.008 93.4524 68.008C91.9964 68.008 90.8604 69.144 90.8604 70.568ZM92.2684 70.568C92.2684 69.864 92.7164 69.336 93.4524 69.336C94.1884 69.336 94.6364 69.864 94.6364 70.568C94.6364 71.272 94.1884 71.8 93.4524 71.8C92.7164 71.8 92.2684 71.272 92.2684 70.568ZM94.1564 61.272L85.5324 72.792L86.4604 73.512L95.0844 61.992L94.1564 61.272ZM97.3539 61C98.4739 62.728 99.0499 64.504 99.0499 67.4C99.0499 70.296 98.4739 72.072 97.3539 73.8H99.0179C99.9299 72.264 100.714 70.536 100.714 67.4C100.714 64.264 99.9299 62.536 99.0179 61H97.3539Z"
                fill="#AEAEAE" />
              <path d="M208.403 148.895L224.862 162.634L208.403 148.895Z" fill="#AEAEAE" />
              <path
                d="M221.504 150.8V162H223.264V157.024H227.312V155.36H223.264V152.464H228.288V150.8H221.504ZM230.125 150.672V162H231.789V150.672H230.125ZM233.57 151.664C233.57 152.304 234.066 152.8 234.722 152.8C235.378 152.8 235.874 152.304 235.874 151.664C235.874 151.024 235.378 150.496 234.722 150.496C234.066 150.496 233.57 151.024 233.57 151.664ZM233.89 153.936V162H235.554V153.936H233.89ZM237.814 156.512C237.814 157.2 238.006 157.776 238.358 158.208C237.766 158.544 237.382 159.12 237.382 159.84C237.382 160.4 237.558 160.816 237.846 161.104C237.43 161.488 237.158 162.048 237.158 162.736C237.158 164.496 238.678 165.392 240.71 165.392C242.518 165.392 244.246 164.608 244.246 162.608C244.246 161.088 243.158 160.304 241.574 160.304H239.798C239.238 160.304 238.966 160.112 238.966 159.68C238.966 159.376 239.11 159.136 239.398 158.976C239.766 159.12 240.214 159.216 240.694 159.216C242.326 159.216 243.59 158.208 243.59 156.512C243.59 156 243.462 155.552 243.238 155.168L244.326 154.56L243.654 153.36L242.166 154.16C241.734 153.936 241.238 153.808 240.694 153.808C239.062 153.808 237.814 154.832 237.814 156.512ZM241.222 161.712C242.006 161.712 242.55 161.968 242.55 162.736C242.55 163.52 241.862 163.952 240.71 163.952C239.478 163.952 238.758 163.44 238.758 162.64C238.758 162.224 238.87 161.92 239.11 161.664C239.366 161.696 239.638 161.712 239.926 161.712H241.222ZM239.43 156.512C239.43 155.712 239.958 155.2 240.694 155.2C241.446 155.2 241.974 155.712 241.974 156.512C241.974 157.312 241.446 157.824 240.694 157.824C239.958 157.824 239.43 157.312 239.43 156.512ZM245.798 150.672V162H247.462V158.592C247.462 156.448 248.726 155.28 249.638 155.28C250.39 155.28 250.902 155.52 250.902 156.752V162H252.566V156.32C252.566 154.608 251.67 153.808 250.182 153.808C248.966 153.808 248.038 154.48 247.462 155.312V150.672H245.798ZM253.961 155.408H255.305V159.248C255.305 161.232 256.425 162.128 257.945 162.128C258.393 162.128 258.809 162.048 259.241 161.84L258.809 160.432C258.521 160.608 258.329 160.656 258.041 160.656C257.305 160.656 256.969 160.256 256.969 159.344V155.408H259.065V153.936H256.969V150.672L255.305 151.712V153.936H253.961V155.408ZM263.385 153.808C261.929 153.808 260.905 154.848 260.905 156.096C260.905 157.328 261.705 157.92 262.633 158.304C263.577 158.704 264.553 158.896 264.553 159.6C264.553 160.336 264.105 160.656 263.465 160.656C262.569 160.656 262.009 159.92 261.689 159.2L260.281 159.968C260.697 160.976 261.737 162.128 263.401 162.128C265.129 162.128 266.201 160.96 266.201 159.568C266.201 158.128 265.033 157.648 263.945 157.232C263.113 156.912 262.505 156.656 262.505 156.08C262.505 155.552 262.889 155.28 263.305 155.28C263.817 155.28 264.313 155.504 264.713 156.016L265.769 155.088C265.257 154.288 264.361 153.808 263.385 153.808ZM278.329 152.56C277.865 151.728 277.161 151.04 276.153 150.784V149.36H274.553V150.752C273.257 151.04 272.313 152.176 272.313 153.616C272.313 157.072 277.081 156.592 277.081 158.784C277.081 159.776 276.377 160.464 275.369 160.464C274.089 160.464 273.433 159.344 273.113 158.096L271.673 159.008C272.009 160.544 273.033 161.744 274.553 162.048V163.44H276.153V162.048C277.753 161.76 278.873 160.496 278.873 158.72C278.873 155.264 274.073 155.696 274.073 153.488C274.073 152.832 274.521 152.272 275.241 152.272C275.897 152.272 276.457 152.656 276.905 153.504L278.329 152.56ZM284.603 150.672C281.963 150.672 280.571 152.864 280.571 156.4C280.571 159.936 281.963 162.128 284.603 162.128C287.243 162.128 288.635 159.936 288.635 156.4C288.635 152.864 287.243 150.672 284.603 150.672ZM284.603 152.208C286.187 152.208 286.939 153.776 286.939 156.4C286.939 159.024 286.187 160.592 284.603 160.592C283.019 160.592 282.267 159.024 282.267 156.4C282.267 153.776 283.019 152.208 284.603 152.208ZM296.549 150C295.637 151.536 294.853 153.264 294.853 156.4C294.853 159.536 295.637 161.264 296.549 162.8H298.213C297.093 161.072 296.517 159.296 296.517 156.4C296.517 153.504 297.093 151.728 298.213 150H296.549ZM303.697 150.672C301.057 150.672 299.665 152.864 299.665 156.4C299.665 159.936 301.057 162.128 303.697 162.128C306.337 162.128 307.729 159.936 307.729 156.4C307.729 152.864 306.337 150.672 303.697 150.672ZM303.697 152.208C305.281 152.208 306.033 153.776 306.033 156.4C306.033 159.024 305.281 160.592 303.697 160.592C302.113 160.592 301.361 159.024 301.361 156.4C301.361 153.776 302.113 152.208 303.697 152.208ZM309.63 153.232C309.63 154.656 310.766 155.792 312.222 155.792C313.678 155.792 314.814 154.656 314.814 153.232C314.814 151.808 313.678 150.672 312.222 150.672C310.766 150.672 309.63 151.808 309.63 153.232ZM311.038 153.232C311.038 152.528 311.486 152 312.222 152C312.958 152 313.406 152.528 313.406 153.232C313.406 153.936 312.958 154.464 312.222 154.464C311.486 154.464 311.038 153.936 311.038 153.232ZM316.126 159.568C316.126 160.992 317.262 162.128 318.718 162.128C320.174 162.128 321.31 160.992 321.31 159.568C321.31 158.144 320.174 157.008 318.718 157.008C317.262 157.008 316.126 158.144 316.126 159.568ZM317.534 159.568C317.534 158.864 317.982 158.336 318.718 158.336C319.454 158.336 319.902 158.864 319.902 159.568C319.902 160.272 319.454 160.8 318.718 160.8C317.982 160.8 317.534 160.272 317.534 159.568ZM319.422 150.272L310.798 161.792L311.726 162.512L320.35 150.992L319.422 150.272ZM322.62 150C323.74 151.728 324.316 153.504 324.316 156.4C324.316 159.296 323.74 161.072 322.62 162.8H324.284C325.196 161.264 325.98 159.536 325.98 156.4C325.98 153.264 325.196 151.536 324.284 150H322.62Z"
                fill="#AEAEAE" />
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M248 91C248 81.4565 245.991 72.0199 242.104 63.3039C238.217 54.5878 232.539 46.7876 225.439 40.4105C218.339 34.0333 209.976 29.2221 200.894 26.2897C191.813 23.3572 182.215 22.3693 172.726 23.3901L172.726 23.39C158.957 24.8713 145.969 30.5212 135.498 39.5841C125.027 48.647 117.573 60.6909 114.132 74.1051C110.692 87.5192 111.429 101.664 116.245 114.648C121.061 127.632 129.726 138.836 141.082 146.762L141.082 146.762C151.276 153.876 163.225 158.061 175.63 158.859C188.036 159.658 200.423 157.041 211.445 151.293C222.467 145.544 231.702 136.884 238.147 126.254C244.592 115.624 248 103.431 248 90.9999L248 91ZM197.859 91C197.859 88.4936 197.331 86.0153 196.31 83.7262C195.289 81.4372 193.798 79.3886 191.933 77.7138C190.069 76.039 187.873 74.7755 185.487 74.0053C183.102 73.2352 180.582 72.9757 178.09 73.2438L178.09 73.2437C174.474 73.6327 171.063 75.1166 168.313 77.4967C165.563 79.8769 163.605 83.0399 162.701 86.5628C161.798 90.0857 161.991 93.8006 163.256 97.2105C164.521 100.62 166.797 103.563 169.779 105.644L169.779 105.644C172.456 107.513 175.594 108.612 178.852 108.822C182.11 109.031 185.363 108.344 188.258 106.834C191.153 105.325 193.578 103.05 195.271 100.259C196.964 97.4669 197.858 94.2647 197.858 90.9999L197.859 91Z"
                fill="#AEAEAE" fill-opacity="0.2" />
              <path
                d="M248 91L247.95 91.4975L248.5 91.5524V91H248ZM225.439 40.4105L225.773 40.0385L225.439 40.4105ZM200.894 26.2897L200.741 26.7655L200.894 26.2897ZM172.726 23.3901L172.272 23.5983L172.422 23.9258L172.78 23.8873L172.726 23.3901ZM172.726 23.39L173.181 23.1819L173.031 22.8544L172.673 22.8929L172.726 23.39ZM135.498 39.5841L135.825 39.9622L135.498 39.5841ZM114.132 74.1051L113.648 73.9808L114.132 74.1051ZM116.245 114.648L116.713 114.474L116.245 114.648ZM141.082 146.762L141.482 147.062L141.793 146.648L141.368 146.352L141.082 146.762ZM141.082 146.762L140.682 146.462L140.372 146.876L140.796 147.172L141.082 146.762ZM175.63 158.859L175.598 159.358L175.63 158.859ZM238.147 126.254L237.72 125.995H237.72L238.147 126.254ZM248 90.9999L248.05 90.5024L247.5 90.4474V90.9999H248ZM197.859 91L197.803 91.4969L198.359 91.5586V91H197.859ZM196.31 83.7262L195.854 83.9299L196.31 83.7262ZM185.487 74.0053L185.641 73.5295L185.487 74.0053ZM178.09 73.2438L177.632 73.4442L177.779 73.7802L178.143 73.741L178.09 73.2438ZM178.09 73.2437L178.548 73.0433L178.401 72.7074L178.036 72.7466L178.09 73.2437ZM162.701 86.5628L162.217 86.4386L162.701 86.5628ZM163.256 97.2105L162.787 97.3844L163.256 97.2105ZM169.779 105.644L170.179 105.944L170.49 105.53L170.065 105.234L169.779 105.644ZM169.779 105.644L169.379 105.344L169.069 105.758L169.493 106.054L169.779 105.644ZM178.852 108.822L178.82 109.321L178.852 108.822ZM188.258 106.834L188.027 106.391L188.258 106.834ZM195.271 100.259L194.843 99.9994H194.843L195.271 100.259ZM197.858 90.9999L197.914 90.503L197.358 90.4413V90.9999H197.858ZM241.647 63.5075C245.506 72.1594 247.5 81.5266 247.5 91H248.5C248.5 81.3863 246.476 71.8803 242.561 63.1002L241.647 63.5075ZM225.105 40.7824C232.153 47.1127 237.789 54.8556 241.647 63.5075L242.561 63.1002C238.645 54.3201 232.925 46.4625 225.773 40.0385L225.105 40.7824ZM200.741 26.7655C209.756 29.6763 218.057 34.4522 225.105 40.7824L225.773 40.0385C218.621 33.6144 210.197 28.7678 201.048 25.8139L200.741 26.7655ZM172.78 23.8873C182.199 22.874 191.726 23.8546 200.741 26.7655L201.048 25.8139C191.899 22.8599 182.231 21.8647 172.673 22.893L172.78 23.8873ZM172.272 23.5982L172.272 23.5983L173.181 23.182L173.181 23.1819L172.272 23.5982ZM135.825 39.9622C146.219 30.9659 159.112 25.3575 172.78 23.8871L172.673 22.8929C158.803 24.3851 145.719 30.0765 135.171 39.2061L135.825 39.9622ZM114.617 74.2293C118.032 60.9138 125.431 48.9585 135.825 39.9622L135.171 39.2061C124.623 48.3356 117.114 60.4681 113.648 73.9808L114.617 74.2293ZM116.713 114.474C111.933 101.586 111.201 87.5448 114.617 74.2293L113.648 73.9808C110.182 87.4936 110.924 101.743 115.776 114.822L116.713 114.474ZM141.368 146.352C130.096 138.484 121.494 127.363 116.713 114.474L115.776 114.822C120.627 127.901 129.356 139.188 140.796 147.172L141.368 146.352ZM140.682 146.462L140.682 146.462L141.482 147.062L141.482 147.062L140.682 146.462ZM140.796 147.172C151.065 154.339 163.102 158.554 175.598 159.358L175.663 158.36C163.348 157.567 151.487 153.414 141.368 146.352L140.796 147.172ZM175.598 159.358C188.095 160.163 200.573 157.527 211.676 151.736L211.214 150.849C200.273 156.555 187.977 159.153 175.663 158.36L175.598 159.358ZM211.676 151.736C222.779 145.945 232.083 137.222 238.575 126.513L237.72 125.995C231.322 136.547 222.155 145.143 211.214 150.849L211.676 151.736ZM238.575 126.513C245.067 115.805 248.5 103.522 248.5 90.9999H247.5C247.5 103.34 244.117 115.443 237.72 125.995L238.575 126.513ZM248.05 90.5024L248.05 90.5024L247.95 91.4975L247.95 91.4975L248.05 90.5024ZM198.359 91C198.359 88.4234 197.816 85.8757 196.767 83.5226L195.854 83.9299C196.846 86.1548 197.359 88.5638 197.359 91H198.359ZM196.767 83.5226C195.717 81.1694 194.184 79.0635 192.268 77.3418L191.599 78.0858C193.412 79.7137 194.861 81.7049 195.854 83.9299L196.767 83.5226ZM192.268 77.3418C190.351 75.6201 188.093 74.3212 185.641 73.5295L185.334 74.4811C187.652 75.2297 189.787 76.4579 191.599 78.0858L192.268 77.3418ZM185.641 73.5295C183.189 72.7378 180.598 72.4711 178.036 72.7467L178.143 73.741C180.565 73.4804 183.015 73.7326 185.334 74.4811L185.641 73.5295ZM178.548 73.0434L178.548 73.0433L177.632 73.4441L177.632 73.4442L178.548 73.0434ZM178.036 72.7466C174.319 73.1465 170.812 74.6719 167.985 77.1187L168.64 77.8748C171.313 75.5612 174.628 74.119 178.143 73.7408L178.036 72.7466ZM167.985 77.1187C165.158 79.5655 163.146 82.8171 162.217 86.4386L163.186 86.687C164.064 83.2628 165.967 80.1883 168.64 77.8748L167.985 77.1187ZM162.217 86.4386C161.288 90.0601 161.487 93.879 162.787 97.3844L163.725 97.0366C162.495 93.7221 162.307 90.1113 163.186 86.687L162.217 86.4386ZM162.787 97.3844C164.088 100.89 166.427 103.915 169.493 106.054L170.065 105.234C167.166 103.211 164.954 100.351 163.725 97.0366L162.787 97.3844ZM169.379 105.344L169.379 105.344L170.179 105.944L170.179 105.944L169.379 105.344ZM178.884 108.323C175.718 108.119 172.667 107.051 170.065 105.234L169.493 106.054C172.245 107.975 175.471 109.105 178.82 109.321L178.884 108.323ZM188.027 106.391C185.213 107.858 182.051 108.527 178.884 108.323L178.82 109.321C182.169 109.536 185.514 108.83 188.489 107.278L188.027 106.391ZM194.843 99.9994C193.198 102.713 190.841 104.924 188.027 106.391L188.489 107.278C191.465 105.726 193.959 103.388 195.698 100.518L194.843 99.9994ZM197.358 90.9999C197.358 94.1733 196.489 97.2859 194.843 99.9994L195.698 100.518C197.438 97.648 198.358 94.3561 198.358 90.9999H197.358ZM197.914 90.503L197.914 90.503L197.803 91.4969L197.803 91.4969L197.914 90.503Z"
                fill="white" />
            </svg>
            <div *ngIf="this.doughnutChartData.datasets[0].data.length > 0" class="chart-container" style="position: relative;">
              <canvas baseChart class="doughnutChart" [data]="doughnutChartData" 
                [type]="doughnutChartType" [options]="doughnutChartOptions">
              </canvas>
            </div>
          </div>

        </div>
      </div>

      <div class="right-panel" style="padding:10px !important;">
        <div class="row">
          <div class="col-12">
            <span class="show" style="margin-left:0px;font-size: 14px !important;">{{daterangepickerModel[0] | date :
              'dd MMM yyyy'}} - {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span>
            <span class="show" style="margin-right:60px;float:right;font-size: 14px !important;"><span
                *ngIf="this.totalBooking >0 || this.totalPreviousBooking>0">%</span>{{ 'dashboard.change' | translate }}
            </span>
            <div class="left-panel1" [ngStyle]="{'height': this.selectBookingValue!=='all' ? '264px':'224px'}">
              <div *ngIf="this.totalBooking===0" class="row">
                <div class="col-8">
                  <div class="bookingdetails">
                    <svg width="12" height="24" viewBox="0 0 16 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M9.12498 10.3973V5.16675C10.1216 5.22579 11.1065 5.41309 12.0552 5.724L13.4427 6.27825L14.5565 3.49275L13.163 2.9355C11.8621 2.48027 10.5018 2.2175 9.12498 2.1555V0H6.87498V2.184C3.78648 2.51325 0.830732 4.209 0.830732 7.51725C0.830732 11.0325 4.03773 12.1245 6.87498 12.8962V18.3795C5.54415 18.3128 4.23429 18.0206 3.00123 17.5155L1.65948 16.8442L0.318481 19.5278L1.65948 20.199C3.30987 20.9126 5.07824 21.3142 6.87498 21.3832V24H9.12498V21.3638C12.8307 21.0263 15.1692 19.008 15.1692 16.0215C15.1692 12.3885 11.9067 11.175 9.12498 10.3973ZM3.83073 7.51725C3.83073 6.03675 5.53698 5.427 6.87498 5.21625V9.7725C4.82373 9.1605 3.83073 8.613 3.83073 7.51725ZM9.12498 18.3495V13.5292C11.174 14.1645 12.1692 14.7765 12.1692 16.0215C12.1692 17.6655 10.4292 18.1882 9.12498 18.3495Z"
                        fill="#AEAEAE" />
                    </svg>
                    <span class="show1"><span *ngIf="this.selectBookingValue==='all'"
                        style="text-transform: capitalize;"> {{'dashboard.Total' | translate }}</span><span
                        *ngIf="this.selectBookingValue!=='all'" style="text-transform: capitalize;"> <span
                          *ngIf="this.selectBookingValue==='flight'">{{ 'dashboard.flight' | translate }}</span> <span
                          *ngIf="this.selectBookingValue==='hotel'">{{ 'dashboard.hotel' | translate }}</span> <span
                          *ngIf="this.selectBookingValue==='car'">{{ 'dashboard.car' | translate }}</span> </span>
                      {{'dashboard.Spend' | translate
                      }}:
                    </span>
                    <span class="show2">{{'dashboard.na' | translate }}</span>
                  </div>
                  <div class="bookingdetails">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M18.75 13.5C17.7117 13.5 16.6966 13.8079 15.8333 14.3848C14.9699 14.9617 14.297 15.7816 13.8996 16.7409C13.5023 17.7002 13.3983 18.7558 13.6009 19.7742C13.8035 20.7926 14.3035 21.7281 15.0377 22.4623C15.7719 23.1965 16.7074 23.6966 17.7258 23.8991C18.7442 24.1017 19.7998 23.9977 20.7591 23.6004C21.7184 23.203 22.5383 22.5301 23.1152 21.6667C23.6921 20.8034 24 19.7884 24 18.75C24 17.3576 23.4469 16.0223 22.4623 15.0377C21.4777 14.0531 20.1424 13.5 18.75 13.5ZM18.5303 20.7803C18.3896 20.9209 18.1989 20.9998 18 20.9998C17.8011 20.9998 17.6104 20.9209 17.4698 20.7803L15.4395 18.75L16.5 17.6895L18 19.1895L21 16.1895L22.0605 17.25L18.5303 20.7803Z"
                        fill="#AEAEAE" />
                      <path
                        d="M12 21.75H3C2.80109 21.75 2.61032 21.671 2.46967 21.5303C2.32902 21.3897 2.25 21.1989 2.25 21V7.5C2.25 7.30109 2.32902 7.11032 2.46967 6.96967C2.61032 6.82902 2.80109 6.75 3 6.75H21C21.1989 6.75 21.3897 6.82902 21.5303 6.96967C21.671 7.11032 21.75 7.30109 21.75 7.5V12H23.25V4.5C23.25 4.10218 23.092 3.72064 22.8107 3.43934C22.5294 3.15804 22.1478 3 21.75 3H18V0.75C18 0.551088 17.921 0.360322 17.7803 0.21967C17.6397 0.0790176 17.4489 0 17.25 0C17.0511 0 16.8603 0.0790176 16.7197 0.21967C16.579 0.360322 16.5 0.551088 16.5 0.75V3H7.5V0.75C7.5 0.551088 7.42098 0.360322 7.28033 0.21967C7.13968 0.0790176 6.94891 0 6.75 0C6.55109 0 6.36032 0.0790176 6.21967 0.21967C6.07902 0.360322 6 0.551088 6 0.75V3H2.25C1.85218 3 1.47064 3.15804 1.18934 3.43934C0.908035 3.72064 0.75 4.10218 0.75 4.5V21.75C0.75 22.1478 0.908035 22.5294 1.18934 22.8107C1.47064 23.092 1.85218 23.25 2.25 23.25H12V21.75Z"
                        fill="#AEAEAE" />
                    </svg>
                    <span class="show1"><span *ngIf="this.selectBookingValue==='all'"
                        style="text-transform: capitalize;">{{'dashboard.Total' | translate }}</span><span
                        *ngIf="this.selectBookingValue!=='all'" style="text-transform: capitalize;"> <span
                          *ngIf="this.selectBookingValue==='flight'">{{ 'dashboard.flight' | translate }}</span> <span
                          *ngIf="this.selectBookingValue==='hotel'">{{ 'dashboard.hotel' | translate }}</span> <span
                          *ngIf="this.selectBookingValue==='car'">{{ 'dashboard.car' | translate }}</span> </span>
                      {{'dashboard.bookings' |
                      translate }}:</span>
                    <span class="show2">{{'dashboard.na' | translate }}</span>
                  </div>
                  <div class="bookingdetails">
                    <svg width="18" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M2.32505 5.77499C2.17505 5.39999 1.65005 5.32499 1.35005 5.47499C0.975051 5.69999 0.825051 6.14999 1.05005 6.52499L10.7251 23.25C10.8751 23.475 11.1001 23.625 11.4001 23.625C11.5501 23.625 11.6251 23.625 11.7751 23.55C12.1501 23.325 12.2251 22.875 12.0751 22.5L2.32505 5.77499Z"
                        fill="#AEAEAE" />
                      <path
                        d="M17.5501 1.64999C17.4751 1.49999 17.2501 1.34999 17.0251 1.34999C16.8001 1.34999 16.5751 1.34999 16.4251 1.57499C15.0001 2.84999 13.5001 2.99999 11.9251 2.99999H10.7251C8.5501 2.99999 6.3001 3.22499 4.6501 6.14999C4.5001 6.37499 4.5001 6.67499 4.6501 6.89999L9.2251 14.85C9.3751 15.075 9.6001 15.225 9.9001 15.225C10.2001 15.225 10.4251 15.075 10.5751 14.85C11.8501 12.6 13.3501 12.45 15.4501 12.45H16.4251C18.2251 12.45 20.1751 12.3 21.9751 10.725C22.2751 10.5 22.2751 10.125 22.1251 9.82499L17.5501 1.64999Z"
                        fill="#AEAEAE" />
                    </svg>

                    <span class="show1"><span *ngIf="this.selectBookingValue==='all'"
                        style="text-transform: capitalize;">{{'dashboard.Overall' | translate
                        }}</span>{{'dashboard.Compliance' | translate }} :</span>
                    <span class="show2">{{'dashboard.na' | translate }}</span>
                  </div>
                  <div class="bookingdetails">
                    <svg width="22" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M14.8094 5.25148C16.1556 5.25148 17.2469 4.16017 17.2469 2.81398C17.2469 1.46779 16.1556 0.37648 14.8094 0.37648C13.4633 0.37648 12.3719 1.46779 12.3719 2.81398C12.3719 4.16017 13.4633 5.25148 14.8094 5.25148Z"
                        fill="#AEAEAE" />
                      <path
                        d="M17.625 17.1652L14.787 14.6152L13.9425 17.4622L15.747 18.5025V22.125C15.747 22.4233 15.8655 22.7095 16.0765 22.9205C16.2875 23.1314 16.5736 23.25 16.872 23.25C17.1704 23.25 17.4565 23.1314 17.6675 22.9205C17.8785 22.7095 17.997 22.4233 17.997 22.125V18C17.9969 17.8425 17.9637 17.6869 17.8996 17.5431C17.8355 17.3993 17.742 17.2706 17.625 17.1652Z"
                        fill="#AEAEAE" />
                      <path
                        d="M9.38921 10.4535L10.5348 10.3492L10.9098 7.99423C10.9384 7.81604 10.984 7.64099 11.046 7.47148L8.48546 8.07598C8.30261 8.11518 8.1324 8.19935 7.99026 8.32088C7.84811 8.4424 7.7385 8.59744 7.67134 8.77198L6.44996 11.9464C6.39331 12.0893 6.36566 12.242 6.36862 12.3957C6.37159 12.5494 6.4051 12.701 6.46722 12.8416C6.52934 12.9822 6.61882 13.1091 6.73045 13.2148C6.84208 13.3205 6.97362 13.4029 7.11742 13.4573C7.26122 13.5116 7.41439 13.5368 7.56803 13.5314C7.72166 13.526 7.87268 13.4901 8.01229 13.4257C8.15189 13.3613 8.2773 13.2698 8.3812 13.1565C8.4851 13.0432 8.56541 12.9104 8.61746 12.7657L8.63096 12.7282L9.38921 10.4535Z"
                        fill="#AEAEAE" />
                      <path
                        d="M22.4625 9.85348C22.4266 9.71015 22.3629 9.57528 22.275 9.45657C22.187 9.33786 22.0766 9.23764 21.9499 9.16162C21.8232 9.0856 21.6828 9.03528 21.5367 9.01353C21.3905 8.99178 21.2415 8.99903 21.0982 9.03486L18.5021 9.68398L14.9801 6.83398C14.6994 6.60648 14.3637 6.45684 14.0069 6.40009C13.65 6.34334 13.2845 6.38147 12.9471 6.51066C12.6096 6.63985 12.3121 6.85555 12.0844 7.1361C11.8566 7.41665 11.7067 7.75217 11.6497 8.10898L9.39972 22.2412C9.38459 22.3273 9.37507 22.4142 9.37122 22.5015C9.37157 22.7793 9.4747 23.0472 9.66076 23.2535C9.84682 23.4598 10.1026 23.59 10.3789 23.6189C10.6552 23.6479 10.9325 23.5736 11.1573 23.4103C11.3821 23.2471 11.5385 23.0064 11.5965 22.7347L15.2913 10.275L17.7071 11.7461C17.8763 11.8483 18.0641 11.916 18.2596 11.9453C18.4552 11.9747 18.6545 11.9651 18.8463 11.9171L21.6446 11.2174C21.9336 11.1448 22.182 10.9606 22.3353 10.7051C22.4887 10.4496 22.5344 10.1438 22.4625 9.85461V9.85348Z"
                        fill="#AEAEAE" />
                      <path
                        d="M4.12495 21.375C4.41421 21.3753 4.6971 21.4599 4.93908 21.6184L7.39421 14.2522C7.21132 14.2409 7.03106 14.203 6.85908 14.1397C6.7948 14.1123 6.73219 14.0812 6.67158 14.0464L6.11808 15.7065L4.16808 15.0562C4.02788 15.0095 3.87985 14.9909 3.73244 15.0014C3.58503 15.0119 3.44114 15.0513 3.30898 15.1175C3.17683 15.1836 3.05899 15.2751 2.96221 15.3868C2.86544 15.4985 2.79161 15.6281 2.74495 15.7684L0.96858 21.0326C0.874978 21.3158 0.897405 21.6245 1.03095 21.8912C1.16449 22.1578 1.39827 22.3607 1.68108 22.4554L2.63583 22.7734C2.66089 22.3951 2.82853 22.0404 3.10491 21.7808C3.38128 21.5213 3.74582 21.3763 4.12495 21.375Z"
                        fill="#AEAEAE" />
                      <path
                        d="M4.125 23.625C4.53921 23.625 4.875 23.2892 4.875 22.875C4.875 22.4608 4.53921 22.125 4.125 22.125C3.71079 22.125 3.375 22.4608 3.375 22.875C3.375 23.2892 3.71079 23.625 4.125 23.625Z"
                        fill="#AEAEAE" />
                    </svg>

                    <span class="show1">{{'dashboard.TotalTravelers' | translate }}:</span>
                    <span class="show2">{{'dashboard.na' | translate }}</span>
                  </div>
                  <div *ngIf="this.selectBookingValue!=='all'" class="bookingdetails">
                    <svg width="18" height="19" viewBox="0 0 25 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M3.37597 4.58984C5.11811 6.8466 9.55643 10.3035 13.3726 6.07692C17.1888 1.85035 21.1242 5.23241 22.6148 7.45176"
                        stroke="#AEAEAE" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                      <path
                        d="M2.38525 11.2495C4.12739 13.5062 8.56571 16.9631 12.3819 12.7366C16.1981 8.50999 20.1334 11.892 21.6241 14.1114"
                        stroke="#AEAEAE" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>

                    <span class="show1">{{'dashboard.Avg.Price' | translate }}<span
                        *ngIf="this.selectBookingValue==='car'">{{'dashboard.Day' | translate }}</span><span
                        *ngIf="this.selectBookingValue==='hotel'">{{'dashboard.Night' | translate }}</span>
                      <span *ngIf="this.selectBookingValue==='flight'">{{'dashboard.Booking' | translate }}</span>:
                    </span>
                    <span class="show2">n/a</span>
                  </div>
                </div>
                <div class="col-4" style="text-align: center;">
                  <div class="bookingdetails1">
                    <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png"> <span
                      class="show1"> 0%</span>
                  </div>
                  <div class="bookingdetails1">
                    <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png"> <span
                      class="show1"> 0%</span>
                  </div>
                  <div class="bookingdetails1">
                    <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png"> <span
                      class="show1"> 0%</span>
                  </div>
                  <div class="bookingdetails1">
                    <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png"> <span
                      class="show1"> 0%</span>
                  </div>
                  <div *ngIf="this.selectBookingValue!=='all'" class="bookingdetails1">
                    <img style="margin-right:3px;position: relative;top: -4px;" src="assets/images/Line111.png"> <span
                      class="show1"> 0%</span>
                  </div>
                </div>
              </div>
              <div *ngIf="this.totalBooking > 0" class="row">
                <div class="col-9">
                  <div class="bookingdetails">
                    <svg width="12" height="24" viewBox="0 0 16 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M9.12486 10.3973V5.16675C10.1215 5.22579 11.1064 5.41309 12.0551 5.724L13.4426 6.27825L14.5564 3.49275L13.1629 2.9355C11.862 2.48027 10.5017 2.2175 9.12486 2.1555V0H6.87486V2.184C3.78636 2.51325 0.830609 4.209 0.830609 7.51725C0.830609 11.0325 4.03761 12.1245 6.87486 12.8962V18.3795C5.54402 18.3128 4.23417 18.0206 3.00111 17.5155L1.65936 16.8442L0.318359 19.5278L1.65936 20.199C3.30975 20.9126 5.07812 21.3142 6.87486 21.3832V24H9.12486V21.3638C12.8306 21.0263 15.1691 19.008 15.1691 16.0215C15.1691 12.3885 11.9066 11.175 9.12486 10.3973ZM3.83061 7.51725C3.83061 6.03675 5.53686 5.427 6.87486 5.21625V9.7725C4.82361 9.1605 3.83061 8.613 3.83061 7.51725ZM9.12486 18.3495V13.5292C11.1739 14.1645 12.1691 14.7765 12.1691 16.0215C12.1691 17.6655 10.4291 18.1882 9.12486 18.3495Z"
                        fill="#8936F3" />
                    </svg>
                    <span class="show3"><span *ngIf="this.selectBookingValue==='all'"
                        style="text-transform: capitalize;"> {{'dashboard.Total' | translate }}</span><span
                        *ngIf="this.selectBookingValue!=='all'" style="text-transform: capitalize;"> <span
                          *ngIf="this.selectBookingValue==='flight'">{{ 'dashboard.flight' | translate }}</span> <span
                          *ngIf="this.selectBookingValue==='hotel'">{{ 'dashboard.hotel' | translate }}</span> <span
                          *ngIf="this.selectBookingValue==='car'">{{ 'dashboard.car' | translate }}</span>
                      </span>{{'dashboard.Spend' | translate
                      }}: </span>
                    <span class="show4">{{this.totalSpend | currency : getCurrencySymbol(this.currency) : 'code' :
                      "1.2-2" }}</span>
                  </div>
                  <div class="bookingdetails">
                    <svg width="18" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M18.75 13.5C17.7117 13.5 16.6966 13.8079 15.8333 14.3848C14.9699 14.9617 14.297 15.7816 13.8996 16.7409C13.5023 17.7002 13.3983 18.7558 13.6009 19.7742C13.8035 20.7926 14.3035 21.7281 15.0377 22.4623C15.7719 23.1965 16.7074 23.6966 17.7258 23.8991C18.7442 24.1017 19.7998 23.9977 20.7591 23.6004C21.7184 23.203 22.5383 22.5301 23.1152 21.6667C23.6921 20.8034 24 19.7884 24 18.75C24 17.3576 23.4469 16.0223 22.4623 15.0377C21.4777 14.0531 20.1424 13.5 18.75 13.5ZM18.5303 20.7803C18.3896 20.9209 18.1989 20.9998 18 20.9998C17.8011 20.9998 17.6104 20.9209 17.4698 20.7803L15.4395 18.75L16.5 17.6895L18 19.1895L21 16.1895L22.0605 17.25L18.5303 20.7803Z"
                        fill="#8936F3" />
                      <path
                        d="M12 21.75H3C2.80109 21.75 2.61032 21.671 2.46967 21.5303C2.32902 21.3897 2.25 21.1989 2.25 21V7.5C2.25 7.30109 2.32902 7.11032 2.46967 6.96967C2.61032 6.82902 2.80109 6.75 3 6.75H21C21.1989 6.75 21.3897 6.82902 21.5303 6.96967C21.671 7.11032 21.75 7.30109 21.75 7.5V12H23.25V4.5C23.25 4.10218 23.092 3.72064 22.8107 3.43934C22.5294 3.15804 22.1478 3 21.75 3H18V0.75C18 0.551088 17.921 0.360322 17.7803 0.21967C17.6397 0.0790176 17.4489 0 17.25 0C17.0511 0 16.8603 0.0790176 16.7197 0.21967C16.579 0.360322 16.5 0.551088 16.5 0.75V3H7.5V0.75C7.5 0.551088 7.42098 0.360322 7.28033 0.21967C7.13968 0.0790176 6.94891 0 6.75 0C6.55109 0 6.36032 0.0790176 6.21967 0.21967C6.07902 0.360322 6 0.551088 6 0.75V3H2.25C1.85218 3 1.47064 3.15804 1.18934 3.43934C0.908035 3.72064 0.75 4.10218 0.75 4.5V21.75C0.75 22.1478 0.908035 22.5294 1.18934 22.8107C1.47064 23.092 1.85218 23.25 2.25 23.25H12V21.75Z"
                        fill="#8936F3" />
                    </svg>

                    <span class="show3"><span *ngIf="this.selectBookingValue==='all'"
                        style="text-transform: capitalize;">{{'dashboard.Total' | translate }} </span><span
                        *ngIf="this.selectBookingValue!=='all'" style="text-transform: capitalize;">
                        {{this.selectBookingValue}}</span> {{'dashboard.bookings' | translate }}: </span>
                    <span class="show4">{{this.totalBooking}}</span>
                  </div>
                  <div class="bookingdetails">
                    <svg width="18" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M2.32505 4.77499C2.17505 4.39999 1.65005 4.32499 1.35005 4.47499C0.975051 4.69999 0.825051 5.14999 1.05005 5.52499L10.7251 22.25C10.8751 22.475 11.1001 22.625 11.4001 22.625C11.5501 22.625 11.6251 22.625 11.7751 22.55C12.1501 22.325 12.2251 21.875 12.0751 21.5L2.32505 4.77499Z"
                        fill="#8936F3" />
                      <path
                        d="M17.5501 0.649991C17.4751 0.499991 17.2501 0.349991 17.0251 0.349991C16.8001 0.349991 16.5751 0.349991 16.4251 0.574991C15.0001 1.84999 13.5001 1.99999 11.9251 1.99999H10.7251C8.5501 1.99999 6.3001 2.22499 4.6501 5.14999C4.5001 5.37499 4.5001 5.67499 4.6501 5.89999L9.2251 13.85C9.3751 14.075 9.6001 14.225 9.9001 14.225C10.2001 14.225 10.4251 14.075 10.5751 13.85C11.8501 11.6 13.3501 11.45 15.4501 11.45H16.4251C18.2251 11.45 20.1751 11.3 21.9751 9.72499C22.2751 9.49999 22.2751 9.12499 22.1251 8.82499L17.5501 0.649991Z"
                        fill="#8936F3" />
                    </svg>
                    <span class="show3"><span *ngIf="this.selectBookingValue==='all'"
                        style="text-transform: capitalize;">{{'dashboard.Overall' | translate }}</span>
                      {{'dashboard.Compliance' | translate }}: </span>
                    <span class="show4">{{this.totalCompliance}}</span>
                  </div>
                  <div class="bookingdetails">
                    <svg width="22" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M14.8093 5.25148C16.1555 5.25148 17.2468 4.16017 17.2468 2.81398C17.2468 1.46779 16.1555 0.37648 14.8093 0.37648C13.4631 0.37648 12.3718 1.46779 12.3718 2.81398C12.3718 4.16017 13.4631 5.25148 14.8093 5.25148Z"
                        fill="#8936F3" />
                      <path
                        d="M17.6249 17.1652L14.7869 14.6152L13.9424 17.4622L15.7469 18.5025V22.125C15.7469 22.4233 15.8654 22.7095 16.0764 22.9205C16.2874 23.1314 16.5735 23.25 16.8719 23.25C17.1703 23.25 17.4564 23.1314 17.6674 22.9205C17.8784 22.7095 17.9969 22.4233 17.9969 22.125V18C17.9967 17.8425 17.9635 17.6869 17.8995 17.5431C17.8354 17.3993 17.7418 17.2706 17.6249 17.1652Z"
                        fill="#8936F3" />
                      <path
                        d="M9.38921 10.4535L10.5348 10.3492L10.9098 7.99423C10.9384 7.81604 10.984 7.64099 11.046 7.47148L8.48546 8.07598C8.30261 8.11518 8.1324 8.19935 7.99026 8.32088C7.84811 8.4424 7.7385 8.59744 7.67134 8.77198L6.44996 11.9464C6.39331 12.0893 6.36566 12.242 6.36862 12.3957C6.37159 12.5494 6.4051 12.701 6.46722 12.8416C6.52934 12.9822 6.61882 13.1091 6.73045 13.2148C6.84208 13.3205 6.97362 13.4029 7.11742 13.4573C7.26122 13.5116 7.41439 13.5368 7.56803 13.5314C7.72166 13.526 7.87268 13.4901 8.01229 13.4257C8.15189 13.3613 8.2773 13.2698 8.3812 13.1565C8.4851 13.0432 8.56541 12.9104 8.61746 12.7657L8.63096 12.7282L9.38921 10.4535Z"
                        fill="#8936F3" />
                      <path
                        d="M22.4626 9.85348C22.4268 9.71015 22.3631 9.57528 22.2751 9.45657C22.1872 9.33786 22.0767 9.23764 21.95 9.16162C21.8233 9.0856 21.6829 9.03528 21.5368 9.01353C21.3906 8.99178 21.2417 8.99903 21.0983 9.03486L18.5022 9.68398L14.9802 6.83398C14.6995 6.60648 14.3638 6.45684 14.007 6.40009C13.6501 6.34334 13.2846 6.38147 12.9472 6.51066C12.6097 6.63985 12.3122 6.85555 12.0845 7.1361C11.8568 7.41665 11.7069 7.75217 11.6498 8.10898L9.39984 22.2412C9.38471 22.3273 9.37519 22.4142 9.37134 22.5015C9.37169 22.7793 9.47483 23.0472 9.66088 23.2535C9.84694 23.4598 10.1028 23.59 10.3791 23.6189C10.6554 23.6479 10.9326 23.5736 11.1574 23.4103C11.3822 23.2471 11.5386 23.0064 11.5966 22.7347L15.2915 10.275L17.7072 11.7461C17.8765 11.8483 18.0643 11.916 18.2598 11.9453C18.4553 11.9747 18.6547 11.9651 18.8465 11.9171L21.6447 11.2174C21.9337 11.1448 22.1821 10.9606 22.3355 10.7051C22.4888 10.4496 22.5345 10.1438 22.4626 9.85461V9.85348Z"
                        fill="#8936F3" />
                      <path
                        d="M4.12495 21.375C4.41421 21.3753 4.6971 21.4599 4.93908 21.6184L7.39421 14.2522C7.21132 14.2409 7.03106 14.203 6.85908 14.1397C6.7948 14.1123 6.73219 14.0812 6.67158 14.0464L6.11808 15.7065L4.16808 15.0562C4.02788 15.0095 3.87985 14.9909 3.73244 15.0014C3.58503 15.0119 3.44114 15.0513 3.30898 15.1175C3.17683 15.1836 3.05899 15.2751 2.96221 15.3868C2.86544 15.4985 2.79161 15.6281 2.74495 15.7684L0.96858 21.0326C0.874978 21.3158 0.897405 21.6245 1.03095 21.8912C1.16449 22.1578 1.39827 22.3607 1.68108 22.4554L2.63583 22.7734C2.66089 22.3951 2.82853 22.0404 3.10491 21.7808C3.38128 21.5213 3.74582 21.3763 4.12495 21.375Z"
                        fill="#8936F3" />
                      <path
                        d="M4.125 23.625C4.53921 23.625 4.875 23.2892 4.875 22.875C4.875 22.4608 4.53921 22.125 4.125 22.125C3.71079 22.125 3.375 22.4608 3.375 22.875C3.375 23.2892 3.71079 23.625 4.125 23.625Z"
                        fill="#8936F3" />
                    </svg>
                    <span class="show3"> {{'dashboard.Total' | translate }}<span *ngIf="this.selectBookingValue!=='all'"
                        style="text-transform: capitalize;"><span *ngIf="this.selectBookingValue==='flight'">{{
                          'dashboard.flight' | translate }}</span> <span *ngIf="this.selectBookingValue==='hotel'">{{
                          'dashboard.hotel' | translate }}</span> <span *ngIf="this.selectBookingValue==='car'">{{
                          'dashboard.car' | translate }}</span></span> {{'dashboard.Travelers' | translate }}: </span>
                    <span class="show4">{{this.totalTraveler}}</span>
                  </div>
                  <div *ngIf="this.selectBookingValue!=='all'" class="bookingdetails">
                    <svg width="18" height="19" viewBox="0 0 25 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M3.37597 4.58984C5.11811 6.8466 9.55643 10.3035 13.3726 6.07692C17.1888 1.85035 21.1242 5.23241 22.6148 7.45176"
                        stroke="#8936F3" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                      <path
                        d="M2.38525 11.2495C4.12739 13.5062 8.56571 16.9631 12.3819 12.7366C16.1981 8.50999 20.1334 11.892 21.6241 14.1114"
                        stroke="#8936F3" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>

                    <span class="show3">{{'dashboard.Avg.Price' | translate }}<span
                        *ngIf="this.selectBookingValue==='car'">{{'dashboard.Day' | translate }}</span><span
                        *ngIf="this.selectBookingValue==='hotel'">{{'dashboard.Night' | translate }}</span>
                      <span *ngIf="this.selectBookingValue==='flight'">{{'dashboard.Booking' | translate }}</span>:
                    </span>
                    <span class="show4">{{this.avgPriceDay | currency : getCurrencySymbol(this.currency) : 'code' :
                      "1.2-2"}}</span>
                  </div>

                </div>
                <div class="col-3" style="text-align: end;padding-left:0px !important;">
                  <div class="bookingdetails1">
                    <div class="row">
                      <div class="col-3" style="padding-right:6px !important;">
                        <img *ngIf="showChangeReport(this.totalSpend,this.totalPreviousSpend)==='positive'"
                          style="max-width: 40px;max-height: 16px;position: relative;top: -4px;"
                          src="assets/images/positive.png">
                        <img *ngIf="showChangeReport(this.totalSpend,this.totalPreviousSpend)==='negative'"
                          style="max-width: 40px;max-height: 16px;position: relative;top: -4px;"
                          src="assets/images/negative.png">
                      </div>
                      <div class="col-9" style="padding-left:0px !important;"> <span class="show3">
                          {{percentageDiff(this.totalSpend,this.totalPreviousSpend)}}%</span></div>
                    </div>
                  </div>
                  <div class="bookingdetails1">
                    <div class="row">
                      <div class="col-3" style="padding-right:6px !important;">
                        <img *ngIf="showChangeReport(this.totalBooking,this.totalPreviousBooking)==='positive'"
                          style="max-width: 40px;max-height: 16px;position: relative;top: -4px;"
                          src="assets/images/positive.png">
                        <img *ngIf="showChangeReport(this.totalBooking,this.totalPreviousBooking)==='negative'"
                          style="max-width: 40px;max-height: 16px;position: relative;top: -4px;"
                          src="assets/images/negative.png">
                      </div>
                      <div class="col-9" style="padding-left:0px !important;"> <span class="show3">
                          {{percentageDiff(this.totalBooking,this.totalPreviousBooking)}}%</span></div>
                    </div>
                  </div>
                  <div class="bookingdetails1">
                    <div class="row">
                      <div class="col-3" style="padding-right:6px !important;">
                        <img *ngIf="showChangeReport(this.totalCompliance,this.totalPreviousCompliance)==='positive'"
                          style="max-width: 40px;max-height: 16px;position: relative;top: -4px;"
                          src="assets/images/positive.png">
                        <img *ngIf="showChangeReport(this.totalCompliance,this.totalPreviousCompliance)==='negative'"
                          style="max-width: 40px;max-height: 16px;position: relative;top: -4px;"
                          src="assets/images/negative.png">
                      </div>
                      <div class="col-9" style="padding-left:0px !important;"><span class="show3">
                          {{percentageDiff(this.totalCompliance,this.totalPreviousCompliance)}}%</span></div>
                    </div>
                  </div>
                  <div class="bookingdetails1">
                    <div class="row">
                      <div class="col-3" style="padding-right:6px !important;">
                        <img *ngIf="showChangeReport(this.totalTraveler,this.totalPreviousTraveler)==='positive'"
                          style="max-width: 40px;max-height: 16px;position: relative;top: -4px;"
                          src="assets/images/positive.png">
                        <img *ngIf="showChangeReport(this.totalTraveler,this.totalPreviousTraveler)==='negative'"
                          style="max-width: 40px;max-height: 16px;position: relative;top: -4px;"
                          src="assets/images/negative.png">
                      </div>
                      <div class="col-9" style="padding-left:0px !important;"> <span class="show3">
                          {{percentageDiff(this.totalTraveler,this.totalPreviousTraveler)}}%</span></div>
                    </div>
                  </div>
                  <div *ngIf="this.selectBookingValue!=='all'" class="bookingdetails1">
                    <div class="row">
                      <div class="col-3" style="padding-right:6px !important;">
                        <img *ngIf="showChangeReport(this.avgPriceDay,this.avgPreviousPriceDay)==='positive'"
                          style="max-width: 40px;max-height: 16px;position: relative;top: -4px;"
                          src="assets/images/positive.png">
                        <img *ngIf="showChangeReport(this.avgPriceDay,this.avgPreviousPriceDay)==='negative'"
                          style="max-width: 40px;max-height: 16px;position: relative;top: -4px;"
                          src="assets/images/negative.png">
                      </div>
                      <div class="col-9" style="padding-left:0px !important;"> <span class="show3">
                          {{percentageDiff(this.avgPriceDay,this.avgPreviousPriceDay)}}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-panel">
        <div class="row">
          <div class="col-12" style="text-align: center;margin-right:20px;">
            <span class="show">{{'dashboard.Total' | translate }} <span *ngIf="this.selectBookingValue!=='all'"
                style="text-transform: uppercase;">
                <span *ngIf="this.selectBookingValue==='flight'">{{ 'dashboard.flight' | translate }}</span> <span
                  *ngIf="this.selectBookingValue==='hotel'">{{ 'dashboard.hotel' | translate }}</span> <span
                  *ngIf="this.selectBookingValue==='car'">{{ 'dashboard.car' | translate }}</span></span>
              {{'dashboard.Spend' | translate }}</span>
            <div *ngIf="this.lineChartLabels && this.lineChartLabels.length ===0" class="left-panel1"
              style="width:auto;padding:20px !important;">
              <svg width="305" class="emptyLineChart" height="102" viewBox="0 0 305 102" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M2.50494 7.76329L62.5029 42.8011L122.501 3.62769L182.499 98.2871L242.497 84.8464L302.495 69.7974"
                  stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M2.50497 9.91736C3.33614 9.91736 4.00993 8.95298 4.00993 7.76337C4.00993 6.57375 3.33614 5.60938 2.50497 5.60938C1.6738 5.60938 1 6.57375 1 7.76337C1 8.95298 1.6738 9.91736 2.50497 9.91736Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M62.5029 44.9551C63.334 44.9551 64.0078 43.9907 64.0078 42.8011C64.0078 41.6115 63.334 40.6471 62.5029 40.6471C61.6717 40.6471 60.9979 41.6115 60.9979 42.8011C60.9979 43.9907 61.6717 44.9551 62.5029 44.9551Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M122.501 5.78174C123.332 5.78174 124.006 4.81739 124.006 3.62781C124.006 2.43822 123.332 1.47388 122.501 1.47388C121.67 1.47388 120.996 2.43822 120.996 3.62781C120.996 4.81739 121.67 5.78174 122.501 5.78174Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M182.499 100.441C183.33 100.441 184.004 99.4768 184.004 98.2872C184.004 97.0976 183.33 96.1333 182.499 96.1333C181.668 96.1333 180.994 97.0976 180.994 98.2872C180.994 99.4768 181.668 100.441 182.499 100.441Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M242.497 87.0005C243.328 87.0005 244.002 86.0361 244.002 84.8465C244.002 83.6569 243.328 82.6925 242.497 82.6925C241.666 82.6925 240.992 83.6569 240.992 84.8465C240.992 86.0361 241.666 87.0005 242.497 87.0005Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M302.495 71.9514C303.326 71.9514 304 70.9871 304 69.7975C304 68.6079 303.326 67.6436 302.495 67.6436C301.664 67.6436 300.99 68.6079 300.99 69.7975C300.99 70.9871 301.664 71.9514 302.495 71.9514Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path d="M2.50494 36.3681L62.5029 43.8351L122.501 71.0613L182.499 31.8878L242.497 69.2232L302.495 48.66"
                  stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M2.50497 38.5219C3.33614 38.5219 4.00993 37.5575 4.00993 36.368C4.00993 35.1784 3.33614 34.2141 2.50497 34.2141C1.6738 34.2141 1 35.1784 1 36.368C1 37.5575 1.6738 38.5219 2.50497 38.5219Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M62.5029 45.9891C63.334 45.9891 64.0078 45.0248 64.0078 43.8351C64.0078 42.6455 63.334 41.6812 62.5029 41.6812C61.6717 41.6812 60.9979 42.6455 60.9979 43.8351C60.9979 45.0248 61.6717 45.9891 62.5029 45.9891Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M122.501 73.215C123.332 73.215 124.006 72.2506 124.006 71.0611C124.006 69.8715 123.332 68.9072 122.501 68.9072C121.67 68.9072 120.996 69.8715 120.996 71.0611C120.996 72.2506 121.67 73.215 122.501 73.215Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M182.499 34.0417C183.33 34.0417 184.004 33.0773 184.004 31.8877C184.004 30.6981 183.33 29.7338 182.499 29.7338C181.668 29.7338 180.994 30.6981 180.994 31.8877C180.994 33.0773 181.668 34.0417 182.499 34.0417Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M242.497 71.3771C243.328 71.3771 244.002 70.4127 244.002 69.2231C244.002 68.0336 243.328 67.0692 242.497 67.0692C241.666 67.0692 240.992 68.0336 240.992 69.2231C240.992 70.4127 241.666 71.3771 242.497 71.3771Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
                <path
                  d="M302.495 50.814C303.326 50.814 304 49.8496 304 48.66C304 47.4705 303.326 46.5061 302.495 46.5061C301.664 46.5061 300.99 47.4705 300.99 48.66C300.99 49.8496 301.664 50.814 302.495 50.814Z"
                  fill="white" stroke="#AEAEAE" stroke-opacity="0.3" stroke-width="2" />
              </svg>

            </div>
            <div *ngIf="this.lineChartLabels && this.lineChartLabels.length >0" class="left-panel1"
              style="padding:10px;">
              <div style="display: block;">
                <canvas baseChart class="linechart" width="400" height="400" [datasets]="lineChartData"
                  [labels]="lineChartLabels" [options]="lineChartOptions" [colors]="lineChartColors"
                  [legend]="lineChartLegend" [type]="lineChartType" [plugins]="lineChartPlugins">
                </canvas>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-panel">
        <div style="text-align: center;">
          <span class="show"> {{'dashboard.approvals' | translate }}</span>
          <div *ngIf="this.pieChartData1.datasets[0].data.length === 0" class="approvalGraph">
            <svg width="151" height="150" viewBox="0 0 151 150" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M132.448 57.8895C127.978 43.6503 118.317 31.5743 105.372 24.0466C92.4264 16.5188 77.1389 14.0865 62.528 17.2299C47.9172 20.3733 35.0452 28.864 26.4542 41.0251C17.8632 53.1863 14.1778 68.1337 16.1257 82.9156L75.3979 75.4255L132.448 57.8895Z"
                fill="#AEAEAE" fill-opacity="0.2" stroke="white" />
              <path d="M16.0257 82.8818C16.797 88.7422 18.4382 94.4596 20.8955 99.8461L75.3223 75.379L16.0257 82.8818Z"
                fill="#AEAEAE" fill-opacity="0.2" stroke="white" />
              <path
                d="M20.8747 99.6975C27.1814 113.513 38.5292 124.438 52.6063 130.245C66.6834 136.053 82.4307 136.307 96.6397 130.956C110.849 125.604 122.451 115.05 129.082 101.443C135.713 87.8357 136.874 72.2 132.328 57.721L75.2775 75.2571L20.8747 99.6975Z"
                fill="#AEAEAE" fill-opacity="0.2" stroke="white" />
            </svg>

          </div>
          <div *ngIf="this.pieChartData1.datasets[0].data.length > 0" class="approvalGraph">
            <div class="chart-container">
              <canvas baseChart class="piechart" [data]="pieChartData1" [labels]="pieChartLabels1"
                [options]="pieChartOptions1"  
                [type]="pieChartType"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="right-panel" style="padding: 10px !important;">
        <div class="col-auto" style="text-align: center;margin-right:10px;margin-top: 15px;">
          <span class="show"> {{'dashboard.Topspenders' | translate }}</span>
          <div *ngIf="this.barChartData1.datasets[0].data.length === 0" class="employeeGraph">
            <svg width="239" height="136" viewBox="0 0 239 136" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.2">
                <path d="M117.827 84.9081L117.827 107.109L-6.20188e-05 107.109L-6.10352e-05 84.9081L117.827 84.9081Z"
                  fill="#AEAEAE" />
                <path d="M239 0L239 22.0493L0.168502 22.0493L0.168503 -1.03693e-05L239 0Z" fill="#AEAEAE" />
                <path d="M101.213 113.288L101.213 135.338L0.168578 135.338L0.168579 113.288L101.213 113.288Z"
                  fill="#AEAEAE" />
                <path d="M212.71 28.4143L212.71 50.615L-9.83648e-07 50.615L0 28.4143L212.71 28.4143Z" fill="#AEAEAE" />
                <path d="M189.243 56.2638L189.243 79.0736L0.168456 79.0736L0.168457 56.2638L189.243 56.2638Z"
                  fill="#AEAEAE" />
              </g>
            </svg>

          </div>
          <div *ngIf="this.barChartData1.datasets[0].data.length > 0" class="employeeGraph">
            <div class="chart-container" style="position: relative;left:-20px;bottom:30px;">
              <canvas baseChart class="barChart" [data]="barChartData1" 
               [options]="barChartOptions2" [legend]="barChartLegend" 
                [type]="barChartType1"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="this.selectBookingValue!=='all'" class="right-panel">
        <div class="col-auto" style="text-align: center;margin-right:10px;margin-top: 15px;">
          <span class="show" style="text-transform:uppercase;">{{this.VehicleMsg}}</span>
          <div *ngIf="this.barChartData5.datasets[0].data.length === 0" class="employeeGraph">
            <svg width="239" height="136" viewBox="0 0 239 136" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.2">
                <path d="M117.827 84.9081L117.827 107.109L-6.20188e-05 107.109L-6.10352e-05 84.9081L117.827 84.9081Z"
                  fill="#AEAEAE" />
                <path d="M239 0L239 22.0493L0.168502 22.0493L0.168503 -1.03693e-05L239 0Z" fill="#AEAEAE" />
                <path d="M101.213 113.288L101.213 135.338L0.168578 135.338L0.168579 113.288L101.213 113.288Z"
                  fill="#AEAEAE" />
                <path d="M212.71 28.4143L212.71 50.615L-9.83648e-07 50.615L0 28.4143L212.71 28.4143Z" fill="#AEAEAE" />
                <path d="M189.243 56.2638L189.243 79.0736L0.168456 79.0736L0.168457 56.2638L189.243 56.2638Z"
                  fill="#AEAEAE" />
              </g>
            </svg>

          </div>
          <div *ngIf="this.barChartData5.datasets[0].data.length > 0" class="employeeGraph">
            <div class="chart-container" style="position: relative;left:-20px;bottom:30px;">
              <canvas baseChart class="barChart" [data]="barChartData5" 
                 [options]="barChartOptions5" 
                [type]="barChartType1"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="divideline">
        <div>
          <div class="subHeading"> {{'dashboard.Uniquetravelers' | translate }}</div>
          <div class="underline2"></div>
        </div>

      </div>

      <div class="right-panel11" style="margin-left:-10px;"
        [ngStyle]="{'height': this.weeklyData.length > 1 ?'345px':'162px'}">
        <div #setHeightOfUniqueTravelerOnMobile onclick="openUniqueBox(this);" class="uniquetravelerbox"
          [ngStyle]="{'height': this.weeklyData.length > 1 ?'285px':'162px'}">
          <div class="row" style="margin-left: 0px !important; margin-right: 0px !important;background: #F7F7F9;">

            <div class="date-box-header" style="padding:0px !important;">
              <span class=""
                style="font-size: 12px; color: #8936F3;font-family: var(--globalFontfamilyr);font-weight: bold;; line-height: 12px;">{{daterangepickerModel[0]
                | date : 'dd MMM yyyy'}} - {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span><br>
              <span class="" style="text-align: center;"> <svg width="24" style="margin-left:20px;" height="24"
                  viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M6.75 0.75C6.75 0.551088 6.67098 0.360322 6.53033 0.21967C6.38968 0.0790176 6.19891 0 6 0C5.80109 0 5.61032 0.0790176 5.46967 0.21967C5.32902 0.360322 5.25 0.551088 5.25 0.75V3H6.75V0.75Z"
                    fill="black" />
                  <path
                    d="M18.75 0.75C18.75 0.551088 18.671 0.360322 18.5303 0.21967C18.3897 0.0790176 18.1989 0 18 0C17.8011 0 17.6103 0.0790176 17.4697 0.21967C17.329 0.360322 17.25 0.551088 17.25 0.75V3H18.75V0.75Z"
                    fill="black" />
                  <path d="M8.25 15.75H3.75V19.5H8.25V15.75Z" fill="black" />
                  <path d="M8.25 10.5H3.75V14.25H8.25V10.5Z" fill="black" />
                  <path d="M14.25 15.75H9.75V19.5H14.25V15.75Z" fill="black" />
                  <path d="M14.25 10.5H9.75V14.25H14.25V10.5Z" fill="black" />
                  <path d="M20.25 10.5H15.75V14.25H20.25V10.5Z" fill="black" />
                  <path
                    d="M20.25 3.75H3.75C2.95435 3.75 2.19129 4.06607 1.62868 4.62868C1.06607 5.19129 0.75 5.95435 0.75 6.75V20.25C0.75 21.0456 1.06607 21.8087 1.62868 22.3713C2.19129 22.9339 2.95435 23.25 3.75 23.25H20.25C21.0456 23.25 21.8087 22.9339 22.3713 22.3713C22.9339 21.8087 23.25 21.0456 23.25 20.25V6.75C23.25 5.95435 22.9339 5.19129 22.3713 4.62868C21.8087 4.06607 21.0456 3.75 20.25 3.75ZM21.75 20.25C21.75 20.6478 21.592 21.0294 21.3107 21.3107C21.0294 21.592 20.6478 21.75 20.25 21.75H3.75C3.35218 21.75 2.97064 21.592 2.68934 21.3107C2.40804 21.0294 2.25 20.6478 2.25 20.25V8.25H21.75V20.25Z"
                    fill="black" />
                </svg>
              </span>
            </div>


            <div class="traveler-box" [ngStyle]="{'height': this.weeklyData.length >0 ? '105px':'156px'}"
              style="margin: 0px 0px;width:44px !important;">
              <div style="text-align: center;">
                <span class="show11"> {{'dashboard.Total' | translate }}</span>
              </div>
              <div class="row">
                <div class="col-12" style="text-align: center;margin-top: 20px;">
                  <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M9.87299 3.50098C10.7704 3.50098 11.498 2.77344 11.498 1.87598C11.498 0.978514 10.7704 0.250977 9.87299 0.250977C8.97552 0.250977 8.24799 0.978514 8.24799 1.87598C8.24799 2.77344 8.97552 3.50098 9.87299 3.50098Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M11.75 11.4434L9.85798 9.74341L9.29498 11.6414L10.498 12.3349V14.7499C10.498 14.9488 10.577 15.1396 10.7177 15.2802C10.8583 15.4209 11.0491 15.4999 11.248 15.4999C11.4469 15.4999 11.6377 15.4209 11.7783 15.2802C11.919 15.1396 11.998 14.9488 11.998 14.7499V11.9999C11.9979 11.895 11.9758 11.7912 11.933 11.6953C11.8903 11.5995 11.828 11.5137 11.75 11.4434Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M6.25954 6.96896L7.02329 6.89946L7.27329 5.32946C7.29235 5.21066 7.32274 5.09396 7.36404 4.98096L5.65704 5.38396C5.53513 5.41009 5.42166 5.46621 5.3269 5.54722C5.23213 5.62824 5.15906 5.7316 5.11429 5.84796L4.30004 7.96421C4.26227 8.05948 4.24383 8.16132 4.24581 8.26378C4.24779 8.36625 4.27013 8.4673 4.31154 8.56105C4.35296 8.65479 4.41261 8.73936 4.48703 8.80983C4.56145 8.88029 4.64914 8.93525 4.74501 8.97149C4.84087 9.00773 4.94299 9.02453 5.04541 9.02091C5.14783 9.0173 5.24851 8.99334 5.34159 8.95043C5.43466 8.90752 5.51826 8.84653 5.58753 8.77099C5.65679 8.69545 5.71034 8.60689 5.74504 8.51046L5.75404 8.48546L6.25954 6.96896Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M14.975 6.56894C14.9511 6.47338 14.9087 6.38347 14.85 6.30433C14.7914 6.22519 14.7178 6.15837 14.6333 6.1077C14.5489 6.05702 14.4552 6.02347 14.3578 6.00897C14.2604 5.99447 14.1611 5.9993 14.0655 6.02319L12.3348 6.45594L9.98678 4.55594C9.79963 4.40427 9.57587 4.30451 9.33796 4.26667C9.10006 4.22884 8.85639 4.25426 8.63142 4.34039C8.40646 4.42652 8.20811 4.57032 8.05629 4.75735C7.90448 4.94438 7.80455 5.16807 7.76653 5.40594L6.26653 14.8274C6.25644 14.8848 6.25009 14.9428 6.24753 15.0009C6.24776 15.1861 6.31652 15.3647 6.44056 15.5023C6.5646 15.6398 6.73514 15.7266 6.91934 15.7459C7.10354 15.7652 7.28838 15.7157 7.43824 15.6068C7.5881 15.498 7.69239 15.3376 7.73103 15.1564L10.1943 6.84994L11.8048 7.83069C11.9176 7.8988 12.0428 7.94394 12.1731 7.96351C12.3035 7.98307 12.4364 7.97668 12.5643 7.94469L14.4298 7.47819C14.6224 7.42983 14.7881 7.30702 14.8903 7.1367C14.9925 6.96637 15.023 6.76246 14.975 6.56969V6.56894Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M2.75001 14.25C2.94285 14.2502 3.13144 14.3066 3.29276 14.4123L4.92951 9.50151C4.80759 9.49393 4.68741 9.46867 4.57276 9.42651C4.52991 9.40824 4.48817 9.38745 4.44776 9.36426L4.07876 10.471L2.77876 10.0375C2.68529 10.0064 2.58661 9.99393 2.48833 10.0009C2.39006 10.0079 2.29413 10.0342 2.20603 10.0783C2.11792 10.1224 2.03937 10.1834 1.97485 10.2579C1.91033 10.3323 1.86111 10.4188 1.83001 10.5123L0.645761 14.0218C0.58336 14.2105 0.598311 14.4163 0.687339 14.5941C0.776368 14.7719 0.932219 14.9072 1.12076 14.9703L1.75726 15.1823C1.77397 14.9301 1.88573 14.6936 2.06998 14.5206C2.25423 14.3475 2.49726 14.2508 2.75001 14.25Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M2.75 15.75C3.02614 15.75 3.25 15.5261 3.25 15.25C3.25 14.9739 3.02614 14.75 2.75 14.75C2.47386 14.75 2.25 14.9739 2.25 15.25C2.25 15.5261 2.47386 15.75 2.75 15.75Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                  </svg>

                </div>
              </div>
            </div>

            <div class="flight-box" [ngStyle]="{'height': this.weeklyData.length >0 ? '105px':'156px'}"
              style="margin: 0px 0px;">
              <div style="text-align: center;">
                <span class="show11"> {{'dashboard.Flights' | translate }}</span>
              </div>
              <div class="row" style="margin-top: 20px;">
                <div class="col-auto" style="margin-left:10px;width:30px; ">
                  <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M9.87299 3.50098C10.7704 3.50098 11.498 2.77344 11.498 1.87598C11.498 0.978514 10.7704 0.250977 9.87299 0.250977C8.97552 0.250977 8.24799 0.978514 8.24799 1.87598C8.24799 2.77344 8.97552 3.50098 9.87299 3.50098Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M11.75 11.4434L9.85798 9.74341L9.29498 11.6414L10.498 12.3349V14.7499C10.498 14.9488 10.577 15.1396 10.7177 15.2802C10.8583 15.4209 11.0491 15.4999 11.248 15.4999C11.4469 15.4999 11.6377 15.4209 11.7783 15.2802C11.919 15.1396 11.998 14.9488 11.998 14.7499V11.9999C11.9979 11.895 11.9758 11.7912 11.933 11.6953C11.8903 11.5995 11.828 11.5137 11.75 11.4434Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M6.25954 6.96896L7.02329 6.89946L7.27329 5.32946C7.29235 5.21066 7.32274 5.09396 7.36404 4.98096L5.65704 5.38396C5.53513 5.41009 5.42166 5.46621 5.3269 5.54722C5.23213 5.62824 5.15906 5.7316 5.11429 5.84796L4.30004 7.96421C4.26227 8.05948 4.24383 8.16132 4.24581 8.26378C4.24779 8.36625 4.27013 8.4673 4.31154 8.56105C4.35296 8.65479 4.41261 8.73936 4.48703 8.80983C4.56145 8.88029 4.64914 8.93525 4.74501 8.97149C4.84087 9.00773 4.94299 9.02453 5.04541 9.02091C5.14783 9.0173 5.24851 8.99334 5.34159 8.95043C5.43466 8.90752 5.51826 8.84653 5.58753 8.77099C5.65679 8.69545 5.71034 8.60689 5.74504 8.51046L5.75404 8.48546L6.25954 6.96896Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M14.975 6.56894C14.9511 6.47338 14.9087 6.38347 14.85 6.30433C14.7914 6.22519 14.7178 6.15837 14.6333 6.1077C14.5489 6.05702 14.4552 6.02347 14.3578 6.00897C14.2604 5.99447 14.1611 5.9993 14.0655 6.02319L12.3348 6.45594L9.98678 4.55594C9.79963 4.40427 9.57587 4.30451 9.33796 4.26667C9.10006 4.22884 8.85639 4.25426 8.63142 4.34039C8.40646 4.42652 8.20811 4.57032 8.05629 4.75735C7.90448 4.94438 7.80455 5.16807 7.76653 5.40594L6.26653 14.8274C6.25644 14.8848 6.25009 14.9428 6.24753 15.0009C6.24776 15.1861 6.31652 15.3647 6.44056 15.5023C6.5646 15.6398 6.73514 15.7266 6.91934 15.7459C7.10354 15.7652 7.28838 15.7157 7.43824 15.6068C7.5881 15.498 7.69239 15.3376 7.73103 15.1564L10.1943 6.84994L11.8048 7.83069C11.9176 7.8988 12.0428 7.94394 12.1731 7.96351C12.3035 7.98307 12.4364 7.97668 12.5643 7.94469L14.4298 7.47819C14.6224 7.42983 14.7881 7.30702 14.8903 7.1367C14.9925 6.96637 15.023 6.76246 14.975 6.56969V6.56894Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M2.75001 14.25C2.94285 14.2502 3.13144 14.3066 3.29276 14.4123L4.92951 9.50151C4.80759 9.49393 4.68741 9.46867 4.57276 9.42651C4.52991 9.40824 4.48817 9.38745 4.44776 9.36426L4.07876 10.471L2.77876 10.0375C2.68529 10.0064 2.58661 9.99393 2.48833 10.0009C2.39006 10.0079 2.29413 10.0342 2.20603 10.0783C2.11792 10.1224 2.03937 10.1834 1.97485 10.2579C1.91033 10.3323 1.86111 10.4188 1.83001 10.5123L0.645761 14.0218C0.58336 14.2105 0.598311 14.4163 0.687339 14.5941C0.776368 14.7719 0.932219 14.9072 1.12076 14.9703L1.75726 15.1823C1.77397 14.9301 1.88573 14.6936 2.06998 14.5206C2.25423 14.3475 2.49726 14.2508 2.75001 14.25Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M2.75 15.75C3.02614 15.75 3.25 15.5261 3.25 15.25C3.25 14.9739 3.02614 14.75 2.75 14.75C2.47386 14.75 2.25 14.9739 2.25 15.25C2.25 15.5261 2.47386 15.75 2.75 15.75Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                  </svg>
                </div>
                <div class="col-auto" style="margin-left:5px;font-size:16px;width:0px;"
                  [ngStyle]="{'color': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}">
                  %
                </div>
              </div>
            </div>

            <div class="traveler-box" [ngStyle]="{'height': this.weeklyData.length >0 ? '105px':'156px'}"
              style="margin: 0px 0px;">
              <div style="text-align: center;"> <span class="show11"> {{'dashboard.Hotels' | translate }}</span></div>
              <div class="row" style="margin-top: 20px;">
                <div class="col-auto" style="margin-left:10px;width:30px; ">
                  <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M9.87299 3.50098C10.7704 3.50098 11.498 2.77344 11.498 1.87598C11.498 0.978514 10.7704 0.250977 9.87299 0.250977C8.97552 0.250977 8.24799 0.978514 8.24799 1.87598C8.24799 2.77344 8.97552 3.50098 9.87299 3.50098Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M11.75 11.4434L9.85798 9.74341L9.29498 11.6414L10.498 12.3349V14.7499C10.498 14.9488 10.577 15.1396 10.7177 15.2802C10.8583 15.4209 11.0491 15.4999 11.248 15.4999C11.4469 15.4999 11.6377 15.4209 11.7783 15.2802C11.919 15.1396 11.998 14.9488 11.998 14.7499V11.9999C11.9979 11.895 11.9758 11.7912 11.933 11.6953C11.8903 11.5995 11.828 11.5137 11.75 11.4434Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M6.25954 6.96896L7.02329 6.89946L7.27329 5.32946C7.29235 5.21066 7.32274 5.09396 7.36404 4.98096L5.65704 5.38396C5.53513 5.41009 5.42166 5.46621 5.3269 5.54722C5.23213 5.62824 5.15906 5.7316 5.11429 5.84796L4.30004 7.96421C4.26227 8.05948 4.24383 8.16132 4.24581 8.26378C4.24779 8.36625 4.27013 8.4673 4.31154 8.56105C4.35296 8.65479 4.41261 8.73936 4.48703 8.80983C4.56145 8.88029 4.64914 8.93525 4.74501 8.97149C4.84087 9.00773 4.94299 9.02453 5.04541 9.02091C5.14783 9.0173 5.24851 8.99334 5.34159 8.95043C5.43466 8.90752 5.51826 8.84653 5.58753 8.77099C5.65679 8.69545 5.71034 8.60689 5.74504 8.51046L5.75404 8.48546L6.25954 6.96896Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M14.975 6.56894C14.9511 6.47338 14.9087 6.38347 14.85 6.30433C14.7914 6.22519 14.7178 6.15837 14.6333 6.1077C14.5489 6.05702 14.4552 6.02347 14.3578 6.00897C14.2604 5.99447 14.1611 5.9993 14.0655 6.02319L12.3348 6.45594L9.98678 4.55594C9.79963 4.40427 9.57587 4.30451 9.33796 4.26667C9.10006 4.22884 8.85639 4.25426 8.63142 4.34039C8.40646 4.42652 8.20811 4.57032 8.05629 4.75735C7.90448 4.94438 7.80455 5.16807 7.76653 5.40594L6.26653 14.8274C6.25644 14.8848 6.25009 14.9428 6.24753 15.0009C6.24776 15.1861 6.31652 15.3647 6.44056 15.5023C6.5646 15.6398 6.73514 15.7266 6.91934 15.7459C7.10354 15.7652 7.28838 15.7157 7.43824 15.6068C7.5881 15.498 7.69239 15.3376 7.73103 15.1564L10.1943 6.84994L11.8048 7.83069C11.9176 7.8988 12.0428 7.94394 12.1731 7.96351C12.3035 7.98307 12.4364 7.97668 12.5643 7.94469L14.4298 7.47819C14.6224 7.42983 14.7881 7.30702 14.8903 7.1367C14.9925 6.96637 15.023 6.76246 14.975 6.56969V6.56894Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M2.75001 14.25C2.94285 14.2502 3.13144 14.3066 3.29276 14.4123L4.92951 9.50151C4.80759 9.49393 4.68741 9.46867 4.57276 9.42651C4.52991 9.40824 4.48817 9.38745 4.44776 9.36426L4.07876 10.471L2.77876 10.0375C2.68529 10.0064 2.58661 9.99393 2.48833 10.0009C2.39006 10.0079 2.29413 10.0342 2.20603 10.0783C2.11792 10.1224 2.03937 10.1834 1.97485 10.2579C1.91033 10.3323 1.86111 10.4188 1.83001 10.5123L0.645761 14.0218C0.58336 14.2105 0.598311 14.4163 0.687339 14.5941C0.776368 14.7719 0.932219 14.9072 1.12076 14.9703L1.75726 15.1823C1.77397 14.9301 1.88573 14.6936 2.06998 14.5206C2.25423 14.3475 2.49726 14.2508 2.75001 14.25Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M2.75 15.75C3.02614 15.75 3.25 15.5261 3.25 15.25C3.25 14.9739 3.02614 14.75 2.75 14.75C2.47386 14.75 2.25 14.9739 2.25 15.25C2.25 15.5261 2.47386 15.75 2.75 15.75Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                  </svg>
                </div>
                <div class="col-auto" style="margin-left:5px;font-size:16px;width:0px;"
                  [ngStyle]="{'color': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}">
                  %
                </div>
              </div>
            </div>

            <div class="flight-box" [ngStyle]="{'height': this.weeklyData.length >0 ? '105px':'156px'}"
              style="margin: 0px 0px;">
              <div style="text-align: center;">

                <span class="show11">{{'dashboard.Cars' | translate }}</span>
              </div>
              <div class="row" style="margin-top: 20px;">
                <div class="col-auto" style="margin-left:10px;width:30px; ">
                  <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M9.87299 3.50098C10.7704 3.50098 11.498 2.77344 11.498 1.87598C11.498 0.978514 10.7704 0.250977 9.87299 0.250977C8.97552 0.250977 8.24799 0.978514 8.24799 1.87598C8.24799 2.77344 8.97552 3.50098 9.87299 3.50098Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M11.75 11.4434L9.85798 9.74341L9.29498 11.6414L10.498 12.3349V14.7499C10.498 14.9488 10.577 15.1396 10.7177 15.2802C10.8583 15.4209 11.0491 15.4999 11.248 15.4999C11.4469 15.4999 11.6377 15.4209 11.7783 15.2802C11.919 15.1396 11.998 14.9488 11.998 14.7499V11.9999C11.9979 11.895 11.9758 11.7912 11.933 11.6953C11.8903 11.5995 11.828 11.5137 11.75 11.4434Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M6.25954 6.96896L7.02329 6.89946L7.27329 5.32946C7.29235 5.21066 7.32274 5.09396 7.36404 4.98096L5.65704 5.38396C5.53513 5.41009 5.42166 5.46621 5.3269 5.54722C5.23213 5.62824 5.15906 5.7316 5.11429 5.84796L4.30004 7.96421C4.26227 8.05948 4.24383 8.16132 4.24581 8.26378C4.24779 8.36625 4.27013 8.4673 4.31154 8.56105C4.35296 8.65479 4.41261 8.73936 4.48703 8.80983C4.56145 8.88029 4.64914 8.93525 4.74501 8.97149C4.84087 9.00773 4.94299 9.02453 5.04541 9.02091C5.14783 9.0173 5.24851 8.99334 5.34159 8.95043C5.43466 8.90752 5.51826 8.84653 5.58753 8.77099C5.65679 8.69545 5.71034 8.60689 5.74504 8.51046L5.75404 8.48546L6.25954 6.96896Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M14.975 6.56894C14.9511 6.47338 14.9087 6.38347 14.85 6.30433C14.7914 6.22519 14.7178 6.15837 14.6333 6.1077C14.5489 6.05702 14.4552 6.02347 14.3578 6.00897C14.2604 5.99447 14.1611 5.9993 14.0655 6.02319L12.3348 6.45594L9.98678 4.55594C9.79963 4.40427 9.57587 4.30451 9.33796 4.26667C9.10006 4.22884 8.85639 4.25426 8.63142 4.34039C8.40646 4.42652 8.20811 4.57032 8.05629 4.75735C7.90448 4.94438 7.80455 5.16807 7.76653 5.40594L6.26653 14.8274C6.25644 14.8848 6.25009 14.9428 6.24753 15.0009C6.24776 15.1861 6.31652 15.3647 6.44056 15.5023C6.5646 15.6398 6.73514 15.7266 6.91934 15.7459C7.10354 15.7652 7.28838 15.7157 7.43824 15.6068C7.5881 15.498 7.69239 15.3376 7.73103 15.1564L10.1943 6.84994L11.8048 7.83069C11.9176 7.8988 12.0428 7.94394 12.1731 7.96351C12.3035 7.98307 12.4364 7.97668 12.5643 7.94469L14.4298 7.47819C14.6224 7.42983 14.7881 7.30702 14.8903 7.1367C14.9925 6.96637 15.023 6.76246 14.975 6.56969V6.56894Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M2.75001 14.25C2.94285 14.2502 3.13144 14.3066 3.29276 14.4123L4.92951 9.50151C4.80759 9.49393 4.68741 9.46867 4.57276 9.42651C4.52991 9.40824 4.48817 9.38745 4.44776 9.36426L4.07876 10.471L2.77876 10.0375C2.68529 10.0064 2.58661 9.99393 2.48833 10.0009C2.39006 10.0079 2.29413 10.0342 2.20603 10.0783C2.11792 10.1224 2.03937 10.1834 1.97485 10.2579C1.91033 10.3323 1.86111 10.4188 1.83001 10.5123L0.645761 14.0218C0.58336 14.2105 0.598311 14.4163 0.687339 14.5941C0.776368 14.7719 0.932219 14.9072 1.12076 14.9703L1.75726 15.1823C1.77397 14.9301 1.88573 14.6936 2.06998 14.5206C2.25423 14.3475 2.49726 14.2508 2.75001 14.25Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                    <path
                      d="M2.75 15.75C3.02614 15.75 3.25 15.5261 3.25 15.25C3.25 14.9739 3.02614 14.75 2.75 14.75C2.47386 14.75 2.25 14.9739 2.25 15.25C2.25 15.5261 2.47386 15.75 2.75 15.75Z"
                      [ngStyle]="{'fill': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}" />
                  </svg>
                </div>
                <div class="col-auto" style="margin-left:5px;font-size:16px;width:0px;"
                  [ngStyle]="{'color': this.weeklyData.length >0 ?'#47494F':'#AEAEAE'}">
                  %
                </div>
              </div>
            </div>

          </div>
          <div *ngIf="this.weeklyData.length > 0" id="travelerDetails"
            style="overflow: auto;height:170px;overflow-x: hidden;width:auto;background: #F7F7F9;">
            <div class="row" *ngFor="let item of this.weeklyData;let i=index;even as isEven"
              style="height:54px;margin-left: 0px !important; margin-right: 0px !important;"
              [ngStyle]="changeUniqueStyle(i)">

              <div class="{{ isEven? 'date-box':'date-box date-box-transparent'}}">
                <span class=""
                  style="color: #AEAEAE;font-size: 12px; font-family: var(--globalFontfamilyr);font-weight: bold;; line-height: 12px;">{{item.week}}</span>
              </div>


              <div class="{{ isEven? 'traveler-box1':'traveler-box1 traveler-box1-transparent'}}"
                style="text-align:center !important;width:44px !important;color: #AEAEAE;">
                {{item.totalTraveler}}
              </div>

              <div class="{{ isEven? 'flight-box1':'flight-box1 date-box-transparent'}}">
                <div class="row" style="margin-top: 0px;">
                  <div class="col-auto" style="margin-left:10px;width:30px;color: #AEAEAE; ">
                    {{item.filghtTra}}
                  </div>
                  <div class="col-auto" style="margin-right:5px;font-size:14px;width:0px;color: #AEAEAE;">
                    {{item.flightPerc}}%
                  </div>
                </div>
              </div>

              <div class="{{ isEven? 'traveler-box1':'traveler-box1 traveler-box1-transparent'}}">
                <div class="row" style="margin-top: 0px;">
                  <div class="col-auto" style="margin-left:10px;width:30px;color: #AEAEAE; ">
                    {{item.hotelTra}}
                  </div>
                  <div class="col-auto" style="margin-right:5px;font-size:14px;width:0px;color: #AEAEAE;">
                    {{item.hotelPerc}}%
                  </div>
                </div>
              </div>

              <div class="{{ isEven? 'flight-box1':'flight-box1 date-box-transparent'}}">
                <div class="row" style="margin-top: 0px;">
                  <div class="col-auto" style="margin-right:10px;width:30px;color: #AEAEAE; ">
                    {{item.carTra}}
                  </div>
                  <div class="col-auto" style="margin-right:5px;font-size:14px;width:0px;color: #AEAEAE;">
                    {{item.carPerc}}%
                  </div>
                </div>
              </div>

            </div>
          </div>

        </div>
        <div *ngIf="this.weeklyData.length > 2" class="row" style="float: right;padding-left: 30px;margin-top:5px;">
          <div (click)="setHeight()">
            <svg class="arrowcircle" style="cursor:pointer;" width="24" height="24" viewBox="0 0 24 24" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M23.5 12C23.5 18.3513 18.3513 23.5 12 23.5C5.64873 23.5 0.5 18.3513 0.5 12C0.5 5.64873 5.64873 0.5 12 0.5C18.3513 0.5 23.5 5.64873 23.5 12Z"
                fill="white" stroke="#8936F3" />
            </svg>

            <svg class="userdownArrow" style="cursor:pointer;" width="15" height="9" viewBox="0 0 15 9" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M6.31799 8.43958L0.439309 2.5609C-0.146477 1.97512 -0.146477 1.02537 0.439309 0.439584C1.0251 -0.146202 1.97484 -0.146202 2.56063 0.439585L7.37865 5.25761L12.1967 0.439586C12.7825 -0.1462 13.7322 -0.1462 14.318 0.439586C14.9038 1.02537 14.9038 1.97512 14.318 2.56091L8.43931 8.43959C7.85352 9.02537 6.90377 9.02537 6.31799 8.43958Z"
                fill="#8936F3" />
            </svg>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="this.applyBtn || this.applyBtn1" class="row" style="text-align: center;padding-top:30px;">
      <app-loader *ngIf="this.applyBtn || this.applyBtn1" style="margin-right: auto;margin-left: auto;"
        [spinnerStyle]="true"></app-loader>
    </div>
  </div>

</div>