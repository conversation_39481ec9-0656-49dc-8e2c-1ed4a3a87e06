@import "../../variables.scss";
.shadow {
    box-shadow: none !important;
}

.card-div {
    border: none !important;
}

a {
    text-transform: initial !important;
}

.card-div-inner {
    padding: 30px 40px 10px;
    margin-left: 30px;
    width: 100%;
}

.presetSelect {
    width: 430px;
    max-height: 32px;
    line-height: 2em;
    padding-left: 25px;
    border-radius: 8px;
    margin-bottom: 5px;
}

.presetSelect:hover {
    width: 430px;
    max-height: 32px;
    line-height: 2em;
    padding-left: 25px;
    border-radius: 8px;
    margin-bottom: 5px;
    background-color: #D3CCDE !important;
}

.mapName {
    width: 147px;
    height: auto;
    font-family: var(--globalFontfamilyr);
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 24px;
    /* or 171% */
    text-transform: capitalize;
    color: #FFFFFF;
}

.traveler12:hover {
    .showHover {
        min-height: 28px;
        text-align: center;
        width: 116px;
        position: relative;
        top: -10px;
        display: inline-block;
        margin-left: 15px;
        background-color: #43226C;
        font-family: "apercu-r";
        border: 2px solid #43226C;
        z-index: 12;
        opacity: .85;
        border-radius: 8px;
    }
}

.perc12:hover {
    .showHover {
        height: auto;
        text-align: center;
        width: 116px;
        position: relative;
        top: -10px;
        z-index: 12;
        display: inline-block;
        margin-left: 15px;
        background-color: #43226C;
        font-family: "apercu-r";
        border: 2px solid #43226C;
        opacity: .85;
        border-radius: 8px;
    }
}

.traveler13:hover {
    .showHover {
        height: auto;
        text-align: center;
        width: 116px;
        position: relative;
        top: -10px;
        display: inline-block;
        margin-left: 15px;
        background-color: #43226C;
        font-family: "apercu-r";
        border: 2px solid #43226C;
        z-index: 12;
        opacity: .85;
        border-radius: 8px;
    }
}

.perc13:hover {
    .showHover {
        height: auto;
        text-align: center;
        width: 116px;
        position: relative;
        top: -10px;
        z-index: 12;
        display: inline-block;
        margin-left: 15px;
        background-color: #43226C;
        font-family: "apercu-r";
        border: 2px solid #43226C;
        opacity: .85;
        border-radius: 8px;
    }
}

.traveler14:hover {
    .showHover {
        height: auto;
        text-align: center;
        width: 116px;
        position: relative;
        top: -10px;
        display: inline-block;
        margin-left: 15px;
        background-color: #43226C;
        font-family: "apercu-r";
        border: 2px solid #43226C;
        z-index: 12;
        opacity: .85;
        border-radius: 8px;
    }
}

.traveler11:hover {
    .showHover {
        height: auto;
        text-align: center;
        width: 116px;
        position: relative;
        top: -25px;
        display: inline-block;
        margin-left: 70px;
        background-color: #43226C;
        font-family: "apercu-r";
        border: 2px solid #43226C;
        z-index: 16;
        opacity: .85;
        border-radius: 8px;
    }
}

.perc14:hover {
    .showHover {
        height: auto;
        text-align: center;
        width: 116px;
        position: relative;
        top: -10px;
        z-index: 222;
        display: inline-block;
        margin-left: 15px;
        background-color: #43226C;
        font-family: "apercu-r";
        border: 2px solid #43226C;
        opacity: .85;
        border-radius: 8px;
    }
}

.show22 {
    width: 91px;
    height: 24px;
    font-family: var(--globalFontfamilyr);
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 24px;
    /* identical to box height, or 171% */
    text-transform: capitalize;
    color: #FFFFFF;
}

.showHover {
    // height: 28px;
    width: auto;
    display: none;
    margin-top: 10px;
    background-color: #43226C;
    font-family: "apercu-r";
    border: 1px solid;
    opacity: .85;
    border-radius: 8px;
}

.pendingtravelerDetailsBox {
    height: 32px;
    margin-top: 15px;
    display: flex;
    padding: 5px;
    cursor: pointer;
    background: #FEE8EB;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    border-radius: 8px;
}

.pendingtravelerDetailsBox1 {
    height: 32px;
    margin-top: 15px;
    padding: 5px;
    cursor: pointer;
    background: #FC6275;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    border-radius: 8px;
    display: flex
}

.change {
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    line-height: 24px;
    margin-top: 24px;
    text-transform: uppercase;
    color: #AEAEAE;
}

.travelerDetailsBox {
    width: 400px;
    height: 40px;
    cursor: pointer;
    display: flex;
    background: #F7F7F9;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    border-radius: 8px;
    margin-top: 15px;
    padding: 8px;
}

.date-box {
    width: 280px;
    padding-right: 30px;
    padding-left: 10px;
    margin: 0px 0 0 0;
    background: #F1F1F1;
    text-align: center;
    height: 100%;
    padding-top: 6px;
}

.date-box-header {
    width: 280px;
    padding-right: 30px;
    padding-left: 10px;
    margin: 10px 0;
    background: white;
    text-align: center;
}

.doughnutChart {
    display: block !important;
    max-height: 190px !important;
    height: auto !important;
    width: auto !important;
    max-width: 421px !important;
}

.traveler-box {
    background: #E8D7FD;
    border-radius: 8px 8px 0px 0px;
    padding: 10px;
    width: 210px;
    height: 156px;
}

.flight-box {
    background: white;
    width: 210px;
    height: 156px;
    padding: 10px;
}

.traveler-box1 {
    background: #DAC9ED;
    margin-top: 0px;
    width: 210px;
    height: 100%;
    padding-top: 6px;
}

.traveler-box1-bg {
    background: #E8D7FD;
}

.flight-box1 {
    background: #E9E9EB;
    width: 210px;
    margin-top: 0px;
    height: 100%;
    padding-top: 6px;
}

.flight-box1-bg {
    background: #F7F7F9;
}

.date-box-transparent {
    background: transparent !important;
}

.traveler-box1-transparent {
    background: transparent !important;
}

.flight-box1-transparent {
    background: transparent !important;
}

.userdownArrow {
    position: relative;
    left: -19.5px;
    top: 1px;
}

.uniquetravelerbox.active .userdownArrow {
    position: relative;
    left: -19px;
    top: 0px;
    -webkit-transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    transform: rotate(-180deg);
}

.drop-arrow {
    display: grid;
    position: relative;
    top: 142px;
    right: -1095px;
    height: 0px;
}

.arrowcircle {
    position: relative;
    left: -23px;
    top: 16px;
}

.uniquetravelerbox {
    width: auto;
    height: 162px;
    background: #FFFFFF;
    border: 2px solid #8936F3;
    border-radius: 8px;
    overflow: hidden;
}

.uniquetravelerbox.active {
    width: auto;
    height: 162px;
    background: #FFFFFF;
    border: 2px solid #8936F3;
    border-radius: 8px;
    overflow: hidden;
}

.graph {
    background: #F7F7F9;
    border-radius: 8px;
    padding: 20px 15px 15px 20px;
    margin-top: 0px;
    width: 1141px;
    display: inline-block;
}

.employeeGraph {
    width: 407px;
    margin-top: 8px;
    height: 189px;
    background: #FFFFFF;
    border: 1px solid #8936F3;
    box-sizing: border-box;
    border-radius: 8px;
    padding-top: 30px;
}

.left-panel1 {
    background: #FFFFFF;
    border: 1px solid #8936F3;
    box-sizing: border-box;
    border-radius: 8px;
    margin-top: 20px;
    height: 224px;
    width: 472px;
    margin-top: 8px;
    padding: 20px;
}

.left-panel11 {
    background: #FFFFFF;
    border: 1px solid #8936F3;
    box-sizing: border-box;
    border-radius: 8px;
    margin-top: 20px;
    height: 224px;
    width: 502px;
    margin-top: 8px;
    padding: 20px;
}

.bookingdetails {
    margin-left: 5px;
    margin-right: 15px;
    white-space: nowrap;
    height: 50px;
}

.show1 {
    color: #AEAEAE;
    font-weight: 300;
    font-size: 16px;
    margin-left: 20px;
}

.show3 {
    color: #47494F;
    font-weight: 300;
    font-size: 17px;
    margin-left: 10px;
}

.showTitle {
    left: 5px;
    height: 24px;
    width: 35px;
    position: relative;
    color: #413E3B;
    font-family: "apercu-b";
    text-transform: uppercase;
    font-size: 16px;
    line-height: 17px;
    top: 10px;
    white-space: nowrap;
}

.show5 {
    color: #8936F3;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    font-weight: 300;
    font-size: 20px;
    margin-left: 20px;
}

.barChart {
    height: 162px !important;
}

.show4 {
    color: #47494F;
    ;
    font-size: 16px;
    font-weight: bold;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    text-transform: uppercase;
}

.approvalGraph-chart-container {
    display: inline-block;
    width: 70%;
}

.approvalGraph {
    width: 225px;
    height: 187px;
    background: #FFFFFF;
    border: 1px solid #8936F3;
    box-sizing: border-box;
    border-radius: 8px;
    margin-top: 8px;
    padding-top: 10px;
    overflow: hidden;
}

.linechart {
    display: block;
    margin: auto !important;
    height: auto !important;
    width: auto !important;
    max-height: 200px !important;
    max-width: 430px !important;
}

.show2 {
    color: #AEAEAE;
    font-size: 16px;
    font-weight: bold;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    text-transform: uppercase;
}

.dollar {
    font-style: normal;
    font-weight: bold;
    font-size: 40px;
    line-height: 24px;
    text-transform: uppercase;
    color: #AEAEAE;
}

.dollar1 {
    font-style: normal;
    font-weight: bold;
    font-size: 40px;
    line-height: 24px;
    text-transform: uppercase;
    color: #8936F3;
}

.right-panel {
    width: 679px;
    height: 236px;
    background: #F7F7F9;
    border-radius: 0px 0px 8px 8px;
    padding: 20px;
}

.shadow {
    padding-bottom: 200px !important;
}

.first-box {
    width: 848px;
    height: 239px;
    border: 1px solid #8936F3;
    box-sizing: border-box;
    display: flex;
    border-radius: 8px;
}

.tab11 {
    display: block;
    border-radius: 8px;
    padding-right: 5px;
}

.name11 {
    width: 141px;
    height: 24px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    font-style: normal;
    font-weight: 300;
    font-size: 16px;
    line-height: 24px;
    /* identical to box height, or 150% */
    color: #47494F;
}

.name111 {
    width: 121px;
    height: 24px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    font-style: normal;
    font-weight: 300;
    color: #AEAEAE;
    font-size: 16px;
    line-height: 24px;
    /* identical to box height, or 150% */
    color: #47494F;
}

.modalAirportFilterInfo .modal-header::-webkit-scrollbar {
    display: none;
}

::-webkit-scrollbar {
    width: 4px;
    background: #C4C4C4;
    z-index: 15;
    border-radius: 8px;
    overflow-x: hidden;
}

::-webkit-scrollbar-thumb {
    background: #8936F3;
    ;
    border-radius: 8px;
}

.name1111 {
    width: 121px;
    height: 24px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    font-style: normal;
    font-weight: 300;
    font-size: 16px;
    line-height: 24px;
    /* identical to box height, or 150% */
    color: #fff;
}

agm-map {
    height: 239px !important;
}

:host ::ng-deep {
    .agm-map-container-inner {
        height: 189px !important;
        width: 325px !important;
    }
    .gm-ui-hover-effect {
        display: none !important;
    }
    .map-container{
        height: 200px !important;
    }
    .gm-style .gm-style-iw {
        width: 173px !important;
        padding: 15px !important;
        height: auto !important;
        overflow: none !important;
        background: #43226C !important;
        opacity: 0.75 !important;
        border-radius: 8px !important;
    }
    .gm-style-iw-d {
        overflow: hidden !important;
    }
    .gm-style .gm-style-iw-t::after {
        content: "";
        height: 0px !important;
        left: 0;
        position: absolute;
        top: 0;
        width: 15px;
    }
    .ng-dropdown-panel {
        min-width: 365px !important;
        top: 28px !important;
        left: -28px !important;
        background: #F7F7F9 !important;
        right: auto !important;
        bottom: auto !important;
    }
    .ng-option.ng-option-selected {
        color: #716F74 !important;
        background: #D3CCDE !important;
    }
    .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
        font-size: 16px !important;
        color: #716F74 !important;
        font-family: var(--globalFontfamilyr) !important;
        max-height: 32px !important;
        line-height: 2em !important;
        padding-left: 25px !important;
        border-radius: 8px !important;
    }
    .ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {
        background: #D3CCDE !important;
        font-size: 16px !important;
        color: #716F74 !important;
    }
    .ng-select .ng-select-container {
        color: #716F74 !important;
        font-size: 16px !important;
        bottom: 4px !important;
        left: -15px !important;
    }
    .gmnoprint a,
    .gmnoprint span {
        display: none !important;
    }
    //img[src^="https://maps.gstatic.com/mapfiles/api-3/images/google_white5_hdpi.png"]{display:none !important}
    .gmnoprint div {
        background: none !important;
    }
    g :nth-child(12) {
        display: none !important;
    }
    #GMapsID div div a div img {
        display: none !important;
    }
    .ng-clear-wrapper {
        display: none !important;
    }
    .ng-select span {
        box-sizing: border-box;
    }
    .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
        display: none !important;
    }
    .ng-select.ng-select-container.ng-value-container.ng-input {
        position: fixed !important;
        top: -14px;
    }
    .ng-select.ng-select-single .ng-select-container .ng-value-container,
    .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
        overflow: visible !important;
        position: relative !important;
        top: -9px !important;
        white-space: nowrap;
        text-overflow: ellipsis !important;
        overflow: hidden !important;
        max-width: 220px;
    }
    .ng-dropdown-panel.ng-select-bottom {
        border: 1px solid var(--dark-bg-color) !important;
    }
    .ng-select-container {
        border: none !important;
        &:after {
            display: none;
        }
    }
}

.input {
    cursor: pointer;
    text-align: center;
    padding-top: 5px;
    width: 253px;
    padding-left: 25px;
    background: #FCFCFC;
    border: 1px solid #8936F3;
    border-radius: 8px;
    height: 32px;
    text-align: left;
    font-size: 14px;
}

:host ::ng-deep {
    bs-datepicker-container {
        position: absolute;
        display: block;
        top: 3px !important;
        left: 14px !important;
    }
}

.modal-dialog {
    max-width: 710px;
    margin-top: 200px !important;
}

.modal-content {
    border-radius: 0px;
    box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
    border: none;
    text-align: center;
    max-width: 710px;
    margin-top: 200px !important;
    width: calc(100% - 48px);
}

.modal-title {
    height: 26px;
    width: 266px;
    color: #fff;
    font-family: "apercu-mono";
    font-size: 22px;
    letter-spacing: -0.98px;
    line-height: 26px;
    text-align: left;
}

.filter {
    margin-right: 24px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.control-icon {
    font-size: 4px;
    margin-left: 10px;
    color: $link-color;
}

.custom-selectbox {
    cursor: pointer;
    position: relative;
    display: inline-block;
    padding-right: 15px;
    margin-right: 5px;
}

.custom-selectbox .field-value {
    color: var(--hyperlink-color);
    font-size: 14px;
    margin-left: 50px !important;
}

.custom-selectbox .control-icon {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.show {
    left: 5px;
    height: 24px;
    width: 35px;
    position: relative;
    color: #47494F;
    font-family: "apercu-r";
    text-transform: uppercase;
    font-size: 18px;
    line-height: 17px;
    top: 10px;
    white-space: nowrap;
}

.left-panel {
    padding-left: 105px;
}

.down-arrow {
    position: relative;
    left: -30px;
    top: -2px;
}

.input.ng-select-opened+a:after {
    position: absolute;
    bottom: -6px;
    left: 0;
    right: 0;
    height: 3px;
    background: #fff;
    content: '';
}

.filter-modal1 {
    width: 100%;
    position: absolute;
    z-index: 1000;
    background: #fff;
    border-radius: 8px;
    left: calc((100% - 290px)/2) !important;
    top: 35px;
    border: 1px solid var(--dark-bg-color);
}

.filter-modal2 {
    width: 100%;
    float: left;
    right: 0px;
    left: 0px;
    position: absolute;
    z-index: 1000;
    background: #fff;
    border: 1px solid var(--dark-bg-color);
    border-radius: 8px;
    top: 35px;
}

.modalAirportFilterInfo {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modalAirportFilterInfo .modal-content {
    border: none;
    border-radius: 6px;
}

.modalAirportFilterInfo .close {
    text-shadow: none;
    color: #fff;
    opacity: 1;
}

.modalAirportFilterInfo .modal-header {
    background-color: var(--button-font-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px;
    border-bottom: none;
}

.modalAirportFilterInfo .modal-header h5 {
    font-size: 14px;
    width: 100%;
}

.modalAirportFilterInfo .modal-footer {
    padding: 0;
    border-top: none;
    float: left;
    width: 100%;
}

.modalAirportFilterInfo .close i {
    font-size: 17px;
}

.modalAirportFilterInfo .close:hover {
    color: #fff !important;
    opacity: 1 !important;
}

.modal-header {
    background-color: var(--button-font-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px !important;
    border-bottom: none;
}

.modal-header h5 {
    font-size: 14px;
    font-family: "apercu-mono";
}

.modal-body {
    padding: 28px 25px 47px 25px;
    border-radius: 0 0 5px 5px;
}

.modal-footer {
    padding: 0;
    border-top: none;
}

.dateShow {
    position: absolute;
    white-space: nowrap;
    left: 10px;
    font-size: 16px;
    top: 6px;
    color: #716F74;
}

.dateShow1 {
    position: absolute;
    white-space: nowrap;
    font-size: 16px;
    right: calc((100% - 650px)/2);
    top: 6px;
    color: gray;
}

.input-icon {
    position: absolute;
    left: 4px;
    z-index: 9;
    height: 100%;
    display: flex;
    align-items: center;
    color: #adadad;
}

.input-box {
    height: 33px;
    cursor: pointer;
    border: none;
    border-radius: 6px;
    background-color: #fff;
    font-size: 14px;
}

.line11 {
    margin-right: 20px;
    position: relative;
    top: 5px;
    margin-left: 20px;
}

ul {
    display: flex;
    width: 100%;
    height: 40px;
}

.divideline {
    margin-top: 40px;
    display: grid;
}

.top-strip {
    background: #fff;
    float: right;
    min-width: 465px;
    height: 43px;
    padding-top: 25px;
    padding-left: 0px !important;
    align-items: center;
    display: flex;
}

.nodata {
    color: #716F74;
    font-size: 16px;
    text-align: center;
    margin-top: 100px;
}

.name {
    font-size: 22px;
    color: #8936F3;
    font-weight: bold;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
}

.alltravelers {
    color: #3563F2;
    font-size: 16px;
    height: 40px;
    white-space: nowrap;
    font-weight: normal;
    cursor: pointer;
    margin-right: 45px;
    margin-top: 4px;
}

.select {
    font-family: "apercu-b" !important;
    position: relative;
    font-size: 20px;
    text-align: left;
    line-height: 30px;
    color: #8936F3 !important;
    width: auto !important;
    display: inline-block;
    margin-right: 15px !important;
    font-weight: 700;
    margin-left: 15px !important;
    margin-bottom: 0px;
    padding-bottom: 0px;
    cursor: pointer;
}

.select1 {
    font-family: "apercu-b" !important;
    position: relative;
    font-size: 16px;
    text-align: left;
    line-height: 30px;
    color: #8936F3 !important;
    width: auto !important;
    display: inline-block;
    margin-right: 15px !important;
    font-weight: 700;
    margin-left: 15px !important;
    margin-bottom: 0px;
    padding-bottom: 0px;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 90px;
}

.unselect {
    font-family: "apercu-r" !important;
    display: inline-block;
    text-align: left;
    color: #8936F3 !important;
    margin-right: 15px;
    margin-left: 15px;
    margin-bottom: 0px;
    padding-bottom: 0px;
    font-size: 20px;
    width: auto !important;
    line-height: 30px;
    font-weight: 400;
    position: relative;
    cursor: pointer;
}

.unselect1 {
    font-family: "apercu-r" !important;
    display: inline-block;
    text-align: left;
    color: #8936F3 !important;
    margin-right: 15px;
    margin-left: 15px;
    margin-bottom: 0px;
    padding-bottom: 0px;
    font-size: 16px;
    width: auto !important;
    line-height: 30px;
    font-weight: 400;
    position: relative;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90px;
}

.underline {
    border-top: 4px solid var(--dark-bg-color);
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
}

.map-class {
    width: 376px;
    height: 239px;
}

.map-box {
    width: 376px;
    height: 239px;
    border-right: 1px solid #8936F3;
    padding: 24px;
    box-sizing: border-box;
}

.approval-box {
    height: 239px;
    border: 1px solid #8936F3;
    padding-bottom: 18px;
    box-sizing: border-box;
    border-radius: 8px;
}

@media(max-width:1400px) {
    .card-div-inner {
        padding: 30px 10px 10px;
        margin-left: 0px;
    }
}

@media(max-width:768px) {
     :host ::ng-deep {
        .ng-dropdown-panel {
            position: absolute !important;
            border-radius: 6px !important;
            left: -28px !important;
            width: 100% !important;
            -ms-transform: translate(0%);
            transform: translate(0%);
            top: 28px !important;
            max-width: none !important;
            min-width: 280px !important;
        }
    }
}

@media(max-width:767px) {
    .chart-container {
        position: relative;
        left: 0px;
        display: inline-block;
    }
    .modal-body {
        min-width: 100vw;
        background: #F7F7F9;
        padding-left: 0px !important;
        padding-right: 5px !important;
        padding-top: 7px !important;
        padding-bottom: 16px !important;
    }
    .filter-modal2 {
        width: 100%;
        float: left;
        right: 0px;
        left: -12px;
        position: absolute;
        z-index: 1000;
        background: #fff;
        border: 1px solid var(--dark-bg-color);
        border-radius: 8px;
        top: 40px;
    }
    .piechart {
        height: auto !important;
        width: auto !important;
        max-height: 100% !important;
        max-width: 100% !important;
    }
    .presetSelect {
        width: auto;
        max-height: 32px;
        line-height: 2em;
        padding-left: 25px;
        border-radius: 8px;
        margin-bottom: 5px;
    }
    .presetSelect:hover {
        width: auto;
        max-height: 32px;
        line-height: 2em;
        padding-left: 25px;
        border-radius: 8px;
        margin-bottom: 5px;
        background-color: #D3CCDE !important;
    }
    .show3 {
        color: #47494F;
        font-weight: 300;
        font-size: 14px;
        margin-left: 10px;
    }
    .alltravelers {
        color: #3563F2;
        font-size: 14px;
        height: 40px;
        white-space: nowrap;
        cursor: pointer;
        margin-right: 45px;
        font-weight: 300;
        margin-top: 4px;
    }
    .select1 {
        font-family: "apercu-b" !important;
        position: relative;
        font-size: 12px;
        white-space: nowrap;
        text-align: left;
        line-height: 30px;
        color: #8936F3 !important;
        width: auto !important;
        display: inline-block;
        margin-right: 15px !important;
        font-weight: 700;
        margin-left: 15px !important;
        margin-bottom: 0px;
        padding-bottom: 0px;
        cursor: pointer;
    }
    .unselect1 {
        font-family: "apercu-r" !important;
        display: inline-block;
        font-size: 12px;
        white-space: nowrap;
        text-align: left;
        color: #8936F3 !important;
        margin-right: 15px;
        margin-left: 15px;
        margin-bottom: 0px;
        padding-bottom: 0px;
        width: auto !important;
        line-height: 30px;
        font-weight: 400;
        position: relative;
        cursor: pointer;
    }
    .arrowcircle {
        position: relative;
        left: 0px;
        top: 0px;
    }
    .card-div-inner {
        padding: 30px 10px 10px;
        margin-left: 0px;
        padding-right: 0px;
    }
    .topHeading {
        width: 105vw;
        height: 33px;
        background: #8936F3;
        text-align: center;
        margin-left: -10px;
    }
    .subHeading {
        height: 24px;
        font-family: var(--globalFontfamilyr);font-weight: bold;;
        display: inline;
        font-style: normal;
        font-weight: bold;
        font-size: 20px;
        color: #8933F8;
    }
    .topHeadingMiddle {
        width: 100px;
        height: 23px;
        font-family: var(--globalFontfamilyr);font-weight: bold;;
        font-style: normal;
        font-weight: bold;
        font-size: 20px;
        /* or 120% */
        color: #FFFFFF;
    }
    .map-box {
        width: auto;
        height: 219px;
        padding: 24px;
        border: 1px solid #8936F3;
        box-sizing: border-box;
        border-radius: 8px;
    }
     :host ::ng-deep {
        .agm-map-container-inner {
            height: 172px !important;
            width: auto !important;
        }
        .ng-dropdown-panel {
            position: fixed !important;
            max-width: 365px !important;
            top: 164px !important;
            left: 50% !important;
            -webkit-transform: translate(-50%, 0);
            -ms-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
            min-width: 100px !important;
            width: calc(100% - 40px) !important;
        }
        .ng-select .ng-select-container {
            color: gray !important;
        }
    }
    .map-class {
        width: 380px;
        height: 182px;
    }
    .first-box {
        width: 99vw;
        margin-top: 0px;
        height: 239px;
        border: none;
        border-bottom: 1px solid #8936F3;
        box-sizing: border-box;
        display: flex;
        border-radius: 0px;
    }
    .top-strip {
        background: #fff;
        float: right;
        min-width: auto;
        height: 43px;
        padding-top: 25px;
        padding-left: 0px !important;
        align-items: center;
        display: flex;
    }
    .travelerDetailsBox {
        width: 310px;
        height: 40px;
        cursor: pointer;
        display: flex;
        background: #F7F7F9;
        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
        border-radius: 8px;
        margin-top: 15px;
        padding: 8px;
    }
    .name11 {
        width: 111px;
        height: 24px;
        white-space: nowrap;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        font-style: normal;
        font-weight: 300;
        font-size: 12px;
        line-height: 24px;
        /* identical to box height, or 150% */
        color: #47494F;
    }
    .approval-box {
        width: auto;
        height: 239px;
        border: none;
        border-bottom: 1px solid #8936F3;
        box-sizing: border-box;
        border-radius: 0px;
        overflow: hidden;
    }
    .pendingtravelerDetailsBox {
        width: 317px;
        height: 32px;
        margin-top: 15px;
        display: flex;
        padding: 5px;
        cursor: pointer;
        background: #FEE8EB;
        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
        border-radius: 8px;
    }
    .pendingtravelerDetailsBox1 {
        width: 317px;
        height: 32px;
        margin-top: 15px;
        padding: 5px;
        cursor: pointer;
        background: #FC6275;
        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
        border-radius: 8px;
        display: flex
    }
    .name111 {
        width: 140px;
        height: 24px;
        white-space: nowrap;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        font-style: normal;
        font-weight: 300;
        color: #AEAEAE;
        font-size: 16px;
        line-height: 24px;
        /* identical to box height, or 150% */
        color: #47494F;
    }
    .name1111 {
        width: 140px;
        height: 24px;
        white-space: nowrap;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        font-style: normal;
        font-weight: 300;
        font-size: 16px;
        line-height: 24px;
        /* identical to box height, or 150% */
        color: #fff;
    }
    .underline1 {
        border-top: 8px solid var(--dark-bg-color);
        border-top-right-radius: 20px;
        border-top-left-radius: 20px;
        border-bottom-right-radius: 20px;
        border-bottom-left-radius: 20px;
        position: relative;
        top: 10px;
        left: 10px;
        width: 65%;
        float: right;
    }
    .underline2 {
        border-top: 8px solid var(--dark-bg-color);
        border-top-right-radius: 20px;
        border-top-left-radius: 20px;
        border-bottom-right-radius: 20px;
        border-bottom-left-radius: 20px;
        position: relative;
        top: 10px;
        left: 0px;
        width: 40%;
        float: right;
    }
    .left-panel {
        padding-left: 0px;
        margin-left: -5px;
    }
    .dateShow1 {
        position: absolute;
        white-space: nowrap;
        right: calc((100% - 346px)/2);
        top: 6px;
        font-size: 14px;
        color: gray;
    }
    .filter-modal1 {
        width: 100%;
        position: absolute;
        z-index: 1000;
        background: #fff;
        border-radius: 8px;
        left: calc((100% - 310px)/2) !important;
        top: 40px;
        border: 1px solid var(--dark-bg-color);
        overflow: hidden;
    }
    .top-strip1 {
        margin-top: 30px;
        text-align: center;
        align-items: center;
        margin-left: auto;
        margin-right: auto;
        width: 100vw;
    }
    .select {
        font-family: "apercu-b" !important;
        position: relative;
        font-size: 20px;
        text-align: left;
        line-height: 30px;
        color: #8936F3 !important;
        width: auto !important;
        display: inline-block;
        margin-right: 15px !important;
        margin-left: 15px !important;
        margin-bottom: 0px;
        padding-bottom: 0px;
        cursor: pointer;
    }
    .unselect {
        font-family: "apercu-r" !important;
        display: inline-block;
        text-align: left;
        color: #8936F3 !important;
        margin-right: 15px;
        margin-left: 15px;
        margin-bottom: 0px;
        padding-bottom: 0px;
        font-size: 20px;
        width: auto !important;
        line-height: 30px;
        position: relative;
        cursor: pointer;
    }
    .right-panel {
        width: auto;
        padding: 20px;
        margin-top: 30px;
        height: 312px;
        margin-left: 0px;
        background: #F7F7F9;
        border-radius: 8px;
        margin-top: 20px;
    }
    .right-panel11.active {
        min-width: 367px;
        padding-top: 17px;
        padding-bottom: 25px;
        padding-left: 5px;
        margin-top: 30px;
        height: 369px;
        margin-left: 0px;
        background: #F7F7F9;
        border-radius: 8px;
        margin-top: 20px;
    }
    .right-panel11 {
        min-width: 367px;
        padding-top: 17px;
        padding-left: 5px;
        padding-bottom: 25px;
        margin-top: 30px;
        height: auto;
        margin-left: 0px;
        background: #F7F7F9;
        border-radius: 8px;
        margin-top: 20px;
    }
    .doughnutChart {
        display: block !important;
        max-height: 190px !important;
        max-width: 302px !important;
        height: auto !important;
        width: auto !important;
    }
    .emptyLineChart {
        width: auto;
        position: relative;
        top: 50px;
    }
    .left-panel1 {
        background: #FFFFFF;
        border: 1px solid #8936F3;
        box-sizing: border-box;
        border-radius: 8px;
        margin-top: 20px;
        height: 224px;
        width: auto;
        margin-top: 8px;
        padding: 20px;
        padding-left: 10px;
        padding-right: 10px;
    }
    .bookingdetails {
        margin-left: 0px;
        height: 50px;
        white-space: nowrap;
        width: 220px;
    }
    .bookingdetails1 {
        margin-left: 0px;
        height: 50px;
        margin-left: 10px;
        white-space: nowrap;
        width: auto;
    }
    .linechart {
        display: block;
        height: auto !important;
        width: auto !important;
        margin: auto !important;
        max-height: 206px !important;
        max-width: 326px !important;
    }
    .approvalGraph {
        min-height: 187px;
        background: #FFFFFF;
        border: 1px solid #8936F3;
        box-sizing: border-box;
        border-radius: 8px;
        margin-top: 8px;
        padding-top: 5px;
        margin-left: auto;
        margin-right: auto;
        width: 100%;
    }
    .right-panel11.active {
        .userdownArrow {
            position: relative;
            left: -19px;
            top: 0px;
            -webkit-transform: rotate(-180deg);
            -ms-transform: rotate(-180deg);
            transform: rotate(-180deg);
            -ms-transform: rotate(-180deg);
            transform: rotate(-180deg);
        }
    }
    .employeeGraph {
        width: auto;
        margin-top: 8px;
        height: 189px;
        background: #FFFFFF;
        border: 1px solid #8936F3;
        box-sizing: border-box;
        border-radius: 8px;
        padding-top: 30px;
    }
    .uniquetravelerbox {
        width: auto;
        height: 162px;
        background: #FFFFFF;
        border: none;
        border-bottom: 2px solid #8936F3;
        border-radius: 0px;
    }
    .uniquetravelerbox.active {
        width: auto;
        height: auto;
        background: #FFFFFF;
        border: none;
        border-bottom: 2px solid #8936F3;
        border-radius: 0px;
    }
    .date-box {
        width: 78px;
        padding: 0 5px;
        margin: 0px 0px;
        background: #F1F1F1;
        text-align: start;
    }
    .date-box-header {
        width: 78px;
        padding: 0 5px;
        margin: 0px 0px;
        background: white;
        text-align: start;
    }
    .traveler-box {
        background: #E8D7FD;
        border-radius: 0px;
        padding: 5px;
        width: 78px;
        height: 156px;
    }
    .flight-box {
        background: white;
        width: 78px;
        height: 156px;
        border-radius: 0px;
        padding: 5px;
    }
    .traveler-box1 {
        background: #DAC9ED;
        padding: 5px;
        margin: 0px 0px;
        width: 78px;
    }
    .flight-box1 {
        background: #E9E9EB;
        width: 78px;
        border-radius: 0px;
        margin: 0px 0px;
        padding: 5px;
    }
    .traveler-box1-transparent {
        background: #E8D7FD !important;
    }
    .show11 {
        color: var(--dark-bg-color);
        font-family: var(--globalFontfamilyr);font-weight: bold;;
        font-size: 16px;
        margin-left: 0px
    }
}

@media(max-width:360px) {
    .underline2 {
        border-top: 8px solid var(--dark-bg-color);
        border-top-right-radius: 20px;
        border-top-left-radius: 20px;
        border-bottom-right-radius: 20px;
        border-bottom-left-radius: 20px;
        position: relative;
        top: 10px;
        left: 10px;
        width: 40%;
        float: right;
    }
    .presetSelect {
        width: auto;
        max-height: 32px;
        line-height: 2em;
        padding-left: 4px;
        border-radius: 8px;
        margin-bottom: 5px;
        padding-right: 24px;
    }
    .presetSelect:hover {
        width: auto;
        max-height: 32px;
        line-height: 2em;
        padding-left: 4px;
        border-radius: 8px;
        margin-bottom: 5px;
        padding-right: 24px;
        background-color: #D3CCDE !important;
    }
}

@media(max-width:400px) {
    .filter-modal1 {
        width: 100%;
        position: absolute;
        z-index: 1000;
        background: #fff;
        border-radius: 8px;
        left: calc((100% - 300px)/2) !important;
        top: 40px;
        border: 1px solid var(--dark-bg-color);
        overflow: hidden;
    }
    .underline1 {
        border-top: 8px solid var(--dark-bg-color);
        border-top-right-radius: 20px;
        border-top-left-radius: 20px;
        border-bottom-right-radius: 20px;
        border-bottom-left-radius: 20px;
        position: relative;
        top: 10px;
        left: 10px;
        width: 63%;
        float: right;
    }
    .dateShow1 {
        position: absolute;
        white-space: nowrap;
        right: calc((100% - 342px)/2);
        top: 6px;
        font-size: 14px;
        color: gray;
    }
    .presetSelect {
        width: auto;
        max-height: 32px;
        line-height: 2em;
        padding-left: 8px;
        border-radius: 8px;
        margin-bottom: 5px;
        padding-right: 8px;
    }
    .presetSelect:hover {
        width: auto;
        max-height: 32px;
        line-height: 2em;
        padding-left: 8px;
        border-radius: 8px;
        margin-bottom: 5px;
        padding-right: 8px;
        background-color: #D3CCDE !important;
    }
    .filter-modal2 {
        width: 100%;
        float: left;
        right: 0px;
        left: -6px;
        position: absolute;
        z-index: 1000;
        background: #fff;
        border: 1px solid var(--dark-bg-color);
        border-radius: 8px;
        top: 40px;
    }
    .chart-container {
        position: relative;
        left: 0px;
        display: inline-block;
    }
    .emptyLineChart {
        width: 280px;
        position: relative;
        top: 50px;
    }
    .linechart {
        display: block;
        height: auto !important;
        width: auto !important;
        margin: auto !important;
        max-height: 206px !important;
        max-width: 286px !important;
    }
    .modal-body {
        min-width: 100vw;
        background: #F7F7F9;
        padding-left: 0px !important;
        padding-right: 5px !important;
        padding-top: 7px !important;
        padding-bottom: 16px !important;
    }
}