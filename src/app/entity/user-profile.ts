import { JsonObject } from '../util/ta-json/src/decorators/json-object';
import { JsonProperty } from '../util/ta-json/src/decorators/json-property';
import { TravelersInfo } from './travelers-info';
import { GallopCashDTO } from './gallopcash-dto';
import { UserEntity } from './user-entity';
import { AddressDTO } from './address-dto';

@JsonObject()
export class UserProfile extends TravelersInfo {

  gallopCash: GallopCashDTO;
  entity: UserEntity;
  userCurrentLoc: AddressDTO;

  public constructor(fields?: any) {
    super(fields);
    if (fields) {
      Object.assign(this, fields);
    }
  }



}
