
import { HotelAmenity } from './hotel-amenity';
import { TraflaImage } from './trafla-image';

export class HotelDetailResult {
    public optionSelectionReason: string;
    public thumbnailURL: string;
    public hotelName: string;
    public address: string;
    public hotelChainCode: string;
    public hotelChainName: string;
    public drivingTime: number;
    public distanceMile: string;
    public walkingTime: number
    public price: number;
    public currency: string;
    public starRating: number;
    public score: number
    public amenities: Map<string, HotelAmenity>;
    public isWithinPolicy: boolean;
    public latitude: number;
    public longitude: number;
    public thumbnailImage: TraflaImage;
    public checkInDate: Date;
    public checkOutDate: Date;
    public recommendedIdx: number;
    public reservationCode: string;
    public hotelRooms: Array<any>;
    public stay: number;
    public originalPrice: number;
    public prepay: boolean;
    public serviceFee: number;
    public constructor(fields?: any) {

        if (fields) {
            Object.assign(this, fields);
        }
    }
}
