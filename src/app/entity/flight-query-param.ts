
import { JsonObject } from '../util/ta-json/src/decorators/json-object';
import { AddressType } from '../enum/address.type';

@JsonObject()
export class FlightQueryParam {

  source: string;
  destination: string;
  constraint: string;
  departureDate: Date;
  arrivalDate: Date;
  durationDate:any;
  constraintTime: string;
  timeTypeFilter: string;
  timeTypeFilter1: string;
  constraintTime1: string;
  originType: AddressType;
  destinationType: AddressType;
}
