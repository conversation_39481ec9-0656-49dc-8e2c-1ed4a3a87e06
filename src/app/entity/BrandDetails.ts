import { FareAttributes } from './fare-attributes';
import { BaggageDetails } from './baggage-details';

export class BrandDetails {
  public code: string;
  public brandId: string;
  public name: string;
  public description: string;
  public fareBasis: string;
  public priceDiff: number;
  public fareAttributes: FareAttributes;
  public baggageAllowance: BaggageDetails;
  public serviceFee: number;
  public constructor(fields?: any) {
    if (fields) {
      Object.assign(this, fields);
    }
  }
}
