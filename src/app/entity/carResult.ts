export class CarResult {
    public airConditioning: boolean;
    public automaticTransmission: boolean;
    public basePrice: number;
    public paymentRequired: boolean;
    public partnerName: string;
    public postPay: boolean;
    public rateKey: string;
    public status: string
    public traflaPartnerCode: any;
    public traflaVehicleCode: any
    public optionSelectionReason: string;
    public images: any;
    public description: string;
    public pickUpLocation: string;
    public dropOffLocation: string;
    public carMake: string;
    public milage: boolean;
    public distance: string;
    public passengers: number;
    public bags: number;
    public doors: number;
    public price: number;
    public currency: string;
    public policy: boolean;
    public handlerType: string;
    public partnerLogo: string;
    public locationInformation: string;
    public eventId: string;

    public constructor(fields?: any) {

        if (fields) {
            Object.assign(this, fields);
        }
    }
}



//export var carList:CarResult[]=[{ locate:'in-terminal',companyLogo:'Advantage', modelName:'Tata Nexon',thumbnailURL:'https://www.pexels.com/photo/audi-automobile-car-lights-cars-1149831/',optionSelectionReason:'Best Option',carType:'Compact',pickUp:'chandni chowk delhi-110006',
//dropOff:'Laxmi nagar delhi-110095',mileage:'Unlimited mileage',distanceMile:'6',seats:4,bags:2,doors:4,price:147,
//currency:"USD",starRating:'4',score:300,withinPolicy:true,pickUpDate:"Tue, December 4",dropOffDate:"Tue, December 4",recommendedIdx:1 }];