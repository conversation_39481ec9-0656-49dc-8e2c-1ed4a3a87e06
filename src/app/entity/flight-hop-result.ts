export class FlightHopResult {

  public from: string;
  public to: string;
  public starts: string;
  public ends: string;
  public carrier: string;
  public operatingCarrier: string;
  public type: string;
  public duration: string;
  public FlightHopResult:string;
  public destinationTerminal: string;
  public flightNumber: number;
  public allianceName: string;
  public fareClassName:string;
  public isLLC: boolean;
  public cabinClass: string;
  public classCode: string;
  public avaailabiltyType: string;
  public availabilitySource: string;
  public classOfService: string;
  public brandClass: string;
  public constructor(fields?: any) {
    if (fields) {
      Object.assign(this, fields);
    }
  }

}
