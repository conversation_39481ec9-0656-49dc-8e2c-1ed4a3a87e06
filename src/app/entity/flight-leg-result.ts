import { FlightHopResult } from './flight-hop-result';
import { BrandDetails } from './BrandDetails';
import { BaggageDetails } from './baggage-details';

export class FlightLegResult {

  public timeToAirport: any;
  public timeFromAirport: any;
  public flightHighlights: any;
  public flightHops: FlightHopResult[];
  public confirmationNumber: string;
  public legId: string;
  public brandDetails: BrandDetails[];
  public baggageAllowance: BaggageDetails;
}
