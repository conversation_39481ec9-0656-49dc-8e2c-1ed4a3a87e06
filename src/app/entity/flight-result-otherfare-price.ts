
import { FlightLegResult } from './flight-leg-result';
import { FareAttributes } from './fare-attributes'
export class FlightResultOtherFarePrice {

  public price: number;
  public discountedPrice: number;
  public currency: string;
  public fareBreakup: any;
  public fareAttributes: FareAttributes;
  public displayPrice:number;
  public displayCurrency:string;
  public legs: FlightLegResult[];
  public constructor(fields?: any) {
    if (fields) {
      Object.assign(this, fields);
    }
  }
}
