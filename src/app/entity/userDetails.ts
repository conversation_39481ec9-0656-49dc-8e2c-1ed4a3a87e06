import { PassportDetails } from './passport-details';
import { FrequentFlyerInfo } from './frequent-flyer-info';

export class UserDetails {
  firstName: string;
  middleName: string;
  lastName: string;
  title: string;
  suffix: string;
  emailId: string;
  profileImage: string;
  gender: string;
  dateOfBirth: string;
  phoneNumber: string;
  nationality: string;
  address: string;
  zipCode: string;
  passportDetails: PassportDetails;
  knownTravellerNumber: number;
  frequentFlyerInfo: FrequentFlyerInfo[];

  public constructor(fields?: any) {

    if (fields) {
      Object.assign(this, fields);
    }
  }
}
