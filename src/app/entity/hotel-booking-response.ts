import { PaymentInstrumentDetails } from './payment-instrument-details';
import { TravelerDetails } from './traveler-details';
import { BookingResponseError } from './booking-response-error';
import { HotelDetailResult } from './hotel-detail-result';

export class HotelBookingResponse {

  status: string = 'ERROR';
  bookingId: string;
  nextCallAfter: number;
  bookingStatusId: any = null;
  transactionId: string;
  eventIdAndOptions: Map<string, string>;
  ticketId: string;
  tripId: string;
  ticketNumber: string;
  errors: BookingResponseError[];
  bookedHotels: HotelDetailResult[];
  changedHotels: HotelDetailResult[];
  paymentDetails: PaymentInstrumentDetails;
  travellerDetails: TravelerDetails;
}
