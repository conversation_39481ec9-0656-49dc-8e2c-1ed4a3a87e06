import { EmailHops } from "./email-hops";
import { Constants } from "src/app/util/constants";

export class FlightViewDTO {


    constructor(private hops: Array<EmailHops>, private flightDuration: string,
        private destinationCity: string) {
    }

    public getHops() {
        return this.hops;
    }

    public getStopsCount() {
        return this.hops.length - 1;
    }
    public getFromAirport() {
        return this.hops[0].fromAirport;
    }

    public getToAirport() {
        return this.hops[this.hops.length - 1].toAirport
    }

    public getDepartureTime() {
        return this.hops[0].departure;
    }

    public getFareattributes() {
        return this.hops[0].fareAttributes;
    }
    public getFarebaggageAllowance() {
        return this.hops[0].baggageAllowance;
    }
    public getOairlineName() {
        return this.hops[0].oAirlineName;
    }

    public getArrivalTime() {
        return this.hops[this.hops.length - 1].arrival;
    }


    public getDepartureDate() {
        return this.hops[0].flightDate;
    }

    public getArrivalDate() {
        return this.hops[this.hops.length - 1].flightEndDate;
    }
    public getStops() {
        if (this.hops.length - 1 === 0) {
            return "Non-stop";
        } else if (this.hops.length - 1 === 1) {
            return "1 stop";
        } else {
            return this.hops.length - 1 + " stops";
        }
    }
    public brandedFairExists() {
        if (this.hops[0].brandClass && this.hops[0].brandClass.trim().length > 0) {
            return true;
        } else {
            return false;
        }
    }
    public getDuration() {
        return this.flightDuration;
    }

    public getDestinationCity() {
        return this.destinationCity;
    }


    public getAirline() {
        return this.hops[0].airlineCode;
    }


    getClassName() {
        let className = null;
        let flightClass = this.hops[0].type;
        if (this.hops[0].typeName) {
            flightClass = this.hops[0].typeName;
        }
        Constants.CLASS_OPTIONS.map(item => {
            if (item.id.toLowerCase() == flightClass.toLowerCase()) {
                className = item.value;
            }
        });
        if (className === null || className.trim.length == 0) {
            className = flightClass;
        }
        if (this.brandedFairExists()) {
            return className + ' (' + this.hops[0].brandClass.trim() + ')';
        }

        return className;

    }
}
