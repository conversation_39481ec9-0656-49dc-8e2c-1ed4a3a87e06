import { TravelerDetails } from "../traveler-details";
import { CardInfo } from "../card-info";
import { AppRoutingModule } from 'src/app/app-routing.module';


export class EmailFlowBookRequest {

  travellerDetails: TravelerDetails;
  selectedCard: CardInfo;
  newCardToken: string;
  projectTagId: string;
  newGalloToken: string;
  public appliedGallopCash: number;
  ticketId: string;
  tripId: string;
  noteToAdmin:string;
  eventIds: Array<string>;
  expensifyEmail: string;
  addToExpensify: boolean;
  expenseProvider: string;
  paymentMethod: string;

  deleted_cards: Array<string>;


  public constructor(fields?: any) {

    if (fields) {
      Object.assign(this, fields);
    }
  }


}
