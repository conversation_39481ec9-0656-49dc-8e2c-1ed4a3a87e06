import { FlightViewDTO } from "./flight-view-dto";

export class MultiFlightViewDTO {

    public isAvailable: boolean = true;
    public revalidating: boolean = false;
    public priceUpdated: boolean = false;
    public isDiscounted: boolean = false;
    constructor(public flights: Array<any>, public price: string
        , private discountedPrice: string, public isWithinPolicy: boolean
        , public serviceFee: string, public travelCreditsInfo: any) {
        this.flights = flights;
        this.price = price;
        this.discountedPrice = discountedPrice;
        this.isWithinPolicy = isWithinPolicy;
        if (this.discountedPrice && this.discountedPrice.trim().length > 0) {
            this.isDiscounted = true;
        }
    }


    public getFlightViewList() {
        return this.flights;
    }
    public getCreditDetails() {
        if (this.travelCreditsInfo) {
            return this.travelCreditsInfo;
        }
    }
    public getPrice() {
        if (this.discountedPrice && this.discountedPrice.trim().length > 0) {
            return this.discountedPrice;
        } else
            return this.price;
    }

    public getDiscountedPrice() {
        if (this.discountedPrice && this.discountedPrice.trim().length > 0) {
            return this.discountedPrice;
        } else
            return undefined;
    }
    public getInPolicyBlockVisibility(policySet: boolean) {
        if (!policySet) {
            return "hidden";
        }
        if (this.isWithinPolicy) {
            return "";
        }
        return "hidden";
    }
    public getOutOfPolicyBlockVisibility(policySet: boolean) {
        if (!policySet) {
            return "hidden";
        }
        if (!this.isWithinPolicy) {
            return "";
        }
        return "hidden";

    }

    public setPrice(price: any) {
        this.price = price;
    }
    public setDiscountedPrice(discountedPrice: any) {
        this.discountedPrice = discountedPrice + '';
    }

    public setWithinPolicy(isWithinPolicy) {
        if (isWithinPolicy) this.isWithinPolicy = isWithinPolicy;
    }

    public getWithinPolicy() {
        return this.isWithinPolicy;
    }
}