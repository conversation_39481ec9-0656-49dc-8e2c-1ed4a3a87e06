import { PaymentTypes } from '../../enum/payment-types';

export class BillingItem {
    public nights: number;
    public rooms: number;
    public guest: number;
    public perNightCharge: number;
    public tax: string;
    public fee: string;
    public resortFee: string;
    public source: string;
    public addPolicy: any;
    public baseprice:number;
    public number: number;
    public credit: any;
    constructor(
        public paymentType: PaymentTypes,
        public type: string,
        public particular: string,
        public amount: string,
        public currency,
        ) {
    }
}
