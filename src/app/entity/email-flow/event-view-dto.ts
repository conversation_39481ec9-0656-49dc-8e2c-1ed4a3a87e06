import { MultiFlightViewDTO } from "./multi-flight-view-dto";

export class EventViewDTO {

    private isViewLess: boolean;
    private skipped: boolean;
    private optionSelected: boolean;
    options: Array<MultiFlightViewDTO>;
    hotelOptions: Array<any>;
    carOptions: Array<any>;
    constructor(private id: string, public type: string, private title: string) {
        this.options = new Array;
    }

    public setOptions(opts: Array<MultiFlightViewDTO>) {
        this.options = opts;
    }

    public setHotelOptions(opts: Array<any>) {
        this.hotelOptions = opts;
    }
    public setCarOptions(opts: Array<any>) {
        this.carOptions = opts;
    }

    public addOption(opt: MultiFlightViewDTO) {
        this.options.push(opt);
    }
    public getTitle() {
        return this.title;
    }

    public getIsViewLess() {
        return this.isViewLess;
    }

    public getSkipped() {
        return this.skipped;
    }

    public getOptionSelected() {
        return this.optionSelected;
    }

    public getOptions(): Array<MultiFlightViewDTO> {
        return this.options;
    }
    public getHotelOptions(): Array<any> {
        return this.hotelOptions;
    }
    public getCarOptions(): Array<any> {
        return this.carOptions;
    }

    public getType(): string {
        return this.type;
    }

    public getEventId() {
        return this.id;
    }

}