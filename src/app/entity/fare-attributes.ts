
import { FareAttributeValues } from '../enum/fare-attributes-types-values';
export class FareAttributes {

  public carryBag: FareAttributeValues;
  public checkedBag: FareAttributeValues;
  public meal: FareAttributeValues;
  public rebooking: FareAttributeValues;
  public refund: FareAttributeValues;
  public seat: FareAttributeValues;
  public wifi: FareAttributeValues;
  public legRoom: FareAttributeValues;
  public constructor(fields?: any) {

    if (fields) {
      Object.assign(this, fields);
    }
  }
}
