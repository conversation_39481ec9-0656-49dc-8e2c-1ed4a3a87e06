import { HotelResult } from "./hotel-result";
import { AddressDTO } from "./address-dto";

export class HotelSearchResponse {
  public hotelsList: HotelResult[];
  public preferredHotelChains = [];
  public otherHotelChains = [];
  public policySet: boolean = false;
  public optimumPrice: string;
  public destination: AddressDTO;
  public constructor(fields?: any) {

    if (fields) {
      Object.assign(this, fields);
    }
  }
}
