<div class="card-div active shadow" style="width:100% !important;"
  [ngStyle]="{'padding-bottom':this.transactionReport && this.transactionReport.length > 10  ?'200px':'600px'}">
  <div class="card-div-inner" style="width:100%;">
    <div class="row">
      <div class="col-lg-8 col-md-8 col-sm-8 col-xs-8" style="text-align: left;margin-bottom:20px;margin-left: -2px;white-space: nowrap;">
        <img class="image11" style="display: none;" src="assets/images/fluid_pay.png">
        <div class="view12"><span class="view" style="display: none;">  {{ 'fuild.Cards' | translate}}</span><span  style="display: none;"class="line1"></span><span
          attr.data-params="tab=Manage" attr.data-track="Admin-Cards" class="{{ this.viewMode==='tab1' ? 'items11':'items1'}}" (click)="tabChanged('tab1')"> {{ 'fuild.ManageCards' | translate}}</span>
          <span class="{{ this.viewMode==='tab2' ? 'items11':'items1'}}"
          attr.data-params="tab=Transactions" attr.data-track="Admin-Cards" (click)="tabChanged('tab2')"> {{ 'fuild.Transactions' | translate}}</span>

        </div>
      </div>

    </div>
    <div class="tab">
      <div [hidden]="this.viewMode==='tab2'">
        <div class="row">
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12"
            style="text-align: left;margin-bottom:20px;margin-left: -2px;">
            <div *ngIf="this.creditEnabled" class="available">

              <span>{{ 'fuild.Currentbalance' | translate}}:</span> <span class="dollar1"
                style="position: relative;top:2px;font-family: var(--globalFontfamilyr);color: #8936F3 !important;">
                {{this.currentBalance | currency :
                getCurrencySymbol(this.currency) : 'code': '1.2-2'}}</span>
            </div>
            <div *ngIf="balanceapiCame" class="available">

              <span> {{ 'fuild.Availablebalance' | translate}}:</span> <span class="dollar1"
                style="position: relative;top:2px;font-family: var(--globalFontfamilyr);"
                [ngStyle]="{'color':this.creditEnabled ? '#000000':'#8936F3'}"> {{this.availableBalance | currency :
                  getCurrencySymbol(this.currency) : 'code': '1.2-2'}}</span>
            </div>
            <div *ngIf="this.creditEnabled" class="available">

              <span> {{ 'fuild.Statementbalance' | translate}}:</span> <span class="dollar1"
                style="position: relative;top:2px;font-family: var(--globalFontfamilyr);color: #000000 !important;">
                {{this.statementBalance | currency :
                  getCurrencySymbol(this.currency) : 'code': '1.2-2'}}</span>
            </div>
            <div *ngIf="balanceapiCame && this.lastPayment" class="available" style="color: gray;">

              ( {{ 'fuild.Lastpayment' | translate}}: {{this.lastPayment | currency :
                getCurrencySymbol(this.currency) : 'code': '1.2-2'}} on {{lastDate| date: 'MMM d, yyyy'}})
            </div>
            <div>
            </div>

          </div>
          <div *ngIf="(balanceapiCame)" class="col-lg-6 col-md-6 col-sm-12 col-xs-12"
            [ngStyle]="{'justify-content' :this.qboEnabled ?'end':'end'}" style="text-align:center;display:flex;;
       
        ">
         <div *ngIf="balanceapiCame" class="addCard" style="width: 100px;"
         (click)="uploadBank(uploadBankModal)">
         <span> <i class="fa fa-bank" aria-hidden="true" style="font-size:45px;position: relative;top: -4px;color:#3564F2"></i>
              </span>
         <div style="font-family: var(--globalFontfamilyr);font-size: 14px;"> {{ 'setting.ConnectBank' | translate }}
         </div>
       </div>
            <div *ngIf="balanceapiCame" class="addCard" style="width: 100px;"
              (click)="transferFundsModal(transferFunds)">
              <span> <img class="inlineblock_m" style="max-width:45px;position: relative;top: -4px;"
                  src="assets/images/transfer_funds.png" /> </span>
              <div style="font-family: var(--globalFontfamilyr);font-size: 14px;"> {{ 'fuild.TransferFunds' | translate}}
              </div>
            </div>
            <div *ngIf="balanceapiCame" class="addCard" style="width: 100px;" (click)="addCardModal(addCard,'Add')">
              <span> <img class="inlineblock_m" style="max-width:45px;position: relative;top: -4px;"
                  src="assets/images/create_card.png" /> </span>
              <div style="font-family: var(--globalFontfamilyr);font-size: 14px;"> {{ 'fuild.AddCard' | translate}} </div>
            </div>
            <div style="display:none;">
              <div *ngIf="!showQucikBookButtonWithCompanyName && !this.disbaledQBoButton && this.qboEnabled"
                style="float: right;margin-top:0px;margin-left: 20px;"> <a href="{{this.quickBookUrl}}" target="_blank"
                  (click)="initiatePolling()" style="text-transform: capitalize !important;"> <img class="inlineblock_m"
                    style="max-width:45px;position: relative;top: -4px;" src="assets/images/qb_thumb.png" /> <span class="addlue"
                    style="margin-left:5px; font-size: 16px !important;"> {{ 'fuild.ConnectQuickBooksOnline' |
                    translate}}</span></a>
              </div>
              <div *ngIf="!showQucikBookButtonWithCompanyName && this.disbaledQBoButton && this.qboEnabled"
                style="position: relative;opacity: .5;float: right;margin-left: 20px;"> <img class="inlineblock_m"
                  style="max-width:45px;position: relative;top: -4px;" src="assets/images/qb_thumb.png" /> <span class="addlue"
                  style="margin-left:5px; "> {{ 'fuild.ConnectQuickBooksOnline' | translate}}</span>
                <div class="" style="position: absolute;
                          bottom: 0;
                          top: 30px;
                          right: 0;
                          left: 0;">
                  <app-loader *ngIf="this.disbaledQBoButton" [spinnerStyle]="true"></app-loader>
                </div>
              </div>
              <div *ngIf="showQucikBookButtonWithCompanyName && this.qboEnabled" (click)="disconnect()"
                style="margin-left: 20px;margin-top: 0px;font-size: 16px;cursor: pointer;position: relative;">
                <img class="inlineblock_m" style="max-width:45px;position: relative;top: -4px;" src="assets/images/qb_thumb.png" />
                <span class="addlue" style="margin-left:5px; "> {{ 'fuild.DisconnectQuickBooksOnline' | translate}}</span>
            
                <div *ngIf="showQucikBookButtonWithCompanyName" class="" style="position: absolute;
                        bottom: 0;
                        top: 30px;
                        right: 0;
                        left: 0;">
                  <app-loader *ngIf="this.disbaledQBoButton" [spinnerStyle]="true"></app-loader>
                </div>
              </div>
            </div>
          </div>

        </div>
        <div *ngIf="this.originalResponse && this.originalResponse.length > 0" class="card-div active shadow1">
          <div class="filter-row">
            <div class="col-auto">
              <input [(ngModel)]="cardNameSearchValue" type='text' class="search-box"
                placeholder="{{ 'fuild.Searchbyemployeecardnameorlast4digitsofcardnumber' | translate}}" (input)="filterData()" />
            </div>
            <div class="col-auto">
              <div id="chartDepartment" style="margin-top: 5px;margin-left: 5px;width: 253px;">
                <div class="input33" style="background: #F7F7F9 !important;">
                  <ng-select #chartDepartment appendTo="#chartDepartment" [disabled]="this.disableDateType"
                    dropdownPosition="bottom" [searchable]="false" [clearable]="false" [(ngModel)]="deptValue"
                    [items]="departmentOptions" bindLabel="value" bindValue="value"
                    (change)="showDepartmentChanged($event.value)">
                    <ng-template ng-option-tmp let-option="item">
                        <span class="option-title">{{option.name |
                            translate}}</span>
                       
                    </ng-template>
                    <ng-template ng-label-tmp let-item="item">
                        <span> {{item.name | translate}}</span>
                      </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg class="down-arrow" (click)="chartDepartment.toggle()" style="left: 225px;top: -28px;" width="15"
                  height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
              </div>
            </div>
            <div class="col-auto">
              <div id="statusDropdown" style="margin-top: 5px;margin-left: 5px;width: 253px;">
                <div class="input" style="background: #F7F7F9 !important;">
                  <ng-select #statusDropdown appendTo="#statusDropdown" [disabled]="this.disableDateType"
                    dropdownPosition="bottom" [searchable]="false" [clearable]="false" [(ngModel)]="statusValue"
                    [items]="statusOptions" bindLabel="value" bindValue="id" (change)="showStatusChanged($event.id)">
                    <ng-template ng-option-tmp let-option="item">
                        <span class="option-title">{{option.name |
                            translate}}</span>
                       
                    </ng-template>
                    <ng-template ng-label-tmp let-item="item">
                        <span> {{item.name | translate}}</span>
                      </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg class="down-arrow" (click)="statusDropdown.toggle()" style="left: 225px;top: -28px;" width="15"
                  height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="(this.cardList && this.cardList.length ===0 && !this.showFluidPayMsg)"
          style="text-align:center !important;margin-top:15px !important;">
          <div class="text1 text-gray" style="font-size: 18px;display: inline-block;width: 100%;">
            {{this.resultErrorMsg}}
          </div>
        </div>
        <app-loader *ngIf="this.responseNotCame" [spinnerStyle]="true"></app-loader>
        <div class="section" *ngIf="(this.cardList && this.cardList.length > 0)">
          <div class="filter-row"
            style="justify-content: space-between !important;background-color: #DBDBDB !important;padding-top:10px;height:42px;">
            <div class="col-auto" style="width:150px;margin-right:4px !important;">
              <span class="show2"> {{ 'fuild.User' | translate}}</span>
            </div>
            <div class="col-auto" style="width:130px;margin-right:4px !important;">
              <span class="show2"> {{ 'fuild.Department' | translate}}</span>
            </div>
            <div class="col-auto" style="width:130px;margin-right:4px !important;">
              <span class="show2"> {{ 'fuild.Card' | translate}}</span>
            </div>
            <div class="col-auto" style="width:150px;margin-right:4px !important;">
              <span class="show2"> {{ 'fuild.Limit' | translate}}</span>
            </div>
            <div class="col-auto" style="width:130px;margin-right:4px !important;">
              <span class="show2"> {{ 'fuild.Utilization' | translate}}</span>
            </div>
            <div class="col-auto" style="width:150px;margin-right:4px !important;">
              <span class="show2"> {{ 'fuild.Status' | translate}}</span>
            </div>
          </div>
          <div *ngFor="let item of this.cardList ,let i=index;" class="{{i%2==0 ? 'filter-row1':'filter-row2'}}"
            style="">
            <div class="col-auto" style="width:150px;padding-top:15px;margin-right:4px !important;">
              <span class="show1" style="width:150px;">{{item.userName}}</span>
            </div>
            <div class="col-auto" style="width:130px; padding-top:15px;margin-right:4px !important;">
              <span class="show1" style="width:130px;">{{item.department}}</span>
            </div>
            <div class="col-auto" style="width:130px; padding-top:10px;margin-right:4px !important;">
              <img class="inlineblock_m"
                style="max-width:25px;margin-right:15px !important;position: relative;top: 8px;"
                src="assets/images/landing_page.png" /><span class="show1"
                style="">{{item.description}}</span>
              <div style="font-size: 12px !important;color: gray !important;margin-left: 45px;">{{item.last4}}</div>
            </div>
            <div class="col-auto" style="width:150px; padding-top:15px;margin-right:4px !important;">
              <span class="show1" style="width:150px;">{{getAmount(item.limit) | currency : getCurrencySymbol(this.currency) :
                'code':
                '1.2-2'}} {{getItemPeriodLabel(item.period)}}</span>
            </div>
            <div class="col-auto" style="width:130px; padding-top:15px;margin-right:4px !important;">
              <span class="show1" style="width:130px;">{{getAmount(item.used) | currency : getCurrencySymbol(this.currency) :
                'code':
                '1.2-2'}}</span>
            </div>
            <div class="col-auto" style="width:150px; padding-top:15px;margin-right:4px !important;">
              <span class="show1" id="userModal_{{i}}" (click)="openMenuModal('userModal_'+i)"
                style="width:auto;cursor: pointer;text-transform: capitalize;"
                [ngStyle]="{'color':item.status==='active' ? 'green':'red'}">{{ item.status}}</span> <i
                (click)="openMenuModal('userModal_'+i)"
                style="cursor:pointer;position: relative;left: 10px !important;top: -2px;"
                class="fa fa-angle-down"></i><span *ngIf="this.revealLoader && this.index===i"
                style="margin-left:10px;">
                <loader-dots></loader-dots>
              </span>
              <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'userModal_'+i)" [hideDelay]="0"
                (onClose)="handleModalEvents('onClose', 'userModal_'+i)"
                (onDismiss)="handleModalEvents('onDismiss', 'userModal_'+i)" [closable]="false" #userModal
                identifier="userModal_{{i}}">

                <div class="modal-container flight-modal-container filter-modal1 modalAirportFilterInfo"
                  (click)="$event.stopPropagation();">

                  <div class="modal-body" style="text-align: left;padding-bottom:5px;display: grid !important;">
                    <a style="color:#413E3B !important;text-transform: capitalize !important;">
                      <div class="items" (click)="addCardModal(addCard,'edit',item.cardId,'userModal_'+i)"> {{ 'fuild.View' | translate}}</div>
                    </a>
                    <a *ngIf="item.status==='active'"
                      style="color:#413E3B !important;text-transform: capitalize !important;">
                      <div class="items"
                        (click)="updatestatus(item.description,item.cardId,'inactive','userModal_'+i,cancelCardModal,i)">
                         {{ 'fuild.Suspend' | translate}}</div>
                    </a>
                    <a *ngIf="item.status==='inactive'"
                      style="color:#413E3B !important;text-transform: capitalize !important;">
                      <div class="items"
                        (click)="updatestatus(item.description,item.cardId,'active','userModal_'+i,cancelCardModal,i)">
                        {{ 'fuild.Activate' | translate}}</div>
                    </a>
                    <a *ngIf="item.status!=='canceled' && item.status!=='inactive'"
                      style="color:#413E3B !important;text-transform: capitalize !important;">
                      <div class="items"
                        (click)="updatestatus(item.description,item.cardId,'canceled','userModal_'+i,cancelCardModal,i)">
                         {{ 'fuild.Cancel' | translate}}</div>
                    </a>
                    <a *ngIf="item.status!=='canceled'  && item.status!=='inactive'"
                      style="color:#413E3B !important;text-transform: capitalize !important;">
                      <div class="items"
                        (click)="updatestatus(item.description,item.cardId,'canceled','userModal_'+i,cancelCardModal,i)">
                        {{ 'fuild.ManageCards' | translate}}</div>
                    </a>
                    <a style="color:#413E3B !important;text-transform: capitalize !important;">
                      <div class="items" style="margin-bottom: 0px;"
                        (click)="routeToTransactions('userModal_'+i,item.last4)"> {{ 'fuild.Transactions' | translate}}</div>
                    </a>
                  </div>
                </div>
              </ngx-smart-modal>
            </div>
          </div>


        </div>
        <div *ngIf="this.showFluidPayMsg && showNotCompleteScreen">
          <div class="boldmsg">
           
            {{ 'fuild.YouarealmostreadytomodernizeyourpaymentmanagementusingFluidPay' | translate}}
          </div>
          <div class="notcompleteImge">
            <img style="max-width: 400px;" src="assets/images/Kalendar_AI-4.png">
          </div>
          <div class="restmsg">
            {{ 'fuild.WeareverifyingtheinformationandwewillsoonactivateFluidPayforyoutypicallywithin5-7businessdays' | translate}}
          </div>
        </div>
        <div *ngIf="this.showFluidPayMsg && !showNotCompleteScreen" class="showMsgforNNotIssuingCard">
          <div class="heading-card">
             {{ 'fuild.ManageCards' | translate}}
          </div>
          <div class="restmsg">
            {{ 'fuild.FluidPayisamodernpaymentmanagementsolution.WithFluidPayyouremployeeswillgetasimplewaytopayforwhatsneededwhilefinanceteamsgetoneplacetobettercontrolmanageandtrackbusinessspend.Thebuilt-incontrolsonsmartcardsensurespendcompliancereal-timespendvisibilityandautomatedexpensereporting.Thissolutionisdesignedtoproviderefreshingexperiencewhetheremployeespayfortravelroutineexpensesspotpurchasesorsoftwaresubscriptions' | translate}}
          </div>
          <div class="restmsg">
             {{ 'fuild.FluidPayislimitedaccessfeatureToenableFluidPayforyourcompanyrequesttheaccessbelow' | translate}}
          </div>
          <div *ngIf="!this.CardRequested && this.onboardmsg1 !=='Request Access To FluidCash'" class="request-button">
            <button class="btn btn-secondary2"
              style="text-transform:inherit !important;cursor:default !important;"><span class="add2"
                style="width: auto;"><i class="fa fa-check" aria-hidden="true"
                  style="margin-right: 5px;color:rgb(31,190,151) !important;"></i>{{this.onboardLevel | translate}}</span></button>
          </div>
          <div *ngIf="!this.CardRequested" class="request-button">
            <a href="{{this.onboardlink}}" target="_blank"> <button *ngIf="this.onboardlink" class="btn btn-secondary"
                style="width: 400px;text-transform:capitalize !important;"><span class="add"
                  style="width: auto;">{{this.onboardMsg | translate}}</span></button></a>
            <button *ngIf="!this.onboardlink" class="btn btn-secondary"
              style="width: 400px;text-transform:capitalize !important;" (click)=requestCard()><span class="add"
                style="width: auto;">{{this.onboardMsg | translate}} <loader-dots *ngIf="bankLodershow" class="loaderAlign">
                </loader-dots></span></button>
          </div>
          <div *ngIf="this.CardRequested" class="request-button1">
            <button class="btn btn-secondary"
              style="width: 700px;background-color:#6c757d59;text-transform:none !important;" [disabled]="true"><span
                class="add" style="width: auto;color:gray;"> {{ 'fuild.YourrequestissubmittedOurteamwillreachouttoyoushortly' | translate}}</span></button>
          </div>
        </div>
      </div>

      <div [hidden]="this.viewMode==='tab1'">
        <div class="card-div active shadow1">
          
          <div class="filter-row">
            <div class="col-auto" style="margin-top: -18px;margin-left:60px;">
              <span class="show" style="margin-right:5px;position: relative;top: 28px;left: -65px;">{{ 'report.Period' | translate }}:</span>
              <div class="filter custom-selectbox">
                <div class="input-box" style="">
                  <span   (click)="openNgxModal('daterangeSelection1',chartDatePicker);chartDatePicker.show()" class="input-icon material-icons">event</span>
                  <input class="input" id="daterangeSelection1" style="min-width:210px !important;"
                    (click)="openNgxModal('daterangeSelection1',chartDatePicker);chartDatePicker.show()"
                    readonly=""><span class="dateShow"
                    (click)="openNgxModal('daterangeSelection1',chartDatePicker);chartDatePicker.show()">{{daterangepickerModel[0]
                    | date : 'dd MMM yyyy'}} - {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span>

                </div>
                <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'daterangeSelection1')" [hideDelay]="0"
                  (onClose)="handleModalEvents('onClose', 'daterangeSelection1')"
                  (onDismiss)="handleModalEvents('onDismiss', ' daterangeSelection1')" [closable]="false"
                  #daterangeSelection1 identifier="daterangeSelection1">
                  <div class="modal-container flight-modal-container filter-modal modalAirportFilterInfo1"
                    [ngStyle]="changeStyle()" (click)="$event.stopPropagation();">
                    <div class="modal-header" style="background-color: #fff !important;padding-top:10px !important;">
                      <div class="tab-list top-strip" style="">

                        <ul>
                          <li class="{{ viewMode1 == 'tab12' ? 'tab-list-item1':'tab-list-item'}}"
                            [class.active]="viewMode1 == 'tab12'" rel="tab11" (click)="presetsTabClicked()"
                            style="margin-left:0px !important;margin-right:0px !important;z-index: 100;">{{ 'report.Presets' | translate }}<div
                              *ngIf="viewMode1 == 'tab12'" class="underline"></div>
                          </li>
                          <li class="{{ viewMode1 == 'tab11' ? 'tab-list-item1':'tab-list-item'}}"
                            [class.active]="viewMode1 == 'tab11'" rel="tab14" (click)="customTabClicked()">{{ 'report.Custom' | translate }} 
                            <div *ngIf="viewMode1 == 'tab11'" class="underline"></div>
                          </li>
                        </ul>
                      </div>
                      <input class="input" [hidden]="viewMode1=='tab12'"
                        style="width:100% !important;position: relative;top:8px;" readonly=""><span class="dateShow1"
                        [hidden]="viewMode1=='tab12'" style="top:23px !important;">{{daterangepickerModel[0] | date :
                        'dd MMM yyyy'}} -
                        {{daterangepickerModel[1] | date: 'dd MMM yyyy'}}</span>
                    </div>
                    <hr>
                    <div [hidden]="viewMode1=='tab12'" style="position: relative;top: -30px;">
                      <input class="input" bsDaterangepicker #chartDatePicker="bsDaterangepicker"
                        style="width:100% !important;visibility: hidden;" [(ngModel)]="daterangepickerModel"
                        (bsValueChange)="setStartDate($event)" [outsideClick]="true" [maxDate]="maximumDate1"
                        [bsConfig]="{ showWeekNumbers: false , showPreviousMonth: true }"
                        (onShown)="onShowPicker($event, chartDatePicker)" (onHidden)="onHidePicker()" container=""
                        readonly />
                    </div>
                    <div class="modal-body" [ngStyle]="changeStyle1()">
                      <div [hidden]="viewMode1=='tab11'">
                        <div *ngFor="let item of this.dateOptions1;let i=index"
                          style="width: auto;min-height: 15px;display:flex;line-height: 3em;min-height: 3em;">
                          <div style="font-size:14px;color:#5f6368;padding-left:22px;margin-bottom:7px;cursor: pointer;"
                            (click)="getSelectedCurrentDate1(item.id)">{{item.name | translate}}</div>
                        </div>

                      </div>
                    </div>
                  </div>
                </ngx-smart-modal>
              </div>

            </div>
            <div *ngIf="this.originalTransactionReport && this.originalTransactionReport.length >0" class="col-auto">
              <div id="cardList" style="margin-top: 5px;margin-left: 5px;width: 200px;">
                <div class="input" style="background: #F7F7F9 !important;width: 200px !important;">
                  <ng-select #cardListt appendTo="#cardList" [disabled]="this.disableDateType" dropdownPosition="bottom"
                    [searchable]="false" [clearable]="false" [(ngModel)]="cardValue" [items]="cardsOptions" bindLabel=""
                    bindValue="last4" (change)="showCardChanged($event)">
                    <ng-template ng-label-tmp let-item="item">
                      <span class="cardName" style="top:0px">{{item.name | translate}}</span><span class="cardName"
                        style="position: relative;top: 0px;max-width:100px !important;">{{item.last4}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-index="index">
                      <span class="cardName" style="top: 2px !important">{{item.name | translate}}</span><span>{{item.last4}}</span>
                    </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg class="down-arrow" (click)="cardList.toggle()" style="left: 175px;top: -28px;" width="15"
                  height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
              </div>
            </div>
            <div *ngIf="this.originalTransactionReport && this.originalTransactionReport.length >0" class="col-auto">
              <div id="receip" style="margin-top: 5px;margin-left: 5px;width: 200px;">
                <div class="input" style="background: #F7F7F9 !important;width: 200px !important;">
                  <ng-select #receipt appendTo="#receip" [disabled]="this.disableDateType" dropdownPosition="bottom"
                    [searchable]="false" [clearable]="false" [(ngModel)]="receiptValue" [items]="receiptOption"
                    bindLabel="value" bindValue="id" (change)="showReceiptChanged($event)">
                    <ng-template ng-option-tmp let-option="item">
                        <span class="option-title">{{option.name |
                            translate}}</span>
                       
                    </ng-template>
                    <ng-template ng-label-tmp let-item="item">
                        <span> {{item.name | translate}}</span>
                      </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg class="down-arrow" (click)="receip.toggle()" style="left: 175px;top: -28px;" width="15" height="9"
                  viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
              </div>
            </div>
            <div *ngIf="this.originalTransactionReport && this.originalTransactionReport.length >0" class="col-auto">
              <div id="exportDropdown" style="margin-top: 5px;margin-left: 5px;width: 200px;">
                <div class="input" style="background: #F7F7F9 !important;width: 200px !important;">
                  <ng-select #exportDropdown appendTo="#exportDropdown" [disabled]="this.disableDateType"
                    dropdownPosition="bottom" [searchable]="false" placeholder="{{myPlaceholder}}" [clearable]="false"
                    [(ngModel)]="fileTypeValue" [items]="getFileOptions()" bindLabel="value" bindValue="id"
                    (change)="showFileTypeChanged($event.id,qboUploadingModal)">
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg class="down-arrow" (click)="exportDropdown.toggle()" style="left: 175px;top: -28px;" width="15"
                  height="9" viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
              </div>
            </div>
          </div>
          <div class="filter-row">
            <div *ngIf="this.originalTransactionReport && this.originalTransactionReport.length >0" class="col-auto">
              <input #input1 [(ngModel)]="cardNameSearchValueForTransaction" (blur)="authidSearch()" type='text'
                class="search-box" placeholder="{{ 'fuild.Searchbyemployeecardname' | translate }}" (input)="filterTransactionnData()" />
            </div>
            <div *ngIf="showDeclinneSwitch" class="col-auto">
              <div class="checkbox-container" style="padding-left:20px !important;">
                <div class="row">
                  <div class="col-8" style="padding-top:10px;">
                    <span class="switchLabel">{{ 'cards.Showdeclinedtransactions' | translate }}:</span>
                  </div>
                  <div class="col-4" style="padding-top:3px !important;padding-left:38px;">
                    <ui-switch color="gray" [(ngModel)]="showDelineTransaction" [checked]="showDelineTransaction"
                      (change)="changeTrannsaction($event)" checkedLabel="{{ 'cards.ON' | translate }}" uncheckedLabel="{{ 'cards.OFF' | translate }}"></ui-switch>

                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="section" style="margin-top: 30px !important;" *ngIf="(this.originalTransactionReport && this.originalTransactionReport.length >0)">
            <div class="row" style="text-align: left;white-space: nowrap;">
                <div class="" style="margin-left: 8px;"><span *ngIf="this.transactionProccessedTab"
                    class="{{ this.transactionTab==='tab55' ? 'items111':'items1'}}" (click)="processedTabChanging('tab55','closed')"> {{ 'fuild.Processed' | translate}}</span>
                  <span *ngIf="this.transactionPendingTab" class="{{ this.transactionTab==='tab56' ? 'items111':'items1'}}"
                    (click)="processedTabChanging('tab56','pending')"> {{ 'fuild.Pending' | translate}}</span>
        
                </div>
              </div>
              </div>
        <div *ngIf="(this.transactionReport && this.transactionReport.length ===0)"
          style="text-align:center !important;margin-top:15px !important;">
          <div class="text1 text-gray" style="font-size: 18px;display: inline-block;width: 100%;">
            {{this.resultErrorMsg1}}
          </div>
        </div>
        <app-loader *ngIf="this.transactionresponseNotCame" [spinnerStyle]="true"></app-loader>
       
        <div class="section" style="margin-top: 0px !important;"*ngIf="(this.transactionReport && this.transactionReport.length > 0)">
          <div class="filter-row"
            style="justify-content: space-between !important;background-color: #DBDBDB !important;padding-top:10px;height:42px;">
            <div class="col-auto" style="width:180px;">
              <div class="checkbox-container1" style="margin-top:0px !important;margin-bottom:4px !important;">
                <label class="" for="'All Transaction'">
                  <input type="checkbox" id="'All Transaction'" style="cursor:pointer !important;"
                    class="mdl-checkbox__input" value="All Transaction"
                    (change)="onSelectTransactionRow('All Transaction',$event.target.checked)"
                    [checked]="this.transactionReport.length===this.rowSelectedForDownload.length">
                  <span class="mdl-checkbox__label"></span>
                </label>
                <span class="show2" style="display: inline !important;"> {{ 'cards.Date' | translate }}</span>
              </div>
            </div>
            <div class="col-auto" style="width:140px;margin-right:4px !important;">
              <span class="show2"> {{ 'cards.Merchant' | translate }}</span>
            </div>
            <div class="col-auto" style="width:100px;margin-right:4px !important;">
              <span class="show2"> {{ 'cards.User' | translate }}</span>
            </div>
            <div class="col-auto" style="width:150px;margin-right:4px !important;">
              <span class="show2"> {{ 'cards.MerchantCategory' | translate }}</span>
            </div>
            <div class="col-auto" style="width:240px;margin-right:4px !important;">
              <span class="show2"> {{ 'cards.ExpenseCategory' | translate }}</span>
            </div>
            <div class="col-auto" style="width:100px;margin-right:4px !important;">
              <span class="show2"> {{ 'cards.Amount' | translate }}</span>
            </div>
            <div *ngIf="showDelineTransaction" class="col-auto" style="width:150px;;margin-right:4px !important;">
              <span class="show2"> {{ 'cards.Status' | translate }}</span>
            </div>
            <div *ngIf="!showDelineTransaction" class="col-auto"
              style="width:50px; padding-top:15px;margin-right:0px !important;">
            </div>
            <div *ngIf="!showDelineTransaction" class="col-auto"
              style="width:50px; padding-top:15px;margin-right:0px !important;">
            </div>
            <div class="col-auto" style="width:50px; padding-top:15px;margin-right:0px !important;">
            </div>
          </div>
          <div *ngFor="let trans of this.transactionReport,let i=index;"
            class="{{i%2==0 ? 'filter-row1':'filter-row2'}}" style="">
            <div class="col-auto" style="width:180px;padding-top:11px;cursor:pointer !important;">
              <div class="checkbox-container1" style="margin-top:4px !important;margin-bottom:4px !important;">
                <label class="" for="{{trans.authId}}}">
                  <input type="checkbox" style="cursor:pointer !important;" id="{{trans.authId}}"
                    class="mdl-checkbox__input" (change)="onSelectTransactionRow(trans.authId,$event.target.checked)"
                    [checked]="isTransChecked(trans.authId)">
                  <span class="mdl-checkbox__label"></span>
                </label>
                <span class="show1">{{trans.date | date : 'dd MMM yy ,hh:mm' }}</span>
              </div>
            </div>
            <div class="col-auto" style="width:140px; padding-top:15px;margin-right:4px !important;">
              <span class="show1" style="width:140px;">{{trans.Merchant}}</span>
            </div>
            <div class="col-auto" style="width:100px; padding-top:15px;margin-right:4px !important;">
              <span class="show1" style="width:100px;">{{trans.name}}</span>
            </div>
            <div class="col-auto" style="width:150px; padding-top:15px;margin-right:4px !important;">
              <span class="show1" style="width:150px;">{{trans.category}} </span>
            </div>
            <div class="col-auto" style="width:240px; padding-top:8px;margin-right:4px !important;">

              <div
                *ngIf="this.expenseCategory && this.expenseCategory.length >0  && isTransactionIsTopUp(trans.authId) && trans.Status!=='declined'"
                class="input2" style="padding-right: 5px;bottom: 5px;width: auto !important;"
                [ngStyle]="{'border-color': trans.qboAccess && trans.qboAccess==='synced' ? '#E7E6E4':this.searchService.darkBgColor}">
                <div id="expense_DropDown{{i}}">
                  <ng-select #expenselist appendTo="#expense_DropDown{{i}}" style="bottom: 2px;padding-left: 10px;"
                    dropdownPosition="middle" [searchable]="true" placeholder="{{getPlaceHolder(trans.Expense)}}"
                    [clearable]="true" [disabled]="trans.qboAccess==='synced'" (open)="this.dropDownopen1=true;" (close)="this.dropDownopen1=false;"
                    (change)="showExpenseTypeChanged($event,trans.authId)" [items]="this.expenseCategory"
                    [(ngModel)]="trans.Expense" bindLabel="" bindValue="categoryId"
                    [searchFn]="this.searchByExpenseName">
                    <ng-template *ngIf="!this.dropDownopen1" ng-label-tmp let-item="item">
                      <span> {{item.qualifiedName}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                      <span style="padding-right:20px;">{{item.name}}</span>
                    </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg *ngIf="trans.qboAccess && trans.qboAccess==='synced'" class="down-arrow1"
                  (click)="expenselist.toggle()" [ngStyle]="getChangeStyle(trans.Expense)"
                  style="position:relative;float:right;right:30px;left:-5px;" width="15" height="9" viewBox="0 0 15 9"
                  fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="gray" />
                </svg>
                <svg *ngIf="!trans.qboAccess || trans.qboAccess!=='synced'" class="down-arrow1"
                  (click)="expenselist.toggle()" [ngStyle]="getChangeStyle(trans.Expense)"
                  style="position:relative;float:right;right:30px;left:-5px;" width="15" height="9" viewBox="0 0 15 9"
                  fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
              </div>
            </div>
            <div class="col-auto" style="width:100px; padding-top:15px;margin-right:4px !important;">
              <span class="show1" style="width:100px;"> {{trans.Amount}}</span>
            </div>
            <div *ngIf="showDelineTransaction" class="col-auto"
              style="width:150px; padding-top:15px;margin-right:4px !important;">
              <span class="show1" style="width:100px;color: red !important;text-transform: capitalize !important;">
                {{trans.Status}}</span>
            </div>
            <div *ngIf="!showDelineTransaction" class="col-auto"
              style="width:50px; padding-top:15px;margin-right:4px !important;">
             
              <span *ngIf="isTransactionIsTopUp(trans.authId)" [tooltip]="'cards.attachareceipt' | translate"  offset="8" placement="top"
                [display]="true" content-type="string"><input type="file" #fileInput id="receiptFile"
                  accept=".xlsx,.pdf,.jpg,.jpeg,.png" (click)="fileInput.value = null" value=""
                  (change)="readURL($event.target.files,trans.authId,i);" style="display:none;" single>
                <img class="memoImage" style="margin-right: 4px;" *ngIf="!trans.receiptMsg"
                  src="assets/images/receipt.png" (click)="this.selectedCardNumber=trans.authId"
                  onclick="document.getElementById('receiptFile').click()"></span>
              <span *ngIf="isTransactionIsTopUp(trans.authId)" [tooltip]="'cards.viewreceipt' | translate" offset="8"
                placement="top" [display]="true" content-type="string"> <a href="{{trans.receiptMsg}}"
                  target="_blank"><img class="memoImage" style="margin-right: 4px;" *ngIf="trans.receiptMsg"
                    src="assets/images/receiptuploaded.png"></a></span>
              <span *ngIf="isTransactionIsTopUp(trans.authId)" [tooltip]="'cards.addamemo' | translate" offset="8" placement="top"
                [display]="true" content-type="string"><img class="memoImage" *ngIf="!trans.memoMsg"
                  (click)="openMemoModal(memoModal,trans.authId,trans)" src="assets/images/memo.png"></span><span
                *ngIf="isTransactionIsTopUp(trans.authId)" [tooltip]="getMemotext(trans)" offset="8" placement="top"
                [display]="true" content-type="string"><img class="memoImage"
                  (click)="openMemoModal(memoModal,trans.authId,trans)" *ngIf="trans.memoMsg"
                  src="assets/images/memouploaded.png"></span>


            </div>
            <div *ngIf="!showDelineTransaction" class="col-auto"
              style="width:50px; padding-top:15px;margin-right:0px !important;">
          
              <span [tooltip]="getSynctext(trans)" offset="8" placement="top" [display]="true"
                content-type="string"> <i *ngIf="trans.qboAccess==='error'" class="fa fa-exclamation-triangle"
                  aria-hidden="true"
                  style="cursor:pointer;color: orange;font-size: 20px;top: 4px;position: relative;"></i></span>
              <span> <img class="memoImage" *ngIf="trans.qboAccess==='synced'" src="assets/images/qb_thumb.png"></span>
            </div>
            <div class="col-auto" style="width:50px; padding-top:15px;margin-right:0px !important;">
              <span *ngIf="isTransactionIsTopUp(trans.authId)" style="color: gray;cursor: pointer;"
                (click)="openTransactionDetailModal(trasactionDetailModal,trans.authId)"><i
                  class="fa fa-angle-right"></i></span>
            </div>


          </div>
        </div>
      </div>
    </div>
  </div>
  <ng-template #qboUploadingModal let-modal>
    <div class="modal-header">
      <h5 class="modal-title" id="myModalLabel" style="width: 100% !important; text-align: left">
        {{ 'fuild.ExporttoQuickBooks' | translate }}</h5>
      <button type="button" style="color: white;" class="close" data-dismiss="modal" (click)="onModelCancel()">
        <i class="material-icons">close</i>
      </button>
      <div *ngIf="this.updateCard" class="approval_request_diaglog_bg_clickhandler" (click)="$event.stopPropagation();">
      </div>
    </div>
    <div class="modal-body requestModalBody">
      <div class="input-field" sttyle="font-family:var(--globalFontfamilyr);font-weight: bold;;">
        <label> {{ 'fuild.ExportingtoQuickBooksonlinePleasewait' | translate }}</label>
      </div>
      <div style="margin-top: 20px;margin-bottom: 20px;margin-left:auto;margin-right:auto">
        <app-loader *ngIf="this.showQboLoader" [spinnerStyle]="true"></app-loader>
      </div>
      <div class="modal-form-button1">

      </div>
    </div>
    <div class="modal-footer1">

    </div>
  </ng-template>
  <ng-template #trasactionDetailModal let-modal>
    <div class="modal-header">
      <h5 class="modal-title" id="myModalLabel" style="width: 100% !important; text-align: left">
         {{ 'cards.TransactionDetails' | translate }}</h5>
      <button type="button" style="color: white;" class="close" data-dismiss="modal"
        (click)="onModelTransactionCancel()">
        <i class="material-icons">close</i>
      </button>
      <div *ngIf="this.updateCard" class="approval_request_diaglog_bg_clickhandler" (click)="$event.stopPropagation();">
      </div>
    </div>
    <div class="modal-body" style=" text-align: left !important;padding-bottom: 17px !important;">
      <div class="colItem" style="margin-top: 20px;">
         {{ 'cards.TransactionDate' | translate }}
      </div>
      <div class="itemcol">
        {{this.transactionSelected[0].date | date : 'dd MMM yy ,hh:mm'}}
      </div>
      <div class="colItem">
         {{ 'cards.Merchant' | translate }}
      </div>
      <div class="itemcol">
        {{this.transactionSelected[0].Merchant}}
      </div>
      <div class="colItem">
         {{ 'cards.Name' | translate }}
      </div>
      <div class="itemcol">
        {{this.transactionSelected[0].name}}
      </div>
      <div class="colItem">
         {{ 'cards.Department' | translate }}
      </div>
      <div class="itemcol">
        {{this.transactionSelected[0].Department}}
      </div>
      <div class="colItem">
         {{ 'cards.CardNumber' | translate }}
      </div>
      <div class="itemcol">
        {{this.transactionSelected[0].CardNumber}}
      </div>
      <div class="colItem">
         {{ 'cards.Status' | translate }}
      </div>
      <div class="itemcol">
        {{this.transactionSelected[0].Status}}
      </div>
      <div *ngIf="this.transactionSelected[0].decline!==''" class="colItem">
         {{ 'cards.Reasonfordecline' | translate }}
      </div>
      <div class="itemcol">
        {{this.transactionSelected[0].decline}}
      </div>
      <div class="colItem">
         {{ 'cards.Amount' | translate }}
      </div>
      <div class="itemcol">
        {{this.transactionSelected[0].Amount}}
      </div>
      <div *ngIf="this.transactionSelected[0].syncError!==''" class="colItem">
         {{ 'fuild.Qbosyncerror' | translate }}
      </div>
      <div class="itemcol">
        {{this.transactionSelected[0].syncError}}
      </div>
      <div *ngIf="showQucikBookButtonWithCompanyName && !showDelineTransaction && this.transactionSelected[0].Expense"
        class="colItem">
         {{ 'cards.ExpenseCategory' | translate }}
      </div>
      <div *ngIf="showQucikBookButtonWithCompanyName && !showDelineTransaction && this.transactionSelected[0].Expense"
        class="itemcol">
        {{this.transactionSelected[0].ExpenseValue}}
      </div>
      <div class="colItem">
         {{ 'cards.MerchantCategory' | translate }}
      </div>
      <div class="itemcol">
        {{this.transactionSelected[0].category}}
      </div>
      <div *ngIf="!showDelineTransaction" class="colItem">
         {{ 'cards.Receipt' | translate }}
      </div>
      <div *ngIf="!showDelineTransaction && !this.transactionSelected[0].receiptMsg" class="colItem">
        <input type="file" #fileInput id="receiptFile" accept=".xlsx,.pdf,.jpg,.jpeg,.png,.gif"
          (click)="fileInput.value = null" value=""
          (change)="readURL($event.target.files,this.transactionSelected[0].authId,0);" style="display:none;" single>
        <span class="addlue" style="cursor:pointer" (click)="this.selectedCardNumber=this.transactionSelected[0].authId"
          onclick="document.getElementById('receiptFile').click()"> {{ 'cards.Addreceipt' | translate }}</span>
      </div>
      <div *ngIf="!showDelineTransaction && this.transactionSelected[0].receiptMsg" class="itemcol">
        <a href="{{this.transactionSelected[0].receiptMsg}}" target="_blank">  {{ 'cards.clicktoview' | translate }}</a> <span
          *ngIf="(!this.transactionSelected[0].qboAccess ||  this.transactionSelected[0].qboAccess!=='synced') && !this.updateCard"
          style="margin-left: 10px;cursor: pointer;"> <img src="assets/images/ic_delete.svg"
            (click)="getDeleteReceipt(this.transactionSelected[0].authId)"></span>
      </div>
      <div *ngIf="!showDelineTransaction" class="colItem">
         {{ 'cards.Memo' | translate }}
      </div>
      <div *ngIf="!showDelineTransaction && !this.transactionSelected[0].memoMsg" class="colItem">
        <span class="addlue" style="cursor:pointer"
          (click)="openMemoModal(memoModal,this.transactionSelected[0].authId,this.transactionSelected[0])">  {{ 'cards.Addmemo' | translate }}</span>
      </div>
      <div *ngIf="!showDelineTransaction && this.transactionSelected[0].memoMsg" class="itemcol">
        {{this.transactionSelected[0].memoMsg}} <span
          *ngIf="(!this.transactionSelected[0].qboAccess ||  this.transactionSelected[0].qboAccess!=='synced')"
          style="margin-left:5px; cursor: pointer;"> <img src="assets/images/icon_edit.svg" style="margin-right:10px;"
            (click)="openMemoModal(memoModal,this.transactionSelected[0].authId,this.transactionSelected[0])"></span>
      </div>
    </div>
    <div class="modal-form-button">
      <div style="text-align: center;margin-top: 20px;margin-bottom:20px;">
        <button class="btn btn-secondary" (click)="onModelTransactionCancel()"><span class="add"> {{ 'cards.Okay' | translate }}</span> </button>
      </div>
    </div>
  </ng-template>
  <ng-template #memoModal let-modal>
    <div class="modal-header">
      <h5 class="modal-title" id="myModalLabel" style="width: 100% !important; text-align: left">
         {{ 'cards.Addamemo' | translate }}</h5>
      <button type="button" style="color: white;" class="close" data-dismiss="modal" (click)="onModelMemoCancel()">
        <i class="material-icons">close</i>
      </button>
      <div *ngIf="this.updateCard" class="approval_request_diaglog_bg_clickhandler" (click)="$event.stopPropagation();">
      </div>
    </div>
    <div class="modal-body requestModalBody">
      <div class="input-field">
        <label> {{ 'cards.Pleaseaddamemotothisexpense' | translate }}</label>
        <input maxlength="100" type="text" placeholder=" {{ 'cards.Adddetailsforthisexpense' | translate }}" [(ngModel)]="memoMsg"
          (focus)="getChecked()" class="modal-textarea input-textfield"><span  class="showNumber">
              {{memoMsg.length}}/100
            </span>
      </div>
      <div *ngIf="showChangeRequestErro">
        <span class="text-danger"> {{ 'cards.Pleaseaddamemotothisexpense' | translate }} </span>
      </div>
      <div class="modal-form-button1">
        <button *ngIf="!this.updateCard" class="btn primary-button" (click)="onAddMemo()"
          [disabled]="this.updateCard"> {{ 'cards.Addmemo' | translate }} </button>
        <button *ngIf="this.updateCard" class="btn primary-button" [disabled]="this.updateCard"><span class="add"
            style="font-family: var(--globalFontfamilyr);font-weight: bold !important;"> {{ 'cards.Wait' | translate }}</span></button><span *ngIf="this.updateCard"
          class="loaderClass">
          <loader-dots class="loaderAlign"></loader-dots>
        </span>
      </div>
    </div>
    <div class="modal-footer1">

    </div>
  </ng-template>
  <ng-template #transferFunds let-modal>
    <div class="modal-header">
      <h5 class="modal-title" id="myModalLabel">
        <span> {{ 'fuild.Transferfunds' | translate }}</span>
      </h5>
      <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
    </div>
    <div *ngIf="this.updateCard" class="approval_request_diaglog_bg_clickhandler" (click)="$event.stopPropagation();">
    </div>
    <div class="modal-body" style=" text-align: left !important;padding-bottom: 17px !important;">
      <div style="margin-left:0px !important;">
        <form [formGroup]="transferFundsForm" style="margin-bottom: 30px;">
          <div style="padding-left:0px;">
            <label class="showLabel">  {{ 'fuild.Transferfrom' | translate }} *</label>
            <div class="" style="margin-bottom:15px;margin-top:5px;display:block !important;width: 353px;">
              <div id="expenselistDiv11">
                <div class="input2" style="padding-right: 40px;bottom: 5px;"
                  [ngStyle]="{'border-color':false ? '#E7E6E4':this.searchService.darkBgColor}">
                  <ng-select #expenselist appendTo="#expenselistDiv11" style="bottom: 5px;" (click)="openDropdown()"
                    (close)="closeDropdown1()" dropdownPosition="middle" [searchable]="true" placeholder="{{'employee.Select' | translate}}"
                    [clearable]="true" formControlName="bankId" (change)="showBankTypeChanged($event)"
                    [items]="this.bankAccounts" bindLabel="" bindValue="bankAccountId" [searchFn]="searchByBankName">
                    <ng-template *ngIf="!this.dropDownopen1" ng-label-tmp let-item="item">
                      <span> {{item.accountName}} - {{item.accountNumberLast4}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                      <span style="text-overflow: ellipsis;display: inline-block !important;
                                      white-space: nowrap;overflow: hidden;padding-right:20px;">{{item.accountName}} -
                        {{item.accountNumberLast4}}</span>
                    </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg class="down-arrow" (click)="expenselist.toggle()"
                  style="top: -22px;position:relative;float:right;right:20px;left:-10px;" width="15" height="9"
                  viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
              </div>
              <div
                *ngIf="transferFundsForm.controls['bankId'].hasError('required') && (transferFundsForm.controls['bankId'].touched || transferFundsForm.controls['bankId'].dirty)"
                class="error">{{'login.thisfieldisrequired' | translate}}</div>
            </div>

            <label class="showLabel"> {{ 'fuild.Amount' | translate }} *</label>
            <div class="" style=" margin-bottom:15px;margin-top:5px;display: flex;">
              <div style="padding-left: 0px;">
                <input type="number" class="input3" formControlName="amount" (input)="inputAmount($event.target.value)">
                <div
                  *ngIf="transferFundsForm.controls['amount'].hasError('required') && (transferFundsForm.controls['amount'].touched || transferFundsForm.controls['amount'].dirty)"
                  class="error">{{'login.thisfieldisrequired' | translate}}</div>
                <div *ngIf="this.showAmountError" class="error">{{ 'fuild.Morethanzerorequired' | translate }}</div>
              </div>
            </div>
            <label *ngIf="this.verficatioCodeSennd" class="showLabel">{{ 'fuild.Verificationcodesentto' | translate }} {{this.userid}}
              )*</label>
            <div *ngIf="this.verficatioCodeSennd" class=""
              style="margin-bottom:15px;margin-top:5px;height:32px;display:block !important;">
              <input type="number" class="input3" formControlName="verificationCode" required><span
                *ngIf="!this.updateCard" class="add"
                style="font-family: var(--globalFontfamilyr);font-weight: bold !important;margin-left:10px;cursor: pointer;"
                (click)="resendVerificationCode();this.updateCard=true;"> {{ 'fuild.ResendCode' | translate }}</span>
              <span *ngIf="this.updateCard" class="add"
                style="font-family: var(--globalFontfamilyr);font-weight: bold !important;margin-left:10px;cursor: pointer;opacity: 0.7;"> {{ 'fuild.ResendCode' | translate }}</span>
              <div
                *ngIf="transferFundsForm.controls['verificationCode'].hasError('required') && (transferFundsForm.controls['verificationCode'].touched || transferFundsForm.controls['verificationCode'].dirty)"
                class="error">{{'login.thisfieldisrequired' | translate}}</div>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="modal-form-button" style="padding: 0px 25px 47px 35px">
      <div class="text-left" style="margin-top: 40px;">
        <button *ngIf="!this.updateCard" class="btn btn-secondary" (click)="transferFundsCall()"><span class="add"
            style="font-family: var(--globalFontfamilyr);font-weight: bold !important;"><span *ngIf="!this.verficatioCodeSennd">{{ 'fuild.ReviewTransfer' | translate }}</span>
            <span *ngIf="this.verficatioCodeSennd"> {{ 'fuild.ConfirmTransfer' | translate }}</span>
          </span></button>
        <button *ngIf="this.updateCard" class="btn btn-secondary" [disabled]="this.updateCard"><span class="add"
            style="font-family: var(--globalFontfamilyr);font-weight: bold !important;"> {{ 'fuild.Wait' | translate }}</span></button><span *ngIf="this.updateCard"
          class="loaderClass">
          <loader-dots class="loaderAlign"></loader-dots>
        </span>
      </div>

    </div>
  </ng-template>
  <ng-template #addCard let-modal>
    <div class="modal-header">
      <h5 class="modal-title" id="myModalLabel">
        <span *ngIf="this.Mode === 'Add'"> {{ 'fuild.Create' | translate }}</span> <span *ngIf="this.Mode === 'edit'">{{ 'fuild.Edit' | translate }}</span> {{ 'fuild.Card' | translate }} 
      </h5>
      <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
    </div>
    <div class="text-center" style="margin-top: 10px !important;font-size:10px !important;">

    </div>
    <div *ngIf="this.updateCard" class="approval_request_diaglog_bg_clickhandler" (click)="$event.stopPropagation();">
    </div>
    <div class="modal-body" style=" text-align: left !important;">
      <div style="margin-left:20px !important;">
        <div *ngIf="this.Mode==='Add' && this.adminPanelService.giftCardsEnabled" class="checkbox-container1" style="padding-left:0px !important;">
          <div class="row">
            <div class="col-4" style="padding-top:10px;max-width: 200px;
            word-wrap: break-word;">
              <span class="switchLabel" style="white-space: unset !important;">{{'fuild.Issueasgiftcard' | translate}}:</span>
            </div>
            <div class="col-4" id="switchForDeparment" [ngStyle]="{'padding-left': this.languageSelected !=='es' ? '8px':'55px'}" style="padding-top:3px !important;text-align: left;">
              <ui-switch color="gray" [(ngModel)]="addGiftCard"
                [checked]="addGiftCard" (change)="changeGiftToggle($event)" checkedLabel="{{ 'fuild.Yes' | translate }}"
                uncheckedLabel="{{ 'fuild.No' | translate }}"></ui-switch>

            </div>
          </div>
        </div>
        <form [formGroup]="addCardForm" style="margin-bottom: 30px;">
          <div style="padding-left:0px;">
            <label class="showLabel">{{ 'fuild.Issuecardto' | translate }} *</label>
            <div  *ngIf="!this.addGiftCard" class="" style="margin-bottom:0px;margin-top:5px;display:block !important;">
              <div id="cardIssuelistDiv">
                <div class="input2" style="padding-right: 40px;bottom: 5px;width: auto !important;"
                  [ngStyle]="{'border-color':this.Mode==='edit' ? '#E7E6E4':this.searchService.darkBgColor}">
                  <ng-select #policylist appendTo="#cardIssuelistDiv" style="bottom: 5px;height: 34px;" (click)="openDropdown()"
                    (close)="closeDropdown1()" dropdownPosition="middle" [searchable]="true" [clearable]="false"
                    formControlName="username" (change)="showEmployeeTypeChanged($event)" [items]="appproverList"
                    bindLabel="" bindValue="email" [searchFn]="searchByApproverNameAndEmailChanged">
                    <ng-template *ngIf="!this.dropDownopen1" ng-label-tmp let-item="item">
                      <span> {{item.fullName | translate}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                      <span
                        style="text-overflow: ellipsis;display: inline-block !important;
                                      white-space: nowrap;overflow: hidden;padding-right:20px;">{{item.fullName | translate}}</span>
                    </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg *ngIf="this.Mode==='Add'" class="down-arrow" (click)="policylist.toggle()"
                  style="top: -22px;position:relative;float:right;right:20px;left:-10px;" width="15" height="9"
                  viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
                <svg *ngIf="this.Mode==='edit'" class="down-arrow" (click)="policylist.toggle()"
                  style="top: -22px;position:relative;float:right;right:20px;left:-10px;" width="15" height="9"
                  viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="gray" />
                </svg>
              </div>
              <div
                *ngIf="addCardForm.controls['username'].hasError('required') && (addCardForm.controls['username'].touched || addCardForm.controls['username'].dirty)"
                class="error">{{'login.thisfieldisrequired' | translate}}</div>
            </div>
            <div  *ngIf="this.addGiftCard" class="" style="margin-bottom:0px;margin-top:5px;display:block !important;">
              <div id="policylistDiv">
                <div class="input2" style="padding-right: 40px;bottom: 5px;width: auto !important;"
                  [ngStyle]="{'border-color':this.Mode==='edit' ? '#E7E6E4':this.searchService.darkBgColor}">
                  <ng-select #policylist appendTo="#policylistDiv" style="bottom: 5px;height: 34px;" (click)="openDropdown()"
                    (close)="closeDropdown1()" dropdownPosition="middle" [searchable]="true" [clearable]="false"
                    formControlName="username" (change)="showEmployeeTypeChanged($event)" [items]="appproverList"
                    bindLabel="" bindValue="email" [searchFn]="searchByApproverNameAndEmailChanged">
                    <ng-template *ngIf="!this.dropDownopen1" ng-label-tmp let-item="item">
                      <span> {{item.fullName | translate}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                      <span
                        style="text-overflow: ellipsis;display: inline-block !important;
                                      white-space: nowrap;overflow: hidden;padding-right:20px;">{{item.fullName | translate}}</span>
                    </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg *ngIf="this.Mode==='Add'" class="down-arrow" (click)="policylist.toggle()"
                  style="top: -22px;position:relative;float:right;right:20px;left:-10px;" width="15" height="9"
                  viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
                <svg *ngIf="this.Mode==='edit'" class="down-arrow" (click)="policylist.toggle()"
                  style="top: -22px;position:relative;float:right;right:20px;left:-10px;" width="15" height="9"
                  viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="gray" />
                </svg>
              </div>
              <div
                *ngIf="addCardForm.controls['username'].hasError('required') && (addCardForm.controls['username'].touched || addCardForm.controls['username'].dirty)"
                class="error">{{'login.thisfieldisrequired' | translate}}</div>
            </div>
            <label *ngIf="!this.addGiftCard" class="showLabel">{{ 'fuild.Carddescription' | translate }} ({{getRemainingCharacters()}} {{ 'fuild.charactersremaining' | translate }}) *</label>
            <div *ngIf="!this.addGiftCard" class="" style="margin-bottom:15px;margin-top:5px;height:32px;display:block !important;">
              <input type="text" class="input3" formControlName="cardDescription" maxlength="40" required
                style="width: 100% !important;">
              <div
                *ngIf="addCardForm.controls['cardDescription'].hasError('required') && (addCardForm.controls['cardDescription'].touched || addCardForm.controls['cardDescription'].dirty)"
                class="error">{{'login.thisfieldisrequired' | translate}}</div>
              <div *ngIf="addCardForm.controls['cardDescription'].hasError('maxLengthArray')" class="error">{{ 'fuild.Maxmium40lettersrequiredforCardDescription.' | translate }}
              </div>
            </div>
            <label *ngIf="this.expenseCategory && this.expenseCategory.length >0" class="showLabel">{{ 'fuild.Expensecategory' | translate }}</label>
            <div *ngIf="this.expenseCategory && this.expenseCategory.length >0" class=""
              style="margin-bottom:0px;margin-top:5px;display:block !important;">
              <div id="expenselistDiv1">
                <div class="input2" style="padding-right: 40px;bottom: 5px;width: auto !important;"
                  [ngStyle]="{'border-color':false ? '#E7E6E4':this.searchService.darkBgColor}">
                  <ng-select #expenselist appendTo="#expenselistDiv1" style="bottom: 5px;height: 34px;" (click)="openDropdown()"
                    (close)="closeDropdown1()" dropdownPosition="middle" [searchable]="true" placeholder="{{ 'employee.Select' | translate}}"
                    [clearable]="true" formControlName="expense_category" (change)="showExpenseTypeChangedFromModal($event)"
                    [items]="this.expenseCategory" bindLabel="" bindValue="categoryId"
                    [searchFn]="this.searchByExpenseName">
                    <ng-template *ngIf="!this.dropDownopen1" ng-label-tmp let-item="item">
                      <span> {{item.qualifiedName}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                      <span style="padding-right:20px;">{{item.name}}</span>
                    </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg class="down-arrow" (click)="expenselist.toggle()"
                  style="top: -22px;position:relative;float:right;right:20px;left:-10px;" width="15" height="9"
                  viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>
              </div>
            </div>
            <div *ngIf="this.GuestUserSelect"class="" style=" margin-bottom:15px;margin-top:5px;display: flex;">
             
              <div class="col-6" style="padding-left: 0px;">
                <label class="showLabel" style="margin-bottom: 5px;">{{ 'fuild.firstname' | translate }} </label> 
                <div>
                <input type="text" class="input1First" formControlName="firstname"  
                >
                <div
                *ngIf="addCardForm.controls['firstname'].hasError('pattern') && (addCardForm.controls['firstname'].touched ||addCardForm.controls['firstname'].dirty)"
                class="error">{{'personal.PleaseenteravalidfirstnameNumericorspecialcharactersarenotallowed' |
                translate}}
              </div>
                <div
                *ngIf="addCardForm.controls['firstname'].hasError('required') && (addCardForm.controls['firstname'].touched || addCardForm.controls['firstname'].dirty)"
                class="error">{{'login.thisfieldisrequired' | translate}}</div>
                </div>
                </div>
                
                <div class="col-6" style="padding-left: 0px;">
                  <label class="showLabel" style="margin-bottom: 5px;">{{ 'fuild.lastname' | translate }} </label>
                  <div>
                  <input type="text" class="input1First" formControlName="lastname"  
                >
                <div
                *ngIf="addCardForm.controls['lastname'].hasError('pattern') && (addCardForm.controls['lastname'].touched ||addCardForm.controls['lastname'].dirty)"
                class="error">{{'personal.PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed' |
                translate}}
              </div>
                <div
                *ngIf="addCardForm.controls['lastname'].hasError('required') && (addCardForm.controls['lastname'].touched || addCardForm.controls['lastname'].dirty)"
                class="error">{{'login.thisfieldisrequired' | translate}}</div>
                </div>
                </div>
                </div>
            <label *ngIf="this.GuestUserSelect"class="showLabel">{{ 'fuild.email' | translate }}</label>
            <div *ngIf="this.GuestUserSelect"class="" style="margin-bottom:15px;margin-top:5px;height:32px;display:block !important;">
              <input type="text" class="input3" formControlName="email"  
                style="width: 100% !important;">
                <div
                  *ngIf="addCardForm.controls['email'].hasError('pattern') && (addCardForm.controls['email'].touched || addCardForm.controls['email'].dirty)"
                  class="error">{{'personal.Pleaseenteravalidemail' | translate}}
                </div>
              <div
                *ngIf="addCardForm.controls['email'].hasError('required') && (addCardForm.controls['email'].touched || addCardForm.controls['email'].dirty)"
                class="error">{{'login.thisfieldisrequired' | translate}}</div>
            </div>
              <label *ngIf="this.GuestUserSelect"class="showLabel">{{'profilePersonal.PhoneNumber' | translate}}</label>
             <div *ngIf="this.GuestUserSelect" style=" margin-bottom:15px;margin-top:5px;display: flex;">
              <div class="select-box select-dropdown searchable-dropdown phone-select-box">

                  <ng-select (change)="onCountrySelected($event)" class="input-textfield title"
                      dropdownPosition="bottom" [items]="countries" formControlName="dialCode"
                      bindLabel="name" bindValue="dial_code" [searchFn]="searchByNameOrCode">
                      <ng-template ng-label-tmp let-item="item">
                        <span style="position: relative;top: 10px;">
                          <img class="flag-label"
                              src="assets/flags/{{getCountryCode(getFormControl('dialCode').value, getFormControl('phoneNumber').value)}}.png" />
                          <span>{{getFormControl('dialCode').value}}</span></span>
                      </ng-template>
                      <ng-template ng-option-tmp let-item="item">
                          <span class="country-name" title="{{item.name}}">{{item.name}}</span>
                          <img class="icon-country-flag"
                              src="assets/flags/{{item.code | lowercase}}.png" />
                      </ng-template>
                  </ng-select>
              </div>
              <!-- <div class="flex-fill"> -->
              <div class="phone-textfield">
                  <input type="tel" mask="{{getPhoneNumberMask(getFormControl('dialCode').value)}}"
                      class="input-textfield phoneNumber"
                      placeholder="{{getPhoneNumberPaceHolder(getFormControl('dialCode').value)}}"
                      formControlName="phoneNumber" />
                  <span
                      *ngIf="getFormControl('phoneNumber').hasError('pattern') && (getFormControl('phoneNumber').touched || getFormControl('phoneNumber').dirty)"
                      class="error">{{'profilePersonal.Pleaseenteravalidphonenumber' | translate}}
                  </span>
              </div>
             
</div>

            <label class="showLabel">{{ 'fuild.Amount' | translate }} *</label>
            <div class="" style=" margin-bottom:15px;margin-top:5px;display: flex;">
              <div class="col-4" style="padding-left: 0px;">
                <input type="number" class="input4" formControlName="amount" (input)="inputAmount($event.target.value)">
                <div
                  *ngIf="addCardForm.controls['amount'].hasError('required') && (addCardForm.controls['amount'].touched || addCardForm.controls['amount'].dirty)"
                  class="error">{{'login.thisfieldisrequired' | translate}}</div>
                <div *ngIf="this.showAmountError" class="error">{{ 'fuild.Morethanzerorequired' | translate }}</div>
              </div>
              <div class="col-4" style="margin-top:0px;padding-left: 0px;">
                <div id="cardlistDiv">
                  <div class="input1" [ngStyle]="{'border-color':this.addGiftCard ? '#E7E6E4':this.searchService.darkBgColor}">
                    <ng-select #cardlist appendTo="#cardlistDiv"  style="bottom: 15px;" dropdownPosition="middle"
                      [searchable]="false" [clearable]="false" formControlName="timePeriod" [items]="timeOptions"
                      bindLabel="value" bindValue="id" [readonly]="this.addGiftCard">
                      <ng-template ng-option-tmp let-option="item">
                          <span class="option-title">{{option.name |
                              translate}}</span>
                         
                      </ng-template>
                      <ng-template ng-label-tmp let-item="item">
                          <span> {{item.name | translate}}</span>
                        </ng-template>
                    </ng-select>
                    <div class="select-overlay"></div>
                  </div>
                  <svg class="down-arrow" (click)="cardlist.toggle()"
                    style="top: -23px;position:relative;float:right;right:20px;left:-30px;" width="15" height="9"
                    viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                      fill="#8936F3" />
                  </svg>

                </div>
                <div
                  *ngIf="addCardForm.controls['timePeriod'].hasError('required') && (addCardForm.controls['timePeriod'].touched || addCardForm.controls['timePeriod'].dirty)"
                  class="error">{{'login.thisfieldisrequired' | translate}}</div>
              </div>
            </div>
            <label *ngIf="!this.addGiftCard" class="showLabel">{{ 'fuild.Transactionlimit' | translate }} <span style="color: gray;">({{ 'fuild.optional' | translate }})</span></label>
            <div *ngIf="!this.addGiftCard" class="" style="margin-bottom:25px;display:block !important;margin-top:5px;height:32px;">
              <input type="number" class="input4" formControlName="limit" (input)="inputLimit($event.target.value)">
              <div
                *ngIf="addCardForm.controls['limit'].hasError('required') && (addCardForm.controls['limit'].touched || addCardForm.controls['limit'].dirty)"
                class="error">{{'login.thisfieldisrequired' | translate}}</div>
              <div *ngIf="this.showLimitError" class="error">{{ 'fuild.Morethanzerorequired' | translate }}</div>
            </div>
          </div>
          <div *ngIf="!this.addGiftCard" style="background: #f3f3f3;padding-left:10px;margin-left: -10px;">
            <label class="showLabel">{{ 'fuild.Merchantrestrictions' | translate }} <span style="color: gray;">({{ 'fuild.optional' | translate }})</span></label>
            <div class="" style="margin-bottom:0px;margin-top:5px;width: 353px;display: flex;">
              <div id="selectApproverDiv">
                <div class="input2" style="overflow: hidden;">
                  <ng-select #approverlist appendTo="#selectApproverDiv" style="bottom: 5px;"
                    (click)="this.dropDownopen =true;filterSelectedlist(this.multipleSelectedmerchant);"
                    dropdownPosition="middle" [searchable]="true" (close)="closeDropdown()" [closeOnSelect]="false"
                    [clearable]="false" [items]="merchandiseOptions" [searchFn]="searchByMerchantValueAndIdChanged"
                    [(ngModel)]="search" [ngModelOptions]="{standalone: true}">
                    <ng-template ng-label-tmp let-item="item">
                      <span> {{getLabelValue() | translate}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                      <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="{{item.id}}"
                        style="height: 36px !important;min-width: 331px !important;margin-bottom: 0px !important;">
                        <input type="checkbox" id="{{item.id}}" class="mdl-checkbox__input"
                          (change)="onApproverChangeClicked(item.id, $event)" [checked]="isApproverChecked(item.id)">
                        <span class="mdl-checkbox__label"
                          style="font-family: var(--globalFontfamilyr) !important;padding-left: 10px !important;font-size: 14px;top: -2px;padding-bottom: 20px;"><span
                            style="display: inline-block;overflow: hidden;position: relative;
                                        top: 8px;">{{item.value}}</span>
                        </span>
                      </label>

                    </ng-template>
                  </ng-select>
                  <div class="select-overlay"></div>
                </div>
                <svg class="down-arrow"
                  (click)="this.dropDownopen =true;filterSelectedlist(this.multipleSelectedmerchant);approverlist.toggle()"
                  style="top: -22px;position:relative;float:right;right:20px;left:-10px;" width="15" height="9"
                  viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                    fill="#8936F3" />
                </svg>

              </div>
              <span *ngIf="this.multipleSelectedmerchant.length > 0" class="addlue"
                style="margin-top: 5px;margin-left: 10px;cursor: pointer;font-family: var(--globalFontfamilyr);font-weight: bold;;"
                (click)="clearMerchandiseSelectedOption()"> {{ 'fuild.Clear' | translate }}</span>
            </div>
          </div>
          <div *ngIf="!this.addGiftCard && this.multipleSelectedmerchant.length > 0 && !this.dropDownopen" class="modalApprover">
            <span *ngFor="let item1 of this.multipleSelectedmerchant;let i=index">
              <div class="shown1">{{getValueOfmerchant(item1)}}</div>
            </span>
          </div>
        </form>

        <div *ngIf="this.Mode==='edit' && revealLoader1 && !this.addGiftCard" style="text-align: center;">
          <loader-dots class="loaderAlign"></loader-dots>
        </div>
        <div *ngIf="this.Mode==='edit' && !this.addGiftCard " class="row">
          <iframe class="iframeDiv" [src]="this.iframeUrl" scrolling="no"
            (load)="this.ng_cc_reveal_ui_frame_loaded($event)"></iframe>
        </div>
      </div>
    </div>
    <div class="modal-form-button" style="padding: 0px 25px 47px 35px">
      <div class="text-left" style="margin-top: 40px;">
        <button *ngIf="!this.updateCard" class="btn btn-secondary" (click)="updateCardList()"><span class="add"><span
              *ngIf="this.Mode === 'Add'">{{ 'fuild.Create' | translate }}</span> <span *ngIf="this.Mode === 'edit'">{{ 'fuild.Update' | translate }}</span>
           {{ 'fuild.Card' | translate }}</span></button>
        <button *ngIf="this.updateCard" class="btn btn-secondary" [disabled]="this.updateCard"><span
            class="add">{{ 'fuild.Wait' | translate }}</span></button><span *ngIf="this.updateCard" class="loaderClass">
          <loader-dots class="loaderAlign"></loader-dots>
        </span>
      </div>

    </div>
  </ng-template>
  <ng-template #cancelCardModal let-modal>
    <div class="modal-header">
      <h5 class="modal-title" id="myModalLabel">
        {{ 'fuild.CancelCard' | translate }}
      </h5>
      <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
        <i class="material-icons" style="color:#fff;">close</i>
      </button>
    </div>
    <div *ngIf="this.deleteButton" class="approval_request_diaglog_bg_clickhandler" (click)="$event.stopPropagation();">
    </div>
    <div class="modal-body" style=" text-align: left !important;padding-top: 20px;">
      <span class="heading" style="margin-top:30px;">{{ 'fuild.Cancelcardfor' | translate }} "{{this.cardDescription}}" ?</span>
      <div class="heading1">{{ 'fuild.Acancelledcardwillrejectfuturetransactionsandcannotbereactivated' | translate }}</div>
      <div class="heading1">{{ 'fuild.Pleaseselectreasonforcancelingcard' | translate }}:</div>
      <div class="row">
        <div class="col">
          <div class="toggle-button" [ngClass]="{'selected': isSelectedReason('')}" (click)="setReason('')">{{ 'fuild.None' | translate }}</div>
        </div>
        <div class="col">
          <div class="toggle-button" [ngClass]="{'selected': isSelectedReason('lost')}" (click)="setReason('lost')">{{ 'fuild.Lost' | translate }}
          </div>
        </div>
        <div class="col">
          <div class="toggle-button" [ngClass]="{'selected': isSelectedReason('stolen')}" (click)="setReason('stolen')">
            {{ 'fuild.Stolen' | translate }}</div>
        </div>
      </div>

      <div class="modal-form-button" style="text-align: center;margin-top:30px;">
        <button *ngIf="!this.deleteButton" class="btn btn-secondary" (click)=cancelCard()><span class="add"> {{ 'fuild.CANCELCARD' | translate }}</span></button>
        <button *ngIf="this.deleteButton" class="btn btn-secondary" [disabled]="this.deleteButton"><span class="add"
            (click)=deleteEmployee()> {{ 'fuild.Wait' | translate }}</span></button>
        <button class="btn btn-normal" (click)="onModelCancel()"><span class="add1"> {{ 'fuild.NEVERMIND' | translate }}</span></button>
      </div>
    </div>
  </ng-template>
  <ng-template #uploadBankModal let-modal>
      <div class="modal-header">
        <h5 class="modal-title">
            {{'setting.BankAccounts' | translate}}
        </h5>
        <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
          <i class="material-icons" style="color:#fff;">close</i>
        </button>
      </div>
    
      <div class="modal-body" style=" text-align: left !important;">
          <div *ngIf="this.uploadedBank && this.uploadedBank.length > 0">
         <div style="font-family: var(--globalFontfamilyr);font-weight: bold;"> {{'setting.ConnectedBankAccounts' | translate}}</div>
         <div *ngFor="let item of this.uploadedBank">
          <span>  {{item.accountName}} </span> - <span>  {{item.bankName}} </span> <span> - {{item.accountNumberLast4}}</span>
           </div>
           </div>
        <div class="modal-form-button" style="margin-bottom: 0px;margin-top: 20px;text-align: center;">
<button class="btn btn-secondary" (click)="bankUploading()"><span class="add"> {{ 'setting.ConnectBank' | translate }}</span> <loader-dots *ngIf="bankLodershow" class="loaderAlign">
            </loader-dots></button>
        </div>
     
      </div>
    </ng-template>