import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FuildPayComponent } from './fuild-pay.component';

describe('FuildPayComponent', () => {
  let component: FuildPayComponent;
  let fixture: ComponentFixture<FuildPayComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [FuildPayComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FuildPayComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
