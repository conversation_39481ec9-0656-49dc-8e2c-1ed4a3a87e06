import { Component, OnInit, HostListener, ViewChild, ElementRef } from '@angular/core';
import { Subscription, throwError } from 'rxjs';
import { AdminPanelService, Department, CompanySettings } from '../admin-panel.service';
import { CommonUtils } from '../util/common-utils';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { UserAccountService } from '../user-account.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { UntypedFormGroup, UntypedFormBuilder, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Constants } from '../util/constants';
import { Router, ActivatedRoute } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { catchError, findIndex } from 'rxjs/operators';
//import { environment } from 'src/environments/environment.prod';
import { environment } from 'src/environments/environment';
import { SearchService } from '../search.service';
import { DateUtils } from '../util/date-utils';
import { DatePipe } from '@angular/common';
import { DeviceDetailsService } from '../device-details.service';
import { TranslateService } from '@ngx-translate/core';
import { countries } from '../util/countries';
import { PhoneNumberDTO } from '../entity/phonenumber-dto';
import { UserCredentialsService } from '../user-credentials.service';
import { UserAccountInfo } from '../entity/user-account-info';
declare var cc_ui_host: any;
declare var cc_ui_base_ctx: any;
declare var convertDataToJSON1: any;
declare var intiatePlaid: any;
declare var accountSuccesfullyMsg: any;
declare var $: any;
@Component({
    selector: 'app-fuild-pay',
    templateUrl: './fuild-pay.component.html',
    styleUrls: ['./fuild-pay.component.scss'],
    standalone: false
})
export class FuildPayComponent implements OnInit {
  changeStyle() {
    if (!this.isMobile1) {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '730px' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '99vw' }
      }
    }
  }
  changeStyle1() {
    if (!this.isMobile1) {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '335px', 'overflow': 'hidden' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '700px', 'overflow': 'hidden' }
      }
    }
  }
  cardNameSearchValue = '';
  cardNameSearchValueForTransaction = '';
  bsModalRef: BsModalRef;
  bsModalReforMemo: BsModalRef;
  bsModalReforTransaction: BsModalRef;
  iframeUrl: any;
 
  dateOptions1 = Constants.DATE_OPTIONS;
  dateValue1 = 'CURRMONTH';
  showDelineTransaction = false;
  request_id = '';
  Mode = '';
  paymentOptionsSubscription: Subscription;
  fetchAccountInfoSubscription: Subscription;
  viewMode = 'tab1';
  fileTypeValue = '';
  deleteButton = false;
  receiptValue = 'All';
  fileOptions = [{ id: 'QuickBooksOnline', value: 'QuickBooks Online' }, { id: 'Csv', value: 'CSV' }];
  receiptOption = [{ id: 'All', value: 'Receipts',name:"fuild.Receipts" }, { id: 'With Receipt', value: 'With Receipt',name:"fuild.WithReceipt" }, { id: 'With Out Receipt', value: 'Without Receipt',name:"fuild.WithoutReceipt" }]
  onboardlink = '';
  cardsOptions = [];
  currentBalance = 0;
  statementBalance = 0;
  lastPayment = 0;
  lastDate: Date;
  onboardMsg = '';
  dropDownopen1 = false;
  transactionPendingTab =false;
  qboEnabled = false;
  revealLoader = false;
  showNotCompleteScreen = false;
  rowSelectedForDownload = [];
  showFluidPayMsg = false;
  revealLoader1 = true;
  verficatioCodeSennd = false;
  selectedCard = '';
  selectRequest = '';
  cancelReason = '';
  cardDescription = '';
  selectedCardNumber = '';
  transactionComplted ='closed'
  transactionProccessedTab = false;
  transactionTab = "tab55"
  quickBookUrl: any;
  creditEnabled = false;
  editCardDetails: any;
  companySettingsSubscription: Subscription;
  listEmployeesResponseSubscription: Subscription;
  disableDateType = false;
  dropDownopen = false;
  companySettings: CompanySettings;
  addCardForm: UntypedFormGroup;
  addGiftForm: UntypedFormGroup;
  transferFundsForm: UntypedFormGroup;
  cardList = [];
  startDate: Date = new Date();
  endDate: Date = new Date();
  minimumDate: Date = new Date();
  maximumDate1: Date = new Date();
  updateCard = false;
  revealCard = false;
  bankLodershow = false;
  showQucikBookButtonWithCompanyName = false;
  originalResponse = [];
  originalTransactionReport = [];
  transactionReport = [];
  appproverList = [];
  viewMode1 = 'tab11';
  deptValue = 'All Departments';
  statusValue = 'All status';
  onboardLevel = '';
  departmentOptions = [];
  responseNotCame = false;
  CardRequested = false;
  merchantChange = false;
  balanceapiCame = false;
  cardValue = "";
  showAmountError = false;
  stopPollinng = false;
  showQuickBookButton = false;
  disbaledQBoButton = false
  showLimitError = false;
  availableBalance = 0;
  index: number;
  expenseCategory: Array<any> = [];
  expenseCategoryTree = {};
  bankAccounts: Array<any> = [];
  companyName = '';
  statusOptions = [];
  cardNumber = [];
  isMobile1: boolean;
  deviceSubscription1: Subscription;
  multipleSelectedmerchant = [];
  ng_cc_reveal_ui_frame_loaded_count = 0;
  timeOptions = [
    { value: 'Daily', id: 'daily', name: 'fuild.Daily' },
    { value: 'Weekly', id: 'weekly', name: 'fuild.Weekly' },
    { value: 'Monthly', id: 'monthly', name: 'fuild.Monthly' },
    { value: 'Yearly', id: 'yearly', name: 'fuild.Yearly' },
    { value: 'All Time', id: 'all_time', name: 'fuild.AllTime' }
  ];
  merchandiseOptions = Constants.Merchant_Restriction;
  daterangepickerModel = [this.startDate, this.endDate];
  originalmerchandiseOptions = this.merchandiseOptions;
  queryParamSubscription: Subscription;
  @ViewChild('trasactionDetailModal') modal1: any;
  constructor(private fb: UntypedFormBuilder,
    private modalService: BsModalService,
    public router: Router,
    public searchService: SearchService,
    public translateService: TranslateService,
    public deviceDetailsService: DeviceDetailsService,
    private sanitizer: DomSanitizer,
    private toastr: ToastrService,
    private activatedRoute: ActivatedRoute,
    private userCredentials: UserCredentialsService, 
    private userAccountService: UserAccountService,
    public ngxSmartModalService: NgxSmartModalService,
    private adminPanelService: AdminPanelService,) { }
  loadeToastr(item, thisObj,data) {
    thisObj.bankLodershow = false;
    if (item) {
      thisObj.toastr.success(thisObj.translateService.instant("fuild.Accountlinkedsuccessfully"));
      thisObj.getCardDetails();
      thisObj.getPaymentOptions();
    } else if(data && data !=='') {
      thisObj.toastr.error(thisObj.translateService.instant(data));
    }
  }
  userid = ''; 
  userAccountInfoObj: UserAccountInfo;
  languageSelected='en';
  myPlaceholder =this.translateService.instant("fuild.Export");
  resultErrorMsg = this.translateService.instant("fuild.Fetchingdata");
  resultErrorMsg1 = this.translateService.instant("fuild.Fetchingdata");
  search = this.translateService.instant("employee.Select");
  ngOnInit(): void {
    this.deviceSubscription1 = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
    this.responseNotCame = true;
    this.queryParamSubscription = this.activatedRoute.queryParams.subscribe(params => {
      if (params && params['subType']) {
        if (!this.bsModalReforTransaction) {
          this.getTransactionDetails(params['subType']);
        }
      }
    });
    this.fetchAccountInfoSubscription = this.userAccountService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      this.userAccountInfoObj = userAccountInfoObj;
     
      this.getPaymentOptions();
      
    });
    this.userid = this.userAccountService.getUserEmail();
     this.languageSelected = this.userCredentials.getLang();
    const getcardRequested = localStorage.getItem(this.userid + '-cardRequested');
    if (getcardRequested === 'yes') {
      this.CardRequested = true;
    }
    accountSuccesfullyMsg(this.loadeToastr, this);
    let self = this;
    //if(this.onboardMsg === 'Complete Onboarding'){
    document.addEventListener("visibilitychange", function () {
      if (document.visibilityState === 'visible' && self.onboardMsg === self.translateService.instant('fuild.CompleteOnboarding')) {
        self.getCardDetails();
      }
    });
    // }

    setTimeout(() => {
      this.subscription();
    }, 500);
  }
  getItemPeriodLabel(id){
    let timeOptionName;
    if(id!==''){
    this.timeOptions.map(item => {
      if (item.id.toLowerCase() == id.toLowerCase()) {
        timeOptionName = item.value;
        // return;
      }
    });
  }

    return timeOptionName;
  }
  getTransactionDetails(referenceId) {
    this.adminPanelService.openTransactiondetailPopup(referenceId).subscribe(resp => {
      if (resp) {
        // 
        let response = convertDataToJSON1(resp, ';');
        let response1 = response.slice(1);
        let transDetail = []
        if (response1 && response1.length > 0) {
          for (let item of response1) {
            let cardItem = {};
            cardItem['Merchant'] = item['Merchant'];
            //this.availableBalance = this.availableBalance + (item.available_balance) ;
            cardItem['name'] = item['Name']
            cardItem['Department'] = item['Department']
            cardItem['description'] = item['Card description']
            cardItem['CardNumber'] = item['Card number']
            cardItem['Status'] = item['Status']
            cardItem['Amount'] = item['Amount']
            cardItem['decline'] = item['Reason for decline']
            cardItem['category'] = item['Merchant category']
            cardItem['Expense'] = (item['Expense Category'] !== "") ? item['Expense Category'] : null;
            cardItem['date'] = item['ds+Transaction date'];
            cardItem['qboAccess'] = item['Sync Status'];
            cardItem['ExpenseValue'] = item['Expense Category Name'];
            cardItem['receiptMsg'] = item['Receipt'] !== "" ? item['Receipt'] : null;
            cardItem['memoMsg'] = item['Memo'] !== "" ? item['Memo'] : null;
            cardItem['authId'] = item['Auth ID'];
            cardItem['referenceId'] = item['Reference Id'];
            cardItem['syncError'] = item['Sync Error'];
            transDetail.push(cardItem);
          }
          this.transactionSelected = transDetail;
          this.bsModalReforTransaction = this.modalService.show(this.modal1, {
            initialState: {
            }, backdrop: true, ignoreBackdropClick: true, keyboard: false,
          });;
        }
      }
    })

  }
  onSmartModelCancel(modelName: string) {
    this.ngxSmartModalService.getModal(modelName).close();
  }
  onModelCancel() {
    this.bsModalRef.hide();
    if (this.revealCard) {
      this.revealCard = false;
    }
  }
  
  setStartDate(date) {
    if (date) {
      // this.daterangepickerModel =date;
      this.startDate = date[0];
      this.endDate = date[1];
    }
    //this.firstDate = date;
    if (this.startDate > this.endDate) {
      this.endDate = date;
      this.lastDate = date;
    }
    this.minimumDate = this.startDate;

  }
  openDropdown() {
    if (this.Mode === 'Add') {
      this.dropDownopen1 = true;
    }
  }
  ngOnDestroy() {
    document.removeEventListener("visibilitychange", function () {

    });
    if (this.queryParamSubscription) {
      this.queryParamSubscription.unsubscribe();
    }
    setTimeout(() => {
      this.myPlaceholder = this.translateService.instant("fuild.Export");
      this.fileTypeValue = null;
    }, 10);
    this.cardNameSearchValueForTransaction = '';
    this.receiptValue = 'All';
    this.transactionProccessedTab = true;
    this.adminPanelService.selectedCard='';
    this.cardValue = "";
    this.transactionTab='tab55';
    this.transactionComplted ='closed'
    this.showDelineTransaction = false;
  }
  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
    this.ngxSmartModalService.close('daterangeSelection1');
    setTimeout(() => {
      this.getTransactionReport();
    }, 100);
  }
  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    const dayHoverHandler = event.dayHoverHandler;
    this.dateValue1 = "CUSTOMDATE";
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        this.searchService.hoverCounter++;
        if (this.searchService.hoverCounter > 1) {
          (picker as any)._datepickerRef.instance.daySelectHandler(cell);
        }
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }
  onSelectTransactionRow(option, event) {
    if (event) {
      if (option == "All Transaction") {
        //  this.rowSelectedForDownload = this.transactionReport;
        for (let item of this.transactionReport) {
          this.rowSelectedForDownload.push(item.authId);
        }
      }
      else {
        this.rowSelectedForDownload = this.rowSelectedForDownload.filter(trans => {
          if (trans !== 'All Transaction') return true;
        });
        //  let slectRow  = this.transactionReport.findIndex(item => item['Card number'] = option['Card number'])
        this.rowSelectedForDownload.push(option);
      }
    } else if (!event && option == "All Transaction") {
      this.rowSelectedForDownload = [];
    } else {
      this.rowSelectedForDownload = this.rowSelectedForDownload.filter(trans => {
        if (trans !== option && trans !== 'All Transaction') return true;
      });
    }
    this.isTransChecked(option);
  }
  isTransChecked(option) {
    return this.rowSelectedForDownload.indexOf(option) > -1;
  }
  getRemainingCharacters() {
    this.addCardForm.get('cardDescription').valueChanges.subscribe(value => {
      if (value && value.length !== 40) {
        return (40 - value.length);
      }
    }
    );
    if (this.addCardForm.get('cardDescription').value.length === 0) {
      return 40;
    } else if (this.addCardForm.get('cardDescription').value.length <= 40) {
      return (40 - this.addCardForm.get('cardDescription').value.length);
    }
  }
  getLabelValue() {
    if (this.dropDownopen) {
      return 'fuild.Typetosearch';
    } else if (!this.dropDownopen) {
      return 'employee.Select';
    }
  }
  chunkSubstr(str, size) {
    const numChunks = Math.ceil(str.length / size)
    const chunks = new Array(numChunks)

    for (let i = 0, o = 0; i < numChunks; ++i, o += size) {
      chunks[i] = str.substr(o, size)
    }

    return chunks
  }
  getQuarter1(date) {
    var m = 0;
    var n = ((date.getMonth() / 3));
    var decimal = ((date.getMonth() % 3))
    if (n >= 0 && n < 1) {
      m = 1;
    } else if (n >= 1 && n < 2) {
      m = 2;
    } else if (n >= 2 && n < 3) {
      m = 3;
    } else if (n >= 3 && n < 4) {
      m = 4;
    }
    var quarter;
    if (m > 4) {
      quarter = (m - 4);
    } else {
      quarter = m;
    }
    if (m && m === 1) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 0, 1);
    } else if (m && m === 2) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 3, 1);
    }
    else if (m && m === 3) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 6, 1);
    }
    else if (m && m === 4) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 9, 1);
    }
  }
  bankUploading() {
    const userid = this.userAccountService.getUserEmail();
    const stoken = this.userAccountService.getSToken();
    this.bankLodershow = true;
    // this.bankLodershow =true;
    intiatePlaid(environment.plaidInitUrl, environment.plaidIssuingUrl, userid, environment.plaidEnvironment,stoken)
  }
  uploadBank(modal) {
    this.bsModalRef = this.modalService.show(modal);
  }
  changeTrannsaction(event) {
    this.showDelineTransaction = event;
    this.rowSelectedForDownload = [];
    this.filterTransactionnData();
  }
  getSelectedCurrentDate1(id: string) {
    this.dateValue1 = id;
    if (id === 'CURRMONTH') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(1);
      this.startDate.setMonth(this.startDate.getMonth());
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'TODAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    }else if (id === 'YESTERDAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 1);
      this.endDate.setDate(this.endDate.getDate() - 1);
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    }else if (id === 'Currentquarter') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.getQuarter1(this.startDate);
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'Currentyear') {
      this.endDate = new Date();
      this.startDate = new Date();
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 0, 1);
      this.daterangepickerModel = [this.startDate, this.endDate];
    }
    else if (id === 'LAST7DAYS') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 6);
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'LAST30DAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 29);
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'LAST90DAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 89);
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'LASTMONTH') {
      this.startDate = new Date();
      this.startDate.setDate(1);
      this.startDate.setMonth(this.startDate.getMonth() - 1);
      this.endDate = new Date(this.startDate.getFullYear(), this.startDate.getMonth() + 1, 0);
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'CUSTOMDATE') {
      this.daterangepickerModel = [this.startDate, this.endDate];
    }
    if (this.ngxSmartModalService.getOpenedModals() &&
      (this.ngxSmartModalService.getOpenedModals().length > 0)
    ) {
      let modals = this.ngxSmartModalService.getOpenedModals();
      for (let index = 0; index < modals.length; index++) {
        if (modals[index].id === 'daterangeSelection1') {
          this.ngxSmartModalService.close('daterangeSelection1');
        }
      }
    }
  }
  revealCardData() {
    if (this.cardNumber && this.cardNumber.length > 0) {
      this.revealCard = true;
      return;
    }
    this.revealLoader = true;
    this.adminPanelService.getrevealCard(this.selectedCard).subscribe(resp => {
      if (resp.status === 'success') {
        
        this.editCardDetails = resp.data;
        this.revealLoader = false;
        this.revealCard = true;
        if (this.editCardDetails && this.editCardDetails) {
          this.cardNumber = this.chunkSubstr(this.editCardDetails.number, 4);
        }
      }
    })
  }
  customTabClicked() {
    this.viewMode1 = 'tab11';
  }
  presetsTabClicked() {
    this.viewMode1 = 'tab12';
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  openNgxModal(id, picker) {
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);

    setTimeout(() => {
      this.viewMode1 = 'tab11';
      picker.show();
    }, 200);
  }
  searchByApproverNameAndEmailChanged(term: string, item: any) {
    term = term.toLowerCase();
    return (item.firstName && item.firstName.toLowerCase().indexOf(term) > -1) ||
      (item.lastName && item.lastName.toLowerCase().indexOf(term) > -1) || item.email.toLowerCase().indexOf(term) > -1;
  }
  checkAllParents(term: string, item: any): boolean {
    for (let counter = 0; counter < this.expenseCategory.length; counter++) {
      const curr = this.expenseCategory[counter];
      if (curr.parentId === item.categoryId) {
        if (curr.name && curr.name.toLowerCase().indexOf(term) > -1) {
          return true;
        }
      }
    }
    return false;
  }
  searchByExpenseName = (term: string, item: any) => {
    term = term.toLowerCase();
    let originalItem = item;
    let parentID = item.parentId;
    if (this.checkAllParents(term, item)) {
      return true;
    }
    item = originalItem;
    if (item.name && item.name.toLowerCase().indexOf(term) > -1) {
      return true;
    }
    while (parentID) {
      item = this.expenseCategoryTree[parentID];
      if (item.name && item.name.toLowerCase().indexOf(term) > -1) {
        return true;
      }
      parentID = item.parentId;
    }
  }
  searchByBankName(term: string, item: any) {
    term = term.toLowerCase();
    return (item.accountName && item.accountName.toLowerCase().indexOf(term) > -1) ||
      (item.accountNumberLast4 && item.accountNumberLast4.toLowerCase().indexOf(term) > -1);
  }
  transferFundsModal(modal) {
    this.updateCard = false;
    this.request_id = '';
    this.verficatioCodeSennd = false;
    this.showAmountError = false;
    this.transferFundsForm = this.fb.group({
      bankId: [null, Validators.compose([Validators.required])],
      verificationCode: [],
      amount: [, Validators.compose([Validators.required, this.minAmount(1)])],
    });
    this.bsModalRef = this.modalService.show(modal);
  }
  transferFundsCall() {
    if (this.transferFundsForm.invalid) {
      this.transferFundsForm.controls['bankId'].markAsTouched();
      this.transferFundsForm.controls['verificationCode'].markAsTouched();
      this.transferFundsForm.controls['amount'].markAsTouched();
      return;
    }
    if (this.showAmountError) {
      return;
    }
    this.updateCard = true;
    if (!this.verficatioCodeSennd) {
      this.resendVerificationCode();
    } else if (this.verficatioCodeSennd) {
      let bankDetails: any = {};
      bankDetails['bank_account_id'] = this.transferFundsForm.controls['bankId'].value;
      bankDetails['verification_code'] = this.transferFundsForm.controls['verificationCode'].value;
      bankDetails['amount'] = (this.transferFundsForm.controls['amount'].value * 100);
      bankDetails['request_id'] = this.request_id;
      this.adminPanelService.getTransferFunds(bankDetails).subscribe(resp => {
        if (resp && resp.status === 'success') {
          this.issuinngBalance();
          this.updateCard = false;
          this.toastr.success(this.translateService.instant("fuild.Fundstransferredsuccessfully"));
          this.transferFundsForm.controls['verificationCode'].clearValidators();
          this.transferFundsForm.controls['verificationCode'].updateValueAndValidity();
          this.bsModalRef.hide();
        } else {
          if (resp.errorMessage && resp.errorMessage.length > 0) {
            this.toastr.error(resp.errorMessage[0]);
          } else {
            this.toastr.error(this.translateService.instant("fuild.Unabletoverifyapprovalcode.Pleasetryagainlate"));
          }
          this.updateCard = false;
          // this.bsModalRef.hide();
        }
      })
    }
  }
  minAmount(min: number) {
    return (c: AbstractControl): { [key: string]: any } => {
      if (c.value) {
        // 
        if (c.value >= min)
          return null;

        return { 'minAmount': { valid: false } };
      }
    }
  }
  inputAmount(item) {
    if (item === "") {
      this.showAmountError = false;
    } else if (item <= 0) {
      this.showAmountError = true;
    } else {
      this.showAmountError = false;
    }
  }
  inputLimit(item) {
    if (item === "") {
      this.showLimitError = false;
    } else if (item <= 0) {
      this.showLimitError = true;
    } else {
      this.showLimitError = false;
    }
  }
  resendVerificationCode() {

    let userid = this.userAccountService.getUserEmail();
    let bankId = this.transferFundsForm.controls['bankId'].value;
    this.adminPanelService.getVerficationCode(bankId).subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.toastr.success(this.translateService.instant("fuild.Verificationcodesentto") + " " + userid + " " + "!!!");
        this.verficatioCodeSennd = true;
        this.request_id = resp.data;
        this.transferFundsForm.controls['verificationCode'].setValidators(Validators.compose([Validators.required]));
        this.transferFundsForm.controls['verificationCode'].updateValueAndValidity();
        this.updateCard = false;
      } else {
        this.updateCard = false;
      }
    })
  }
  addGiftCard =false;
  countries = countries;
  selectedCountryCode='';
  getCountryCode(code, phoneNumber) {
    return CommonUtils.getCountryCode(code, phoneNumber, this.selectedCountryCode);
  }
  onCountrySelected(item) {
    if (item) {
      this.selectedCountryCode = item.code;
    } else {
      this.selectedCountryCode = '';
    }

  }
  getPhoneNumberMask(inputDIalCode) {

    return CommonUtils.getPhoneNumberMask(inputDIalCode);

  }
  getPhoneNumberPaceHolder(inputDIalCode) {
    return CommonUtils.getPhoneNumberPaceHolder(inputDIalCode);
  }
  getFormControl(name: string) {
    return this.addCardForm.controls[name];
  }
  GuestUserSelect =false;
  changeGiftToggle(event){
    if(event){
      this.addCardForm = this.fb.group({
        username: ['', Validators.compose([Validators.required])],
        firstname: [''],
        lastname: [''],
        phoneNumber: [],
         email: [''],
         dialCode:['+1'],
        cardDescription: ['', Validators.compose([Validators.required, this.maxLengthArray(40)])],
        amount: [, Validators.compose([Validators.required])],
        timePeriod: ['monthly'],
        expense_category: [null],
        limit: [],
        merchantRestrictio: [''],
      });
     let guest =  { fullName: 'Guest',firstName: 'Guest',lastName: 'Guest',  email: 'GUEST',Id:1 }
     this.appproverList.unshift(guest);
      this.addCardForm.controls['cardDescription'].setValue('Routespring Gift Card');
      this.addCardForm.controls['timePeriod'].setValue('all_time');
      this.addCardForm.get('timePeriod').disable();
    }else{
      this.addCardForm = this.fb.group({
        username: ['', Validators.compose([Validators.required])],
        firstname: [''],
        lastname: [''],
        phoneNumber: [],
         email: [''],
         dialCode:['+1'],
        cardDescription: ['', Validators.compose([Validators.required, this.maxLengthArray(40)])],
        amount: [, Validators.compose([Validators.required])],
        timePeriod: ['monthly'],
        expense_category: [null],
        limit: [],
        merchantRestrictio: [''],
      });
      this.GuestUserSelect =false;
     
      this.addCardForm.get('timePeriod').enable();
      this.appproverList =  this.appproverList.filter(item => item.email!=='GUEST');
    }
    this.addGiftCard  =event;
  }
  addCardModal(modal, mode, cardId?, modal2?) {
    this.dropDownopen = false;
    this.dropDownopen1 = false;
    this.addGiftCard =false;
    this.GuestUserSelect =false;
    this.showAmountError = false;
    this.showLimitError = false;
    this.Mode = mode;
    if (this.adminPanelService.employeeList && this.adminPanelService.employeeList.length > 0) {
      this.appproverList=[];
      this.appproverList = this.sortList(this.adminPanelService.employeeList);
    }
    //this.adminPanelService.employeePopOpen =true;
    if (mode === 'Add') {
      this.multipleSelectedmerchant = [];
      this.addCardForm = this.fb.group({
        username: ['', Validators.compose([Validators.required])],
        firstname: [''],
        lastname: [''],
        phoneNumber: [],
         email: [''],
         dialCode:['+1'],
        cardDescription: ['', Validators.compose([Validators.required, this.maxLengthArray(40)])],
        amount: [, Validators.compose([Validators.required])],
        timePeriod: ['monthly'],
        expense_category: [null],
        limit: [],
        merchantRestrictio: [''],
      });
      // this.addCardForm.get('timePeriod').enable();
      this.addCardForm.get('username').enable();
    } else {
      this.ngxSmartModalService.getModal(modal2).close();
      this.revealCard = false;
      this.revealLoader1 = true;
      this.cardNumber = [];
      let findIndex = this.originalResponse.findIndex(item => item.issuing_card_id === cardId);
      let cardDetails = this.originalResponse[findIndex];
      this.selectedCard = cardDetails.issuing_card_id;
      this.cardDescription = cardDetails.description;
      this.editCardDetails = cardDetails;
      let userid = this.userAccountService.getUserEmail();
      let sToken = this.userAccountService.getSToken();
      let url = cc_ui_host + '/' + cc_ui_base_ctx + '/cc-view.html?userid=' + userid + "&sToken=" + sToken + '&cardid='
        + this.selectedCard + '&last4=' + cardDetails.last4;
      this.iframeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
      this.addCardForm = this.fb.group({
        username: [cardDetails.email ? cardDetails.email : '', Validators.compose([Validators.required])],
        cardDescription: [cardDetails.description ? cardDetails.description : '', Validators.compose([Validators.required, this.maxLengthArray(40)])],
        amount: [cardDetails.spending_limit ? (cardDetails.spending_limit.amount / 100) : '', Validators.compose([Validators.required])],
        timePeriod: [cardDetails.spending_limit ? cardDetails.spending_limit.interval : ''],
        limit: [cardDetails.transaction_limit ? (cardDetails.transaction_limit / 100) : ''],
        expense_category: [(cardDetails.expense_category && cardDetails.expense_category.categoryId) ? cardDetails.expense_category.categoryId : null],
        merchantRestrictio: [''],
      });
      this.addGiftCard = (cardDetails.cardType === 'gift_card' || 'Routespring Gift Card' === cardDetails.description) ;
      if(this.addGiftCard){
        let guest =  { fullName: cardDetails.name,firstName: cardDetails.name,lastName: cardDetails.name,  email: cardDetails.email,Id:1 }
        this.appproverList.unshift(guest);
      }
      this.addCardForm.get('username').disable();
      this.multipleSelectedmerchant = cardDetails.merchant_restrictions ? cardDetails.merchant_restrictions : [];
    }
    this.bsModalRef = this.modalService.show(modal);
  }
  maxLengthArray(max: number) {
    return (c: AbstractControl): { [key: string]: any } => {
      if (c.value) {
        if (c.value.length <= max)
          return null;

        return { 'maxLengthArray': { valid: false } };
      }
    }
  }
  requestCard() {
    this.bankLodershow = true;
    if (this.onboardMsg === this.translateService.instant('fuild.ConnectBankAccount')) {
      const userid = this.userAccountService.getUserEmail();
      const sToken = this.userAccountService.getSToken();
      // this.bankLodershow =true;
      intiatePlaid(environment.plaidInitUrl, environment.plaidIssuingUrl, userid, environment.plaidEnvironment,sToken)
    } else {
      this.adminPanelService.getRequestCard().subscribe(resp => {
        if (resp.status === 'success') {
          this.bankLodershow = false;
          const userid = this.userAccountService.getUserEmail();
          this.CardRequested = true;
        }
      });
    }
  }
  uploadedBank = [];
  getPaymentOptions() {

  
    this.resultErrorMsg = this.translateService.instant("fuild.Fetchingdata");

    this.paymentOptionsSubscription = this.adminPanelService.getPaymentOptions().subscribe(resp => {
      if (resp.status === 'success') {
        if (this.bsModalRef) {
          this.bsModalRef.hide();
        }
        if (resp && resp.data.bankAccounts) {
          this.uploadedBank = resp.data.bankAccounts;
        }
        
      } else if (resp) {
       // this.applyButton = false;
        if (this.bsModalRef) {
          this.bsModalRef.hide();
        }
        this.resultErrorMsg = this.translateService.instant("fuild.Nodata");
      }
    })
  }
  uuidKey = '';
  initiatePolling() {
    this.disbaledQBoButton = true;
    //this.uuidKey = this.generateUUID()
    setInterval(() => {
      this.stopPollinng = true;
    }, 300000);
    setTimeout(() => {
      this.getIntiateQBO()
    }, 5000);
  }
  initiatePolling1() {
    setTimeout(() => {
      this.getIntiateQBO()
    }, 5000);
  }
  disconnect() {
    this.disbaledQBoButton = true;
    this.adminPanelService.getDisconnnectQuickBooks().subscribe(resp => {
      if (resp.success) {
        this.showQucikBookButtonWithCompanyName = false;
        this.disbaledQBoButton = false;
        this.fileOptions = this.fileOptions.slice(1);
        this.initializeExpenseCategory(null);
        let userid = this.userAccountService.getUserEmail();
        let sToken = this.userAccountService.getSToken();
        this.uuidKey = this.generateUUID();
        let url = environment.apiForQuickBooksInitiate + '?userid=' + userid + '&sToken=' + sToken + '&request-id=' + this.uuidKey;
        
        this.quickBookUrl = url;
      } else {
        this.disbaledQBoButton = false;
        this.showQucikBookButtonWithCompanyName = true;
      }
    })
  }
  private generateUUID(): string {
    var text = "";
    var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    for (var i = 0; i < 5; i++)
      text += possible.charAt(Math.floor(Math.random() * possible.length));

    return text;
  }
  getIntiateQBO() {
    try {
      this.adminPanelService.getQuickBookdetails(this.uuidKey)
        .pipe(catchError(err => {
          return throwError(err);
        }))
        .subscribe((res) => {
          this.processQBO(res);
        }, error => {

        });
    } catch (error) {

    }
  }

  initializeExpenseCategory(expenseCategoryList: Array<any>) {
    this.expenseCategoryTree = {};
    this.expenseCategory = expenseCategoryList;
    if (expenseCategoryList) {
      for (let i = 0; i < expenseCategoryList.length; i++) {
        this.expenseCategoryTree[expenseCategoryList[i].categoryId] = expenseCategoryList[i];
      }
    }
  }

  getFileOptions() {
    return this.fileOptions;
  }
  processQBO(res) {
    if (res.data && res.data.value && res.data.value === 'in_progress' && !this.stopPollinng) {
      this.initiatePolling1();
      return;
    }

    if (res.success) {
      this.showQuickBookButton = true;
      this.disbaledQBoButton = false;
      this.fileOptions = [];
      this.fileOptions = [{ id: 'QuickBooksOnline', value: 'QuickBooks Online' }, { id: 'Csv', value: 'CSV' }];
      this.showQucikBookButtonWithCompanyName = true;
      this.companyName = res.data.qboCompanyName;
      
      if (res.data.categories && res.data.categories.length > 0) {
        this.initializeExpenseCategory(res.data.categories);
      }
    } else {
      let userid = this.userAccountService.getUserEmail();
      this.disbaledQBoButton = false;
      let sToken = this.userAccountService.getSToken();
      this.uuidKey = this.generateUUID();
      let url = environment.apiForQuickBooksInitiate + '?userid=' + userid + '&sToken=' + sToken + '&request-id=' + this.uuidKey;
      
      this.quickBookUrl = url;
      this.showQucikBookButtonWithCompanyName = false;
      this.showQuickBookButton = true;
    }
  }
  currency = 'USD';
  subscription() {
    this.listEmployeesResponseSubscription = this.adminPanelService.employeeListResponseObservable$.subscribe(response => {
      if (response) {
        if (this.adminPanelService.employeeList && this.adminPanelService.employeeList.length > 0) {
          let employeeFilterList = this.adminPanelService.employeeList.filter(item => (item.employeeInfo && item.employeeInfo.role === "ADMIN"));
          this.appproverList = this.sortList(this.adminPanelService.employeeList);
        }

      }
    });
    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {
        this.qboEnabled = settings.company.qboEnabled;
        this.currency = settings.company.currency;
      }
    });
    this.adminPanelService.getQuickBookdetails(this.uuidKey).subscribe(resp => {
      if (resp.success) {
        this.showQuickBookButton = true;
        this.showQucikBookButtonWithCompanyName = true;
       
        this.companyName = resp.data.qboCompanyName;
      } else {
        this.fileOptions = this.fileOptions.slice(1);
        let userid = this.userAccountService.getUserEmail();
        let sToken = this.userAccountService.getSToken();
        this.uuidKey = this.generateUUID();
        let url = environment.apiForQuickBooksInitiate + '?userid=' + userid + '&sToken=' + sToken + '&request-id=' + this.uuidKey;
        
        this.quickBookUrl = url;
        this.showQucikBookButtonWithCompanyName = false;
        this.showQuickBookButton = true;
      }
    })
    this.getSelectedCurrentDate1('CURRMONTH');
    setTimeout(() => {
      this.getTransactionReport();
    }, 10);
    this.uuidKey = this.generateUUID();
    this.adminPanelService.getQuickBookdetails(this.uuidKey).subscribe(resp => {
      if (resp.success) {
        this.showQuickBookButton = true;
        this.showQucikBookButtonWithCompanyName = true;
        if (resp.data.categories && resp.data.categories.length > 0) {
          this.initializeExpenseCategory(resp.data.categories);
        }
        this.companyName = resp.data.qboCompanyName;
      }
    })

    this.issuinngBalance();
    this.getCardDetails();
  }
  transactionresponseNotCame = false;
  previousDate = '';
  showDeclinneSwitch = false;
  getTransactionReport() {
    let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
    let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);

    let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
    let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
    let currentdate = startDate + 'T' + endDate;
    if (this.previousDate !== currentdate) {
      this.transactionresponseNotCame = true;
      this.transactionReport = [];
      this.rowSelectedForDownload = [];
      this.originalTransactionReport = [];
      this.cardsOptions = [];
      this.showDeclinneSwitch =false;
      this.transactionComplted='closed'
      this.resultErrorMsg1=   this.translateService.instant("fuild.Fetchingdata");
      this.adminPanelService.getTransactioReport(startDate, endDate).subscribe(resp => {
        if (resp) {
          this.transactionresponseNotCame = false;
          let response = convertDataToJSON1(resp, ';');
          this.originalTransactionReport = response.slice(1);
          
          if (this.originalTransactionReport && this.originalTransactionReport.length > 0) {
            let found = 0;
            this.cardsOptions.push({ name: 'fuild.Allcards', last4: '', fullname: '' });
            for (let item of this.originalTransactionReport) {
              if (item['Status'] === 'declined') {
                found = 1;
              }
              let cardDetails = { name: '', last4: '', fullname: '' };
              cardDetails.name = item['Card description']
              cardDetails.last4 = item['Card number'];
              cardDetails.fullname = item['Card description'] + " " + item['Card number'];
              if (cardDetails.last4 !== "" && this.cardsOptions.find((test) => test.last4 === cardDetails.last4) === undefined) {
                this.cardsOptions.push(cardDetails);
              }
            }
            if (found === 1) {
              this.showDeclinneSwitch = true;

            }
            
            this.filterTransactionnData(true);
            //this.buildTransactionReport(this.originalTransactionReport);
            this.previousDate = startDate + 'T' + endDate;
          } else {
            this.transactionresponseNotCame = false;
           
            this.resultErrorMsg1 = this.translateService.instant("fuild.Nodata");
          
            this.previousDate = startDate + 'T' + endDate;
          }
          this.transactionresponseNotCame = false;
          this.previousDate = startDate + 'T' + endDate;
          
        } else {
          if (resp){
          this.transactionresponseNotCame = false;
          
          this.resultErrorMsg1 = this.translateService.instant("fuild.Nodata");
          
          }
        }
      })
    }
  }
  issuinngBalance() {
    this.adminPanelService.getLiveBalance().subscribe(resp => {
      if (resp.status === 'success') {
        // 
        //  if(resp.data.amountAvailable){
        this.currentBalance = (resp.data.currBalance / 100);
        this.availableBalance = (resp.data.amountAvailable / 100);
        this.statementBalance = (resp.data.statementBalance / 100);
        this.lastPayment = (resp.data.lastPayment / 100);
        this.creditEnabled = resp.data.creditEnabled;
        this.lastDate = new Date(resp.data.lastPaymentDate);

        //  }
        if (resp.data.bankAccounts && resp.data.bankAccounts.length > 0) {
          this.bankAccounts = this.sortBankList(resp.data.bankAccounts);
          
        }
        this.balanceapiCame = true;
      } else if (resp) {
        // this.toastr.error("Card issuing not supported ...")
        this.showFluidPayMsg = true;
      }
    })
  }
  onboardmsg1='';
  isTransactionIsTopUp(authId) {
    if (authId) {
      const [item, item2] = authId.split('_');
      if (item && item !== 'tu') {
        return true;
      } else {
        return false;
      }
    }
  }
  
  getCardDetails() {
    this.originalResponse = [];
    //this.cardList =[];
    this.responseNotCame = true;
    this.adminPanelService.getCardStatus().subscribe((resp) => {
      if (resp.status === 'success' && resp.data.length > 0) {
        
        if (this.bsModalRef) {
          this.bsModalRef.hide();
          this.updateCard = false;
          this.deleteButton = false;
        }
        this.responseNotCame = false;
        this.originalResponse = resp.data;
        this.getDepartments(resp.data);
        this.getStatusOptions(resp.data);
        this.filterData();
        //this.buildData(resp.data);
      } else {
        if (resp){
        this.responseNotCame = false;
        this.resultErrorMsg = this.translateService.instant("fuild.Nodata");
        if (resp.data && resp.data.onboardingStatus) {
          this.CardRequested = false;
          if (resp.data.onboardingStatus === 'NO_ACCESS') {
            this.CardRequested = false;
            this.onboardMsg = 'NO_ACCESS';
            this.onboardlink = null;
            this.onboardmsg1= 'Request Access To FluidCash'
            this.onboardMsg = this.translateService.instant('fuild.RequestAccessToFluidPay')
          } else if (resp.data.onboardingStatus === 'NOT_ONBOARDED') {
            this.CardRequested = false;
            this.onboardmsg1= '';
            this.onboardLevel = this.translateService.instant('fuild.FluidPayAccessApproved');
            this.onboardlink = resp.data.onboardingLink ? resp.data.onboardingLink : null;
            this.onboardMsg = this.translateService.instant('fuild.CompleteOnboarding')
          } else if (resp.data.onboardingStatus === 'BANK_NOT_CONNECTED') {
            this.CardRequested = false;
            this.onboardmsg1= '';
            this.onboardLevel = this.translateService.instant('fuild.OnboardingcompleteYouarealmostthere');
            this.onboardlink = null;
            this.onboardMsg = this.translateService.instant('fuild.ConnectBankAccount')
          } else if (resp.data.onboardingStatus === 'NOT_COMPLETE') {
            this.showNotCompleteScreen = true;
          } else if (resp.data.onboardingStatus === 'REQUEST_RECEIVED') {
            this.CardRequested = true;
          }
        }
      }
      }
    });
  }
  sortList(data) {
    data.sort(function (a, b) {
      // 
      if (a.firstName === "" || !a.firstName) {
        return 1;
      } else if (b.firstName === "" || !b.firstName) {
        return -1;
      } else if (a.firstName < b.firstName) { return -1; }
      else if (a.firstName > b.firstName) { return 1; }
      return 0;
    })
    return data;
  }
  sortCardList(data) {
    data.sort(function (a, b) {
      // 
      if (a.userName === "" || !a.userName) {
        return 1;
      } else if (b.userName === "" || !b.userName) {
        return -1;
      } else if (a.userName < b.userName) { return -1; }
      else if (a.userName > b.userName) { return 1; }
      return 0;
    })
    return data;
  }
  sortExpenseList(data) {
    data.sort(function (a, b) {
      // 
      if (a.name === "" || !a.name) {
        return 1;
      } else if (b.name === "" || !b.name) {
        return -1;
      } else if (a.name < b.name) { return -1; }
      else if (a.name > b.name) { return 1; }
      return 0;
    })
    return data;
  }
  sortBankList(data) {
    data.sort(function (a, b) {
      // 
      if (a.bankName === "" || !a.bankName) {
        return 1;
      } else if (b.bankName === "" || !b.bankName) {
        return -1;
      } else if (a.bankName < b.bankName) { return -1; }
      else if (a.bankName > b.bankName) { return 1; }
      return 0;
    })
    return data;
  }
  getAmount(item) {
    let amount = '';
    if (item) {
      item = item / 100;
      amount = item;
    }
    return amount;
  }
  clearMerchandiseSelectedOption() {
    this.multipleSelectedmerchant = [];
  }
  showChangeRequestErro = false;
  memoMsg = '';
  getChecked() {
    this.showChangeRequestErro = false;
  }
  getDeleteReceipt(authid) {
    this.updateCard = true;
    let indexSelect = this.originalTransactionReport.findIndex(item => item['Auth ID'] === authid);
    this.adminPanelService.getDeleteReceipt(authid).subscribe(resp => {
      if (resp && resp.status == 'success') {
        this.updateCard = false;
        this.originalTransactionReport[indexSelect]['Receipt'] = "";
        this.transactionSelected[0].receiptMsg = null;
        this.filterTransactionnData();
        //  this.buildTransactionReport(this.originalTransactionReport);
      //  this.toastr.success("Receipt deleted successfully !!!");
        this.toastr.error(this.translateService.instant('cards.Receiptdeletedsuccessfully'));
      } else {
        this.updateCard = false;
        this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
      }
    })
  }
  getMemotext(item) {
    if (item.qboAccess && item.qboAccess === 'synced') {
      return  this.translateService.instant('cards.memo');
    } else {
      return this.translateService.instant('cards.viewmemo');
    }
  }
  onAddMemo() {
    if (this.memoMsg !== '') {
      this.updateCard = true;
      //  this.memoMsg = this.memoMsg.replace(/(\r\n|\n|\r)/gm, "");
      let indexSelect = this.originalTransactionReport.findIndex(item => item['Auth ID'] === this.selectedCardNumber);
      this.adminPanelService.getUpdateMemo(this.originalTransactionReport[indexSelect]['Auth ID'], this.memoMsg).subscribe(resp => {
        if (resp && resp.status === 'success') {
          this.updateCard = false;
          this.originalTransactionReport[indexSelect]['Memo'] = this.memoMsg;

          this.filterTransactionnData();
          if (this.transactionSelected.length > 0) {
            this.transactionSelected[0].memoMsg = this.memoMsg;
          }
        //  this.toastr.success("Memo added successfully !!!");
          this.toastr.success(this.translateService.instant('cards.Memoaddedsuccessfully'));
          this.memoMsg = '';
          this.bsModalReforMemo.hide();
        } else {
          this.updateCard = false;
          this.memoMsg = '';
         // this.toastr.error("Please try again later !!!");
          this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
          this.bsModalReforMemo.hide();
        }
      })


    } else {
      this.showChangeRequestErro = true;
      return;
    }
  }
  onModelMemoCancel() {
    this.bsModalReforMemo.hide();
  }
  onModeCancel() {
    this.bsModalRef.hide();
  }
  onModelTransactionCancel() {
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'managecards',
        },
        replaceUrl: false
      }
    );
    this.viewMode = 'tab2';
    this.bsModalReforTransaction.hide();
  }
  openMemoModal(modal, cardNumber, item) {
    if (item.qboAccess === 'synced' && item.memoMsg) {
      return;
    }
    if (item.memoMsg) {
      this.memoMsg = item.memoMsg;
    } else {
      this.memoMsg = '';
    }
    this.selectedCardNumber = cardNumber;
    this.bsModalReforMemo = this.modalService.show(modal);
  }
  transactionSelected = []
  openTransactionDetailModal(modal, number) {
    this.transactionSelected = [];
    let selectIndex = this.transactionReport.findIndex(item => item.authId === number);
    this.transactionSelected.push(this.transactionReport[selectIndex]);
    // this.getTransactionDetails(this.transactionSelected[0].authId);
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'managecards',
          subType: this.transactionSelected[0].referenceId
        },
        replaceUrl: false
      }
    );
    this.bsModalReforTransaction = this.modalService.show(modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true, keyboard: false,
    });

  }
  getPlaceHolder(item) {
    if (item) {
      return "";
    } else  if(!this.dropDownopen1){
      return "Select";
    }
  }
  showExpenseTypeChanged(event, id) {
    
    // this.myPlaceholderForExpensify ="";
    if (event) {
      this.expenseApiCall(id, event, event.name);
    } else {
      this.expenseApiCall(id, "", "");
    }
  }
  getChangeStyle(expense) {
    if (this.adminPanelService.employeePopOpen) {
      return { 'top': '-50px' }
    } else if (expense) {
      return { 'top': '-68px' }
    } else if (!expense) {
      return { 'top': '-62px' }
    }
  }
  expenseApiCall(id, event, item) {
    this.adminPanelService.getUpdateExpense(id, event.categoryId).subscribe(resp => {
      if (resp && resp.status === 'success') {
        let selectIndex = this.originalTransactionReport.findIndex(item => item['Auth ID'] === id);
        this.originalTransactionReport[selectIndex]['Expense Category'] = event.categoryId;
        this.originalTransactionReport[selectIndex]['Expense Category Name'] = item;
        this.filterTransactionnData();
        //this.buildTransactionReport(this.originalTransactionReport);
      //  this.toastr.success("Expense category updated successfully !!!")
        this.toastr.success(this.translateService.instant('cards.Expensecategoryupdatedsuccessfully'))

      } else {
       // this.toastr.error("Expense  category not updated . Please try again later !!!");
       this.toastr.error(this.translateService.instant('cards.Expensecategorynotupdated.Pleasetryagainlater'))
      }
    })
  }
  buildTransactionReport(resp) {
    this.transactionReport = [];
    // this.availableBalance =0;
   // let found=0;
    if (resp && resp.length > 0) {
      for (let item of resp) {
        let cardItem = {};
        cardItem['Merchant'] = item['Merchant'];
        //this.availableBalance = this.availableBalance + (item.available_balance) ;
        cardItem['name'] = item['Name']
        cardItem['Department'] = item['Department']
        cardItem['description'] = item['Card description']
        cardItem['CardNumber'] = item['Card number']
        cardItem['Status'] = item['Status']
        cardItem['Amount'] = item['Amount']
        cardItem['decline'] = item['Reason for decline']
        cardItem['category'] = item['Merchant category']
        cardItem['Expense'] = (item['Expense Category'] !== "") ? item['Expense Category'] : null;
        cardItem['date'] = item['ds+Transaction date'];
        cardItem['qboAccess'] = item['Sync Status'];
        cardItem['ExpenseValue'] = item['Expense Category Name'];
        cardItem['receiptMsg'] = item['Receipt'] !== "" ? item['Receipt'] : null;
        cardItem['memoMsg'] = item['Memo'] !== "" ? item['Memo'] : null;
        cardItem['authId'] = item['Auth ID'];
        cardItem['referenceId'] = item['Reference Id'];
        cardItem['syncError'] = item['Sync Error'];
        this.transactionReport.push(cardItem);
      }
     
    } else {
      if(!this.transactionresponseNotCame){
      this.resultErrorMsg1 = this.translateService.instant("fuild.Nodata");
      }
    //  this.adminPanelService.selectedCard ='';
    }
    
  }
  getSynctext(item) {
    if (item.syncError) {
      return item.syncError;
    }
  }
  selectedFile = null;
  imageSrc: any;
  readURL(event, cardNumber, i): void {
    let indexSelect = this.originalTransactionReport.findIndex(item => item['Auth ID'] === this.selectedCardNumber);
    if (this.originalTransactionReport[indexSelect]['Receipt'] !== "" && this.originalTransactionReport[indexSelect]['Sync Status'] === 'synced' || this.originalTransactionReport[indexSelect]['Status'] === 'declined') {
      return
    }
    if (event && event[0]) {
      const file = event[0];
      this.selectedFile = file;
      this.renderPostUploadFile(cardNumber, i);
    }
  }
  renderPostUploadFile(cardNumber, i) {
    const reader = new FileReader();
    const file = this.selectedFile;
    reader.onload = e => this.imageSrc = reader.result;

    reader.readAsDataURL(file);
    let indexSelect = this.originalTransactionReport.findIndex(item => item['Auth ID'] === this.selectedCardNumber);
    // this.buildTransactionReport(this.originalTransactionReport);
    this.adminPanelService.uploadReceipt(this.originalTransactionReport[indexSelect]['Auth ID'], this.selectedFile).subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.originalTransactionReport[indexSelect]['Receipt'] = resp.data;
        this.filterTransactionnData();
        if (this.transactionSelected.length > 0) {
          this.transactionSelected[0].receiptMsg = resp.data;
        }
        //this.buildTransactionReport(this.originalTransactionReport);
        
      //  this.toastr.success("Receipt uploaded successfully !!!");
      this.toastr.success(this.translateService.instant('cards.Receiptuploadedsuccessfully'));
      } else {
        if (resp.errorMessage && resp.errorMessage.length > 0) {
          this.toastr.error(resp.errorMessage[0]);
        } else {
          this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
        }
      }
    })

  }
  buildData(resp) {
    this.cardList = [];
    // this.availableBalance =0;
    if (resp && resp.length > 0) {
      for (let item of resp) {
        let cardItem = {};
        cardItem['userName'] = item.name;
        //this.availableBalance = this.availableBalance + (item.available_balance) ;
        cardItem['department'] = item.department;
        cardItem['email'] = item.email;
        cardItem['status'] = item.status;
        cardItem['last4'] = '****' + item.last4;
        cardItem['limit'] = item.spending_limit ? item.spending_limit.amount : '';
        cardItem['period'] = item.spending_limit ? item.spending_limit.interval : '';
        cardItem['description'] = item.description;
        cardItem['used'] = (item.used);
        cardItem['cardId'] = item.issuing_card_id;
        this.cardList.push(cardItem);
      }
      if (this.cardList.length > 0) {
        this.sortCardList(this.cardList);
      }
    } else {
      this.resultErrorMsg = this.translateService.instant("fuild.Nodata");
    }
    
  }
  openMenuModal(modal) {

    this.ngxSmartModalService.getModal(modal).open()

  }
  updatestatus(description, cardId, request, modal, modal2?, index?) {
    this.cardDescription = description;
    this.ngxSmartModalService.getModal(modal).close();
    this.index = index;
    this.selectRequest = request;
    this.selectedCard = cardId;
    if (request !== 'canceled') {
      this.revealLoader = true;
      this.getupdateCardStatus(cardId, request);
    } else {
      this.cancelReason = '';
      this.bsModalRef = this.modalService.show(modal2);
    }

  }
  routeToTransactions(modal, cardId) {
    this.ngxSmartModalService.getModal(modal).close();
    this.adminPanelService.selectedCard = cardId

    this.tabChanged('tab2');
    this.filterTransactionnData(true);
  }
  isSelectedReason(value: string): boolean {
    return (this.cancelReason == value);
  }
  setReason(val) {
    this.cancelReason = val;
  }
  cancelCard() {
    this.deleteButton = true;
    this.getupdateCardStatus(this.selectedCard, this.selectRequest, this.cancelReason)

  }
  getupdateCardStatus(cardId, request, reason?) {
    this.adminPanelService.updateCardStatus(cardId, request, reason).subscribe((resp) => {
      if (resp.status === 'success') {
        this.revealLoader = false;
        
        this.toastr.success(this.translateService.instant("fuild.Cardfor") + " " + this.cardDescription + " " + this.translateService.instant("fuild.is") + " " + request + " " + ".");
        if (request === 'active') {
          this.cardList[this.index].status = 'active';
          let findIndex = this.originalResponse.findIndex(item => item.issuing_card_id === this.cardList[this.index].cardId);
          this.originalResponse[findIndex].status = 'active';
          this.filterData();
        } else if (request === 'inactive') {
          this.cardList[this.index].status = 'inactive';
          let findIndex = this.originalResponse.findIndex(item => item.issuing_card_id === this.cardList[this.index].cardId);
          this.originalResponse[findIndex].status = 'inactive';
          this.filterData();
        } else {
          this.getCardDetails();
        }
      } else if (resp.status === 'error') {
        this.revealLoader = false;
        this.toastr.success(resp.errorMessage[0]);
      }
    })
  }
  getStatusOptions(resp) {
    this.statusOptions = [{ value: 'All status', id: 'All status',name:'fuild.Allstatus' }];
    if (resp && resp.length > 0) {
      for (let item of resp) {
        let deptValue = { value: '', id: '',name:'' }
        deptValue.value = item.status.charAt(0).toUpperCase() + item.status.slice(1);
        deptValue.id = item.status;
        deptValue.name = item.status.charAt(0).toUpperCase() + item.status.slice(1);
        this.statusOptions.push(deptValue);
      }
      //
      this.statusOptions = this.statusOptions.filter((test, index, array) =>
        index === array.findIndex((findTest) =>
          findTest.value.toLowerCase() === test.value.toLowerCase()
        )
      );
    }
    // 
  }
  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }
  getDepartments(resp) {
    this.departmentOptions = [{ value: 'All Departments', id: '' ,name:'fuild.AllDepartments'}];
    if (resp && resp.length > 0) {
      for (let item of resp) {
        let deptValue = { value: '', id: '' ,name:''}
        if (item.department) {
          deptValue.value = item.department;
          deptValue.id = item.department;
          deptValue.name = item.department;
          this.departmentOptions.push(deptValue);
        }
      }
      //
      this.departmentOptions = this.departmentOptions.filter((test, index, array) =>
        index === array.findIndex((findTest) =>
          findTest.value.toLowerCase() === test.value.toLowerCase()
        )
      );
    }
    // 

  }
  sortListDept(data) {
    data.sort(function (a, b) {
      if (a.value < b.value) { return -1; }
      if (a.value > b.value) { return 1; }
      return 0;
    })
    return data;
  }
  showDepartmentChanged(event) {
    this.filterData();
    // this.buildData(originalResponse);

  }
  showReceiptChanged(event) {
    this.filterTransactionnData();
  }
  processedTabChanging(tab,item){
    this.transactionTab =tab;
    this.transactionComplted = item;
    this.filterTransactionnData()
  }
  showCardChanged(event) {
    this.filterTransactionnData();
   
  }
  filterTransactionnData(event?) {
    this.rowSelectedForDownload = [];
    let originalResponse = JSON.parse(JSON.stringify(this.originalTransactionReport));
    if (this.receiptValue === 'With Receipt') {
      originalResponse = originalResponse.filter(item => {
        if (item['Receipt'] !== "") {
          return true;
        }
      });
    }
    if (this.cardValue !== '') {
      originalResponse = originalResponse.filter(item => {
        if (item['Card number'] === this.cardValue) {
          return true;
        }
      });
    }  

    if (!this.showDelineTransaction) {
      originalResponse = originalResponse.filter(item => {
        if (item['Status'] !== 'declined') {
          return true;
        }
      });
    }
    if (this.showDelineTransaction) {
      originalResponse = originalResponse.filter(item => {
        if (item['Status'] === 'declined') {
          return true;
        }
      });
    }
    if (this.receiptValue === 'With Out Receipt') {
      originalResponse = originalResponse.filter(item => {
        if (item['Receipt'] === "") {
          return true;
        }
      });
    }
    if (this.adminPanelService.selectedCard !== '') {
      originalResponse = originalResponse.filter(item => { 
        if (item['Card number'] === this.adminPanelService.selectedCard) {
        return true;
      }
    });
    }
    if (this.cardNameSearchValueForTransaction && this.cardNameSearchValueForTransaction.trim().length > 0) {
      originalResponse = this.searchByNameChanged(this.cardNameSearchValueForTransaction, originalResponse);
    } 
    if(this.transactionComplted ==='closed'){
      let  originalResponseForPending = originalResponse.filter(item => {
        if (item['Transaction Status'] === "pending") {
          return true;
        }
      });
      originalResponse = originalResponse.filter(item => {
        if (item['Transaction Status'] === "" || item['Transaction Status'] !== "pending") {
          return true;
        }
      });
  
    
      if(originalResponseForPending  && originalResponseForPending.length >0){
        this.transactionPendingTab =true;
      }else{
        this.transactionPendingTab =false;
      }
      if(originalResponse.length > 0 && event){
        this.transactionTab ='tab55';
        this.transactionProccessedTab =true;
      }else if(originalResponse.length === 0 && originalResponseForPending  && originalResponseForPending.length > 0 && event){
        this.transactionTab ='tab56';
        this.transactionComplted ='pending'
        this.transactionProccessedTab =false;
        originalResponse = [...originalResponseForPending]
      }
    }else{
      originalResponse = originalResponse.filter(item => {
        if (item['Transaction Status'] === "pending") {
          return true;
        }
      });
      if(originalResponse.length ===0 && event){
        
        this.transactionPendingTab =false;
      }else{
        if(event){
          this.transactionPendingTab =false;
        }
      }
    }
    let found =0
    if(this.adminPanelService.selectedCard !==''){
      this.showDeclinneSwitch = this.isDataHAveDeclinedTransaction(this.adminPanelService.selectedCard);
      if(originalResponse.length ===0){
        this.transactionPendingTab =false;
        this.transactionProccessedTab =false;
      }
    }else{
      for (let item of this.originalTransactionReport) {
        if (item['Status'] === 'declined') {
          found = 1;
        }
      } 
      if(found===1){
        this.showDeclinneSwitch =true;
      }else{
        this.showDeclinneSwitch =false;
      }
    }
   // this.adminPanelService.selectedCard ='';
    this.buildTransactionReport(originalResponse);
  }
  isDataHAveDeclinedTransaction(event){
    let originalResponse = JSON.parse(JSON.stringify(this.originalTransactionReport));

    originalResponse = originalResponse.filter(item => { 
      if (item['Card number'] === event) {
      return true;
    }
  });
  originalResponse = originalResponse.filter(item => {
    if (item['Status'] === 'declined') {
      return true;
    }
  });

  if(originalResponse.length > 0){
    return true;
  }else{
    return false;
  }
  }
  filterData() {
    let originalResponse = JSON.parse(JSON.stringify(this.originalResponse));
    if (this.deptValue !== 'All Departments') {
      originalResponse = originalResponse.filter(item => item.department === this.deptValue);
    }
    if (this.statusValue !== 'All status') {
      originalResponse = originalResponse.filter(item => item.status === this.statusValue);
    }
    if (this.cardNameSearchValue && this.cardNameSearchValue.trim().length > 0) {
      originalResponse = this.searchByNameChanged(this.cardNameSearchValue, originalResponse);
    }
    this.buildData(originalResponse);
    //return originalResponse
  }
  showStatusChanged(event) {
    // this.statusValue = event;
    this.filterData();
    //this.buildData(originalResponse);

  }
  tabChanged(tab) {
    this.viewMode = tab;
    if (tab === 'tab2'){
    this.transactionComplted ='closed'
    }
    if (tab === 'tab1' &&  this.adminPanelService.selectedCard !== '') {
     // this.filterTransactionnData(true);
     this.adminPanelService.selectedCard = '';
     this.cardNameSearchValueForTransaction = '';
     this.receiptValue = 'All';
     this.transactionProccessedTab = true;
     this.cardValue = "";
     this.transactionTab='tab55';
     this.transactionComplted ='closed'
     this.showDelineTransaction = false;
     this.filterTransactionnData(true);
      
    }else if (tab === 'tab1') {
      this.onSelectTransactionRow('All Transaction', false);
      setTimeout(() => {
        this.myPlaceholder = this.translateService.instant("fuild.Export");
        this.fileTypeValue = null;
      }, 10);
      this.cardNameSearchValueForTransaction = '';
      this.receiptValue = 'All';
      this.transactionProccessedTab = true;
      this.cardValue = "";
      this.transactionTab='tab55';
      this.transactionComplted ='closed'
      this.showDelineTransaction = false;
      this.filterTransactionnData(true);
    }else{
      if (this.originalTransactionReport && this.originalTransactionReport.length === 0 && !this.transactionresponseNotCame &&  !this.responseNotCame){
        this.resultErrorMsg1 = this.translateService.instant("fuild.Nodata");
      }
      if(this.adminPanelService.selectedCard === '')
      this.filterTransactionnData(true);
    }

    setTimeout(() => {
      this.myPlaceholder = this.translateService.instant("fuild.Export");
      this.fileTypeValue = null;
    }, 10);
    this.cardNameSearchValueForTransaction = '';
    this.receiptValue = 'All';
  }
  showFileTypeChanged(event, modal) {
    // let indexSelect =  this.originalTransactionReport.findIndex(item => item['Auth ID']===this.selectedCardNumber);
    if (this.fileTypeValue !== '') {
      this.myPlaceholder = '';
    }
    this.DownLoadData(modal);
  }
  DownLoadData(modal) {
    if (this.rowSelectedForDownload.length > 0) {
      let selectedRow = [];
      let selectedID = [];
      let reportTable = [];
      let found = 0;
      for (let item1 of this.rowSelectedForDownload) {
        if (this.isTransactionIsTopUp(item1)) {
          found = 1;

        }
        let selectInndex = this.transactionReport.findIndex(item => item.authId === item1);
        selectedRow.push(this.transactionReport[selectInndex]);
        selectedID.push(this.transactionReport[selectInndex].authId);
      }
      if (found === 0) {
        this.toastr.warning(this.translateService.instant("fuild.Noexpensetransactionswereselected"))
        setTimeout(() => {
          this.myPlaceholder = this.translateService.instant("fuild.Export");
          this.fileTypeValue = null;
        }, 10);
        return;
      }
      let reportRow = [];
      reportRow.push('Date');
      reportRow.push('Description');
      reportRow.push('Name');
      reportRow.push('Card Description');
      reportRow.push('Department');
      reportRow.push('Card Number');
      reportRow.push('Status');
      reportRow.push('Reason for decline');
      reportRow.push('Expense Category');
      reportRow.push('Merchant Category');
      reportRow.push('Reciept');
      reportRow.push('Memo');
      reportRow.push('Amount');
      reportTable.push(reportRow);
      for (let rowItem of selectedRow) {
        let reportRow = [];
        let dateObj = DateUtils.getFormattedDateWithoutTimeZone(rowItem['date']);
        let datePipe = new DatePipe('en-US');
        let setDate = datePipe.transform(dateObj, 'MM/dd/yyyy');
        reportRow.push(setDate);
        reportRow.push(rowItem['Merchant'])
        reportRow.push(rowItem['name'])
        reportRow.push(rowItem['description'])
        reportRow.push(rowItem['Department'])
        reportRow.push(rowItem['CardNumber'])
        reportRow.push(rowItem['Status'])
        reportRow.push(rowItem['decline'])
        reportRow.push(rowItem['ExpenseValue'])
        reportRow.push(rowItem['category'])
        reportRow.push(rowItem['receiptMsg'])
        reportRow.push(rowItem['memoMsg'])
        reportRow.push(rowItem['Amount'] * -1);
        reportTable.push(reportRow);
      }
      if (this.fileTypeValue == 'Csv') {
        this.downloadFile(reportTable);
      } else if (this.fileTypeValue == 'XLSX') {
        this.downloadExcelFile(reportTable)
      } else if (this.fileTypeValue == 'QuickBooksOnline') {
        if (this.showDelineTransaction) {
          this.toastr.warning(this.translateService.instant("fuild.Pleaseremovethedeclinedtransactionsforexport"))
          setTimeout(() => {
            this.myPlaceholder = this.translateService.instant("fuild.Export");
            this.fileTypeValue = null;
          }, 10);
          return
        }
        this.downnloadQbo(selectedID, modal)
      }
    } else {
      this.toastr.error(this.translateService.instant("fuild.Notransactionsareselected"));
      setTimeout(() => {
        this.myPlaceholder = this.translateService.instant("fuild.Export");
        this.fileTypeValue = null;
      }, 10);

    }
  }
  showQboLoader = false;
  downnloadQbo(ids, modal) {
    this.bsModalRef = this.modalService.show(modal);
    this.showQboLoader = true;
    this.updateCard = true;
    this.adminPanelService.getQboSync(ids).subscribe(resp => {
      if (resp) {
        if (resp.bookingStatusId) {
          this.initiateImmediatePolling(resp.bookingStatusId, resp.nextCallAfter);
        } else {
          this.bsModalRef.hide();
          this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
          this.showQboLoader = false;
          this.updateCard = false;
        }
      } else {
        this.bsModalRef.hide();
        this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
        this.showQboLoader = false;
        this.updateCard = false;
      }
    })

  }
  initiateImmediatePolling(bookingRequestId: string, delay) {
    setTimeout(() => {
      try {
        this.adminPanelService.getQboStatus(bookingRequestId)
          .pipe(catchError(err => {
            return throwError(err);
          }))
          .subscribe((res) => {
            this.proceedstatusOfQbo(res);
          }, error => {

          });
      } catch (error) {

      }
    }, delay);


  }
  proceedstatusOfQbo(resp) {
    if (resp && resp.status === 'inprogress') {
      this.initiateImmediatePolling(resp.bookingStatusId, resp.nextCallAfter);
    } else if (resp && resp.status === 'success') {
      
      this.bsModalRef.hide();
      this.showQboLoader = false;
      this.updateCard = false;
      if (resp.errorMsgs && resp.errorMsgs.length > 0) {
        for (let item of resp.errorMsgs) {
          let selecindex = this.originalTransactionReport.findIndex(item1 => item1['Auth ID'] === item.name);
          if (this.originalTransactionReport[selecindex] && this.originalTransactionReport[selecindex]['Sync Status'] !== 'synced') {
            this.originalTransactionReport[selecindex]['Sync Status'] = 'error';
            this.originalTransactionReport[selecindex]['Sync Error'] = item.value;

          }
        }
        this.toastr.success(this.translateService.instant("fuild.ExportCompleteFewtransactionsdidnotupload.Seetheerrormessageoneachtransactionfordetails"));
      }
      if (resp.synced && resp.synced.length > 0) {
        for (let item of resp.synced) {
          let selecindex = this.originalTransactionReport.findIndex(item1 => item1['Auth ID'] === item);
          this.originalTransactionReport[selecindex]['Sync Status'] = 'synced';
          this.originalTransactionReport[selecindex]['Sync Error'] = "";
        }
        // this.toastr.success("Export Complete !!!");
        if (!resp.errorMsgs || resp.errorMsgs.length == 0) {
          this.toastr.success(this.translateService.instant("fuild.ExportComplete"));
        }
      }
      this.filterTransactionnData();
      // this.buildTransactionReport(this.originalTransactionReport);
      setTimeout(() => {
        this.myPlaceholder =this.translateService.instant("fuild.Export");
        this.fileTypeValue = null;
      }, 10);
    } else {
      this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
      this.bsModalRef.hide();
      this.showQboLoader = false;
      this.updateCard = false;
      setTimeout(() => {
        this.myPlaceholder = this.translateService.instant("fuild.Export");;
        this.fileTypeValue = null;
      }, 10);
    }
  }
  @ViewChild('input1', { static: false }) inputEl: ElementRef;

  downloadFile(data) {
    const replacer = (key, value) => (value === null ? '' : value); // specify how you want to handle null values here
    const header = Object.keys(data[0]);
    const csv = data.map((row) =>
      header
        .map((fieldName) => JSON.stringify(row[fieldName], replacer))
        .join(',')
    );
    csv.unshift(header.join(','));
    const csvArray = csv.join('\r\n');

    const a = document.createElement('a');
    const blob = new Blob([csvArray], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);

    a.href = url;
    a.download = 'card_transactions.csv';
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
    setTimeout(() => {
      this.myPlaceholder = this.translateService.instant("fuild.Export");
      this.fileTypeValue = null;
      this.inputEl.nativeElement.focus();
    }, 10);
  }
  downloadExcelFile(data) {
    let filename = "card_transactions.xlsx";
    this.adminPanelService.downloadReport(data, filename).subscribe(
      data => {
        // saveAs(data, filename);
        // FileSaver.saveAs(pdf, filename);
        let blob = new Blob([data], { type: 'application/xlsx' });

        var downloadURL = window.URL.createObjectURL(data);
        var link = document.createElement('a');
        link.href = downloadURL;
        link.download = "card transactions.xlsx";
        link.click();
        setTimeout(() => {
          this.myPlaceholder = this.translateService.instant("fuild.Export");
          this.fileTypeValue = null;
          this.inputEl.nativeElement.focus();
        }, 10);
      },
      err => {
        alert(this.translateService.instant("fuild.Problemwhiledownloadingthefile"));
        console.error(err);
        setTimeout(() => {
          this.myPlaceholder =this.translateService.instant("fuild.Export");
          this.fileTypeValue = null;
          this.inputEl.nativeElement.focus();
        }, 10);
      }
    );
  }
  authidSearch() {
    if (this.cardNameSearchValueForTransaction && this.cardNameSearchValueForTransaction.search('REFID-') !== -1 && this.transactionReport.length == 0) {
      this.getTransactionDetails(this.cardNameSearchValueForTransaction);
    }
  }
  searchByNameChanged(nameString, originalResponse) {
    // let originalResponse =JSON.parse(JSON.stringify(this.originalResponse));
    if (nameString && nameString.trim().length > 0) {
      originalResponse = originalResponse.filter(item => {
        let userInfo = item;
        return (userInfo.name && userInfo.name.toLowerCase().startsWith(nameString.toLowerCase()))
          || (userInfo.Name && userInfo.Name.toLowerCase().startsWith(nameString.toLowerCase()))
          || (userInfo.last4 && userInfo.last4.toLowerCase().startsWith(nameString.toLowerCase())
            || (userInfo['Reference Id'] && userInfo['Reference Id'].toLowerCase().startsWith(nameString.toLowerCase()))
            || (userInfo.description && userInfo.description.toLowerCase().startsWith(nameString.toLowerCase())));
      });
    }
    return originalResponse;
    //this.buildData(originalResponse);

  }
  searchByMerchantValueAndIdChanged(term: string, item: any) {
    term = term.toLowerCase();
    return (item.value && item.value.toLowerCase().indexOf(term) > -1) ||
      (item.id && item.id.toLowerCase().indexOf(term) > -1);
  }
  closeDropdown() {
    this.dropDownopen = false;
  }
  closeDropdown1() {
    this.dropDownopen1 = false;
  }
  filterSelectedlist(item) {
    for (let option of item) {
      this.merchandiseOptions = this.merchandiseOptions.filter(item2 => item2.id !== option);
    }
    // 
    for (let option of item) {
      if (this.originalmerchandiseOptions && this.originalmerchandiseOptions.length > 0) {
        let approver = this.originalmerchandiseOptions.find(item3 => item3.id === option)
        this.merchandiseOptions.unshift(approver);
      }
    }
    //
  }
  onApproverChangeClicked(item, event) {
    //
    this.merchantChange = true;
    if (event.target.checked) {
      if (this.multipleSelectedmerchant.indexOf(item) === -1) {
        this.multipleSelectedmerchant.push(item);
      }

    } else {
      this.multipleSelectedmerchant = this.multipleSelectedmerchant.filter(item1 => item1 !== item);
      this.isApproverChecked(item);
    }
  }
  getValueOfmerchant(item) {
    let merchant = this.originalmerchandiseOptions.find(item3 => item3.id === item);
    return merchant.value;

  }
  isApproverChecked(item) {
    return this.multipleSelectedmerchant.indexOf(item) > -1;
  }
  showBankTypeChanged(event){

  }
  showEmployeeTypeChanged(event) {
    this.dropDownopen = false;
    if(event.fullName==='Guest'){
      this.GuestUserSelect =true;
      this.addCardForm.controls['firstname'].setValidators(Validators.compose([Validators.required,
        Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)]));
      this.addCardForm.controls['lastname'].setValidators(Validators.compose([Validators.required,
        Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)]));
      this.addCardForm.controls['email'].setValidators(Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_EMAIL)]));
      this.addCardForm.controls['amount'].setValidators(Validators.compose([Validators.required]));
      this.addCardForm.controls['firstname'].updateValueAndValidity();
      this.addCardForm.controls['lastname'].updateValueAndValidity();
    } else {
      this.GuestUserSelect =false;
      this.addCardForm.controls['firstname'].clearValidators();
      this.addCardForm.controls['lastname'].clearValidators();
      this.addCardForm.controls['email'].clearValidators();
      this.addCardForm.controls['firstname'].updateValueAndValidity();
      this.addCardForm.controls['lastname'].updateValueAndValidity();
      this.addCardForm.controls['email'].updateValueAndValidity();
    }
  }
  showExpenseTypeChangedFromModal(event) {
    this.dropDownopen = false;
  }
  updateCardList() {
    if (this.addCardForm.invalid) {
      this.addCardForm.controls['username'].markAsTouched();
      this.addCardForm.controls['cardDescription'].markAsTouched();
      this.addCardForm.controls['amount'].markAsTouched();
      this.addCardForm.controls['firstname'].markAsTouched();
      this.addCardForm.controls['lastname'].markAsTouched();
      this.addCardForm.controls['email'].markAsTouched();
      return;
    }
    if (this.showAmountError) {
      return;
    }
    if (this.showLimitError) {
      return;
    }
    let cardDetails = {};
    if (this.Mode == 'Add') {
      cardDetails['expense_category'] = this.addCardForm.controls['expense_category'].value;
      if(this.GuestUserSelect){
        cardDetails['email'] = this.addCardForm.controls['email'].value; 
      }else{
      cardDetails['email'] = this.addCardForm.controls['username'].value;
      }
      if(this.addGiftCard){
        cardDetails['firstName'] = this.addCardForm.controls['firstname'].value;
        cardDetails['lastName'] = this.addCardForm.controls['lastname'].value;
        cardDetails['phoneNumber'] = new PhoneNumberDTO();
        cardDetails['phoneNumber'].countryCode = this.addCardForm.controls['dialCode'].value;
        cardDetails['phoneNumber'].number = this.addCardForm.controls['phoneNumber'].value
      }
      cardDetails['transaction_limit'] = (this.addCardForm.controls['limit'].value * 100);
      cardDetails['description'] = this.addCardForm.controls['cardDescription'].value;
      cardDetails['merchant_restrictions'] = this.multipleSelectedmerchant;
      cardDetails['spending_limit'] = { amount: (this.addCardForm.controls['amount'].value * 100), interval: this.addCardForm.controls['timePeriod'].value };
      cardDetails['giftCard'] = this.addGiftCard;
      this.updateCard = true;
      this.adminPanelService.addNewCardTransaction(cardDetails).subscribe((resp => {
        if (resp.status === 'success' && resp.data) {
          
          this.getCardDetails();
        } else if (resp.status = 'error') {
          // if(this.bsModalRef){
          // this.bsModalRef.hide();
          this.updateCard = false;
          this.deleteButton = false;
          //}
          this.toastr.error(resp.errorMessage[0]);
        }
      }))
    } else if (this.Mode == 'edit') {
      cardDetails['issuing_card_id'] = this.selectedCard;
      cardDetails['expense_category'] = this.addCardForm.controls['expense_category'].value;
      cardDetails['email'] = this.addCardForm.controls['username'].value;
      cardDetails['transaction_limit'] = (this.addCardForm.controls['limit'].value * 100);
      cardDetails['description'] = this.addCardForm.controls['cardDescription'].value;
      cardDetails['merchant_restrictions'] = this.multipleSelectedmerchant;
      cardDetails['spending_limit'] = { amount: (this.addCardForm.controls['amount'].value * 100), interval: this.addCardForm.controls['timePeriod'].value };
      cardDetails['giftCard'] = this.addGiftCard;

      this.updateCard = true;
      this.adminPanelService.updateCardDetails(cardDetails).subscribe((resp => {
        if (resp.status === 'success' && resp.data) {
          
          this.getCardDetails();
        } else if (resp.status = 'error') {
          // if(this.bsModalRef){
          // this.bsModalRef.hide();
          this.updateCard = false;
          this.deleteButton = false;
          // }
          this.toastr.error(resp.errorMessage[0]);
        }
      }))
    }
  }

  ng_cc_reveal_ui_frame_loaded(event) {
    this.ng_cc_reveal_ui_frame_loaded_count++;
    if (this.ng_cc_reveal_ui_frame_loaded_count % 2 === 0 || event.target.src !== '') {
      this.revealLoader1 = false;
    }
  }
}
