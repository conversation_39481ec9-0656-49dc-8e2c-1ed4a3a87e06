import { NgModule } from "@angular/core";

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';

import { TooltipModule } from 'ng2-tooltip-directive';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgChartsModule } from 'ng2-charts';
import { ShareModule } from '../share.module';
import { AppApprovalRoutingModule } from './app-approval-routing.module';






@NgModule({
  imports: [
    AppApprovalRoutingModule,
    NgbModule,
    ShareModule,
    TooltipModule,
    NgChartsModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  declarations: [
   
    ],
})
export class AppApprovalModule {

}