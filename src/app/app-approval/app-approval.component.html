<email-header *ngIf="!this.userAccountInfoService.njoySpecificBuild"></email-header>
<section class="tab-container">
    <div class="main-wrapper">
      <div class="container">
        <div class="tab">
          <div class="col-lg-12">
            <div *ngIf="!showApprovalDetails" class="tab profileTab" id="pageTab">
              <div class="tab-list top-strip">
                <ul>
                  <li class="tab-list-item active" data-tab="1" ><a
                     >{{'cards.PENDINGAPPROVALS' | translate}}</a></li>
                  
                  <!-- <li class="tab-list-item" data-tab="2"  onclick="activeTab(this);" ><a href="javascript:void(0);">Authorized</a></li>
                                          <li class="tab-list-item" data-tab="3" onclick="activeTab(this);" ><a href="javascript:void(0);">Completed</a></li>
                                          <li class="tab-list-item" data-tab="4"   onclick="activeTab(this);"><a href="javascript:void(0);" >Rejected</a></li>-->
  
                </ul>
              </div>
              <div class="tab-content">
                <div class="tab-content-item active" id="1">
                  <div class="card-div active shadow">
                    <div class="card-div-inner" style="width:100%;">
                      <div class="booking-container">
                            <div *ngIf="this.originalApprovalsList.length >0 && !responseNotCame && isUserCompanyManager()" style="float: left;width: inherit;margin-bottom: 30px;">
                                    <div class="row">
                                            <div class="col-12" style="padding-top:3px !important;display: flex;">
                                                    <span  class="switchLabel">{{'approval.Showpendingapprovalsforalladmins.' | translate }}:</span>
                                              <ui-switch color="gray" 
                                                [checked]="showPenndingApprovals" (change)="allowAllAdminApprovals($event)" checkedLabel="{{ 'cards.ON' | translate }}"
                                                uncheckedLabel="{{ 'cards.OFF' | translate }}"></ui-switch>
                  
                                            </div>
                                          
                                          </div>
                                
                                  </div>
                        <div *ngIf=" responseNotCame" class="row"
                          style="text-align: center;padding-top:20px;padding-left:15px;">
                          <app-loader *ngIf="responseNotCame" style="margin-left: auto;margin-right: auto;"
                            [spinnerStyle]="true"></app-loader>
                        </div>
                        <div *ngIf="!responseNotCame && approvallist && approvallist.length ===0"
                        style="text-align:center !important;margin-top:15px !important;">
                        <div class="text1 " style="font-size: 18px;color:gray;" ><span *ngIf="(resultPendingErrorMessage!=='' && resultErrorMessage!=='Fetching data')">{{this.resultPendingErrorMessage | translate}}<br></span>{{errormsg3 | translate}}</div>
                      </div>
                      <div *ngIf="approvallist && approvallist.length >0">
                         <div *ngFor="let item of approvallist;let mainIndex=index">
                             <div *ngIf="this.getTimeDiffernece(item).mins > 0">
                                 <div class="result-card-box">
                                     <div *ngFor="let flight of item.data;let i=index">
                                        <div  class="result-card-box-inner" *ngIf="flight.type=='flight'">
                                            <div class="booking-details">
                                                <div class="booking-details-inner">
                                                    <div class="booking-img">
                                                        <img src="assets/images/flight-circle.png" />
                                                    </div>
                                                    <div class="booking-text">
                                                        <p style="margin-right: 2px;">{{'ngOption.Depart' | translate}}</p>
                                                        <p class="font-bold">{{getDisplayDateTimeForFlights(flight.option.flight_option.legs[0].hops[0].starts,'EE MMM d, yyyy')}}</p>
                                                    </div>
                                                </div>
                                                <div class="destination-hotel"  [attr.translate]="'no'" style="display: flex;">
                                                    <p class="primary-text" >{{flight.option.flight_option.legs[0].hops[0].from}} -</p>
                                                    <p class="primary-text">{{flight.option.flight_option.legs[0].hops[flight.option.flight_option.legs[0].hops.length-1].to}}</p>
                                                </div>
                                            </div>
                                            <div class="origin-destination hotel">
                                                <div class="destination-hotel" [ngStyle]="changeMobileDesign()">
                                                    <b>{{this.airlines[flight.option.flight_option.legs[0].hops[0].carrier]}} Airline</b>
                                                    <div *ngIf="this.isMobile" class="origin-destination1" style="display: flex;">
                                                        <p class="">{{getDisplayDateTimeForFlights(flight.option.flight_option.legs[0].hops[0].starts,'h:mm a')}} -</p>
                                                        <p class="">{{getDisplayDateTimeForFlights( getLastFlightLegsAndHope(flight.option.flight_option.legs).ends,'h:mm a')}}</p>
                                                    </div>
                                                </div>
                                                <div *ngIf="!this.isMobile" class="origin-destination1" style="display: flex;">
                                                    <p class="">{{getDisplayDateTimeForFlights(flight.option.flight_option.legs[0].hops[0].starts,'h:mm a')}} -</p>
                                                    <p class="">{{getDisplayDateTimeForFlights(getLastFlightLegsAndHope(flight.option.flight_option.legs).ends,'h:mm a')}}</p>
                                                </div>
                                            </div>
                                        </div>
                                         <div class="result-card-box-inner" *ngIf="flight.type =='hotel'">
                                            <div class="booking-details">
                                                <div class="booking-details-inner">
                                                    <div class="booking-img">
                                                        <img src="assets/images/hotel-circle.png" />
                                                    </div>
                                                    <div class="booking-text">
                                                        <p style="margin-right: 2px;">{{'bookingHistory.CheckIn' | translate}}</p>
                                                        <p class="font-bold">
                                                            {{getDisplayDate(flight.option.hotel_option.checkInDate,'EE MMM d, yyyy')}}</p>
                                                        <!-- <p class="font-bold">  {{flight.option.hotel_option.checkInDate | date:'EE MMM d'}}</p> -->
                                                    </div>
                                                </div>
                                                <div class="destination-hotel">
                                                    <p class="hoteldetail primary-text">
                                                        {{flight.option.hotel_option.hotelRooms[0].hotelRoomName}}</p>
                                                    <span style="white-space: nowrap;">{{flight.option.hotel_option.stay}}-{{flight.option.hotel_option.stay
                                                        > 1 ? ('bookingHistory.Nights' | translate)
                                                        :('bookingHistory.Night' | translate)}}</span>
                                            
                                                </div>
                                            </div>
                                            <div class="origin-destination hotel">
                                                <div class="destination-hotel" [ngStyle]="changeMobileDesign()">
                                                    <p class="font-bold"> {{flight.option.hotel_option.hotelName}}</p>
                                                    <div *ngIf="this.isMobile" class="origin-destination1">
                                            
                                                        <p>{{flight.option.hotel_option.address}}</p>
                                                    </div>
                                                </div>
                                                <div *ngIf="!this.isMobile" class="origin-destination1">
                                                    <p>{{flight.option.hotel_option.address}}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="result-card-box-inner" *ngIf="flight.type =='cars'">
                                            <div class="booking-details">
                                                <div class="booking-details-inner">
                                                    <div class="booking-img">
                                                        <img src="assets/images/car-circle.png" />
                                                    </div>
                                                    <div class="booking-text">
                                                        <p style="margin-right: 2px;">{{'bookingHistory.Pick-update'| translate}}</p>
                                                        <p *ngIf="flight.option.car_option.pickUpDate!==null" class="font-bold">
                                                            {{getDisplayDateTimeForFlights(flight.option.car_option.pickUpDate,'EE MMM d, yyyy')}}</p>
                                                        <!-- <p class="font-bold">  {{flight.option.hotel_option.checkInDate | date:'EE MMM d'}}</p> -->
                                                    </div>
                                                </div>
                                                <div class="destination-hotel">
                                                    <p class="primary-text">{{flight.option.car_option.description}}</p>
                                                    <p style="white-space: nowrap;">{{flight.option.car_option.numberOfDay}}-{{flight.option.car_option.numberOfDay > 1 ? ('bookingHistory.Days'| translate): ('bookingHistory.Day'| translate) }}</p>
                                                </div>
                                            </div>
                                            <div class="origin-destination hotel">
                                                <div class="destination-hotel" [ngStyle]="changeMobileDesign()">
                                                    <p class="font-bold">{{flight.option.car_option.partnerName}}</p>
                                                    <div *ngIf="this.isMobile" class="origin-destination1">
                                                        <p>{{flight.option.car_option.pickUpLocation}}</p>
                                                    </div>
                                                </div>
                                                <div *ngIf="!this.isMobile" class="origin-destination1">
                                                    <p>{{flight.option.car_option.pickUpLocation}}</p>
                                                </div>
                                            </div>
                                        </div>
                                     </div>
                                     <div class="bottomcontainer" >
                                        <div class="mobileBtton">
                                            <div class="origin-destination1" style="display: block;">
                                                    <div [ngStyle]="channgeAlignment()" class="showinUSerdetails"><span style="font-family:var(--globalFontfamilyr);font-weight: bold;">{{'itinerary.Traveller'| translate}}: </span> <span style="font-family: var(--globalFontfamilyr);">{{getPrimaryTravellername(item)}}</span></div>
                                                                
                                                                <div  class="" style="color: rgb(249, 61, 48);font-size: 14px;white-space: nowrap;padding-top: 2px; padding-right: 35px;">
                                                                    {{'approval.Approval' | translate }} {{'dashboard.expiresin' | translate }} 
                                                                    <span *ngIf="getTimeDiffernece(item)?.hrs > 0">{{getTimeDiffernece(item)?.hrs + 'h '}}</span> 
                                                                    <span *ngIf="getTimeDiffernece(item)?.mins > 0">{{ getTimeDiffernece(item)?.mins + 'm'}}</span>
                                                                </div>
                                                            
        
                                                </div>
                                                <div *ngIf="this.isMobile" class="booking-view-button">
                                                        <button class="btn primary-button"
                                                            (click)="getDetailView(item,false)">{{'fuild.View'
                                                            | translate}}</button>
                                                    </div>
                                        </div>
                                        <div class="origin-destination1">

                                            <span [ngStyle]="channgeAlignment()" class="showinUSerdetails">
                                                <span style="font-family: var(--globalFontfamilyr);font-weight: bold;">{{'approval.Approver' | translate}}: </span> <span style="font-family: var(--globalFontfamilyr);">{{getApproversByData(item)}}</span>
                                            </span>

                                        </div>
                                        <div *ngIf="!this.isMobile" class="booking-view-button">
                                            <button class="btn primary-button"
                                                (click)="getDetailView(item,false)">{{'fuild.View'
                                                | translate}}</button>
                                        </div>
                                 </div>
                                     
                                 </div>
                             </div>
                         </div> 
                        </div>
                        <!-- <div *ngIf="approvallist.length > 0  && responseNotCame" class="row"
                                style="text-align: center;padding-top:20px;padding-left:15px;">
                                <app-loader *ngIf="responseNotCame" style="margin-left: auto;margin-right: auto;"
                                  [spinnerStyle]="true"></app-loader>
                        </div> -->
                      </div>
                    </div>
                  </div>
                </div>
            
  
  
              </div>
            </div>
            <app-approval-detail *ngIf="showApprovalDetails"  (goBackEmitter)='handleApprovalChange($event)'></app-approval-detail>
          </div>
        </div>
      </div>
    </div>
  </section>

  <app-navigation *ngIf="!showApprovalDetails"></app-navigation>