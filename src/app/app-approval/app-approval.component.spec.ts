import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AppApprovalComponent } from './app-approval.component';

describe('AppApprovalComponent', () => {
  let component: AppApprovalComponent;
  let fixture: ComponentFixture<AppApprovalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ AppApprovalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppApprovalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
