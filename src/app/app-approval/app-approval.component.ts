import { Component, OnInit } from '@angular/core';
import { CommonUtils } from '../util/common-utils';
import { TranslateService } from '@ngx-translate/core';
import { DateUtils } from '../util/date-utils';
import { AdminPanelService } from '../admin-panel.service';
import { UserAccountService } from '../user-account.service';
import { CompanyReportResponse } from '../entity/company-report-response';
import { Subscription } from 'rxjs';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { SearchActionType } from '../enum/search-action.type';
import { DeviceDetailsService } from '../device-details.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { SearchService } from '../search.service';
import { debug } from 'console';

@Component({
    selector: 'app-app-approval',
    templateUrl: './app-approval.component.html',
    styleUrls: ['./app-approval.component.scss'],
    standalone: false
})
export class AppApprovalComponent implements OnInit {

  constructor(private searchService: SearchService,public progressBar: NgxUiLoaderService,  private titleService: Title,  private activatedRoute: ActivatedRoute,
    private deviceDetailsService: DeviceDetailsService, public router: Router,public translateService: TranslateService,  public userAccountInfoService: UserAccountService,private adminPanelService: AdminPanelService,) { }
  airports;
  responseNotCame=true;
  airlines;
  showApprovalDetails = false;
  approvallist=[];
  approvers: any;
  isMobile: boolean;
  apiReportResponse: CompanyReportResponse;
  queryParmsSubscription: Subscription;
  companyApprovalSubscription: Subscription;
  
  deviceSubscription: Subscription;
  previousDate = '';
  users ;
  originalApprovalsList = [];
  resultPendingErrorMessage = '';
  startDate: Date = new Date();
  endDate: Date = new Date();
  errormsg3='';
  ngOnInit(): void {
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.PendingApprovals'));
  //  this.initializeQueryParamsfromRoute();
    this.subscription();
    this.queryParmsSubscription =  this.activatedRoute.queryParams.subscribe(params => {
      this.setParameters(params);
      // if(params['type'] == 'list')
      // this.bookingHistoryComponentInit();
    });
  }
  changeMobileDesign(){
    if(this.isMobile){
      return {'position': 'relative','top': '5px'}
    }else{
      return {'position': 'relative','top': '0px'}
    }
  }
  subscription(){
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });
  this.approvallist = [];
this.getApprovals();
setTimeout(() => {
  this.responseNotCame= true;
this.companyApprovalSubscription = this.adminPanelService.companyApprovalResponseObservable$.subscribe((reportResponse) => {
  this.progressBar.stop(SearchActionType.DETAIL);
  if (reportResponse) {
    //  this.originalApprovalsList=[];
   // if (this.viewMode2 === 'tab21') {
    this.showSecondLoader =false;
      if (reportResponse.pendingApprovals.length > 0) {
        this.errormsg3 = this.translateService.instant('report.FetchingData');
        this.resultPendingErrorMessage = '';
      } else {
        this.resultPendingErrorMessage = this.translateService.instant('approval.Yay');
this.errormsg3 =this.translateService.instant('approval.Youdonthaveanypendingapprovals');
      }

    this.apiReportResponse = reportResponse;
    this.users = reportResponse.users
    this.airlines = reportResponse.airlineNames;
    this.responseNotCame= false;
    this.buildCompanyReportData(reportResponse);
  } else {
    this.showSecondLoader =false;
    this.responseNotCame= false;
  }
});
}, 2000);
  }
 

  private setParameters(params) {
    if (params['transactionid']) {
     this.showApprovalDetails =true;
     this.searchService.showApprovalaInMenu =true;
    }else{
      this.showApprovalDetails =false;
    }
  }
  ngOnDestroy() {
    
    if (this.companyApprovalSubscription) {
      this.companyApprovalSubscription.unsubscribe();
    }
    if(this.queryParmsSubscription){
      this.queryParmsSubscription.unsubscribe();
    }
    this.adminPanelService.originalTravellerResponse = undefined;
    this.adminPanelService.waitingForCompanySettings = false;
    this.adminPanelService.wdrdata = [];
  }
  showSecondLoader =false;
  handleApprovalChange(event){
    if(event){
      this.showApprovalDetails =false;
      this.showSecondLoader =true;
      this.approvallist =[];
      this.getApprovals();
    }
  }
  getApprovals() {
    this.startDate.setDate(this.startDate.getDate() -1);
    this.adminPanelService.firstApprovalDate1 = this.startDate;
    this.adminPanelService.lastApprovalDate1 = this.endDate;
    let startDate1: Date = new Date();
    let endDate1: Date = new Date();
    endDate1.setDate(endDate1.getDate() + 1);
    startDate1.setDate(startDate1.getDate() -1);
    let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(startDate1);
    let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(endDate1);
    let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
    let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
    this.errormsg3 = this.translateService.instant('report.FetchingData');
    let searchDates = startDate + 'T' + endDate;
    this.responseNotCame = true;
    // this.adminPanelService.filterAppliedEmployeeId = this.empValue;
    if (this.previousDate === searchDates) {
      this.adminPanelService.filterApprovalByEmployee('all');
    } else {
      this.showPenndingApprovals =false;
      this.adminPanelService.fetchCompanyApprovals(this.userAccountInfoService.getUserCompanyId(), startDate, endDate, 'all');
    }
    this.previousDate = startDate + 'T' + endDate;
  }
  getUserBookingStatus(status: string, type: string, item: any,) {
    return CommonUtils.getStatusLabel(type, status, item,this.translateService);
  
  }
  channgeAlignment() {
    if (this.isMobile) {
     
        return { 'text-align': 'left' };
     
    }
  }
  showPenndingApprovals = false;
  allowAllAdminApprovals(event) {
    if (event) {
      this.showPenndingApprovals = event;
      this.adminPanelService.showPenndingApprovals = event;
      this.approvallist = [];
      const localList = [...this.originalApprovalsList];
      this.approvallist = this.filterbyTripSessionId(localList);
    } else {
      this.showPenndingApprovals = event;
      this.adminPanelService.showPenndingApprovals = event;
      this.approvallist = [];
      const localList = this.filterSelfApprovalList(this.approvers);
      this.approvallist = this.filterbyTripSessionId(localList);
      if (this.approvallist.length === 0) {
        this.errormsg3 = this.translateService.instant('report.NoData');

      }
    }
  }
  isUserCompanyManager() {
    return this.userAccountInfoService.isUserCorporateAdmin();
  }
  private buildCompanyReportData(reportResponse: CompanyReportResponse) {
    this.approvallist = [];
    this.originalApprovalsList = [];
    if(reportResponse.airports){
    this.airports = reportResponse.airports
    }
    for (let optionItem of reportResponse.pendingApprovals) {
      let reportItem = {};
      this.approvers = reportResponse.approvers;
      reportItem['approvalExpiryTime'] = optionItem.approvalExpiryTime;
      reportItem['ticketid'] = optionItem.ticketid;
      reportItem['tripid'] = optionItem.tripid;
      reportItem['groupTravelEventId'] = optionItem.groupTravelEventId;
      reportItem['option'] = optionItem.option;
      reportItem['selectTransId'] = optionItem.option.selectTransId;
      reportItem['name'] = reportResponse.users[optionItem.userid].userName;
      reportItem['bookingDate'] = optionItem.bookingDate;
      reportItem['type'] = optionItem.type;
      reportItem['userid'] = optionItem.userid;
      reportItem['approvers'] = this.approvalUsers(reportResponse.users, optionItem.userid,reportItem['groupTravelEventId']);
      reportItem['allTravellers'] = optionItem['allTravellers'];
      reportItem['primaryTraveller'] = optionItem['primaryTraveller'];
      if(optionItem["tripSessionId"] != undefined){
        reportItem['tripSessionId'] = optionItem["tripSessionId"];
      }
      if (optionItem.type === 'flight') {
        reportItem['departure'] = optionItem.option.flight_option.legs[0].hops[0].starts;
        let firstLeg = optionItem.option.flight_option.legs[0];
        let destination = firstLeg.hops[firstLeg.hops.length - 1].to;
        if (reportResponse.airports[destination]) {
          destination = reportResponse.airports[destination].city;
        }
        // let destination = optionItem.option.flight_option.legs[0].hops[0].from;
        for (let i = 1; i < optionItem.option.flight_option.legs.length - 1; i++) {
          let thisleg = optionItem.option.flight_option.legs[i];
          let lastHop = thisleg.hops[thisleg.hops.length - 1];
          if (reportResponse.airports[lastHop.to]) {
            destination = destination + ", " + reportResponse.airports[lastHop.to].city;
          } else {
            destination = destination + ", " + optionItem.option.flight_option.legs[i].hops[thisleg.hops.length - 1].to;
          }
          // destination = destination+ ", " +lastHop.to;
        }
        reportItem['destinations'] = destination;
      } else if (optionItem.type === 'hotel') {
        reportItem['departure'] = optionItem.option.hotel_option.checkInDate;
        reportItem['return'] = optionItem.option.hotel_option.checkOutDate;
        reportItem['destinations'] = optionItem.destinationCity;
      } else if (optionItem.type === 'cars') {
        if (optionItem.option.car_option && optionItem.option.car_option.pickUpDate) {
          reportItem['departure'] = optionItem.option.car_option.pickUpDate;
        }
        if (optionItem.option.car_option && optionItem.option.car_option.dropOffDate) {
          reportItem['return'] = optionItem.option.car_option.dropOffDate;
        }
        reportItem['destinations'] = optionItem.destinationCity;

      }
      this.originalApprovalsList.push(reportItem);
    }
    let uniqueUserIds = {};
   
    if (this.originalApprovalsList.length > 0) {

      const localList = this.filterSelfApprovalList(reportResponse.approvers);
      this.approvallist = this.filterbyTripSessionId(localList);
      if (this.approvallist.length === 0) {
        this.errormsg3 = this.translateService.instant('report.NoData');
      }
    }
   
  }
  getDisplayDate(dateString: string, format: string): string {
    return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
  }
  getDisplayDateTimeForFlights(dateString: string, format: string): string {
    return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }
  getAirportCity(code) {

    return code && this.airports[code] && this.airports[code]['name'] ? this.airports[code].name : code;
  }
  approvalUsers(users, userid,eventid) {
    let approvers = [];
    for (let key in this.approvers) {
      if(eventid && eventid===key){
        let approverList = this.approvers[key];
        for (let item of approverList) {
          for (let key1 in users) {
            let approverdetail = users[key1];
            if (key1 === item) {
              approvers.push(approverdetail.userName);
            }
          }
        }
      }else if (!eventid && userid === key) {
        let approverList = this.approvers[key];
        for (let item of approverList) {
          for (let key1 in users) {
            let approverdetail = users[key1];
            if (key1 === item) {
              approvers.push(approverdetail.userName);
            }
          }
        }
      }

    }
    return approvers;
  }
  filterSelfApprovalList(approver) {
    let companyReport = this.originalApprovalsList.filter(item => {
      let userid = item.userid;
      let approverid = this.userAccountInfoService.getUserEmail();
      for (let key in approver) {
        if (userid === key) {
          let approverList = approver[key];
          for (let approver of approverList) {
            if (approver === approverid) {
              return true;
            }
          }
        }
      }
    })
    return companyReport;
  }
  getApprovers(item){
    let approvers = item.join(', ');
    return approvers;
  }
  getApproversByData(data){
    const item = data.data[0].approvers
    let approvers = item.join(', ');
    return approvers;
  }
  getTimeDiffernece(data) {
    let time = 0;
    for(let counter=0; counter < data.data.length; counter++) {
      if ( data.data[counter].approvalExpiryTime > time) {
        time = data.data[counter].approvalExpiryTime;
      }
    }
    let bookingTime = new Date(time);
    let currentTime = new Date();
    let durationInMins = DateUtils.getDateDiffInMinutes(bookingTime, currentTime);
    //);
    return DateUtils.getDurationAsHrsMinObj(durationInMins);
  }
  getPrimaryTravellername(e){
    let data = e.data[0];
    if(data && data.allTravellers){
      const trevelers = data['allTravellers'];
       if(trevelers && trevelers.length > 0){
         let string = '';
         trevelers.forEach((a,i)=>{
           string += a.userName;
           string += i == trevelers.length -1 ? "":", "
         })
         return string;
       }else{
         return false;
       };
    }else{
     if(data && data.primaryTraveller){
       const trevelers = data['primaryTraveller'];
       return trevelers.userName
     }else{
       false
     };
    };
  }
  
  getDetailView(item,item1?){
      
      let bookedOption = item.data[0];
      
      this.previousDate ='';
      this.showApprovalDetails = true;
      //this.adminPanelService.gotoDetail = true;
      this.adminPanelService.gotoAppApproval = true;
      let name = bookedOption.name;
      if(bookedOption.tripSessionId != undefined){
        this.router.navigate(["approval"],
          {
            queryParams:
            {
              view: 'approval',
              type: 'list',
              name: name,
              bookingType: 'pending',
              userEmail: bookedOption.userid,
              ticketid: bookedOption.ticketid,
              tripid: bookedOption.tripid,
              transactionid: bookedOption.selectTransId,
              tripSessionId:bookedOption.tripSessionId
            },
            replaceUrl: true
          }
        );
      }else{
        this.router.navigate(["approval"],
          {
            queryParams:
            {
              view: 'approval',
              type: 'list',
              name: name,
              bookingType: 'pending',
              userEmail: bookedOption.userid,
              ticketid: bookedOption.ticketid,
              tripid: bookedOption.tripid,
              transactionid: bookedOption.selectTransId
            },
            replaceUrl: true
          }
        );
      }
    }

    filterbyTripSessionId(item){
      let getBysession = [];
    
    item.forEach((a,i)=>{
      if(a.tripSessionId){
        let find = getBysession.find((e) => a.tripSessionId == e.tripSessionId);
  
        if(find == undefined || i == 0 ){
          let obj = {"data":[],"tripSessionId":""};
  
         item.forEach((b) =>{
            if(obj.data.length > 0){
              const ticketIdFind = obj.data.find(a => a.ticketed == b.ticketId );
              if(obj.data[0].tripSessionId == b.tripSessionId && ticketIdFind == undefined){
                if(a.tripSessionId == b.tripSessionId){
                  let findDummy = obj.data.find((c) => b.ticketid == c.ticketid);
                  if(findDummy == undefined){
                    obj.data.push(b);
                  };
                };
              }else{
                const ticketIdFind = obj.data.find(a => a.ticketed == b.ticketId );
                if(obj.tripSessionId == b.tripSessionId && ticketIdFind){
                  // 
                  obj.data.push(b);
                }
              }
            }else{
              if(getBysession.length > 0){
                let newfind = getBysession.find((e) => b.tripSessionId == e.tripSessionId);
                if(newfind == undefined){
                  obj.data.push(b);
                  obj.tripSessionId = b.tripSessionId;
                };
              }else{
                obj.data.push(b);
                  obj.tripSessionId = b.tripSessionId;
              }
            };
          });
          // 
          getBysession.push(obj);
        }else{
          // 
        }
      }else{
        let obj = {"data":[a]};
        getBysession.push(obj);
      }
    });
    // 
    return getBysession;
    }
  
    getLastFlightLegsAndHope(legs){
      let hopesIndex = legs[0].hops.length - 1;
      return legs[0].hops[hopesIndex];

    }

    log(e){
      
    }
}
