@import "../../variables.scss";

:host {
  width: 100vw;
}

.font-bold {
  font-family: $fontBold;
}
.font-bold1 {
  font-family: $fontBold;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.hoteldetail{
  text-overflow: ellipsis;
    overflow: hidden;

    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
.styelSwitch1 {
  position: relative;
  z-index: 10;
  left: -55px;
  top: -9px;
  color: #fff;
  font-size: 13px;
  font-weight: bolder;
  font-family: var(--globalFontfamilyr);
  font-weight: bold;
  cursor: pointer;
  text-transform: uppercase;
  width: 25px;
  display: inline-block;
}
.mobileBtton{
  display: contents;
}
.destination-hotel{
  // min-width: 190px;
  // max-width: 190px;
  width: 100%;
  padding-right: 20px;
}
.bottomcontainer{
  display: flex;
  background: #fff;
    float: left;
    width: 100%;
    padding: 12px 32px 16px;
  justify-content: space-between;
  background-color: #F7F7F7;
  align-items: center;
}
.switchLabel {
  font-size: 16px;
  line-height: 35px;
  white-space: nowrap;
  text-align: right;
  font-family: "apercu-r";
  padding-left: 0px;
  position: relative;
  display: inline-block;
 
  margin-right: 10px;
 
}
.styelSwitch2 {
  position: relative;
  z-index: 10;
  left: -35px;
  top: -9px;
  color: #fff;
  font-size: 13px;
  font-weight: bolder;
  font-family:var(--globalFontfamilyr);font-weight: bold;
  cursor: pointer;
  text-transform: uppercase;
  width: 25px;
  display: inline-block;
}

:host ::ng-deep.switch-medium.checked small {
  height: 22px !important;
  width: 22px !important;
  margin-top: 1px !important;
  background-color: var(--hyperlink-color) !important;
  border: none !important;
  box-sizing: none !important;
  left: 37px !important;
}

:host ::ng-deep.switch.switch-medium.checked {
  border: 1px solid var(--dark-bg-color) !important;
}
:host ::ng-deep.switch.switch-medium small {
  width: 30px;
  height: 30px;
  right: calc(100% - 20px) !important;
}
:host ::ng-deep.switch.switch-medium>.switch-pane .switch-label-unchecked {
  padding-left: 35px;
  padding-right: 15px;
  color: white;
  line-height: 23px;
  font-size: 13px;
  font-family:var(--globalFontfamilyr);font-weight: bold;
}
:host ::ng-deep.switch.switch-medium > .switch-pane .switch-label-checked {
  padding-left: 15px;
  padding-right: 35px;
  color: white;
  line-height: 23px;
  font-size: 13px;
  font-family: "apercu-b";
}
:host ::ng-deep.switch {
  height: 24px !important;
  margin-top: 5px !important;
  width: 60px !important;
  border-radius: 30px !important;
  background-color: gray !important;
}

:host ::ng-deep.ng-select .ng-select-container {
  color: #333;
  cursor: default;
  display: flex;
  outline: 0;
  overflow: hidden;
  position: relative;
  width: 100%;
  bottom: 8px;
}

:host ::ng-deep.switch-medium small {
  height: 22px !important;
  width: 22px !important;
  box-sizing: border-box;
  margin-top: 1px !important;
  border: 1px solid #979797;
}
.top-strip {
  background: $themeColor2;
  float: left;
  width: 100%;
  height: 43px;
}

.tab-content {
  float: left;
  width: 100%;
  padding: 0 0px;
}

.image11 {
  width: 45px;
  height: 45px;
}

.primary-button{
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}
.view {
  height: 29px;
  width: auto;
  color: var(--dark-bg-color) !important;
  font-family: "apercu-r";
  font-size: 24px;
  line-height: 5px;
  padding-top: 18px;
}

:host ::ng-deep {
  #chartDepartment .ng-select.ng-select-single .ng-select-container .ng-clear-wrapper {
    bottom: 18px !important;
    font-size: 28px !important;
    color: var(--dark-bg-color) !important;
    display: inline-block !important;
    left: 2px;
  }

  #chartDepartment .ng-select .ng-select-container .ng-value-container {
    -ms-flex-align: stretch;
    align-items: stretch;
    padding: 0.4375em 0;
    border-top: 0.14375em solid transparent !important;
  }

  

  

  .ng-option.ng-option-selected {}

  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
    font-size: 16px !important;
    font-family: var(--globalFontfamilyr) !important;
    // max-height: 32px !important;
    line-height: 2em !important;
    padding-left: 25px !important;
    border-radius: 8px !important;
  }

  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {

    font-size: 16px !important;
  }

  .ng-select .ng-select-container {
    font-size: 16px !important;
    bottom: 4px !important;
    left: -15px !important;
    white-space: nowrap;
  }

  .ng-clear-wrapper {
    display: none !important;
  }

  .ng-select span {
    box-sizing: border-box;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    display: none !important;
  }

  .ng-select.ng-select-container.ng-value-container.ng-input {
    position: fixed !important;
    top: -14px;
  }

  .ng-select.ng-select-single .ng-select-container .ng-value-container,
  .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
    overflow: hidden !important;
    display: list-item !important;
  }

  .ng-dropdown-panel.ng-select-bottom {
    border: 1px solid var(--dark-bg-color) !important;
  }

  .ng-select-container {
    border: none !important;

    &:after {
      display: none;
    }
  }
}

.input {
  cursor: pointer;
  text-align: center;
  width: 100%;
  padding-top: 5px;
  padding-left: 25px;
  background: #FCFCFC;
  border: 1px solid #8936F3;
  border-radius: 8px;
  height: 32px;
  text-align: left;
  font-size: 14px;
}

.down-arrow {
  position: absolute;
  top: 16px;
  right: 5px;
}

.view12 {
  position: absolute;
  top: 8px;
  margin-left: 45px;
}

.line1 {
  border-right: 2px solid #000000;
  margin-right: 15px !important;
  margin-left: 10px !important;
}

.input-textfield-lg {
  width: 100% !important;
}

.font-bold {
  font-family: $fontBold;
}
.custom-selectbox {
  cursor: pointer;
  position: relative;
  display: inline-block;
  padding-right: 15px;
  margin-right: 5px;
  top: -20px;
}

.labelTime{
  position: relative;
  top: 0px;
  margin-right: 5px;
}
.labelBookingType{
  position: relative;
  top: 10px;
  margin-right: 5px;
}
.down-Arrow{
  position: absolute;
  left: 220px;
  top: 12px;
}
.down-arrow{
  left: 225px;top: -28px;position: relative;
}
.dateShow {
  position: absolute;
  white-space: nowrap;
  left: 10px;
  font-size: 16px;
  top: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.filter-row{
    display: flex;
    justify-content: space-between;
}
.input {
  cursor: pointer;
  text-align: center;
  padding-top: 5px;
  width: 253px;
  padding-left: 25px;
  background: #FCFCFC;
  border: 1px solid #8936F3;
  border-radius: 8px;
  height: 32px;
  text-align: left;
  font-size: 14px;
}
.input-box {
  height: 33px;
  cursor: pointer;
  border: none;
  border-radius: 6px;
  background-color: #fff;
  font-size: 14px;
}

.custom-selectbox .field-value {
  color: var(--hyperlink-color);
  font-size: 14px;
  margin-left: 50px !important;
}

.custom-selectbox .control-icon {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.top-strip {
  background: $themeColor2;
  float: left;
  width: 100%;
  height: 43px;
}
:host ::ng-deep {
 
 
 
  .ng-dropdown-panel {
    left: 50% !important;
    -ms-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
    max-height: 300px;
    position: absolute !important;
    min-width: 100% !important;
    top: auto !important;
    right: auto !important;
    max-width: 100% !important;
  }


  



  .ng-option.ng-option-selected {}

  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
    font-size: 16px !important;
    font-family: var(--globalFontfamilyr) !important;
    // max-height: 32px !important;
    line-height: 2em !important;
    padding-left: 25px !important;
    border-radius: 8px !important;
  }

  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {

    font-size: 16px !important;
  }

  .ng-select .ng-select-container {
    font-size: 16px !important;
    bottom: 4px !important;
    left: -15px !important;
  }

  .ng-clear-wrapper {
    display: none !important;
  }

  .ng-select span {
    box-sizing: border-box;
  }

  .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    display: none !important;
  }

  .ng-select.ng-select-container.ng-value-container.ng-input {
    position: fixed !important;
    top: -14px;
  }

 
   .ng-select.ng-select-single .ng-select-container .ng-value-container,
  .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
    position: relative !important;
    top: -9px !important;
    white-space: nowrap;
    text-overflow: ellipsis !important;
    overflow: visible ;
    max-width: 210px;
  }
 
  .ng-dropdown-panel.ng-select-bottom {
    border: 1px solid var(--dark-bg-color) !important;
  }

  .ng-select-container {
    border: none !important;

    &:after {
      display: none;
    }
  }
}

.search-box {
  width: 180px;
  padding: 0 17px 0 17px;
  font-size: 12px;
  line-height: 15px;
  text-align: left;
  height: 40px;
  border: 1px solid #E4E4E4;
  border-radius: 6px;
  padding-right: 30px;
  background-color: #F7F7F7;

}
.modalAirportFilterInfo .modal-header::-webkit-scrollbar {
  display: none;
}
.dateShow1 {
  position: absolute;
  white-space: nowrap;
  font-size: 16px;
  right: calc((100% - 650px)/2);
  top: 6px;
  color: gray;
}
.select {
  font-family: "apercu-b" !important;
  position: relative;
  font-size: 20px;
  text-align: left;
  line-height: 45px;
  color: #8936F3 !important;
  width: auto !important;
  display: inline-block;
  margin-right: 15px !important;
  font-weight: 700;
  margin-left: 15px !important;
  margin-bottom: 0px;
  padding-bottom: 0px;
  cursor: pointer;

}



.unselect {
  font-family: "apercu-r" !important;
  display: inline-block;
  text-align: left;
  color: #8936F3 !important;
  margin-right: 15px;
  margin-left: 15px;
  margin-bottom: 0px;
  padding-bottom: 0px;
  font-size: 20px;
  width: auto !important;
  line-height: 30px;
  font-weight: 400;
  position: relative;
  cursor: pointer;
}

.modalAirportFilterInfo {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modalAirportFilterInfo .modal-content {
  border: none;
  border-radius: 6px;
}

.modalAirportFilterInfo .close {
  text-shadow: none;
  color: #fff;
  opacity: 1;
}

.modalAirportFilterInfo .modal-header {
  background-color: var(--hyperlink-color);
  color: #FFFFFF;
  font-size: 14px;
  height: 40px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 6px 6px 0 0;
  padding: 0 8px 0 22px;
  border-bottom: none;
}

.modalAirportFilterInfo .modal-header h5 {
  font-size: 14px;
  width: 100%;
}

.modalAirportFilterInfo .modal-footer {
  padding: 0;
  border-top: none;
  float: left;
  width: 100%;
}

.modalAirportFilterInfo .close i {
  font-size: 17px;
}

.modalAirportFilterInfo .close:hover {
  color: #fff !important;
  opacity: 1 !important;
}
.statusDrop{
  margin-top: 5px;margin-left: 0px;width: 253px;
}
.filter-modal1 {
  width: 253px;
  position: absolute;
  z-index: 1000;
  background: #fff;
  border-radius: 8px;
  left: calc((100% - 270px)/2) !important;
  top: 59px;
  border: 1px solid var(--dark-bg-color);
}

.tab-content {
  float: left;
  width: 100%;
  padding: 0 0px;
}

.tab-list-item {
  display: inline-block;
  margin-right: 60px;
  padding-bottom: 0;
  margin-bottom: 0;
}

.tab-list-item a {
  cursor: pointer;
  position: relative;
  display: inline-block;
  margin-left: 20px;
  color: #FFFFFF;
  font-size: 12px;
  opacity: 0.6;
  line-height: 15px;
  padding: 10px 5px 13px 5px;
}

.tab-list-item a img {
  margin-right: 10px;
}

.tab-list-item.active a {
  opacity: 1;
}

.tab-list-item.active a::after {
  position: absolute;
  content: "";
  bottom: 2px;
  background: #fff;
  left: 0;
  right: 0;
  width: auto;
  height: 3px;
}

.showUserDetail {
  display: block;
  overflow: hidden;
  display: inline-block;
  width: 100%;
  text-overflow: ellipsis;
}

.showinUSerdetails {
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  font-family:var(--globalFontfamilyr);font-weight: bold;
  width: 100%;
  text-align: left !important;
  vertical-align: middle;
  padding-right: 0px;
}

.tab-list-item12 {
  font-family: "apercu-r" !important;
  display: inline-block;
  text-align: left;
  color: #595959 !important;
  margin-right: 15px;
  margin-left: 15px;
  margin-bottom: 0px;
  padding-bottom: 0px;
  text-transform: uppercase;
  font-size: 18px;
  width: auto !important;
  line-height: 30px;
  font-weight: 400;
  cursor: pointer;
}

.tab-list-item12.active a {
  opacity: 1;
}

.tab-list-item11 {
  font-family: "apercu-b" !important;
  position: relative;
  text-transform: uppercase;
  font-size: 18px;
  text-align: left;
  line-height: 30px;
  color: var(--dark-bg-color) !important;
  text-decoration: underline;
  width: auto !important;
  display: inline-block;
  margin-right: 15px !important;
  font-weight: 700;
  margin-left: 15px !important;
  margin-bottom: 0px;
  padding-bottom: 0px;
  cursor: pointer;

}

.readonly-thumbs {
  width: 25px;
  height: 25px;
  margin-left: 25px;
}

.expense-image {
  height: 30px;
  width: auto;
  padding-bottom: 7px;
}

.bed-image {
  height: 60px;
  width: 60px;
}

.feature {
  text-align: left;
  padding-left: 0px;
  width: 100%;
}

.feature1 {
  padding-left: 0px;
  margin-top: 15px;
  margin-bottom: 15px;
  display: inline-block;
}

.admin-name {
  font-size: 15px;
  color: var(--hyperlink-color) !important;
  padding-right: 12px;
  font-family: "apercu-r";
}

.tab-content-item {
  float: left;
  width: 100%;
}

.result-card-box {
  position: relative;
  cursor: pointer;
  background: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
  border-radius: 6px;
  font-family: $fontRegular;
  float: left;
  width: 100%;
  margin-bottom: 16.37px;
}

.header-left {
  text-align: right;
  width: 50% !important;
}

.header-right {
  text-align: left;
  width: 50% !important;
}

.header-inner {
  padding: 0px 0px 0px 0px;
  margin-bottom: 0px;
  height: 30px;
}

.mdl-radio__label {
  font-size: 14px;
  font-weight: normal;
  font-family: $fontRegular;
  text-align: left
}

.mdl-radio__outer-circle {
  position: relative !important;
  border: 1px solid #979797;
  background-color: #FFFFFF;
}

.mdl-radio.is-checked .mdl-radio__outer-circle {
  border: 1px solid var(--button-bg-color);
}

.radiostyle1 {
  margin-right: 1px;
  width: 20px;
}

.result-card-box-inner {
  position: relative;
  z-index: 2;
  background: #fff;
  float: left;
  width: 100%;
  border-radius: 6px;
  padding: 12px 20px 16px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  justify-content: space-between;
  height: 105.63px;
  font-size: 14px;
}

.origin-destination.flight {
  justify-content: space-between;
}

.origin-destination.hotel {
  justify-content: space-between;
  align-items: center;
}

.origin-destination1.hotel {
  justify-content: space-between;
}

.booking-details {
  display: flex;
  border-right: none;
  width: 200px;
  height: 100%;
  justify-content: space-between;
  align-items: center;
}

.origin-destination {
  // width: calc(100% - 338px);
  width: 100%;
  display: flex;
  // padding: 0 20px;
}

.origin-destination1 {
  // width: calc(100% - 438px);
  width: 100%;

  display: flex;
  padding: 0 20px;
}

.booking-view-button {
  width: 63px !important;
  white-space: nowrap;
}

.booking-img {
  line-height: 12px;
  margin-right: 12px;
  width: 35px;
}

.origin-destination-section {
  width: calc(100% - 200px);
}

.origin-destination-switch {
  height: 28px;
  display: none;
  width: 28px;
  border: 2px solid #E7E6E4;
  background-color: #FFFFFF;
  border-radius: 50%;
  margin: 0 30px;
  justify-content: center;
  align-items: center;
}

.origin-destination-switch span {
  font-size: 10px;
  display: none;
  color: rgba(0, 0, 0, 0.3);
}

.booking-view-button button {
  height: 43px;
  width: 63px;
  font-size: 12px;
  letter-spacing: 1px;
  line-height: 18px;
}

.booking-text {}

.hotel-nights-count {
  font-family: $fontBold;
  padding-top: 10px;
  max-width: 60px;
  min-width: 60px;
}

.booking-container {
  float: left;
  width: 100%;
  padding: 21px 0;
}

.booking-history-detail {
  float: left;
  width: 100%;
}

.booking-history-detail-heading {
  float: left;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 2px 30px;
}

.booking-history-detail-heading h4 {
  color: #fff;
  font-size: 12px;
  letter-spacing: 0.51px;
  line-height: 15px;
  margin: 0;
  padding: 0;
}

.booking-history-heading-link {
  line-height: 10px;
  margin-right: 18px;
}

.booking-details-inner {
  align-items: center;
  display: flex;
  width: 100%;
}

.booking-history-detail-content {
  float: left;
  width: 100%;
  padding-bottom: 50px;
}

.booking-history-detail-content-inner {
  display: flex;
}

.booking-history-detail-content-left {
  float: left;
  width: 100%;
  max-width: 429.3px;
  font-family: $fontRegular;
  font-size: 14px;
  margin-right: 51.7px;
}

.booking-history-detail-content-right {
  padding-top: 60px;
}

.booking-detail-data {
  background: #fff;
  float: left;
  width: 100%;
  padding: 0 50px 31px 50px;
  border-radius: 6px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
}

.booking-detail-header {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  line-height: 16px;
  padding: 17px 8px 10px 8px;
}


.date-duration {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E3E3;
  padding: 11px 12px;
}

.date {
  font-family: $fontBold;
}

/*.duration{font-size: 16px;}*/
.duration label {
  font-family: $fontRegular;
  font-size: 12px;
}

.duration span {
  font-family: $fontBold;
}

.flight-box {
  float: left;
  width: 100%;
  display: flex;
  font-family: $fontRegular;
  padding: 10px 15px 12px 15px;
}

.flight-modal-name-duration {
  display: flex;
  justify-content: space-between;
  float: left;
  width: 100%;
}

.flight-modal-name {
  font-size: 16px;
}

.flight-modal-duration {
  font-size: 14px;
}

.flight-ticket-detail {
  float: left;
  width: 100%;
  color: #AEAEAE;
  font-size: 11px;
  margin-bottom: 6px;
}

.flight-timing-stops ul li {
  position: relative;
  margin-bottom: 7px;
  font-size: 12px;
  float: left;
  width: 100%;
  padding-left: 20px;
  line-height: normal;
}

.flight-timing-stops ul li label {
  display: inline-block;
  vertical-align: middle;
}

.flight-timing-stops ul li span {
  margin-left: 0px;
  display: block;
}

.flight-timing-stops ul li:before {
  height: 9px;
  width: 9px;
  border-radius: 50%;
  background-color: var(--hyperlink-color);
  position: absolute;
  left: 0;
  top: 4px;
  content: '';
}

.flight-layover {
  float: left;
  width: 100%;
  padding: 0 10px 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #EEEDEB;
  background-color: #EFFAFC;
  height: 37px;
  font-size: 14px;
}

.flight-layover-right {
  font-size: 14px;
}

.flight-box-right {
  padding-left: 18px;
}

.flight-modal-price {
  font-size: 19px;
  font-family: $fontMedium;
  line-height: 19px;
  color: var(--button-font-color);
}

.flight-modal-trip-type {
  color: #A6A5A4;
  font-size: 11px;
  line-height: 12px;
}

.seat-no {
  font-size: 12px;
  line-height: 14px;
  color: var(--button-font-color);
  font-family: $fontBold;
}



.flight-box-left {

  padding-top: 8px;

}

.price-section {
  float: left;
  width: 100%;
}

.price-section-content {
  float: left;
  width: 100%;
  border-top: 1px solid #E3E3E3;
  border-bottom: 1px solid #E3E3E3;
  padding: 5px 0;
}

.price-row {
  float: left;
  width: 100%;
  display: flex;
  align-items: center;
}

.price-row label {
  font-size: 12px;
  width: 134px;
  margin-right: 14px;
}

.price-row span {
  font-size: 14px;
  font-family: $fontMedium;
  width: 90px;
}

.price-section-total .price-row span {
  color: var(--button-font-color);
  font-size: 19px;
}

.price-section-total {
  float: left;
  width: 100%;
  padding: 6px 0;
}

.button-white {
  height: 44px;
  border: 1px solid #e9e9ed;
  background: #fff;
  border-radius: 6px;
  font-size: 15px;
  letter-spacing: 0.65px;
  line-height: 13px;
  color: var(--button-font-color);
  font-family: $fontMedium;
  width: 176px;
  display: -ms-flexbox;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 5px 0 9px;
  cursor: pointer;
}


.button-white img {
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
}

.button-white span {
  display: inline-block;
  vertical-align: middle;
}

.button-text {
  white-space: nowrap;
  font-size: 12px;
  letter-spacing: 1px;
  line-height: 13px;
  color: var(--button-font-color);
  font-family: $fontBold;
}

.button-text {
  font-size: 12px;
  letter-spacing: 1px;
  line-height: 13px;
  color: var(--button-font-color);
  font-family: $fontBold;
  background: none;
  border: none;
  padding: 0;
  white-space: nowrap;
}

.booking-button-text-container {
  border-top: 1px solid #E3E3E3;
  padding: 13px 30px;
  width: 240px;
}

.booking-button-text-container1 {
  border-top: 1px solid #E3E3E3;
  padding: 13px 30px;
  width: 130%;
}

.booking-history-detail-content-inner {
  max-width: 720px;
  margin: 0 auto;
}

.button-text {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.button-text img {
  margin-right: 13px;
}

.booking-button-container {
  margin-bottom: 18px;
  text-align: center;
}

.button-white-img {
  margin-right: 6px;
  width: 26px;
}

.marginTop {
  margin-top: 17px;
}

.input-textfield {
  padding: 5px 15px;
  color: #000000;
  font-size: 12px;
  width: 100%;
}

.modal-textarea {
  height: 111.46px;
  border: 1px solid #E4E4E4;
  border-radius: 6px;
  background-color: #F7F7F7;
  resize: none;
}

.footer-button {}

.booking-div-header h4 {
  font-size: 16px;
  padding-top: 20px;
  margin: 0;
  padding-bottom: 10px;
  font-family: $fontBold;
}

.modal-title {
  display: inline-block;
  float: left;
  width: 25% !important;
}

.booking-details {
  width: 100%;
}



.input-style {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;

}

.input-control {
  margin-top: 0px;
  cursor: pointer;
}

.checkbox_input {
  padding-left: 3px;
  margin-top: 10px;
  margin-bottom: 10px;
  border: 1px var(--button-font-color);
}

.mdl-checkbox__input {
  position: absolute;
  left: -9999px;
}

.mdl-checkbox__input:checked+.mdl-checkbox__label:after {
  background-image: url(/../../assets/images/check-icon.png);
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: center center;
}

.label1 {
  padding-top: 2px;
  padding-left: 10px;
  font-size: 14px;
  font-weight: bold;
  margin-top: 2px;
  margin-left: 5px;
}



.historyModal {
  min-width: 324px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modal-content {
  border: none;
  border-radius: 6px;
}

.close {
  text-shadow: none;
  color: #fff;
  opacity: 1;
}

.modal-header {
  background-color: var(--hyperlink-color);
  color: #FFFFFF;
  font-size: 14px;
  height: 40px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 6px 6px 0 0;
  padding: 0 8px 0 22px !important;
  border-bottom: none;
}

.modal-header h5 {
  font-size: 14px;
}

.modal-body {
  padding: 28px 25px 47px 25px;
  border-radius: 0 0 5px 5px;
}

.modal-footer {
  padding: 0;
  border-top: none;
}

.close i {
  font-size: 17px;
}

.close:hover {
  color: #fff !important;
  opacity: 1 !important;
}

.input-field {
  float: left;
  width: 100%;
  padding: 0;
}

.input-field label {
  float: left;
  width: 100%;
  font-size: 13px;
  color: #000000;
  line-height: 16px;
  margin-bottom: 14px;
}

.input-field input {
  float: none !important;
  width: 60%;
  border: 1px solid #E4E4E4;
  border-radius: 6px;
  background-color: #F7F7F7;
  height: 41px;
  font-size: 12px;
}

.modal-form-button {
  float: left;
  width: 100%;
  margin-top: 18.5px;
  padding-left: 12px;
}

.modal-form-button button {
  float: left;
  width: 100%;
  height: 46px;
  font-size: 12px;
  letter-spacing: 1px;
  font-weight: normal;
  font-family: $fontBold;
}

.modal-footer {
  background-color: #EFFAFC;
  border-radius: 0 0 6px 6px;
}

.modal-footer p {
  font-size: 12px;
  line-height: 19px;
  font-family: $fontRegular;
  padding: 18px 20px 33px 30px;
}

.modal-footer p span {
  font-family: $fontBold;
}

.requestModalBody.modal-body {
  padding-bottom: 15px;
}

.mdl-checkbox {
  float: none !important;
  width: 18px;
  font-weight: normal;
  cursor: pointer;
  margin-right: 8px;
  text-align: right;
  margin-bottom: 6px;
}

.empty-container {
  float: left;
  width: 100%;
  text-align: center;
  padding: 43px 0 43px 0;
}

.empty-img {
  float: left;
  width: 100%;
}

.empty-text {
  float: left;
  width: 100%;
  font-size: 18px;
  line-height: 22px;
  padding: 38px 0 50px;
  color: #18CFDD;
}

.empty-button {
  float: left;
  width: 100%;
}

.empty-button button {
  height: 46px;
  width: 296px;
  border-radius: 6px;
  max-width: 100%;
}

.button-text-img {
  width: 20px;
  margin-right: 12px;
}

.option-group {

  text-align: left;
  background: #fff;
  padding: 13px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
  font-family: $fontRegular;
  border-radius: 8px;

}

.option-group-list-item {
  float: left;
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.option-group-list-item:last-child {
  margin-bottom: 0;
}

.option-group-list-item-label {
  font-family: $fontBold;
}

.option-group-list-item-img {
  width: 20px;
  margin-right: 15px;
  display: inline-block;
}

.option-group-list-item-label span {
  font-size: 12px;
  font-style: italic;
  font-family: $fontLight;
  color: #aeaeae;
  padding-left: 3px;
}

.hotel-booking-detail {
  display: flex;
  margin-top: 27px;
  margin-bottom: 8px;
  padding: 0 15px;
}

.door {
  width: 15px !important;
  height: 15px !important;
}

.hotel-booking-img {
  width: 71px;
  height: 71px;
  border-radius: 6px;
}

.hotel-booking-img img {
  border-radius: 6px;
}

.hotel-booking-content {
  width: calc(100% - 71px);
  padding-left: 11px;
  display: flex;
  flex-wrap: wrap;
}

.hotel-contact {
  float: left;
  width: 100%;
  padding: 0 15px;
  font-size: 12px;
}

.book-data {
  float: left;
  width: 50%;
  line-height: 16px;
  margin-bottom: 10px;
}

.book-data label {
  font-size: 12px;
  display: block;
}

.book-data span {
  font-size: 14px;
  font-family: $fontBold;
  display: block;
}

.hotel-about-detail-text {
  float: left;
  width: 100%;
  font-size: 12px;
  line-height: 16px;
  color: #AEAEAE;
  text-transform: uppercase;
  border-top: 1px solid #e3e3e3;
  padding: 10px 15px 13px 15px;
  margin-top: 15px;
}

.contact-icon {
  width: 18px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 2px;
}

.contact-text {
  display: inline;
  vertical-align: middle;
}

.footer-button {
  float: left;
  width: 100%;
  text-align: center;
  padding: 20px 10px;
  position: fixed;
  bottom: 0;
  left: 0;
}

.footer-button button {
  border-radius: 6px;
  width: 277px;
  height: 46px;
  background-color: var(--button-bg-color) !important;
}

.booking-div-container {
  float: left;
  width: 100%;
  border-bottom: 2px solid #000;
}

.gallopCash {
  color: rgb(117, 0, 128);
  letter-spacing: 0.65px;
  font-family: $fontMedium;
  font-weight: bold;
}

@media (max-width: 1200px) {
  .booking-details {
      // width: 210px;
      width: 100%;
  }
  .statusDrop{
      margin-top: 12px;margin-left: 0px;width: 253px;
    }
  .filter-row{
      display: block;
  }
  .origin-destination {
      // width: calc(100% - 313px);
      width: 100%;
      padding: 0 30px;

  }

  .origin-destination1 {
      // width: calc(100% - 338px);
      width: 100%;
      padding: 0 30px;

  }

  .origin-destination-section {
      // width: calc(100% - 200px);
      width: 100%;
  }

  .origin-destination-switch {
      margin: 0 20px;
  }
}

@media (max-width: 991px) {
  .result-card-box-inner {
      padding: 12px 16px 16px;
      font-size: 13px;
  }

  .booking-details {
      width: 210px;
  }

  .booking-img {
      margin-right: 8px;
  }

  .booking-view-button {
      width: 63px;
      white-space: nowrap;
  }



  .origin-destination1 {
      width: 90px;
      padding: 0 10px;
  }

  .origin-destination-section {
      width: calc(100% - 200px);
  }
  .origin-destination1 {
      width: calc(100% - 370px);
      display: flex;
      padding: 0 10px;
  }
  .destination-hotel{
    margin-left: 35px;
  }

  .origin-destination.hotel{
    justify-content: space-between;
    align-items: center;
  }
}

@media(max-width:1024px) {
  
  .booking-text{
      display: block;
  }
  .booking-details {
      width: 100%;
      border-right: none;
      height: auto;
  }
  .result-card-box-inner {
      flex-wrap: nowrap;
      font-size: 12px;
      padding: 8px 10px 12px;
      height: auto;
  }
  .origin-destination1 {
      width: 100%;
      padding: 0 0px;
      padding-top: 10px;
  }

  .text-left {
      margin-left: 10px;
  }

  .footer-button {
      display: block;
  }

  .origin-destination {
      width: 100%;
      padding: 0;
  }

  .origin-destination-section {
      width: 130px;
  }
 
}

@media (max-width: 768px) {
  
  :host ::ng-deep {
      .ng-dropdown-panel{position: absolute !important; max-width: 253px !important; top: 32px !important;
          left: auto !important;
          background: #F7F7F9 !important;
          right: auto !important;
          -ms-transform: translate(0%);
          transform: translate(-6%);
          bottom: auto !important;min-width: 100px !important; width: calc(100% - 40px) !important;}
          #bookingDropdown .ng-dropdown-panel{position: absolute !important; max-width: 253px !important; top: 33px !important;
              left: auto !important;
              background: #F7F7F9 !important;
              right: auto !important;
              -ms-transform: translate(0%);
              transform: translate(-6%);
              bottom: auto !important;min-width: 100px !important; width: calc(100% - 40px) !important;}
  }
  .price-section {
      float: left;
      width: auto;
  }

  .hotel-about-detail-text {
      font-size: 9px;
      line-height: 15px;
  }

  .booking-button-text-container1 {
      border-top: 1px solid #E3E3E3;
      padding: 13px 0px;
      width: 110%;
  }

  .hotel-booking-detail {
      padding: 0 5px;
  }

  .book-data1 {
      width: 55%;
  }

  .book-data2 {
      width: 45%;
  }

  .hotel-booking-content {
      padding-left: 8px;
  }

  .tab-list-item {
      width: 50%;
      float: left;
      display: block;
      text-align: center;
      margin-right: 0;
  }

  .tab-list-item a {
      margin-left: 0;
  }

  .result-card-box-inner {
      flex-wrap: nowrap;
      font-size: 12px;
      padding: 8px 10px 12px;
      height: auto;
  }



  .booking-details {
      width: 100%;
      border-right: none;
      height: auto;
      display: block;
  }

  .origin-destination1 {
      width: 100%;
      padding: 0 0px;
      padding-top: 5px;
  }

  .text-left {
      margin-left: 10px;
  }

  .footer-button {
      display: block;
  }

  .origin-destination {
      width: 100%;
      padding: 0;
  }

  .origin-destination-section {
      width: 130px;
  }

  .origin-destination-switch {
      margin: 0 12px;
  }

  .tab-content {
      padding: 0 0px;
      overflow: auto;

  }

  .booking-img {
      /*margin-top: -3px;*/
      margin-right: 10px;
      width: 26px 
  }

  /*.booking-img img{width: 24px;}*/
  .booking-text {
      display: block;
  }


  .booking-history-detail-content-inner {
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 0 18px;
  }

  .booking-history-detail-content-left {
      margin-right: 0;
  }

  .booking-history-detail-content-right {
      padding-top: 24px;
      padding-bottom: 50px;
  }

  .booking-detail-data {
      padding: 0 8px 31px 8px;
  }

  .price-row {
      justify-content: flex-end;
  }

  .hotel-nights-count {
      margin-top: -20px;
      padding-top: 0;
      word-wrap: nowrap;
      max-width: 60px;
  min-width: 60px;
  }

  .primary-text {
      font-size: 13px;
  }
  .destination-hotel{
    margin-left: 0px;
  }
  
}

@media(max-width:767px) {
  .origin-destination1 {
      width: 100%;
  }
  .destination-hotel{
    // min-width: 100px;
    // max-width: 100px;
    width: 100%;
    padding-right: 0px;
  }

  .booking-details .destination-hotel{
    padding-left: 37px !important;
  }
  .origin-destination.hotel {
    padding-left: 35px;
  }
  .mobileBtton{
    display: flex;
    align-items: center;
  }
  .bottomcontainer{
    display: block;
    background: #fff;
      float: left;
      width: 100%;
      padding: 12px 12px 16px;
      
  }
  .switchLabel {
        font-size: 12px;
        line-height: 20px;
       text-align: left;
        white-space: break-spaces;
        font-family: "apercu-r";
        padding-left: 0px;
       position: relative;
       padding-top: 8px;
        display: inline-block;
      
        margin-right: 10px;
      }
  :host ::ng-deep {
      .ng-dropdown-panel{position: absolute !important; max-width: 223px !important; top: 32px !important;
          left: auto !important;
          background: #F7F7F9 !important;
          right: auto !important;
          -ms-transform: translate(0%);
          transform: translate(-6%);
          bottom: auto !important;min-width: 100px !important; width: calc(100% - 40px) !important;}

         #bookingDropdown .ng-dropdown-panel{position: absolute !important; max-width: 223px !important; top: 33px !important;
              left: auto !important;
              background: #F7F7F9 !important;
              right: auto !important;
              -ms-transform: translate(0%);
              transform: translate(-6%);
              bottom: auto !important;min-width: 100px !important; width: calc(100% - 40px) !important;}
  }
  .labelTime{
      position: relative;
      top: 0px;
      margin-right: 5px;
    }
  .dateShow {
      position: absolute;
      white-space: nowrap;
      left: 10px;
      font-size: 16px;
      top: 6px;
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;
  }
  .input {
      cursor: pointer;
      text-align: center;
      padding-top: 5px;
      width: 223px;
      padding-left: 25px;
      background: #FCFCFC;
      border: 1px solid #8936F3;
      border-radius: 8px;
      height: 32px;
      text-align: left;
      font-size: 14px;
    }
  .down-arrow{
      left: 195px;top: -28px;position: relative;
  }
  .down-Arrow{
      position: absolute;
  left: 190px;
  top: 12px;
  }
  .dateShow1 {
      position: absolute;
      white-space: nowrap;
      right: calc((100% - 346px)/2);
      top: 6px;
      font-size: 14px;
      color: gray;
    }
  
  .filter-modal1 {
      width: 223px;
      position: absolute;
      z-index: 1000;
      background: #fff;
      border-radius: 8px;
      left: auto !important;
      top: 60px;
      right: auto !important;
      border: 1px solid var(--dark-bg-color);
      overflow: hidden;
    }
  .flight-timing-stops ul li span {
      margin-left: 0px;
      display: block;
  }
  .showUserDetail {
      display: block;
  }

  .showinUSerdetails {
      line-height: 20px;
      font-family: var(--globalFontfamilyr);font-weight: bold;
      width: 100%;
      padding-right: 5px;
      vertical-align: middle;
      text-align: left !important;
  }

  .tab-list-item11 {
      font-family: "apercu-b" !important;
      position: relative;
      text-transform: uppercase;
      font-size: 14px;
      text-align: left;
      line-height: 30px;
      color: var(--dark-bg-color) !important;
      text-decoration: underline;
      width: auto !important;
      display: inline-block;
      margin-right: 10px !important;
      font-weight: 700;
      margin-left: 10px !important;
      margin-bottom: 0px;
      padding-bottom: 0px;
      cursor: pointer;
      white-space: nowrap;
  }

  .tab-list-item12 {
      font-family: "apercu-r" !important;
      display: inline-block;
      text-align: left;
      color: #595959 !important;
      margin-right: 10px;
      margin-left: 10px;
      margin-bottom: 0px;
      padding-bottom: 0px;
      text-transform: uppercase;
      font-size: 14px;
      white-space: nowrap;
      width: auto !important;
      line-height: 30px;
      font-weight: 400;
      cursor: pointer;
  }
}


@media(max-width:550px) {
  .labelTime{
      position: relative;
      top: -10px;
      margin-right: 5px;
    }

}


@media (max-width: 499px) {
  .origin-destination-section {
      width: 110px;
  }
  .labelTime{
      position: relative;
      top: 0px;
      margin-right: 5px;
    }
  .origin-destination1-section {
      width: 110px;
  }

  .totalPrice {
      font-size: 16px !important;
  }

  .result-card-box-inner {
    flex-wrap: nowrap;
  }
}