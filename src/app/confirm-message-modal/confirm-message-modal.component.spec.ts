import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ConfirmMessageModalComponent } from './confirm-message-modal.component';

describe('ConfirmMessageModalComponent', () => {
  let component: ConfirmMessageModalComponent;
  let fixture: ComponentFixture<ConfirmMessageModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ConfirmMessageModalComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ConfirmMessageModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
