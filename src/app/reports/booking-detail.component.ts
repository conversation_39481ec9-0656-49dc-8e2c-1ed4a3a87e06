import { Component, OnInit, Input } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators, UntypedFormControl, Form, ValidationErrors } from '@angular/forms';
import { NavigationExtras, Router, ActivatedRoute } from '@angular/router';
import { FilterService } from '../filter.service';
import { BookingData, BookedOption } from '../util/bookingdata';
import { BsModalService, BsModalRef, ModalDirective } from 'ngx-bootstrap/modal';
import { DeviceDetailsService } from '../device-details.service';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { NgxSmartModalComponent } from 'ngx-smart-modal';
import { Subscription, Subject } from 'rxjs';
import { SearchService } from '../search.service';
import { Constants } from '../util/constants';
import { DateUtils } from '../util/date-utils';
import { FlightUtils } from '../util/flight-utils';
import { BookingHistoryService } from '../booking-history.service';
import { ConditionalExpr } from '@angular/compiler';
import { UserAccountService } from '../user-account.service';
import { CancelSuccessComponent } from '../cancel-success/cancel-success.component';
import { HotelHistoryModalComponent } from '../hotel-history-modal/hotel-history-modal.component';
import { UserAccountInfo } from '../entity/user-account-info';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtils } from '../util/common-utils';
import { Location } from '@angular/common';
import { ImageRequest } from '../entity/image-request';
import { HotelSearchService } from '../hotel-search.service';
import { FlightHopResult } from '../entity/flight-hop-result';
import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { GallopAnalyticsUtil } from '../analytics.service';
import { AdminPanelService } from '../admin-panel.service';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { ClientConfiguration } from '../client-config.service';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { UserCredentialsService } from '../user-credentials.service';
import { constants } from 'buffer';
@Component({
    selector: 'booking-detail-component',
    templateUrl: './booking-detail.component.html',
    styleUrls: ['./booking-detail.component.scss'],
    standalone: false
})


export class bookingDetailComponent {
  //bookingData:BookingData;
  selectOptionForModal = '';
  search='';
  flightlength = 0;
  isDataLoading = false;
  viewMode = 'tab1';
  hideChanges = false;
  reportComponet=true;
  resultErrorMessage = this.translateService.instant('report.FetchingData');
  backTo = '';
  messageForChange = '';
  clicked = false;
  classOptions = Constants.CLASS_OPTIONS;
  y: number;
  z: number;
  tripItUrl = '';
  tripItHotelUrl = '';
  itineraryUrl = '';
  invoiceUrl: Array<string> = [];
  calenderUrl = '';
  layoverShow = false;
  allowAddToExpensify = false;
  airlines;
  airports;
  changingValue: Subject<any> = new Subject();
  imageResuestObj: ImageRequest[]
  flightList2: Array<BookedOption> = [];
  hotelList2: Array<BookedOption> = [];
  all_bookingData: any;
  modalShow = false;
  modalShow1 = false;
  detailView = false;
  flightView = false;
  hotelView = false;
  combinedView = false;
  isEmpty = false;
  isEmpty2 = false;
  vendorDetails = [];
  detailList: Array<any> = [];
  detailTransaction: any = {};
  fareList: Array<any> = [];
  hotelList: Array<any> = [];
  flightList: Array<any> = [];
  isMobile: boolean;
  length1 = 0;
  gallopCash = 0;
  deviceSubscription: Subscription;
  fetchImagesSubscription: Subscription;
  noOfFlightLegs = [];
  bsModalRef: BsModalRef;
  pageMode: string;
  userEmail: string;
  ticketid = '';
  tripid = '';
  bookingType = '';
  errorMsg = false;
  sendRequestProgress = false;
  showCheckBox = false;
  selectTransId = '';
  travellerName = '';
  expensify_add = [];
  expensify_add_hotel = [];
  tagShow = [];
  selectedTag= [];
  tagset=[];
  savingTag = false;
  username = '';
  phonenumber = '';
  email = '';
  showRadioError = false;
  dropDownopen =[];
  showChangeRequestError = false;
  traveler = [];
  radioSelect = '';
  checked = false;
  emergencyContact: any;
  expensifyType = [{ name: this.translateService.instant('bookingHistory.hotel'), value: 'hotel' },
  { name: this.translateService.instant('bookingHistory.flight'), value: 'flight' }];
  expensifyEmailControl: UntypedFormControl;
  expensifyEmail: string = null;
  type = ['hotel', 'flight'];
  selectOption = '';
  errorInCalenderRadio = false;
  calenderUrlHotel = '';
  tripDetails;
  bookingHistoryDetailResp;
  private fetchAccountInfoSubscription: Subscription;
  private userAccountInfoObj: UserAccountInfo;
  constructor(private modalService: BsModalService,
    private adminPanelService: AdminPanelService,
    private searchService: SearchService,
    private searchService1: HotelSearchService,
    private userCredentials: UserCredentialsService,
    private location: Location,
    private userAccountService: UserAccountService,
    private gallopLocalStorage: GallopLocalStorageService,
    private router: Router,
    public ngxSmartModalService: NgxSmartModalService,
    private deviceDetailsService: DeviceDetailsService,
    private bookingHistoryService: BookingHistoryService,
    public translateService: TranslateService,
    private activatedRoute: ActivatedRoute,
    private titleService: Title,
    private toastr: ToastrService,
    private ngxAnaltics:GoogleAnalyticsService,
    private clientConfig: ClientConfiguration) { }

  private bookingHistoryComponentInit() {

    if (this.pageMode === 'detail') {
      this.detailView = true;
      this.fetchBookingDetails();
    } else {
      this.detailView = false;
      // this.fetchBookingHistoryList();
    }


  }
  ngOnInit() {
    
    this.hotelList = [];
    this.titleService.setTitle(this.translateService.instant('report.TransactionsReport'));
    this.hotelList2 = [];
    this.radioSelect = '';
    this.messageForChange = '';
    this.expensifyEmailControl = new UntypedFormControl(this.expensifyEmail, Validators.compose([this.expensifyEmailValidator.bind(this), Validators.pattern(Constants.RGEX_EMAIL)]));
    this.expensify_add = [];
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });
    let from = this.gallopLocalStorage.getItem("from");
    this.adminPanelService.from = from;
    if (this.adminPanelService.from === 'activeTraveller') {
      this.backTo = ('activeTraveler.ActiveTravelers');
    } else if (this.adminPanelService.from === 'dashboard') {
      this.backTo = ('dashboardWrapper.Dashboard');
    } else {
      this.backTo = ('report.Reports');
    }
    this.fetchAccountInfoSubscription = this.userAccountService.userAccountInfoObjObserver$.subscribe((userAccountInfo: UserAccountInfo) => {
      if (!userAccountInfo || userAccountInfo == null) return;
      this.userAccountInfoObj = userAccountInfo;
      if (this.userAccountInfoObj.userInfo.expensifyId
        && this.userAccountInfoObj.userInfo.expensifyId.trim().length > 0) {
        this.expensifyEmail = this.userAccountInfoObj.userInfo.expensifyId;
        this.expensifyEmailControl.setValue(this.expensifyEmail);

      } else {
        this.expensifyEmail = this.userAccountInfoObj.userInfo.email;
        this.expensifyEmailControl.setValue(this.expensifyEmail);
      }

    });

    // let params = this.activatedRoute.snapshot.queryParams;
    // this.setParameters(params);
    this.activatedRoute.queryParams.subscribe(params => {
      this.setParameters(params);
      // if(params['type'] == 'list')
      this.bookingHistoryComponentInit();
    });



  }

  private setParameters(params) {
    if (params['type']) {
      this.pageMode = params['type'];
      this.userEmail = decodeURIComponent(params['userEmail']);
      this.travellerName = params['name'];
      this.tripid = params['tripid'];
      this.ticketid = params['ticketid'];
      this.selectTransId = params['transactionid'];
      this.bookingType = params['bookingType'];
      if (this.pageMode === 'list') {
        this.backToListWork();

      }
    }
  }

  expensifyEmailValidator(control: AbstractControl): ValidationErrors | null {
    let result = null;

    if ((!control.value || control.value.trim().length === 0)) {
      return { 'required': true };
    }
    return result;
  }
  getEticktNo(bookingList){
    let ticketNo;
    let index=0;
    for(let item of bookingList){
      if(ticketNo){
    ticketNo =ticketNo +', '+ item.ticketnumber;
      }else{
        ticketNo =item.ticketnumber;
      }
    }
    return ticketNo;
  }
  private fetchBookingHistoryList() {
    this.bsModalRef = this.modalService.show(HotelHistoryModalComponent, {
      initialState: {

      }, backdrop: true, ignoreBackdropClick: true
    });
    const userId = this.userAccountService.getUserEmail();
    const sToken = this.userAccountService.getSToken();
    this.bookingHistoryService.requestBookingHistoryList(userId,sToken).subscribe(resp => {
      if (resp.success === true && resp.data) {
        this.isEmpty2 = false;
        this.isEmpty = false;
        let bookingHistory: BookingData = JSON.parse(JSON.stringify(resp));
        this.isDataLoading = true;
        setTimeout(() => {
          this.bsModalRef.hide();
        }, 100);

        this.airlines = bookingHistory.data.airlineNames;
        this.airports = bookingHistory.data.airports;
        //this.getBookingPastList(bookingHistory.data.past);
        this.getBookingList(bookingHistory.data.upcoming);
      } else {
        this.isDataLoading = true;
        setTimeout(() => {
          this.bsModalRef.hide();
        }, 100);
        this.isEmpty2 = true;
        this.isEmpty = true;
      }
    }, error => {
      this.isDataLoading = true;
      setTimeout(() => {
        this.bsModalRef.hide();
      }, 100);
      this.isEmpty2 = true;
      this.isEmpty = true;
    });
  }
 
 
  handleResponseFromDropdwn(event){
    const companyid = this.userAccountService.getUserCompanyId();
    let tripTransactions = [];
    for(let x of this.tripDetails){
      x.transaction.projectTagId = event.toString(); 
      tripTransactions.push(x.transaction); 
     };
    // this.detailTransaction.projectTagId = event.toString(); 
    
    this.savingTag = true;
    this.changingValue.next({value1:this.savingTag});
    this.adminPanelService.UpdateTagFromTrip(companyid, tripTransactions).subscribe(resp => {
      if (resp.status === 'success') {
        this.savingTag = false;
        this.changingValue.next({value1:this.savingTag});
        this.toastr.success(this.translateService.instant("setting.TripTagaddedsuccessfully"));
       // this.getSelectedData(false,index);
      } else {
        this.savingTag = false;
        this.changingValue.next({value1:this.savingTag});
        this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntreportdata.Pleasetryagainlater"));
      }
    }, error => {
      if (error.status != 403) {
        setTimeout(() => {
          this.savingTag = false;
          this.changingValue.next({value1:this.savingTag});
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })

  }
  sortList(data) {
    data.sort(function (a, b) {
      if (a.tag_name.toLowerCase() < b.tag_name.toLowerCase()) { return -1; }
      if (a.tag_name.toLowerCase() > b.tag_name.toLowerCase()) { return 1; }
      return 0;
    })
    return data;
  }
  getAllTags() {
    
    this.tagShow = [];
    this.tagset=[];
    this.selectedTag=[];
    this.adminPanelService.getTagAllList().subscribe(resp => {
      if (resp.status === 'success') {
        if (resp.data && Object.keys((resp.data)).length > 0) {
          
          this.getAllTagsSet()
          for (let key in resp.data) {
            let keysValue = key
            this.tagset.push(keysValue);
           // this.selectedTag.push('');
          this.dropDownopen.push(false);
          }
          this.tagShow = new Array(this.tagset.length).fill(null).map(_ => [])
          for(let i=0;i<this.tagset.length;i++){
            let arrayVAlue = resp.data[this.tagset[i]];
            for(let item1 of arrayVAlue){
              this.tagShow[i].push(item1);
            }
            this.tagShow[i] = this. sortList(this.tagShow[i]);
          }
          if (this.detailTransaction && this.detailTransaction.projectTagId && this.detailTransaction.projectTagId !== '') {
            // this.selectedTag = this.detailTransaction.projectTagId.split(',');
             
             this.selectedTag = CommonUtils.selectagDropdown(this.detailTransaction.projectTagId,this.tagset,this.tagShow,this.selectedTag)
          
            }
          
        }
      } else {
        this.toastr.error("Apologies! something went wrong, we could'nt report data. Please try again later");
      }
    }, error => {
      if (error.status != 403) {
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
  }


  tagsSetData = {
    "isMandotary":false,
    "tags":[]
  }

  getAllTagsSet(){
    const companyid = this.userAccountService.getUserCompanyId();
    this.adminPanelService.getTagSet(companyid).subscribe((res)=>{
      if(res.status == "success"){
        
       const  new_tagSets_array = res.data.tagsets;

        this.tagsSetData.tags = [];
        new_tagSets_array.forEach(e =>{
        let mandatory = false;
        if(e.mandatory && e.activeTagCount > 0){
          this.tagsSetData.isMandotary = true;
          mandatory = true;
        }
          const obj  = {
            "companyId": e.companyId,
            "tagsetId": e.tagsetId,
            "tagSetName": e.tagSetName,
            "mandatory": mandatory,
            "tags":""
         };
  
         this.tagsSetData.tags.push(obj)
      })
      }
    })
   
  }
  
  cabinClass: any;
  private OldfetchBookingDetails() {
    // this.bsModalRef= this.modalService.show(HotelHistoryModalComponent,{
    //   initialState: {
    //   }, backdrop: true, ignoreBackdropClick: true
    // });
    this.getAllTags();
    this.bookingHistoryService.getReportDetail(this.userEmail, this.ticketid, this.tripid, this.selectTransId).subscribe(resp => {
      if (resp.success === true && resp.data) {
        let bookingHistoryDetail = JSON.parse(JSON.stringify(resp));
        this.airlines = bookingHistoryDetail.data.airlineNames;
        this.invoiceUrl = bookingHistoryDetail.data.invoiceUrl ? bookingHistoryDetail.data.invoiceUrl : [];
        this.airports = bookingHistoryDetail.data.airports;
        if (bookingHistoryDetail.data.cabinClassNames) {
          this.cabinClass = bookingHistoryDetail.data.cabinClassNames
        }
        let bookingHistoryArray = bookingHistoryDetail.data.detail;
        this.getBookingList(bookingHistoryDetail.data.detail);
        let fareList = bookingHistoryDetail.data.transaction.data;
        this.gallopCash = bookingHistoryDetail.data.transaction.gallopCashUsed;
        this.detailTransaction = bookingHistoryDetail.data.transaction;
        if (this.detailTransaction && this.detailTransaction.projectTagId && this.detailTransaction.projectTagId !== '') {
        // this.selectedTag = this.detailTransaction.projectTagId.split(',');
         
         this.selectedTag = CommonUtils.selectagDropdown(this.detailTransaction.projectTagId,this.tagset,this.tagShow,this.selectedTag)
      
        }
        this.length1 = bookingHistoryArray.length;
        let travellerdetail = bookingHistoryDetail.data.travellersData;
        if (travellerdetail) {
          this.traveler = [...travellerdetail.travellers];
        }
        this.emergencyContact = bookingHistoryDetail.data.travellersData.emergencyContact;
        for (let item of bookingHistoryArray) {
          if (item.type == 'flight') {
            item.option.flight_option.layoverInfo = this.getLegWiseLayoverList(item);
            this.hotelList.push(item);
            this.selectOptionForModal = 'flight';
            this.expensify_add.push('flight');
            this.tripItUrl = bookingHistoryDetail.data.tripItUrl;
            this.calenderUrl = bookingHistoryDetail.data.calenderUrl;
          } else if (item.type == 'cars') {
            //item.option.flight_option.layoverInfo = this.getLegWiseLayoverList(item);
            this.hotelList.push(item);
            this.selectOptionForModal = 'cars';
            if (item.option.car_option.phoneNumber) {
              let vendorDetails = { name: '', phonenumber: '' }
              vendorDetails.name = item.option.car_option.partnerName;
              vendorDetails.phonenumber =  item.option.car_option.phoneNumber.displayText;
              this.vendorDetails.push(vendorDetails);
            }
          }
          else {
            this.fetchImages(item);
            this.selectOptionForModal = 'hotel';
            this.hotelList2.push(item);
            this.expensify_add_hotel.push('hotel');
            this.tripItHotelUrl = bookingHistoryDetail.data.tripItUrlHotel;
            this.calenderUrlHotel = bookingHistoryDetail.data.calenderUrlHotel;
          }
          //  
          this.detailList.push(item);
          if (this.hotelList) {
            this.flightlength = this.hotelList.length;
          }

        }
        if (this.hotelList.length > 0 && this.hotelList2.length > 0) {
          this.showCheckBox = true;
          this.selectOptionForModal = '';
          this.expensify_add = [];
          this.expensify_add_hotel = [];
        } else {
          this.showCheckBox = false;
        }
        for (let item1 of fareList) {
          if (item1.archived) continue;
          if (item1.event_type !== 'other' || this.canShowLineItems()) {
            this.fareList.push(item1);
          }
        }


        // setTimeout(() => {
        //   this.bsModalRef.hide();
        // }, 1000);
      } else {
        // setTimeout(() => {
        //   this.bsModalRef.hide();
        // }, 200);
        this.detailView = false;
        //   this.bsModalRef = this.modalService.show(CancelSuccessComponent, {
        //     initialState: {
        // message: this.translateService.instant('bookingHistory.NoResultsFound'),
        //     },
        // });
      }
    }, error => {
      // this.bsModalRef.hide();
    }
    );
  }
  
  private fetchBookingDetails(){
    this.getAllTags();
    this.bookingHistoryService.getReportDetail(this.userEmail, this.ticketid, this.tripid, this.selectTransId).subscribe(resp => {
      if (resp.success === true && resp.data) {
        let bookingHistoryDetail = JSON.parse(JSON.stringify(resp));
        // let fareList = bookingHistoryDetail.data.transaction.data;
        this.bookingHistoryDetailResp = bookingHistoryDetail;
      this.tripDetails = [];
      bookingHistoryDetail.data.tripDetail.forEach(e => {
        if(e.detail.length != 0){this.tripDetails.push(e)};
      });
      this.invoiceUrl = bookingHistoryDetail.data.invoiceUrl;
      this.airlines = bookingHistoryDetail.data.airlineNames;
      this.airports = bookingHistoryDetail.data.airports;
      let bookingHistoryArray = []
      let travellerdetail = bookingHistoryDetail.data.travellersData;
      this.allowAddToExpensify = bookingHistoryDetail.data.allowAddToExpensify;
      
      this.tripDetails.forEach(e => {
          e.detail.forEach(f => {
              bookingHistoryArray.push(f)
              
          });
      });

       for(let x of this.tripDetails){
        this.detailTransaction = x.transaction;
        if (x.transaction && x.transaction.projectTagId && x.transaction.projectTagId !== '') {
           this.selectedTag = CommonUtils.selectagDropdown(x.transaction.projectTagId,this.tagset,this.tagShow,this.selectedTag)
            break;
          };
       };

        
        this.length1 = bookingHistoryArray.length;

        if (travellerdetail) {
          this.traveler = [...travellerdetail.travellers];
        }
        this.emergencyContact = bookingHistoryDetail.data.travellersData.emergencyContact;
        for (let item of bookingHistoryArray) {
          if (item.type == 'flight') {
            item.option.flight_option.layoverInfo = this.getLegWiseLayoverList(item);
            this.hotelList.push(item);
            this.selectOptionForModal = 'flight';
            this.expensify_add.push('flight');
            this.tripItUrl = bookingHistoryDetail.data.tripItUrl;
            this.calenderUrl = bookingHistoryDetail.data.calenderUrl;
          } else if (item.type == 'cars') {
            //item.option.flight_option.layoverInfo = this.getLegWiseLayoverList(item);
            this.hotelList.push(item);
            this.selectOptionForModal = 'cars';
            if (item.option.car_option.phoneNumber) {
              let vendorDetails = { name: '', phonenumber: '' }
              vendorDetails.name = item.option.car_option.partnerName;
              vendorDetails.phonenumber =  item.option.car_option.phoneNumber.displayText;
              this.vendorDetails.push(vendorDetails);
            }
          }
          else {
            this.fetchImages(item);
            this.selectOptionForModal = 'hotel';
            this.hotelList2.push(item);
            this.expensify_add_hotel.push('hotel');
            this.tripItHotelUrl = bookingHistoryDetail.data.tripItUrlHotel;
            this.calenderUrlHotel = bookingHistoryDetail.data.calenderUrlHotel;
          }
          //  
          this.detailList.push(item);
          if (this.hotelList) {
            this.flightlength = this.hotelList.length;
          }

        }
        if (this.hotelList.length > 0 && this.hotelList2.length > 0) {
          this.showCheckBox = true;
          this.selectOptionForModal = '';
          this.expensify_add = [];
          this.expensify_add_hotel = [];
        } else {
          this.showCheckBox = false;
        }
        // for (let item1 of fareList) {
        //   if (item1.archived) continue;
        //   if (item1.event_type !== 'other' || this.canShowLineItems()) {
        //     this.fareList.push(item1);
        //   }
        // }


        // setTimeout(() => {
        //   this.bsModalRef.hide();
        // }, 1000);
      } else {
        // setTimeout(() => {
        //   this.bsModalRef.hide();
        // }, 200);
        this.detailView = false;
        //   this.bsModalRef = this.modalService.show(CancelSuccessComponent, {
        //     initialState: {
        // message: this.translateService.instant('bookingHistory.NoResultsFound'),
        //     },
        // });
      }
    }, error => {
      // this.bsModalRef.hide();
    }
    );

  }

  getCarPrice(item){
    if(item.displayBasePrice){
      return item.displayBasePrice
    }else{
      return item.basePrice
    }
  }
  getFlightOptionDesc(eventId, optionId) {
    let foundFlightOption = undefined;
    for (let bookedItem of this.detailList) {
      if (bookedItem.type == 'flight' && bookedItem.eventId == eventId && bookedItem.option.id == optionId) {
        // foundFlightOption = bookedItem.option.flight_option;
        foundFlightOption = bookedItem.option.flight_option.legs[0].hops[0].from + " - " + bookedItem.option.flight_option.legs[0].hops[bookedItem.option.flight_option.legs[0].hops.length - 1].to;
        break;
      }
    }


    return foundFlightOption;
  }

  getBookingList(upcomingList: Array<BookedOption>) {
    for (let bookedOption of upcomingList) {
      if (bookedOption.option.selectTransId) {
        this.flightList.push(bookedOption);
      }
    }

    if (this.flightList.length == 0) {
      this.isEmpty = true;
    } else {
      this.isEmpty = false;
    }
  }


  getDetailView(bookingOption: any, hideChange) {
    GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics, 'bookingDetails', 'Bookings');

    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }

    this.bsModalRef = this.modalService.show(HotelHistoryModalComponent, {
      initialState: {

      }, backdrop: true, ignoreBackdropClick: true
    });
    this.hideChanges = hideChange;
    this.detailView = true;
    this.detailList = [];
    this.fareList = [];
    this.length1 = 0;
    this.tripItUrl = '';
    this.tripItHotelUrl = '';
    this.invoiceUrl = [];
    this.calenderUrl = '';
    this.calenderUrlHotel = '';
    let ticketid = bookingOption.ticketid;
    let tripid = bookingOption.tripid;
    let selectTransId = bookingOption.option.selectTransId;
    let tripSessionId = bookingOption.tripSessionId
    this.bookingHistoryService.getDetail(ticketid, tripid, selectTransId,tripSessionId).subscribe(resp => {
      if (resp.success === true && resp.data) {
        let bookingHistoryDetail = JSON.parse(JSON.stringify(resp));
        this.invoiceUrl = bookingHistoryDetail.data.invoiceUrl ? bookingHistoryDetail.data.invoiceUrl : ['https://beta.routespring.com/TraflaBeta-mflyDev/api/bookingHistory/download/invoice?userid=<EMAIL>&ticketid=173971000080104001&tripid=68719E6pwhDmc&transactionid=RZ3AhP4V&type=flight'];
        let bookingHistoryArray = bookingHistoryDetail.data.detail;

        let fareList = bookingHistoryDetail.data.transaction.data;
        this.gallopCash = bookingHistoryDetail.data.transaction.gallopCashUsed;
        this.detailTransaction = bookingHistoryDetail.data.transaction;
        this.allowAddToExpensify = bookingHistoryDetail.data.allowAddToExpensify;
        this.length1 = bookingHistoryArray.length;
        for (let item of bookingHistoryArray) {
          if (item.type == 'flight') {
            item.option.flight_option.layoverInfo = this.getLegWiseLayoverList(item);
            this.hotelList.push(item);
            this.expensify_add.push('flight');
            this.selectOptionForModal = 'flight';
            this.tripItUrl = bookingHistoryDetail.data.tripItUrl;
            this.calenderUrl = bookingHistoryDetail.data.calenderUrl;
          } else if (item.type == 'cars') {
            //item.option.flight_option.layoverInfo = this.getLegWiseLayoverList(item);
            this.hotelList.push(item);
            this.selectOptionForModal = 'cars';
          }
          else {
            this.fetchImages(item);
            this.hotelList2.push(item);
            this.expensify_add_hotel.push('hotel');
            this.selectOptionForModal = 'hotel';
            this.tripItHotelUrl = bookingHistoryDetail.data.tripItUrlHotel;
            this.calenderUrlHotel = bookingHistoryDetail.data.calenderUrlHotel;
          }
          this.detailList.push(item);
        }
        // 
        if (this.hotelList.length > 0 && this.hotelList2.length > 0) {
          this.showCheckBox = true;
          this.selectOptionForModal = '';
          this.expensify_add = [];
          this.expensify_add_hotel = [];
        } else {
          this.showCheckBox = false;
        }
        for (let item1 of fareList) {
          if (item1.archived) continue;
          if (item1.event_type !== 'other' || this.canShowLineItems()) {
            this.fareList.push(item1);
          }
        }


        setTimeout(() => {
          this.bsModalRef.hide();
        }, 100);
      } else {
        this.detailView = false;
        setTimeout(() => {
          this.bsModalRef.hide();
          //this.bsModalRef = this.modalService.show(CancelSuccessComponent, {
          //initialState: {
          //message: this.translateService.instant('bookingHistory.NoResultsFound'),
          //  },
          // });
        }, 100);

      }


    })

  }
  showDetailView(bookingOption: any) {
    //   let ticketid= bookingOption.ticketid;
    //   let tripid=bookingOption.tripid;
    //   let selectTransId=bookingOption.option.selectTransId;
    //   this.router.navigate(["admin"],
    //   {
    //     queryParams:
    //       {
    //         view: 'detail',
    //         type: 'detail',
    //         bookingType:'past',
    //         ticketid:ticketid,
    //         tripid:tripid,
    //         transactionid:selectTransId
    //       },
    //   }
    // );
    this.detailView = true;
  }
  getTaxForCar(item) {
    if(item.option.car_option.displayBasePrice){
      let base = item.option.car_option.displayBasePrice;
      let total = item.option.car_option.displayPrice;
      return (total - base);
    }else if (item.option.car_option.basePrice) {
      let base = item.option.car_option.basePrice;
      let total = item.option.car_option.price;
      return (total - base);
    }
  }
  carUrl(car) {
    if (car.images) {
      return car.images.S;
    }
  }
  getCarPerDayPrice(item) {
    var subTotal = (item.option.car_option.price - (item.option.car_option.price - item.option.car_option.basePrice))
    return (subTotal / item.option.car_option.numberOfDay);
  }
  fetchImages(item) {
    item.url = 'assets/images/hotel/hotel.png';
    let hotelImagesArray;
    var firstRequestCodes: ImageRequest[] = [];
    var requestObj: ImageRequest = new ImageRequest();
    requestObj.code = item.option.hotel_option.hotelCode;
    requestObj.chainCode = item.option.hotel_option.hotelChain;
    firstRequestCodes.push(requestObj);
    this.fetchImagesSubscription = this.searchService1.requestAllHotelImage(firstRequestCodes).subscribe(res => {
      if (res.success === true && res.data) {
        hotelImagesArray = res.data;
        if (hotelImagesArray[0].hotelImages) {
          item.url = CommonUtils.getThumbnailImage(hotelImagesArray[0].hotelImages);
        }
      }
    });
  }
  onExpensifyClicked(option, event) {


    if (event.target.checked) {
      if (option == 'flight') {
        this.expensify_add.push(option);
        this.errorMsg = false;
      } else {
        this.expensify_add_hotel.push(option);
        this.errorMsg = false;
      }
    } else {
      if (this.expensify_add.length > 0 || this.expensify_add_hotel.length > 0) {
        this.deleteMsg(option);
      }
    }
  }
  deleteMsg(msg) {
    if (msg == 'flight') {
      const index: number = this.expensify_add.indexOf(msg);
      if (index !== -1) {
        this.expensify_add.splice(index, 1);
      }
    } else {
      const index: number = this.expensify_add_hotel.indexOf(msg);
      if (index !== -1) {
        this.expensify_add_hotel.splice(index, 1);
      }
    }
  }
  getCurrencySymbol(item): string {
    if(item.displayCurrency){
      return CommonUtils.getCurrencySymbol(item.displayCurrency);
    }else{
    return CommonUtils.getCurrencySymbol(item.currency);
    }
  }
  
  getPriceItem(item){
    if(item.displayPrice){
      return item.displayPrice;
    }else{
      return item.price;
    }
  }

  isExpensifyChecked(option) {
    if (option == 'flight') {
      return this.expensify_add.indexOf(option) > -1;
    } else {
      return this.expensify_add_hotel.indexOf(option) > -1;
    }
  }
  backToListWork() {
    this.selectOption = '';
    this.messageForChange = '';
    this.expensify_add = [];
    this.expensify_add_hotel = [];
    this.hotelList = [];
    this.hotelList2 = [];
    this.radioSelect = '';
    this.detailView = false;
    this.gallopCash = 0;
    this.selectOptionForModal = '';
    // this.router.navigate(["bookingHistory"],
    //{
    // queryParams:
    // {
    //type: 'list',
    //},
    //replaceUrl: true
    //}
    //);
  }

  backToList() {
    // this.location.back();
    this.detailView = false;
  }
  backToReports() {
    // this.location.back();
    let from = this.gallopLocalStorage.getItem("from");
    this.adminPanelService.from = from;
    if (this.adminPanelService.from === 'activeTraveller') {
      if (this.adminPanelService.selectDateRange === '') {
       // this.adminPanelService.selectDateRange = 'today';
      }
      if(this.adminPanelService.filterTravellerName!==''){
        this.adminPanelService.filterTravellerName='';
      }
     history.go(-1);
    } else if (this.adminPanelService.from === 'report') {
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: 'report',
            subType: 'Transactions'
          },
          replaceUrl: true
        }
      );
    }
    else if (this.adminPanelService.from === 'dashboard') {
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: 'dashboard',
          },
          replaceUrl: true
        }
      );
    } else {
      this.adminPanelService.from = 'report';
      this.gallopLocalStorage.setItem("from", this.adminPanelService.from);
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: 'report',
            subType: 'Transactions'
          },
          replaceUrl: true
        }
      );
    }
  }
  getCreditPrice(fare) {
    if(fare.displayPrice){
      return fare.displayPrice;
    }else{
    return fare.price;
    }
  }
  getTotalPrice(){
    let finalPrice = 0;
    let totalTraveler =[];
      totalTraveler.push(this.traveler);
    if(this.tripDetails){
      this.tripDetails.forEach((e)=>{
        // let price = this.getTotalFare(e.transaction.data,e.detail[0])
        let price = CommonUtils.getTotalFare(e.transaction.data,e.detail[0],this.gallopCash,totalTraveler);
        finalPrice = finalPrice + price;

      })
      return finalPrice;
    } 
  }



  getCurrencyByItem(){
    let  detail =  this.getTripDetailsAndType(0).detail;
    if(detail.type == "flight"){
    return detail.option.flight_option;
   }else if(detail.type == "hotel"){
     return detail.option.hotel_option;
   }else if(detail.type == "cars"){
     return detail.option.car_option;
   };
   }

   getTripDetailsAndType(i){
    if(this.tripDetails){
      const tripDetail = this.tripDetails[i];
      const type = tripDetail.detail[0].type;
      const detail = tripDetail.detail[0];
      return {
        tripDetail:tripDetail,
        type:type,
        detail:detail
      };
    }

  }

  getHotelPhoneNo(hotel): any {
    if (hotel.option.hotel_option.phoneNumber && hotel.option.hotel_option.phoneNumber.length > 0) {
      return hotel.option.hotel_option.phoneNumber[0].number;
    }
    else {
      return 'phone number not available';
    }
  }
  getHotelPolicy(hotel): string {
    let roomPolicy = '';
    let policyArray = hotel.option.hotel_option.hotelRooms[0].hotelRates[0].cancellationPolicies;
    if (policyArray) {
      for (let policy of policyArray) {
        if (policy.cancellationRule) {
          roomPolicy = roomPolicy + policy.cancellationRule + ',';
        }
      }
      return roomPolicy;
    } else {
      return null;
    }
  }
  goToTripId(addTripitModal) {
    if (this.showCheckBox) {
      this.bsModalRef = this.modalService.show(addTripitModal);
    } else {
      if (this.hotelList.length > 0) {
        window.open(this.tripItUrl);

      } else {
        window.open(this.tripItHotelUrl);
      }
    }
  }
  goToCalendar(addCalendarModal) {
    if (this.showCheckBox) {
      this.bsModalRef = this.modalService.show(addCalendarModal);
    } else {
      this.modalShow = true;
      this.modalShow1 = false;
      this.ngxSmartModalService.getModal('calendarOptionModal').open();
      // if(this.hotelList.length>0){
      // window.open('https://'+this.calenderUrl);
      // 
      // }else{
      // window.open('https://'+this.calenderUrlHotel);
      //}
    }
  }
  setTripItUrl(option) {
    if (option) {
      this.showRadioError = false;
    }
  }
  setCalendarUrl(option) {
    if (option) {
      this.errorInCalenderRadio = false;
    }
  }
  proceedtoTripIt() {

    if (!this.radioSelect) {
      this.showRadioError = true;
    } else {
      this.showRadioError = false;
      if (this.radioSelect == 'hotel') {
        window.open(this.tripItHotelUrl);
        this.bsModalRef.hide();
      } else {
        window.open(this.tripItUrl);
        this.bsModalRef.hide();
      }
    }
  }

  getConfirmationNo(item): string {
    let confirmNo = '';
    for (let leg of item.option.flight_option.legs) {
      for (let hop of leg.hops) {
        if (hop.pnr) {
          confirmNo = confirmNo + hop.pnr + ',';
        }
      }
    }
    var trim = confirmNo.replace(/(^,)|(,$)/g, "");
    return trim;
  }
  getLayoverDetails(item, i, j): boolean {
    if (item.option.flight_option.layoverInfo.length > 0) {
      if (item.option.flight_option.layoverInfo[i][j]) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }
  getHopSeatNo(hop): boolean {
    if (hop.seatNo && hop.seatNo != ' ') {
      return true;
    } else {
      return false;
    }
  }
  getDurationOfHop(legIndex: number): string {
    let result = '';

    var totalTime = legIndex;
    var quotient = Math.floor(totalTime / 60);
    var rem = (totalTime % 60);
    return result = quotient + 'h' + ' ' + rem + 'm';
  }
  getDuration(flight, legIndex: number) {
    return this.getFlightLegDuration(flight, legIndex);
  }
  getFlightLegDuration(flight, legIndex: number): any {
    let durationInMins = this.getFlightLegDurationInMin(flight, legIndex);
    return DateUtils.getDurationAsHrsMinObj(durationInMins);
  }

  getFlightLegDurationInMin(flight, legIndex: number): any {
    if (!flight && !flight.option.flight_option.legs[legIndex]) return 0;
    let startDate = new Date(flight.option.flight_option.legs[legIndex].hops[0].starts);
    let endDate = new Date(flight.option.flight_option.legs[legIndex].hops[flight.option.flight_option.legs[legIndex].hops.length - 1].ends);

    return DateUtils.getDateDiffInMinutes(endDate, startDate);
  }
  getAirlineFullName(code) {
    let airlineFullName = null;
    airlineFullName = this.airlines[code];
    return (airlineFullName === '' || airlineFullName === null || airlineFullName === undefined) ? code : airlineFullName;
  }

  getAirportCity(code) {

    return code && this.airports[code] && this.airports[code]['name'] ? this.airports[code].name : code;
  }
  getDisplayDate(dateString: string, format: string): string {
    return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
  }
  getDisplayDateTimeForFlights(dateString: string, format: string): string {
    return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }

  showModal(roomGalleryModal) {
    this.bsModalRef = this.modalService.show(roomGalleryModal, { class: 'historyModal' });
  }
  onModelCancel() {
    this.bsModalRef.hide();
  }
  cancelRequest(item: any) {
    this.sendRequestProgress = true;
    let ticketid = item[0].ticketid;
    let tripid = item[0].tripid;
    let selectTransId = item[0].option.selectTransId;
    this.bookingHistoryService.getPost(ticketid, tripid, selectTransId).subscribe(data => {
      this.bsModalRef.hide();
      this.sendRequestProgress = false;
      this.bsModalRef = this.modalService.show(CancelSuccessComponent, {
        initialState: {
          message: this.translateService.instant('bookingHistory.YourcancellationRequesthasbeensentsuccessfully.'),
        },
      })
    })

  }
  getChecked() {
    this.checked = false;
  }
  onRequestChange(item: any) {
    if (this.messageForChange) {
      this.showChangeRequestError = false;
      this.checked = true;
      let ticketid = item[0].ticketid;
      let tripid = item[0].tripid;
      let selectTransId = item[0].option.selectTransId;
      this.bookingHistoryService.getPostOnChange(ticketid, tripid, selectTransId, this.messageForChange).subscribe(data => {
        this.bsModalRef.hide();
        this.bsModalRef = this.modalService.show(CancelSuccessComponent, {
          initialState: {
            message: this.translateService.instant('bookingHistory.Yourchangerequesthasbeensentsuccessfully'),
          },
        })
      });
    } else {
      this.showChangeRequestError = true;
    }
  }
  onExpensifyAdd(item: any) {
    if (this.expensifyEmailControl.valid && this.expensify_add.length > 0 || this.expensify_add_hotel.length > 0) {
      let expensifyType = '';
      this.sendRequestProgress = true;
      this.errorMsg = false;
      if (this.expensify_add.length > 0 && this.expensify_add_hotel.length > 0) {
        expensifyType = 'All';
      } else {
        if (this.expensify_add.length > 0) {
          expensifyType = 'flight';
        } else if (this.expensify_add_hotel.length > 0) {
          expensifyType = 'hotel';
        }
      }

      let ticketid = item[0].ticketid;
      let tripid = item[0].tripid;
      let selectTransId = item[0].option.selectTransId;
      this.bookingHistoryService.getPostExpensifyAdd(ticketid, tripid, selectTransId, expensifyType, this.expensifyEmailControl.value, 'EXPENSE_EXPENSIFY').subscribe(data => {
        this.bsModalRef.hide();
        this.sendRequestProgress = false;
        this.bsModalRef = this.modalService.show(CancelSuccessComponent, {
          initialState: {
            message: 'Expense sent successfully !!',
          },
        });
      });
    } else if (this.expensify_add.length == 0 && this.expensify_add_hotel.length == 0) {
      this.errorMsg = true;
    }
  }
  detailViewMobile(text) {
    this.detailView = true;
    switch (text) {
      case 'flight':
        this.flightView = true;
        this.hotelView = false;
        this.combinedView = false;
        break;
      case 'hotel':
        this.flightView = false;
        this.hotelView = true;
        this.combinedView = false;
        break;
      case 'combined':
        this.flightView = false;
        this.hotelView = false;
        this.combinedView = true;
        break;
    }
  }
  getClassName(id) {
    let className = null;

    this.classOptions.map(item => {
      if (item.id.toLowerCase() == id.toLowerCase()) {
        className = item.value;
      }
    });

    if (className === null) {
      return id;
    }

    return className;

  }
  getClassNameAndBrandName(hop: any) {
    let cabinClass = CommonUtils.classNameReturnFromMap(this.cabinClass, hop.carrier, hop.type);
    if (cabinClass) {
      return cabinClass;
    }
    let className = this.getClassName(hop.type);
    if (hop.brandDetail && hop.brandDetail.trim().length > 0 && hop.brandDetail.trim() != 'null') {
      let brandObj = JSON.parse(hop.brandDetail);
      if (brandObj.name && brandObj.name.trim().length > 0) {
        className = className + ' (' + brandObj.name.trim() + ')';
      }
    }
    return className;
  }

  goToSearch() {
    this.router.navigate(['search']);
  }
  getLegWiseLayoverList(flight): any[] {

    let flightLayoverInfoList = [];

    if (flight) {

      flight.option.flight_option.legs.forEach((leg) => {

        let numOfHops = leg.hops.length;

        let flightLegLayoverInfoList = [];

        if (numOfHops > 1) {

          for (let i = 0; i < numOfHops - 1; i++) {

            var layoverIn = (leg.hops[i].to == leg.hops[i + 1].from) ?
              leg.hops[i].to :
              leg.hops[i].to + "/" + leg.hops[i + 1].from;

            let flightLegLayoverInfo = { in: '', duration: {} };
            flightLegLayoverInfo.in = layoverIn;

            var hop1Date = new Date(leg.hops[i].ends);
            var hop2Date = new Date(leg.hops[i + 1].starts);

            var timeDiffInMin = DateUtils.getDateDiffInMinutes(hop2Date, hop1Date);

            flightLegLayoverInfo.duration = DateUtils.getDurationAsHrsMinObj(timeDiffInMin);

            flightLegLayoverInfoList.push(flightLegLayoverInfo);
          }
        }

        if (flightLegLayoverInfoList.length > 0) {
          flightLayoverInfoList.push(flightLegLayoverInfoList);
        }

      })

    }
    return flightLayoverInfoList;
  }


  private canShowLineItems() {
    let canShow: boolean = true;
    if (this.detailList) {
      for (let item of this.detailList) {
        if (item.type === 'flight' && !(item.option.status === 'ticketed' || item.option.status === 'expensed')) {
          canShow = false;
          break;
        } else if (item.type === 'hotel' && !(item.option.status === 'booked' || item.option.status === 'expensed')) {
          canShow = false;
          break;
        }
      }
    } else {
      canShow = false;
    }
    return canShow;
  }

  private canShowGallopCashEarning() {
    //
    return this.userAccountService.getSubscriptionPlan()
      && this.userAccountService.getSubscriptionPlan() != null
      && this.userAccountService.getSubscriptionPlan().id.toString() !== 'subscription_free';
  }

  private isTransactionApproved() {
    return this.detailTransaction && this.detailTransaction.status === 'approved';
  }

  private getGallopCashEarning(): number {
    let earning = 0;
    if (this.detailList) {
      for (let item of this.detailList) {
        if (item.type === 'flight' && (item.option.status === 'ticketed' || item.option.status === 'expensed')) {
          earning = earning + Number.parseFloat(item.option.flight_option.price);
        } else if (item.type === 'hotel' && (item.option.status === 'selected' || item.option.status === 'ticketed' || item.option.status === 'booked' || item.option.status === 'expensed')) {
          earning = earning + Number.parseFloat(item.option.hotel_option.minPrice);
        }
        else if (item.type === 'cars' && (item.option.status === 'selected' || item.option.status === 'ticketed' || item.option.status === 'booked' || item.option.status === 'expensed')) {
          earning = earning + Number.parseFloat(item.option.car_option.price);
        }
      }
    }
    return this.userAccountService.getGallopCashBack(earning);
  }
  getOpenModal() {
    if (!this.selectOption) {
      this.errorInCalenderRadio = true;
    } else {
      this.modalShow = false;
      this.modalShow1 = true;
      this.selectOptionForModal = this.selectOption;
      this.ngxSmartModalService.getModal('calendarOptionModal').open();
    }
  }
  onSmartModelCancel(modelName: string) {
    this.ngxSmartModalService.getModal(modelName).close();
  }


  downloadInvoices() {
    if (this.invoiceUrl) {
      let count = 1;
      for (let url of this.invoiceUrl) {
        url = url+'&selectedLanguage='+this.userCredentials.getLang();
        setTimeout(() => {
          window.open(url, "_blank");
        }, 500 * count);
        count++;
      }
    }
  }

  showCalenderButton() {
    return (this.calenderUrl && this.calenderUrl.trim().length > 0) || (this.calenderUrlHotel && this.calenderUrlHotel.trim().length > 0);
  }

  showTripItButton() {
    return (this.tripItUrl && this.tripItUrl.trim().length > 0) || (this.tripItHotelUrl && this.tripItHotelUrl.trim().length > 0);
  }

  showDownloadInvoiceButton() {
    return this.invoiceUrl && this.invoiceUrl.length > 0;
  }

  ngOnDestroy() {
    this.fetchAccountInfoSubscription.unsubscribe();
    this.deviceSubscription.unsubscribe();
  }
  getAgentEmailId() {
    return this.clientConfig.agentEmailId;
  }
  getBrandName() {
    return this.clientConfig.brandName;
  }
  getHelpDeskPhoneNumber() {
    return Constants.GALLOP_HELP_CONTACT_NO;
  }
  getTripDetails(){
    return this.tripDetails;
  }
  getDetailsResponse(){
    return this.bookingHistoryDetailResp.data;
  }
  getTripName(){

    let  detail =  this.bookingHistoryDetailResp.data.tripDetail[0].detail[0];
    if(detail.tripSessionName != undefined){
      return detail.tripSessionName
    }else{
      if(detail.type ==  "flight"){
        return this.getAirportCity(detail.option.flight_option.legs[0].hops[detail.option.flight_option.legs[0].hops.length-1].to);
      }else if(detail.type == "hotel"){
        detail.option.hotel_option.address;
      }else if(detail.type == "cars"){
        return detail.option.car_option.dropOffLocation;
      };
    }
  }
  getTrevelerName(){
    let data = this.bookingHistoryDetailResp.data.travellersData.travellers[0];
    return data.firstName + " " + data.lastName+"'s Trip: ";
  }
}
