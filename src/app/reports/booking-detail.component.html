<div class="main-wrapper">
    <div class="content">
        <div class="container" style="height: 100vh;">

            <div class="tab" *ngIf="!detailView">
                <!-- <div class="top-strip booking-history-detail-heading">
                            <a class="booking-history-heading-link" href="javascript:void(0)" (click)="backToList()">
                               <img src="assets/images/hotel/backarrow.svg" alt="">
                            </a>
                            <h4>{{'bookingHistory.BacktoTrips' | translate}}</h4>
                </div> -->
                <div class="top-strip booking-history-detail-heading" style="margin-bottom: 20px !important;">
                    <a class="booking-history-heading-link" [class.active]="viewMode == 'tab1'" rel="tab1"
                        href="javascript:void(0)" (click)="backToReports()">
                        <img src="assets/images/hotel/backarrow.svg" alt="">
                    </a>
                    <span class="back" style="color:#fff !important"> {{'bookingHistory.Back' | translate}}</span>
                </div>

                <!--   <li class="tab-list-item" [class.active]="viewMode == 'tab2'" rel="tab2" (click)="switchBookingListTab('tab2')">
                                <a href="javascript:void(0)">
                                  {{'bookingHistory.PastTrips' | translate}}
                                </a>
                            </li>-->

                <div *ngIf="viewMode=='tab1'">
                    <span class="title"> {{this.travellerName}}' {{'bookingHistory.sUpcomingTrip' | translate}} </span>
                </div>
                <div [ngSwitch]="viewMode">
                    <div id="tab1" class="tab-content-item" *ngSwitchCase="'tab1'">
                        <div class="booking-container" *ngIf="!isEmpty">
                            <div *ngFor="let flight of flightList;let i=index">
                                <div class="result-card-box">
                                    <div *ngIf="flight.type=='flight'">
                                        <div class="result-card-box-inner" (click)="showDetailView(flight)">
                                            <div class="booking-details">
                                                <div class="booking-details-inner">
                                                    <div class="booking-img">
                                                        <img src="assets/images/flight-orange-icon.svg" />
                                                    </div>
                                                    <div class="booking-text">
                                                        <p class="font-bold">
                                                            {{getDisplayDateTimeForFlights(flight.option.flight_option.legs[0].hops[0].starts,
                                                            'EE MMM d')}}</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="origin-destination flight">
                                                <div class="origin-destination-section">
                                                    <p class="font-bold primary-text">
                                                        {{flight.option.flight_option.legs[0].hops[0].from}}</p>
                                                    <p>{{getAirportCity(flight.option.flight_option.legs[0].hops[0].from)}}
                                                    </p>
                                                </div>
                                                <div class="origin-destination-switch">
                                                    <span class="icon-arrow"></span>
                                                </div>
                                                <div class="origin-destination-section">
                                                    <p class="font-bold primary-text">
                                                        {{flight.option.flight_option.legs[0].hops[flight.option.flight_option.legs[0].hops.length-1].to}}
                                                    </p>
                                                    <p>{{getAirportCity(flight.option.flight_option.legs[0].hops[flight.option.flight_option.legs[0].hops.length-1].to)}}
                                                    </p>
                                                </div>

                                            </div>
                                            <div class="booking-view-button">
                                                <button class="btn primary-button"
                                                    (click)="showDetailView(flight)">{{'bookingHistory.ViewDetail' | translate}}</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="flight.type =='hotel'">
                                        <div class="result-card-box-inner" (click)="showDetailView(flight)">
                                            <div class="booking-details">
                                                <div class="booking-details-inner">
                                                    <div class="booking-img"><img
                                                            src="assets/images/hotel-booking-history.svg" /></div>
                                                    <div class="booking-text">
                                                        <p>{{'bookingHistory.CheckIn' | translate}}</p>
                                                        <p class="font-bold">
                                                            {{getDisplayDate(flight.option.hotel_option.checkInDate, 'EE
                                                            MMM d')}}</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="origin-destination hotel">
                                                <div class="destination-hotel">
                                                    <p class="font-bold primary-text">
                                                        {{flight.option.hotel_option.hotelName}}</p>
                                                    <p>{{flight.option.hotel_option.address}}</p>
                                                </div>
                                                <div class="hotel-nights-count" style="white-space:nowrap;">
                                                    <p>{{flight.option.hotel_option.stay}}-{{flight.option.hotel_option.stay
                                                        > 1 ? ('bookingHistory.Nights' | translate)
                                                        :('bookingHistory.Night' | translate)}}</p>
                                                </div>
                                            </div>
                                            <div class="booking-view-button">
                                                <button class="btn primary-button"
                                                    (click)="showDetailView(flight)">{{'bookingHistory.ViewDetail' |
                                                    translate}}</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="flight.type =='cars'">
                                        <div class="result-card-box-inner" (click)="showDetailView(flight)">
                                            <div class="booking-details">
                                                <div class="booking-details-inner">
                                                    <div class="booking-img">
                                                        <i class="fa fa-car" class="addlue"
                                                            style="margin-right:5px;c"
                                                            aria-hidden="true"></i>
                                                    </div>
                                                    <div class="booking-text">
                                                        <p>Pick-up date:</p>
                                                        <p *ngIf="flight.option.car_option.pickUpDate!==null"
                                                            class="font-bold">
                                                            {{getDisplayDateTimeForFlights(flight.option.car_option.pickUpDate,
                                                            'EE MMM d')}}</p>
                                                        <!-- <p class="font-bold">  {{flight.option.hotel_option.checkInDate | date:'EE MMM d'}}</p> -->
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="origin-destination hotel">
                                                <div class="destination-hotel">
                                                    <p class="font-bold primary-text">
                                                        {{flight.option.car_option.description}}</p>
                                                    <p>{{flight.option.car_option.pickUpLocation}}</p>
                                                </div>
                                                <div class="hotel-nights-count">
                                                    <span
                                                        style="white-space: nowrap;">{{flight.option.car_option.numberOfDay}}-{{flight.option.car_option.numberOfDay
                                                        > 1 ? 'days':'day'}}</span>
                                                </div>

                                            </div>
                                            <div class="booking-view-button">
                                                <button class="btn primary-button"
                                                    (click)="showDetailView(flight)">{{'bookingHistory.ViewDetail'
                                                    | translate}}</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="empty-container" *ngIf="isEmpty">
                            <div class="empty-img"><img src="assets/images/no-flight-found.png" /></div>
                            <div class="empty-text">{{'bookingHistory.Youdonthaveanyupcomingtrips' | translate}} </div>
                            <div class="empty-button">
                                <!--         <button class="btn primary-button">{{'bookingHistory.BOOKNOW' | translate}}</button>-->
                            </div>
                        </div>
                    </div>
                    <!-- <div id="tab2" class="tab-content-item" *ngSwitchCase="'tab2'">
                      <div class="booking-container"  *ngIf="!isEmpty2">
                        <div *ngFor="let flight of flightList2;let i=index">
                          <div class="result-card-box">
                              <div *ngIf="flight.type=='flight'">
                              <div class="result-card-box-inner" (click)="showDetailView()">
                                  <div class="booking-details">
                                      <div class="booking-details-inner">
                                          <div class="booking-img">
                                              <img src="assets/images/flight-orange-icon.svg" />
                                          </div>
                                          <div class="booking-text">
                                              <p class="font-bold">{{getDisplayDateTimeForFlights(flight.option.flight_option.legs[0].hops[0].starts,
                                                  'EE MMM d')}}</p>
                                          </div>
                                      </div>
                                  </div>
                                  <div class="origin-destination flight">
                                      <div class="origin-destination-section">
                                          <p class="font-bold primary-text">{{flight.option.flight_option.legs[0].hops[0].from}}</p>
                                          <p>{{getAirportCity(flight.option.flight_option.legs[0].hops[0].from)}}</p>
                                      </div>
                                      <div class="origin-destination-switch">
                                          <span class="icon-arrow"></span>
                                      </div>
                                      <div class="origin-destination-section">
                                          <p class="font-bold primary-text">{{flight.option.flight_option.legs[0].hops[flight.option.flight_option.legs[0].hops.length-1].to}}</p>
                                          <p>{{getAirportCity(flight.option.flight_option.legs[0].hops[flight.option.flight_option.legs[0].hops.length-1].to)}}</p>
                                      </div>
                                  </div>
                                  <div class="booking-view-button">
                                      <button class="btn primary-button" (click)="showDetailView()">{{'bookingHistory.ViewDetail' | translate}}</button>
                                  </div>
                              </div>
                          </div>
                         <div *ngIf="flight.type=='hotel'">
                            <div class="result-card-box-inner" (click)="showDetailView()">
                                <div class="booking-details">
                                    <div class="booking-details-inner">
                                        <div class="booking-img"><img src="assets/images/hotel-booking-history.svg" /></div>
                                        <div class="booking-text">
                                            <p>{{'bookingHistory.CheckIn' | translate}}</p>
                                            <p class="font-bold">  {{getDisplayDate(flight.option.hotel_option.checkInDate, 'EE MMM d')}}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="origin-destination hotel">
                                    <div class="destination-hotel">
                                        <p class="font-bold primary-text">{{flight.option.hotel_option.hotelName}}</p>
                                        <p>{{flight.option.hotel_option.address}}</p>
                                    </div>
                                    <div class="hotel-nights-count">
                                      {{flight.option.hotel_option.stay}}-{{flight.option.hotel_option.stay > 1 ? ('bookingHistory.Nights' | translate) :('bookingHistory.Night' | translate)}}
                                    </div>
                                </div>
                                <div class="booking-view-button">
                                    <button class="btn primary-button" (click)="showDetailView()">{{'bookingHistory.ViewDetail' | translate}}</button>
                                </div>
                            </div>
                          </div>
                       </div>
                      </div>
                      </div>
                        <div class="empty-container"  *ngIf="isEmpty2">
                        <div class="empty-img"><img src="assets/images/no-flight-found.png" /></div>
                        <div class="empty-text">{{'bookingHistory.Youdonthaveanypasttrips' | translate}} </div>
                        <div class="empty-button">
                         <button class="btn primary-button">{{'bookingHistory.BOOKNOW' | translate}}</button>
                        </div>
                      </div>
                   </div>-->
                </div>
            </div>
            <div class="booking-history-detail" *ngIf="detailView">
                <div *ngIf="this.adminPanelService.subTabForBookingDetail!=='active'" class="row"
                    style="margin-left:-22px !important;">
                    <div class="col-lg-7 col-md-6 col-sm-4 col-xs-4"
                        style="text-align: left;margin-bottom:20px;margin-left: 12px;">
                        <img class="image11" style="display: none;" src="assets/images/reports_purple.png">
                        <div class="view12"><span class="view" style="display: none;">  {{'report.Reports' | translate}}</span><span class="line1" style="display: none;"></span><span
                                style="font-size:20px;display: none;">{{'report.Transactions' | translate}}</span></div>
                    </div>
                    <div class="col-lg-5 col-md-6 col-sm-4 col-xs-4">

                    </div>
                </div>
                <div *ngIf="this.adminPanelService.subTabForBookingDetail==='active'" class="row"
                    style="margin-top: 0px;margin-bottom:20px;margin-left:-22px !important;">
                    <div style="text-align: left;display:inline-block;">
                        <img class="image11" style="display: none;" src="assets/images/duty_of_care_purple.png">
                        <div class="view12"><span class="view" style="display: none;">{{'activeTraveler.DutyofCare' | translate}}</span><span class="line1" style="display: none;"></span><span
                                style="font-size:20px;display: none;"> {{'activeTraveler.ActiveTravelers' | translate}}</span></div>
                    </div>
                </div>
                <div class="top-strip booking-history-detail-heading">
                    <a class="booking-history-heading-link" href="javascript:void(0)" (click)="backToReports()">
                        <img src="assets/images/hotel/backarrow.svg" alt="">
                    </a>
                    <h4>{{'bookingHistory.Backto' | translate}} {{this.backTo | translate}}</h4>
                </div>
                <div *ngIf="this.detailList && this.detailList.length===0"
                    style="text-align:center !important;margin-top:30px !important;">
                    <div class="text1 text-danger" style="font-size: 18px;">{{resultErrorMessage | translate}}</div>
                </div>
                <app-loader *ngIf="this.detailList.length===0" [spinnerStyle]="true"></app-loader>
                <div class="booking-history-detail-content">
                    <div class="booking-history-detail-content-inner">
                        <div class="booking-history-detail-content-left">
                            <div *ngFor="let item of detailList;let i=index">
                                <div class="booking-history-upper-header" *ngIf="i == 0">
									<div class="font-bold" >
										<span *ngIf="getTripName()">{{getTrevelerName()}} {{getTripName()}}</span>
									</div>
								</div>
                                <app-trip-details-card 
								[details]="item" 
								[tripDeatilsIndex]="i"
								[detailList]="this.detailList" 
								[newtripDetails]="getTripDetails()" 
                                [showDetailsActionButtons]="false"
                                [getFullDetailsResponse]="getDetailsResponse()"
                                [forReports]="true"
								></app-trip-details-card>
                            </div>
                        </div>
                            <div *ngIf="this.detailList.length > 0 " class="booking-history-detail-content-right"
                                [ngStyle]="{'padding-bottom':(tagShow && tagShow.length >0 && this.dropDownopen) ? '200px':'20px'}">
                                <div *ngIf="showDownloadInvoiceButton()" class="booking-button-text-container" style="border-top: none !important;">
                                    <button class="button-text">
                                        <span class="button-text-img"> <img src="assets/images/ic_receipt.svg" alt="" />
                                        </span> <span attr.data-track="TripDetailsPageButton" attr.data-params="button=DownloadInvoice"
                                            (click)="downloadInvoices()">{{'bookingHistory.DOWNLOADINVOICE'
                                            | translate}}</span>
                                    </button>
                                </div>
                                <hr style="margin-top: 0px !important;">
                                <div *ngFor="let item of this.traveler">
                                    <div class="title">{{'bookingHistory.Travelercontactdetails' | translate}}:</div>
                                    <div style="font-weight: bolder;font-family: var(--globalFontfamilyr);font-weight: bold;;">{{item.firstName}}
                                        {{item.lastName}} </div>
                                    <div>{{item.phoneNumber}}</div>
                                    <div>{{item.email}}</div>
                                </div>
                                <hr *ngIf="this.traveler[0] && this.traveler[0].emergencyContactName && this.traveler[0].emergencyContactName.length>0"
                                    style="margin-top: 0px !important;">
                                <div *ngFor="let item of this.traveler">
                                    <div *ngIf="item.emergencyContactName && item.emergencyContactName.length>0" class="title">{{item.firstName}}
                                        {{item.lastName}}' {{'bookingHistory.semergencycontact' | translate}}:</div>
                                    <div *ngIf="item.emergencyContactName && item.emergencyContactName.length>0"
                                        style="font-weight: bolder;font-family: var(--globalFontfamilyr);font-weight: bold;;">
                                        {{item.emergencyContactName}}({{item.relationship}})</div>
                                    <div *ngIf="item.contactNumber">{{item.contactNumber}}</div>
                                </div>
                                <hr *ngIf="tagset && tagset.length > 0" style="margin-top: 0px !important;">
                                <div *ngIf="tagset && tagset.length > 0">
                                    <div>
                                        <app-dropdown class="trip-tags-elem" 
                                            [tagset]="tagset" 
                                            [tagShow]="tagShow" 
                                            [changing]="changingValue"
                                            [selectedTag]="selectedTag" 
                                            [dropDownopen]="dropDownopen" 
                                            [reportComponet]="reportComponet"
                                            [newTagSetsArray]="tagsSetData.tags"
                                            (goBackDropDownOptions)="handleResponseFromDropdwn($event)">
                                        </app-dropdown>
                                    </div>
                                </div>
                                <div class="total-price-box" *ngIf="bookingHistoryDetailResp">
                                    <div class="total-price-box-inner-div">
                                        <div class="total-price-label">{{'bookingHistory.TotalPrice' |translate}}</div>
                                        <div class="price-div">{{getTotalPrice() | currency : getCurrencySymbol(getCurrencyByItem()) :'code': '1.2-2'}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <ng-template #addExpensifyModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                {{'bookingHistory.AddtoExpensify' | translate}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
                <i class="material-icons">close</i>
            </button>
        </div>
        <div class="modal-body">
            <div *ngIf="this.showCheckBox">
                <label>{{'bookingHistory.ExpensifyAddDetails' | translate}}</label>
                <div *ngFor="let option of expensifyType">
                    <div class="header-inner">
                        <div class="header-left"> <input type="checkbox" id="{{option.name}}" class="mdl-checkbox"
                                (change)="onExpensifyClicked(option.value, $event)"
                                [checked]="isExpensifyChecked(option.value)"></div>
                        <div class="header-right"><span class="mdl-checkbox__label">{{option.name | translate}}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="input-field">
                <label style="font-size: 16px;">{{'bookingHistory.ExpensifyLoginEmail' | translate}}</label>
                <form method="post" id="expensifyForm" class="expensifyForm">
                    <div class="row">
                        <div class="col-lg-12  col-md-12 col-sm-12 col-xs-12">
                            <div class="input-box">
                                <input [formControl]="expensifyEmailControl" class="input-textfield input-textfield-lg"
                                    id="expensifyLoginEmail" name="expensifyLoginEmail"
                                    placeholder="{{'bookingHistory.ExpensifyLoginEmail' | translate}}" type="text">
                            </div>
                            <div *ngIf="expensifyEmailControl.hasError('required') && (expensifyEmailControl.touched || expensifyEmailControl.dirty)"
                                class="error">{{'bookingHistory.Thisfieldisrequired' | translate}}
                            </div>
                            <div *ngIf="expensifyEmailControl.hasError('pattern') && (expensifyEmailControl.touched || expensifyEmailControl.dirty)"
                                class="error">{{'bookingHistory.Pleaseenteravalidemail' | translate}}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div *ngIf="this.errorMsg">
                <span class="text-danger">{{'bookingHistory.Pleaseselectanyofcheckboxdefineabove' | translate}}</span>
            </div>
            <div class="modal-form-button">
                <button *ngIf="!sendRequestProgress" class="btn primary-button"
                    (click)="onExpensifyAdd(detailList)">Send Receipt to Expense</button>
                <button *ngIf="sendRequestProgress" class="btn primary-button">{{'paymentDetails.Pleasewait' |
                    translate}}
                </button>
            </div>
        </div>
    </ng-template>


    <ng-template #requestChangeModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                {{'bookingHistory.RequestChanges' | translate}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
                <i class="material-icons">close</i>
            </button>
        </div>
        <div class="modal-body requestModalBody">
            <div class="input-field">
                <label>{{'bookingHistory.PleasetelluswhatchangeyouwouldlikeforyourTrip' | translate}}</label>
                <div *ngFor="let item of detailList">
                    <div *ngIf="item.type =='flight'">
                        <label
                            class="font-bold">{{getAirlineFullName(item.option.flight_option.legs[0].hops[0].ocarrier)}}:{{getDisplayDateTimeForFlights(item.option.flight_option.legs[0].hops[0].starts,
                            'EE MMM d')}} {{'bookingHistory.from' | translate}}
                            {{item.option.flight_option.legs[0].hops[0].from}} {{'bookingHistory.to' | translate}}
                            {{item.option.flight_option.legs[0].hops[item.option.flight_option.legs[0].hops.length-1].to}}
                        </label>
                    </div>
                    <div *ngIf="item.type =='hotel'">
                        <label class="font-bold">
                            {{'bookingHistory.CheckIn' |
                            translate}}{{getDisplayDate(item.option.hotel_option.checkInDate , 'EE MMM
                            d')}}-{{item.option.hotel_option.hotelName}},{{item.option.hotel_option.address}}
                        </label>
                    </div>
                </div>
                <textarea placeholder="{{'bookingHistory.Adddetailsforyourchange' | translate}}"
                    [(ngModel)]="messageForChange" (focus)="getChecked()"
                    class="modal-textarea input-textfield"></textarea>
            </div>
            <div *ngIf="showChangeRequestError">
                <span class="text-danger">{{'bookingHistory.youmusthavetowritethechangemessage' | translate}} </span>
            </div>
            <div class="modal-form-button">
                <button class="btn primary-button" (click)="onRequestChange(detailList)"
                    [disabled]="this.checked">{{'bookingHistory.SubmitChangeRequest' | translate}}</button>
            </div>
        </div>
        <div class="modal-footer">
            <p><span>{{'bookingHistory.Note' | translate}}</span>
                {{'bookingHistory.Sendingarequestwillnotimpactormodifyyourreservationwithoutfurtheraction.YouwillreceiveanemailfromTripwiththebestoptionsandapplicablechangefeeorfaredifferencesforyoutorespondtobeforeachangeisexecuted'
                | translate}}</p>
        </div>
    </ng-template>


    <ng-template #requestCancelModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                {{'bookingHistory.RequestCancellation' | translate}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
                <i class="material-icons">close</i>
            </button>
        </div>
        <div class="modal-body requestModalBody">
            <div class="input-field">
                <label>{{'bookingHistory.Tripcanhelpyoucancelyourreservationforyourbooking' | translate}}</label>
                <div *ngFor="let item of detailList">
                    <div *ngIf="item.type =='flight'">
                        <label
                            class="font-bold">{{getAirlineFullName(item.option.flight_option.legs[0].hops[0].ocarrier)}}:{{getDisplayDateTimeForFlights(item.option.flight_option.legs[0].hops[0].starts,
                            'EE MMM d')}} {{'bookingHistory.from' | translate}}
                            {{item.option.flight_option.legs[0].hops[0].from}} {{'bookingHistory.to' | translate}}
                            {{item.option.flight_option.legs[0].hops[item.option.flight_option.legs[0].hops.length-1].to}}
                        </label>
                    </div>
                    <div *ngIf="item.type =='hotel'">
                        <label class="font-bold">
                            {{'bookingHistory.CheckIn' |
                            translate}}{{getDisplayDate(item.option.hotel_option.checkInDate, 'EE MMM
                            d')}}-{{item.option.hotel_option.hotelName}},{{item.option.hotel_option.address}}
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-form-button">
                <button *ngIf="!sendRequestProgress" class="btn primary-button"
                    (click)="cancelRequest(detailList)">{{'bookingHistory.SubmitCancellationRequest' |
                    translate}}</button>
                <button *ngIf="sendRequestProgress" class="btn primary-button">{{'paymentDetails.Pleasewait' |
                    translate}}
                </button>
            </div>
        </div>
        <div class="modal-footer">
            <p><span>{{'bookingHistory.Note' | translate}}</span>
                {{'bookingHistory.Thisisonlyarequestforcancellationandwontcancelyourflight/hotelrightaway.YouwillreceiveanemailfromTripwiththedetailsofthecancellationpenaltyandapplicablerefundsbeforethisiscancelled'
                | translate}}</p>
        </div>
    </ng-template>

    <ng-template #addTripitModal let-modal>
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                {{'bookingHistory.AddtoTripIt' | translate}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
                <i class="material-icons">close</i>
            </button>
        </div>
        <div class="modal-body">
            <div *ngFor="let option of expensifyType">
                <div class="header-inner">
                    <div class="header-left"><input id="{{option.name}}" value="{{option.value}}"
                            class="mdl-radio__outer-circle" type="radio" name="{{option.name}}"
                            [(ngModel)]="radioSelect" (change)="setTripItUrl(option.value)"></div>
                    <div class="header-right"> <span class="mdl-radio__label"> {{option.name | translate}}</span></div>
                </div>
            </div>
            <div *ngIf="this.showRadioError">
                <span class="text-danger">{{'bookingHistory.youmusthavetoselectoneoftheabove' | translate}}</span>
            </div>
            <div class="modal-form-button">
                <button class="btn primary-button" (click)="proceedtoTripIt()">{{'bookingHistory.AddtoTripIt' |
                    translate}}</button>
            </div>
        </div>
    </ng-template>
    <ng-template #addCalendarModal let-modal style="max-width: 310px !important;">
        <div class="modal-header">
            <h5 class="modal-title" id="myModalLabel">
                {{'bookingHistory.AddtoCalender' | translate}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel()">
                <i class="material-icons">close</i>
            </button>
        </div>
        <div class="modal-body" (click)="onSmartModelCancel('calendarOptionModal')">
            <div *ngFor="let option of expensifyType">
                <div class="header-inner">
                    <div class="header-left"> <input id="option_answer_{{option.name}}" [value]='option.value'
                            type="radio" name="type" [(ngModel)]="selectOption" class="mdl-radio__outer-circle"
                            (change)="setCalendarUrl(option.value)"></div>
                    <div class="header-right"> <span class="mdl-radio__label"> {{option.name | translate}}</span></div>
                </div>
            </div>
            <div *ngIf="this.errorInCalenderRadio">
                <span class="text-danger">{{'bookingHistory.youmusthavetoselectoneoftheabove' | translate}} </span>
            </div>
            <div class="modal-form-button">
                <button class="btn primary-button"
                    (click)="getOpenModal();$event.stopPropagation();">{{'bookingHistory.AddtoCalender' |
                    translate}}</button>
            </div>
            <div [hidden]="!this.modalShow1">
                <ngx-new-modal [flightUrl]="this.calenderUrl" [hotelUrl]="this.calenderUrlHotel"
                    [selectOption]="this.selectOptionForModal"></ngx-new-modal>
            </div>
        </div>
    </ng-template>