import { Component, OnInit, ElementRef, HostListener } from '@angular/core';
import { Constants } from '../util/constants';
import { BsModalService, BsModalRef, ModalDirective } from 'ngx-bootstrap/modal';
import { UserAccountService } from '../user-account.service';
import { AdminPanelService, Department, CompanySettings } from '../admin-panel.service';
import { Subscription } from 'rxjs';
import { CompanyReportResponse, UserInfoBasic } from '../entity/company-report-response';
import { DateUtils } from '../util/date-utils';
import { Router, ActivatedRoute } from '@angular/router';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators, FormControl, Form, ValidationErrors, ValidatorFn } from '@angular/forms';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { SearchActionType } from '../enum/search-action.type';
import { COLUMN } from '../enum/report-coloum.type';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { ToastrService } from 'ngx-toastr';
import { CommonUtils } from '../util/common-utils';
import { SearchService } from '../search.service';
import { DatePipe } from '@angular/common';
import { ChangeDetectorRef } from '@angular/core';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { environment } from 'src/environments/environment';
import { ClientConfiguration } from '../client-config.service';
import { IfStmt } from '@angular/compiler';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { Title } from '@angular/platform-browser';
import { DeviceDetailsService } from '../device-details.service';
import { TranslateService } from '@ngx-translate/core';

declare var initializeWdr: any;
declare var isApiCompleted: any
declare var pivotOffButton: any;
declare var emptyPreviousReportData: any;
declare var isFilteredReportDataEmptyJS: any;
declare var getWDRLoadStatus: any;
declare var getPreviousPivotData: any;
declare var getPreviousfilteredData: any;
declare var setTicketNumberClickHandlerFN: any;
declare var clearPreviousSliceColumn: any;
declare var getApidata: any;
declare var currReportConfig: any;
@Component({
    selector: 'app-reports',
    templateUrl: './reports.component.html',
    styleUrls: ['./reports.component.scss'],
    standalone: false
})

export class ReportsComponent implements OnInit {

  changeStyle() {
    if (!this.isMobile1) {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '730px' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'min-width': '230px' };
      } else {
        return { 'min-width': '99vw' }
      }
    }
  }
  changeStyle1() {
    if (!this.isMobile1) {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '335px', 'overflow': 'hidden' }
      }
    } else {
      if (this.viewMode1 === 'tab12') {
        return { 'height': '300px', 'overflow': 'auto' };
      } else {
        return { 'height': '700px', 'overflow': 'hidden' }
      }
    }
  }
  spinnerStyle = false;
  selectedReportDate='transaction'
  reportDateOptions=[{id:'transaction',value:this.translateService.instant('report.Transactiondates')},{id:'travel',value:this.translateService.instant('report.Traveldates')}]
  isMobile1: boolean;
  viewMode1 = 'tab11';
  dateOptions1 = Constants.DATE_OPTIONS;
  dateValue1 = 'CURRMONTH';
  bookingValue = "ALL";
  viewMode2 = 'tab21';
  selectMode = '';
  selectSubMode = '';
  applyBtn = false;
  showPagination = false;
  deviceSubscription1: Subscription;
  cardTrannsactionReport = [];
  previousDate = '';
  totalRecord: number;
  minimumDate: Date = new Date();
  maximumDate: Date = new Date();
  maximumDate1: Date = new Date();
  maxDoBDate: Date = new Date();
  selectedEmployee = '';
  bookingOptions = [{ id: 'ALL', value: 'All Types' }, { id: 'cars', value: 'Car' }, { id: 'flight', value: 'Flight' }, { id: 'hotel', value: 'Hotel' }];
  selectedDepart = '';
  employeeOptions = [{ id: '', value: 'All Employees', departmentId: ' ' }];
  recordsPerPageOptions = [{ id: 20, value: 20 }];
  callComplete = false;
  empValue = '';
  airlines;
  empNameSearchValue = '';
  departmentOptions = [{ value: 'All Departments', id: '' }];
  deptValue = '';
  firstDate: Date;
  wdrData: any;
  lastDate: Date = new Date();
  startDate: Date = new Date();
  endDate: Date = new Date();
  resultErrorMessage = this.translateService.instant('report.FetchingData');
  resultErrorMessage1 = this.translateService.instant('report.FetchingData');
  switchInitValues: Map<string, boolean>;
  switchAppliedValues: Map<string, boolean>;
  bsModalRef: BsModalRef;
  companyReport: Array<any>;
  companyPageReport: Array<any>;
  queryParamSubscription: Subscription;
  companyReportTotal: string;
  companyReportSubscription: Subscription;
  companysettingSubscription: Subscription;
  companySettings: CompanySettings;
  companySettingsSubscription: Subscription;
  currency = 'USD';
  apiReportResponse: CompanyReportResponse;
  downloadData = { start: '', end: '', deptValue: '', empValue: '' };

  recordsPerPage = 20;
  showCardReport = false;
  activePage: number = 1;
  daterangepickerModel = [this.startDate, this.endDate];
  constructor(private modalService: BsModalService,
    private userAccountInfoService: UserAccountService,
    public router: Router,
    public deviceDetailsService: DeviceDetailsService,
    public progressBar: NgxUiLoaderService,
    public translateService: TranslateService,
    private gallopLocalStorage: GallopLocalStorageService,
    private toastr: ToastrService,
    private searchService: SearchService,
    private titleService: Title,
    private cdRef: ChangeDetectorRef,
    public adminPanelService: AdminPanelService,
    private activatedRoute: ActivatedRoute,
    public clientConfig: ClientConfiguration,
    public ngxSmartModalService: NgxSmartModalService,
    public el: ElementRef,) {
     
    this.initSwitch(undefined);
  }
  ticketNumberClickHandlerFN(transid, thisObj) {
    thisObj.bookingDetailsPageForWdrData(transid);
    thisObj.adminPanelService.companySaveReport = getPreviousPivotData();
  }
  tempSlice: any;
  loadCardTransactionData(thisObj) {
  
    thisObj.cardTrannsactionReport = [];
    thisObj.cardTrannsactionReport = getPreviousfilteredData();
    if (thisObj.cardTrannsactionReport && thisObj.cardTrannsactionReport.length > 0) {
      thisObj.cardTrannsactionReport = thisObj.cardTrannsactionReport.slice(1);
      thisObj.downloadForQuickBooks();
    }
  }
  ngOnInit() {
    this.deviceSubscription1 = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
    this.companySettingsubscription();

    this.activePage = 1;
    this.lastDate = new Date();
    var curryear = this.lastDate.getFullYear();
    var currmonth = this.lastDate.getMonth();
    this.startDate = new Date(curryear, currmonth, 1);

    this.daterangepickerModel = [this.startDate, this.endDate];
    this.adminPanelService.wdrdata = this.filterDuplicate();
    // if(this.selectMode==='Card Transactions'){
    getApidata(this.loadCardTransactionData, this);
    // }
    setTicketNumberClickHandlerFN(this.ticketNumberClickHandlerFN, this);
    this.queryParamSubscription = this.activatedRoute.queryParams.subscribe(params => {
      if (params['subType'] == 'Travel Credits') {
        this.selectMode = params['subType'];
        this.titleService.setTitle(this.translateService.instant('report.CreditReport'));
        this.viewMode2 = 'tab21';
        this.getWdrData();
        initializeWdr(this.pivotTableDataUrl(), this.wdrData, this.userAccountInfoService.getUserCompanyId(),this.userAccountInfoService.getUserEmail(), this.currency,this.selectedReportDate,this.userAccountInfoService.getSToken());
      } else if (params['subType'] == 'Card Transactions') {
        this.selectMode = params['subType'];
        this.titleService.setTitle(this.translateService.instant('report.CardTransactions'));
        this.viewMode2 = 'tab21';
        this.getWdrData();
        initializeWdr(this.pivotTableDataUrl(), this.wdrData, this.userAccountInfoService.getUserCompanyId(),this.userAccountInfoService.getUserEmail(), this.currency,this.selectedReportDate,this.userAccountInfoService.getSToken());
      } else {
        this.viewMode2 = 'tab21';
        this.selectMode = params['subType'];
        this.daterangepickerModel = [this.startDate, this.endDate];
        this.getWdrData();
        initializeWdr(this.pivotTableDataUrl(), this.wdrData, this.userAccountInfoService.getUserCompanyId(),this.userAccountInfoService.getUserEmail(), this.currency,this.selectedReportDate,this.userAccountInfoService.getSToken());
      }
    });
    if (this.selectMode === 'Card Transactions') {
      setTimeout(() => {
        this.showCardReport = true;
      }, 300000);
    }
    if (this.adminPanelService.firstReportDate1 && this.adminPanelService.lastReportDate1) {
      this.lastDate = this.adminPanelService.lastReportDate1;
      this.firstDate = this.adminPanelService.firstReportDate1;
      this.startDate = this.adminPanelService.firstReportDate1;
      this.endDate = this.adminPanelService.lastReportDate1;
      this.maximumDate1 = new Date();
      //this.daterangepickerModel=[this.startDate,this.endDate];
    } else {
      var curryear = this.lastDate.getFullYear();
      var currmonth = this.lastDate.getMonth();
      this.startDate = new Date(curryear, currmonth, 1);
      this.maximumDate1 = this.endDate;
      //this.daterangepickerModel=[this.startDate,this.endDate];
    }
    this.maximumDate = this.endDate;
    this.minimumDate = this.startDate;
    this.bookingValue = this.adminPanelService.bookingType;
    this.deptValue = this.adminPanelService.filterAppliedDepartmentId;
    this.empValue = this.adminPanelService.filterAppliedEmployeeId;
    this.empNameSearchValue = this.adminPanelService.filterAppliedEmployeeName;
  }

  getWdrData() {
    for (let item of this.adminPanelService.wdrdata) {
      if (this.adminPanelService.selectedCard !== '' && this.selectMode === 'Card Transactions') {
        if (item.id === this.selectMode) {
          let filterItem = { uniqueName: '', aggregation: '', format: '' }
          item.slice.reportFilters = [];
          item.slice.measures = [];
          filterItem.uniqueName = 'Card number';
          filterItem.aggregation = this.adminPanelService.selectedCard;
          filterItem.format = 'formatForCardnumberColumn';
          let filter1 = { members: [] };
          filter1.members.push('Card number.' + this.adminPanelService.selectedCard);
          let filter2 = { filter: {}, uniqueName: '' }
          filter2.filter = filter1;
          filter2.uniqueName = 'Card number';
          item.slice.reportFilters.push(filter2);
          let i = 0;
          for (let item1 of item.slice.rows) {
            if (item1.uniqueName === 'Card number') {
              let filter1 = { members: [] };
              filter1.members.push('Card number.' + this.adminPanelService.selectedCard);
              let filter2 = { filter: {}, uniqueName: '' }
              filter2.filter = filter1;
              filter2.uniqueName = item1.uniqueName;
              item.slice.rows[i] = filter2;
            }
            i++;
          }
          item.slice.measures.push(filterItem);
          this.wdrData = item;

          this.selectSubMode = item.Title;
        }
      } else if (item.id === this.selectMode) {
        this.wdrData = JSON.parse(JSON.stringify(item));

        this.selectSubMode = item.Title;
      }
    }
  }
  filterDuplicate() {
    let wdrdata = this.adminPanelService.wdrdata.filter((test, index, array) =>
      index === array.findIndex((findTest) =>
        findTest.id === test.id
      ))
    return JSON.parse(JSON.stringify(wdrdata));
  }
  routeToReport(type, type2) {
    
    this.adminPanelService.selectedCard = '';
    setTimeout(() => {
      if (!this.getDataLoadingStatus()) {
        return
      }
    }, 200);
    if (type2 === 'By Traveler') {
      this.titleService.setTitle(this.translateService.instant('report.ComplianceByTraveler'));
    } else if (type2 === 'By Department') {
      this.titleService.setTitle(this.translateService.instant('report.ComplianceByDepartment'));
    } else {
      if (type2 === 'Flights') {
        this.titleService.setTitle(this.translateService.instant('report.Flights') + ' ' + this.translateService.instant('report.Report'));
      }
      if (type2 === 'Cars') {
        this.titleService.setTitle(this.translateService.instant('report.Cars') + ' ' + this.translateService.instant('report.Report'));
      }
      if (type2 === 'Hotels') {
        this.titleService.setTitle(this.translateService.instant('report.Hotels') + ' ' + this.translateService.instant('report.Report'));
      }
      if (type2 === 'Travelers') {
        this.titleService.setTitle(this.translateService.instant('report.Travelers') + ' ' + this.translateService.instant('report.Report'));
      }
      if (type2 === 'Departments') {
        this.titleService.setTitle(this.translateService.instant('report.Departments') + ' ' + this.translateService.instant('report.Report'));
      }
      if (type2 === 'CustomFields') {
        this.titleService.setTitle(this.translateService.instant('report.ProjectTags') + ' ' + this.translateService.instant('report.Report'));
      }
      if (type2 === 'Transactions') { 
        this.titleService.setTitle(this.translateService.instant('report.Transactions') + ' ' + this.translateService.instant('report.Report'));
      }
      if (type2 === 'Travel Credit') {
        this.titleService.setTitle(this.translateService.instant('report.TravelCredit') + ' ' + this.translateService.instant('report.Report'));
      }
    }
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
          subType: type2
        },
        replaceUrl: false
      }
    );
  }
  companySettingsubscription() {
    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {
        this.companySettings = settings;
        this.currency = this.getCurrencySymbol(this.companySettings.company.currency);
      }
    });
  }
  subscription() {
    this.companyReportSubscription = this.adminPanelService.companyReportsResponseObservable$.subscribe((reportResponse) => {
      this.progressBar.stop(SearchActionType.DETAIL);
      if (reportResponse) {
        if (reportResponse.bookingList.length > 0) {
          this.resultErrorMessage = this.translateService.instant('report.FetchingData');
        } else {
          this.resultErrorMessage = this.translateService.instant('report.NoData');
        }
        this.applyBtn = false;
        this.apiReportResponse = reportResponse;
        this.airlines = reportResponse.airlineNames;
        this.buildCompanyReportData(reportResponse);
      } else {
        this.applyBtn = false;
        this.resultErrorMessage = this.translateService.instant('report.NoData');
      }
    });
    // this.companysettingSubscription =  this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {

    if (this.adminPanelService.waitingForCompanySettings) {
      this.initSwitch(this.adminPanelService.getColumnSwitchMap());
      if (!this.adminPanelService.companySaveReport) {
        this.getSelectedCurrentDate1(this.dateValue1);
        this.getReport(true);
      } else {
        this.maximumDate1 = new Date();
        this.startDate = this.adminPanelService.firstReportDate1;
        this.endDate = this.adminPanelService.lastReportDate1;
        this.dateValue1 = this.adminPanelService.dateValue;
        this.getSelectedCurrentDate1(this.dateValue1);
        this.maximumDate1 = new Date();
        let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
        let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);
        this.previousDate = this.adminPanelService.previousReportDate;
        this.downloadData.start = tempdate1;
        this.downloadData.end = tempdate2;
        this.downloadData.deptValue = this.adminPanelService.deptValue;
        this.downloadData.empValue = this.adminPanelService.empValue;
        if (!this.clientConfig.classicReports) {
          if (this.selectMode === 'Transactions') {
            initializeWdr(this.pivotTableDataUrl(), this.wdrData, this.userAccountInfoService.getUserCompanyId(),this.userAccountInfoService.getUserEmail(), this.currency,this.selectedReportDate,this.userAccountInfoService.getSToken());
          }
        }
      }
    }
    //  });
    this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      if (userAccountInfoObj == null) {
        this.apiReportResponse = null;
        this.companyReport = [];
        this.empValue = '';
        this.deptValue = '';
        this.adminPanelService.filterAppliedEmployeeId = '';
        this.adminPanelService.filterAppliedDepartmentId = '';
        this.adminPanelService.filterAppliedEmployeeName = '';
        this.adminPanelService.filterEmployeeListBySearchName = '';
        this.adminPanelService.bookingType = '';
        this.adminPanelService.loginSessionChanged();
      }
    });
  }
  customTabClicked() {
    this.viewMode1 = 'tab11';
  }
  presetsTabClicked() {
    this.viewMode1 = 'tab12';
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  openNgxModal(id, picker) {
    if (isApiCompleted()) {
      return
    }
    setTimeout(() => {
      this.ngxSmartModalService.getModal(id).open()
    }, 100);

    setTimeout(() => {
      this.viewMode1 = 'tab11';
      picker.show();
    }, 200);
  }
  initSwitch(initialValues: Map<string, boolean>) {
    this.switchInitValues = new Map<string, boolean>();
    this.switchInitValues['NAME'] = initialValues ? initialValues['NAME'] : true;
    this.switchInitValues['BK_DATE'] = initialValues ? initialValues['BK_DATE'] : true;
    this.switchInitValues['Department'] = initialValues ? initialValues['Department'] : true;
    this.switchInitValues['BK_TYPE'] = initialValues ? initialValues['BK_TYPE'] : true;
    this.switchInitValues['TRV_DATE'] = initialValues ? initialValues['TRV_DATE'] : true;
    this.switchInitValues['STATUS'] = initialValues ? initialValues['STATUS'] : true;
    this.switchInitValues['DEST'] = initialValues ? initialValues['DEST'] : true;
    this.switchInitValues['POLICY'] = initialValues ? initialValues['POLICY'] : true;
    this.switchInitValues['PRICE'] = initialValues ? initialValues['PRICE'] : true;
    this.switchInitValues['ADV_BK'] = initialValues ? initialValues['ADV_BK'] : false;
    this.switchInitValues['BILL_INF'] = initialValues ? initialValues['BILL_INF'] : false;
    this.switchInitValues['Vendor'] = initialValues ? initialValues['Vendor'] : false;
    this.switchInitValues['Refrence'] = initialValues ? initialValues['Refrence'] : false;
    this.switchInitValues['TRAVELER_NAME'] = initialValues ? initialValues['TRAVELER_NAME'] : false;
    this.applySwitch();
  }

  applySwitch() {
    this.switchAppliedValues = deserialize(JSON.parse(JSON.stringify(this.switchInitValues)), Map);
    // this.switchAppliedValues = new Map<string,boolean>();
    // this.switchAppliedValues.set('NAME',this.switchName);
    // this.switchAppliedValues.set('BK_DATE',this.switchBookingDate);
    // this.switchAppliedValues.set('BK_TYPE',this.switchBookingType);
    // this.switchAppliedValues.set('DEP_DATE',this.switchTravelDate);
    // this.switchAppliedValues.set('STATUS',this.switchBookingStatus);
    // this.switchAppliedValues.set('RET_DATE',this.switchTravelDate);
    // this.switchAppliedValues.set('DEST',this.switchDestination);
    // this.switchAppliedValues.set('POLICY',this.switchWithinPolicy);
    // this.switchAppliedValues.set('PRICE',this.switchPrice);
    // this.switchAppliedValues.set('ADV_BK',this.switchAdvanceDays);
    // this.switchAppliedValues.set('BILL_INF',this.switchBillingInfo);
  }
  showFetchingData() {
    if (this.selectMode === 'Transactions' && this.clientConfig.classicReports) {
      return true;
    } else if (this.selectMode === 'Travel Credits') {
      return true;
    } else {
      return false;
    }
  }
  getQuarter1(date) {
    var m = 0;
    var n = ((date.getMonth() / 3));
    var decimal = ((date.getMonth() % 3))
    if (n >= 0 && n < 1) {
      m = 1;
    } else if (n >= 1 && n < 2) {
      m = 2;
    } else if (n >= 2 && n < 3) {
      m = 3;
    } else if (n >= 3 && n < 4) {
      m = 4;
    }
    var quarter;
    if (m > 4) {
      quarter = (m - 4);
    } else {
      quarter = m;
    }
    if (m && m === 1) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 0, 1);
    } else if (m && m === 2) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 3, 1);
    }
    else if (m && m === 3) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 6, 1);
    }
    else if (m && m === 4) {
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 9, 1);
    }
  }
  getSelectedCurrentDate1(id: string) {
    this.dateValue1 = id;
    if (id === 'CURRMONTH') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(1);
      this.startDate.setMonth(this.startDate.getMonth());
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'TODAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    } else if (id === 'YESTERDAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 1);
      this.endDate.setDate(this.endDate.getDate() - 1);
      this.daterangepickerModel = [this.startDate, this.endDate];
      // this.getPreviousTimeData();
    } else if (id === 'Currentquarter') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.getQuarter1(this.startDate);
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'Currentyear') {
      this.endDate = new Date();
      this.startDate = new Date();
      var curryear = this.startDate.getFullYear();
      this.startDate = new Date(curryear, 0, 1);
      this.daterangepickerModel = [this.startDate, this.endDate];
    }
    else if (id === 'LAST7DAYS') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 6);
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'LAST30DAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 29);
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'LAST90DAY') {
      this.startDate = new Date();
      this.endDate = new Date();
      this.startDate.setDate(this.startDate.getDate() - 89);
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'LASTMONTH') {
      this.startDate = new Date();
      this.startDate.setDate(1);
      this.startDate.setMonth(this.startDate.getMonth() - 1);
      this.endDate = new Date(this.startDate.getFullYear(), this.startDate.getMonth() + 1, 0);
      this.daterangepickerModel = [this.startDate, this.endDate];
    } else if (id === 'CUSTOMDATE') {
      this.daterangepickerModel = [this.startDate, this.endDate];
    }
    if (this.ngxSmartModalService.getOpenedModals() &&
      (this.ngxSmartModalService.getOpenedModals().length > 0)
    ) {
      let modals = this.ngxSmartModalService.getOpenedModals();
      for (let index = 0; index < modals.length; index++) {
        if (modals[index].id === 'daterangeSelection1') {
          this.ngxSmartModalService.close('daterangeSelection1');
        }
      }
    }
  }

  canShowColumn(columnNameId) {
    return this.switchAppliedValues[columnNameId];
  }
  getSelectedBookingType(id: string) {
    let bookingObj = this.bookingOptions.find(obj => obj.id === id);
    if (bookingObj) {
      return bookingObj.value;
    }
  }

  pendingTabClicked() {
    this.viewMode2 = 'tab21'
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'report',
          subType: 'Transaction'
        },
        replaceUrl: false
      }
    );;
  }

  historyTabClicked() {
    this.viewMode2 = 'tab22';
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'report',
          subType: 'TravelCredit'
        },
        replaceUrl: false
      }
    );
    initializeWdr(this.pivotTableDataUrl(), this.wdrData, this.currency,this.selectedReportDate,this.userAccountInfoService.getSToken());
  }
  getShownColumnList() {
    let shownColum = [];
    for (let key in this.switchAppliedValues) {
      if (this.switchAppliedValues[key]) shownColum.push(key);
    }
    return shownColum;
  }
  private buildCompanyReportData(reportResponse: CompanyReportResponse) {
    this.companyReport = [];
    let reportTotal = 0;
    this.activePage = 1;
    this.recordsPerPageOptions = [{ id: 20, value: 20 }];
    if (reportResponse) {
      if (reportResponse.bookingList.length > 0 && reportResponse.bookingList.length <= 20) {
        this.showPagination = false;
      } else if (reportResponse.bookingList.length > 20 && reportResponse.bookingList.length <= 50) {
        this.showPagination = true;
        this.recordsPerPageOptions.push({ id: 50, value: 50 });
      } else if (reportResponse.bookingList.length > 50) {
        this.showPagination = true;
        this.recordsPerPageOptions.push({ id: 50, value: 50 });
        this.recordsPerPageOptions.push({ id: 100, value: 100 });
      }
    }
    for (let optionItem of reportResponse.bookingList) {
      let reportItem = {};
      this.totalRecord = reportResponse.bookingList.length;
      reportItem['name'] = reportResponse.users[optionItem.userid].userName;
      reportItem['traveler'] = optionItem.primaryTraveller.userName;
      let departmentName = this.adminPanelService.getDepartmentName(reportResponse.users[optionItem.userid].departmentId);
      reportItem['department'] = departmentName;
      reportItem['bookingDate'] = optionItem.bookingDate;
      reportItem['type'] = optionItem.type;
      reportItem['ticketNumber'] = optionItem.ticketnumber ? optionItem.ticketnumber : null;
      reportItem['status'] = CommonUtils.getStatusLabel(optionItem.type, optionItem.option.status, optionItem, this.translateService);
      if (optionItem.type === 'flight') {
        reportItem['departure'] = optionItem.option.flight_option.legs[0].hops[0].starts;
        // if (optionItem.option.status == 'selected' || optionItem.option.status == 'booked' || optionItem.option.status == 'quoted') {
        //   reportItem['status'] = 'Under review';
        // } else if (optionItem.option.status == 'ticketed' || optionItem.option.status == 'expensed') {
        //   reportItem['status'] = 'Booked';
        // } else if (optionItem.option.status == 'cancelled') {
        //   reportItem['status'] = 'Cancelled';
        // } else {
        //   reportItem['status'] = optionItem.option.status;
        // }
        // if (optionItem.option.flight_option.cancellationRequestReceived) {
        //   reportItem['status'] = 'CNCL under review';
        // }
        let firstLeg = optionItem.option.flight_option.legs[0];
        let destination = firstLeg.hops[firstLeg.hops.length - 1].to;
        if (reportResponse.airports[destination]) {
          destination = reportResponse.airports[destination].city;
        }

        let lastLeg = optionItem.option.flight_option.legs[optionItem.option.flight_option.legs.length - 1];
        let firstHop = lastLeg.hops[0];
        let lastHop = lastLeg.hops[lastLeg.hops.length - 1];

        if (lastHop.to == optionItem.option.flight_option.legs[0].hops[0].from) {
          reportItem['return'] = firstHop.starts;
        }
        // let destination = optionItem.option.flight_option.legs[0].hops[0].from;

        // let destination = optionItem.option.flight_option.legs[0].hops[0].from;
        for (let i = 1; i < optionItem.option.flight_option.legs.length; i++) {
          let thisleg = optionItem.option.flight_option.legs[i];
          let lastHop = thisleg.hops[thisleg.hops.length - 1];
          destination = destination + ", " + reportResponse.airports[lastHop.to].city;
          // destination = destination+ ", " +lastHop.to;
        }
        reportItem['destinations'] = destination;
        reportItem['withinPolicy'] = optionItem.option.flight_option.withinPricePolicy ? 'Yes' : 'No';
        reportItem['price'] = optionItem.option.flight_option.price ? optionItem.option.flight_option.price : 0;
        if (optionItem.option.flight_option.discountedPrice && optionItem.option.flight_option.discountedPrice !== optionItem.option.flight_option.price) {
          reportItem['price'] = optionItem.option.flight_option.discountedPrice;
        }
        if (optionItem.option.status == 'refund') {
          reportItem['price'] = optionItem.option.flight_option.refundAmount;
          reportTotal = reportTotal - Number.parseFloat(reportItem['price']);
        } else {
          reportTotal = reportTotal + Number.parseFloat(reportItem['price']);
        }

        reportItem['currency'] = optionItem.option.flight_option.currency;
        reportItem['carrier'] = optionItem.option.flight_option.legs[0].hops[0].carrier;

      } else if (optionItem.type === 'hotel') {
        // if (optionItem.option.status == 'selected' || optionItem.option.status == 'quoted') {
        //   reportItem['status'] = 'Under review';
        // } else if (optionItem.option.status == 'ticketed' || optionItem.option.status == 'booked' || optionItem.option.status == 'expensed') {
        //   reportItem['status'] = 'Booked';
        // } else if (optionItem.option.status == 'cancelled') {
        //   reportItem['status'] = 'Cancelled';
        // } else {
        //   reportItem['status'] = optionItem.option.status;
        // }
        // if (optionItem.option.hotel_option.cancellationRequestReceived) {
        //   reportItem['status'] = 'CNCL under review';
        // }
        reportItem['departure'] = optionItem.option.hotel_option.checkInDate;
        reportItem['return'] = optionItem.option.hotel_option.checkOutDate;
        reportItem['destinations'] = optionItem.destinationCity;
        reportItem['withinPolicy'] = optionItem.option.hotel_option.inPolicy ? 'Yes' : 'No';
        reportItem['price'] = optionItem.option.hotel_option.minPrice ? optionItem.option.hotel_option.minPrice : 0;
        if (optionItem.option.status == 'refund') {
          reportItem['price'] = optionItem.option.hotel_option.refundAmount ? optionItem.option.hotel_option.refundAmount : 0;
          reportTotal = reportTotal - Number.parseFloat(reportItem['price']);
        } else {
          reportTotal = reportTotal + optionItem.option.hotel_option.minPrice;
        }
        reportItem['currency'] = optionItem.option.hotel_option.currency;
        reportItem['carrier'] = optionItem.option.hotel_option.hotelName;
      } else if (optionItem.type === 'cars') {
        // if (optionItem.option.status == 'selected' || optionItem.option.status == 'quoted') {
        //   reportItem['status'] = 'Under review';
        // } else if (optionItem.option.status == 'ticketed' || optionItem.option.status == 'booked' || optionItem.option.status == 'expensed') {
        //   reportItem['status'] = 'Booked';
        // } else if (optionItem.option.status == 'cancelled') {
        //   reportItem['status'] = 'Cancelled';
        // } else {
        //   reportItem['status'] = optionItem.option.status;
        // }
        // if (optionItem.option.car_option.cancellationRequestReceived) {
        //   reportItem['status'] = 'CNCL under review';
        // }
        if (optionItem.option.car_option && optionItem.option.car_option.pickUpDate) {
          reportItem['departure'] = optionItem.option.car_option.pickUpDate;
        }
        if (optionItem.option.car_option && optionItem.option.car_option.dropOffDate) {
          reportItem['return'] = optionItem.option.car_option.dropOffDate;
        }

        reportItem['destinations'] = optionItem.destinationCity;
        reportItem['withinPolicy'] = optionItem.option.car_option.policy ? 'Yes' : 'No';
        if (optionItem.option.car_option && optionItem.option.car_option.price) {
          reportItem['price'] = optionItem.option.car_option.price ? optionItem.option.car_option.price : 0;
        }
        if (optionItem.option.status == 'refund') {
          reportItem['price'] = optionItem.option.car_option.refundAmount ? optionItem.option.car_option.refundAmount : 0;
          reportTotal = reportTotal - Number.parseFloat(reportItem['price']);
        } else {
          if (optionItem.option.car_option && optionItem.option.car_option.price) {
            reportTotal = reportTotal + optionItem.option.car_option.price;
          }
        }
        if (optionItem.option.car_option && optionItem.option.car_option.currency) {
          reportItem['currency'] = optionItem.option.car_option.currency;
        }
        if (optionItem.option.car_option && optionItem.option.car_option.partnerName) {
          reportItem['carrier'] = optionItem.option.car_option.partnerName.split(' ')[0];
        }
      }
      if (reportItem['bookingDate']) {
        var date1 = new Date(this.getBookingDate(reportItem['bookingDate'], 'yyyy-MM-dd'));
      }
      if (reportItem['departure']) {
        var date2 = new Date(this.getDisplayDate(reportItem['departure'], 'yyyy-MM-dd', reportItem['type']));
      }
      var diffDays = Math.ceil(date2.getTime() / (1000 * 3600 * 24)) - Math.ceil(date1.getTime() / (1000 * 3600 * 24));
      if (optionItem.type !== 'cars') {
        reportItem['billingType'] = optionItem.billingType == 'personalCard' ? 'Personal' : 'Business';
      } else if (optionItem.type === 'cars' && optionItem.option.car_option && !optionItem.option.car_option.paymentRequired) {
        reportItem['billingType'] = 'pay at counter';
      } else {
        reportItem['billingType'] = optionItem.billingType == 'personalCard' ? 'Personal' : 'Business';
      }
      reportItem['advanceBookingDays'] = diffDays + " Days";
      this.companyReport.push(reportItem);
      this.companyReportTotal = '' + reportTotal;
      let initColumnValues: any = {};
      if (reportResponse.columns && reportResponse.columns.length > 0) {
        for (let column of reportResponse.columns) {
          initColumnValues[column] = true;
        }

      }
      //  if(Object.keys((initColumnValues)).length !== 0 ){
      // this.initSwitch(initColumnValues);
      // }else{
      // this.initSwitch(this.adminPanelService.getColumnSwitchMap());
      //}
      // this.applySwitch();
    }

    this.departmentOptions = [{ value: 'All Departments', id: '' }];
    let apiDepartmentList: Array<Department> = this.adminPanelService.getDepartments();
    for (let department of apiDepartmentList) {
      this.departmentOptions.push({ value: department.name, id: '' + department.departmentId });
    }

    this.employeeOptions = [{ id: '', value: 'All Employees', departmentId: ' ' }];
    for (let userid in reportResponse.users) {
      let user: UserInfoBasic = reportResponse.users[userid];
      this.employeeOptions.push({ value: user.userName, id: '' + user.userId, departmentId: user.departmentId + '' });
    }
    this.employeeOptions.sort(function (a, b) {
      if (a.id !== '' && b.id !== '') {
        if (a.value < b.value) { return -1; }
        if (a.value > b.value) { return 1; }
      }
      return 0;
    })
  }

  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }
  showReportOptionChanged(event){
 initializeWdr(this.pivotTableDataUrl(), this.wdrData, this.userAccountInfoService.getUserCompanyId(),this.userAccountInfoService.getUserEmail(), this.currency,this.selectedReportDate,this.userAccountInfoService.getSToken());
 this.setStartDate(this.daterangepickerModel);
  }
  getSelectedReportOption(id: string) {
    let employeeObj = this.reportDateOptions.find(obj => obj.id === id);
    if (employeeObj) {
      //this.selectedReportDate = employeeObj.value;
      return employeeObj.value;
    } else {
      //this.selectedReportDate = '';
      //this.cdRef.detectChanges();
      //return 'All Employees';
    }
  }
  getSelectedEmployeeName(id: string) {
    let employeeObj = this.employeeOptions.find(obj => obj.id === id);
    if (employeeObj) {
      this.selectedEmployee = employeeObj.value;
      return employeeObj.value;
    } else {
      this.empValue = '';
      this.adminPanelService.filterAppliedEmployeeId = this.empValue;
      this.cdRef.detectChanges();
      return 'All Employees';
    }
  }
  getStartDate(picker) {
    picker._bsValue = this.startDate;
    return this.startDate;
  }

  getEndDate(picker) {
    picker._bsValue = this.endDate;
    return this.endDate;
  }
  getSelectedDepartmentNamed(id: string) {
    let employeeObj = this.employeeOptions.find(obj => obj.id === id);
    let departmentObj;
    if (employeeObj) {
      departmentObj = this.departmentOptions.find(obj => obj.id === employeeObj.departmentId);
    } else {
      departmentObj = this.departmentOptions.find(obj => obj.id === this.adminPanelService.filterAppliedDepartmentId);
    }
    if (!id || id === '0' || id === '') {
      departmentObj = this.departmentOptions.find(obj => obj.id === this.adminPanelService.filterAppliedDepartmentId);
    }
    if (departmentObj) {
      this.selectedDepart = departmentObj.value;
      return departmentObj.value;
    } else {
      this.deptValue = '';
      this.selectedDepart = 'All Departments';
      return 'All Departments';
    }
  }
  getAirlineFullName(code) {
    let airlineFullName = null;
    //if(code.type==='flight'){
    airlineFullName = this.airlines[code];
    return (airlineFullName === undefined || airlineFullName === null) ? code : airlineFullName;
    // }//else{
    //return code.carrier;
    //}
  }
  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
    this.ngxSmartModalService.close('daterangeSelection1');
    if ((!this.clientConfig.classicReports || this.selectMode !== 'Transactions') && (this.getDataLoadingStatus() && this.dateChanged)) {
      this.getReport(true);
    }
  }
  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    const dayHoverHandler = event.dayHoverHandler;
    this.dateValue1 = "CUSTOMDATE";
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        this.searchService.hoverCounter++;
        if (this.searchService.hoverCounter > 1) {
          (picker as any)._datepickerRef.instance.daySelectHandler(cell);
        }
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }
  onModelCancel() {
    this.bsModalRef.hide();
  }
  showModal1(Modal) {
    this.bsModalRef = this.modalService.show(Modal, {
      initialState: {
      }, backdrop: true, keyboard: false, ignoreBackdropClick: true
    });
    if (this.adminPanelService.filterAppliedEmployeeName) {
      this.adminPanelService.filterAppliedEmployeeName = '';
      this.empNameSearchValue = '';
      this.adminPanelService.filterCompanyReport();
    }
  }

  isFilteredReportDataEmpty() {
    this.resultErrorMessage = this.translateService.instant('report.FetchingData');
    var noData = isFilteredReportDataEmptyJS();
    if (noData && (this.wdrData.apiName === 'Report' || this.wdrData.apiName === 'CustomFields' || this.wdrData.apiName === 'Approval') && this.getDataLoadingStatus()) {
      this.resultErrorMessage = this.translateService.instant('report.NoData');
    } else if (noData && this.wdrData.apiName === 'Card' && this.getDataLoadingStatus()) {
      this.resultErrorMessage = this.translateService.instant('report.Notransactionfound');
    }
    else if (noData && this.getDataLoadingStatus()) {
      this.resultErrorMessage = this.translateService.instant('report.Nocreditsfound');
    }
    return noData;
  }
  dateChanged =false;
  setStartDate(date) {
    if (date) {
      // this.daterangepickerModel =date;
      this.startDate = date[0];
      this.endDate = date[1];
      let modals = this.ngxSmartModalService.getOpenedModals();
      for (let index = 0; index < modals.length; index++) {
        if (modals[index].id === 'daterangeSelection1') {
          if(this.lastDate && (this.lastDate[0]!==date[0] || this.lastDate[1]!==date[1])){
            this.dateChanged =true;
          }
        }
      }
      
      this.lastDate = date;
    }
    if(this.selectedReportDate==='travel'){
      this.maximumDate1 =  new Date(this.maximumDate1.setDate(this.maximumDate1.getDate() + 329));
     // this.endDate = this.daterangepickerModel[1];
     }else{
       this.maximumDate1 = new Date();
     }
    //this.firstDate = date;
    if (this.startDate > this.endDate) {
      this.endDate = date;
      this.lastDate = date;
    }
    this.minimumDate = this.startDate;
  }
  setEndDate(date) {
    this.endDate = date;
    this.maximumDate = this.endDate;
  }


  getReport(onload: boolean) {
    this.adminPanelService.companySaveReport = undefined;
    this.applyBtn = true;
    this.companyReport = [];
    let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
    let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);

    let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
    let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
    this.applySwitch();
    this.downloadData.start = tempdate1;
    this.downloadData.end = tempdate2;
    this.adminPanelService.dateValue = this.dateValue1;
    this.adminPanelService.filterAppliedDepartmentId = this.deptValue;
    this.adminPanelService.bookingType = this.bookingValue;
    this.adminPanelService.filterAppliedEmployeeId = this.empValue;
    this.adminPanelService.firstReportDate1 = this.startDate;
    this.adminPanelService.lastReportDate1 = this.endDate;
    this.downloadData.deptValue = this.selectedDepart;
    this.downloadData.empValue = this.selectedEmployee;
    let reportColumns = new Array<string>();
    for (let column in this.switchAppliedValues) {
      if (this.switchAppliedValues[column]) {
        reportColumns.push(column);
      }
    }
    if (this.clientConfig.classicReports && this.selectMode === 'Transactions') {
      this.progressBar.start(SearchActionType.DETAIL);
      if (reportColumns.length > 0) {
        this.resultErrorMessage = 'Fetching Data';
        this.adminPanelService.fetchCompanyReports(this.userAccountInfoService.getUserCompanyId(), startDate, endDate, reportColumns);
        this.adminPanelService.previousReportDate = this.previousDate;
      } else {
        this.applyBtn = false;
        this.resultErrorMessage = 'No Data';
        this.progressBar.stop(SearchActionType.DETAIL);
        this.toastr.error("Atleat select one column to get Report !!!");
      }
    } else if (this.clientConfig.classicReports && this.selectMode !== 'Transactions') {
      this.getWdrData();
      initializeWdr(this.pivotTableDataUrl(), this.wdrData, this.userAccountInfoService.getUserCompanyId(),this.userAccountInfoService.getUserEmail(), this.currency,this.selectedReportDate,this.userAccountInfoService.getSToken());
    }
    else if (!this.clientConfig.classicReports && this.viewMode2 !== 'tab22') {
      // setTimeout(() => {
      // if(this.selectMode==='Transactions'){
      this.getWdrData();
      initializeWdr(this.pivotTableDataUrl(), this.wdrData, this.userAccountInfoService.getUserCompanyId(),this.userAccountInfoService.getUserEmail(), this.currency,this.selectedReportDate,this.userAccountInfoService.getSToken());
      
    }
    this.dateChanged=false;
    this.previousDate = startDate + 'T' + endDate;
  }

  showEmployeeChanged(employee) {
    if (employee.id.length > 0) {
      this.deptValue = '' + this.apiReportResponse.users[employee.id].departmentId;
      // this.adminPanelService.filterAppliedDepartmentId = this.deptValue;
    }
    // this.adminPanelService.filterAppliedEmployeeId = employee.id;
    this.empValue = employee.id;
    // this.adminPanelService.filterCompanyReport();
  }

  showRecordsPerPageChanged(recordPerPageSelected) {
    this.activePage = 1;
  }

  searchByNameChanged(text: string) {
    this.adminPanelService.filterAppliedEmployeeName = text;
    this.adminPanelService.filterCompanyReport();
  }
  showDepartmentChanged(department) {
    this.adminPanelService.filterAppliedDepartmentId = department.id;
    // this.adminPanelService.filterCompanyReport();
  }
  downloadForQuickBooks() {
    let reportTable = [];
    
    this.cardTrannsactionReport = getPreviousfilteredData();

    if (this.cardTrannsactionReport.length > 0) {
      let reportRow = [];
      let colNameCaptionMap = {};
      for (let key of currReportConfig.slice.rows) {
        if ('caption' in key) {
          colNameCaptionMap[key['uniqueName']] = key['caption'];
        } else {
          colNameCaptionMap[key['uniqueName']] = key['uniqueName'];
        }
      }
      let firstRowContainsTypes = false;
      let item = this.cardTrannsactionReport[0];
      for (let key of currReportConfig.slice.flatOrder) {
        if (!firstRowContainsTypes && typeof item[key] === 'object') {
          firstRowContainsTypes = true;
        }
        reportRow.push(colNameCaptionMap[key]);
      }
      if (firstRowContainsTypes) {
        this.cardTrannsactionReport = this.cardTrannsactionReport.slice(1);
      }
      reportTable.push(reportRow);
      for (let rowItem of this.cardTrannsactionReport) {
        let reportRow = [];
        // let dateObj = DateUtils.getFormattedDateWithoutTimeZone(rowItem['Transaction date']);
        // let datePipe = new DatePipe('en-US');
        // let setDate = datePipe.transform(dateObj, 'MM/dd/yyyy');
        for (let key of currReportConfig.slice.flatOrder) {
          if (key === 'Start Date'
            || key === 'End Date'
            || key === 'Booking Date'
            || key === 'Transaction Date'
            || key === 'Rebooking Date'
            || key === 'Expiry Date'
            || key === 'Cancellation Date'
            || key === 'Transaction date'
          ) {
            let dateObj = DateUtils.getFormattedDateWithoutTimeZone(rowItem[key]);
            let datePipe = new DatePipe('en-US');
            let setDate = datePipe.transform(dateObj, 'MM/dd/yyyy');
            reportRow.push(setDate);
          } else if (key === 'Spend'
          || key === 'Average Spend'
          || key === 'Pre-tax fare'
          || key === 'Taxes & fees'
          ) {
            if (Number(rowItem[key]) >= 0) {
              reportRow.push(this.currency + Math.abs(Number(rowItem[key])));
            } else {
              reportRow.push('-' + this.currency + Math.abs(Number(rowItem[key])));
            }
          } else {
            reportRow.push(rowItem[key]);
          }
        }
        reportTable.push(reportRow);
      }
      this.downloadFile(reportTable)
    } else {
      this.toastr.error("No transaction are present !!!");
    }
  }
  downloadFile(data) {
    const replacer = (key, value) => (value === null ? '' : value); // specify how you want to handle null values here
    const header = Object.keys(data[0]);
    const csv = data.map((row) =>
      header
        .map((fieldName) => JSON.stringify(row[fieldName], replacer))
        .join(',')
    );
    csv.unshift(header.join(','));
    const csvArray = csv.join('\r\n');

    const a = document.createElement('a');
    const blob = new Blob([csvArray], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);

    a.href = url;
    a.download = 'report_transactions.csv';
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }
  downloadReport() {
    if (!this.companyReport || this.companyReport.length == 0) {
      this.toastr.error("No record available to generate report");
      this.bsModalRef.hide();
      return;
    }
    if (!this.getShownColumnList() || this.getShownColumnList().length < 1) {
      this.toastr.error("Please select at least one column to generate report");
      this.bsModalRef.hide();
      return;
    }
    let reportTable = [];
    let reportRow = []
    if (this.canShowColumn('NAME')) reportRow.push('Booked by');
    if (this.canShowColumn('TRAVELER_NAME')) reportRow.push('Traveler name');
    if (this.canShowColumn('Department')) reportRow.push('Department');
    if (this.canShowColumn('BK_DATE')) reportRow.push('Transaction Date');
    if (this.canShowColumn('ADV_BK')) reportRow.push('Advance Booking');
    if (this.canShowColumn('BK_TYPE')) reportRow.push('BookingType');
    if (this.canShowColumn('Vendor')) reportRow.push('Vendor');
    if (this.canShowColumn('TRV_DATE')) reportRow.push('Departure/Check-in');
    if (this.canShowColumn('TRV_DATE')) reportRow.push('Return/Check-out');
    if (this.canShowColumn('DEST')) reportRow.push('Destination');
    if (this.canShowColumn('STATUS')) reportRow.push('Status');
    if (this.canShowColumn('POLICY')) reportRow.push('Within Policy');
    if (this.canShowColumn('PRICE')) reportRow.push('Price');
    if (this.canShowColumn('BILL_INF')) reportRow.push('Billing Info');
    if (this.canShowColumn('Refrence')) reportRow.push('Reference');
    reportTable.push(reportRow);
    for (let rowItem of this.companyReport) {
      let reportRow = []
      if (this.canShowColumn('NAME')) reportRow.push(rowItem['name']);
      if (this.canShowColumn('TRAVELER_NAME')) reportRow.push(rowItem['traveler']);
      if (this.canShowColumn('Department')) reportRow.push(rowItem['department']);
      if (this.canShowColumn('BK_DATE')) reportRow.push(DateUtils.getFormattedDateForGivenTimeZone(rowItem['bookingDate'], 'd MMM yyyy'));
      if (this.canShowColumn('ADV_BK')) reportRow.push(rowItem['advanceBookingDays']);

      if (this.canShowColumn('BK_TYPE')) reportRow.push(rowItem['type']);
      if (this.canShowColumn('Vendor')) reportRow.push(this.getAirlineFullName(rowItem['carrier']));
      if (this.canShowColumn('TRV_DATE')) {
        if (rowItem['type'] === 'flight') {
          reportRow.push(DateUtils.getFormattedDateForGivenTimeZone(rowItem['departure'], 'd MMM yyyy'));
          if (rowItem['return']) {
            reportRow.push(DateUtils.getFormattedDateForGivenTimeZone(rowItem['return'], 'd MMM yyyy'));
          } else {
            reportRow.push('-');
          }
        } else {
          reportRow.push(DateUtils.getFormattedDateWithoutTimeZoneFromFormat(rowItem['departure'], 'd MMM yyyy'));
          reportRow.push(DateUtils.getFormattedDateWithoutTimeZoneFromFormat(rowItem['return'], 'd MMM yyyy'));
        }
      }
      if (this.canShowColumn('DEST')) reportRow.push(rowItem['destinations']);
      if (this.canShowColumn('STATUS')) reportRow.push(rowItem['status']);

      if (this.canShowColumn('POLICY')) reportRow.push(rowItem['withinPolicy']);
      if (this.canShowColumn('PRICE')) {
        reportRow.push((rowItem['status'] === 'refund' ? '-' : '') + rowItem['currency'] + rowItem['price']);
      }
      if (this.canShowColumn('BILL_INF')) reportRow.push(rowItem['billingType']);
      if (this.canShowColumn('Refrence')) reportRow.push(rowItem['ticketNumber']);
      reportTable.push(reportRow);
    }
    let filename = "Report.pdf";
    //  
    this.adminPanelService.downloadReport(reportTable, filename).subscribe(
      data => {
        // saveAs(data, filename);
        // FileSaver.saveAs(pdf, filename);
        let blob = new Blob([data], { type: 'application/xls' });

        var downloadURL = window.URL.createObjectURL(data);
        var link = document.createElement('a');
        link.href = downloadURL;
        link.download = "report.xls";
        link.click();
        this.bsModalRef.hide();
      },
      err => {
        alert("Problem while downloading the file.");
        console.error(err);
      }
    );
  }
  getDisplayDateTimeForFlights(dateString: string, format: string): string {
    return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }

  getDisplayDate(dateString: string, format: string, type: string): string {
    if (type && type.toLowerCase() === 'flight') {
      return this.getDisplayDateTimeForFlights(dateString, format);
    }
    else if (type && type.toLowerCase() === 'cars') {
      return this.getDisplayDateTimeForFlights(dateString, format);
    } else {
      return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
    }
  }
  getBookingDate(dateString: string, format: string) {
    let datePipe = new DatePipe('en-US');
    return datePipe.transform(new Date(dateString), format);

    // return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }
  getClassAccordingToPolicy(item) {
    return item.withinPolicy === 'No' && item.status !== 'refund' ? 'noPolicy' : 'show1';
  }
  bookingDetailsPageForWdrData(transid) {
    this.adminPanelService.from = 'report';
    this.gallopLocalStorage.setItem("from", this.adminPanelService.from);
    this.adminPanelService.dateValue = this.dateValue1;
    if (this.wdrData.apiName === 'Approval') {
      this.router.navigateByUrl(transid);
    } else {
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            view: 'detail',
            type: 'detail',
            name: '',
            bookingType: 'upcoming',
            userEmail: this.userAccountInfoService.getUserCompanyId(),
            ticketid: '',
            tripid: '',
            transactionid: transid
          },
        }
      );
    }
  }
  viewDetail(event, index: number) {
    event.preventDefault();
    this.titleService.setTitle(this.translateService.instant('report.TransactionsReport'));
    this.adminPanelService.companySaveReport = this.apiReportResponse;
    this.adminPanelService.from = 'report';
    this.adminPanelService.deptValue = this.selectedDepart;
    this.adminPanelService.empValue = this.selectedEmployee;
    this.adminPanelService.dateValue = this.dateValue1
    this.adminPanelService.previousReportDate = this.previousDate;
    let bookedOption;
    this.adminPanelService.from = 'report';
    this.gallopLocalStorage.setItem("from", this.adminPanelService.from);
    if (this.showPagination && this.activePage > 1) {
      let activePage = this.activePage - 1;
      let recordsPerPage = this.recordsPerPage;
      let index1 = (activePage * recordsPerPage) + (index);
      bookedOption = (index1 !== this.apiReportResponse.bookingList.length) ? this.apiReportResponse.bookingList[index1] : this.apiReportResponse.bookingList[index1 - 1];
    } else {
      bookedOption = this.apiReportResponse.bookingList[index];
    }
    let name = this.companyReport[index].name;
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          view: 'detail',
          type: 'detail',
          name: name,
          bookingType: 'upcoming',
          userEmail: bookedOption.userid,
          ticketid: bookedOption.ticketid,
          tripid: bookedOption.tripid,
          transactionid: bookedOption.option.selectTransId
        },
      }
    );
  }
  viewDetail1(index: number): string {
    let bookedOption;
    let name;
   
    this.titleService.setTitle(this.translateService.instant('report.TransactionsReport'));
    if (this.showPagination && this.activePage > 1) {
      let activePage = this.activePage - 1;
      let recordsPerPage = this.recordsPerPage;
      let index1 = (activePage * recordsPerPage) + (index);
      bookedOption = (index1 !== this.apiReportResponse.bookingList.length) ? this.apiReportResponse.bookingList[index1] : this.apiReportResponse.bookingList[index1 - 1];
    } else {
      bookedOption = this.apiReportResponse.bookingList[index];
    }
    
    name = this.companyReport[index].name;
    let url = '/admin?view=detail&type=detail&name=' + name + '&bookingType=upcoming&userEmail=' + encodeURIComponent(bookedOption.userid) + '&ticketid=' + bookedOption.ticketid + '&tripid=' + bookedOption.tripid + '&transactionid=' + bookedOption.option.selectTransId
    return url;
  }
  pivotTableDataUrl() {
    let start = JSON.parse(JSON.stringify(this.startDate));
    let end= JSON.parse(JSON.stringify(this.endDate));
    if(this.selectedReportDate ==='travel'){
     start = new Date(this.startDate);
     end = new Date(this.endDate);
      start.setDate(this.startDate.getDate() - 1);
      end.setDate(this.endDate.getDate() + 1);
    }
    let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(start);
    let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(end);
    let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
    let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
   
   // this.maximumDate1 = this.maximumDate;
    if (this.wdrData.apiName === 'Report') {
      return environment.apiChartsData
        + '?companyid=' + this.userAccountInfoService.getUserCompanyId()
        + '&startDate=' + encodeURIComponent(startDate)
        + '&endDate=' + encodeURIComponent(endDate)
        + '&userid=' + encodeURIComponent(this.userAccountInfoService.getUserEmail())
        + '&sToken=' + encodeURIComponent(this.userAccountInfoService.getSToken())
         + '&dateType=' + encodeURIComponent(this.selectedReportDate);
        ;
    } else if (this.wdrData.apiName === 'Approval') {
      return environment.apiForWdrApproval
        + '?companyid=' + this.userAccountInfoService.getUserCompanyId()
        + '&startDate=' + encodeURIComponent(startDate)
        + '&endDate=' + encodeURIComponent(endDate)
        + '&userid=' + encodeURIComponent(this.userAccountInfoService.getUserEmail())
        + '&sToken=' + encodeURIComponent(this.userAccountInfoService.getSToken())
        ;
    }
    else if (this.wdrData.apiName === 'CustomFields') {
      return environment.apiChartsData
        + '?companyid=' + this.userAccountInfoService.getUserCompanyId()
        + '&forTags=true'
        + '&startDate=' + encodeURIComponent(startDate)
        + '&endDate=' + encodeURIComponent(endDate)
        + '&userid=' + encodeURIComponent(this.userAccountInfoService.getUserEmail())
        + '&sToken=' + encodeURIComponent(this.userAccountInfoService.getSToken())
        + '&dateType=' + encodeURIComponent(this.selectedReportDate);
        ;
    }
    else if (this.wdrData.apiName === 'Card') {
      return environment.apiForCardReportTransaction
        + '?companyid=' + this.userAccountInfoService.getUserCompanyId()
        + '&startDate=' + encodeURIComponent(startDate)
        + '&endDate=' + encodeURIComponent(endDate)
        + '&userid=' + encodeURIComponent(this.userAccountInfoService.getUserEmail())
        + '&sToken=' + encodeURIComponent(this.userAccountInfoService.getSToken())
        ;
    } else {
      return environment.apiForTravelCredit
        + "?userid=" + encodeURIComponent(this.userAccountInfoService.getUserEmail())
        + "&sToken=" + encodeURIComponent(this.userAccountInfoService.getSToken());
    }
  }
  getDataLoadingStatus() {
    if (this.clientConfig.classicReports && this.selectMode === 'Transactions') {
      return !this.applyBtn;
    } else {
      return getWDRLoadStatus();
    }
  }
  getStatus() {
    return (!this.getDataLoadingStatus());
  }
  ngOnDestroy() {
    if (this.clientConfig.classicReports && this.selectMode !== 'Transactions') {
      clearPreviousSliceColumn();
      pivotOffButton();
    } else if (!this.clientConfig.classicReports) {
      clearPreviousSliceColumn();
      pivotOffButton();
    }
    if (this.adminPanelService.companySaveReport) {
      this.adminPanelService.companySaveReport = undefined;
    }
    emptyPreviousReportData()
    if (this.companyReportSubscription) {
      this.companyReportSubscription.unsubscribe();
    }
    this.queryParamSubscription.unsubscribe();
    //this.companysettingSubscription.unsubscribe();
  }

}
