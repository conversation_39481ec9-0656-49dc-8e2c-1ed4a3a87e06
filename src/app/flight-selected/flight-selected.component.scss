@import "../../variables.scss";
.result-card {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.19);
    border-radius: 6px;
    margin: 16px 0;
    cursor: pointer;
    float: left;
    width: 100%;
    background: #fff;
    &.selected {
        margin: 0 -8px;
        border: 1px solid $primary-color;
    }
    &.static {
        background: #fff;
        margin: 0;
        cursor: default;
        .list-container,
        .source,
        .destination,
        .list .icon-plane {
            background-color: #FFFFFF;
        }
        .action-container {
            box-shadow: none;
        }
    }
}

.flight-trip-type11 {
    font-size: 12px;
}

.fare-attr-pref-airline {
    color: #27c198;
}

.city {
    font-size: 14px;
    margin-bottom: 5px;
    max-width: 110px;
    overflow: hidden;
    white-space: nowrap;
    display: inline-block;
    text-overflow: ellipsis !important;
}

.flght-selected-info-icon {
    font-size: 20px;
    color: var(--hyperlink-color);
}

.price {
    margin-right: 10px;
    font-size: 18px;
    font-weight: 600;
    color: $link-color;
}

.list-container {
    padding: 16px;
    border-radius: 6px 0 0 6px;
    &:last-child {
        border-bottom: none;
    }
}

.list {
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 1;
    .icon-plane {
        font-size: 30px;
        padding: 0 20px;
        background: #fff;
        color: $primary-color;
    }
}

.source,
.destination {
    font-size: 18px;
    font-weight: 600;
    line-height: 26px;
    background-color: #fff;
    display: inline-flex;
    align-items: center;
    .flight-icon {
        margin-right: 2px;
        height: 12px;
    }
}

.source {
    padding-right: 20px;
}

.destination {
    padding-left: 20px;
}

.info {
    font-size: 14px;
    line-height: 18px;
    color: $secondary-text-color;
    margin: 3px 0;
}

.info-content {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    position: relative;
}

.info-content:after {
    position: absolute;
    right: -8px;
    content: ', '
}

.info-content:last-child {
    margin-right: 0;
}

.info-content:last-child:after {
    display: none
}

.action-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5px 0 5px;
    height: auto;
    border-radius: 0 6px 6px 0;
    .icon-toggle {
        padding: 10px;
        cursor: pointer;
    }
}

.layover-container {
    position: relative;
    margin: 24px -24px;
    display: flex;
    justify-content: center;
    &:after {
        content: '';
        position: absolute;
        top: 50%;
        background: $border-light-color;
        left: 0;
        right: 0;
        height: 1px;
    }
    .layover {
        display: inline-block;
        padding: 3px 24px;
        border: 1px solid $border-light-color;
        position: relative;
        z-index: 1;
        background: $background-light;
    }
}

.icon-toggle {
    color: $accent-color;
}

.flight-number {
    margin-bottom: 16px;
}

.pipe {
    margin: 0 10px;
}

.separator-right {
    border-right: 1px solid $border-dark-color;
}

.flight-icon {
    margin-right: 10px;
}

.in-policy {
    color: rgb(136, 194, 94);
    font-family: "apercu-r";
    padding-left: 5px;
    text-align: center;
}

.out-policy {
    color: rgb(249, 61, 48);
    font-family: "apercu-r";
    padding-left: 5px;
    text-align: center;
}

.no-policy {
    color: rgb(136, 194, 94);
    font-family: "apercu-r";
    padding-left: 5px;
    text-align: center;
}


/*new css*/

.result-card-box {
    background: #fff;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    border-radius: 6px;
    font-family: $fontRegular;
    float: left;
    width: 100%;
}

.flight-detail {
    display: flex;
    justify-content: space-between;
}

.flight-timings {
    display: flex;
    justify-content: space-between;
}

.flight-timings-section {
    position: relative;
    z-index: 2;
}

.flight-time {
    font-size: 24px;
    line-height: 20px;
}

.flight-time .extra-time {
    color: #f93d30;
    margin-left: 5px;
    top: 0;
}

.flight-airport {
    font-size: 14px;
    color: #A6A5A4;
    margin-top: 13px;
}

.block {
    display: block;
}

.stop-box {
    display: inline-block;
    height: 11px;
    width: 11px;
    border: 1px solid #979797;
    background-color: #FFFFFF;
    position: relative;
    top: 3px;
    z-index: 2;
}

.flight-duration {
    font-size: 14px;
    line-height: 12px;
    margin-top: 14px;
}

.flight-price {
    color: var(--hyperlink-color);
    font-size: 24px;
    letter-spacing: 2px;
    font-family: $fontBold;
    line-height: 22px;
    margin-top: 0;
}

.flight-price.high-price {
    color: #f93d30;
}

.flight-trip-type {
    font-size: 14px;
    color: #A6A5A4;
}

.flight-name-duration {
    width: 104px;
}

.flight-timings-div {
    width: calc(100% - 104px);
    position: relative;
}

.flight-timings {
    width: 100%;
    position: relative;
}

.flight-timings-line {
    position: absolute;
    height: 1px;
    width: calc(100% - 250px);
    left: 120px;
    content: '';
    background: #979797;
    top: 8px;
    z-index: 1;
}

.flight-timings-line:after {
    position: absolute;
    content: '';
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 10px solid #979797;
    right: -2px;
    top: -4px;
}

.flight-facilities {
    border-top: 2px solid #E3E3E3;
    float: left;
    width: 100%;
    position: relative;
    padding: 0 10px;
}

.flight-facilities ul li {
    display: inline-block;
    margin-right: 38px;
    padding: 15px 0 0 0;
}

.flight-facilities ul li img {
    display: inline-block;
    vertical-align: middle;
}

.flight-facilities ul li span {
    font-size: 12px;
    line-height: 12px;
    color: #A6A5A4;
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
}

.flight-facilities ul li.active .green-img {
    display: inline-block;
}

.flight-facilities ul li.active .red-img {
    display: none;
}

.flight-facilities ul li.not-active .green-img {
    display: none;
}

.flight-facilities ul li.not-active .red-img {
    display: inline-block;
}

.flight-select-button button {
    height: 64px;
    width: 153.4px;
    font-size: 16px;
    letter-spacing: 1.33px;
    color: var(--button-font-color);
}

.flight-detail-container {
    padding: 25px 25px 20px;
    float: left;
    width: 100%;
}

.flight-price-div {
    float: left;
    width: 100%;
}

.flight-select-container {
    padding: 22px 14px 20px 0;
    position: relative;
    float: left;
    width: 100%;
}

.info-div {
    display: inline-block;
    position: absolute;
    right: 20px;
    top: 14px;
    line-height: 0;
    cursor: pointer;
}

.result-card-box-left {
    width: calc(100% - 240px);
    float: left;
}

.result-card-box-right {
    width: 240px;
    float: left;
}

.flight-name-logo {
    line-height: 0;
    max-width: 50%;
}

.flight-name-logo img {
    display: inline-block;
    vertical-align: top;
}

.flight-select-button {
    float: left;
    width: 100%;
}

.stop-box-container {
    position: relative;
    top: -14px;
}

.stop-box-div {
    height: 20px;
    display: inline-block;
}

.stop-div {
    position: absolute;
    left: 0;
    right: 0;
    text-align: center;
    top: 20px;
    z-index: 0;
}

.flight-modal-container {
    font-family: $fontRegular;
}

.flight-modal-container .modal-header {
    height: 40px;
    background-color: var(--hyperlink-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flight-modal-container .modal-title {
    font-size: 14px;
    color: #FFFFFF;
    width: calc(100% - 30px);
    text-align: center;
}

.flight-modal-container .modal-body {
    padding: 0 11px;
}

.flight-modal-container .close {
    padding: 0;
    text-shadow: none;
    color: #fff;
    opacity: 1;
    margin-right: 0;
}

.flight-modal-container .date-duration {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #E3E3E3;
    padding: 7px 12px;
}

.flight-modal-container .date {
    font-size: 16px;
    font-family: $fontBold;
}

.flight-modal-container .duration {
    font-size: 16px;
}

.flight-modal-container .duration label {
    font-family: $fontRegular;
}

.flight-modal-container .duration span {
    font-family: $fontBold;
}

.flight-modal-container .flight-box {
    float: left;
    width: 100%;
    display: flex;
    font-family: $fontRegular;
    padding: 12px 15px 7px 15px;
}

.flight-modal-container .flight-modal-name-duration {
    display: flex;
    justify-content: space-between;
    float: left;
    width: 100%;
}

.flight-modal-container .flight-modal-name {
    font-size: 18px;
}

.flight-modal-container .flight-modal-duration {
    font-size: 16px;
}

.flight-modal-container .flight-ticket-detail {
    float: left;
    width: 100%;
    color: #AEAEAE;
    font-size: 13px;
    margin-bottom: 10px;
    text-align: left;
}

.flight-modal-container .flight-timing-stops ul li {
    position: relative;
    margin-bottom: 11px;
    font-size: 14px;
    display: flex;
    text-align: left;
    float: left;
    width: 100%;
    padding-left: 20px;
    line-height: normal;
}

.flight-modal-container .flight-timing-stops ul li label {
    display: inline-block;
    vertical-align: middle;
}

.flight-modal-container .flight-timing-stops ul li span {
    margin-left: 28px;
    display: inline-block;
    vertical-align: middle;
}

.flight-modal-container .flight-timing-stops ul li:before {
    height: 9px;
    width: 9px;
    border-radius: 50%;
    background-color: var(--hyperlink-color);
    position: absolute;
    left: 0;
    top: 4px;
    content: '';
}

.flight-modal-container .flight-layover {
    float: left;
    width: 100%;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #EEEDEB;
    background-color: #EFFAFC;
    height: 37px;
    font-size: 14px;
}

.flight-modal-container .flight-layover-right {
    font-size: 16px;
}

.flight-modal-container .flight-box-right {
    padding-left: 18px;
}

.flight-modal-container .flight-modal-price {
    font-size: 19px;
    font-family: $fontMedium;
    line-height: 19px;
    color: var(--button-font-color);
}

.flight-modal-container .flight-modal-trip-type {
    color: #A6A5A4;
    font-size: 11px;
    line-height: 12px;
}

.flight-modal-container .flight-modal-button {
    float: left;
    width: 100%;
    padding: 0 15px;
}

.flight-modal-container .flight-modal-button button {
    width: 100%;
    border-radius: 6px;
    font-size: 12px;
    font-weight: normal;
    letter-spacing: 1px;
    font-family: $fontBold;
    height: 46px;
}

.flight-modal-container .flight-facilities {
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
    align-items: center;
    height: 55px;
    border-top: 1px solid #E3E3E3;
}

.flight-modal-container .flight-facilities ul li {
    margin-right: 20px;
    padding: 0;
}

.flight-modal-container .flight-facilities ul li span {
    margin-left: 4px;
}

.flight-feature-list {
    text-align: left;
    display: flex;
    justify-content: space-between;
    float: left;
    width: 100%;
    padding: 8px 25px 24px 25px;
}

.flight-feature-list ul li {
    line-height: 15px;
}

.flight-feature-list ul li img {
    display: inline-block;
    vertical-align: middle;
}

.flight-feature-list ul li span {
    color: #AEAEAE;
    font-size: 10px;
    display: inline-block;
    margin-left: 5px;
    vertical-align: middle;
    font-size: 12px;
}

.flight-modal-container .close i {
    font-size: 18px;
}

.flight-modal-container .flight-facilities ul li span {
    font-family: $fontRegular;
}

.change-link {
    font-size: 16px;
    line-height: 12px;
    color: var(--button-font-color);
}

.result-date {
    float: left;
    width: 100%;
    padding: 0 25px;
}

.result-card-box-inner:first-child .result-date {
    margin-top: 15px;
}

.selected-total-box {
    float: left;
    width: 100%;
    padding: 0 90px 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 48px;
    border-top: 1px solid #EEEDEB;
}

.selected-class,
.selected-passenger {
    font-size: 14px;
    line-height: 17px;
    color: #AEAEAE;
    display: inline-block;
    vertical-align: middle;
}

.selected-class {
    margin-right: 13px;
}

.total-flight-cost {
    font-size: 24px;
    color: var(--button-font-color);
    letter-spacing: 2px;
    font-family: $fontBold;
}

.total-trip-cost {
    float: left;
    width: 100%;
    padding: 22px 20px;
    font-size: 24px;
    letter-spacing: 2px;
    line-height: 14px;
    text-align: right;
    text-transform: uppercase;
}

.total-trip-cost label {
    margin-right: 10px;
}

.number-of-passengers {
    padding-left: 9px;
}

@media (max-width: 991px) {
    .flight-detail-container {
        padding: 25px 20px 5px;
    }
    .flight-duration {
        font-size: 12px;
        line-height: 12px;
        margin-top: 7px;
    }
    .result-card-box-right {
        width: 160px;
    }
    .result-card-box-left {
        width: calc(100% - 160px);
    }
    .stop-div {
        top: 12px;
    }
    .flight-airport {
        font-size: 12px;
        margin-top: 12px;
    }
    .flight-time {
        font-size: 14px;
        line-height: 14px;
    }
    .flight-facilities ul li {
        margin-right: 15px;
        padding: 10px 0 0 0;
    }
    .flight-facilities ul li span {
        font-size: 12px;
        margin-left: 6px;
    }
    .flight-trip-type {
        font-size: 12px;
    }
    .flight-price {
        font-size: 14px;
        letter-spacing: 1.17px;
    }
    .flight-name-duration {
        width: 60px;
    }
    .flight-timings-line {
        width: calc(100% - 120px);
        content: '';
        top: 6px;
        left: 60px;
    }
    .stop-box-container {
        position: relative;
        top: -5px;
    }
    .stop-box-div {
        line-height: 12px;
        height: 14px;
    }
    .stop-box {
        top: -5px;
        height: 11px;
        width: 11px;
    }
    .flight-price-div {
        width: 100%;
    }
    .flight-timings-div {
        width: calc(100% - 60px);
    }
    .flight-name-logo img {
        max-height: 15px;
    }
    .info-div {
        right: 10px;
        top: 10px;
    }
    .flight-select-button button {
        height: 40px;
        width: 100.4px;
        font-size: 12px;
        letter-spacing: 1.33px;
        color: var(--button-font-color);
    }
    .total-flight-cost {
        font-size: 20px;
    }
    .selected-total-box {
        padding: 0 55px 0 30px;
    }
}

@media (min-width: 767.98px) {
    .list:after {
        content: '';
        position: absolute;
        top: 20%;
        left: 0;
        right: 0;
        height: 1px;
        background: $border-light-color;
        z-index: -1;
    }
    .flight-icon {
        margin-right: 10px;
        height: 15px;
    }
    .flight-time .extra-time {
        color: #f93d30;
        margin-left: 3px;
        top: 0;
    }
}

@media(max-width:990px) {
    .city {
        font-size: 10px;
        margin-bottom: 5px;
        width: 50px;
        white-space: break-spaces;
        display: inline-block;
        text-overflow: ellipsis !important;
    }
}

@media (max-width: 767px) {
    .flight-time .extra-time {
        color: #f93d30;
        margin-left: 2px;
        top: 0;
    }
    .flight-trip-type11 {
        font-size: 8px;
    }
    .flght-selected-info-icon {
        font-size: 16px;
        color: var(--hyperlink-color);
    }
    .in-policy {
        color: rgb(136, 194, 94);
        font-family: "apercu-r";
        padding-right: 15px;
        display: inline-block;
        float: right;
    }
    .out-policy {
        color: rgb(249, 61, 48);
        font-family: "apercu-r";
        padding-right: 15px;
        display: inline-block;
        float: right;
    }
    .no-policy {
        color: rgb(136, 194, 94);
        font-family: "apercu-r";
        padding-right: 15px;
        display: inline-block;
        float: right;
    }
    .action-container {
        display: inline-block;
        padding: 0px 5px 0 5px;
    }
    .result-card-box {
        margin-bottom: 14px;
    }
    .result-card-box-left {
        width: calc(100% - 80px);
    }
    .result-card-box-right {
        width: 80px;
    }
    .flight-detail-container {
        padding: 15px 10px 5px;
    }
    /*.flight-detail{padding-bottom: 7px;}*/
    .flight-duration {
        font-size: 10px;
        line-height: 12px;
        margin-top: 5px;
    }
    .flight-airport {
        font-size: 10px;
        margin-top: 7px;
    }
    .flight-time {
        font-size: 12px;
        line-height: 12px;
    }
    .flight-facilities ul li {
        margin-right: 10px;
        padding: 0 0 0 0;
    }
    .flight-facilities ul li span {
        font-size: 9px;
        margin-left: 4px;
    }
    .flight-trip-type {
        font-size: 9px;
    }
    .flight-price {
        font-size: 14px;
        letter-spacing: 1.17px;
    }
    .flight-name-duration {
        width: 60px;
    }
    .flight-timings-line {
        width: calc(100% - 120px);
        content: '';
        top: 6px;
        left: 60px;
    }
    .stop-box-container {
        position: relative;
        top: -6px;
    }
    .city {
        font-size: 10px;
        margin-bottom: 5px;
        width: 50px;
        white-space: break-spaces;
        display: inline-block;
        text-overflow: ellipsis !important;
    }
    .stop-box-div {
        line-height: 12px;
        height: 12px;
    }
    .stop-box {
        top: -7px;
        height: 7px;
        width: 7px;
    }
    .flight-price-div {
        width: auto;
    }
    .flight-timings-div {
        width: calc(100% - 60px);
    }
    .flight-name-logo img {
        max-height: 20px;
    }
    .info-div {
        right: 5px;
        top: 5px;
    }
    .flight-modal-container .date {
        font-size: 14px;
    }
    .flight-modal-container .duration {
        font-size: 14px;
    }
    .flight-modal-container .flight-modal-name {
        font-size: 16px;
    }
    .flight-modal-container .flight-modal-duration {
        font-size: 14px;
    }
    .flight-modal-container .flight-ticket-detail {
        font-size: 11px;
    }
    .flight-modal-container .flight-timing-stops ul li {
        font-size: 12px;
    }
    .flight-modal-container .flight-timing-stops ul li span {
        margin-left: 15px;
    }
    .flight-modal-container .flight-facilities {
        padding: 0 8px;
    }
    .flight-modal-container .flight-modal-button {
        padding: 0 5px;
    }
    .flight-modal-container .flight-facilities ul li {
        margin-right: 13px;
    }
    .flight-modal-container .flight-layover-right {
        font-size: 14px;
    }
    .flight-modal-container .flight-layover {
        padding: 0 12px;
    }
    .flight-feature-list {
        padding: 12px 25px 24px 25px;
    }
    .change-link {
        font-size: 9px;
        line-height: 12px;
        color: var(--button-font-color);
    }
    .flight-select-container {
        padding: 15px 5px;
    }
    .selected-total-box {
        padding: 0 20px 0 10px;
        height: 42px;
    }
    .selected-class,
    .selected-passenger {
        font-size: 12px;
        line-height: 15px;
    }
    .total-flight-cost {
        font-size: 14px;
        letter-spacing: 1.17px;
    }
    .total-trip-cost {
        padding: 20px 0;
        font-size: 17px;
        letter-spacing: 1.42px;
        text-transform: capitalize;
    }
    .result-date {
        font-size: 10px;
        line-height: 12px;
        padding: 0 12px;
    }
    .flight-modal-container .flight-box {
        padding: 12px 10px 7px 10px;
    }
}

@media (max-width: 359px) {
    .flight-modal-container .flight-facilities ul li span {
        margin-left: 2px;
    }
    .flight-modal-container .flight-facilities ul li {
        margin-right: 10px;
    }
    .flight-right {
        text-align: right;
    }
}

@media (max-width: 360px) {
    .stop-box-container {
        display: none !important;
    }
}