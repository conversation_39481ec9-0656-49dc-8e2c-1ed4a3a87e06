import { Component, OnInit } from '@angular/core';
import { AdminPanelService } from '../admin-panel.service';
import { DateUtils } from '../util/date-utils';
import { UserAccountService } from '../user-account.service';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { CommonUtils } from '../util/common-utils';
import { DatePipe } from '@angular/common';
import { unwatchFile } from 'fs';
import { Title } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { SearchService } from '../search.service';

@Component({
    selector: 'app-message',
    templateUrl: './message.component.html',
    styleUrls: ['./message.component.scss'],
    standalone: false
})
export class MessageComponent implements OnInit {
  selectDateRange = 'today';
  empNameSearchValue = '';
  number = 0;
  resultErrorMessage = this.translateService.instant('activeTraveler.FetchingData');
  hover: boolean;
  messages = [];
  activeTravellersList = [];
  startDate: Date = new Date();
  endDate: Date = new Date();
  boxSelect = false;
  applyBtn = false;
  applyBtn1 = false;
  applyBtn2 = false;
  message = '';
  sortOptions = [{ value: 'Travel date',name:"activeTraveler.Traveldate" }, { value: 'Name',name:"activeTraveler.Name" }];
  sortValue1 ='activeTraveler.Traveldate';
  sortValue ='Travel date';
  dateType = 'all';
  selectedUser = [];
  dateTypeOptions = [{ id: '7Days', name: 'Next 7 days' }, { id: '30Days', name: 'Next 30 days' }]
  origReportResponse: any;
  constructor(private adminPanelService: AdminPanelService,
    private titleService: Title,
    private userAccountInfoService: UserAccountService,
    public searchService: SearchService,
    public translateService: TranslateService,
    public ngxSmartModalService: NgxSmartModalService) { }

  ngOnInit() {
   // this.titleService.setTitle('Messages');
    this.subscriptionEvents()
    setTimeout(() => {
      this.observable();
    }, 2000);
  }

  subscriptionEvents() {
    this.adminPanelService.originalTravellerResponse = undefined;
    this.adminPanelService.filterDateType = 'all';
    this.applyBtn = true;
    this.activeTravellersList = [];
    this.selectedUser = [];
    this.adminPanelService.filterTravellerName = '';
    this.empNameSearchValue = '';
    this.resultErrorMessage = this.translateService.instant('activeTraveler.FetchingData');
    this.endDate = new Date();
    this.endDate.setDate(this.startDate.getDate() + 180);
    this.applyBtn = true;
    let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.startDate);
    let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(this.endDate);

    let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
    let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
    this.adminPanelService.navigateFrom = 'message';
    this.adminPanelService.fetchActiveTraveler(this.userAccountInfoService.getUserCompanyId(), startDate, endDate);


    this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      if (userAccountInfoObj == null) {
        this.origReportResponse = null;
        this.adminPanelService.originalTravellerResponse = undefined;
        this.adminPanelService.filterTravellerName = '';
        this.adminPanelService.removeDataAfterLogging();
        this.adminPanelService.selectDateRange = 'today';
        this.adminPanelService.loginSessionChanged();
      }
    });
  }
  observable() {
    this.applyBtn = true;
    this.resultErrorMessage = this.translateService.instant('activeTraveler.FetchingData');
    this.selectedUser = [];
    this.messages = [];
    this.adminPanelService.activeTravellerResponseObservable$.subscribe((response) => {
      if (response) {
        this.origReportResponse = deserialize(response);
        this.applyBtn = false;
        if (this.origReportResponse.bookingList.length > 0) {
          this.resultErrorMessage = this.translateService.instant('activeTraveler.FetchingData');
        } else {
          this.resultErrorMessage = 'activeTraveler.0travelersfound';
        }
        this.buildCompanyReportData(this.origReportResponse);
      } else {
        setTimeout(() => {
          this.resultErrorMessage = 'activeTraveler.0travelersfound';
          this.applyBtn = false;
        }, 1000);
      }
    });
  }
  changeStyle($event, i) {
    if ($event.type == 'mouseover') {
      this.hover = true;
      this.number = i;
    } else {
      this.hover = false;
      this.number = 0;
    }
  }

  sortOptionChanged(sortOption) {
    if (!sortOption) return;
    this.sortValue = sortOption.value;
    if(this.sortValue==='Travel date'){
    this.sortValue1 = 'activeTraveler.Traveldate';
  } else if(this.sortValue==='Name'){
    this.sortValue1 = 'activeTraveler.Name';
  }

    this.applyDateFilter();
  }
  buildCompanyReportData(response) {
    this.activeTravellersList = [];
    let index = 0;
    for (let optionItem of response.bookingList) {
      const reportItem = {};
      // const [ first, last ] = response.users[optionItem.userid].userName.split(" ");
      let travellerFullName = response.users[optionItem.userid].userName;
      if (optionItem.primaryTraveller && optionItem.primaryTraveller.userName) {
        travellerFullName = optionItem.primaryTraveller.userName;
      }
      reportItem['email'] = optionItem.userid;

      reportItem['name'] = travellerFullName;
      const [first, last] = travellerFullName.split(' ');
      reportItem['shortname'] = (first.charAt(0) + last.charAt(0));
      let departmentName = this.adminPanelService.getDepartmentName(response.users[optionItem.userid].departmentId);
      reportItem['department'] = departmentName;
      reportItem['bookingDate'] = optionItem.bookingDate;
      reportItem['type'] = optionItem.type;
      reportItem['id'] = index;
      reportItem['option'] = optionItem.option;
      reportItem['destinations'] = optionItem.destinationCity
      reportItem['fitBounds'] = true;
      reportItem['selected'] = false;
      reportItem['ticketNumber'] = optionItem.ticketnumber ? optionItem.ticketnumber : null;
      this.activeTravellersList.push(reportItem);
      index++;
      //  
    }
    this.applySorting(this.activeTravellersList, this.sortValue)
  }
  public applySorting(response, currentSortOptionId) {
    if (currentSortOptionId === 'Travel date') {
      this.sortResultsByTravelByDate(response);
    } else if (currentSortOptionId === 'Name') {
      this.sortResultsByName(response);
    }

  }
  searchByNameChanged(nameString: string) {
    this.adminPanelService.filterTravellerName = nameString;
    this.selectedUser = [];
    this.messages = [];
    this.adminPanelService.filterActiveTraveler1();
  }
  selectEmployee(item) {
    item.selected = true;
    let index = this.selectedUser.findIndex(item1 => item1.email === item.email);
    if (index === -1) {
      this.selectedUser.push(item)
    } else {
      return;
    }
    this.applyBtn1 = true;
    this.messages = [];
    this.adminPanelService.getMessages(this.selectedUser).subscribe(resp => {
      if (resp && resp.success) {

        this.messages = resp.messages;
        this.messages = this.messages.sort(function (a, b) {
          let date = a.time;
          let date1 = b.time;
          date = new Date(date);
          date1 = new Date(date1);
          if (date < date1) return -1;
          else if (date > date1) return 1;
          else return 0;
        });
        this.applyBtn1 = false;
      }
    });
  }
  sendMessage(message) {
    if (this.selectedUser.length === 0) {
      return;
    }
    if (message.trim().length === 0) {
      return;
    }
    if (this.applyBtn2) {
      return;
    }
    this.applyBtn2 = true;
    this.messages = [];
    let splitMsg = message.split("\n", 50);
    let msgArray = [];
    msgArray = [...splitMsg];
    this.adminPanelService.getSentMessages(this.selectedUser, msgArray).subscribe(resp => {
      if (resp && resp.success) {

        this.messages = resp.messages;
        this.messages = this.messages.sort(function (a, b) {
          let date = a.time;
          let date1 = b.time;
          date = new Date(date);
          date1 = new Date(date1);
          if (date < date1) return -1;
          else if (date > date1) return 1;
          else return 0;
        });
        this.applyBtn2 = false;
        this.message = '';
      } else {
        this.applyBtn2 = false;
      }
    });
  }
  unselectedEmployee(item) {
    let index = this.selectedUser.findIndex(item1 => item1.id === item.id);
    let index1 = this.activeTravellersList.findIndex(item1 => item1.id === item.id);
    this.activeTravellersList[index1].selected = false;
    this.selectedUser[index].selected = false;
    for (let item1 of this.activeTravellersList) {
      if (item1.email === item.email) {
        item1.selected = false;
      }
    }
    this.selectedUser.splice(index, 1);
    this.messages = [];
    if (this.selectedUser.length > 0) {
      this.applyBtn1 = true;
      this.adminPanelService.getMessages(this.selectedUser).subscribe(resp => {
        if (resp && resp.success) {

          this.messages = resp.messages;
          this.messages = this.messages.sort(function (a, b) {
            let date = a.time;
            let date1 = b.time;
            date = new Date(date);
            date1 = new Date(date1);
            if (date < date1) return -1;
            else if (date > date1) return 1;
            else return 0;
          });
          this.applyBtn1 = false;
        }
      });
    }
  }
  sortResultsByTravelByDate(response) {
    response = response.sort(function (a, b) {
      let dateA = CommonUtils.getTravelDate(a);
      let dateB = CommonUtils.getTravelDate(b);
      let date = new Date(dateA);
      let date1 = new Date(dateB);
      if (date < date1) return -1;
      else if (date > date1) return 1;
      else return 0;
    });

  }
  sortResultsByName(response) {
    response = response.sort(function (a, b) {
      if (a.name < b.name) return -1;
      else if (a.name > b.name) return 1;
      else return 0;
    });
  }
  selectdateType(type) {
    this.dateType = type;
    this.applyDateFilter();
  }
  clearFilter() {
    this.dateType = 'all';
    this.applyDateFilter();
  }
  applyDateFilter() {
    this.selectedUser = [];
    this.messages = [];
    if (this.dateType === '') {
      this.adminPanelService.filterDateType = 'all';
    } else {
      this.adminPanelService.filterDateType = this.dateType;
    }
    this.adminPanelService.filterActiveTraveler1()
    this.ngxSmartModalService.getModal('travelerFilterModal').close();
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  onSmartModelCancel(modelName: string) {
    this.ngxSmartModalService.getModal(modelName).close();
  }
  getDisplayDateTimeForFlights(dateString: string, format: string): string {
    let date1 = new Date(dateString);
    let datePipe = new DatePipe('en-US');
    return datePipe.transform(new Date(date1), format);
  }
  getTravelerDate(item) {
    return CommonUtils.getTravelDate(item);
  }
}
