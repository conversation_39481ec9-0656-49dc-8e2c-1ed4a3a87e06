import { Component, OnInit } from '@angular/core';
import { UserEntity } from '../entity/user-entity';
import { UserAccountService } from 'src/app/user-account.service';
import { Subscription } from 'rxjs';
import { UserAccountInfo } from 'src/app/entity/user-account-info';
import { environment } from 'src/environments/environment';
import { ActivatedRoute, Router, NavigationEnd, Event } from '@angular/router';
import { DeviceDetailsService } from '../device-details.service';
import { NavigationUtil } from '../util/navigation-util';
import { Title } from '@angular/platform-browser';
@Component({
    selector: 'app-user-profile',
    templateUrl: './user-profile.component.html',
    styleUrls: ['./user-profile.component.scss'],
    standalone: false
})
export class UserProfileComponent implements OnInit {
  isMobile: boolean;
  userName = '';
  deviceSubscription: Subscription;
  private userAccountSubscription: Subscription;
  constructor(private userAccountService: UserAccountService, public router: Router,
    private titleService: Title,
    private deviceDetailsService: DeviceDetailsService) { }

  ngOnInit() {
    this.titleService.setTitle('Profile');
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
      this.userAccountSubscription = this.userAccountService.userAccountInfoObjObserver$.subscribe((userAccountInfo: UserAccountInfo) => {
        this.userName = this.userAccountService.getUserName();
      });
    });
    if (!this.isLoggingIn() && !this.isLoggedIn()) {
      this.router.navigate([this.userAccountService.getDefaultRoutePath()]);
    }
    NavigationUtil.setCurrentNavigationMenu(NavigationUtil.NAVIGATION_MENU_PROFILE);
  }

  isLoggedIn() {
    return this.userAccountService.isLoggedIn();
  }

  isLoggingIn() {
    return this.userAccountService.isLoginInProgress;
  }

}
