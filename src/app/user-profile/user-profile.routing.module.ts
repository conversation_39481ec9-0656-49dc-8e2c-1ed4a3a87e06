import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { UserProfileComponent } from '../user-profile/user-profile.component';
import { ProfileAccountSettingsComponent } from '../profile-account-settings/profile-account-settings.component';




const routes: Routes = [
  {
    path: '',
    component: UserProfileComponent,
    data: { title: 'Profile' }
  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UserProfileRoutingModule {

}

