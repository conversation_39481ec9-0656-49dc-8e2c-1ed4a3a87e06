import { NgModule } from "@angular/core";
import { CommonModule } from '@angular/common';
import { UserProfileRoutingModule } from './user-profile.routing.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ShareModule } from '../share.module';
import { TooltipModule } from 'ng2-tooltip-directive';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { UiSwitchModule } from 'ngx-ui-switch';
import { UserProfileComponent } from './user-profile.component';
import { UserProfileTablistComponent } from '../user-profile-tablist/user-profile-tablist.component';
import { ProfileUserSummaryComponent } from '../profile-user-summary/profile-user-summary.component';
import { ProfileTablistComponent } from '../profile-tablist/profile-tablist.component';
import { ProfilePersonalInfoComponent } from '../profile-personal-info/profile-personal-info.component';
import { ProfileLoyaltyNumbersComponent } from '../profile-loyalty-numbers/profile-loyalty-numbers.component';
import { ProfilePreferencesComponent } from '../profile-preferences/profile-preferences.component';
import { ProfileAddTravellersComponent } from '../profile-add-travellers/profile-add-travellers.component';
import { ProfileAccountSettingsComponent } from '../profile-account-settings/profile-account-settings.component';
import { ProfileHelpComponent } from '../profile-help/profile-help.component';
import { SlackMsgComponent } from '../slack-msg/slack-msg.component';





@NgModule({
  imports: [
    CommonModule,
    UserProfileRoutingModule,
    NgbModule,
    ShareModule,
    TooltipModule,
    NgxSmartModalModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    BsDatepickerModule,
    UiSwitchModule,
  ],
  declarations: [UserProfileComponent,
    UserProfileTablistComponent,
    ProfileUserSummaryComponent,
    ProfileTablistComponent,
    ProfilePersonalInfoComponent,
    ProfileLoyaltyNumbersComponent,
    SlackMsgComponent,
    ProfilePreferencesComponent,
    ProfileAccountSettingsComponent,
    ProfileAddTravellersComponent,
    ProfileHelpComponent,
    ProfileUserSummaryComponent,
  ],
})
export class UserProfileModule {

}