:host {
  width: 100vw;
}

body {
  color: #413E3B;
  margin: 0 auto;
  font-family: "apercu-r";
  font-size: 16px;
  width: 100%;
  overflow-x: hidden;
  background: #F6F6F6;
}

.pageBody {
  margin: 0 auto;
  max-width: 1440px;
  width: 100%;
  min-height: 100vh;
}

* {
  outline: none !important;
  list-style: none;
}

img {
  max-width: 100%;
  max-height: 100%;
}

a {
  color: var(--button-font-color);
  font-family: "apercu-b";
  text-transform: uppercase;
  font-size: 12px;
}

.link-font {
  font-size: 12px;
}

a:hover,
a:focus,
a:active {
  text-decoration: none;
}

p {
  margin-bottom: 0;
  font-size: inherit;
  line-height: normal;
}

ul {
  padding: 0;
  margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "apercu-r";
  margin: 0;
}

.pad_0 {
  padding: 0 !important;
}

label {
  margin-bottom: 0;
  font-weight: normal;
}

*::-moz-focus-inner {
  border: 0 !important;
}

select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #000;
}

.table-view {
  display: table;
  float: left;
  width: 100%;
  height: 100%;
}

.table-cell-view {
  display: table-cell;
  vertical-align: middle;
}

strong {
  font-weight: normal;
  font-family: "apercu-m";
}

b {
  font-weight: normal;
  font-family: "apercu-b";
}

.inlineblock_m {
  display: inline-block;
  vertical-align: middle;
}

.inlineblock_b {
  display: inline-block;
  vertical-align: bottom;
}

.inlineblock_t {
  display: inline-block;
  vertical-align: top;
}

.row {
  margin-left: -8px !important;
  margin-right: -8px !important;
}

.table-view {
  display: table;
  height: 100%;
  width: 100%;
}

.table-cell-view {
  display: table-cell;
  vertical-align: middle;
}

.link-icon,
.link-text {
  display: inline-block;
  vertical-align: middle;
}

.link-icon {
  margin-right: 7px;
}

.flexContainer {
  display: flex;
  flex-wrap: wrap;
  float: left;
  width: 100%;
  justify-content: flex-start;
}

[class^="col-"],
[class^=" col-"] {
  padding-right: 8px;
  padding-left: 8px;
}

.custom-wd-sm {
  width: 118px !important;
  flex: 0 0 118px !important;
}

.line-break {
  float: left;
  width: 100%;
  height: 0px;
}

/*  .container {
    padding-left: 64px;
    padding-right: 64px;
    max-width: none; }*/

span.error {
  color: #f00;
  font-size: 12px;
}

.stripeElements {
  float: left;
  width: 100%;
}

.card-div {
  border: 1px solid #E1E1E1;
  border-radius: 6px;
  background-color: #FFFFFF;
  margin-bottom: 14px;
  float: left;
  width: 100%;
}

.shadow {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.19);
}

.shadow2 {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
}

/*body css*/
header {
  background: linear-gradient(39.62deg, var(--dark-bg-color) 0%, var(--dark-bg-color) 3.43%, #862EF6 11.42%, #7940F3 25.41%, #655EEE 42.81%, #4888E8 62.15%, #25BDE0 82.83%, var(--button-bg-color) 100%);
}

.header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 114px;
  margin-bottom: 49px;
}

.logo a {
  display: inline-block;
  width: 170px;
}

.navigation ul li {
  display: inline-block;
  margin-right: 19px;
}

.navigation ul li:last-child {
  margin-right: 0;
}

.navigation ul li a {
  color: #FFFFFF;
  font-size: 12px;
  letter-spacing: 1px;
  line-height: 18px;
  font-family: "apercu-b";
}

.navigation {
  display: inline-block;
}

.admin {
  display: inline-block;
  margin-left: 28px;
  position: relative;
  text-align: right;
}

.admin-name-img {
  cursor: pointer;
}

.admin-name {
  font-size: 14px;
  line-height: 17px;
  color: #FFFFFF;
  display: inline-block;
  vertical-align: middle;
}

.admin-img {
  display: inline-block;
  background-color: #E2E2E2;
  height: 30px;
  width: 30px;
  border-radius: 50%;
  vertical-align: middle;
}

.admin-img img {
  display: inline-block;
  vertical-align: top;
}

.admin-list {
  width: 258px;
  height: auto;
  display: none;
  padding: 20px 32px;
  z-index: 1;
  background: #fff;
  position: relative;
  border-radius: 6px;
  border: 1px solid #E1E1E1;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  right: -38px;
  top: 31px;
  text-align: left;
}

.admin-list:before {
  position: absolute;
  top: -5px;
  right: 32px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 5px solid #fff;
  content: '';
}

.admin-list ul li {
  margin-bottom: 8px;
}

.admin-list ul li:last-child {
  margin-bottom: 0;
}

.admin-list-icon {
  color: #fff;
  font-size: 16px;
  display: inline-block;
  vertical-align: middle;
}

.admin-list ul li a {
  font-family: "apercu-r";
  text-transform: uppercase;
  font-size: 12px;
  color: #413E3B;
  letter-spacing: 1px;
}

/*tab*/
/*  .tab-list {
    overflow: auto;
    position: relative;
    z-index: 0; }
  
  .tab-list > ul {
    white-space: nowrap; }
  
  .tab-content {
    display: none; }
  
  .tab-content.active {
    display: block; }
  
  .tab-container {
    width: 100%;
    float: left;
    margin-top: 15px; }
  
  .tab-list ul li {
    display: inline-block;
    margin-right: 32px;
    text-transform: uppercase;
    cursor: pointer;
    padding-bottom: 3px;
    margin-bottom: 0;
    font-size: 12px;
    letter-spacing: 1px;
    line-height: 18px;
    color: #413E3B; }
  
  .tab-list ul li:last-child {
    margin-right: 0; }
  
  .tab-list ul li.active {
    color: var(--button-font-color);
    font-family: "apercu-b";
    border-bottom: 1px solid var(--button-font-color);
  }*/


.top-strip {
  background: #000;
  float: left;
  width: 100%;
  height: 43px;
}

.tab-content {
  float: left;
  width: 100%;
  background-color: #FFFFFF;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
  padding: 0 20px;
}

.tab-list-item {
  display: inline-block;
  margin-right: 60px;
  margin-bottom: 0;
  padding-bottom: 0;

}

.tab-list-item a {
  cursor: pointer;
  position: relative;
  display: inline-block;
  color: #FFFFFF;
  font-size: 12px;
  opacity: 0.6;
  line-height: 15px;
  padding: 10px 5px 13px 5px;
}

.tab-list-item a img {
  margin-right: 10px;
}

.tab-list-item.active a {
  opacity: 1;
}

.tab-list-item.active a::after {
  position: absolute;
  content: "";
  bottom: 2px;
  background: #fff;
  left: 0;
  right: 0;
  width: auto;
  height: 3px;
}



/*card div*/
.card-div-header {
  float: left;
  width: 100%;
  cursor: pointer;
  margin-bottom: 20px;
  position: relative;
  font-family: "apercu-b";
  font-size: 12px;
  letter-spacing: 1px;
  line-height: 18px;
}

.card-div-body {
  float: left;
  width: 100%;
  margin-top: 10px;
}

.multiple-inputs {
  float: left;
  width: 100%;
}

.card-div-header h3 {
  font-size: 22px;
  letter-spacing: -0.98px;
  line-height: 26px;
  font-family: "apercu-mono";
}

.card-div-inner {
  float: left;
  padding: 30px 40px 10px;
}

.card-div-footer {
  float: left;
  width: 100%;
  padding: 10px 0 20px;
}

.card-edit {
  position: absolute;
  right: 0;
  height: 100%;
  display: flex;
  align-items: center;
  top: 0px;
  color: var(--button-font-color);
  cursor: pointer;
}

.edit-text {
  display: none;
  text-transform: uppercase;
  padding-right: 3px;
}

.edit-icon {
  font-size: 20px;
}

.filled .edit-text {
  display: block;
}

.filled .edit-icon {
  display: block !important;
}

.card-div.active .edit-icon {
  transform: rotate(-180deg);
}

.card-div.active .edit-text {
  display: none !important;
}

.card-div.active .edit-button {
  display: none !important;
}

.traveller-short-info {
  font-size: 14px;
  letter-spacing: -0.62px;
  line-height: 16px;
  color: #A6A5A4;
  font-family: "apercu-mono";
}

.traveller-short-info-inner {
  float: left;
  width: 100%;
  padding-top: 3px;
  line-height: 21px;
}

.traveller-short-info-inner span {
  display: inline-block;
  vertical-align: middle;
}

.traveller-short-info-inner .shortInfoSeperator:last-child {
  display: none;
}

.traveller-short-info-inner.shortInfoPrimary {
  font-size: 16px;
  line-height: 33px;
  color: #6C6865;
  margin-top: 23px;
}

/*.card-div.active .card-div-header{ cursor: default;}*/
.accord-header {
  float: left;
  width: 100%;
  cursor: pointer;
}

.accord-header h4 {
  font-size: 16px;
  letter-spacing: -0.71px;
  line-height: 22px;
  font-family: "apercu-mono";
}

.accord-content {
  float: left;
  width: 100%;
}

@media (max-width: 1200px) {
  .admin-list {
    right: 0;
  }
}

@media (max-width: 767px) {
  .admin-img {
    height: 19px;
    width: 19px;
  }

  .admin-name {
    font-size: 12px;
    line-height: 15px;
  }

  .filled .edit-text {
    display: none;
  }

  .traveller-short-info-inner.shortInfoPrimary {
    font-size: 12px;
    letter-spacing: -0.53px;
    line-height: 12px;
    color: #A6A5A4;
  }

  .overflow-hidden {
    overflow: hidden !important;
  }

  .overlay {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.5);
    z-index: 9;
  }

  .admin-list {
    position: static;
    border: none;
    border-radius: 0;
  }

  .admin-list::before {
    display: none;
  }

  .admin.active .admin-name-img {
    border-bottom: 1px solid #e6e6e6;
    padding: 9px 30px 14px 30px;
  }

  .admin.active {
    margin-left: 0;
    position: fixed;
    z-index: 9999999999;
    right: 0;
    background: #fff;
    top: 0;
  }

  .admin.active .admin-name {
    color: #413E3B;
  }
}

/*# sourceMappingURL=common.css.map */

/*placeholder*/
::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  opacity: 1;
  color: #aeaeae;
}

::-moz-placeholder {
  /* Firefox 19+ */
  opacity: 1;
  color: #aeaeae;
}

:-ms-input-placeholder {
  /* IE 10+ */
  opacity: 1;
  color: #aeaeae;
}

:-moz-placeholder {
  /* Firefox 18- */
  opacity: 1;
  color: #aeaeae;
}

/*error*/
span.error {
  color: #f00;
  font-size: 12px;
  float: left;
  width: 100%;
}

/*button styles*/
.button-container {
  float: left;
  width: 100%;
}

.button {
  border-radius: 2px !important;
  cursor: pointer;
  height: 50px;
  padding: 0 32px;
  border: none;
  text-transform: capitalize;
  font-size: 16px;
  font-family: var(--globalFontfamilyr);font-weight: bold;;
  letter-spacing: 0.48px;
  line-height: 25px;
}

.button-primary {
  background: var(--button-bg-color);
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
  text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
  color: var(--button-font-color);
}

.button-secondary {
  background: transparent;
  color: var(--button-bg-color);
  border: 2px solid var(--button-bg-color);
}

.button-text {
  background: none;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--button-font-color);
  border: none;
  border-radius: 0;
  padding: 0;
  margin: 0 20px;
  font-family: "apercu-b";
  font-size: 12px;
}

.button-black {
  background: #000;
  border-radius: 5px !important;
}

.button-yellow {
  background: #ffc34f;
  border-radius: 5px !important;
}

.pay-buttons .button {
  display: block;
  width: 266px;
  margin-bottom: 16px;
}

.button.disabled {
  pointer-events: none;
  opacity: 0.4;
}

/*radio button styles*/
.mdl-radio {
  font-family: "apercu-r";
  line-height: 26px;
}

.mdl-radio__outer-circle {
  height: 18px;
  width: 18px;
  border: 1px solid #C8C8C8;
}

.mdl-radio__inner-circle {
  top: 6px;
  left: 2px;
  width: 14px;
  height: 14px;
  background: var(--button-bg-color);
}

.mdl-radio.is-checked .mdl-radio__outer-circle {
  border: 1px solid var(--button-bg-color);
}

.mdl-radio.is-checked .mdl-radio__label {
  font-family: "apercu-b";
}

.mdl-radio,
.mdl-radio.is-upgraded {
  width: auto;
  margin-right: 20px;
  padding: 0 0 0 28px;
  font-family: "apercu-r";
}

/*checkbox styles*/
.mdl-checkbox {
  float: left;
  width: auto;
  font-weight: normal;
  cursor: pointer;
  margin-right: 8px;
  margin-bottom: 6px;
}

.mdl-checkbox:last-child {
  margin-right: 0;
}

.mdl-checkbox__box-outline {
  border: 1px solid #979797;
  width: 17px;
  height: 17px;
  border-radius: 0;
}

.mdl-checkbox.is-checked .mdl-checkbox__box-outline {
  border: 1px solid var(--button-bg-color);
}

.mdl-checkbox.is-checked .mdl-checkbox__tick-outline {
  background-color: var(--button-bg-color) !important;
}

.mdl-checkbox__ripple-container {
  top: 4px;
  left: 2px;
}

.selection-checkbox.checkbox2.checkbox-seperate {
  margin-right: 8px !important;
  margin-left: 0;
  width: auto;
  padding: 0 38px;
}

.selection-checkbox.checkbox2.checkbox-seperate .mdl-radio__label {
  font-family: "apercu-mono" !important;
}

/*selection checkbox*/
.selection-checkbox .mdl-radio__ripple-container,
.selection-checkbox .mdl-radio__outer-circle,
.selection-checkbox .mdl-radio__inner-circle {
  display: none;
}

.selection-checkbox .mdl-radio {
  float: left;
  width: 100%;
  border: 2px solid #E7E6E4;
  background-color: #FFFFFF;
  height: 50px;
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: normal;
  cursor: pointer;
}

.selection-checkbox .mdl-radio.is-checked {
  background: var(--button-bg-color);
  border: 2px solid var(--button-bg-color);
}

.selection-checkbox.mdl-checkbox,
.selection-checkbox.mdl-radio {
  cursor: pointer;
  height: 155px;
  width: 155px;
  border: 2px solid #e7e6e4;
  background-color: rgba(255, 255, 255, 0.3);
  padding: 5px;
  margin-bottom: 16px;
}

.selection-checkbox .mdl-checkbox__box-outline,
.selection-checkbox .mdl-checkbox__focus-helper,
.selection-checkbox .mdl-checkbox__ripple-container,
.selection-checkbox .mdl-radio__outer-circle,
.selection-checkbox .mdl-radio__inner-circle,
.selection-checkbox .mdl-radio__ripple-container {
  display: none;
}

.selection-checkbox .mdl-checkbox__label,
.selection-checkbox .mdl-radio__label {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  justify-content: center;
  align-items: center;
  font-family: "apercu-r";
}

.selection-checkbox .mdl-checkbox__label img,
.selection-checkbox .mdl-radio__label img {
  margin-bottom: 10px;
}

.selection-checkbox.mdl-checkbox.is-checked,
.selection-checkbox.mdl-radio.is-checked {
  background-color: #FFFFFF;
  background-color: #ECFFFD;
  border: 2px solid var(--button-bg-color);
}

.selection-checkbox.mdl-checkbox.is-checked .mdl-checkbox__label span,
.selection-checkbox.mdl-radio.is-checked .mdl-checkbox__label span {
  font-family: "apercu-b";
}

.selection-checkbox.checkbox2 {
  height: 50px;
  width: 165px;
  float: left;
  margin-left: -2px;
  position: relative;
  margin-right: 0 !important;
  padding: 0 10px;
}

.selection-checkbox.checkbox2:first-child {
  margin-left: 0;
}

.selection-checkbox.checkbox2.is-checked {
  z-index: 2;
}

.airlinesBack {
  position: absolute;
  left: 15px;
  width: 13px;
  top: 9px;
  display: none;
}

.airlineCheckboxContainer,
.hotelCheckboxContainer {
  width: 684px;
  float: left;
}

.airlineCheckboxContainer .mdl-checkbox .mdl-checkbox__label .mdl-checkbox__label-img img {
  width: 70px;
  max-width: 100px;
  max-height: 100px;
}

.any .selected {
  display: none;
}

.any.is-checked .notSelected {
  display: none;
}

.any.is-checked .selected {
  display: block;
}

/*.airlineCheckboxContainer,.hotelCheckboxContainer {width: 900px;float: none; margin: 0 -8px;}*/
.airline-div {
  padding-left: 8px;
  padding-right: 8px;
}

.checkbox-radio-container .error {
  padding: 0 8px;
}

.seat-selection img.active {
  display: none;
}

.seat-selection.is-checked img.inactive {
  display: none;
}

.seat-selection.is-checked img.active {
  display: block;
}

.mdl-checkbox__label-img {
  height: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
  float: left;
  width: 100%;
}

.mdl-checkbox__label-text {
  float: left;
  width: 100%;
  text-align: center;
}

/*input-textfield*/
.input-box {
  float: left;
  width: 100%;
  margin-bottom: 12px;
  position: relative;
}

.input-label {
  float: left;
  width: 100%;
  color: #ABA7A4;
  font-size: 14px;
  line-height: 17px;
  margin-bottom: 8px;
  font-family: "apercu-r";
  font-weight: normal;
}

.input-textfield {
  float: left;
  width: 100%;
  border: 2px solid #E7E6E4;
  background-color: #FFFFFF;
  height: 50px;
  font-size: 16px;
  padding: 5px 16px;
  color: #413E3B;
  resize: none;
  border-radius: 0 !important;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  font-family: "apercu-mono";
}

textArea.input-textfield {
  padding-top: 10px;
  padding-bottom: 10px;
}

.input-textfield[disabled] {
  background-color: #F6F6F6;
}

.label-note {
  font-size: 12px;
  line-height: 12px;
  color: var(--dark-bg-color);
  padding-left: 7px;
}

.datepicker {
  background-image: url(/../../assets/images/calendar.png);
  background-repeat: no-repeat;
  background-position: right 17px center;
}

.input-textfield.datepicker {
  padding-right: 50px;
}

.input-box-icon-container {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  height: 50px;
  left: 19px;
}

.input-box-icon-container.withLabel {
  margin-top: 25px;
}

.input-box-icon-container.left {
  left: 17px;
}

.input-box-icon-container.right {
  right: 19px;
}

.input-box-icon-container.right+input {
  padding-right: 50px;
}

.input-box-icon-container.left+input {
  padding-left: 50px;
}

.input-label {
  float: left;
  width: 100%;
  color: #ABA7A4;
  font-size: 14px;
  line-height: 17px;
  margin-bottom: 8px;
  font-family: "apercu-r";
  font-weight: normal;
}

.input-textfield {
  float: left;
  width: 100%;
  border: 2px solid #E7E6E4;
  font-size: 16px;
  background-color: #FFFFFF;
  height: 50px;
  padding: 5px 16px;
}

.input-with-checkbox input {
  padding-right: 130px;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__label {
  font-size: 14px;
  line-height: 17px;
  font-weight: normal;
  font-family: "apercu-r";
  width: 100%;
}

.input-textfield-lg {
  height: 64px;
}

.input-textfield {
  margin-bottom: 8px;
}

.input-box-with-icon input {
  padding-left: 45px;
}

/*datepicker*/
.ui-datepicker {
  z-index: 9999 !important;
}

.ui-datepicker-month,
.ui-datepicker-year {
  background: #fff;
  border: 2px solid #E7E6E4;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  font-family: "apercu-r" !important;
  font-weight: normal !important;
}

.ui-widget-content {
  color: #413E3B;
}

.ui-widget {
  font-family: "apercu-r";
  font-weight: normal;
}

.ui-datepicker-month,
.ui-datepicker-year {
  background-image: url(/../../assets/images/arrow-down.png);
  background-repeat: no-repeat;
  background-position: right 5px center;
}

.ui-widget-header {
  border: 1px solid #EEECEB;
  background: #EEECEB;
  color: #413E3B;
  font-weight: normal;
}

.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
  border: 1px solid #E7E6E4;
  background: #EEECEB;
  color: #413E3B;
}

.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
  border: 1px solid #E7E6E4;
  background: #EEECEB;
  font-weight: normal;
  color: #413E3B;
}

.ui-widget-header .ui-icon.ui-icon-circle-triangle-w {
  background-image: url(/../../assets/images/down-icon.png);
  background-position: -4px 4px;
  background-size: 24px auto;
  transform: rotate(90deg);
  height: 24px;
  width: 24px;
}

.ui-widget-header .ui-icon.ui-icon-circle-triangle-e {
  background-image: url(/../../assets/images/down-icon.png);
  background-position: 4px -4px;
  background-size: 24px auto;
  transform: rotate(-90deg);
  height: 24px;
  width: 24px;
}

.ui-datepicker .ui-datepicker-prev-hover {
  left: 2px;
  top: 2px;
}

.ui-datepicker .ui-datepicker-next-hover {
  right: 2px;
  top: 2px;
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
  border: 1px solid var(--button-bg-color);
  background: var(--button-bg-color);
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  top: 6px;
}

/*intelinput*/
.intl-tel-input {
  width: 100%;
}

.intl-tel-input .selected-flag .iti-arrow {
  border: none !important;
  background-image: url(/../../assets/images/arrow-down1.png);
  background-repeat: no-repeat;
  width: 28px;
  height: 28px;
  background-size: 28px;
  background-position: right center;
  top: 3px;
  height: 100%;
  right: 6px;
}

.intl-tel-input.allow-dropdown .selected-flag,
.intl-tel-input.separate-dial-code .selected-flag {
  width: 75px;
}

.intl-tel-input .selected-flag {
  padding: 0 0 0 19px;
}

.intl-tel-input.allow-dropdown input,
.intl-tel-input.allow-dropdown input[type="text"],
.intl-tel-input.allow-dropdown input[type="tel"],
.intl-tel-input.separate-dial-code input,
.intl-tel-input.separate-dial-code input[type="text"],
.intl-tel-input.separate-dial-code input[type="tel"] {
  padding-left: 80px;
}

/*select box styles*/
.select-box {
  position: relative;
  float: left;
  width: 100%;
}

.title-select-box {
  max-width: 118px;
}

.select-box select {
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  position: relative;
  z-index: 2;
  background-image: url(/../../assets/images/arrow-down1.png);
  background-repeat: no-repeat;
  background-position: right 5px center;
  background-size: 26px;
}

/*select2*/
select {
  font-family: "apercu-mono";
}

.select2-container {
  width: 100% !important;
  font-family: "apercu-mono";
}

.select2-selection__arrow {
  position: relative;
  z-index: 9999;
}

.select2-selection__arrow b {
  border-width: 0 !important;
}

.select2-selection__arrow:after {
  content: '';
  position: absolute;
  background-image: url(/../../assets/images/arrow-down1.png);
  background-repeat: no-repeat;
  height: 36px;
  width: 36px;
  right: 6px;
  top: 8px;
}

.select2-container--default .select2-selection--single {
  height: 50px;
  border-radius: 0;
  border: 2px solid #E7E6E4;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 46px;
}

.select2-dropdown {
  border: 2px solid #E7E6E4;
  z-index: 9999;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--button-bg-color) !important;
  color: white;
}

.select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 16px;
  padding-right: 40px;
}

.selection-checkbox.mdl-radio.is-upgraded:not(.checkbox2) {
  padding-left: 0;
}

/*modal css*/
.modal-dialog {
  max-width: 710px;
}

.modal-content {
  border-radius: 0px;
  box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
  border: none;
  text-align: center;
  max-width: 710px;
  margin: 24px auto;
  width: calc(100% - 48px);
}

.modal-header {
  border: none;
  padding: 22px 17px 15px 25px;
}

.modal-title {
  display: inline-block;
  width: 35px;
  float: left;
}

.modal-title img {
  width: 35px;
}

.close {
  color: #000;
  opacity: 1;
}

.close:hover {
  color: #000;
  opacity: 1;
}

.modal-body {
  padding-bottom: 35px;
}

.modal-icon-container {
  float: left;
  width: 100%;
  text-align: center;
}

.modal-icon {
  background-color: #DDFFF7;
  border: 3px solid #1EBD97;
  width: 48px;
  height: 48px;
  display: inline-flex;
  color: #1EBD97;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
}

.modal-icon i {
  font-size: 30px;
}

.modal-content-heading {
  font-size: 22px;
  letter-spacing: -0.98px;
  line-height: 56px;
  color: #3C413B;
  float: left;
  width: 100%;
}

.modal-content-heading {
  font-size: 22px;
  letter-spacing: -0.98px;
  line-height: 56px;
  color: #3C413B;
  float: left;
  width: 100%;
  margin-top: 15px;
  margin-bottom: 10px;
}

.modal-content-heading h3 {
  margin: 0;
  font-size: 22px;
  color: #413E3B;
}

.modal-content-text {
  color: #6C6865;
  font-size: 18px;
  font-family: "apercu-r";
  line-height: 26px;
  margin-bottom: 50px;
  max-width: 530px;
  margin-left: auto;
  margin-right: auto;
}

.modal-content-text p {
  line-height: 26px;
}

.modal-backdrop {
  background-color: #eeedeb;
}

.modal-backdrop.in {
  filter: alpha(opacity=80);
  opacity: .8;
}

.fullBox {
  max-width: 667px;
}

.airlineSearch {
  background-image: url(/../../assets/images/inactive-arrow.png);
  background-repeat: no-repeat;
}

.airlineSearch {
  width: 100%;
  padding-right: 40px;
  background-image: url(/../../assets/images/inactive-arrow.png);
  background-repeat: no-repeat;
  background-position: right 14px center;
}

.primary-label {
  font-size: 16px;
  line-height: 27px;
  color: #413E3B;
}

.text-label {
  margin-bottom: 0;
}

.map {
  max-width: 665px;
}

.ui-autocomplete {
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
}

.card-add-block {
  float: left;
  width: 100%;
  margin-bottom: 20px;
}

.card-save-button {
  text-align: right;
}

.card-box {
  max-width: 660px;
}

.payment-radio-button {
  height: 40px;
}

.payment-radio-button .mdl-radio {
  width: 100%;
}

.payment-radio-button .mdl-radio__label img {
  width: 25px;
  margin-top: -3px;
}

@media (max-width: 991px) {
  .input-textfield {
    max-width: 600px;
  }
}

@media (max-width: 767px) {
  .input-box {
    margin-bottom: 0;
  }

  .input-label {
    font-size: 12px;
    line-height: 15px;
    margin-bottom: 4px;
  }

  .primary-label {
    font-size: 14px;
    line-height: 18px;
    margin-bottom: 7px;
  }

  .select-box {
    margin-bottom: 8px;
  }

  .select2-container--default .select2-selection--single {
    height: 40px;
  }

  .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 37px;
    font-size: 12px;
  }

  .select2-selection__arrow:after {
    background-size: 26px;
  }

  .select2-selection__arrow::after {
    height: 26px;
    width: 26px;
    top: 7px;
  }

  .input-textfield {
    height: 40px;
    font-size: 12px;
    line-height: 15px;
  }

  .selection-checkbox.checkbox2 {
    height: 40px;
    font-size: 12px;
    line-height: 15px;
  }

  .selection-checkbox.checkbox2.checkbox-seperate {
    padding: 0 20px;
  }

  .button {
    height: 46px;
    font-size: 12px;
    line-height: 12px;
    letter-spacing: 1px;
    text-transform: uppercase;
  }

  .selection-checkbox.mdl-checkbox,
  .selection-checkbox.mdl-radio {
    height: 90px;
    width: 90px;
    margin-bottom: 8px;
    margin-right: 0;
  }

  .mdl-checkbox__label,
  .mdl-radio__label {
    font-size: 12px;
    line-height: 15px;
  }

  .selection-checkbox.checkbox2 {
    height: 57px;
    width: 100%;
    margin-bottom: 0;
    margin-top: -2px;
    margin-left: 0;
  }

  .selection-checkbox.checkbox2 .mdl-checkbox__label,
  .selection-checkbox.checkbox2 .mdl-radio__label {
    align-items: flex-start !important;
  }

  .selection-checkbox.checkbox2.checkbox-seperate {
    height: 40px;
    margin-bottom: 8px;
  }

  span.error {
    font-size: 10px;
    margin-top: -4px;
  }

  .mdl-radio__outer-circle {
    height: 14px;
    width: 14px;
  }

  .mdl-radio__inner-circle {
    top: 7px;
    left: 3px;
    width: 8px;
    height: 8px;
  }

  .mdl-radio,
  .mdl-radio.is-upgraded {
    padding: 0 0 0 20px;
  }

  .mdl-radio {
    line-height: 19px;
  }

  .mdl-checkbox__label-img {
    height: 60px;
  }

  .card-box {
    max-width: none;
    width: 100%;
  }
}

@media (max-width: 359px) {

  .selection-checkbox.mdl-checkbox:not(.checkbox2),
  .selection-checkbox.mdl-radio:not(.checkbox2) {
    height: 85px;
    width: 85px;
  }
}

/*# sourceMappingURL=component.css.map */
@media (max-width: 1200px) {
  .container {
    padding-left: 30px;
    padding-right: 30px;
  }

  .card-div-inner {
    padding: 30px 30px 10px;
  }

  .user-summery-inner {
    padding: 10px 19px 10px 26px;
  }
}

@media (max-width: 992px) {
  .container {
    padding-left: 16px;
    padding-right: 16px;
  }

  .card-div-inner {
    padding: 30px 20px 10px;
  }

  .features {
    display: none;
  }
}

@media (max-width: 767px) {
  .container {
    padding-left: 16px;
    padding-right: 16px;
  }

  .tab-list {
    padding-left: 16px;
    padding-right: 16px;
  }

  .user-summery-inner {
    height: auto;
    align-items: flex-start;
    flex-direction: column;
    padding: 0;
  }

  .user-summery-left {
    float: left;
    width: 100%;
    padding: 15px 15px 20px 15px;
  }

  .user-summery-right {
    float: left;
    width: 100%;
  }

  .user-summery-card.card-div.shadow2 {
    box-shadow: none;
    width: 50%;
    margin: 0;
    border-radius: 0;
    border-bottom: none;
    border-left: none;
    height: 85px;
    padding: 4px 10px 23px 15px;
  }

  .user-summery-card.card-div.shadow2:first-child {
    border-radius: 0 0 0 6px;
  }

  .user-summery-card.card-div.shadow2:last-child {
    border-right: none;
    border-radius: 0 0 6px 0;
  }

  .logo a {
    width: 88px;
  }

  .user-img {
    width: 51px;
  }

  .user-img img {
    border-radius: 50%;
  }

  .user-name {
    font-size: 16px;
    line-height: 20px;
  }

  .user-email,
  .user-phone {
    font-size: 12px;
    line-height: 17px;
  }

  .user-summery-card-heading span {
    font-size: 12px;
    line-height: 16px;
  }

  .gallop-cash-div .user-summery-card-content {
    font-size: 18px;
    line-height: 22px;
  }

  .user-summery-card-content {
    font-size: 14px;
    line-height: 17px;
  }

  .cashback-text-width {
    max-width: none;
  }

  .header-inner {
    height: 112px;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .user-summery-card-content {
    height: auto;
    margin-top: 5px;
  }

  .card-div-header h3 {
    font-size: 16px;
    letter-spacing: -0.71px;
    line-height: 22px;
  }

  .card-div.active {
    width: calc(100% + 32px);
    margin-left: -16px;
    border-radius: 0;
    box-shadow: none;
  }

  .card-div {
    margin-bottom: 6px;
  }

  /*    .tab-list ul li {
      padding-bottom: 7px;
      margin-bottom: 15px;
      margin-right: 22px; }*/

  .card-div-inner {
    padding: 24px 16px 4px;
  }

  .card-div.active .card-div-inner {
    padding: 24px 32px 4px;
  }

  .airline-div {
    padding-left: 4px;
    padding-right: 4px;
  }

  .airlineCheckboxContainer,
  .hotelCheckboxContainer {
    width: 100%;
  }

  .airlineCheckboxContainer .mdl-checkbox .mdl-checkbox__label .mdl-checkbox__label-img img {
    width: 34px;
    max-width: 50px;
    max-height: 50px;
  }

  .seatselectionContainer .mdl-checkbox__label-img img {
    max-width: 40px;
    max-height: 40px;
  }

  .traveller-short-info {
    font-size: 12px;
    letter-spacing: -0.53px;
    line-height: 12px;
  }

  .card-div-body {
    margin-top: 2px;
  }

  .card-div-footer {
    padding: 16px 0 20px;
  }

  .tab-container {
    margin-bottom: 24px;
  }

  .accord {
    margin-bottom: 7px;
  }

  .heading-link {
    margin-top: 9px;
    margin-bottom: 0;
  }

  .user-summery {
    margin-top: 0;
  }

  .iti-container {
    width: auto;
  }

  /*.intl-tel-input .country-list{width: 280px !important; max-height: 200px !important;}*/
  .basic-economy-warning-inner {
    font-size: 14px;
    line-height: 18px;
    padding: 10px 20px;
  }

  .basicEconomyRadioButtons {
    margin-top: 17px;
    margin-bottom: 8px;
  }

  .preffered-home-airport {
    margin-top: 16px;
  }

  .passwordCheck {
    top: 7px;
  }

  .subscribe-card-div .card-edit {
    float: left;
    position: static;
    margin-top: 14px;
  }

  .payment {
    padding-left: 0;
    margin-top: 4px;
    margin-bottom: 2px;
  }

  .payment-inner {
    border: none;
  }

  .payment-inner {
    align-items: flex-start;
    flex-direction: column-reverse;
  }

  .payment h4 {
    font-size: 14px;
    line-height: 17px;
  }

  .payment h4 span {
    display: inline-block;
    vertical-align: middle;
  }

  .payment h4 span.prices {
    font-size: 16px;
    letter-spacing: -0.87px;
    margin-left: 5px;
  }

  .promo {
    border-top: 1px solid #e6e6e6;
    border-bottom: 1px solid #e6e6e6;
    float: left;
    width: 100%;
    padding: 9px 0 11px;
    margin-bottom: 16px;
  }

  .promo .link-font {
    font-size: 10px;
  }

  .promo-box {
    position: fixed;
    top: auto;
    bottom: 0;
    width: 100%;
    height: auto;
    padding: 32px;
  }

  .promo-box-input .link-font {
    top: 36px;
  }

  .card-box {
    padding-left: 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 9;
    margin-top: 0;
    margin-bottom: 0;
  }

  .card-box-inner {
    background-color: #fff;
    border: 2px solid #E5E5E5;
    box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.14);
    border-radius: 0;
    padding: 15px 32px 40px 32px;
  }

  .input-box-icon-container {
    height: 40px;
  }

  /*.subscribe-card-div .card-div-header{ margin-bottom: 0;}*/
  .subscription-details ul {
    flex-direction: column;
  }

  .subscription-details ul li::after {
    display: none;
  }

  .subscription-details ul li {
    font-size: 12px;
    margin-bottom: 24px;
  }

  .subscription-details ul li:last-child {
    margin-bottom: 0;
  }

  .cancelSubscriptionButton {
    margin-top: 20px;
  }

  .paymentMethodSection {
    margin-bottom: 8px;
  }

  .paymentMethodHeading {
    margin-bottom: 15px;
  }

  .billingRadio .mdl-radio__label {
    font-size: 14px;
    line-height: 22px;
  }

  .subscribe-card-div.active .card-div-header {
    margin-bottom: 0;
  }

  .admin-list {
    right: 0;
  }

  .creditCardBox.creditCardBoxPaymentMethods .card-box-inner {
    padding: 15px 32px 40px 32px;
  }
}

@media (max-width: 359px) {
  .card-div.active .card-div-inner {
    padding-left: 15px;
    padding-right: 15px;
  }
}

/*# sourceMappingURL=responsive.css.map */
.user-summery {
  float: left;
  width: 100%;
  margin-top: 0;
}

.user-summery-inner {
  height: 169px;
  width: 100%;
  float: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 39px 10px 46px;
}

.user-detail {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.user-img {
  width: 90px;
  border-radius: 50%;
  margin-right: 14px;
}

.user-img img {
  border-radius: 50%;
}

.user-name {
  display: block;
  font-size: 22px;
  line-height: 26px;
  font-family: "apercu-b";
}

.user-email {
  display: block;
  font-size: 16px;
  line-height: 24px;
}

.user-phone {
  display: block;
  font-size: 16px;
  line-height: 24px;
}

.user-summery-card {
  margin-right: 14px;
  height: 123px;
  width: 170px;
  padding: 11px 20px 20px 20px;
}

.user-summery-card:last-child {
  margin-right: 0;
}

.user-summery-card-heading span {
  color: #6C6865;
  font-size: 14px;
  line-height: 16px;
}

.user-summery-card-content {
  font-size: 20px;
  line-height: 16px;
  font-family: "apercu-b";
  line-height: 22px;
  height: 55px;
  display: flex;
  align-items: center;
}

.user-summery-card-link a {
  font-size: 12px;
  letter-spacing: 1px;
  line-height: 18px;
  font-family: "apercu-b";
  text-transform: uppercase;
}

.user-summery-right {
  display: flex;
}

.cashback-text-width {
  max-width: 111px;
}

.user-summery-card-heading span,
.user-summery-card-heading img {
  display: inline-block;
  vertical-align: middle;
}

.user-summery-card-heading span {
  margin-left: 3px;
}

.gallop-cash-div .user-summery-card-content {
  font-size: 30px;
}

.payment {
  padding-left: 0px;
  margin-top: 24px;
  float: left;
  width: 100%;
  margin-bottom: 16px;
}

.payment-inner {
  border-bottom: 1px solid #E6E6E6;
  position: relative;
  float: left;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 14px;
}

.payment h4 {
  font-size: 16px;
  line-height: 26px;
  font-family: "apercu-b";
}

.payment h4 span {
  display: inline-block;
  vertical-align: middle;
}

.payment h4 span.prices {
  color: #FFFFFF;
  font-size: 18px;
  letter-spacing: -0.98px;
  line-height: 22px;
  background-color: #9C4AF6;
  border-radius: 2px;
  height: 27px;
  padding: 0 8px;
  margin-left: 9px;
}

.new-price {
  padding-left: 6px;
}

.promo-saving {
  font-size: 12px;
  letter-spacing: 0.4px;
  line-height: 18px;
  font-family: "apercu-r";
  margin-left: 8px;
}

.promo {
  font-size: 12px;
  letter-spacing: 0.4px;
  line-height: 12px;
}

.promo-box {
  border: 2px solid #E5E5E5;
  z-index: 10;
  position: absolute;
  right: 0;
  top: 0;
  background-color: #FFFFFF;
  box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.14);
  height: 111px;
  width: 282px;
  padding: 16px;
  display: none;
}

.remove {
  position: absolute;
  right: 8px;
  top: 0;
  height: 8px;
  width: 8px;
  cursor: pointer;
}

.promo-box-input {
  float: left;
  width: 100%;
  position: relative;
}

.promo-box-input .input-label {
  font-size: 14px;
  line-height: 23px;
  margin-bottom: 4px;
}

.promo-box-input .link-font {
  position: absolute;
  right: 10px;
  top: 41px;
}

.features {
  width: 408px;
  border: 1px solid #D6EEF4;
  border-radius: 6px;
  background-color: #F5FDFF;
  padding: 45px 70px;
  margin-top: -59px;
}

.features-list-item {
  font-size: 16px;
  line-height: 22px;
  position: relative;
  margin-bottom: 14px;
  padding-left: 25px;
}

.features-list-item:before {
  font-family: 'FontAwesome';
  content: "\f00c";
  left: 0;
  color: var(--dark-bg-color);
  font-size: 12px;
  position: absolute;
}

.paymentform-div {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.paymentform-div .paymentform-div-left {
  max-width: 660px;
}

.exp-text {
  display: block;
}

.exp-img {
  display: none;
}

.card-div.active .exp-img {
  display: block;
}

.card-div.active .exp-text {
  display: none;
}

.addSecondaryEmail {
  margin-top: 11px;
}

.addSecondaryEmail a {
  margin-bottom: 16px;
  display: inline-block;
}

.closeSubscriptionDiv {
  float: left;
  width: 100%;
  margin-top: 13px;
  margin-bottom: 26px;
}

.add-traveller-blank {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 489px;
  flex-direction: column;
}

.add-traveller-blank-img {
  height: 32px;
  width: 32px;
  margin-bottom: 4px;
}

.add-traveller-blank-text {
  font-size: 18px;
  line-height: 23px;
}

.heading-link {
  float: left;
  font-size: 12px;
  letter-spacing: 1px;
  line-height: 18px;
  font-family: "apercu-b";
  margin-top: 14px;
  margin-bottom: 5px;
  text-transform: uppercase;
}

.heading-link img {
  width: 20px;
}

.card-header-link {
  display: inline-block;
  margin: 18px 0;
}

.accord {
  float: left;
  width: 100%;
  margin-bottom: 10px;
}

.accord-header {
  float: left;
  width: 100%;
}

.accord-content {
  float: left;
  width: 100%;
  display: none;
}

.accord-buttton {
  float: left;
  width: 100%;
}

.loader-div {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.mdl-spinner--single-color .mdl-spinner__layer-1 {
  border-color: var(--dark-bg-color);
}

.mdl-spinner--single-color .mdl-spinner__layer-2 {
  border-color: var(--dark-bg-color);
}

.mdl-spinner--single-color .mdl-spinner__layer-3 {
  border-color: var(--dark-bg-color);
}

.mdl-spinner--single-color .mdl-spinner__layer-4 {
  border-color: var(--dark-bg-color);
}

.mdl-spinner {
  width: 50px;
  height: 50px;
}

form {
  float: left;
  width: 100%;
  position: relative;
}

.basic-economy-warning {
  background-color: #FFEED0;
  margin-top: 6px;
  margin-bottom: 14px;
  width: 100%;
}

.basic-economy-warning-inner {
  min-height: 50px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 10px 40px;
}

.passwordCheck {
  position: absolute;
  right: 14px;
  top: 12px;
  display: none;
}

.flexShortInfoTemplate {
  display: flex;
  align-items: center;
  line-height: 20px !important;
}

.flexShortInfoTemplate img {
  width: 17px;
}

.flexShortInfoTemplate span {
  width: calc(100% - 17px);
}

.promo img {
  margin-right: 5px;
}

.old-price.strike {
  font-family: "apercu-r";
  text-decoration: line-through;
}

.paymentMethodAddition img {
  width: 20px;
}

.removeFlyerNumberDiv {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  margin-left: 8px;
}

.hide {
  display: none;
}

.help-accord {
  border-bottom: 1px solid #E6E6E6;
  padding: 7px 0 0 0;
}

.help-accord:last-child {
  border-bottom: none;
}

.help-accord-header {
  position: relative;
  padding-bottom: 15px;
}

.help-accord-header::after {
  content: "\f107";
  font-family: 'FontAwesome';
  position: absolute;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 20px;
  top: 0;
  color: var(--button-bg-color);
  margin-top: -6px;
}

/*.basicEconomyRadioButtons{ display: none;}*/
.side-tab {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.side-tab-list {
  width: 270px;
  padding-right: 70px;
}

.side-tab-content-list {
  width: calc(100% - 270px);
  float: left;
}

.side-tab-content {
  float: left;
  width: 100%;
  margin-bottom: 26px;
  display: none;
}

.side-tab-content.active {
  display: block;
}

.side-tab-content-header {
  float: left;
  width: 100%;
  border-bottom: 1px solid #E6E6E6;
  padding-bottom: 16px;
}

.side-tab-content-header h3 {
  font-size: 18px;
  font-family: var(--globalFontfamilyr);font-weight: bold;;
}

.side-tab-list-item {
  font-size: 12px;
  position: relative;
  letter-spacing: 1px;
  line-height: 18px;
  color: #413E3B;
  text-transform: uppercase;
  margin-bottom: 13px;
  cursor: pointer;
}

.side-tab-list-item.active {
  font-family: var(--globalFontfamilyr);font-weight: bold;;
  color: var(--button-font-color);
}

.side-tab-content-block h4 {
  font-size: 14px;
  font-family: var(--globalFontfamilyr);font-weight: bold;;
  line-height: 22px;
}

.side-tab-content-block p {
  font-size: 14px;
  line-height: 22px;
  color: #6C6865;
  font-family: "apercu-r";
}

.side-tab-content-block {
  float: left;
  width: 100%;
  margin-bottom: 20px;
}

.side-tab-content-block h4 {
  float: left;
  width: 100%;
  padding-top: 10px;
  padding-bottom: 4px;
}

.side-tab-content-block p {
  float: left;
  width: 100%;
}

.side-tab-list-item span {
  position: relative;
  display: inline-block;
}

.side-tab-list-item.active span::after {
  font-family: 'FontAwesome';
  content: "\f105";
  right: -15px;
  color: var(--button-bg-color);
  font-size: 16px;
  position: absolute;
}

.flyer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  letter-spacing: -0.53px;
  line-height: 12px;
  border-bottom: 1px solid #E6E6E6;
  padding: 14px 0;
  margin-bottom: 9px;
}

.flyer-remove {
  cursor: pointer;
  display: inline-block;
  height: 10px;
  width: 10px;
}

.flyer-airline {
  display: inline-block;
  font-family: "apercu-mono";
}

.card-box {
  float: left;
  width: 100%;
  margin-top: -20px;
  margin-bottom: 28px;
}

.card-box-inner {
  float: left;
  width: 100%;
  position: relative;
  border: 1px solid #EBEBEB;
  padding: 15px 118px 24px 32px;
  border-radius: 6px;
  background-color: #FFFFFF;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.05);
}

.card-box-inner .remove {
  top: 8px;
  right: 16px;
}

.cc-button-container {
  float: left;
  width: 100%;
}

.card-box-heading {
  float: left;
  width: 100%;
  margin-bottom: 10px;
}

.card-box-content {
  float: left;
  width: 100%;
}

.card-box-heading h4 {
  color: #6C6865;
  font-size: 16px;
  line-height: 26px;
}

.creditCardBox {
  display: none;
}

.subscription-details {
  float: left;
  width: 100%;
}

.subscription-details ul {
  display: flex;
  justify-content: flex-start;
}

.subscription-details ul li {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 14px;
  line-height: 21px;
}

.subscription-details ul li:after {
  position: absolute;
  content: '';
  height: 50px;
  width: 1px;
  right: 30px;
  top: 0;
  background: #e6e6e6;
}

.subscription-details ul li:last-child:after {
  display: none;
}

.subscription-details ul li label {
  float: left;
  width: 100%;
  color: #6C6865;
  font-family: "apercu-r";
}

.subscription-details ul li span {
  float: left;
  width: 100%;
  font-family: "apercu-b";
}

.subscription-details ul li span img {
  width: 25px;
  margin-right: 7px;
}

.cancelSubscriptionButton {
  margin-top: 30px;
}

/*.subscription-details{display: none;}*/
.paymentMethodChange {
  padding-bottom: 22px;
}

.paymentMethodSection {
  margin-bottom: 20px;
}

.paymentMethodSection .mdl-radio__label img {
  margin-right: 5px;
}

.paymentMethodButton {
  margin-top: 10px;
}

.paymentMethodHeading {
  float: left;
  width: 100%;
  margin-bottom: 22px;
}

.paymentMethodHeading h4 {
  font-size: 14px;
  line-height: 21px;
  color: #6C6865;
}

.addSecondaryEmailLink {
  margin-bottom: 15px;
}

.shortInfoSeperator {
  padding: 0 9px;
}

.creditCardBoxPaymentMethods {
  max-width: 517px;
  margin-top: 0;
  display: block;
}

.creditCardBox.creditCardBoxPaymentMethods .card-box-inner {
  border: none;
  padding: 0;
  box-shadow: none;
}

.section-loader {
  width: 100%;
  min-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.section-loader .spinner {
  margin: 0;
}

/*# sourceMappingURL=style.css.map */