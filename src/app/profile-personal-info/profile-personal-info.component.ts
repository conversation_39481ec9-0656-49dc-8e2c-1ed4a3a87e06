import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, UntypedFormControl, Validators, ValidatorFn, AbstractControl, ValidationErrors } from '@angular/forms';
import { Constants } from '../util/constants';
import { DateUtils } from '../util/date-utils';
import { Subscription } from 'rxjs';
import { UserAccountService } from '../user-account.service';
import { UserAccountInfo } from '../entity/user-account-info';
import { DatePipe } from '@angular/common';
import { UserInfo } from '../entity/user-info';
import { PassportDTO } from '../entity/passport-dto';
import { ToastrService, ActiveToast } from 'ngx-toastr';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { PhoneNumberDTO } from '../entity/phonenumber-dto';
import { AddressDTO } from '../entity/address-dto';
import { _ } from 'src/app/util/title';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtils } from '../util/common-utils';
import { countries } from '../util/countries';
import { SearchService } from '../search.service';
import { environment } from 'src/environments/environment';
import { DeviceDetailsService } from '../device-details.service';
import { Title } from '@angular/platform-browser';
import { LANGUAGE_BY_LOCALE } from '../util/locale';

declare var setSelectedHomeAirport: any;
declare var selectedHomeAirport: any;

@Component({
    selector: 'app-profile-personal-info',
    templateUrl: './profile-personal-info.component.html',
    styleUrls: ['./profile-personal-info.component.scss'],
    standalone: false
})
export class ProfilePersonalInfoComponent implements OnInit {

  public passengerForm: UntypedFormGroup;
  public ktnForm: UntypedFormGroup;
  public passportInfoForm: UntypedFormGroup;
  public emergencyContact: UntypedFormGroup;
  countriesName = countries;
  isMobile: boolean;
  njoySpecificBuild: boolean;

  maxDoBDate: Date = new Date();
  minPassportDate: Date = new Date();
  countries = countries;
  languages_locale = LANGUAGE_BY_LOCALE;
  titleOptions = [
    { 'label': 'Select', 'code': '' },
    { 'code': 'MR', 'label': 'Mr' },
    { 'code': 'MRS', 'label': 'Mrs' },
    { 'code': 'MISS', 'label': 'Miss' },
    { 'code': 'MS', 'label': 'Ms' },
    { 'code': 'MASTER', 'label': 'Master' },
    { 'code': 'LORD', 'label': 'Lord' },
    { 'code': 'LADY', 'label': 'Lady' },
    { 'code': 'SIR', 'label': 'Sir' },
    { 'code': 'INF', 'label': 'Inf' }
  ];
  dialCode = "+1";
  activeToast: ActiveToast<any>;
  fetchAccountInfoSubscription: Subscription;
  travellerPreFillData: {};
  isRequestInProgress: boolean;
  constructor(private fb: UntypedFormBuilder,
    private titleService: Title,
    private userAccountInfoService: UserAccountService,
    private toastr: ToastrService,
    public translateService: TranslateService,
    private searchService: SearchService,
    private deviceDetailsService: DeviceDetailsService,
    private datePipe: DatePipe) {
  }
countryCode = 'US';
validity(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: boolean } | null => {
    let dateValid = this.isValidDateForForm(control.value);
   
      if (!dateValid) {
        return { 'dateValidity':  true  };
      }else{
      return null;
      }
  }

}
isValidDateForForm(dateString: string): boolean {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
}
  private createForm(passengerData: any) {
    this.passengerForm = this.fb.group({
      title: [passengerData && passengerData['title'] ? passengerData['title'] : null],
      firstName: [passengerData && passengerData['firstName'] ? passengerData['firstName'] : "", [Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)]],
      middleName: [passengerData && passengerData['middleName'] ? passengerData['middleName'] : "", [Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)]],
      lastName: [passengerData && passengerData['lastName'] ? passengerData['lastName'] : "", [Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)]],
      gender: [passengerData && passengerData['gender'] ? passengerData['gender'] : "", Validators.compose([Validators.required])],
      dateOfBirth: [passengerData && passengerData['dateOfBirth'] ? passengerData['dateOfBirth'] : null, Validators.compose([Validators.required,this.validity()])],
      // email: [passengerData && passengerData['email'] ? passengerData['email']:null, this.conditionalValidators(true, [Validators.required, Validators.pattern(Constants.RGEX_EMAIL)])],
      phoneNumber: [passengerData && passengerData['phoneNumber'] ? passengerData['phoneNumber'] : null, this.conditionalValidators(true, [Validators.required,CommonUtils.PhoneNumberValidator(this.findCountryCode(passengerData,'dialCode'))])],
      // phoneNumber: [null, this.conditionalValidators((i == 0), [Validators.required, Validators.pattern(Constants.RGEX_ONLY_DIGITS)])],
      homeAddress: [passengerData && passengerData['address'] ? passengerData['address'].trim() : null],
      workAddress: [passengerData && passengerData['address'] ? passengerData['address'] : null],
      locale:[passengerData && passengerData['locale'] ? passengerData['locale'] :navigator.language],
      // zipCode: new FormControl(passengerData && passengerData['zipCode'] ? passengerData['zipCode']:null, {
      //   validators: this.conditionalValidators((i == 0) && this.isZipCodeRequired, [Validators.required]),
      //   // asyncValidators:this.conditionalAsyncValidators((i==0),[this.zipCodeValidator.bind(this)]),
      //   updateOn: 'blur'
      // }),
      dialCode: [passengerData && passengerData['dialCode'] ? passengerData['dialCode'] : "+1", Validators.compose([Validators.required])],
    });
    this.emergencyContact = this.fb.group({
      name: [''],
      relationship: [''],
      contactNumber: [""],
      dialCode1: [this.setDialCode(this.userAccountInfoService.getAccountInfo()), Validators.compose([Validators.required])],
    })
    let maxNumber = environment.maxKTNFieldLength;
    this.ktnForm = this.fb.group({
      knownTravellerNumber: [passengerData && passengerData['knownTravellerNumber'] ? passengerData['knownTravellerNumber'] : null, this.conditionalValidators(true, [Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC), Validators.minLength(8), Validators.maxLength(maxNumber)])],
    });
    // this.ktnForm.get('knownTravellerNumber').setValidators(this.conditionalValidators(true, [Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC), Validators.minLength(9), Validators.maxLength(9)]));
    this.passportInfoForm = this.fb.group({
      passportNumber: [passengerData && passengerData['passportNumber'] ? passengerData['passportNumber'] : null, this.conditionalValidators(true, [Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)])],
      passportCountry: [passengerData && passengerData['passportCountry'] ? passengerData['passportCountry'] : null, this.conditionalValidators(true, [Validators.required])],
      passportNationality: [passengerData && passengerData['passportNationality'] ? passengerData['passportNationality'] : null, this.conditionalValidators(true, [Validators.required])],
      passportExpiryDate: [passengerData && passengerData['passportExpiryDate'] ? passengerData['passportExpiryDate'] : null, this.conditionalValidators(true, [Validators.required])],
    });
  }
  findCountryCodeFromDialCode(dialCodeNum){
    const findIndex = this.countries.findIndex(item1 => (item1.dial_code===dialCodeNum));
    if(findIndex > -1){
     return  this.countries[findIndex].code;
    }
    let countryCode= navigator.language.split('-');
    let country = countries.filter(item => item.code===countryCode[countryCode.length-1]);
    if(country && country[0]){
      return country[0].code;
    }
  return null;
  }
  onLocaleChange(){

  }
  getMaxKtnLimit(){
    return environment.maxKTNFieldLength;
  }
  findCountryCode(data,item){
    if(data && data[item]){
      let findIndex = this.countries.findIndex(item1 => (item1.dial_code===data[item].value || item1.dial_code===data[item]));
      if(findIndex > -1){
       return  this.countries[findIndex].code;
      }
    }
    let countryCode= navigator.language.split('-');
    let country = countries.filter(item => item.code===countryCode[countryCode.length-1]);
    if(country && country[0]){
      return country[0].code;
    }
  return null;
  }
  sortCountriesByName(data) {
    data.sort(function (a, b) {
      if (a.nationality.toLowerCase() < b.nationality.toLowerCase()) { return -1; }
      if (a.nationality.toLowerCase() > b.nationality.toLowerCase()) { return 1; }
      return 0;
    })
    return data;
  }
  sortCountries(data) {
    data.sort(function (a, b) {
      if (a.name.toLowerCase() < b.name.toLowerCase()) { return -1; }
      if (a.name.toLowerCase() > b.name.toLowerCase()) { return 1; }
      return 0;
    })
    return data;
  }

  ngOnInit() {
    

    this.njoySpecificBuild = this.userAccountInfoService.isItNjoyBuild();
    this.titleService.setTitle('Profile');
    if(this.countries && this.countries.length>0){
      this.countries =JSON.parse(JSON.stringify(this.sortCountriesByName(this.countries)));
      }
      if(this.countriesName && this.countriesName.length>0){
        this.countriesName =JSON.parse(JSON.stringify(this.sortCountries(this.countriesName)));
        }
    this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });

    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      this.fillTravelerProfileData();
    });
    this.createForm(this.travellerPreFillData);
    if (this.userAccountInfoService.isLoggedIn()) {
      this.fillTravelerProfileData();
    }
    // this.changeDetection();
  }


  private fillTravelerProfileData() {
    let userAccountInfoObj: any = this.userAccountInfoService.getAccountInfo();
    if (!userAccountInfoObj || !this.passengerForm) return;
    // let storagePassangerData = this.gallopLocalStorage.getItem('passengersFormData');
    // if(storagePassangerData && storagePassangerData!=null){
    //   this.travellerPreFillData = JSON.parse(storagePassangerData);
    // }else{
    this.travellerPreFillData = this.getTravellerInfo(userAccountInfoObj);
   
    // this.createForm(this.travellerPreFillData);
    this.passengerForm.patchValue(this.travellerPreFillData);
    if(userAccountInfoObj && userAccountInfoObj.userInfo.phoneNumber&&  userAccountInfoObj.userInfo.phoneNumber.countryCode){
    this.passengerForm.controls['phoneNumber'].setValidators(this.conditionalValidators(true,[Validators.required,CommonUtils.PhoneNumberValidator(this.findCountryCode(this.passengerForm.controls,'dialCode'))]));
   
    }
    this.ktnForm.patchValue(this.getKTNFormData(userAccountInfoObj));
    this.emergencyContact.patchValue(this.getEmergencyFormData(userAccountInfoObj));
    this.passportInfoForm.patchValue(this.getPassportFormData(userAccountInfoObj));
    let emergencyContactDIalCode = 'US';
    if (this.emergencyContact.controls['dialCode1'].value) {
      emergencyContactDIalCode = this.findCountryCodeFromDialCode(this.emergencyContact.controls['dialCode1'].value);
    }
    if ((this.emergencyContact.controls['name'].value !== '' && this.emergencyContact.controls['relationship'].value !== '') && (this.emergencyContact.controls['contactNumber'].value !== '' && this.emergencyContact.controls['dialCode1'].value !== null)) {
      this.emergencyContact.controls['contactNumber'].setValidators([Validators.required,this.conditionalValidators(true,[CommonUtils.PhoneNumberValidator(emergencyContactDIalCode)])]);
      this.emergencyContact.controls['name'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      this.emergencyContact.controls['relationship'].setValidators([Validators.required, this.conditionalValidators(true, [Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)])]);
      this.emergencyContact.controls['dialCode1'].setValidators([Validators.required]);
      this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
      this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
      this.emergencyContact.controls['relationship'].updateValueAndValidity();
      this.emergencyContact.controls['name'].updateValueAndValidity();
      this.emergencyContact.controls['name'].markAsTouched();
      this.emergencyContact.controls['relationship'].markAsTouched();
      this.emergencyContact.controls['dialCode1'].markAsTouched();

    }
    // }
  }
  preventEntry(event) {
  }

  submitPersonalInfo() {

    //    this.validateAllFormFields(this.passengerForm);
    if (this.passengerForm.valid) {
      this.saveAndUpdateProfileInfo(this.getUpdatedPersonalInfoData(), 'savePersonalInfoBtn');
    } else {
      // this.passengerForm.markAsTouched();
      this.passengerForm.controls['gender'].markAsTouched();
      this.passengerForm.controls['title'].markAsTouched();
      this.passengerForm.controls['dateOfBirth'].markAsTouched();
      this.passengerForm.controls['phoneNumber'].markAsTouched();
    }
  }

  submitKTNInfo() {

    //    this.validateAllFormFields(this.ktnForm);
    if (this.ktnForm.valid) {
      this.saveAndUpdateProfileInfo(this.getUpdatedKTNData(), 'saveKTNInfoBtn');
    } else if (this.ktnForm.controls['knownTravellerNumber'].value === '') {
      this.saveAndUpdateProfileInfo(this.getUpdatedKTNData(), 'saveKTNInfoBtn');
    } else {
      this.ktnForm.controls['knownTravellerNumber'].markAsTouched()
    }
  }
  getName(text) {
    if (text !== '') {
      this.emergencyContact.controls['relationship'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      this.emergencyContact.controls['name'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      this.emergencyContact.controls['contactNumber'].setValidators([Validators.required,this.conditionalValidators(true, [CommonUtils.PhoneNumberValidator(this.findCountryCode(this.emergencyContact.controls,'dialCode1'))])]);
      //  this.emergencyContact.controls['dialCode1'].setValidators([Validators.required]);
      //  this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
      this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
      this.emergencyContact.controls['relationship'].updateValueAndValidity();
      this.emergencyContact.controls['name'].updateValueAndValidity();
      this.emergencyContact.controls['relationship'].markAsTouched();
      this.emergencyContact.controls['contactNumber'].markAsTouched();
      this.emergencyContact.controls['dialCode1'].markAsTouched();
    } else {
      if (this.emergencyContact.controls['relationship'].value === '' && this.emergencyContact.controls['contactNumber'].value === '') {
        //  this.emergencyContact.controls['dialCode1'].clearValidators();
        this.emergencyContact.controls['relationship'].clearValidators();
        this.emergencyContact.controls['name'].clearValidators();
        this.emergencyContact.controls['contactNumber'].clearValidators();
        // this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
        this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
        this.emergencyContact.controls['name'].updateValueAndValidity();
        this.emergencyContact.controls['relationship'].updateValueAndValidity();
        if (this.emergencyContact.controls['dialCode1'].invalid) {
          this.emergencyContact.controls['dialCode1'].clearValidators();
          this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
        }
      }
    }
  }
  minLengthArray(min: number) {
    return (c: AbstractControl): { [key: string]: any } => {
      if (c.value) {
        let stringVal = c.value.replace(/\s/g, "")
        if (stringVal.length > 0)
          return null;

        return { 'minLengthArray': { valid: false } };
      }
    }
  }
  getRelationship(text) {
    if (text !== '') {
      this.emergencyContact.controls['relationship'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      this.emergencyContact.controls['name'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      this.emergencyContact.controls['contactNumber'].setValidators([Validators.required, this.conditionalValidators(true, [CommonUtils.PhoneNumberValidator(this.findCountryCode(this.emergencyContact.controls,'dialCode1'))])]);
      //  this.emergencyContact.controls['dialCode1'].setValidators([Validators.required]);
      this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
      // this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
      this.emergencyContact.controls['name'].updateValueAndValidity();
      this.emergencyContact.controls['relationship'].updateValueAndValidity();
      this.emergencyContact.controls['name'].markAsTouched();
      this.emergencyContact.controls['contactNumber'].markAsTouched();
      this.emergencyContact.controls['dialCode1'].markAsTouched();
    } else {
      if (this.emergencyContact.controls['name'].value === '' && this.emergencyContact.controls['contactNumber'].value === '') {
        //  this.emergencyContact.controls['dialCode1'].clearValidators();
        this.emergencyContact.controls['name'].clearValidators();
        this.emergencyContact.controls['relationship'].clearValidators();
        this.emergencyContact.controls['contactNumber'].clearValidators();
        this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
        //  this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
        this.emergencyContact.controls['name'].updateValueAndValidity();
        this.emergencyContact.controls['relationship'].updateValueAndValidity();
        if (this.emergencyContact.controls['dialCode1'].invalid) {
          this.emergencyContact.controls['dialCode1'].clearValidators();
          this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
        }
      }
    }
  }
  getphoneNumber(text) {
    if (text !== '') {
      this.emergencyContact.controls['contactNumber'].setValidators([Validators.required, this.conditionalValidators(true, [CommonUtils.PhoneNumberValidator(this.findCountryCode(this.emergencyContact.controls,'dialCode1'))])]);
      this.emergencyContact.controls['name'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      this.emergencyContact.controls['relationship'].setValidators([Validators.required, this.conditionalValidators(true, [Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)])]);
      // this.emergencyContact.controls['dialCode1'].setValidators([Validators.required]);
      // this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
      this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
      this.emergencyContact.controls['relationship'].updateValueAndValidity();
      this.emergencyContact.controls['name'].updateValueAndValidity();
      this.emergencyContact.controls['name'].markAsTouched();
      this.emergencyContact.controls['relationship'].markAsTouched();
      this.emergencyContact.controls['dialCode1'].markAsTouched();
    } else {
      if (this.emergencyContact.controls['relationship'].value === '' && this.emergencyContact.controls['name'].value === '') {
        // this.emergencyContact.controls['dialCode1'].clearValidators();
        this.emergencyContact.controls['name'].clearValidators();
        this.emergencyContact.controls['contactNumber'].clearValidators();
        this.emergencyContact.controls['relationship'].clearValidators();
        //  this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
        this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
        this.emergencyContact.controls['relationship'].updateValueAndValidity();
        this.emergencyContact.controls['name'].updateValueAndValidity();
        if (this.emergencyContact.controls['dialCode1'].invalid) {
          this.emergencyContact.controls['dialCode1'].clearValidators();
          this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
        }
      }
    }
  }
  submitPassportInfo() {

    //    this.validateAllFormFields(this.passportInfoForm);
    //    if (this.passportInfoForm.valid) {
    this.saveAndUpdateProfileInfo(this.getUpdatedPassportData(), 'savePassportInfoBtn');
    //    }
  }
  public findInvalidControls() {
    const invalid = [];
    const controls = this.emergencyContact.controls;
    for (const name in controls) {
      if (controls[name].invalid) {
        invalid.push(name);
      }
    }
    return invalid;
  }
  submitEmergencyInfo() {
    //
    //    this.validateAllFormFields(this.passportInfoForm);
    if (this.emergencyContact.controls['name'].value !== '' || this.emergencyContact.controls['relationship'].value !== '' || this.emergencyContact.controls['contactNumber'].value !== '') {
      if (this.emergencyContact.invalid) {
        this.emergencyContact.controls['name'].markAsTouched();
        this.emergencyContact.controls['relationship'].markAsTouched();
        this.emergencyContact.controls['contactNumber'].markAsTouched();
        this.emergencyContact.controls['dialCode1'].markAsTouched();
        return;
      }
    } else {
      this.emergencyContact.controls['name'].clearValidators();
      this.emergencyContact.controls['relationship'].clearValidators();
      this.emergencyContact.controls['contactNumber'].clearValidators();
      // this.emergencyContact.controls['dialCode1'].clearValidators();
      this.emergencyContact.controls['name'].updateValueAndValidity();
      this.emergencyContact.controls['relationship'].updateValueAndValidity();
      this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
      //this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
    }
    //  if (this.emergencyContact.valid) {
    this.saveAndUpdateProfileInfo(this.getUpdatedEmergencyData(), 'saveEmergencyContactBtn');
    // }else{
    // this.emergencyContact.markAsDirty();
    //}
  }

  validateAllFormFields(formGroup: UntypedFormGroup) {         //{1}
    Object.keys(formGroup.controls).forEach(field => {  //{2}
      const control = formGroup.get(field);             //{3}
      if (control instanceof UntypedFormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof UntypedFormGroup) {        //{5}
        this.validateAllFormFields(control);            //{6}
      }
    });
  }
  isDOBCalendarReadOnly() {
    if (this.njoySpecificBuild || this.isMobile) {
      return true;
    }
    return false;
  }

  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
  }

  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    const dayHoverHandler = event.dayHoverHandler;
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        this.searchService.hoverCounter++;
        if (this.searchService.hoverCounter > 1) {
          (picker as any)._datepickerRef.instance.daySelectHandler(cell);
        }
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }
  setGender(value: string) {
    this.passengerForm.get('gender').setValue(value);
  }

  isSelectedGender(value: string): boolean {
    let val = this.passengerForm.get('gender').value;
    return (val == value);
  }

  getGenderName(val) {
    let nameArray = {
      M:  this.translateService.instant('personal.Male'),
      F: this.translateService.instant('personal."Female'),
      XL:this.translateService.instant('personal.Unspecified')
    }

    return nameArray[val];
  }
  private conditionalValidators(condition, validators: ValidatorFn[]) {
    if (condition) {
      return Validators.compose(validators);
    }
    return null;
  }

  minAgeValidator(control: AbstractControl): ValidationErrors | null {
    let result = null;

    if (control.value && control.valid) {
      let yearsDiff = DateUtils.getYearsDiffFromNow(control.value);
      if (yearsDiff < Constants.MIN_DOB_AGE) {
        result = { 'lessThanMinAge': true };
      }
    }
    return result;
  }

  getFormControl(name: string) {
    return this.passengerForm.controls[name];
  }

  private getKTNFormData(userAccountInfoObj: UserAccountInfo) {
    if (userAccountInfoObj && userAccountInfoObj.userInfo) {
      let ktnData = {
        "knownTravellerNumber": userAccountInfoObj.userInfo.known_traveller_number ? userAccountInfoObj.userInfo.known_traveller_number.trim() : "",
      }
      return ktnData;
    }
    return {};
  }

  private getPassportFormData(userAccountInfoObj: UserAccountInfo) {
    if (userAccountInfoObj && userAccountInfoObj.userInfo) {
      let passportFormData = {
        "passportNumber": userAccountInfoObj.userInfo.passportDTO ? userAccountInfoObj.userInfo.passportDTO.passportNumber : "",
        "passportCountry": userAccountInfoObj.userInfo.passportDTO ? userAccountInfoObj.userInfo.passportDTO.countryCode : null,
        "passportNationality": userAccountInfoObj.userInfo.nationality ? userAccountInfoObj.userInfo.nationality : null,
        "passportExpiryDate": userAccountInfoObj.userInfo.passportDTO && CommonUtils.isValidDOBDate(userAccountInfoObj.userInfo.passportDTO.expiryDate)
          ? userAccountInfoObj.userInfo.passportDTO.expiryDate : null,
      }
      return passportFormData;
    }
    return {};
  }
  private getEmergencyFormData(userAccountInfoObj: UserAccountInfo) {
    if (userAccountInfoObj && (userAccountInfoObj.userInfo && userAccountInfoObj.userInfo.emergencyContact)) {
      let emergencyFormData = {
        "name": userAccountInfoObj.userInfo.emergencyContact ? userAccountInfoObj.userInfo.emergencyContact.name : "",
        "relationship": userAccountInfoObj.userInfo.emergencyContact ? userAccountInfoObj.userInfo.emergencyContact.relationship : "",
        "contactNumber": userAccountInfoObj.userInfo.emergencyContact.contactNumber ? userAccountInfoObj.userInfo.emergencyContact.contactNumber.number : "",
        "dialCode1": userAccountInfoObj.userInfo.emergencyContact.contactNumber &&
          userAccountInfoObj.userInfo.emergencyContact.contactNumber.countryCode ? userAccountInfoObj.userInfo.emergencyContact.contactNumber.countryCode : "+1",
      }
      return emergencyFormData;
    }
    return {};
  }

  getCountryJSON(c: string) {
    return CommonUtils.getCountryJSON(c);
  }

  handleAddressChange(fieldName, address) {
    this.passengerForm.controls[fieldName].setValue(address.formatted_address);
    setSelectedHomeAirport(selectedHomeAirport[0], address.formatted_address);
  }
  private getTravellerInfo(userAccountInfoObj: UserAccountInfo) {
    if (userAccountInfoObj && userAccountInfoObj.userInfo) {
      let travellerData = {

        "title": userAccountInfoObj.userInfo.title ? userAccountInfoObj.userInfo.title.trim() : "",
        "firstName": userAccountInfoObj.userInfo.firstname ? userAccountInfoObj.userInfo.firstname.trim() : "",
        "middleName": userAccountInfoObj.userInfo.middlename ? userAccountInfoObj.userInfo.middlename.trim() : "",
        "lastName": userAccountInfoObj.userInfo.lastname ? userAccountInfoObj.userInfo.lastname.trim() : "",
        "gender": userAccountInfoObj.userInfo.gender ? userAccountInfoObj.userInfo.gender.trim() : '',
        "dateOfBirth": userAccountInfoObj.userInfo.dob && CommonUtils.isValidDOBDate(userAccountInfoObj.userInfo.dob) ? userAccountInfoObj.userInfo.dob : null,
        "phoneNumber": userAccountInfoObj.userInfo.phoneNumber && userAccountInfoObj.userInfo.phoneNumber.number ? userAccountInfoObj.userInfo.phoneNumber.number.trim() : "",
        "homeAddress": userAccountInfoObj.userInfo.home && userAccountInfoObj.userInfo.home.address ? userAccountInfoObj.userInfo.home.address.trim() : "",
        "workAddress": userAccountInfoObj.userInfo.work && userAccountInfoObj.userInfo.work.address ? userAccountInfoObj.userInfo.work.address.trim() : "",
        "dialCode": userAccountInfoObj.userInfo.phoneNumber &&
          userAccountInfoObj.userInfo.phoneNumber.countryCode ? userAccountInfoObj.userInfo.phoneNumber.countryCode : this.setDialCode(userAccountInfoObj),
          "locale":userAccountInfoObj.userInfo.locale ? userAccountInfoObj.userInfo.locale:navigator.language,
      }
      return travellerData;

    }
    return {};
  }



setDialCode(userAccountInfoObj){
  if(userAccountInfoObj && userAccountInfoObj.userInfo && userAccountInfoObj.userInfo.locale){
    let countryCode= userAccountInfoObj.userInfo.locale.split('-');
    let country = countries.filter(item => item.code===countryCode[countryCode.length-1]);
    if(country && country[0]){
      return country[0].dial_code;
    }

  }else{
    let countryCode= navigator.language.split('-');
    let country = countries.filter(item => item.code===countryCode[countryCode.length-1]);
    if(country && country[0]){
      return country[0].dial_code;
    }
    return null;
  }
}
  private getUpdatedPersonalInfoData(): UserInfo {
    let p = this.passengerForm.value;
    let userInfoDTO = this.userAccountInfoService.getAccountInfo().userInfo;
    userInfoDTO = CommonUtils.getUpdateedPassengerInnfo(p, userInfoDTO);
    return userInfoDTO;
  }


  private getUpdatedKTNData(): UserInfo {
    let p = this.ktnForm.value;
    let userInfoDTO = this.userAccountInfoService.getAccountInfo().userInfo;
    userInfoDTO.known_traveller_number = p.knownTravellerNumber && p.knownTravellerNumber.trim().length > 0 ? p.knownTravellerNumber.trim() : "";
    return userInfoDTO;
  }

  private getUpdatedPassportData(): UserInfo {
    let p = this.passportInfoForm.value;
    let passportInfo: PassportDTO = this.userAccountInfoService.getAccountInfo().userInfo.passportDTO;
    passportInfo.passportNumber = p.passportNumber;
    passportInfo.expiryDate = this.datePipe.transform(new Date(p.passportExpiryDate), 'MM/dd/yyyy');;
    passportInfo.countryCode = p.passportCountry;
    this.userAccountInfoService.getAccountInfo().userInfo.nationality = p.passportNationality;
    // passportInfo.countryCode = p.passportNumber;
    // "passportNumber": userAccountInfoObj.userInfo.passportDTO ? userAccountInfoObj.userInfo.passportDTO.passportNumber : "",
    // "passportCountry": userAccountInfoObj.userInfo.passportDTO ? this.getCountryJSON(userAccountInfoObj.userInfo.passportDTO.countryCode) : null,
    // "passportNationality": userAccountInfoObj.userInfo.nationality ? this.getCountryJSON(userAccountInfoObj.userInfo.nationality) : null,
    // "passportExpiryDate": userAccountInfoObj.userInfo.passportDTO && this.isValidDate(userAccountInfoObj.userInfo.passportDTO.expiryDate)
    // ? userAccountInfoObj.userInfo.passportDTO.expiryDate : null,
    return this.userAccountInfoService.getAccountInfo().userInfo;
  }
  private getUpdatedEmergencyData(): UserInfo {
    let p = this.emergencyContact.value;
    let emergencyContact: any = { name: '', relationship: '', contactNumber: new PhoneNumberDTO() }
    emergencyContact = this.userAccountInfoService.getAccountInfo().userInfo.emergencyContact;
    if (emergencyContact) {
      emergencyContact.name = p.name;
      emergencyContact.relationship = p.relationship;
      if (p.contactNumber) {
        if (emergencyContact.contactNumber == "") {
          emergencyContact.contactNumber = new PhoneNumberDTO();
        }
        emergencyContact.contactNumber.countryCode = p.dialCode ? p.dialCode : this.setDialCode(this.userAccountInfoService.getAccountInfo());
        emergencyContact.contactNumber.number = p.contactNumber ? p.contactNumber.trim() : "";
      }
      emergencyContact.contactNumber.number = p.contactNumber;
      emergencyContact.contactNumber.countryCode = p.dialCode1;
    } else {
      emergencyContact = { name: '', relationship: '', contactNumber: new PhoneNumberDTO() }
      emergencyContact.name = p.name;
      emergencyContact.relationship = p.relationship;
      if (p.contactNumber) {
        if (emergencyContact.contactNumber == "") {
          emergencyContact.contactNumber = new PhoneNumberDTO();
        }
        emergencyContact.contactNumber.countryCode = p.dialCode1 ? p.dialCode1 : this.setDialCode(this.userAccountInfoService.getAccountInfo());
        emergencyContact.contactNumber.number = p.contactNumber ? p.contactNumber : "";
      }
      emergencyContact.contactNumber.number = p.contactNumber;
      emergencyContact.contactNumber.countryCode = p.dialCode1;
      this.userAccountInfoService.getAccountInfo().userInfo.emergencyContact = emergencyContact;
    }

    return this.userAccountInfoService.getAccountInfo().userInfo;
  }

  private saveAndUpdateProfileInfo(userInfoDTO: UserInfo, saveButtonId: string) {
    this.isRequestInProgress = true;
    this.showProgress();
    this.fetchAccountInfoSubscription = this.userAccountInfoService.saveAccountInfo(
      userInfoDTO).subscribe(res => {
        this.isRequestInProgress = false;
        if (res.success == true) {
          this.showSuccess();
          //   this.isChangePasswordMode = false;
          if (res.data) {
            let userAccountInfoObj: UserAccountInfo = deserialize(res.data, UserAccountInfo);
            this.userAccountInfoService.setAccountInfo(userAccountInfoObj);
          }

        } else if (res.status == 'error') {
          // this.router.navigate(['/errors/404']);
          this.showError();
        } else {
          // this.router.navigate(['/errors/404']);
          // window.alert('Error');
          this.showError();
        }
      }, error => {
        this.showError();
      });
  }

  showSuccess() {
    this.toastr.remove(this.activeToast.toastId);
    this.activeToast = this.toastr.success(this.translateService.instant('profilePage.Profilesavedsuccessfully').toString());
  }

  showProgress() {
    if (this.activeToast) {
      this.toastr.remove(this.activeToast.toastId);
    }
    this.activeToast = this.toastr.info(this.translateService.instant('profilePage.Pleasewait').toString(), this.translateService.instant('profilePage.Saving').toString());
  }

  showError() {
    if (this.activeToast) {
      this.toastr.remove(this.activeToast.toastId);
    }
    this.activeToast = this.toastr.success('Error!', 'Unknown error while saving profile');
  }

  getPassportShortInfo(num, date, country) {
    let templateArray = [];
    let template = "";
    if (num != "" && num != undefined && num != "undefined" && num != null && num != "null") {
      templateArray.push(this.translateService.instant("profilePersonal.Number") + num);
    }

    if (date != "" && date != undefined && date != "undefined" && date != null && date != "null") {
      templateArray.push(this.translateService.instant("profilePersonal.Exp") + date);
    }

    if (country != "" && country != undefined && country != "undefined" && country != null && country != "null") {
      templateArray.push(this.getContryName(country));
    }

    template = templateArray.join(" | ")
    return template;
  }
  selectedCountryCode = '';
  onCountrySelected(item) {
    if (item) {
      this.selectedCountryCode = item.code;
      this.passengerForm.controls['phoneNumber'].setValidators(this.conditionalValidators(true,[Validators.required,CommonUtils.PhoneNumberValidator(item.code)]));
      this.passengerForm.controls['phoneNumber'].markAsDirty();
      this.passengerForm.controls['phoneNumber'].updateValueAndValidity();
    } else {
      this.selectedCountryCode = '';
    }

  }
  selectedCountryCodeForEC = '';
  onCountrySelectedForEC(item) {
    if (item) {
      this.selectedCountryCodeForEC = item.code;
      this.emergencyContact.controls['contactNumber'].setValidators(this.conditionalValidators(true,[Validators.required,CommonUtils.PhoneNumberValidator(item.code)]));
      this.emergencyContact.controls['contactNumber'].markAsDirty();
      this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
    } else {
      this.selectedCountryCodeForEC = '';
      if (this.emergencyContact.controls['relationship'].value === '' && (this.emergencyContact.controls['contactNumber'].value === '' && this.emergencyContact.controls['name'].value === '')) {
        this.emergencyContact.controls['dialCode1'].clearValidators();
        this.emergencyContact.controls['relationship'].clearValidators();
        this.emergencyContact.controls['name'].clearValidators();
        this.emergencyContact.controls['contactNumber'].clearValidators();
        this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
        this.emergencyContact.controls['relationship'].updateValueAndValidity();
        this.emergencyContact.controls['name'].updateValueAndValidity();
        this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
      }
    }

  }


  getContryName(code) {
    return CommonUtils.getContryName(code);
  }
  searchByNameOrCode(term: string, item) {
    return CommonUtils.searchByNameOrCode(term, item);
  }
  getPhoneNumberMask(inputDIalCode) {

    if (this.emergencyContact.controls['contactNumber'].value !== '') {
      this.emergencyContact.controls['dialCode1'].setValidators([Validators.required]);
      this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
      return CommonUtils.getPhoneNumberMask(inputDIalCode);
    } else {
      if (this.emergencyContact.controls['relationship'].value === '' && this.emergencyContact.controls['name'].value === '') {
        //this.emergencyContact.controls['dialCode1'].clearValidators();
        this.emergencyContact.controls['relationship'].clearValidators();
        this.emergencyContact.controls['name'].clearValidators();
        this.emergencyContact.controls['contactNumber'].clearValidators();
        //  this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
        this.emergencyContact.controls['contactNumber'].updateValueAndValidity();
        this.emergencyContact.controls['name'].updateValueAndValidity();
        this.emergencyContact.controls['relationship'].updateValueAndValidity();
        if (this.emergencyContact.controls['dialCode1'].invalid) {
          this.emergencyContact.controls['dialCode1'].clearValidators();
          this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
        }
      }
    }
    return CommonUtils.getPhoneNumberMask(inputDIalCode);

  }
  getPhoneNumberPaceHolder(inputDIalCode) {
    return CommonUtils.getPhoneNumberPaceHolder(inputDIalCode);
  }
  getCountryCode(code, phoneNumber) {
    return CommonUtils.getCountryCode(code, phoneNumber, this.selectedCountryCode);
  }
  getCountryCode1(code, phoneNumber) {
    return CommonUtils.getCountryCode(code, phoneNumber, this.selectedCountryCodeForEC);
  }



}
