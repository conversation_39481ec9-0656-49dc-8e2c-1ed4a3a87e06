<div class="card-div active shadow" id="personalDetailsCard">
    <div class="card-div-inner">
        <form method="post" id="personalDetailsForm" class="personalDetailsForm">
            <div class="traveller-form">
                <div class="card-div-header" onclick="openCardEdit(this);">
                    <h3>{{'profilePersonal.PersonalInformation' | translate}}</h3>
                    <div class="card-edit">
                        <span class="edit-text">Edit</span>
                        <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                    </div>
                    <div class="traveller-short-info">
                        <div class="traveller-short-info-inner">
                            {{getGenderName(passengerForm.controls['gender'].value) | translate }} |
                            {{'profilePersonal.DOB' | translate}}-{{(passengerForm.controls['dateOfBirth'].value | date:'MMM d, yyyy')}}</div>
                    </div>
                </div>
                <div class="card-div-body" [formGroup]="passengerForm">
                    <div class="row">
                        <div *ngIf="false" class="col-lg-2 col-md-3 col-sm-4">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.Title' | translate}}</label>
                                <div class="select-box select-dropdown title-select-box">
                                    <!-- <select class="input-textfield title" id="title" name="title">
                                        <option>Mr.</option>
                                        <option>Mrs.</option>
                                    </select> -->
                                    <ng-select bindLabel="label" bindValue="code" class="input-textfield title"
                                        name="title" id="title-passenger" [searchable]="false" [clearable]="false"
                                        [items]="titleOptions" formControlName="title"></ng-select>
                                    <div *ngIf="getFormControl('title').hasError('required') && (getFormControl('title').touched || getFormControl('title').dirty)"
                                        id="title-error" class="error">{{'personal.Thisfieldisrequired' | translate}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="input-box input-box-multiple">
                                <label class="input-label">{{'profilePersonal.Name' | translate}}<span
                                        class="label-note">{{'profilePersonal.Usesameasinidincludingmiddlename' |
                                        translate}}</span></label>
                                <div class="multiple-inputs">
                                    <div class="row">
                                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                            <input formControlName="firstName" class="input-textfield firstName"
                                                id="firstName" name="firstName" type="text"
                                                placeholder="{{'profilePersonal.FirstName' | translate}}" />
                                            <!--                                            <span *ngIf="getFormControl('firstName').hasError('required') && (getFormControl('firstName').touched || getFormControl('firstName').dirty)" id="title-error" class="error">
                                                This field is required.</span>-->
                                            <span
                                                *ngIf="getFormControl('firstName').hasError('pattern') && (getFormControl('firstName').touched || getFormControl('firstName').dirty)"
                                                id="title-error" class="error">
                                                {{'profilePersonal.PleaseenteravalidfirstnameNumericorspecialcharactersarenotallowed'
                                                | translate}}</span>
                                        </div>
                                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                            <input formControlName="middleName" class="input-textfield middleName"
                                                id="middleName" name="middleName" type="text"
                                                placeholder="{{'profilePersonal.MiddleNameIfshownonid' | translate}}" />
                                            <span
                                                *ngIf="getFormControl('middleName').hasError('pattern') && (getFormControl('middleName').touched || getFormControl('middleName').dirty)"
                                                id="title-error" class="error">
                                                {{'profilePersonal.PleaseenteravalidmiddlenameNumericorspecialcharactersarenotallowed'
                                                | translate}}</span>
                                        </div>
                                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                            <input formControlName="lastName" class="input-textfield lastName"
                                                id="lastName" name="lastName" type="text"
                                                placeholder="{{'profilePersonal.LastName' | translate}}" />
                                            <span
                                                *ngIf="getFormControl('lastName').hasError('pattern') && (getFormControl('lastName').touched || getFormControl('lastName').dirty)"
                                                id="title-error" class="error">
                                                {{'profilePersonal.PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed'
                                                | translate}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-lg-2 custom-wd-sm">
                            <div class="input-box">
                                <label class="input-label">Suffix</label>
                                <div class="select-box title-select-box">
                                    <select class="input-textfield title" id="suffix" name="suffix">
                                        <option>1</option>
                                        <option>2</option>
                                    </select>
                                </div>
                            </div>
                        </div> -->
                        <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.DOB' | translate}}</label>
                                <input readonly="" style="cursor: pointer;"
                                    class="input-textfield datepicker shortInfoField" id="dobb" name="dobb" bsDatepicker
                                    #dobDatePicker="bsDatepicker" [outsideClick]="true"
                                    [bsConfig]="{showWeekNumbers: false}" [maxDate]="maxDoBDate"
                                    formControlName="dateOfBirth" (keydown)="preventEntry($event)"
                                    (onShown)="onShowPicker($event, dobDatePicker)"
                                    placeholder="{{'profilePersonal.MMDDYY' | translate}}"
                                    [readOnly]="isDOBCalendarReadOnly()" (onHidden)="onHidePicker()" />
                                <div *ngIf="getFormControl('dateOfBirth').hasError('lessThanMinAge') && (getFormControl('dateOfBirth').touched || getFormControl('dateOfBirth').dirty)"
                                    class="error">{{'profilePersonal.Ageshouldbeatleast12years' | translate}}
                                </div>
                                <div
                                *ngIf="getFormControl('dateOfBirth').hasError('dateValidity') && (getFormControl('dateOfBirth').touched || getFormControl('dateOfBirth').dirty)"
                                class="error">please enter a valid date
                              </div>
                                <div *ngIf="getFormControl('dateOfBirth').hasError('required') && (getFormControl('dateOfBirth').touched || getFormControl('dateOfBirth').dirty)"
                                    class="error">{{'personal.Thisfieldisrequired' | translate}}
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.Gender' | translate}}</label>
                                <div class="checkbox-container">
                                    <label
                                        class="mdl-radio selection-checkbox checkbox2 checkbox-seperate mdl-js-radio mdl-js-ripple-effect"
                                        [ngClass]="{'is-checked': isSelectedGender('M')}" for="male">
                                        <input style="display:none;" type="radio" id="male"
                                            class="mdl-radio__button shortInfoField" name="gender" value="M"
                                            formControlName="gender" (keyup.enter)="setGender('M')"
                                            (click)="setGender('M')">
                                        <span class="mdl-radio__label">{{'profilePersonal.Male' | translate}}</span>
                                    </label>
                                    <!--                                    <label  *ngIf="isSelectedGender('F')" class="mdl-radio selection-checkbox checkbox2 checkbox-seperate mdl-js-radio mdl-js-ripple-effect" for="male">
                                        <input style="display:none;" type="radio" id="male" class="mdl-radio__button shortInfoField" name="gender" value="M" formControlName="gender"
                                        (keyup.enter)="setGender('M')"  (click)="setGender('M')"
                                        >
                                        <span class="mdl-radio__label">Male</span>
                                    </label>-->
                                    <label
                                        class="mdl-radio selection-checkbox checkbox2 checkbox-seperate mdl-js-radio mdl-js-ripple-effect"
                                        [ngClass]="{'is-checked': isSelectedGender('F')}" for="female">
                                        <input style="display:none;" type="radio" id="female"
                                            class="mdl-radio__button shortInfoField" name="gender" value="F"
                                            formControlName="gender" (keyup.enter)="setGender('F')"
                                            (click)="setGender('F')">
                                        <span class="mdl-radio__label">{{'profilePersonal.Female' | translate}}</span>
                                    </label>
                                    <!--                                    <label *ngIf="isSelectedGender('M')" class="mdl-radio selection-checkbox checkbox2 checkbox-seperate mdl-js-radio mdl-js-ripple-effect" for="female">
                                        <input style="display:none;" type="radio" id="female" class="mdl-radio__button shortInfoField" name="gender" value="F" formControlName="gender"
                                        (keyup.enter)="setGender('F')" (click)="setGender('F')">
                                        <span class="mdl-radio__label">Female</span>
                                    </label>-->
                                    <label
                                        class="mdl-radio selection-checkbox checkbox2 checkbox-seperate mdl-js-radio mdl-js-ripple-effect"
                                        [ngClass]="{'is-checked': isSelectedGender('X')}" for="other">
                                        <input style="display:none;" type="radio" id="other"
                                            class="mdl-radio__button shortInfoField" name="gender" value="X"
                                            formControlName="gender" (keyup.enter)="setGender('X')"
                                            (click)="setGender('X')">
                                        <span class="mdl-radio__label">{{'personal.Unspecified' | translate}}</span>
                                    </label>
                                </div>
                                <div *ngIf="getFormControl('gender').hasError('required') && (getFormControl('gender').touched || getFormControl('gender').dirty)"
                                    class="error" style="width:30%;">{{'personal.Thisfieldisrequired' | translate}}
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.PhoneNumber' | translate}}</label>
                                <!-- <input class="input-textfield phoneNumber" type="tel" id="number" name="number" placeholder="0000000000" /> -->
                                <div class="select-box select-dropdown searchable-dropdown phone-select-box">

                                    <ng-select (change)="onCountrySelected($event)" class="input-textfield title"
                                        dropdownPosition="bottom" [items]="countries" formControlName="dialCode"
                                        bindLabel="name" bindValue="dial_code" [searchFn]="searchByNameOrCode">
                                        <ng-template ng-label-tmp let-item="item">
                                            <img class="flag-label"
                                                src="assets/flags/{{getCountryCode(getFormControl('dialCode').value, getFormControl('phoneNumber').value)}}.png" />
                                            <span>{{getFormControl('dialCode').value}}</span>
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item">
                                            <span class="country-name" title="{{item.name}}">{{item.name}}</span>
                                            <img class="icon-country-flag"
                                                src="assets/flags/{{item.code | lowercase}}.png" />
                                        </ng-template>
                                    </ng-select>
                                    <span *ngIf="getFormControl('dialCode').hasError('required')" class="error"
                                    style="width:auto !important;white-space: break-spaces !important;">{{'personal.Thisfieldisrequired' | translate}}</span>
                                </div>
                                <!-- <div class="flex-fill"> -->
                                <div class="phone-textfield">
                                    <input type="tel" 
                                        class="input-textfield phoneNumber"
                                        placeholder="{{getPhoneNumberPaceHolder(getFormControl('dialCode').value)}}"
                                        formControlName="phoneNumber" />
                                    <span
                                        *ngIf="getFormControl('phoneNumber').hasError('pattern') && (getFormControl('phoneNumber').touched || getFormControl('phoneNumber').dirty)"
                                        class="error">{{'profilePersonal.Pleaseenteravalidphonenumber' | translate}}
                                    </span>
                                </div>
                                <div style="margin-top: 90px;">
                                  
                                    <span
                                        *ngIf="getFormControl('phoneNumber').hasError('required') && (getFormControl('phoneNumber').touched || getFormControl('phoneNumber').dirty)"
                                        class="error"
                                        style="width:auto !important;white-space: break-spaces !important;">{{'personal.Thisfieldisrequired' | translate}}
                                    </span>
                                </div>

                                <!-- <int-phone-prefix [locale]="'en'" [defaultCountry]="'us'" formControlName="phoneNumber"></int-phone-prefix> -->


                            </div>
                        </div>
                        <div class="line-break"></div>
                        <div class="col-lg-8 col-md-9 col-sm-9 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.HomeAddress' | translate}}</label>
                                <!-- <textarea class="input-textfield googleAddress" id="homeAddress" name="homeAddress" placeholder="Write your home address here.."></textarea> -->
                                <input ngx-google-places-autocomplete
                                    (onAddressChange)="handleAddressChange('homeAddress', $event)" type="text"
                                    class="input-textfield googleAddress" id="homeAddress" name="homeAddress"
                                    placeholder="{{'profilePersonal.Enteryourhomeaddresshere' | translate}}"
                                    formControlName="homeAddress" />
                                <!--                                <div *ngIf="getFormControl('homeAddress').hasError('required') && (getFormControl('homeAddress').touched || getFormControl('homeAddress').dirty)"
                                    class="error">This field is required.
                                </div>-->
                            </div>
                        </div>
                        <div class="col-lg-8 col-md-9 col-sm-9 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.WorkAddress' | translate}}</label>
                                <input ngx-google-places-autocomplete
                                    (onAddressChange)="handleAddressChange('workAddress', $event)" type="text"
                                    class="input-textfield googleAddress" id="workAddress" name="workAddress"
                                    placeholder="{{'profilePersonal.Enteryourworkaddresshere' | translate}}"
                                    formControlName="workAddress" />
                                <!--                                <div *ngIf="getFormControl('workAddress').hasError('required') && (getFormControl('workAddress').touched || getFormControl('workAddress').dirty)"
                                    class="error">This field is required.
                                </div>-->
                            </div>
                        </div>
                        <div *ngIf="false" class="col-lg-8 col-md-9 col-sm-9 col-xs-12">
                        <div class="airlineFlyerNumberDiv-container" >
                                <div 
                                    class="row airlineFlyerNumberDiv" id="airlineFlyerNumberDiv" >
                                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                                        <div class="input-box">
                                            <label class="input-label">{{'profileLoyalty.Locale' |
                                                translate}}</label>
                                            <div class="select-box select-dropdown searchable-dropdown">
                                                <!-- <select formControlName="airline" onchange="selectAirlineChange(this);" class="input-textfield airlineOptions js-example-basic-single" id="airlineOptions" name="airlineOptions">
                                                    <option *ngFor="let aItem of all_airlines" value="aItem.id">{{aItem.name}}</option>
                                                </select> -->
                                                <ng-select formControlName="locale"
                                                    class="input-textfield airlineOptions js-example-basic-single"
                                                    (change)="onLocaleChange()" [searchable]="true" [clearable]="false"
                                                    [items]="this.languages_locale" bindLabel="label" bindValue="id"></ng-select>
                                                
                                            </div>
                                        </div>
                                    </div>
                                 
                                  
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-xl-2 col-lg-3  col-md-3 col-sm-3 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">Zip Code</label>
                                <input class="input-textfield" type="text" id="homeZip" name="homeZip" placeholder="Area Zip Code" />
                            </div>
                        </div> -->
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton" (click)="submitPersonalInfo()"
                                    id="savePersonalInfoBtn"> {{'profilePersonal.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profilePersonal.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<div class="card-div">
    <div class="card-div-inner">
        <form method="post" id="knownTravellerForm" class="knownTravellerForm">
            <div class="traveller-form">
                <div class="card-div-header" onclick="openCardEdit(this);">
                    <h3>{{'profilePersonal.KnownTravelerNumber' | translate}}</h3>
                    <div class="card-edit">
                        <span class="edit-text">Edit</span>
                        <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                    </div>
                    <div class="traveller-short-info">
                        <div class="traveller-short-info-inner">{{ktnForm.controls['knownTravellerNumber'].value}}</div>
                    </div>
                </div>
                <div class="card-div-body" style="display: none;" [formGroup]="ktnForm">
                    <div class="row">
                        <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.KnownTravelerNumber' | translate}}</label>
                                <input [maxLength]="getMaxKtnLimit()" formControlName="knownTravellerNumber"
                                    class="input-textfield shortInfoField" id="travellerNumber" name="travellerNumber"
                                    type="text" placeholder="{{'profilePersonal.KTNNumber' | translate}}" />
                                <span
                                    *ngIf="ktnForm.get('knownTravellerNumber').hasError('minlength') && !ktnForm.get('knownTravellerNumber').hasError('pattern') && (ktnForm.get('knownTravellerNumber').touched || ktnForm.get('knownTravellerNumber').dirty)"
                                    class="error">{{'profilePersonal.Thisfieldcannotbelessthan9characters' | translate}}
                                </span>
                                <!--                  <span *ngIf="ktnForm.get('knownTravellerNumber').hasError('required') && (ktnForm.get('knownTravellerNumber').touched || ktnForm.get('knownTravellerNumber').dirty)"
                    class="error">This field is required.
                  </span>-->
                                <span
                                    *ngIf="ktnForm.get('knownTravellerNumber').hasError('pattern') && (ktnForm.get('knownTravellerNumber').touched || ktnForm.get('knownTravellerNumber').dirty)"
                                    class="error">{{'profilePersonal.PleaseenteravalidKnownTravelerNumber' | translate}}
                                </span>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton" (click)="submitKTNInfo()"
                                    id="saveKTNInfoBtn">{{'profilePersonal.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profilePersonal.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<div class="card-div">
    <div class="card-div-inner">
        <form method="post" id="passportForm" class="passportForm">
            <div class="traveller-form">
                <div class="card-div-header" onclick="openCardEdit(this);">
                    <h3>{{'profilePersonal.Passport' | translate}}</h3>
                    <div class="card-edit">
                        <span class="edit-text">Edit</span>
                        <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                    </div>
                    <div class="traveller-short-info">
                        <div class="traveller-short-info-inner">
                            {{getPassportShortInfo(passportInfoForm.controls['passportNumber'].value,(passportInfoForm.controls['passportExpiryDate'].value
                            | date:'MMM d, yyyy'),passportInfoForm.controls['passportCountry'].value)}}</div>
                    </div>
                </div>
                <div class="card-div-body" style="display: none;" [formGroup]="passportInfoForm">
                    <div class="row">
                        <div class="col-xl-4 col-lg-4  col-md-6 col-sm-6 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.PassportNumber' | translate}}</label>
                                <input maxlength="50" formControlName="passportNumber" class="input-textfield shortInfoField"
                                    type="text" id="passportNumber" name="passportNumber"
                                    placeholder="{{'profilePersonal.PassportNumber' | translate}}" />
                                <span
                                    *ngIf="passportInfoForm.get('passportNumber').hasError('pattern') && (passportInfoForm.get('passportNumber').touched || passportInfoForm.get('passportNumber').dirty)"
                                    class="error">{{'profilePersonal.Pleaseenteravalidpassportnumber' | translate}}
                                </span>
                                <!--                                <span *ngIf="passportInfoForm.get('passportNumber').hasError('required') && (passportInfoForm.get('passportNumber').touched || passportInfoForm.get('passportNumber').dirty)"
                                    class="error">This field is required.
                                </span>-->
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.PassportExpiryDate' | translate}}</label>
                                <!-- <input style="cursor: pointer;" class="input-textfield shortInfoField passportDate datepicker" id="passportExpiryDate" name="passportExpiryDate" type="tel" placeholder="mm/dd/yyyy" readonly/> -->
                                <input [readOnly]="isDOBCalendarReadOnly()" style="cursor: pointer;"
                                    class="input-textfield shortInfoField datepicker" id="passportExpiryDate"
                                    name="passportExpiryDate" bsDatepicker #expiryDatePicker="bsDatepicker"
                                    [outsideClick]="true" [bsConfig]="{showWeekNumbers: false}"
                                    placeholder="{{'profilePersonal.MMDDYY' | translate}}" [minDate]="minPassportDate"
                                    formControlName="passportExpiryDate" (keydown)="$event.preventDefault()"
                                    (onShown)="onShowPicker($event, expiryDatePicker)" (onHidden)="onHidePicker()" />
                                <!--                                <span *ngIf="passportInfoForm.get('passportExpiryDate').hasError('required') && (passportInfoForm.get('passportExpiryDate').touched || passportInfoForm.get('passportExpiryDate').dirty)"
                                    class="error">This field is required.
                            </span>-->
                            </div>
                        </div>
                        <div class="line-break"></div>
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.Nationality' | translate}}</label>
                                <div class="select-box select-dropdown searchable-dropdown">
                                    <!-- <select class="input-textfield js-example-basic-single countryDropdown" id="nationality" name="nationality"></select> -->
                                    <ng-select dropdownPosition="bottom" placeholder="" [items]="countries"
                                        bindLabel="nationality" bindValue="code" formControlName="passportNationality"
                                        [searchable]="true"
                                        class="input-textfield js-example-basic-single countryDropdown"></ng-select>
                                    <!--                                    <span *ngIf="passportInfoForm.get('passportNationality').hasError('required') && (passportInfoForm.get('passportNationality').touched || passportInfoForm.get('passportNationality').dirty)"
                                        class="error">This field is required.
                                    </span>-->
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6 col-xs-12">
                            <div class="input-box">
                                <label class="input-label">{{'profilePersonal.Country' | translate}}</label>
                                <div class="select-box select-dropdown searchable-dropdown">
                                    <!-- <select class="input-textfield js-example-basic-single countryDropdown" id="country" name="country"></select> -->
                                    <ng-select class="input-textfield js-example-basic-single countryDropdown"
                                        dropdownPosition="bottom" placeholder="" [items]="countriesName" bindLabel="name"
                                        bindValue="code" [searchable]="true" formControlName="passportCountry">
                                    </ng-select>
                                    <!-- <i18n-country-select [(iso3166Alpha2)]="country.isocode" size="sm" placeholder="Select Issuance Country" (click)="onSelectPassportCountry(i,$event)"></i18n-country-select> -->

                                    <!--                  <span *ngIf="passportInfoForm.get('passportCountry').hasError('required') && (passportInfoForm.get('passportCountry').touched || passportInfoForm.get('passportCountry').dirty)"
                                    class="error">This field is required.
                                </span>-->
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton" (click)="submitPassportInfo()"
                                    id="savePassportInfoBtn">{{'profilePersonal.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profilePersonal.PleaseWait' | translate}} <loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card-div">
    <div class="card-div-inner">
        <form method="post" id="emergencyContact" class="emergencyContact">
            <div class="traveller-form">
                <div class="card-div-header" onclick="openCardEdit(this);">
                    <h3>{{'profilePersonal.EmergencyContact' | translate}}</h3>
                    <div class="card-edit">
                        <span class="edit-text">Edit</span>
                        <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                    </div>
                    <div class="traveller-short-info">
                        <div class="traveller-short-info-inner">{{'profilePersonal.Pleasekeepyouremergencycontactdetailsuptodate.' | translate}}
                        </div>
                    </div>
                </div>
                <div class="card-div-body" style="display: none;" [formGroup]="emergencyContact">
                    <div class="row">
                        <div class="input-box input-box-multiple">
                            <div class="multiple-inputs">
                                <div class="traveller-short-info-inner" style="margin-bottom: 20px;color: gray">{{'profilePersonal.Someonetrustedtohelpoutinanemergency.' | translate}}</div>
                                <div class="row">
                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                        <input formControlName="name" class="input-textfield firstName" maxlength="100"
                                            (input)="getName($event.target.value)"
                                            (focusout)="getName($event.target.value)" type="text"
                                            placeholder="{{'personal.FullName' | translate}}" />
                                        <div *ngIf="emergencyContact.get('name').hasError('pattern') && (emergencyContact.get('name').touched || emergencyContact.get('name').dirty)"
                                            class="error">
                                            {{'personal.PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed' |
                                            translate}}
                                        </div>
                                        <div *ngIf="emergencyContact.get('name').hasError('required') && (emergencyContact.get('name').touched || emergencyContact.get('name').dirty)"
                                            id="title-error" class="error">
                                            {{'personal.Thisfieldisrequired' | translate}}</div>
                                        <div *ngIf="emergencyContact.get('name').hasError('minLengthArray')"
                                            class="error">{{'personal.Spacenotallowed' | translate}}</div>
                                    </div>
                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                        <input formControlName="relationship" class="input-textfield firstName"
                                            (input)="getRelationship($event.target.value)" maxlength="50"
                                            (focusout)="getRelationship($event.target.value)" type="text"
                                            placeholder="{{'personal.Relationship' | translate}}" />
                                        <div *ngIf="emergencyContact.get('relationship').hasError('pattern') && (emergencyContact.get('relationship').touched || emergencyContact.get('relationship').dirty)"
                                            class="error">
                                            {{'personal.PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed' |
                  translate}}
                                        </div>
                                        <div *ngIf="emergencyContact.get('relationship').hasError('required') && (emergencyContact.get('relationship').touched || emergencyContact.get('relationship').dirty)"
                                            class="error">
                                            {{'personal.Thisfieldisrequired' | translate}}</div>
                                        <div *ngIf="emergencyContact.get('relationship').hasError('minLengthArray')"
                                            class="error">{{'personal.Spacenotallowed' | translate}}</div>
                                    </div>
                                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                                        <div class="input-box">
                                            <div
                                                class="select-box select-dropdown searchable-dropdown phone-select-box">

                                                <ng-select (change)="onCountrySelectedForEC($event)" class="phone-number"
                                                    dropdownPosition="bottom" [items]="countries"
                                                    formControlName="dialCode1" bindLabel="name" bindValue="dial_code"
                                                    [searchFn]="searchByNameOrCode">
                                                    <ng-template ng-label-tmp let-item="item">
                                                        <img class="flag-label1"
                                                            src="assets/flags/{{getCountryCode1(emergencyContact.get('dialCode1').value, emergencyContact.get('contactNumber').value)}}.png" />
                                                        <span>{{emergencyContact.get('dialCode1').value}}</span>
                                                    </ng-template>
                                                    <ng-template ng-option-tmp let-item="item">
                                                        <span class="country-name"
                                                            title="{{item.name}}">{{item.name}}</span>
                                                        <img class="icon-country-flag"
                                                            src="assets/flags/{{item.code | lowercase}}.png" />
                                                    </ng-template>
                                                </ng-select>

                                            </div>
                                            <div class="phone-textfield1">
                                                <input type="tel"
                                                  
                                                    class="formControl1" (input)="getphoneNumber($event.target.value)"
                                                    (focusout)="getphoneNumber($event.target.value)"
                                                    placeholder="{{getPhoneNumberPaceHolder(emergencyContact.get('dialCode1').value)}}"
                                                    formControlName="contactNumber" />
                                                <div *ngIf="emergencyContact.get('contactNumber').hasError('pattern') && (emergencyContact.get('contactNumber').touched || emergencyContact.get('contactNumber').dirty)"
                                                    class="error">{{'profilePersonal.Pleaseenteravalidphonenumber' |
                                                    translate}}
                                                </div>
                                            </div>
                                            <div>
                                                <span *ngIf="emergencyContact.get('dialCode1').hasError('required')"
                                                    class="error"
                                                    style="width:auto !important;white-space: break-spaces !important;">{{'personal.Thisfieldisrequired' | translate}}</span>
                                                <span
                                                    *ngIf="emergencyContact.get('contactNumber').hasError('required') && (emergencyContact.get('contactNumber').touched || emergencyContact.get('contactNumber').dirty)"
                                                    class="error"
                                                    style="width:auto !important;margin-left:8px !important;white-space: break-spaces !important;">{{'personal.Thisfieldisrequired' | translate}}
                                                </span>
                                            </div>


                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton" (click)="submitEmergencyInfo()"
                                    id="saveEmergencyContactBtn">{{'profilePersonal.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profilePersonal.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>