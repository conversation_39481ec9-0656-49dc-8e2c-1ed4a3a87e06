import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ProfilePersonalInfoComponent } from './profile-personal-info.component';

describe('ProfilePersonalInfoComponent', () => {
  let component: ProfilePersonalInfoComponent;
  let fixture: ComponentFixture<ProfilePersonalInfoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ProfilePersonalInfoComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProfilePersonalInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
