/*placeholder*/
::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  opacity: 1;
  color: #aeaeae;
}

::-moz-placeholder {
  /* Firefox 19+ */
  opacity: 1;
  color: #aeaeae;
}

.loaderClass {
  display: flex;
  margin-top: 0px;
}

:host ::ng-deep {

  .ng-select.ng-select-single .ng-select-container .ng-value-container,
  .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
    overflow: visible !important;
    position: relative !important;
    top: 0px !important;
  }

  .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
    position: absolute;
    left: 0;
    width: 100%;
    top: 0px;
  }

  .ng-select .ng-select-container .ng-value-container .ng-input>input {
    box-sizing: content-box;
    background: none;
    border: 0;
    box-shadow: none;
    outline: 0;
    cursor: default;
    width: 100%;
    color: black;
  }
}

.formControl1 {
  height: 50px;
  outline: none;
  width: 100%;
  border: 1px solid var(--dark-bg-color);
  background-color: #FFFFFF;
  border-radius: 8px;
  padding-left: 100px;
  font-family: "apercu-mono";
  letter-spacing: -0.98px;
}

.phone-number {
  position: absolute;
  width: 90px;
  margin-top: 0px;
  padding-top: 4px;
  height: 50px;
  margin-left: 8px;
  border: none;
  cursor: pointer;
  outline: none;
  border-right: 1px solid var(--dark-bg-color);
}

.flag-label1 {
  height: 15px;
  width: 20px;
  margin-right: 5px;
  margin-top: 0px;
  text-decoration: none;
  cursor: pointer;
}

:-ms-input-placeholder {
  /* IE 10+ */
  opacity: 1;
  color: #aeaeae;
}

:-moz-placeholder {
  /* Firefox 18- */
  opacity: 1;
  color: #aeaeae;
}

/*error*/
span.error {
  color: #f00;
  font-size: 12px;
  float: left;
  width: 100%;
}

/*button styles*/
.button-container {
  float: left;
  width: 100%;
}

.card-div-inner {
  width: 100% !important;
}

.button {
  border-radius: 2px !important;
  cursor: pointer;
  height: 50px;
  padding: 0 32px;
  border: none;
  text-transform: capitalize;
  font-size: 16px;
  font-family: var(--globalFontfamilyr);font-weight: bold;;
  letter-spacing: 0.48px;
  line-height: 25px;
}

.button-primary {
  background: var(--button-bg-color);
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
  text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
  color: var(--button-font-color);
}

.button-secondary {
  background: transparent;
  color: var(--button-bg-color);
  border: 2px solid var(--button-bg-color);
}

.button-text {
  background: none;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--button-font-color);
  border: none;
  border-radius: 0;
  padding: 0;
  margin: 0 20px;
  font-family: "apercu-b";
  font-size: 12px;
}

.button-black {
  background: #000;
  border-radius: 5px !important;
}

.button-yellow {
  background: #ffc34f;
  border-radius: 5px !important;
}

.pay-buttons .button {
  display: block;
  width: 266px;
  margin-bottom: 16px;
}

.button.disabled {
  pointer-events: none;
  opacity: 0.4;
}

/*radio button styles*/
.mdl-radio {
  font-family: "apercu-r";
  line-height: 26px;
}

.mdl-radio__outer-circle {
  height: 18px;
  width: 18px;
  border: 1px solid #C8C8C8;
}

.mdl-radio__inner-circle {
  top: 6px;
  left: 2px;
  width: 14px;
  height: 14px;
  background: var(--button-bg-color);
}

.mdl-radio.is-checked .mdl-radio__outer-circle {
  border: 1px solid var(--button-bg-color);
}

.mdl-radio.is-checked .mdl-radio__label {
  font-family: "apercu-b";
}

.mdl-radio,
.mdl-radio.is-upgraded {
  width: auto;
  margin-right: 20px;
  padding: 0 0 0 28px;
  font-family: "apercu-r";
}

/*checkbox styles*/
.mdl-checkbox {
  float: left;
  width: auto;
  font-weight: normal;
  cursor: pointer;
  margin-right: 8px;
  margin-bottom: 6px;
}

.mdl-checkbox:last-child {
  margin-right: 0;
}

.mdl-checkbox__box-outline {
  border: 1px solid #979797;
  width: 17px;
  height: 17px;
  border-radius: 0;
}

.mdl-checkbox.is-checked .mdl-checkbox__box-outline {
  border: 1px solid var(--button-bg-color);
}

.mdl-checkbox.is-checked .mdl-checkbox__tick-outline {
  background-color: var(--button-bg-color) !important;
}

.mdl-checkbox__ripple-container {
  top: 4px;
  left: 2px;
}

.selection-checkbox.checkbox2.checkbox-seperate {
  margin-right: 8px !important;
  margin-left: 0;
  width: auto;
  padding: 0 38px;
}

.selection-checkbox.checkbox2.checkbox-seperate .mdl-radio__label {
  font-family: "apercu-mono" !important;
}

/*selection checkbox*/
.selection-checkbox .mdl-radio__ripple-container,
.selection-checkbox .mdl-radio__outer-circle,
.selection-checkbox .mdl-radio__inner-circle {
  display: none;
}

.selection-checkbox .mdl-radio {
  float: left;
  width: 100%;
  border: 2px solid #E7E6E4;
  background-color: #FFFFFF;
  height: 50px;
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: normal;
  cursor: pointer;
}

.selection-checkbox .mdl-radio.is-checked {
  background: var(--genderSelected);
  border: 2px solid var(--button-bg-color);
}

.selection-checkbox.mdl-checkbox,
.selection-checkbox.mdl-radio {
  cursor: pointer;
  height: 155px;
  width: 155px;
  border: 2px solid #E7E6E4;
  background-color: rgba(255, 255, 255, 0.3);
  padding: 5px;
  margin-bottom: 16px;
}

.selection-checkbox .mdl-checkbox__box-outline,
.selection-checkbox .mdl-checkbox__focus-helper,
.selection-checkbox .mdl-checkbox__ripple-container,
.selection-checkbox .mdl-radio__outer-circle,
.selection-checkbox .mdl-radio__inner-circle,
.selection-checkbox .mdl-radio__ripple-container {
  display: none;
}

.selection-checkbox .mdl-checkbox__label,
.selection-checkbox .mdl-radio__label {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  justify-content: center;
  align-items: center;
  font-family: "apercu-r";
}

.selection-checkbox .mdl-checkbox__label img,
.selection-checkbox .mdl-radio__label img {
  margin-bottom: 10px;
}

.selection-checkbox.mdl-checkbox.is-checked,
.selection-checkbox.mdl-radio.is-checked {
  background-color: var(--genderSelected);
  border: 2px solid var(--button-bg-color);
  color: var(--button-font-color);
}

.selection-checkbox.mdl-checkbox.is-checked .mdl-checkbox__label span,
.selection-checkbox.mdl-radio.is-checked .mdl-checkbox__label span {
  font-family: "apercu-b";
}

.selection-checkbox.checkbox2 {
  height: 50px;
  width: 165px;
  float: left;
  margin-left: -2px;
  position: relative;
  margin-right: 0 !important;
  padding: 0 10px;
}

.selection-checkbox.checkbox2:first-child {
  margin-left: 0;
}

.selection-checkbox.checkbox2.is-checked {
  z-index: 2;
}

.tab-list ul .tab-list-item {
  margin-bottom: 8px !important;
}

.airlinesBack {
  position: absolute;
  left: 15px;
  width: 13px;
  top: 9px;
  display: none;
}

.airlineCheckboxContainer,
.hotelCheckboxContainer {
  width: 684px;
  float: left;
}

.airlineCheckboxContainer .mdl-checkbox .mdl-checkbox__label .mdl-checkbox__label-img img {
  width: 70px;
  max-width: 100px;
  max-height: 100px;
}

.any .selected {
  display: none;
}

.any.is-checked .notSelected {
  display: none;
}

.any.is-checked .selected {
  display: block;
}

/*.airlineCheckboxContainer,.hotelCheckboxContainer {width: 900px;float: none; margin: 0 -8px;}*/
.airline-div {
  padding-left: 8px;
  padding-right: 8px;
}

.checkbox-radio-container .error {
  padding: 0 8px;
}

.seat-selection img.active {
  display: none;
}

.seat-selection.is-checked img.inactive {
  display: none;
}

.seat-selection.is-checked img.active {
  display: block;
}

.mdl-checkbox__label-img {
  height: 90px;
  display: flex;
  justify-content: center;
  align-items: center;
  float: left;
  width: 100%;
}

.mdl-checkbox__label-text {
  float: left;
  width: 100%;
  text-align: center;
}

/*input-textfield*/
.input-box {
  float: left;
  width: 100%;
  margin-bottom: 12px;
  position: relative;
}

.input-label {
  float: left;
  width: 100%;
  color: #ABA7A4;
  font-size: 14px;
  line-height: 17px;
  margin-bottom: 8px;
  font-family: "apercu-r";
  font-weight: normal;
}

.input-textfield {
  float: left;
  width: 100%;
  border: 2px solid #E7E6E4;
  background-color: #FFFFFF;
  height: 50px;
  font-size: 16px;
  padding: 5px 16px;
  color: #413E3B;
  resize: none;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  font-family: "apercu-mono";
}

textArea.input-textfield {
  padding-top: 10px;
  padding-bottom: 10px;
}

.input-textfield[disabled] {
  background-color: #F6F6F6;
}

.label-note {
  font-size: 12px;
  line-height: 12px;
  color: var(--dark-bg-color);
  padding-left: 7px;
}

.datepicker {
  background-image: url(/../../assets/images/calendar.png);
  background-repeat: no-repeat;
  background-position: right 17px center;
}

.input-textfield.datepicker {
  padding-right: 50px;
}

.input-box-icon-container {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  height: 50px;
  left: 19px;
}

.input-box-icon-container.withLabel {
  margin-top: 25px;
}

.input-box-icon-container.left {
  left: 17px;
}

.input-box-icon-container.right {
  right: 19px;
}

.input-box-icon-container.right+input {
  padding-right: 50px;
}

.input-box-icon-container.left+input {
  padding-left: 50px;
}

.input-label {
  float: left;
  width: 100%;
  color: #ABA7A4;
  font-size: 14px;
  line-height: 17px;
  margin-bottom: 8px;
  font-family: "apercu-r";
  font-weight: normal;
}

.input-textfield {
  float: left;
  width: 100%;
  border: 1px solid var(--dark-bg-color);
  font-size: 16px;
  background-color: #FFFFFF;
  border-radius: 8px !important;
  height: 50px;
  padding: 5px 16px;
}

.input-with-checkbox input {
  padding-right: 130px;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__label {
  font-size: 14px;
  line-height: 17px;
  font-weight: normal;
  font-family: "apercu-r";
  width: 100%;
}

.input-textfield-lg {
  height: 64px;
}

.input-textfield {
  margin-bottom: 8px;
}

.input-box-with-icon input {
  padding-left: 45px;
}

/*datepicker*/
.ui-datepicker {
  z-index: 9999 !important;
}

.ui-datepicker-month,
.ui-datepicker-year {
  background: #fff;
  border: 2px solid #E7E6E4;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  font-family: "apercu-r" !important;
  font-weight: normal !important;
}

.ui-widget-content {
  color: #413E3B;
}

.ui-widget {
  font-family: "apercu-r";
  font-weight: normal;
}

.ui-datepicker-month,
.ui-datepicker-year {
  background-image: url(/../../assets/images/arrow-down.png);
  background-repeat: no-repeat;
  background-position: right 5px center;
}

.ui-widget-header {
  border: 1px solid #EEECEB;
  background: #EEECEB;
  color: #413E3B;
  font-weight: normal;
}

.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
  border: 1px solid #E7E6E4;
  background: #EEECEB;
  color: #413E3B;
}

.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
  border: 1px solid #E7E6E4;
  background: #EEECEB;
  font-weight: normal;
  color: #413E3B;
}

.ui-widget-header .ui-icon.ui-icon-circle-triangle-w {
  background-image: url(/../../assets/images/down-icon.png);
  background-position: -4px 4px;
  background-size: 24px auto;
  transform: rotate(90deg);
  height: 24px;
  width: 24px;
}

.ui-widget-header .ui-icon.ui-icon-circle-triangle-e {
  background-image: url(/../../assets/images/down-icon.png);
  background-position: 4px -4px;
  background-size: 24px auto;
  transform: rotate(-90deg);
  height: 24px;
  width: 24px;
}

.ui-datepicker .ui-datepicker-prev-hover {
  left: 2px;
  top: 2px;
}

.ui-datepicker .ui-datepicker-next-hover {
  right: 2px;
  top: 2px;
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
  border: 1px solid var(--button-bg-color);
  background: var(--button-bg-color);
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  top: 6px;
}

/*intelinput*/
.intl-tel-input {
  width: 100%;
}

.intl-tel-input .selected-flag .iti-arrow {
  border: none !important;
  background-image: url(/../../assets/images/arrow-down1.png);
  background-repeat: no-repeat;
  width: 28px;
  height: 28px;
  background-size: 28px;
  background-position: right center;
  top: 3px;
  height: 100%;
  right: 6px;
}

.intl-tel-input.allow-dropdown .selected-flag,
.intl-tel-input.separate-dial-code .selected-flag {
  width: 75px;
}

.intl-tel-input .selected-flag {
  padding: 0 0 0 19px;
}

.intl-tel-input.allow-dropdown input,
.intl-tel-input.allow-dropdown input[type="text"],
.intl-tel-input.allow-dropdown input[type="tel"],
.intl-tel-input.separate-dial-code input,
.intl-tel-input.separate-dial-code input[type="text"],
.intl-tel-input.separate-dial-code input[type="tel"] {
  padding-left: 80px;
}

/*select box styles*/
.select-box {
  position: relative;
  float: left;
  width: 100%;
}

.title-select-box {
  max-width: 218px;
}

.phone-select-box {
  max-width: 170px;
}

.select-box select {
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  position: relative;
  z-index: 2;
  background-image: url(/../../assets/images/arrow-down1.png);
  background-repeat: no-repeat;
  background-position: right 5px center;
  background-size: 26px;
}

/*select2*/
select {
  font-family: "apercu-mono";
}

.select2-container {
  width: 100% !important;
  font-family: "apercu-mono";
}

.select2-selection__arrow {
  position: relative;
  z-index: 9999;
}

.select2-selection__arrow b {
  border-width: 0 !important;
}

.select2-selection__arrow:after {
  content: '';
  position: absolute;
  background-image: url(/../../assets/images/arrow-down1.png);
  background-repeat: no-repeat;
  height: 36px;
  width: 36px;
  right: 6px;
  top: 8px;
}

.select2-container--default .select2-selection--single {
  height: 50px;
  border-radius: 0;
  border: 2px solid #E7E6E4;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 46px;
}

.select2-dropdown {
  border: 2px solid #E7E6E4;
  z-index: 9999;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--button-bg-color) !important;
  color: white;
}

.select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 16px;
  padding-right: 40px;
}

.selection-checkbox.mdl-radio.is-upgraded:not(.checkbox2) {
  padding-left: 0;
}

/*modal css*/
.modal-dialog {
  max-width: 710px;
}

.modal-content {
  border-radius: 0px;
  box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
  border: none;
  text-align: center;
  max-width: 710px;
  margin: 24px auto;
  width: calc(100% - 48px);
}

.modal-header {
  border: none;
  padding: 22px 17px 15px 25px;
}

.modal-title {
  display: inline-block;
  width: 35px;
  float: left;
}

.modal-title img {
  width: 35px;
}

.close {
  color: #000;
  opacity: 1;
}

.close:hover {
  color: #000;
  opacity: 1;
}

.modal-body {
  padding-bottom: 35px;
}

.modal-icon-container {
  float: left;
  width: 100%;
  text-align: center;
}

.modal-icon {
  background-color: #DDFFF7;
  border: 3px solid #1EBD97;
  width: 48px;
  height: 48px;
  display: inline-flex;
  color: #1EBD97;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
}

.modal-icon i {
  font-size: 30px;
}

.modal-content-heading {
  font-size: 22px;
  letter-spacing: -0.98px;
  line-height: 56px;
  color: #3C413B;
  float: left;
  width: 100%;
}

.modal-content-heading {
  font-size: 22px;
  letter-spacing: -0.98px;
  line-height: 56px;
  color: #3C413B;
  float: left;
  width: 100%;
  margin-top: 15px;
  margin-bottom: 10px;
}

.modal-content-heading h3 {
  margin: 0;
  font-size: 22px;
  color: #413E3B;
}

.modal-content-text {
  color: #6C6865;
  font-size: 18px;
  font-family: "apercu-r";
  line-height: 26px;
  margin-bottom: 50px;
  max-width: 530px;
  margin-left: auto;
  margin-right: auto;
}

.modal-content-text p {
  line-height: 26px;
}

.modal-backdrop {
  background-color: #eeedeb;
}

.modal-backdrop.in {
  filter: alpha(opacity=80);
  opacity: .8;
}

.fullBox {
  max-width: 667px;
}

.airlineSearch {
  background-image: url(/../../assets/images/inactive-arrow.png);
  background-repeat: no-repeat;
}

.airlineSearch {
  width: 100%;
  padding-right: 40px;
  background-image: url(/../../assets/images/inactive-arrow.png);
  background-repeat: no-repeat;
  background-position: right 14px center;
}

.primary-label {
  font-size: 16px;
  line-height: 27px;
  color: #413E3B;
}

.text-label {
  margin-bottom: 0;
}

.map {
  max-width: 665px;
}

.ui-autocomplete {
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
}

.card-add-block {
  float: left;
  width: 100%;
  margin-bottom: 20px;
}

.card-save-button {
  text-align: right;
}

.card-box {
  max-width: 660px;
}

.payment-radio-button {
  height: 40px;
}

.payment-radio-button .mdl-radio {
  width: 100%;
}

.payment-radio-button .mdl-radio__label img {
  width: 25px;
  margin-top: -3px;
}

.phone-select-box {
  margin-right: 8px;
}

.phone-textfield {
  float: left;
  width: 350px;
}

.phone-textfield1 {
  float: left;
  width: auto;
}

@media (max-width: 991px) {
  .input-textfield {
    max-width: 600px;
  }
}
@media (max-width: 768px) {
.traveller-short-info-inner{
  word-break: break-all;
}
}
@media (max-width: 767px) {
  .loaderClass {
    display: flex;
    margin-top: 10px;
  }

  .loaderAlign {
    position: relative;
    top: -5px;
  }

  .input-box {
    margin-bottom: 0;
  }

  .input-label {
    font-size: 12px;
    line-height: 15px;
    margin-bottom: 4px;
  }

  .primary-label {
    font-size: 14px;
    line-height: 18px;
    margin-bottom: 7px;
  }

  .select-box {
    margin-bottom: 8px;
  }

  .select2-container--default .select2-selection--single {
    height: 40px;
  }

  .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 37px;
    font-size: 12px;
  }

  .select2-selection__arrow:after {
    background-size: 26px;
  }

  .select2-selection__arrow::after {
    height: 26px;
    width: 26px;
    top: 7px;
  }

  .input-textfield {
    height: 40px;
    font-size: 12px;
    line-height: 15px;
  }

  .selection-checkbox.checkbox2 {
    height: 40px;
    font-size: 12px;
    line-height: 15px;
  }

  .selection-checkbox.checkbox2.checkbox-seperate {
    padding: 0 20px;
  }

  .button {
    height: 46px;
    font-size: 12px;
    line-height: 12px;
    letter-spacing: 1px;
    text-transform: uppercase;
  }

  .selection-checkbox.mdl-checkbox,
  .selection-checkbox.mdl-radio {
    height: 90px;
    width: 90px;
    margin-bottom: 8px;
    margin-right: 0;
  }

  .mdl-checkbox__label,
  .mdl-radio__label {
    font-size: 12px;
    line-height: 15px;
  }

  .selection-checkbox.checkbox2 {
    height: 57px;
    width: 100%;
    margin-bottom: 0;
    margin-top: -2px;
    margin-left: 0;
  }

  .selection-checkbox.checkbox2 .mdl-checkbox__label,
  .selection-checkbox.checkbox2 .mdl-radio__label {
    align-items: flex-start !important;
  }

  .selection-checkbox.checkbox2.checkbox-seperate {
    height: 40px;
    margin-bottom: 8px;
  }

  span.error {
    font-size: 10px;
    margin-top: -4px;
  }

  .mdl-radio__outer-circle {
    height: 14px;
    width: 14px;
  }

  .mdl-radio__inner-circle {
    top: 7px;
    left: 3px;
    width: 8px;
    height: 8px;
  }

  .mdl-radio,
  .mdl-radio.is-upgraded {
    padding: 0 0 0 20px;
  }

  .mdl-radio {
    line-height: 19px;
  }

  .mdl-checkbox__label-img {
    height: 60px;
  }

  .formControl1 {
    height: 40px;
    outline: none;
    width: 100%;
    border: 1px solid var(--dark-bg-color);
    border-radius: 8px;
    background-color: #FFFFFF;
    padding-left: 95px !important;
    font-family: "apercu-mono";
    letter-spacing: -0.98px;
  }

  .phone-number {
    position: absolute;
    width: 90px;
    margin-top: 10px;
    height: 38px;
    margin-left: 8px;
    border: none;
    cursor: pointer;
    outline: none;
    border-right: 2px solid #E7E6E4;
  }

  .card-box {
    max-width: none;
    width: 100%;
  }

  .phone-textfield1 {
    float: left;
    width: 100%;
    margin-bottom: 8px;
  }
}

@media (max-width: 576px) {
  .phone-textfield {
    float: left;
    width: 100%;
  }
}

@media(max-width:576px) {
  .ng-dropdown-panel {
    position: absolute !important;
    max-width: 365px !important;
    top: auto !important;
    left: 50% !important;
    -webkit-transform: translate(-50%, 0);
    -ms-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
    min-width: 100px !important;
  }

  .formControl1 {
    height: 40px;
    outline: none;
    width: 100%;
    border: 1px solid var(--dark-bg-color);
    border-radius: 8px !important;
    background-color: #FFFFFF;
    padding-left: 95px !important;
    font-family: "apercu-mono";
    letter-spacing: -0.98px;
  }

  .phone-number {
    position: absolute;
    width: 90px;
    margin-top: 10px;
    height: 38px;
    margin-left: 8px;
    border: none;
    cursor: pointer;
    outline: none;
    border-right: 2px solid #E7E6E4;
  }
}

@media (max-width: 420px) {

  .selection-checkbox.mdl-checkbox:not(.checkbox2),
  .selection-checkbox.mdl-radio:not(.checkbox2) {
    height: 85px;
    width: 85px;
  }

  .formControl1 {
    height: 40px;
    outline: none;
    width: 100% !important;
    border: 1px solid var(--dark-bg-color);
    background-color: #FFFFFF;
    padding-left: 95px !important;
    font-family: "apercu-mono";
    letter-spacing: -0.98px;
  }
}

@media (max-width: 380px) {

  .selection-checkbox.mdl-checkbox:not(.checkbox2),
  .selection-checkbox.mdl-radio:not(.checkbox2) {
    height: 85px;
    width: 85px;
  }

  .formControl1 {
    height: 40px;
    outline: none;
    width: 100% !important;
    border: 1px solid var(--dark-bg-color);
    background-color: #FFFFFF;
    padding-left: 95px !important;
    font-family: "apercu-mono";
    letter-spacing: -0.98px;
  }
}

@media(max-width:360px) {
  .formControl1 {
    height: 40px;
    outline: none;
    width: 100% !important;
    border: 1px solid var(--dark-bg-color);
    background-color: #FFFFFF;
    padding-left: 95px !important;
    font-family: "apercu-mono";
    letter-spacing: -0.98px;
  }
}

/*# sourceMappingURL=component.css.map */