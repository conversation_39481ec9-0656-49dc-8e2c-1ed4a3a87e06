import { Component, OnInit, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { BsModalService, BsModalRef, ModalDirective } from 'ngx-bootstrap/modal';
import { NgxGalleryOptions, NgxGalleryImage, NgxGalleryAnimation } from '@kolkov/ngx-gallery';

@Component({
    selector: 'room-gallery-modal',
    templateUrl: './room-gallery-modal.component.html',
    styleUrls: ['./room-gallery-modal.component.scss'],
    standalone: false
})
export class roomGalleryModal implements OnInit {
  [x: string]: any;
  @ViewChild('roomGalleryModal', { static: true }) modal: ModalDirective;
  bsModalRef: BsModalRef;
  galleryOptions: NgxGalleryOptions[];
  @Input() galleryImages: NgxGalleryImage[];
  @Input() hotelRoomIndex: number;
  @Output() hotelRoomSelectedEvent = new EventEmitter();
  @Input() selectedRoomIndex = -1;
  @Output() changeRoom = new EventEmitter<number>();
  constructor(
    private modalService: BsModalService,
  ) {

  }

  public ngOnInit(): void {
    this.galleryOptions = [
      {
        width: '623px',
        height: '423px',
        thumbnails: false,
        imageAnimation: NgxGalleryAnimation.Slide,
        arrowPrevIcon: 'fa fa-angle-left',
        arrowNextIcon: 'fa fa-angle-right',
        preview: false,
        imageInfinityMove: true
      },
      {
        breakpoint: 767,
        width: '371px',
        height: '252px',
      },

      {
        breakpoint: 400,
        width: '360px',
        height: '252px',
      },

      {
        breakpoint: 359,
        width: '320px',
        height: '252px',
      },
    ];

    // this.galleryImages = [
    //     {
    //         small: 'https://travelport.leonardocontentcloud.com/imageRepo/3/0/75/137/341/NYCMF_33154505_P.jpg',
    //         medium: 'https://travelport.leonardocontentcloud.com/imageRepo/3/0/75/137/341/NYCMF_33154505_P.jpg',
    //         big: 'https://travelport.leonardocontentcloud.com/imageRepo/3/0/75/137/341/NYCMF_33154505_P.jpg'
    //     },
    //     {
    //         small: 'https://media.iceportal.com/58059/photos/61660593_XXL.jpg',
    //         medium: 'https://media.iceportal.com/58059/photos/61660593_XXL.jpg',
    //         big: 'https://media.iceportal.com/58059/photos/61660593_XXL.jpg'
    //     },
    //     {
    //         small: 'https://travelport.leonardocontentcloud.com/imageRepo/2/0/71/630/7/NYCSF_4207136281_P.jpg',
    //         medium: 'https://travelport.leonardocontentcloud.com/imageRepo/2/0/71/630/7/NYCSF_4207136281_P.jpg',
    //         big: 'https://travelport.leonardocontentcloud.com/imageRepo/2/0/71/630/7/NYCSF_4207136281_P.jpg'
    //     },
    //     {
    //         small: 'https://travelport.leonardocontentcloud.com/imageRepo/4/0/63/713/847/NYCBPDT_NYCBP_Entrance_2011_P.jpg',
    //         medium: 'https://travelport.leonardocontentcloud.com/imageRepo/4/0/63/713/847/NYCBPDT_NYCBP_Entrance_2011_P.jpg',
    //         big: 'https://travelport.leonardocontentcloud.com/imageRepo/4/0/63/713/847/NYCBPDT_NYCBP_Entrance_2011_P.jpg'
    //     },
    //     {
    //         small: 'https://media.iceportal.com/60206/photos/64021755_XXL.jpg',
    //         medium: 'https://media.iceportal.com/60206/photos/64021755_XXL.jpg',
    //         big: 'https://media.iceportal.com/60206/photos/64021755_XXL.jpg'
    //     },
    //     {
    //         small: 'https://media.iceportal.com/60214/photos/63350580_XXL.jpg',
    //         medium: 'https://media.iceportal.com/60214/photos/63350580_XXL.jpg',
    //         big: 'https://media.iceportal.com/60214/photos/63350580_XXL.jpg'
    //     },
    //     {
    //         small: 'https://media.iceportal.com/49722/photos/64811019_XXL.jpg',
    //         medium: 'https://media.iceportal.com/49722/photos/64811019_XXL.jpg',
    //         big: 'https://media.iceportal.com/49722/photos/64811019_XXL.jpg'
    //     }
    // ];
  }
  showModal(roomGalleryModal) {

    this.bsModalRef = this.modalService.show(roomGalleryModal, { class: 'galleryModal' });
  }

  hideModal(roomGalleryModal) {
    this.bsModalRef.hide()
  }

  selectRoom() {
    this.hotelRoomSelectedEvent.emit(this.hotelRoomIndex);
    this.bsModalRef.hide()
  }
  selectedRoom() {
    this.changeRoom.emit(this.selectedRoomsIndex);
    this.hideModal(roomGalleryModal);
  }
}
