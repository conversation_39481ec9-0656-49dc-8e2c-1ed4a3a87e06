.modal-body {
    padding: 0;
}

.modal-loader {
    background: #fff;
    text-align: center;
    border-radius: 5px;
    background-color: #FFFFFF;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    width: 100%;
}

.modal-loader-top {
    float: left;
    width: 100%;
    padding: 22px 68px 44px 69px;
}

.modal-loader-bottom {
    float: left;
    width: 100%;
    background-color: #EFFAFC;
    padding: 28px 0 28px 0;
    border-radius: 0 0 6px 6px;
}

.modal-waiting-img img {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.modal-loader-top-text p {
    font-size: 18px;
    line-height: 24px;
    text-align: center;
}

.modal-loader-bottom-text p {
    font-size: 15px;
    line-height: 19px;
    text-align: center;
    max-width: 247px;
    margin: 0 auto;
}

.arrow-icon-vertical-container {
    text-align: center;
    margin-left: -88px;
}

.arrow-icon-vertical {
    height: 21px;
    width: 2px;
    background: #979797;
    display: inline-block;
    position: relative;
}

.arrow-icon-vertical:before {
    position: absolute;
    content: '';
    top: -2px;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 10px solid #979797;
    left: -4px;
}

.modal-loader-bottom-text {
    float: left;
    width: 100%;
    padding-top: 5px;
    padding-bottom: 37px;
}


.modal-button {
    position: absolute;
    top: 5px;
    right: 5px;
}

.modal-button button {
    width: 24px;
    height: 24px;
    border: none;
    padding: 0;
    color: var(--button-font-color);
    cursor: pointer;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
}

.galleryModalCloseButton {
    height: 32px;
    width: 32px;
    z-index: 9999;
    cursor: pointer;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    background-color: #FFFFFF;
    padding: 0;
    border-radius: 6px;
    color: var(--button-font-color);
    position: absolute;
    right: 17px;
    top: 14px;
    border: none;
}

.selectButton {
    position: absolute;
    bottom: -9px;
    left: 50%;
    z-index: 9999;
    transform: translate(-50%);
    height: 46px;
    width: 296px;
    font-size: 12px;
    letter-spacing: 1px;
}

@media (max-width: 1200px) {
    .modal-logo {
        width: 87.27px;
    }

    .modal-loader-top-text p {
        font-size: 12px;
        line-height: 19px;
        max-width: 214px;
        margin: 0 auto;
    }

    .modal-loader-top {
        padding: 30px 20px 38px 20px;
    }

    .arrow-icon-vertical {
        margin-left: -170px;
    }
}

@media (max-width: 767px) {
    .arrow-icon-vertical-container {
        margin-left: -108px;
    }

    .modal-loader-bottom-text {
        padding-bottom: 16px;
    }

    .galleryModalCloseButton {
        right: 10px;
        top: 10px;
    }

    .modal-button {
        top: 3px;
        right: 3px;
    }
}