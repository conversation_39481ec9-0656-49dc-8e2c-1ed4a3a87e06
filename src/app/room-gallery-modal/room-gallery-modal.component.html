<ng-template #roomGalleryModal let-modal>
    <div class="modal-body" style="display: flex;">
        <ngx-gallery [options]="galleryOptions" [images]="galleryImages"></ngx-gallery>
        <button class="galleryModalCloseButton" (click)="hideModal(roomGalleryModal)">X</button>
        <button class="btn primary-button selectButton" (click)="selectRoom()">{{'roomGallery.SELECTROOM' |
            translate}}</button>
    </div>
</ng-template>

<div class="modal-button">
    <button (click)="showModal(roomGalleryModal)"><img src="assets/images/hotel/expand.svg" alt=""
            style="z-index: 100" /></button>
</div>