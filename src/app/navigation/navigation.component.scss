.app-navigation-menu {
  display: grid;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, .3);
  float: left;
  background-color: #fff !important;
  position: fixed;
  bottom: 0;
  width: 100%;
  left: 0;
}

.app-navigation-menu-item {
  margin: 0 5px;
  text-align: center;
}
.add {
  height: 24px;
  width: auto !important;
  white-space: nowrap;
  color: #2D57FA;
  font-family: "apercu-r";
  font-size: 15px !important;
  font-weight: bold;
  letter-spacing: 0.6px;
  line-height: 25px;
  text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}
.modal-body {
  padding: 28px 25px 47px 25px;
  border-radius: 0 0 5px 5px;
}
.add1 {
  height: 24px;
  width: 68px;
  color: #2D57FA;
  font-family: "apercu-r";
  font-size: 15px;
  font-weight: bold;
  letter-spacing: 1px;
  line-height: 25px;
}

.app-navigation-menu-items {
  display: flex;
  justify-content: space-between;
  padding-top: 5px;
  padding-bottom: 25px;
}

.select {
  align-items: center;
  text-align: center;
  color: var(--hyperlink-color);
}
.btn-normal {
  height: 40px;
  width: 150px;
  letter-spacing: 1px;
  background-color: transparent;
  margin-top: 15px;
  border: none;
  box-shadow: none;
}
.btn-secondary {
  height: 40px;
  width: 150px !important;
  border-radius: 0px !important;
  letter-spacing: 1px;
  background-color: var(--button-bg-color) !important;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
  margin-top: 15px;
  border: none;
}
.unselect {
  align-items: center;
  text-align: center;
  color: gray;
}