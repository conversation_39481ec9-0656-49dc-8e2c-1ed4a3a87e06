import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ApprovalRequestDetailComponent } from './approval-request-detail.component';

describe('ApprovalDetailComponent', () => {
  let component: ApprovalRequestDetailComponent;
  let fixture: ComponentFixture<ApprovalRequestDetailComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ApprovalRequestDetailComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ApprovalRequestDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
