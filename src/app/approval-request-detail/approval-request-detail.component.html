<div class="main-wrapper" >
    <div class="content">
        <div class="container">
            <div *ngIf="!responnsecame " class="row" style="text-align: center;padding-top:30px;">
                <app-loader style="margin-right: auto;margin-left: auto;" [spinnerStyle]="true"></app-loader>
            </div>
            <div  class="top-strip booking-history-detail-heading" style="margin-bottom: 20px !important;">
                <a class="booking-history-heading-link" rel="tab1" href="javascript:void(0)" (click)="backToApproval()">
                    <img src="assets/images/hotel/backarrow.svg" alt="">
                </a>
                <span class="back" style="color:#fff !important"> {{'approvalDetail.Back' | translate }}</span>
            </div>
            <div  *ngIf="false && !waitData  && responnsecame && isSingleDetails"  class="row approver-info">
                <div class="col-lg-6 col-sm-12" *ngIf="!this.disApprovedReason && !this.approvedReason" >
                        <div class="title"> {{this.travellerName}}'s  {{'approvalDetail.itineraryforApproval' | translate }}</div>
                        <div class="textInformation" *ngIf="this.approvedReason">{{'dashboard.ApprovedOn' | translate }} {{this.selectedDetail.reviewedOn | date:'MMM d, y'}}</div>
                        <div class="title" *ngIf="this.disApprovedReason">Disapproved on {{this.selectedDetail.reviewedOn | date:'MMM d, y'}}</div>
                        <div class="textInformation" *ngIf="this.disApprovedReason || this.approvedReason"><span>By</span> {{getReviewedBy()}}</div>
                </div>

                <div class="col-lg-6 col-sm-12" *ngIf=" false && this.approvedReason && !this.disApprovedReason ">
                    <div class="d-flex">
                        <div style="margin-right: 10px;" class="approval-icon">
                            <img src="assets/images/approve-icon.svg" alt="">
                        </div>
                        <div style="width: 90%;" >
                            <div class="title" style="margin-top: 0px !important;margin-bottom: 0px !important;"> {{this.travellerName}}'s  Approved itinerary</div>
                            <div class="textInformation" *ngIf="this.approvedReason">{{'dashboard.ApprovedOn' | translate }} {{this.selectedDetail.reviewedOn | date:'MMM d, y'}}</div>
                            <div class="textInformation" *ngIf="this.disApprovedReason || this.approvedReason"><span>By</span> {{getReviewedBy()}}</div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-sm-12" *ngIf=" false&& this.disApprovedReason && !this.approvedReason">
                    <div class="d-flex">
                        <div style="margin-right: 10px;" class="approval-icon">
                            <img src="assets/images/disapproved-icon.svg" alt="">
                        </div>
                        <div style="width: 90%;">
                            <div class="title" style="margin-top: 0px !important;margin-bottom: 0px !important;"> {{this.travellerName}}'s  Disapproved itinerary</div>
                            <div class="textInformation" *ngIf="this.disApprovedReason">Disapproved on {{this.selectedDetail.reviewedOn | date:'MMM d, y'}}</div>
                            <div class="textInformation" *ngIf="this.disApprovedReason || this.approvedReason"><span>By</span> {{getReviewedBy()}}</div>
                        </div>
                    </div>                      
                </div>
                
                <div *ngIf=" false&& this.selectedDetail && this.selectedDetail.merchantname && this.selectedDetail.adminNote !=='' " class="col-lg-6 col-sm-12 reviewers-note">
                    <div style="font-family: var(--globalFontfamilyr);font-weight: bold;;">{{'approvalDetail.ReviewersNote' | translate }}:</div>
                    <div class="textInformation">
                        {{this.selectedDetail.adminNote}}
                    </div>
                </div>

            </div>
           
            <div style="text-align: center" *ngIf="this.errormsg !=='' && responnsecame">{{this.errormsg}}</div>
            <div *ngIf="responnsecame && this.errormsg == ''" class="card-div active shadow" style="margin-top:20px !important;">
                <div class="card-div-inner" [ngStyle]="{'padding-bottom':this.dropDownopen ?'300px':'10px'}">
                        <div>
                                <!-- <div   style="margin-top: 5px" class="approval-trip-name">
                                    <p style="font-family: var(--globalFontfamilyr);font-weight: bold;;"> {{this.detailList.submitter}}'s Trip: {{this.detailList.report_name}}</p>  
                                </div> -->

                                <!-- <div  style="margin-top: 5px;" class="information">
                                    <div class="messageForInformation" style="font-family: var(--globalFontfamilyr);font-weight: bold;;">{{'approvalDetail.Travelersnote' | translate }}</div>  
                                    <div class="textInformation" >{{selectedDetail.submitter}} </div>
                                </div> -->

                                <div  style="margin-top: 5px" class="information information2">
                                    <div class="messageForInformation" style="font-family: var(--globalFontfamilyr);font-weight: bold;;">Traveller Name </div>  
                                    <span class="textInformation" >{{getTravellerName(this.selectedDetail)}}</span>
                                </div>
                                
                                <div style="margin-top: 5px;padding-bottom: 10px" class="information information2">
                                    <div class="messageForInformation" style="font-family: var(--globalFontfamilyr);font-weight: bold;;">Traveller Email</div>  
                                    <span class="textInformation" >{{this.selectedDetail.submitter}}</span>
                                </div>
                                <div style="margin-top: 5px;padding-bottom: 10px" class="information information2">
                                    <div class="messageForInformation" style="font-family: var(--globalFontfamilyr);font-weight: bold;;"> Report : </div>  
                                    <span class="textInformation" >{{this.selectedDetail.report_name}}</span>
                                </div>
                                <ng-container >
                                    <div class="result-card-container">
                                        <div class="result-card-box" *ngFor="let report  of this.selectedDetail.report_data">
                                            <div>
                                                <div class="result-card-box-inner">
                                                    <div class="booking-details date">
                                                        <p >   Date</p>
                                                        <p class="bold-font"> {{report.date}}</p>
                                                    </div>
                                                    <div class="booking-details merchantname">
                                                        <p >   Merchant Name </p>
                                                        <p class="bold-font"> {{report.merchantname}}</p>
                                                    </div>
                                                    <div class="booking-details merchantcategory">
                                                        <p >  Merchant Category </p>
                                                        <p class="bold-font"> {{report.merchantcategory}}</p>
                                                    </div>
                                                    <!-- <div class="booking-details amount">
                                                        <span>Amount : {{report.amount}}</span>
                                                    </div> -->
                                                    <div class="booking-details expensecategory">
                                                        <p > Expense Category</p>
                                                        <p class="bold-font"> {{report.expensecategory}}</p>
                                                    </div>
                                                </div>
                                        
                                            </div>
                                            <div class="bottomcontainer" >
                                                <div class="mobileBtton" *ngIf="this.isMobile1">
                                                        <div  class="booking-view-button">
                                                                <button class="btn primary-normal"
                                                                    (click)="getReceipt(report)">
                                                                    <span class="add1">
                                                                        Receipt
                                                                    </span> </button>
                                                            </div>
                                                </div>
                                                <div class="origin-destination1">
        
                                                    <span [ngStyle]="channgeAlignment()" class="showinUSerdetails">
                                                        <span style="font-family: var(--globalFontfamilyr);font-weight: bold;;">Memo : </span> <span>{{report.memofields}}</span> 
                                                    </span>
        
                                                </div>
                                                <div *ngIf="!this.isMobile1" class="booking-view-button">
                                                    <button class="btn btn-normal"
                                                        (click)="getReceipt(report)">
                                                      <span class="add1"> Receipt  </span>
                                                    </button>
                                                </div>
                                                <div> Flag Status : {{report.flagstatus}}</div>
                                                <div class="booking-details amount" > Amount : {{report.amount}}</div>
                                         </div>
                                        </div>
                                        <!-- <div class="result-card-box" *ngFor="let report  of this.selectedDetail.report_data">
                                            <div class="result-card-flex" style="display: flex;justify-content: space-between;align-items: stretch;flex-basis: auto;gap:160px">
                                                <div class="result-card-flex-left" style="display: flex;justify-content: space-between;align-items: center;padding: 20px;width:100%">
                                                    <div>
                                                        <p>  <span class="bold-items">Date: </span> 26 sep 2024</p>
                                                        <p> <span class="bold-items">  Merchant Name : </span> Shivam</p>
                                                        <p> <span class="bold-items">  Merchants Category :</span> Sabre</p>
                                                    </div>
                                                    <div>
                                                        <p>  <span class="bold-items">  MemoField :</span> memofield</p>
                                                        <p>  <span class="bold-items">  flag Status :</span> flag1</p>
                                                        <p>  <span class="bold-items">  Expense Category :</span>  food</p>
                                                    </div>
                                                </div>
                                                <div  class="result-card-flex-right" style="width:250px;background-color: #E3E3E3;border-bottom-right-radius: 10px;border-top-right-radius: 10px;display: flex;align-items: center;flex-direction: column;justify-content: center; flex-shrink: 0px;">
                                                    <span class="price-show">$4500 </span>
                                                   <button class="btn btn-primary">
                                                    Receipt
                                                   </button>

                                                </div>
                                            </div>
                                        </div> -->
                                    </div>
                                </ng-container>

                            </div>
                            <div>
                                <div  class="approval-details">
                                    <div class="approvalButtonsBlock">
                                        <div class="message">Please click 'Approve Expense' to approve expeneses of following itinerary, or click 'Return' if you do not want to approve this expense report.</div>
                                        <div style="display: grid;position: relative;">
                                            <textarea 
                                                placeholder="{{'approvalDetail.Typeanoteforthetraveleroptional' | translate }}"
                                                maxlength="512" 
                                                [(ngModel)]="messageForDisapprove" 
                                                class="modal-textarea input-textfield"
                                                (focus)="this.error=false;" style="position: relative;padding-right: 55px;padding-top: 20px;"
                                                [attr.disabled]="this.disableSendNoteBtn ? true : null" 
                                                maxlength="512">
                                            </textarea>
                                            <span class="showNumber">
                                                {{messageForDisapprove.length}}/512
                                            </span>
                                            
                                        </div>
                                        <div class="approveButtonDiv">
                                            <button class="btn btn-secondary" (click)="approveed('approve',thankyoumodal)" [disabled]="this.disableApprovalBtn">
                                                <span class="add"> Approve </span>
                                            </button>
                                            <button class="btn btn-normal" (click)="approveed('disapprove',thankyoumodal)" [disabled]="this.disableApprovalBtn">
                                                <span class="add1">Return</span>
                                            </button>
                                            <app-loader *ngIf="this.disableSendNoteBtn" [spinnerStyle]="true"></app-loader>
                                        </div>
                                    </div>
                                </div>
                            
                            </div>
                        <hr>
                   
                </div>
            </div>
                <ng-template #thankyoumodal let-modal>
                        <div class="table-view">
                            <div class="table-cell-view">
                                <div class="modal-dialog modal-dialog-md" role="document">
                                    <div class="modal-content">
                                       
                                        <div class="modal-header">
                                                <span>
                                                        <img class="footerimage" [src]="this.searchService.footerLogo">
                                                      </span>
                                            <button *ngIf="!this.disabled" type="button" class="close" data-dismiss="modal"
                                                (click)="onCancel()">
                                                <i class="material-icons">close</i>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                           
                    
                                            <div class="modal-content-text modal-content-width">
                                               {{ 'employee.ThankyouforresolvingthisapprovalrequestThereare' | translate}}

                                            </div>
                                          
                                            <div class="modal-content-button" [ngStyle]="{'margin-bottom': this.disabled ? '20px':'44px'}">
                                                <button class="btn btn-secondary"
                                                    (click)="routeToApproval('approvals','pending',false)"
                                                    [disabled]="this.disabled"><span class="add">Back to list Page</span>
                                                </button>
                                              
                                            </div>
                                          
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-template>