import { Component, OnInit, EventEmitter, Output, ViewChild, Input } from '@angular/core';
import { NavigationExtras, Router, ActivatedRoute } from '@angular/router';
import { BookingHistoryService } from '../booking-history.service';
import { CommonUtils } from '../util/common-utils';
import { Constants } from '../util/constants';
import { DateUtils } from '../util/date-utils';
import { FlightUtils } from '../util/flight-utils';
import { TranslateService } from '@ngx-translate/core';
import { EmailQuoteOptionsService } from '../email-quote-options.service';
import { AdminPanelService } from '../admin-panel.service';
import { Location } from '@angular/common';
import { SearchResultService } from '../search-result.service';
import { BookingResponse } from '../entity/booking-response';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { BookingResponseErrorType } from '../enum/booking-response-error.type';
import { BookingService } from '../booking.service';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { BookingMessageModalComponent } from '../booking-message-modal/booking-message-modal.component';
import { ToastrService } from 'ngx-toastr';
import { Subscription, Subject } from 'rxjs';
import { UserAccountService } from '../user-account.service';
import { PopupComponent } from '../popup/popup.component';
import { SearchService } from '../search.service';
import { Title } from '@angular/platform-browser';
import { NgSelectComponent } from '@ng-select/ng-select';
import { DeviceDetailsService } from '../device-details.service';
import { BaggageDetails } from '../entity/baggage-details';
import { FareAttributes } from '../entity/fare-attributes';
import { HttpClient } from '@angular/common/http';
import { TypedRule } from 'tslint/lib/rules';
import { CancelSuccessComponent } from '../cancel-success/cancel-success.component';
@Component({
    selector: 'app-approval-request-detail',
    templateUrl: './approval-request-detail.component.html',
    styleUrls: ['./approval-request-detail.component.scss'],
    standalone: false
})
export class ApprovalRequestDetailComponent implements OnInit {
  @Output() goBackEmitter = new EventEmitter();
  @Input() approvalRequestDetailData;
  showDisapprovedReason = false;
  bookServiceSubscription: Subscription;
  companyApprovalSubscription:Subscription
  changingValue: Subject<any> = new Subject();
  modalOpen:any;
  pendingApprovals=[];
  spinnerStyle = false;
  employeedetail:any;
  waitData = true;
  disableApprovalBtn = false;
  bsModalRef: BsModalRef;
  showApprovedReason = false;
  messageForApprove:string="";
  responnsecame = false;
  disableSendNoteBtn = false;
  messageForDisapprove = '';
  pageMode = '';
  userEmail = '';
  availabilityError = false;
  classOptions = Constants.CLASS_OPTIONS;
  travellerName = '';
  ticketid = '';
  tripid = '';
  selectTransId = '';
  tripSessionId ='';
  startDate: Date = new Date();
  endDate: Date = new Date();
  errormsg = '';
  approvedReason = false;
  disApprovedReason = false;
  alreadyApproved = false;
  alreadyRejected = false;
  alreadyExpired = false;
  dropDownopen = [];
  error = false;
  detailList;
  flightList = [];
  cabinClass: any;
  airlineName:any;
  tagShow = [];
  tagset=[]
  selectedTag= [];
  airlines;
  airports;
  bookingStatusId;
  type = '';
  noOfPassenger = 0;
  flightLayoverInfoList: any[] = [];
  approvalTransactionDetail: any;
  isMobile1:boolean;
  queryParamSubscription: Subscription;
  deviceSubscription1: Subscription;
  flightFareChangeSubscription: Subscription;
  url: string = '/assets/test.json';
  detailsListLength: number;
  airLineName: any;
  tripDetails;
  bookingHistoryDetailResp;
  transaction;
  checkBookingResponse;
  selectedDetail: any;
  showAllReadyHandeled: boolean = false;
  isSingleDetails = true;
  indexOfExpenseItem = -1;
  constructor(private activatedRoute: ActivatedRoute,
    private adminPanelService: AdminPanelService,
    public translateService: TranslateService,
    private bookingService: BookingService,
    private modalService: BsModalService,
    private location: Location,
    public deviceDetailsService: DeviceDetailsService,
    public router: Router,
    private toastr: ToastrService,
    private titleService: Title,
    private searchResultService: SearchResultService,
    private userAccountInfoService: UserAccountService,
    public searchService: SearchService,
    private http1: HttpClient,

  ) { }
  // private bookingHistoryComponentInit() {
  //     this.fetchBookingDetails();
  // }
  ngOnInit() {
    
    this.queryParamSubscription = this.activatedRoute.queryParams.subscribe(params => {
      if(params['index']){
        this.indexOfExpenseItem = params['index'];
      }
   
    });
    this.fetchBookingDetails();
    this.modalService.onHide.subscribe(result => {
      if (this.searchService.networkErrorpopupHide) {
        this.disableSendNoteBtn = false;
        this.disableApprovalBtn = false;
        this.changingValue.next({value1:this.disableApprovalBtn});
      }
    });
    this.deviceSubscription1 = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
  }  
  changeStyle() {
    if (this.isMobile1) {
      return { 'background-color': '#f7f7f7', 'border-top': '2px solid #e3e3e3','display':'block'};
    } else {
      return { 'background-color': '#f3f3f3', 'border-top': 'none','display':'flex','align-items':'center' }
    }
  }
  isTransactionBookingConfirmed = true;
  tempSelectTag =[];
  handleResponseFromDropdwn(event){
   this.tempSelectTag=event;
  }
 
  sortList(data) {
    data.sort(function (a, b) {
      if (a.tag_name.toLowerCase() < b.tag_name.toLowerCase()) { return -1; }
      if (a.tag_name.toLowerCase() > b.tag_name.toLowerCase()) { return 1; }
      return 0;
    })
    return data;
  }

  tagsSetData = {
    "isMandotary":false,
    "tags":[]
  }

  tripSelectedToshowOnpopUp:any;
  openModal(modal,type,item){
    this.tripSelectedToshowOnpopUp=item;
    this.tripSelectedToshowOnpopUp['type']=type;
    
    this.bsModalRef = this.modalService.show(modal);
    
  }

  private fetchBookingDetails() {
    this.responnsecame = false;
    this.waitData = true;
    this.detailList = [];
    this.setgetApprovalDetail();
  }

  
  private setgetApprovalDetail(){
    this.detailList = this.approvalRequestDetailData;
    this.selectedDetail = this.approvalRequestDetailData 
    
    this.detailsListLength = this.detailList.length;
    this.responnsecame = true;
    this.errormsg ='';
    this.waitData = false;
    
    }
  carUrl(car) {
    if (car.images) {
      return car.images.S;
    }
  }
  approveed(item,modal){
    this.bsModalRef = this.modalService.show(modal);
  }
  routeToApproval(type, type11, item) {
    this.bsModalRef.hide();
this.userAccountInfoService.approvalRequestData.splice(this.indexOfExpenseItem,1);
  this.userAccountInfoService.setExpenseApprovalData(this.userAccountInfoService.approvalRequestData);
    this.router.navigate(["approvalRequest"],
    {
      queryParams:
      {
        type: 'list',
      },
      replaceUrl: true
    }
  );
  }
  getDetailsStyleModal(){
    if(this.isMobile1){
      return {'justify-content':'center','border-left':'none'}
    }else{
      return {'justify-content':'start','border-left':'2px solid #e3e3e3'}
    }
  }
 
  backToApprovalRequestList() {
      this.goBackEmitter.emit(true);
      this.router.navigate(["approvalRequest"],
    {
      queryParams:
      {
        type: 'list',
      },
      replaceUrl: true
    }
  );
   
  }
  cancelDisapproval() {
    this.showDisapprovedReason = false;
  }
  cancelApproval() {
    this.showApprovedReason = false;
  }
  getCurrencySymbol(item): string {
    if(item.displayCurrency){
      return CommonUtils.getCurrencySymbol(item.displayCurrency);
    }else{
    return CommonUtils.getCurrencySymbol(item.currency);
    }
  }
  getDisplayDate(dateString: string, format: string): string {
    return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
  }
  getDisplayFlightDate(dateString: string, format: string): string {
    return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }
  getCarDisplayDate(datestring, format) {
    return DateUtils.getFormattedDateForGivenTimeZone(datestring, format);
  }
  backToList() {
    //this.location.back();
  }

  onCancel(){
    this.bsModalRef.hide();
  }
  getTripDetails(){
    return this.tripDetails
  }
  getDetailsResponse(){
    return this.bookingHistoryDetailResp.data;
  }
  
  channgeAlignment() {
    if (this.isMobile1) {
        return { 'text-align': 'end' };
    } else {
        return { 'text-align': 'center','padding-top': '0px' };
    }
  }
  getReceipt(data){
    window.open(data.receipt);
    
  }
  backToApproval(){
    this.router.navigate(["approvalRequest"],
    {
      queryParams:
      {
        type: 'list',
      },
      replaceUrl: true
    }
  );
  }
  getTravellerName(data){
    let email = data.submitter;
  return this.searchService.getTravellerName(email);
 } 
}
