import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class TimeSliderService {


  // minTimeInHrs: frequent_flyer_number;
  // maxTimeInHrs: frequent_flyer_number;
  // minValue: frequent_flyer_number;
  // maxValue: frequent_flyer_number;
  totalSteps: number;
  maxDuration: number = 0;

  stepTimeInMins: number = 1;

  minLeftPx: number = 0;
  maxLeftPx: number = 516;
  pxPerValue: number;

  constructor() { }

  calculate() {
    this.totalSteps = this.maxDuration / this.stepTimeInMins;
    this.pxPerValue = this.maxLeftPx / this.totalSteps;
  }

  convertTimeToValue(time) {
    return time.hrs * 60 + time.mins;
  }

  convertValueToTime(value) {
    return {
      hrs: Math.floor(value / 60),
      mins: Math.floor(value % 60)
    };
  }

  public convertTimeToPx(time) {
    return time.hrs * 60 * this.pxPerValue + time.mins * this.pxPerValue;
  }

  // public convertValueToPx(value) {
  //  return (value - this.minValue) * this.pxPerValue;
  // }

  public setSliderRange(flightSearchResponse) {

    if (flightSearchResponse && flightSearchResponse.flightsLists[0]) {
      flightSearchResponse.flightsLists[0].flights.map(flight => {
        let start = new Date(flight.legs[0].flightHops[0].starts);
        let end = new Date(flight.legs[0].flightHops[flight.legs[0].flightHops.length - 1].ends);

        let duration = Math.ceil((end.getTime() - start.getTime()) / 60000);
        duration += flight.timeToAirport ? flight.timeToAirport.avgTimeInMin : 0;
        duration += flight.timeFromAirport ? flight.timeFromAirport.avgTimeInMin : 0;
        if (duration > this.maxDuration) {
          this.maxDuration = duration;
        }
      });

      this.calculate();
    }
  }
}
