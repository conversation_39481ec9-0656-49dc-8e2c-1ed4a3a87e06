import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { LookupEmployeeByEmailComponent } from './lookup-employee-by-email.component';

describe('LookupEmployeeByEmailComponent', () => {
  let component: LookupEmployeeByEmailComponent;
  let fixture: ComponentFixture<LookupEmployeeByEmailComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [LookupEmployeeByEmailComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(LookupEmployeeByEmailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
