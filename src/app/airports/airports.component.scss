@import "../../variables.scss";
$color-overlay: rgba(0, 0, 0, .7);
$dialog-position-top: 20%;
//@import "ngx-smart-modal/ngx-smart-modal";

#searchInputModal ngx-smart-modal.nsm-content {
    width: 100%;
}

#searchInputModal ngx-smart-modal.nsm-content.nsm-body {
    width: 100%;
}

.airport-search-dropdown {
    opacity: 0.5;
    padding-left: 5px;
}

.card-div11 {
    white-space: pre;
    width: 100%;
}
.airportItem:hover,
.airportItem:focus{
    background-color: rgb(229,243,252);
    width: 100%;
}
.city-child:hover,
.city-child:focus-within{
    background-color: rgb(229, 238, 252);
    width: 100%;
}
.modal-body {
    padding: 10px 10px 0px 0px;
}

.edit-icon2 {
    color: #000000 !important;
    font-size: 16px;
    cursor: pointer;
    padding-top: 0px;
    display: inline-block;
}

.airportCity {
    cursor: pointer;
    white-space: pre;
    position: relative;
    width: auto;
    padding: 0 14px 0 14px;
}

.airportCity1 {
    cursor: pointer;
    white-space: pre;
    position: relative;
    width: auto;
    padding: 0px 14px 0 14px;
}

input:focus {
    border: 1px solid var(--dark-bg-color) !important;
    border-radius: 6px !important;
}

.card-div11.active .edit-icon2 {
    -webkit-transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    transform: rotate(-180deg);
}

.support {
    float: right;
    cursor: pointer;
    padding-bottom: 10px;
}

.mobile-input:focus {
    border: none !important;
    border-radius: 0px !important;
}

.mobile-input {
    margin-left: 0px;
    border-radius: 0px !important;
    border-bottom: none !important;
    ;
    background: #fff !important;
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;

}

.filter-modal {
    width: 100%;
    float: left;
    right: 0px;
    position: absolute;
    z-index: 1000;
    background: #fff;
    border-radius: 8px;
}

.filter-modal1 {
    width: 100%;
    float: left;
    right: 0px;
    position: absolute;
    z-index: 1000;
    background: #fff;
    border-radius: 8px;
    max-height: 340px;
    overflow: scroll !important;
}

.filter-modal2 {
    width: 100%;
    float: left;
    right: 0px;
    position: absolute;
    z-index: 1000;
    background: #fff;
    border-radius: 8px;
    max-height: 340px;
    overflow: scroll !important;
}

.modalAirportFilterInfo {
    min-width: auto !important;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    overflow: auto !important;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modalAirportFilterInfo .modal-content {
    border: none;
    border-radius: 6px;
}

.modalAirportFilterInfo .close {
    text-shadow: none;
    color: #fff;
    opacity: 1;
}

.modalAirportFilterInfo .modal-header {
    background-color: var(--hyperlink-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px;
    border-bottom: none;
}

.modalAirportFilterInfo .modal-header h5 {
    font-size: 14px;
    width: 100%;
}

.modalAirportFilterInfo .modal-footer {
    padding: 0;
    border-top: none;
    float: left;
    width: 100%;
}

.modalAirportFilterInfo .close i {
    font-size: 17px;
}

.modalAirportFilterInfo .close:hover {
    color: #fff !important;
    opacity: 1 !important;
}

@media (max-width: 991px) {
    .summary-view-inner {
        flex-wrap: wrap;
    }

    .source-dest {
        float: left;
        width: 100%;
        margin-bottom: 20px;
    }

    .summary-view-middle {
        float: left;
        width: 80%;
    }

    .edit-summery-view {
        float: left;
        width: 20%;
        justify-content: flex-end;
    }

    .tab-list-item {
        margin-right: 20px;
    }

    .airportCity {
        cursor: pointer;
        white-space: pre;
        position: relative;
        width: auto;
        padding: 0 14px 0 14px;
    }

    .airportCity1 {
        cursor: pointer;
        white-space: pre;
        position: relative;
        width: auto;
        padding: 0px 14px 0 14px;
    }
}




@media (max-width: 767px) {
    .airportCity {
        cursor: pointer;
        white-space: pre;
        position: relative;
        width: 100%;
        padding: 0 4px 0 4px;
    }

    .airportCity1 {
        cursor: pointer;
        white-space: pre;
        position: relative;
        width: 100%;
        padding: 0 4px 0 4px;
    }

    .error {
        position: absolute;
        top: 45px;
    }

    .text-input {
        margin-bottom: 18px;
    }

    .modal-body {
        padding: 1rem;
    }

    .filter-modal1 {
        top: 0;
        position: fixed;
        bottom: 0 !important;
        z-index: 100;
        overflow: scroll;
        max-height: 100% !important;
    }

    .filter-modal2 {
        top: 0;
        position: fixed;
        bottom: 0 !important;
        z-index: 100;
        overflow: scroll;
        max-height: 100% !important;
    }

    .switch-book-for {
        top: -100px !important;
        display: inline-block;
        width: 100% !important;
        margin-right: 0px !important;
    }
}