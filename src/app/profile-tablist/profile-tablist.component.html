<section class="tab-container">
    <div class="main-wrapper">
        <div class="container">
            <div class="tab">
                <div class="col-lg-12">
                    <div class="tab profileTab" id="pageTab">
                        <div class="tab-list top-strip">
                            <ul>
                                <li class="tab-list-item active" data-tab="1" attr.data-track="ProfilePageTab"
                                    attr.data-params="tab=Travel Profile" (click)="activeTab('1')" onclick="activeTab(this)"
                                    (click)="this.userAccountInfoService.slackTab =''"><a href="javascript:void(0);"
                                        attr.data-track="ProfilePageTab"
                                        attr.data-params="tab=Travel Profile">{{'profileTablist.TravelProfile' |
                                        translate}}</a></li>
                                <li class="tab-list-item" data-tab="2" attr.data-track="ProfilePageTab"
                                    attr.data-params="tab=Loyality numbers" (click)="activeTab('2')" onclick="activeTab(this)"
                                    (click)="this.userAccountInfoService.slackTab =''"><a href="javascript:void(0);"
                                        attr.data-track="ProfilePageTab"
                                        attr.data-params="tab=Loyality numbers">{{'profileTablist.Loyaltynumbers' |
                                        translate}}</a></li>
                                <li class="tab-list-item" data-tab="3" attr.data-track="ProfilePageTab"
                                    attr.data-params="tab=preference" (click)="activeTab('3')" onclick="activeTab(this)"
                                    (click)="this.userAccountInfoService.slackTab =''"><a href="javascript:void(0);"
                                        attr.data-track="ProfilePageTab"
                                        attr.data-params="tab=preference">{{'profileTablist.Preferences' |
                                        translate}}</a></li>
                                <li class="tab-list-item" data-tab="4" id="account" attr.data-track="ProfilePageTab"
                                    attr.data-params="tab=account" (click)="activeTab('4')" onclick="activeTab(this)"
                                    (click)="accountTab();this.userAccountInfoService.slackTab =''"><a
                                        href="javascript:void(0);" attr.data-track="ProfilePageTab"
                                        attr.data-params="tab=account">{{'profileTablist.Account' | translate}}</a></li>
                                <!--    <li class="tab-list-item" data-tab="7" id="slack" attr.data-track="ProfilePageTab" attr.data-params="tab=addToSlack"  ><a href="javascript:void(0);" attr.data-track="ProfilePageTab" attr.data-params="tab=addToSlack">{{this.userAccountInfoService.slackTab}}</a></li>
                            <li *ngIf="!amnetSpecificRelease && !njoySpecificBuild" class="tab-list-item" data-tab="7" attr.data-track="ProfilePageTab" attr.data-params="tab=faqs" onclick="activeTab(this);" (click)="getFaqCompo()" style="color:#FFFFFF !important;"><a ref="javascript:void(0);" attr.data-track="ProfilePageTab" attr.data-params="tab=faqs">{{'profileTablist.FAQs' | translate}}</a></li>-->

                                <!--  <li class="tab-list-item" data-tab="5" onclick="activeTab(this);"><a href="javascript:void(0);">Add travelers</a></li>
                                <li class="tab-list-item" data-tab="6" onclick="activeTab(this);"><a href="javascript:void(0);">Help</a></li> -->
                            </ul>
                        </div>
                        <div class="tab-content">
                            <div class="{{viewMode==='1' ? 'tab-content-item active':'tab-content-item'}}" id="1">
                                <app-profile-personal-info></app-profile-personal-info>
                            </div>
                            <div class="{{viewMode==='2' ? 'tab-content-item active':'tab-content-item'}}" id="2">
                                <app-profile-loyalty-numbers></app-profile-loyalty-numbers>
                            </div>
                            <div class="{{viewMode==='3' ? 'tab-content-item active':'tab-content-item'}}"id="3">
                                <app-profile-preferences></app-profile-preferences>
                            </div>
                            <div class="{{viewMode==='4' ? 'tab-content-item active':'tab-content-item'}}" id="4">
                                <app-profile-account-settings></app-profile-account-settings>
                            </div>
                            <!--   <div class="tab-content-item "  id="7">
                                <app-faq *ngIf="viewMode=== 'tab7'"></app-faq>
                            </div>-->
                            <div class="tab-content-item" id="6">
                                <app-profile-help></app-profile-help>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>