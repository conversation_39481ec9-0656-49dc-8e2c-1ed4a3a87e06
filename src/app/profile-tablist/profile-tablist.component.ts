import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { environment } from 'src/environments/environment';
import { UserAccountService } from '../user-account.service';
import { SearchService } from '../search.service';
import { ProfileAccountSettingsComponent } from '../profile-account-settings/profile-account-settings.component';
import { CommonUtils } from '../util/common-utils';
import { Location } from '@angular/common';
import { GallopHttpClient } from '../shared/gallop-httpclient.service';
import { ToastrService } from 'ngx-toastr';
import { Subscriber, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
declare var activeTab: any;
declare var openCardEdit: any;
declare var parseUrlParam: any;
@Component({
    selector: 'app-profile-tablist',
    templateUrl: './profile-tablist.component.html',
    styleUrls: ['./profile-tablist.component.scss'],
    standalone: false
})
export class ProfileTablistComponent implements OnInit {
  viewMode = '1';
  @ViewChild(ProfileAccountSettingsComponent) accounntTabChild: ProfileAccountSettingsComponent;
  amnetSpecificRelease: boolean = environment.amnetSpecificRelease;
  njoySpecificBuild: boolean;
  redirectSubscription: Subscription;
  constructor(public translateService: TranslateService,private toastr: ToastrService, private userAccountService: UserAccountService, private location: Location, private el: ElementRef, private http: GallopHttpClient,
    public userAccountInfoService: UserAccountService, private searchService: SearchService,) { }

  ngOnInit() {

    if (this.isSlackconnectRequested()) {
      var params = parseUrlParam();
      var code = params["code"];
      var state = params["state"];
      this.redirectSubscription = this.userAccountInfoService.redirectFromSlack(code, state).subscribe(res => {
        // 
        if (res && res.success) {
          this.userAccountInfoService.connectingToSlack = true;
          CommonUtils.checkSlackStatusFromRemote(this.http, this.userAccountInfoService, environment.apiForSlackStatusCheck);
          this.handleFromSlackTab(true);
          this.toastr.success(this.translateService.instant('search.Slackconnectedsuccessfully.'));
        } else {
          this.handleFromSlackTab(true);
          this.toastr.error(res.error_message);
          this.userAccountInfoService.connectingToSlack = false;
        }

      })
      this.location.replaceState('profile');
    }
    this.userAccountInfoService.userAccountInitObjObserver$.subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.userAccountInfoService.initResponseReceived = true;
        if (this.userAccountInfoService.userIsDeptAdmin) {
          if (this.userAccountInfoService.onBoardingTask.indexOf('profile') === -1) {
            let params = "?ua_action=OnboardingTasks&ua_item=profile";
            this.searchService.letsTrack(params);
          }
        }
      }
    });
    this.njoySpecificBuild = this.userAccountService.isItNjoyBuild();
  }
  ngOnDestroy() {
    if (this.redirectSubscription) {
      this.redirectSubscription.unsubscribe();
    }
  }
  activeTab(id){
 this.viewMode =id;
  }
  handleFromSlackTab(event) {
    if (event) {
      let obj = document.getElementById("account");
      activeTab(obj);
    }
    setTimeout(() => {
      let obj = document.getElementById("openSlackcardDiv");
      openCardEdit(obj);
      this.accounntTabChild.visibilitychange();
      this.scrollToFirstInvalidControl();
    }, 100);
  }
  scrollTo(el: Element): void {
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }
  accountTab() {
    // setTimeout(() => {
    this.accounntTabChild.statusCheck();
    // },200);
  }
  private scrollToFirstInvalidControl() {
    const firstInvalidControl: HTMLElement = document.getElementById("openSlackcardDiv");
    this.scrollTo(firstInvalidControl);
  }

  isSlackconnectRequested() {
    // );
    return this.userAccountService.getCurrentRouterPath().indexOf('slack_callback') === 1;
  }

}
