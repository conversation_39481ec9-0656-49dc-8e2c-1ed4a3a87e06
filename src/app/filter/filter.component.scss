@import "../../variables.scss";

.filters {
    display: flex;
    justify-content: space-between;
    padding: 17px;
    border: 1px solid $border-color;
    border-radius: 6px;
}

.filters-container {
    display: flex;
}

.server-filter,
.client-filter {
    padding: 0 17px;
    border: 1px solid #EDEDEB;
    border-radius: 6px;
    justify-content: space-between;
    display: flex;
}

.server-filter {
    flex: 1;
    margin-right: 20px;

    .select-input {
        padding: 0 17px;
    }
}

.client-filter {
    flex: 2;
}

.select-input .label {
    color: $primary-text-color;
}

.score-filter {
    flex: 1;
    text-align: center;
    padding: 17px;
    cursor: pointer;
    border-right: 1px solid $border-color;

    &.active {
        color: $accent-color;
    }

    &:last-child {
        border-right: none;
    }
}

.score-filters {
    display: flex;
    justify-content: space-between;
    border: 1px solid $border-color;
    border-radius: 6px;
    align-items: center;
}

.switch {
    margin: 5px 0;

    .icon {
        height: 32px;
        width: 32px;
        display: inline-block;
        text-align: center;
        line-height: 32px;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
        // background-color: $background-light;

        color: $icon-color;

        &:first-child {
            border-radius: 6px 0 0 6px;
        }

        &:last-child {
            border-radius: 0 6px 6px 0;
        }

        &.selected {
            background: $accent-color;
            color: #fff;
        }
    }
}


/*new css for new design*/

.filter-strip {
    float: left;
    width: 100%;
    background: $themeColor2;
    padding: 0 15px;
}

.filter-strip-inner {
    float: left;
    width: 100%;
    height: 43px;
}

.filters-container {
    float: left;
    width: 100%;
}

.filter-item {
    display: inline-block;
    vertical-align: middle;
    padding: 9px 20px;
    position: relative;
}

.filter-item:first-child {
    padding-left: 0;
}

.filter-item:last-child {
    padding-right: 0;
}

.filter-select-box {
    visibility: hidden;
    z-index: 0;
    position: absolute;
}

.filter-item-link {
    position: relative;
    display: inline-block;
    color: #fff;
    font-size: 16px;
    letter-spacing: 0.69px;
    font-family: $fontRegular;
}

.filter-select-box.ng-select-opened+a:after {
    position: absolute;
    bottom: -6px;
    left: 0;
    right: 0;
    height: 3px;
    background: #fff;
    content: '';
}

.policy-link-container {
    float: left;
    width: 100%;
    padding: 6px 30px 24px 30px;
}

.policy-link-container a {
    font-size: 12px;
    letter-spacing: 1px;
    line-height: 18px;
    color: var(--button-font-color);
    font-family: $fontBold;
    text-transform: uppercase;
}

.policy-link-container a span {
    display: inline-block;
    vertical-align: middle;
}

.policy-link-container a img {
    display: inline-block;
    vertical-align: middle;
    margin-left: 9px;
    margin-top: 2px;
}

.mdl-checkbox {
    height: auto;
}

.mdl-checkbox__input {
    position: absolute;
    left: -9999px;
}

.mdl-checkbox__label {
    padding-left: 30px;
    position: relative;
    display: inline-block;
}

.mdl-checkbox__label:before,
.mdl-checkbox__label:after {
    height: 15px;
    width: 15px;
    position: absolute;
    left: 0;
    content: '';
    top: 0;
}

.mdl-checkbox__label:before {
    border: 1px solid #aeaeae;
    border-radius: 4px;
    background-color: #F7F7F7;
}

.mdl-checkbox__input:checked+.mdl-checkbox__label:after {
    background-image: url(/../../assets/images/check-icon.png);
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center center;
}

.mdl-checkbox__label {
    font-size: 12px;
    line-height: normal;
    font-family: $fontMono;
}

.checkbox-group-items .mdl-checkbox {
    width: 100%;
}


.mdl-radio {
    height: auto;
}

.mdl-radio__button {
    position: absolute;
    left: -9999px;
}

.mdl-radio__label {
    padding-left: 30px;
    position: relative;
    display: inline-block;
    white-space: break-spaces;
}

.mdl-radio__label:before,
.mdl-radio__label:after {
    position: absolute;
    content: '';
}

.mdl-radio__label:before {
    border: 1px solid #aeaeae;
    border-radius: 50%;
    background-color: #fff;
    left: 0;
    top: 0;
    height: 18px;
    width: 18px;
}

.mdl-radio__button:checked+.mdl-radio__label:after {
    height: 12px;
    width: 12px;
    top: 3px;
    left: 3px;
    border-radius: 50%;
    background: linear-gradient(180deg, #3CC8FA 0%, #1C97F3 100%);
    background-position: center center;
}

.mdl-radio__label {
    font-size: 12px;
    line-height: normal;
    font-family: $fontMono;
}


.selectBox-footer-filter-button {
    height: 88px;
    background-color: #EFFAFC;
    float: left;
    width: 100%;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 29px;
    border-radius: 6px;
}

.selectBox-footer-filter-button button {
    width: 100%;
    height: 46px;
    font-size: 12px;
    letter-spacing: 1px;
}

.checkbox-group-container {
    margin-bottom: 22px;
    padding: 0 16px;
}

.checkbox-group-header {
    font-size: 14px;
    line-height: 17px;
    color: #000000;
    padding: 5px 0 10px 0;
}

.flightTimeOptionDiv {
    float: left;
    width: 100%;
}

.filter-modal {
    width: 100%;
    float: left;
    position: absolute;
    left: 0;
    background: #fff;
    z-index: 10;
    border-radius: 8px;
    top: 100%;
}


.modalAirportFilterInfo {
    min-width: 324px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modalAirportFilterInfo .modal-content {
    border: none;
    border-radius: 6px;
}

.modalAirportFilterInfo .close {
    text-shadow: none;
    color: #fff;
    opacity: 1;
}

.modalAirportFilterInfo .modal-header {
    background-color: var(--hyperlink-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px !important;
    border-bottom: none;
}

.modalAirportFilterInfo .modal-header h5 {
    font-size: 14px;
    width: 100%;
}

.modalAirportFilterInfo .modal-footer {
    padding: 0;
    border-top: none;
    float: left;
    width: 100%;
    position: relative;
}

.modalAirportFilterInfo .close i {
    font-size: 17px;
}

.modalAirportFilterInfo .close:hover {
    color: #fff !important;
    opacity: 1 !important;
}





@media (max-width: 767px) {
    .filter-strip {
        position: relative;
    }
    .mdl-radio__label {
        padding-left: 30px;
        position: relative;
        display: inline-block;
        white-space: break-spaces;
        max-width: 300px;
    }
    .filter-strip-inner {
        height: 48px;
    }

    .filter-item {
        padding: 12px 7px;
    }

    .filter-item-link {
        color: #fff;
        font-size: 12px;
        letter-spacing: 0.51px;
    }

    .filter-item {
        position: static;
    }

    .modalAirportFilterInfo .modal-header {
        background-color: #fff;
        color: #000;
        font-size: 14px;
        padding: 16px 8px 2px 22px !important;
    }

    .modalAirportFilterInfo .modal-header h5 {
        font-weight: normal;
        font-size: 14px;
    }

    .modalAirportFilterInfo .close {
        margin: -4px 4px 0 0;
        padding: 0;
    }

    .modalAirportFilterInfo .close i {
        font-size: 24px;
        color: #ADADAD;
    }
    .flight_duration_filter{
        height: 92vh !important;
    }

}

@media (max-width: 575.98px) {
    .filter-list-container {
        max-width: 100%;
        overflow-x: auto;
        height: 55px;
        overflow-y: hidden;
    }

    .filter-list {
        font-size: 10px;
        font-weight: 600;
        width: 390px;

        &.single-line li {
            line-height: 35px;
        }

        li {
            display: inline-block;
            padding: 4px;
            margin: 0 12px;
            cursor: pointer;
            text-align: center;

            &.selected {
                color: $accent-color;
            }

            .select-input {
                .label {
                    margin: 0 auto;
                }
            }
        }
    }
}

@media (max-width: 991.98px) {
    .filters {
        margin-bottom: 10px;
    }
}

@media(max-width:600px) {
    .filter-strip-inner {
        overflow-x: scroll !important;
        overflow-y: hidden !important;
    }
    .filter-strip-inner ul {
        overflow: scroll !important;
        width: fit-content !important;
        min-width: 125% !important;
        display: flex;
    }
    .filter-strip-inner ul .filter-item{
      white-space: nowrap;
    }    .filter-strip-inner>.filter-list-holder {
        overflow: scroll !important;
        width: fit-content !important;
        min-width: 125% !important;
        display: flex;
    }
    
    .filter-strip-inner>.filter-list-holder .filter-item{
      white-space: nowrap;
    }
    
    .filter-strip-inner>.filter-list-holder .modal-container{
      white-space: wrap;
    }
}

@media (max-width: 359px) {
    .filter-item {
        padding: 12px 5px;
    }
}