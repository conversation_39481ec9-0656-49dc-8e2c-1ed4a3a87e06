@import "../../variables.scss";

.filter-modal {
  width: 100%;
  position: relative;
  z-index: 1000;
  background: #fff;
  border-radius: 8px;
}
.icon_img_div{
  width:40px;
  display: inline-block;
}
.icon_label_div{
  display: inline-block;
  position: relative;
  top: 5px;
}
.modalAirportFilterInfo {
  min-width: 220px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modalAirportFilterInfo .modal-content {
  border: none;
  border-radius: 6px;
}

.modalAirportFilterInfo .close {
  text-shadow: none;
  color: #fff;
  opacity: 1;
}

.modalAirportFilterInfo .modal-header {
  background-color: var(--hyperlink-color);
  color: #FFFFFF;
  font-size: 14px;
  height: 40px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 6px 6px 0 0;
  padding: 0 8px 0 22px !important;
  border-bottom: none;
}

.modalAirportFilterInfo .modal-header h5 {
  font-size: 14px;
  width: 100%;
}

.modalAirportFilterInfo .modal-footer {
  padding: 0;
  border-top: none;
  float: left;
  width: 100%;
}

.modalAirportFilterInfo .close i {
  font-size: 17px;
}

.modalAirportFilterInfo .close:hover {
  color: #fff !important;
  opacity: 1 !important;
}

.trends {
  margin-left: 10px;
  font-size: 18px;
  font-family: $fontRegular;
  cursor: pointer;
  font-weight: bold;
  color: black;
}

.calendar {
  margin-bottom: 10px;
  text-align: left !important;
}
.calendar2{
  margin-bottom: 20px;
}
.calendar2 .trends{
  cursor: default !important;
}
.image {
  padding-right: 15px;
  margin-right: 5px;
  width: auto;
}

.modal-body {
  padding: 28px 25px 15px 25px !important;
  border-radius: 0 0 5px 5px;
}




.links{
  height: 0px;
  opacity: 0;
}
.calendar_link{
  margin-bottom: 30px;
}
.calendar_link .links{
  height: 100%;
  opacity: 1;
  transition: height 5s;

}
.calendar_link .details_link{
  margin: 30px 0px;
  margin-left: 10px;
  color: black;
}
.details_link a{
  display: flex;
}
.details_link .img-container{
  margin-right:10px
}
.details_link:hover .img-container{
  margin-right:20px;
  transition: margin 0.5s;
}

@media (max-width: 499px) {
  .details_link .img-container {
    width: 40px;
  }
}