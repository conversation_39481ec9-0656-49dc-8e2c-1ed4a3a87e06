.checkHeight {
  height: 70px !important;
}

.loaderClass {
  display: flex;
  margin-top: 0px;
}

.loaderAlign {
  position: relative;
  top: 2px;
}

.mdl-checkbox__label-text {
  float: left;
  width: 100%;
  text-align: center;
  word-break: nowrap !important;
}

:host ::ng-deep {

  .ng-select.ng-select-single .ng-select-container .ng-value-container,
  .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
    overflow: visible !important;
    position: relative !important;
    top: 0px !important;
  }

}

@media (max-width: 767px) {

  .mdl-checkbox__label,
  .mdl-radio__label {
    font-size: 8px !important;
    line-height: 15px;
  }

  .loaderAlign {
    position: relative;
    top: -5px;

  }

  .loaderClass {
    display: flex;
    margin-top: 10px;
  }
}

.card-div-inner {
  width: 100% !important;
}

@media(max-width:768px) {

  .airlineCheckboxContainer,
  .hotelCheckboxContainer {
    width: auto;
  }
}