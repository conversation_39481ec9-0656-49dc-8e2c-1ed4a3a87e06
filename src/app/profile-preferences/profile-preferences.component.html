<div class="card-div active shadow" id="frequentFlyerCard">
    <div class="card-div-inner">
        <div class="traveller-form">
            <div class="card-div-header" onclick="openCardEdit(this);">
                <h3>{{'profilePreference.PreferredAirlines' | translate}}</h3>
                <div class="card-edit">
                    <span class="edit-text">Edit</span>
                    <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                </div>
                <div class="traveller-short-info">
                    <div class="traveller-short-info-inner">{{getAirlineString()}}</div>
                </div>
            </div>
            <div class="card-div-body">
                <form method="post" id="airlinePreferenceForm" class="airlinePreferenceForm"
                    [formGroup]="airlinePreferenceForm">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="input-box">
                                <label
                                    class="input-label primary-label">{{'profilePreference.Doyouhaveanypreferredairlines'
                                    | translate}}</label>
                                <div class="airlineCheckboxContainer flexContainer checkbox-radio-container">
                                    <div formArrayName="prefAirlines"
                                        *ngFor="let airline of highlightedAirlines; let i = index;" class="airline-div"
                                        data-type="type">
                                        <label
                                            class="mdl-checkbox mdl-js-checkbox selection-checkbox mdl-js-ripple-effect"
                                            [ngClass]="{'is-checked':isPreferredAirline(airline)}"
                                            [ngClass]="airline === 'ANY'?'any':''" for="{{airline}}">
                                            <input style="display:none;" [formControlName]="i"
                                                (change)="togglePreferredAirline(airline);" value="{{airline}}"
                                                data-value="{{airline}}" type="checkbox" id="{{airline}}"
                                                [checked]="isPreferredAirline(airline)" name="prefferedAirlines"
                                                class="mdl-checkbox__input">
                                            <span class="mdl-checkbox__label">
                                                <span class="mdl-checkbox__label-img">
                                                    <img onerror="this.onerror = null; this.src = 'https://s3.amazonaws.com/images.biztravel.ai/template/default.gif';"
                                                        src="{{getAirlineImageUrl(airline)}}" alt="{{airline}}" />
                                                </span>
                                                <span
                                                    class="mdl-checkbox__label-text">{{getAirlineName(airline)}}</span>
                                            </span>
                                        </label>
                                    </div>
                                    <div class="airline-div" data-type="type">
                                        <label
                                            class="mdl-checkbox mdl-js-checkbox selection-checkbox mdl-js-ripple-effect any"
                                            [ngClass]="{'is-checked':!(userPreference && userPreference.preferredAirlines && userPreference.preferredAirlines.length > 0)}"
                                            for="ANY">
                                            <input formControlName="selectAny" (change)="toggleAnyAirline();"
                                                value="ANY"
                                                [checked]="!(userPreference && userPreference.preferredAirlines && userPreference.preferredAirlines.length > 0)"
                                                data-value="ANY" type="checkbox" id="ANY" name="prefferedAirlines"
                                                class="mdl-checkbox__input">
                                            <span class="mdl-checkbox__label">
                                                <span class="mdl-checkbox__label-img">
                                                    <img class="selected" src="assets/images/airlines/ANY.png" />
                                                    <img class="notSelected" src="assets/images/airlines/ANY_NS.png" />
                                                </span>
                                                <span class="mdl-checkbox__label-text">{{'profilePreference.AnyAirline'
                                                    | translate}}</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="input-box searchContainer select-dropdown searchable-dropdown"
                                    id="searchContainer">
                                    <div id="airlinesBack" class="airlinesBack" onclick="inactiveIcon(this);">
                                        <img src="assets/images/arrow-left.png" alt="" />
                                    </div>
                                    <!-- <input class="input-textfield airlineSearch fullBox" id="airlineSearch" name="airlineSearch" type="text" placeholder="Search other airlines" onfocus="activeIcon(this);" onblur="activeIcon(this);" /> -->
                                    <ng-select formControlName="selectedAirline"
                                        class="input-textfield airlineSearch fullBox" id="airlineSearch"
                                        name="airlineSearch" (change)="onAirlineChange()" [searchable]="true"
                                        [clearable]="false" [items]="selectableAirlines |  translateOptions" bindLabel="value"
                                        bindValue="id"></ng-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"
                                    (click)="saveUserPrefererences()">{{'profilePreference.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"><span
                                        class="loaderClass">{{'profilePreference.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="card-div">
    <div class="card-div-inner">
        <div class="traveller-form">
            <div class="card-div-header" onclick="openCardEdit(this);">
                <h3>{{'profilePreference.PreferredClass' | translate}}</h3>
                <div class="card-edit">
                    <span class="edit-text">Edit</span>
                    <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                </div>
                <div class="traveller-short-info">
                    <div class="traveller-short-info-inner">{{getClassString()}}</div>
                </div>
            </div>
            <div class="card-div-body" style="display: none;">
                <form method="post" id="classPreferenceForm" class="classPreferenceForm"
                    [formGroup]="classPreferenceForm">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="input-box">
                                <label class="input-label primary-label">{{'profilePreference.Whatisyourpreferredclass'
                                    | translate}}</label>
                                <div class="flexContainer checkbox-radio-container classSelectionContainer">
                                    <label formArrayName="prefFlightClasses"
                                        *ngFor="let fclass of preDefinedFlightClasses; let i = index;"
                                        class="mdl-checkbox mdl-js-checkbox selection-checkbox checkbox2 mdl-js-ripple-effect"
                                        [ngClass]="{'is-checked': isPreferredFlightClass(fclass)}" for="{{fclass}}">
                                        <input [formControlName]="i" data-value="{{fclass}}" value="{{fclass}}"
                                            type="checkbox" id="{{fclass}}" (change)="toggleFlightClass(fclass)"
                                            name="prefferedClass" class="mdl-checkbox__input">
                                        <span class="mdl-checkbox__label">{{getFlightClassText(fclass)}}</span>
                                    </label>
                                    <!-- <label class="mdl-checkbox mdl-js-checkbox selection-checkbox checkbox2 mdl-js-ripple-effect" for="premiumEconomy">
                                        <input data-value="Premium Economy" value="PREMIUM_ECONOMY" type="checkbox" id="premiumEconomy" name="prefferedClass" class="mdl-checkbox__input">
                                        <span class="mdl-checkbox__label">Premium Economy</span>
                                    </label>
                                    <label class="mdl-checkbox mdl-js-checkbox selection-checkbox checkbox2 mdl-js-ripple-effect" for="business">
                                        <input data-value="Business" value="BUSINESS" type="checkbox" id="business" name="prefferedClass" class="mdl-checkbox__input">
                                        <span class="mdl-checkbox__label">Business</span>
                                    </label>
                                    <label class="mdl-checkbox mdl-js-checkbox selection-checkbox checkbox2 mdl-js-ripple-effect" for="firstClass">
                                        <input data-value="First" value="FIRST_CLASS" type="checkbox" id="firstClass" name="prefferedClass" class="mdl-checkbox__input">
                                        <span class="mdl-checkbox__label">First</span>
                                    </label> -->
                                </div>
                            </div>
                        </div>
                        <div *ngIf="isEconomyPreferred()"
                            class="col-lg-12 col-md-12 col-sm-12 basicEconomyRadioButtons">
                            <div class="input-box">
                                <label
                                    class="input-label primary-label">{{'profilePreference.Doyoupreferflyingonthebasiceconomywhenavailable'
                                    | translate}}</label>
                                <div class="checkbox-radio-container">
                                    <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="economyYes">
                                        <input formControlName="basicEconomy" (click)="toggleBasicEconomyClass('YES')"
                                            type="radio" id="economyYes" class="mdl-radio__button" value="YES">
                                        <span class="mdl-radio__label">{{'profilePreference.Yes' | translate}}</span>
                                    </label>
                                    <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="economyNo">
                                        <input formControlName="basicEconomy" (click)="toggleBasicEconomyClass('NO')"
                                            type="radio" id="economyNo" class="mdl-radio__button" value="NO">
                                        <span class="mdl-radio__label">{{'profilePreference.No' | translate}}</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="isEconomyPreferred() && isBasicEconomyPreferred()"
                            class="col-lg-12 col-md-12 col-sm-12">
                            <div class="basic-economy-warning">
                                <div class="basic-economy-warning-inner">
                                    {{'profilePreference.Basiceconomyisarestrictiveclasswhichmaynotprovideadvanceseatselectionoranyupgrades'
                                    | translate}}
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"
                                    (click)="saveUserPrefererences()">{{'profilePreference.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profilePreference.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="card-div">
    <div class="card-div-inner">
        <div class="traveller-form">
            <div class="card-div-header" onclick="openCardEdit(this);">
                <h3>{{'profilePreference.PreferredSeat' | translate}}</h3>
                <div class="card-edit">
                    <span class="edit-text">Edit</span>
                    <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                </div>
                <div class="traveller-short-info">
                    <div class="traveller-short-info-inner">{{getSeatString()}}</div>
                </div>
            </div>
            <div class="card-div-body" style="display: none;">
                <form method="post" id="seatPreferenceForm" class="seatPreferenceForm" [formGroup]="seatPreferenceForm">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="input-box">
                                <label class="input-label primary-label">{{'profilePreference.Whatisyourpreferredseat' |
                                    translate}}</label>
                                <div class="flexContainer seatselectionContainer checkbox-radio-container">

                                    <div class="airline-div">
                                        <label
                                            class="mdl-radio seat-selection mdl-js-radio selection-checkbox mdl-js-ripple-effect"
                                            [ngClass]="{'is-checked': (getSeatPreference() == '1')}" for="aisle">
                                            <input formControlName="seatPreference" (click)="setSeatPreference('1')"
                                                data-value="1" value="1" type="radio" id="aisle"
                                                class="mdl-radio__button">
                                            <span class="mdl-radio__label">
                                                <span class="mdl-checkbox__label-img">
                                                    <img class="inactive" src="assets/images/aisle-seat.png"
                                                        alt="aisle" />
                                                    <img class="active" src="assets/images/aisle-seat-selected.png"
                                                        alt="aisle" />
                                                </span>
                                                <span class="mdl-checkbox__label-text">{{'profilePreference.Aisle' |
                                                    translate}}</span>
                                            </span>
                                        </label>
                                    </div>
                                    <div class="airline-div">
                                        <label
                                            class="mdl-radio seat-selection mdl-js-radio selection-checkbox mdl-js-ripple-effect"
                                            [ngClass]="{'is-checked': (getSeatPreference() == '2')}" for="window">
                                            <input formControlName="seatPreference" (click)="setSeatPreference('2')"
                                                data-value="2" value="2" type="radio" id="window"
                                                class="mdl-radio__button">
                                            <span class="mdl-radio__label">
                                                <span class="mdl-checkbox__label-img">
                                                    <img class="inactive" src="assets/images/window-seat.png"
                                                        alt="window" />
                                                    <img class="active" src="assets/images/window-seat-selected.png"
                                                        alt="window" />
                                                </span>
                                                <span class="mdl-checkbox__label-text">{{'profilePreference.Window' |
                                                    translate}}</span>
                                            </span>
                                        </label>
                                    </div>
                                    <div class="airline-div">
                                        <label
                                            class="mdl-radio seat-selection mdl-js-radio selection-checkbox mdl-js-ripple-effect"
                                            [ngClass]="{'is-checked': (getSeatPreference() == '3')}"
                                            for="noPreferenceSeat">
                                            <input formControlName="seatPreference" (click)="setSeatPreference('3')"
                                                data-value="3" value="3" type="radio" id="noPreferenceSeat"
                                                class="mdl-radio__button">
                                            <span class="mdl-radio__label">
                                                <span class="mdl-checkbox__label-img">
                                                    <img class="inactive" src="assets/images/no-preferences.png"
                                                        alt="noPreferenceSeat" />
                                                    <img class="active" src="assets/images/no-preferences-selected.png"
                                                        alt="noPreferenceSeat" />
                                                </span>
                                                <span
                                                    class="mdl-checkbox__label-text">{{'profilePreference.NoPreference'
                                                    | translate}}</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"
                                    (click)="saveUserPrefererences()">{{'profilePreference.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profilePreference.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="card-div">
    <div class="card-div-inner">
        <div class="traveller-form">
            <div class="card-div-header" onclick="openCardEdit(this);">
                <h3>{{'profilePreference.PreferredHomeAirport' | translate}}</h3>
                <div class="card-edit">
                    <span class="edit-text">Edit</span>
                    <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                </div>
                <div class="traveller-short-info">
                    <div class="traveller-short-info-inner">{{getHomeAirport()}}</div>
                </div>
            </div>
            <div class="card-div-body" style="display: none;">
                <form method="post" id="airportSelectionForm" class="airportSelectionForm">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="input-box searchContainer addressContainer">
                                <label class="input-label primary-label">{{'profilePreference.Whatsyourhomeaddress' |
                                    translate}}</label>
                                <div class="airlinesBack" onclick="inactiveIcon(this);">
                                    <img src="assets/images/arrow-left.png" alt="" />
                                </div>
                                <input class="input-textfield googleAddress fullBox" name="mapAddress" id="mapAddress"
                                    placeholder="{{'profilePreference.Address' | translate}}" type="text">
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="input-box">
                                <div id="map" class="map"></div>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="preffered-home-airport hidden">
                                <div class="input-box">
                                    <label
                                        class="input-label primary-label">{{'profilePreference.Whatsyourpreferredhomeairport'
                                        | translate}}</label>
                                    <div class="flexContainer homeAirportContainer">

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"
                                    (click)="saveUserPrefererences()">{{'profilePreference.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profilePreference.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="card-div" id="frequentFlyerCard">
    <div class="card-div-inner">
        <div class="traveller-form">
            <div class="card-div-header" onclick="openCardEdit(this);">
                <h3>{{'profilePreference.PreferredHotel' | translate}}</h3>
                <div class="card-edit">
                    <span class="edit-text">Edit</span>
                    <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                </div>
                <div class="traveller-short-info">
                    <div class="traveller-short-info-inner">{{getHotelString()}}</div>
                </div>
            </div>
            <div class="card-div-body" style="display: none;">
                <form method="post" id="hotelPreferenceForm" class="hotelPreferenceForm"
                    [formGroup]="hotelPreferenceForm">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="input-box">
                                <label
                                    class="input-label primary-label">{{'profilePreference.Whatisyourfavoritehotelbrand'
                                    | translate}}</label>
                                <div class="hotelCheckboxContainer flexContainer checkbox-radio-container">
                                    <div formArrayName="prefHotelBrands"
                                        *ngFor="let brand of preDefinedHotelBrands; let i = index;" class="airline-div">
                                        <label
                                            class="mdl-checkbox mdl-js-checkbox selection-checkbox mdl-js-ripple-effect"
                                            [ngClass]="{'is-checked':isPreferredHotelBrand(brand.value)}"
                                            for="brand{{brand.value}}">
                                            <input (change)="togglePreferredHotelBrand(brand.value);"
                                                [formControlName]="i" value="{{brand.value}}"
                                                data-value="{{brand.label}}" type="checkbox" id="brand{{brand.value}}"
                                                class="mdl-checkbox__input">
                                            <span class="mdl-checkbox__label">
                                                <span class="mdl-checkbox__label-img">
                                                    <img class="d-none d-md-block"
                                                        src="assets/images/hotels/{{brand.value}}.png" alt="objvalue" />
                                                    <img class="d-block d-md-none"
                                                        src="assets/images/hotels/{{brand.value}}_xs.png"
                                                        alt="objvalue" />
                                                </span>
                                                <span class="mdl-checkbox__label-text">{{brand.label}}</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="input-box">
                                <label
                                    class="input-label primary-label">{{'profilePreference.Whatsyourpreferredhotelclass'
                                    | translate}}</label>
                                <div class="flexContainer checkbox-radio-container hotelRatingContainer">
                                    <label
                                        class="mdl-radio mdl-js-radio selection-checkbox checkbox2 mdl-js-ripple-effect"
                                        [ngClass]="{'is-checked': (getPreferredHotelRating()=='3')}" for="3star">
                                        <input (click)="setHotelRatingPref('3')" formControlName="hotelRating"
                                            data-value="3 star" value="3" type="radio" id="3star"
                                            [checked]="getPreferredHotelRating()=='3'" name="hotelRating"
                                            class="mdl-radio__button">
                                        <span class="mdl-checkbox__label">{{'profilePreference.3star' |
                                            translate}}</span>
                                    </label>
                                    <label
                                        class="mdl-radio mdl-js-radio selection-checkbox checkbox2 mdl-js-ripple-effect"
                                        [ngClass]="{'is-checked': (getPreferredHotelRating()=='4')}" for="4star">
                                        <input (click)="setHotelRatingPref('4')" formControlName="hotelRating"
                                            data-value="4 star" value="4" type="radio" id="4star"
                                            [checked]="getPreferredHotelRating()=='4'" name="hotelRating"
                                            class="mdl-radio__button">
                                        <span class="mdl-checkbox__label">{{'profilePreference.4star' |
                                            translate}}</span>
                                    </label>
                                    <label
                                        class="mdl-radio mdl-js-radio selection-checkbox checkbox2 mdl-js-ripple-effect"
                                        [ngClass]="{'is-checked': (getPreferredHotelRating()=='5')}" for="5star">
                                        <input (click)="setHotelRatingPref('5')" formControlName="hotelRating"
                                            data-value="5 star" value="5" type="radio" id="5star"
                                            [checked]="getPreferredHotelRating()=='5'" name="hotelRating"
                                            class="mdl-radio__button">
                                        <span class="mdl-checkbox__label">{{'profilePreference.5star' |
                                            translate}}</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="input-box">
                                <label
                                    class="input-label primary-label">I am eligible for below discount/membership rates.</label>
                                <div class="flexContainer checkbox-radio-container hotelRatingContainer">
                                    <label class = "mdl-checkbox mdl-js-checkbox checkbox2  selection-checkbox mdl-js-ripple-effect"  [ngClass]="{'is-checked': isPreferredAllowedHotelRate('AAA')} " for = "AAA">
                                        <input (click)="toggleHotelAllowedRates('AAA')" type = "checkbox" id = "AAA"   [checked]="isPreferredAllowedHotelRate('AAA')"  formControlName="aaRate"
                                           class = "mdl-checkbox__input">
                                        <span class = "mdl-checkbox__label">AAA</span>
                                     </label>
                                     <label class = "mdl-checkbox mdl-js-checkbox checkbox2  selection-checkbox mdl-js-ripple-effect"  [ngClass]="{'is-checked': isPreferredAllowedHotelRate('Government')} " for = "government">
                                        <input (click)="toggleHotelAllowedRates('Government')" type = "checkbox" [checked]="isPreferredAllowedHotelRate('Government')" id = "government"  formControlName="governmentRate"
                                           class = "mdl-checkbox__input">
                                        <span class = "mdl-checkbox__label">Goverment/Military</span>
                                     </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"
                                    (click)="saveUserPrefererences()">{{'profilePreference.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profilePreference.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="card-div">
    <div class="card-div-inner">
        <div class="traveller-form">
            <div class="card-div-header" onclick="openCardEdit(this);">
                <h3>{{'profilePreference.Preferredcar' | translate}}</h3>
                <div class="card-edit">
                    <span class="edit-text">Edit</span>
                    <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                </div>
                <div class="traveller-short-info">
                    <div class="traveller-short-info-inner">{{getCarString()}}</div>
                </div>
            </div>
            <div class="card-div-body" style="display: none;">
                <form method="post" id="carPreferenceForm" class="hotelPreferenceForm" [formGroup]="carPreferenceForm">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="input-box">
                                <label
                                    class="input-label primary-label">{{'profilePreference.Whatisyourfavoritecarbrand' |
                                    translate}}</label>
                                <div class="hotelCheckboxContainer flexContainer checkbox-radio-container">
                                    <div formArrayName="prefCarBrands"
                                        *ngFor="let brand of preDefinedCarBrands; let k = index;" class="airline-div">
                                        <label
                                            class="mdl-checkbox mdl-js-checkbox selection-checkbox mdl-js-ripple-effect"
                                            [ngClass]="{'is-checked':isPreferredCarBrand(brand.value)}"
                                            for="{{brand.value}}">
                                            <input (change)="togglePreferredCarBrand(brand.value);"
                                                [formControlName]="k" value="{{brand.value}}"
                                                data-value="{{brand.label}}" type="checkbox" id="{{brand.value}}"
                                                class="mdl-checkbox__input">
                                            <span class="mdl-checkbox__label">
                                                <span class="mdl-checkbox__label-img">
                                                    <img class="d-none d-md-block"
                                                        src="assets/images/cars/{{brand.label}}.png" alt="objvalue" />
                                                    <img class="d-block d-md-none"
                                                        src="assets/images/cars/{{brand.label}}.png" alt="objvalue" />
                                                </span>
                                                <span class="mdl-checkbox__label-text">{{brand.label}}</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="input-box">
                                <label class="input-label primary-label">{{'profilePreference.Whatisyourpreferredcarclass'
                                        | translate}}</label>
                                <div class="hotelCheckboxContainer flexContainer checkbox-radio-container">
                                    <div formArrayName="prefCarSize" *ngFor="let size of carSize; let j = index;"
                                        class="airline-div">
                                        <label
                                            class="mdl-checkbox mdl-js-checkbox selection-checkbox mdl-js-ripple-effect checkHeight"
                                            [ngClass]="{'is-checked':isPreferredCarSize(size)}" for="{{size}}">
                                            <input (change)="togglePreferredCarSize(size);" [formControlName]="j"
                                                value="{{size}}" data-value="{{size}}" type="checkbox" id="{{size}}"
                                                class="mdl-checkbox__input">
                                            <span class="mdl-checkbox__label">
                                                <span class="mdl-checkbox__label-text">{{ getCarSizeText(size)}}</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="input-box">
                                <label class="input-label primary-label">{{'profilePreference.Whatisyourpreferredcartype'
                                        | translate}}</label>
                                <div class="hotelCheckboxContainer flexContainer checkbox-radio-container">
                                    <div formArrayName="prefCarType" *ngFor="let type of carType; let j = index;"
                                        class="airline-div">
                                        <label
                                            class="mdl-checkbox mdl-js-checkbox selection-checkbox mdl-js-ripple-effect checkHeight"
                                            [ngClass]="{'is-checked':isPreferredCarType(type)}" for="{{type}}">
                                            <input (change)="togglePreferredCarType(type);" [formControlName]="j"
                                                value="{{type}}" data-value="{{type}}" type="checkbox" id="{{type}}"
                                                class="mdl-checkbox__input">
                                            <span class="mdl-checkbox__label">
                                                <span class="mdl-checkbox__label-text">{{ getCarTypeText(type)}}</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <div class="card-div-footer">
                                <button *ngIf="!isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"
                                    (click)="saveUserPrefererences()">{{'profilePreference.Save' | translate}}</button>
                                <button *ngIf="isRequestInProgress" type="submit"
                                    class="button button-primary saveButton"> <span
                                        class="loaderClass">{{'profilePreference.PleaseWait' | translate}}<loader-dots
                                            class="loaderAlign"></loader-dots></span></button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>