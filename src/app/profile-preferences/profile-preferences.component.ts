import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { UntypedFormBuilder, UntypedFormGroup, UntypedFormArray, UntypedFormControl, Validators } from '@angular/forms';
import { UserAccountService } from '../user-account.service';
import { UserPreferences } from '../entity/user-prefs';
import { ALL_AIRLINES } from '../util/airlines';
import { ActiveToast, ToastrService } from 'ngx-toastr';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { CommonUtils } from '../util/common-utils';
import { Constants } from '../util/constants';
import { _ } from 'src/app/util/title';
import { TranslateService } from "@ngx-translate/core";
import { constants } from 'zlib';

declare var updateDOM: any;
declare var initGoogleMapOnElement: any;
declare var getSelectedHomeAirport: any;
declare var setSelectedHomeAirport: any;

@Component({
    selector: 'app-profile-preferences',
    templateUrl: './profile-preferences.component.html',
    styleUrls: ['./profile-preferences.component.scss'],
    standalone: false
})
export class ProfilePreferencesComponent implements OnInit {
  all_airlines = ALL_AIRLINES;
  preDefinedHighlightedAirlines = [];
  preDefinedFlightClasses = ['ECONOMY', 'PREMIUM_ECONOMY', 'BUSINESS', 'FIRST_CLASS',];
  preDefinedHotelBrands = [{ "value": "SW", "label": "Marriott" }, { "value": "HL", "label": "Hilton" }, { "value": "IC", "label": "Inter Continental" }, { "value": "WY", "label": "Wyndham" }, { "value": "UB", "label": "Choice Hotels" }, { "value": "BW", "label": "Best Western" }, { "value": "HY", "label": "Hyatt" }, { "value": "CW", "label": "Carlson Hospitality" }]
  flightClassesNames = {
    'BASIC_ECONOMY': this.translateService.instant('profilePref.BasicEconomy'), 'ECONOMY': this.translateService.instant('profilePref.Economy'), 'PREMIUM_ECONOMY': this.translateService.instant('profilePref.PremiumEconomy'),
    'BUSINESS': this.translateService.instant('profilePref.Business'), 'FIRST_CLASS': this.translateService.instant('profilePref.First'), 'PREMIUM_FIRST': this.translateService.instant('profilePref.Premium First')
  };
  carSize = ['Economy',
    'Intermediate',
    'Standard',
    'Fullsize',
    'Premium',
    'Luxury'
  ];
  carType1 = {
    'Car':  this.translateService.instant('profilePref.Car'),
    'Convertible':  this.translateService.instant('profilePref.Convertible'),
    'SUV':  this.translateService.instant('profilePref.SUV'),
    'Van':  this.translateService.instant('profilePref.Van'),
    'Pickup': this.translateService.instant('profilePref.Pickup')
  }
  carType = [
    'Car', 'Convertible', 'SUV', 'Van', 'Pickup'
  ]
  carSize1 = {
    'Economy': this.translateService.instant('profilePref.Economy'),
    'Intermediate': this.translateService.instant('profilePref.Intermediate'),
    'Standard': this.translateService.instant('profilePref.Standard'),
    'Fullsize': this.translateService.instant('profilePref.Fullsize'),

    'Premium': this.translateService.instant('profilePref.Premium'),
    'Luxury': this.translateService.instant('profilePref.Luxury')
  };
  highlightedAirlines = [];
  selectableAirlines = [];
  fetchUserPreferencesSubscription: Subscription;
  fetchAccountInfoSubscription: Subscription;
  userPreference: UserPreferences;
  airlinePreferenceForm: UntypedFormGroup;
  classPreferenceForm: UntypedFormGroup;
  seatPreferenceForm: UntypedFormGroup;
  hotelPreferenceForm: UntypedFormGroup;
  carPreferenceForm: UntypedFormGroup;
  activeToast: ActiveToast<any>;
  isRequestInProgress: boolean;
  constructor(private fb: UntypedFormBuilder,
    private userAccountInfoService: UserAccountService,
    private toastr: ToastrService,
    public translateService: TranslateService,
  ) {

  }
  preDefinedCarBrands = [{ "value": "AVS", "label": "Avis" }, { "value": "BGT", "label": "Budget" }, { "value": "ALM", "label": "Alamo" }, { "value": "NNL", "label": "National" }, { "value": "ENR", "label": "Enterprise" }, { "value": "HRZ", "label": "Hertz" }, { "value": "DLR", "label": "Dollar" },
  { "value": "THR", "label": "Thrifty" }, { "value": "PYL", "label": "Payless" }, { "value": "AVT", "label": "Advantage" }, { "value": "FX", "label": "Fox" }];
  ngOnInit() {

    this.fetchUserPreferencesSubscription = this.userAccountInfoService.userPreferencesObjObserver$.subscribe((userPref: UserPreferences) => {
      if (!userPref || userPref == null) {
        userPref = new UserPreferences();
        return;
      }
      this.userPreference = userPref;

      this.mergedHighlightedAirlines();
      var savedHomeAirport = null;
      if (this.userPreference.homeAirport && this.userPreference.homeAirport.length > 0) {
        savedHomeAirport = this.userPreference.homeAirport[0];
      }

      setSelectedHomeAirport(savedHomeAirport, this.userPreference.homeAddress);
      // window.alert('this.userPreference');

      this.initForms();

    });
    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInf) => {
      let userAccountInfoObj: any = userAccountInf;
    if (!userAccountInfoObj) return;
    if(userAccountInfoObj && userAccountInfoObj.userInfo && userAccountInfoObj.userInfo.home && userAccountInfoObj.userInfo.home.country_code){
           let itemFound = Constants.EUROPE.findIndex(item => item ===userAccountInfoObj.userInfo.home.country_code);
           if(itemFound>-1){
             this.preDefinedHighlightedAirlines = Constants.EUROPEAN_AIRLINES;
           }else if(userAccountInfoObj.userInfo.home.country_code==='IN'){
            this.preDefinedHighlightedAirlines = Constants.INDIAN_AIRLINES;
           }else{
            this.preDefinedHighlightedAirlines = Constants.US_AIRLINES;
           }
    }else  if(userAccountInfoObj && userAccountInfoObj.userInfo && userAccountInfoObj.userInfo.work && userAccountInfoObj.userInfo.work.country_code){
      let itemFound = Constants.EUROPE.findIndex(item => item ===userAccountInfoObj.userInfo.home.country_code);
      if(itemFound>-1){
        this.preDefinedHighlightedAirlines = Constants.EUROPEAN_AIRLINES;
      }else if(userAccountInfoObj.userInfo.home.country_code==='IN'){
       this.preDefinedHighlightedAirlines = Constants.INDIAN_AIRLINES;
      }else{
       this.preDefinedHighlightedAirlines = Constants.US_AIRLINES;
      }
}
    });
    this.userAccountInfoService.fetchUserPreferences();
    this.initForms();
    initGoogleMapOnElement();
  }

  ngOnChanges() {

  }

  getFlightClassText(fClass: string) {
    return this.flightClassesNames[fClass];
  }
  getCarSizeText(brand: string) {
    return this.carSize1[brand];
  }
  getCarTypeText(brand: string) {
    return this.carType1[brand];
  }
  initForms() {

    this.airlinePreferenceForm = this.fb.group({
      prefAirlines: this.fb.array([]),
      selectedAirline: ['', null],
      selectAny: [false]
    });
    for (let airline of this.highlightedAirlines) {
      const control = new UntypedFormControl(this.isPreferredAirline(airline)); // if first item set to true, else false
      (<UntypedFormArray>this.airlinePreferenceForm.controls['prefAirlines']).controls.push(control);
    }

    this.classPreferenceForm = this.fb.group({
      prefFlightClasses: this.fb.array([]),
      basicEconomy: [this.isBasicEconomyPreferred() ? 'YES' : 'NO', Validators.required]
    });

    for (let fClass of this.preDefinedFlightClasses) {
      const control = new UntypedFormControl(this.isPreferredFlightClass(fClass)); // if first item set to true, else false
      (<UntypedFormArray>this.classPreferenceForm.controls['prefFlightClasses']).controls.push(control);
    }

    this.seatPreferenceForm = this.fb.group({
      seatPreference: [this.getSeatPreference(), Validators.required]
    });

    this.hotelPreferenceForm = this.fb.group({
      prefHotelBrands: this.fb.array([]),
      hotelRating: this.getPreferredHotelRating(),
      aaRate : [],
      governmentRate: []
    });

    for (let fClass of this.preDefinedHotelBrands) {
      const control = new UntypedFormControl(this.isPreferredHotelBrand(fClass.value)); // if first item set to true, else false
      (<UntypedFormArray>this.hotelPreferenceForm.controls['prefHotelBrands']).controls.push(control);
    }
    this.carPreferenceForm = this.fb.group({
      prefCarBrands: this.fb.array([]),
      prefCarSize: this.fb.array([]),
      prefCarType: this.fb.array([]),
    });
    for (let fClass of this.preDefinedCarBrands) {
      const control = new UntypedFormControl(this.isPreferredCarBrand(fClass.value)); // if first item set to true, else false
      (<UntypedFormArray>this.carPreferenceForm.controls['prefCarBrands']).controls.push(control);
    }
    for (let fClass1 of this.carSize) {
      const control = new UntypedFormControl(this.isPreferredCarSize(fClass1)); // if first item set to true, else false
      (<UntypedFormArray>this.carPreferenceForm.controls['prefCarSize']).controls.push(control);
    }
    for (let fClass1 of this.carType) {
      const control = new UntypedFormControl(this.isPreferredCarType(fClass1)); // if first item set to true, else false
      (<UntypedFormArray>this.carPreferenceForm.controls['prefCarType']).controls.push(control);
    }
    this.updateDom();

  }
  mergedHighlightedAirlines() {
    let highlightedAirlines = JSON.parse(JSON.stringify(this.preDefinedHighlightedAirlines));
    if (this.userAccountInfoService.isLoggedIn()
      && Constants.CompanySpecificHighlightedAirlines[this.userAccountInfoService.getUserCompanyId()]
    ) {
      highlightedAirlines = Constants.CompanySpecificHighlightedAirlines[this.userAccountInfoService.getUserCompanyId()];
    }
    for (let preferred of this.getPreferredAirlines()) {
      if (highlightedAirlines.indexOf(preferred) == -1) highlightedAirlines.push(preferred);
    }
    this.highlightedAirlines = highlightedAirlines;
    this.updateSelectableAirlines();
  }

  getPreferredAirlines() {

    if (this.userPreference) {
      return this.userPreference.preferredAirlines;
    } else {
      return [];
    }
  }

  getPreferredClasses() {
    if (this.userPreference) {
      return this.userPreference.preferredClass;
    } else {
      return [];
    }
  }

  getPreferredHotelRating() {
    if (this.userPreference
      && this.userPreference.preferredHotelClass
      && this.userPreference.preferredHotelClass[0]) {
      return this.userPreference.preferredHotelClass[0];
    } else {
      return '4';
    }
  }

  getSeatPreference() {
    if (this.userPreference && this.userPreference.seatPreference) {
      return this.userPreference.seatPreference;
    } else {
      return '';
    }
  }

  isBasicEconomyPreferred() {
    if (this.userPreference && this.userPreference.preferredClass) {
      return this.userPreference.preferredClass.indexOf('BASIC_ECONOMY') > -1;
    } else {
      return false;
    }
  }

  isEconomyPreferred() {
    if (this.userPreference && this.userPreference.preferredClass) {
      return this.userPreference.preferredClass.indexOf('ECONOMY') > -1;
    } else {
      return false;
    }
  }

  isPreferredAirline(airlineCode) {
    if (this.userPreference && this.userPreference.preferredAirlines) {
      return this.userPreference.preferredAirlines.indexOf(airlineCode) > -1;
    } else {
      return false;
    }
  }
  isPreferredCarBrand(brand) {
    if (this.userPreference
      && this.userPreference.rentalCarPreferences
      && this.userPreference.rentalCarPreferences.preferredCarRentals) {
      return this.userPreference.rentalCarPreferences.preferredCarRentals.indexOf(brand) > -1;
    } else {
      return false;
    }
  }
  togglePreferredCarBrand(brand) {
    if (this.isPreferredCarBrand(brand)) {
      this.userPreference.rentalCarPreferences.preferredCarRentals = this.userPreference.rentalCarPreferences.preferredCarRentals.filter(carBrands => {
        return brand !== carBrands;
      });
    } else {
      if (!this.userPreference.rentalCarPreferences) {
        this.userPreference.rentalCarPreferences = {};
      }
      if (!this.userPreference.rentalCarPreferences.preferredCarRentals) {
        this.userPreference.rentalCarPreferences.preferredCarRentals = [];
      }
      this.userPreference.rentalCarPreferences.preferredCarRentals.push(brand);
    }

  }
  isPreferredCarSize(brand) {
    if (this.userPreference
      && this.userPreference.rentalCarPreferences
      && this.userPreference.rentalCarPreferences.preferredCarClasses) {
      return this.userPreference.rentalCarPreferences.preferredCarClasses.indexOf(brand) > -1;
    } else {
      return false;
    }
  }
  isPreferredCarType(brand) {
    if (this.userPreference
      && this.userPreference.rentalCarPreferences
      && this.userPreference.rentalCarPreferences.preferredCarTypes) {
      return this.userPreference.rentalCarPreferences.preferredCarTypes.indexOf(brand) > -1;
    } else {
      return false;
    }
  }
  togglePreferredCarSize(brand) {
    if (this.isPreferredCarSize(brand)) {
      this.userPreference.rentalCarPreferences.preferredCarClasses = this.userPreference.rentalCarPreferences.preferredCarClasses.filter(carsSize => {
        return brand !== carsSize;
      });
    } else {
      if (!this.userPreference.rentalCarPreferences) {
        this.userPreference.rentalCarPreferences = {};
      }
      if (!this.userPreference.rentalCarPreferences.preferredCarClasses) {
        this.userPreference.rentalCarPreferences.preferredCarClasses = [];
      }
      this.userPreference.rentalCarPreferences.preferredCarClasses.push(brand);
    }

  }
  togglePreferredCarType(brand) {
    if (this.isPreferredCarType(brand)) {
      this.userPreference.rentalCarPreferences.preferredCarTypes = this.userPreference.rentalCarPreferences.preferredCarTypes.filter(carsSize => {
        return brand !== carsSize;
      });
    } else {
      if (!this.userPreference.rentalCarPreferences) {
        this.userPreference.rentalCarPreferences = {};
      }
      if (!this.userPreference.rentalCarPreferences.preferredCarTypes) {
        this.userPreference.rentalCarPreferences.preferredCarTypes = [];
      }
      this.userPreference.rentalCarPreferences.preferredCarTypes.push(brand);
    }

  }
  isPreferredFlightClass(fclass) {
    if (this.userPreference && this.userPreference.preferredClass) {
      return this.userPreference.preferredClass.indexOf(fclass) > -1;
    } else {
      return false;
    }
  }

  isPreferredHotelBrand(brand) {
    if (this.userPreference && this.userPreference.preferredHotelChains) {
      return this.userPreference.preferredHotelChains.indexOf(brand) > -1;
    } else {
      return false;
    }
  }
  isPreferredAllowedHotelRate(rateType){
    if (this.userPreference && this.userPreference.allowedHotelRates) {
      return this.userPreference.allowedHotelRates.indexOf(rateType) > -1;
    } else {
      return false;
    }
  }


  togglePreferredHotelBrand(brand) {
    if (this.isPreferredHotelBrand(brand)) {
      this.userPreference.preferredHotelChains = this.userPreference.preferredHotelChains.filter(hotelChain => {
        return brand !== hotelChain;
      });
    } else {
      if (!this.userPreference.preferredHotelChains) {
        this.userPreference.preferredHotelChains = [];
      }
      this.userPreference.preferredHotelChains.push(brand);
    }

  }
  toggleHotelAllowedRates(rateType){
    if (this.isPreferredAllowedHotelRate(rateType)) {
      this.userPreference.allowedHotelRates = this.userPreference.allowedHotelRates.filter(rate => {
        return rateType !== rate;
      });
    } else {
      if (!this.userPreference.allowedHotelRates) {
        this.userPreference.allowedHotelRates = [];
      }
      this.userPreference.allowedHotelRates.push(rateType);
    }
  }

  getAirlineName(airlineCode) {
    if (airlineCode === 'ANY') return this.translateService.instant('profilePref.AnyAirline').toString();
    let airlineObj = ALL_AIRLINES.find(airline => {
      return airline.id == airlineCode;
    });
    return airlineObj.value;
  }
  toggleAnyAirline() {
    let selectedAny = this.airlinePreferenceForm.controls['selectAny'].value;
    if (selectedAny) {
      this.userPreference.preferredAirlines = [];
    }
  }


  togglePreferredAirline(airlineCode) {
    if (this.isPreferredAirline(airlineCode)) {
      this.userPreference.preferredAirlines = this.userPreference.preferredAirlines.filter(airline => {
        return airline !== airlineCode;
      })
    } else {
      this.userPreference.preferredAirlines.push(airlineCode);
    }

    this.updateDom();
  }

  toggleFlightClass(fclass) {

    if (this.isPreferredFlightClass(fclass)) {
      this.userPreference.preferredClass = this.userPreference.preferredClass.filter(fc => {
        return fc !== fclass;
      })
    } else {
      this.userPreference.preferredClass.push(fclass);
    }
    this.updateDom();

  }


  setSeatPreference(spref) {
    this.userPreference.seatPreference = spref;
  }

  setHotelRatingPref(hotelRating) {
    this.userPreference.preferredHotelClass = [hotelRating];
  }
  toggleBasicEconomyClass(selected) {
    if ((selected == 'YES' && !this.isPreferredFlightClass('BASIC_ECONOMY'))
      ||
      (selected == 'NO' && this.isPreferredFlightClass('BASIC_ECONOMY'))
    ) {
      this.toggleFlightClass('BASIC_ECONOMY');
    }
  }
  updateSelectableAirlines() {
    this.selectableAirlines = ALL_AIRLINES.filter(airlineObj => {
      return this.highlightedAirlines.indexOf(airlineObj.id) == -1;
    });
  }

  updateDom() {
    updateDOM();
    // setTimeout(()=>{
    //   onAirlineChangeJS();
    // },200)
  }

  onAirlineChange() {

    let selectedAirline = this.airlinePreferenceForm.controls['selectedAirline'].value;
    if (selectedAirline !== '') {
      this.userPreference.preferredAirlines.push(selectedAirline);
      this.highlightedAirlines.push(selectedAirline);
      this.updateSelectableAirlines();
      this.userAccountInfoService.setUserPreferences(this.userPreference);
      // this.mergedHighlightedAirlines();
      this.initForms();
    }

  }

  saveUserPrefererences() {
    this.userPreference.homeAirport = getSelectedHomeAirport();
    this.saveAndUpdateProfileInfo(this.userPreference);
  }

  private saveAndUpdateProfileInfo(userPref: UserPreferences) {
    this.isRequestInProgress = true;
    this.showProgress();
    this.fetchUserPreferencesSubscription = this.userAccountInfoService.saveUserPreference(userPref).subscribe(res => {
      this.isRequestInProgress = false;
      if (res.status === 'success') {
        this.showSuccess();
        //   this.isChangePasswordMode = false;
        if (res.data) {
          let userPref: UserPreferences = deserialize(res.data, UserPreferences);
          this.userAccountInfoService.setUserPreferences(userPref);
        }
      } else {
        this.showError();
      }
    }, error => {
      this.showError();
    });
  }

  showSuccess() {
    this.toastr.remove(this.activeToast.toastId);
    this.activeToast = this.toastr.success(this.translateService.instant('profilePage.Profilesavedsuccessfully').toString());
  }

  showProgress() {
    if (this.activeToast) {
      this.toastr.remove(this.activeToast.toastId);
    }
    this.activeToast = this.toastr.info(this.translateService.instant('profilePage.Pleasewait').toString(), this.translateService.instant('profilePage.Saving').toString());
  }

  showError() {
    if (this.activeToast) {
      this.toastr.remove(this.activeToast.toastId);
    }
    this.activeToast = this.toastr.success('Error!', 'Unknown error while saving profile');
  }
  getAirlineImageUrl(airlineCode: string) {
    return CommonUtils.getAirlineImageUrl(airlineCode);
  }

  getAirlineString() {
    if (this.userPreference && this.userPreference.preferredAirlines
      && this.userPreference.preferredAirlines.length > 0) {
      let nameArray = [];
      for (let name of this.userPreference.preferredAirlines) {
        nameArray.push(this.getAirlineName(name));
      }
      return nameArray.join(' | ');
    }
    return '';
  }

  getHomeAirport() {
    if (this.userPreference && this.userPreference.homeAirport && this.userPreference.homeAirport.length > 0) {
      return this.userPreference.homeAirport[0];
    }
    return '';
  }
  getClassString() {
    if (this.userPreference && this.userPreference.preferredClass
      && this.userPreference.preferredClass.length > 0) {
      let nameArray = [];
      for (let name of this.userPreference.preferredClass) {
        nameArray.push(this.getFlightClassText(name));
      }
      return nameArray.join(' | ');;
    }
    return '';
  }

  getSeatString() {

    if (this.userPreference && this.userPreference.seatPreference && this.userPreference.seatPreference.length > 0) {
      let nameArray = {
        1: this.translateService.instant('profilePref.Aisle'),
        2: this.translateService.instant('profilePref.Window'),
        3: this.translateService.instant('profilePref.NoPreference')
      }
      return nameArray[this.userPreference.seatPreference];
    }
    return '';
  }

  getHotelString() {
    let output1 = '';
    let output2 = '';
    if (this.userPreference && this.userPreference.preferredHotelChains
      && this.userPreference.preferredHotelChains.length > 0
    ) {
      let nameArray = [];
      for (let name of this.userPreference.preferredHotelChains) {
        nameArray.push(this.getHotelName(name));
      }
      output1 = nameArray.join(', ');
    }
    if (this.userPreference && this.userPreference.preferredHotelClass &&
      this.userPreference.preferredHotelClass.length > 0) {
      output2 = this.userPreference.preferredHotelClass[0] + this.translateService.instant('profilePref.spacestar').toString();
    }
    if (output1 !== '' && output2 != '') {
      return output1 + ' | ' + output2;
    } else if (output1 !== '') {
      return output1;
    } else if (output2 !== '') {
      return output2;
    }
    return '';
  }
  //  preDefinedHotelBrands

  getHotelName(hotelCode) {
    let hotelObj = this.preDefinedHotelBrands.find(hotel => {
      return hotel.value == hotelCode;
    });
    return hotelObj.label;
  }


  getCarString() {
    if (this.userPreference && this.userPreference.rentalCarPreferences
      && this.userPreference.rentalCarPreferences.preferredCarRentals
      && this.userPreference.rentalCarPreferences.preferredCarRentals.length > 0) {
      let nameArray = [];
      for (let name of this.userPreference.rentalCarPreferences.preferredCarRentals) {
        nameArray.push(name);
      }
      return nameArray.join(', ');
    }
    return '';
  }



}
