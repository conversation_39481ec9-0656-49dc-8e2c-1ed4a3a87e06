@import "../../variables.scss";
:host {
    width: 100vw;
    
}

.corporateRate {
    font-size: 14px;
    color: var(--hyperlink-color);
}

.summery-view-container {
    float: left;
    width: 100%;
    background: #fff;
}
.policyReview{
    cursor: pointer;
    display: block;
    color: var(--hyperlink-color);
}
  .bulletPoint{
    font-weight: 800;
    font-size: 28px;
    position: relative;
    top: -8px;
    margin-right: 5px;
  }
.summary-view {
    float: left;
    width: 100%;
    background: #fff;
}

.filter-search-box-containerScroll {
    float: left;
    width: 100%;
    margin: 25px 0;
    padding: 0 7px;
    position: relative;
    z-index: 2;
}

:host ::ng-deep {
    .ng-dropdown-panel .scroll-host {
        overflow: overlay !important;
        display: inline-block !important;
    }
}

.summary-view-inner {
    float: left;
    width: 100%;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -ms-flex-align: center;
    align-items: center;
    padding: 20px 40px;
}

.source-dest {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    letter-spacing: 0.42px;
    line-height: 17px;
    color: #000000;
}

.summary-view-middle {
    display: inline-flex;
    flex-direction: column;
    font-size: 12px;
    line-height: 15px;
    color: #AEAEAE;
}

.summary-view-middle .date {
    letter-spacing: -0.6px;
    margin-bottom: 5px;
}

.summery-room-details {
    display: flex;
    margin-bottom: 6px;
}

.edit-summery-view {
    display: inline-flex;
}

.edit-summery-view .icon-edit {
    background: #fff;
    height: 32px;
    width: 32px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    color: var(--secondarybutton-font-color);
    cursor: pointer;
}

.summery-room {
    margin-right: 20px;
}

.no-flight-found-container {
    width: 100%;
    padding-top: 20px;
    padding-left: 10px;
    margin-right: auto;
    margin-left: auto;
    margin-bottom: 0px;
}

.summery-view-container {
    float: left;
    width: 100%;
    background: #fff;
}

.tab-content-item {
    float: left;
    width: 100%;
}

.selected-flight-container {
    float: left;
    width: 100%;
    padding: 5px 20px 13px;
}

.summery-person {
    margin-right: 20px;
}

.summery-person .container-icon {
    margin-right: 8px;
}

.hidden {
    display: none;
}

.loaderAlign {
    position: relative;
    top: 4px;
}

.btn-secondary {
    height: 64px;
    width: max-content;
    border-radius: 0px !important;
    text-transform: unset !important;
    letter-spacing: 1px;
    background-color: var(--button-bg-color) !important;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    margin-top: 15px;
    border: none;
}

.add {
    height: 24px;
    width: auto;
    white-space: nowrap;
    color: #2D57FA;
    font-family: "apercu-r";
    font-size: 20px;
    font-weight: bold;
    letter-spacing: 0.6px;
    line-height: 25px;
    text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}

.modal-title {
    height: 26px;
    width: 266px;
    color: #fff;
    font-family: "apercu-mono";
    font-size: 22px;
    letter-spacing: -0.98px;
    line-height: 26px;
    text-align: left;
}

.modal-header {
    background-color: var(--hyperlink-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px !important;
    border-bottom: none;
}
.modal-header1 {
    color: transparent;
    font-size: 14px;
    display: flex;
    padding: 22px 17px 15px 25px;
    justify-content: space-between;
    border-radius: 6px 6px 0 0;
    border-bottom: none;
}
.modal-title1{
    display: inline-block;
    width: 35px;
    margin-bottom: 0;
    line-height: 1.5;
}
.modal-header h5 {
    font-size: 14px;
    font-family: "apercu-mono";
}

.modal-body {
    padding: 28px 25px 47px 25px;
    border-radius: 0 0 5px 5px;
}

.modal-footer {
    padding: 0;
    border-top: none;
}

.modal-content {
    text-align: left !important;
}

.feedbackImage {
    width: 30px;
    height: 28px;
}

.hearctIcon {
    position: relative;
    left: -10px;
    font-size: 22px;
    top: 2px;
    cursor: pointer;
}
.hearctIconPopup {
    position: relative;
    left: -10px;
    font-size: 22px;
    top: 2px;
    cursor: pointer;
}
.hearctIcon1 {
    position: relative;
    left: -10px;
    font-size: 22px;
    cursor: pointer;
}

.select-header {
    background: $themeColor2;
}

.custom-selectbox {
    cursor: pointer;
    position: relative;
    display: inline-block;
    padding-right: 15px;
    margin-right: 5px;
}

.custom-selectbox .field-value {
    color: #AEAEAE;
}

.custom-selectbox-value {
    font-size: 16px !important;
    letter-spacing: -0.71px !important;
    color: #413E3B !important;
    line-height: 16px !important;
}

.custom-selectbox .control-icon {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.filter-select-container .custom-selectbox-value {
    max-width: 120px;
    text-overflow: ellipsis;
    overflow: hidden;
}

.control-icon {
    font-size: 4px;
    margin-left: 10px;
    color: $link-color;
}


/*box css*/

.inlineblock {
    display: inline-block;
    vertical-align: middle;
}

.result-card-box {
    position: relative;
    background: #fff;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    border-radius: 6px;
    font-family: $fontRegular;
    float: left;
    width: 100%;
    margin-bottom: 16px;
}

.block {
    display: block;
}

.top-recommandation-box {
    background-color: var(--button-font-color);
    border-radius: 6px;
    float: left;
    width: 100%;
    height: 50px;
    font-size: 12px;
    line-height: 15px;
    color: #fff;
    padding: 3px 17px;
    position: relative;
    z-index: 1;
    margin-bottom: -29px;
}

.top-recommandation-box1 {
    background-color: #27c198;
    border-radius: 6px;
    float: left;
    display: flex;
    width: 100%;
    height: 50px;
    font-size: 12px;
    line-height: 15px;
    color: #fff;
    padding: 3px 17px;
    position: relative;
    z-index: 1;
    margin-bottom: -29px;
}

.result-card-box-inner {
    position: relative;
    z-index: 2;
    background: #fff;
    float: left;
    width: 100%;
    border-radius: 6px;
    padding: 8px;
    display: flex;
    align-items: center;
}

.result-card-left {
    width: 125px;
    padding-right: 20px;
}

.result-card-left img {
    height: 105px;
    width: 105px;
    border-radius: 6px;
}

.hotel-name {
    font-size: 18px;
    font-family: $fontRegular;
    line-height: 23px;
}


/*.hotel-name{
    font-size: 18px;
    font-family: "apercu-r";
    line-height: 15px;
    float: left;
    width: 100%;
    position: relative;
    height: 25px;
    overflow: hidden;
}*/


/*.hotel-name-inner{
    white-space: nowrap;
    float: left;
    position: absolute;
    left: 0px;
    right: auto;
    -webkit-animation-name: ticker;  Safari 4.0 - 8.0
    -webkit-animation-duration: 2s;  Safari 4.0 - 8.0
    -webkit-animation-iteration-count: infinite;
    animation-name: ticker;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}*/

@-webkit-keyframes ticker {
    from {
        left: 0px;
        right: auto;
    }
    to {
        left: auto;
        right: 0px;
    }
}


/* Standard syntax */

@keyframes ticker {
    from {
        left: 0px;
        right: auto;
    }
    to {
        left: auto;
        right: 0px;
    }
}

.hotel-distance-address {
    font-size: 14px;
    line-height: 15px;
    padding-top: 10px;
}

.hotel-address {
    color: #AEAEAE;
    position: relative;
    padding-left: 6px;
    margin-left: 8px;
    word-wrap: break-word;
}

.hotel-address::after {
    position: absolute;
    content: '';
    width: 2px;
    height: 2px;
    border-radius: 50%;
    background: #AEAEAE;
    left: 0;
    top: 9px;
}

.result-card-middle-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding-bottom: 11px;
    padding-left: 7px;
    padding-right: 17px;
}

.result-card-right1 {
    width: 200px;
    text-align: right;
}

.result-card-right {
    width: 200px;
    text-align: center;
}

.result-card-middle {
    width: calc(100% - 325px);
}

.duration-section {
    display: inline-block;
    vertical-align: middle;
    margin-right: 22px;
}

.duration-section span {
    font-size: 12px;
    line-height: 16px;
    color: #AEAEAE;
}

.result-card-middle-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.result-card-middle-bottom-left {
    display: flex;
    align-items: center;
}

.result-card-middle-bottom {
    border-top: 2px solid #e3e3e3;
    float: left;
    width: 100%;
    padding-top: 4px;
    padding-left: 7px;
    padding-right: 10px;
}

.icon-margin {
    margin-right: 5px;
}

.star-container {
    margin-right: 22px;
    line-height: 16px;
}
.lowercase {
    text-transform: lowercase;
}
.hotel-price {
    font-size: 24px;
    font-family: $fontBold;
    margin-bottom: 4px;
    float: left;
    width: 100%;
    color: var(--hyperlink-color);
    letter-spacing: .5px;
   word-break: break-all;
    display: contents;
}

.hotel-select-button {
    float: left;
    width: 100%;
}

.hotel-select-button button {
    width: 153px;
    border-radius: 6px;
    height: 43px;
    font-size: 12px;
    letter-spacing: 1.33px;
    font-family: $fontBold;
    font-weight: normal;
}

.facilities-container span {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
}

.policy-container span {
    font-size: 12px;
    line-height: 12px;
    color: #AEAEAE;
}

.hotel-list {
    float: left;
    width: 100%;
}

.top-recommandation-box {
    background-color: var(--button-font-color);
    border-radius: 6px;
    float: left;
    width: 100%;
    height: 50px;
    font-size: 12px;
    line-height: 15px;
    color: #fff;
    padding: 3px 17px;
    position: relative;
    z-index: 1;
    margin-bottom: -29px;
}

.svg-color {
    color: #000 !important;
}

.ratingLabel .mdl-checkbox__label:before,
.ratingLabel .mdl-checkbox__label:after {
    top: 15px;
}

.star-rating-template {
    display: inline-block;
    vertical-align: top;
}

.star-cb-group {
    /*font-size: 0;*/
    /*unicode-bidi: bidi-override;*/
    /*direction: rtl;*/
    transform: rotateY(180deg);
    display: block;
    height: 38px;
    float: left;
    width: 100%;
}

.star-cb-group * {
    font-size: 54px;
}

.star-cb-group>input {
    display: none;
}

.star-cb-group>input+label {
    display: inline-block;
    overflow: hidden;
    /*text-indent: 9999px;*/
    width: 45px;
    height: 38px;
    white-space: nowrap;
    cursor: pointer;
    position: relative;
}

.star-cb-group>input+label:before {
    display: inline-block;
    position: absolute;
    top: 0;
    left: 5px;
    content: '';
    background-size: 40px;
    height: 38px;
    width: 40px;
    /*text-indent: -9999px;*/
    background-image: url(/../../assets/images/hotel/staropen.svg);
    /*color: #a4a4a4;*/
}

.star-cb-group>input:checked~label:before,
.star-cb-group>input+label:hover~label:before,
.star-cb-group>input+label:hover:before {
    background-image: url(/../../assets/images/hotel/starfilled-big.svg);
    /*color: var(--button-font-color);*/
}

.star-cb-group>.star-cb-clear+label {
    /*text-indent: -9999px;*/
    width: .5em;
    margin-left: -.5em;
    display: none;
}

.star-cb-group>.star-cb-clear+label:before {
    width: .5em;
}

.star-cb-group:hover>input+label:before {
    background-image: url(/../../assets/images/hotel/staropen.svg);
    /*    color: #a4a4a4;
        text-shadow: none;*/
}

.mobileMsg {
    font-size: 18px;
}

.star-cb-group:hover>input+label:hover~label:before,
.star-cb-group:hover>input+label:hover:before {
    background-image: url(/../../assets/images/hotel/starfilled-big.svg);
    /*color: var(--button-font-color);*/
}

:root {
    font-family: Helvetica, arial, sans-serif;
}

.amenities-img {
    display: inline-block;
    vertical-align: middle;
    margin-right: 7px;
}

.amenities-value {
    display: inline-block;
    vertical-align: middle;
}

.mdl-checkbox__label {
    padding-left: 26px;
}

@media (max-width: 991px) {
    .summary-view-inner {
        flex-wrap: wrap;
        padding: 9px 20px 17px;
    }
    .source-dest {
        order: 1;
        width: 100%;
    }
    .summary-view-middle {
        order: 2;
        width: 100%;
        max-width: 275px;
    }
    .edit-summery-view {
        order: 3;
        position: relative;
        top: 0px;
    }
    .hotel-price {
        font-size: 24px;
    }
}

.top-strip {
    background: $themeColor2;
    float: left;
    width: 100%;
    height: 43px;
}

@media (max-width: 991px) and (min-width: 768px) {
    .result-card-right {
        width: 125px;
    }
    .result-card-middle {
        width: calc(100% - 250px);
    }
    .result-card-left {
        padding-right: 15px;
    }
    .result-card-middle {
        padding-right: 15px;
    }
    .result-card-middle-top {
        align-items: flex-start;
        flex-direction: column;
    }
    .hotel-select-button button {
        width: 100%;
        font-size: 14px;
        letter-spacing: normal;
    }
}

@media(max-width:768px) {
    .hearctIcon {
        position: relative;
        left: 38px;
        font-size: 22px;
        top: 10px;
        cursor: pointer;
    }
}

@media (max-width: 767px) {
    .corporateRate {
        font-size: 12px;
        color: var(--hyperlink-color);
        width: max-content;
        display: flex !important;
    }
    .hotels-taxes-fees{
        font-size: 12px;
        padding-left: 30px;
    }
    .hearctIconPopup {
        position: relative;
        left: -18px;
        font-size: 22px;
        top: 9px !important;
        cursor: pointer;
    } 
    .hearctIcon {
        position: relative;
        left: 18px;
        font-size: 22px;
        top: 10px;
        cursor: pointer;
    }
    .btn-secondary {
        height: 64px;
        width: 300px;
        border-radius: 0px !important;
        text-transform: unset !important;
        letter-spacing: 1px;
        background-color: var(--button-bg-color) !important;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
        margin-top: 15px;
        border: none;
    }
    .add {
        height: 24px;
        width: auto;
        white-space: nowrap;
        color: #2D57FA;
        font-family: "apercu-r";
        font-size: 15px;
        font-weight: bold;
        letter-spacing: 0.6px;
        line-height: 25px;
        text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
    }
    .custom-selectbox-value {
        font-size: 14px !important;
        letter-spacing: -0.71px !important;
        color: #413E3B !important;
        line-height: 16px !important;
    }
    .flight-list-sorting .ng-dropdown-panel,
    .simple-dropdown .ng-dropdown-panel {
        left: 0% !important;
    }
}

@media (max-width: 767px) {
    .filter-strip {
        float: left;
        width: 100%;
        background: $themeColor2;
        padding: 0 15px;
        display: flex;
        justify-content: space-between;
    }
    .filter-strip-inner {
        float: left;
        width: 100%;
        height: 43px;
        display: flex;
        justify-content: space-between;
    }
    .result-card-middle {
        width: calc(100% - 64px);
    }
    .mobileMsg {
        font-size: 14px;
    }
    .hotel-name {
        font-size: 13px;
        font-family: "apercu-r";
        line-height: 15px;
    }
    .hotel-distance-address {
        font-size: 9px;
        line-height: 15px;
        padding-top: 0;
    }
    .feedbackImage {
        width: 16px;
        height: 14px;
    }
    .upVote {
        color: green;
        font-size: 10px;
        margin-right: 3px;
    }
    .downVote {
        color: red;
        font-size: 10px;
        margin-right: 0px;
    }
    .star-container .star img {
        width: 16px;
    }
    .hotel-price {
        font-size: 14px;
        margin-bottom: 2px;
        white-space: nowrap;
        text-align: right;
    }
    .duration-section span {
        font-size: 9px;
        line-height: 16px;
    }
    .result-card-left {
        width: 64px;
        padding-right: 4px;
        padding-top: 2px;
    }
    .result-card-left img {
        height: 60px;
        width: 60px;
    }
    .result-card-middle-top {
        padding-left: 3px;
        padding-right: 3px;
        padding-bottom: 4px;
        align-items: flex-start;
    }
    .result-card-middle-top-left {
        width: auto;
    }
    .result-card-middle-top-right {
        width: auto;
        text-align: center;
        line-height: 12px;
    }
    .facilities-container span {
        margin-right: 7px;
    }
    .facilities-container span img {
        max-width: 18px;
    }
    .policy-container img {
        width: 10px;
    }
    .policy-container span {
        font-size: 9px;
    }
    .duration-section {
        margin-right: 0;
        width: 100%;
        text-align: right;
    }
    .result-card-box {
        margin-bottom: 0px;
        margin-top: 8px;
    }
    .result-card-box-inner {
        padding: 6px 6px;
        align-items: flex-start;
        cursor: pointer;
    }
    .star-container {
        margin-right: 0;
        line-height: 12px;
    }
    .result-card-middle-bottom {
        padding-top: 5px;
        border-top-width: 1px;
        line-height: 10px;
    }
    .facilities-container {
        line-height: 10px;
    }
    .car-duration img {
        width: 11px;
    }
    .walk-duration img {
        width: 8px;
    }
    .walk-duration {
        margin-top: -9px;
    }
    .hotel-list {
        padding: 8px;
    }
    .top-recommandation-box {
        font-size: 10px;
        margin-bottom: -31px;
    }
    .hotel-address:after {
        top: 5px;
    }
    .selectox-header {
        line-height: 20px;
        margin-bottom: -12px;
    }
    .container {
        padding-left: 0;
        padding-right: 0;
    }
}
@media(max-width:400px){
    .no-flight-found-container {
        width: 100%;
        padding-top: 20px;
        padding-left: 10px;
        margin-right: auto;
        margin-left: auto;
        margin-bottom: 100px;
    }
}
@media (max-width: 359px) {
    .facilities-container span {
        margin-right: 5px;
    }
    .facilities-container span img {
        max-width: 16px;
    }
}

@media (max-width: 320px) {
    .mobileMsg {
        font-size: 12px;
    }
    .btn-secondary {
        height: 64px;
        width: 260px;
        border-radius: 0px !important;
        text-transform: unset !important;
        letter-spacing: 1px;
        background-color: var(--button-bg-color) !important;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
        margin-top: 15px;
        border: none;
    }
    .add {
        height: 24px;
        width: auto;
        white-space: nowrap;
        color: #2D57FA;
        font-family: "apercu-r";
        font-size: 14px;
        font-weight: bold;
        letter-spacing: 0.6px;
        line-height: 25px;
        text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
    }
}


/*new css for new design*/

.filter-strip {
    float: left;
    width: 100%;
    background: $themeColor2;
    padding: 0 15px;
    display: flex;
    justify-content: space-between;
}

.filter-strip-inner {
    float: left;
    width: 100%;
    height: 43px;
    display: flex;
    justify-content: space-between;
}

.filters-container {
    float: left;
    width: 100%;
}

.filter-item {
    display: inline-block;
    vertical-align: middle;
    padding: 9px 20px;
    position: relative;
}

.filter-item:first-child {
    padding-left: 0;
}

.filter-item:last-child {
    padding-right: 0;
}

.filter-select-box {
    visibility: hidden;
    z-index: 0;
    position: absolute;
}

.filter-item-link {
    position: relative;
    display: inline-block;
    color: #fff;
    font-size: 16px;
    letter-spacing: 0.69px;
    font-family: $fontRegular;
}

.filter-select-box.ng-select-opened+a:after {
    position: absolute;
    bottom: -6px;
    left: 0;
    right: 0;
    height: 3px;
    background: #fff;
    content: '';
}

.policy-link-container {
    float: left;
    width: 100%;
    padding: 6px 30px 24px 30px;
}

.policy-link-container a {
    font-size: 12px;
    letter-spacing: 1px;
    line-height: 18px;
    color: var(--button-font-color);
    font-family: $fontBold;
    text-transform: uppercase;
}

.policy-link-container a span {
    display: inline-block;
    vertical-align: middle;
}

.policy-link-container a img {
    display: inline-block;
    vertical-align: middle;
    margin-left: 9px;
    margin-top: 2px;
}

.mdl-checkbox {
    height: auto;
}

.mdl-checkbox__input {
    position: absolute;
    left: -9999px;
}

.mdl-checkbox__label {
    padding-left: 30px;
    position: relative;
    display: inline-block;
}

.mdl-checkbox__label:before,
.mdl-checkbox__label:after {
    height: 15px;
    width: 15px;
    position: absolute;
    left: 0;
    content: '';
    top: 0;
}

.mdl-checkbox__label:before {
    border: 1px solid #aeaeae;
    border-radius: 4px;
    background-color: #F7F7F7;
}

.mdl-checkbox__input:checked+.mdl-checkbox__label:after {
    background-image: url(/../../assets/images/check-icon.png);
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center center;
}

.mdl-checkbox__label {
    font-size: 12px;
    line-height: normal;
    font-family: $fontMono;
}

.mdl-radio {
    height: auto;
}

.mdl-radio__button {
    position: absolute;
    left: -9999px;
}

.mdl-radio__label {
    padding-left: 30px;
    position: relative;
    display: inline-block;
}

.mdl-radio__label:before,
.mdl-radio__label:after {
    position: absolute;
    content: '';
}

.mdl-radio__label:before {
    border: 1px solid #aeaeae;
    border-radius: 50%;
    background-color: #fff;
    left: 0;
    top: 0;
    height: 18px;
    width: 18px;
}

.mdl-radio__button:checked+.mdl-radio__label:after {
    height: 12px;
    width: 12px;
    top: 3px;
    left: 3px;
    border-radius: 50%;
    background: linear-gradient(180deg, #3CC8FA 0%, #1C97F3 100%);
    background-position: center center;
}

.mdl-radio__label {
    font-size: 12px;
    line-height: normal;
    font-family: $fontMono;
}

.selectBox-footer-filter-button {
    height: 88px;
    background-color: #EFFAFC;
    float: left;
    width: 100%;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 29px;
}

.selectBox-footer-filter-button button {
    width: 100%;
    height: 46px;
    font-size: 12px;
    letter-spacing: 1px;
}

.checkbox-group-container {
    margin-bottom: 22px;
    padding: 0 16px;
}

.checkbox-group-header {
    font-size: 14px;
    line-height: 17px;
    color: #000000;
    padding: 5px 0 10px 0;
}

.flightTimeOptionDiv {
    float: left;
    width: 100%;
}
.modal-body1 {
    padding: 18px 0px 17px 15px;
    border-radius: 0 0 5px 5px;
  }
  .hotelsInDropDown{
      cursor: pointer;
      line-height: 30px;
  }
.filter-modal {
    width: 100%;
    float: left;
    position: absolute;
    left: 0;
    background: #fff;
    z-index: 10;
    border-radius: 8px;
    top: 100%;
}
.filter-modalpopup {
    width: 100%;
    float: left;
    position: absolute;
    left: 15%;
    background: #fff;
    z-index: 10;
    border-radius: 8px;
    top: 10%;
}
.modalAirportFilterInfo {
    min-width: 324px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modalAirportFilterInfo .modal-content {
    border: none;
    border-radius: 6px;
}

.modalAirportFilterInfo .close {
    text-shadow: none;
    color: #fff;
    opacity: 1;
}

.modalAirportFilterInfo .modal-header {
    background-color: var(--hyperlink-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px !important;
    border-bottom: none;
}

.modalAirportFilterInfo .modal-header h5 {
    font-size: 14px;
}

.modalAirportFilterInfo .modal-footer {
    padding: 0;
    border-top: none;
}

.modalAirportFilterInfo .close i {
    font-size: 17px;
}

.modalAirportFilterInfo .close:hover {
    color: #fff !important;
    opacity: 1 !important;
}
.modalAirportFilterInfo1 {
    max-width: 280px;
    z-index: 10;
   // position: relative !important;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.modalAirportFilterInfo1 .modal-content {
    border: none;
    border-radius: 6px;
}

.modalAirportFilterInfo1 .close {
    text-shadow: none;
    color: #fff;
    opacity: 1;
}

.modalAirportFilterInfo1 .modal-header {
    background-color: var(--hyperlink-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px !important;
    border-bottom: none;
}

.modalAirportFilterInfo1 .modal-header h5 {
    font-size: 14px;
}

.modalAirportFilterInfo1 .modal-footer {
    padding: 0;
    border-top: none;
}

.modalAirportFilterInfo1 .close i {
    font-size: 17px;
}

.modalAirportFilterInfo1 .close:hover {
    color: #fff !important;
    opacity: 1 !important;
}

.mapFilterLink img {
    margin-right: 14px;
}

.mapFilterLink {
    min-width: 60px;
}

.hotel-list-container {
    float: left;
    width: 100%;
    position: relative;
}

.filter-map-container {
    float: left;
    width: 100%;
    position: relative;
}

.filter-map-container-inner {
    float: left;
    width: 100%;
    position: relative;
    top: 0px;
    z-index: 0;
}

.hotel-map-item {
    float: left;
    width: 100%;
    position: sticky;
    left: 0;
    right: 0;
    bottom: 0;
}

.hotel-map-item .hotel-list-item {
    float: left;
    width: 100%;
    padding: 0 10px;
}

.map-close {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #fff;
    z-index: 9;
    line-height: 0;
    padding: 5px;
    border-radius: 6px;
    color: var(--button-font-color);
    cursor: pointer;
}

.seeMoreLink {
    float: left;
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    padding-top: 6px;
}

.seeMoreLink a {
    font-size: 14px;
    letter-spacing: 1.17px;
    line-height: 18px;
    font-family: $fontBold;
}

.seeMoreLink a span {
    display: block;
}

.mdl-radio__label {
    white-space: normal;
}

.mdl-radio,
.mdl-radio.is-upgraded {
    padding: 0px;
}

@media (max-width: 767px) {
    .filter-strip {
        position: relative;
    }
    .filter-strip-inner {
        height: 48px;
    }
    .filter-item {
        padding: 12px 10px;
    }
    .filter-item-link {
        color: #fff;
        font-size: 12px;
        letter-spacing: 0.51px;
    }
    .filter-item {
        position: static;
    }
    .modalAirportFilterInfo .modal-header {
        background-color: #fff;
        color: #000;
        font-size: 14px;
        padding: 16px 8px 2px 22px !important;
    }
    .modalAirportFilterInfo .modal-header h5 {
        font-weight: normal;
        font-size: 14px;
    }
    .modalAirportFilterInfo .close {
        margin: -4px 4px 0 0;
        padding: 0;
    }
    .modalAirportFilterInfo .close i {
        font-size: 24px;
        color: #ADADAD;
    }
    .hotel-map-item {
        bottom: 15px;
    }
}

@media (max-width: 359px) {
    .filter-item {
        padding: 12px 5px;
    }
}

.filter-search-box-container {
    float: left;
    width: 100%;
    margin: 0px 0;
    margin-bottom: 20px;
    padding: 0 7px;
    position: relative;
    z-index: 2;
}

.filter-search-box-containerScroll {
    float: left;
    width: 100%;
    margin: 25px 0;
    padding: 0 7px;
    position: relative;
    z-index: 2;
}

.filter-search-box {
    float: left;
    width: 100%;
    position: relative;
    margin-top: 0px !important;
}

.filter-search-box input {
    font-size: 12px;
    padding-left: 42px;
    letter-spacing: 0.51px;
    line-height: 15px;
    border: 1px solid #E4E4E4;
    background-color: #FFFFFF;
    height: 43px;
    width: 100%;
}

.filter-search-box input::placeholder {
    color: #A5A4A3;
}

.filter-search-box img {
    position: absolute;
    left: 10px;
    top: 7px;
}

.flight-list-sorting {
    position: relative;
}

.flight-list-sorting-container {
    float: left;
    width: 100%;
    padding: 0 11px;
}

.flight-list-sorting-container-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    float: left;
    width: 100%;
    padding: 19px 0;
}

@media (max-width: 767px) {
    .flight-list-sorting-container {
        padding: 0 19px;
    }
    .flight-list-sorting-container-inner {
        padding: 15px 0 0px;
    }
    .filter-search-box {
        float: left;
        width: 100%;
        position: relative;
        margin-top: 7px !important;
    }
    .filter-search-box-container {
        padding: 0 0px !important;
        margin: 0px 0;
    }
    .filter-search-box-containerScroll {
        padding: 0 0px !important;
        margin: 0px 0;
    }
    .filter-search-box img {
        width: 18px;
        left: 7px;
    }
    .filter-search-box input {
        padding-left: 30px;
        height: 31px;
    }
    .flight-list-date {
        font-size: 14px;
        letter-spacing: -0.62px;
        line-height: 16px;
    }
    .hotel-list {
        padding-top: 4px;
    }
}