import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HotelResultRoutingModule } from './hotel-result.routing.module';
import { hotelResultComponent } from './hotel-result.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ShareModule } from '../share.module';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { NgSelectModule } from '@ng-select/ng-select';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';



@NgModule({
  imports: [
    CommonModule,
    HotelResultRoutingModule,
    NgbModule,
    ShareModule,
 //   AgmCoreModule.forRoot({ apiKey: 'AIzaSyA8z-JfNhr9cQgPz58usOioKHIXWSJsvy0' }),
    NgxSmartModalModule,
    NgSelectModule,
    BsDatepickerModule,
    ReactiveFormsModule,
    FormsModule,
  ],
  declarations: [
    hotelResultComponent,

  ],
})
export class HotelResultModule {

}