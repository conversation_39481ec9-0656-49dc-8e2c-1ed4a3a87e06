<div [hidden]="isMobile && initialMapListFilterText == 'map'">
    <email-header></email-header>
</div>
<div class="main-wrapper">
    <div class="content">
        <div class="container">
            <div [hidden]="isMobile && initialMapListFilterText == 'map'">
                <hotel-request-view [backToSearchSteps]="1"></hotel-request-view>
            </div>
            <div *ngIf="(isSearchResultsCount(orgHotelSearchResponse) === 0 && callComplete && !this.searchFroomMap)"
                class="no-flight-found-container text-center" style="display: block;">
                <img style="margin-top: 20px;" src="assets/images/no-flight-found.png" />
                <div class="text1 text-danger" style="font-size: 18px;">{{resultErrorMessage}}</div>
            </div>
            <div class="hotel-list-container"  style="display: block;">
                <div style="position: sticky;top:0;z-index: 5;" 
                    *ngIf="(isSearchResultsCount(orgHotelSearchResponse) > 0 && bookingWizardStep==0) && callComplete  || this.searchFroomMap">

                    <div *ngIf="(isSearchResultsCount(orgHotelSearchResponse) > 0 && bookingWizardStep==0) && callComplete "
                        class="filter-strip" [hidden]="isFullScreenMapShowing()">
                        <div class="filter-strip-inner">
                            <ul>
                                <li class="filter-item">
                                    <span class="filter-item-link" href="javascript:void(0)">
                                        <span class="icon icon-setting"></span>
                                    </span>
                                </li>
                                <li class="filter-item" id="filter-container-stops1">
                                    <ng-select class="filter-select-box ngSelectStatic" [closeOnSelect]="false" #stops
                                        appendTo="#filter-container-stops1" dropdownPosition="bottom"
                                        [items]="ratingOptions" [searchable]="false" [clearable]="false">
                                        <ng-template ng-header-tmp>
                                            <div class="selectox-header">
                                                <span>{{'hotelResult.FilterbyStarRating' | translate}}
                                                </span>
                                                <span class="selectBox-remove" (click)="stops.toggle()"><span
                                                        class="material-icons">clear</span></span>
                                            </div>
                                        </ng-template>
                                        <ng-template ng-option-tmp let-option="item">
                                            <div
                                                style="float: left; width: 100%; padding-bottom: 36px;margin-top: -6px;">
                                                <div style="float: left; width: 100%;">
                                                    <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect"
                                                        for="any">
                                                        <input type="checkbox" id="any" class="mdl-checkbox__input"
                                                            [checked]="getAnyRatingCheckStatus()"
                                                            (change)="toggleAnyRating()">
                                                        <span class="mdl-checkbox__label">{{'hotelResult.Any' | translate}}</span>
                                                    </label>
                                                </div>

                                                <div style="float: left; width: 100%;">
                                                    <label
                                                        class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect ratingLabel "
                                                        for="starRating">
                                                        <input type="checkbox" class="mdl-checkbox__input"
                                                            id="starRating"
                                                            [checked]="getStarFilterCheckStatus(0, false)"
                                                            (change)="handleStarFilterClick(0, false)">
                                                        <span class="star-rating-template">
                                                            <span class="star-cb-group">
                                                                <input (change)="handleStarFilterClick(false, 0)"
                                                                    type="radio" id="rating-0" name="rating" value="0"
                                                                    class="star-cb-clear" /><label
                                                                    for="rating-0"></label>
                                                                <input (change)="handleStarFilterClick(false, 5)"
                                                                    [checked]="getStarFilterCheckStatus(5, false)"
                                                                    type="radio" id="rating-1" name="rating"
                                                                    value="5" /><label for="rating-1"></label>
                                                                <input (change)="handleStarFilterClick(false, 4)"
                                                                    [checked]="getStarFilterCheckStatus(4, false)"
                                                                    type="radio" id="rating-2" name="rating"
                                                                    value="4" /><label for="rating-2"></label>
                                                                <input (change)="handleStarFilterClick(false, 3)"
                                                                    [checked]="getStarFilterCheckStatus(3, false)"
                                                                    type="radio" id="rating-3" name="rating"
                                                                    value="3" /><label for="rating-3"></label>
                                                                <input (change)="handleStarFilterClick(false, 2)"
                                                                    [checked]="getStarFilterCheckStatus(2, false)"
                                                                    type="radio" id="rating-4" name="rating"
                                                                    value="2" /><label for="rating-4"></label>
                                                                <input (change)="handleStarFilterClick(false, 1)"
                                                                    [checked]="getStarFilterCheckStatus(1, false)"
                                                                    type="radio" id="rating-5" name="rating"
                                                                    value="1" /><label for="rating-5"></label>
                                                            </span>
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </ng-template>
                                        <ng-template ng-footer-tmp>
                                            <div class="selectBox-footer-filter-button">
                                                <button class="btn primary-button"
                                                    (click)="[applyRatingFilter(),stops.toggle()]">{{'hotelResult.Apply'
                                                    | translate}}
                                                </button>
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                    <a class="filter-item-link" href="javascript:void(0)"
                                        attr.data-track="HotelResultsFilter" attr.data-params="filter=Rating"
                                        (click)="stops.toggle()">{{'hotelResult.RATING' | translate}}
                                    </a>
                                </li>
                                <li *ngIf="hotelBrandOptions.length > 1" class="filter-item"
                                    id="filter-container-brand">
                                    <ng-select class="filter-select-box flightTimeTemplate" groupBy="Type"
                                        [closeOnSelect]="false" [multiple]="true" #brand
                                        appendTo="#filter-container-brand" dropdownPosition="bottom"
                                        [searchable]="false" [clearable]="false" [items]="hotelBrandOptions"
                                        bindLabel="value" bindValue="id">
                                        <ng-template ng-header-tmp>
                                            <div class="selectox-header">
                                                <span>{{'hotelResult.FilterbyHotelBrand' | translate}}</span>
                                                <span class="selectBox-remove" (click)="brand.toggle()"><span
                                                        class="material-icons">clear</span></span>
                                            </div>
                                        </ng-template>
                                        <ng-template ng-option-tmp let-option="item">
                                            <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect"
                                                for="{{option.id}}">
                                                <input type="checkbox" id="{{option.id}}" class="mdl-checkbox__input"
                                                    (change)="onHotelChainClicked(option.id, $event)"
                                                    [checked]="isHotelChainChecked(option.id)">
                                                <span class="mdl-checkbox__label">{{option.value}}</span>
                                            </label>
                                        </ng-template>
                                        <ng-template ng-footer-tmp>
                                            <div class="selectBox-footer-filter-button">
                                                <button class="btn primary-button"
                                                    (click)="[applyHotelChainFilter(),brand.toggle()]">{{'hotelResult.Apply'
                                                    | translate}}
                                                </button>
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                    <a class="filter-item-link" href="javascript:void(0)"
                                        attr.data-track="HotelResultsFilter" attr.data-params="filter=Brand"
                                        (click)="brand.toggle()">{{'hotelResult.BRAND' | translate}}</a>
                                </li>
                                <li class="filter-item" id="filter-container-amenities">
                                    <ng-select class="filter-select-box" groupBy="Type" [closeOnSelect]="false"
                                        #amenities appendTo="#filter-container-amenities" dropdownPosition="bottom"
                                        [searchable]="false" [clearable]="false"
                                        [items]="amenitiesOptions | translateOptions" bindLabel="value" bindValue="id">
                                        <ng-template ng-header-tmp>
                                            <div class="selectox-header">
                                                <span>{{'hotelResult.FilterbyAmenities' | translate}}</span>
                                                <span class="selectBox-remove" (click)="amenities.toggle()"><span
                                                        class="material-icons">clear</span></span>
                                            </div>
                                        </ng-template>
                                        <ng-template ng-option-tmp let-option="item">
                                            <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect"
                                                for="{{option.id}}">
                                                <input type="checkbox" id="{{option.id}}" class="mdl-checkbox__input"
                                                    (change)="onHotelAmenityClicked(option.id, $event)"
                                                    [checked]="isHotelAmenityChecked(option.id)">
                                                <span class="mdl-checkbox__label"><img class="amenities-img"
                                                   style="max-width: 20px;"     src="{{option.imgSrc}}" /><span
                                                        class="amenities-value">{{option.value}}</span></span>
                                            </label>
                                        </ng-template>
                                        <ng-template ng-footer-tmp>
                                            <div class="selectBox-footer-filter-button">
                                                <button class="btn primary-button"
                                                    (click)="[applyAmenityFilter(),amenities.toggle()]">{{'hotelResult.Apply'
                                                    | translate}}
                                                </button>
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                    <a class="filter-item-link" href="javascript:void(0)"
                                        attr.data-track="HotelResultsFilter" attr.data-params="filter=Ameneties"
                                        (click)="amenities.toggle()">{{'hotelResult.AMENITIES' | translate}}
                                    </a>
                                </li>
                                <li *ngIf="canShowPolicyFilter()" class="filter-item" id="filter-container-policy">
                                    <ng-select class="filter-select-box" [closeOnSelect]="false" [multiple]="true"
                                        #policy appendTo="#filter-container-policy" dropdownPosition="bottom"
                                        [searchable]="false" [clearable]="false" [items]="policyOptions"
                                        bindLabel="value" bindValue="id"
                                        (open)="handleModalEvents('onOpen', undefined)">
                                        <ng-template ng-header-tmp>
                                            <div class="selectox-header">
                                                <span>{{'filter.FilterbyPolicy' | translate}}</span>
                                                <span class="selectBox-remove" (click)="policy.toggle()"><span
                                                        class="material-icons">clear</span></span>
                                            </div>
                                        </ng-template>
                                        <ng-template ng-option-tmp let-option="item">
                                            <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect"
                                                for="{{option.id}}">
                                                <input type="radio" id="{{option.id}}" name="stopsFilterName"
                                                    class="mdl-radio__button"
                                                    (change)="onPolicySelectionChange(option.id)"
                                                    [checked]="option.selected">
                                                <span class="mdl-radio__label">{{option.value | translate}}</span>
                                            </label>
                                        </ng-template>
                                        <ng-template ng-footer-tmp>
                                            <!-- <div class="policy-link-container">
                                              <a href="javascript:void(0)"><span>configure policy</span> <img src="assets/images/arrow-right.svg" /></a>
                                          </div> -->

                                            <div class="selectBox-footer-filter-button">
                                                <button class="btn primary-button"
                                                    (click)="[policy.toggle(),applyPolicyFilter()]">{{'filter.Apply' |
                                                    translate}}</button>
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                    <a class="filter-item-link" href="javascript:void(0)"
                                        attr.data-track="HotelResultsFilter" attr.data-params="filter=Policy"
                                        (click)="policy.toggle()">{{'filter.POLICY' | translate}}</a>
                                </li>
                            </ul>
                            <ul>

                            </ul>
                        </div>
                    </div>

                    <!-- <hotel-modal-basic *ngIf="initialMapListFilterText == 'list'"></hotel-modal-basic> -->

                    <div class="filter-search-box-container" style="background: var(--light-bg-color);padding-top: 12px;padding-bottom: 12px;">
                        <div *ngIf="(isSearchResultsCount(orgHotelSearchResponse) > 0 && bookingWizardStep==0) && callComplete "
                            class="row">
                            <div class="col-10" style="padding-left: 12px !important;">
                                <div class="filter-search-box" [hidden]="isMobile && initialMapListFilterText == 'map'">
                                    <img src="assets/images/ic_search.svg" />
                                    <input type='text' (focus)="this.boxSelect=true;" (focusout)="this.boxSelect=false;"
                                        [ngStyle]="{'border-color': this.boxSelect ? this.searchService1.darkBgColor:'#F7F7F7'}"
                                        placeholder="{{'hotelResult.SearchbyName' | translate}}"
                                        (input)="onSearchTextChange($event.target.value,'searchBoxHotels')" value="{{getSearchText()}}" />
                                        <div id="searchBoxHotels">
                                            <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'searchBoxHotels')" [hideDelay]="0"
                                            (onClose)="handleModalEvents('onClose', 'searchBoxHotels')"
                                            (onDismiss)="handleModalEvents('onDismiss', 'searchBoxHotels')" [closable]="false"
                                            #searchBoxHotels identifier="searchBoxHotels">
                                            <div class="modal-container flight-modal-container filter-modal modalAirportFilterInfo"
                                              (click)="$event.stopPropagation();">
                                              
                                            
                                              <div class="modal-body1" style="background-color: #fff !important;">
                                                  <div *ngFor="let item of this.newHotelsNames;let i=index">
                                                      <span class="hotelsInDropDown" (click)="selectHotelFromDropdown(item,i,'searchBoxHotels')">{{item.hotelName}}</span>
                                                  </div>
                                                </div>
                                            </div>
                                          </ngx-smart-modal>
                                          </div>
                                </div>
                               
                            </div>
                            <div class="col-2">
                                <li class="filter-item">
                                    <a *ngIf='initialMapListFilterText == "map"' class="addlue"
                                        href="javascript:void(0)" [hidden]="isMobile" style=""
                                        (click)="initialMapListFilterText = 'list' && this.searchService.selectTypeFrom='list';mapSearchButton=false; "><i
                                            [hidden]="isMobile" class="fa fa-list" aria-hidden="true"
                                            style="margin-right:3px;"></i> {{'hotelResult.List' | translate}}</a>
                                    <a *ngIf='initialMapListFilterText == "list" && this.searchService1.mapSupprted' class="addlue"
                                        href="javascript:void(0)" style=""
                                        attr.data-track="HotelResultsMapView"
                                        (click)="initialMapListFilterText = 'map' && this.searchService.selectTypeFrom='map'"><i
                                            class="fa fa-map" aria-hidden="true" style="margin-right:3px;"></i> {{'hotelResult.Map' | translate}}</a>
                                </li>
                            </div>

                        </div>
                        <div class="flight-list-sorting-container">
                                <form
                                    *ngIf="(isSearchResultsCount(hotelSearchResponse) > 0 && bookingWizardStep==0) && callComplete"
                                    [formGroup]="hotelResultForm">
                                    <div class="flight-list-sorting-container-inner">
                                       
                                        <div class="flight-list-sorting" id="sortingContainer">
                                            <div class="filter custom-selectbox" attr.data-track="HotelResultsSortBy"
                                                (click)="sortingDropdown.toggle()">
                                                <div class="select-input12">
                                                    <ng-select #sortingDropdown appendTo="#sortingContainer"
                                                        value="recommanded" dropdownPosition="bottom"
                                                        [searchable]="false" [clearable]="false"
                                                        [items]="sortOptionsHotel | translateOptions" bindLabel="value"
                                                        bindValue="id" formControlName="sortingDropdown"
                                                        (change)="sortOptionChanged($event)">
                                                        <ng-template ng-header-tmp>
                                                            <div class="selectox-header">
                                                                <span>{{'hotelResult.Sortby' | translate}}</span>
                                                                <span class="selectBox-remove"
                                                                    (click)="sortingDropdown.toggle()"><span
                                                                        class="material-icons">clear</span></span>
                                                            </div>
                                                        </ng-template>
                                                    </ng-select>
                                                    <div class="select-overlay"></div>
                                                </div>
                                                <div class="field-value custom-selectbox-value"
                                                    attr.data-track="HotelResultsSortBy">{{this.sortValue | translate}} <span class="control-icon icon-triangle"
                                                   
                                                    attr.data-track="HotelResultsSortBy"></span></div>
                                                
                                            </div>
                                        </div>
                                        <div  *ngIf="this.searchService.policyObject" class="policyReview" (click)="openPolicyModal(policyModal)">
                                                {{'setting.ReviewYourPolicy' | translate}}
                                            </div> 
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div *ngIf="(isSearchResultsCount(hotelSearchResponse) === 0 && bookingWizardStep==0 && callComplete && !this.searchFroomMap)"
                        class="no-flight-found-container text-center">
                        <img style="margin-top: 20px;" src="assets/images/no-flight-found.png" />
                        <div class="text1 text-danger" style="font-size: 18px;">{{resultErrorMessage}}</div>
                        <div> <a class="addlue" (click)="clearAll()" style="cursor:pointer;"> {{'hotelResult.ClearAllFilter' | translate}}</a></div>
                    </div>
                    <div *ngIf="initialMapListFilterText == 'map'">
                        <div *ngIf="this.searchService.selectTypeFrom === 'map' && mapSearchButton && !searchComplete && (isSearchResultsCount(hotelSearchResponse) > 0 || this.searchFroomMap)"
                            style="text-align: center;height: 0px;">
                            <button class="btn primary-button"
                                style="position: relative;z-index: 1;top: 0px;border-radius: 85px;background: var(--light-bg-color);color: var(--hyperlink-color);height: 45px;width: 195px;"
                                (click)="searchHotelOnMap()" [disabled]="searchComplete"> <img
                                    style="width:20px;height:20px;margin-left:0px;vertical-align:inherit;margin-right:10px;"
                                    src="assets/images/ic_search.svg" /> {{'hotelResult.Searchthisarea' | translate}}<loader-dots
                                    *ngIf="searchComplete" class="loaderAlign"></loader-dots></button>
                        </div>
                        <div *ngIf="this.searchService.selectTypeFrom === 'map' && searchComplete"
                            style="text-align: center;height: 0px;">
                            <button class="btn primary-button"
                                style="position: relative;z-index: 100;top: 0px;border-radius: 85px;background: var(--light-bg-color);color: var(--hyperlink-color);opacity:0.7;height: 45px;width: 195px;">
                                <loader-dots class="loaderAlign"></loader-dots> <span
                                    style="position: relative;top: -3px;margin-left:10px;">{{ 'hotelResult.Searchthisarea' | translate}} </span>
                            </button>
                        </div>

                        <div *ngIf="isSearchResultsCount(hotelSearchResponse) > 0 || this.searchFroomMap"
                            class="filter-map-container">
                            <div class="filter-map-container-inner">
                                <div class="map-close" *ngIf="isMobile"
                                    (click)="initialMapListFilterText = 'list' && this.searchService.selectTypeFrom='list'">
                                    <span class="material-icons">clear</span></div>
                                    <google-map
                                    #map
                                    [center]="{ lat: latitudeForDestination, lng: longitudeForDestination }"
                                    [zoom]="zoom"
                                    [options]="mapOptions"
                                    height="400px"
                                    width="100%"
                                    (centerChanged)="centerChange($event)"
                                    (mapReady)="mapReady($event)"
                                  >
                                    <!-- Dynamic hotel markers -->
                                    <map-marker
                                      *ngFor="let data of latLongData; let i = index"
                                      [position]="{ lat: data.latitude, lng: data.longitude }"
                                      [zIndex]="data.markerClicked ? 100 : i"
                                      [label]="{
                                        color: data.markerClicked ? 'black' : 'white',
                                        fontSize: '10px',
                                        fontFamily: 'apercu-mono',
                                        text: getMarkerPrice(data)
                                      }"
                                      [icon]="{
                                        url: data.markerClicked
                                          ? markerImageActive
                                          : data.optionSelectionReason === 'BEST_OPTION'
                                          ? markerImageBest
                                          : markerImage,
                                        scaledSize: {
                                          width: getMarkerWidth(data),
                                          height: 50
                                        }
                                      }"
                                      (mapClick)="markerClick($event, data)"
                                    >
                                    </map-marker>
                                  
                                    <!-- Center marker -->
                                    <map-marker
                                      [position]="{ lat: latitudeForDestination, lng: longitudeForDestination }"
                                      [icon]="marker2"
                                    ></map-marker>
                                  </google-map>
                                  
                            </div>
                            <div *ngIf="isSearchResultsCount(hotelSearchResponse) > 0">
                                <div class="hotel-map-item">
                                    <div class="result-card-box">
                                        <div *ngIf="getRecommendedTextForTransaltion(mapObject) && getRecommendedTextForTransaltion(mapObject)!=='COMPANY_RECOMMENDED' && getRecommendedTextForTransaltion(mapObject)!=='COMPANY_BLACKLISTED'"
                                            class="top-recommandation-box" s>
                                            <p>{{getRecommendedText(mapObject)}}</p>
                                        </div>
                                        <div *ngIf="getRecommendedTextForTransaltion(mapObject)  && getRecommendedTextForTransaltion(mapObject)==='COMPANY_BLACKLISTED'"
                                        class="top-recommandation-box" style="background: #F73F39 !important;">
                                        <p>{{getRecommendedText(mapObject)}}</p>
                                    </div>
                                        <div *ngIf="getRecommendedTextForTransaltion(mapObject) && getRecommendedTextForTransaltion(mapObject)==='COMPANY_RECOMMENDED'"
                                            class="top-recommandation-box1">
                                            <span style="margin-right: 10px;"><i class="fa fa-star"
                                                    style="color:#fff" aria-hidden="true"></i></span>
                                            <p>{{getRecommendedText(mapObject)}}</p>
                                        </div>
                                        <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'leisureTopModal_')"
                                        [hideDelay]="0" (onClose)="handleModalEvents('onClose', 'leisureTopModal_')"
                                        (onDismiss)="handleModalEvents('onDismiss', 'leisureTopModal_')"
                                        [closable]="false" #leisureTopModal_ identifier="leisureTopModal_">
                        
                                        <div class="modal-container flight-modal-container filter-modalpopup modalAirportFilterInfo1"
                                            (click)="$event.stopPropagation();" style="top: -30% !important;">
                                            <div class="modal-header">
                                                <h5 class="modal-title" style="float: left;
                                                width: 100%;
                                                display: -ms-flexbox;
                                                display: flex;
                                                -ms-flex-pack: justify;
                                                justify-content: space-between;
                                                font-size: 12px;
                                                white-space: nowrap;">  {{'hotelResult.Recommendedhotels' | translate}}</h5>
                                                <button type="button" class="close" data-dismiss="modal"
                                                    (click)="onSmartModelCancel('leisureTopModal_')">
                                                    <i class="material-icons">close</i>
                                                </button>
                                            </div>
                        
                                            <div class="modal-body" style="padding: 18px 15px 17px 25px !important;">
                                                <div  style="margin-top: 5px;margin-bottom: 5px;margin-left: 2px;">
                                                    
                                                    <div style="display: flex;cursor: pointer;margin-bottom: 10px;;"
                                                    *ngIf="(getRecommendedTextForTransaltion(this.selectedHotel)!=='COMPANY_RECOMMENDED') && (getRecommendedTextForTransaltion(this.selectedHotel)!=='COMPANY_BLACKLISTED')"
                                                    class=""
                                                    (click)="onUpdateHotel('recommanded')"><span>
                                                    <i class="fa fa-heart hearctIconPopup" style="color: #27c198;position: relative;
                                                    top: 2px;"
                                                        aria-hidden="true"></i></span><span class="selectox-header" style="margin-left:-5px;align-items:center;white-space:normal !important; ">{{'hotelResult.MarkAsCompanyRecommended'| translate}} </span></div>
                                                        <div
                                                    *ngIf="(getRecommendedTextForTransaltion(this.selectedHotel)!=='COMPANY_RECOMMENDED') && (getRecommendedTextForTransaltion(this.selectedHotel)!=='COMPANY_BLACKLISTED')"
                                                    class=""style="display: flex;cursor:pointer;"
                                                    (click)="onUpdateHotel('blacklisted')"><span>
                                                    <i class="fa fa-heart hearctIconPopup" style="color: #F73F39;position: relative;
                                                    top: 2px;"
                                                        aria-hidden="true"></i></span><span class="selectox-header" style="margin-left:-5px;align-items: center;white-space:normal !important;">{{'hotelResult.MarkAsCompanyBlacklisted'| translate}} </span></div>
                                                <div
                                                    *ngIf="getRecommendedTextForTransaltion(this.selectedHotel) ==='COMPANY_RECOMMENDED' || getRecommendedTextForTransaltion(this.selectedHotel)==='COMPANY_BLACKLISTED'"
                                                    class="" style="display: flex;cursor: pointer;"
                                                    (click)="onUpdateHotel('normalize')"> <span><i
                                                        class="fa fa-heart-o hearctIconPopup" class="addlue" style="position: relative;
                                                        top: 2px;"
                                                        aria-hidden="true"></i></span><span *ngIf="getRecommendedTextForTransaltion(this.selectedHotel)==='COMPANY_RECOMMENDED'"  class="selectox-header" style="margin-left:-5px;align-items: center;white-space:normal !important;">{{'hotelResult.Removefromcompanyrecommendation'| translate}} </span><span *ngIf="getRecommendedTextForTransaltion(this.selectedHotel )==='COMPANY_BLACKLISTED'" class="selectox-header" style="margin-left:-5px;align-items: center;white-space:normal !important;">{{'hotelResult.Removefromcompanyblacklisted'| translate}} </span></div>   
                                                
                        
                                            </div> 
                                            </div>
                                           
                                           
                                        </div>
                                    </ngx-smart-modal>
                                        <div class="result-card-box-inner"
                                            >
                                            <div class="result-card-left">
                                                <img onerror="this.onerror=null;this.src='assets/images/hotel/hotel.png'"
                                                    src="{{mapObject.thumbnailImage.url}}" />
                                                    <div *ngIf="isUserCompanyManager()" id="leisureTopModal_" class="d-block d-lg-none"  style="margin-bottom:10px;">
                                                        <span 
                                                        *ngIf="getRecommendedTextForTransaltion(mapObject) && getRecommendedTextForTransaltion(mapObject)==='COMPANY_RECOMMENDED'"
                                                        class="hearctIcon"
                                                        (click)="showRecommendedModal1('leisureTopModal_',mapObject)"> <i
                                                            class="fa fa-heart" style="color: #27c198;"
                                                            aria-hidden="true"></i></span>
                                                            <span 
                                                        *ngIf="getRecommendedTextForTransaltion(mapObject) && getRecommendedTextForTransaltion(mapObject)==='COMPANY_BLACKLISTED'"
                                                        class="hearctIcon"
                                                        (click)="showRecommendedModal1('leisureTopModal_',mapObject)">
                                                        <i class="fa fa-heart" style="color: #F73F39;"
                                                            aria-hidden="true"></i></span>
                                                    <span 
                                                        *ngIf="(getRecommendedTextForTransaltion(mapObject)!=='COMPANY_RECOMMENDED') && (getRecommendedTextForTransaltion(mapObject)!=='COMPANY_BLACKLISTED') || !getRecommendedTextForTransaltion(mapObject)"
                                                        class="hearctIcon"
                                                        (click)="showRecommendedModal('leisureTopModal_',mapObject)"> <i
                                                            class="fa fa-heart-o" class="addlue"
                                                            aria-hidden="true"></i></span>
                                                          
                                                    </div>
                                              
                                            </div>
                                            <div class="result-card-middle">
                                                <div (click)="onHotelSelectionMobile(mapObject,y,'map')" class="result-card-middle-top">
                                                    <div class="result-card-middle-top-left">
                                                        <div class="hotel-name">{{mapObject.hotelName}}</div>
                                                        <div class="hotel-distance-address">
                                                            <span class="hotel-distance">{{mapObject.distanceMile |
                                                                number : '.2-2'}} {{'hotelResult.miles' |
                                                                translate}}</span>
                                                            <span class="hotel-address">{{mapObject.address}}</span>
                                                        </div>
                                                        <div class="star-container  d-block d-lg-none">
                                                            <span
                                                                *ngFor="let starType of getRatingStarsMap(mapObject.starRating); let i = index;"
                                                                class="star">
                                                                <img *ngIf="starType == 'full'"
                                                                    src="assets/images/hotel/star-filled.svg" />
                                                                <img *ngIf="starType == 'half'"
                                                                    src="assets/images/hotel/halfstar.png" />
                                                                <img *ngIf="starType == 'none'"
                                                                    src="assets/images/hotel/star.svg" />
                                                            </span>
                                                            <span *ngIf="mapObject.ups" style="margin-left:10px;">
                                                                <span
                                                                    style="color:green;font-size:14px;margin-right:10px;"><img
                                                                        class="feedbackImage"
                                                                        src="assets/images/Untitled_design-2_copy.png">{{mapObject.ups}}</span>
                                                            </span>
                                                            <span *ngIf="mapObject.downs">
                                                                <span
                                                                    style="color:red;font-size:14px;margin-right:15px;"><img
                                                                        class="feedbackImage"
                                                                        src="assets/images/Untitled_design_copy.png">{{mapObject.downs}}</span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="result-card-middle-top-right">
                                                        <div class="hotel-price d-block d-md-none">
                                                                {{getPerNightPrice(mapObject) | currency :
                                                                    getCurrencySymbol(mapObject) : 'code': '1.0-0' }}/<span class="lowercase">{{'bookingHistory.Night' | translate}}</span>
                                                           </div>
                                                            <div 
                                                                    class="hotels-taxes-fees d-block d-md-none" style="color: #000000">
                                                                    {{getAbsoluteNumber(mapObject,mapObject) |
                                                                        currency : getCurrencySymbol(mapObject) :
                                                                        'code': '1.0-0'}}     {{'setting.Totalperroom' | translate}} 
                                                                </div>
                                                        <div class="duration-section car-duration">
                                                            <img class="inlineblock icon-margin"
                                                                src="assets/images/car.svg" />
                                                            <span class="inlineblock">{{mapObject.drivingTime}}
                                                                {{'hotelResult.min' | translate}}</span>
                                                        </div>
                                                        <div *ngIf="mapObject.walkingTime < 30"
                                                            class="duration-section walk-duration">
                                                            <img class="inlineblock icon-margin"
                                                                src="assets/images/walk.svg" />
                                                            <span class="inlineblock">{{mapObject.walkingTime}}
                                                                {{'hotelResult.min' | translate}}</span>
                                                        </div>
                                                        <div class=" d-block d-md-none"
                                                            style="width:max-content;float:right;">
                                                            <span *ngIf="mapObject.ups" style="margin-left:5px;">
                                                                <span class="upVote"><img class="feedbackImage"
                                                                        src="assets/images/Untitled_design-2_copy.png">{{mapObject.ups}}</span>
                                                            </span>
                                                            <span *ngIf="mapObject.downs">
                                                                <span class="downVote"><img class="feedbackImage"
                                                                        src="assets/images/Untitled_design_copy.png">{{mapObject.downs}}</span>
                                                            </span>
                                                        </div>
                                                        <div *ngIf="getCheckCorporaterate(mapObject)" class="corporateRate d-block d-md-none"><i class="fa fa-star" style="margin-right: 4px;"></i> {{'hotelResult.Corporaterate' | translate}}</div>
                                                    </div>
                                                </div>
                                                <div class="result-card-middle-bottom">
                                                    <div class="result-card-middle-bottom-left">
                                                        <div *ngIf="isUserCompanyManager()" id="leisureTopModal_" class="d-none d-lg-block"  style="margin-bottom:10px;">
                                                            <span 
                                                            *ngIf="getRecommendedTextForTransaltion(mapObject) && getRecommendedTextForTransaltion(mapObject)==='COMPANY_RECOMMENDED'"
                                                            class="hearctIcon"
                                                            (click)="showRecommendedModal1('leisureTopModal_',mapObject)"> <i
                                                                class="fa fa-heart" style="color: #27c198;"
                                                                aria-hidden="true"></i></span>
                                                                <span 
                                                            *ngIf="getRecommendedTextForTransaltion(mapObject) && getRecommendedTextForTransaltion(mapObject)==='COMPANY_BLACKLISTED'"
                                                            class="hearctIcon"
                                                            (click)="showRecommendedModal1('leisureTopModal_',mapObject)">
                                                            <i class="fa fa-heart" style="color: #F73F39;"
                                                                aria-hidden="true"></i></span>
                                                        <span 
                                                            *ngIf="(getRecommendedTextForTransaltion(mapObject)!=='COMPANY_RECOMMENDED') && (getRecommendedTextForTransaltion(mapObject)!=='COMPANY_BLACKLISTED') || !getRecommendedTextForTransaltion(mapObject)"
                                                            class="hearctIcon"
                                                            (click)="showRecommendedModal('leisureTopModal_',mapObject)"> <i
                                                                class="fa fa-heart-o" class="addlue"
                                                                aria-hidden="true"></i></span>
                                                              
                                                        </div>
                                                        <div class="star-container d-none d-lg-block">
                                                            <span
                                                                *ngFor="let starType of getRatingStarsMap(mapObject.starRating); let i = index;"
                                                                class="star">
                                                                <img *ngIf="starType == 'full'"
                                                                    src="assets/images/hotel/star-filled.svg" />
                                                                <img *ngIf="starType == 'half'"
                                                                    src="assets/images/hotel/halfstar.png" />
                                                                <img *ngIf="starType == 'none'"
                                                                    src="assets/images/hotel/star.svg" />
                                                            </span>

                                                        </div>
                                                        <div class="d-none d-md-block" *ngIf="mapObject.ups">
                                                            <span
                                                                style="color:green;font-size:14px;margin-right:10px;"><img
                                                                    class="feedbackImage"
                                                                    src="assets/images/Untitled_design-2_copy.png">{{mapObject.ups}}</span>
                                                        </div>
                                                        <div class="d-none d-md-block" *ngIf="mapObject.downs">
                                                            <span
                                                                style="color:red;font-size:14px;margin-right:15px;"><img
                                                                    class="feedbackImage"
                                                                    src="assets/images/Untitled_design_copy.png">{{mapObject.downs}}</span>
                                                        </div>
                                                      
                                                        <div class="facilities-container">
                                                            <span><img
                                                                    src="assets/images/hotel/wifi{{isAmenityAvailable('AMENITY_TYPE_WIFI',mapObject) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/bar{{isAmenityAvailable('AMENITY_TYPE_BAR',mapObject) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/local_restaurant{{isAmenityAvailable('AMENITY_TYPE_RESTAURANT',mapObject) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/local_cafe{{isAmenityAvailable('AMENITY_TYPE_BKFAST',mapObject) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/bell-call{{isAmenityAvailable('AMENITY_TYPE_ROOMSERVICE',mapObject) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/swimming-pool-person{{isAmenityAvailable('AMENITY_TYPE_POOL',mapObject) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/fitness-dumbbell{{isAmenityAvailable('AMENITY_TYPE_GYM',mapObject) ? '-active' : ''}}.svg" /></span>
                                                                        <span><i class="fa fa-bus" aria-hidden="true" [ngStyle]="{'color' :isAmenityAvailable('AMENITY_TYPE_SHUTTLE',mapObject) ? 'rgb(39,193,153)':'rgb(166,165,164)'}"></i></span>
                                                        </div>
                                                    </div>
                                                    <div class="result-card-middle-bottom-right">
                                                        <div *ngIf="isPolicySet()" class="policy-container">
                                                            <img class="inlineblock icon-margin"
                                                                src="assets/images/hotel/policy{{mapObject.withinPolicy ? '-active' : ''}}.svg" />
                                                            <span class="inlineblock">{{'hotelResult.Policy' |
                                                                translate}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="result-card-right d-none d-md-block">
                                                <div class="hotel-price">
                                                        {{getPerNightPrice(mapObject) | currency :
                                                            getCurrencySymbol(mapObject) : 'code': '1.0-0' }}/<span class="lowercase">{{'bookingHistory.Night' | translate}}</span></div>
                                                    <div   
                                                                    class="hotels-taxes-fees d-none d-md-block" style="color: #000000">
                                                                    {{getAbsoluteNumber(mapObject,mapObject) |
                                                                        currency : getCurrencySymbol(mapObject) :
                                                                        'code': '1.0-0'}}     {{'setting.Totalperroom' | translate}} 
                                                                </div>
                                                    <div *ngIf="getCheckCorporaterate(mapObject)" class="corporateRate d-none d-md-block"><i class="fa fa-star" style="margin-right: 4px;"></i> {{'hotelResult.Corporaterate' | translate}}</div>
                                                <div class="hotel-select-button">
                                                    <button class="btn primary-button"
                                                        (click)=" onHotelSelection(mapObject,y,'map')">{{'hotelResult.SelectRoom'
                                                        | translate}}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                        </div>
                      
                        <div *ngIf="initialMapListFilterText == 'list'">
                           

                            <div *ngIf="isSearchResultsCount(hotelSearchResponse) > 0" class="hotel-list">
                                <div *ngFor="let hotelItem of hotelArray(); let i = index;" class="hotel-list-item">
                                    <div class="result-card-box">
                                        <div *ngIf="getRecommendedTextForTransaltion(hotelItem) && getRecommendedTextForTransaltion(hotelItem)!=='COMPANY_RECOMMENDED' && getRecommendedTextForTransaltion(hotelItem)!=='COMPANY_BLACKLISTED'"
                                            class="top-recommandation-box" >
                                            <p>{{getRecommendedText(hotelItem)}}</p>
                                        </div>
                                        <div *ngIf="getRecommendedTextForTransaltion(hotelItem)  && getRecommendedTextForTransaltion(hotelItem)==='COMPANY_BLACKLISTED'"
                                        class="top-recommandation-box" style="background: #F73F39 !important;">
                                        <p>{{getRecommendedText(hotelItem)}}</p>
                                    </div>
                                        <div *ngIf="getRecommendedTextForTransaltion(hotelItem) && getRecommendedTextForTransaltion(hotelItem)==='COMPANY_RECOMMENDED'"
                                            class="top-recommandation-box1">
                                            <span style="margin-right: 10px;"><i class="fa fa-star" style="color:#fff"
                                                    aria-hidden="true"></i></span>
                                            <p>{{getRecommendedText(hotelItem)}}</p>
                                        </div>
                                        <ngx-smart-modal (onOpen)="handleModalEvents('onOpen', 'leisureTopModal_'+i)"
                                        [hideDelay]="0" (onClose)="handleModalEvents('onClose', 'leisureTopModal_'+i)"
                                        (onDismiss)="handleModalEvents('onDismiss', 'leisureTopModal_'+i)"
                                        [closable]="false" #leisureTopModal_{{i}} identifier="leisureTopModal_{{i}}">
                        
                                        <div class="modal-container flight-modal-container filter-modalpopup modalAirportFilterInfo1"
                                            (click)="$event.stopPropagation();">
                                            <div class="modal-header">
                                                <h5 class="modal-title" style="float: left;
                                                width: 100%;
                                                display: -ms-flexbox;
                                                display: flex;
                                                -ms-flex-pack: justify;
                                                justify-content: space-between;
                                                font-size: 12px;
                                                white-space: nowrap;">  {{'hotelResult.Recommendedhotels' | translate}}</h5>
                                                <button type="button" class="close" data-dismiss="modal"
                                                    (click)="onSmartModelCancel('leisureTopModal_'+i)">
                                                    <i class="material-icons">close</i>
                                                </button>
                                            </div>
                        
                                            <div class="modal-body" style="padding: 18px 15px 17px 25px !important;">
                                                <div  style="margin-top: 5px;margin-bottom: 5px;margin-left: 2px;">
                                                    
                                                    <div style="display: flex;cursor: pointer;margin-bottom: 10px;"
                                                    *ngIf="(getRecommendedTextForTransaltion(this.selectedHotel)!=='COMPANY_RECOMMENDED') && (getRecommendedTextForTransaltion(this.selectedHotel)!=='COMPANY_BLACKLISTED')"
                                                    class=""
                                                    (click)="onUpdateHotel('recommanded')"><span>
                                                    <i class="fa fa-heart hearctIconPopup" style="color: #27c198;position: relative;
                                                    top: 2px;"
                                                        aria-hidden="true"></i></span><span class="selectox-header" style="margin-left:-5px;align-items:center;white-space:normal !important; ">{{'hotelResult.MarkAsCompanyRecommended'| translate}} </span></div>
                                                        <div
                                                    *ngIf="(getRecommendedTextForTransaltion(this.selectedHotel)!=='COMPANY_RECOMMENDED') && (getRecommendedTextForTransaltion(this.selectedHotel)!=='COMPANY_BLACKLISTED')"
                                                    class=""style="display: flex;cursor:pointer;"
                                                    (click)="onUpdateHotel('blacklisted')"><span>
                                                    <i class="fa fa-heart hearctIconPopup" style="color: #F73F39;position: relative;
                                                    top: 2px;"
                                                        aria-hidden="true"></i></span><span class="selectox-header" style="margin-left:-5px;align-items: center;white-space:normal !important;">{{'hotelResult.MarkAsCompanyBlacklisted'| translate}} </span></div>
                                                <div
                                                    *ngIf="getRecommendedTextForTransaltion(this.selectedHotel) ==='COMPANY_RECOMMENDED' || getRecommendedTextForTransaltion(this.selectedHotel)==='COMPANY_BLACKLISTED'"
                                                    class="" style="display: flex;cursor: pointer;"
                                                    (click)="onUpdateHotel('normalize')"> <span><i
                                                        class="fa fa-heart-o hearctIconPopup" class="addlue" style="position: relative;
                                                        top: 2px;"
                                                        aria-hidden="true"></i></span><span *ngIf="getRecommendedTextForTransaltion(this.selectedHotel)==='COMPANY_RECOMMENDED'"  class="selectox-header" style="margin-left:-5px;align-items: center;white-space:normal !important;">{{'hotelResult.Removefromcompanyrecommendation'| translate}} </span><span *ngIf="getRecommendedTextForTransaltion(this.selectedHotel )==='COMPANY_BLACKLISTED'" class="selectox-header" style="margin-left:-5px;align-items: center;white-space:normal !important;">{{'hotelResult.Removefromcompanyblacklisted'| translate}} </span></div>   
                                                
                        
                                            </div> 
                                            </div>
                                           
                                           
                                        </div>
                                    </ngx-smart-modal>
                                        <div class="result-card-box-inner">
                                            <div class="result-card-left">
                                                <img onerror="this.onerror=null;this.src='assets/images/hotel/hotel.png'"
                                                    *ngIf="hotelItem.thumbnailImage"
                                                    src="{{hotelItem.thumbnailImage.url}}" />
                                                <img *ngIf="!hotelItem.thumbnailImage"
                                                    src="assets/images/hotel/hotel.png" />
                                                <!-- <hotel-async-images [hotelItem]="hotelItem" ></hotel-async-images> -->
                                                <div *ngIf="isUserCompanyManager()" id="leisureTopModal_{{i}}" class="d-block d-lg-none">
                                                    <span 
                                                    *ngIf="getRecommendedTextForTransaltion(hotelItem) && getRecommendedTextForTransaltion(hotelItem)==='COMPANY_RECOMMENDED'"
                                                    class="hearctIcon"
                                                    (click)="showRecommendedModal1('leisureTopModal_'+i,hotelItem)"> <i
                                                        class="fa fa-heart" style="color: #27c198;"
                                                        aria-hidden="true"></i></span>
                                                        <span 
                                                    *ngIf="getRecommendedTextForTransaltion(hotelItem) && getRecommendedTextForTransaltion(hotelItem)==='COMPANY_BLACKLISTED'"
                                                    class="hearctIcon"
                                                    (click)="showRecommendedModal1('leisureTopModal_'+i,hotelItem)">
                                                    <i class="fa fa-heart" style="color: #F73F39;"
                                                        aria-hidden="true"></i></span>
                                                <span 
                                                    *ngIf="(getRecommendedTextForTransaltion(hotelItem)!=='COMPANY_RECOMMENDED') && (getRecommendedTextForTransaltion(hotelItem)!=='COMPANY_BLACKLISTED') || !getRecommendedTextForTransaltion(hotelItem)"
                                                    class="hearctIcon"
                                                    (click)="showRecommendedModal('leisureTopModal_'+i,hotelItem)"> <i
                                                        class="fa fa-heart-o" class="addlue"
                                                        aria-hidden="true"></i></span>
                                                      
                                                </div>
                                            </div>
                                            <div class="result-card-middle"
                                                (click)="onHotelSelectionMobile(hotelItem,i,'list')">
                                                <div class="result-card-middle-top">
                                                    <div class="result-card-middle-top-left">
                                                        <div class="hotel-name">
                                                            <!--<marquee behavior="slide" direction="right">{{hotelItem.hotelName}}</marquee>-->
                                                            {{hotelItem.hotelName}}
                                                        </div>
                                                        <div class="hotel-distance-address">
                                                            <span class="hotel-distance">{{hotelItem.distanceMile |
                                                                number : '.2-2'}} {{'hotelResult.miles' |
                                                                translate}}</span>
                                                            <span class="hotel-address">{{hotelItem.address}}</span>
                                                        </div>

                                                        <div class="star-container  d-block d-lg-none">
                                                            <span
                                                                *ngFor="let starType of getRatingStarsMap(hotelItem.starRating); let i = index;"
                                                                class="star">
                                                                <img *ngIf="starType == 'full'"
                                                                    src="assets/images/hotel/star-filled.svg" />
                                                                <img *ngIf="starType == 'half'"
                                                                    src="assets/images/hotel/halfstar.png" />
                                                                <img *ngIf="starType == 'none'"
                                                                    src="assets/images/hotel/star.svg" />
                                                            </span>
                                                              
                                                        </div>
                                                    </div>
                                                    <div class="result-card-middle-top-right">
                                                          
                                                        <div class="hotel-price d-block d-md-none">
                                                                {{getPerNightPrice(hotelItem) | currency :
                                                                    getCurrencySymbol(hotelItem) : 'code': '1.0-0' }}/<span class="lowercase">{{'bookingHistory.Night' | translate}}</span>
                                                        </div>
                                                        <div  
                                                                    class="hotels-taxes-fees d-block d-md-none" style="color: #000000">
                                                                    {{getAbsoluteNumber(hotelItem,hotelItem) |
                                                                        currency : getCurrencySymbol(hotelItem) :
                                                                        'code': '1.0-0'}}     {{'setting.Totalperroom' | translate}} 
                                                                </div>
                                                        <div>
                                                            <div class="duration-section car-duration">
                                                                <img class="inlineblock icon-margin "
                                                                    src="assets/images/car.svg" />
                                                                <span class="inlineblock">{{hotelItem.drivingTime}}
                                                                    {{'hotelResult.min' | translate}}</span>
                                                            </div>

                                                            <div *ngIf="hotelItem.walkingTime < 30"
                                                                class="duration-section walk-duration">
                                                                <img class="inlineblock icon-margin"
                                                                    src="assets/images/walk.svg" />
                                                                <span class="inlineblock ">{{hotelItem.walkingTime}}
                                                                    {{'hotelResult.min' | translate}}</span>
                                                            </div>
                                                            <div class=" d-block d-md-none"
                                                                style="width:max-content;float:right;">
                                                                <span *ngIf="hotelItem.hotelInfo.upVotes"
                                                                    style="margin-left:5px;">
                                                                    <span class="upVote"><img class="feedbackImage"
                                                                            src="assets/images/Untitled_design-2_copy.png">{{hotelItem.hotelInfo.upVotes}}</span>
                                                                </span>
                                                                <span *ngIf="hotelItem.hotelInfo.downVotes">
                                                                    <span class="downVote"><img class="feedbackImage"
                                                                            src="assets/images/Untitled_design_copy.png">{{hotelItem.hotelInfo.downVotes}}</span>
                                                                </span>
                                                            </div>
                                                            <div *ngIf="getCheckCorporaterate(hotelItem)" class="corporateRate d-block d-md-none"><i class="fa fa-star" style="margin-right: 4px;"></i> {{'hotelResult.Corporaterate' | translate}}</div>
                                                        </div>
                                                      
                                                    </div>
                                                </div>
                                                <div class="result-card-middle-bottom">
                                                    <div class="result-card-middle-bottom-left">
                                                        <div *ngIf="isUserCompanyManager()" id="leisureTopModal_{{i}}" class="d-none d-lg-block">
                                                            <span 
                                                            *ngIf="getRecommendedTextForTransaltion(hotelItem) && getRecommendedTextForTransaltion(hotelItem)==='COMPANY_RECOMMENDED'"
                                                            class="hearctIcon"
                                                            (click)="showRecommendedModal1('leisureTopModal_'+i,hotelItem)"> <i
                                                                class="fa fa-heart" style="color: #27c198;"
                                                                aria-hidden="true"></i></span>
                                                                <span 
                                                            *ngIf="getRecommendedTextForTransaltion(hotelItem) && getRecommendedTextForTransaltion(hotelItem)==='COMPANY_BLACKLISTED'"
                                                            class="hearctIcon"
                                                            (click)="showRecommendedModal1('leisureTopModal_'+i,hotelItem)">
                                                            <i class="fa fa-heart" style="color: #F73F39;"
                                                                aria-hidden="true"></i></span>
                                                        <span 
                                                            *ngIf="(getRecommendedTextForTransaltion(hotelItem)!=='COMPANY_RECOMMENDED') && (getRecommendedTextForTransaltion(hotelItem)!=='COMPANY_BLACKLISTED') || !getRecommendedTextForTransaltion(hotelItem)"
                                                            class="hearctIcon"
                                                            (click)="showRecommendedModal('leisureTopModal_'+i,hotelItem)"> <i
                                                                class="fa fa-heart-o addlue"
                                                                aria-hidden="true"></i></span>
                                                              
                                                        </div>
                                                      
                                                        <div class="star-container d-none d-lg-block">
                                                            <span
                                                                *ngFor="let starType of getRatingStarsMap(hotelItem.starRating); let i = index;"
                                                                class="star">
                                                                <img *ngIf="starType == 'full'"
                                                                    src="assets/images/hotel/star-filled.svg" />
                                                                <img *ngIf="starType == 'half'"
                                                                    src="assets/images/hotel/halfstar.png" />
                                                                <img *ngIf="starType == 'none'"
                                                                    src="assets/images/hotel/star.svg" />
                                                            </span>

                                                        </div>
                                                        <div class="d-none d-md-block"
                                                            *ngIf="hotelItem.hotelInfo.upVotes">
                                                            <span
                                                                style="color:green;font-size:14px;margin-right:10px;"><img
                                                                    class="feedbackImage"
                                                                    src="assets/images/Untitled_design-2_copy.png">{{hotelItem.hotelInfo.upVotes}}</span>
                                                        </div>
                                                        <div class="d-none d-md-block"
                                                            *ngIf="hotelItem.hotelInfo.downVotes">
                                                            <span
                                                                style="color:red;font-size:14px;margin-right:15px;"><img
                                                                    class="feedbackImage"
                                                                    src="assets/images/Untitled_design_copy.png">{{hotelItem.hotelInfo.downVotes}}</span>
                                                        </div>
                                                        <div class="facilities-container">
                                                            <span><img
                                                                    src="assets/images/hotel/wifi{{isAmenityAvailable('AMENITY_TYPE_WIFI',hotelItem) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/bar{{isAmenityAvailable('AMENITY_TYPE_BAR',hotelItem) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/local_restaurant{{isAmenityAvailable('AMENITY_TYPE_RESTAURANT',hotelItem) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/local_cafe{{isAmenityAvailable('AMENITY_TYPE_BKFAST',hotelItem) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/bell-call{{isAmenityAvailable('AMENITY_TYPE_ROOMSERVICE',hotelItem) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/swimming-pool-person{{isAmenityAvailable('AMENITY_TYPE_POOL',hotelItem) ? '-active' : ''}}.svg" /></span>
                                                            <span><img
                                                                    src="assets/images/hotel/fitness-dumbbell{{isAmenityAvailable('AMENITY_TYPE_GYM',hotelItem) ? '-active' : ''}}.svg" /></span>
                                                                    <span><i class="fa fa-bus" aria-hidden="true" [ngStyle]="{'color' :isAmenityAvailable('AMENITY_TYPE_SHUTTLE',hotelItem) ? 'rgb(39,193,153)':'rgb(166,165,164)'}"></i></span>
                                                        </div>
                                                    </div>
                                                    <div class="result-card-middle-bottom-right">
                                                       
                                                        <div *ngIf="isPolicySet()" class="policy-container">
                                                            <img class="inlineblock icon-margin"
                                                                src="assets/images/hotel/policy{{hotelItem.withinPolicy ? '-active' : ''}}.svg" />
                                                            <span class="inlineblock">{{'hotelResult.Policy' |
                                                                translate}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="result-card-right d-none d-md-block">
                                                   
                                                <div class="hotel-price"> {{getPerNightPrice(hotelItem) | currency :
                                                        getCurrencySymbol(hotelItem) : 'code': '1.0-0' }}/<span class="lowercase">{{'bookingHistory.Night' | translate}}</span></div>
                                                    <div *ngIf="this.priceIncludesTaxesAndFees(hotelItem)"  
                                                                    class="hotels-taxes-fees d-none d-md-block" style="color: #000000">
                                                                    {{getAbsoluteNumber(hotelItem,hotelItem) |
                                                                        currency : getCurrencySymbol(hotelItem) :
                                                                        'code': '1.0-0'}}     {{'setting.Totalperroom' | translate}} 
                                                                </div>
                                                    <div *ngIf="getCheckCorporaterate(hotelItem)" class="corporateRate"><i class="fa fa-star"></i> {{'hotelResult.Corporaterate' | translate}}</div>
                                                <div class="hotel-select-button">
                                                    <button class="btn primary-button"
                                                        (click)="onHotelSelection(hotelItem,i,'list')">{{'hotelResult.SelectRoom'
                                                        | translate}}</button>
                                                    <!-- <button class="btn primary-button" (click)="goToSelectRoom()">Select Room</button> -->

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="resultLen">
                                    <div class="seeMoreLink" *ngIf=" !isSeenComplete">
                                        <a href="javascript:void(0)" attr.data-track="SeeMoreHotelResults"
                                            (click)="loadAllItems()">
                                            <span attr.data-track="SeeMoreHotelResults">{{'hotelResult.SEEMOREOPTIONS' |
                                                translate}}</span>
                                            <span><i class="fa fa-chevron-down" style="
                                                position: relative;
                                                top: -5px;
                                            "></i></span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <app-navigation></app-navigation>

                            <div class="filter-map-container" *ngIf="initialMapListFilterText == 'map'">
                                <div class="filter-map-container-inner">
                                    <div class="map-close" *ngIf="isMobile"
                                        (click)="initialMapListFilterText = 'list' && this.searchService.selectTypeFrom='list'">
                                        <span class="material-icons">clear</span></div>
                                    <agm-map [latitude]="lat" [longitude]="lng" (mapClick)="placeMarker($event)">
                                        <agm-marker class="marker-class" (markerClick)="markerClick($event,data)"
                                            *ngFor="let data of latLongData; let i = index" [latitude]="data.lat"
                                            [longitude]="data.long"
                                            [label]="{color: data.markerClicked ? 'black':'white', fontSize:'12px', fontFamily:'apercu-mono', text: data.hotelPrice}"
                                            [iconUrl]="{url: data.markerClicked ? markerImageActive : markerImage}">
                                        </agm-marker>
                                        <agm-marker [iconUrl]="marker2" [latitude]="lat" [longitude]="lng"></agm-marker>
                                    </agm-map>
                                </div>

                                <div class="hotel-map-item">
                                    <div class="container">
                                        <div class="hotel-list-item">
                                            <div class="result-card-box">
                                                <div class="top-recommandation-box" *ngIf="mapObject.isRecommanded">
                                                    <p> {{'hotelResult.TOPRECOMMENDATIONFORYOU' | translate}}</p>
                                                </div>
                                                <div class="result-card-box-inner" (click)="selectHotelMobile()">
                                                    <div class="result-card-left">
                                                        <img onerror="this.onerror=null;this.src='assets/images/hotel/hotel.png'"
                                                            src="https://travelport.leonardocontentcloud.com/imageRepo/3/0/75/137/341/NYCMF_33154505_P.jpg" />
                                                    </div>
                                                    <div class="result-card-middle">
                                                        <div class="result-card-middle-top">
                                                            <div class="result-card-middle-top-left">
                                                                <div class="hotel-name">{{mapObject.hotelName}}</div>
                                                                <div class="hotel-distance-address">
                                                                    <span
                                                                        class="hotel-distance">{{mapObject.hotelDistance}}</span>
                                                                    <span
                                                                        class="hotel-address">{{mapObject.hotelAddress}}</span>
                                                                </div>
                                                                <div class="star-container  d-block d-lg-none">
                                                                    <span class="star"><img
                                                                            src="assets/images/hotel/star-filled.svg" /></span>
                                                                    <span class="star"><img
                                                                            src="assets/images/hotel/star-filled.svg" /></span>
                                                                    <span class="star"><img
                                                                            src="assets/images/hotel/star-filled.svg" /></span>
                                                                    <span class="star"><img
                                                                            src="assets/images/hotel/star-filled.svg" /></span>
                                                                    <span class="star"><img
                                                                            src="assets/images/hotel/star.svg" /></span>
                                                                </div>
                                                            </div>
                                                            <div class="result-card-middle-top-right">
                                                                <div class="hotel-price d-block d-md-none">$750</div>
                                                                <div class="duration-section car-duration">
                                                                    <img class="inlineblock icon-margin"
                                                                        src="assets/images/car.svg" />
                                                                    <span class="inlineblock">6 min</span>
                                                                </div>
                                                                <div class="duration-section walk-duration">
                                                                    <img class="inlineblock icon-margin"
                                                                        src="assets/images/walk.svg" />
                                                                    <span class="inlineblock">8 min</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="result-card-middle-bottom">
                                                            <div class="result-card-middle-bottom-left">
                                                                <div class="star-container d-none d-lg-block">
                                                                    <span><img
                                                                            src="assets/images/hotel/star-filled.svg" /></span>
                                                                    <span><img
                                                                            src="assets/images/hotel/star-filled.svg" /></span>
                                                                    <span><img
                                                                            src="assets/images/hotel/star-filled.svg" /></span>
                                                                    <span><img
                                                                            src="assets/images/hotel/star-filled.svg" /></span>
                                                                    <span><img
                                                                            src="assets/images/hotel/star.svg" /></span>
                                                                </div>
                                                                <div class="facilities-container">
                                                                    <span><img
                                                                            src="assets/images/hotel/wifi-active.svg" /></span>
                                                                    <span><img
                                                                            src="assets/images/hotel/bar.svg" /></span>
                                                                    <span><img
                                                                            src="assets/images/hotel/local_restaurant-active.svg" /></span>
                                                                    <span><img
                                                                            src="assets/images/hotel/local_cafe-active.svg" /></span>
                                                                    <span><img
                                                                            src="assets/images/hotel/bell-call-active.svg" /></span>
                                                                    <span><img
                                                                            src="assets/images/hotel/swimming-pool-person.svg" /></span>
                                                                    <span><img
                                                                            src="assets/images/hotel/fitness-dumbbell.svg" /></span>
                                                                </div>
                                                            </div>
                                                            <div class="result-card-middle-bottom-right">
                                                                <div class="policy-container">
                                                                    <img class="inlineblock icon-margin"
                                                                        src="assets/images/hotel/policy-active.svg" />
                                                                    <span class="inlineblock">Policy</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="result-card-right d-none d-md-block">
                                                        <div class="hotel-price">$750</div>
                                                        <div class="hotel-select-button">
                                                            <button class="btn primary-button"
                                                                (click)="goToSelectRoom()">Select Room</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                
                <ng-template #addRecommended let-modal>
                    <div class="modal-header">
                        <h5 class="modal-title">
                             {{'hotelResult.Recommendedhotels' | translate}}
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel1()">
                            <i class="material-icons" style="color:#fff;">close</i>
                        </button>
                    </div>
                    <div class="modal-body" style=" text-align: left !important;">
                        <div style="margin-top: 30px;margin-bottom: 30px;margin-left: 8px;">
                            <div class="row" *ngFor="let hotelItem of this.selectRecommendedHotel">
                                <div class="result-card-box-inner">
                                    <div class="result-card-left">
                                        <img onerror="this.onerror=null;this.src='assets/images/hotel/hotel.png'"
                                            *ngIf="hotelItem.thumbnailImage" src="{{hotelItem.thumbnailImage.url}}" />
                                        <img *ngIf="!hotelItem.thumbnailImage" src="assets/images/hotel/hotel.png" />
                                    </div>
                                    <div class="result-card-middle">
                                        <div class="result-card-middle-top">
                                            <div class="result-card-middle-top-left">
                                                <div class="hotel-name">
                                                    {{hotelItem.hotelName}}
                                                </div>
                                                <div class="hotel-distance-address">
                                                    <span class="hotel-distance">{{hotelItem.distanceMile | number :
                                                        '.2-2'}} {{'hotelResult.miles' | translate}}</span>
                                                    <span class="hotel-address">{{hotelItem.address}}</span>
                                                </div>

                                                <div class="star-container">
                                                    <span
                                                        *ngFor="let starType of getRatingStarsMap(hotelItem.starRating); let i = index;"
                                                        class="star">
                                                        <img *ngIf="starType == 'full'"
                                                            src="assets/images/hotel/star-filled.svg" />
                                                        <img *ngIf="starType == 'half'"
                                                            src="assets/images/hotel/halfstar.png" />
                                                        <img *ngIf="starType == 'none'"
                                                            src="assets/images/hotel/star.svg" />
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="margin-top: 20px;">
                                <p> {{'hotelResult.Markthishotelascompanyrecommendedtopriortizeamongstallotherhotelsintheareaofyourallemployees' | translate}}</p>
                            </div>
                        </div>
                        <div class="modal-form-button" style="text-align: center;">
                            <button class="btn btn-secondary" [disabled]="this.updateHotel"
                                (click)="onConfirmUpdateHotel()"><span class="add">{{'hotelResult.Addascompanyrecommended' | translate}}</span></button>
                        </div>
                    </div>
                </ng-template>

                <ng-template #addRecommended let-modal>
                    <div class="modal-header">
                        <h5 class="modal-title">
                           {{'hotelResult.Recommendedhotels' | translate}}
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel1()">
                            <i class="material-icons" style="color:#fff;">close</i>
                        </button>
                    </div>
                    <div class="modal-body" style=" text-align: left !important;">
                        <div style="margin-top: 30px;margin-bottom: 30px;margin-left: 8px;">
                            <div class="row" *ngFor="let hotelItem of this.selectRecommendedHotel">
                                <div class="result-card-box-inner">
                                    <div class="result-card-left" style="width:30%;">
                                        <img onerror="this.onerror=null;this.src='assets/images/hotel/hotel.png'"
                                            *ngIf="hotelItem.thumbnailImage" src="{{hotelItem.thumbnailImage.url}}" />
                                        <img *ngIf="!hotelItem.thumbnailImage" src="assets/images/hotel/hotel.png" />
                                    </div>
                                    <div class="result-card-middle" style="width: 100%;">
                                        <div class="result-card-middle-top">
                                            <div class="result-card-middle-top-left">
                                                <div class="hotel-name">
                                                    {{hotelItem.hotelName}}
                                                </div>
                                                <div class="hotel-distance-address">
                                                    <span class="hotel-distance">{{hotelItem.distanceMile | number :
                                                        '.2-2'}} {{'hotelResult.miles' | translate}}</span>
                                                    <span class="hotel-address">{{hotelItem.address}}</span>
                                                </div>

                                                <div class="star-container">
                                                    <span
                                                        *ngFor="let starType of getRatingStarsMap(hotelItem.starRating); let i = index;"
                                                        class="star">
                                                        <img *ngIf="starType == 'full'"
                                                            src="assets/images/hotel/star-filled.svg" />
                                                        <img *ngIf="starType == 'half'"
                                                            src="assets/images/hotel/halfstar.png" />
                                                        <img *ngIf="starType == 'none'"
                                                            src="assets/images/hotel/star.svg" />
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="margin-top: 20px;">
                                <p class="mobileMsg">{{'hotelResult.Markthishotelascompanyrecommendedtopriortizeamongstallotherhotelsintheareaofyourallemployees' | translate}}</p>
                            </div>
                        </div>
                        <div class="modal-form-button" style="text-align: center;text-transform: inherit !important;">
                            <button class="btn btn-secondary" [disabled]="this.updateHotel"
                                (click)="onConfirmUpdateHotel()"><span class="add">{{'hotelResult.Addascompanyrecommended' | translate}}</span></button>
                        </div>
                    </div>
                </ng-template>
              
                <ng-template #removeRecommended let-modal>
                    <div class="modal-header">
                        <h5 class="modal-title">
                            {{'hotelResult.Recommendedhotels' | translate}}
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" (click)="onModelCancel1()">
                            <i class="material-icons" style="color:#fff;">close</i>
                        </button>
                    </div>
                    <div class="modal-body" style=" text-align: left !important;">
                        <div style="margin-top: 30px;margin-bottom: 30px;margin-left: 8px;">
                            
                                <div
                                *ngIf="(getRecommendedText(this.selectedHotel)!=='Company recommended') && (getRecommendedText(this.selectedHotel)!=='Company blacklisted')"
                                class="hearctIcon"
                                (click)="onUpdateHotel('recommanded')">
                                <i class="fa fa-heart" style="color: green;position: relative;
                                top: 2px;"
                                    aria-hidden="true"></i><span class="mobileMsg" style="margin-left:8px;">{{'hotelResult.MarkAsCompanyRecommended'| translate}} </span></div>
                                    <div
                                *ngIf="(getRecommendedText(this.selectedHotel)!=='Company recommended') && (getRecommendedText(this.selectedHotel)!=='Company blacklisted')"
                                class="hearctIcon"
                                (click)="onUpdateHotel('blacklisted')">
                                <i class="fa fa-heart" style="color: red;position: relative;
                                top: 2px;"
                                    aria-hidden="true"></i><span class="mobileMsg" style="margin-left:8px;">{{'hotelResult.MarkAsCompanyBlacklisted'| translate}} </span></div>
                            <div
                                *ngIf="getRecommendedText(this.selectedHotel) ==='Company recommended' || getRecommendedText(this.selectedHotel)==='Company blacklisted'"
                                class="hearctIcon"
                                (click)="onUpdateHotel('normalize')"> <i
                                    class="fa fa-heart-o" class="addlue" style="position: relative;
                                    top: 2px;"
                                    aria-hidden="true"></i><span *ngIf="getRecommendedText(this.selectedHotel)==='Company recommended'"  class="mobileMsg" style="margin-left:8px;">{{'hotelResult.Removefromcompanyrecommendation'| translate}} </span><span *ngIf="getRecommendedText(this.selectedHotel )==='Company blacklisted'" class="mobileMsg" style="margin-left:8px;">{{'hotelResult.Removefromcompanyblacklisted'| translate}} </span></div>   
                            

                        </div>
                        <div class="modal-form-button" style="text-align: center;text-transform: inherit !important;">
                            
                        </div>
                    </div>
                </ng-template>
                <ng-template #policyModal let-modal>
                        <div class="table-view">
                            <div class="table-cell-view">
                                <div class="modal-dialog modal-dialog-md" role="document">
                                    <div class="modal-content">
                                        <div *ngIf="this.disabled" class="approval_request_diaglog_bg_clickhandler"></div>
                                        <div class="modal-header1">
                                                <span>
                                                        <img class="footerimage" [src]="this.searchService1.footerLogo">
                                                      </span>
                                            <button *ngIf="!this.disabled" type="button" class="close" data-dismiss="modal"
                                                (click)="onCancel()">
                                                <i class="material-icons">close</i>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <div  style="position: relative;text-align: left;">
                    
                                                <ul >
                                                  <li *ngIf="this.searchService.policyObject.maxHotelRatingAllowed" style="font-size: 18px;display: flex;">
                                                        <div class="bulletPoint"> . </div>  <div>{{'setting.Maximumclassallowance' | translate}}  {{ this.searchService.policyObject.maxHotelRatingAllowed}} star</div>
                                                  </li>
                                                 
                                                  <li *ngIf="this.searchService.policyObject.pricePolicyList && this.searchService.policyObject.pricePolicyList.length > 0"  style="font-size: 18px;">
                                                    <div *ngFor="let item of this.searchService.policyObject.pricePolicyList">
                                                        <div *ngIf="item.type==='markup'" style="display: flex;">
                                                        <div class="bulletPoint"> . </div>  <div>{{'setting.Absolutepricelimitperdayincludingtaxesfees' | translate}} {{getCurrencySymbol1(this.currency)}}{{item.benchMark}}</div>
                                                        </div>
                                                        <div *ngIf="item.type==='markup_over_cheapest'" style="display: flex;">
                                                                <div class="bulletPoint"> . </div>  <div>{{'setting.Relativepricelimitisupto' | translate}} {{getCurrencySymbol1(this.currency)}}{{item.benchMark}} {{'setting.morefromthelowestlogicalfareinaparticularcategory' | translate}}</div>
                                                                </div>
                                                                <div *ngIf="item.type==='percent_markup_over_cheapest'" style="display: flex;">
                                                                        <div class="bulletPoint"> . </div>  <div>{{'setting.Relativepricelimitisupto' | translate}} {{item.benchMark}}% {{'setting.ofthelowestlogicalfareinaparticularcategory' | translate}}</div>
                                                                        </div>
                                                    </div>
                                                       
                                                  </li>
                                                 
                                                  <li *ngIf="this.searchService.policyObject.restrictNonRefundable"  style="font-size: 18px;display: flex;">
                                                        <div class="bulletPoint"> . </div>  <div> {{'setting.Nonrefundableroomratesarerestricted' | translate}} </div>
                                                  </li>
                                                </ul>
                                                
                                             </div>    
                                           
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-template>