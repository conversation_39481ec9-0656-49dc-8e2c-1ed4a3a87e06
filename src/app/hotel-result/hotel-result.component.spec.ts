import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { hotelResultComponent } from './hotel-result.component';

describe('hotelResultComponent', () => {
  let component: hotelResultComponent;
  let fixture: ComponentFixture<hotelResultComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [hotelResultComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(hotelResultComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
