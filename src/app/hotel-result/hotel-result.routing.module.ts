import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { hotelResultComponent } from '../hotel-result/hotel-result.component';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: hotelResultComponent,
    data: { title: 'Hotel Results' }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class HotelResultRoutingModule {

}