import { Component, OnInit, Input } from '@angular/core';
import { AbstractControl, FormArray, UntypedFormBuilder, UntypedFormGroup, Validators, FormControl, Form, ValidationErrors } from '@angular/forms';
import { FlightClassType } from '../enum/flight-class.type';
import { NoOfStops } from '../enum/no-of-stops.type';
import { NavigationExtras, Router, ActivatedRoute, Params } from '@angular/router';
//import {policyFilter} from '../enum/policy.type';
import { FlightSearchResponse } from '../entity/flight-search-response';
import { SearchService } from '../search.service';
import { FilterService } from '../filter.service';
import { Constants } from '../util/constants';
import { DeviceDetailsService } from '../device-details.service';
import { Subscription } from 'rxjs';
import { ScoringLabel } from '../entity/scoring-label';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { FilterType } from '../enum/filter.type';
import { HotelQueryParam } from '../entity/hotel-query-param';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { HotelSearchService } from '../hotel-search.service';
import { HotelSearchResponse } from '../entity/hotel-search-response.';
import { SearchActionType } from '../enum/search-action.type';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { CommonUtils } from '../util/common-utils';
import { HotelResult } from '../entity/hotel-result';
import { hotelModalBasic } from '../hotel-modal/hotel-modal.component';
import { UserAccountService } from '../user-account.service';
import { UserAccountInfo } from '../entity/user-account-info';
import { ALL_HOTELCHAINS } from '../util/hotel-chains';
import { SearchResultService } from '../search-result.service';
import { HotelDetailResult } from '../entity/hotel-detail-result';
import { getCurrencySymbol, PlatformLocation } from '@angular/common';
import { CurrencyPipe } from '@angular/common';
import { Locale } from 'ngx-bootstrap/chronos/locale/locale.class';
import { ImageRequest } from '../entity/image-request';
import { _ } from 'src/app/util/title';
import { TranslateService } from "@ngx-translate/core";
import { ConnectionService } from 'ng-connection-service';
import { PopupComponent } from '../popup/popup.component';
import { TraflaImage } from '../entity/trafla-image';
import { NavigationUtil } from '../util/navigation-util';
import { HotelFilterDTO } from '../entity/hotel-filter-dto';
import { Title } from '@angular/platform-browser';
import { BookingService } from '../booking.service';
import { ToastrService } from 'ngx-toastr';
import { JsonDiscriminatorValue } from '../util/ta-json/src';

declare var adjustMapHeightForFullScreen: any;
declare var  addingHeader:any;
declare var getCurrentlyOpenNgxSmartModalIds: any;
declare var setNgxSmartModalOpenStateClosed: any;
@Component({
    selector: 'hotel-result-component',
    templateUrl: './hotel-result.component.html',
    styleUrls: ['./hotel-result.component.scss'],
    standalone: false
})


export class hotelResultComponent implements OnInit {
  hotelResultForm: UntypedFormGroup;
  boxSelect = false;
  currency='USD';
  policyOptions = Constants.POLICY_OPTIONS;
  isMobile: boolean=false;
  hotelElementCount = 5;
  searchComplete = false;
  searchFroomMap = false;
  mapSearchButtonShow = 0;
  validity = true;
  mapSearchButton = false;
  isSeenComplete: boolean = false;
  deviceSubscription: Subscription;
  hotelBrandOptions = Constants.HOTEL_BRAND_OPTIONS;
  amenitiesOptions = [{ id: 'ANY', value: _('ngOption.Any') }, { id: 'AMENITY_TYPE_BKFAST', value: _('ngOption.Breakfast'), imgSrc: 'assets/images/hotel/local_cafe.svg', Type: this.translateService.instant(('hotelResult.MustInclude')) }, { id: 'AMENITY_TYPE_RESTAURANT', value: _('ngOption.Restaurant'), imgSrc: 'assets/images/hotel/local_restaurant.svg', Type: this.translateService.instant(('hotelResult.MustInclude')) }, { id: 'AMENITY_TYPE_BAR', value: _('ngOption.Bar'), imgSrc: 'assets/images/hotel/bar.svg', Type:this.translateService.instant(('hotelResult.MustInclude'))  }, { id: 'AMENITY_TYPE_WIFI', value: _('ngOption.FreeWiFi'), imgSrc: 'assets/images/hotel/wifi.svg', Type: this.translateService.instant(('hotelResult.MustInclude')) }, { id: 'AMENITY_TYPE_ROOMSERVICE', value: _('ngOption.Concierge'), imgSrc: 'assets/images/hotel/bell-call.svg', Type: this.translateService.instant(('hotelResult.MustInclude')) }, { id: 'AMENITY_TYPE_GYM', value: _('ngOption.Gym'), imgSrc: 'assets/images/hotel/fitness-dumbbell.svg', Type: this.translateService.instant(('hotelResult.MustInclude')) }, { id: 'AMENITY_TYPE_POOL', value: _('ngOption.Pool'), imgSrc: 'assets/images/hotel/swimming-pool-person.svg', Type: this.translateService.instant(('hotelResult.MustInclude')) },{ id: 'AMENITY_TYPE_SHUTTLE', value: _('ngOption.Shuttle'), imgSrc: 'assets/images/shuttle.png', Type: this.translateService.instant(('hotelResult.MustInclude')) }];;
  sortOptionsHotel = Constants.SORT_OPTIONS_HOTEL;
  initialMapListFilterText = 'list';
  zoom = 18;
  
  y: number;
 
  mapValidator = false;
  selectRecommendedHotel = [];
  updateHotel = false;
  mapObject: HotelResult;
  currencyPipe: CurrencyPipe = new CurrencyPipe('en_US');
  latLongData = [];
  latitudeForDestination: number;
  longitudeForDestination: number;
  latitudeForWindowSize: number;
  longitudeForWindowSize: number;
  newCenterLat: number;
  newCenterLng: number;
  ratingOptions = [1];
  marker2 = 'assets/images/map_pin.svg';
  markerImage = 'assets/images/map-marker.png';
  markerImageActive = 'assets/images/map-marker-active.png';
  markerImageBest = 'assets/images/map-marker-best.png';
  quickSearchSubscription: Subscription;
  fetchImagesSubscription: Subscription;
  callComplete: boolean = true;
  hotelSearchResponse: HotelSearchResponse;
  orgHotelSearchResponse: HotelSearchResponse;
  hotelSearchQueryParam: HotelQueryParam;
  queryParmsSubscription: Subscription;
  bookingWizardStep: number = 0;
  hotelSearchRequestSubscription: Subscription;
  hotelSearchRequest: any;
  hotelSearchResponseSubscription: Subscription;
  resultLen = true;

  connectionListener: Subscription;

  constructor(private searchService: HotelSearchService,
    public searchService1: SearchService,
    private toastr: ToastrService,
    private activatedRoute: ActivatedRoute,
    private fb: UntypedFormBuilder,
    private bookingService: BookingService,
    private gallopLocalStorage: GallopLocalStorageService,
    private searchResultService: SearchResultService,
    public progressBar: NgxUiLoaderService,
    public router: Router,
    private deviceDetailsService: DeviceDetailsService,
    private filterService: FilterService,
    private userAccountInfoService: UserAccountService,
    public translateService: TranslateService,
    private connectionService: ConnectionService,
    private modalService: BsModalService,
    private titleService: Title,
    private flightSearchService: SearchService,
    public ngxSmartModalService: NgxSmartModalService,
    location1: PlatformLocation) {
    location1.onPopState(() => {
    //  this.searchService1.searchHeaderCliked = true;
    if (this.userAccountInfoService.showRadio) {
      this.searchService1.searchHeaderCliked =false;
      }else {
        this.searchService1.searchHeaderCliked =true;
      }
      if (!this.callComplete) {
        this.bsModalRef1.hide();
        if (this.quickSearchSubscription) {
          this.quickSearchSubscription.unsubscribe();
        }
        this.progressBar.stop(SearchActionType.DETAIL);
      }
    });
    this.createForm();
    if (this.userAccountInfoService 
      && this.userAccountInfoService.getAccountInfo()
      && this.userAccountInfoService.getAccountInfo().userInfo
      && this.userAccountInfoService.getAccountInfo().userInfo.currency
    ) {
      this.currency = this.userAccountInfoService.getAccountInfo().userInfo.currency;
    }
  }

sortValue = this.translateService.instant('ngOption.Recommended');
  private initFilters() {
    this.filterService.hotelFilterDefs = new HotelFilterDTO();
  }
  ngOnInit() {
    //this.titleService.setTitle('Web Search');
   // this.titleService.setTitle('');
   this.searchService.retryHotelFailedBooking= false;
   addingHeader();
   this.titleService.setTitle(this.translateService.instant('search.HotelResults'));
    this.subscribeSearchServiceEvents();
    // this.subscribeSearchResultServiceEvents();
    this.initializeQueryParamsfromRoute();
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });

    this.orgHotelSearchResponse = this.filterService.originalHotelSearchResponse;
    this.hotelSearchQueryParam =
      this.gallopLocalStorage.getItem("hotelSearchRequest") ?
        deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")))
        :
        undefined;

    this.hotelSearchRequest =
      this.gallopLocalStorage.getItem("hotelSearchRequest") ?
        JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest"))
        :
        undefined;
   
    if(!this.filterService.hotelFilterDefs.currentSortOptionId){
    
    this.filterService.hotelFilterDefs.currentSortOptionId = 'recommended';
    }else{
      let sortvaule = this.sortOptionsHotel.filter(item => item.id === this.filterService.hotelFilterDefs.currentSortOptionId);
      if(sortvaule){
      this.sortValue  = sortvaule[0].value;
      this.hotelResultForm.controls['sortingDropdown'].setValue(this.filterService.hotelFilterDefs.currentSortOptionId);
      }
    }
    this.filterResults();
    NavigationUtil.setCurrentNavigationMenu(NavigationUtil.NAVIGATION_MENU_RESULTS);
  }
  mapOptions: google.maps.MapOptions = {
    gestureHandling: 'greedy',
    streetViewControl: false,
    zoomControl: !this.isMobile,
  };
  mapReady(map: google.maps.Map) {
    if (this.isSearchResultsCount(this.hotelSearchResponse) > 0) {
      const bounds = new google.maps.LatLngBounds();
  
      this.latLongData.forEach(data => {
        if (data.fitBounds) {
          bounds.extend({ lat: data.latitude, lng: data.longitude });
        }
      });
  
      bounds.extend({
        lat: this.latitudeForDestination,
        lng: this.longitudeForDestination
      });
  
      map.fitBounds(bounds);
      this.mapSearchButtonShow = 1
    }
  }
  updateFilterHotelChain(preferredHotelChain: Array<string>, otherHotelChain: Array<string>) {
    let hotelList = this.orgHotelSearchResponse ? this.orgHotelSearchResponse.hotelsList : [];
    this.hotelBrandOptions = [];
    this.hotelBrandOptions.push({ 'id': 'ANY', 'value': this.translateService.instant('hotelResult.Any').toString() });
    for (let chainCode of preferredHotelChain) {
      let hotelChainObj = ALL_HOTELCHAINS.find(obj => obj.chainCode === chainCode);
      if (hotelChainObj) this.hotelBrandOptions.push({ 'id': chainCode, 'value': hotelChainObj.chainName, 'Type': this.translateService.instant('hotelResult.Preferred').toString() });
    }
    for (let chainCode of otherHotelChain) {
      let hotelChainObj = ALL_HOTELCHAINS.find(obj => obj.chainCode === chainCode);
      if (hotelChainObj) this.hotelBrandOptions.push({ 'id': chainCode, 'value': hotelChainObj.chainName, 'Type': this.translateService.instant('hotelResult.Other').toString() });
    }
    if (hotelList && hotelList.length > 0) {
      this.hotelBrandOptions.push({ 'id': 'Others', 'value': 'Others', 'Type': this.translateService.instant('hotelResult.Other').toString() });
    }

  }

  subscribeSearchServiceEvents() {

    this.hotelSearchRequestSubscription = this.searchService.hotelRequest$.subscribe((hotelSearchRequest) => {
      this.hotelSearchRequest = hotelSearchRequest;
      if (this.hotelSearchRequest != null) {
       // this.searchService1.multihotelQuery.push(this.hotelSearchRequest);
     //  this.hotelSearchRequest.travellerEmail = this.searchService1.employeeEmail;
        this.gallopLocalStorage.setItem("hotelSearchRequest", JSON.stringify(this.hotelSearchRequest));
      }
    });

    this.hotelSearchResponseSubscription = this.searchService.hotelResponseSubject.subscribe((hotelSearchResponse: HotelSearchResponse) => {

      let hotelCode = this.searchService.hotelCodeHasNoRoom;

      if (hotelSearchResponse && hotelSearchResponse.destination && this.hotelSearchRequest) {
        this.hotelSearchRequest.hotelAddress = hotelSearchResponse.destination.address;
        this.hotelSearchRequest.hotelCity = hotelSearchResponse.destination.city +', '+hotelSearchResponse.destination.state_code +', '+hotelSearchResponse.destination.country_code;
        this.searchService.destination = hotelSearchResponse.destination;
        this.searchService.broadCastHotelRequest(this.hotelSearchRequest);
      }
      if (hotelSearchResponse && this.searchService.hotelCodeHasNoRoom && this.searchService.selectedHotelFromMap) {
        if (this.searchService.selectedHotelFromMap.hotelCode === hotelCode[0]) {
          this.searchService.selectedHotelFromMap = undefined;
        }
      }
      if (hotelSearchResponse && this.searchService.hotelCodeHasNoRoom) {
        for (let code of hotelCode) {
          hotelSearchResponse.hotelsList = hotelSearchResponse.hotelsList.filter(item => {
            return item.hotelCode !== code;
          });
        }
      }

      if (this.searchService.roomPriceChange && this.searchService.roomPriceChange.length > 0 && this.searchService.roomPriceChange[0].hotelCode != null) {

        for (let code of this.searchService.roomPriceChange) {
          hotelSearchResponse.hotelsList.filter(item => {
            if (item.hotelCode === code.hotelCode) {
              item.price = code.price;
              item.withinPolicy = code.inPolicy;
              return true;
            }
          });
          this.filterService.originalHotelSearchResponse.hotelsList.filter(item => {
            if (item.hotelCode === code.hotelCode) {
              item.price = code.price;
              item.withinPolicy = code.inPolicy;
              return true;
            }
          });
        }
      }
      if (hotelSearchResponse && hotelSearchResponse.hotelsList && hotelSearchResponse.hotelsList.length > this.hotelElementCount) {
        this.resultLen = true;
        if (this.isSeenComplete) {
          this.isSeenComplete = false;
        }
      } else {
        this.resultLen = false;

      }
      if(this.bsModalRef1 && this.callComplete){
        this.bsModalRef1.hide();
      }
      this.initialMapListFilterText = this.searchService.selectTypeFrom;
      this.hotelSearchResponse = hotelSearchResponse;

      if (this.hotelSearchResponse) {
        this.updateFilterHotelChain(hotelSearchResponse.preferredHotelChains, hotelSearchResponse.otherHotelChains);
        this.mapDataList(hotelSearchResponse);
        //this.applyPolicyFilter();
      }
    });

  }
  createForm(): void {


    this.hotelResultForm = this.fb.group({
      sortingDropdown: ['recommended']
    });

  }

  isPolicySet() {
    return this.hotelSearchResponse && this.hotelSearchResponse.policySet ? true : false;
  }
  onPolicySelectionChange(value: any) {
    this.filterService.hotelFilterDefs.temp_filter_policy = value;
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }

  changeProperty() {
    this.latLongData.map(function (x) {
      x.markerClicked = false;
      return x;
    });
  }

  markerClick($event, data) {
    let params = "?ua_action=HotelSelectedOnMap";
    this.searchService1.letsTrack(params);
    this.changeProperty();
    data.markerClicked = true;
    data.isRecommanded = true;
    this.mapObject = this.getDimensionsByFind($event.latitude);
    this.searchService.selectedHotelFromMap = this.mapObject;

    this.y = $event._id;

  }

  getDimensionsByFilter(id) {
    return this.latLongData.filter(x => x.latitude === id);
  }

  getDimensionsByFind(id) {
    return this.latLongData.find(x => x.latitude === id);
  }



  //  placeMarker($event){
  //    
  //    
  //  }
  //
  //    onMouseOver(m){
  //      
  //  }



  selectHotelMobile() {
    if (this.isMobile) {
      this.goToSelectRoom();
    }

  }

  goToSelectRoom() {
    // this.requestHotelDetails()
    //  this.router.navigate(["hotelSelection"]);
    // this.gallopLocalStorage.removeItem("passengers");
    // this.gallopLocalStorage.removeItem("passengersFormData");
    window.scrollTo(0, 0);
    this.searchService1.emptyIntervalID();
    this.router.navigate(['/hotelSelection'], { relativeTo: this.activatedRoute, queryParams: {} });
  }

  initializeQueryParamsfromRoute(): void {
    this.queryParmsSubscription = this.activatedRoute.queryParamMap.subscribe((queryParams: Params) => {

      if (queryParams && queryParams.params && Object.keys(queryParams.params).length > 0) {
        if (queryParams.params.resultFound && this.filterService.originalHotelSearchResponse  
          && this.filterService.originalHotelSearchResponse.hotelsList && this.filterService.originalHotelSearchResponse.hotelsList.length > 0) return;
        this.initFilters();
        let queryParam: HotelQueryParam = deserialize(JSON.parse(decodeURIComponent(queryParams.params.query)), HotelQueryParam);
        this.hotelSearchQueryParam = queryParam;
        this.bookingWizardStep = Number(queryParams.params.step);

        if (this.bookingWizardStep === 0) {
          this.filterService.originalHotelSearchResponse = null;
          this.mapSearchButton = false;
          this.searchHotel();
        }
      }

    });
  }

  searchHotelOnMap() {
    let currentlocation = this.newCenterLat + "," + this.newCenterLng;
    this.hotelSearchQueryParam.currentLocation = currentlocation;
    if (this.quickSearchSubscription) {
      this.quickSearchSubscription.unsubscribe();
    }

    this.searchComplete = true;
    this.mapSearchButton = false;
    this.searchFroomMap = true;
    //this.searchHotelDetailed(this.hotelSearchQueryParam);
    this.progressBar.start(SearchActionType.DETAIL);
    this.quickSearchSubscription = this.searchService.searchHotel(this.hotelSearchQueryParam, SearchActionType.DETAIL).subscribe(res => {
      this.callComplete = true;
      if (this.connectionListener) {
        this.connectionListener.unsubscribe();
      }
      setTimeout(() => {
        this.resultErrorMessage = this.translateService.instant('hotelResult.Nohotelsfound').toString();
        if (res.success === true && res.data) {
          // this.bsModalRef1.hide();
          this.searchComplete = false;
          this.zoom = 18;
          //  this.mapSearchButton=false;
          // this.mapSearchButtonShow=0;
          setTimeout(() => {
            //this.mapReady(event);
          }, 0);
          this.searchFroomMap = false;
          this.mapSearchButton = false;
          this.progressBar.stop(SearchActionType.DETAIL);
          this.userAccountInfoService.approvalRequiredFor = res.data.approvalRequiredFor;
          let hotelSearchResponse: HotelSearchResponse = deserialize(res.data, HotelSearchResponse);
          this.setHotelRespoonseFromMap(hotelSearchResponse, SearchActionType.DETAIL);
          var hotelRequestCount = 15;
          if (hotelSearchResponse.hotelsList.length < hotelRequestCount) {
            hotelRequestCount = hotelSearchResponse.hotelsList.length;
          }
          if (hotelRequestCount > 0) {
            var firstRequestCodes: ImageRequest[] = [];
            for (var index = 0; index < hotelRequestCount; index++) {
              var requestObj: ImageRequest = new ImageRequest();
              if (hotelSearchResponse.hotelsList[index].handleType === 'TravelPort') {
                requestObj.code = hotelSearchResponse.hotelsList[index].hotelCode;
                requestObj.chainCode = hotelSearchResponse.hotelsList[index].hotelChainCode;
                firstRequestCodes.push(requestObj);
              }
            }
            this.fetchHotelImages(firstRequestCodes);
            if (hotelRequestCount < hotelSearchResponse.hotelsList.length) {
              var secondRequestCodes: ImageRequest[] = [];
              for (var index = hotelRequestCount; index < hotelSearchResponse.hotelsList.length; index++) {
                var requestObj: ImageRequest = new ImageRequest();
                if (hotelSearchResponse.hotelsList[index].handleType === 'TravelPort') {
                  requestObj.code = hotelSearchResponse.hotelsList[index].hotelCode;
                  requestObj.chainCode = hotelSearchResponse.hotelsList[index].hotelChainCode;
                  secondRequestCodes.push(requestObj);
                }
              }
              this.fetchHotelImages(secondRequestCodes);
            }
          }
        } else {
          this.searchComplete = false;
          if (!res.success && res.error_message && res.error_message.trim().length > 0) {
            this.resultErrorMessage = res.error_message;

            //  this.bsModalRef1.hide();
          }
         // this.toastr.error("");
          this.toastr.success(this.translateService.instant('hotelResult.Hotelsnotfound'));
          this.mapSearchButton = false;
          this.mapSearchButtonShow = 1;
          this.latitudeForDestination = this.newCenterLat;
          this.longitudeForDestination = this.newCenterLng;
          this.searchComplete = false;
          setTimeout(() => {
           // this.mapReady(event);
            this.zoom = 12;
          }, 0);
          this.setHotelRespoonseFromMap(new HotelSearchResponse(), SearchActionType.DETAIL);
          let hotelSearchResponse: HotelSearchResponse = deserialize(res.data, HotelSearchResponse);
          if (hotelSearchResponse && hotelSearchResponse.destination) {
            this.hotelSearchRequest.hotelAddress = hotelSearchResponse.destination.address;
            this.searchService.broadCastHotelRequest(this.hotelSearchRequest);
          }

        }
      }, 100);


    }, error => {
      
      if (error.status != 403) {
        setTimeout(() => {
          this.callComplete = true;
          this.resultErrorMessage = this.translateService.instant('hotelResult.Pleasecheckyourinternet').toString();
          this.setHotelRespoonseFromMap(new HotelSearchResponse(), SearchActionType.DETAIL);
         
         
        }, 100);
      }
    });
  }
  searchHotel() {
    this.callComplete = false;
    this.searchService.broadCastHotelResponse(undefined);
    this.policyOptions[0].selected = false;
    this.searchService1.priceChange = undefined
    this.filterService.hotelFilterDefs.currentSortOptionId = 'recommended';
    this.searchService1.displayPrice =   undefined;
    this.searchService1.displayCurrency =undefined;
    this.searchService1.seatSelectArray1 = [];
    this.policyOptions[1].selected = true;
    this.searchFroomMap = false;
    this.searchService.policyObject=null;
    this.filterService.hotelFilterDefs.filter_hotelName = '';
    // if( this.searchComponent) this.searchComponent.searchInProgress = true;
    this.unsubscribeSearch();
    this.searchHotelDetailed(this.hotelSearchQueryParam);
    this.gallopLocalStorage.removeItem("passengers");
    this.gallopLocalStorage.removeItem("passengersFormData");
   // this.gallopLocalStorage.removeItem("hotelSearchRequest");
    this.gallopLocalStorage.removeItem("hotelSearchRequestForBooking");
    if(!this.searchService1.multiTripBooking){
    this.gallopLocalStorage.removeItem("flightSearchRequestForBooking");
    this.searchService1.multicarQuery =[];
      this.searchService1.multiflightQuery=[];
      this.searchService1.multihotelQuery=[];
    this.bookingService.cardAllowed=[];
    this.searchService1.editMytripname='false';
    this.gallopLocalStorage.removeItem("carSearchRequestForBooking");
    this.gallopLocalStorage.removeItem("flightSearchRequest");
    }
  }
  centerChange(event) {
    
    if (this.mapSearchButtonShow !== 1) {
      this.mapSearchButton = true;
    } else {
      this.mapSearchButton = false;
    }
    this.mapSearchButtonShow = 0;
    this.newCenterLat = event.lat;
    this.newCenterLng = event.lng;

  }
  
  unsubscribeSearch() {
   

    if (this.quickSearchSubscription) {
      this.quickSearchSubscription.unsubscribe();
    }
  }

  bsModalRef1: BsModalRef;
  resultErrorMessage = this.translateService.instant('hotelResult.Nohotelsfound').toString();
  searchHotelDetailed(hotelSearchQueryParam: HotelQueryParam) {
    //  window.alert(JSON.stringify(hotelSearchQueryParam)) ;
    // return this.searchHotelDetailedTest(hotelSearchQueryParam);
    this.progressBar.start(SearchActionType.DETAIL);
    if(this.bsModalRef1){
      this.bsModalRef1.hide();
    }
     if(this.searchService1.searchPopMsg && this.searchService1.searchPopMsg.length > 0){
      this.searchService1.currentMessage = this.searchService1.searchPopMsg[0];
     this.searchService1.showNextMessage();
   }
    this.bsModalRef1 = this.modalService.show(hotelModalBasic, {
      initialState: {

      }, backdrop: true, keyboard: false, ignoreBackdropClick: true
    });
   




    this.quickSearchSubscription = this.searchService.searchHotel(hotelSearchQueryParam, SearchActionType.DETAIL).subscribe(res => {
     
      if (this.connectionListener) {
        this.connectionListener.unsubscribe();
      }
      setTimeout(() => {
        this.resultErrorMessage = this.translateService.instant('hotelResult.Nohotelsfound').toString();
        if (res.success === true && res.data) {
          this.bsModalRef1.hide();
          this.callComplete = true;
          if(res.data && res.data.hotelPolicy){
            this.searchService.policyObject = res.data.hotelPolicy;
           
          }
          this.userAccountInfoService.approvalRequiredFor = res.data.approvalRequiredFor;
          this.searchService1.travelPurposeMandatory = res.data.travelPurposeMandatory;
          let hotelSearchResponse: HotelSearchResponse = deserialize(res.data, HotelSearchResponse);
          this.setHotelResults(hotelSearchResponse, SearchActionType.DETAIL);
          var hotelRequestCount = 15;
          if (hotelSearchResponse.hotelsList.length < hotelRequestCount) {
            hotelRequestCount = hotelSearchResponse.hotelsList.length;
          }
          if (hotelRequestCount > 0) {
            var firstRequestCodes: ImageRequest[] = [];
            for (var index = 0; index < hotelRequestCount; index++) {
              var requestObj: ImageRequest = new ImageRequest();
              if (hotelSearchResponse.hotelsList[index].handleType === 'TravelPort') {
                requestObj.code = hotelSearchResponse.hotelsList[index].hotelCode;
                requestObj.chainCode = hotelSearchResponse.hotelsList[index].hotelChainCode;
                firstRequestCodes.push(requestObj);
              }
            }
            this.fetchHotelImages(firstRequestCodes);
            if (hotelRequestCount < hotelSearchResponse.hotelsList.length) {
              var secondRequestCodes: ImageRequest[] = [];
              for (var index = hotelRequestCount; index < hotelSearchResponse.hotelsList.length; index++) {
                var requestObj: ImageRequest = new ImageRequest();
                if (hotelSearchResponse.hotelsList[index].handleType === 'TravelPort') {
                  requestObj.code = hotelSearchResponse.hotelsList[index].hotelCode;
                  requestObj.chainCode = hotelSearchResponse.hotelsList[index].hotelChainCode;
                  secondRequestCodes.push(requestObj);
                }
              }
              this.fetchHotelImages(secondRequestCodes);
            }
          }
        } else {
        
          if (!res.success && res.error_message && res.error_message.trim().length > 0) {
            this.resultErrorMessage = res.error_message;
           
          }
          
          if (this.bsModalRef1) {
            this.bsModalRef1.hide();
          }
          this.callComplete = true;
          this.setHotelResults(new HotelSearchResponse(), SearchActionType.DETAIL);
        }
      }, 100);


    }, error => {
      if (this.bsModalRef1) {
        this.bsModalRef1.hide();
      }
      if (error.status != 403) {
        setTimeout(() => {
          if (this.bsModalRef1) {
            this.bsModalRef1.hide();
          }
          this.callComplete = true;
          this.resultErrorMessage = this.translateService.instant('hotelResult.Pleasecheckyourinternet').toString();
          this.setHotelResults(new HotelSearchResponse(), SearchActionType.DETAIL,'offline');
         
           
          
         
        }, 100);
      }
    });
  }

  fetchHotelImages(imageResuestObj: ImageRequest[]) {
    this.fetchImagesSubscription = this.searchService.requestAllHotelImage(imageResuestObj).subscribe(res => {
      // setTimeout(()=>{
      if (res.success === true && res.data) {
        this.patchImagesIntoHotels(res.data)
      }
      // },100);

    }, error => {

    });
  }

  private patchImagesIntoHotels(hotelImagesArray: any[]) {
    if (!hotelImagesArray) return;
    for (let hotelImages of hotelImagesArray) {
      let hotelResult: HotelResult = this.filterService.originalHotelSearchResponse.
        hotelsList.find(hotelItem =>
          hotelItem.hotelChainCode === hotelImages.hotelChain && hotelItem.hotelCode === hotelImages.hotelCode);
      if (hotelResult) {
        hotelResult.hotelImages = hotelImages.hotelImages;
        hotelResult.thumbnailURL = CommonUtils.getThumbnailImage(hotelResult.hotelImages);
        hotelResult.thumbnailImage = new TraflaImage();
        hotelResult.thumbnailImage.url = hotelResult.thumbnailURL;
      }
    }
    this.filterResultsAsync();
  }
  HOTEL_RATINGS = [1.0, 2.0, 3.0, 4.0, 5.0];
  HOTEL_RECOMMENDED_TEXT_MAP = { 'COMPANY_RECOMMENDED': this.translateService.instant('hotelResult.Companyrecommended'),'COMPANY_BLACKLISTED': this.translateService.instant('hotelResult.Companyblacklisted'), 'BEST_OPTION': this.translateService.instant('hotelResult.BestOption'), 'PREFERRED_BRAND': this.translateService.instant('hotelResult.Preferrredhotelbrand'), 'PREFERRED_RATING': this.translateService.instant('hotelResult.Preferredstarshotel') };
  getRecommendedText(hotel: HotelResult) {
    if (this.filterService.hotelFilterDefs.currentSortOptionId === 'recommended' && hotel &&  hotel.optionSelectionReason) {
      return this.HOTEL_RECOMMENDED_TEXT_MAP[hotel.optionSelectionReason];
    } else {
      return undefined;
    }

  }
  getBasePrice(hotelDataObj) {
    if (hotelDataObj.hotelRateDetail.displayBase) {
        return hotelDataObj.hotelRateDetail.displayBase;
    }else{
        return hotelDataObj.hotelRateDetail.base.substring(3);
    }
  }

  getPerNightPrice(mapObject) {
    if(mapObject.hotelInfo && mapObject.hotelInfo.stay){
        return ((this.getBasePrice(mapObject.hotelInfo)) / mapObject.hotelInfo.stay);
    }else{
      return (this.getBasePrice(mapObject.hotelInfo));
    }
  }
  getAbsoluteNumber(input, hotelDataObj) {
    if (hotelDataObj.hotelInfo.hotelRateDetail.displayTotal) {
      return hotelDataObj.hotelInfo.hotelRateDetail.displayTotal;
    } else {
      return hotelDataObj.hotelInfo.hotelRateDetail.total.substring(3);
    }
  }

  getRecommendedTextForTransaltion(hotel: HotelResult) {
    if (this.filterService.hotelFilterDefs.currentSortOptionId === 'recommended' && hotel &&  hotel.optionSelectionReason) {
      return hotel.optionSelectionReason;
    } else {
      return undefined;
    }

  }
  onSmartModelCancel(modelName: string) {
    this.ngxSmartModalService.getModal(modelName).close();
  }
  getRatingStarsMap(hotelStars) {
    let hStar = Number.parseFloat(hotelStars);
    let starMap = [];

    for (let i = 1; i <= 5; i++) {
      if (hStar >= i) starMap.push('full');
      else if (hStar > (i - 1) && hStar < i) starMap.push('half');
      else starMap.push('none');
    }
    return starMap;
  }
  isAmenityAvailable(type: string, hotelItem: HotelResult) {
    if (hotelItem && hotelItem.amenities) {
      return hotelItem.amenities[type];
    }
    return false;
  }
getCheckCorporaterate(hotelDataObj){
  if(hotelDataObj.hotelInfo.hotelRateList[0].corporateRate){
    return hotelDataObj.hotelInfo.hotelRateList[0].corporateRate
  }
return false;
}

  getCurrencySymbol(item): string {
    if(item.displayCurrency){
      return CommonUtils.getCurrencySymbol(item.displayCurrency);
    }else{
    return CommonUtils.getCurrencySymbol(item.currency);
    }
  }

  getResultsCount() {
    return this.hotelSearchResponse && this.hotelSearchResponse.hotelsList ? this.hotelSearchResponse.hotelsList.length : 0;
  }
  isSearchResultsCount(hotelSearchResponse) {
    return hotelSearchResponse && hotelSearchResponse.hotelsList ? hotelSearchResponse.hotelsList.length : 0;
    //      if(hotelSearchResponse && hotelSearchResponse.hotelsList){
    //          this.hotelArrayModified = hotelSearchResponse.hotelsList.length;
    //      }
    //      else{
    //          this.hotelArrayModified = 0;
    //      }

  }
  clearAll() {
    this.initFilters();
    this.policyOptions[0].selected = false;
    this.policyOptions[1].selected = true;
    this.filterService.hotelFilterDefs.currentSortOptionId = 'recommended';
    let sortvaule = this.sortOptionsHotel.filter(item => item.id === this.filterService.hotelFilterDefs.currentSortOptionId);
    if(sortvaule){
    this.sortValue  = sortvaule[0].value;
    }
    this.hotelResultForm.controls['sortingDropdown'].setValue( this.filterService.hotelFilterDefs.currentSortOptionId );
    let hotelSearchResponse = JSON.parse(JSON.stringify(this.filterService.originalHotelSearchResponse));
    this.searchService.applySorting(hotelSearchResponse, this.filterService.hotelFilterDefs.currentSortOptionId);
    this.isSearchResultsCount(hotelSearchResponse);
  }
  setHotelRespoonseFromMap(hotelSearchResponse: HotelSearchResponse, searchActionType: SearchActionType) {
    this.progressBar.stop(searchActionType);
    this.searchService.searchType = searchActionType;
    this.orgHotelSearchResponse = hotelSearchResponse;
    this.mapDataList(hotelSearchResponse);
    // this.hotelSearchResponse = hotelSearchResponse;
    // if(!(hotelSearchResponse && hotelSearchResponse.hotelsList
    //   && hotelSearchResponse.hotelsList.length > 0 )) return ;

    // this.showLessFlag = true;
    let hotelResponse = JSON.parse(JSON.stringify(hotelSearchResponse));
    this.searchService.setHotelSearchResponse(hotelResponse, true);
  }
  openPolicyModal(modal){
    this.bsModalRef1 = this.modalService.show(modal);
  }
  public onCancel(): void {
    this.bsModalRef1.hide();
  }
  getCurrencySymbol1(item): string {
   
    return CommonUtils.getCurrencySymbol(item);
    
  }
  setHotelResults(hotelSearchResponse: HotelSearchResponse, searchActionType: SearchActionType,internet?) {
    this.progressBar.stop(searchActionType);
    this.searchService.searchType = searchActionType;
    this.orgHotelSearchResponse = hotelSearchResponse;
    this.mapDataList(hotelSearchResponse);
    // this.hotelSearchResponse = hotelSearchResponse;
    // if(!(hotelSearchResponse && hotelSearchResponse.hotelsList
    //   && hotelSearchResponse.hotelsList.length > 0 )) return ;

    // this.showLessFlag = true;
    let hotelResponse = JSON.parse(JSON.stringify(hotelSearchResponse));
    if( hotelResponse &&  hotelResponse.hotelsList[0].displayCurrency){
      this.currency =  hotelResponse.hotelsList[0].displayCurrency;
    }else{
    this.currency =  (hotelResponse && hotelResponse.hotelsList.length> 0)  ?  hotelResponse.hotelsList[0].currency:'USD';
    }
    
    if(this.searchService.policyObject && !this.searchService.policyObject.restrictNonRefundable && this.searchService.policyObject.pricePolicyList.length ===0 && 
      !this.searchService.policyObject.maxHotelRatingAllowed){
        this.searchService.policyObject =null;
      }
    this.searchService.setHotelSearchResponse(hotelResponse, true);
    let userid = this.userAccountInfoService.getUserEmail();
    let sToken = this.userAccountInfoService.getSToken();
    if(!internet){
    this.router.navigate(["hotels"],
      {
        queryParams:
        {
          query: encodeURIComponent(JSON.stringify(this.hotelSearchQueryParam)),
          step: 0,
          resultFound: 1
        },
        replaceUrl: true
      }
    );
    }
  }
  getMarkerWidth(hotel) {
    if(hotel.displayPrice){
     
      return 50 + 10 * Math.log10(hotel.displayPrice - 999);
    }else{
    if (hotel.price < 999) {
      return 50;
    }
    return 50 + 10 * Math.log10(hotel.price - 999);
  }
  }
  getMarkerPrice(hotel) {
    if(hotel.displayPrice){
      return this.currencyPipe.transform(hotel.displayPrice, hotel.displayCurrency, 'code', '1.0-0');
    }else{
    return this.currencyPipe.transform(hotel.priceIncludingResortFee, hotel.currency, 'code', '1.0-0');
    }
  }
  mapDataList(hotelSearchResponse) {
    this.latLongData = [];
    if (!(hotelSearchResponse && hotelSearchResponse.hotelsList)) {
      return;
    }
    let mapList = JSON.parse(JSON.stringify(hotelSearchResponse.hotelsList));
    mapList = mapList.sort(function (a, b) {
      if (a.distanceMile < b.distanceMile) return -1;
      else if (a.distanceMile > b.distanceMile) return 1;
      else return 0;
    });

    this.latitudeForDestination = Number.parseFloat(hotelSearchResponse.destination.lattitude);
    this.longitudeForDestination = Number.parseFloat(hotelSearchResponse.destination.longitude);
    var bestOptionFound = false;
    for (let counter = 0; counter < mapList.length; counter++) {
      var item = mapList[counter];
      var thumbnailImage = 'assets/images/hotel/hotel.png';
      if (item.thumbnailImage && item.thumbnailImage.url) {
        thumbnailImage = item.thumbnailImage.url;
      }
      let resortFee = item.hotelInfo.hotelRateList[0].resortFee ? Number.parseFloat(item.hotelInfo.hotelRateList[0].resortFee.substring(3)) : null;
      let priceWithResorFee = item.price + resortFee;
      var currObject: any =
      {
        latitude: item.latitude,
        longitude: item.longitude,
        price: '' + item.price,
        hotelName: item.hotelName,
        distanceMile: item.distanceMile,
        address: item.address,
        markerClicked: false,
        isRecommanded: true,
        ups: item.hotelInfo.upVotes ? item.hotelInfo.upVotes : null,
        downs: item.hotelInfo.downVotes ? item.hotelInfo.downVotes : null,
        drivingTime: item.drivingTime,
        walkingTime: item.walkingTime,
        priceIncludingResortFee: priceWithResorFee,
        currency: item.currency,
        hotelRateList: item.hotelInfo.hotelRateList,
        thumbnailImage: { url: thumbnailImage },
        starRating: item.starRating,
        amenities: item.amenities,
        displayPrice:item.displayPrice ? item.displayPrice:undefined,
        displayCurrency:item.displayCurrency ? item.displayCurrency:undefined,
        withinPolicy: item.withinPolicy,
        hotelChainCode: item.hotelChainCode,
        hotelInfo: item.hotelInfo ? item.hotelInfo : null,
        handleType: item.handleType,
        optionSelectionReason: item.optionSelectionReason,
        score: item.score,
        fitBounds: false,
        hotelCode: item.hotelCode,
        checkInDate: item.checkInDate,
        checkOutDate: item.checkOutDate
      };
      if (!this.searchService.selectedHotelFromMap) {
        if (counter == 0) {
          currObject.markerClicked = true;
          this.mapObject = currObject;
        }
        if (item && item.optionSelectionReason && item.optionSelectionReason == 'BEST_OPTION') {
          bestOptionFound = true;
          currObject.markerClicked = true;
          this.mapObject = currObject;
          currObject.fitBounds = true;
          if (counter !== 0) {
            this.latLongData[0].markerClicked = false;
          }
        }
      }
      if (counter < 5) {
        currObject.fitBounds = true;
      }
      this.latLongData.push(currObject);
    }
    //let selectedHotel = this.gallopLocalStorage.getItem("selectedHotel");
    if (this.searchService.selectedHotelFromMap) {
      let value = this.latLongData.find(item => item.hotelCode === this.searchService.selectedHotelFromMap.hotelCode);
      if (value) {
        value.markerClicked = true;
        value.fitBounds = true;
        this.mapObject = value;
      } else {
        this.latLongData[0].markerClicked = true;
        this.latLongData[0].fitBounds = true;
        this.mapObject = this.latLongData[0];
      }
    }
  }

  sortOptionChanged(sortOption) {
    if (!sortOption) return;
    this.filterService.hotelFilterDefs.currentSortOptionId = sortOption.id;
    if (sortOption.id) {
      this.sortValue = sortOption.value ? sortOption.value : this.translateService.instant('ngOption.Recommended');
    }
    this.searchService.applySorting(this.hotelSearchResponse, this.filterService.hotelFilterDefs.currentSortOptionId);
    let params = "?ua_action=HotelResultSortdByItem&ua_item=" + sortOption.id;
    this.searchService1.letsTrack(params);
  }
newHotelsNames=[];

  onSearchTextChange(text,modal) {
    this.searchService.selectedHotelFromMap = undefined;
   
    if (text && text.length > 0) {
      // this.filter_hotelName = text;
     
      this.filterService.hotelFilterDefs.filter_hotelName = text;
      if (this.filterService.hotelFilterDefs.appliedFilterList.indexOf(FilterType.HOTEL_NAME) === -1) {
        this.filterService.hotelFilterDefs.appliedFilterList.push(FilterType.HOTEL_NAME);
      }
    } else {
      this.newHotelsNames=[];
      if(modal){
        this.ngxSmartModalService.close(modal);
      }
      this.filterService.hotelFilterDefs.appliedFilterList = this.filterService.hotelFilterDefs.appliedFilterList.filter(filter => {
        if (filter.toString() !== FilterType.HOTEL_NAME.toString()) { return true; }
      });
      
    }

    this.filterResults(modal);
    if (text && text.length > 2) {
      this.searchService.searchMoreHotel(text).subscribe(resp =>{
        if(resp && resp.success){
          if(resp && resp.data && resp.data.length > 0){
this.newHotelsNames=[...resp.data];

for(let item of this.orgHotelSearchResponse.hotelsList){
  this.newHotelsNames= this.newHotelsNames.filter(hotel => hotel.hotelCode!==item.hotelCode);
}
      setTimeout(() => {
        if(modal  && this.newHotelsNames.length > 0){
            this.ngxSmartModalService.getModal(modal).open()
          
        };
      }, 200);
          }
          
        }
      })

    }
    
  }
  filterResults(modal?) {
    // setTimeout(()=>{

    // }),200);
    setTimeout(() => {
      this.filterResultsAsync(modal);
    }, 200);
  }
  isFullScreenMapShowing() {
    if (this.isMobile && this.initialMapListFilterText == 'map') {
      adjustMapHeightForFullScreen();
      return true;
    }
    return false;
  }

  filterResultsAsync(modal?) {

    if (!this.filterService.originalHotelSearchResponse) {
      return;
    }
   
    let flightSearchRes = JSON.parse(JSON.stringify(this.filterService.originalHotelSearchResponse));
    for (let filter of this.filterService.hotelFilterDefs.appliedFilterList) {
      if (filter === FilterType.HOTEL_CHAIN) {
        flightSearchRes = this.filterService.filterByHotelChains(this.filterService.hotelFilterDefs.filter_hotelChains, flightSearchRes);
      } else if (filter === FilterType.HOTEL_STARS) {
        flightSearchRes = this.filterService.filterByStarRating(this.filterService.hotelFilterDefs.filter_starRating, flightSearchRes);
      } else if (filter === FilterType.HOTEL_AMENITIES) {
        flightSearchRes = this.filterService.filterByAmenities(this.filterService.hotelFilterDefs.filter_amenities, flightSearchRes);
      } else if (filter === FilterType.HOTEL_NAME) {
        flightSearchRes = this.filterService.filterHotelByName(this.filterService.hotelFilterDefs.filter_hotelName, flightSearchRes);
      } else if (filter === FilterType.POLICY) {
        flightSearchRes = this.filterService.filterByWithinPolicyHotel(flightSearchRes);
      }

    }
    if (flightSearchRes.hotelsList.length < this.hotelElementCount && flightSearchRes.hotelsList.length > 0) {
      this.resultLen = false;

    } else {
      this.resultLen = true;
      if (this.isSeenComplete) {
        this.isSeenComplete = false;
      }

    }
    this.searchService.applySorting(flightSearchRes, this.filterService.hotelFilterDefs.currentSortOptionId);
   
  }

  getSearchText() {
    return this.filterService.hotelFilterDefs.filter_hotelName;
  }
  toggleAnyRating() {
    this.filterService.hotelFilterDefs.isRatingAny = !this.filterService.hotelFilterDefs.isRatingAny;
    this.filterService.hotelFilterDefs.isRated = 0;
  }
  getAnyRatingCheckStatus() {
    return this.filterService.hotelFilterDefs.isRatingAny;
  }
  applyRatingFilter() {
    // this.ngxSmartModalService.getModal('stops').close();
    this.searchService.selectedHotelFromMap = undefined;
    this.filterService.hotelFilterDefs.appliedFilterList = this.filterService.hotelFilterDefs.appliedFilterList.filter(filter => {
      if (filter.toString() !== FilterType.HOTEL_STARS.toString()) {
        return true;
      }
    });
    if (this.filterService.hotelFilterDefs.isRated > 0) {
      this.filterService.hotelFilterDefs.appliedFilterList.push(FilterType.HOTEL_STARS);
      this.filterService.hotelFilterDefs.filter_starRating = this.filterService.hotelFilterDefs.isRated;
    }
    let rating = 'Any';
    if (this.filterService.hotelFilterDefs.isRated == 0) {
      rating = 'Any'
    } else if (this.filterService.hotelFilterDefs.isRated == 1) {
      rating = '1 star'
    } else if (this.filterService.hotelFilterDefs.isRated == 2) {
      rating = '2 star'
    } else if (this.filterService.hotelFilterDefs.isRated == 3) {
      rating = '3 star'
    }
    else if (this.filterService.hotelFilterDefs.isRated == 4) {
      rating = '4 star'
    }
    else if (this.filterService.hotelFilterDefs.isRated == 5) {
      rating = '5 star'
    }
    let params = "?ua_action=HotelResultsFilter&ua_filter=Rating&ua_rating=" + rating;
    this.searchService1.letsTrack(params);

    this.filterResults();
  }
  applyHotelChainFilter() {
    // if(this.temp_filter_airlines){
    this.searchService.selectedHotelFromMap = undefined;
    if (this.filterService.hotelFilterDefs.temp_filter_hotelChains.length == 0) {
      this.filterService.hotelFilterDefs.appliedFilterList = this.filterService.hotelFilterDefs.appliedFilterList.filter(filter => {
        if (filter.toString() !== FilterType.HOTEL_CHAIN.toString()) {
          return true;
        }
      });
    } else if (this.filterService.hotelFilterDefs.appliedFilterList.indexOf(FilterType.HOTEL_CHAIN) === -1) {
      this.filterService.hotelFilterDefs.appliedFilterList.push(FilterType.HOTEL_CHAIN);
    }
    let params = "?ua_action=HotelResultsFilter&ua_filter=Brand&ua_brand=" + this.filterService.hotelFilterDefs.temp_filter_hotelChains;
    this.searchService1.letsTrack(params);
    this.filterService.hotelFilterDefs.filter_hotelChains = this.filterService.hotelFilterDefs.temp_filter_hotelChains;
    this.filterResults();
  }
  isHotelChainChecked(airport) {
    return (airport === 'ANY' && this.filterService.hotelFilterDefs.temp_filter_hotelChains.length === 0)
      || this.filterService.hotelFilterDefs.temp_filter_hotelChains.indexOf(airport) > -1;
  }

  isHotelAmenityChecked(airport) {
    return (airport === 'ANY' && this.filterService.hotelFilterDefs.temp_filter_amenities.length === 0)
      || this.filterService.hotelFilterDefs.temp_filter_amenities.indexOf(airport) > -1;
  }

  applyAmenityFilter() {
    this.searchService.selectedHotelFromMap = undefined;
    if (this.filterService.hotelFilterDefs.temp_filter_amenities.length == 0) {
      this.filterService.hotelFilterDefs.appliedFilterList = this.filterService.hotelFilterDefs.appliedFilterList.filter(filter => {
        if (filter.toString() !== FilterType.HOTEL_AMENITIES.toString()) { return true; }
      });
    } else if (this.filterService.hotelFilterDefs.appliedFilterList.indexOf(FilterType.HOTEL_AMENITIES) === -1) {
      this.filterService.hotelFilterDefs.appliedFilterList.push(FilterType.HOTEL_AMENITIES);
    }
    let params = "?ua_action=HotelResultsFilter&ua_filter=Ameneties&ua_ameneties=" + this.filterService.hotelFilterDefs.temp_filter_amenities;
    this.searchService1.letsTrack(params);
    this.filterService.hotelFilterDefs.filter_amenities = this.filterService.hotelFilterDefs.temp_filter_amenities;

    this.filterResults();
  }
  applyPolicyFilter() {
    this.searchService.selectedHotelFromMap = undefined;
    this.filterService.hotelFilterDefs.appliedFilterList = this.filterService.hotelFilterDefs.appliedFilterList.filter(filter => {
      if (filter !== FilterType.POLICY) { return true; }
    });
    if (this.filterService.hotelFilterDefs.temp_filter_policy
      && this.filterService.hotelFilterDefs.temp_filter_policy != null
      && this.filterService.hotelFilterDefs.temp_filter_policy === 'WITHINPOLICY') {
      this.filterService.hotelFilterDefs.appliedFilterList.push(FilterType.POLICY);
    }
    this.filterService.hotelFilterDefs.filter_policy = this.filterService.hotelFilterDefs.temp_filter_policy;
    if (this.filterService.hotelFilterDefs.filter_policy === 'WITHINPOLICY') {
      this.policyOptions[0].selected = true;
      this.policyOptions[1].selected = false;
      let params = "?ua_action=HotelResultsFilter&ua_filter=Policy&ua_policyType=withinpolicy";
      this.searchService1.letsTrack(params);
    } else {
      this.policyOptions[0].selected = false;
      this.policyOptions[1].selected = true;
      let params = "?ua_action=HotelResultsFilter&ua_filter=Policy&ua_policyType=all";
      this.searchService1.letsTrack(params);
    }
    this.filterResults();
  }
  isUserCompanyManager() {
    return this.userAccountInfoService.isUserCorporateAdmin();
  }
  onHotelChainClicked(option, event) {


    if (event.target.checked) {
      if (option == "ANY") {
        this.filterService.hotelFilterDefs.temp_filter_hotelChains = ['ANY'];
      } else {
        this.filterService.hotelFilterDefs.temp_filter_hotelChains
          = this.filterService.hotelFilterDefs.temp_filter_hotelChains.filter(airport => {
            if (airport !== 'ANY') {
              return true;
            }
          });
        this.filterService.hotelFilterDefs.temp_filter_hotelChains.push(option);
      }
    } else {
      this.filterService.hotelFilterDefs.temp_filter_hotelChains =
        this.filterService.hotelFilterDefs.temp_filter_hotelChains.filter(airport => {
          if (airport !== option && airport !== 'ANY') {
            return true;
          }
        });
      if (this.filterService.hotelFilterDefs.temp_filter_hotelChains.length === 0) {
        this.filterService.hotelFilterDefs.temp_filter_hotelChains = ['ANY'];
      }
    }
    this.isHotelChainChecked(option);

  }
  canShowPolicyFilter() {
    return this.filterService.originalHotelSearchResponse.policySet;
  }
  onHotelAmenityClicked(option, event) {


    if (event.target.checked) {
      if (option === 'ANY') {
        this.filterService.hotelFilterDefs.temp_filter_amenities = ['ANY'];
      }
      else {
        this.filterService.hotelFilterDefs.temp_filter_amenities =
          this.filterService.hotelFilterDefs.temp_filter_amenities.filter(airport => {
            if (airport !== 'ANY') {
              return true;
            }
          });
        this.filterService.hotelFilterDefs.temp_filter_amenities.push(option);
      }
    } else {
      this.filterService.hotelFilterDefs.temp_filter_amenities =
        this.filterService.hotelFilterDefs.temp_filter_amenities.filter(airport => {
          if (airport !== option && airport !== 'ANY') {
            return true;
          }
        });
      if (this.filterService.hotelFilterDefs.temp_filter_amenities.length === 0) {
        this.filterService.hotelFilterDefs.temp_filter_amenities = ['ANY'];
      }
    }
    this.isHotelAmenityChecked(option);

  }
  onHotelSelection(hotel: HotelResult, index: number, value) {
    this.gallopLocalStorage.setItem('failedRates', '{}');
    this.searchService.selectTypeFrom = value;
    this.searchService.selectedHotel = hotel;
    this.filterService.hotelFilterDefs.filter_hotelName='';
    this.searchResultService.broadcastSelectedHotel(hotel, index,true);
    this.goToSelectRoom();
  }
  selectHotelFromDropdown(item,i,id){
    this.gallopLocalStorage.setItem('failedRates', '{}');
    this.searchService.selectTypeFrom = 'list';
    item['hotelInfo']={}
    item['hotelInfo']['hotelImages']={};
    item['hotelInfo']['upVotes']=item.upVotes;
    item['hotelInfo']['downVotes']=item.downVotes;
    this.searchService.selectedHotel = item;
    this.filterService.hotelFilterDefs.filter_hotelName='';
    this.searchService.hotelSelectFromDropDown =true;
    this.searchResultService.broadcastSelectedHotel(item, i,true);
    this.goToSelectRoom();
    setTimeout(() => {
     // this.ngxSmartModalService.getModal(id).close();
    }, 10);
  }
  onHotelSelectionMobile(hotel, i, value) {
    if (this.isMobile) {
      this.onHotelSelection(hotel, i, value);
    }
  }
  onConfirmUpdateHotel() {
    this.updateHotel = true;
    let hotelHandler = this.selectRecommendedHotel[0].handleType;
    let hotelCode = this.selectRecommendedHotel[0].hotelCode;
    let gallopid = this.selectRecommendedHotel[0].hotelInfo.gallopId ? this.selectRecommendedHotel[0].hotelInfo.gallopId : null;
    this.bookingService.addRecommendHottel(hotelCode, hotelHandler, gallopid).subscribe(res => {
      if (res && res.status === 'success') {
        this.updateHotel = false;
        // this.bsModalRef1.hide();
        let findIndex = this.hotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.hotelSearchResponse.hotelsList[findIndex].optionSelectionReason = 'COMPANY_RECOMMENDED';
        let findIndex2 = this.orgHotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.orgHotelSearchResponse.hotelsList[findIndex2].optionSelectionReason = 'COMPANY_RECOMMENDED';
        let findIndex1 = this.latLongData.findIndex(item => item.hotelCode === hotelCode);
        this.latLongData[findIndex1].optionSelectionReason = 'COMPANY_RECOMMENDED';
        let findIndex3 = this.filterService.originalHotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.filterService.originalHotelSearchResponse.hotelsList[findIndex3].optionSelectionReason = 'COMPANY_RECOMMENDED';
        this.mapObject = this.latLongData[findIndex1];
        this.toastr.success(this.translateService.instant('hotelResult.Hotelupdatedascompanyrecommended'));
      }else{
        this.bsModalRef1.hide();
          if (!res.success && res.error_message && res.error_message.trim().length > 0) {
            this.resultErrorMessage = res.error_message;
          }
      }

    });
  }
  onUpdateHotel(type) {
    this.updateHotel = true;
    let hotelHandler = this.selectRecommendedHotel[0].handleType;
    let hotelCode = this.selectRecommendedHotel[0].hotelCode;
    let gallopid = this.selectRecommendedHotel[0].hotelInfo.gallopId ? this.selectRecommendedHotel[0].hotelInfo.gallopId : null;
    if(type==='recommanded'){
    this.bookingService.addRecommendHottel(hotelCode, hotelHandler, gallopid).subscribe(res => {
      if (res && res.status === 'success') {
        this.updateHotel = false;
        this.ngxSmartModalService.getModal(this.selectedModal).close();
        // this.bsModalRef1.hide();
        let findIndex2 = this.orgHotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.orgHotelSearchResponse.hotelsList[findIndex2].optionSelectionReason = 'COMPANY_RECOMMENDED';
        let findIndex = this.hotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.hotelSearchResponse.hotelsList[findIndex].optionSelectionReason = 'COMPANY_RECOMMENDED';
        let findIndex1 = this.latLongData.findIndex(item => item.hotelCode === hotelCode);
        let findIndex3 = this.filterService.originalHotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.filterService.originalHotelSearchResponse.hotelsList[findIndex3].optionSelectionReason = 'COMPANY_RECOMMENDED';
        this.latLongData[findIndex1].optionSelectionReason = 'COMPANY_RECOMMENDED';
        this.mapObject = this.latLongData[findIndex1];
       // this.toastr.warning('Hotel removed from company recommendation');
        this.toastr.success(this.translateService.instant('hotelResult.Hotelupdatedascompanyrecommended'));
      }else{
       // this.bsModalRef1.hide();
       this.ngxSmartModalService.getModal(this.selectedModal).close();
          if (!res.success && res.error_message && res.error_message.trim().length > 0) {
            this.resultErrorMessage = res.error_message;
          }
      }
    })
  }else  if(type==='blacklisted'){
    this.bookingService.removeRecommendHottel(hotelCode, hotelHandler, gallopid).subscribe(res => {
      if (res && res.status === 'success') {
        this.updateHotel = false;
        this.ngxSmartModalService.getModal(this.selectedModal).close();
       //  this.bsModalRef1.hide();
        let findIndex2 = this.orgHotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.orgHotelSearchResponse.hotelsList[findIndex2].optionSelectionReason = 'COMPANY_BLACKLISTED';
        let findIndex = this.hotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.hotelSearchResponse.hotelsList[findIndex].optionSelectionReason = 'COMPANY_BLACKLISTED';
        let findIndex1 = this.latLongData.findIndex(item => item.hotelCode === hotelCode);
        let findIndex3 = this.filterService.originalHotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.filterService.originalHotelSearchResponse.hotelsList[findIndex3].optionSelectionReason = 'COMPANY_BLACKLISTED';
        this.latLongData[findIndex1].optionSelectionReason = 'COMPANY_BLACKLISTED';
        this.mapObject = this.latLongData[findIndex1];
       // this.toastr.warning('Hotel removed from company recommendation');
        this.toastr.success(this.translateService.instant('hotelResult.Hotelblockedfromcompany'));
      }else{
       //this.bsModalRef1.hide();
        this.ngxSmartModalService.getModal(this.selectedModal).close();
          if (!res.success && res.error_message && res.error_message.trim().length > 0) {
            this.resultErrorMessage = res.error_message;
          }
      }
    })
  }else if(type==='normalize'){
    this.bookingService.normalizeRecommendHottel(hotelCode, hotelHandler, gallopid).subscribe(res => {
      if (res && res.status === 'success') {
        this.updateHotel = false;
        this.ngxSmartModalService.getModal(this.selectedModal).close();
        if(this.getRecommendedText(this.selectedHotel)==='Company blacklisted'){
          this.toastr.success(this.translateService.instant('hotelResult.Hotelremovedfromcompanyblacklist'));
         }else{
          this.toastr.success(this.translateService.instant('hotelResult.Hotelremovedfromcompanyrecommendation'));
         }
        // this.bsModalRef1.hide();
        let findIndex2 = this.orgHotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.orgHotelSearchResponse.hotelsList[findIndex2].optionSelectionReason = '';
        let findIndex = this.hotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.hotelSearchResponse.hotelsList[findIndex].optionSelectionReason = '';
        let findIndex1 = this.latLongData.findIndex(item => item.hotelCode === hotelCode);
        let findIndex3 = this.filterService.originalHotelSearchResponse.hotelsList.findIndex(item => item.hotelCode === hotelCode);
        this.filterService.originalHotelSearchResponse.hotelsList[findIndex3].optionSelectionReason = '';
        this.latLongData[findIndex1].optionSelectionReason = '';
        this.mapObject = this.latLongData[findIndex1];
       // this.toastr.warning('Hotel removed from company recommendation');
       
      }else{
       // this.bsModalRef1.hide();
       this.ngxSmartModalService.getModal(this.selectedModal).close();
          if (!res.success && res.error_message && res.error_message.trim().length > 0) {
            this.resultErrorMessage = res.error_message;
          }
      }
    })
  }
  }
  onModelCancel1() {
    this.bsModalRef1.hide();
  }
  selectedModal;
  showRecommendedModal(modal, item) {
    this.selectRecommendedHotel = [];
    this.selectRecommendedHotel.push(item);
    this.selectedHotel =item;
    this.selectedModal = modal;
    setTimeout(() => {
      this.ngxSmartModalService.getModal(modal).open()
    }, 10);
   
  //  this.bsModalRef1= this.modalService.show(modal);
   // this.onConfirmUpdateHotel();

  }
  selectedHotel:any;
  showRecommendedModal1(modal, item) {
    this.selectRecommendedHotel = [];
    this.selectRecommendedHotel.push(item);
    this.selectedHotel =item;
    this.selectedModal = modal;
    setTimeout(() => {
      this.ngxSmartModalService.getModal(modal).open()
    }, 10);
   // this.bsModalRef1= this.modalService.show(modal);

  }
  hotelArray() {
    let newArray = [];
    let maxToReturn = this.hotelElementCount;
    if (this.isSeenComplete) {
      maxToReturn = this.hotelSearchResponse.hotelsList.length;
    }
    for (let i = 0; i < maxToReturn && i < this.hotelSearchResponse.hotelsList.length; i++) {
      newArray.push(this.hotelSearchResponse.hotelsList[i]);
    }

    return newArray;
  }

  loadAllItems() {
    this.isSeenComplete = true;
  }
  ngOnDestroy() {
    this.searchService1.networkPopupListener.next(null);
    if (!this.callComplete) {
      this.bsModalRef1.hide();
      if (this.quickSearchSubscription) {
        this.quickSearchSubscription.unsubscribe();
      }
    }
    if (this.connectionListener) {
      this.connectionListener.unsubscribe();
    }
    if (this.queryParmsSubscription) {
      this.queryParmsSubscription.unsubscribe();
    }
    if (this.quickSearchSubscription) {
      this.quickSearchSubscription.unsubscribe();
      this.progressBar.stop(SearchActionType.DETAIL);
    }
    if(this.hotelSearchResponseSubscription){
      this.hotelSearchResponseSubscription.unsubscribe();
    }
  }
  public handleStarFilterClick(isRatingAny, isRated) {
    this.filterService.hotelFilterDefs.isRatingAny = isRatingAny;
    this.filterService.hotelFilterDefs.isRated = isRated;
  }
  public getStarFilterCheckStatus(isRated, isRatingAny) {
    if (isRated === 0) {
      return (this.filterService.hotelFilterDefs.isRatingAny === isRatingAny);
    }
    return (this.filterService.hotelFilterDefs.isRated === isRated
      && this.filterService.hotelFilterDefs.isRatingAny === isRatingAny);
  }
  public priceIncludesTaxesAndFees(hotelDataObj){
    return hotelDataObj.priceIncludesTaxesAndFees;
  }
}
