import { Component, OnInit } from '@angular/core';
import { AdminPanelService } from '../admin-panel.service';
import { ToastrService } from 'ngx-toastr';
declare var initializeWdrFileDataForReconcile: any;
declare var isFileDataUplodedForReconcile: any;
declare var getWDRLoadStatus: any;
declare var pivotOffButtonForReconcile: any;
@Component({
    selector: 'app-reconcile',
    templateUrl: './reconcile.component.html',
    styleUrls: ['./reconcile.component.scss'],
    standalone: false
})
export class ReconcileComponent implements OnInit {

  constructor(private adminPanelService: AdminPanelService,
    private toastr: ToastrService,) { }
  csvContent: string;
  ngOnInit() {
  }
  onFileLoad(fileLoadedEvent) {
    const textFromFileLoaded = fileLoadedEvent.target.result;
    this.csvContent = textFromFileLoaded;
    //
    initializeWdrFileDataForReconcile(this.csvContent)
  }

  selectedFile = null;
  readURL(event): void {
    if (event && event[0]) {
      const files = event[0];
      var content = this.csvContent;
      const fileToRead = files;
      const fileReader = new FileReader();
      fileReader.onload = this.onFileLoad;
      fileReader.readAsText(fileToRead, "UTF-8");
      this.selectedFile = files;
    }
  }
  fileUploaded() {
    return isFileDataUplodedForReconcile();
  }
  uploadFile() {
    this.adminPanelService.uploadFileRequest(this.selectedFile).subscribe(res => {
      if (res && res.success) {
        this.csvContent = JSON.parse(JSON.stringify(res.data));
        //
        initializeWdrFileDataForReconcile(this.csvContent)
        this.toastr.success("File reconcile successfully");
      }
    })
  }
  getDataLoadingStatus() {
    return getWDRLoadStatus();
  }
  ngOnDestroy() {
    pivotOffButtonForReconcile();
  }
}
