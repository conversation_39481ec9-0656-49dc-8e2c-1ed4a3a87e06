@import "../../variables.scss";

.shadow {
  box-shadow: none !important;
}

.card-div-inner {
  width: 100%;
  padding: 0px 15px 10px !important;
}

.card-div {
  border: none !important;
}

.section {
  margin-top: 0px;
  background-color: #fff;
  border: none;
  box-shadow: #fff;
  min-width: 550px !important;
}

.shadow {
  overflow-x: auto !important;
  padding-bottom: 200px !important;
}

.shadow2 {
  overflow-x: auto;
  padding-right: 60px;
  padding-bottom: 200px !important;
}

.add {
  height: 24px;
  width: 188px;
  color: #2D57FA;
  font-family: "apercu-r";
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 1px;
  line-height: 25px;
  text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}

.upload-box {
  height: auto;
  width: auto;
  color: var(--button-font-color);
  font-family: "apercu-r";
  font-size: 18px;
  line-height: 18px;
}

.btn-secondary {
  height: 34px;
  width: 256px;
  cursor: pointer;
  letter-spacing: 1px;
  background-color: var(--button-bg-color) !important;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
  margin-top: 15px;
  border: none;
}

input[type='file'] {
  display: none !important;
}

.view {
  height: 29px;
  width: auto;
  color: var(--dark-bg-color) !important;
  font-family: "apercu-r";
  font-size: 24px;
  line-height: 5px;
  position: absolute;
  padding-top: 18px;
}

.image11 {
  width: 45px;
  height: 45px;
}