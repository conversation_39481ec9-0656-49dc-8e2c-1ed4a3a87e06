import { Component, OnInit } from '@angular/core';
import { TravelersInfo } from '../entity/travelers-info';
import { Subscription } from "rxjs/index";
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from '../../environments/environment';
import { UserProfile } from '../entity/user-profile';
@Component({
    selector: 'app-header',
    templateUrl: './header.html',
    standalone: false
})
export class HeaderComponent implements OnInit {

  userDetails: UserProfile;
  userDetailsSubscription: Subscription;
  constructor(private route: ActivatedRoute, private router: Router) { }

  ngOnInit() {
    // this.userDetailsSubscription = this.userDetailsService.userDetails$.subscribe(details => this.userDetails = details);
  }
  signOut() {
    // this.userDetailsService.clearUserDetails();
    localStorage.clear();
    this.router.navigate(['.'], { relativeTo: this.route, queryParams: {} });
  }
  goToUrl() {
  }

  ngOnDestroy() {
    if (this.userDetailsSubscription)
      this.userDetailsSubscription.unsubscribe();
  }


  public getSubscriptionPlan() {

    if (this.userDetails.entity) {
      return this.userDetails.entity.subscription.plan;
    } else {
      return null;
    }
  }


  public getGallopCashBack(total: number) {
    let plan = this.getSubscriptionPlan();
    if (plan && plan != null) {
      if (plan.cashback.cashbackType.toString() === 'absolute') {
        return plan.cashback.amount;
      } else {
        return total * plan.cashback.amount / 100;
      }
    }
  }



  public canAccessCompanyCard() {
    if (this.userDetails && this.userDetails.entity) {
      return this.userDetails.entity.companyCardAccess;
    } else {
      return false;
    }
  }

  public getGallopCash() {
    if (this.userDetails.gallopCash) {
      return this.userDetails.gallopCash.amount;
    } else {
      return "0";
    }
  }

  public isPaidSubscription() {
    let plan = this.getSubscriptionPlan();
    if (plan && plan != null) {
      return plan.id.toString() !== 'subscription_free';
    } else {
      return true;
    }

  }
}
