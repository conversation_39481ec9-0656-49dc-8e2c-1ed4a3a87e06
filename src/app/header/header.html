<div class="header">
  <div class="brand"><img src="assets/images/logo.png" alt=""></div>
  <div>
    <div class="profile" *ngIf="userDetails" (click)="goToUrl()">
      <div>
        <img [src]="userDetails.profileImage" class="profile-image" *ngIf="userDetails.profileImage">
        <div class="profile-image" *ngIf="!userDetails.profileImage"></div>
      </div>
      <div>
        <div>{{userDetails.firstName}} {{userDetails.lastName}}</div>
        <div class="text-secondary">{{userDetails?.email}}</div>
        <div class="text-secondary">GallopCash: {{getGallopCash()| currency}}</div>
      </div>
    </div>
    <div class="signout" style="margin-left: 45px;">
      <div *ngIf="userDetails" (click)="signOut()">Logout</div>
    </div>
  </div>


  <div class="signin" *ngIf="!userDetails" (click)="signin()">Sign In</div>
</div>