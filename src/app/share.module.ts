import { NgModule } from "@angular/core";
import { CommonModule } from '@angular/common';
import { AppLoaderComponent } from './email-booking-flow/app-loader/app-loader.component';
import { AddCardWidgetComponent } from './email-booking-flow/add-card-widget/add-card-widget.component';
import { LoaderDotsComponent } from './loader-dots/loader-dots.component';
import { ngxNewModal } from './ngx-new-modal/ngx-new-modal.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { EmailHeaderComponent } from './email-booking-flow/header/header.component';
import { NavigationComponent } from './navigation/navigation.component';
import { HotelfeedbackComponent } from './hotelfeedback/hotelfeedback.component';
import { SearchComponent } from './search/search.component';
import { PaymentComponent } from './payment/payment.component';
import { flightSelected } from './flight-selected/flight-selected.component';
import { UiSwitchModule } from 'ngx-ui-switch';
import { FilterComponent } from './filter/filter.component';
import { RangeSliderComponent } from './range-slider/range-slider.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateOptionsPipe } from './util/translate-option';
import { SafeUrlPipe } from './util/safeUrl';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { SwitchComponentComponent } from './switch-component/switch-component.component';
import { AirportsComponent } from './airports/airports.component';
import { CollapseModule } from 'ngx-bootstrap/collapse';
import { CarSearchComponent } from './car-search/car-search.component';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { HotelRequestViewComponent } from './hotel-request-view/hotel-request-view.component';
import { FlightBoxComponent } from './search-result/flight-box/flight-box.component';
import { RouterModule } from '@angular/router';
import { SuperHeaderComponent } from './super-header/super-header.component';
import { ApprovalDetailComponent } from './approval-detail/approval-detail.component';


import { OnboardingComponent } from './onboarding/onboarding.component';
import { DropdownComponent } from './dropdown/dropdown.component';
import { bookingHistoryComponent } from './booking-history/booking-history.component';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { TripDetailsCardComponent } from "./trip-details-card/trip-details-card.component";
import { ApprovalRequestDetailComponent } from "./approval-request-detail/approval-request-detail.component";
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { GoogleMapsModule } from '@angular/google-maps';
import { MatSelectModule } from '@angular/material/select';
import { UiScrollModule } from "ngx-ui-scroll";

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    NgxSmartModalModule,
    ReactiveFormsModule,
    TranslateModule,
    CollapseModule,
    AccordionModule,
    MatSelectModule,
    NgSelectModule,
    RouterModule,
    GoogleMapsModule,
    BsDatepickerModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatAutocompleteModule,
    MatIconModule,
     UiScrollModule,
    NgxSliderModule,
    UiSwitchModule, 
  ScrollingModule,
    
  ],
  declarations: [OnboardingComponent,FlightBoxComponent, HotelRequestViewComponent, CarSearchComponent, HotelfeedbackComponent, FilterComponent, RangeSliderComponent,SafeUrlPipe, AirportsComponent, TranslateOptionsPipe, SwitchComponentComponent,
    AppLoaderComponent, LoaderDotsComponent, AddCardWidgetComponent, ngxNewModal, bookingHistoryComponent,
    EmailHeaderComponent, NavigationComponent, SearchComponent, PaymentComponent,ApprovalRequestDetailComponent,  ApprovalDetailComponent,flightSelected,SuperHeaderComponent,DropdownComponent,TripDetailsCardComponent
  ],

  exports: [OnboardingComponent,ApprovalDetailComponent,ApprovalRequestDetailComponent,FlightBoxComponent,
    HotelRequestViewComponent,GoogleMapsModule,  bookingHistoryComponent,   SafeUrlPipe,CarSearchComponent,CollapseModule, AirportsComponent, SwitchComponentComponent,  TranslateOptionsPipe, RangeSliderComponent, FilterComponent, flightSelected, PaymentComponent, SearchComponent, HotelfeedbackComponent, TranslateModule, AppLoaderComponent, LoaderDotsComponent, AddCardWidgetComponent, ngxNewModal, EmailHeaderComponent, NavigationComponent,SuperHeaderComponent,DropdownComponent,TripDetailsCardComponent
  ]
})
export class ShareModule {

}