@import "../../variables.scss";

.switch {
    position: absolute;
    height: 48px;
    width: 48px;
    border-radius: 50%;
    right: -24px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 8px;
    z-index: 1; // cursor: pointer;
    border: 2px solid #E7E6E4;
}







/*new scss for new design*/


.switch-toggle-label {
    outline: none !important;
    cursor: pointer;
}

.class-toggle-switch-book-for {
    width: 250px;
    display: inline-block;
    border: 1px solid #E4E4E4 !important;
    border-radius: 6px !important
}

.switch-toggle-label-book-for {
    background-color: var(--dark-bg-color) !important;
}

.class-toggle-switch {
    width: 212px;
    display: inline-block;
}

.switch-toggle.switch-candy,
.switch-light.switch-candy>span {
    color: #AEAEAE;
    border: 1px solid #E4E4E4;
    border-radius: 7px;
    background-color: #F7F7F7;
    box-shadow: none
}

.switch-light.switch-candy span span,
.switch-light.switch-candy input:checked~span span:first-child,
.switch-toggle.switch-candy label {
    color: #AEAEAE;
    font-size: 14px;
    line-height: 24px;
    text-shadow: none;
    height: 24px;
    font-weight: normal;
}

.switch-light.switch-candy input~span span:first-child,
.switch-light.switch-candy input:checked~span span:nth-child(2),
.switch-candy input:checked+label {
    border: none;
    border-radius: 6px;
    background-color: #transparent;
    color: #fff;
    text-shadow: none;
}

.switch-candy a {
    background-color: #aeaeae;
    border: 1px solid #E4E4E4;
    box-shadow: none;
    border-radius: 6px;
    background-image: none;
}

.switch-toggle a {
    position: absolute;
    top: 0;
    left: 0;
    padding: 0;
    z-index: 1;
    width: 50%;
    height: 100%;
    border: none;
}



// For Tablet
@media (max-width: 767.98px) {


    .switch {
        height: 28px;
        width: 28px;
        right: -13px;
        top: 7px;
    }

    .switch .icon-arrow {
        font-size: 8px;
    }



    .switch-div {
        width: 100%;
        text-align: center;
        position: fixed;
        top: 420px;
        max-width: none;
        min-width: 1px;
        left: 0;
    }

    .class-toggle-switch {
        margin-top: 15px;
    }

}


@media (max-width:650px) {
    .class-toggle-switch-book-for {
        width: 100%;
        min-width: 250px;
        max-width: 400px;
        border: 2px solid var(--dark-bg-color) !important;
        border-radius: 8px !important
    }
}





// For Mobile
@media (max-width: 575.98px) {

    .search-wrapper,
    .no-flight-found-container {
        padding: 16px 16px;
        /*margin: 0 8px;*/
    }

    .date-input {
        /*margin: 0 4px 0 2px;*/
    }

    .select-input {
        margin: 0;

    }

    .text-input .icon {
        left: 14px;
    }

    .text-input .icon+input {
        padding-left: 46px;
    }

    .text-input input {
        padding-left: 16px;
    }

    .primary-button {
        padding: 14px 35px;
        letter-spacing: 2px;
        width: 100%;
    }

    .add-more {
        width: 100%;
        margin-bottom: 24px;
    }
}

@media (max-width: 450px) {
    .switch-div {
        top: 440px;
    }
}