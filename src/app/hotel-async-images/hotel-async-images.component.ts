import { Component, OnInit, Input } from '@angular/core';
import { HotelResult } from '../entity/hotel-result';
import { Subscription } from 'rxjs';
import { HotelSearchService } from '../hotel-search.service';
import { TraflaImage } from '../entity/trafla-image';
import { CommonUtils } from '../util/common-utils';

@Component({
    selector: 'hotel-async-images',
    templateUrl: './hotel-async-images.component.html',
    styleUrls: ['./hotel-async-images.component.scss'],
    standalone: false
})
export class HotelAsyncImagesComponent implements OnInit {


  @Input() hotelItem: HotelResult;
  hotelThumbnailImage: String;
  imageRequestSubscription: Subscription;

  constructor(private searchService: HotelSearchService) {
    setTimeout(() => {
      this.syncHotelImage();
    }
      , 500);
  }

  ngOnInit() {

  }

  private syncHotelImage() {
    if (!this.hotelItem.thumbnailImage) {

      this.imageRequestSubscription = this.searchService.requestHotelImage(this.hotelItem).subscribe(res => {
        // setTimeout(()=>{
        if (res.success === true && res.data) {
          this.hotelItem.hotelImages = res.data.hotelImages;
          this.hotelThumbnailImage = CommonUtils.getThumbnailImage(this.hotelItem.hotelImages);
        }
      }, error => {
        setTimeout(() => {

        }, 100);
      });
    }
  }

}
