.modal-logo {
  max-width: 250px;
}

.modal-body {
  padding: 0;
}

.modal-loader {
  background: #fff;
  text-align: center;
  border-radius: 5px;
  background-color: #FFFFFF;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
  width: 100%;
}

.modal-loader-top {
  float: left;
  width: 100%;
  padding: 22px 68px 44px 69px;
}
.popMsg{
  background: #EDFAFC;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 20px;
}
.modal-loader-bottom {
  float: left;
  width: 100%;
  background-color: #EFFAFC;
  padding: 28px 0 28px 0;
  border-radius: 0 0 6px 6px;
}

.modal-waiting-img img {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.modal-loader-top-text p {
  font-size: 18px;
  line-height: 24px;
  text-align: center;
}

.modal-loader-bottom-text p {
  font-size: 15px;
  line-height: 19px;
  text-align: center;
  max-width: 247px;
  margin: 0 auto;
}

.arrow-icon-vertical-container {
  text-align: center;
  margin-left: -88px;
}

.arrow-icon-vertical {
  height: 21px;
  width: 2px;
  background: #979797;
  display: inline-block;
  position: relative;
}

.arrow-icon-vertical:before {
  position: absolute;
  content: '';
  top: -2px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 10px solid #979797;
  left: -4px;
}

.image {
  transform: rotate(360deg);
}

@-webkit-keyframes rotating {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

.rotating {
  -webkit-animation: rotating 5s linear infinite;
}

.modal-loader-bottom-text {
  float: left;
  width: 100%;
  padding-top: 5px;
  padding-bottom: 37px;
}

@media (max-width: 1200px) {
  .modal-logo {
    width: 87.27px;
  }

  .modal-loader-top-text p {
    font-size: 12px;
    line-height: 19px;
    max-width: 214px;
    margin: 0 auto;
  }

  .modal-loader-top {
    padding: 30px 20px 38px 20px;
  }

  .arrow-icon-vertical {
    margin-left: -170px;
  }
}

@media (max-width: 767px) {
  .arrow-icon-vertical-container {
    margin-left: -108px;
  }

  .modal-loader-bottom-text {
    padding-bottom: 16px;
  }
}

@media(max-width:768px) {
  ::ng-deep.modal-body1 {
    background-color: var(--dark-bg-color) !important;
    width: 100%;
    display: inline-block;
  }

  .modal-loader1 {
    height: 100vh;
    display: inline-block;
    background: #6610f2 !important;
    text-align: center;
    border-radius: 5px;
    background-color: var(--dark-bg-color) !important;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    width: 100%;
  }

  .modal-loader-top1 {
    height: 100vh;
    float: left;
    width: 100%;
    padding: 0 30px;
    background-color: var(--dark-bg-color) !important;
  }

  ::ng-deep.modal-content {
    border-radius: 0px;
    box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
    border: none;
    text-align: center;
    max-width: 710px;
    margin: 0px !important;
    width: calc(100% - 0px) !important;
  }

  .modal-loader-bottom1 {
    float: left;
    width: 100%;
    background-color: var(--dark-bg-color) !important;
    color: #fff !important;
    padding: 28px 0 28px 0;
    border-radius: 0 0 6px 6px;
  }
}

.filter-row {
  display: flex;
  justify-content: space-between;
}

@media (max-width: 320px) {
  .filter-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
  }

  .search {
    font-family: "apercu-r";
    font-size: 8px;
    letter-spacing: 0px;
    font-weight: none;
  }

  .spinner1 {
    margin: 0px !important;
    width: 40px !important;
    text-align: left !important;
  }

  .spinner1>div {
    width: 10px !important;
    height: 10px !important;
    background-color: rgb(248, 244, 252);

    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite;
    animation: sk-bouncedelay 1.4s infinite;
  }
}

.search {
  font-family: "apercu-r";
  font-size: 14px;
  letter-spacing: 1px;
  font-weight: none;
}

.spinner1 {
  margin: 0px !important;
  width: 60px !important;
  text-align: center;
}

.spinner1>div {
  width: 14px !important;
  height: 14px !important;
  background-color: rgb(248, 244, 252);

  border-radius: 100%;
  display: inline-block;
  -webkit-animation: sk-bouncedelay 1.4s infinite;
  animation: sk-bouncedelay 1.4s infinite;
}

.result {
  align-items: center;
  font-family: "apercu-r";
  font-weight: none;
  text-align: center;

}

.result1 {
  align-items: center;
  font-family: "apercu-r";
  font-weight: none;
}

.spinner1 .bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.spinner1 .bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {

  0%,
  80%,
  100% {
    -webkit-transform: scale(0)
  }

  40% {
    -webkit-transform: scale(1.0)
  }
}

@keyframes sk-bouncedelay {

  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1.0);
    transform: scale(1.0);
  }
}