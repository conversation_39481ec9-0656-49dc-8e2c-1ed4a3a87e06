import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { Subscription, throwError } from 'rxjs';
import { Router, ActivatedRoute } from '@angular/router';
import { UserAccountService } from '../user-account.service';
import { AdminPanelService, CompanySettings } from '../admin-panel.service';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { TranslateService } from '@ngx-translate/core';
import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Constants } from '../util/constants';
import { environment } from 'src/environments/environment';
import { catchError } from 'rxjs/operators';
import { CommonUtils } from '../util/common-utils';
import { Data } from '../util/bookingdata';
import { CancelSuccessComponent } from '../cancel-success/cancel-success.component';
import { SearchService } from '../search.service';


@Component({
    selector: 'app-admin-integeration',
    templateUrl: './admin-integeration.component.html',
    styleUrls: ['./admin-integeration.component.scss'],
    standalone: false
})
export class AdminIntegerationComponent implements OnInit {
  companySettingsSubscription: Subscription;
  viewMode2='tab21';
  lccList = {};
  companyName : '';
  qboCompanyName='';
  cardIndex: number;
  switchon = false;
  showmstSwitch = false;
  settingSaveProcessing = false;
  qboEnabled = false;
  allowedOtherEmployees = false;
  mst_url = '';
  mst_url_original = '';
  flightExpenseCategory: any;
  hotelExpenseCategory:any;
  expenseBooleanArray = [];
  expenseProviders = [];
  expensifyArray = [];
  slackEnabled = false;
  bsModalRef: BsModalRef;
  bsModalRef1: BsModalRef;
  queryParamSubscription: Subscription;
  hideGoBackButton = false;
  allowedExpensify = [];
  showLoader=false;
  expenseCategory: Array<any> = [];
  expenseCategoryTree = {};
  qboMap=[];
  companyCards=[];
  companySettings: CompanySettings;
  integrationData : any;
  samlType='';
  samlTypeIndex=-1;
  samleTypeName = {'okta':'Okta', 'msal':'Microsoft Entra', 'zoho':'Zoho', 'google_sso':'Google'};
  constructor( public router: Router, public userAccountInfoService: UserAccountService,
    public adminPanelService: AdminPanelService,
    private el: ElementRef,
    private gallopLocalStorage: GallopLocalStorageService,
    public translateService: TranslateService,
    private activatedRoute: ActivatedRoute,
    public searchService: SearchService,
    private ngxAnaltics:GoogleAnalyticsService,
    private modalService: BsModalService,
    private toastr: ToastrService,) { }
    @ViewChild('inputField') inputField: ElementRef;
    @ViewChild('inputField2') inputField2: ElementRef;
    updatingQboExpense=false;
    slactConfiClick=false;
    mstConfiClick=false
    oktaConfiClick =false;
    mainscreen =true;
    selectedExpensedId='';
    quickbooksConfClick =false;
    showoktaSwitch = false;
    showoktawitch =false; 
    oktaSSoName='';
    oktaId='';
    oktaMetadataURL='';
    okta_fields=[];
    oktaNameIDformat='emailAddress';
    okta_url='';
    customerID = '';
    showExpenseTypeChanged(event, type) {
      let expenseCatIdToSend = this.hotelExpenseCategory;
      if (type === 'flight') {
        expenseCatIdToSend = this.flightExpenseCategory;
      }
      if(event) {
        this.expenseApiCall(expenseCatIdToSend, type);
      } else {
        this.expenseApiCall('', type);
      }
    }
    backtoOriginalScreen(){
      this.slactConfiClick =false;
      this.mainscreen =true;
      if (this.mst_url === '') {
        this.showmstSwitch =false;
      }
      if(this.oktaConfig[this.samlTypeIndex]){
       // this.oktaMetadataURL = this.oktaConfig.metadataUrl;
        this.metaurlError =false;
              if(this.oktaConfig[this.samlTypeIndex] && this.oktaConfig[this.samlTypeIndex].activeStatus!=='disabled'){
                this.showoktaSwitch = true;
              }
            }
      this.mstConfiClick=false;
      this.quickbooksConfClick =false;
      this.selectedExpensedId='';
      this.oktaConfiClick =false;
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'setting',
          subType:'integrations',
        },
        replaceUrl: false
      }
    );
    }
    uploadingMetaDataUrl =false;
    uploadMeataUrl(){
     
              this.metaurlError=false;
      this.uploadingMetaDataUrl=true;
      this.adminPanelService.uploadSamlMetaData(this.oktaMetadataURL).subscribe(resp =>{
        if(resp && resp.status==='success'){
          this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"));
          this.uploadingMetaDataUrl=false;
        }else if (resp && resp.status === 'error') {
          this.uploadingMetaDataUrl=false;
          if(resp.errorMessage && resp.errorMessage.length > 0){
          this.toastr.error(resp.errorMessage[0]);
          }
        }
      });
    }
    selectedFile = null;
    imageSrc: any;
    readURL(event): void {
      if (event && event[0]) {
        const file = event[0];
        this.selectedFile = file;
        this.uploadingMetaDataUrl=true;
        this.renderPostUploadFile();
      }
    }
    renderPostUploadFile() {
      const reader = new FileReader();
      const file = this.selectedFile;
      reader.onload = e => this.imageSrc = reader.result;
  
      reader.readAsDataURL(file);
      
      this.adminPanelService.uploadSamlMetaData(this.selectedFile).subscribe(resp => {
        if (resp && resp.status === 'success') {
          this.uploadingMetaDataUrl=false;
          this.oktaMetadataURL = resp.data;
          this.oktaConfig[this.samlTypeIndex].metadataUrl =this.oktaMetadataURL ;
          this.metaurlError =false;
          //this.buildTransactionReport(this.originalTransactionReport);
        
        //  this.toastr.success("Receipt uploaded successfully !!!");
        this.toastr.success(this.translateService.instant('dashboard.MetadataFileuploadedsuccessfully'));
        } else {
          this.uploadingMetaDataUrl=false;
          if (resp.errorMessage && resp.errorMessage.length > 0) {
            this.toastr.error(resp.errorMessage[0]);
          } else {
            this.toastr.error(this.translateService.instant('cards.Pleasetryagainlater'));
          }
        }
      })
  
    }
    isSAMLDisabled(){
      if((this.oktaConfig && this.oktaConfig.length > 0 && this.oktaConfig[this.samlTypeIndex]) && this.oktaConfig[this.samlTypeIndex].activeStatus!=='disabled'){
        return true;
      }else{
        return null;
      } 
    }
    getDisabledOktaUrlInput(){
      if((this.oktaConfig && this.oktaConfig.length > 0 && this.oktaConfig[this.samlTypeIndex]) && (this.oktaConfig[this.samlTypeIndex].metadataUrl && this.oktaConfig[this.samlTypeIndex].metadataUrl!=='')){
        return true;
      }else{
        return true;
      }
    }
    openOktaConfigureModal(modal){
      this.bsModalRef1 = this.modalService.show(modal);
    }
    deactivateSaml(){
       
                  this.showoktaSwitch =false;
                  this.metaurlError =false;
                 
                  this.oktaConfig[this.samlTypeIndex].metadataUrl = this.oktaMetadataURL;
                  
                
          this.showInputloader =true;
          
          this.adminPanelService.getSamlDataDeactivate(this.oktaConfig[this.samlTypeIndex]).subscribe(resp =>{
            if(resp && resp.status==='success'){
              this.oktaConfig[this.samlTypeIndex].activeStatus ='disabled';
              this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"));
            
            this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
              this.showInputloader = false;
            }else if (resp && resp.status === 'error') {
              this.showInputloader = false;
              if(resp.errorMessage && resp.errorMessage.length > 0){
                this.showoktaSwitch =false;
                this.oktaConfig[this.samlTypeIndex].metadataUrl=null;
               // this.oktaConfig[this.samlTypeIndex].activeStatus ='disabled';
              this.toastr.error(resp.errorMessage[0]);
              }
            }else{
              this.showInputloader = false;
            }
          })
          }
    
    activateSaml(){
  
        
    if(this.oktaMetadataURL==='' || !this.oktaMetadataURL){
      this.metaurlError=true;
     
      setTimeout(() => {
        this.showoktaSwitch =false;
        const firstInvalidControl: HTMLElement = this.el.nativeElement.querySelector(".error");
        this.scrollTo(firstInvalidControl);
      }, 100);
      return;
            }
            this.showoktaSwitch =true;
          
            this.oktaConfig[this.samlTypeIndex].ssoName = this.oktaSSoName;
            this.oktaConfig[this.samlTypeIndex].metadataUrl = this.oktaMetadataURL;
         
    this.showInputloader =true;
    
    this.adminPanelService.getOktaDataSaving(this.oktaConfig[this.samlTypeIndex]).subscribe(resp =>{
      if(resp && resp.status==='success'){
        if(this.showoktaSwitch){
          let msg;
          this.oktaConfig[this.samlTypeIndex].activeStatus ='active';
          //if(this.samlType==='okta'){
            msg = this.translateService.instant('dashboard.Yourrequestforactivating') +" " +this.samlType.toUpperCase()+ " "+this.translateService.instant('dashboard.SSOhasbeenreceivedOurteamwillreviewandenabletheintegrationForanyquestionspleasereachouttotriproutespringcom') 
         // }
       //  msg = this.translateService.instant('<EMAIL>')
        this.bsModalRef = this.modalService.show(CancelSuccessComponent, {
          initialState: {
            message:   msg,
            message2: 'AllowedToProceed'
          }, backdrop: false, keyboard: false, ignoreBackdropClick: false
        })
      }else{
        this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"));
      }
      this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
        this.showInputloader = false;
      }else if (resp && resp.status === 'error') {
        this.showInputloader = false;
        if(resp.errorMessage && resp.errorMessage.length > 0){
          this.showoktaSwitch =false;
          this.oktaConfig[this.samlTypeIndex].metadataUrl=null;
        //  this.oktaConfig[this.samlTypeIndex].activeStatus ='disabled';
        this.toastr.error(resp.errorMessage[0]);
        }
      }else{
        this.showInputloader = false;
      }
    })
    }
    quickbooksConfiguration(subType){
      this.mainscreen=false;
      this.quickbooksConfClick =true;
      this.router.navigate(["admin"],
    {
      queryParams:
      {
        type: 'setting',
          subType:'integrations',
          subType2: subType,
        product:'quickbooks'
      },
      replaceUrl: false
    }
  );
      window.scrollTo(0, 0);
    }
    expenseConfiguration(id,subType){
      this.mainscreen=false;
      this.selectedExpensedId=id;
      let prod='';
      if(id==='EXPENSE_EXPENSIFY'){
         prod = 'expensify';
      }else if(id==='EXPENSE_CONCUR'){
        prod = 'concur';
     }else{
       prod ='zoho';
     }
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'setting',
          subType:'integrations',
          subType2: subType,
          product:prod
        },
        replaceUrl: false
      }
    );
      window.scrollTo(0, 0);
    }
    slackConfiguration(subType){
      this.slactConfiClick =true;
      this.mainscreen =false;
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'setting',
          subType:'integrations',
          subType2: subType,
          product:'slack'
        },
        replaceUrl: false
      }
    );
      window.scrollTo(0, 0);
    }
    oktaConfiguration(subType,index,subtype1){
      this.oktaConfiClick =true;
this.samlType = subtype1.toLowerCase();
this.samlTypeIndex =index;
      this.mainscreen =false;
      if(this.oktaConfig && this.oktaConfig[index]){
        for(let key in this.allProviderFields){
          if(key===this.oktaConfig[this.samlTypeIndex].provider){
          let value = this.allProviderFields[key];
          this.okta_fields = value;
          }
        }
        this.oktaMetadataURL = this.oktaConfig[index].metadataUrl;
        this.oktaSSoName = this.oktaConfig[index].ssoName;
        //this.okta_fields=  this.allProviderFields[index];
        this.okta_url = this.oktaConfig[index].ssoUrl;
        this.oktaId= this.oktaConfig[index].spEntityId;
        this.oktaStatus =this.oktaConfig[index].status;
        this.customerID = this.oktaConfig[index].ssoUrl.substring(this.oktaConfig[index].ssoUrl.lastIndexOf('/') + 1);
     // this.oktaMetadataURL = this.oktaConfig.metadataUrl;
            this.oktaSSoName = this.oktaConfig.ssoName;
            if(this.oktaConfig[index] && this.oktaConfig[index].activeStatus!=='disabled'){
              this.showoktaSwitch = true;
            }
          }
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'setting',
          subType:'integrations',
          subType2: subType,
          product:subtype1.toLowerCase(),
          index:this.samlTypeIndex
        },
        replaceUrl: false
      }
    );
      window.scrollTo(0, 0);
    }
    mstConfiguration(subType){
      this.mstConfiClick =true;
      this.mainscreen =false;
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: 'setting',
          subType:'integrations',
          subType2: subType,
          product:'msteams'
        },
        replaceUrl: false
      }
    );
      window.scrollTo(0, 0);
    }
    showFormatColumn(){
      if (this.oktaConfig[this.samlTypeIndex].provider === 'OKAT' || this.oktaConfig[this.samlTypeIndex].provider === 'ZOHO') {
        return true;
      }
      return false;
    }
    changeQuickBooks(event,modal){
      if(event){
        window.open(this.quickBookUrl, "_blank");
        this.stopPollinng =false;
        this.initiatePolling();
      }else{
       
        this.bsModalRef1 = this.modalService.show(modal, {
          initialState: {
          }, backdrop: true, ignoreBackdropClick: true
        });
        
      }
    }
    copyInputValue3(value){
      const selBox = document.createElement('textarea');
      selBox.style.position = 'fixed';
      selBox.style.left = '0';
      selBox.style.top = '0';
      selBox.style.opacity = '0';
      selBox.value = value;
      document.body.appendChild(selBox);
      selBox.focus();
      selBox.select();
      document.execCommand('copy');
      document.body.removeChild(selBox);
        this.toastr.success(this.translateService.instant('dashboard.Copiedtoclipboard'));
    }
    copyInputValue() {
      const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = this.okta_url;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
      this.toastr.success(this.translateService.instant('dashboard.Copiedtoclipboard'));
    }
    copyInputValue2() {
      const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = this.oktaId;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
      this.toastr.success(this.translateService.instant('dashboard.Copiedtoclipboard'));
    }
    expenseApiCall(id, type) {
      this.updatingQboExpense=true;
      this.adminPanelService.getUpdateQboExpense(id, type).subscribe(resp => {
        if (resp && resp.status === 'success') {
          this.updatingQboExpense=false;
          this.toastr.success(this.translateService.instant('cards.Expensecategoryupdatedsuccessfully'))
  
        } else {
          this.updatingQboExpense=false;
         // this.toastr.error("Expense  category not updated . Please try again later !!!");
         this.toastr.error(this.translateService.instant('cards.Expensecategorynotupdated.Pleasetryagainlater'))
        }
      })
    }
    oktaStatus='';
    oktaConfig:any;
    ssoHelpLinks:any;
    allProviderFields:any;
    getOktaData(){
      this.adminPanelService.getOktaDataRendering().subscribe(resp =>{
        if(resp && resp.status==='success'){
         
          if(resp.data && resp.data.configs && resp.data.configs.length >0){
            
          this.ssoHelpLinks = resp.data.helpLinks;
            this.oktaConfig = resp.data.configs;
            if(resp.data.configs[0].activeStatus==='active'){
              this.showoktaSwitch = true;
            }
           
          }
          if(resp.data && resp.data.allProviderFields &&  Object.keys(resp.data.allProviderFields).length >0){
          
   this.allProviderFields=resp.data.allProviderFields;
  
            

          }
          if(this.samlTypeIndex > -1){
            for(let key in resp.data.allProviderFields){
              if(key===this.oktaConfig[this.samlTypeIndex].provider){
              let value = resp.data.allProviderFields[key];
              this.okta_fields = value;
              }

            }
            this.oktaMetadataURL = this.oktaConfig[this.samlTypeIndex].metadataUrl;
            this.oktaSSoName = this.oktaConfig[this.samlTypeIndex].ssoName;
            this.okta_url = this.oktaConfig[this.samlTypeIndex].ssoUrl;
            this.oktaId= this.oktaConfig[this.samlTypeIndex].spEntityId;
            this.oktaStatus =this.oktaConfig[this.samlTypeIndex].status;
            this.customerID = this.oktaConfig[this.samlTypeIndex].ssoUrl.substring(this.oktaConfig[this.samlTypeIndex].ssoUrl.lastIndexOf('/') + 1);
         // this.oktaMetadataURL = this.oktaConfig.metadataUrl;
                this.oktaSSoName = this.oktaConfig.ssoName;
                if(this.oktaConfig[this.samlTypeIndex] && this.oktaConfig[this.samlTypeIndex].activeStatus!=='disabled'){
                  this.showoktaSwitch = true;
                }
              }
         
        }else if(resp){
          
        }
      })
    }
  

    openOkta(item){
      let url;
      if(item){
       url = this.ssoHelpLinks[item];
       if(url){
       window.open(url,'_blank');
       }
    }
   
    }
    openMSTHelpArticle(){
      window.open('https://help.routespring.com/portal/en/kb/articles/configure-approval-notifications-on-microsoft-teams','_blank');
    }
    getssoName(item){
      if(item!==''){
        this.ssonameError = false;
      }
    }
    getMetaurl(item){
      if(item!==''){
        this.metaurlError =false;
      }
      
    }
    showInputloader =false;
    ssonameError = false;
    metaurlError =false;
    saveOkta(){
      
    }
getQboMap(){
  this.qboMap=[];
  this.adminPanelService.getPaymentOptions().subscribe(resp => {
    if(resp && resp.status==='success') {
      this.showLoader=false;
      if(!this.disccconedQbo){
      this.stopPollinng =true;
      }
      if(resp.data && resp.data.companyCards){
        this.companyCards = resp.data.companyCards;
      }
      if(resp.data && resp.data.qboAccountMap){
        this.qboMap = resp.data.qboAccountMap;
      }
      if(resp.data && resp.data.expenseCategory){
        if (resp.data.expenseCategory.flightExpenseCategory) {
          this.flightExpenseCategory = resp.data.expenseCategory.flightExpenseCategory;
        } else {
          this.flightExpenseCategory =null;
        }
          if (resp.data.expenseCategory.hotelExpenseCategory) {
          this.hotelExpenseCategory = resp.data.expenseCategory.hotelExpenseCategory;
        } else {
          this.hotelExpenseCategory =null;
        }
      } else {
        this.flightExpenseCategory =null;
        this.hotelExpenseCategory =null;
      }
    } else if(resp) {
      this.showLoader=false;
    }
  });
}
scrollTo(el: Element): void {
  if (el) {
    el.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

private scrollToFirstInvalidControl() {
  const firstInvalidControl: HTMLElement = this.el.nativeElement.querySelector(".quickBooks");
  this.scrollTo(firstInvalidControl);
}

qboCards=[];
getPlaceHolder(item) {
  if (item) {
    return "";
  } else {
    return this.translateService.instant('employee.Select');
  }
}
getLabelValue(cardValue) {
  if (cardValue) {
    let findValue = this.qboCards.findIndex(item => item.categoryId === cardValue);
    let cardString= this.qboCards[findValue].name;
    return  cardString;
  } else  {
    return this.translateService.instant('employee.Select');
  }
}
getCardName(id){
  let findIndex = this.companyCards.findIndex(item => item.id===id);
  if(findIndex > -1){
  return this.companyCards[findIndex].nameOnCard; 
 } }
  getBrandName(id){
    let findIndex = this.companyCards.findIndex(item => item.id===id);
    if(findIndex > -1){
    return this.companyCards[findIndex].brand; 
    }else {
      return '';
    }
  }
  getLastFour(id){
    let findIndex = this.companyCards.findIndex(item => item.id===id);
    if(findIndex > -1){
    return this.companyCards[findIndex].last4; 
    }
  }
  ngOnInit(): void {
    this.expenseProviders = Constants.ALL_EXPENSE_PROVIDERS;
    if(!localStorage.getItem('integrationData')){
      this.getIntergrationData();
    }else {
      this.integrationData = JSON.parse(localStorage.getItem('integrationData'));
    }
    this.queryParamSubscription = this.activatedRoute.queryParams.subscribe(params => {
      if (params['subType2'] == 'expense') {

        if(params['product']==='expensify'){
         this.expenseConfiguration('EXPENSE_EXPENSIFY',params['subType2']);
        }else if(params['product']==='concur'){
          this.expenseConfiguration('EXPENSE_CONCUR',params['subType2']);
         }else{
          this.expenseConfiguration('EXPENSE_ZOHO',params['subType2']);
         }
        
      
      } else if (params['subType2'] == 'notifications') {
        if(params['product']==='slack'){
         this.slackConfiguration(params['subType2'])
        }else{
          this.mstConfiguration(params['subType2']) 
        }
      } else if (params['subType2'] == 'hri') {
        this.samlTypeIndex = params['index'] ;
        this.samlType = params['product'] 
         this.oktaConfiguration(params['subType2'],this.samlTypeIndex,this.samlType)
        
      }else if (params['subType2'] == 'accounting') {
        this.quickbooksConfiguration(params['subType2'])
       
     }

    });
    if(this.adminPanelService.routeToQuickBooks){
      setTimeout(() => {
        this.scrollToFirstInvalidControl();
      }, 100);
    
      this.adminPanelService.routeToQuickBooks=false
    }
    this.uuidKey = this.generateUUID();
    this.expenseCategory=[];
    this.adminPanelService.getQuickBookdetails(this.uuidKey).subscribe(resp => {
      if (resp.success) {
        this.showQuickBookButton = true;
        this.showQucikBookButtonWithCompanyName = true;
        if(resp.data && resp.data.accounts && resp.data.accounts.length >0){
          this.qboCards = resp.data.accounts;
          let qboAccount ={name:this.translateService.instant('setting.None') ,categoryId:'',accountType:'Null'}
          this.qboCards.unshift(qboAccount);
        }
        if (resp.data.categories && resp.data.categories.length > 0) {
          this.initializeExpenseCategory(resp.data.categories);
        }
 this.showLoader=true;
       this.getQboMap();
        this.qboCompanyName = resp.data.qboCompanyName;
      } else {
        let userid = this.userAccountInfoService.getUserEmail();
        let sToken = this.userAccountInfoService.getSToken();
        this.uuidKey = this.generateUUID();
        let url = environment.apiForQuickBooksInitiate + '?userid=' + userid + '&sToken=' + sToken + '&request-id=' + this.uuidKey;
      
        this.quickBookUrl = url;
        this.showQucikBookButtonWithCompanyName = false;
        this.showQuickBookButton = true;
      }
    })
    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {
        this.companySettings = settings;
        //
        this.qboEnabled = settings.company.qboEnabled;
        this.getOktaData();
        if (this.companySettings.company.allowedExpenseVendors && this.companySettings.company.allowedExpenseVendors.length > 0) {
          this.allowedExpensify = this.companySettings.company.allowedExpenseVendors;

          this.expenseProviders = this.expenseProviders.filter(item1 => {
            for (let item of this.allowedExpensify) {
              if (item1.id === item) {
                //  item1.enable =true;
                return true;
              }
            }
          });

         
        }
        if (this.companySettings.company.ms_teams_channel_url && this.companySettings.company.ms_teams_channel_url.length > 0) {
          this.showmstSwitch = true;
          this.mst_url = this.companySettings.company.ms_teams_channel_url;
          this.mst_url_original = this.mst_url;
        }
        if (this.companySettings.company.expenseProviders && this.companySettings.company.expenseProviders.length > 0) {
          this.expensifyArray = this.companySettings.company.expenseProviders;
          for (let item of this.expensifyArray) {
            for (let item1 of this.expenseProviders) {
              if (item1.id === item) {
                item1.enable = true;
              } else {
                item1.enable = false;
              }
            }
          }
        }

        
       
        if (this.bsModalRef1) {
          this.bsModalRef1.hide();
          this.userAccountInfoService.deletingCard = false;
        }
        this.companyName = this.companySettings.company.name;
        this.slackEnabled = this.companySettings.company.slackEnabled;
       
       
      
      }
    });
  }
  TabClicked(item,subType) {
    this.viewMode2 = item;
    this.router.navigate(["admin"],
    {
      queryParams:
      {
        type: 'integrations',
        subType: subType
      },
      replaceUrl: false
    }
  );
  }
  onModelCancel1() {
    this.bsModalRef1.hide();
  }
  callSettingApi() {
    if (this.mst_url !== this.mst_url_original) {
      this.processSaveCompanySettings();
    } else {
      return;
    }
  }
  updatingQbo=false;
  showCardTypeChanged(event,cardid){
   
    this.updatingQbo=true;
    this.adminPanelService.getQboCardUpload(event.categoryId ,cardid).subscribe(resp=>{
      if(resp && resp.status==='success'){
        this.updatingQbo=false;
       this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"))
       
      }
    })

  }
  ngOnDestroy() {
   if(this.queryParamSubscription){
    this.queryParamSubscription.unsubscribe();
   }
   this.disccconedQbo =false;
   if(this.companySettingsSubscription){
    this.companySettingsSubscription.unsubscribe();
   }
  }
  getCurrencySymbol(item): string {
    return CommonUtils.getCurrencySymbol(item);

  }
 
  removeMSt(event) {
    if (event) {
      this.showmstSwitch = true;
      this.switchon = false;
    } else {
      this.showmstSwitch = false;
      this.switchon = true;
      this.processSaveCompanySettings();
    }
  }
  checkAllParents(term: string, item: any): boolean {
    for (let counter = 0; counter < this.expenseCategory.length; counter++) {
      const curr = this.expenseCategory[counter];
      if (curr.parentId === item.categoryId) {
        if (curr.name && curr.name.toLowerCase().indexOf(term) > -1) {
          return true;
        }
      }
    }
    return false;
  }
  searchByExpenseName = (term: string, item: any) => {
    term = term.toLowerCase();
    let originalItem = item;
    let parentID = item.parentId;
    if (this.checkAllParents(term, item)) {
      return true;
    }
    item = originalItem;
    if (item.name && item.name.toLowerCase().indexOf(term) > -1) {
      return true;
    }
    while (parentID) {
      item = this.expenseCategoryTree[parentID];
      if (item.name && item.name.toLowerCase().indexOf(term) > -1) {
        return true;
      }
      parentID = item.parentId;
    }
  }
  initializeExpenseCategory(expenseCategoryList: Array<any>) {
    this.expenseCategoryTree = {};
    this.expenseCategory = expenseCategoryList;
    if (expenseCategoryList) {
      for (let i = 0; i < expenseCategoryList.length; i++) {
        this.expenseCategoryTree[expenseCategoryList[i].categoryId] = expenseCategoryList[i];
      }
    }
  }
  openModal(modal) {
    this.bsModalRef1 = this.modalService.show(modal);
  }
  stopPollinng = false;
  showQuickBookButton = false;
  showQucikBookButtonWithCompanyName = false;
  quickBookUrl: any;
  disbaledQBoButton = false
  processQBO(res) {
    if (res.data && res.data.value && res.data.value === 'in_progress' && !this.stopPollinng) {
      this.initiatePolling1();
      return;
    }

    if (res.success) {
      this.showQuickBookButton = true;
      this.disbaledQBoButton = false;
      this.stopPollinng =true;
      this.showQucikBookButtonWithCompanyName = true;
      if (res.data.categories && res.data.categories.length > 0) {
        this.initializeExpenseCategory(res.data.categories);
      }
      this.showLoader=true;
      this.getQboMap();
      if(res.data && res.data.accounts && res.data.accounts.length >0){
        this.qboCards = res.data.accounts;
        let qboAccount ={name:'None' ,categoryId:''}
        this.qboCards.unshift(qboAccount);
      }
      this.toastr.success(this.translateService.instant("setting.CongratulationsYourQuickBooksOnlineisnowsuccessfullyconnectedAnyfuturetransactionswillnowbeautomaticallysyncedtoyourQuickBooksOnlineaccount"))
      this.companyName = res.data.qboCompanyName;
      
    } else {
      let userid = this.userAccountInfoService.getUserEmail();
      this.disbaledQBoButton = false;
      let sToken = this.userAccountInfoService.getSToken();
      this.uuidKey = this.generateUUID();
      this.toastr.error(this.translateService.instant("setting.AhSomethingwentwrongandyourQuickBooksOnlineaccountfailedtoconnectPleasetryagainoremailusattriproutespringcom"));
      let url = environment.apiForQuickBooksInitiate + '?userid=' + userid + '&sToken=' + sToken + '&request-id=' + this.uuidKey;
      
      this.quickBookUrl = url;
      this.showQucikBookButtonWithCompanyName = false;
      this.showQuickBookButton = true;
    }
  }
  uuidKey = '';
  initiatePolling() {
    this.disbaledQBoButton = true;
    
    //this.uuidKey = this.generateUUID()
    setInterval(() => {
      this.stopPollinng = true;
    }, 300000);
    setTimeout(() => {
      this.getIntiateQBO()
    }, 5000);
  }
  initiatePolling1() {
    setTimeout(() => {
      this.getIntiateQBO()
    }, 5000);
  }
  disconnectQbo(modal){
    this.bsModalRef1= this.modalService.show(modal);
  }
  disccconedQbo=false;
  disconnect() {
    this.disbaledQBoButton = true;
    if(this.bsModalRef1){
      this.bsModalRef1.hide();
    }
  
    this.adminPanelService.getDisconnnectQuickBooks().subscribe(resp => {
      if (resp.success) {
        this.disccconedQbo=true;
        this.getQboMap()
        this.showQucikBookButtonWithCompanyName = false;
        this.stopPollinng =false;
        this.stopPollinng =false;
        this.disbaledQBoButton = false;
        let userid = this.userAccountInfoService.getUserEmail();
        let sToken = this.userAccountInfoService.getSToken();
        this.uuidKey = this.generateUUID();
        let url = environment.apiForQuickBooksInitiate + '?userid=' + userid + '&sToken=' + sToken + '&request-id=' + this.uuidKey;
       
        this.quickBookUrl = url;
      } else {
        this.disbaledQBoButton = false;
        this.showQucikBookButtonWithCompanyName = true;
      }
    })
  }
  private generateUUID(): string {
    var text = "";
    var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    for (var i = 0; i < 5; i++)
      text += possible.charAt(Math.floor(Math.random() * possible.length));

    return text;
  }
  getIntiateQBO() {
    try {
      this.adminPanelService.getQuickBookdetails(this.uuidKey)
        .pipe(catchError(err => {
          return throwError(err);
        }))
        .subscribe((res) => {
          this.processQBO(res);
        }, error => {

        });
    } catch (error) {

    }
  }

  expenseOptionSelected(event, option) {
    if (event) {
      this.switchon = false;
      this.expensifyArray = [];
      //if (this.expensifyArray.indexOf(option) === -1) {
      for (let item of this.expenseProviders) {
        if (item.id === option) {
          item.enable = true;
        } else {
          item.enable = false;
        }
      }
      this.expensifyArray.push(option)
      // }
    } else {
      this.switchon = true;
      for (let item of this.expenseProviders) {
        if (item.id === option) {
          item.enable = false;
        }
      }
      if (this.expensifyArray.length > 0) {
        this.deleteItem(option);
      }
    }
    // this.isExpensifyChecked(option)
    
    this.processSaveCompanySettings();
  }
  deleteItem(msg) {
    const index: number = this.expensifyArray.indexOf(msg);
    if (index !== -1) {
      this.expensifyArray.splice(index, 1);
    }
  }
  isExpensifyChecked(item) {
    return this.expensifyArray.indexOf(item) > -1;
  }
  isAllowExpensify(item) {
    return (this.allowedExpensify.indexOf(item) !== -1);
  }
  changeSlack(event) {
    if (event) {
      
      this.switchon = false;
    } else {
      this.switchon = true;
    }
    this.slackEnabled = event
    this.settingSaveProcessing = true;
    this.processSaveCompanySettings();
  }
  processSaveCompanySettings() {
    let companySettings: any = {};
    companySettings['companyName'] = this.companyName ? this.companyName : '';
    if (this.showmstSwitch) {
      companySettings['ms_teams_channel_url'] = this.mst_url;
    } else {
      companySettings['ms_teams_channel_url'] = '';
    }
    companySettings['expenseProviders'] = this.expensifyArray;

    companySettings.allowBookForOthers = this.allowedOtherEmployees;
    companySettings.slackEnabled = this.slackEnabled;

    this.adminPanelService.saveCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId()
      , companySettings).subscribe(res => {
        if (res && res.success) {
          this.settingSaveProcessing = false;
          this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
          if (this.switchon) {
            this.toastr.warning(this.translateService.instant("setting.Settingssavedsuccessfully"));
          } else {
            this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"));
          }
        } else if (res && res.error_message) {
          this.settingSaveProcessing = false;
          this.toastr.error(res.error_message);
        } else {
          this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntprocessrequest.Pleasetryagainlaterorcontactsupport"));
        }
      }, error => {
        if (error.status != 403) {
          setTimeout(() => {
            this.settingSaveProcessing = false;
            let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
            this.toastr.error(resultErrorMessage);
          }, 100);
        }
      })
  }

  copyCustomerID() {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = this.customerID;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
    this.toastr.success(this.translateService.instant('dashboard.Copiedtoclipboard'));
  }
  getIntergrationData(){
    this.adminPanelService.getIntegrationsListFromJson().subscribe(resp =>{
      if(resp){
        this.integrationData = resp.data[0];
        
      }else {
        
      }
    });
  }
  requestMoreInfo(categoryName,index,subCategory){
    
    this.adminPanelService.sendIntegration(categoryName,subCategory).subscribe(resp=>{
      if(resp){
        this.toastr.success("Integration   Requested Successfully");
        var  data = this.integrationData;
        if( localStorage.getItem('integrationData')){
         data = JSON.parse(localStorage.getItem('integrationData'));
         localStorage.removeItem('integrationData')
        }
        for (let cat of data[categoryName].categories) {
          if (cat.name == subCategory) {
            cat.status = "requested";
            break;
          }
        }
        this.integrationData = data;
        localStorage.setItem("integrationData",JSON.stringify(this.integrationData));
        
      }
      else {
        
      }
    })
  }
}
