import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EmailBookingFlowRoutingModule } from './email-booking-flow.routing.module';
import { EmailBookingFlowComponent } from './email-booking-flow.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ShareModule } from '../share.module';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { NgSelectModule } from '@ng-select/ng-select';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { PersonalDetailsComponent } from './personal-details/personal-details.component';
import { PaymentDetailsComponent } from './payment-details/payment-details.component';
import { OptionSelectionComponent } from './option-selection/option-selection.component';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { UiSwitchModule } from 'ngx-ui-switch';


@NgModule({
  imports: [
    CommonModule,
    EmailBookingFlowRoutingModule,
    NgbModule,
    ShareModule,
    NgxSmartModalModule,
    AccordionModule,
    NgSelectModule,
    BsDatepickerModule,
    UiSwitchModule,
    ReactiveFormsModule,
    FormsModule,
  ],
  declarations: [
    EmailBookingFlowComponent,
    OptionSelectionComponent,
    PersonalDetailsComponent,
    PaymentDetailsComponent

  ],
})
export class EmailBookingFlowModule {

}