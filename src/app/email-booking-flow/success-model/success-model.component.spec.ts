import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SuccessModelComponent } from './success-model.component';

describe('SuccessModelComponent', () => {
  let component: SuccessModelComponent;
  let fixture: ComponentFixture<SuccessModelComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SuccessModelComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SuccessModelComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
