@import "../../../variables.scss";

.overflow-hidden {
    overflow: hidden !important;
}

.disabled {
    pointer-events: none;
    opacity: 0.5;
}

.mdl-radio__label {
    font-size: 14px;
    font-weight: normal;
    font-family: $fontRegular;
}

.card {
    padding: 40px;
}
.rightside{
    line-height: 20px;
    text-align: left;
}
.leftside{
    text-transform: capitalize;
    margin-right: 10px;
    position: relative;
    display: flex;
    align-items: center;
}
.payment-note {
    float: left;
    width: 100%;
    font-size: 14px;
    letter-spacing: 0.23px;
    line-height: 18px;
    font-family: $font2Regular;
}

.payment-note-inner {
    max-width: 1092px;
    margin-left: 40px;
}

td {
    font-size: 14px;
    letter-spacing: -0.62px;
    line-height: 26px;
    padding: 4px 0;
}

.payment-msg {
    font-size: 14px;
    letter-spacing: 0.1px;
    line-height: 18px;
    color: $themeColor2;
    font-family: $font2Regular;
    float: left;
    width: 100%;
    margin-bottom: 21px;
}

.button-container {
    float: left;
    width: 100%;
}

.button {
    border-radius: 2px !important;
    height: 64px;
    padding: 0 32px;
    border: none;
    text-transform: capitalize;
    font-size: 20px;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    letter-spacing: 0.6px;
    line-height: 25px;
}

.button-primary {
    cursor: pointer;
    background: $themeColor;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
    color: var(--button-font-color);
}

.button-full {
    width: 100%;
}

.payment-table {
    width: 100%;
    margin-top: 9px;
    margin-bottom: 62px;
}

.mdl-checkbox__label {
    font-size: 14px;
    line-height: 17px;
    font-weight: normal;
    font-family: $fontRegular;
}

.gallop-cash-input {
    height: 50px;
    width: 92px;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    padding: 0 5px;
    text-align: center;
}

.mdl-checkbox__box-outline {
    border: 1px solid #979797;
    width: 17px;
    height: 17px;
    border-radius: 0;
    background: #fff;
}

.mdl-checkbox.is-checked .mdl-checkbox__box-outline {
    border: 1px solid var(--button-bg-color);
}

.mdl-checkbox.is-checked .mdl-checkbox__tick-outline {
    background-color: var(--button-bg-color) !important;
}

.mdl-radio__outer-circle {
    border: 1px solid #979797;
    background-color: #FFFFFF;
}

.mdl-radio.is-checked .mdl-radio__outer-circle {
    border: 1px solid var(--button-bg-color);
}

//.mdl-radio__inner-circle{background: var(--button-bg-color); z-index: 2; height: 10px; width: 10px;top: 7px;left: 3px;}
.walletCashDiv {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 25px;
    margin-bottom: 49px;
    float: left;
    width: 100%;
}

.walletCashDiv .mdl-checkbox {
    width: auto;
    margin-right: 7px;
    height: auto;
}

.payment-options .mdl-radio img {
    width: 25px;
}

.payment-option-section {
    margin-bottom: 8px;
}

.payment-option-section .mdl-radio {
    line-height: 19px;
}

.card1 {
    margin-top: 7px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.19);
    background-color: #FFFFFF;
    border-radius: 6px;
    max-width: 612px;
    padding: 33px 48px;
    float: left;
    width: 100%;
}

.input-box {
    float: left;
    width: 100%;
    margin-bottom: 12px;
    position: relative;
}

.input-textfield {
    float: left;
    font-family: $fontMono;
    width: 100%;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    height: 50px;
    font-size: 16px;
    padding: 5px 16px;
    color: $primaryColor;
    resize: none;
    border-radius: 0 !important;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    margin-bottom: 8px;
}

.text-button {
    background: none;
    box-shadow: none;
    border: none;
    padding: 0 20px;
}

.text-button:first-child {
    padding-left: 0;
}

.text-button:last-child {
    padding-right: 0;
}

/*.delete-modal-button .text-button{ width: 45%;}*/
.button1 {
    width: 111px;
    text-align: left;
}

.button2 {
    width: 133px;
    text-align: right;
}

.card1-header h3 {
    margin: 8px 0 0 0;
    line-height: 26px;
    letter-spacing: -0.98px;
    font-size: 22px;
    margin-bottom: 22px;
}

.card1-header {
    float: left;
    width: 100%;
}

.card1-body {
    float: left;
    width: 100%;
}

.card1-footer {
    float: left;
    width: 100%;
    margin-top: 12px;
}

.add-card-div {
    float: left;
    width: 100%;
    line-height: 0;
    margin-top: 2px;
}

.add-card-link {
    float: left;
    width: 100%;
}

.add-card-link a i {
    font-size: 10px;
}

.flexContainer-payment {
    justify-content: space-between;
    align-items: stretch;
}

.card.small-card {
    width: 329px;
    margin: 0;
    padding: 25px 20px;
}

.card-inner {
    display: flex;
    align-items: stretch;
    flex-direction: column;
    justify-content: space-between;
}

.total-payment-amount {
    color: $themeColor2;
}

.card-body {
    padding: 0;
}

.input-box-icon-container {
    position: absolute;
    top: 0;
    height: 50px;
    display: flex;
    align-items: center;
    left: 19px;
}

.input-box-with-icon input {
    padding-left: 45px;
}

.modal-content-button {
    margin-bottom: 44px;
    float: left;
    width: 100%;
    justify-content: center;
    display: flex;
}

.modal-content-button button {
    position: relative;
    float: left;
}

.modal-content-button button:after {
    background: #c5c5c5;
    width: 2px;
    height: 14px;
    content: "";
    position: absolute;
    right: 0;
    top: 2px;
}

.modal-content-button button:last-child:after {
    display: none;
}

.modal-content-width {
    width: 333px;
}

.card-header {
    padding-bottom: 18px;
}

.card.small-card .card-header {
    padding-bottom: 12px;
}

.backLink {
    margin-bottom: 4px;
}

@media (max-width:1439px) and (min-width:1200px) {
    .container {
        width: 100%;
    }
}

@media (max-width:1199px) {
    .container {
        width: 970px;
    }

    .logo-icon {
        width: 160px;
        height: 55px;
        background-size: 160px;
    }

    .header-left {
        padding-left: 0;
    }

    .card1 {
        padding: 20px 20px 30px 20px;
        max-width: 500px;
    }
}

@media (max-width:991px) {
    .container {
        width: 740px;
    }

    .logo-icon {
        width: 160px;
        height: 55px;
        background-size: 160px;
    }

    .header-inner {
        height: 120px;
    }

    .walletCashDiv {
        flex-direction: column;
        align-items: flex-start;
    }

    .card {
        padding: 20px;
    }

    .card.small-card {
        width: 260px;
        padding: 15px 15px;
    }

    .card1 {
        padding: 20px;
        max-width: 350px;
    }

    .input-box {
        margin-bottom: 0;
    }

    .input-textfield {
        height: 40px;
        font-size: 12px;
        line-height: 12px;
    }

    .card1-header h3 {
        font-size: 16px;
        letter-spacing: -0.71px;
        line-height: 26px;
        margin-bottom: 16px;
    }

    .card1-footer {
        margin-top: 16px;
    }

    .input-box-icon-container {
        height: 40px;
    }

    .button-primary {
        font-size: 12px;
        letter-spacing: 1px;
        line-height: 18px;
        height: 46px;
        text-transform: uppercase;
    }

    .link-primary {
        font-size: 10px;
        letter-spacing: 0.83px;
        line-height: 18px;
    }

    .gallop-cash-input {
        height: 40px;
        width: 72px;
        font-size: 12px;
        line-height: 15px;
        margin-left: 20px;
    }

    .walletCashDiv .mdl-checkbox {
        margin-bottom: 5px;
    }

    .text-button {
        padding: 0 25px;
    }
}

@media (max-width:767px) {
    .container {
        width: 100%;
        padding: 0 15px;
    }
    .leftside{
        text-transform: capitalize;
        margin-right: 10px;
        position: relative;
        display: flex;
        align-items: center;
    }
    .logo-icon {
        width: 92px;
        height: 30px;
        background-size: 90px;
    }

    .header-inner {
        height: 96px;
    }

    .user-profile-img,
    .user-email {
        display: none;
    }

    .gallop-cash {
        font-size: 10px;
    }

    .user-profile-content {
        padding-left: 0;
        width: 100%;
    }

    .page-content {
        padding-bottom: 16px;
    }

    .flexContainer-payment {
        flex-direction: column;
        justify-content: flex-start;
    }

    .card {
        padding: 0;
    }

    .card.small-card {
        width: 100%;
        background: #fff;
        padding: 19px 16px;
    }

    .card-inner {
        float: left;
        width: 100%;
        padding: 23px 16px 16px;
    }

    .walletCashDiv {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 0;
    }

    .card-header h3 {
        font-size: 16px;
        letter-spacing: -0.71px;
        line-height: 26px;
    }

    .mdl-radio__label {
        font-size: 12px;
        line-height: 15px;
    }

    .mdl-radio__outer-circle {
        height: 14px;
        width: 14px;
    }

    .payment-options .mdl-radio img {
        width: 20px;
    }

    .payment-option-section .mdl-radio {
        line-height: 16px;
    }

    .add-card-link a i {
        font-size: 8px;
    }

    .mdl-checkbox {
        line-height: 12px;
    }

    .mdl-checkbox__label {
        font-size: 12px;
        line-height: 15px;
        font-family: $fontMono;
    }

    .mdl-checkbox__box-outline {
        width: 12px;
        height: 12px;
    }

    .card.small-card .card-header {
        display: none;
    }

    .payment-note {
        font-size: 12px;
        line-height: 19px;
    }

    .gallop-cash-input {
        height: 40px;
        width: 72px;
        font-size: 12px;
        line-height: 15px;
        margin-left: 16px;
    }

    .payment-button-container {
        padding: 24px 0 0;
    }

    td {
        font-size: 12px;
        line-height: 15px;
        font-family: $fontRegular;
        padding: 3px 0;
        letter-spacing: normal;
    }

    .total-payment-label {
        line-height: 32px;
        letter-spacing: normal;
        font-size: 16px;
        font-family: $fontMono;
    }

    .total-payment-amount {
        font-size: 16px;
        line-height: 32px;
        font-family: $fontBold;
    }

    .payment-table {
        margin-top: 0;
        margin-bottom: 20px;
    }

    .payment-msg {
        margin-bottom: 0;
        font-size: 12px;
    }

    .mdl-checkbox.is-upgraded {
        padding-left: 20px;
    }

    .addCardFormTemplate {
        position: fixed;
        z-index: 9;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(238, 237, 235, 0.8);
        display: none;
    }

    .addCardFormTemplate.active {
        display: block;
    }

    .card1 {
        background-color: #EEEDEB;
        box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
        max-width: none;
        margin-top: 0;
        padding: 15px 32px 17px;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9;
    }

    .mdl-radio__inner-circle {
        height: 8px;
        width: 8px;
    }

    .payment-note-inner {
        margin-left: 0;
    }

    .user-name {
        text-align: right;
    }

    .button1 {
        width: 104px;
    }

    .button2 {
        width: 116px;
    }

    .appleDevice .input-textfield,
    .appleDevice .gallop-cash-input {
        font-size: 16px !important;
        min-width: 100px;
        letter-spacing: -1.1px !important;
        word-spacing: -2px !important;
        padding-top: 0px;
        padding-bottom: 0;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
    }
}


/*modal css*/
.modal-dialog {
    width: calc(100% - 48px);
    max-width: 896px;
    margin: 0 auto;
}

.modal-dialog-md {
    max-width: 710px;
}

.modal-backdrop {
    background-color: #fff;
}

.modal-backdrop.in {
    opacity: 0.8;
}

.modal-content {
    border-radius: 0px;
    box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
    border: none;
    margin-top: 24px;
    margin-bottom: 24px;
}

.modal-header {
    border: none;
    padding: 24px 17px 20px 25px;
}

.modal-title {
    display: inline-block;
    width: 35px;
    float: left;
}

.modal-title img {
    width: 35px;
}

.close {
    color: #000;
    opacity: 1;
}

.close:hover {
    color: #000;
    opacity: 1;
}

.modal-body {
    padding-bottom: 35px;
    float: left;
    width: 100%;
    padding-top: 8px;
}

.modal-content {
    text-align: center;
    float: left;
    width: 100%;
}

.modal-icon-container {
    float: left;
    width: 100%;
    text-align: center;
}

.modal-icon {
    background-color: #DDFFF7;
    border: 3px solid #1EBD97;
    width: 48px;
    height: 48px;
    display: inline-flex;
    color: #1EBD97;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
}

.modal-icon i {
    font-size: 30px;
}

.modal-content-heading {
    float: left;
    width: 100%;
    margin-top: 15px;
    margin-bottom: 14px;
}

.modal-content-heading h3 {
    margin: 0 auto;
    max-width: 654px;
    font-size: 22px;
    letter-spacing: -0.98px;
    line-height: 34px;
}

.modal-content-text {
    margin-bottom: 13px;
    float: left;
    width: 100%;
}

.modal-content-note {
    float: left;
    width: 100%;
    margin-bottom: 42px;
}

.modal-content-text p {
    line-height: 26px;
    font-family: "apercu-r";
    color: #6C6865;
    font-size: 18px;
    margin-left: auto;
    margin-right: auto;
}

.modal-content-note p {
    letter-spacing: 0.23px;
    line-height: 21px;
    font-size: 14px;
    color: var(--dark-bg-color);
    max-width: 534px;
    margin: 0 auto;
}

.modal-content-width {
    margin-bottom: 33px;
}




@media (max-width:991px) {
    .modal button.link-primary {
        font-size: 12px;
        letter-spacing: 1px;
        line-height: 18px;
    }
}

/*common css ends*/
@media (max-width:767px) {
    .modal-body {
        padding-bottom: 15px;
    }

    .modal-content-heading h3 {
        letter-spacing: -0.71px;
        line-height: 23px;
        font-size: 16px;
    }

    .modal-content-text p {
        font-size: 12px;
        line-height: 19px;
    }

    .modal-content-note p {
        font-size: 10px;
        letter-spacing: -0.45px;
        line-height: 16px;
    }

    .modal-title img {
        width: 23px;
    }

    .close i {
        font-size: 16px;
    }

    .modal-header {
        padding: 16px 17px 15px 10px;
    }

    .modal-content-heading {
        margin-top: 14px;
        margin-bottom: 14px;
    }

    .modal-content-note {
        margin-top: 4px;
        margin-bottom: 62px;
    }

    .modal-content-button {
        margin-bottom: 28px;
    }

    .modal-icon-container {
        margin-top: 12px;
    }

    .delete-modal-button {
        margin-bottom: 0;
        margin-top: 23px;
    }
}


::placeholder {
    /* Chrome/Opera/Safari */
    color: #AEAEAE;
    opacity: 1;
}

::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    color: #AEAEAE;
    opacity: 1;
}

::-moz-placeholder {
    /* Firefox 19+ */
    color: #AEAEAE;
    opacity: 1;
}

:-ms-input-placeholder {
    /* IE 10+ */
    color: #AEAEAE;
    opacity: 1;
}

:-moz-placeholder {
    /* Firefox 18- */
    color: #AEAEAE;
    opacity: 1;
}



.appleDevice ::placeholder {
    /* Chrome/Opera/Safari */
    padding-top: 4px;
}

.appleDevice ::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    padding-top: 4px;
}

.appleDevice ::-moz-placeholder {
    /* Firefox 19+ */
    padding-top: 4px;
}

.appleDevice :-ms-input-placeholder {
    /* IE 10+ */
    padding-top: 4px;
}

.appleDevice :-moz-placeholder {
    /* Firefox 18- */
    padding-top: 4px;
}