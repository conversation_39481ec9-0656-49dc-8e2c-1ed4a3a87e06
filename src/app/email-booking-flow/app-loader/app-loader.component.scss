//.spinner {
//   margin: 50vh auto 0 auto;
//   width: 80px;
//   text-align: center;
// }
.spinner21 {
  margin: 5vh auto 0 auto !important;
  width: 80px;
  text-align: center;
}



.spinner .bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.spinner .bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {

  0%,
  80%,
  100% {
    -webkit-transform: scale(0)
  }

  40% {
    -webkit-transform: scale(1.0)
  }
}

@keyframes sk-bouncedelay {

  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1.0);
    transform: scale(1.0);
  }
}

.spinner21>div {
  width: 18px;
  height: 18px;
  background-color: rgb(117, 0, 244);

  border-radius: 100%;
  display: inline-block;
  -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinner21 .bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.spinner21 .bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}



.spinner {
  margin: 50vh auto 0 auto;
  width: 80px;
  height: 40px;
  text-align: center;
}

.spinner12 {
  margin: 0px auto 0 auto;
  width: 80px;
  height: 40px;
  text-align: center;
}

.spinner>div {
  background-color: var(--dark-bg-color);
  height: 100%;
  width: 2px;
  display: inline-block;
  margin: 0 4px;
  -webkit-animation: sk-stretchdelay .8s infinite ease-in-out;
  animation: sk-stretchdelay 0.8s infinite ease-in-out;
}

.spinner12>div {
  background-color: var(--dark-bg-color);
  height: 100%;
  width: 2px;
  margin: 0 4px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay .8s infinite ease-in-out;
  animation: sk-stretchdelay 0.8s infinite ease-in-out;
}

.spinner .rect1 {
  -webkit-animation-delay: -1.2s;
  animation-delay: -1.2s;
}

.spinner .rect2 {
  -webkit-animation-delay: -.8s;
  animation-delay: -.8;
}

.spinner12 .rect1 {
  -webkit-animation-delay: -1.2s;
  animation-delay: -1.2s;
}

.spinner12 .rect2 {
  -webkit-animation-delay: -.8s;
  animation-delay: -.8s;
}

@-webkit-keyframes sk-stretchdelay {

  0%,
  80%,
  100% {
    -webkit-transform: scaleY(0.4);
    opacity: 1
  }

  40% {
    -webkit-transform: scaleY(1.0);
    opacity: .4
  }
}

@keyframes sk-stretchdelay {

  0%,
  80%,
  100% {
    transform: scaleY(0.4);
    -webkit-transform: scaleY(0.4);
    ;
    opacity: 1
  }

  40% {
    transform: scaleY(1.0);
    -webkit-transform: scaleY(1.0);
    ;
    opacity: .4
  }
}