import { Component, OnInit, ViewChild } from '@angular/core';
import { EmailSearchV4DTO } from '../entity/email-flow/email-searchv4-dto';
import { Subscription } from 'rxjs';
import { EmailQuoteOptionsService } from '../email-quote-options.service';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { PersonalDetailsComponent } from './personal-details/personal-details.component';
import * as Script from "../../assets/js/emailflow/script.js";
import { BillingItem } from '../entity/email-flow/billing-item';
import { ValidatorFn, Validators, UntypedFormControl, AbstractControl, ValidationErrors, UntypedFormGroup, UntypedFormBuilder, UntypedFormArray, FormArray } from '@angular/forms';
import { UserAccountInfo } from '../entity/user-account-info';
import { UserAccountService } from '../user-account.service';
import { EmailHeaderComponent } from './header/header.component';
import { OptionSelectionComponent } from './option-selection/option-selection.component';
import { Constants } from '../util/constants';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { CommonUtils } from '../util/common-utils';
import { PaymentTypes } from '../enum/payment-types';
import { _ } from 'src/app/util/title';
import { TranslateService } from "@ngx-translate/core";
import { environment } from 'src/environments/environment';
import { FlightSearchRequest } from '../entity/flight-search-request';
import { AlgoTypes } from '../enum/algo-types';
import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { GallopAnalyticsUtil } from '../analytics.service';
import { countries } from '../util/countries';
import { BookingService } from '../booking.service';
import { PaymentDetailsComponent } from './payment-details/payment-details.component';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { SeatComponent } from '../seat/seat.component';
import { SearchService } from '../search.service';
import { DeviceDetailsService } from '../device-details.service';
import { DeleteCardModelComponent } from './delete-card-model/delete-card-model.component';
import { Title } from '@angular/platform-browser';
import { AdminPanelService } from '../admin-panel.service';
import { PhoneNumberDTO } from '../entity/phonenumber-dto';
import { ALL_HOTELCHAINS } from '../util/hotel-chains';
import { PlatformLocation, LocationChangeEvent } from '@angular/common';
import { PaymentBreakupComponent } from '../payment-breakup/payment-breakup.component';

declare var seatModalHack: any;
declare var setRequestParam: any;
declare var selectionCompleted: any;
declare var nextButtonClicked: any;
declare var updateAddToExpensifyUICheckbox: any;
declare var enableExpensifyTextbox: any;


@Component({
    selector: 'email-booking-flow',
    templateUrl: './email-booking-flow.component.html',
    styleUrls: ['./email-booking-flow.component.scss'],
    standalone: false
})
export class EmailBookingFlowComponent implements OnInit {

  isLoading: boolean = true;
  isMobile: boolean;
  bsModelLoginRef: BsModalRef;
  bsModalRef: BsModalRef;
  showExpenseProviders = false;
  expenseProviders = JSON.parse(JSON.stringify(Constants.ALL_EXPENSE_PROVIDERS));
  seatSelectionPage = false;
  countries = countries;
  allowExpensepRoviders = [];
  tagShow : Array<any>[];
  tagset=[]
  newTagSetsArray =[];
  showSeat = false;
  type1 = '';
  addToExpensify: boolean;
  emailQuotOptions: EmailSearchV4DTO;
  ticketId: string;
  emailId: string;
  threadId: string;
  priceChange: string;
  sToken: string;
  fetchEmailQuotSubscription: Subscription;
  paymentPage: boolean;
  showPaymentButton: boolean;
  showPaymentLink = false;

  pageMode: string = "emailflow";
  billingItemList: Array<BillingItem>;
  public uniqueAirlines = [];
  public uniqueHotels = [];
  uniqueCars = [];
  public eventIdAndOptions;
  passportMandatory: boolean;
  zipCodeMandatory: boolean;
  outsidePolicyFlag: boolean;
  canAttemptDirectBooking: boolean;
  isRevalidationInProgress: boolean;
  pageStage: string = 'selection';
  public currencyCode: string = 'USD';
  @ViewChild(PaymentDetailsComponent) paymentDetailChild: PaymentDetailsComponent;
  @ViewChild(PersonalDetailsComponent) personalDetailChild: PersonalDetailsComponent;
  @ViewChild(OptionSelectionComponent) optionSelectionChild: OptionSelectionComponent;
  @ViewChild(EmailHeaderComponent) headerChild: EmailHeaderComponent;

  private expensifyEmailControl: UntypedFormControl;
  private expenseEmailControl: UntypedFormControl;
  public emergencyContact: UntypedFormGroup;
  private addToExpensifyControl: UntypedFormControl;

  private travellersCount: number = 1;
  private travellerTypeData: any;
  private fetchEmailQuoteOptionsSubscription: Subscription;
  private fetchQueryParamSubscription: Subscription;
  private fetchAccountInfoSubscription: Subscription;
  private flightFareChangeSubscription: Subscription;
  private userAccountInfoObj: UserAccountInfo;
  private expensifyEmail: string = null;
  private urlParameters: any;
  constructor(private toastr: ToastrService, private activatedRoute: ActivatedRoute,
    private userAccountInfoService: UserAccountService,
    private modalService: BsModalService,
    private titleService: Title,
    private fb: UntypedFormBuilder,
    public adminPanelService: AdminPanelService,
    private deviceDetailsService: DeviceDetailsService,
    private gallopLocalStorage: GallopLocalStorageService,
    private emailBookingFlowService: EmailQuoteOptionsService, public router: Router,
    public translateService: TranslateService,
    public bookingService: BookingService,
    private searchService1: SearchService,
    public ngxAnaltics:GoogleAnalyticsService,
     location1: PlatformLocation) {
      location1.onPopState((event: LocationChangeEvent) => {
        this.searchService1.selectedFilter = [];
        this.bookingService.proceedButton =false;
        this.personalDetailChild.disableEmailForForm();
        this.router.events.subscribe(event => {
          if (event instanceof NavigationEnd) {
            if (event.url !=="/emailflow?pageMode=WebSearch" && this.searchService1.exitFromMultiTrip) {
             
                this.searchService1.multiTripBooking =false;
                this.searchService1.exitFromMultiTrip =false;

              this.router.navigate(['/'+this.userAccountInfoService.getDefaultRoutePath()], {
                queryParams: {},
                replaceUrl: true
              });
            }
          }
        });
       
      });
    this.isLoading = true;
  }
  createForm() {
    this.emergencyContact = this.fb.group({
      name: [''],
      relationship: [''],
      contactNumber: [""],
      dialCode1: ["+1"],
    })

  }
  ngOnInit() {
    this.deviceDetailsService.isMobile1().subscribe(isMobile => {
      this.isMobile = isMobile;
    });
    if (this.paymentPage) {
     // this.titleService.setTitle('');
      this.titleService.setTitle(this.translateService.instant('search.PaymentDetails'));
    } else {
     // this.titleService.setTitle('Traveller Details');
      this.titleService.setTitle(this.translateService.instant('search.TravellerDetails'));
    }
    this.expenseEmailControl = new UntypedFormControl('');
    this.modalService.onHide.subscribe(result => {
      if (this.bookingService.proceedButton) {
        this.paymentPage = this.bookingService.proceedButton;
        this.gallopLocalStorage.setItem("expensifyDetails", JSON.stringify({ 'addToExpensify': (this.addToExpensifyControl.value) ? true : false, 'expensifyEmail': this.expensifyEmailControl.value }));
        this.gallopLocalStorage.setItem("expenseDetails", this.expenseEmailControl.value);
        GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
          'proceedToPayButtonClicked', 'WebSearchUI'
        );
      }
    });

    this.fetchQueryParamSubscription = this.activatedRoute.queryParams.subscribe(params => {
      if ('redirectedBackFromSignIn' in params && params['redirectedBackFromSignIn'] == 'true') return;
      this.emailId = params['emailId'];
      this.ticketId = params['ticketId'];
      this.threadId = params['threadId'];
      this.sToken = params['sToken'];
      this.urlParameters = params;

      if (params['traveller']) {
        this.travellersCount = parseInt(params['traveller']);
      }
      if (params['travellerData']) {
        this.travellerTypeData = JSON.parse(params["travellerData"]);
      }
      if (params['pageMode']) {
        this.pageMode = params['pageMode'];
      }
      setRequestParam(params['emailId'], params['ticketId'], params['threadId'],
        params['ticketNumber'], "", this.travellerTypeData, this.travellersCount, false);
      if (this.pageMode !== 'WebSearch') {
        this.isLoading = true;
      } else {
        this.isLoading = false;
      }
      this.initiateEmailQuoteOptions();
    });
    this.createForm();
    this.gallopLocalStorage.removeItem("bookingResponse");
    this.gallopLocalStorage.removeItem("hotelBookingResponse");
    this.gallopLocalStorage.removeItem("carBookingResponse");
    this.fetchEmailQuoteOptionsSubscription = this.emailBookingFlowService.searchResultOptionsObserver$.subscribe((quoteOptions: EmailSearchV4DTO) => {
      if (quoteOptions && quoteOptions != null) {
        this.isLoading = false;
      }
      this.handleEmailQuoteOptions(quoteOptions);
    });

    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfo: UserAccountInfo) => {
      if (!userAccountInfo || userAccountInfo == null) return;
      // this.isLoading = false;
      this.userAccountInfoObj = userAccountInfo;
      if (this.userAccountInfoObj && !this.paymentPage) {
        this.getAllTags();
      }
      if (this.userAccountInfoService.isLoggedIn() && this.userAccountInfoObj.userInfo.employeeInfo && this.userAccountInfoObj.userInfo.employeeInfo.agentRole === 'BOOK_FOR_SELF') {
        // this.emergencyContact.patchValue(this.getEmergencyFormData(this.userAccountInfoObj));
      } else if ((this.userAccountInfoService.isLoggedIn() && this.userAccountInfoObj.userInfo.employeeInfo) && this.searchService1.typeOfBooking === 'BOOK_FOR_SELF') {
        //this.emergencyContact.patchValue(this.getEmergencyFormData(this.userAccountInfoObj));
      } else if (this.userAccountInfoService.isLoggedIn() && !this.userAccountInfoObj.userInfo.employeeInfo) {
        // this.emergencyContact.patchValue(this.getEmergencyFormData(this.userAccountInfoObj));
      }
      // this.allowExpensepRoviders
      this.setExpensifyFormControlValidations();
      this.expenseProviders = CommonUtils.getActiveExpenseProviders(this.userAccountInfoObj);

      if (this.userAccountInfoService.isLoggedIn() && this.isBusinessTrip()) {
        this.userAccountInfoObj = this.userAccountInfoService.getAccountInfo();
        if (this.userAccountInfoObj && this.userAccountInfoObj.companySettings
          && this.userAccountInfoObj.companySettings.expensifySupported
          && this.userAccountInfoObj.userInfo && this.userAccountInfoObj.userInfo.expensifyId
          && this.userAccountInfoObj.userInfo.expensifyId.trim().length > 0) {
          this.expensifyEmail = this.userAccountInfoObj.userInfo.expensifyId;
          this.addToExpensify = true;
        }
      }
      setTimeout(() => {
        if (this.showExpensifyField()) {
          if (this.showPaymentButton || this.showPaymentLink) {
            $('#expensifyCard').removeClass('inactive');
          }
          enableExpensifyTextbox();
        }
      }, 100);
      setTimeout(() => {
        if (this.showPaymentButton || this.showPaymentLink) {
          $('#emergencyContactCard').removeClass('inactive');
        }
        //  enableExpensifyTextbox();
      }, 100);


    });
    //

    this.addToExpensifyControl = new UntypedFormControl(this.addToExpensify);
    this.expensifyEmailControl = new UntypedFormControl(this.expensifyEmail, Validators.compose([this.expensifyEmailValidator.bind(this), Validators.pattern(Constants.RGEX_EMAIL)]));
    if (this.expenseProviders && this.expenseProviders.length > 0) {
      this.expenseEmailControl = new UntypedFormControl(this.expenseProviders[0].id);
    } else {
      this.expenseEmailControl = new UntypedFormControl();
    }
    if (!this.addToExpensify) {
      this.expenseEmailControl.disable();
    } else {
      this.expenseEmailControl.enable();
    }

    this.emailBookingFlowService.policyFlagChangeListener.asObservable().subscribe(currenPolicyStatus => {
      if (currenPolicyStatus != null) {
        this.outsidePolicyFlag = !currenPolicyStatus;

      }
    });

    this.flightFareChangeSubscription = this.bookingService.flightFareChangeSubject.subscribe((newTransactionAmount) => {

      if (newTransactionAmount != null) {
        let newBillItemList = new Array<BillingItem>();
        for (let billingItemIndex = 0; billingItemIndex < this.billingItemArray.length; billingItemIndex++) {
          let billingItem = this.billingItemArray[billingItemIndex];
          if (billingItem.type !== 'other') {
            if (newTransactionAmount.discountedPrice) {
              billingItem.amount = newTransactionAmount.discountedPrice;
            } else {
              billingItem.amount = newTransactionAmount.price;
            }
          }
          newBillItemList.push(billingItem);
          if (billingItem.type !== 'other') {
            this.searchService1.priceChange = billingItem.amount;
            this.optionSelectionChild.setBillingItemArray(billingItem.amount)
          }
        }
        if (this.searchService1.priceChange) {
          this.getTotalPayble();
          this.billingItemList = this.billingItemArray;
        }
        // this.bilingItemListSubject.next(newBillItemList);
        this.billingItemArray = [];
        for (let item of newBillItemList) {
          this.billingItemArray.push(item);
        }
        // this.gallopLocalStorage.setItem('billingItemList',JSON.stringify(this.getBillingBreakup()));
        if (this.paymentDetailChild) {
          this.paymentDetailChild.setBillingItemArray(this.billingItemArray);
        }
      }

    });
  }
  isALlowed(item) {
    return (this.allowExpensepRoviders.indexOf(item.id) !== -1)
  }
  getPlaceholder() {
    return CommonUtils.getExpensefieldPlaceholderText(this.expenseEmailControl.value);
  }
  billingItemArray: Array<BillingItem>;

  private setExpensifyFormControlValidations() {
    if (this.userAccountInfoObj && this.userAccountInfoObj.companySettings
      && this.userAccountInfoObj.companySettings.expensifySupported
      && this.userAccountInfoObj.userInfo
      && this.userAccountInfoObj.userInfo.expensifyId
      && this.userAccountInfoObj.userInfo.expensifyId.trim().length > 0
      && this.expensifyEmailControl
    ) {
      this.expensifyEmail = this.userAccountInfoObj.userInfo.expensifyId;
      this.addToExpensify = true;
      this.expensifyEmailControl.setValue(this.expensifyEmail);
      if (this.addToExpensify) {
        this.expensifyEmailControl.markAsDirty();
        updateAddToExpensifyUICheckbox(true);
      } else {
        updateAddToExpensifyUICheckbox(false);
      }
    }
  }
  selectedCountryCode = '';

  minLengthArray(min: number) {
    return (c: AbstractControl): { [key: string]: any } => {
      if (c.value) {
        let stringVal = c.value.replace(/\s/g, "")
        if (stringVal.length > 0)
          return null;

        return { 'minLengthArray': { valid: false } };
      }
    }
  }
  handleResponseOfemergencyConntact(event) {
    if (Object.keys((event)).length > 0) {
      let emergencyFormData = {
        "name": event ? event.name : "",
        "relationship": event ? event.relationship : '',
        "contactNumber": event && event.contactNumber ? event.contactNumber.number : '',
        "dialCode1": event && event.contactNumber &&
          event.contactNumber.countryCode ? event.contactNumber.countryCode : "+1",
      }
      this.emergencyContact.patchValue(emergencyFormData);
    } else {
      this.emergencyContact.patchValue({});
    }
  }

  private initiateEmailQuoteOptions() {
    if (this.emailId && this.emailId.length > 0 && this.ticketId && this.ticketId.length > 0 && this.sToken && this.sToken.length > 0) {
      // this.fetchEmailQuoteOptions(this.ticketId, this.threadId, this.sToken);
      this.emailBookingFlowService.fetchEmailQuoteOptions(this.ticketId, this.threadId, this.sToken);
      this.userAccountInfoService.fetchUserPreferences();
      if (this.pageMode === 'emailflowAgent') {
        this.emailBookingFlowService.fetchSavedBookingDetails(this.emailId, this.sToken, this.ticketId, this.urlParameters['tripId'], this.urlParameters['eventId']);
      }
      // this.fetchUserAccountInfo(this.emailId,this.sToken);
    } else //if(this.pageMode && this.pageMode === 'WebSearch')
    {
      // this.fetchUserAccountInfo(this.emailId,this.sToken);
      this.isLoading = false;
      this.emailBookingFlowService.requestSearchResultEventOptions();
    }
    // else {
    //   this.toastr.error('parameters not found', 'Error');
    // }
  }

  private handleEmailQuoteOptions(quoteOptions: EmailSearchV4DTO) {
    if (quoteOptions) {
      this.emailQuotOptions = quoteOptions;
      let keyForCars = {};
      if (quoteOptions.approvalRequiredFor) {
        this.userAccountInfoService.approvalRequiredFor = quoteOptions.approvalRequiredFor;
      }
      if (this.emailQuotOptions.eventIdAndTypeMapDestination) {
        if (this.emailQuotOptions.carOptionsList && this.emailQuotOptions.carOptionsList.length > 0) {
          for (let counter = 0; counter < this.emailQuotOptions.carOptionsList.length; counter++) {
            const currentCarOptionEventId = this.emailQuotOptions.carOptionsList[counter][0].eventId;
            this.emailQuotOptions.carOptionsList[counter][0].airportCity =
              this.emailQuotOptions.eventIdAndTypeMapDestination[currentCarOptionEventId];
          }
        }
      }
      if (this.pageMode === 'emailflow' && this.emailQuotOptions.isUserSelected) {
        this.router.navigate(['/errors'], { queryParams: { errorCode: 'alreadyConfirmed' } });
        return;
      }
      if (this.emailQuotOptions.flightSearchOptions) {
        this.travellersCount = this.emailQuotOptions.flightSearchOptions.adultCount +
          this.emailQuotOptions.flightSearchOptions.infantCount +
          this.emailQuotOptions.flightSearchOptions.childCount;
      }
      this.setExpensifyFormControlValidations();
    }
  }


  expensifyEmailValidator(control: AbstractControl): ValidationErrors | null {
    let result = null;
    if (this.addToExpensify && (!control.value || control.value.trim().length === 0)) {
      return { 'required': true };
    }
    return result;
  }
  
  isPersonalInformationChange() {
    let resultArray = [];
    let equal3;
    if (this.userAccountInfoService.userFormOpen === -1) {
      return;
    }
   // let travellerChangeForMultiple = JSON.parse(JSON.stringify(this.personalDetailChild.passengersForm.controls['passengers'].value[this.userAccountInfoService.userFormOpen]));
    let travellerChange = JSON.parse(JSON.stringify(this.personalDetailChild.passengersForm.controls['passengers'].value[this.userAccountInfoService.userFormOpen]));
    let userInfoDTO = this.userAccountInfoService.getAccountInfo().userInfo;
    if (travellerChange['employeeType'] === 0) {

      let userAccountInfoObj: any = this.userAccountInfoService.getAccountInfo();
      let travellerData = this.personalDetailChild.getPrimaryTravellerInfo(userAccountInfoObj);
      let travellerData1 = JSON.parse(JSON.stringify(travellerData['passengers'][0]));
      if ((travellerChange.frequentFlyerInfo && travellerChange.frequentFlyerInfo.length > 0)) {
        for (let i = 0; i < travellerChange.frequentFlyerInfo.length; i++) {
          if (travellerData1.frequentFlyerInfo && travellerData1.frequentFlyerInfo.length > 0) {
            for (let j = 0; j < travellerData1.frequentFlyerInfo.length; j++) {
              if (travellerChange.frequentFlyerInfo[i].airlineCode === travellerData1.frequentFlyerInfo[j].airlineCode && !travellerChange.frequentFlyerInfo[i].ffnAvailable) {
                travellerChange.frequentFlyerInfo[i].frequent_flyer_number = null;
                delete travellerChange.frequentFlyerInfo[i].ffnAvailable;
                delete travellerData1.frequentFlyerInfo[j].ffnAvailable;
              }
            }
          }
        }

      }
      if ((travellerChange.carLoyalityInfo && travellerChange.carLoyalityInfo.length > 0)) {
        for (let i = 0; i < travellerChange.carLoyalityInfo.length; i++) {
          if (travellerData1.carLoyalityInfo && travellerData1.carLoyalityInfo.length > 0) {
            for (let j = 0; j < travellerData1.carLoyalityInfo.length; j++) {
              if (travellerChange.carLoyalityInfo[i].rentalCarCode === travellerData1.carLoyalityInfo[j].rentalCarCode && !travellerChange.carLoyalityInfo[i].clnAvailable) {
                travellerChange.carLoyalityInfo[i].rentalCarLoyaltyNumber = null;
                delete travellerChange.carLoyalityInfo[i].clnAvailable;
                delete travellerData1.carLoyalityInfo[j].clnAvailable;
              }
            }
          }
        }

      }
      if ((travellerChange.hotelLoyalityInfo && travellerChange.hotelLoyalityInfo.length > 0)) {
        for (let i = 0; i < travellerChange.hotelLoyalityInfo.length; i++) {
          if (travellerData1.hotelLoyalityInfo && travellerData1.hotelLoyalityInfo.length > 0) {
            for (let j = 0; j < travellerData1.hotelLoyalityInfo.length; j++) {
              if (travellerChange.hotelLoyalityInfo[i].loyaltyPointsSupported && travellerChange.hotelLoyalityInfo[i].hotelCode === travellerData1.hotelLoyalityInfo[j].hotelCode && !travellerChange.hotelLoyalityInfo[i].hlnAvailable) {
                travellerChange.hotelLoyalityInfo[i].hotel_loyality_number = null;
                delete travellerChange.hotelLoyalityInfo[i].hlnAvailable;
                delete travellerData1.hotelLoyalityInfo[j].hlnAvailable;
              }
            }
          }
        }

      }
      travellerData1['employeeType'] = 0;
      if (!travellerChange['ktnAvailable']) {
        travellerChange['knownTravellerNumber'] = "";
      }
      if(travellerChange['roomId']){
        delete travellerChange['roomId'];
        delete travellerData1['roomId'];
     }   
      delete travellerData1['ktnAvailable'];
      delete travellerChange['ktnAvailable'];
      if(travellerChange['hotelLoyalityInfo'] && travellerChange['hotelLoyalityInfo'].length > 0 && !travellerChange['hotelLoyalityInfo'][0].loyaltyPointsSupported){
        delete travellerData1['hotelLoyalityInfo'];
        delete travellerChange['hotelLoyalityInfo'];
        }
        if(!travellerChange['hotelLoyalityInfo']  && travellerData1['hotelLoyalityInfo'] && travellerData1['hotelLoyalityInfo'].length === 0){
          delete travellerData1['hotelLoyalityInfo'];
          delete travellerChange['hotelLoyalityInfo'];
        }
      delete travellerData1['zipCode'];
      delete travellerChange['zipCode'];
      if (!travellerData1['gender'] && travellerChange['gender'] === '') {
        travellerChange['gender'] = null;
      }
     
      if (!travellerData1['emergencyContactName'] && travellerChange['emergencyContactName'] === '') {
        travellerChange['emergencyContactName'] = null;
      }
      if (!travellerData1['relationship'] && travellerChange['relationship'] === '') {
        travellerChange['relationship'] = null;
      }
      if (travellerData1['title'] === '' && !travellerChange['title']) {
        travellerChange['title'] = '';
      }
      if (!travellerData1['contactNumber'] && travellerChange['contactNumber'] === '') {
        travellerChange['contactNumber'] = null;
      }
      equal3 = this.bookingService.equals(travellerChange, travellerData1);
      

      if (!equal3) {
        this.personalDetailChild.disabledUserEntry((this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0));
        this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
          initialState: {
            title: this.translateService.instant('emailBookingFlow.Youseemtohaveaddedorupdatedsomeinformation.Wouldyouliketosaveitto')+" "+ userInfoDTO.email + " " + this.translateService.instant('emailBookingFlow.profile?'),
            message: '',
            yesButtonSubText: this.translateService.instant('emailBookingFlow.YES'),
            noButtonSubText: this.translateService.instant('emailBookingFlow.NO'),
            userInfoDTO: userInfoDTO,
            passengerDetails: travellerChange,
            color: 'black',
            buttonColor: '#008080',
            profile: true,
            others:false,
          }, backdrop: true, keyboard: false, ignoreBackdropClick: true
        });
      }
    }else if((this.userAccountInfoService.isUserCorporateAdmin() || this.userAccountInfoService.isUserIsTravelManager()) && (travellerChange['employeeType'] !== 0 && this.searchService1.employeeEmail[this.userAccountInfoService.userFormOpen].email !== "GUEST")){
      let travellerChange = JSON.parse(JSON.stringify(this.personalDetailChild.passengersForm.controls['passengers'].value[this.userAccountInfoService.userFormOpen]));
      let employeeInfoDTO = this.searchService1.employeeList.filter(item =>item.email ===travellerChange['email']);
      if(employeeInfoDTO && employeeInfoDTO[0]){
      let travellerData = this.personalDetailChild.getTravellerInfo(employeeInfoDTO[0]);
      let travellerData1 = JSON.parse(JSON.stringify(travellerData));
      if ((travellerChange.frequentFlyerInfo && travellerChange.frequentFlyerInfo.length > 0)) {
        for (let i = 0; i < travellerChange.frequentFlyerInfo.length; i++) {
          if (travellerData1.frequentFlyerInfo && travellerData1.frequentFlyerInfo.length > 0) {
            for (let j = 0; j < travellerData1.frequentFlyerInfo.length; j++) {
              if (travellerChange.frequentFlyerInfo[i].airlineCode === travellerData1.frequentFlyerInfo[j].airlineCode && !travellerChange.frequentFlyerInfo[i].ffnAvailable) {
                travellerChange.frequentFlyerInfo[i].frequent_flyer_number = null;
                delete travellerChange.frequentFlyerInfo[i].ffnAvailable;
                delete travellerData1.frequentFlyerInfo[j].ffnAvailable;
              }
            }
          }
        }

      }
      if ((travellerChange.carLoyalityInfo && travellerChange.carLoyalityInfo.length > 0)) {
        for (let i = 0; i < travellerChange.carLoyalityInfo.length; i++) {
          if (travellerData1.carLoyalityInfo && travellerData1.carLoyalityInfo.length > 0) {
            for (let j = 0; j < travellerData1.carLoyalityInfo.length; j++) {
              if (travellerChange.carLoyalityInfo[i].rentalCarCode === travellerData1.carLoyalityInfo[j].rentalCarCode && !travellerChange.carLoyalityInfo[i].clnAvailable) {
                travellerChange.carLoyalityInfo[i].rentalCarLoyaltyNumber = null;
                delete travellerChange.carLoyalityInfo[i].clnAvailable;
                delete travellerData1.carLoyalityInfo[j].clnAvailable;
              }
            }
          }
        }

      }
      if ((travellerChange.hotelLoyalityInfo && travellerChange.hotelLoyalityInfo.length > 0)) {
        for (let i = 0; i < travellerChange.hotelLoyalityInfo.length; i++) {
          if (travellerData1.hotelLoyalityInfo && travellerData1.hotelLoyalityInfo.length > 0) {
            for (let j = 0; j < travellerData1.hotelLoyalityInfo.length; j++) {
              if (travellerChange.hotelLoyalityInfo[i].loyaltyPointsSupported && travellerChange.hotelLoyalityInfo[i].hotelCode === travellerData1.hotelLoyalityInfo[j].hotelCode && !travellerChange.hotelLoyalityInfo[i].hlnAvailable) {
                travellerChange.hotelLoyalityInfo[i].hotel_loyality_number = null;
                delete travellerChange.hotelLoyalityInfo[i].hlnAvailable;
                delete travellerData1.hotelLoyalityInfo[j].hlnAvailable;
              }
            }
          }
        }

      }
    //  travellerData1['employeeType'] =employeeInfoDTO[0].employeeInfo.employeeId;
      if (!travellerChange['ktnAvailable']) {
        travellerChange['knownTravellerNumber'] = "";
      }
      delete travellerData1['ktnAvailable'];
      delete travellerData1['employeeType'];
      delete travellerChange['employeeType'];
      delete travellerChange['ktnAvailable'];
      if(travellerChange['roomId']){
         delete travellerChange['roomId'];
         delete travellerData1['roomId'];
      }   
      if(travellerChange['hotelLoyalityInfo'] && travellerChange['hotelLoyalityInfo'].length > 0 && !travellerChange['hotelLoyalityInfo'][0].loyaltyPointsSupported){
      delete travellerData1['hotelLoyalityInfo'];
      delete travellerChange['hotelLoyalityInfo'];
      }
      if(!travellerChange['hotelLoyalityInfo']  && travellerData1['hotelLoyalityInfo'] && travellerData1['hotelLoyalityInfo'].length === 0){
        delete travellerData1['hotelLoyalityInfo'];
        delete travellerChange['hotelLoyalityInfo'];
      }
      delete travellerData1['zipCode'];
      delete travellerChange['zipCode'];
      if (!travellerData1['gender'] && travellerChange['gender'] === '') {
        travellerChange['gender'] = null;
      }
      if (travellerData1['title'] === '' && !travellerChange['title']) {
        travellerChange['title'] = '';
      }
      if (!travellerData1['contactNumber'] && travellerChange['contactNumber'] === '') {
        travellerChange['contactNumber'] = null;
      }
      if (!travellerData1['emergencyContactName'] && travellerChange['emergencyContactName'] === '') {
        travellerChange['emergencyContactName'] = null;
      }
      if (!travellerData1['relationship'] && travellerChange['relationship'] === '') {
        travellerChange['relationship'] = null;
      }
      equal3 = this.bookingService.equals(travellerChange, travellerData1);
      
      if (!equal3) {
     //   this.personalDetailChild.disabledUserEntry((this.personalDetailChild.passengersForm.get('passengers') as FormArray).at(0));
        this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
          initialState: {
            title: this.translateService.instant('emailBookingFlow.Youseemtohaveaddedorupdatedsomeinformation.Wouldyouliketosaveitto')+" "+ employeeInfoDTO[0].email + " " + this.translateService.instant('emailBookingFlow.profile?'),
            message: '',
            yesButtonSubText: this.translateService.instant('emailBookingFlow.YES'),
            noButtonSubText: this.translateService.instant('emailBookingFlow.NO'),
            userInfoDTO: employeeInfoDTO[0],
            passengerDetails: travellerChange,
            color: 'black',
            buttonColor: '#008080',
            profile: true,
            others:true,
          }, backdrop: true, keyboard: false, ignoreBackdropClick: true
        });
      }
    }
    }


  }
  handleBackFromPaymentPage2(event){
    if(event){
      this.optionSelectionChild.scrollToFirstInvalidControl1('ErroHere1');
    }
  }
  processNext(): void {
    this.personalDetailChild.saveProfileButton =false;
    this.personalDetailChild.validatePassangersForm();
    if (!this.personalDetailChild.passengersForm.valid) return;
    this.showPaymentButton = this.personalDetailChild.processNext();
    this.showPaymentLink = this.showPaymentButton;
    if (this.travellersCount > 1 && !this.showPaymentButton) {
      this.personalDetailChild.scrollToFirstFormconntrol();
      // this.personalDetailChild.accordionOpen[this.personalDetailChild.numOfFormPassengers-2]=!this.personalDetailChild.accordionOpen[this.personalDetailChild.numOfFormPassengers-2];
      // this.personalDetailChild.accordionOpen[this.personalDetailChild.numOfFormPassengers-1]=!this.personalDetailChild.accordionOpen[this.personalDetailChild.numOfFormPassengers-1];
    }
    this.personalDetailChild.nextButtonClicked();
    if (this.personalDetailChild.numOfFormPassengers === 2) {
      this.personalDetailChild.enabledUserEntry((this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0));
      this.isPersonalInformationChange();

      let passenger: any = (this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0);
      for (let key in passenger.controls) {
        let value = passenger.controls[key];
        if ((key === 'employeeType') && value.value === 0 && this.userAccountInfoService.promptUserTosaveProfile) {
          this.personalDetailChild.disabledUserEntry((this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0));
        }
        break;
      }
    }
    if (this.showPaymentButton || this.showPaymentLink) {
      $('#expensifyCard').removeClass('inactive');
      this.personalDetailChild.updateFormValueInBookingService();
    }
    setTimeout(() => {
      if (this.showPaymentButton || this.showPaymentLink) {
        $('#emergencyContactCard').removeClass('inactive');
      }
      //  enableExpensifyTextbox();
    }, 100);
  }

  isPersonalDetailFormValid(): boolean {
    if (this.personalDetailChild)
      return this.personalDetailChild.passengersForm.valid;
    else
      return false;
  }
  selectSeat() {
    if (!this.searchService1.paymentPage) {
      this.bookingService.proceedButton = false;
    }
    this.personalDetailChild.validatePassangersForm();
    this.expensifyEmailControl.markAsTouched({ onlySelf: true });
    if (this.optionSelectionChild.isSelectedRevalidating) {
      this.toastr.error(this.translateService.instant('emailBookingFlow.Pleasewaitwhilefarevalidationinprogress').toString(), 'Error');
      return;
    }
    if (!this.isPersonalDetailFormValid()) {
      this.toastr.error(this.translateService.instant('emailBookingFlow.Pleasefillallmandatoryfields').toString());
    }
    this.seatSelectionPage = this.isPersonalDetailFormValid() && this.expensifyEmailControl.valid;
    if (this.seatSelectionPage) {

      this.personalDetailChild.enabledUserEntry1((this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0));
      let travellerChange = JSON.parse(JSON.stringify(this.personalDetailChild.passengersForm.controls['passengers'].value[0]));
      let userInfoDTO: any = {};
      userInfoDTO.name = travellerChange.emergencyContactName ? travellerChange.emergencyContactName : '';
      userInfoDTO.relationship = travellerChange.relationship ? travellerChange.relationship : '';
      userInfoDTO.dialcode1 = travellerChange.dialCode1 ? travellerChange.dialCode1 : "+1";
      userInfoDTO.contactNumber = travellerChange.contactNumber ? travellerChange.contactNumber : "";
      this.gallopLocalStorage.setItem("emergencyContactDetails", JSON.stringify(userInfoDTO));
      if (this.showPaymentButton) {
        this.isPersonalInformationChange();
      }
      // this.searchService1.backFromPayment = this.paymentPage;
      this.gallopLocalStorage.setItem("expensifyDetails", JSON.stringify({ 'addToExpensify': (this.addToExpensifyControl.value) ? true : false, 'expensifyEmail': this.expensifyEmailControl.value }));
      this.gallopLocalStorage.setItem("expenseDetails", this.expenseEmailControl.value);

      window.scrollTo(0, 0);
    //  history.pushState(null, null, window.location.href);
      this.personalDetailChild.updateFormValueInBookingService();
      this.bsModelLoginRef = this.modalService.show(SeatComponent, {
        initialState: {
        }, backdrop: true, keyboard: false, ignoreBackdropClick: true
      });
      seatModalHack();
      //document.documentElement.scrollTop = 0;
    }
    //this.personalDetailChild.updateFormValueInBookingService();
  }
  changeStyle(item1) {
    if (item1 === 'DISABLED') {
      return { 'border': '1px solid #E7E6E4' };
    } else
      return { 'border': '1px solid var(--dark-bg-color)' };

  }
  handleBackFromSeat(event) {
    this.paymentPage = event;
    this.seatSelectionPage = false;
    if (this.paymentPage) {
      window.scrollTo(0, 0);
      //document.documentElement.scrollTop = 0;
    }
    GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
      'proceedToPayButtonClicked', 'WebSearchUI'
    );
  }
  getStyleforPaymentPage(){
    if(this.paymentPage && !this.isMobile){
return {'padding-bottom':'0px','margin-bottom':'-24px'}
    }else if(this.paymentPage && this.isMobile){
      return {'padding-bottom':'0px','margin-bottom':'-8px'}
          }else{
      return {'padding-bottom':'62px','margin-bottom':'0px'}
    }
  }
  getSeatArray(){
    //let seatArray = JSON.parse(this.gallopLocalStorage.getItem("selectedSeat"));
    let arrayOfSeat =[];
    let seatArray = JSON.parse(this.gallopLocalStorage.getItem("selectedSeat"));
    
    if(seatArray){
      arrayOfSeat=[...seatArray];
      let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
      for(let i=0;i<arrayOfSeat.length;i++){
      
        if(selectedFlight[i].legs[0].flightHops[0].from===arrayOfSeat[i][0].origin && selectedFlight[i].legs[0].flightHops[selectedFlight[i].legs[0].flightHops.length-1].to===arrayOfSeat[i][arrayOfSeat[i].length-1].destination){
          this.searchService1.seatForMultipleBooking[i]=[];
          this.searchService1.seatForMultipleBooking[i] =[...arrayOfSeat[i]];
         // this.searchService1.selectedSeatArrayForPaymentPage[i].push(arrayOfSeat[i]);
        }
        
      }
      
    }
    
      
    

    
  }
  showCreditError=false;
  crediterName='';

  showCreditNameDoesNotMatchError() {
    const selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
    if(selectedFlight && selectedFlight.length > 0){
      const creditFlightdata = this.personalDetailChild.isCreditNameAndFormNameSame();
      if(creditFlightdata && !creditFlightdata.value){
        this.crediterName = creditFlightdata.name;
        this.bookingService.travelerIndex = creditFlightdata.index;
        return true;
      }
    }
    return false;
  }
  proceedToPay(): void {
   
    this.searchService1.seatSelectArray1 = [];
    this.searchService1.selectedSeatArrayForPaymentPage = [];
    this.personalDetailChild.saveProfileButton =false;
    this.personalDetailChild.validatePassangersForm();

    this.expensifyEmailControl.markAsTouched({ onlySelf: true });
    const selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
    if(selectedFlight && selectedFlight.length > 0){
      let adultTravelerExist = this.personalDetailChild.isTravellerListHasAnAdultTraveler();
      if(!adultTravelerExist){
        return;
      }
      if (this.showCreditNameDoesNotMatchError()) {
        //user wants to continue without credits
        delete selectedFlight[0].travelCreditsInfo;
        for(let item of this.billingItemArray){
          if(item.type==='flight'){
            if(item.credit){
              delete item.credit;
            }
          }
        }
        for(let item of this.billingItemList){
          if(item.type==='flight'){
            if(item.credit){
              delete item.credit;
            }
          }
        }
       if(selectedFlight[0].legs[0].flightHighlights.outOfPolicyContext.withoutCreditReason){
        selectedFlight[0].legs[0].flightHighlights.withinPolicy =false;
        this.emailBookingFlowService.policyFlagChangeListener.next(false);
        this.optionSelectionChild.policyChangeAfterCreditsRemoved();
       }
       this.gallopLocalStorage.setItem('selectedFlight', JSON.stringify(selectedFlight));
       this.optionSelectionChild.removeCredits();
      }
    }
    this.bookingService.priceChange = false;

    
    if (this.optionSelectionChild.isSelectedRevalidating) {
      this.toastr.error(this.translateService.instant('emailBookingFlow.Pleasewaitwhilefarevalidationinprogress').toString(), 'Error');
      return;
    }
    if(this.travellersCount===1){
      this.personalDetailChild.enabledUserEntry1((this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0));
      let travellerChange = this.personalDetailChild.passengersForm.controls['passengers'].value[0];
      let flightIndex=0;
      let carIndex=0;
      let hotelIndex =0;
      let index=0;
      if(this.optionSelectionChild.tripDetails.length >0){
        for(let item of this.optionSelectionChild.tripDetails){
          if(item.type==='flight'){
            if(Object.keys((item.paymenttype)).length ==0 && !item.paymentError){
              this.optionSelectionChild.tripDetails[index].error =true;
              setTimeout(() => {
                this.optionSelectionChild.scrollToFirstInvalidControl2(index);  
              }, 100);
              return;
            }
           // for (let j = 0; j < travellerChange.frequentFlyerInfo.length; j++) {
              
              if(item.ffnno[0]){
                if(item.ffnno[0].frequent_flyer_number!==null && item.ffnno[0].frequent_flyer_number.length===0){
                  item.ffnno[0].ffnAvailable =false;
                }
            travellerChange['frequentFlyerInfo'][flightIndex] = item.ffnno[0];
            flightIndex++;
              }
            }
         // }
          if(item.type==='hotel'){
            if(Object.keys((item.paymenttype)).length ==0){
              this.optionSelectionChild.tripDetails[index].error =true;
              setTimeout(() => {
                this.optionSelectionChild.scrollToFirstInvalidControl2(index);  
              }, 100);
              return;
             
            }
           // for (let j = 0; j < travellerChange.hotelLoyalityInfo.length; j++) {
              if(item.hlnno[0] && item.hlnno[0].loyaltyPointsSupported){
                if(item.hlnno[0].hotel_loyality_number !==null && item.hlnno[0].hotel_loyality_number.length===0){
                  item.hlnno[0].hlnAvailable =false;
                }
            travellerChange['hotelLoyalityInfo'][hotelIndex]= item.hlnno[0];
            hotelIndex++;
              }
           // }
          }
          if(item.type==='car'){
            if(item.prepay){
            if(Object.keys((item.paymenttype)).length ==0){
              this.optionSelectionChild.tripDetails[index].error =true;
              setTimeout(() => {
                this.optionSelectionChild.scrollToFirstInvalidControl2(index);  
              }, 100);
              return;
            }
          }
           
           // for (let j = 0; j < travellerChange.carLoyalityInfo.length; j++) {
              if(item.clnno[0]){
                if(item.clnno[0].rentalCarLoyaltyNumber  !==null&& item.clnno[0].rentalCarLoyaltyNumber.length===0){
                  item.clnno[0].clnAvailable =false;
                }
              travellerChange['carLoyalityInfo'][carIndex] = item.clnno[0];
              carIndex++;
              }

           // }
          }
          index=index+1;
        }
          if(!this.optionSelectionChild.allLoyaltyNumberMandatoryFieldsAreValidOrNOt()){
            this.toastr.error("please enter loyality number.");
            setTimeout(() => {
              this.optionSelectionChild.scrollToFirstInvalidControl1('loyalityError');
            }, 100);
            
            this.personalDetailChild.disabledUserEntry((this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0));
            return;
          }
       
        (this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0).setValue(travellerChange);
      }
      if(travellerChange['employeeType']===0){
      this.personalDetailChild.disabledUserEntry((this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0));
      }
    }else{
      let index=0;
      if(this.optionSelectionChild.tripDetails.length >0){
        for(let item of this.optionSelectionChild.tripDetails){
          if(item.type==='flight'){
            if(Object.keys((item.paymenttype)).length ==0 && !item.paymentError){
              this.optionSelectionChild.tripDetails[index].error =true;
              setTimeout(() => {
                this.optionSelectionChild.scrollToFirstInvalidControl2(index);  
              }, 100);
              return;
            }
           
          }
          if(item.type==='hotel'){
            if(Object.keys((item.paymenttype)).length ==0){
              this.optionSelectionChild.tripDetails[index].error =true;
              setTimeout(() => {
                this.optionSelectionChild.scrollToFirstInvalidControl2(index);  
              }, 100);
              return;
             
            }
            
          }
          if(item.type==='car'){
            if(Object.keys((item.paymenttype)).length ==0){
              this.optionSelectionChild.tripDetails[index].error =true;
              setTimeout(() => {
                this.optionSelectionChild.scrollToFirstInvalidControl2(index);  
              }, 100);
              return;
            }
           
            
          }
          index=index+1;
        }
       // (this.personalDetailChild.passengersForm.get('passengers') as FormArray).at(0).setValue(travellerChange);
      }
    }
    
    if (!this.isPersonalDetailFormValid()) {
      let params = "?ua_action=ProceedToPay&ua_page=" + this.searchService1.type1 + "&ua_mandatoryCheck=false";
      this.searchService1.letsTrack(params);
      this.toastr.error(this.translateService.instant('emailBookingFlow.Pleasefillallmandatoryfields'));
    }
  
    this.bookingService.priceChange = false;
    this.paymentPage = this.isPersonalDetailFormValid() && this.expensifyEmailControl.valid;
    if (this.paymentPage) {
      if(this.travellersCount===1 && !this.searchService1.tripFeatureEnabled){
      this.showSeatButton();
      }
      this.searchService1.paymentPage = this.paymentPage;
      if(this.optionSelectionChild.tripDetails && this.optionSelectionChild.tripDetails.length===1 && this.searchService1.multiTripBooking){
        this.searchService1.multiTripBooking =false;
      }
      //this.titleService.setTitle('Payment Details');
      this.titleService.setTitle(this.translateService.instant('search.PaymentDetails'));
      // this.searchService1.backFromPayment = this.paymentPage;
      if(this.travellersCount===1){
       // this.getSeatArray();

      }
      history.pushState(null, null, window.location.href);
      for (let i = 0; i < (<FormArray>this.personalDetailChild.passengersForm.controls['passengers']).length; i++) {
        let passenger: any = (this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(i);
      for (let key in passenger.controls) {
        let value = passenger.controls[key];
        if ((key === 'employeeType') && (value.value === 0 || value.value > 1)){
        this.personalDetailChild.enabledUserEntry1((this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(i))
        }
      }
      }
      //this.personalDetailChild.enabledUserEntry1((this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0));
      let travellerChange = JSON.parse(JSON.stringify(this.personalDetailChild.passengersForm.controls['passengers'].value[0]));
      let userInfoDTO: any = {};
      userInfoDTO.name = travellerChange.emergencyContactName ? travellerChange.emergencyContactName : '';
      userInfoDTO.relationship = travellerChange.relationship ? travellerChange.relationship : '';
      userInfoDTO.dialcode1 = travellerChange.dialCode1 ? travellerChange.dialCode1 : "+1";
      userInfoDTO.contactNumber = travellerChange.contactNumber ? travellerChange.contactNumber : "";
      this.gallopLocalStorage.setItem("emergencyContactDetails", JSON.stringify(userInfoDTO));
      if (this.showPaymentButton) {
        this.isPersonalInformationChange();
      }
      let params = "?ua_action=ProceedToPay&ua_page=" + this.searchService1.type1 + "&ua_mandatoryCheck=true";
      this.searchService1.letsTrack(params);
      window.scrollTo(0, 0);
      //document.documentElement.scrollTop = 0;

      this.gallopLocalStorage.setItem("expensifyDetails", JSON.stringify({ 'addToExpensify': (this.addToExpensifyControl.value) ? true : false, 'expensifyEmail': this.expensifyEmailControl.value }));
      // this.gallopLocalStorage.setItem("emergencyContactDetails", JSON.stringify(this.emergencyContact.value));
      this.gallopLocalStorage.setItem("expenseDetails", this.expenseEmailControl.value);

      this.personalDetailChild.updateFormValueInBookingService();
    }
    GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
      'proceedToPayButtonClicked', 'WebSearchUI'
    );
    // this.router.navigate(['paymentDetails'],{ queryParams:{emailId:this.emailId,sToken:this.sToken,ticketId:this.ticketId,tripId:this.emailQuotOptions.tripId}});

  }

  public handleBackFromPaymentPage(data: string) {
    if (data === 'notifyAgent') {
      this.optionSelectionChild.notifyAgentConfirmation();
    } else {
      this.paymentPage = false;
      //this.titleService.setTitle('Traveler Details');
      this.titleService.setTitle(this.translateService.instant('search.TravellerDetails'));
      let passenger: any = (this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0);
      this.personalDetailChild.disableEmailForForm();
      for (let key in passenger.controls) {
        let value = passenger.controls[key];
        if ((key === 'employeeType') && value.value === 0 && this.userAccountInfoService.promptUserTosaveProfile) {
          this.personalDetailChild.disabledUserEntry((this.personalDetailChild.passengersForm.get('passengers') as UntypedFormArray).at(0));
        }
        break;
      }
      window.scrollTo(0, 0);
      if (data === 'backToSelectDifferentOption') {
        this.optionSelectionChild.handleSelectDifferentOption();
      } else if (data === 'backToSearch') {
        //history.go(-2);
        setTimeout(() => {
          this.router.navigate(['search'],
            {
              queryParams:
                {},
              replaceUrl: true
            });
        }, 200);
      }
    }
  }
  public handleBackFromSeatPage(data) {
    if (data) {
      this.seatSelectionPage = false;
    }
  }
  handlePaymentchange(event){
    if(event){
      this.paymentDetailChild.processUserAccountInfo();
    }
  }
  public isSelectionComplete(): boolean {
    return this.bookingService.nextButtonClicked;
  }
  bsModalRefForUpcomingTrip: BsModalRef;
  public handleToOpenPaymentModal(event){
    if(event && event.id==='payment'){
    this.bsModalRefForUpcomingTrip = this.modalService.show(PaymentBreakupComponent, {
      initialState: {
        billingItemArray: this.billingItemArray,
        noOfPassengers:this.travellersCount,

      },
    });
    }else{
      this.paymentDetailChild.openPaymentdetailsModal(event);
    }
  }
  public getBillingBreakup(): Array<BillingItem> {
    let flightBillingItem: BillingItem = null;
    let hotelBillingItem: BillingItem = null;
    let carBillingItem: BillingItem = null;
    let postPaidCarBillingItem: BillingItem = null;
    let postPaidHotelBillingItem: BillingItem = null;
    let billingBreakup: Array<BillingItem> = new Array;
    let serviceFeeLineItem: BillingItem = null;
    if (this.billingItemList && this.billingItemList.length === 1 && this.billingItemList[0].number === 1 && this.billingItemList[0].type === 'hotel') {
      this.billingItemArray = this.billingItemList;
      return this.billingItemArray;
    } else if (this.billingItemList && this.billingItemList.length === 1 && this.billingItemList[0].number === 1 && this.billingItemList[0].type === 'cars') {
      this.billingItemArray = this.billingItemList;
      return this.billingItemArray;
    } else {
      for (let item of this.billingItemList) {
        if (item.type === 'flight') {
          if (flightBillingItem && !this.searchService1.multiTripBooking) {
            flightBillingItem.amount = '' + (Number.parseFloat(flightBillingItem.amount) + Number.parseFloat(item.amount));
          } else {
            if(this.searchService1.trainFilter.length > 0){
              flightBillingItem = new BillingItem(PaymentTypes.PREPAID, 'flight', this.translateService.instant('result.Trainfare').toString(), item.amount, item.currency);
            }else{
            flightBillingItem = new BillingItem(PaymentTypes.PREPAID, 'flight', this.translateService.instant('emailBookingFlow.AirFare').toString(), item.amount, item.currency);
            }
          }
          if(item.credit){
            flightBillingItem.credit =item.credit;
          }
          if(this.searchService1.multiTripBooking){
            if (flightBillingItem) {
              billingBreakup.push(flightBillingItem);
            }
          }
        } else if (item.type === 'hotel') {
          // Temporary change. Do not break up hotel amount in pre pay and post pay
          item.paymentType = PaymentTypes.PREPAID;
          if (item.paymentType === PaymentTypes.PREPAID) {
            if (hotelBillingItem && !this.searchService1.multiTripBooking) {
              hotelBillingItem.amount = '' + (Number.parseFloat(hotelBillingItem.amount) + Number.parseFloat(item.amount));
              hotelBillingItem.number = this.billingItemList[0].number;
            } else {
              hotelBillingItem = new BillingItem(PaymentTypes.PREPAID, 'hotel', this.translateService.instant('emailBookingFlow.HotelFare').toString(), item.amount, item.currency);
              hotelBillingItem.number = this.billingItemList[0].number;
            }
          }
          else {
            if (postPaidHotelBillingItem&& !this.searchService1.multiTripBooking) {
              postPaidHotelBillingItem.amount = '' + (Number.parseFloat(postPaidHotelBillingItem.amount) + Number.parseFloat(item.amount));
              hotelBillingItem.number = this.billingItemList[0].number;
            } else {
              postPaidHotelBillingItem = new BillingItem(PaymentTypes.POSTPAID, 'hotel', this.translateService.instant('emailBookingFlow.HotelFare').toString(), item.amount, item.currency);
              hotelBillingItem.number = this.billingItemList[0].number;
            }
          }
          if(this.searchService1.multiTripBooking){
           
            if (hotelBillingItem) {
              hotelBillingItem.guest =  item.guest
              hotelBillingItem.source = item.source
              if(item.resortFee){
                hotelBillingItem.resortFee =item.resortFee;
              }
              hotelBillingItem.nights = item.nights;
              hotelBillingItem.rooms = item.rooms;
              hotelBillingItem.perNightCharge = item.perNightCharge;
              hotelBillingItem.fee = item.fee
              hotelBillingItem.tax = item.tax
              hotelBillingItem.addPolicy =item.addPolicy
              billingBreakup.push(hotelBillingItem);
            }
            if (postPaidHotelBillingItem) {
              postPaidHotelBillingItem.guest =  item.guest
              postPaidHotelBillingItem.source = item.source
              postPaidHotelBillingItem.nights = item.nights;
              if(item.resortFee){
                hotelBillingItem.resortFee =item.resortFee;
              }
              postPaidHotelBillingItem.rooms = item.rooms;
              postPaidHotelBillingItem.perNightCharge = item.perNightCharge;
              postPaidHotelBillingItem.fee = item.fee
              postPaidHotelBillingItem.tax = item.tax
              postPaidHotelBillingItem.addPolicy =item.addPolicy
              billingBreakup.push(postPaidHotelBillingItem);
            }
          }
        } else if (item.type === 'cars') {
          // Temporary change. Do not break up hotel amount in pre pay and post pay
        //  item.paymentType = PaymentTypes.PREPAID;
          if (item.paymentType === PaymentTypes.PREPAID) {
            if (carBillingItem&& !this.searchService1.multiTripBooking) {
              carBillingItem.amount = '' + (Number.parseFloat(carBillingItem.amount) + Number.parseFloat(item.amount));
              carBillingItem.number = item.number;
            } else {
              carBillingItem = new BillingItem(PaymentTypes.PREPAID, 'cars', 'Car Fare', item.amount, item.currency);
              carBillingItem.number = this.billingItemList[0].number;
            }
            if(this.searchService1.multiTripBooking){
              carBillingItem.baseprice = item.baseprice;
              carBillingItem.tax = item.tax;
              if (carBillingItem) {
                billingBreakup.push(carBillingItem);
              }
            }
          }
          else {
            if (postPaidCarBillingItem && !this.searchService1.multiTripBooking) {
              postPaidCarBillingItem.amount = '' + (Number.parseFloat(postPaidCarBillingItem.amount) + Number.parseFloat(item.amount));
              postPaidCarBillingItem.number = item.number;
              postPaidCarBillingItem.baseprice = item.baseprice;
              postPaidCarBillingItem.tax = item.tax
            } else {
              postPaidCarBillingItem = new BillingItem(PaymentTypes.POSTPAID, 'cars', 'Car Fare', item.amount, item.currency);
              postPaidCarBillingItem.number = item.number;
              postPaidCarBillingItem.baseprice = item.baseprice;
              postPaidCarBillingItem.tax = item.tax
            }
            if (postPaidCarBillingItem && this.searchService1.multiTripBooking) {
             
              billingBreakup.push(postPaidCarBillingItem);
             }
          }
        
        }
        else {
          //  Assumption line items are all pre paid
          // The only line item present is assumed to be service fee
          if (serviceFeeLineItem) {
            serviceFeeLineItem.amount = '' + (Number.parseFloat(serviceFeeLineItem.amount) + Number.parseFloat(item.amount));
          } else {
            serviceFeeLineItem = new BillingItem(PaymentTypes.PREPAID, 'other', this.translateService.instant('emailBookingFlow.ServiceFee').toString(), item.amount, item.currency);
          }
        }
      }
      if(!this.searchService1.multiTripBooking){
      if (flightBillingItem) {
        billingBreakup.push(flightBillingItem);
      }
      if (hotelBillingItem) {
        billingBreakup.push(hotelBillingItem);
      }
      if (carBillingItem) {
        billingBreakup.push(carBillingItem);
      }
      if (postPaidHotelBillingItem) {
        billingBreakup.push(postPaidHotelBillingItem);
      }
      if (postPaidCarBillingItem) {
        billingBreakup.push(postPaidCarBillingItem);
      }
    }
      if (serviceFeeLineItem) {
        billingBreakup.push(serviceFeeLineItem);
      }

     
      this.billingItemArray = billingBreakup;
      return billingBreakup;
    }
  }

  public handleChangeSelectionClicked(data: any) {
    this.showPaymentButton = false;
    this.showPaymentLink = false;
    this.seatSelectionPage = false
    if (data && data === "changeSelection") {
      this.personalDetailChild.updateFormValueInBookingService();
    }
  }

  public handleSelectedFlightChange(data: any) {
    this.billingItemList = data.billingList;
    this.uniqueAirlines = data.airlines;
    this.uniqueHotels = data.hotels;
    this.uniqueCars = data.cars;
    this.eventIdAndOptions = data.choices;
    this.passportMandatory = data.isPassportMandatory;
    this.zipCodeMandatory = data.isZipCodeMandatory;
    this.canAttemptDirectBooking = data.canAttemptDirectBooking;
    //  this.getAllTags();
    this.bookingService.bookingData = data;
    this.personalDetailChild.applyChanges(data);
    this.showPaymentButton = this.personalDetailChild.showProceedToPaymentButton() && selectionCompleted();
    this.isRevalidationInProgress = data.isRevalidationInProgress;
    
    this.gallopLocalStorage.setItem('billingItemList', JSON.stringify(this.getBillingBreakup()));
    this.bookingService.currencyCode = this.billingItemList[0].currency;
    this.currencyCode = this.billingItemList[0].currency;
    this.bookingService.currencyCode = this.billingItemList[0].currency;
   
    this.showPaymentLink = this.showPaymentButton;
    if (this.showPaymentButton || this.showPaymentLink) {
      $('#expensifyCard').removeClass('inactive');
    }
    setTimeout(() => {
      if (this.showPaymentButton || this.showPaymentLink) {
        $('#emergencyContactCard').removeClass('inactive');
      }
      //  enableExpensifyTextbox();
    }, 100);
    if(this.searchService1.tripFeatureEnabled || this.travellersCount > 1){
    this.showSeatButton();
    }
    this.outsidePolicyFlag = data.outsidePolicy;

  }
  showSeatButton() {
    this.bookingService.responseData = undefined;
    this.bookingService.proceedButton = false;
    let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
    if (!this.searchService1.paymentPage && this.uniqueAirlines.length > 0 && selectedFlight && selectedFlight.length>0) {
      this.showSeat = this.personalDetailChild.showProceedToPaymentButton() && selectionCompleted();
      // this.bookingService.uniqueAirlines =[];
      
      for(let i=0;i<selectedFlight.length;i++){
        let ffnMap = new Map<string,string>();
      if (this.canAttemptDirectBooking)
      if(this.optionSelectionChild.tripDetails.length >0){
        for(let j=i;j<this.optionSelectionChild.tripDetails.length;j++){
          if(this.optionSelectionChild.tripDetails[j].type==='flight'){
            if(this.optionSelectionChild.tripDetails[j].ffnno[0]){
              if(this.optionSelectionChild.tripDetails[j].ffnno[0].frequent_flyer_number!==null  && this.optionSelectionChild.tripDetails[j].ffnno[0].frequent_flyer_number.length!==0){
                let airline = selectedFlight[i].legs[0].flightHops[0].carrier;
                ffnMap[airline] = this.optionSelectionChild.tripDetails[j].ffnno[0].frequent_flyer_number;
                break;
              }
            }

          }

        }
      }
      this.searchService1.seatForMultipleBooking[i]=[];
              this.bookingService.forMultipleBooking[i]=[]
        this.bookingService.getSeatDeatils(this.travellersCount, this.ticketId, this.emailQuotOptions.tripId, this.eventIdAndOptions,i,ffnMap).subscribe(res => {
          if (res && res.success) {
            if (res.data && res.data.length > 0) {
              if(!this.searchService1.multiTripBooking){
              this.gallopLocalStorage.removeItem("selectedSeat");
              }
             
              this.bookingService.responseData = res.data;
              this.searchService1.seatForMultipleBooking[i]=[];
              this.bookingService.forMultipleBooking[i]=res.data
              
              this.bookingService.selectedSeatArray = new Array(this.bookingService.responseData.length).fill(null).map(_ => []);
              //this.bookingService.uniqueAirlines =  this.uniqueAirlines ;
              //
            }else{
              this.bookingService.responseData = res.data;
            }
          }else if (res && !res.success){
            this.bookingService.responseData=[];
          }
        });
      }
      if (this.showSeat) {
        $('#expensifyCard').removeClass('inactive');
      }
      if (this.showSeat) {
        $('#emergencyContactCard').removeClass('inactive');
      }
      return this.showSeat;
    }
  }
  isBusinessTrip() {
    if (!this.gallopLocalStorage.getItem("flightSearchRequestForBooking")) return true;
    let searchQuery: FlightSearchRequest = deserialize(JSON.parse(this.gallopLocalStorage.getItem("flightSearchRequestForBooking")));
    return searchQuery.algoType as AlgoTypes === AlgoTypes.PODUCTIVITY as AlgoTypes;
  }
  showExpensifyField() {
    if (this.isBusinessTrip() && this.userAccountInfoObj && this.userAccountInfoObj.companySettings
      && this.userAccountInfoObj.companySettings.expensifySupported) {
      if ((this.uniqueAirlines && this.uniqueAirlines.length > 0) || (this.uniqueHotels && this.uniqueHotels.length > 0)) {
        return true;
      }
    }
    return false;
  }
  public getTotalPayble(): number {
    let total: number = 0.00;
    for (let billingItem of this.billingItemList) {
      if (billingItem.type !== 'other') {
        total = total + Number.parseFloat(billingItem.amount);
      }
      if (billingItem.type === 'flight') {
        total = this.bookingService.totalPayble
      }
    }
    if (this.searchService1.priceChange) {
      this.bookingService.total = Number.parseFloat(this.searchService1.priceChange);
      // var credit = this.bookingService.bookingResponse.changedFlights.flights[0].travelCreditsInfo;
      // if((credit && credit.length >0) && credit.creditAmount > 0){
      //   if(this.bookingService.total > credit.creditAmount ){
      //   return (this.bookingService.total - credit.creditAmoun);
      //  }else if(this.bookingService.total <= credit.creditAmount ){
      //    return 0;
      //   }
      // }else{
      return Number.parseFloat(this.searchService1.priceChange);
      //  }
    } else {
      this.bookingService.total = total;
      return total;
    }
  }
  // public getTotalPassengers(){
  //   if(this.emailQuotOptions && this.emailQuotOptions.flightSearchOptions){
  //     return this.emailQuotOptions.flightSearchOptions.adultCount +
  //        this.emailQuotOptions.flightSearchOptions.infantCount +
  //        this.emailQuotOptions.flightSearchOptions.childCount;
  //   }else{
  //     this.travellersCount;
  //   }
  // }

  public getUniqueAirlines() {
    return this.uniqueAirlines;
  }

  isSelectionCompleted() {
    return selectionCompleted();
  }
  public getUniqueHotels() {
    return this.uniqueHotels;
  }
  public expensifyCheckboxChanged(data: any) {
    // this.addToExpensify = data;

    if (this.addToExpensify === true) {
      this.expensifyEmailControl.setValidators(Validators.compose([this.expensifyEmailValidator.bind(this), Validators.pattern(Constants.RGEX_EMAIL)]));
      this.expenseEmailControl.enable();
    } else {
      this.expensifyEmailControl.setValidators(null);
      this.expenseEmailControl.disable();
    }
    this.expensifyEmailControl.updateValueAndValidity();
    this.expensifyEmailControl.markAsTouched({ onlySelf: true });

  }
  conditionalValidators(condition, validators: ValidatorFn[]) {
    if (condition) {
      return Validators.compose(validators);
    }
    return null;
  }
  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }
  dropDownopen=[];
  getAllTags() {
    const companyid = this.userAccountInfoService.getUserCompanyId();
    this.adminPanelService.getAllTags(companyid).subscribe(res =>{
      if(res.status == "success"){
        this.newTagSetsArray = res.data.tagsets
        this.getAllActiveTagList()
      }else{
        this.toastr.error("Apologies! something went wrong, we could'nt report data. Please try again later");
      };
    },
    error => {
      if (error.status != 403) {
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      };
    }
    );
    
  }

  getAllActiveTagList(){
    const companyid = this.userAccountInfoService.getUserCompanyId();
    this.tagShow = [];
    this.tagset=[];
    this.dropDownopen=[];
    
    this.adminPanelService.getTagAllList().subscribe(resp => {
      if (resp.status === 'success') {
        
        if (resp.data && Object.keys((resp.data)).length > 0) {

          for (let key in resp.data) {
            let keysValue = key
            this.tagset.push(keysValue);
            this.dropDownopen.push(false);

          }
          this.tagShow = new Array(this.tagset.length).fill(null).map(_ => [])
          for(let i=0;i<this.tagset.length;i++){
            let arrayVAlue = resp.data[this.tagset[i]];
            for(let item1 of arrayVAlue){
              this.tagShow[i].push(item1);
            }
            this.tagShow[i] = this.sortList(this.tagShow[i]);
          }
          
          
        }
      } else {
        this.toastr.error("Apologies! something went wrong, we could'nt report data. Please try again later");
      }
    }, error => {
      if (error.status != 403) {
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
  }
  sortList(data) {
    data.sort(function (a, b) {
      if (a.tag_name.toLowerCase() < b.tag_name.toLowerCase()) { return -1; }
      if (a.tag_name.toLowerCase() > b.tag_name.toLowerCase()) { return 1; }
      return 0;
    })
    return data;
  }
  fetchUserAccountInfo() {
    this.userAccountInfoObj = this.userAccountInfoService.getAccountInfo();


    //  this.getAllTags();
    // this.fetchAccountInfoSubscription = this.userAccountInfoService.requestAccountInfo(userEmail,sToken).subscribe(res => {
    //   this.isLoading=false;
    //   if(!res && res == null){
    //       this.router.navigate(['/errors'],{ queryParams: { errorCode: 'unknown' } });
    //       return;
    //   }
    //   else if (res.success == true) {
    //     this.userAccountInfoObj = deserialize(res.data, UserAccountInfo);
    //     this.userAccountInfoService.setAccountInfo(this.userAccountInfoObj);
    //   }else if (res.status == 'error') {
    //     this.router.navigate(['/errors'],{ queryParams: { errorCode: 'unknown' } });
    //   }

    // },error => {
    //   
    //    this.router.navigate(['/errors'],{ queryParams: { errorCode: '403' } });
    // });

  }


}
