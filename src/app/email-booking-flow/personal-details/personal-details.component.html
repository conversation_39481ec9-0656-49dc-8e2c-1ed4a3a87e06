<form [formGroup]="passengersForm" id="passengersFormTarget" autocomplete="off">
  <!-- <div class="container"> -->
  <ng-container formArrayName="passengers" >

    <accordion [closeOthers]="true" #accordion>
      <accordion-group *ngFor="let passenger of getPassengerFormArrayControl(); let i= index; let last = last"
        id="passenger{{i}}accordion" [isOpen]="i ==0" [formGroupName]="i"
        (isOpenChange)="changeaccordion($event,i)"
        [isDisabled]="passengersForm.controls['passengers'].controls.length === 1 && i === 0"
        [ngClass]="{'disabled': passengersForm.controls['passengers'].controls.length === 1 && i === 0}">

        <div id="element_within_div{{i}}" class="card-heading" style="padding-bottom: 29px !important; padding: 20px"(click)="indexOfOpenedAccordion(i)"  accordion-heading>
          <span *ngIf="this.carBooking" class="circle">
            <span class="number">{{i+1}}</span>
          </span>

          <span *ngIf="(!this.accordionOpen[i] && this.carBooking)" class="heading"> {{getPasenegrText(i)}}
            <br>
            <div class="label" style="white-space: normal;"> {{getPassengerDetailText(i)}}</div>
          </span>
          <span *ngIf="(this.accordionOpen[i] && this.carBooking)" class="heading"> {{getPasenegrText(i)}}

          </span>
          <!--<span   *ngIf="this.accordionOpen[i] && this.userAccountInfoService.showRadio" class="field-input1" (click)="$event.stopPropagation();"  style="position: relative;top: 11px;"[ngStyle]="{'padding-left': (!this.carBooking && this.isMobile) ? '20px':'20px'}">
                 <ng-select id ="typeEmployee"  class="field-control" #employeeType tabindex="{{1 * (i + 1)}}" (click)="isdropDownOpen()" groupBy="Type" dropdownPosition="bottom"
               [searchable]="true" [clearable]="false" [hideSelected]="true"  [closeOnSelect]="true" (close)="closeDropdown(i)" 
               [items]="employeeOptions[i]" placeholder="Select passenger" bindLabel="" bindValue="Id" formControlName="employeeType"  [searchFn]="searchByApproverNameAndEmailChanged"
               (change)="showEmployeeTypeChanged($event.Id,i)">
                 <ng-template ng-header-tmp>
                     <div class="selectox-header">
                       <span>Select Passenger</span>
                         </div>
                      </ng-template>
                      <ng-template *ngIf="!this.dropDownopen" ng-label-tmp let-item="item">
                          <span>       {{item.Name}}</span>
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                            <span style="text-overflow: ellipsis;display: inline-block !important;
                            white-space: nowrap;overflow: hidden;padding-right:20px;">{{item.Name}}</span>
                       </ng-template>
                      </ng-select>
                     <div class="select-overlay"></div>
                       </span>-->
          <span *ngIf="this.isMobile"><br></span>
          <span *ngIf="this.accordionOpen[i]  && passenger.controls['employeeType'].value===0" class="edit-box">
            <span *ngIf="this.userAccountInfoService.promptUserTosaveProfile"
              (click)="$event.stopPropagation();EnablingForm(i);" class="Edit">{{'personal.EditInformation' | translate}}</span>
            <span
              *ngIf="!this.userAccountInfoService.promptUserTosaveProfile && !this.userAccountInfoService.savingProfileData"
              (click)="$event.stopPropagation();this.saveProfileButton=true;saveProfileData(i);" class="Edit">{{'personal.Savechangestoprofile' | translate}}</span>
            <span
              *ngIf="!this.userAccountInfoService.promptUserTosaveProfile && !this.userAccountInfoService.savingProfileData"
              (click)="$event.stopPropagation();showEmployeeTypeChanged(passenger.controls['employeeType'].value,i);this.userAccountInfoService.promptUserTosaveProfile=true;"
              class="Edit" style="margin-left:20px;">{{'personal.Cancel' | translate}}</span>
            <span
              *ngIf="!this.userAccountInfoService.promptUserTosaveProfile && this.userAccountInfoService.savingProfileData"
              (click)="$event.stopPropagation();" class="Edit" style="margin-left:20px;">{{'personal.Saving...' | translate}}</span><span
              *ngIf="!this.userAccountInfoService.promptUserTosaveProfile && this.userAccountInfoService.savingProfileData"
              class="loaderClass">
              <loader-dots class="loaderAlign"></loader-dots>
            </span>
          </span>
          <span class="triangle"></span>

        </div>

        <div>
          <div class="row">
            <div *ngIf="false" class="col-6 col-md-4">
              <div class="field-input">
                <div class="label">{{'personal.Title' | translate}}</div>
                <ng-select id="title-passenger" #titlePass tabindex="{{1 * (i + 1)}}"
                  (click)="showToastr(passenger.controls['title'].status)" dropdownPosition="bottom"
                  [searchable]="false" [clearable]="false"
                  [ngStyle]="{'border': passenger.controls['title'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  [closeOnSelect]="true" [items]="titleOptions" placeholder="{{'personal.Title' | translate}}"
                  bindLabel="value" bindValue="titleId" formControlName="title">
                  <ng-template ng-header-tmp>
                    <div class="selectox-header">
                      <span>{{'personal.Title' | translate}}</span>
                      <span class="selectBox-remove" (click)="titlePass.toggle()"><span
                          class="material-icons">clear</span></span>
                    </div>
                  </ng-template>
                </ng-select>
                <div class="select-overlay"></div>
              </div>
              <div
                *ngIf="passenger.controls['title'].hasError('required') && (passenger.controls['title'].touched || passenger.controls['title'].dirty)"
                class="error">{{'personal.Thisfieldisrequired' | translate}}
              </div>
            </div>

            <div class="w-100"></div>

            <div class="col-12">
              <div class="label1">{{'personal.Name' | translate}}</div>
            </div>

            <div class="col-12 col-md-4">
              <div class="field-input" (click)="showToastr(passenger.controls['firstName'].status)">
                <input type="text" tabindex="{{2 * (i + 1)}}" class="field-control"
                  [ngStyle]="{'border': passenger.controls['firstName'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  placeholder="{{'personal.firstName' | translate}}" formControlName="firstName" />
                <div
                  *ngIf="passenger.controls['firstName'].hasError('pattern') && (passenger.controls['firstName'].touched || passenger.controls['firstName'].dirty)"
                  class="error">{{'personal.PleaseenteravalidfirstnameNumericorspecialcharactersarenotallowed' |
                  translate}}
                </div>
                <div
                  *ngIf="passenger.controls['firstName'].hasError('required') && (passenger.controls['firstName'].touched || passenger.controls['firstName'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
                <div *ngIf="passenger.controls['firstName'].hasError('minLengthArray')" class="error">{{'personal.Minimum2lettersrequiredforfirstname.' | translate}}
                </div>
              </div>
            </div>

            <div *ngIf="this.carBooking" class="col-12 col-md-4">
              <div class="field-input" (click)="showToastr(passenger.controls['middleName'].status)">
                <input type="text" tabindex="{{3 * (i + 1)}}" class="field-control"
                  [ngStyle]="{'border': passenger.controls['middleName'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  placeholder="{{'personal.MiddlenameifshownonID' | translate}}" formControlName="middleName" />
                <div
                  *ngIf="passenger.controls['middleName'].hasError('pattern') && (passenger.controls['middleName'].touched || passenger.controls['middleName'].dirty)"
                  class="error">{{'personal.PleaseenteravalidmiddlenameNumericorspecialcharactersarenotallowed' |
                  translate}}
                </div>
              </div>
            </div>

            <div class="col-12 col-md-4">
              <div class="field-input" (click)="showToastr(passenger.controls['lastName'].status)">
                <input type="text" tabindex="{{4 * (i + 1)}}" class="field-control"
                  [ngStyle]="{'border': passenger.controls['lastName'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  placeholder="{{'personal.LastName' | translate}}" formControlName="lastName" />
                  <div
                  *ngIf="(passenger.controls['lastName'].hasError('minlength'))&& !passenger.controls['lastName'].hasError('pattern') && (passenger.controls['lastName'].touched || passenger.controls['lastName'].dirty)"
                  class="error"> {{'personal.Minimum2lettersrequiredforlastname' | translate}}
                </div>
                <div
                  *ngIf="passenger.controls['lastName'].hasError('pattern') && (passenger.controls['lastName'].touched || passenger.controls['firstName'].dirty)"
                  class="error">{{'personal.PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed' |
                  translate}}
                </div>
                <div
                  *ngIf="passenger.controls['lastName'].hasError('required') && (passenger.controls['lastName'].touched || passenger.controls['lastName'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
              </div>
            </div>

            <div class="w-100"></div>

            <div *ngIf="this.carBooking" class="col-12 col-md-6 col-lg-4">
              <div class="field-input">
                <div class="label">{{'personal.Gender' | translate}}</div>
                <div class="row"  style="margin-left: -16px !important;">
                  <div class="col">
                    <div tabindex="{{5 * (i + 1)}}" class="toggle-button"
                      (keyup.enter)="setGender(i,'M',passenger.controls['employeeType'],passenger.controls['gender'].status)"
                      [ngClass]="{'selected': isSelectedGender(i,'M')}"
                      (click)="setGender(i,'M',passenger.controls['employeeType'],passenger.controls['gender'].status)">
                      {{'personal.Male' | translate}}</div>
                  </div>
                  <div style="visibility: hidden;width: 0px;">
                    <input formControlName="gender">
                  </div>
                  <div class="col">
                    <div tabindex="{{6 * (i + 1)}}" class="toggle-button"
                      (keyup.enter)="setGender(i,'F',passenger.controls['employeeType'],passenger.controls['gender'].status)"
                      [ngClass]="{'selected': isSelectedGender(i,'F')}"
                      (click)="setGender(i,'F',passenger.controls['employeeType'],passenger.controls['gender'].status)">
                      {{'personal.Female' | translate}}</div>
                  </div>

                  <div class="col">
                    <div tabindex="{{7 * (i + 1)}}" class="toggle-button"
                      (keyup.enter)="setGender(i,'X',passenger.controls['employeeType'],passenger.controls['gender'].status)"
                      [ngClass]="{'selected': isSelectedGender(i,'X')}"
                      (click)="setGender(i,'X',passenger.controls['employeeType'],passenger.controls['gender'].status)">
                     {{'personal.Unspecified' | translate}} </div>
                  </div>
                </div>
                <div
                  *ngIf="passenger.controls['gender'].hasError('required') && (passenger.controls['gender'].touched || passenger.controls['gender'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
              </div>
            </div>

            <div *ngIf="this.carBooking" class="col-12 col-md-6 col-lg-4">
              <div class="field-input" (click)="showToastr(passenger.controls['dateOfBirth'].status)">
                <div class="label">{{'personal.DateofBirth' | translate}}</div>
                <div class="on-right">
                  <input tabindex="{{8 * (i + 1)}}" class="field-control"
                    [ngStyle]="changeStyle(this.maskDob,passenger.controls['dateOfBirth'].status)" bsDatepicker
                    #dobDatePicker="bsDatepicker" [outsideClick]="true" [bsConfig]="{showWeekNumbers: false}"
                    placeholder="{{'personal.MMDDYY' | translate}}" [maxDate]="maxDoBDate" formControlName="dateOfBirth"
                    (keydown)="preventEntry($event)" [readonly]="isDatePickerReadonly"
                    (onShown)="onShowPicker($event, expiryDatePicker);this.maskDob=false;"
                    (onHidden)="onHidePicker();this.maskDob=getFocusOutInput(passenger.controls['employeeType'].value,passenger.controls['dateOfBirth'].value,this.maskDob)"
                     />
                  <span class="icon icon-calendar" (click)="dobDatePicker.toggle()"></span>
                </div>
                <div
                *ngIf="passenger.controls['dateOfBirth'].hasError('invalidInfantAge') && (passenger.controls['dateOfBirth'].touched || passenger.controls['dateOfBirth'].dirty)"
                class="error">{{'personal.Agecannotbelessthan3years' | translate}}
              </div>
                <div
                  *ngIf="passenger.controls['dateOfBirth'].hasError('invalidAdultAge') && (passenger.controls['dateOfBirth'].touched || passenger.controls['dateOfBirth'].dirty)"
                  class="error"><span *ngIf="this.hotelAdultCount===0"> {{'personal.Agecannotbelessthan12years' |
                    translate}}</span>
                  <span *ngIf="this.hotelAdultCount>0"> {{'personal.Agecannotbelessthan12years' | translate}}</span>
                </div>
                <div
                *ngIf="passenger.controls['dateOfBirth'].hasError('invalidAdultAgeForHotel') && (passenger.controls['dateOfBirth'].touched || passenger.controls['dateOfBirth'].dirty)"
                class="error"><span *ngIf="this.hotelAdultCount===0"> {{'personal.Agecannotbelessthan12years' |
                  translate}}</span>
                <span *ngIf="this.hotelAdultCount>0"> {{'personal.Agecannotbelessthan18years' | translate}}</span>
              </div>
                <div
                  *ngIf="passenger.controls['dateOfBirth'].hasError('invalidChildAge') && (passenger.controls['dateOfBirth'].touched || passenger.controls['dateOfBirth'].dirty)"
                  class="error">{{'personal.Ageshouldbeup2yearsonly' | translate}}
                </div>
               
               
                <div
                  *ngIf="passenger.controls['dateOfBirth'].hasError('required') && (passenger.controls['dateOfBirth'].touched || passenger.controls['dateOfBirth'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
                <div
                *ngIf="passenger.controls['dateOfBirth'].hasError('dateValidity') && (passenger.controls['dateOfBirth'].touched || passenger.controls['dateOfBirth'].dirty)"
                class="error">please enter a valid date
              </div>
              </div>
            </div>

            <div class="w-100"></div>

            <div class="col-12 col-md-6 col-lg-4">
              <div class="field-input">
                <div class="label">{{'personal.Email' | translate}}</div>
                <input tabindex="{{9 * (i + 1)}}" type="email" class="field-control" maxlength="50"
                  [ngStyle]="{'border': emailFieldDisabled(passenger.controls['email'].value,passenger.controls['employeeType'].value) ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  (keydown.space)="$event.preventDefault()" placeholder="{{'personal.Email' | translate}}"
                  formControlName="email" />
                <div
                  *ngIf="passenger.controls['email'].hasError('pattern') && (passenger.controls['email'].touched || passenger.controls['email'].dirty)"
                  class="error">{{'personal.Pleaseenteravalidemail' | translate}}
                </div>
                <div
                  *ngIf="passenger.controls['email'].hasError('required') && (passenger.controls['email'].touched || passenger.controls['email'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
              </div>
            </div>

            <div class="col-12 col-md-6 col-lg-4">
              <div class="field-input">
                <div class="label">{{'personal.PhoneNumber' | translate}}</div>
                <div class="flex-wrapper">
                  <ng-select id="dialCode" #dialCode (change)="onCountrySelected($event,i)"
                    (click)="isdropDownOpen1(passenger.controls['employeeType'].value,this.maskDial)"
                    (close)="closeDropdown1(passenger.controls['employeeType'].value,passenger.get('dialCode').value,this.maskDial)"
                    tabindex="{{9 * (i + 1)}}" class="phone-number"
                    [ngStyle]="{'border': passenger.controls['dialCode'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                    (click)="showToastr(passenger.controls['dialCode'].status)" dropdownPosition="bottom"
                    [items]="countries" formControlName="dialCode" bindValue="dial_code"
                    [searchFn]="searchByNameOrCode">
                    <ng-template ng-header-tmp>
                      <div class="selectox-header">
                        <span>{{'personal.CountryName' | translate}}</span>
                      </div>
                    </ng-template>
                    <ng-template ng-label-tmp let-item="item">
                      <img class="flag-label"
                        src="assets/flags/{{getCountryCode(passenger.controls['dialCode'].value,passenger.controls['phoneNumber'].value)}}.png" />
                      <span style="padding-top:5px;"
                        [ngStyle]="{'font-family': this.maskDial ? 'password':'ApercuProMono'}">{{getSelecteddropdownValue(passenger.controls['employeeType'].value,passenger.controls['dialCode'].value,this.maskDial)}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item">
                      <span class="country-name" title="{{item.name}}">{{item.name}}</span>
                      <img class="icon-country-flag" src="assets/flags/{{item.code | lowercase}}.png" />
                    </ng-template>
                  </ng-select>
                  <div class="flex-fill" (click)="showToastr(passenger.controls['phoneNumber'].status)">
                    <input tabindex="{{10 * (i + 1)}}" type="tel"
                      mask="{{getPhoneNumberMask(passenger.controls['dialCode'].value)}}" class="field-control"
                      (focus)="this.maskPhone=false;"
                      [ngStyle]="changeStyle(this.maskPhone,passenger.controls['phoneNumber'].status)"
                      (focusout)="this.maskPhone=getFocusOutInput(passenger.controls['employeeType'].value,passenger.get('phoneNumber').value,this.maskPhone)"
                      placeholder="{{getPhoneNumberPaceHolder(passenger.controls['dialCode'].value)}}"
                      formControlName="phoneNumber" />


                  </div>
                  <!-- <int-phone-prefix [locale]="'en'" [defaultCountry]="'us'" formControlName="phoneNumber"></int-phone-prefix> -->
                </div>

                <span
                  *ngIf="passenger.controls['dialCode'].hasError('required') && (passenger.get('dialCode').touched || passenger.get('dialCode').dirty)"
                  style="width:50% !important;margin-left: 0px !important;white-space: break-spaces !important;"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </span>
                <span
                  *ngIf="passenger.get('phoneNumber').hasError('pattern') && (passenger.get('phoneNumber').touched || passenger.get('phoneNumber').dirty)"
                  class="error"
                  style="float:right;width:50% !important;margin-left: 0px !important;white-space: break-spaces !important;">{{'personal.Pleaseenteravalidphonenumber'
                  | translate}}
                </span>
                <span
                  *ngIf="passenger.get('phoneNumber').hasError('required') && (passenger.get('phoneNumber').touched || passenger.get('phoneNumber').dirty)"
                  class="error"
                  style="float:right;width:50% !important;margin-left: 0px !important;white-space: break-spaces !important;">{{'personal.Thisfieldisrequired'
                  | translate}}
                </span>
              </div>
            </div>

            <div class="w-100"></div>

            <div *ngIf="this.carBooking" class="col-12 col-md-8">
              <div class="field-input" (click)="showToastr(passenger.controls['address'].status)">
                <div class="label">{{'personal.Address' | translate}}</div>
                <input tabindex="{{11 * (i + 1)}}" ngx-google-places-autocomplete
                  (onAddressChange)="handleAddressChange(i, $event)" type="text" class="field-control"
                  [ngStyle]="changeStyle(this.maskAdd,passenger.controls['address'].status)"
                  (focus)="this.maskAdd=false;"
                  (focusout)="this.maskAdd=getFocusOutInput(passenger.controls['employeeType'].value,passenger.controls['address'].value,this.maskAdd)"
                  placeholder="{{'personal.Writeyourhomeaddresshere' | translate}}" formControlName="address" />
                <div
                  *ngIf="passenger.controls['address'].hasError('required') && (passenger.controls['address'].touched || passenger.controls['address'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
              </div>
            </div>

            <div *ngIf="this.isZipCodeRequired" class="col-12 col-md-4">
              <div class="field-input" (click)="showToastr(passenger.controls['zipCode'].status)">
                <div class="label">{{'personal.ZipCode' | translate}}</div>
                <input tabindex="{{12 * (i + 1)}}" type="text" class="field-control" (focus)="this.maskZip=false;"
                  [ngStyle]="changeStyle(this.maskZip,passenger.controls['zipCode'].status)"
                  (focusout)="this.maskZip=getFocusOutInput(passenger.controls['employeeType'].value,passenger.controls['zipCode'].value,this.maskZip)"
                  class="field-control" placeholder="{{'personal.AreaZipCode' | translate}}"
                  formControlName="zipCode" />
                <div
                  *ngIf="passenger.controls['zipCode'].hasError('invalidZipCode') && (passenger.controls['zipCode'].touched || passenger.controls['zipCode'].dirty)"
                  class="error">{{'personal.Pleaseenteravalidzipcode' | translate}}
                </div>
                <div
                  *ngIf="passenger.controls['zipCode'].hasError('required') && (passenger.controls['zipCode'].touched || passenger.controls['zipCode'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
                <div
                  *ngIf="passenger.controls['zipCode'].hasError('pattern') && (passenger.controls['zipCode'].touched || passenger.controls['zipCode'].dirty)"
                  class="error">{{'personal.Pleaseenteravalidzipcodewithoutspace' | translate}}
                </div>
              </div>
            </div>

            <div class="col-12 col-md-6 col-xl-4" *ngIf="uniqueCars.length > 0 && noOfPassengers >1" formArrayName="carLoyalityInfo">
              <div class="field-input" *ngFor="let cln of passenger.controls['carLoyalityInfo'].controls; let j=index;"
                [formGroupName]="j">
                <div style="white-space: unset;" class="label">{{'personal.CarLoyaltyNumber' | translate}}
                  ({{cln.controls['rentalCarName'].value}})</div>
                <div class="check-or-input" [ngClass]="{'checked': !isTravelerNumAvailable(i,'CLN',j)}"
                  [ngStyle]="{'border':cln.controls['rentalCarLoyaltyNumber'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  (click)="showToastr(cln.controls['rentalCarLoyaltyNumber'].status)">
                  <input tabindex="{{13 * (i + 1)}}" type="text" class="field-control"  maxlength="20"(focus)="this.maskCln=false;"
                    [ngStyle]="{'font-family': this.maskCln ? 'password':'ApercuProMono'}"
                    (focusout)="this.maskCln=getFocusOutInput(passenger.controls['employeeType'].value,cln.controls['rentalCarLoyaltyNumber'].value,this.maskCln)"
                    class="field-control" placeholder="{{'personal.LoyaltyNumber' | translate}}" formControlName="rentalCarLoyaltyNumber"
                    value="{{cln.controls['rentalCarLoyaltyNumber'].value}}" />
                  <div class="check-input" *ngIf="hideKtnffnCheckBoxes(i)"
                    (click)="checkTravelerNumNotAvailableCheckBox(i,'CLN', clnref,passenger.controls['employeeType'],cln.controls['rentalCarLoyaltyNumber'].status,j)">
                    <input tabindex="{{14 * (i + 1)}}" type="checkbox" [checked]="!isTravelerNumAvailable(i,'CLN',j)"
                      id="{{'cln-' + i}}" (click)="preventCheck($event);$event.stopPropagation()" #clnref />
                    <label for="{{'cln-' + i}}">{{'personal.NotAvailable' | translate}}</label>
                  </div>
                </div>
                <div
                  *ngIf="cln.controls['rentalCarLoyaltyNumber'].hasError('required') && (cln.controls['rentalCarLoyaltyNumber'].touched || cln.controls['rentalCarLoyaltyNumber'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
                <div
                  *ngIf="cln.controls['rentalCarLoyaltyNumber'].hasError('pattern') && (cln.controls['rentalCarLoyaltyNumber'].touched || cln.controls['rentalCarLoyaltyNumber'].dirty)"
                  class="error">{{'personal.PleaseenteravalidCarLoyalityNumber' | translate}}
                </div>

              </div>
            </div>

            <div class="w-100"></div>

            <div class="col-12 col-md-6 col-xl-4" *ngIf="uniqueAirlines.length > 0 && noOfPassengers >1" formArrayName="frequentFlyerInfo">
              <div class="field-input"
                *ngFor="let ffn of passenger.controls['frequentFlyerInfo'].controls; let j=index;" [formGroupName]="j">
                <div style="white-space: unset;" class="label">{{'personal.FrequentFlyerNumber' | translate}}
                  ({{ffn.controls['name'].value}})</div>
                <div class="check-or-input" [ngClass]="{'checked': !isTravelerNumAvailable(i,'FFN',j)}"
                  [ngStyle]="{'border':ffn.controls['frequent_flyer_number'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  (click)="showToastr(ffn.controls['frequent_flyer_number'].status)">
                  <input tabindex="{{13 * (i + 1)}}" type="text" class="field-control" maxlength="20" (focus)="this.maskFfn=false;"
                    [ngStyle]="{'font-family': this.maskFfn ? 'password':'ApercuProMono'}"
                    (focusout)="this.maskFfn=getFocusOutInput(passenger.controls['employeeType'].value,ffn.controls['frequent_flyer_number'].value,this.maskFfn)"
                    class="field-control" placeholder="{{'personal.FlyerNumber' | translate}}"
                    formControlName="frequent_flyer_number" value="{{ffn.controls['frequent_flyer_number'].value}}" />
                  <div class="check-input" *ngIf="hideKtnffnCheckBoxes(i)"
                    (click)="checkTravelerNumNotAvailableCheckBox(i,'FFN', ffnref,passenger.controls['employeeType'],ffn.controls['frequent_flyer_number'].status,j)">
                    <input tabindex="{{14 * (i + 1)}}" type="checkbox" [checked]="!isTravelerNumAvailable(i,'FFN',j)"
                      id="{{'ffn-' + i}}" (click)="preventCheck($event);$event.stopPropagation()" #ffnref />
                    <label for="{{'ffn-' + i}}">{{'personal.NotAvailable' | translate}}</label>
                  </div>
                </div>
                <div
                  *ngIf="ffn.controls['frequent_flyer_number'].hasError('required') && (ffn.controls['frequent_flyer_number'].touched || ffn.controls['frequent_flyer_number'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
                <div
                  *ngIf="ffn.controls['frequent_flyer_number'].hasError('pattern') && (ffn.controls['frequent_flyer_number'].touched || ffn.controls['frequent_flyer_number'].dirty)"
                  class="error">{{'personal.PleaseenteravalidFrequentFlyerNumber' | translate}}
                </div>

              </div>
            </div>



            <div class="col-12 col-md-6 col-xl-4" *ngIf="uniqueAirlines.length > 0 && this.bookingService.showKtn">
              <div class="field-input">
                <div class="label">{{'personal.KnownTravelerNumber' | translate}}</div>
                <div class="check-or-input" [ngClass]="{'checked': !isTravelerNumAvailable(i,'KTN')}"
                  [ngStyle]="{'border':passenger.controls['knownTravellerNumber'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  (click)="showToastr(passenger.controls['knownTravellerNumber'].status)">
                  <input tabindex="{{15 * (i + 1)}}" type="text" class="field-control" (focus)="this.maskKtn=false;"
                    [ngStyle]="{'font-family': this.maskKtn ? 'password':'ApercuProMono'}"
                    (focusout)="this.maskKtn=getFocusOutInput(passenger.controls['employeeType'].value,passenger.controls['knownTravellerNumber'].value,this.maskKtn)"
                    class="field-control" placeholder="{{'personal.KTNNumber' | translate}}"
                    formControlName="knownTravellerNumber" maxlength="12" />
                  <div class="check-input" *ngIf="hideKtnffnCheckBoxes(i)"
                    (click)="checkTravelerNumNotAvailableCheckBox(i,'KTN', ktnref,passenger.controls['employeeType'],passenger.controls['knownTravellerNumber'].status)">
                    <input tabindex="{{16 * (i + 1)}}" type="checkbox" [checked]="!isTravelerNumAvailable(i,'KTN')"
                      id="{{ 'ktn-' + i}}" (click)="preventCheck($event);$event.stopPropagation()" #ktnref />
                    <label for="{{ 'ktn-' + i}}">{{'personal.NotAvailable' | translate}}</label>
                  </div>
                </div>
                <div
                  *ngIf="passenger.controls['knownTravellerNumber'].hasError('minlength') && !passenger.controls['knownTravellerNumber'].hasError('pattern') && (passenger.controls['knownTravellerNumber'].touched || passenger.controls['knownTravellerNumber'].dirty)"
                  class="error">{{'personal.Thisfieldcannotbelessthan9characters' | translate}}
                </div>
                <div
                  *ngIf="passenger.controls['knownTravellerNumber'].hasError('required') && (passenger.controls['knownTravellerNumber'].touched || passenger.controls['knownTravellerNumber'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
                <div
                  *ngIf="passenger.controls['knownTravellerNumber'].hasError('pattern') && (passenger.controls['knownTravellerNumber'].touched || passenger.controls['knownTravellerNumber'].dirty)"
                  class="error">{{'personal.PleaseenteravalidKnownTravelerNumber' | translate}}
                </div>
              </div>
            </div>
          </div>


          <div class="sub-heading" *ngIf="uniqueAirlines.length > 0 && isPassportRequired">
            {{'personal.PassportInformation' | translate}}</div>

          <div class="row" *ngIf="uniqueAirlines.length > 0 && isPassportRequired">
            <div class="col-12 col-md-4">
              <div class="field-input" (click)="showToastr(passenger.controls['passportNumber'].status)">
                <div class="label">{{'personal.PassportNumber' | translate}}</div>

                <input tabindex="{{17 * (i + 1)}}" type="text" class="field-control" (focus)="this.maskPnum=false;"
                  [ngStyle]="changeStyle(this.maskKtn,passenger.controls['passportNumber'].status)"
                  (focusout)="this.maskPnum=getFocusOutInput(passenger.controls['employeeType'].value,passenger.controls['passportNumber'].value,this.maskPnum)"
                  class="field-control" placeholder="{{'personal.EnterPassportNumber' | translate}}"
                  formControlName="passportNumber" />
                <div
                  *ngIf="passenger.controls['passportNumber'].hasError('pattern') && (passenger.controls['passportNumber'].touched || passenger.controls['passportNumber'].dirty)"
                  class="error">{{'personal.Pleaseenteravalidpassportnumber' | translate}}
                </div>
                <div
                  *ngIf="passenger.controls['passportNumber'].hasError('required') && (passenger.controls['passportNumber'].touched || passenger.controls['passportNumber'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
              </div>
            </div>

            <div class="col-12 col-md-4">
              <div class="field-input" (click)="showToastr(passenger.controls['passportCountry'].status)">
                <div class="label">{{'personal.IssuanceCountry' | translate}}</div>
                <ng-select tabindex="{{18 * (i + 1)}}" id="passportIssuance" #pCountry
                  (click)="isdropDownOpen2(passenger.controls['employeeType'].value,this.maskPcon)"
                  [ngStyle]="{'border':passenger.controls['passportCountry'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  (close)="closeDropdown2(passenger.controls['employeeType'].value,passenger.controls['passportCountry'].value.name,this.maskPcon)"
                  dropdownPosition="bottom" placeholder="{{'personal.SelectIssuanceCountry' | translate}}"
                  [items]="countriesName" bindLabel="name" formControlName="passportCountry">
                  <ng-template ng-header-tmp>
                    <div class="selectox-header">
                      <span>Select Country</span>
                    </div>
                  </ng-template>
                  <ng-template ng-label-tmp let-item="item">
                    <span style="padding-top:5px;"
                      [ngStyle]="{'font-family': this.maskPcon ? 'password':'ApercuProMono'}">{{getSelecteddropdownValue(passenger.controls['employeeType'].value,passenger.controls['passportCountry'].value.name,this.maskPcon)}}</span>
                  </ng-template>
                </ng-select>
                <!-- <i18n-country-select [(iso3166Alpha2)]="country.isocode" size="sm" placeholder="Select Issuance Country" (click)="onSelectPassportCountry(i,$event)"></i18n-country-select> -->

                <div
                  *ngIf="passenger.controls['passportCountry'].hasError('required') && (passenger.controls['passportCountry'].touched || passenger.controls['passportCountry'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
              </div>
            </div>

            <div class="col-12 col-md-4">
              <div class="field-input">
                <div class="label">{{'personal.Nationality' | translate}}</div>
                <ng-select tabindex="{{19 * (i + 1)}}" id="passportNationality" #pNational
                  (click)="isdropDownOpen3(passenger.controls['employeeType'].value,this.maskPnat)"
                  [ngStyle]="{'border': passenger.controls['passportNationality'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  (close)="closeDropdown3(passenger.controls['employeeType'].value,passenger.controls['passportNationality'].value.nationality,this.maskPnat)"
                  dropdownPosition="bottom" placeholder="{{'personal.SelectNationality' | translate}}"
                  [items]="countries" bindLabel="nationality"
                  (click)="showToastr(passenger.controls['passportNationality'].status)"
                  formControlName="passportNationality">
                  <ng-template ng-header-tmp>
                    <div class="selectox-header">
                      <span>Select Nationality</span>
                    </div>
                  </ng-template>
                  <ng-template ng-label-tmp let-item="item">
                    <span style="padding-top:5px;"
                      [ngStyle]="{'font-family': this.maskPnat ? 'password':'ApercuProMono'}">{{getSelecteddropdownValue(passenger.controls['employeeType'].value,passenger.controls['passportNationality'].value.nationality,this.maskPnat)}}</span>
                  </ng-template>
                </ng-select>
                <div
                  *ngIf="passenger.controls['passportNationality'].hasError('required') && (passenger.controls['passportNationality'].touched || passenger.controls['passportNationality'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
              </div>
            </div>

            <div class="col-6 col-md-4">
              <div class="field-input" (click)="showToastr(passenger.controls['passportExpiryDate'].status)">
                <div class="label">{{'personal.ExpiryDate' | translate}}</div>
                <div class="on-right">
                  <input tabindex="{{20 * (i + 1)}}" class="field-control"
                    [ngStyle]="changeStyle(this.maskPexp,passenger.controls['passportExpiryDate'].status)" bsDatepicker
                    #expiryDatePicker="bsDatepicker" [outsideClick]="true" [bsConfig]="{showWeekNumbers: false}"
                    placeholder="{{'personal.MMDDYY' | translate}}" [minDate]="minPassportDate"
                    formControlName="passportExpiryDate" (keydown)="preventEntry($event)"
                    (onShown)="onShowPicker($event, expiryDatePicker);this.maskPexp=false;"
                    (onHidden)="onHidePicker();this.maskPexp=getFocusOutInput(passenger.controls['employeeType'].value,passenger.controls['passportExpiryDate'].value,this.maskPexp)"
                    readonly />
                  <span class="icon icon-calendar" (click)="expiryDatePicker.toggle()"></span>
                </div>

                <div
                  *ngIf="passenger.controls['passportExpiryDate'].hasError('required') && (passenger.controls['passportExpiryDate'].touched || passenger.controls['passportExpiryDate'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 col-xl-4" *ngIf="uniqueHotels.length > 0 && noOfPassengers >1" formArrayName="hotelLoyalityInfo">
            <div class="field-input"
              *ngFor="let hln of passenger.controls['hotelLoyalityInfo'].controls; let hlIndex=index;"
              [formGroupName]="hlIndex">
              <ng-container *ngIf="hln.controls['loyaltyPointsSupported'].value">

                <div   style="white-space: unset;" class="label">{{'personal.HotelLoyaltyNumber' | translate}}
                  ({{hln.controls['name'].value}})</div>
                <div class="check-or-input" [ngClass]="{'checked': !isTravelerNumAvailable(i,'HLN',hlIndex)}"
                  [ngStyle]="{'border': hln.controls['hotel_loyality_number'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                  (click)="showToastr1(hln.controls['hotel_loyality_number'].status,hln.controls['loyaltyPointsSupported'].value)">
                  <input tabindex="{{21 * (i + 1)}}" type="text" (focus)="this.maskHln=false;" maxlength="20"
                    [ngStyle]="{'font-family': this.maskHln ? 'password':'ApercuProMono'}"
                    (focusout)="this.maskHln=getFocusOutInput(passenger.controls['employeeType'].value,hln.controls['hotel_loyality_number'].value,this.maskHln)"
                    [attr.disabled]="(!hln.controls['loyaltyPointsSupported'].value)? true:null" class="field-control"
                    placeholder="{{'personal.LoyaltyNumber' | translate}}" formControlName="hotel_loyality_number" />
                  <div class="check-input" *ngIf="!loyaltyNumberMandatoryOrNot() && hideKtnffnCheckBoxes(i)"
                    (click)="checkTravelerNumNotAvailableCheckBox(i,'HLN', hlnref,passenger.controls['employeeType'],hln.controls['hotel_loyality_number'].status,hlIndex)">
                    <input tabindex="{{22 * (i + 1)}}" type="checkbox"
                      [disabled]="!hln.controls['loyaltyPointsSupported'].value"
                      [checked]="!isTravelerNumAvailable(i,'HLN',hlIndex)" id="{{'hln-' + i}}"
                      (click)="preventCheck($event);$event.stopPropagation()" #hlnref />
                    <label for="{{'hln-' + i}}">{{'personal.NotAvailable' | translate}}</label>
                  </div>
                </div>
                <div
                  *ngIf="hln.controls['hotel_loyality_number'].hasError('required') &&  (hln.controls['hotel_loyality_number'].touched || hln.controls['hotel_loyality_number'].dirty)"
                  class="error">{{'personal.Thisfieldisrequired' | translate}}
                </div>
                <div
                  *ngIf="hln.controls['hotel_loyality_number'].hasError('pattern') && (hln.controls['hotel_loyality_number'].touched || hln.controls['hotel_loyality_number'].dirty)"
                  class="error">{{'personal.PleaseenteravalidHotelLoyaltyNumber' | translate}}
                </div>
                <div *ngIf="!hln.controls['loyaltyPointsSupported'].value" class="error">
                  <i class="fa fa-ban" aria-hidden="true"></i> {{'personal.Thisdiscountedfaredoesntallowloyaltyrewards.' | translate}}
                </div>
              </ng-container>
            </div>
          </div>

        <div class="w-100"></div>
        <div class="row">
          <div *ngIf="uniqueAirlines.length > 0" class="col-12 col-md-8" >
            <div class="field-input">
              <div class="label">{{'personal.SeatPreference' | translate}}</div>
              <div class="row" style="margin-left: -16px !important;">
                <div class="col">
                  <div tabindex="{{21 * (i + 1)}}" class="toggle-button"
                    (keyup.enter)="setSeatPref(i,'AISLE',passenger.controls['employeeType'],passenger.controls['seat'].status)"
                    [ngClass]="{'selected': isSelectedSeatPref(i,'AISLE')}"
                    (click)="setSeatPref(i,'AISLE',passenger.controls['employeeType'],passenger.controls['seat'].status)">
                    {{'personal.Aisle' | translate}}</div>
                </div>
                <div style="visibility: hidden;width: 0px;">
                  <input formControlName="seat">
                </div>
                <div class="col">
                  <div tabindex="{{22 * (i + 1)}}" class="toggle-button"
                    (keyup.enter)="setSeatPref(i,'WINDOW',passenger.controls['employeeType'],passenger.controls['seat'].status)"
                    [ngClass]="{'selected': isSelectedSeatPref(i,'WINDOW')}"
                    (click)="setSeatPref(i,'WINDOW',passenger.controls['employeeType'],passenger.controls['seat'].status)">
                    {{'personal.Window' | translate}}</div>
                </div>
                
                <div class="col" style="">
                  <div tabindex="{{7 * (i + 1)}}" class="toggle-button"
                    (keyup.enter)="setSeatPref(i,'NO_PREF',passenger.controls['employeeType'],passenger.controls['seat'].status)"
                    [ngClass]="{'selected': isSelectedSeatPref(i,'NO_PREF')}"
                    (click)="setSeatPref(i,'NO_PREF',passenger.controls['employeeType'],passenger.controls['seat'].status)">
                    {{'personal.NoPreference' | translate}}</div>
                </div>
              </div>
              <div
                *ngIf="passenger.controls['seat'].hasError('required') && (passenger.controls['seat'].touched || passenger.controls['seat'].dirty)"
                class="error">{{'personal.Thisfieldisrequired' | translate}}
              </div>
            </div>
          </div>
        </div>
          <div class="w-100"></div>
        <div class="label" style="display: flex;"[ngStyle]="{'margin-left': this.isMobile ? '20px':'0px'}">{{'profilePersonal.EmergencyContact' | translate}}<span
          *ngIf="passenger.get('emergencyContactName').hasError('required') && (passenger.get('emergencyContactName').touched || passenger.controls.get('emergencyContactName').dirty) || 
          passenger.get('emergencyContactName').hasError('pattern') && (passenger.get('emergencyContactName').touched || passenger.get('emergencyContactName').dirty) ||
          passenger.get('relationship').hasError('pattern') && (passenger.get('relationship').touched || passenger.get('relationship').dirty) ||
          passenger.controls['dialCode1'].hasError('required') && (passenger.get('dialCode1').touched || passenger.get('dialCode1').dirty) ||
          passenger.get('contactNumber').hasError('required') && (passenger.get('contactNumber').touched || passenger.get('contactNumber').dirty)"class="error" style="margin-left: 5px;" [ngStyle]="{'margin-top': this.isMobile ? '4px':'4px'}">*{{'profilePersonal.Pleaseupdateallfieldsorleaveitempty' |
          translate}}</span></div>
          
          <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
              <div class="field-input" (click)="showToastr(passenger.controls['emergencyContactName'].status)">
                <input tabindex="{{23 * (i + 1)}}" maxlength="100" formControlName="emergencyContactName" class="field-control"
                  (input)="getName($event.target.value,i)"
                  [ngStyle]="changeStyle(this.maskEmeNnname,passenger.controls['emergencyContactName'].status)"
                  (focus)="this.maskEmeNnname=false;"
                  (focusout)="this.maskEmeNnname=getFocusOutInput(passenger.controls['employeeType'].value,passenger.controls['emergencyContactName'].value,this.maskEmeNnname);getName($event.target.value,i)"
                  type="text" placeholder="{{'personal.FullName' | translate}}" />
                <div
                  *ngIf="passenger.get('emergencyContactName').hasError('pattern') && (passenger.get('emergencyContactName').touched || passenger.get('emergencyContactName').dirty)"
                  id="title-error" class="error">
                  {{'personal.PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed' |
                  translate}}</div>
               
                <div *ngIf="passenger.get('emergencyContactName').hasError('minLengthArray')" class="error">{{'personal.Minimum2lettersrequiredforfirstname.' | translate}}</div>
              </div>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12" [ngStyle]="{'margin-top':isMobile ? '10px':'0px'}">
              <div class="field-input" (click)="showToastr(passenger.controls['relationship'].status)">
                <input tabindex="{{24 * (i + 1)}}" maxlength="50" formControlName="relationship" class="field-control"
                  (input)="getRelationship($event.target.value,i)"
                  [ngStyle]="changeStyle(this.maskRel,passenger.controls['relationship'].status)"
                  (focus)="this.maskRel=false;"
                  (focusout)="this.maskRel=getFocusOutInput(passenger.controls['employeeType'].value,passenger.controls['relationship'].value,this.maskRel);getRelationship($event.target.value,i)"
                  type="text" placeholder="{{'personal.Relationship' | translate}}" />
                <div
                  *ngIf="passenger.get('relationship').hasError('pattern') && (passenger.get('relationship').touched || passenger.get('relationship').dirty)"
                  class="error">
                  {{'personal.PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed' |
                  translate}}</div>
              
                <div *ngIf="passenger.get('relationship').hasError('minLengthArray')" class="error">{{'personal.Minimum2lettersrequiredforfirstname.' | translate}}</div>
              </div>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
              <div class="field-input">
                <div class="flex-wrapper">
                  <ng-select id="dialCode1" #dialCode1 (change)="onCountrySelected1($event,i)"
                    (click)="isdropDownOpen1(passenger.controls['employeeType'].value,this.maskDial1)"
                    (close)="closeDropdown1(passenger.controls['employeeType'].value,passenger.get('dialCode').value,this.maskDial1)"
                    tabindex="{{9 * (i + 1)}}" class="phone-number"
                    [ngStyle]="{'border': passenger.controls['dialCode1'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                    (click)="showToastr(passenger.controls['dialCode1'].status)" dropdownPosition="bottom"
                    [items]="countries" formControlName="dialCode1" bindValue="dial_code"
                    [searchFn]="searchByNameOrCode">
                    <ng-template ng-header-tmp>
                      <div class="selectox-header">
                        <span>{{'personal.CountryName' | translate}}</span>
                      </div>
                    </ng-template>
                    <ng-template ng-label-tmp let-item="item">
                      <img class="flag-label"
                        src="assets/flags/{{getCountryCode(passenger.controls['dialCode1'].value,passenger.controls['contactNumber'].value)}}.png" />
                      <span style="padding-top:5px;"
                        [ngStyle]="{'font-family': this.maskDial ? 'password':'ApercuProMono'}">{{getSelecteddropdownValue(passenger.controls['employeeType'].value,passenger.controls['dialCode1'].value,this.maskDial)}}</span>
                    </ng-template>
                    <ng-template ng-option-tmp let-item="item">
                      <span class="country-name" title="{{item.name}}">{{item.name}}</span>
                      <img class="icon-country-flag" src="assets/flags/{{item.code | lowercase}}.png" />
                    </ng-template>
                  </ng-select>
                  <div class="flex-fill" (click)="showToastr(passenger.controls['contactNumber'].status)">
                    <input tabindex="{{25 * (i + 1)}}" type="tel"
                      mask="{{getPhoneNumberMask(passenger.controls['dialCode1'].value)}}" class="field-control"
                      (focus)="this.maskPhone1=false;"
                      [ngStyle]="changeStyle(this.maskPhone1,passenger.controls['contactNumber'].status)"
                      (focusout)="this.maskPhone1=getFocusOutInput(passenger.controls['employeeType'].value,passenger.get('contactNumber').value,this.maskPhone1);getphoneNumber($event.target.value,i)"
                      (input)="getphoneNumber($event.target.value,i)"
                      placeholder="{{getPhoneNumberPaceHolder(passenger.controls['dialCode1'].value)}}"
                      formControlName="contactNumber" />


                  </div>
                  <!-- <int-phone-prefix [locale]="'en'" [defaultCountry]="'us'" formControlName="phoneNumber"></int-phone-prefix> -->
                </div>
             
                <span
                  *ngIf="passenger.get('contactNumber').hasError('pattern') && (passenger.get('contactNumber').touched || passenger.get('contactNumber').dirty)"
                  class="error"
                  style="float:right;width:50% !important;margin-left: 0px !important;white-space: break-spaces !important;">{{'personal.Pleaseenteravalidphonenumber'
                  | translate}}
                </span>
                
              </div>


            </div>
          </div>
        </div>
      </accordion-group>
    </accordion>
  </ng-container>
  <!-- </div> -->
</form>