
import { Component, HostListener, Input, OnInit, Output, EventEmitter, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import {
  AbstractControl, AsyncValidatorFn, FormArray,FormBuilder, FormControl, FormGroup, ValidationErrors, Validator, ValidatorFn,
  Validators,
} from '@angular/forms';
import { Constants } from '../../util/constants';
import { Observable, Subscription, fromEvent } from 'rxjs';
import { Router, ActivatedRoute } from '@angular/router';
import { countries } from '../../util/countries';
import { DeviceDetailsService } from '../../device-details.service';
import { DateUtils } from '../../util/date-utils';
import { GeoLocationService } from '../../geo-location.service';
import { catchError, map, debounceTime, take } from 'rxjs/operators';
import { BookingService } from '../../booking.service';
import { UserAccountInfo } from '../../entity/user-account-info';
import { UserAccountService } from '../../user-account.service';
import { FrequentFlyerInfo } from '../../entity/frequent-flyer-info';
import { isDate } from 'util';
import { FlightSearchOptions } from 'src/app/entity/email-flow/flight-search-options';
import { deserialize } from 'src/app/util/ta-json/src/methods/deserialize';
import { FlightSearchRequest } from 'src/app/entity/flight-search-request';
import { DatePipe } from '@angular/common';
import { GallopLocalStorageService } from 'src/app/gallop-local-storage.service';
import { HotelSearchRequest } from 'src/app/entity/hotel-search-request';
import { UserInfo } from 'src/app/entity/user-info';
import { EmailQuoteOptionsService } from 'src/app/email-quote-options.service';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtils } from 'src/app/util/common-utils';
import { SearchService } from 'src/app/search.service';
import { environment } from 'src/environments/environment';
import { AdminPanelService } from 'src/app/admin-panel.service';
import { eventNames } from 'cluster';
import { NgSelectComponent } from '@ng-select/ng-select';
import { ItemsList } from '@ng-select/ng-select/lib/items-list';
import { PassportDTO } from 'src/app/entity/passport-dto';
import { AddressDTO } from 'src/app/entity/address-dto';
import { UserPreferences } from 'src/app/entity/user-prefs';
import { ToastrService } from 'ngx-toastr';
import { DeleteCardModelComponent } from '../delete-card-model/delete-card-model.component';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { HotelQueryParam } from 'src/app/entity/hotel-query-param';
import { datepickerAnimation } from 'ngx-bootstrap/datepicker/datepicker-animations';
import { EmailSearchV4DTO } from 'src/app/entity/email-flow/email-searchv4-dto';
declare var animateScrollToTravellerDetails: any;
declare var selectionCompleted: any;
declare var getTraflaHotelChainCode: any;
declare var toggleAccordionClose: any;
declare var toggleAccordion: any
declare var processQuotationResponseInternal: any;
@Component({
    selector: 'personal-details',
    templateUrl: './personal-details.component.html',
    styleUrls: ['./personal-details.component.scss'],
    standalone: false
})
export class PersonalDetailsComponent implements OnInit {
  uniqueAirlines = [];
  uniqueHotels = [];
  uniqueCars = [];
  @Input() noOfPassengers: number;
  @Input() emailQuotOptions: EmailSearchV4DTO;
  isPassportRequired: boolean;
  isZipCodeRequired: boolean;
  bsModalRef: BsModalRef;
  countries = countries;
  countriesName = countries;
  hotelAdultCount = 0;
  pageMode = '';
  disableEmail = false;
  dropDownopen = false;
  maskDob = false;
  maskAdd = false;
  maskPhone = false;
  maskZip = false;
  maskKtn = false;
  maskCln = false;
  maskDial = false;
  maskHln = false;
  isDatePickerReadonly =false
  maskPexp = false;
  maskFfn = false;
  maskPnum = false;
  maskPnat = false;
  maskPcon = false;
  maskEmeNnname = false;
  maskRel = false;
  maskDial1 = false;
  maskPhone1 = false;
  employeeList = [];
  allEmployeeList =[];
  travellerPreFillData = null;
  accordionOpen = []
  isNjoySpecificRelease: boolean;
  hotelTravellerData = [];
  hotelRoomIdArray = [];
  employeeOptions: Array<any>[];
  originalEmployeeOptions: Array<any>[];
  carBooking = true;
  @Output() goToPaymentRequest = new EventEmitter();
  @Output() emergencyContact = new EventEmitter();
  @Output() enablePaymentRequest = new EventEmitter<boolean>();


  public passengersForm: FormGroup;
  maxDoBDate: Date = new Date();
  minPassportDate: Date = new Date();

  selectedFlightSubscription: Subscription;

  fetchAccountInfoSubscription: Subscription;
  fetchSavedBookingDetailsSubscription: Subscription;
  listEmployeesResponseSubscription: Subscription;

  titleOptions = [
    { 'value': 'Select', 'titleId': '' },
    { 'value': 'Mr', 'titleId': 'MR' },
    { 'value': 'Mrs', 'titleId': 'MRS' },
    { 'value': 'Ms', 'titleId': 'MS' },
    { 'value': 'Miss', 'titleId': 'MISS' },
    { 'value': 'Master', 'titleId': 'MASTER' },
    { 'value': 'Lord', 'titleId': 'LORD' },
    { 'value': 'Infant', 'titleId': 'INF' },
    { 'value': 'Lady', 'titleId': 'LADY' },
    { 'value': 'Doctor', 'titleId': 'DR' }
  ];
  @ViewChild('accordion') accordion: ElementRef;
  @ViewChild('employeeType') ngselect: NgSelectComponent;
  @ViewChild('dialCode') ngselect1: NgSelectComponent;
  @ViewChild('pCountry') ngselect2: NgSelectComponent;
  @ViewChild('pNational') ngselect3: NgSelectComponent;



  dialCode = "+1";
  isMobile: boolean;

  deviceSubscription: Subscription;


  constructor(private fb: FormBuilder,
    public router: Router,
    private el: ElementRef,
    private toastr: ToastrService,
    private modalService: BsModalService,
    private datePipe: DatePipe,
    private adminPanelService: AdminPanelService,
    public translateService: TranslateService,
    private activatedRoute: ActivatedRoute,
    private gallopLocalStorage: GallopLocalStorageService,
    private bookingService: BookingService,
    private emailBookingFlowService: EmailQuoteOptionsService,
    private deviceDetailsService: DeviceDetailsService,
    private userAccountInfoService: UserAccountService,
    private searchService: SearchService,
    private translate: TranslateService,
    private geoLocationService: GeoLocationService,) {
  }
  sortCountries(data) {
    data.sort(function (a, b) {
      if (a.nationality.toLowerCase() < b.nationality.toLowerCase()) { return -1; }
      if (a.nationality.toLowerCase() > b.nationality.toLowerCase()) { return 1; }
      return 0;
    })
    return data;
  }
  sortCountriesByName(data) {
    data.sort(function (a, b) {
      if (a.name.toLowerCase() < b.name.toLowerCase()) { return -1; }
      if (a.name.toLowerCase() > b.name.toLowerCase()) { return 1; }
      return 0;
    })
    return data;
  }
  ngOnInit() {
    this.hotelAdultCount = 0;
    
    if(this.countries && this.countries.length>0){
      this.countries =JSON.parse(JSON.stringify(this.sortCountries(this.countries)));
      }
      if(this.countriesName && this.countriesName.length>0){
        this.countriesName =JSON.parse(JSON.stringify(this.sortCountriesByName(this.countriesName)));
        }
    this.isNjoySpecificRelease = this.userAccountInfoService.isItNjoyBuild();
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
      if(isMobile){
        this.isDatePickerReadonly=true;
      }
    });
    this.subscribeEvents();

    this.activatedRoute.queryParams.subscribe(params => {
      if (params['travellerData']) {
        this.hotelTravellerData = this.buildHotelPassengerTitles(JSON.parse(params["travellerData"]));
        this.hotelRoomIdArray = this.buildHotelRoomIdArray(JSON.parse(params["travellerData"]));
      } else {
        let flightPassengersData: FlightSearchOptions = new FlightSearchOptions();
        if (this.gallopLocalStorage.getItem('flightSearchRequestForBooking') && this.gallopLocalStorage.getItem('flightSearchRequestForBooking') !== null) {
          let flightSearchReq: FlightSearchRequest = deserialize(JSON.parse(this.gallopLocalStorage.getItem('flightSearchRequestForBooking')));

          flightPassengersData.infantCount = flightSearchReq.travellers.infants;
          flightPassengersData.adultCount = flightSearchReq.travellers.adults;
          flightPassengersData.childCount = flightSearchReq.travellers.children;
          this.noOfPassengers = flightPassengersData.infantCount + flightPassengersData.adultCount
            + flightPassengersData.childCount;
          this.hotelTravellerData = this.buildFlightPassengerTypeMap(flightPassengersData);
        } else if (this.gallopLocalStorage.getItem('hotelSearchRequestForBooking') && this.gallopLocalStorage.getItem('hotelSearchRequestForBooking') !== null) {
          let hotelSearchReq: HotelSearchRequest = deserialize(JSON.parse(this.gallopLocalStorage.getItem('hotelSearchRequestForBooking')));
          flightPassengersData.infantCount = 0;
          flightPassengersData.adultCount = Number.parseInt(hotelSearchReq.totalTravellers);
          this.hotelAdultCount = Number.parseInt(hotelSearchReq.totalTravellers);
          flightPassengersData.childCount = 0;
          this.noOfPassengers = flightPassengersData.infantCount + flightPassengersData.adultCount
            + flightPassengersData.childCount;
          this.hotelTravellerData = this.buildFlightPassengerTypeMap(flightPassengersData);
          this.hotelRoomIdArray = this.buildHotelRoomIdArray(hotelSearchReq.travellerRooms);
        }
      }
    });
   
    if (this.searchService.employeeEmail.length > 0) {
      let travellerPreFillData = new Array(this.searchService.employeeEmail.length).fill(null).map(_ => []);
      let userAccountInfoObj: any = this.userAccountInfoService.getAccountInfo();
      let userid = this.userAccountInfoService.getUserEmail();
      this.createformFromOta();
      for (let i = 0; i < this.searchService.employeeEmail.length; i++) {
        if (this.searchService.employeeEmail[i].email === userid) {
          let travellerData = this.getPrimaryTravellerInfo(userAccountInfoObj);
          travellerPreFillData[i].push(travellerData['passengers'][0]);
          this.addPassenger(i, travellerData['passengers'][0], 0);
        } else if (this.searchService.employeeEmail[i].email === "GUEST") {
          this.forGuesttTravlerForm(i, 1);
        } else {
          let employee;
          employee = this.searchService.employeeList.filter(item => item.email === this.searchService.employeeEmail[i].email);
          let travellerData = this.getTravellerInfo(employee[0]);
          travellerPreFillData[i].push(travellerData);
          if (employee && employee[0] && employee[0].employeeInfo) {
            this.addPassenger(i, travellerData, employee[0].employeeInfo.employeeId);
          } else {
            let travellerData = this.getPrimaryTravellerInfo(userAccountInfoObj);
            travellerPreFillData[i].push(travellerData['passengers'][0]);
            this.addPassenger(i, travellerData['passengers'][0], 0);
          }
        }
        this.Opened(i)
      }
    } else {
      this.createForm();
    }

  }


  private applyChangesInternal() {
    
    
    let userAccountInfoObj: any = this.userAccountInfoService.getAccountInfo();
    let userid = this.userAccountInfoService.getUserEmail();
    let storagePassangerData: any = this.gallopLocalStorage.getItem('passengersFormData');
    if (storagePassangerData && (storagePassangerData != null)) {
      this.travellerPreFillData = JSON.parse(storagePassangerData);
      this.disableEmail = true;
      for (let i = 0; i < this.accordionOpen.length - 1; i++) {
        this.accordionOpen[i] = false;
      }
      this.accordionOpen[this.accordionOpen.length - 1] = true;
      let userid = this.userAccountInfoService.getUserEmail();
      let index = 0;
      for (let item of this.travellerPreFillData['passengers']) {
        let employeetype1 = item;
        if (employeetype1.email === userid) {
          if (this.uniqueAirlines.length > 0) {
            if (userAccountInfoObj && userAccountInfoObj.userInfo) {
              let ffnData = this.getAirLoyalitynumber(userAccountInfoObj.userInfo)
              this.travellerPreFillData.passengers[index].frequentFlyerInfo = ffnData;
            }
          }

          if (this.uniqueHotels.length > 0) {
            if (userAccountInfoObj && userAccountInfoObj.userInfo) {
              let hlnData = this.getLoyalitynumber(userAccountInfoObj.userInfo)
              this.travellerPreFillData.passengers[index].hotelLoyalityInfo = hlnData;
            }
          }


          if (this.uniqueCars.length > 0) {
            if (userAccountInfoObj && userAccountInfoObj.userInfo) {
              let clnData = this.getCarLoyalityNumbers(userAccountInfoObj.userInfo)
              this.travellerPreFillData.passengers[index].carLoyalityInfo = clnData;
            }
          }
          if (!this.userAccountInfoService.doNotSaveProfile) {
            this.travellerPreFillData['passengers'][0] = this.removeSavedvalueFromForm(employeetype1);
          }
        } else {
          let employee;
          employee = this.searchService.employeeList.filter(item1 => item1.email === item.email);
          if (employee && employee.length > 0) {
            if (this.uniqueAirlines.length > 0) {
              let ffnData = this.getAirLoyalitynumber(employee[0])
              this.travellerPreFillData.passengers[index].frequentFlyerInfo = ffnData;
            }

            if (this.uniqueHotels.length > 0) {
              let hlnData = this.getLoyalitynumber(employee[0])
              this.travellerPreFillData.passengers[index].hotelLoyalityInfo = hlnData;
            }


            if (this.uniqueCars.length > 0) {
              let clnData = this.getCarLoyalityNumbers(employee[0])
              this.travellerPreFillData.passengers[index].carLoyalityInfo = clnData;
            }
          } else {
            if (this.uniqueAirlines.length > 0) {
              let ffnData = this.getAirLoyalitynumber(employeetype1)
              this.travellerPreFillData.passengers[index].frequentFlyerInfo = ffnData;
            }

            if (this.uniqueHotels.length > 0) {
              let hlnData = this.getLoyalitynumber(employeetype1)
              this.travellerPreFillData.passengers[index].hotelLoyalityInfo = hlnData;
            }


            if (this.uniqueCars.length > 0) {
              let clnData = this.getCarLoyalityNumbers(employeetype1)
              this.travellerPreFillData.passengers[index].carLoyalityInfo = clnData;
            }
          }
        }
        index++;
      }
      this.createForm();
    }
    else {
      if (this.searchService.employeeEmail.length > 0) {
        let travellerPreFillData = new Array(this.searchService.employeeEmail.length).fill(null).map(_ => []);
        this.createformFromOta();
        for (let i = 0; i < this.searchService.employeeEmail.length; i++) {
          if (this.searchService.employeeEmail[i].email === userid) {
            let travellerData = this.getPrimaryTravellerInfo(userAccountInfoObj);
            travellerPreFillData[i].push(travellerData['passengers'][0]);
            this.addPassenger(i, travellerData['passengers'][0], 0);
          } else if (this.searchService.employeeEmail[i].id === "GUEST") {
            this.forGuesttTravlerForm(i, 1);
          } else {
            let employee;
            employee = this.searchService.employeeList.filter(item => item.email === this.searchService.employeeEmail[i].email);
            if(employee && employee[0]){
            let travellerData = this.getTravellerInfo(employee[0]);
            travellerPreFillData[i].push(travellerData);
            if (employee && employee[0] && employee[0].employeeInfo) {
              this.addPassenger(i, travellerData, employee[0].employeeInfo.employeeId);
            } else {
              let travellerData = this.getPrimaryTravellerInfo(userAccountInfoObj);
              travellerPreFillData[i].push(travellerData['passengers'][0]);
              this.addPassenger(i, travellerData['passengers'][0], 0);
            }
            }else{
              this.forGuesttTravlerForm(i, 1,this.searchService.employeeEmail[i]);
            //  this.searchService.employeeEmail[i].email ='GUEST';
            }
          }
          this.Opened(i)
        }
      } else {
        this.travellerPreFillData = this.getPrimaryTravellerInfo(userAccountInfoObj);
        this.createForm();
      }
    }

  }
  removeSavedvalueFromForm(item) {
    if (!item['ktnAvailable']) {
      item['knownTravellerNumber'] = "";
    }
    if (item.frequentFlyerInfo && item.frequentFlyerInfo.length > 0) {
      for (let i = 0; i < item.ffnMapping; i++) {
        if (!item.ffnMapping[i].ffnAvailable) {
          item.ffnMapping.splice(i, 1);
        }
      }
    }
    if (item.carLoyalityInfo && item.carLoyalityInfo.length > 0) {
      for (let i = 0; i < item.carLoyalityInfo; i++) {
        if (!item.carLoyalityInfo[i].clnAvailable) {
          item.carLoyalityInfo.splice(i, 1);
        }
      }
    }
    return item;
  }
  forGuesttTravlerForm(i, event,item?) {
    let newPassengerForm: any = {
      employeeType: 1,
      title: null,
      firstName: null,
      middleName: null,
      lastName: null,
      gender: null,
      dateOfBirth: null,
      email: (item && item.email && item.email!=='GUEST') ? item.email:null,
      phoneNumber: null,
      address: null,
      zipCode: null,
      ktnAvailable: [false],
      knownTravellerNumber: "",
      dialCode: this.setDialCode(this.userAccountInfoService.getAccountInfo()),
      dialCode1: this.setDialCode(this.userAccountInfoService.getAccountInfo()),
      passportCountry: null,
      passportNationality: null,
      passportExpiryDate: null,
      seat: null,
      openFormOnUI: [false],
      frequentFlyerInfo: [],
      hotelLoyalityInfo: [],
      carLoyalityInfo: [],
      roomId: "1",
    };
    for (let airlineObj of this.uniqueAirlines) {
      let ffnNumber = undefined;
      if (newPassengerForm.ffnMapping) {
        let filteredObj: any = newPassengerForm.ffnMapping.find(x => x.airline_code === airlineObj['code']);
        if (filteredObj) {
          ffnNumber = filteredObj.frequent_flyer_number.trim();
        }
      }
      let ffnData = {
        "frequent_flyer_number": ffnNumber ? ffnNumber : null,
        "airlineCode": airlineObj['code'],
        "name": airlineObj['name'],
        "ffnAvailable": true
      }
      newPassengerForm.frequentFlyerInfo.push(ffnData);
    }


    for (let hotelObj of this.uniqueHotels) {
      let hlnNumber = "";
      let hotelChainCode = hotelObj['traflaChainCode'] ? hotelObj['traflaChainCode'] : hotelObj['code']
      if (newPassengerForm.loyalityCards) {
        let filteredObj: any = newPassengerForm.loyalityCards.find(x => x.chain === getTraflaHotelChainCode(hotelChainCode));
        if (filteredObj && filteredObj.number) {
          hlnNumber = filteredObj.number.trim();
        } else {
          filteredObj = newPassengerForm.loyalityCards.find(x => x.hotel_name === hotelObj['name']);
          if (filteredObj && filteredObj.number) {
            hlnNumber = filteredObj.number.trim();
          }
        }
      }
      let hlnData = {
        "hotel_loyality_number": hlnNumber ? hlnNumber : null,
        "hotelCode": hotelChainCode,
        "name": hotelObj['name'],
        "hlnAvailable": true,
        'handler': hotelObj['handler'],
        'loyaltyPointsSupported': hotelObj['loyaltyPointsSupported']

      }
      if (hlnData.loyaltyPointsSupported) {
        newPassengerForm.hotelLoyalityInfo.push(hlnData);
      }
    }
    for (let carObj of this.uniqueCars) {
      let clnNumber = "";
      if (newPassengerForm.carLoyaltyNumbers) {
        let filteredObj: any = newPassengerForm.carLoyaltyNumbers.find(x => x.traflaPartnerCode === carObj['traflaPartnerCode']);
        if (filteredObj && filteredObj.rentalCarLoyaltyNumber) {
          clnNumber = filteredObj.rentalCarLoyaltyNumber.trim();
        } else {
          filteredObj = newPassengerForm.carLoyaltyNumbers.find(x => x.rentalCarName === carObj['partnerName']);
          if (filteredObj && filteredObj.rentalCarLoyaltyNumber) {
            clnNumber = filteredObj.rentalCarLoyaltyNumber.trim();
          }
        }
      }
      let clnData = {
        "rentalCarLoyaltyNumber": clnNumber ? clnNumber : null,
        "rentalCarCode": carObj['traflaPartnerCode'],
        "rentalCarName": carObj['partnerName'],
        "clnAvailable": true,
      }
      newPassengerForm.carLoyalityInfo.push(clnData);
    }
    this.addPassenger(i, newPassengerForm, event);
  }
  filteredTravellers() {
    for (let k = 0; k < this.employeeOptions.length; k++) {
      this.employeeOptions[k] = [...this.originalEmployeeOptions[k]]
    }
    for (let j = 0; j < this.passengersForm.controls['passengers'].value.length; j++) {
      let travellerChange = this.passengersForm.controls['passengers'].value[j];
      if (travellerChange['employeeType'] !== 0 && travellerChange['employeeType'] !== 1) {
        for (let k = 0; k < this.employeeOptions.length; k++) {
          let travellerChange1 = this.passengersForm.controls['passengers'].value[k]
          if (travellerChange1 && travellerChange1['employeeType'] !== travellerChange['employeeType']) {
            this.employeeOptions[k] = this.employeeOptions[k].filter(item => item.Id !== travellerChange['employeeType']);
          } else if (!travellerChange1) {
            this.employeeOptions[k] = this.employeeOptions[k].filter(item => item.Id !== travellerChange['employeeType']);
          }

        }
      }
    }
  }
  showEmployeeTypeChanged(event, i) {
    let userAccountInfoObj: any = this.userAccountInfoService.getAccountInfo();
    this.dropDownopen = false;
    let employee;
    // this.filteredTravellers();
    if (event !== 0 && event !== 1) {
      employee = this.employeeList.filter(item => item.employeeInfo.employeeId === event);
    }
    if (event === 0) {
      let travellerData = this.getPrimaryTravellerInfo(userAccountInfoObj);
      this.updatePassenger(i, travellerData['passengers'][0], event);
    }
    // this.updatePassenger(i,travellerData,event);
    // this.addPassenger(i,travellerData);


  }
  saveProfileButton=false;
  saveProfileData(i) {
    let equal3;
    let  travellerChange;
    if(i){
    travellerChange = JSON.parse(JSON.stringify(this.passengersForm.controls['passengers'].value[i]));
    }else{
      travellerChange = JSON.parse(JSON.stringify(this.passengersForm.controls['passengers'].value[0]));
    }
    let userInfoDTO = this.userAccountInfoService.getAccountInfo().userInfo;
    if(this.saveProfileButton){
    this.isPassengerFormValidate(i);
     }
    let   formGroup = <FormGroup>(<FormArray>this.passengersForm.controls['passengers']).at(i);
    if(formGroup.invalid){

   return;
    }
    if (travellerChange['employeeType'] === 0 && !this.userAccountInfoService.promptUserTosaveProfile) {
      
      let userAccountInfoObj: any = this.userAccountInfoService.getAccountInfo();
      let travellerData = this.getPrimaryTravellerInfo(userAccountInfoObj);
      let travellerData1 = JSON.parse(JSON.stringify(travellerData['passengers'][0]));
      if ((travellerChange.frequentFlyerInfo && travellerChange.frequentFlyerInfo.length > 0)) {
        for (let i = 0; i < travellerChange.frequentFlyerInfo.length; i++) {
          if (travellerData1.frequentFlyerInfo && travellerData1.frequentFlyerInfo.length > 0) {
            for (let j = 0; j < travellerData1.frequentFlyerInfo.length; j++) {
              if (travellerChange.frequentFlyerInfo[i].airlineCode === travellerData1.frequentFlyerInfo[j].airlineCode && !travellerChange.frequentFlyerInfo[i].ffnAvailable) {
                delete travellerChange.frequentFlyerInfo[i].ffnAvailable;
                delete travellerData1.frequentFlyerInfo[j].ffnAvailable;
              }
            }
          }
        }

      }
      if ((travellerChange.carLoyalityInfo && travellerChange.carLoyalityInfo.length > 0)) {
        for (let i = 0; i < travellerChange.carLoyalityInfo.length; i++) {
          if (travellerData1.carLoyalityInfo && travellerData1.carLoyalityInfo.length > 0) {
            for (let j = 0; j < travellerData1.carLoyalityInfo.length; j++) {
              if (travellerChange.carLoyalityInfo[i].rentalCarCode === travellerData1.carLoyalityInfo[j].rentalCarCode && !travellerChange.carLoyalityInfo[i].clnAvailable) {
                delete travellerChange.carLoyalityInfo[i].clnAvailable;
                delete travellerData1.carLoyalityInfo[j].clnAvailable;
              }
            }
          }
        }

      }
      if ((travellerChange.hotelLoyalityInfo && travellerChange.hotelLoyalityInfo.length > 0)) {
        for (let i = 0; i < travellerChange.hotelLoyalityInfo.length; i++) {
          if (travellerData1.hotelLoyalityInfo && travellerData1.hotelLoyalityInfo.length > 0) {
            for (let j = 0; j < travellerData1.hotelLoyalityInfo.length; j++) {
              if (travellerChange.hotelLoyalityInfo[i].loyaltyPointsSupported && travellerChange.hotelLoyalityInfo[i].hotelCode === travellerData1.hotelLoyalityInfo[j].hotelCode && !travellerChange.hotelLoyalityInfo[i].hlnAvailable) {
                travellerChange.hotelLoyalityInfo[i].hotel_loyality_number = null;
                delete travellerChange.hotelLoyalityInfo[i].hlnAvailable;
                delete travellerData1.hotelLoyalityInfo[j].hlnAvailable;
              }
            }
          }
        }

      }
      travellerData1['employeeType'] = 0;
      if (!travellerChange['ktnAvailable']) {
        travellerChange['knownTravellerNumber'] = "";
      }
      if(travellerChange['roomId']){
        delete travellerChange['roomId'];
        delete travellerData1['roomId'];
     }   
      delete travellerData1['ktnAvailable'];
      delete travellerChange['ktnAvailable'];
      if(travellerChange['hotelLoyalityInfo'] && travellerChange['hotelLoyalityInfo'].length > 0 && !travellerChange['hotelLoyalityInfo'][0].loyaltyPointsSupported){
        delete travellerData1['hotelLoyalityInfo'];
        delete travellerChange['hotelLoyalityInfo'];
        }
        if(!travellerChange['hotelLoyalityInfo']  && travellerData1['hotelLoyalityInfo'] && travellerData1['hotelLoyalityInfo'].length === 0){
          delete travellerData1['hotelLoyalityInfo'];
          delete travellerChange['hotelLoyalityInfo'];
        }
      delete travellerData1['zipCode'];
      delete travellerChange['zipCode'];
      if (!travellerData1['gender'] && travellerChange['gender'] === '') {
        travellerChange['gender'] = null;
      }
      if (travellerData1['title'] === '' && !travellerChange['title']) {
        travellerChange['title'] = '';
      }
      if (!travellerData1['emergencyContactName'] && travellerChange['emergencyContactName'] === '') {
        travellerChange['emergencyContactName'] = null;
      }
      if (!travellerData1['contactNumber'] && travellerChange['contactNumber'] === '') {
        travellerChange['contactNumber'] = null;
      }
      if (!travellerData1['relationship'] && travellerChange['relationship'] === '') {
        travellerChange['relationship'] = null;
      }
      equal3 = this.bookingService.equals(travellerChange, travellerData1);
     
this.saveProfileButton=false;
      if (!equal3) {
        this.disabledUserEntry((this.passengersForm.get('passengers') as FormArray).at(0));
        this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
          initialState: {
            title: this.translateService.instant('emailBookingFlow.Youseemtohaveaddedorupdatedsomeinformation.Wouldyouliketosaveitto')+" "+ userInfoDTO.email + " " + this.translateService.instant('emailBookingFlow.profile?'),
            message: '',
            yesButtonSubText: this.translateService.instant('emailBookingFlow.YES'),
            noButtonSubText: this.translateService.instant('emailBookingFlow.NO'),
            userInfoDTO: userInfoDTO,
            passengerDetails: travellerChange,
            color: 'black',
            buttonColor: '#008080',
            profile: true,
            others:false,
          }, backdrop: true, keyboard: false, ignoreBackdropClick: true
        });
      }

    }else if((this.userAccountInfoService.isUserCorporateAdmin() || this.userAccountInfoService.isUserIsTravelManager()) && (travellerChange['employeeType'] !== 0 && this.searchService.employeeEmail[i].email !== "GUEST")){
      let travellerChange = JSON.parse(JSON.stringify(this.passengersForm.controls['passengers'].value[i]));
      let employeeInfoDTO = this.searchService.employeeList.filter(item =>item.email ===travellerChange['email']);
      if(employeeInfoDTO && employeeInfoDTO[0]){
      let travellerData = this.getTravellerInfo(employeeInfoDTO[0]);
      let travellerData1 = JSON.parse(JSON.stringify(travellerData));
      if ((travellerChange.frequentFlyerInfo && travellerChange.frequentFlyerInfo.length > 0)) {
        for (let i = 0; i < travellerChange.frequentFlyerInfo.length; i++) {
          if (travellerData1.frequentFlyerInfo && travellerData1.frequentFlyerInfo.length > 0) {
            for (let j = 0; j < travellerData1.frequentFlyerInfo.length; j++) {
              if (travellerChange.frequentFlyerInfo[i].airlineCode === travellerData1.frequentFlyerInfo[j].airlineCode && !travellerChange.frequentFlyerInfo[i].ffnAvailable) {
                travellerChange.frequentFlyerInfo[i].frequent_flyer_number = null;
                delete travellerChange.frequentFlyerInfo[i].ffnAvailable;
                delete travellerData1.frequentFlyerInfo[j].ffnAvailable;
              }
            }
          }
        }

      }
      if ((travellerChange.carLoyalityInfo && travellerChange.carLoyalityInfo.length > 0)) {
        for (let i = 0; i < travellerChange.carLoyalityInfo.length; i++) {
          if (travellerData1.carLoyalityInfo && travellerData1.carLoyalityInfo.length > 0) {
            for (let j = 0; j < travellerData1.carLoyalityInfo.length; j++) {
              if (travellerChange.carLoyalityInfo[i].rentalCarCode === travellerData1.carLoyalityInfo[j].rentalCarCode && !travellerChange.carLoyalityInfo[i].clnAvailable) {
                travellerChange.carLoyalityInfo[i].rentalCarLoyaltyNumber = null;
                delete travellerChange.carLoyalityInfo[i].clnAvailable;
                delete travellerData1.carLoyalityInfo[j].clnAvailable;
              }
            }
          }
        }

      }
      if ((travellerChange.hotelLoyalityInfo && travellerChange.hotelLoyalityInfo.length > 0)) {
        for (let i = 0; i < travellerChange.hotelLoyalityInfo.length; i++) {
          if (travellerData1.hotelLoyalityInfo && travellerData1.hotelLoyalityInfo.length > 0) {
            for (let j = 0; j < travellerData1.hotelLoyalityInfo.length; j++) {
              if (travellerChange.hotelLoyalityInfo[i].loyaltyPointsSupported && travellerChange.hotelLoyalityInfo[i].hotelCode === travellerData1.hotelLoyalityInfo[j].hotelCode && !travellerChange.hotelLoyalityInfo[i].hlnAvailable) {
                travellerChange.hotelLoyalityInfo[i].hotel_loyality_number = null;
                delete travellerChange.hotelLoyalityInfo[i].hlnAvailable;
                delete travellerData1.hotelLoyalityInfo[j].hlnAvailable;
              }
            }
          }
        }

      }
    //  travellerData1['employeeType'] =employeeInfoDTO[0].employeeInfo.employeeId;
      if (!travellerChange['ktnAvailable']) {
        travellerChange['knownTravellerNumber'] = "";
      }
      delete travellerData1['ktnAvailable'];
      delete travellerData1['employeeType'];
      delete travellerChange['employeeType'];
      delete travellerChange['ktnAvailable'];
      if(travellerChange['hotelLoyalityInfo'] && travellerChange['hotelLoyalityInfo'].length > 0 && !travellerChange['hotelLoyalityInfo'][0].loyaltyPointsSupported){
        delete travellerData1['hotelLoyalityInfo'];
        delete travellerChange['hotelLoyalityInfo'];
        }
        if(!travellerChange['hotelLoyalityInfo']  && travellerData1['hotelLoyalityInfo'] && travellerData1['hotelLoyalityInfo'].length === 0){
          delete travellerData1['hotelLoyalityInfo'];
          delete travellerChange['hotelLoyalityInfo'];
        }
      delete travellerData1['zipCode'];
      delete travellerChange['zipCode'];
      if(travellerChange['roomId']){
        delete travellerChange['roomId'];
        delete travellerData1['roomId'];
     }   
      if (!travellerData1['gender'] && travellerChange['gender'] === '') {
        travellerChange['gender'] = null;
      }
      if (travellerData1['title'] === '' && !travellerChange['title']) {
        travellerChange['title'] = '';
      }
      if (!travellerData1['emergencyContactName'] && travellerChange['emergencyContactName'] === '') {
        travellerChange['emergencyContactName'] = null;
      }
      if (!travellerData1['contactNumber'] && travellerChange['contactNumber'] === '') {
        travellerChange['contactNumber'] = null;
      }
      
      if (!travellerData1['relationship'] && travellerChange['relationship'] === '') {
        travellerChange['relationship'] = null;
      }
      this.saveProfileButton=false;
      equal3 = this.bookingService.equals(travellerChange, travellerData1);
     
      if (!equal3) {
       // this..disabledUserEntry((this.personalDetailChild.passengersForm.get('passengers') as FormArray).at(0));
        this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
          initialState: {
            title: this.translateService.instant('emailBookingFlow.Youseemtohaveaddedorupdatedsomeinformation.Wouldyouliketosaveitto')+" "+ employeeInfoDTO[0].email + " " + this.translateService.instant('emailBookingFlow.profile?'),
            message: '',
            yesButtonSubText: this.translateService.instant('emailBookingFlow.YES'),
            noButtonSubText: this.translateService.instant('emailBookingFlow.NO'),
            userInfoDTO: employeeInfoDTO[0],
            passengerDetails: travellerChange,
            color: 'black',
            buttonColor: '#008080',
            profile: true,
            others:true,
          }, backdrop: true, keyboard: false, ignoreBackdropClick: true
        });
      }
    }
  }

  }
showKtn(){
  let airports = JSON.parse(this.gallopLocalStorage.getItem('airports'));
  let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
  if(selectedFlight && selectedFlight.length>0){
    for(let item of selectedFlight){
      for(let leg of item.legs){
        for(let hop of leg.flightHops){
          let origin = airports[hop.from].country;
          let destination = airports[hop.to].country;
          if(origin==='US' || destination==='US'){
            this.bookingService.showKtn =true;
            break;
          }

        }
      }
    }
  }
}
   setCarFlightHotel(data){
    this.uniqueAirlines = data.airlines;
    this.uniqueHotels = data.hotels;
    this.uniqueCars = data.cars;
   }
  public applyChanges(data: any) {
    this.showKtn();
    this.uniqueAirlines = data.airlines;
    this.uniqueHotels = data.hotels;
    this.uniqueCars = data.cars;
    if (this.uniqueCars && this.uniqueCars.length > 1) {
      (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[0]).controls['carLoyalityInfo']) = undefined;
    }
    if (this.uniqueAirlines.length === 0 && this.uniqueHotels.length === 0 && this.uniqueCars && this.uniqueCars.length > 0) {
      this.carBooking = false;
    } else {
      this.carBooking = true;
    }
    this.isPassportRequired = data.isPassportMandatory;
    this.isZipCodeRequired = data.isZipCodeMandatory;
    this.bookingService.isZipCodeRequired = data.isZipCodeMandatory;
    if (!this.bookingService.priceChange) {
      this.applyChangesInternal();

    }
  }
  sortList(data) {
    data.sort(function (a, b) {
     
      if (a.firstName === "" || !a.firstName) {
        return 1;
      } else if (b.firstName === "" || !b.firstName) {
        return -1;
      } else if (a.firstName < b.firstName) { return -1; }
      else if (a.firstName > b.firstName) { return 1; }
      return 0;
    })
    return data;
  }
  subscribeEvents() {
    this.listEmployeesResponseSubscription = this.adminPanelService.employeeListResponseObservable$.subscribe(response => {
      if (!this.userAccountInfoService.paymentPageSave) {
        if (response) {
          if (response.length > 0) {
            this.employeeList = response;
            this.allEmployeeList = response;
            this.searchService.employeeList = response;
            this.accordionOpen.push(true);
            let sortEmployee = this.sortList(response);
            this.employeeOptions = new Array(this.noOfPassengers).fill(null).map(_ => []);
            this.originalEmployeeOptions = new Array(this.noOfPassengers).fill(null).map(_ => []);
            this.originalEmployeeOptions[0] = [{ Name: 'Myself', Id: 0 }, { Name: 'Guest Traveler', Id: 1 }];
            this.employeeOptions[0] = [{ Name: 'Myself', Id: 0 }, { Name: 'Guest Traveler', Id: 1 }];
            for (let item of sortEmployee) {
              let employee = { Name: '', Id: -1, Type: '' }
              let userid = this.userAccountInfoService.getUserEmail();
              if (userid !== item.email) {
                employee['Name'] = item.firstName + " " + item.lastName;
                employee['Id'] = item.employeeInfo.employeeId;
                employee.Type = "Other Employees"
                this.employeeOptions[0].push(employee);
                this.originalEmployeeOptions[0].push(employee);
              }
            }
            for (let i = 1; i < this.noOfPassengers; i++) {
              this.employeeOptions[i] = [...(this.employeeOptions[0].slice(1))];
              this.originalEmployeeOptions[i] = [...(this.originalEmployeeOptions[0].slice(1))];
              this.accordionOpen.push(false);

            }
          } else {
            this.employeeList = [{ Name: 'Myself', Id: 0 }, { Name: 'Guest Traveler', Id: 1 }];
            this.employeeOptions = new Array(this.noOfPassengers).fill(null).map(_ => []);
            this.accordionOpen.push(true);
            this.originalEmployeeOptions = new Array(this.noOfPassengers).fill(null).map(_ => []);
            this.originalEmployeeOptions[0] = [{ Name: 'Myself', Id: 0 }, { Name: 'Guest Traveler', Id: 1 }];
            this.employeeOptions[0] = [{ Name: 'Myself', Id: 0 }, { Name: 'Guest Traveler', Id: 1 }];
            for (let i = 1; i < this.noOfPassengers; i++) {
              this.employeeOptions[i] = [...(this.employeeOptions[0].slice(1))];
              this.originalEmployeeOptions[i] = [...(this.originalEmployeeOptions[0].slice(1))];
              this.accordionOpen.push(false);

            }
          }
         

        } else {
          this.employeeList = [{ Name: 'Myself', Id: 0 }, { Name: 'Guest Traveler', Id: 1 }];
          this.employeeOptions = new Array(this.noOfPassengers).fill(null).map(_ => []);
          this.accordionOpen.push(true);
          this.originalEmployeeOptions = new Array(this.noOfPassengers).fill(null).map(_ => []);
          this.originalEmployeeOptions[0] = [{ Name: 'Myself', Id: 0 }, { Name: 'Guest Traveler', Id: 1 }];
          this.employeeOptions[0] = [{ Name: 'Myself', Id: 0 }, { Name: 'Guest Traveler', Id: 1 }];
          for (let i = 1; i < this.noOfPassengers; i++) {
            this.employeeOptions[i] = [...(this.employeeOptions[0].slice(1))];
            this.accordionOpen.push(false);
            this.originalEmployeeOptions[i] = [...(this.originalEmployeeOptions[0].slice(1))];

          }
        }
      }
    });
    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      if(userAccountInfoObj && ( !this.allEmployeeList ||  this.allEmployeeList.length == 0)
          &&  (userAccountInfoObj.userInfo.employeeInfo.agentRole !== "BOOK_FOR_SELF"
                || userAccountInfoObj.userInfo.employeeInfo.role !== "EMPLOYEE"
              )
        ){
       
        this.adminPanelService.fetchEmployeesList(userAccountInfoObj.userInfo.companyId);
      }
      this.pageMode = this.activatedRoute.snapshot.queryParams["pageMode"];
      //      if(pageMode && (pageMode === 'WebSearch')) return;
      if (!this.userAccountInfoService.showRadio) {
        for (let i = 0; i < this.noOfPassengers; i++) {
          this.accordionOpen.push(false);

        }
      }
      if (!this.userAccountInfoService.notToRefreshForm) {
        this.applyChangesInternal();
      }
    });


    this.fetchSavedBookingDetailsSubscription = this.emailBookingFlowService.savedBookingDetailsObserver$.subscribe((bookingDetails: any) => {
      if (bookingDetails && bookingDetails.success) {  
        let bookingData: any = JSON.parse(bookingDetails.data);
        let userInfoBookingDetails: UserInfo[] = deserialize(bookingData.userInfo.user_infos);
        if (userInfoBookingDetails.length > 0) {
          let travellerData = {
            "passengers": []
          };
          for (let userInfoItem of userInfoBookingDetails) {
            travellerData.passengers.push(this.getTravellerInfo(userInfoItem));
          }
          if(bookingData && bookingData.userInfo  && bookingData.userInfo.stripe_card_detail){
            let cardDetails: any = JSON.parse(bookingData.userInfo.stripe_card_detail);
            if(this.pageMode === 'emailflowAgent' && cardDetails && cardDetails.payment && cardDetails.payment.paymentMethod  && cardDetails.payment.paymentMethod!=='billToCompany'){
              this.bookingService.addToTransactionDefaultPayment =cardDetails.payment 
            }
          }
          this.gallopLocalStorage.setItem("passengersFormData", JSON.stringify(travellerData));
          this.applyChangesInternal();
        }
        else if(userInfoBookingDetails.length == 0 ){
          this.activatedRoute.queryParams.subscribe(params => {
           var bookedFor = params['bookedFor'];
           if(bookedFor && bookedFor != undefined){
            this.getPassengerDetailFromEmail(bookedFor);
            }
          });  
        }
        
      }
    });
  }
  emailFieldDisabled(email: string, employeeType: number): boolean {
    return employeeType === 0 || employeeType > 1;
  }
  closeOtherAccordion(i) {
    for (let j = 0; j < (this.accordion as any)['groups'].length; j++) {
      if (i !== j) {
        (this.accordion as any)['groups'][i].isOpen= false;
      }
    }
    (this.accordion as any)['groups'][i].isOpen= true;
  }
  closeDropdown(i) {
    if (this.ngselect.isOpen) {
      this.dropDownopen = true;
    } else {
      this.dropDownopen = false;
    }
    //let el = document.getElementById("passenger"+i+"accordion");
    
  }
  closeDropdown1(item, value, item2) {
    if (this.ngselect1.isOpen) {
      this.maskDial = false;
    } else {
      this.maskDial = this.getFocusOutInput(item, value, item2);
    }
  }
  isdropDownOpen() {
    if (this.ngselect.isOpen) {
      this.dropDownopen = true;
    } else {
      this.dropDownopen = false;
    }
  }
  isdropDownOpen1(item, value, item2) {
    if (this.ngselect1.isOpen) {
      this.maskDial = false;
    } else {
      this.maskDial = this.getFocusOutInput(item, value, item2);
    }
  }
  isdropDownOpen2(item, value, item2) {
    if (this.ngselect2.isOpen) {
      this.maskPcon = false;
    } else {
      this.maskPcon = this.getFocusOutInput(item, value, item2);
    }
  }
  closeDropdown2(item, value, item2) {
    if (this.ngselect2.isOpen) {
      this.maskPcon = false;
    } else {
      this.maskPcon = this.getFocusOutInput(item, value, item2);
    }
  }
  isdropDownOpen3(item, value, item2) {
    if (this.ngselect3.isOpen) {
      this.maskPnat = this.getFocusOutInput(item, value, item2);
    } else {
      this.maskPnat = this.getFocusOutInput(item, value, item2);
    }
  }
  closeDropdown3(item, value, item2) {
    if (this.ngselect3.isOpen) {
      this.maskPnat = false;
    } else {
      this.maskPnat = this.getFocusOutInput(item, value, item2);
    }
  }
  showProceedToPaymentButton(): boolean {
    return this.numOfFormPassengers == this.noOfPassengers;
  }

  public updateFormValueInBookingService() {
    this.enabledUserEntry1((this.passengersForm.get('passengers') as FormArray).at(0));
    this.bookingService.passengersFormValue = this.passengersForm.value;
    this.gallopLocalStorage.setItem("passengers", JSON.stringify(this.passengersForm.value));
    let passengersDetails: any = this.passengersForm.value;
    passengersDetails['passengers'].forEach((p) => {
      if (p.dateOfBirth && p.dateOfBirth !== null) {
        p.dateOfBirth = this.datePipe.transform(new Date(p.dateOfBirth), 'MM/dd/yyyy');
      }

      if (p['passportExpiryDate'] && p['passportExpiryDate'] !== null) {
        p.passportExpiryDate = this.datePipe.transform(new Date(p['passportExpiryDate']), 'MM/dd/yyyy');
      }
    });

    this.gallopLocalStorage.setItem("passengersFormData", JSON.stringify(passengersDetails));
  }
hideKtnffnCheckBoxes(i){
  let  travellerChange = JSON.parse(JSON.stringify(this.passengersForm.controls['passengers'].value[i]));
  if(travellerChange['employeeType']!==0 && (this.userAccountInfoService.isUserCorporateAdmin() || this.userAccountInfoService.isUserIsTravelManager())){
    return false;
  }else{
    return true;
  }
}
  setKTNValidator(i?) {
    if (this.uniqueAirlines.length == 0) return;
    let vaidatorRequiredNotNeeded =false;
   
    if(!this.bookingService.showKtn){
      return;
    }
    for (let i = 0; i < (this.numOfFormPassengers); i++) {
      let  travellerChange = JSON.parse(JSON.stringify(this.passengersForm.controls['passengers'].value[i]));
      if(travellerChange['employeeType']!==0 && (this.userAccountInfoService.isUserCorporateAdmin() || this.userAccountInfoService.isUserIsTravelManager())){
        vaidatorRequiredNotNeeded =true;
      }else{
        vaidatorRequiredNotNeeded =false;
      }
      let passenger = this.getFormArrayPassengerObject(i);
      let ktnAvailable = passenger.get('ktnAvailable').value;
      if(!vaidatorRequiredNotNeeded){
      passenger.get('knownTravellerNumber')
        .setValidators(this.conditionalValidators(ktnAvailable,
          [
            Validators.required,
            Validators.pattern(Constants.RGEX_ALPHANUMERIC),
            Validators.minLength(8),
            Validators.maxLength(environment.maxKTNFieldLength)]));
      }else{
        passenger.get('knownTravellerNumber')
          .setValidators(this.conditionalValidators(ktnAvailable,
            [
              Validators.pattern(Constants.RGEX_ALPHANUMERIC),
              Validators.minLength(8),
              Validators.maxLength(environment.maxKTNFieldLength)]));
      }
    }
  }
  setMinLastNameLengthForHotel(i?) {
    let processedOptions: any = processQuotationResponseInternal(this.emailQuotOptions);
    var checkIfHotelPresetOrNot = processedOptions.filter((event) => event.eventType=="hotel");
    if(checkIfHotelPresetOrNot.length == 0 || this.pageMode == 'emailflowAgent'){
      return; 
    }
    for (let i = 0; i < (this.numOfFormPassengers); i++) {
      let passenger = this.getFormArrayPassengerObject(i);
      passenger.get('lastName').setValidators([this.customLastNameValidator]);
    }
  }
  customLastNameValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (value.trim().length === 0) return { required: true };
    const regex = new RegExp(Constants.RGEX_ONLY_ALPHA_AND_SPACE);
    if (!regex.test(value.trim())) { return { pattern: true };}
    if (value.trim().length < 2) return { minlength: true };
    return null;
  }
  setFFNValidator(i?) {
    let vaidatorRequiredNotNeeded =false;
    if(this.noOfPassengers ===1){
      return;
    }
    for (let i = 0; i < (this.numOfFormPassengers); i++) {
      let  travellerChange = JSON.parse(JSON.stringify(this.passengersForm.controls['passengers'].value[i]));
    if(travellerChange['employeeType']!==0 && (this.userAccountInfoService.isUserCorporateAdmin() || this.userAccountInfoService.isUserIsTravelManager())){
      vaidatorRequiredNotNeeded =true;
    }else{
      vaidatorRequiredNotNeeded =false;
    }
      let passenger = this.getFormArrayPassengerObject(i);
      let frequentFlyerInfo = (<FormArray>passenger.get('frequentFlyerInfo'));

      for (let j = 0; j < frequentFlyerInfo.length; j++) {
        let ffnAvailable = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[i]).controls['frequentFlyerInfo']).at(j).get('ffnAvailable').value;
        if(!vaidatorRequiredNotNeeded){
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[i]).controls['frequentFlyerInfo']).at(j).get('frequent_flyer_number').setValidators(this.conditionalValidators(ffnAvailable, [Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
        }else{
          (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[i]).controls['frequentFlyerInfo']).at(j).get('frequent_flyer_number').setValidators(this.conditionalValidators(ffnAvailable, [Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));  
        }
      }

    }
  }
  setCLNValidator() {
    let vaidatorRequiredNotNeeded =false;
    if(this.noOfPassengers ===1){
      return;
    }
    for (let i = 0; i < (this.numOfFormPassengers); i++) {
      let passenger = this.getFormArrayPassengerObject(i);
      if(passenger['employeeType']!==0 && (this.userAccountInfoService.isUserCorporateAdmin() || this.userAccountInfoService.isUserIsTravelManager())){
        vaidatorRequiredNotNeeded =true;
      }else{
        vaidatorRequiredNotNeeded =false;
      }
      let carLoyalityInfo = (<FormArray>passenger.get('carLoyalityInfo'));

      for (let j = 0; j < carLoyalityInfo.length; j++) {
        let clnAvailable = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[i]).controls['carLoyalityInfo']).at(j).get('clnAvailable').value;
        if(!vaidatorRequiredNotNeeded){
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[i]).controls['carLoyalityInfo']).at(j).get('rentalCarLoyaltyNumber').setValidators(this.conditionalValidators(clnAvailable, [Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
        }else{
          (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[i]).controls['carLoyalityInfo']).at(j).get('rentalCarLoyaltyNumber').setValidators(this.conditionalValidators(clnAvailable, [Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
        }
      }

    }
  }

  setHLNValidator() {
    let vaidatorRequiredNotNeeded =false;
    if(this.noOfPassengers ===1){
      return;
    }
    for (let i = 0; i < (this.numOfFormPassengers); i++) {
      let passenger = this.getFormArrayPassengerObject(i);
      if(passenger['employeeType']!==0 && (this.userAccountInfoService.isUserCorporateAdmin() || this.userAccountInfoService.isUserIsTravelManager())){
        vaidatorRequiredNotNeeded =true;
      }else{
        vaidatorRequiredNotNeeded =false;
      }
      if (this.loyaltyNumberMandatoryOrNot()) vaidatorRequiredNotNeeded = false;
      let hotelLoyalityInfo = (<FormArray>passenger.get('hotelLoyalityInfo'));

      for (let j = 0; j < hotelLoyalityInfo.length; j++) {
        let hlnAvailable = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[i]).controls['hotelLoyalityInfo']).at(j).get('hlnAvailable').value;
        let isHotelBeds = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[i]).controls['hotelLoyalityInfo']).at(j).get('loyaltyPointsSupported').value;
        hlnAvailable = hlnAvailable && isHotelBeds;
        if(!vaidatorRequiredNotNeeded){
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[i]).controls['hotelLoyalityInfo']).at(j).get('hotel_loyality_number').setValidators(this.conditionalValidators(hlnAvailable, [Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
        }else{
          (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[i]).controls['hotelLoyalityInfo']).at(j).get('hotel_loyality_number').setValidators(this.conditionalValidators(hlnAvailable, [Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
        }
      }

    }
  }
  createformFromOta() {
    this.passengersForm = this.fb.group(
      {
        passengers: this.fb.array([])
      }, { validator: '' });
  }
  createForm(): void {

    this.passengersForm = this.fb.group(
      {
        passengers: this.fb.array([])
      }, { validator: '' });

    if (this.travellerPreFillData && this.travellerPreFillData['passengers']) {
      for (let i = 0; i < (this.travellerPreFillData['passengers'].length); i++) {
        let userid = this.userAccountInfoService.getUserEmail();
        if (this.travellerPreFillData['passengers'][i]['email'] === userid) {
          this.addPassenger(i, this.travellerPreFillData['passengers'][i], 0);
        } else {
          this.addPassenger(i, this.travellerPreFillData['passengers'][i]);
        }
      }
    } else {
      this.addPassenger(0, undefined);
    }

    if (this.disableEmail) {
      this.filteredTravellers();
    }
  }
  setDialCode(userAccountInfoObj){
    if(userAccountInfoObj && userAccountInfoObj.userInfo && userAccountInfoObj.userInfo.locale){
      let countryCode= userAccountInfoObj.userInfo.locale.split('-');
      let country = countries.filter(item => item.code===countryCode[countryCode.length-1]);
      if(country && country[0]){
        return country[0].dial_code;
      }
  
    }else{
      let countryCode= navigator.language.split('-');
      let country = countries.filter(item => item.code===countryCode[countryCode.length-1]);
      if(country && country[0]){
        return country[0].dial_code;
      }
      return null;
    }
  }
  fillFormValue(i, passengerData) {
    let newPassengerForm = this.fb.group({
      employeeType: [passengerData && passengerData['employeeType'] ? passengerData['employeeType'] : null],
      title: [passengerData && passengerData['title'] ? passengerData['title'] : null],
      firstName: [passengerData && passengerData['firstName'] ? passengerData['firstName'] : null, Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)])],
      middleName: [(passengerData && passengerData['middleName']) ? passengerData['middleName'] : "", this.conditionalValidators(true, [Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)])],
      lastName: [passengerData && passengerData['lastName'] ? passengerData['lastName'] : "", Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)])],
      gender: [passengerData && passengerData['gender'] ? passengerData['gender'] : "", this.conditionalValidators(this.carBooking, [Validators.required])],
      dateOfBirth: [passengerData && passengerData['dateOfBirth'] ? passengerData['dateOfBirth'] : null, this.conditionalValidators(this.carBooking, [Validators.required,this.validity(), this.ageRangeValidator(i)])],
      email: [passengerData && passengerData['email'] ? passengerData['email'] : null, [Validators.required, Validators.pattern(Constants.RGEX_EMAIL)]],
      phoneNumber: [passengerData && passengerData['phoneNumber'] ? passengerData['phoneNumber'] : null, this.conditionalValidators(true, [Validators.required,CommonUtils.PhoneNumberValidator(this.findCountryCode(passengerData,'dialCode'))])],
      // phoneNumber: [null, this.conditionalValidators((i == 0), [Validators.required, Validators.pattern(Constants.RGEX_ONLY_DIGITS)])],
      address: [passengerData && passengerData['address'] ? passengerData['address'] : null, this.conditionalValidators(false, [Validators.required])],
      zipCode: new FormControl(passengerData && passengerData['zipCode'] ? passengerData['zipCode'] : null, {
        validators: this.conditionalValidators(this.isZipCodeRequired, [Validators.required]),
        // asyncValidators:this.conditionalAsyncValidators((i==0),[this.zipCodeValidator.bind(this)]),
        updateOn: 'blur'
      }),
      ktnAvailable: [true],
      knownTravellerNumber: [passengerData && passengerData['knownTravellerNumber'] ? passengerData['knownTravellerNumber'] : ""],
      dialCode: [passengerData && passengerData['dialCode'] ? passengerData['dialCode'] :  this.setDialCode(this.userAccountInfoService.getAccountInfo()), Validators.compose([Validators.required])],
      passportNumber: [passengerData && passengerData['passportNumber'] ? passengerData['passportNumber'] : null, this.conditionalValidators(this.isPassportRequired, [Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)])],
      passportCountry: [passengerData && passengerData['passportCountry'] ? passengerData['passportCountry'] : null, this.conditionalValidators(this.isPassportRequired, [Validators.required])],
      passportNationality: [passengerData && passengerData['passportNationality'] ? passengerData['passportNationality'] : null, this.conditionalValidators(this.isPassportRequired, [Validators.required])],
      passportExpiryDate: [passengerData && passengerData['passportExpiryDate'] ? passengerData['passportExpiryDate'] : null, this.conditionalValidators(this.isPassportRequired, [Validators.required])],
      seat: [passengerData && passengerData['seat'] ? passengerData['seat'] : null, this.conditionalValidators(this.isBookerIsTravelmanager(passengerData,this.uniqueAirlines.length > 0), [Validators.required])],
      openFormOnUI: [false],
      emergencyContactName: [passengerData && passengerData['emergencyContactName'] ? passengerData['emergencyContactName'] : null],
      relationship: [passengerData && passengerData['relationship'] ? passengerData['relationship'] : null],
      contactNumber: [passengerData && passengerData['contactNumber'] ? passengerData['contactNumber'] : null],
      dialCode1: [passengerData && passengerData['dialCode1'] ? passengerData['dialCode1'] :  this.setDialCode(this.userAccountInfoService.getAccountInfo())],
      frequentFlyerInfo: this.fb.array([]),
      hotelLoyalityInfo: this.fb.array([]),
      carLoyalityInfo: this.fb.array([]),
      roomId: this.hotelRoomIdArray[i] ? this.hotelRoomIdArray[i] : "1",
    });
    if (passengerData && passengerData['employeeType'] === 0) {
      newPassengerForm.controls['employeeType'].setValue(0);
    }
    return newPassengerForm;
  }
  isBookerIsTravelmanager(passeneger,condition){
    let userid = this.userAccountInfoService.getUserEmail();
    if(passeneger && passeneger['email'] !== userid && condition && (this.userAccountInfoService.isUserCorporateAdmin() || this.userAccountInfoService.isUserIsTravelManager())){
      return false;
    }else if (passeneger && passeneger['email'] === userid && condition) {
      return true;
    }else if(condition){
    return true;
    }
  }
  getName(text, i) {
    // let passenger: AbstractControl = this.getFormArrayPassengerObject(i);
    if (text !== '') {
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].setValidators([Validators.required, this.conditionalValidators(true, [CommonUtils.PhoneNumberValidator(this.findCountryCode((<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls,'dialCode1'))])]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].setValidators([Validators.required]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].markAsTouched();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].markAsTouched();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].markAsTouched();
    } else {
      if (!(<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].value && !(<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].value) {
        //  (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].clearValidators();
        // (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
        if ((<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].invalid) {
          (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].clearValidators();
          (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        }
      } else {
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].setValidators([Validators.required, this.conditionalValidators(true, [Validators.pattern(Constants.RGEX_ONLY_DIGITS)])]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].setValidators([Validators.required]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].markAsTouched();
      }
    }
  }
  getDialCode1(text, i) {
    //let passenger = this.getFormArrayPassengerObject(i);
    if (text !== '') {
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].setValidators([Validators.required,this.conditionalValidators(true, [CommonUtils.PhoneNumberValidator(this.findCountryCode((<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls,'dialCode1'))])]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].setValidators([Validators.required]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
    } else {
      if (!(<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].value && (!(<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].value && ((<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].value !== ''))) {
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
      } else {
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].setValidators([Validators.required, this.conditionalValidators(true, [Validators.pattern(Constants.RGEX_ONLY_DIGITS)])]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].setValidators([Validators.required]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].markAsTouched();
      }
    }
  }
  getRelationship(text, i) {
    // let passenger: AbstractControl = this.getFormArrayPassengerObject(i);
    if (text !== '') {
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].setValidators([Validators.required, this.conditionalValidators(true, [CommonUtils.PhoneNumberValidator(this.findCountryCode((<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls,'dialCode1'))])]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].setValidators([Validators.required]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].markAsTouched();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].markAsTouched();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].markAsTouched();
    } else {
      if (!(<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].value && !(<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].value) {
        //  (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
        //  (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
        if ((<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].invalid) {
          (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].clearValidators();
          (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        }
      } else {
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].setValidators([Validators.required, this.conditionalValidators(true, [Validators.pattern(Constants.RGEX_ONLY_DIGITS)])]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].setValidators([Validators.required]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].markAsTouched();
      }
    }
  }
  getphoneNumber(text, i) {
    if (text) {
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].setValidators([Validators.required, this.conditionalValidators(true, [CommonUtils.PhoneNumberValidator(this.findCountryCode((<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls,'dialCode1'))])]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].setValidators([Validators.required, this.conditionalValidators(true, [Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)])]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].setValidators([Validators.required]);
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].markAsTouched();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].markAsTouched();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].markAsTouched();
    } else {
      if (!(<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].value && !(<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].value) {
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].clearValidators();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
        if ((<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].invalid) {
          (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].clearValidators();
          (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        }
      } else {
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].setValidators([Validators.required, Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE), this.minLengthArray(2)]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].setValidators([Validators.required, this.conditionalValidators(true, [Validators.pattern(Constants.RGEX_ONLY_DIGITS)])]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].setValidators([Validators.required]);
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].updateValueAndValidity();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['emergencyContactName'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['dialCode1'].markAsTouched();
        (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['relationship'].markAsTouched();
      }
    }
  }
  updatePassenger(i, passengerData, id) {
    // (<FormArray>this.passengersForm.controls['passengers']).removeAt(i);
    let newPassengerForm = this.fillFormValue(i, passengerData);
    newPassengerForm.controls['employeeType'].setValue(id);
    (this.passengersForm.get('passengers') as FormArray).at(i).patchValue(passengerData);
    
    setTimeout(() => {
      if (i === 0 && newPassengerForm && newPassengerForm.controls['employeeType'].value == 0 && this.userAccountInfoService.promptUserTosaveProfile) {
        this.disabledUserEntry((this.passengersForm.get('passengers') as FormArray).at(i));
        this.maskDob = false;
        this.maskAdd = false;
        this.maskPhone = false;
        this.maskZip = false;
        this.maskKtn = false;
        this.maskCln = false;
        this.maskHln = false;
        this.maskFfn = false;
        this.maskDial = false;
        this.maskPnum = false;
        this.maskPnat = false;
        this.maskPcon = false;
        this.maskPexp = false;
      } else if (i === 0 && newPassengerForm && (newPassengerForm.controls['employeeType'].value == 0 || newPassengerForm.controls['employeeType'].value == 1)) {
        this.enabledUserEntry((this.passengersForm.get('passengers') as FormArray).at(i));
        this.maskDob = false;
        this.maskAdd = false;
        this.maskPhone = false;
        this.maskZip = false;
        this.maskKtn = false;
        this.maskCln = false;
        this.maskHln = false;
        this.maskFfn = false;
        this.maskPnat = false;
        this.maskPcon = false;
        this.maskPnum = false;
        this.maskDial = false;
        this.maskPexp = false;
      } else {
        this.enabledUserEntry((this.passengersForm.get('passengers') as FormArray).at(i));
        if (newPassengerForm && newPassengerForm.controls['employeeType'].value && newPassengerForm.controls['employeeType'].value !== 1) {
          this.maskDob = true;
          this.maskAdd = true;
          this.maskPhone = true;
          this.maskZip = true;
          this.maskKtn = true;
          this.maskCln = true;
          this.maskHln = true;
          this.maskFfn = true;
          this.maskPnat = true;
          this.maskPcon = true;
          this.maskPnum = true;
          this.maskDial = true;
          this.maskPexp = true;
        }
      }
    }, 100);
    //  (<FormArray>this.passengersForm.controls['passengers']).insert(i,newPassengerForm);


  }
  nextButtonClicked() {
    this.maskDial = false;
  }
  getTypeOfInput(item, item2, item3?) {
    if (!item3 || item3 === '') {
      return false;
    }
    if (item !== 0 && item !== 1 && item2) {
      return item2;
    } else {
      item2 = false;
      return item2;
    }
  }
  getSelecteddropdownValue(item, value, item2) {
    if (item !== 0 && item !== 1 && item2) {
      if (!value || value === undefined) {
        return "";
      }
      return value;
    } else {
      if (!value || value === undefined) {
        return "";
      }
      return value;
    }

  }

  getFocusOutInput(item, item3, item2) {
    if (!item) {
      return false;
    }
    if (!item3 || item3 === '') {
      return false;
    }
    if (item !== 0 && item !== 1) {
      item2 = true;
      return item2;
    } else {
      item2 = false;
      return item2;
    }
  }
  EnablingForm(i) {
    this.userAccountInfoService.promptUserTosaveProfile = false;
    this.enabledUserEntry((this.passengersForm.get('passengers') as FormArray).at(i));
  }
  validateAllFormFields(formGroup: FormGroup) {         //{1}
    Object.keys(formGroup.controls).forEach(field => {  //{2}
      const control = formGroup.get(field);             //{3}
      if (control instanceof FormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof FormGroup) {        //{5}
        this.validateAllFormFields(control);            //{6}
      }
    });
  }
 
  
  isValidDateForForm(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }
  isPassengerFormValidate(i){
     let   formGroup = <FormGroup>(<FormArray>this.passengersForm.controls['passengers']).at(i);
      let dateValue = formGroup.controls['dateOfBirth'].value;
      if(dateValue && this.isValidDateForForm(dateValue)){
      let dateAfterFormatting = this.getDateType(dateValue);
      formGroup.controls['dateOfBirth'].setValue(dateAfterFormatting);
      }
      this.validateAllFormFields(formGroup);
      this.validateAllFormFields(<FormGroup>formGroup.controls['frequentFlyerInfo']);
      this.validateAllFormFields(<FormGroup>formGroup.controls['hotelLoyalityInfo']);
      this.validateAllFormFields(<FormGroup>formGroup.controls['carLoyalityInfo']);
      if ((<FormArray>this.passengersForm.controls['passengers']).length > 1) {
        if (!formGroup.valid) {
          toggleAccordion((this.accordion as any)['groups'][i].isOpen, (this.accordion as any)['groups'].length, i);
          this.closeOtherAccordion(i);
          for (let j = 0; j < this.accordionOpen.length; j++) {
            if (i !== j) {
              this.accordionOpen[j] = false;
            }
          }
          this.accordionOpen[i] = true;

          setTimeout(() => {
            this.scrollToFirstInvalidControl()
          }, 100)
         
        }
      } else if ((<FormArray>this.passengersForm.controls['passengers']).length == 1) {
        if (!formGroup.valid) {
          this.scrollToFirstInvalidControl();
        }
      }

  
  }
  invalid =false;
  isFormValid(j){
    for (let i = 0; i < (<FormArray>this.passengersForm.controls['passengers']).length; i++) {
      let   formGroup = <FormGroup>(<FormArray>this.passengersForm.controls['passengers']).at(i);
      let dateValue = formGroup.controls['dateOfBirth'].value;
      if(dateValue && this.isValidDateForForm(dateValue)){
      let dateAfterFormatting = this.getDateType(dateValue);
      formGroup.controls['dateOfBirth'].setValue(dateAfterFormatting);
      }
      this.validateAllFormFields(formGroup);
      this.validateAllFormFields(<FormGroup>formGroup.controls['frequentFlyerInfo']);
      this.validateAllFormFields(<FormGroup>formGroup.controls['hotelLoyalityInfo']);
      this.validateAllFormFields(<FormGroup>formGroup.controls['carLoyalityInfo']);
      if ((<FormArray>this.passengersForm.controls['passengers']).length > 1) {
        if (!formGroup.valid && this.userAccountInfoService.userFormOpen===i) {
          toggleAccordionClose(true, (this.accordion as any)['groups'][i].length, j);
          (this.accordion as any)['groups'][i].isOpen = false;
          this.accordionOpen[j] = false;
          this.invalid =true;
          setTimeout(() => {
            toggleAccordion((this.accordion as any)['groups'][i].isOpen, (this.accordion as any)['groups'].length, i);
            (this.accordion as any)['groups'][i].isOpen= true;
          this.accordionOpen[i] = true;
          
          this.userAccountInfoService.userFormOpen =i; 
          }, 10);
         

          setTimeout(() => {
            this.scrollToFirstInvalidControl()
          }, 100)
         break;
        }else{
          this.invalid =false;
        }
      } else if ((<FormArray>this.passengersForm.controls['passengers']).length == 1) {
        if (!formGroup.valid) {
          this.invalid =true;
          this.scrollToFirstInvalidControl();
        }else{
          this.invalid =false;
        }
      }

    }
    
  }
  isCreditNameAndFormNameSame(){
    let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
    let obj={name:'',value:true,index:0};
  //let travelerIndex=this.bookingService.travelerIndex;
    for(let item of selectedFlight){
      if(item.travelCreditsInfo && item.travelCreditsInfo.length > 0){
    for (let travelerIndex = 0; travelerIndex < (<FormArray>this.passengersForm.controls['passengers']).length; travelerIndex++) {
      let   formGroup = <FormGroup>(<FormArray>this.passengersForm.controls['passengers']).at(travelerIndex);
      let email = formGroup.controls['email'].value;
      for(let item1 of item.travelCreditsInfo){
        if(item1.travellerEmail===email){
          let name ;
          if(formGroup.controls['middleName'].value && formGroup.controls['middleName'].value!==''){
           name = formGroup.controls['firstName'].value +" "+ formGroup.controls['middleName'].value+" "+formGroup.controls['lastName'].value;
          }else{
            name = formGroup.controls['firstName'].value + " "+formGroup.controls['lastName'].value;
          }
          let matchingName = item1.travellerFirstName +" "+ item1.travellerLastName;
          if(name!==matchingName){
            obj = {name:matchingName,value:false,index:(travelerIndex+1)};
            return obj;
          }
        }
      }

    }
  }
  }
  
  }
  public isTravellerListHasAnAdultTraveler(){
    let adultCount=0
    for (let i = 0; i < (<FormArray>this.passengersForm.controls['passengers']).length; i++) {
      let   formGroup = <FormGroup>(<FormArray>this.passengersForm.controls['passengers']).at(i);
      let dateValue = formGroup.controls['dateOfBirth'].value;
      let dob = new Date(dateValue);
      let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
        if(this.pageMode !=='WebSearch' && this.emailQuotOptions && this.emailQuotOptions.flightLegRequests && this.emailQuotOptions.flightLegRequests.length > 0){
          selectedFlight = this.emailQuotOptions.flightLegRequests;
          
    }
        let todayDate;
        if(this.pageMode !=='WebSearch' && selectedFlight){
          todayDate = new Date(  todayDate = new Date(selectedFlight[0].dateTime.split('T')[0]));
        }
        else if(selectedFlight){
         todayDate = new Date(selectedFlight[0].legs[0].flightHops[0].starts.split('T')[0]);
        }else{
          todayDate = new Date();
        }
      const oneDay = 24 * 60 * 60 * 1000;
      let sixteenAgo: Date = new Date(todayDate.getFullYear() - 16, todayDate.getMonth(), todayDate.getDate());
      let differenceInMs2: number = todayDate.getTime() - sixteenAgo.getTime();
      let days2: number = Math.floor(differenceInMs2 / (1000 * 60 * 60 * 24));
      let diff2 = Math.floor((todayDate.getTime() - dob.getTime()) / oneDay);
      if(diff2 > days2){
        adultCount=1;
      }
      
    }
    if(adultCount===0){
      this.toastr.error(this.translateService.instant('setting.Pleaseselectatleast1adulttravelerwithagegreaterthan16'));
        return false;
    }else{
      return true;
    }

  }
  public validatePassangersForm(item?) {
    for (let i = 0; i < (<FormArray>this.passengersForm.controls['passengers']).length; i++) {
      let   formGroup = <FormGroup>(<FormArray>this.passengersForm.controls['passengers']).at(i);
      let dateValue = formGroup.controls['dateOfBirth'].value;
      if(dateValue && this.isValidDateForForm(dateValue)){
      let dateAfterFormatting = this.getDateType(dateValue);
      formGroup.controls['dateOfBirth'].setValue(dateAfterFormatting);
      }
      this.validateAllFormFields(formGroup);
      this.validateAllFormFields(<FormGroup>formGroup.controls['frequentFlyerInfo']);
      this.validateAllFormFields(<FormGroup>formGroup.controls['hotelLoyalityInfo']);
      this.validateAllFormFields(<FormGroup>formGroup.controls['carLoyalityInfo']);
      if ((<FormArray>this.passengersForm.controls['passengers']).length > 1) {
        if (!formGroup.valid) {
          toggleAccordion((this.accordion as any)['groups'][i].isOpen, (this.accordion as any)['groups'].length, i);
          this.closeOtherAccordion(i);
          for (let j = 0; j < this.accordionOpen.length; j++) {
            if (i !== j) {
              this.accordionOpen[j] = false;
            }
          }
          if(!item){
          this.indexOfOpenedAccordion(i);
          }
          this.accordionOpen[i] = true;

          setTimeout(() => {
            this.scrollToFirstInvalidControl()
          }, 100)
         break;
        }
      } else if ((<FormArray>this.passengersForm.controls['passengers']).length == 1) {
        if (!formGroup.valid) {
          this.scrollToFirstInvalidControl();
        }
      }

    }

  }
  scrollTo(el: Element): void {
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }
  scrollTo1(el: Element): void {
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }
  public scrollToFirstFormconntrol() {
    const firstInvalidControl: HTMLElement = this.el.nativeElement.querySelector(".card-heading");
    this.scrollTo1(firstInvalidControl);
  }
  private scrollToFirstInvalidControl() {
    const firstInvalidControl: HTMLElement = this.el.nativeElement.querySelector(".ng-invalid[formControlName]");
    this.scrollTo(firstInvalidControl);
  }

  processNext(): boolean {

    this.validatePassangersForm();
    if (!this.passengersForm.valid) return false;
    if (this.numOfFormPassengers < this.noOfPassengers) {
      this.addPassenger(this.numOfFormPassengers, undefined);
      return false;
    } else {
      return true;
    }
  }
  findCountryCode(data,item){
    if(data && data[item]){
      let findIndex = this.countries.findIndex(item1 => (item1.dial_code===data[item].value || item1.dial_code=== data[item]));
      if(findIndex > -1){
       return  this.countries[findIndex].code;
      }
    }
    let countryCode= navigator.language.split('-');
    let country = countries.filter(item => item.code===countryCode[countryCode.length-1]);
    if(country && country[0]){
      return country[0].code;
    }
  return null;
  }
  disableEmailForForm(){
    (this.passengersForm.controls['passengers'] as FormArray).controls.forEach((passenger: FormGroup) => {
      const emailControl = passenger.controls['email'];
      const employeeTypeControl = passenger.controls['employeeType'];
    
      employeeTypeControl.valueChanges.subscribe((value: number) => {
        const shouldDisable = this.emailFieldDisabled(emailControl.value, value);
    
        if (shouldDisable) {
          emailControl.disable({ emitEvent: false });
        } else {
          emailControl.enable({ emitEvent: false });
        }
      });
    
      // Optional: run it once initially
      const initialValue = employeeTypeControl.value;
      if (this.emailFieldDisabled(emailControl.value, initialValue)) {
        emailControl.disable({ emitEvent: false });
      }
    });
    
  }
  addPassenger(i, passengerData, id?) {
    if(this.bookingService.bookingData){
   this.setCarFlightHotel(this.bookingService.bookingData);
    }
    let newPassengerForm = this.fillFormValue(i, passengerData);
    if (i == 0 && !this.disableEmail) {
      let userid = this.userAccountInfoService.getUserEmail();
      if (newPassengerForm.controls['email'].value === userid) {
        newPassengerForm.controls['employeeType'].setValue(0);
      } else if (id){
        newPassengerForm.controls['employeeType'].setValue(id);
      }
    } else if (id){
      newPassengerForm.controls['employeeType'].setValue(id);
    }
    (<FormArray>this.passengersForm.controls['passengers']).push(newPassengerForm);
this.disableEmailForForm()
    if (i === 0 && newPassengerForm && newPassengerForm.controls['employeeType'].value == 0) {
      if (this.userAccountInfoService.getAccountInfo()) {
        let userInfoDTO = this.userAccountInfoService.getAccountInfo().userInfo;
        if (userInfoDTO.emergencyContact) {
          this.emergencyContact.emit(userInfoDTO.emergencyContact);
        } else {
          this.emergencyContact.emit({});
        }
      }
    } else if (i === 0 && newPassengerForm && newPassengerForm.controls['employeeType'].value == 1) {
      this.emergencyContact.emit({});
    } else if (i === 0 && newPassengerForm && newPassengerForm.controls['employeeType'].value !== 0 && newPassengerForm.controls['employeeType'].value !== 1) {
      let employee;
      if (this.employeeList && this.employeeList.length > 2) {
        employee = this.employeeList.find(item => item.employeeInfo.employeeId === newPassengerForm.controls['employeeType'].value);
      }
      if (employee && employee.emergencyContact) {
        this.emergencyContact.emit(employee.emergencyContact);
      } else {
        this.emergencyContact.emit({});
      }
    }

    for (let j = 0; j < this.uniqueAirlines.length; j++) {
      let ffn = null;
      if (passengerData && passengerData['frequentFlyerInfo']) {
        let filteredObj: any = passengerData['frequentFlyerInfo'].find(x => x.airlineCode === this.uniqueAirlines[j]['code']);
        if (filteredObj) ffn = filteredObj.frequent_flyer_number;
      }
      this.addFFNDetails(i, this.uniqueAirlines[j], ffn);
    }
    for (let j = 0; j < this.uniqueCars.length; j++) {
      let cln = null;
      if (passengerData && passengerData['carLoyalityInfo']) {
        let filteredObj: any = passengerData['carLoyalityInfo'].find(x => x.rentalCarCode === this.uniqueCars[j]['traflaPartnerCode']);
        if (filteredObj) cln = filteredObj.rentalCarLoyaltyNumber;
      }
      this.addCLNDetails(i, this.uniqueCars[j], cln);
    }

  

      for (let j = 0; j < this.uniqueHotels.length; j++) {
        let hln = null;
        if (passengerData && passengerData['hotelLoyalityInfo']) {
          let filteredObj: any = passengerData['hotelLoyalityInfo'].find(x => x.hotelCode === this.uniqueHotels[j]['code']);
          if (filteredObj) hln = filteredObj.hotel_loyality_number;
        }
        this.addHLNDetails(i, this.uniqueHotels[j], hln);
      }
      this.setHLNValidator();
    
    this.setKTNValidator(i);
    this.setFFNValidator(i);
    this.setCLNValidator();
    this.setMinLastNameLengthForHotel(i);
   
    setTimeout(() => {
      if (i === 0 && newPassengerForm && newPassengerForm.controls['employeeType'].value == 0 && this.userAccountInfoService.promptUserTosaveProfile) {
        this.disabledUserEntry((this.passengersForm.get('passengers') as FormArray).at(i));
        this.maskDob = false;
        this.maskAdd = false;
        this.maskPhone = false;
        this.maskZip = false;
        this.maskKtn = false;
        this.maskEmeNnname = false;
        this.maskRel = false;
        this.maskDial1 = false;
        this.maskPhone1 = false;
        this.maskCln = false;
        this.maskHln = false;
        this.maskPnat = false;
        this.maskPcon = false;
        this.maskFfn = false;
        this.maskPnum = false;
        this.maskPexp = false;
        this.maskDial = false;
      } else if (i === 0 && newPassengerForm && (newPassengerForm.controls['employeeType'].value == 0 || newPassengerForm.controls['employeeType'].value == 1)) {
        this.enabledUserEntry((this.passengersForm.get('passengers') as FormArray).at(i));
        this.maskDob = false;
        this.maskAdd = false;
        this.maskPhone = false;
        this.maskZip = false;
        this.maskKtn = false;
        this.maskEmeNnname = false;
        this.maskRel = false;
        this.maskDial1 = false;
        this.maskPhone1 = false;
        this.maskCln = false;
        this.maskHln = false;
        this.maskPnat = false;
        this.maskPcon = false;
        this.maskFfn = false;
        this.maskDial = false;
        this.maskPnum = false;
        this.maskPexp = false;
      } else {
        this.enabledUserEntry((this.passengersForm.get('passengers') as FormArray).at(i));
        if (newPassengerForm && newPassengerForm.controls['employeeType'].value && newPassengerForm.controls['employeeType'].value !== 1) {
          this.maskDob = true;
          this.maskAdd = true;
          this.maskPhone = true;
          this.maskZip = true;
          this.maskKtn = true;
          this.maskCln = true;
          this.maskEmeNnname = true;
          this.maskRel = true;
          this.maskDial1 = true;
          this.maskPhone1 = true;
          this.maskHln = true;
          this.maskPnat = true;
          this.maskPcon = true;
          this.maskFfn = true;
          this.maskPnum = true;
          this.maskDial = true;
          this.maskPexp = true;
        }
      }
    }, 100);

    let el = document.getElementById('passenger' + i + 'accordion');
    // if(el && el!=null) el.scrollIntoView();
    animateScrollToTravellerDetails(i);

  }
  disabledUserEntry(item) {
    for (let key in item.controls) {
      let value = item.controls[key];
      if ((key !== 'employeeType' && key !== 'email') && value.status === 'VALID') {
        value.disable();
      }
      if (key === 'address' || key === 'middleName' || key === 'relationship' || key === 'contactNumber' || key === 'emergencyContactName') {
        if (!value.value || value.value === "") {
          value.enable();
        }
      }
      if(key ==='dialCode' || key ==='dialCode1'){
        if(item.controls['employeeType'].value == 1){
          value.enable();
        }
      }
      if (key === 'frequentFlyerInfo') {
        for (let item2 of value.value)
          if (!item2.ffnAvailable) {
            value.enable();
          }
      }
      if (key === 'carLoyalityInfo') {
        for (let item2 of value.value)
          if (!item2.clnAvailable) {
            value.enable();
          }
      }
      if (key === 'hotelLoyalityInfo') {
        for (let item2 of value.value)
          if (!item2.hotel_loyality_number && item2.loyaltyPointsSupported) {
            value.enable();
          }
      }
      if (key === 'knownTravellerNumber') {
        let value1 = item.controls['ktnAvailable'];
        if (!value1.value) {
          value.enable();
        }
      }
    }
  }
  enabledUserEntry(item) {
    for (let key in item.controls) {
      let value = item.controls[key];
      if (key !== 'email') {
        value.enable();
      }
      if (key === 'hotelLoyalityInfo') {
        for (let item2 of value.value)
          if (!item2.loyaltyPointsSupported) {
            value.disable();
          }
      }

    }
  }
  enabledUserEntry1(item) {
    for (let key in item.controls) {
      let value = item.controls[key];
      //if (key !== 'email') {
        value.enable();
     // }
    }
  }
  minLengthArray(min: number) {
    return (c: AbstractControl): { [key: string]: any } => {
      if (c.value) {
        if (c.value.length >= min)
          return null;

        return { 'minLengthArray': { valid: false } };
      }
    }
  }
  conditionalValidators(condition, validators: ValidatorFn[]) {
    if (condition) {
      return Validators.compose(validators);
    }
    return null;
  }

  conditionalAsyncValidators(condition, validators: AsyncValidatorFn[]) {
    if (condition) {
      return Validators.composeAsync(validators);
    }
    return null;
  }
  validity(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: boolean } | null => {
      let dateValid = this.isValidDateForForm(control.value);
     
        if (!dateValid) {
          return { 'dateValidity':  true  };
        }else{
        return null;
        }
    }
  
  }
  
  ageRangeValidator(formIndex: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: boolean } | null => {
      if (!this.hotelTravellerData[formIndex]) return null;
      let dateValid = this.isValidDateForForm(control.value);
      if(dateValid && this.pageMode==='WebSearch'){
      let yearsDiff = DateUtils.getYearsDiffFromNow(control.value);
      // if (this.hotelAdultCount > 0 && this.hotelTravellerData[formIndex].type === 'ADT' && yearsDiff < 21) {
      //   return { 'invalidAdultAge': true };
      // }
      // if (this.hotelAdultCount > 0 && this.hotelTravellerData[formIndex].type === 'ADT' && yearsDiff < 21) {
      //   return { 'invalidAdultAge': true };
      // } else 
      if(this.searchService.ageGroupArray.length >0  && this.searchService.infantBookingAllowed 
        && this.uniqueAirlines.length > 0 && this.uniqueCars.length===0 && this.uniqueHotels.length===0){
        const oneDay = 24 * 60 * 60 * 1000;
        let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
        if(this.pageMode !=='WebSearch' && this.emailQuotOptions && this.emailQuotOptions.flightLegRequests && this.emailQuotOptions.flightLegRequests.length > 0){
          selectedFlight = this.emailQuotOptions.flightLegRequests;
          
    }
        let todayDate;
        if(this.pageMode !=='WebSearch' && selectedFlight){
          todayDate = new Date(  todayDate = new Date(selectedFlight[0].dateTime.split('T')[0]));
        }
        else if(selectedFlight){
         todayDate = new Date(selectedFlight[0].legs[0].flightHops[0].starts.split('T')[0]);
        }else{
          todayDate = new Date();
        }
        let dob = new Date(control.value);
        let twelveYearsAgo: Date = new Date(todayDate.getFullYear() - 12, todayDate.getMonth(), todayDate.getDate()); 
        let twoYearsAgo: Date = new Date(todayDate.getFullYear() - 2, todayDate.getMonth(), todayDate.getDate()); 
        let twoyesrDiff = Math.round(Math.abs((todayDate.getTime() - twoYearsAgo.getTime()) / oneDay));
        let differenceInMs: number =todayDate.getTime() - twelveYearsAgo.getTime();
      let  days: number = Math.floor(differenceInMs / (1000 * 60 * 60 * 24));
        let diff2 = Math.floor((todayDate.getTime() - dob.getTime()) / oneDay);
       
        if (this.searchService.ageGroupArray[formIndex].id === 'ADT' && diff2 <= days) {
          return { 'invalidAdultAge': true };
        } else if (this.searchService.ageGroupArray[formIndex].id === 'CHD' && (diff2 < twoyesrDiff || diff2  > days)) {
          return { 'invalidChildAge': true };
        } else if (this.searchService.ageGroupArray[formIndex].id === 'INF' && (diff2 >= twoyesrDiff)) {
          return { 'invalidInfantAge': true };
        }
      }else{
        let hotelSearchQueryParam: HotelQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));
       // let carSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("carSearchRequest")));
       let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
       if(this.pageMode !=='WebSearch' && this.emailQuotOptions && this.emailQuotOptions.flightLegRequests && this.emailQuotOptions.flightLegRequests.length > 0){
        selectedFlight = this.emailQuotOptions.flightLegRequests;
        
  }
        let todayDate;
       // const oneDay = 24 * 60 * 60 * 1000;
       let twelveYearsAgo: Date ;
        if(hotelSearchQueryParam && hotelSearchQueryParam.checkinDate){
          todayDate =new Date(hotelSearchQueryParam.checkinDate);
          twelveYearsAgo = new Date(todayDate.getFullYear() - 18, todayDate.getMonth(), todayDate.getDate()); 
        }  if(this.pageMode !=='WebSearch' && selectedFlight){
          todayDate = new Date(selectedFlight[0].dateTime.split('T')[0]);
          twelveYearsAgo = new Date(todayDate.getFullYear() - 17, todayDate.getMonth(), todayDate.getDate()); 
        }
        else if(selectedFlight){
          todayDate = new Date(selectedFlight[0].legs[0].flightHops[0].starts.split('T')[0]);
          twelveYearsAgo = new Date(todayDate.getFullYear() - 17, todayDate.getMonth(), todayDate.getDate()); 
         }else{
          todayDate = new Date();
          twelveYearsAgo = new Date(todayDate.getFullYear() - 18, todayDate.getMonth(), todayDate.getDate()); 
        }
        let dob = new Date(control.value);
        const oneDay = 24 * 60 * 60 * 1000;
        let twoYearsAgo: Date = new Date(todayDate.getFullYear() - 2, todayDate.getMonth(), todayDate.getDate()); 
        let twoyesrDiff = Math.round(Math.abs((todayDate.getTime() - twoYearsAgo.getTime()) / oneDay));
    // twelveYearsAgo = new Date(todayDate.getFullYear() - 18, todayDate.getMonth(), todayDate.getDate()); 
      let differenceInMs: number =todayDate.getTime() - twelveYearsAgo.getTime();
    let  days: number = Math.floor(differenceInMs / (1000 * 60 * 60 * 24));

      var diff2 = Math.round(Math.abs((todayDate.getTime() - dob.getTime()) / oneDay));
      if (hotelSearchQueryParam && this.hotelTravellerData[formIndex].type === 'ADT' && diff2 <= days) {
        return { 'invalidAdultAgeForHotel': true };
      }else  if (!hotelSearchQueryParam && selectedFlight && this.hotelTravellerData[formIndex].type === 'ADT' && diff2 <= days) {
        return { 'invalidAdultAgeForHotel': true };
      } else if (this.hotelTravellerData[formIndex].type === 'CHD' && (diff2 < twoyesrDiff || diff2 > days)) {
        return { 'invalidChildAge': true };
      } else if (this.hotelTravellerData[formIndex].type === 'INF' && (diff2 >= twoyesrDiff)) {
        return { 'invalidInfantAge': true };
      }
    }
    }
      return null;
    };
  }
  minAgeValidator(control: AbstractControl): ValidationErrors | null {
    let result = null;

    if (control.value && control.valid) {
      let yearsDiff = DateUtils.getYearsDiffFromNow(control.value);
      if (yearsDiff < Constants.MIN_DOB_AGE) {
        result = { 'lessThanMinAge': true };
      }
    }
    return result;
  }

  zipCodeValidator(control: AbstractControl): Promise<ValidationErrors> | null | Observable<ValidationErrors> {

    return this.geoLocationService.getLocationByZipCode(control.value).pipe(
      map(response => ((response && response.results.length == 0 || response.status != 'OK') ? { invalidZipCode: true } : null)),
      catchError(() => null)
    );
  }

  addFFNDetails(passengerFormIndex: number, airlineObj: any, ffn: string) {

    let ffnForm = this.fb.group({
      frequent_flyer_number: [ffn],
      airlineCode: [airlineObj['code']],
      name: [airlineObj['name']],
      ffnAvailable: [true]
    });

    (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[passengerFormIndex]).controls['frequentFlyerInfo']).push(ffnForm);
  }
  addCLNDetails(passengerFormIndex: number, carObj: any, cln: string) {

    let ffnForm = this.fb.group({
      rentalCarLoyaltyNumber: [cln],
      rentalCarCode: [carObj['traflaPartnerCode']],
      rentalCarName: [carObj['partnerName']],
      clnAvailable: [true],
    });
    let loyaltyNo = (<FormArray>this.passengersForm.controls['passengers']).value.find((item) => item.carLoyalityInfo.find(item1 =>
      item1.rentalCarCode === carObj['traflaPartnerCode']));
    if (!loyaltyNo) {
      (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[passengerFormIndex]).controls['carLoyalityInfo']).push(ffnForm);
    }
    // 
  }

  addHLNDetails(passengerFormIndex: number, hotelObj: any, hln: string) {

    let hlnForm = this.fb.group({
      hotel_loyality_number: [hln],
      hotelCode: [hotelObj['traflaChainCode']],
      name: [hotelObj['name']],
      hlnAvailable: [true],
      'handler': hotelObj['handler'],
      'loyaltyPointsSupported': hotelObj['loyaltyPointsSupported']
    });
    let loyaltyNo = (<FormArray>this.passengersForm.controls['passengers']).value.find((item) => item.hotelLoyalityInfo.find(item1 =>
      item1.hotel_loyality_number === hln));
  //  if (!loyaltyNo) {
      (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[passengerFormIndex]).controls['hotelLoyalityInfo']).push(hlnForm);
   // }
  }

  setGender(formArrayIndex: number, value: string, item, item2) {
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let genderValue = passenger.get('gender').value;
    if (item.value === 0 && (this.userAccountInfoService.promptUserTosaveProfile && genderValue !== "" && item2 === 'DISABLED')) {
      this.toastr.warning(this.translateService.instant('personal.Pleaseclickoneditinformationtomakechanges'));
      return
    }
    passenger.get('gender').setValue(value);
    this.passengersForm.patchValue({ ...this.passengersForm });
  }

  isSelectedGender(formArrayIndex: number, value: string): boolean {
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let val = passenger.get('gender').value;
    return (val == value);
  }

  setSeatPref(formArrayIndex: number, value: string, item, item2) {
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let seatValue = passenger.get('seat').value;
    if (seatValue && (item.value === 0 && this.userAccountInfoService.promptUserTosaveProfile && item2 === 'DISABLED')) {
    //  this.toastr.warning("Please click on" + "'edit information'" + " to make changes");
    this.toastr.warning(this.translateService.instant('personal.Pleaseclickoneditinformationtomakechanges'));
      return
    }
    passenger.get('seat').setValue(value);
    this.passengersForm.patchValue({ ...this.passengersForm });
  }

  isSelectedSeatPref(formArrayIndex: number, value: string): boolean {
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let val = passenger.get('seat').value;
    return (val == value);
  }
  showToastr(item) {
    if (item === 'DISABLED') {
      //this.toastr.warning("Please click on" + "'edit information'" + " to make changes");
      this.toastr.warning(this.translateService.instant('personal.Pleaseclickoneditinformationtomakechanges'));
    }
  }
  changeStyle(item, item1) {
    if (item && item1 === 'DISABLED') {
      return { 'font-family': 'password', 'border': '1px solid #E7E6E4' };
    } else if (item && item1 !== 'DISABLED') {
      return { 'font-family': 'password', 'border': '1px solid var(--dark-bg-color)' };
    } else if (!item && item1 !== 'DISABLED') {
      return { 'font-family': 'ApercuProMono', 'border': '1px solid var(--dark-bg-color)' };
    } else if (!item && item1 === 'DISABLED') {
      return { 'font-family': 'ApercuProMono', 'border': '1px solid #E7E6E4' };
    }

  }
  showToastr1(item, item2?) {
    if (!item2 && !this.userAccountInfoService.promptUserTosaveProfile) {
      return;
    }
    if (item === 'DISABLED') {
    //  this.toastr.warning("Please click on" + "'edit information'" + " to make changes");
    this.toastr.warning(this.translateService.instant('personal.Pleaseclickoneditinformationtomakechanges'));
    }
  }
  checkTravelerNumNotAvailableCheckBox(formArrayIndex: number, field: string, element, item, item2, innerArrayIndex?: number,) {

    let val = element.checked;
    // let fieldName = (field == 'FFN') ? 'ffnAvailable' : 'ktnAvailable';

    let passenger: AbstractControl = this.getFormArrayPassengerObject(formArrayIndex);

    if (field == 'FFN') {
      let ffnNumber = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').value;
      if (ffnNumber && item.value === 0 && this.userAccountInfoService.promptUserTosaveProfile && item2 === 'DISABLED') {
        //   this.toastr.warning("Please click on edit informationto make changes");
        return
      }
      (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('ffnAvailable').setValue(val);
    } else if (field == 'HLN') {
      let hlnNumber = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('hotel_loyality_number').value;
      if (hlnNumber && item.value === 0 && this.userAccountInfoService.promptUserTosaveProfile && item2 === 'DISABLED') {
        // this.toastr.warning("Please click on edit informationto make changes");
        return
      }
      (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('hlnAvailable').setValue(val);
    } else if (field == 'CLN') {
      let clnNumber = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['carLoyalityInfo']).at(innerArrayIndex).get('rentalCarLoyaltyNumber').value;
      if (clnNumber && item.value === 0 && this.userAccountInfoService.promptUserTosaveProfile && item2 === 'DISABLED') {
        // this.toastr.warning("Please click on edit informationto make changes");
        return
      }
      (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['carLoyalityInfo']).at(innerArrayIndex).get('clnAvailable').setValue(val);
    } else {
      let ktnNumber = passenger.get('knownTravellerNumber').value;
      if ((ktnNumber !== "") && item.value === 0 && this.userAccountInfoService.promptUserTosaveProfile && item2 === 'DISABLED') {
        // this.toastr.warning("Please click on edit informationto make changes");
        return
      }
     
      passenger.get('ktnAvailable').setValue(val);
    }

    if (!val) {

      if (field == 'FFN') {
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').setValidators(null);
        //        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').setValue(null);
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').setErrors(null);
      } else if (field == 'HLN') {
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('hotel_loyality_number').setValidators(null);
        //       (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('hotel_loyality_number').setValue(null);
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('hotel_loyality_number').setErrors(null);
      } else if (field == 'CLN') {
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['carLoyalityInfo']).at(innerArrayIndex).get('rentalCarLoyaltyNumber').setValidators(null);
        //       (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('hotel_loyality_number').setValue(null);
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['carLoyalityInfo']).at(innerArrayIndex).get('rentalCarLoyaltyNumber').setErrors(null);
      }
      else {
        passenger.get('knownTravellerNumber').setValidators(null);
        //        passenger.get('knownTravellerNumber').setValue(null);
        passenger.get('knownTravellerNumber').setErrors(null);
      }
    }
    else {
      if (field == 'FFN') {
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').setValidators(Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').markAsTouched();
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('frequent_flyer_number').updateValueAndValidity();

      } else if (field == 'HLN') {
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('hotel_loyality_number').setValidators(Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('hotel_loyality_number').markAsTouched();
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('hotel_loyality_number').updateValueAndValidity();

      } else if (field == 'CLN') {
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['carLoyalityInfo']).at(innerArrayIndex).get('rentalCarLoyaltyNumber').setValidators(Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC)]));
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['carLoyalityInfo']).at(innerArrayIndex).get('rentalCarLoyaltyNumber').markAsTouched();
        (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['carLoyalityInfo']).at(innerArrayIndex).get('rentalCarLoyaltyNumber').updateValueAndValidity();

      } else {
        passenger.get('knownTravellerNumber').setValidators(Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_ALPHANUMERIC), Validators.minLength(8), Validators.maxLength(environment.maxKTNFieldLength)]));
        (<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['knownTravellerNumber'].markAsTouched();
        (<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['knownTravellerNumber'].updateValueAndValidity();

      }

    }
  }

  isTravelerNumAvailable(formArrayIndex: number, field: string, innerArrayIndex?: number): boolean {
    // let fieldName = (field == 'FFN') ? 'ffnAvailable' : 'ktnAvailable';
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let val;

    if (field == 'FFN') {
      val = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['frequentFlyerInfo']).at(innerArrayIndex).get('ffnAvailable').value;
    } else if (field == 'HLN') {
      let val1 = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('loyaltyPointsSupported').value;
      val = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['hotelLoyalityInfo']).at(innerArrayIndex).get('hlnAvailable').value;
      if (!val1) {
        val = true;
      }
    } else if (field == 'CLN') {
      val = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[formArrayIndex]).controls['carLoyalityInfo']).at(innerArrayIndex).get('clnAvailable').value;
    } else {
      val = passenger.get('ktnAvailable').value;
    }
    return (val);
  }


  getFormArrayPassengerObject(formArrayIndex: number): AbstractControl {
    return (<FormArray>this.passengersForm.controls['passengers']).at(formArrayIndex);
  }

  onAccordianClick(formArrayIndex: number) {
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let val = passenger.get('openFormOnUI').value;
    passenger.get('openFormOnUI').setValue(!val);

    if (!val) {

      for (let i = 0; i < (this.numOfFormPassengers); i++) {

        if (i != formArrayIndex) {
          let p = this.getFormArrayPassengerObject(i);
          p.get('openFormOnUI').setValue(false);
        }
      }
    }

    this.passengersForm.patchValue({ ...this.passengersForm });
  }

  isFormOpen(formArrayIndex: number): boolean {
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    let val = passenger.get('openFormOnUI').value;
    return val;
  }


  onSelectPassportCountry(formArrayIndex: number, event: any) {
    let val = event.target.value.split(":")[1];
    let passenger = this.getFormArrayPassengerObject(formArrayIndex);
    passenger.get('passportCountry').setValue(val.toUpperCase().trim());
    this.passengersForm.patchValue({ ...this.passengersForm });
  }

  getPassengerFormTitle(passengerFormArrayIndex: number): string {
    let passenger = this.getFormArrayPassengerObject(passengerFormArrayIndex);

    if (passenger.get('title').value &&
      passenger.get('firstName').value &&
      passenger.get('lastName').value
    ) {
      let name = passenger.get('title').value + '. ';
      name += passenger.get('firstName').value + ' ';

      if (passenger.get('middleName').value) {
        name += passenger.get('middleName').value + ' ';
      }

      name += passenger.get('lastName').value;
      if (this.noOfPassengers > 1) {
        name = this.hotelTravellerData[passengerFormArrayIndex].text + ' (' + name + ')';
      }
      return name;
    }
    if (this.noOfPassengers > 1) {
      return this.hotelTravellerData[passengerFormArrayIndex].text;
    } else {
      return this.translate.instant("personal.PassengerDetails");
    }

  }
  getPasenegrText(passengerFormArrayIndex: number) {
    let name ;
    if(this.searchService.ageGroupArray.length > 0 && this.searchService.infantBookingAllowed){
     name =this.translate.instant("personal.Passenger")  + (passengerFormArrayIndex + 1) + " " + this.searchService.ageGroupArray[passengerFormArrayIndex].label;
    }else{
     name = this.translate.instant("personal.Passenger")  + (passengerFormArrayIndex + 1);
    }
    return name;

  }
  changeaccordion(event, i) {

    this.accordionOpen[i] = event;
   
    let passenger: any = (this.passengersForm.get('passengers') as FormArray).at(i);
    for (let key in passenger.controls) {
      let value = passenger.controls[key];
      if ((i === 0 && (key === 'employeeType')) && (event && (value.value === 0 || value.value === 1))) {
        this.maskDob = false;
        this.maskAdd = false;
        this.maskPhone = false;
        this.maskZip = false;
        this.maskKtn = false;
        this.maskCln = false;
        this.maskEmeNnname = false;
        this.maskRel = false;
        this.maskDial1 = false;
        this.maskPhone1 = false;
        this.maskHln = false;
        this.maskPnat = false;
        this.maskPcon = false;
        this.maskFfn = false;
        this.maskDial = false;
        this.maskPnum = false;
        this.maskPexp = false;
      } else if (((key === 'employeeType') && value.value) && (value.value !== 0 && value.value !== 1 && event)) {
        this.maskDob = true;
        this.maskAdd = true;
        this.maskPhone = true;
        this.maskZip = true;
        this.maskKtn = true;
        this.maskEmeNnname = true;
        this.maskRel = true;
        this.maskDial1 = true;
        this.maskPhone1 = true;
        this.maskCln = true;
        this.maskHln = true;
        this.maskPnat = true;
        this.maskPcon = true;
        this.maskFfn = true;
        this.maskPnum = true;
        this.maskDial = true;
        this.maskPexp = true;
      }
      if (((key === 'employeeType') && value.value) && (value.value !== 0 && value.value !== 1 && event)) {
        this.maskDial = true;
        this.maskDial1 = true;
      }
      break;
    }
   

  }
  indexOfOpenedAccordion(i){
    if(this.numOfFormPassengers > 1){
     this.isFormValid(i);
    if(this.invalid){
      return;      
    }
    }
   
    if(this.userAccountInfoService.userFormOpen > -1 && this.numOfFormPassengers > 1){
    let  travellerChange = JSON.parse(JSON.stringify(this.passengersForm.controls['passengers'].value[0]));
    if (this.userAccountInfoService.userFormOpen ===0 && travellerChange['employeeType'] === 0 ){
      this.userAccountInfoService.promptUserTosaveProfile = false;
      this.enabledUserEntry((this.passengersForm.get('passengers') as FormArray).at(0));
    }else if (this.userAccountInfoService.userFormOpen !==0 && travellerChange['employeeType'] === 0 ) {
      this.userAccountInfoService.promptUserTosaveProfile = true;
      this.disabledUserEntry((this.passengersForm.get('passengers') as FormArray).at(0));
    }
      this.saveProfileData(this.userAccountInfoService.userFormOpen);
    
      this.userAccountInfoService.userFormOpen = i;  
      setTimeout(() => {
        this.scrolltoDiv(i);
      }, 100);
     
   
  }
}
scrolltoDiv(i){
setTimeout(() => {
 document.getElementById("element_within_div"+i).scrollIntoView({behavior: 'smooth'});
}, 100);
}
  Opened(i) {
    if (this.numOfFormPassengers > 1) {
      for (let j = 0; j < this.accordionOpen.length; j++) {
        if (i !== 0) {
          this.accordionOpen[j] = false;
          // this.accordion.groups[j].isOpen =false;
        }
      }
      //if(this.accordion.groups[i].isOpen){
      //  if(this.accordionOpen[i]){
          this.userAccountInfoService.userFormOpen = 0;
      //  }
      this.accordionOpen[0] = !this.accordionOpen[0];

      //}
      
      // setTimeout(()=>{
      // this.openOtherAccordion(i);
      // toggleAccordionClose(this.accordion.groups[i].isOpen,this.accordion.groups,i);
      // this.accordion.groups[i].isOpen=false;
      //},100)


    }else{
    this.userAccountInfoService.userFormOpen = i;
    }
  }
  searchByApproverNameAndEmailChanged(term: string, item: any) {
    term = term.toLowerCase();
    return (item.Name && item.Name.toLowerCase().indexOf(term) > -1)
  }
  getPassengerDetailText(passengerIndex: number) {
    let passenger = this.getFormArrayPassengerObject(passengerIndex);
    if ( passenger.get('firstName').value &&
      passenger.get('lastName').value && passenger.get('dateOfBirth').value
    ) {
      let datePipe = new DatePipe('en-US');

      let text = 'DOB - ' + datePipe.transform(new Date(passenger.get('dateOfBirth').value), 'd MMM, yyyy');
      if (this.uniqueAirlines.length > 0 && passenger.get('knownTravellerNumber').value) {
        text = text + ' | KTN - ' + passenger.get('knownTravellerNumber').value;
      }
      // passenger.get('frequentFlyerInfo').at(innerArrayIndex).get('ffnAvailable').value
      let ffnText = undefined;
      for (let itr = 0; itr < (<FormArray>(<FormGroup>passenger).controls['frequentFlyerInfo']).length; itr++) {
        let ffnValue = (<FormArray>(<FormGroup>passenger).controls['frequentFlyerInfo']).at(itr).get('frequent_flyer_number').value;
        let ffnAirline = (<FormArray>(<FormGroup>passenger).controls['frequentFlyerInfo']).at(itr).get('airlineCode').value
        if (ffnValue && ffnValue.trim().length > 0) {
          if (ffnText) ffnText = ffnText + ', '; else ffnText = "";
          ffnText = ffnText + ffnAirline + ' ' + ffnValue;
        }
      }
      if (ffnText !== undefined) {
        text = text + ' | FFN - ' + ffnText;
      }

      let hlnText = undefined;
      for (let itr = 0; itr < (<FormArray>(<FormGroup>passenger).controls['hotelLoyalityInfo']).length; itr++) {
        let hlnValue = (<FormArray>(<FormGroup>passenger).controls['hotelLoyalityInfo']).at(itr).get('hotel_loyality_number').value;
        let hlnHotel = (<FormArray>(<FormGroup>passenger).controls['hotelLoyalityInfo']).at(itr).get('hotelCode').value
        if (hlnValue && hlnValue.trim().length > 0) {
          if (hlnText) hlnText = hlnText + ', '; else hlnText = "";
          hlnText = hlnText + hlnHotel + ' ' + hlnValue;
        }
      }
      if (hlnText !== undefined) {
        text = text + ' | ' + this.translate.instant("personal.LoyalityNo") + '- ' + hlnText;
      }
      return text;
    }
    return '';
  }
  onHidePicker() {
    this.searchService.calendarOpenEventListsner.next(null);
  }

  onShowPicker(event, picker) {
    this.searchService.calendarOpenEventListsner.next(picker);
    const dayHoverHandler = event.dayHoverHandler;
    const hoverWrapper = (hoverEvent) => {
      const { cell, isHovered } = hoverEvent;

      if ((isHovered &&
        !!navigator.platform &&
        /iPad|iPhone|iPod/.test(navigator.platform)) &&
        'ontouchstart' in window
      ) {
        (picker as any)._datepickerRef.instance.daySelectHandler(cell);
      }

      return dayHoverHandler(hoverEvent);
    };
    event.dayHoverHandler = hoverWrapper;
  }

  goToPayment() {
    this.updateFormValueInBookingService();
    this.goToPaymentRequest.emit();
  }

  preventEntry(event) {
  }

  handleAddressChange(index, address) {
    (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[index]).controls['address']).setValue(address.formatted_address);

    this.autoFillZipCode(index, address);
  }

  autoFillZipCode(index, address) {
    let zipCodeControl = (<FormArray>(<FormGroup>(<FormArray>this.passengersForm.controls['passengers']).controls[index]).controls['zipCode']);
    if (zipCodeControl.status == "INVALID") {
      address.address_components.map(item => {
        if (item.types.includes("postal_code")) {
          zipCodeControl.setValue(item.short_name);
        }
      })
    }
  }

  get numOfFormPassengers(): number {
    return (<FormArray>this.passengersForm.get('passengers')).length;
  }
  selectedCountryCode1 = '';
  onCountrySelected1(item, i) {
    let passenger = this.getFormArrayPassengerObject(i);
    if (item) {
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].setValidators([Validators.required, this.conditionalValidators(true, [CommonUtils.PhoneNumberValidator(item.code)])]);
      this.selectedCountryCode1 = item.code;
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].markAsDirty();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['contactNumber'].updateValueAndValidity();

    } else {
      this.selectedCountryCode1 = '';
      if (passenger['relationship'].value === '' && (passenger['contactNumber'].value === '' && passenger['name'].value === '')) {
        passenger['relationship'].clearValidators();
        passenger['emergencyContactName'].clearValidators();
        passenger['contactNumber'].clearValidators();
        // this.emergencyContact.controls['dialCode1'].updateValueAndValidity();
        passenger['contactNumber'].updateValueAndValidity();
        passenger['emergencyContactName'].updateValueAndValidity();
        passenger['relationship'].updateValueAndValidity();
        if (passenger['dialCode1'].invalid) {
          passenger['dialCode1'].clearValidators();
          passenger['dialCode1'].updateValueAndValidity();
        }
      }
    }
  }
  getDialCode(text, i) {
    let passenger = this.getFormArrayPassengerObject(i);
  }

  selectedCountryCode = '';
  onCountrySelected(item,i) {
    if (item) {
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['phoneNumber'].setValidators([Validators.required, this.conditionalValidators(true, [CommonUtils.PhoneNumberValidator(item.code)])]);
      this.selectedCountryCode = item.code;
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['phoneNumber'].markAsDirty();
      (<FormArray>(<FormGroup>this.passengersForm.controls['passengers']).controls[i]).controls['phoneNumber'].updateValueAndValidity();
    } else {
      this.selectedCountryCode = '';
    }

  }

  searchByNameOrCode(term: string, item) {
    return CommonUtils.searchByNameOrCode(term, item);
  }

  getCountryJSON(c: string) {
    return CommonUtils.getCountryJSON(c);

  }

  getCountryCode(code, phoneNumber) {
    //if(phoneNumber && Object.keys((phoneNumber)).length >0){
    return CommonUtils.getCountryCode(code, phoneNumber, this.selectedCountryCode);
    //}
  }

  unsubscribeEvents() {
    this.deviceSubscription.unsubscribe();
    if (this.listEmployeesResponseSubscription) {
      this.listEmployeesResponseSubscription.unsubscribe();
    }
  }

  ngOnDestroy() {
    this.unsubscribeEvents();
  }

  getPassengerFormArrayControl(): AbstractControl[] {
    return (<FormArray>this.passengersForm.controls['passengers']).controls;
  }

  preventCheck(event) {
    event.preventDefault();
  }

  isValidDate(dob: string) {
    if (dob && dob !== null) {
      let dobArr = dob.split('/');
      if (dobArr.length != 3) return false;
      for (let val of dobArr) {
        if (val.trim().length == 0) {
          return false;
        }
      }
      return true;
    }
    return false;
  }
  isDOBCalendarReadOnly() {
    if (this.isNjoySpecificRelease || this.isMobile) {
      return true;
    }
    return false;
  }
  getemployeeInfo(userAccountInfoObj) {

  }
  getDateType(date){
    let datePipe = new DatePipe('en-US');
    let label1 = datePipe.transform(new Date(date), 'MM/dd/yyyy');
    return label1;
  }
  public getPrimaryTravellerInfo(userAccountInfoObj: UserAccountInfo) {
    if (userAccountInfoObj && userAccountInfoObj.userInfo) {
      let travellerData = {
        "passengers": [
          {
            "title": userAccountInfoObj.userInfo.title ? userAccountInfoObj.userInfo.title.trim() : "",
            "firstName": userAccountInfoObj.userInfo.firstname ? userAccountInfoObj.userInfo.firstname.trim() : "",
            "middleName": userAccountInfoObj.userInfo.middlename ? userAccountInfoObj.userInfo.middlename.trim() : "",
            "lastName": userAccountInfoObj.userInfo.lastname ? userAccountInfoObj.userInfo.lastname.trim() : "",
            "gender": userAccountInfoObj.userInfo.gender ? userAccountInfoObj.userInfo.gender.trim() : null,
            "dateOfBirth": userAccountInfoObj.userInfo.dob && this.isValidDate(userAccountInfoObj.userInfo.dob) ? this. getDateType(userAccountInfoObj.userInfo.dob) : null,
            "email": userAccountInfoObj.userInfo.email,
            "phoneNumber": userAccountInfoObj.userInfo.phoneNumber && userAccountInfoObj.userInfo.phoneNumber.number ? userAccountInfoObj.userInfo.phoneNumber.number.trim() : "",
            "address": (userAccountInfoObj.userInfo.home && Object.keys(userAccountInfoObj.userInfo.home).length > 0) ? userAccountInfoObj.userInfo.home.address : null,
            "zipCode": userAccountInfoObj.userInfo.home && userAccountInfoObj.userInfo.home.postal_code ? userAccountInfoObj.userInfo.home.postal_code.trim() : "",
            "ktnAvailable": true,
            "knownTravellerNumber": userAccountInfoObj.userInfo.known_traveller_number ? userAccountInfoObj.userInfo.known_traveller_number.trim() : "",
            "dialCode": userAccountInfoObj.userInfo.phoneNumber &&
              userAccountInfoObj.userInfo.phoneNumber.countryCode ? userAccountInfoObj.userInfo.phoneNumber.countryCode : this.setDialCode(this.userAccountInfoService.getAccountInfo()),
            "passportNumber": userAccountInfoObj.userInfo.passportDTO && userAccountInfoObj.userInfo.passportDTO.passportNumber ? userAccountInfoObj.userInfo.passportDTO.passportNumber : null,
            "passportCountry": (userAccountInfoObj.userInfo.passportDTO && userAccountInfoObj.userInfo.passportDTO.countryCode !== "" && this.getCountryJSON(userAccountInfoObj.userInfo.passportDTO.countryCode) !== undefined) ? this.getCountryJSON(userAccountInfoObj.userInfo.passportDTO.countryCode) : null,
            "passportNationality": userAccountInfoObj.userInfo.nationality && userAccountInfoObj.userInfo.nationality ? this.getCountryJSON(userAccountInfoObj.userInfo.nationality) : null,
            "passportExpiryDate": userAccountInfoObj.userInfo.passportDTO && this.isValidDate(userAccountInfoObj.userInfo.passportDTO.expiryDate)
              ? userAccountInfoObj.userInfo.passportDTO.expiryDate : null,
            "openFormOnUI": false,
            "emergencyContactName": userAccountInfoObj && (userAccountInfoObj.userInfo && userAccountInfoObj.userInfo.emergencyContact) ? userAccountInfoObj.userInfo.emergencyContact.name : null,
            "relationship": userAccountInfoObj && (userAccountInfoObj.userInfo && userAccountInfoObj.userInfo.emergencyContact) ? userAccountInfoObj.userInfo.emergencyContact.relationship : null,
            "contactNumber": userAccountInfoObj && (userAccountInfoObj.userInfo && userAccountInfoObj.userInfo.emergencyContact) && userAccountInfoObj.userInfo.emergencyContact.contactNumber ? userAccountInfoObj.userInfo.emergencyContact.contactNumber.number : null,
            "dialCode1": userAccountInfoObj && (userAccountInfoObj.userInfo && userAccountInfoObj.userInfo.emergencyContact) && userAccountInfoObj.userInfo.emergencyContact.contactNumber &&
              userAccountInfoObj.userInfo.emergencyContact.contactNumber.countryCode ? userAccountInfoObj.userInfo.emergencyContact.contactNumber.countryCode : this.setDialCode(this.userAccountInfoService.getAccountInfo()),
            "frequentFlyerInfo": [
            ],
            "hotelLoyalityInfo": [
            ],
            "carLoyalityInfo": [
            ],
            "roomId": '1',
            "seat": userAccountInfoObj.seatPref ? userAccountInfoObj.seatPref : null
          }
        ]
      }



      if (this.uniqueAirlines.length > 0) {
        let ffnData = this.getAirLoyalitynumber(userAccountInfoObj.userInfo)
        travellerData.passengers[0].frequentFlyerInfo = ffnData;
      }

      if (this.uniqueHotels.length > 0) {
        let hlnData = this.getLoyalitynumber(userAccountInfoObj.userInfo)
        travellerData.passengers[0].hotelLoyalityInfo = hlnData;
      }


      if (this.uniqueCars.length > 0) {
        let clnData = this.getCarLoyalityNumbers(userAccountInfoObj.userInfo)
        travellerData.passengers[0].carLoyalityInfo = clnData;
      }
      return travellerData;

    }
    return {};

  }
  checkIfHotelIsHotelBeds(inputObject) {
    if (inputObject && inputObject['handler']) {
      if (inputObject['handler'].value === 'HotelBeds') {
        return true;
      }
    }
    return false;
  }
  getLoyalitynumber(userAccountInfoObj) {
    let hlnData1 = []
    for (let hotelObj of this.uniqueHotels) {
      let hlnNumber = "";
      if (userAccountInfoObj && userAccountInfoObj && userAccountInfoObj.loyalityCards) {
        let filteredObj: any = userAccountInfoObj.loyalityCards.find(x => x.chain === getTraflaHotelChainCode(hotelObj['traflaChainCode']));
        if (filteredObj && filteredObj.number) {
          hlnNumber = filteredObj.number.trim();
        } else {
          filteredObj = userAccountInfoObj.loyalityCards.find(x => x.hotel_name === hotelObj['name']);
          if (filteredObj && filteredObj.number) {
            hlnNumber = filteredObj.number ? filteredObj.number.trim() : null;
          }
        }
      }
      let hlnData = {
        "hotel_loyality_number": hlnNumber ? hlnNumber : null,
        "hotelCode": hotelObj['traflaChainCode'] ?  hotelObj['traflaChainCode']:hotelObj['hotelChain'],
        "name": hotelObj['name'],
        "hlnAvailable": true,
        'handler': hotelObj['handler'],
        'loyaltyPointsSupported': hotelObj['loyaltyPointsSupported']

      }
      if (hlnData.loyaltyPointsSupported) {
        hlnData1.push(hlnData);
      }
    }
    return hlnData1;
  }
  isDisabled1(item) {
    if (item === 'PriceLine') {
      return true;
    } else if (item === 'HotelBeds') {
      return true;
    } else {
      return null;
    }
  }
  isDisabled(item) {
    if (item === 'PriceLine') {
      return true;
    } else if (item === 'HotelBeds') {
      return true;
    } else {
      return false;
    }
  }
  getAirLoyalitynumber(userAccountInfoObj) {
    let hlnData1 = []
    for (let airlineObj of this.uniqueAirlines) {
      let ffnNumber = undefined;
      if (userAccountInfoObj.ffnMapping) {
        let filteredObj: any = userAccountInfoObj.ffnMapping.find(x => x.airline_code === airlineObj['code']);
        if (filteredObj) {
          ffnNumber = filteredObj.frequent_flyer_number ? filteredObj.frequent_flyer_number.trim() : null;
        }
      }
      let ffnData = {
        "frequent_flyer_number": ffnNumber ? ffnNumber : null,
        "airlineCode": airlineObj['code'],
        "name": airlineObj['name'],
        "ffnAvailable": true
      }
      hlnData1.push(ffnData);
    }
    return hlnData1;
  }
  getCarLoyalityNumbers(userAccountInfoObj) {
    let clnData1 = [];
    for (let carObj of this.uniqueCars) {
      let clnNumber = "";
      if (userAccountInfoObj.carLoyaltyNumbers) {
        let filteredObj: any = userAccountInfoObj.carLoyaltyNumbers.find(x => x.traflaPartnerCode === carObj['traflaPartnerCode']);
        if (filteredObj && filteredObj.rentalCarLoyaltyNumber) {
          clnNumber = filteredObj.rentalCarLoyaltyNumber.trim();
        } else {
          filteredObj = userAccountInfoObj.carLoyaltyNumbers.find(x => x.rentalCarName === carObj['partnerName']);
          if (filteredObj && filteredObj.rentalCarLoyaltyNumber) {
            clnNumber = filteredObj.rentalCarLoyaltyNumber ? filteredObj.rentalCarLoyaltyNumber.trim() : null;
          }
        }
      }
      let clnData = {
        "rentalCarLoyaltyNumber": clnNumber ? clnNumber : null,
        "rentalCarCode": carObj['traflaPartnerCode'],
        "rentalCarName": carObj['partnerName'],
        "clnAvailable": true,
      }
      clnData1.push(clnData);
    }
    return clnData1;
  }
  public getTravellerInfo(userInfo: UserInfo) {
    if (userInfo) {
      const passportData = {
        passportNumber: (userInfo.passportDTO  && userInfo.passportDTO.passportNumber ) ? userInfo.passportDTO.passportNumber : null,
        passportCountry:"",
        passportNationality:"",
        passportExpiryDate:""
      };

      // let travellerData = {
      // "passengers": [
      let passenger = {
        "title": userInfo.title ? userInfo.title.trim() : "",
        "firstName": userInfo.firstname ? userInfo.firstname.trim() : "",
        "middleName": userInfo.middlename ? userInfo.middlename.trim() : "",
        "lastName": userInfo.lastname ? userInfo.lastname.trim() : "",
        "gender": userInfo.gender ? userInfo.gender.trim() : null,
        "dateOfBirth": userInfo.dob && this.isValidDate(userInfo.dob) ?  this.getDateType(userInfo.dob) : null,
        "email": userInfo.email,
        "phoneNumber": userInfo.phoneNumber && userInfo.phoneNumber.number ? userInfo.phoneNumber.number.trim() : null,
        "address": Object.keys((userInfo.home)).length > 0  ? userInfo.home.address : null,
        "zipCode": userInfo.home && userInfo.home.postal_code ? userInfo.home.postal_code.trim() : "",
        "ktnAvailable": userInfo.known_traveller_number ? true : false,
        "knownTravellerNumber": userInfo.known_traveller_number ? userInfo.known_traveller_number.trim() : "",
        "dialCode": userInfo.phoneNumber &&
          userInfo.phoneNumber.countryCode ? userInfo.phoneNumber.countryCode : this.setDialCode(this.userAccountInfoService.getAccountInfo()),
        "passportNumber": (userInfo.passportDTO
            && userInfo.passportDTO.passportNumber )
            ? userInfo.passportDTO.passportNumber : null,
        "passportCountry": (userInfo.passportDTO
            && Object.keys(userInfo.passportDTO).length > 0
            && userInfo.passportDTO.countryCode!=='')
            ? this.getCountryJSON(userInfo.passportDTO.countryCode) : null,
        "passportNationality": userInfo.nationality ? this.getCountryJSON(userInfo.nationality) : null,
        "passportExpiryDate": (userInfo.passportDTO
            && Object.keys(userInfo.passportDTO).length > 0
            && this.isValidDate(userInfo.passportDTO.expiryDate))
          ? userInfo.passportDTO.expiryDate : null,
        "openFormOnUI": false,
        "emergencyContactName": (userInfo && userInfo.emergencyContact) ? userInfo.emergencyContact.name : null,
        "relationship": (userInfo && userInfo.emergencyContact) ? userInfo.emergencyContact.relationship : null,
        "contactNumber": (userInfo && userInfo.emergencyContact) && userInfo.emergencyContact.contactNumber ? userInfo.emergencyContact.contactNumber.number : null,
        "dialCode1": (userInfo && userInfo.emergencyContact) && userInfo.emergencyContact.contactNumber &&
          userInfo.emergencyContact.contactNumber.countryCode ? userInfo.emergencyContact.contactNumber.countryCode : this.setDialCode(this.userAccountInfoService.getAccountInfo()),
        "frequentFlyerInfo": [
        ],
        "hotelLoyalityInfo": [
        ],
        "carLoyalityInfo": [
        ],
        "roomId": '1',
        "seat": userInfo.seatPref ? userInfo.seatPref : null
      }
      // ]
      // }

      for (let airlineObj of this.uniqueAirlines) {
        let ffnNumber = undefined;
        if (userInfo.ffnMapping) {
          let filteredObj: any = userInfo.ffnMapping.find(x => x.airline_code === airlineObj['code']);
          if (filteredObj) {
            ffnNumber = filteredObj.frequent_flyer_number ? filteredObj.frequent_flyer_number.trim() : null;
          }
        }
        let ffnData = {
          "frequent_flyer_number": ffnNumber ? ffnNumber : null,
          "airlineCode": airlineObj['code'],
          "name": airlineObj['name'],
          "ffnAvailable": true
        }
        passenger.frequentFlyerInfo.push(ffnData);
      }


      for (let hotelObj of this.uniqueHotels) {
        let hlnNumber = "";
        if (userInfo.loyalityCards) {
          let filteredObj: any = userInfo.loyalityCards.find(x => x.chain === getTraflaHotelChainCode(hotelObj['traflaChainCode']));
          if (filteredObj && filteredObj.number) {
            hlnNumber = filteredObj.number.trim();
          } else {
            filteredObj = userInfo.loyalityCards.find(x => x.hotel_name === hotelObj['name']);
            if (filteredObj && filteredObj.number) {
              hlnNumber = filteredObj.number.trim();
            }
          }
        }
        let hlnData = {
          "hotel_loyality_number": hlnNumber ? hlnNumber : null,
          "hotelCode": hotelObj['traflaChainCode'],
          "name": hotelObj['name'],
          "hlnAvailable": true,
          'handler': hotelObj['handler'],
          'loyaltyPointsSupported': hotelObj['loyaltyPointsSupported']

        }
        if (hlnData.loyaltyPointsSupported) {
          passenger.hotelLoyalityInfo.push(hlnData);
        }
      }
      for (let carObj of this.uniqueCars) {
        let clnNumber = "";
        if (userInfo.carLoyaltyNumbers) {
          let filteredObj: any = userInfo.carLoyaltyNumbers.find(x => x.traflaPartnerCode === carObj['traflaPartnerCode']);
          if (filteredObj && filteredObj.rentalCarLoyaltyNumber) {
            clnNumber = filteredObj.rentalCarLoyaltyNumber.trim();
          } else {
            filteredObj = userInfo.carLoyaltyNumbers.find(x => x.rentalCarName === carObj['partnerName']);
            if (filteredObj && filteredObj.rentalCarLoyaltyNumber) {
              clnNumber = filteredObj.rentalCarLoyaltyNumber.trim();
            }
          }
        }
        let clnData = {
          "rentalCarLoyaltyNumber": clnNumber ? clnNumber : null,
          "rentalCarCode": carObj['traflaPartnerCode'],
          "rentalCarName": carObj['partnerName'],
          "clnAvailable": true,
        }
        passenger.carLoyalityInfo.push(clnData);
      }

      return passenger;

    }
    return {};

  }

  buildFlightPassengerTypeMap(data: FlightSearchOptions) {
    let titleArray = [];
    for (let index = 0; index < data.adultCount; index++) {
      let entry: any = {};
      entry.type = 'ADT';
      entry.text = this.translate.instant("personal.AdultPassenger") + " #" + (index + 1);
      titleArray.push(entry)
    }
    for (let index = 0; index < data.childCount; index++) {
      let entry: any = {};
      entry.type = 'CHD';
      entry.text = this.translate.instant("personal.ChildPassenger") + " #" + (index + 1);
      titleArray.push(entry)
    }

    for (let index = 0; index < data.infantCount; index++) {
      let entry: any = {};
      entry.type = 'INF';
      entry.text = this.translate.instant("personal.InfantPassenger") + " #" + (index + 1);
      titleArray.push(entry)
    }
    return titleArray;
  }

  buildHotelPassengerTitles(userRoomData: any) {
    let titleArray = [];
    titleArray.push({ 'type': 'ADT', 'text': this.translate.instant("personal.Room") + ' 1 ' + this.translate.instant("personal.Adult") + ' 1' });

    var counter = 1
    var nextCOunter = 0;

    for (let val in userRoomData) {

      if ('numberOfAdult' in userRoomData[val]) {
        nextCOunter += userRoomData[val].numberOfAdult
      } else if ('numberOfTraveller' in userRoomData[val]) {
        nextCOunter += userRoomData[val].numberOfTraveller
      }
      var adultCounter = 0;

      for (let i = counter; i < nextCOunter; i++) {
        if (counter == 1) {
          let text = this.translate.instant("personal.Room") + " " + (parseInt(val) + 1) + " " + this.translate.instant("personal.Adult") + " "
            + (adultCounter + 2);
          titleArray.push({ 'type': 'ADT', 'text': text });
        } else {
          let text = this.translate.instant("personal.Room") + " " + (parseInt(val) + 1) + " " + this.translate.instant("personal.Adult") + " "
            + (adultCounter + 1);
          titleArray.push({ 'type': 'ADT', 'text': text });
        }
        counter++;
        adultCounter++;


      }
      nextCOunter += userRoomData[val].numberOfChildren;
      var childrenCounter = 0;
      let ages = userRoomData[val].childrenAges;
      for (let i = counter, ndx = 0; i < nextCOunter; i++, ndx++) {
        let text =
          this.translate.instant("personal.Room") + " " + (parseInt(val) + 1) + " " + this.translate.instant("personal.Child") + " "
          + (childrenCounter + 1);
        if (ages[ndx] <= 2) {
          titleArray.push({ 'type': 'INF', 'text': text });
        } else {
          titleArray.push({ 'type': 'CHD', 'text': text });
        }
        counter++;

        childrenCounter++;


      }

    }
    return titleArray;

  }
  getPhoneNumberMask(inputDIalCode) {
    if (inputDIalCode) {
      return CommonUtils.getPhoneNumberMask(inputDIalCode);
    }
  }
  getPhoneNumberPaceHolder(inputDIalCode) {
    return CommonUtils.getPhoneNumberPaceHolder(inputDIalCode);
  }
  buildHotelRoomIdArray(userRoomData: any) {
    let roomIdArray = [];
    roomIdArray.push('1');

    var counter = 1
    var nextCOunter = 0;

    for (let val in userRoomData) {

      if ('numberOfAdult' in userRoomData[val]) {
        nextCOunter += userRoomData[val].numberOfAdult
      } else if ('numberOfTraveller' in userRoomData[val]) {
        nextCOunter += userRoomData[val].numberOfTraveller
      }
      var adultCounter = 0;

      for (let i = counter; i < nextCOunter; i++) {
        if (counter == 1) {
          roomIdArray.push(
            parseInt(val) + 1);
        } else {
          roomIdArray.push(
            parseInt(val) + 1);
        }
        counter++;
        adultCounter++;


      }
      nextCOunter += userRoomData[val].numberOfChildren;
      var childrenCounter = 0;

      for (let i = counter; i < nextCOunter; i++) {

        roomIdArray.push(
          parseInt(val) + 1);


        counter++;

        childrenCounter++;


      }

    }

    return roomIdArray;

  }
   // Code For getting Passenger details From BookedFor and BookedForstoken
   getPassengerDetailFromEmail(email){
    let subscription = this.adminPanelService.employeeListResponseObservable$.subscribe(response => {      
      if(response){
        this.filterPassengerDetailFromResponseAndEmail(this.allEmployeeList,email,subscription);
      }      
    });
  }
  filterPassengerDetailFromResponseAndEmail(res,email,subscription){
   var passengerData =  res.filter(function (el) {
    return ((el.email.toLowerCase().startsWith(email.toLowerCase())));
    });
    let travellerData = {
      "passengers": []
    };
    for (let userInfoItem of passengerData) {
      travellerData.passengers.push(this.getTravellerInfo(userInfoItem));
    }
    this.gallopLocalStorage.setItem("passengersFormData", JSON.stringify(travellerData));
    this.applyChangesInternal();
    subscription.unsubscribe();      
  }
  loyaltyNumberMandatoryOrNot() {
    let loyalNumberMandatory = false;
    if(this.pageMode == 'emailflowAgent'){
      let processedOptions: any = processQuotationResponseInternal(this.emailQuotOptions);
      if (processedOptions[0].eventType == 'hotel' && Object.keys(processedOptions[0]).includes('eventOptions') && Object.keys(processedOptions[0].eventOptions[0]).includes('rateDetail') &&  Object.keys(processedOptions[0].eventOptions[0].rateDetail).includes('loyaltyNumberMandatory') && processedOptions[0].eventOptions[0].rateDetail.loyaltyNumberMandatory) {
        loyalNumberMandatory = processedOptions[0].eventOptions[0].rateDetail.loyaltyNumberMandatory;
        return loyalNumberMandatory;
      }
    }
      if(this.emailQuotOptions.hotelOptions && this.emailQuotOptions.hotelOptions[0] &&this.emailQuotOptions.hotelOptions[0].loyaltyNumberMandatory ){
        loyalNumberMandatory = this.emailQuotOptions.hotelOptions[0].loyaltyNumberMandatory;
      }
      else if(this.emailQuotOptions.hotelOptionsList && this.emailQuotOptions.hotelOptionsList[0] && this.emailQuotOptions.hotelOptionsList[0][0] && this.emailQuotOptions.hotelOptionsList[0][0].loyaltyNumberMandatory){
      loyalNumberMandatory = this.emailQuotOptions.hotelOptionsList[0][0].loyaltyNumberMandatory;
    return loyalNumberMandatory;
      }
  }
}
