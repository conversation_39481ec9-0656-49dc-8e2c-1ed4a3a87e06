@import "../../../variables.scss";

.header-lang-block {
  float: right;
  margin-bottom: 30px;
}

.header-right {
  font-size: 12px;
  margin-top: 5px !important;
  margin-bottom: 5px !important;
  color: var(--hyperlink-color) ;
  text-align: right;
  float: right;
}
.user-profile-name{
  color: var(--hyperlink-color);
}
.image {
  max-height: 80px;
  max-width: 200px;
  display: inline-block;
  margin-top: 20px;
  text-align: center;
  align-items: center;
}

.add {
  height: 24px;
  width: auto !important;
  white-space: nowrap;
  color: #2D57FA;
  font-family: "apercu-r";
  font-size: 15px !important;
  font-weight: bold;
  letter-spacing: 0.6px;
  line-height: 25px;
  text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}
.eventStarttime{
  margin-left: 15px;
  font-family: var(--globalFontfamilyr);font-weight: bold;;
  margin-bottom: 0px;
}
.eventtimings1{
  display: flex;
  margin-top: 0px;
}
.eventtimings{
  display: flex;
}
.modal-body {
  padding: 28px 25px 47px 25px;
  border-radius: 0 0 5px 5px;
}
.add1 {
  height: 24px;
  width: 68px;
  color: #2D57FA;
  font-family: "apercu-r";
  font-size: 15px;
  font-weight: bold;
  letter-spacing: 1px;
  line-height: 25px;
}
.eventList{
  padding-left: 26px;
}
.events{
  cursor: pointer;
  font-size: 14px;
  text-transform: capitalize;
}
.eventStart{
  font-family: var(--globalFontfamilyr);font-weight: bold;;
  margin-right: 5px;
}
.down-arrow {
  position: relative;
  left: 20px;
  top: 0px;
}
.removeTransform{
  transform: scaleY(-1);
}
.btn-normal {
  height: 40px;
  width: 150px;
  letter-spacing: 1px;
  background-color: transparent;
  margin-top: 15px;
  border: none;
  box-shadow: none;
}
.btn-secondary {
  height: 40px;
  width: 150px !important;
  border-radius: 0px !important;
  letter-spacing: 1px;
  background-color: var(--button-bg-color) !important;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
  margin-top: 15px;
  border: none;
}
.header-right1 {
  margin-top: 5px !important;
  margin-bottom: 5px !important;
  color: var(--hyperlink-color) !important;
  visibility: hidden;
}
.filter-row {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
}
.text-button {
  background: none;
  box-shadow: none;
  border: none;
  padding: 0 20px;
}

.text-button:first-child {
  padding-left: 0;
}

.text-button:last-child {
  padding-right: 0;
}
.subheading{
  font-size: 16px;
}
.heading{
  text-transform: capitalize
}
.eventDetails{
  float: left;
  background: #fff;
    width: 100%;
    padding: 20px;
}
.multibookingDetails{
  float: right;
}
.rebookingDetails{
  display: flex;
  padding: 10px;
  align-items: center;
  justify-content: space-between;
  background-color: #FFFCD2;
  position: relative;
  bottom: -5px;
}
.call-amnet-link {
  text-decoration: underline;
  font-size: 16px;
  color: var(--dark-bg-color);
}

.user-profile-link {
  width: 100%;
  padding: 20px 20px 15px 20px;
}

.user-profile-box-container {
  position: relative;
  margin-right: 0px;
  display: flex;
}

.user-profile-name {
  position: relative;
  padding-left: 16px;
  margin-left: 0px;
  padding-right: 15px;
  font-size: 12px;
}
.username{
  white-space: nowrap;
}
.user-profile-name::before {
  background-color: var(--button-bg-color) !important;
  height: 10px;
  width: 10px;
  display: none;
  position: absolute;
  left: 0;
  content: '';
  border-radius: 50%;
  top: 35px;
}

.newbutton {
  background-color: #EDFAFC;
  border: none;
  cursor: pointer;
  position: relative;
  font-size: 12px;
  opacity: 0.6;
  text-decoration: underline;
}

.active {
  font-weight: bold;
  background-color: #EDFAFC;
  border: none;
  text-decoration: none;
  cursor: pointer;
  font-size: 12px;
  opacity: 1;
}

button:active,
button:visited,
button:focus {
  opacity: 2;
  font-weight: bold;
}
.userBefore{
  background-color: var(--button-bg-color) !important;
  height: 10px;
  width: 10px;
  position: absolute;
  left: 0;
  content: '';
  border-radius: 50%;
  top: 5px;
}
.userAfter{
  
  height: 6px;
  width: 9px;
  
  position: absolute;
  right: 0;
  content: '';
  border-radius: 50%;
  background-repeat: no-repeat;
  top: 5px;
  background-position: center;
}
.user-profile-name::after {
 
  height: 6px;
  width: 9px;
  position: absolute;
  display: none;
  right: 0;
  content: '';
  border-radius: 50%;
  background-repeat: no-repeat;
  top: 35px;
  background-position: center;
}

.box-close {
  position: absolute;
  right: 10px;
  top: 10px;
  line-height: 12px;
  font-size: 18px;
  color: #aeaeae;
}

.box-close i {
  font-size: 18px;
}

.user-profile-box {
  position: absolute;
  right: 0;
  top: 10px;
  margin-top: 10px;
  width: 324px;
  border-radius: 5px;
  background-color: #FFFFFF;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
  z-index: 20;
  padding: 10px 25px 10px 10px;
  text-align: left;
}

.user-profile-box ul li a:hover {
  background-color: #ECFFFD;
}

.user-profile-box ul li a {
  font-size: 12px;
  color: $primaryColor !important;
  line-height: normal;
  display: inline-block;
}

.user-profile-box-div:after {
  position: fixed;
  top: 0px;
  bottom: 0;
  left: 0;
  right: 0;
  content: '';
  z-index: 15;
  cursor: default;
}

@media (max-width: 767px) {
  .logo-icon {
    width: 137.27px;
    height: 28.95px;
    background-size: 137.27px !important;
  }
  .image{
    max-height: 35px;
    max-width: 130px;
    display: inline-block;
    margin-top: 0px;
    text-align: center;
    -ms-flex-align: center;
    align-items: center;
}
  .eventtimings{
    display: block;
  }
  .eventtimings1{
    display: block;
    margin-top: 10px;
  }
  .eventStarttime{
    margin-left: 0px;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    line-height: 25px;
    margin-bottom: 0px;
  }
  .user-profile-box-container {
    position: relative;
    margin-right: 0px;
    display: block;
  }
  .header-right {
    font-size: 12px;
    margin-top: 5px !important;
    margin-bottom: 5px !important;
    color: var(--hyperlink-color) !important;
    text-align: right;
    float: right;
    right: 20px;
    position: relative;
}
  .user-profile-box-div:after {
    position: fixed;
    top: 0px;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    content: '';
    z-index: 15;
    cursor: default;
  }

  .user-profile-box {
    position: fixed;
    right: auto;
    top: 48px;
    left: 50%;
    transform: translate(-50%);
  }
}


@media (max-width: 359px) {
  .user-profile-box {
    width: 280px;
  }

  .user-profile-box-container {
    margin-right: 0px;
  }

  .heading {
    font-size: 10px;
    width: 80px;
  }
}

.heading {
  font-size: 8px;
  width: 100px;
  white-space: nowrap;
}