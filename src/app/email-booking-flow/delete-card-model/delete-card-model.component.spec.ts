import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DeleteCardModelComponent } from './delete-card-model.component';

describe('DeleteCardModelComponent', () => {
  let component: DeleteCardModelComponent;
  let fixture: ComponentFixture<DeleteCardModelComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [DeleteCardModelComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DeleteCardModelComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
