<div class="card payment-card inactive">
  <div class="card-header">
    <h3 class="exp-text">Expensify</h3>
    <h3 class="exp-img">
      <img class="hidden-xs" src="assets/images/emailflow/Expensify-logo.png" />
      <img class="visible-xs" src="assets/images/emailflow/Expensify-logo-mobile.png" />
    </h3>
  </div>
  <div class="card-body">
    <form method="post" id="expensifyForm" class="expensifyForm">
      <div class="row">
        <div class="col-lg-12  col-md-12 col-sm-12 col-xs-12">
          <div class="input-box">
            <input class="input-textfield expensifyLoginEmail disabled input-textfield-lg" id="expensifyLoginEmail"
              name="expensifyLoginEmail" placeholder="Expensify Login Email" type="text">
          </div>
          <label class="mdl-checkbox expensifyLoginEmailCheckboxDiv input-checkbox mdl-js-checkbox mdl-js-ripple-effect"
            for="expensifyLoginEmailCheckbox">
            <input onchange="enableExpensifyTextbox(this);" type="checkbox" id="expensifyLoginEmailCheckbox"
              name="expensifyLoginEmailCheckbox" value="expensifyLoginEmailCheckbox"
              class="mdl-checkbox__input knownTravellerCheckbox traveller-group">
            <span class="mdl-checkbox__label">Add receipt to my expensify</span>
          </label>
        </div>
      </div>
    </form>
  </div>
</div>