@import "../../../variables.scss";

@font-face {
    font-family: 'sourceSansPro-r';
    src: url("../../../assets/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
}


/*common css starts*/
body {
    color: $primaryColor;
    margin: 0 auto;
    font-family: $fontMono;
    font-size: 16px;
    width: 100%;
    overflow-x: hidden;
}

.pageBody {
    margin: 0 auto;
    max-width: 1440px;
    width: 100%;
    background: #EEECEB;
    min-height: 100vh;
}

* {
    outline: none !important;
    list-style: none;
}

img {
    max-width: 100%;
    max-height: 100%;
}

a {
    color: $themeColor;
}

a:hover,
a:focus,
a:active {
    text-decoration: none;
}

p {
    margin-bottom: 0;
    font-size: inherit;
    line-height: normal;
}

ul {
    padding: 0;
    margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: $fontMono;
}

.pad_0 {
    padding: 0 !important;
}

label {
    margin-bottom: 0;
}

*::-moz-focus-inner {
    border: 0 !important;
}

select:-moz-focusring {
    color: transparent;
    text-shadow: 0 0 0 #000;
}

.table-view {
    display: table;
    float: left;
    width: 100%;
    height: 100%;
}

.table-cell-view {
    display: table-cell;
    vertical-align: middle;
}

strong {
    font-weight: normal;
    font-family: $fontMedium;
}

b {
    font-weight: normal;
    font-family: $fontBold;
}

.inlineblock_m {
    display: inline-block;
    vertical-align: middle;
}

.inlineblock_b {
    display: inline-block;
    vertical-align: bottom;
}

.inlineblock_t {
    display: inline-block;
    vertical-align: top;
}

.middle {
    vertical-align: middle;
}

.top {
    vertical-align: top !important;
}

.container {
    width: 1343px;
}

.content {
    float: left;
    width: 100%;
    background: #EEECEB;
}

.card {
    background-color: #EEEDEB;
    float: left;
    width: 100%;
    margin-bottom: 24px;
}

.card-header {
    float: left;
    width: 100%;
    padding-bottom: 12px;
}

.card-sec-header {
    padding-bottom: 22px;
    padding-top: 18px;
}

.card-header h3,
.card-sec-header h3 {
    font-size: 22px;
    letter-spacing: -0.98px;
    line-height: 26px;
    margin: 0;
    display: inline-block;
    vertical-align: middle;
}

.card-header p {
    font-size: 14px;
    line-height: 17px;
    font-family: $fontRegular;
    margin-top: 2px;
    margin-bottom: 1px;
}

.card-body {
    float: left;
    width: 100%;
    padding: 0 40px 40px;
}

.card-footer {
    float: left;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 40px 40px;
}

.flexContainer {
    display: flex;
}

.link-icon,
.link-text {
    display: inline-block;
    vertical-align: middle;
}

.link-primary {
    color: var(--button-font-color);
    font-size: 12px;
    letter-spacing: 1px;
    line-height: 18px;
    font-family: $fontBold;
    text-transform: uppercase;
}

.page-content {
    padding-bottom: 62px;
    float: left;
    width: 100%;
}

.row {
    margin-left: -8px !important;
    margin-right: -8px !important;
}

[class^="col-"],
[class^=" col-"] {
    padding-right: 8px;
    padding-left: 8px;
}

.logo {
    line-height: 0;
}

.logo a {
    display: inline-block;
    line-height: 0;
}

header {
    float: left;
    background-size: auto 100%;
    width: 100%;
    background-repeat: no-repeat;
    background-position: left center;
}

.header-inner {
    height: 180px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-icon {
    width: 214px;
    height: 71px;
    background-image: url(/../../../assets/images/emailflow/logo.png);
    background-repeat: no-repeat;
    display: inline-block;
    background-size: 214px;
}

.user-profile {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
}

.user-profile-img {
    border-radius: 50%;
    overflow: hidden;
    width: 30px;
    height: 30px;
    justify-content: center;
    align-items: center;
    display: flex;
}

.user-profile-content {
    width: calc(100% - 30px);
    padding-left: 9px;
    font-size: 14px;
    line-height: 17px;
    font-family: $fontRegular;
    margin-top: -2px;
}

.user-name {
    color: $primaryColor;
}

.user-email {
    color: #A6A5A4;
}

.gallop-cash {
    color: #A6A5A4;
}

.gallop-cash-amount {
    color: var(--dark-bg-color);
    letter-spacing: 0.42px;
    font-family: $fontBold;
    margin-right: 5px;
}

@media (max-width:991px) {
    .row {
        margin-left: -4px !important;
        margin-right: -4px !important;
    }

    [class^="col-"],
    [class^=" col-"] {
        padding-right: 4px;
        padding-left: 4px;
    }
}


/*placeholder*/
::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    opacity: 1;
    color: #AEAEAE;
}

::-moz-placeholder {
    /* Firefox 19+ */
    opacity: 1;
    color: #AEAEAE;
}

:-ms-input-placeholder {
    /* IE 10+ */
    opacity: 1;
    color: #AEAEAE;
}

:-moz-placeholder {
    /* Firefox 18- */
    opacity: 1;
    color: #AEAEAE;
}

.input-with-checkbox.disabled input {
    background-color: #E7E6E4;
    pointer-events: none;
}

.mdl-checkbox {
    float: left;
    width: auto;
    font-weight: normal;
    cursor: pointer;
    margin-right: 8px;
    margin-bottom: 6px;
}

.mdl-checkbox:last-child {
    margin-right: 0;
}

span.error {
    color: #f00;
    font-size: 12px;
}


.button-container {
    float: left;
    width: 100%;
}

.button {
    border-radius: 2px !important;
    height: 64px;
    padding: 0 32px;
    border: none;
    text-transform: capitalize;
    font-size: 20px;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    letter-spacing: 0.6px;
    line-height: 25px;
}

.button-primary {
    cursor: pointer;
    background: $themeColor;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
    color: var(--button-font-color);
}


.button-secondary {
    background: transparent;
    color: $themeColor;
    border: 2px solid $themeColor;
}

.button-text {
    background: none;
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0;
    color: $themeColor;
    font-family: $fontRegular;
}

.select-box {
    position: relative;
    float: left;
    width: 100%;
}

.title-select-box {
    max-width: 118px;
}

.select-box select {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    position: relative;
    z-index: 2;
    background-image: url(/../../../assets/images/emailflow/arrow-down1.png);
    background-repeat: no-repeat;
    background-position: right 5px center;
    background-size: 26px;
}

.mdl-radio__ripple-container,
.mdl-radio__outer-circle,
.mdl-radio__inner-circle {
    display: none;
}

.mdl-radio {
    float: left;
    width: 100%;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    height: 50px;
    padding: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.mdl-radio.is-checked {
    background: $themeColor;
    color: #fff;
}

.input-with-checkbox.disabled input {
    background-color: #E7E6E4;
    pointer-events: none;
}

.input-box {
    float: left;
    width: 100%;
    margin-bottom: 12px;
    position: relative;
}

.input-label {
    float: left;
    width: 100%;
    color: #ABA7A4;
    font-size: 14px;
    line-height: 17px;
    margin-bottom: 8px;
    font-family: $fontRegular;
    font-weight: normal;
}

.input-textfield {
    float: left;
    width: 100%;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    height: 50px;
    font-size: 16px;
    padding: 5px 16px;
    color: $primaryColor;
    resize: none;
    border-radius: 0 !important;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
}

textArea.input-textfield {
    padding-top: 10px;
    padding-bottom: 10px;
}


.input-textfield+.mdl-checkbox {
    width: auto;
    position: absolute;
    right: 2px;
    top: 27px;
    background: #F7F7F7;
    height: 46px;
    padding: 0 19px 0px 38px;
    border: none;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__box-outline {
    top: 14px;
    left: 12px;
    background-color: #FFFFFF;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__focus-helper {
    top: 14px;
    left: 12px;
}

.mdl-checkbox__ripple-container {
    top: 4px;
    left: 2px;
}

.input-with-checkbox input {
    padding-right: 130px;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__label {
    font-size: 14px;
    line-height: 26px;
    font-weight: normal;
    font-family: $fontRegular;
}

.link-icon,
.link-text {
    display: inline-block;
    vertical-align: middle;
}

.mdl-checkbox__box-outline {
    border: 1px solid #979797;
    width: 17px;
    height: 17px;
    border-radius: 0;
}

.mdl-checkbox.is-checked .mdl-checkbox__box-outline {
    border: 1px solid $themeColor;
}

.mdl-checkbox.is-checked .mdl-checkbox__tick-outline {
    background-color: $themeColor;
}


/*placeholder*/
.input-with-checkbox.disabled input::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    opacity: 0;
}

.input-with-checkbox.disabled input::-moz-placeholder {
    /* Firefox 19+ */
    opacity: 0;
}

.input-with-checkbox.disabled input:-ms-input-placeholder {
    /* IE 10+ */
    opacity: 0;
}

.input-with-checkbox.disabled input:-moz-placeholder {
    /* Firefox 18- */
    opacity: 0;
}

.input-with-checkbox.disabled .mdl-checkbox {
    left: 0;
    right: 0;
    background: #E7E6E4;
}




.inline-checkbox.mdl-checkbox {
    float: none;
    display: inline-block;
    padding-left: 0;
    padding-right: 24px;
}

.inline-checkbox .mdl-checkbox__label {
    color: var(--button-font-color);
    font-size: 12px;
    letter-spacing: 1px;
    line-height: 18px;
    font-family: $fontBold;
    text-transform: uppercase;
}

.inline-checkbox .mdl-checkbox__box-outline {
    right: 0;
    left: auto;
    bottom: 1px;
    top: auto;
}


/*modal css*/
.modal-dialog {
    max-width: 710px;
}

.modal-content {
    border-radius: 0px;
    box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
    border: none;
    text-align: center;
    max-width: 710px;
    margin: 24px auto;
    width: calc(100% - 48px);
}

.modal-header {
    border: none;
    padding: 22px 17px 15px 25px;
}

.modal-title {
    display: inline-block;
    width: 35px;
    float: left;
}

.modal-title img {
    width: 35px;
}

.close {
    color: #000;
    opacity: 1;
}

.close:hover {
    color: #000;
    opacity: 1;
}

.modal-body {
    padding-bottom: 35px;
}

.modal-icon-container {
    float: left;
    width: 100%;
    text-align: center;
}

.modal-icon {
    background-color: #DDFFF7;
    border: 3px solid #1EBD97;
    width: 48px;
    height: 48px;
    display: inline-flex;
    color: #1EBD97;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
}

.modal-icon i {
    font-size: 30px;
}

.modal-content-heading {
    font-size: 22px;
    letter-spacing: -0.98px;
    line-height: 56px;
    color: #3C413B;
    float: left;
    width: 100%;
}

.modal-content-heading {
    font-size: 22px;
    letter-spacing: -0.98px;
    line-height: 56px;
    color: #3C413B;
    float: left;
    width: 100%;
    margin-top: 15px;
    margin-bottom: 10px;

}

.modal-content-heading h3 {
    margin: 0;
    font-size: 22px;
    color: $primaryColor;
}

.modal-content-text {
    color: $secondaryColor;
    font-size: 18px;
    font-family: $fontRegular;
    line-height: 26px;
    margin-bottom: 50px;
    max-width: 530px;
    margin-left: auto;
    margin-right: auto;
}

.modal-content-text p {
    line-height: 26px;
}


span.error {
    color: #f00;
    font-size: 12px;
    float: left;
    width: 100%;
}

#flyerNumberCheckbox-error,
#knownTravellerCheckbox-error,
#guestNumberCheckbox-error {
    display: none !important;
}

.datepicker {
    background-image: url(/../../../assets/images/emailflow/calendar.svg);
    background-repeat: no-repeat;
    background-position: right 17px center;
}

.input-textfield.datepicker {
    padding-right: 50px;
}

.input-box-icon-container {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    height: 50px;
}

.input-box-icon-container.withLabel {
    margin-top: 25px;
}

.input-box-icon-container.left {
    left: 17px;
}

.input-box-icon-container.right {
    right: 19px;
}

.input-box-icon-container.right+input {
    padding-right: 50px;
}

.input-box-icon-container.left+input {
    padding-left: 50px;
}


.label-inputbox {
    margin-bottom: 16px;
}

.input-label {
    float: left;
    width: 100%;
    color: #ABA7A4;
    font-size: 14px;
    line-height: 17px;
    margin-bottom: 8px;
    font-family: $fontRegular;
    font-weight: normal;
}

.input-textfield {
    float: left;
    width: 100%;
    border: 2px solid #E7E6E4;
    font-size: 16px;
    background-color: #FFFFFF;
    height: 50px;
    padding: 5px 16px;
}

.input-with-checkbox input {
    padding-right: 130px;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__label {
    font-size: 14px;
    line-height: 17px;
    font-weight: normal;
    font-family: $fontRegular;
    width: 100%;
}

.input-textfield-lg {
    height: 64px;
}

/*for userDetailsPage*/
.mdl-radio,
.mdl-radio.is-upgraded {
    width: auto;
    margin-right: 5px;
    padding: 0 35px;
}

.checkbox-container {
    float: left;
    width: 100%;
}

.checkbox-container .mdl-radio {
    margin-bottom: 8px;
}

/*datepicker*/
.ui-datepicker {
    z-index: 9 !important;
}

.ui-datepicker-month,
.ui-datepicker-year {
    background: #fff;
    border: 2px solid #E7E6E4;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    font-family: $fontRegular  !important;
    font-weight: normal !important;
}

.ui-widget-content {
    color: $primaryColor;
}

.ui-widget {
    font-family: $fontRegular;
    font-weight: normal;
}

.ui-datepicker-month,
.ui-datepicker-year {
    background-image: url(/../../../assets/images/emailflow/arrow-down.png);
    background-repeat: no-repeat;
    background-position: right 5px center;
}

.ui-widget-header {
    border: 1px solid #EEECEB;
    background: #EEECEB;
    color: $primaryColor;
    font-weight: normal;
}

.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
    border: 1px solid #E7E6E4;
    background: #EEECEB;
    color: $primaryColor;
}

.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
    border: 1px solid #E7E6E4;
    background: #EEECEB;
    font-weight: normal;
    color: $primaryColor;
}

.ui-widget-header .ui-icon.ui-icon-circle-triangle-w {
    background-image: url(/../../../assets/images/emailflow/down-icon.png);
    background-position: -4px 4px;
    background-size: 24px auto;
    transform: rotate(90deg);
    height: 24px;
    width: 24px;
}

.ui-widget-header .ui-icon.ui-icon-circle-triangle-e {
    background-image: url(/../../../assets/images/emailflow/down-icon.png);
    background-position: 4px -4px;
    background-size: 24px auto;
    transform: rotate(-90deg);
    height: 24px;
    width: 24px;
}

.ui-datepicker .ui-datepicker-prev-hover {
    left: 2px;
    top: 2px;
}

.ui-datepicker .ui-datepicker-next-hover {
    right: 2px;
    top: 2px;
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
    border: 1px solid $themeColor;
    background: $themeColor;
}

.intl-tel-input {
    width: 100%;
}

.intl-tel-input .selected-flag .iti-arrow {
    border: none !important;
    background-image: url(/../../../assets/images/emailflow/arrow-down1.png);
    background-repeat: no-repeat;
    width: 28px;
    height: 28px;
    background-size: 28px;
    background-position: right center;
    top: 3px;
    height: 100%;
    right: 6px;
}


.intl-tel-input.allow-dropdown .selected-flag,
.intl-tel-input.separate-dial-code .selected-flag {
    width: 75px;
}

.intl-tel-input .selected-flag {
    padding: 0 0 0 19px;
}

/*.intl-tel-input .selected-flag .iti-arrow:after{ content: 'keyboard_arrow_down';font-family: 'Material Icons'; position: absolute;}*/
.intl-tel-input.allow-dropdown input,
.intl-tel-input.allow-dropdown input[type="text"],
.intl-tel-input.allow-dropdown input[type="tel"],
.intl-tel-input.separate-dial-code input,
.intl-tel-input.separate-dial-code input[type="text"],
.intl-tel-input.separate-dial-code input[type="tel"] {
    padding-left: 80px;
}


::placeholder {
    /* Chrome/Opera/Safari */
    color: #AEAEAE;
    opacity: 1;
}

::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    color: #AEAEAE;
    opacity: 1;
}

::-moz-placeholder {
    /* Firefox 19+ */
    color: #AEAEAE;
    opacity: 1;
}

:-ms-input-placeholder {
    /* IE 10+ */
    color: #AEAEAE;
    opacity: 1;
}

:-moz-placeholder {
    /* Firefox 18- */
    color: #AEAEAE;
    opacity: 1;
}


.appleDevice ::placeholder {
    /* Chrome/Opera/Safari */
    /*padding-top: 4px;*/
}

.appleDevice ::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    /*padding-top: 4px;*/
}

.appleDevice ::-moz-placeholder {
    /* Firefox 19+ */
    /*padding-top: 4px;*/
}

.appleDevice :-ms-input-placeholder {
    /* IE 10+ */
    /*padding-top: 4px;*/
}

.appleDevice :-moz-placeholder {
    /* Firefox 18- */
    /*padding-top: 4px;*/
}



.modal-backdrop {
    background-color: rgba(238, 237, 235, 1);
}

.modal-backdrop.in {
    filter: alpha(opacity=80);
    opacity: .8;
}

.table-view {
    display: table;
    height: 100%;
    width: 100%;
}

.table-cell-view {
    display: table-cell;
    vertical-align: middle;
}

.passportDate {
    background-image: url(/../../../assets/images/emailflow/calendar.png);
    background-repeat: no-repeat;
}

.input-textfield {
    margin-bottom: 8px;
}

.line-seperator {
    float: left;
    width: 100%;
    background: #C1BDBD;
    height: 2px;
    margin-bottom: 20px;
}



/*select2*/
.select2-container {
    width: 100% !important;
}

.select2-selection__arrow {
    position: relative;
    z-index: 9999;
}

.select2-selection__arrow b {
    border-width: 0 !important;
}

.select2-selection__arrow:after {
    content: '';
    position: absolute;
    background-image: url(/../../../assets/images/emailflow/arrow-down1.png);
    background-repeat: no-repeat;
    height: 36px;
    width: 36px;
    right: 6px;
    top: 14px;
}

.select2-container--default .select2-selection--single {
    height: 64px;
    border-radius: 0;
    border: 2px solid #E7E6E4;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 60px;
}

.select2-dropdown {
    border: 2px solid #E7E6E4;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: $themeColor;
    color: white;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding-left: 16px;
    padding-right: 40px;
}


@media (max-width:991px) {

    .mdl-radio,
    .mdl-radio.is-upgraded {
        padding: 0 30px;
    }

    .input-textfield {
        font-size: 14px;
        padding: 5px 10px;
        letter-spacing: -1.5px;
        word-spacing: -2px;
    }

    .mdl-radio {
        font-size: 14px;
        letter-spacing: -1.5px;
        word-spacing: -2px;
    }
}



@media (max-width:767px) {
    .button {
        height: 46px;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
        letter-spacing: 1px;
        line-height: 11px;
        font-size: 12px;
        text-transform: uppercase;
    }

    .input-label {
        font-size: 12px;
        line-height: 15px;
    }

    .input-textfield {
        height: 40px;
        font-size: 12px;
        padding: 5px 14px;
        letter-spacing: normal;
    }

    .mdl-radio,
    .mdl-radio.is-upgraded {
        height: 40px;
        font-size: 12px;
        line-height: 15px;
        padding: 0 18px;
    }

    .input-textfield+.mdl-checkbox {
        height: 36px;
        top: 25px;
        padding-right: 17px;
    }

    .input-textfield+.mdl-checkbox {
        top: 21px;
    }

    .inline-checkbox .mdl-checkbox__label {
        line-height: 30px;
    }

    .input-textfield+.mdl-checkbox .mdl-checkbox__label {
        font-size: 10px;
        line-height: 18px;
        vertical-align: top;
    }

    .input-textfield+.mdl-checkbox .mdl-checkbox__box-outline {
        width: 12px;
        height: 12px;
        top: 11px;
        left: 16px;
    }

    textarea.input-textfield {
        height: 80px;
    }

    .mdl-radio {
        letter-spacing: normal;
    }

    .appleDevice .input-textfield {
        font-size: 16px !important;
        letter-spacing: -1.1px !important;
        word-spacing: -2px !important;
        padding-top: 0px;
        padding-bottom: 0;
        padding-left: 8px;
        padding-right: 8px;
    }

    .appleDevice .input-box-icon-container.left {
        left: 10px;
    }

    .appleDevice .input-box-icon-container.left+input {
        padding-left: 32px;
    }

    .appleDevice select.input-textfield {
        padding-top: 0;
    }

    .appleDevice .container {
        padding: 0 10px;
    }

    .appleDevice .card-body {
        padding: 22px 10px 31px 10px;
    }

    .appleDevice .card-header {
        padding: 15px 10px;
    }

    .appleDevice .mdl-radio {
        font-size: 16px;
        line-height: 24px;
    }

    .appleDevice .mdl-radio,
    .appleDevice .mdl-radio.is-upgraded {
        padding: 0 15px;
    }

    .mdl-checkbox__ripple-container {
        height: 24px;
        width: 24px;
        top: 7px;
        left: 10px;
    }

    .appleDevice textarea.input-textfield {
        padding-top: 8px;
        padding-bottom: 8px;
    }

    .select2-container--default .select2-selection--single {
        height: 40px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 37px;
        font-size: 12px;
    }

    .select2-selection__arrow:after {
        background-size: 26px;
    }

    .select2-selection__arrow::after {
        height: 26px;
        width: 26px;
        top: 7px;
    }

    .appleDevice .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 37px;
        font-size: 16px;
        padding-left: 8px;
    }
}





@media (max-width:1439px) and (min-width:1200px) {
    .container {
        width: 100%;
    }
}

@media (max-width:1199px) {
    .container {
        width: 970px;
    }

    .logo-icon {
        width: 160px;
        height: 55px;
        background-size: 160px;
    }

    .header-left {
        padding-left: 0;
    }

    .row-top-content {
        display: block;
    }
}

@media (max-width:991px) {
    .container {
        width: 740px;
    }

    .logo-icon {
        width: 160px;
        height: 55px;
        background-size: 160px;
    }

    .flight-row-left-section .line {
        left: 80px;
        right: 80px;
    }

    .flight-row-left {
        width: calc(100% - 170px);
    }

    .flight-row-right {
        width: 170px;
        padding: 0 10px;
        background-size: 24px;
        background-position: right 8px center;
    }

    .header-inner {
        height: 120px;
    }

    .card-header {
        padding: 30px 16px;
    }

    .card-body {
        padding: 0 20px 20px;
    }

    .card-footer {
        padding: 0 20px 24px;
    }

    .traveller-form .card-header {
        padding: 17px 16px;
    }


    .hotal-listing-row .flight-row-left-section .line,
    .car-listing-row .flight-row-left-section .line {
        display: none;
    }

    .traveller-form .card-body {
        margin-top: 0;
        padding-top: 0;
    }
}

@media (max-width:767px) {
    .container {
        width: 100%;
        padding: 0 15px;
    }

    .card-header h3 {
        font-size: 16px;
        letter-spacing: -0.71px;
        line-height: 16px;
    }

    .card-header p {
        font-size: 10px;
        line-height: 16px;
        letter-spacing: -0.45px;
    }

    .card {
        background-color: #EEEDEB;
        box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
        float: left;
        width: 100%;
        position: relative;
        margin-bottom: 8px;
    }

    .card-body {
        padding: 0 16px 15px;
    }

    .card-footer {
        padding: 0 16px 24px
    }

    .logo-icon {
        width: 92px;
        height: 30px;
        background-size: 90px;
    }

    .input-box {
        margin-bottom: 0;
    }

    .input-label {
        margin-bottom: 4px;
    }

    .flight-row-left-section .line {
        display: none;
    }

    .flight-row {
        flex-direction: column;
    }

    .flight-row-left {
        width: 100%;
        border-radius: 6px 6px 0 0;
    }

    .secondary-text-part {
        display: block;
    }

    .flight-row-right {
        width: 100%;
        justify-content: flex-start;
        height: 28px;
        border-radius: 0 0 6px 6px;
        border-top: 2px solid #e3e3e3;
        box-shadow: none;
        background-color: #f3f3f3;
        padding: 0 16px;
        background-size: 18px;
    }

    .card-section-header h4 {
        font-size: 12px;
        letter-spacing: -0.53px;
        line-height: 16px;
    }

    .airport-name {
        font-size: 12px;
        line-height: 12px;
    }

    .secondary-text {
        font-size: 10px;
        line-height: 12px;
    }

    .price {
        font-size: 14px;
        letter-spacing: 1.17px;
        line-height: 14px;
    }

    .flight-logo-icon {
        width: 11px;
    }

    .flight-icon img {
        width: 24px;
    }

    .flight-icon-inner {
        padding: 0;
    }

    .flight-row-left-section {
        padding: 0 16px;
    }

    .flight-row-left-section-inner {
        padding: 11px 0 16px;
    }

    .flight-listing-row.selected .flight-row-right {
        background-color: #fff;
    }

    .listing-card .card-header {
        padding-bottom: 4px;
    }

    .card-header p {
        margin-top: 1px;
    }

    .card-section-header h4 {
        margin-bottom: 7px
    }

    .flight-summery span {
        font-size: 10px;
        line-height: 12px;
    }

    .flight-row-layover-content {
        font-size: 10px;
        line-height: 12px;
    }

    .flight-row-layover-content {
        height: 19px;
        width: 156px;
    }

    .flight-row-layover-content span {
        font-family: "apercu-b";
    }

    .flight-listing-row.selected .flight-row {
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.14);
    }

    .header-inner {
        height: 96px;
    }

    .user-profile-img,
    .user-email {
        display: none;
    }

    .gallop-cash {
        font-size: 10px;
    }

    .user-profile-content {
        padding-left: 0;
        width: 100%;
    }

    .flight-summery {
        padding-top: 0px;
    }

    .page-content {
        padding-bottom: 12px;
    }

    .flight-row-right::after {
        right: 10px;
    }

    .flight-row-layover {
        margin-bottom: 7px;
    }

    .user-name {
        text-align: right;
    }

    .card-sec-header h3 {
        font-size: 12px;
        line-height: 16px;
        letter-spacing: -0.53px;
    }

    .travellerShortDetailsText {
        display: block;
    }

    .seperator {
        display: none;
    }

    .traveller-short-info {
        font-size: 12px;
        letter-spacing: -0.53px;
        line-height: 19px;
        margin-top: 5px;
    }

    .form-number {
        height: 26px;
        width: 26px;
        font-size: 14px;
        letter-spacing: -0.62px;
        line-height: 14px;
    }

    .traveller-form .card-body {
        margin-bottom: 0;
        margin-top: -1px;
        padding-top: 0;
    }

    .form-arrow-down {
        display: none !important;
    }

    .payment-card .card-header {
        padding-top: 18px;
        padding-bottom: 23px;
    }

    .card-sec-header {
        padding-bottom: 11px;
        padding-top: 11px;
    }

    .datepicker {
        background-size: 12px;
        background-position: right 14px center;
    }

    .input-textfield.datepicker {
        padding-right: 40px;
    }

    .row-heading {
        font-size: 12px;
        line-height: 12px;
    }

    .distance-img-text {
        font-size: 10px;
        line-height: 12px;
    }

    .row-sec-text {
        font-size: 10px;
        line-height: 12px;
    }

    .distance-label {
        font-size: 10px;
        line-height: 12px;
    }

    .hotel-date {
        font-size: 12px;
        line-height: 14px;
    }

    .car-listing-row .flight-center-column {
        display: none;
    }

    .star-icon i {
        font-size: 8px;
    }

    .star-icon {
        margin-right: 2px;
        line-height: 8px;
    }

    .total-amount-inner {
        max-width: 185px;
    }

    .amount {
        font-size: 16px;
        letter-spacing: -0.87px;
    }

    .gallop-cash-text {
        font-size: 10px;
        letter-spacing: -0.45px;
        line-height: 16px;
    }

    .expensifyForm .mdl-checkbox__label {
        font-size: 12px;
        line-height: 15px;
    }

    .expensifyForm .mdl-checkbox {
        line-height: 16px;
        padding-left: 0px;
    }

    .expensifyForm .mdl-checkbox__box-outline {
        height: 12px;
        width: 12px;
    }

    .paymentButton {
        margin-top: 92px;
    }

    .paymentButton button {
        width: 100%;
    }

    .price-text {
        line-height: 14px;
        font-size: 12px;
        margin-left: 0;
        margin-top: 5px;
    }

    .taxi-location {
        font-size: 12px;
        line-height: 16px;
        color: $primaryColor;
        margin-bottom: 2px;
        display: inline-block;
    }


}


@media (max-width:767px) {
    .card-section-heading {
        display: block;
    }

    .viewAllLink {
        margin-right: 10px;
    }
}




.form-card .card-footer {
    padding-top: 20px;
}


/*flight css*/
.flight-row {
    display: flex;
    border-radius: 6px;
    background-color: #F3F3F3;
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.13);
}

.flight-listing-row.selected .flight-row {
    background-color: #fff;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.19);
}

.flight-listing-row.selected .flight-row-layover-content {
    background-color: #fff;
}

.flight-row.detailed {
    background-color: #F3F3F3;
}

.flight-row-left {
    width: calc(100% - 240px);
    cursor: pointer;
}

.flight-row-left-section {
    padding: 0 23px;
}

.flight-row-left-section-inner {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 22px 0 22px;
    position: relative;
    z-index: 2;
    float: left;
    width: 100%;
}

.flight-row-left-section .line {
    top: 35px;
    z-index: 1;
    left: 87px;
    right: 87px;
}

.flight-row-right {
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    background-size: 25px;
    position: relative;
    background-color: #F7F7F7;
    padding: 0 23px;
    box-shadow: -1px 1px 3px 0 #D4D4D4;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 240px;
}

.flight-row-right:after {
    content: '';
    position: absolute;
    right: 14px;
    background-image: url(/../../../assets/images/emailflow/arrow-down1.png);
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 25px;
    height: 25px;
    width: 25px;
}

.flight-row.detailed .flight-row-right:after {
    transform: rotate(180deg);
}

.airport-name {
    color: $primaryColor;
    font-family: Helvetica;
    font-size: 18px;
    font-weight: bold;
    line-height: 26px;
}

img+.airport-name {
    margin-left: -5px;
}

.flight-summery img+span {
    margin-left: -5px;
}

.secondary-text {
    color: #A6A5A4;
    font-family: $fontRegular;
    font-size: 14px;
    line-height: 18px;
}

.price {
    color: var(--dark-bg-color);
    font-family: $fontBold;
    font-size: 18px;
    letter-spacing: 0.54px;
    line-height: 22px;
    text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}

.flight-row-layover {
    text-align: center;
    position: relative;
    float: left;
    width: 100%;
}

.flight-row-layover-content {
    height: 27px;
    width: 188px;
    border: 1px solid #D3D2D1;
    background-color: #F3F3F3;
    color: $secondaryColor;
    font-family: $fontRegular;
    font-size: 14px;
    line-height: 18px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
}

.line {
    background-color: #e3e3e3;
    position: absolute;
    left: 0;
    height: 2px;
    z-index: 1;
    right: 0;
}

.flight-row-layover .line {
    top: 13px;
}

.flight-row-left-section {
    position: relative;
}

.flight-icon-inner {
    background: #F3F3F3;
    padding: 0 22px;
    display: inline-block;
}

.flight-icon img {
    min-width: 29px;
}

.flight-listing-row.selected .flight-icon-inner {
    background: #fff;
}

.flight-column {
    position: relative;
    z-index: 10;
}

.flight-left-column,
.flight-right-column {
    max-width: 240px;
}

.flight-left-column {
    padding-right: 5px;
}

.flight-right-column {
    padding-left: 5px;
}

.flight-summery {
    padding-top: 17px;
}

.flight-summery span {
    color: $secondaryColor;
    font-size: 14px;
    line-height: 17px;
    font-family: $fontRegular;
}

.flight-listing-row {
    margin-bottom: 15px;
}

.card-section {
    border-bottom: 1px solid #E3E3E3;
    padding-bottom: 8px;
    margin-bottom: 8px;
    float: left;
    width: 100%;
}

.card-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/*.card-section:last-child h4{ margin-bottom: 0;}*/


.card.inactive .card-header {
    color: rgba(65, 62, 59, 0.5);
}

.card.inactive .card-footer {
    display: none;
}

.card.inactive .card-body {
    display: none;
}

.card-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-section-header h4 {
    font-size: 16px;
    line-height: 26px;
    color: $primaryColor;
}

.flight-center-column .secondary-text-part {
    display: block;
}

.flight-logo-icon {
    width: 16px;
}

.secondary-text {
    display: block;
    margin-bottom: 2px;
}

.secondary-text:last-child {
    margin-bottom: 0;
}

.secondary-text-top {
    margin-bottom: 1px !important;
}

.total-amount {
    float: left;
    width: 100%;
    text-align: right;
    display: none;
    margin-top: 9px;
}

.total-amount-inner {
    display: inline-block;
    max-width: 270px;
}

.amount-count {
    margin-bottom: 9px;
}

.amount-count-label {
    font-size: 16px;
    letter-spacing: -0.71px;
    line-height: 26px;
    font-weight: normal;
    margin-right: 10px;
}

.amount {
    border-radius: 2px;
    background-color: #9C4AF6;
    height: 27px;
    display: inline-flex;
    align-items: center;
    color: #FFFFFF;
    font-size: 18px;
    letter-spacing: -0.98px;
    line-height: 16px;
    padding: 0 17px;
}

.gallop-cash-text {
    font-size: 14px;
    letter-spacing: -0.62px;
    line-height: 20px;
    color: #9C4AF6;
}

.form-number {
    display: none;
    height: 34px;
    width: 34px;
    background: #fff;
    justify-content: center;
    align-items: center;
    border: 2px solid $themeColor;
    font-size: 18px;
    letter-spacing: -0.8px;
    line-height: 26px;
    border-radius: 50%;
    margin-right: 12px;
}

.seperator {
    padding: 0 7px 0 15px;
}

.form-arrow-down {
    color: $themeColor;
    line-height: 0;
    margin-left: 10px;
    display: none;
}

.card-header {
    float: left;
    width: 100%;
    padding: 40px 40px;
}

.listing-card .card-header {
    padding-bottom: 12px;
}

.traveller-form {
    float: left;
    width: 100%;
}

.traveller-form .card-body {
    border-bottom: 5px solid #fff;
}

.traveller-form:last-child .card-body {
    border-bottom: none;
}

.traveller-form-closed .card-header {
    box-shadow: 0 -4px 19px 0 rgba(0, 0, 0, 0.13);
}

.traveller-form .card-body {
    margin-top: -16px;
    padding-bottom: 17px;
}


.traveller-form-filled .form-number {
    display: inline-flex;
    vertical-align: middle;
}

.traveller-form-filled .form-arrow-down {
    display: inline-block;
    vertical-align: middle;
}

.traveller-short-info {
    color: #A6A5A4;
    font-size: 14px;
    letter-spacing: -0.62px;
    line-height: 16px;
    margin-top: 11px;
}

.traveller-short-info li {
    display: inline-block;
}

.traveller-short-info li label {
    font-weight: normal;
}

.row-heading {
    color: $primaryColor;
    font-family: Helvetica;
    font-size: 18px;
    font-weight: bold;
    line-height: 26px;
}

.row-top-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 23px;
}

.distance-label {
    font-weight: normal;
    line-height: 17px;
    font-size: 14px;
    font-family: $fontLight;
    display: inline-block;
    vertical-align: bottom;
}

.distance-img-text {
    color: #9C4AF6;
    font-weight: normal;
    line-height: 17px;
    font-size: 14px;
    font-family: $fontMedium;
    margin-left: 3px;
    display: inline-block;
    vertical-align: bottom;
}

.price-text {
    color: #9C4AF6;
    font-weight: normal;
    line-height: 17px;
    font-size: 14px;
    font-family: $fontRegular;
    margin-left: 3px;
    display: inline-block;
}

/*.distance-time-text{ margin-right: 10px;}*/
.distance-time-text:last-child {
    margin-right: 0;
}

.distance-time-text,
.distance-time-img {
    display: inline-block;
    vertical-align: middle;
}

.row-top-section {
    float: left;
    width: 100%;
}

.row-sec-text {
    color: #A6A5A4;
    font-size: 14px;
    line-height: 17px;
    font-family: $fontRegular;
    margin-top: 5px;
}

.row-rating {
    float: left;
    width: 100%;
}

.star-icon {
    float: left;
    margin-right: 5px;
}

.star-icon:last-child {
    margin-right: 0;
}

.star-icon i {
    color: #C6C6C6;
    font-size: 11px;
}

.star-icon.active i {
    color: #F5C841;
}

.hotel-date {
    font-size: 18px;
    line-height: 26px;
    font-family: Helvetica;
    color: $primaryColor;
}

.hotal-listing-row .flight-row-left-section .line {
    left: 162px;
    right: 162px;
    top: 20px;
}

.hotal-listing-row .flight-row-left-section-inner {
    padding: 8px 0 22px;
}

.car-listing-row .flight-row-left-section .line {
    left: 165px;
    right: 165px;
    top: 54px;
}

.car-listing-row .flight-icon {
    margin-top: 20px;
}

.hotal-listing-row .flight-row-right:after,
.car-listing-row .flight-row-right:after {
    background-image: none;
}

.hotal-listing-row .flight-row-right,
.car-listing-row .flight-row-right {
    cursor: default;
}

.flight-icon {
    min-width: 75px;
}

.hotal-listing-row .flight-icon {
    margin-bottom: 5px;
}

.card-header h3.exp-img {
    display: block;
}

.card-header h3.exp-text {
    display: none;
}

.card.inactive .card-header h3.exp-img {
    display: none;
}

.card.inactive .card-header h3.exp-text {
    display: block;
}

.traveller-form .card-header {
    display: flex;
    padding-bottom: 0px;
}


.expensifyLoginEmail {
    max-width: 516px;
    margin-top: 15px;
}

.expensifyForm .mdl-checkbox__label {
    font-size: 14px;
    line-height: 17px;
    font-family: $fontRegular;
}

.mdl-checkbox__ripple-container {
    display: none;
}

.expensifyLoginEmailCheckboxDiv {
    margin-top: 6px;
}

.input-textfield.disabled {
    pointer-events: none;
    opacity: 0.5;
}

.row-content-mobile {
    padding: 16px 0 12px;
}

.row-content-mobile .row-heading {
    line-height: 16px;
}

.row-content-mobile .secondary-text {
    font-size: 12px;
    line-height: 16px;
    color: $primaryColor;
    font-family: $fontRegular;
}

.row-content-mobile .secondary-text.dates {
    font-size: 10px;
    line-height: 12px;
}

.row-content-mobile .row-sec-text {
    font-size: 12px;
    line-height: 16px;
    color: $primaryColor;
    font-family: $fontRegular;
    margin-bottom: 10px;
    margin-top: 1px;
}

.row-content-mobile .distance-label {
    font-size: 12px;
    line-height: 16px;
    color: $primaryColor;
    font-family: $fontRegular;
}

.row-content-mobile .distance-img-text {
    font-size: 12px;
    line-height: 16px;
    font-family: $fontRegular;
}

.row-content-mobile .row-rating {
    margin-bottom: 8px;
}


.overflow-hidden {
    overflow: hidden !important;
}

.disabled {
    pointer-events: none;
    opacity: 0.5;
}

.mdl-radio__label {
    font-size: 14px;
    font-weight: normal;
    font-family: $fontRegular;
}

.card {
    padding: 40px;
}

.payment-note {
    float: left;
    width: 100%;
    font-size: 14px;
    letter-spacing: 0.23px;
    line-height: 18px;
    font-family: $font2Regular;
}

.payment-note-inner {
    max-width: 1092px;
    margin-left: 40px;
}

td {
    font-size: 14px;
    letter-spacing: -0.62px;
    line-height: 26px;
    padding: 4px 0;
}

.payment-msg {
    font-size: 14px;
    letter-spacing: 0.1px;
    line-height: 18px;
    color: $themeColor2;
    font-family: $font2Regular;
    float: left;
    width: 100%;
    margin-bottom: 21px;
}

.button-container {
    float: left;
    width: 100%;
}

.button {
    border-radius: 2px !important;
    height: 64px;
    padding: 0 32px;
    border: none;
    text-transform: capitalize;
    font-size: 20px;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    letter-spacing: 0.6px;
    line-height: 25px;
}

.button-primary {
    background: $themeColor;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
    color: var(--button-font-color);
}

.button-full {
    width: 100%;
}

.payment-table {
    width: 100%;
    margin-top: 9px;
    margin-bottom: 62px;
}

.mdl-checkbox__label {
    font-size: 14px;
    line-height: 17px;
    font-weight: normal;
    font-family: $fontRegular;
}

.gallop-cash-input {
    height: 50px;
    width: 92px;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    padding: 0 5px;
    text-align: center;
}

.mdl-checkbox__box-outline {
    border: 1px solid #979797;
    width: 17px;
    height: 17px;
    border-radius: 0;
    background: #fff;
}

.mdl-checkbox.is-checked .mdl-checkbox__box-outline {
    border: 1px solid var(--button-bg-color);
}

.mdl-checkbox.is-checked .mdl-checkbox__tick-outline {
    background-color: var(--button-bg-color) !important;
}

.mdl-radio__outer-circle {
    border: 1px solid #979797;
    background-color: #FFFFFF;
}

.mdl-radio.is-checked .mdl-radio__outer-circle {
    border: 1px solid var(--button-bg-color);
}

.mdl-radio__inner-circle {
    background: var(--button-bg-color);
    z-index: 2;
    height: 10px;
    width: 10px;
    top: 7px;
    left: 3px;
}

.walletCashDiv {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 25px;
    margin-bottom: 49px;
    float: left;
    width: 100%;
}

.walletCashDiv .mdl-checkbox {
    width: auto;
    margin-right: 7px;
    height: auto;
}

.payment-options .mdl-radio img {
    width: 25px;
}

.payment-option-section {
    margin-bottom: 8px;
}

.payment-option-section .mdl-radio {
    line-height: 19px;
}

.card1 {
    margin-top: 7px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.19);
    background-color: #FFFFFF;
    border-radius: 6px;
    max-width: 612px;
    padding: 33px 48px;
    float: left;
    width: 100%;
}

.input-box {
    float: left;
    width: 100%;
    margin-bottom: 12px;
    position: relative;
}

.input-textfield {
    float: left;
    font-family: $fontMono;
    width: 100%;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    height: 50px;
    font-size: 16px;
    padding: 5px 16px;
    color: $primaryColor;
    resize: none;
    border-radius: 0 !important;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    margin-bottom: 8px;
}

.text-button {
    background: none;
    box-shadow: none;
    border: none;
    padding: 0 20px;
}

.text-button:first-child {
    padding-left: 0;
}

.text-button:last-child {
    padding-right: 0;
}

/*.delete-modal-button .text-button{ width: 45%;}*/
.button1 {
    width: 111px;
    text-align: left;
}

.button2 {
    width: 133px;
    text-align: right;
}

.card1-header h3 {
    margin: 8px 0 0 0;
    line-height: 26px;
    letter-spacing: -0.98px;
    font-size: 22px;
    margin-bottom: 22px;
}

.card1-header {
    float: left;
    width: 100%;
}

.card1-body {
    float: left;
    width: 100%;
}

.card1-footer {
    float: left;
    width: 100%;
    margin-top: 12px;
}

.add-card-div {
    float: left;
    width: 100%;
    line-height: 0;
    margin-top: 2px;
}

.add-card-link {
    float: left;
    width: 100%;
}

.add-card-link a i {
    font-size: 10px;
}

.flexContainer-payment {
    justify-content: space-between;
    align-items: stretch;
}

.card.small-card {
    width: 329px;
    margin: 0;
    padding: 25px 20px;
}

.card-inner {
    display: flex;
    align-items: stretch;
    flex-direction: column;
    justify-content: space-between;
}

.total-payment-amount {
    color: $themeColor2;
}

.card-body {
    padding: 0;
}

.input-box-icon-container {
    position: absolute;
    top: 0;
    height: 50px;
    display: flex;
    align-items: center;
    left: 19px;
}

.input-box-with-icon input {
    padding-left: 45px;
}

.modal-content-button {
    margin-bottom: 44px;
    float: left;
    width: 100%;
    justify-content: center;
    display: flex;
}

.modal-content-button button {
    position: relative;
    float: left;
}

.modal-content-button button:after {
    background: #c5c5c5;
    width: 2px;
    height: 14px;
    content: "";
    position: absolute;
    right: 0;
    top: 2px;
}

.modal-content-button button:last-child:after {
    display: none;
}

.modal-content-width {
    width: 333px;
}

.card-header {
    padding-bottom: 18px;
}

.card.small-card .card-header {
    padding-bottom: 12px;
}

.backLink {
    margin-bottom: 4px;
}

@media (max-width:1439px) and (min-width:1200px) {
    .container {
        width: 100%;
    }
}

@media (max-width:1199px) {
    .container {
        width: 970px;
    }

    .logo-icon {
        width: 160px;
        height: 55px;
        background-size: 160px;
    }

    .header-left {
        padding-left: 0;
    }

    .card1 {
        padding: 20px 20px 30px 20px;
        max-width: 500px;
    }
}

@media (max-width:991px) {
    .container {
        width: 740px;
    }

    .logo-icon {
        width: 160px;
        height: 55px;
        background-size: 160px;
    }

    .header-inner {
        height: 120px;
    }

    .walletCashDiv {
        flex-direction: column;
        align-items: flex-start;
    }

    .card {
        padding: 20px;
    }

    .card.small-card {
        width: 260px;
        padding: 15px 15px;
    }

    .card1 {
        padding: 20px;
        max-width: 350px;
    }

    .input-box {
        margin-bottom: 0;
    }

    .input-textfield {
        height: 40px;
        font-size: 12px;
        line-height: 12px;
    }

    .card1-header h3 {
        font-size: 16px;
        letter-spacing: -0.71px;
        line-height: 26px;
        margin-bottom: 16px;
    }

    .card1-footer {
        margin-top: 16px;
    }

    .input-box-icon-container {
        height: 40px;
    }

    .button-primary {
        font-size: 12px;
        letter-spacing: 1px;
        line-height: 18px;
        height: 46px;
        text-transform: uppercase;
    }

    .link-primary {
        font-size: 10px;
        letter-spacing: 0.83px;
        line-height: 18px;
    }

    .gallop-cash-input {
        height: 40px;
        width: 72px;
        font-size: 12px;
        line-height: 15px;
        margin-left: 20px;
    }

    .walletCashDiv .mdl-checkbox {
        margin-bottom: 5px;
    }

    .text-button {
        padding: 0 25px;
    }
}

@media (max-width:767px) {
    .container {
        width: 100%;
        padding: 0 15px;
    }

    .logo-icon {
        width: 92px;
        height: 30px;
        background-size: 90px;
    }

    .header-inner {
        height: 96px;
    }

    .user-profile-img,
    .user-email {
        display: none;
    }

    .gallop-cash {
        font-size: 10px;
    }

    .user-profile-content {
        padding-left: 0;
        width: 100%;
    }

    .page-content {
        padding-bottom: 16px;
    }

    .flexContainer-payment {
        flex-direction: column;
        justify-content: flex-start;
    }

    .card {
        padding: 0;
    }

    .card.small-card {
        width: 100%;
        background: #fff;
        padding: 19px 16px;
    }

    .card-inner {
        float: left;
        width: 100%;
        padding: 23px 16px 16px;
    }

    .walletCashDiv {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 0;
    }

    .card-header h3 {
        font-size: 16px;
        letter-spacing: -0.71px;
        line-height: 26px;
    }

    .mdl-radio__label {
        font-size: 12px;
        line-height: 15px;
    }

    .mdl-radio__outer-circle {
        height: 14px;
        width: 14px;
    }

    .payment-options .mdl-radio img {
        width: 20px;
    }

    .payment-option-section .mdl-radio {
        line-height: 16px;
    }

    .add-card-link a i {
        font-size: 8px;
    }

    .mdl-checkbox {
        line-height: 12px;
    }

    .mdl-checkbox__label {
        font-size: 12px;
        line-height: 15px;
        font-family: $fontMono;
    }

    .mdl-checkbox__box-outline {
        width: 12px;
        height: 12px;
    }

    .card.small-card .card-header {
        display: none;
    }

    .payment-note {
        font-size: 12px;
        line-height: 19px;
    }

    .gallop-cash-input {
        height: 40px;
        width: 72px;
        font-size: 12px;
        line-height: 15px;
        margin-left: 16px;
    }

    .payment-button-container {
        padding: 24px 0 0;
    }

    td {
        font-size: 12px;
        line-height: 15px;
        font-family: $fontRegular;
        padding: 3px 0;
        letter-spacing: normal;
    }

    .total-payment-label {
        line-height: 32px;
        letter-spacing: normal;
        font-size: 16px;
        font-family: $fontMono;
    }

    .total-payment-amount {
        font-size: 16px;
        line-height: 32px;
        font-family: $fontBold;
    }

    .payment-table {
        margin-top: 0;
        margin-bottom: 20px;
    }

    .payment-msg {
        margin-bottom: 0;
        font-size: 12px;
    }

    .mdl-checkbox.is-upgraded {
        padding-left: 20px;
    }

    .addCardFormTemplate {
        position: fixed;
        z-index: 9;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(238, 237, 235, 0.8);
        display: none;
    }

    .addCardFormTemplate.active {
        display: block;
    }

    .card1 {
        background-color: #EEEDEB;
        box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
        max-width: none;
        margin-top: 0;
        padding: 15px 32px 17px;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9;
    }

    .mdl-radio__inner-circle {
        height: 8px;
        width: 8px;
    }

    .payment-note-inner {
        margin-left: 0;
    }

    .user-name {
        text-align: right;
    }

    .button1 {
        width: 104px;
    }

    .button2 {
        width: 116px;
    }

    .appleDevice .input-textfield,
    .appleDevice .gallop-cash-input {
        font-size: 16px !important;
        min-width: 100px;
        letter-spacing: -1.1px !important;
        word-spacing: -2px !important;
        padding-top: 0px;
        padding-bottom: 0;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
    }
}


/*modal css*/
.modal-dialog {
    width: calc(100% - 48px);
    max-width: 896px;
    margin: 0 auto;
}

.modal-dialog-md {
    max-width: 710px;
}

.modal-backdrop {
    background-color: #fff;
}

.modal-backdrop.in {
    opacity: 0.8;
}

.modal-content {
    border-radius: 0px;
    box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
    border: none;
    margin-top: 24px;
    margin-bottom: 24px;
}

.modal-header {
    border: none;
    padding: 24px 17px 20px 25px;
}

.modal-title {
    display: inline-block;
    width: 35px;
    float: left;
}

.modal-title img {
    width: 35px;
}

.close {
    color: #000;
    opacity: 1;
}

.close:hover {
    color: #000;
    opacity: 1;
}

.modal-body {
    padding-bottom: 35px;
    float: left;
    width: 100%;
    padding-top: 8px;
}

.modal-content {
    text-align: center;
    float: left;
    width: 100%;
}

.modal-icon-container {
    float: left;
    width: 100%;
    text-align: center;
}

.modal-icon {
    background-color: #DDFFF7;
    border: 3px solid #1EBD97;
    width: 48px;
    height: 48px;
    display: inline-flex;
    color: #1EBD97;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
}

.modal-icon i {
    font-size: 30px;
}

.modal-content-heading {
    float: left;
    width: 100%;
    margin-top: 15px;
    margin-bottom: 14px;
}

.modal-content-heading h3 {
    margin: 0 auto;
    max-width: 654px;
    font-size: 22px;
    letter-spacing: -0.98px;
    line-height: 34px;
}

.modal-content-text {
    margin-bottom: 13px;
    float: left;
    width: 100%;
}

.modal-content-note {
    float: left;
    width: 100%;
    margin-bottom: 42px;
}

.modal-content-text p {
    line-height: 26px;
    font-family: "apercu-r";
    color: #6C6865;
    font-size: 18px;
    margin-left: auto;
    margin-right: auto;
}

.modal-content-note p {
    letter-spacing: 0.23px;
    line-height: 21px;
    font-size: 14px;
    color: var(--dark-bg-color);
    max-width: 534px;
    margin: 0 auto;
}

.modal-content-width {
    margin-bottom: 33px;
}




@media (max-width:991px) {
    .modal button.link-primary {
        font-size: 12px;
        letter-spacing: 1px;
        line-height: 18px;
    }
}

/*common css ends*/
@media (max-width:767px) {
    .modal-body {
        padding-bottom: 15px;
    }

    .modal-content-heading h3 {
        letter-spacing: -0.71px;
        line-height: 23px;
        font-size: 16px;
    }

    .modal-content-text p {
        font-size: 12px;
        line-height: 19px;
    }

    .modal-content-note p {
        font-size: 10px;
        letter-spacing: -0.45px;
        line-height: 16px;
    }

    .modal-title img {
        width: 23px;
    }

    .close i {
        font-size: 16px;
    }

    .modal-header {
        padding: 16px 17px 15px 10px;
    }

    .modal-content-heading {
        margin-top: 14px;
        margin-bottom: 14px;
    }

    .modal-content-note {
        margin-top: 4px;
        margin-bottom: 62px;
    }

    .modal-content-button {
        margin-bottom: 28px;
    }

    .modal-icon-container {
        margin-top: 12px;
    }

    .delete-modal-button {
        margin-bottom: 0;
        margin-top: 23px;
    }
}


::placeholder {
    /* Chrome/Opera/Safari */
    color: #AEAEAE;
    opacity: 1;
}

::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    color: #AEAEAE;
    opacity: 1;
}

::-moz-placeholder {
    /* Firefox 19+ */
    color: #AEAEAE;
    opacity: 1;
}

:-ms-input-placeholder {
    /* IE 10+ */
    color: #AEAEAE;
    opacity: 1;
}

:-moz-placeholder {
    /* Firefox 18- */
    color: #AEAEAE;
    opacity: 1;
}



.appleDevice ::placeholder {
    /* Chrome/Opera/Safari */
    padding-top: 4px;
}

.appleDevice ::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    padding-top: 4px;
}

.appleDevice ::-moz-placeholder {
    /* Firefox 19+ */
    padding-top: 4px;
}

.appleDevice :-ms-input-placeholder {
    /* IE 10+ */
    padding-top: 4px;
}

.appleDevice :-moz-placeholder {
    /* Firefox 18- */
    padding-top: 4px;
}