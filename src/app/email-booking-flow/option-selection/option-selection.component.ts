import { Component, OnInit, Input, OnChanges, SimpleChanges, Output, EventEmitter, ChangeDetectorRef, ViewChild, ElementRef } from '@angular/core';
import { EmailSearchV4DTO } from '../../entity/email-flow/email-searchv4-dto';
import { MultiFlightViewDTO } from '../../entity/email-flow/multi-flight-view-dto';
import { EventViewDTO } from '../../entity/email-flow/event-view-dto';
import { FlightViewDTO } from '../../entity/email-flow/flight-view-dto';
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';
import { environment } from '../../../environments/environment';
import { BillingItem } from '../../entity/email-flow/billing-item';
import * as Script from "../../../assets/js/emailflow/script.js";
import { UserAccountService } from '../../user-account.service';
import { UserAccountInfo } from '../../entity/user-account-info';
import { SubscriptionPlan } from '../../entity/subscription-plan';
import { FlightLegEmailDTO } from '../../entity/email-flow/flight-leg-email-dto';
import { Subscription, Subject } from 'rxjs';
import { BookingService } from 'src/app/booking.service';
import { BookingResponse } from 'src/app/entity/booking-response';
import { Constants } from 'src/app/util/constants';
import { BookingResponseErrorType } from 'src/app/enum/booking-response-error.type';
import { deserialize } from 'src/app/util/ta-json/src/methods/deserialize';
import { ToastrService } from 'ngx-toastr';
import { BookingResponseError } from 'src/app/entity/booking-response-error';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Utils } from 'ngx-bootstrap/utils';
import { DeleteCardModelComponent } from '../delete-card-model/delete-card-model.component';
import { BookingMessageModalComponent } from 'src/app/booking-message-modal/booking-message-modal.component';
import { GallopLocalStorageService } from 'src/app/gallop-local-storage.service';
import { CommonUtils } from '../../util/common-utils';
import { PaymentTypes } from 'src/app/enum/payment-types';
import { DateUtils } from 'src/app/util/date-utils';
import { EmailQuoteOptionsService } from 'src/app/email-quote-options.service';
import { _ } from 'src/app/util/title';
import { TranslateService } from "@ngx-translate/core";
import { GallopAnalyticsUtil } from 'src/app/analytics.service';
import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { AlgoTypes } from 'src/app/enum/algo-types';
import { Location, CurrencyPipe, PlatformLocation, LocationChangeEvent, DatePipe } from '@angular/common';
import { ConnectionService } from 'ng-connection-service';
import { format } from 'util';
import { HotelSearchService } from 'src/app/hotel-search.service';
import { CarBookingService } from 'src/app/car-booking.service';
import { CarResult } from 'src/app/entity/carResult';
import { MultiCarViewDTO } from 'src/app/entity/email-flow/car-view-dto';
import { SearchService } from 'src/app/search.service';
import { FlightSearchQueryParam } from 'src/app/entity/flight-search-query-param';
import { SeatSelect } from 'src/app/entity/seatSelected';
import { SeatComponent } from 'src/app/seat/seat.component';
import { DeviceDetailsService } from 'src/app/device-details.service';
import { FlightResult } from 'src/app/entity/flight-result';
import { FilterService } from 'src/app/filter.service';
import { FlightUtils } from 'src/app/util/flight-utils';
import { FareAttributes } from 'src/app/entity/fare-attributes';
import { BaggageDetails } from 'src/app/entity/baggage-details';
import { TravelerDetails } from 'src/app/entity/traveler-details';
import { CardInfo } from 'src/app/entity/card-info';
import { AddCardWidgetComponent } from '../add-card-widget/add-card-widget.component';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { AbstractControl } from '@angular/forms';
import { TripDetails } from 'src/app/entity/email-flow/TripDetails';
import { TouchSequence } from 'selenium-webdriver';
import { constants } from 'http2';
import { AdminPanelService } from 'src/app/admin-panel.service';

declare var seatModalHack: any;
declare var updateDOM: any;
declare var setRequestParam: any;
declare var buildConfirmationBookingData: any;
declare var getSelectedFlightIndex: any;
declare var selectionCompleted: any;
declare var isPassportMandatoryForBooking: any;
declare var isZipcodeMandatoryForBooking: any;
declare var canAttemptDirectBooking: any;
declare var canAttemptRevalidate: any;
declare var canAttemptCarRevalidate: any;
declare var notifyAgent: any;
declare var getTraflaHotelChainCode: any;
declare var revalidateUpdateFlightPrice: any;
declare var revalidateUpdateCarPrice: any;
declare var selectRowByIndexes: any;
declare var processQuotationResponseInternal: any;
declare var toggleListByIndex: any;
declare var calculateSelectionByEventIndex: any;
declare var calculateSelectionByEventIndex1: any;
declare var isOutsidePolicy: any;
declare var getCurrentlyOpenNgxSmartModalIds: any;
declare var setNgxSmartModalOpenStateClosed: any;
declare var showTravellersCard: any;
declare var hideTravellersCard: any;
declare var updatePaymentDOM: any;
declare var alignNextButtonToBottom: any;
declare var nextButtonClicked: any;
declare var autoChangeSelection: any;

@Component({
    selector: 'option-selection',
    templateUrl: './option-selection.component.html',
    styleUrls: ['./option-selection.component.scss', '../app-loader/app-loader.component.scss'],
    standalone: false
})
export class OptionSelectionComponent implements OnInit, OnChanges {
  changeStyle() {
    if (this.isMobile) {
      return { 'background-color': '#f7f7f7', 'border-top': '2px solid #e3e3e3', 'display': 'block' };
    } else {
      return { 'background-color': '#f3f3f3', 'border-top': 'none', 'display': 'flex', 'align-items': 'center' }
    }
  }
  getMobileStyle(){
    if (this.isMobile) {
      return { 'margin-left': '45px', 'margin-top': '10px' };
    } else {
      return { 'margin-left': '0px', 'margin-top': '0px',  }
    }
  }
  singleOption = false;
  singleOption1 = true;
  showError = false;
  rebookingConfirm = false;
  selectedOptionIndex=0;
  cabinClasses;
  creditExist: Array<boolean>[];;
  bsModelLoginRef: BsModalRef;
  eventIndex: string;
  @Output() goBackEmitter1 = new EventEmitter();
  @Output() goBackEmitterForModal = new EventEmitter();
  @Output() goBackEmitterForpaymentChange = new EventEmitter();
  eventCounter = 0;
  resortFee = 0;
  tripSelectedToshowOnpopUp: any;
  public eventList: Array<EventViewDTO>;
  @Input() emailQuotOptions: EmailSearchV4DTO;
  @Input() noOfPassengers: number;
  public totalPayble: number;
  public currencyCode: string = 'USD';
  billingItemList: Array<BillingItem>;
  currencyPipe: CurrencyPipe = new CurrencyPipe('en_US');
  private revalidationPriceChanged: boolean = false;
  private preSelectedEventIdx = -1;
  private preSelectedOptionIdx: number = -1;
  public travellersCount: number = 1;
  private travellerTypeData: any;
  revalidationCompleted = false;
  revalidationCompleted1 = false;
  private preSelectedEventOptions: any;
  stars: Array<number> = [1, 2, 3, 4, 5];
  optionList = [];
  carArray = [];
  isMobile: boolean;
  isMobile1: boolean;
  cancellationPolicy: string;
  deviceSubscription: Subscription;
  deviceSubscription1: Subscription;
  private selectionType: string = 'none';
  @Input() pageMode: string;
  private ticketId: string;
  private threadId: string;
  bsModalRef: BsModalRef;
  bsModalRefTripsModal: BsModalRef;
  selectedFlightsAvailable: boolean;
  hideGoBackButton = false;
  isSelectedRevalidating: boolean;
  connectionListener: Subscription;
  @Output() selectedFlightChange = new EventEmitter();
  @Output() changeSelectionClicked = new EventEmitter();
  njoySpecificBuild: boolean;
  skipInPolicyOptions = false;
  showLoyaltyNumberValidation = false;
  constructor(private activatedRoute: ActivatedRoute,
    private searchService: HotelSearchService,
    public searchService1: SearchService,
    public bookingService: BookingService,
    private toastr: ToastrService,
    private modalService: BsModalService,
    public ngxSmartModalService: NgxSmartModalService,
    private gallopLocalStorage: GallopLocalStorageService,
    public router: Router,
    public deviceDetailsService: DeviceDetailsService,
    public translateService: TranslateService,
    private userAccountInfoService: UserAccountService,
    private location: Location,
    private cdRef: ChangeDetectorRef,
    private filterService: FilterService,
    private connectionService: ConnectionService,
    private emailBookingFlowService: EmailQuoteOptionsService,
    private ngxAnaltics:GoogleAnalyticsService,
    private adminPanelService: AdminPanelService,
    private carBookingService: CarBookingService,
    location1: PlatformLocation) {
    this.searchService1.filterReset = true;
    this.bookingService.priceChange = false;
    this.searchService1.changeClick = false;
    location1.onPopState((event: LocationChangeEvent) => {
      this.searchService1.selectedFilter = [];
      this.bookingService.proceedButton = false;

      if (this.bsModalRef) {
        this.bsModalRef.hide();
        ;
      }
      if (this.bookServiceSubscription) {
        this.bookServiceSubscription.unsubscribe()
       
      }
    });
  }
  airlines: any;
  isNjoySpecificRelease: boolean;
  airports: any;
  currScreenWidth: any;

  ngOnInit() {
    this.isNjoySpecificRelease = this.userAccountInfoService.isItNjoyBuild();
    if (this.searchService1.displayCurrency) {
      this.currencyCode = this.searchService1.displayCurrency;
    }

    this.airlines = this.bookingService.airlines;
    this.airports = this.bookingService.airports;
    this.creditExist = new Array(10).fill(null).map(_ => []);
    this.njoySpecificBuild = this.userAccountInfoService.isItNjoyBuild();
    this.deviceSubscription = this.deviceDetailsService.isMobile1().subscribe(isMobile => {
      this.isMobile = isMobile;
    });
    this.deviceSubscription = this.deviceDetailsService.currScreenWidth$.subscribe(screenWidth => {
      this.currScreenWidth = screenWidth;
    });
    this.deviceSubscription1 = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
    this.activatedRoute.queryParams.subscribe(params => {
      let reminderMode = false;
      if (params['confirmedOptions']) {
        this.preSelectedEventOptions = JSON.parse(params['confirmedOptions']);
        reminderMode = true;
      }
      this.pageMode = 'emailflow'
      if (params['selected'] && params['legIndex']) {
        this.preSelectedEventIdx = parseInt(params['legIndex']);
        this.preSelectedOptionIdx = parseInt(params['selected']) - 1;
        this.selectionType = 'flight';
      } else if (params['SelectedHotelOption'] && params['SelectedHotelOption'].length > 0) {
        let selections = params['SelectedHotelOption'].split('_');
        this.preSelectedEventIdx = selections[1];
        this.preSelectedOptionIdx = parseInt(selections[0]) - 1;
        this.selectionType = 'hotel';
      } else if (params['SelectedCarOption'] && params['SelectedCarOption'].length > 0) {
        let selections = params['SelectedCarOption'].split('_');
        this.preSelectedEventIdx = selections[1];
        this.preSelectedOptionIdx = parseInt(selections[0]) - 1;
        this.selectionType = 'cars';
      }
      else if (params['pageMode'] && params['pageMode'] !== 'emailflow') {
        this.preSelectedEventIdx = 0;
        this.preSelectedOptionIdx = 0;
        this.selectionType = 'default'
        this.pageMode = params['pageMode'];
      }

      if (params['traveller']) {
        this.travellersCount = parseInt(params['traveller']);
      }
      if (params['travellerData']) {
        this.travellerTypeData = JSON.parse(params["travellerData"]);
      }
      this.ticketId = params['ticketId'];
      this.threadId = params['threadId'];
      let notifyURL = environment.agentNotifyURL;
      this.bookingService.pageMode = this.pageMode;
      if (this.pageMode === 'emailflowAgent') notifyURL = environment.agentGenTransactionURL;
      setRequestParam(params['emailId'], params['ticketId'], params['threadId'],
        params['ticketNumber'], notifyURL, this.travellerTypeData, this.travellersCount, true);
    });
    updateDOM();

    //  if(this.preSelectedEventIdx >=0 && this.preSelectedOptionIdx >=0){
    //   setTimeout(()=>{
    //   this.preSelectionWork()
    //   ,1000}
    //     );
    //  }

    if (this.pageMode === 'WebSearch') {
      this.cabinClasses = JSON.parse(this.gallopLocalStorage.getItem('cabinClassNames'));
    }
  }
  fetchAccountInfoSubscription: Subscription;
  bookingRequestStarted = false;
  userAccountInfoObj: UserAccountInfo;
  isUatpallowedUser(eIndex) {
    if (this.showBillToCompany('flight')) {
      return true;

    } else if (this.isLoggedIn() && this.userAccountInfoObj.userInfo.employeeInfo
      && this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse != null
      && this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse
      && this.isUserSelectedUATPSpecificAccountCode(eIndex)) {
      return true;
    } else if (!this.showBillToCompany('flight') && !this.isUserSelectedUATPSpecificAccountCode(eIndex)) {
      return false;
    }
  }
  gettripdetails(type, eIndex) {
    if (this.getTotalPassengers() === 1) {
      if (type == 'flight') {
        if (this.tripDetails && this.tripDetails[eIndex] && this.tripDetails[eIndex].ffnno && this.tripDetails[eIndex].ffnno.length > 0) {
          this.ffnNo[eIndex] = this.tripDetails[eIndex].ffnno[0].frequent_flyer_number;
          return true
        } else {
          return false;
        }
      } else if (type == 'car') {
        if (this.tripDetails && this.tripDetails[eIndex] && this.tripDetails[eIndex].clnno && this.tripDetails[eIndex].clnno.length > 0) {
          this.clnNo[eIndex] = this.tripDetails[eIndex].clnno[0].rentalCarLoyaltyNumber;
          return true
        } else {
          return false;
        }

      } else if (type == 'hotel') {
        if (this.tripDetails && this.tripDetails[eIndex] && this.tripDetails[eIndex].hlnno && this.tripDetails[eIndex].hlnno.length > 0) {
          if (this.tripDetails[eIndex].hlnno[0].loyaltyPointsSupported) {
            this.hlnNo[eIndex] = this.tripDetails[eIndex].hlnno[0].hotel_loyality_number;
          }else if(!this.tripDetails[eIndex].hlnno[0].loyaltyPointsSupported){
            return false
          }
          return true
        } else {
          return false;
        }
      } else {
        return false;
      }

    }

  }

  omit_special_char(event) {
    const allowedKeys = ['Enter', 'Tab']; // Allow Enter and Tab
    const inputElement = event.target as HTMLInputElement;

    if (allowedKeys.indexOf(event.key) !== -1 || /^[a-zA-Z0-9]+$/.test(event.key)) {
      // Allow the key event
    } else {
      event.preventDefault(); // Prevent the default behavior
    }

  }
  getFfnBorderColor(type, eIndex,hotel ?) {
    if (type === 'hotel') {
     
      if (this.searchService1.paymentPage) {
        return { 'border': '1px solid rgb(218,218,218)' }
      }else if(!this.getLoyalityPointSupported(eIndex)){
      return { 'border': '1px solid rgb(218,218,218)' }
      }
      else if(this.showLoyaltyNumberValidation &&  this.loyalNumberMandatoryOrNot(eIndex,hotel)&& (this.hlnNo[eIndex]=='' || this.hlnNo[eIndex]==null)){
          return{'border': '1px solid var(--dark-bg-color)'};
      }
    } else if (this.searchService1.paymentPage) {
      return { 'border': '1px solid rgb(218,218,218)' }
    } else {
      return { 'border': '1px solid var(--dark-bg-color)' }
    }
  }
  getFfnchange(event, type, index) {

    if (type === 'flight') {
      if (event && event.length === 0) {
        this.tripDetails[index].ffnno[0].ffnAvailable = false;
      }
      this.tripDetails[index].ffnno[0].frequent_flyer_number = event;


    } else if (type === 'car') {
      if (event && event.length === 0) {
        this.tripDetails[index].clnno[0].clnAvailable = false;
      }
      this.tripDetails[index].clnno[0].rentalCarLoyaltyNumber = event;
    }
    else if (type === 'hotel') {
      if (event && event.length === 0) {
        this.tripDetails[index].hlnno[0].hlnAvailable = false;
      }
      this.tripDetails[index].hlnno[0].hotel_loyality_number = event;
    }
  }
  getLoyalityPointSupported(eIndex) {
    if (this.tripDetails && this.tripDetails[eIndex] && this.tripDetails[eIndex].hlnno && this.tripDetails[eIndex].hlnno.length > 0) {
      return this.tripDetails[eIndex].hlnno[0].loyaltyPointsSupported;

    }
  }
  getffnname(type, eIndex) {
    if (type == 'flight') {
      if (this.tripDetails && this.tripDetails[eIndex] && this.tripDetails[eIndex].ffnno && this.tripDetails[eIndex].ffnno.length > 0) {
        return this.tripDetails[eIndex].ffnno[0].name

      }
    } else if (type == 'car') {
      if (this.tripDetails && this.tripDetails[eIndex] && this.tripDetails[eIndex].clnno && this.tripDetails[eIndex].clnno.length > 0) {
        return this.tripDetails[eIndex].clnno[0].rentalCarName;

      }

    }
    else if (type == 'hotel') {
      if (this.tripDetails && this.tripDetails[eIndex] && this.tripDetails[eIndex].hlnno && this.tripDetails[eIndex].hlnno.length > 0) {
        return this.tripDetails[eIndex].hlnno[0].name;

      }

    }
  }
  getffnbumber(type, eIndex) {
    if (type == 'flight') {
      if (this.tripDetails && this.tripDetails[eIndex] && this.tripDetails[eIndex].ffnno && this.tripDetails[eIndex].ffnno.length > 0) {
        return this.tripDetails[eIndex].ffnno[0].frequent_flyer_number;

      } else {
        return ""
          ;
      }
    } else if (type == 'car') {
      if (this.tripDetails && this.tripDetails[eIndex] && this.tripDetails[eIndex].clnno && this.tripDetails[eIndex].clnno.length > 0) {
        return this.tripDetails[eIndex].clnno[0].rentalCarLoyaltyNumber;

      } else {
        return ""
          ;
      }
    }
    else if (type == 'hotel') {
      if (this.tripDetails && this.tripDetails[eIndex] && this.tripDetails[eIndex].hlnno && this.tripDetails[eIndex].hlnno.length > 0 && this.tripDetails[eIndex].hlnno[0].loyaltyPointsSupported) {
        return this.tripDetails[eIndex].hlnno[0].hotel_loyality_number;

      } else {
        return ""
          ;
      }
    }
  }
  fetchSavedBookingDetailsSubscription: Subscription;
  subscriptionevents() {
    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      // if (!this.userAccountInfoService.paymentPageSave) {
      if (!this.bookingRequestStarted && userAccountInfoObj) {
        this.userAccountInfoObj = userAccountInfoObj;


        if (this.emailQuotOptions && this.pageMode !== 'WebSearch') {
          let processedOptions: any = processQuotationResponseInternal(this.emailQuotOptions);
          for (let event of processedOptions) {
            this.tripDetails = [];
            let eventViewDTO = new EventViewDTO(event.eventId, event.eventType, event.locationAddress);
            if (!event.eventOptions) continue;
            if (event.eventType == 'flight') {

              for (let option of event.eventOptions) {


                let details = { ffnno: [], seatinfo: [], paymenttype: {}, lugagge: "", type: 'flight', error: false, clnno: [], hlnno: [], prepay: false, paymentError: false,selectedPartnerCode:'' }

                details.ffnno = this.getAirLoyalitynumber(this.userAccountInfoObj.userInfo, option)
                this.tripDetails.push(details);
              }




            } else if (event.eventType == 'hotel') {

              for (let option of event.eventOptions) {

                let details = { ffnno: [], seatinfo: [], paymenttype: {}, lugagge: "", type: 'hotel', error: false, clnno: [], hlnno: [], prepay: true, paymentError: false,selectedPartnerCode:'' }

                details.hlnno = this.getLoyalitynumber(this.userAccountInfoObj.userInfo, option)
                this.tripDetails.push(details);
              }


            } else if (event.eventType == 'cars') {


              for (let option of event.eventOptions) {



                let details = { ffnno: [], seatinfo: [], paymenttype: {}, lugagge: "", type: 'car', error: false, clnno: [], hlnno: [], prepay: true, paymentError: false ,selectedPartnerCode:''}
                details.clnno = this.getCarLoyalityNumbers(this.userAccountInfoObj.userInfo, option)
                this.tripDetails.push(details);
              }




            }
          }

        }
        //this.isExpenseIsSelected();
        this.unsetCardMode();
        this.getCardList();
        //this.updateUserAccountInfo();
        if (this.bsModalRef) {
          this.bsModalRef.hide();
          this.userAccountInfoService.deletingCard = false;
          //}
        } else {
          // this.bookingRequestStarted = false;
        }
      }
    });
  
  }
  private getAttemptDirectBookingFlag(): boolean {
    if (this.pageMode == 'emailflow') {
      return canAttemptDirectBooking();
    } else {
      return true;
    }
  }
  showSkip() {
    if (this.searchService1.paymentPage || this.searchService1.multiTripBooking) {
      return true;
    } else if (this.preSelectedEventOptions) {
      return true;
    }
  }
  showOutPolicy(index) {
    if (this.searchService1.paymentPage) {
      return true;
    } else if (this.isOutPolicyScenario1(index) && !this.searchService1.paymentPage) {
      return false;
    } else {
      return true;
    }
  }
  carImageShow() {
    if (this.searchService1.paymentPage && this.isMobile) {
      return false;
    } else {
      return true;
    }
  }
  openModal(modal, type, item, option?) {
    this.tripSelectedToshowOnpopUp = item;
    this.tripSelectedToshowOnpopUp['type'] = type;
    if (option) {
      let policy = option.getWithinPolicy();
      this.tripSelectedToshowOnpopUp['policy'] = policy;
    }

    this.bsModalRef = this.modalService.show(modal);
    
  }
  private preSelectionWork() {
    if (this.searchService1.multiTripBooking) {
      toggleListByIndex(0);
      this.preSelectedEventOptions = this.selectionForMultiBooking();
      for (let index in this.eventList) {
        let event = this.eventList[index];
        if (this.preSelectedEventOptions['event-' + index]) {
          selectRowByIndexes(index, this.preSelectedEventOptions['event-' + index]);
        } else {
          calculateSelectionByEventIndex(index);
          this.toggleSkip();
        }
      }
      this.rowSelected('true', -1);
    } else {
      for (let index in this.eventList) {
        toggleListByIndex(index);
      }
      if (this.selectionType && this.selectionType === 'hotel') {
        for (let index in this.eventList) {
          let event = this.eventList[parseInt(index)];
          if (this.eventList[index].getEventId() === '' + this.preSelectedEventIdx) {
            this.preSelectedEventIdx = parseInt(index);
            break;
          }
        }
        // toggleListByIndex(this.preSelectedEventIdx);
        selectRowByIndexes(this.preSelectedEventIdx, this.preSelectedOptionIdx);
        this.rowSelected('true', -1);
      } else if (this.selectionType && this.selectionType === 'flight') {
        let selectedFlightEventId = this.emailQuotOptions.multiFlightOptions[this.preSelectedEventIdx][0][0].eventId;
        for (let index in this.eventList) {
          let event = this.eventList[parseInt(index)];
          if (this.eventList[index].getEventId() === '' + selectedFlightEventId) {
            this.preSelectedEventIdx = parseInt(index);
            break;
          }
        }
        // toggleListByIndex(this.preSelectedEventIdx);
        selectRowByIndexes(this.preSelectedEventIdx, this.preSelectedOptionIdx);
        this.rowSelected('true', -1);
      } else if (this.selectionType && this.selectionType === 'cars') {
        for (let index in this.eventList) {
          let event = this.eventList[parseInt(index)];
          if (this.eventList[index].getEventId() === '' + this.preSelectedEventIdx) {
            this.preSelectedEventIdx = parseInt(index);
            break;
          }
        }
        selectRowByIndexes(this.preSelectedEventIdx, this.preSelectedOptionIdx);
        this.rowSelected('true', -1);
        // toggleListByIndex(0);
      }
      else if (this.selectionType && this.selectionType === 'default') {
        // toggleListByIndex(this.preSelectedEventIdx);
        selectRowByIndexes(this.preSelectedEventIdx, this.preSelectedOptionIdx);
        this.rowSelected('true', -1);
      }
      for (let index in this.eventList) {
        if (parseInt(index) !== this.preSelectedEventIdx) {
          let optionsCount = this.eventList[index].getOptions().length;

          if (optionsCount == 0 && this.eventList[index].getHotelOptions() != null) {
            optionsCount = this.eventList[index].getHotelOptions().length;
          } else if (optionsCount == 0 && this.eventList[index].getCarOptions() != null) {
            optionsCount = this.eventList[index].getCarOptions().length;
          }
          if ((parseInt(index) === 1 && this.searchService1.outsidePolicyselected) && this.searchService1.inPolicyFlight.length > 0) {
            if (optionsCount == 1) {
              // if(parseInt(index) !== (this.preSelectedEventIdx+1) ){
              //   toggleListByIndex(index);
              // }
              //selectRowByIndexes(index,0);
              //this.rowSelected('true',-1);
            }
          } else {
            if (optionsCount == 1) {
              // if(parseInt(index) !== (this.preSelectedEventIdx+1) ){
              //   toggleListByIndex(index);
              // }
              selectRowByIndexes(index, 0);
              this.rowSelected('true', -1);
            }
          }
        }
      }

    }

  }
  isOutPolicyScenario(index) {
    if ((index === 0 && this.searchService1.outsidePolicyselected) && this.searchService1.inPolicyFlight.length > 0) {
      return false;
    } else {
      return true;
    }
  }
  isOutPolicyScenario1(index) {
    if ((index === 1 && this.searchService1.outsidePolicyselected) && this.searchService1.inPolicyFlight.length > 0) {
      return true;
    } else {
      return false;
    }
  }
  showContinueButton(){
    return (this.getTotalPassengers() === 1 || this.eventList.length > 1) && !this.searchService1.paymentPage  && !this.hideButtonForAddingTrips && this.searchService1.tripFeatureEnabled  && !this.searchService1.selectedEventID
  }
  showContinueButton1(){
    return this.eventList.length > 1 && !this.searchService1.paymentPage && this.pageMode!=='WebSearch' && this.searchService1.tripFeatureEnabled && !this.searchService1.selectedEventID;
  }
  public getTotalPassengers() {
    if (this.emailQuotOptions.hotelOptionsList && this.emailQuotOptions.hotelOptionsList.length > 0) {
      if(this.searchService.retryHotelFailedBooking){
        let passengerFormData: any = JSON.parse(this.gallopLocalStorage.getItem('passengersFormData'));
        if(passengerFormData && passengerFormData['passengers']) return passengerFormData['passengers'].length;
      }
      return this.emailQuotOptions.hotelOptionsList[0][0].hotelRooms.length;
    } else if (this.emailQuotOptions && this.emailQuotOptions.flightSearchOptions) {
      this.searchService1.adultCount =
        this.emailQuotOptions.flightSearchOptions.adultCount + this.emailQuotOptions.flightSearchOptions.childCount;
      return this.emailQuotOptions.flightSearchOptions.adultCount +
        this.emailQuotOptions.flightSearchOptions.infantCount +
        this.emailQuotOptions.flightSearchOptions.childCount;
    } else {
      return this.travellersCount;
    }
  }
  getDuration(item, legIndex) {
    return FlightUtils.getFlightRebookingLegDuration(item, legIndex);
  }
  public getStops(hops) {
    if (hops.length - 1 === 0) {
      return "non-stop";
    } else if (hops.length - 1 === 1) {
      return "1 stop";
    } else {
      return hops.length - 1 + " stops";
    }
  }
  public getPolicySetFlag(option?, type?) {
  
      if (this.emailQuotOptions && this.getSearchAlgoType() === AlgoTypes.PODUCTIVITY.toString()) {
        return this.emailQuotOptions.policySet;
      } else {
        return false;
      }
    
  }
  getNUmberOfdigitOfPrice(symbol, price) {
    if (this.pageMode === 'WebSearch' && this.eventList[0].hotelOptions
      && this.eventList[0].hotelOptions.length === 1) {
      return;
    }
    let str = symbol + price;
    if (!this.isMobile) {
      if (str.length >= 8) {
        return { 'font-size': '14px' }
      } else if (str.length < 8) {
        return { 'font-size': '16px' }
      } else if (str.length <= 6) {
        return { 'font-size': '18px' }
      }
    } else {
      if (str.length > 6) {
        return { 'font-size': '10px' }
      } else if (str.length <= 6) {
        return { 'font-size': '12px' }
      } else if (str.length <= 5) {
        return { 'font-size': '14px' }
      }
    }
  }
  isEventSelectedPaymnetPage(index) {
    if (this.searchService1.paymentPage) {
      return this.isEventSelected(index);
    } else {
      return true;
    }
  }
  clicked() {
    if (this.ngxSmartModalService.getOpenedModals() &&
      this.ngxSmartModalService.getOpenedModals().length > 0
    ) {
      let modals = this.ngxSmartModalService.getOpenedModals();
      for (let index = 0; index < modals.length; index++) {
        var currModalTimeStamps = getCurrentlyOpenNgxSmartModalIds();
        let helpModalAlreadyOpen = false;
        if (currModalTimeStamps[modals[index].id] &&
          new Date().getTime() - currModalTimeStamps[modals[index].id] > 500
        ) {
          if (modals[index].id !== 'helpModal') {
            this.ngxSmartModalService.getModal(modals[index].id).close();
            setNgxSmartModalOpenStateClosed(modals[index].id);
          } else {
            if ($('.modalAirportFilterInfo').css('display') !== 'none') {
              $('.modalAirportFilterInfo').slideToggle();
              setNgxSmartModalOpenStateClosed(modals[index].id);
            }
          }
        }
      }
    }
  }
  getpreviousBookingGrossAmount(){
    if(this.bookingService.previousBooking.option.flight_option.displayPrice){
return this.bookingService.previousBooking.option.flight_option.displayPrice;
    }else{
      return this.bookingService.previousBooking.option.flight_option.price
    }
  }
  previousbookingCredit(){
    let travel_credit = this.bookingService.previousTransaction.data.find(e=> e.transactionEventType == "travel_credit")
      if(travel_credit){
        return true;
      }else{
        false;
      }
  }
  getBaggageDetailsColor1(item) {
    let baggageDetails: BaggageDetails = this.tripSelectedToshowOnpopUp.hops[0].baggageAllowance;
    if (this.searchService1.brandedFareCheckBox && this.tripSelectedToshowOnpopUp.hops[0].brandDetails && this.tripSelectedToshowOnpopUp.hops[0].brandDetails.length > 0) {
      baggageDetails = this.tripSelectedToshowOnpopUp.hops[0].baggageAllowance;
    }
    if (baggageDetails) {
      if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return 'allowed';
      }
      if ((baggageDetails.maxWeightAllowed && baggageDetails.maxWeightAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.maxWeightAllowed) === 0) || (!baggageDetails.maxWeightAllowed)) {
        return 'notallowed';
      } else {
        return 'allowed';
      }
    }
    return null;
  }
  getBaggageNumber(item) {
    let baggageDetails: BaggageDetails = this.tripSelectedToshowOnpopUp.hops[0].baggageAllowance;
    if (this.searchService1.brandedFareCheckBox && this.tripSelectedToshowOnpopUp.hops[0].brandDetails && this.tripSelectedToshowOnpopUp.hops[0].brandDetails.length > 0) {
      baggageDetails = this.tripSelectedToshowOnpopUp.hops[0].baggageAllowance;
    }
    if (baggageDetails) {
      if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return (Number.parseInt(baggageDetails.numberOfPieceAllowed))
      }
      if ((baggageDetails.maxWeightAllowed && baggageDetails.maxWeightAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.maxWeightAllowed) === 0) || (!baggageDetails.maxWeightAllowed)) {
        return this.translateService.instant('flightChart.Nocheckedbags');
      } else {
        return (Number.parseInt(baggageDetails.maxWeightAllowed));
      }
    }
    return null;
  }
  getpreviousBookingAmount(){
   let  totalMonney =0
   const data = this.bookingService.previousBooking;
   const fare = this.bookingService.previousTransaction.data;
   let gallopCash = this.bookingService.previousTransaction.gallopCashUsed;
   let traveler =[];
   let travlerList = [];
   for(let i=0;i<this.bookingService.rebookingTravlleremail.length;i++){
    // if(this.bookingService.rebookingTravlleremail[i].travellerType!=='INFANT'){
    traveler.push(this.bookingService.rebookingTravlleremail[i]);
    // }
   }
   
   travlerList.push(traveler);
   return CommonUtils.getTotalFare(fare, data, gallopCash,travlerList);
  
  }
  onDeleteevent(index, type, option) {
    
    let i = 0;
    if (type == 'flight') {
      let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))


      i = index;
      let seatArray = JSON.parse(this.gallopLocalStorage.getItem("selectedSeat"));
      if (seatArray && seatArray.length > 0 && selectedFlight && selectedFlight.length > 0 && seatArray.length === selectedFlight.length) {
        seatArray.pop();
        this.gallopLocalStorage.setItem("selectedSeat", JSON.stringify(seatArray));
      }
      if (selectedFlight[i].travelCreditsInfo && selectedFlight[i].travelCreditsInfo.length > 0) {
        this.searchService1.previousSelectedCreditAirline = this.searchService1.previousSelectedCreditAirline.filter(item1 => item1 !== (selectedFlight[i].legs[0].flightHops[0].carrier));
      }
      if (i === (selectedFlight.length - 1)) {
        this.searchService1.multiTripFlightDelete = true;
      }
      selectedFlight.splice(index, 1);
      this.bookingService.forMultipleBooking.splice(index, 1);
      this.searchService1.multiflightQuery.splice(index,1);
      this.searchService1.seatForMultipleBooking.splice(index, 1);
      let tempTripDetails = JSON.parse(JSON.stringify(this.tripDetails));
      this.tripDetails.splice(index, 1);
      if(this.tripDetails && this.tripDetails.length > 0 && selectedFlight && selectedFlight.length >0){
        if(tempTripDetails[index].ffnno && tempTripDetails[index].ffnno.length > 0  && tempTripDetails[index].ffnno[0].airlineCode){
          let flightIndex  = this.tripDetails.findIndex(item => item.selectedPartnerCode===tempTripDetails[index].ffnno[0].airlineCode)
            if(flightIndex >-1){
             this.tripDetails[flightIndex].ffnno = tempTripDetails[index].ffnno;
            }
       }
    }

      if (selectedFlight && selectedFlight.length > 0) {
        this.gallopLocalStorage.setItem("selectedFlight", JSON.stringify(selectedFlight));
      } else {
        this.gallopLocalStorage.removeItem("selectedFlight")
      }



      selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
      
    } else if (type === 'car') {
      let selectedCar = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"))
      i = option.flights.optionId;
      let source = option.flights.optionId;
      if (i === (selectedCar.length - 1)) {
        this.searchService1.multiTripCarDelete = true;
      }

      selectedCar.splice(source, 1);
      this.searchService1.multicarQuery.splice(i,1);
      let tempTripDetails = JSON.parse(JSON.stringify(this.tripDetails));
      this.tripDetails.splice(index, 1);
      if(this.tripDetails && this.tripDetails.length > 0 && selectedCar && selectedCar.length >0){
        if(tempTripDetails[index].clnno && tempTripDetails[index].clnno.length > 0  && tempTripDetails[index].clnno[0].rentalCarCode){
          let carIndex  = this.tripDetails.findIndex(item => item.selectedPartnerCode === tempTripDetails[index].clnno[0].rentalCarCode)
            if(carIndex >-1){
             this.tripDetails[carIndex].clnno = tempTripDetails[index].clnno;
            }
       }
    }
     

      if (selectedCar && selectedCar.length > 0) {
        this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(selectedCar));
      } else {
        this.gallopLocalStorage.removeItem("selectedCar")
      }


    } else if (type === 'hotel') {
      let selectedHotel = JSON.parse(this.gallopLocalStorage.getItem("selectedHotel"));
      let selectedHotelDetailsObj = JSON.parse(this.gallopLocalStorage.getItem("selectedHotelDetailedObj"));
      let source = option.optionId;
      i = option.optionId;
      if (i === (selectedHotelDetailsObj.length - 1)) {
        this.searchService1.multiTripHotelDelete = true;
      }
      selectedHotelDetailsObj.splice(source, 1);
      this.bookingService.cardAllowed.splice(i,1);
      selectedHotel.splice(source, 1);
      this.searchService1.multihotelQuery.splice(i,1);
      let tempTripDetails = JSON.parse(JSON.stringify(this.tripDetails));
      this.tripDetails.splice(index, 1);
      if(this.tripDetails && this.tripDetails.length > 0 && selectedHotel && selectedHotel.length >0){
        if(tempTripDetails[index].hlnno && tempTripDetails[index].hlnno.length > 0  && tempTripDetails[index].hlnno[0].hotelCode){
          let hotelIndex  = this.tripDetails.findIndex(item => item.selectedPartnerCode===tempTripDetails[index].hlnno[0].hotelCode)
            if(hotelIndex >-1){
             this.tripDetails[hotelIndex].hlnno = tempTripDetails[index].hlnno;
             let getIndex  = selectedHotel.findIndex(item => item.hotelChainCode===tempTripDetails[index].hlnno[0].hotelCode);
             if(getIndex > -1){
             this.tripDetails[hotelIndex].hlnno[0].name = selectedHotel[getIndex].hotelName;
             }
            }
       }
    }

      // this.searchService1.multiTripHotelDelete =true;
      if (selectedHotelDetailsObj && selectedHotelDetailsObj.length > 0) {
        this.gallopLocalStorage.setItem("selectedHotelDetailedObj", JSON.stringify(selectedHotelDetailsObj));
        this.gallopLocalStorage.setItem("selectedHotel", JSON.stringify(selectedHotel));
      } else {
        this.gallopLocalStorage.removeItem("selectedHotelDetailedObj");
        this.gallopLocalStorage.removeItem("selectedHotel")
      }
    }
    this.eventList.splice(index, 1);
    this.changeEmailQuotaions(index, type, i);
    let processedOptions: any = processQuotationResponseInternal(this.emailQuotOptions);
    setTimeout(() => {
      if (this.searchService1.multiTripBooking) {

        this.preSelectedEventOptions = this.selectionForMultiBooking();
        for (let index in this.eventList) {
          let event = this.eventList[index];
          if (this.preSelectedEventOptions['event-' + index]) {
            selectRowByIndexes(index, this.preSelectedEventOptions['event-' + index]);
          } else {
            calculateSelectionByEventIndex(index);
            this.toggleSkip();
          }
        }
        this.rowSelected('nothing', -1, i, type);
      }
    }, 500);

    // this.rowSelected('nothing',-1,i,type);
    updateDOM()
    
  }
  changeEmailQuotaions(i, type, j) {
    
    this.emailQuotOptions.eventList.splice(i, 1);
    let thirdKey;
    for (let key in this.emailQuotOptions.eventIdAndTypeMap) {
      let value = this.emailQuotOptions.eventIdAndTypeMap[key];
      if (type === value && parseInt(key) === i) {
        thirdKey = key;
        break
      }
    }

    delete this.emailQuotOptions.eventIdAndTypeMap[thirdKey];

    // delete this.emailQuotOptions.eventIdAndTypeMap[i];
    if (type === 'car') {
      this.emailQuotOptions.carOptionsList.splice(j, 1);
    } else if (type === 'hotel') {
      this.emailQuotOptions.hotelOptionsList.splice(j, 1);
    } else if (type === 'flight') {
      this.emailQuotOptions.multiFlightOptions.splice(j, 1);
    }
    


  }
  selectSeat(index) {
    this.bookingService.proceedButton = true;
    // history.pushState(null, null,window.location.href);
    window.scrollTo(0, 0);
    //  history.pushState(null, null, window.location.href);
    this.bsModelLoginRef = this.modalService.show(SeatComponent, {
      initialState: {
        seatIndex: index
      }, backdrop: true, keyboard: false, ignoreBackdropClick: true
    });
    seatModalHack();
  }

  ngOnChanges(changes: SimpleChanges) {
    this.subscriptionevents();
    if (this.emailQuotOptions) {
      
      let processedOptions: any = processQuotationResponseInternal(this.emailQuotOptions)
      this.processEmailQuoteOptions(processedOptions);
      setTimeout(() => {
        this.preSelectionWork()
      }, 1000);
    }
    updateDOM();
  }

  private getSearchAlgoType() {
    if (this.emailQuotOptions && this.emailQuotOptions.flightSearchOptions) {
      return this.emailQuotOptions.flightSearchOptions.algoType;
    }
    return AlgoTypes.PODUCTIVITY.toString();
  }
  
  public getCarInPolicyBlockVisibility(policySet: boolean, hotelOption: any) {
    if (!policySet) {
      return "hidden";
    }
    if (hotelOption.policy) {
      return "";
    }
    return "hidden";

  }
  public backToSelectionPage() {
    this.searchService1.paymentPage = false;
    history.go(-1);
    this.goBackEmitter1.emit('back');
  }
  public getCarOutOfPolicyBlockVisibility(policySet: boolean, hotelOption: any) {
    //if (!policySet){
    //  return "hidden";
    // }
    if (!hotelOption.policy) {
      return "";
    }
    return "hidden";

  }
  public getHotelOutOfPolicyBlockVisibility(policySet: boolean, option?) {
    if (policySet) {
      
        if (option && !option.inPolicy) {
          return ""
        } else  {
          return "hidden"
        } 
  
      
    }
    
    return "hidden";

  }
  public getFlightInPolicyBlockVisibility(policySet: boolean, option?) {
    if (policySet) {
     
        if (option && option.flights[0].hops[0].flightHighlights.withinPolicy) {
          return ""
        } else  {
          return "hidden"
        } 
  
       
    }
    return "hidden";

  }
  public getHotelInPolicyBlockVisibility(policySet: boolean, option?) {
    if (policySet) {
    
        if (option && option.inPolicy) {
          return ""
        } else  {
          return "hidden"
        } 
  
      
    }
    return "hidden";

  }
  public getFlightOutOfPolicyBlockVisibility(policySet: boolean, option?) {
    if (policySet) {
     
        if (option && !option.flights[0].hops[0].flightHighlights.withinPolicy) {
          return ""
        } else  {
          return "hidden"
        } 
  
      
    }
    
    return "hidden";

  }
  checkEventList() {
    if (this.eventList && this.eventList.length === 1
      && this.eventList[0].options
      && this.eventList[0].options.length === 1) {
      return false;
    } else if (this.eventList && this.eventList.length === 1
      && this.eventList[0].hotelOptions
      && this.eventList[0].hotelOptions.length === 1) {
      return false;
    } else {
      return true;
    }
  }
  getLocationType(item) {
    if (item && item !== '') {
      if (item === 'Terminal') {
        return 'At airport terminal'
      } else {
        return 'Outside airport - shuttle from airport terminal'
      }
    }
  }
  getDisplayDate(dateString: string): string {
    return DateUtils.getDisplayDate(dateString);
  }

  getDurationOfHop(duration) {
    let d = DateUtils.getDurationAsHrsMinObj(duration);
    let flightDuration = d.hrs + ' hr ' + d.mins + ' min';
    return flightDuration;
  }
  getRatingStarsMap(hotelStars) {
    return EmailQuoteOptionsService.getHotelRatingStarsMap(hotelStars);
  }
  isLoggedIn(): boolean {
    return this.userAccountInfoService.isLoggedIn();
  }
  currentModal = '';
  private cardToken: CardInfo;
  selectedCardIndex = -1;
  addCardMode: boolean;

  getDetailsStyleModal() {
    if (this.isMobile) {
      return { 'justify-content': 'center', 'border-left': 'none' }
    } else {
      return { 'justify-content': 'start', 'border-left': '2px solid #e3e3e3' }
    }
  }
  setCardMode(modal?,index?) {
    this.addCardMode = true;
    this.selectedOptionIndex =index;

    if (modal) {
      this.currentModal = modal;
    }
    if (!this.isMobile1) {
      this.bsModalRef = this.modalService.show(modal, {
        initialState: {
        }, backdrop: true, ignoreBackdropClick: true
      });
    } else {
      setTimeout(() => {
        this.clicked();
      }, 100);
    }

  }
  curremntNgxModalId = '';
  setCurrentModalId(id) {
    this.curremntNgxModalId = id;
    setTimeout(() => {
      this.openNgxModal(id);
    }, 100);

  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  onImgError(event){
    event.target.src = 'assets/images/carnotavailable_S.jpg'
   //Do other stuff with the event.target
   }
  iscardallowedforhotelbooking(item,index){
    if(item && item.brand!=='Bill to company' && (this.bookingService.cardAllowed[index] && Object.keys(this.bookingService.cardAllowed[index]).length !== 0) ){
      const cardName = (item.brand.replace(' ', '')).toLowerCase();
             const withoutSpaceCardAllowed = {};
             for(const key in this.bookingService.cardAllowed[index]) {
               if (this.bookingService.cardAllowed[index].hasOwnProperty(key)) {
                 withoutSpaceCardAllowed[key] = (this.bookingService.cardAllowed[index][key].replace(' ', '')).toLowerCase();
               }
             }
      //        this.bookingService.cardAllowed = withoutSpaceCardAllowed;
             if (!Object.values(withoutSpaceCardAllowed).includes(cardName)) {
               let  str1 =  Object.values(this.bookingService.cardAllowed[index])[0];
               for(let i=1;i< Object.keys(this.bookingService.cardAllowed[index]).length;i++) {
                 if (i === Object.keys(this.bookingService.cardAllowed[index]).length - 1) {
                   str1 = str1 + ' or ' + Object.values(this.bookingService.cardAllowed[index])[i] + '.';
                 } else {
                   str1 = str1 + ', ' + Object.values(this.bookingService.cardAllowed[index])[i];
                 }
               }
               this.toastr.error(this.translateService.instant('paymentDetails.ThiscardisnotacceptedbytherentalcompanyPleaseuse')+ ' ' + str1,
                 this.translateService.instant('paymentDetails.Attention').toString());
               return false;
             }else{
               return true;
             }
         }else{
           return true;
         }
  }
  updatePaymentMethodForDepartment(item, index, modal,itemindex?,type?) {
    if(type==='hotel'){
      let cardallowed = this.iscardallowedforhotelbooking(item,itemindex);
      if(!cardallowed){
        return
      }
    }
    this.tripDetails[index].paymenttype = item;
    this.tripDetails[index].error = false;
    if (this.ngxSmartModalService.getModal(modal)) {
      this.ngxSmartModalService.getModal(modal).close();
    }
    if (this.searchService1.paymentPage) {
      this.goBackEmitterForpaymentChange.emit(true);
    }
  }
  getCardName(id, index) {
    if (this.tripDetails && this.tripDetails[index] && Object.keys((this.tripDetails[index].paymenttype)).length > 0) {
      let item1 = this.tripDetails[index].paymenttype;
      if (item1.id === 'Bill to company') {
        return 'Bill to company';
      }
      let findIndex = this.cardlist.findIndex(item => item.id === item1.id);
      if (findIndex > -1) {
        return this.cardlist[findIndex].name;
      }
    } else {
      return this.translateService.instant('setting.SelectCard')
    }
  }
  openNgxModal(id) {
    if (this.userAccountInfoService.deletingCard || this.bookingService.bookRequestProgress) {
      return;
    }
    if (true) {
      setTimeout(() => {
        this.ngxSmartModalService.getModal(id).open()
      }, 100);

    } else {
      return;
    }
  }
  getBrandName(id, index) {
    if (this.tripDetails && this.tripDetails[index] && Object.keys((this.tripDetails[index].paymenttype)).length > 0) {
      let item1 = this.tripDetails[index].paymenttype;
      if (item1.id === 'Bill to company') {
        return '';
      }
      let findIndex = this.cardlist.findIndex(item => item.id === item1.id);
      if (findIndex > -1) {
        return this.cardlist[findIndex].brand;
      }
    } else {
      return '';
    }
  }
  getLastFour(id, index) {
    if (this.tripDetails && this.tripDetails[index] && Object.keys((this.tripDetails[index].paymenttype)).length > 0) {
      let item1 = this.tripDetails[index].paymenttype;
      if (item1.id === 'Bill to company') {
        return 'Bill to company';
      }
      let findIndex = this.cardlist.findIndex(item => item.id === item1.id);
      if (findIndex > -1) {
        return this.cardlist[findIndex].last4;
      }
    } else {
      return this.translateService.instant('setting.SelectCard')
    }
  }
  unsetCardMode() {
    this.addCardMode = false;
  }
  private cardTokenData: any;
  cardlist = [];
  public getCardList() {
    if (this.isLoggedIn() && this.userAccountInfoObj.cardList) {
      let paymentrequired;
             
              if(this.cardlist && this.cardlist.length > 0 && this.cardlist[0].id==='Bill to company'){
                paymentrequired = this.cardlist[0];
              }
      this.cardlist = JSON.parse(JSON.stringify(this.userAccountInfoObj.cardList.card_list));
      if(paymentrequired){
        this.cardlist.unshift(paymentrequired);
      }
      if (this.deletingcard) {
        if (!this.searchService1.multiTripBooking) {
        //  this.getSelectedItem('carddetailschanged');
        } else {
        //  this.getSelectedItem('carddetailschanged')
        }
        // this.rowSelected('true',-1);
        this.deletingcard = false;


      }
      if (this.cardAdded) {
        if(this.cardlist && this.cardlist.length > 1 && this.cardlist[0].id==='Bill to company'){
          if(this.tripDetails[this.selectedOptionIndex] && this.tripDetails[this.selectedOptionIndex].type==='hotel'){
            let cardallowed = this.iscardallowedforhotelbooking(this.cardlist[1],this.selectedOptionIndex);
                if(cardallowed){
                  this.tripDetails[this.selectedOptionIndex].paymenttype = this.cardlist[1];
                  this.tripDetails[this.selectedOptionIndex].error=false;
        }
          }else{
            this.tripDetails[this.selectedOptionIndex].paymenttype = this.cardlist[1];
            this.tripDetails[this.selectedOptionIndex].error=false;
          }
        }else{
          if(this.tripDetails[this.selectedOptionIndex] && this.tripDetails[this.selectedOptionIndex].type==='hotel'){
            let cardallowed = this.iscardallowedforhotelbooking(this.cardlist[0],this.selectedOptionIndex);
                if(cardallowed){
                  this.tripDetails[this.selectedOptionIndex].paymenttype = this.cardlist[0];
                  this.tripDetails[this.selectedOptionIndex].error=false;
                }
          }else{
            this.tripDetails[this.selectedOptionIndex].paymenttype = this.cardlist[0];
            this.tripDetails[this.selectedOptionIndex].error=false;
          }
        }
        
        this.cardAdded = false;

      }
      if (this.pageMode !== 'WebSearch') {
        this.getSelectedItem();
      }
      
    } else {
      let cardArray = new Array<CardInfo>();
      if (this.cardToken) cardArray.push(this.cardToken);
      this.cardlist = cardArray;
      if (this.deletingcard) {
        if (!this.searchService1.multiTripBooking) {
          this.getSelectedItem();
        } else {
          this.getSelectedItem()
        }
        this.deletingcard = false;

      }
      if (this.cardAdded) {
        if (!this.searchService1.multiTripBooking) {
          this.getSelectedItem();
        } else {
          this.getSelectedItem()
        }
        this.cardAdded = false;


      }
      if (this.pageMode !== 'WebSearch') {
        this.getSelectedItem();
      }
      
    }

  }
  @ViewChild(AddCardWidgetComponent) addCardChild: AddCardWidgetComponent;
  addCardFlow: boolean = false;
  changingValue: Subject<any> = new Subject();
  cardAdded = false;
  public handleBackFromAddCard(data: any) {
    if (!data) {
      if (this.bsModalRef) {
        this.bsModalRef.hide();
      }
      setTimeout(() => {
        this.unsetCardMode();
      }, 200);

      return;
    }

    this.userAccountInfoService.paymentPageSave = false;
    let tokenData = JSON.parse(data);
    if (tokenData && tokenData.type === 'newCardAdded') {

      // this.userAccountInfoService.fetchUserAccountInfo(this.emailId, this.sToken);
      let cardTokens: any = tokenData.tokens;
      if (cardTokens && cardTokens.error && cardTokens.error.length > 0) {
        // this.toastr.error(cardTokens.error, 'Card Error!');
        this.changingValue.next({ value: '', value1: false });
      }

      else if (cardTokens.token && cardTokens.gToken) {
        this.cardTokenData = cardTokens;
        GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
          'cardTokenCreated', 'WebSearchUI'
        );

        if (!this.isLoggedIn()) {
          let travelerDetails: TravelerDetails = this.bookingService.getTravelerDetails('WebSearch');
          this.userAccountInfoService.requestCardInfoFromToken(cardTokens.gToken, cardTokens.token, travelerDetails.email).subscribe(res => {
            if (res.status === 'success') {
              // this.userAccountInfoService.fetchUserAccountInfo(false);
              // this.unsetCardMode();
              this.cardAdded = true;
              let card: CardInfo = deserialize(res.card);
              this.cardToken = card;
              //  this.getCardList().push(card);
              this.selectedCardIndex = 0;
              this.addCardChild.setAddCardProgress(false);
              this.addCardChild.setErrorMessage('');
              this.unsetCardMode();
              updatePaymentDOM();
            } else if (res.status === 'CARDERROR') {
              // this.toastr.error(res.message, 'Card Error!');
              this.addCardChild.setErrorMessage(res.message);
              this.addCardChild.setAddCardProgress(false);

            } else {
              // this.toastr.error(res.message, 'Error!');
              this.addCardChild.setErrorMessage(res.message);
              this.addCardChild.setAddCardProgress(false);
            }
          });
          // this.cardToken = new CardInfo();
          // this.cardToken.name = tokenData.result.card.name;
          // this.cardToken.brand = tokenData.result.card.brand;
          // this.cardToken.address = new AddressDTO();
          // this.cardToken.address.postal_code = tokenData.result.card.address_zip;
          // this.cardToken.exp_month = tokenData.result.card.exp_month;
          // this.cardToken.exp_year = tokenData.result.card.exp_year;
          // this.cardToken.id = cardTokens.token;
          // this.cardToken.last4 = tokenData.result.card.last4;
        } else {
          this.userAccountInfoService.requestSaveCardInfo(cardTokens.token, cardTokens.gToken).subscribe(res => {
            if (res.status === 'success') {
              this.addCardFlow = true;
              this.cardAdded = true;
              this.userAccountInfoService.fetchUserAccountInfo(false);
              // this.unsetCardMode();
            } else if (res.status === 'CARDERROR') {
              // this.toastr.error(res.message, 'Card Error!');
              this.changingValue.next({ value: res.message, value1: false });

            } else {
              // this.toastr.error(res.message, 'Error!');
              this.changingValue.next({ value: res.message, value1: false });
            }
          });
        }
      } else {
        // this.addCardChild.setErrorMessage(this.translateService.instant('paymentDetails.UnknownErrorPleasetryagain').toString());
        // this.addCardChild.setAddCardProgress(false);
      }
    }

  }
  isAirlineSupportsAccountCodeWithUATPOnly(item) {
    if(item && !item.fareSupportsPersonalCard)
    // We are no more supporting United PassPlus
    return true;
  }
  isAccountCodeUATPSpecific(item) {
    if (item && item.fareWithAccountCode && this.isAirlineSupportsAccountCodeWithUATPOnly(item)) {
      return true;
    }
    return false;
  }
  isUserSelectedUATPSpecificAccountCode(index?) {
    let selectedCounter = 0;
    const flight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlightInPolicy"));
    let flight1 = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"));
    if (index && flight1[index]) {
      flight1 = flight1[index]
    }
    //  if (this.billingItemList && this.billingItemList.length > 1 || (this.billingItemList && this.billingItemList.length > 0 && this.billingItemList[0].type === 'flight')) {
    if (flight) {
      if (this.searchService1.nonCombo) {
        for (const item of this.searchService1.nonComboSelectedFlight) {
          if (this.isAccountCodeUATPSpecific(item)) {
            selectedCounter = 1;
          }
        }
      } else {
        if (this.isAccountCodeUATPSpecific(flight)) {
          selectedCounter = 1;
        }
      }
    } else if (flight1) {
      if (this.searchService1.nonCombo) {
        for (const item of this.searchService1.nonComboSelectedFlight) {
          if (this.isAccountCodeUATPSpecific(item)) {
            selectedCounter = 1;
          }
        }
      } else {
        if (this.isAccountCodeUATPSpecific(flight1[0])) {
          selectedCounter = 1;
        }
      }
    }
    // }
    if (selectedCounter > 0) {
      return false;
    } else {
      return true;
    }
  }
  hasTravelportOrSabreHotel() { 
    for (let billingItemIndex = 0; billingItemIndex < this.billingItemList.length; billingItemIndex++) {
      if (this.billingItemList[billingItemIndex].type.toLowerCase() === 'hotel'
        && this.billingItemList[billingItemIndex].source
        && (
          this.billingItemList[billingItemIndex].source.toLowerCase() === 'travelport'
          || this.billingItemList[billingItemIndex].source.toLowerCase() === 'sabre'
        )
      ) {
        return true;
      }
    }
    return false;
  }
  showBillToCompanyForHotel(source) {
    if (this.userAccountInfoService.confermaStatus) {
      return true;
    } else {
      if (!this.userAccountInfoService.confermaStatus && (source === 'TravelPort' || source === 'Sabre')) {
        return false;
      }
    }
    return true;
  }
  isCarPriceline() {

    let selectedCar: any = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
    if (selectedCar && selectedCar.length>0) {
      if (!this.userAccountInfoService.hasBillingIdConfigured(selectedCar[0].traflaPartnerCode)) {
        return true;
      };
    }

  }
  getUserAllowed(type?): boolean {
    if (this.billingItemList && this.billingItemList.length > 0 && this.billingItemList[0].type === 'cars') {
      let selectedCar: any = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
      if (selectedCar && selectedCar.length > 0) {
        if (!this.userAccountInfoService.hasBillingIdConfigured(selectedCar[0].traflaPartnerCode)) {
          return true;
        };
      }
    } else {
      if (this.userAccountInfoService.confermaStatus) {
        if (this.isLoggedIn() && this.userAccountInfoObj.userInfo.employeeInfo
          && this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse != null) {
          return this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse;
        }
      } else {
        if (this.billingItemList && this.billingItemList.length > 0 && this.billingItemList[0].type === 'hotel' && this.hasTravelportOrSabreHotel()) {
          return true;
        }
      }
    }
    if (this.isLoggedIn() && this.userAccountInfoObj.userInfo.employeeInfo
      && this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse != null) {
      return this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse;
    } else {
      return true;
    }
  }
  deletingcard = false;
  public confirmCardDeleteModel(cardIndex: number,eventindex) {
    this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
      initialState: {
        title: this.translateService.instant('paymentDetails.Deletingthecardquestion'),
        // message: This payment method will not be displayed in your list of payment options',
        message: this.translateService.instant('paymentDetails.deleteCardWaringMsg'),
        yesButtonSubText: this.translateService.instant('paymentDetails.Delete'),
        deleteCard: true
      }, backdrop: true, ignoreBackdropClick: true
    });
    this.bsModalRef.content.onClose.subscribe(result => {

      if (result) {
        this.userAccountInfoService.paymentPageSave = false;
        this.deletingcard = true;
        this.selectedOptionIndex=eventindex;
        this.markCardDeleted(cardIndex);
      }
    });
  }
  public markCardDeleted(cardIndex: number) {
    let cardIndex1 = -1
    if (this.userAccountInfoObj && this.userAccountInfoObj.cardList) {
      this.userAccountInfoService.requestDeleteCard(this.cardlist[cardIndex].id).subscribe(res => {
        if (res.status === 'success') {
          if(this.tripDetails[this.selectedOptionIndex].paymenttype && this.tripDetails[this.selectedOptionIndex].paymenttype.id  === this.cardlist[cardIndex].id){
            this.tripDetails[this.selectedOptionIndex].paymenttype={};
          }
          this.userAccountInfoService.fetchUserAccountInfo(false);
        } else {
          this.toastr.error(res.message, 'Attention!');
        }
      });
    }
  }

  gettravelrdetail() {
    let userid = this.userAccountInfoService.getUserEmail();
    if (this.searchService1.employeeEmail.length > 0) {
      for (let i = 0; i < this.searchService1.employeeEmail.length; i++) {
        if (this.searchService1.employeeEmail[i].email === userid) {
          return this.userAccountInfoObj.userInfo;
        } else if (this.searchService1.employeeEmail[i].email === "GUEST") {
          return null;
        } else {
          let employee;
          employee = this.searchService1.employeeList.filter(item => item.email === this.searchService1.employeeEmail[i].email);

          if (employee && employee[0] && employee[0].employeeInfo) {
            return employee[0];
          }
        }

      }
    }
  }

  clnNo = [];
  hlnNo = [];
  ffnNo = [];
  tripDetails = new Array<TripDetails>();
  private processEmailQuoteOptions(processedOptions: any) {
    try {
      this.threadId = this.activatedRoute.snapshot.queryParams['threadId'];
      this.revalidationPriceChanged = false;
      this.eventList = new Array;
      let eventIds = [];
      let eventCounter = 0;
      let userinfo;
      if (this.pageMode === 'WebSearch') {
        userinfo = this.gettravelrdetail();
      } else {
        if (this.userAccountInfoObj && this.userAccountInfoObj.userInfo) {
          this.tripDetails = [];
          userinfo = this.userAccountInfoObj.userInfo;
        }
      }

      let hotelEventCount = 0;
      let carEventCount = 0;
      let flightEventCount = 0;
      for (let event of processedOptions) {
        let eventViewDTO = new EventViewDTO(event.eventId, event.eventType, event.locationAddress);
        if (!event.eventOptions) continue;
        if (event.eventType == 'flight') {
          flightEventCount++;
          let eventOptions = new Array<MultiFlightViewDTO>();
          // 
          this.eventCounter = this.eventCounter + 1;
          this.currencyCode = event.eventOptions[0][0].currency;
          if (event.eventOptions.length == 1) {
            this.singleOption = true;
          } else {
            this.singleOption = false;
            this.singleOption1 = false;
          }
          for (let option of event.eventOptions) {
            let flightLegs = new Array<FlightViewDTO>();
            for (let leg of option) {
              flightLegs.push(new FlightViewDTO(leg.flight_hops, leg.flight_duration, leg.destinationText));
            }
            if (option[0].travelCreditsInfo) {
              if (option[0].displayPrice) {
                this.currencyCode = option[0].displayCurrency;
                eventOptions.push(new MultiFlightViewDTO(flightLegs, option[0].displayPrice, option[0].discountedPrice
                  , option[0].withinPolicy, option[0].serviceFee, option[0].travelCreditsInfo))
              } else {
                eventOptions.push(new MultiFlightViewDTO(flightLegs, option[0].price, option[0].discountedPrice
                  , option[0].withinPolicy, option[0].serviceFee, option[0].travelCreditsInfo))
              }
            } else {
              if (option[0].displayPrice) {
                this.currencyCode = option[0].displayCurrency;
                eventOptions.push(new MultiFlightViewDTO(flightLegs, option[0].displayPrice, option[0].discountedPrice
                  , option[0].withinPolicy, option[0].serviceFee, {}))
              } else {
                eventOptions.push(new MultiFlightViewDTO(flightLegs, option[0].price, option[0].discountedPrice
                  , option[0].withinPolicy, option[0].serviceFee, {}))
              }

            }
            if (option[0].travelCreditsInfo && (Object.keys((option[0].travelCreditsInfo)).length === 0)) {
              if (this.searchService1.outsidePolicyselected && this.pageMode == 'WebSearch') {
                if (event.eventId === '0') {
                  this.revalidateFlight(this.eventList.length - (hotelEventCount + carEventCount),
                    event.eventId, "" + (eventOptions.length - 1), option[0].optionId,
                    this.threadId, eventOptions[eventOptions.length - 1], (flightEventCount - 1));
                }
              } else {
                this.revalidateFlight(this.eventList.length - (hotelEventCount + carEventCount),
                  event.eventId, "" + (eventOptions.length - 1), option[0].optionId,
                  this.threadId, eventOptions[eventOptions.length - 1], (flightEventCount - 1));
              }
            }
            let details = { ffnno: [], seatinfo: [], paymenttype: {}, lugagge: "", type: 'flight', error: false, clnno: [], hlnno: [], prepay: false, paymentError: false,selectedPartnerCode:'' }
            this.ffnNo.push('');
            details.ffnno = this.getAirLoyalitynumber(userinfo, option);
            details.selectedPartnerCode = details.ffnno[0].airlineCode;
            if (this.tripDetails.length === 0) {
              this.tripDetails.push(details);
            } else if (this.getDuplicateFFnNo(details)) {
              this.tripDetails.push(details);
            } else {
              details.ffnno = [];
              this.tripDetails.push(details);
            }
          }
          eventViewDTO.setOptions(eventOptions);
        } else if (event.eventType == 'hotel') {
          hotelEventCount++;
          // 
          this.currencyCode = event.eventOptions[0].currency;
          eventViewDTO.setHotelOptions(event.eventOptions);
          let optIdx = 0;
          for (let option of event.eventOptions) {
            if (this.pageMode == 'WebSearch' && option.rateDetail && option.rateDetail.resortFee) {
              if (option.displayPrice) {
                this.resortFee = this.resortFee + Number.parseFloat(option.displayResortFee)
              }
              else {
                this.resortFee = this.resortFee + Number.parseFloat(option.rateDetail.resortFee.substring(3))
              }
            } else if (option.rateDetail && option.rateDetail.resortFee) {
              if (option.rateDetail.displayBase) {
                this.resortFee = this.resortFee + Number.parseFloat(option.rateDetail.displayResortFee)
              }
            }
            this.revalidatedOptionsMap['event-' + this.eventList.length + '-oIdx' + optIdx] = 'revalidated';
            optIdx++;

            let details = { ffnno: [], seatinfo: [], paymenttype: {}, lugagge: "", type: 'hotel', error: false, clnno: [], hlnno: [], prepay: true, paymentError: false ,selectedPartnerCode:''}
            this.hlnNo.push('');
            details.hlnno = this.getLoyalitynumber(userinfo, option);
            details.selectedPartnerCode = details.hlnno[0].hotelCode;
            if (this.tripDetails.length === 0) {
              this.tripDetails.push(details);
            } else if (this.getDuplicateHlnnNo(details)) {
              this.tripDetails.push(details);
            } else {
              details.hlnno = [];
              this.tripDetails.push(details);
            }
          }


        } else if (event.eventType == 'cars') {
          carEventCount++;
          let eventOptions = new Array<MultiCarViewDTO>();
          // 
          this.currencyCode = event.eventOptions[0].currency;
          let index = 0;
          this.optionList = event.eventOptions;
          if (event.eventOptions && event.eventOptions.length > 1) {
            if (this.preSelectedEventOptions) {
              index = this.preSelectedEventOptions;
            }
          }
          for (let option of event.eventOptions) {

            eventOptions.push(new MultiCarViewDTO(option, option.price, option.discountedPrice
              , option.policy, option.serviceFee))
            this.revalidateCar(this.eventList.length - (hotelEventCount + flightEventCount),
              event.eventId, "" + (event.eventOptions.length - 1), option.optionId,
              this.threadId, eventOptions[eventOptions.length - 1], option);
            this.clnNo.push('');
            let details = { ffnno: [], seatinfo: [], paymenttype: {}, lugagge: "", type: 'car', error: false, clnno: [], hlnno: [], prepay: true, paymentError: false,selectedPartnerCode:'' }
            details.clnno = this.getCarLoyalityNumbers(userinfo, option);
            details.selectedPartnerCode = details.clnno[0].rentalCarCode;
            if (this.tripDetails.length === 0) {
              this.tripDetails.push(details);
            } else if (this.getDuplicateClnNo(details)) {
              this.tripDetails.push(details);
            } else {
              details.clnno = [];
              this.tripDetails.push(details);
            }
          }


          eventViewDTO.setCarOptions(eventOptions);
        }
        this.eventList.push(eventViewDTO);
        eventIds.push(event.eventId);
        // 

      }
      
      this.connectionListener = this.connectionService.monitor().subscribe(isConnected => {
        if (!isConnected) {
          this.isSelectedRevalidating = false;
          for (let eventViewDTO of this.eventList) {
            if (eventViewDTO.getOptions()) {
              for (let flightOptions of eventViewDTO.getOptions()) {
                flightOptions.revalidating = false;
              }
            }
          }
        }
      });
      // 

      this.gallopLocalStorage.setItem("eventIds", JSON.stringify(eventIds));
    } catch (error) {
      this.router.navigate(['/errors'], { queryParams: { errorCode: '403' } });
    }
  }
  getDuplicateFFnNo(details) {
    if (this.tripDetails.length > 0) {
      let found = true;
      for (let item of this.tripDetails) {
        if ((item.ffnno.length > 0) && (item.ffnno[0].airlineCode === details.ffnno[0].airlineCode)) {
          found = false;
        }
      }
      return found;
    } else {
      return true;
    }

  }
  getDuplicateHlnnNo(details) {
    if (this.tripDetails.length > 0) {
      let found = true;
      for (let item of this.tripDetails) {
        if ((item.hlnno.length > 0) && (item.hlnno[0].hotelCode === details.hlnno[0].hotelCode)) {
          found = false;

        }
      }
      return found;
    } else {
      return true;
    }

  }
  getDuplicateClnNo(details) {
    if (this.tripDetails.length > 0) {
      let found = true;
      for (let item of this.tripDetails) {
        if ((item.clnno.length > 0) && (item.clnno[0].rentalCarCode === details.clnno[0].rentalCarCode)) {
          found = false;
        }
      }
      return found
    } else {
      return true;
    }

  }
  getError(index) {
    if (this.tripDetails.length > 0 && this.tripDetails[index]) {
      return this.tripDetails[index].error;
    }

  }
  getAirLoyalitynumber(userAccountInfoObj, Airlines) {
    let hlnData1 = []
    for (let airlineObj of Airlines) {
      let ffnNumber = undefined;
      if (userAccountInfoObj && userAccountInfoObj.ffnMapping ) {
        
        let filteredObj: any = userAccountInfoObj.ffnMapping.find(x => x.airline_code === airlineObj['airline']);
        if (filteredObj) {
          ffnNumber = filteredObj.frequent_flyer_number ? filteredObj.frequent_flyer_number.trim() : null;
        }
      }
      if(hlnData1.length > 0 && hlnData1.find((test) => test.airlineCode === airlineObj['airline']) === undefined){
      let ffnData = {
        "frequent_flyer_number": ffnNumber ? ffnNumber : null,
        "airlineCode": airlineObj['airline'],
        "name": airlineObj['airlineName'],
        "ffnAvailable": true
      }
      hlnData1.push(ffnData);
    }else if(hlnData1.length === 0){
      let ffnData = {
        "frequent_flyer_number": ffnNumber ? ffnNumber : null,
        "airlineCode": airlineObj['airline'],
        "name": airlineObj['airlineName'],
        "ffnAvailable": true
      }
      hlnData1.push(ffnData);
    }
    }
    return hlnData1;
  }
  getLoyalitynumber(userAccountInfoObj, hotelObj) {
    let hlnData1 = []
    let hlnNumber = "";
    if (userAccountInfoObj && userAccountInfoObj && userAccountInfoObj.loyalityCards) {
      let filteredObj: any = userAccountInfoObj.loyalityCards.find(x => x.chain === getTraflaHotelChainCode(hotelObj['traflaHotelChain']));
      if (filteredObj && filteredObj.number) {
        hlnNumber = filteredObj.number.trim();
        
      } else {
        filteredObj = userAccountInfoObj.loyalityCards.find(x => x.hotel_name === hotelObj['hotelName']);
        if (filteredObj && filteredObj.number) {
          hlnNumber = filteredObj.number ? filteredObj.number.trim() : null;
        }
      }
    }
    let hlnData = {
      "hotel_loyality_number": hlnNumber ? hlnNumber : null,
      "hotelCode": hotelObj['traflaHotelChain'] ?  hotelObj['traflaHotelChain']:hotelObj['hotelChain'],
      "name": hotelObj['hotelName'],
      "hlnAvailable": true,
      'handler': hotelObj['source'],
      'loyaltyPointsSupported': hotelObj['loyaltyPointsSupported']

    }

    hlnData1.push(hlnData);
    
    return hlnData1;
  }

  getCarLoyalityNumbers(userAccountInfoObj, carObj) {
    let clnData1 = [];
    let clnNumber = "";
    if (userAccountInfoObj && userAccountInfoObj.carLoyaltyNumbers) {
      let filteredObj: any = userAccountInfoObj.carLoyaltyNumbers.find(x => x.traflaPartnerCode === carObj['traflaPartnerCode']);
      if (filteredObj && filteredObj.rentalCarLoyaltyNumber) {
        clnNumber = filteredObj.rentalCarLoyaltyNumber.trim();
      } else {
        filteredObj = userAccountInfoObj.carLoyaltyNumbers.find(x => x.rentalCarName === carObj['partnerName']);
        if (filteredObj && filteredObj.rentalCarLoyaltyNumber) {
          clnNumber = filteredObj.rentalCarLoyaltyNumber ? filteredObj.rentalCarLoyaltyNumber.trim() : null;
        }
      }
    }
    let clnData = {
      "rentalCarLoyaltyNumber": clnNumber ? clnNumber : null,
      "rentalCarCode": carObj['traflaPartnerCode'],
      "rentalCarName": carObj['partnerName'],
      "clnAvailable": true,
    }
    clnData1.push(clnData);

    return clnData1;
  }
  getCurrentFlightFareAttributesForMoreOptions(item): FareAttributes {
    return item.fareAttributes;
  }
  isMixed(item): boolean {
    if(item.flightHighlights){
    return item.flightHighlights.mixedClass;
    }
  }
  showWithinPolicy() {
    return this.tripSelectedToshowOnpopUp.hops[0].withinPolicy;
  }
  getBaggageDetailsColor(item) {
    let baggageDetails: BaggageDetails = item.legs[0].baggageAllowance;
    if (this.searchService1.brandedFareCheckBox && item.legs[0].brandDetails && item.legs[0].brandDetails.length > 0) {
      baggageDetails = item.legs[0].brandDetails[0].baggageAllowance;
    }
    if (baggageDetails) {
      if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return '#27c198 !important';
      }
      if ((baggageDetails.maxWeightAllowed && baggageDetails.maxWeightAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.maxWeightAllowed) === 0) || (!baggageDetails.maxWeightAllowed)) {
        return '#f93d30 !important';
      } else {
        return '#27c198 !important';
      }
    }
    return null;
  }
  isAmenityAvailable(type: string) {
    if (this.tripSelectedToshowOnpopUp && this.tripSelectedToshowOnpopUp.amenities) {
      return this.tripSelectedToshowOnpopUp.amenities[type];
    }
    return false;
  }
  getPerDayPrice(car) {
    if (car.displayPrice) {
      var subTotal = (car.displayPrice / car.numberOfDay);
      return subTotal;
    } else {
      var subTotal = (car.price / car.numberOfDay);
      return subTotal;
    }
  }
  getLegRoomFareAttribute() {
    return this.getCurrentFlightFareAttributes().legRoom;
  }
  getLegroomFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).legRoom;
  }
  getBaggageDetails(item) {
    let baggageDetails: BaggageDetails = this.tripSelectedToshowOnpopUp.hops[0].baggageAllowance;
    if (this.searchService1.brandedFareCheckBox && this.tripSelectedToshowOnpopUp.hops[0].brandDetails && this.tripSelectedToshowOnpopUp.hops[0].brandDetails.length > 0) {
      baggageDetails = this.tripSelectedToshowOnpopUp.hops[0].brandDetails[0].baggageAllowance;
    }
    if (baggageDetails) {
      if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
        return (Number.parseInt(baggageDetails.numberOfPieceAllowed) === 1) ? this.translateService.instant('flightChart.1checkedbagperadult')
          : baggageDetails.numberOfPieceAllowed + ' ' + this.translateService.instant('flightChart.checkedbagsperadult');
      }
      if ((baggageDetails.maxWeightAllowed && baggageDetails.maxWeightAllowed.trim().length > 0
        && Number.parseInt(baggageDetails.maxWeightAllowed) === 0) || (!baggageDetails.maxWeightAllowed)) {
        return this.translateService.instant('flightChart.Nocheckedbags');
      } else {
        return baggageDetails.maxWeightAllowed + ' ' + baggageDetails.weightUnit.toLowerCase() + this.translateService.instant('flightChart.peradult');
      }
    }
    return null;
  }
  getCorporateFareAttributeMoreOptions(item) {
    if (item.corporateRate) {
      return item.corporateRate;
    }
  }
  isBaggageAvailable(item) {
    let baggageDetails: BaggageDetails = this.tripSelectedToshowOnpopUp.hops[0].baggageAllowance;
    if (baggageDetails.numberOfPieceAllowed && baggageDetails.numberOfPieceAllowed.trim().length > 0
      && Number.parseInt(baggageDetails.numberOfPieceAllowed) > 0) {
      return true;
    } else {
      return false;
    }
  }
  isWithinPolicy(item?) {
    if (this.tripSelectedToshowOnpopUp.hops[0].withinPolicy) {
      return this.tripSelectedToshowOnpopUp.hops[0].withinPolicy;
    }
  }
  getWifiFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).wifi;
  }
  getCheckedBagFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).checkedBag;
  }
  getCarryOnBagFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).carryBag;
  }
  getRefundAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).refund;
  }
  getChangesFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).rebooking;
  }
  getSeatFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).seat;
  }
  getCurrentFlightFareAttributes(): FareAttributes {

    return this.tripSelectedToshowOnpopUp.hops[0].fareAttributes;
  }
  getWifiAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).wifi;
  }
  getMealFareAttributeMoreOptions(item) {
    return this.getCurrentFlightFareAttributesForMoreOptions(item).meal;
  }
  getWifiFareAttribute() {
    return this.getCurrentFlightFareAttributes().wifi;
  }
  getCheckedBagFareAttribute() {
    return this.getCurrentFlightFareAttributes().checkedBag;
  }
  getCarryOnBagFareAttribute() {
    return this.getCurrentFlightFareAttributes().carryBag;
  }
  getRefundAttribute() {
    return this.getCurrentFlightFareAttributes().refund;
  }
  getChangesFareAttribute() {
    return this.getCurrentFlightFareAttributes().rebooking;
  }
  getSeatFareAttribute() {
    return this.getCurrentFlightFareAttributes().seat;
  }
  getWifiAttribute() {
    return this.getCurrentFlightFareAttributes().wifi;
  }
  getMealFareAttribute() {
    return this.getCurrentFlightFareAttributes().meal;
  }
  isOptionSelected(eventIdx: number, optionIdx: number) {
    var selectedOptions = getSelectedFlightIndex();
    return selectedOptions['event-' + eventIdx] && selectedOptions['event-' + eventIdx] == optionIdx;
  }

  isEventSelected(eventIdx: number): boolean {
    var selectedOptions = getSelectedFlightIndex();
    return selectedOptions['event-' + eventIdx]
  }
  isSelectionCompleted() {
    return selectionCompleted();
  }
  carUrl(car) {
    if (car.images) {
      return car.images.S;
    }
  }
  getAbsoluteNumber(input: number) {
    if (input < 0) {
      return input * -1;
    }
    return input;
  }
  getAllLayoverTimeHour(layover) {
    let time = 0;
    let hops = layover.getHops();
    for (let i = 1; i < hops.length; i++) {
      const seconds = +parseInt(hops[i].duration.split(' ')[2]);
      const minutes = +parseInt(hops[i].duration.split(' ')[0]);
      const secondsOfMinutes = minutes * 60;
      time += seconds + secondsOfMinutes;


    }
    let totalTime = 0;
    totalTime = time;
    if (totalTime > 0) {
      return Math.floor(totalTime / 60) + ' hr ' + " " + (totalTime % 60 ? totalTime % 60 : '00') + " min "

    } else {
      return null;
    }
  }
  isCreditValueapplied(credit) {
    if ((credit && (Object.keys((credit)).length !== 0))) {
      return true;
    } else {
      return false;
    }
  }
  getstopsNumber(item) {
    return parseInt(item);
  }
  getCabinClass(flightleg) {
    let hops = flightleg.getHops();
    if (hops && hops.length > 0) {
      return hops[0].fareClassName;
    }

    return flightleg.getClassName();
  }
  getNumberCarriers(item) {
    let carrierArray = [];
    for (let hop of item.getHops()) {
      if (carrierArray.indexOf(hop.carrier) === -1) {
        carrierArray.push(hop.carrier)
      }


    }
    return (carrierArray.length - 1);
  }
  getCabinClass1(flightleg) {
    let hops = flightleg.hops;
    if (hops && hops.length > 0) {
      return hops[0].fareClassName;
    }
    return flightleg.hops[0].fareClassName;
  }
  getFlightIconURL(flightCode: string) {
    return CommonUtils.getAirlineImageUrl(flightCode);
  }
  getCabinClassForSelected(carrier, cabinClass1) {
    let cabinClass = CommonUtils.classNameReturnFromMap(this.cabinClasses, carrier, cabinClass1);
    if (cabinClass) {
      return cabinClass;
    }
  }
  getHotleOptionStyle() {
    return { 'width': 'auto', 'max-width': 'none' }
  }
  getCarPrice(car) {
    if (car.displayPrice) {
      return car.displayPrice
    } else {
      return car.price
    }
  }
  getHotelPrice(hotel) {
    if (hotel.displayPrice) {
      return hotel.displayPrice
    } else {
      return hotel.price
    }
  }
  calculateTravelCredits(price, price1) {
    let carrier = this.searchService1.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier;
    for (let i = 1; i < this.searchService1.nonComboSelectedFlight.length; i++) {
      if (carrier === this.searchService1.nonComboSelectedFlight[i].legs[0].flightHops[0].carrier) {
        if (this.searchService1.nonComboSelectedFlight[0].displayPrice) {
          price += this.searchService1.nonComboSelectedFlight[i].displayPrice;
        } else {
          price += this.searchService1.nonComboSelectedFlight[i].price;
        }
      } else {
        if (this.searchService1.nonComboSelectedFlight[0].displayPrice) {
          price1 += this.searchService1.nonComboSelectedFlight[i].displayPrice;
        } else {
          price1 += this.searchService1.nonComboSelectedFlight[i].price;
        }
      }

    }
    return [price, price1];

  }

  getCreditPrice(item) {
    if (item && item.displayCreditAmount) {
      return item.displayCreditAmount;
    } else {
      return item.creditAmount;
    }
  }


  routeToflight() {
    if (this.bookingService.bookRequestProgress) {
      return;
    }
    //this.titleService.setTitle(this.translateService.instant('search.FlightSearch'));
    // this.searchForm.controls['trip'].setValue('ONEWAY');
    // this.setInitTripType(this.initResponse,false)
    let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
    if (selectedFlight && selectedFlight.length) {
      for (let item of selectedFlight) {
        if (item.travelCreditsInfo && item.travelCreditsInfo.length > 0) {
          this.searchService1.previousSelectedCreditAirline.push(item.legs[0].flightHops[0].carrier)
        }
      }
    }
    this.searchService1.tripType = 'Flight';
    this.searchService1.multiTripBooking = true;
    this.searchService1.exitFromMultiTrip =false;
    this.router.navigate([this.userAccountInfoService.getDefaultRoutePath()],
    {
      queryParams:
      {
      },
      replaceUrl: false
    }
  );
  }
  routeTohotel() {
    if (this.bookingService.bookRequestProgress) {
      return;
    }
    this.searchService1.tripType = 'Hotel';
    this.searchService1.multiTripBooking = true;
    this.searchService1.exitFromMultiTrip =false;
    this.router.navigate([this.userAccountInfoService.getDefaultRoutePath()],
      {
        queryParams:
        {
          type: 'hotel',
        },
        replaceUrl: false
      }
    );
  }
  routeTocar() {
    if (this.bookingService.bookRequestProgress) {
      return;
    }
    this.searchService1.tripType = 'Car';
    this.searchService1.multiTripBooking = true;
    this.searchService1.exitFromMultiTrip =false;
    this.router.navigate([this.userAccountInfoService.getDefaultRoutePath()],
      {
        queryParams:
        {
          type: 'car',
        },
        replaceUrl: false
      }
    );
  }
  openPaymentdetailsModal(modal, index, option?) {
    if (this.bookingService.bookRequestProgress) {
      return;
    }
    this.bookingService.selectedPaymentEventIndex = index;
    this.goBackEmitterForModal.emit({id:'payment',type:null});



  }
  openPolicydetailsModal(modal, index, option?,type1?) {
    if (this.bookingService.bookRequestProgress) {
      return;
    }
    if(type1==='hotel'){
    var id = index; 
    var res = id.substr(id.length - 1);
    this.bookingService.selectedPaymentEventIndex =parseInt(res);
    }else{
    this.bookingService.selectedPaymentEventIndex =parseInt(index);
    }
    if(type1==='flight' && !this.searchService1.multiTripBooking){
      this.bookingService.selectedPaymentEventIndex  =  0;  
    }
    if(type1==='cars'){
      this.bookingService.selectedPaymentEventIndex  =  this.bookingService.selectedPaymentEventIndex -1; 
    }
    this.goBackEmitterForModal.emit({id:"policy",type:type1});



  }
  getCreditValue(price, credit, index, index1) {
    if ((credit && (Object.keys((credit)).length !== 0))) {
      let totalMonney = 0;
      let totalMoney1 = 0;
      if (this.searchService1.nonCombo) {
        let price1;
        if (this.searchService1.nonComboSelectedFlight[0].displayPrice) {
          price1 = this.searchService1.nonComboSelectedFlight[0].displayPrice;
        } else {
          price1 = this.searchService1.nonComboSelectedFlight[0].price;
        }
        [price1,totalMoney1] = this.calculateTravelCredits(price1,totalMoney1);
        totalMonney = CommonUtils.getPriceAfterTravelCredits(price1, credit,this.searchService1.adultCount);
      } else {
         totalMonney = CommonUtils.getPriceAfterTravelCredits(price, credit,this.searchService1.adultCount);
      }
      if (totalMonney !== price) {
        this.creditExist[index][index1] = true;
      } else {
        this.creditExist[index][index1] = false;
      }
      if (this.searchService1.nonCombo) {
        return (totalMonney + totalMoney1)
      } else {
        return totalMonney;
      }
    } else {
      this.creditExist[index][index1] = false;
      return price;
    }
  }
  isSelected(event, index1, index2) {
    if (this.searchService1.paymentPage) {
      var isSelect = this.isOptionSelected(index1, index2);
      return (isSelect)
    } else {
      return true;
    }
  }
  showBillTocompany = true;
  seatButtonStyle() {
    if (this.currScreenWidth >= 1024) {
      return { 'margin-left': '20px' };
    } else
      return { 'margin-left': '0px' };

  }
  getStyleEqualToFFN() {
    if (this.currScreenWidth > 1024) {
      return { 'position': 'absolute', 'bottom': '-3px', 'width': '80%' }
    } else if (this.currScreenWidth > 767) {
      return { 'position': 'absolute', 'bottom': '18px', 'width': '90%' }
    } else {

      return { 'position': 'relative', 'bottom': '0', 'width': 'auto' }

    }
  }
  isBillToCompanyNeedToShow(car) {
    if (this.pageMode === 'emailflowAgent') {
      return true;
    }


    if (car) {
      this.showBillTocompany = (this.userAccountInfoService.hasBillingIdConfigured(car.traflaPartnerCode) &&
        this.userAccountInfoService.canAccessCompanyCard());
      return this.showBillTocompany;
    } else if (car) {
      this.showBillTocompany = (this.userAccountInfoService.hasBillingIdConfigured(car.traflaPartnerCode) &&
        this.userAccountInfoService.canAccessCompanyCard());
      return this.showBillTocompany;
    }

  }
  public showBillToCompany(type, index?) {
    // if (this.pageMode === 'emailflowAgent') {
    //   return true;
    // }
   // 
    if (type === 'cars') {
    //  
      let selectedCar: any = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
      if ((selectedCar && selectedCar.length > 0 && index) || (selectedCar && selectedCar.length > 0 && index===0)) {
        this.showBillTocompany = (this.userAccountInfoService.hasBillingIdConfigured(selectedCar[index].traflaPartnerCode) &&
          this.userAccountInfoService.canAccessCompanyCard());
        return this.showBillTocompany;
      } else if (this.pageMode !=='WebSearch' && this.emailQuotOptions && this.emailQuotOptions.carOptionsList.length > 0) {
        this.showBillTocompany = (this.userAccountInfoService.hasBillingIdConfigured(this.emailQuotOptions.carOptionsList[0][0].traflaPartnerCode) &&
          this.userAccountInfoService.canAccessCompanyCard());
        return this.showBillTocompany;
      }
    }
    return this.userAccountInfoService.canAccessCompanyCard();
  }
  getCarPrepay(car) {
    //  
    if (car && car.flights && !car.flights.postPay) {
      return true;
    } else {
      return false;
    }
  }
  inputTripNameRequired = false;
  saveTripName() {
    if (this.searchService1.tripName && this.searchService1.tripName !== '') {
      this.searchService1.editMytripname = 'modelshow';
      this.searchService1.tripName = this.tripName;
    } else {
      this.inputTripNameRequired = true;
    }
  }
  tripName='';
  cancelTripName() {
    this.tripName = '';
    this.searchService1.editMytripname = 'false';
  }
  getInputTripName(event) {
    if (this.searchService1.tripName && this.searchService1.tripName !== '') {
      this.inputTripNameRequired = false;

    }
  }
  onEditEventName(type, options) {
    if (this.bookingService.bookRequestProgress) {
      return;
    }
    let datePipe = new DatePipe('en-US');
    this.searchService1.editMytripname = 'true';
    this.tripName = this.searchService1.tripName;
    //this.searchService1.tripName = '';
    if (type === 'flight' && this.searchService1.tripName === '') {
      if (options.flights.length == 1) {
        this.searchService1.tripName = 'Trip to' + ' ' + options.flights[0].destinationCity ;
      } else {
        this.searchService1.tripName = 'Trip to' + ' ' + options.flights[0].destinationCity;
      }
      //return options.flights[0].hops[0].flightDate;
    } else if (type === 'hotel' && this.searchService1.tripName === '') {
      this.searchService1.tripName = 'Trip to' + ' ' + options.title ;
      // return options.checkIn;
    }
    else if (type === 'car' && this.searchService1.tripName === '') {
      this.searchService1.tripName = 'Trip to' + ' ' + options.title;
      // return options.pickUpDate;
    }
  }
  getEventdate(type, options,event) {
    if (this.searchService1.editMytripname === 'false') {
     // this.searchService1.tripName = '';
      let datePipe = new DatePipe('en-US');
      let  todayDate;
      
      if (type === 'flight' && this.searchService1.tripName==='') {
        if (options.flights.length == 1) {
          if(this.pageMode !=='webSearch' && this.emailQuotOptions && this.emailQuotOptions.flightLegRequests && this.emailQuotOptions.flightLegRequests.length > 0){
            todayDate = new Date(this.emailQuotOptions.flightLegRequests[0].dateTime.split('T')[0]);
            this.searchService1.tripName = 'Trip to' + ' ' + options.flights[0].destinationCity ;
            return this.searchService1.tripName;
         }
          this.searchService1.tripName = 'Trip to' + ' ' + options.flights[0].destinationCity;
          return this.searchService1.tripName;
        } else if (options.flights.length > 1) {
          if(this.pageMode !=='webSearch' && this.emailQuotOptions && this.emailQuotOptions.flightLegRequests && this.emailQuotOptions.flightLegRequests.length > 0){
            todayDate = new Date(this.emailQuotOptions.flightLegRequests[0].dateTime.split('T')[0]);
            this.searchService1.tripName = 'Trip to' + ' ' + options.flights[0].destinationCity;
            return this.searchService1.tripName;
         }
          this.searchService1.tripName = 'Trip to' + ' ' + options.flights[0].destinationCity;
          return this.searchService1.tripName;
        }
       
      } else if (type === 'hotel' && this.searchService1.tripName==='') {
        if(this.pageMode !=='webSearch' && this.emailQuotOptions && this.emailQuotOptions.resultDate){
          todayDate = new Date(this.emailQuotOptions.resultDate);
          this.searchService1.tripName = 'Trip to' + ' ' + event.title ;
       
          return this.searchService1.tripName;
       }
        this.searchService1.tripName = 'Trip to' + ' ' + event.title;
        return this.searchService1.tripName;
      }
      else if (type === 'car' && this.searchService1.tripName==='') {
        if(this.pageMode !=='webSearch' && this.emailQuotOptions && this.emailQuotOptions.resultDate){
          todayDate = new Date(this.emailQuotOptions.resultDate);
          this.searchService1.tripName = 'Trip to' + ' ' + event.title ;
          return this.searchService1.tripName;
       }
        this.searchService1.tripName = 'Trip to' + ' ' + event.title ;
        return this.searchService1.tripName;
      }else if(this.searchService1.tripName!==''){
        return this.searchService1.tripName;
      }
    }
  }
  selectionForMultiBooking() {
    let event = {};
    for (let i = 0; i < this.eventList.length; i++) {
      event['event-' + i] = '0';
    }
    return event;
  }
  getSelectedItem(emitEventToParent?) {
    this.billingItemList = new Array<BillingItem>();
    var selectedOptions;
    // if(!this.searchService1.multiTripBooking){
    if (emitEventToParent === 'nothing') {
      selectedOptions = this.selectionForMultiBooking();
    } else {
      selectedOptions = getSelectedFlightIndex();
    }

    
    //{ 'event-0': "0"};
    if (this.revalidationCompleted1) {
      this.showError = false;
    }
    if ((Object.keys(selectedOptions).length > 1 && this.searchService1.outsidePolicyselected) && this.searchService1.inPolicyFlight.length > 0) {
      if (this.searchService1.inPolicyFlight.length == 1) {
        delete selectedOptions[this.eventIndex];
      } else {
        for (let key in selectedOptions) {
          if (key !== this.eventIndex) {
            delete selectedOptions[this.eventIndex];
          }
          else {
            delete selectedOptions[key];
          }
          break;
        }
      }
    }
    this.isSelectedRevalidating = false;
    this.selectedFlightsAvailable = true;
    this.totalPayble = 0.0;
    if (this.pageMode !== 'WebSearch') {
      this.resortFee = 0
    }
    let carArray = [];
    this.searchService1.type1 = [];
    let fligthIndex = -1;
    let carIndex = -1;
    let hotelIndex = -1;
    for (var val in selectedOptions) {
      let eventIndex = Number.parseInt(val.split('-')[1]);
      let event: EventViewDTO = this.eventList[eventIndex];
      let type = event.getType();
      this.searchService1.type1.push(type);
      if (event.getType() === 'flight') {
        this.searchService1.priceChange = undefined;

        fligthIndex++;

        if (this.eventList && this.eventList.length > 1) {
          this.bookingService.nextButtonClicked = false;
        } else if (this.searchService1.multiTripBooking) {
          this.bookingService.nextButtonClicked = true;
        }
        
        let mflightV: MultiFlightViewDTO = event.getOptions()[0];
        // this.isSelectedRevalidating = this.isSelectedRevalidating || mflightV.revalidating;
        // this.selectedFlightsAvailable = this.selectedFlightsAvailable && mflightV.isAvailable;

        if (this.searchService1.outsidePolicyselected && this.searchService1.inPolicyFlight.length > 0) {
          this.eventIndex = val;
          if (false && eventIndex === 1) {
            var index = +selectedOptions[val];
            this.gallopLocalStorage.setItem("selectedFlightInPolicy", JSON.stringify(this.searchService1.inPolicyFlight[index]));
            //let inPolicyFlight = this.gallopLocalStorage.getItem("selectedFlightInPolicy");
            this.bookingService.responseData = undefined;
            this.bookingService.proceedButton = false;
            let eventIdAndOptions;
            this.bookingService.getSeatDeatils(this.travellersCount, this.ticketId, this.emailQuotOptions.tripId, eventIdAndOptions).subscribe(res => {
              if (res && res.success) {
                if (res.data && res.data.length > 0) {
                  this.bookingService.responseData = res.data;
                  this.bookingService.selectedSeatArray = new Array(this.bookingService.responseData.length).fill(null).map(_ => []);
                  //this.bookingService.uniqueAirlines =  this.uniqueAirlines ;
                  //
                }
              }
            });
          } else {
            let flight = this.gallopLocalStorage.getItem("selectedFlightInPolicy");
            if (flight) {
              this.gallopLocalStorage.removeItem("selectedFlightInPolicy");
              if (this.bookingService.selectedFlight1) {
                this.gallopLocalStorage.setItem("selectedFlightInPolicy", JSON.stringify(this.bookingService.selectedFlight1));
              } else {
                let selectedFlight1: FlightResult = deserialize(JSON.parse(this.gallopLocalStorage.getItem("selectedFlight")), FlightResult);
                this.gallopLocalStorage.setItem("selectedFlightInPolicy", JSON.stringify(selectedFlight1));
              }
              this.bookingService.responseData = undefined;
              this.bookingService.proceedButton = false;
              let eventIdAndOptions;
              this.bookingService.getSeatDeatils(this.travellersCount, this.ticketId, this.emailQuotOptions.tripId, eventIdAndOptions, fligthIndex).subscribe(res => {
                if (res && res.success) {
                  if (res.data && res.data.length > 0) {
                    this.bookingService.responseData = res.data;
                    this.bookingService.selectedSeatArray = new Array(this.bookingService.responseData.length).fill(null).map(_ => []);
                    //this.bookingService.uniqueAirlines =  this.uniqueAirlines ;
                    //
                  }
                }
              });
            }
          }
        }
        //    ),  this.isUserSelectedUATPSpecificAccountCode())
        if (emitEventToParent !== 'nothing') {
          if (this.showBillToCompany(event.getType()) && emitEventToParent!=='newCardAdded') {
            let paymentrequired = { name: 'Bill to company', id: 'Bill to company', brand: 'Bill to company', last4: 'Bill to company' };
            if (this.cardlist.find((test) => test.id === paymentrequired.id) === undefined) {
              this.cardlist.unshift(paymentrequired);
            }
            this.tripDetails[eventIndex].paymentError = false;
            this.tripDetails[eventIndex].paymenttype = paymentrequired;
          }
          else if (this.isLoggedIn() && this.userAccountInfoObj.userInfo.employeeInfo
            && this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse != null
            && this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse
            && this.isUserSelectedUATPSpecificAccountCode(fligthIndex)) {
            this.tripDetails[eventIndex].paymentError = false;
            if (this.cardlist && this.cardlist.length > 0) {
              this.tripDetails[eventIndex].paymenttype = this.cardlist[0];
            }
          }
          else if (!this.showBillToCompany(event.getType()) && !this.isUserSelectedUATPSpecificAccountCode(fligthIndex)) {
            this.tripDetails[eventIndex].paymentError = true;
          }
        }

        if (mflightV.serviceFee
          && mflightV.serviceFee.trim().length > 0
          && parseFloat(mflightV.serviceFee.trim()) > 0
        ) {
          this.billingItemList.push(new BillingItem(PaymentTypes.PREPAID, 'other', '', mflightV.serviceFee, this.currencyCode));
        }
        let billingItem: BillingItem = null;
        var credit = mflightV.getCreditDetails()
        //this.totalPayble = this.totalPayble + parseFloat(mflightV.getPrice());
        if (Object.keys(credit).length !== 0 && credit[0].creditAmount > 0) {
          let totalMoney1 = 0;
          let totalMonney = 0
          if (this.searchService1.nonCombo) {
            let price1;
            if (this.searchService1.nonComboSelectedFlight[0].displayPrice) {
              price1 = this.searchService1.nonComboSelectedFlight[0].displayPrice;
            } else {
              price1 = this.searchService1.nonComboSelectedFlight[0].price;
            }
            [price1, totalMoney1] = this.calculateTravelCredits(price1, totalMoney1);
            totalMonney = CommonUtils.getPriceAfterTravelCredits(price1, credit, this.searchService1.adultCount);
            // totalMonney = CommonUtils.getPriceAfterTravelCredits(price1, credit, this.noOfPassengers);
            this.totalPayble = (totalMonney + totalMoney1);
          } else {
            this.totalPayble = this.totalPayble + CommonUtils.getPriceAfterTravelCredits(mflightV.getPrice(), credit, this.searchService1.adultCount);
            billingItem = new BillingItem(PaymentTypes.PREPAID, 'flight', '', mflightV.getPrice(), this.currencyCode);
            billingItem.credit = credit;
            this.billingItemList.push(billingItem);
          }

          // this.totalPayble = this.totalPayble + parseFloat(mflightV.getPrice());
          this.bookingService.totalPayble = this.totalPayble;
        } else {
          this.billingItemList.push(new BillingItem(PaymentTypes.PREPAID, 'flight', '', mflightV.getPrice(), this.currencyCode));
          this.totalPayble = this.totalPayble + parseFloat(mflightV.getPrice());
          this.bookingService.totalPayble = this.totalPayble;
        }
      }
      else if (event.getType() === 'cars') {
        let mflightV = event.getCarOptions()[selectedOptions[val]];

        carIndex++;

        this.isSelectedRevalidating = this.isSelectedRevalidating || mflightV.revalidating;
        if (this.carArray && this.carArray.length > 0) {
          for (let car of this.carArray) {
            if (mflightV.flights.optionId === car.optionId) {
              mflightV.flights.basePrice = car.basePrice;
              if (selectedOptions.length === 1) {
                carArray = [];
                carArray.push(car);
              } else {
                carArray.push(car);
              }
            }
          }
          this.gallopLocalStorage.setItem("selectedCarForBooking", JSON.stringify(carArray));
        } else {
          if (selectedOptions.length === 1) {
            carArray = [];
            carArray.push(mflightV.flights);
          } else {
            carArray.push(mflightV.flights);
          }
          this.gallopLocalStorage.setItem("selectedCarForBooking", JSON.stringify(carArray));
        }
        // 

        let billingItem: BillingItem = null;
        if (!mflightV.flights.postPay) {
          if (mflightV.flights.displayPrice) {
            billingItem = new BillingItem(PaymentTypes.PREPAID, 'cars', '', mflightV.flights.displayPrice, mflightV.flights.displayCurrency);
          } else {
            billingItem = new BillingItem(PaymentTypes.PREPAID, 'cars', '', mflightV.price, this.currencyCode);
          }
          if (emitEventToParent !== 'nothing') {
            if (this.showBillToCompany(event.getType(), carIndex) && emitEventToParent!=='newCardAdded') {
              let paymentrequired = { name: 'Bill to company', id: 'Bill to company', brand: 'Bill to company', last4: 'Bill to company' };
              if (this.cardlist.find((test) => test.id === paymentrequired.id) === undefined) {
                this.cardlist.unshift(paymentrequired);
              }
              this.tripDetails[eventIndex].paymenttype = paymentrequired;
            } else {
              if (this.cardlist && this.cardlist.length > 0) {
                if (this.cardlist[0].brand === 'Bill to company' && this.cardlist.length > 1) {
                  this.tripDetails[eventIndex].paymenttype = this.cardlist[1];
                } else if (this.cardlist[0].brand !== 'Bill to company' && this.cardlist.length > 0) {
                  this.tripDetails[eventIndex].paymenttype = this.cardlist[0];
                }
              }
            }
          }
        } else {
          this.tripDetails[eventIndex].prepay = false;
          if (mflightV.flights.displayPrice) {
            billingItem = new BillingItem(PaymentTypes.POSTPAID, 'cars', '', mflightV.flights.displayPrice, mflightV.flights.displayCurrency);
          } else {
            billingItem = new BillingItem(PaymentTypes.POSTPAID, 'cars', '', mflightV.price, this.currencyCode);
          }
        }
        if (mflightV.flights.displayPrice) {
          if (mflightV.flights.displayBasePrice && mflightV.flights.displayBasePrice !== 0 && mflightV.flights.displayPrice > mflightV.flights.displayBasePrice) {
            billingItem.tax = '' + (mflightV.flights.displayPrice - mflightV.flights.displayBasePrice);
          } else {
            billingItem.tax = '0';
          }
        } else {
          if (mflightV.flights.basePrice && mflightV.flights.basePrice !== 0 && mflightV.flights.price > mflightV.flights.basePrice) {
            billingItem.tax = '' + (mflightV.flights.price - mflightV.flights.basePrice);
          } else {
            billingItem.tax = '0';
          }
        }
        // billingItem.tax='0';
        billingItem.baseprice = mflightV.flights.basePrice
        billingItem.nights = mflightV.flights.numberOfDay;
        if (this.eventList.length > 1) {
          billingItem.number = this.eventList.length;
        } else if (carArray.length > 1) {
          billingItem.number = carArray.length;
        }
        else {
          billingItem.number = this.eventList.length;
        }
        //  billingItem.number=this.eventList ? this.eventList.length:1;
        this.billingItemList.push(billingItem);
        if (mflightV.flights.displayPrice) {
          this.totalPayble = this.totalPayble + parseFloat(mflightV.flights.displayPrice);
        } else {
          this.totalPayble = this.totalPayble + parseFloat(mflightV.price);
        }
      } else if (event.getType() === 'hotel') {
        let mflightV = event.getHotelOptions()[selectedOptions[val]];
        //  
        hotelIndex++
        var noOfGuest = this.getTotalPassengers() ? this.getTotalPassengers() : this.getTotalPassengerFromEmail(mflightV);
        var totalTax = 0;
        if (mflightV.displayPrice && mflightV.displayTax) {
          totalTax = Number.parseFloat(mflightV.displayTax);
        } else {
          if (mflightV.rateDetail.displayTax) {
            totalTax = Number.parseFloat(mflightV.rateDetail.displayTax.toFixed(2));
          } else if (mflightV.rateDetail.tax && mflightV.rateDetail.tax.trim().length > 0) {
            totalTax = Number.parseFloat(mflightV.rateDetail.tax.substring(3));
          }
        }
        //         totalTax= CommonUtils.getTaxAmountFromRateDetail(mflightV.rateDetail, mflightV.hotelRooms.length);
        var perNightCharge;
        if (mflightV.displayPrice) {
          perNightCharge = ((mflightV.displayPrice - totalTax) / mflightV.stay);
        } else {
          perNightCharge = ((mflightV.price - totalTax) / mflightV.stay);
        }

        let billingItem: BillingItem = null;
        if (mflightV.prepay) {
          if (mflightV.displayPrice) {
            billingItem = new BillingItem(PaymentTypes.PREPAID, 'hotel', '', mflightV.displayPrice, this.currencyCode);
          } else {
            billingItem = new BillingItem(PaymentTypes.PREPAID, 'hotel', '', mflightV.price, this.currencyCode);
          }
          if (emitEventToParent !== 'nothing') {
            if (this.showBillToCompanyForHotel(mflightV.source) && this.showBillToCompany(event.getType())&& emitEventToParent!=='newCardAdded') {
              let paymentrequired = { name: 'Bill to company', id: 'Bill to company', brand: 'Bill to company', last4: 'Bill to company' };
              if (this.cardlist.find((test) => test.id === paymentrequired.id) === undefined) {
                this.cardlist.unshift(paymentrequired);
              }
              this.tripDetails[eventIndex].paymenttype = paymentrequired;
            } else {
              if (this.cardlist && this.cardlist.length > 0) {
                for(let card of this.cardlist){
                  if (card && card.brand !== 'Bill to company') {
                  let cardallowed = this.iscardallowedforhotelbooking(card,hotelIndex);
                  if(cardallowed){
                    this.tripDetails[eventIndex].paymenttype =card;  
                    break;
                  }
                }
              
                }
              }
            }
          }

        } else {
          if (mflightV.displayPrice) {
            billingItem = new BillingItem(PaymentTypes.POSTPAID, 'hotel', '', mflightV.displayPrice, this.currencyCode);
          }
          else {
            billingItem = new BillingItem(PaymentTypes.POSTPAID, 'hotel', '', mflightV.price, this.currencyCode);
          }

          if (this.showBillToCompanyForHotel(mflightV.source) && this.showBillToCompany(event.getType())&& emitEventToParent!=='newCardAdded') {
            let paymentrequired = { name: 'Bill to company', id: 'Bill to company', brand: 'Bill to company', last4: 'Bill to company' };
            if (this.cardlist.find((test) => test.id === paymentrequired.id) === undefined) {
              this.cardlist.unshift(paymentrequired);
            }
            this.tripDetails[eventIndex].paymenttype = paymentrequired;
          } else {
            if (this.cardlist && this.cardlist.length > 0) {
              for(let card of this.cardlist){
                if (card && card.brand !== 'Bill to company') {
                let cardallowed = this.iscardallowedforhotelbooking(card,hotelIndex);
                if(cardallowed){
                  this.tripDetails[eventIndex].paymenttype =card;  
                  break;
                }
              }
            
              }
            }
          }
        }

        billingItem.guest = noOfGuest;
        billingItem.source = mflightV.source;
        billingItem.nights = mflightV.stay;
        billingItem.rooms = mflightV.hotelRooms.length
        billingItem.perNightCharge = perNightCharge;
        billingItem.fee = mflightV.rateDetail.fee ? mflightV.rateDetail.fee.substring(3) : null;
        billingItem.tax = '' + totalTax;
        billingItem.addPolicy = mflightV.rateDetail.policy;
        if (this.pageMode === 'WebSearch') {
          if (mflightV.displayPrice) {
            billingItem.resortFee = mflightV.displayResortFee ? mflightV.displayResortFee : null;
          } else {
            billingItem.resortFee = mflightV.rateDetail.resortFee ? mflightV.rateDetail.resortFee.substring(3) : null;
          }
        } else {
          if (mflightV.displayPrice || mflightV.rateDetail.displayBase) {
            billingItem.resortFee = mflightV.rateDetail.displayResortFee ? "" + mflightV.rateDetail.displayResortFee : null;
          } else {
            billingItem.resortFee = mflightV.rateDetail.resortFee ? mflightV.rateDetail.resortFee.substring(3) : null;
          }
        }
        billingItem.number = this.eventList.length;


        this.billingItemList.push(billingItem);
        if (mflightV.serviceFee && mflightV.serviceFee > 0) {
          this.billingItemList.push(new BillingItem(PaymentTypes.PREPAID, 'other', '', mflightV.serviceFee, this.currencyCode));
        }
        if (this.pageMode !== 'WebSearch') {
          if (mflightV.displayPrice || mflightV.rateDetail.displayBase) {
            this.resortFee = this.resortFee + mflightV.rateDetail.displayResortFee ? parseFloat(mflightV.rateDetail.displayResortFee) : null;
          } else {
            this.resortFee = this.resortFee + mflightV.rateDetail.resortFee ? parseFloat(mflightV.rateDetail.resortFee.substring(3)) : null;
          }
        }
        if (mflightV.displayPrice) {
          this.totalPayble = this.totalPayble + parseFloat(mflightV.displayPrice) + this.resortFee;
        }
        else {
          this.totalPayble = this.totalPayble + parseFloat(mflightV.price) + this.resortFee;
        }
      }
    }
    if(emitEventToParent !== 'carddetailschanged' && emitEventToParent !== 'newCardAdded' && this.pageMode === 'emailflowAgent' && this.bookingService.addToTransactionDefaultPayment && this.userAccountInfoObj && this.userAccountInfoObj.cardList && this.userAccountInfoObj.cardList.card_list && this.userAccountInfoObj.cardList.card_list.length > 0){
      let cardlist = JSON.parse(JSON.stringify(this.userAccountInfoObj.cardList.card_list));
      let card = cardlist.filter(item=>item.id === this.bookingService.addToTransactionDefaultPayment.stripe_card.id)
      if(card && card[0]){
        for(let i=0;i<this.tripDetails.length;i++){
          this.tripDetails[i].paymenttype=card[0];
        }
      }
    }
    if (emitEventToParent === 'carddetailschanged' && this.hideButtonForAddingTrips) {
      this.goToPersondetailsPage(emitEventToParent)
    }
  }
  goToPersondetailsPage(emitEventToParent) {
    var selectedFlightOptionDetails: any = buildConfirmationBookingData();
    

    // this.totalPayble = selectedFlightOptionDetails.totalPrice;

    // 

    if (!selectionCompleted()) {
      return;
    }

    
    if (this.hideButtonForAddingTrips && (((this.eventList.length == 1 || this.searchService1.multiTripBooking) && emitEventToParent !== 'true' && emitEventToParent !== 'false')) || this.searchService1.selectedEventID || !this.searchService1.tripFeatureEnabled || this.getTotalPassengers() > 1 || this.pageMode !== 'WebSearch') {

      this.bookingService.nextButtonClicked = true;
      emitEventToParent = 'true';
      
      this.proceedNextToFlightSelection(selectedFlightOptionDetails, emitEventToParent);
    } else {
      alignNextButtonToBottom();
    }
  }
  
  public rowSelected(emitEventToParent: string, value, index?, type?): void {
    // 

    this.getSelectedItem(emitEventToParent);
    this.goToPersondetailsPage(emitEventToParent)

  }
  getTotalPassengerFromEmail(data) {
    var noOfGuest = 0;
    for (let item of data.rooms) {
      noOfGuest = noOfGuest + item.numberOfAdult;
    }
    return noOfGuest;
  }
  getSourceAndDestination(item){
    if(item.legs && item.legs.length ===1){
      return (item.legs[0].hops[item.legs[0].hops.length-1].to);
    }else  if(item.legs && item.legs.length ===2){
      if(item.legs[0].hops[0].from === item.legs[1].hops[item.legs[1].hops.length-1].to && item.legs[0].hops[item.legs[0].hops.length-1].to === item.legs[1].hops[0].from){
        return (item.legs[0].hops[item.legs[0].hops.length-1].to)
      }else{
        let info='';
        let index=0;
        for(let leg of item.legs){
         info = leg.hops[leg.hops.length-1].to
         
         index=index+1;
        }
        return info;
      }
    }else{
      let info='';
        let index=0;
        for(let leg of item.legs){
         info = leg.hops[leg.hops.length-1].to
         
         index=index+1;
        }
        return info;
    }
  }
  selectionChange() {
    this.eventIndex = "event-0";
    var selectedFlightOptionDetails: any = buildConfirmationBookingData();
    this.bookingService.priceChange = false;
    if (!selectedFlightOptionDetails.eventIds[0]) {
      this.bookingService.nextButtonClicked = false;
      return;
    }
    this.changeSelectionClicked.emit('changeSelection');
  }
  getPaymentType(hotelOption: any) {
    if (hotelOption.prepay) {
      return this.translateService.instant('optionSelection.prepay');
    } else {
      return this.translateService.instant('optionSelection.payathotel');
    }
  }
  public onCancel(): void {
    this.bsModalRef.hide();
    this.addCardMode = false;
  }
  getCancellationPolicyText(hotelOption: any) {
    if (hotelOption.cancellationPolicyInfo
      && hotelOption.cancellationPolicyInfo != null
      && hotelOption.cancellationPolicyInfo.trim().length > 0) {
      //return this.translateService.instant('optionSelection.CancellationPolicy')+": "
      //this.cancellationPolicy = hotelOption.cancellationPolicyInfo.toLowerCase();
      return hotelOption.cancellationPolicyInfo.toLowerCase();
    }
    if (hotelOption.rateDetail
      && hotelOption.rateDetail.cancelInfo
      && hotelOption.rateDetail.cancelInfo.text
      && hotelOption.rateDetail.cancelInfo.text.length > 0) {
      // this.cancellationPolicy= hotelOption.rateDetail.cancelInfo.text[0];
      return hotelOption.rateDetail.cancelInfo.text[0];
    }
    if (hotelOption.hotelRooms && hotelOption.hotelRooms != null
      && hotelOption.hotelRooms.length > 0
      && hotelOption.hotelRooms[0].hotelRates
      && hotelOption.hotelRooms[0].hotelRates != null
      && hotelOption.hotelRooms[0].hotelRates.length > 0
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies != null
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies.length > 0
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].cancellationDate) {
      let dateStr = new Date(DateUtils.getFormattedDateWithoutTimeZone(
        hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].cancellationDate
      )).toDateString().split(' ');
      return this.translateService.instant('optionSelection.CancellationPolicy').toString()
        + hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].amountAfterCancellation
        + this.translateService.instant('optionSelection.spacefeeafterspace').toString()
        + dateStr[1] + ' ' + dateStr[2];

      // let cancellationPolicyText = "";
      // for(let policyText of hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies){
      //   if(cancellationPolicyText.length > 0) cancellationPolicyText = cancellationPolicyText+", ";
      //   cancellationPolicyText = cancellationPolicyText+policyText;
      // }
      // return cancellationPolicyText;
    }
  }
  getCancellationPolicy(hotelOption) {
    this.cancellationPolicy = '';
    if (hotelOption.cancellationPolicyInfo
      && hotelOption.cancellationPolicyInfo != null
      && hotelOption.cancellationPolicyInfo.trim().length > 0) {
      this.cancellationPolicy = hotelOption.cancellationPolicyInfo.toLowerCase();
    }
    if (hotelOption.rateDetail
      && hotelOption.rateDetail.cancelInfo
      && hotelOption.rateDetail.cancelInfo.text
      && hotelOption.rateDetail.cancelInfo.text.length > 0) {
      this.cancellationPolicy = hotelOption.rateDetail.cancelInfo.text[0];
    }
    if (hotelOption.hotelRooms && hotelOption.hotelRooms != null
      && hotelOption.hotelRooms.length > 0
      && hotelOption.hotelRooms[0].hotelRates
      && hotelOption.hotelRooms[0].hotelRates != null
      && hotelOption.hotelRooms[0].hotelRates.length > 0
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies != null
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies.length > 0
      && hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].cancellationDate) {
      let dateStr = new Date(DateUtils.getFormattedDateWithoutTimeZone(
        hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].cancellationDate
      )).toDateString().split(' ');
      this.cancellationPolicy = this.translateService.instant('optionSelection.CancellationPolicy').toString()
        + hotelOption.hotelRooms[0].hotelRates[0].cancellationPolicies[0].amountAfterCancellation
        + this.translateService.instant('optionSelection.spacefeeafterspace').toString()
        + dateStr[1] + ' ' + dateStr[2];
    }
    return this.cancellationPolicy;
  }


  showModal(modal, hotelOption: any) {

    if (this.cancellationPolicy.length > 0) {
      this.bsModalRef = this.modalService.show(modal);
    }
  }
  getOAirlineName(flightLeg){
    if(flightLeg && flightLeg.hops[0].oAirlineName && flightLeg.hops[0].oAirlineName!==flightLeg.hops[0].airlineName){
         return true ;
    }else{
      return false
    }
  }
  scrollTo(el: Element): void {
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }
  private scrollToFirstInvalidControl() {
    const firstInvalidControl: HTMLElement = document.getElementById("ErroHere");
    this.scrollTo(firstInvalidControl);
  }
  public scrollToFirstInvalidControl1(id) {
    const firstInvalidControl: HTMLElement = document.getElementById(id);
    this.scrollTo(firstInvalidControl);
  }
  public scrollToFirstInvalidControl2(index) {
    let firstInvalidControl: HTMLElement
    if (!this.isMobile) {
      firstInvalidControl = document.getElementById("error" + index);
    }
    if (this.isMobile) {
      firstInvalidControl = document.getElementById("errorMobile" + index);
    }
    this.scrollTo(firstInvalidControl);
  }
  hideButtonForAddingTrips = false;
  processNextToPersonalDetails(modal?) {
    var selectedFlightOptionDetails: any = buildConfirmationBookingData();
    let params;
    if (this.pageMode === 'WebSearch') {
      if (this.skipInPolicyOptions) {
        params = "?ua_action=proceedToPersonalDetails&ua_policyType=outOfPolicy";
      } else {
        params = "?ua_action=proceedToPersonalDetails&ua_policyType=inPolicy";
      }
      this.searchService1.letsTrack(params);
    }
    if (this.searchService1.outsidePolicyselected && this.searchService1.inPolicyFlight.length > 0) {
      if (!this.skipInPolicyOptions && !selectedFlightOptionDetails.eventIds[0]) {
        this.showError = true;
        this.scrollToFirstInvalidControl();
        this.bookingService.nextButtonClicked = false;
        return;
      }
    }
    if (!selectionCompleted()) {
      // this.toastr.warning('Please "Select" an option to book from each set OR "Skip" if you wish to skip any set of options.', '');
      this.toastr.warning(this.translateService.instant('optionSelection.selectOptionOrSkip').toString());
      return;
    }
    nextButtonClicked = true;
    if (this.bookingService.priceChange) {
      this.bookingService.priceChange = false;
    }
    this.showError = false;
    
    this.hideButtonForAddingTrips = true;
    this.proceedNextToFlightSelection(selectedFlightOptionDetails, 'true',modal);
    this.bookingService.nextButtonClicked = true;
    if (!this.revalidationCompleted1) {
      this.bookingService.beforeRevalidation = true;
    }
  }
  setBillingItemArray(amount) {
    if (this.eventList && this.eventList.length === 1) {
      for (let item of this.eventList) {
        if (item.options && item.options.length > 0) {
          for (let item1 of item.options) {
            item1.price = amount;
            if (item1.travelCreditsInfo && item1.travelCreditsInfo.length > 0) {
              if (item1.travelCreditsInfo[0].creditAmount >= amount) {
                this.totalPayble = 0;
              } else if (item1.travelCreditsInfo[0].creditAmount < amount) {
                this.totalPayble = amount - item1.travelCreditsInfo[0].creditAmount;
              }
            } else {
              this.totalPayble = amount;
            }
            // item1.isDiscounted = amount;
            let newDiscountedPrice = this.bookingService.bookingResponse.changedFlights.flights[0].discountedPrice;
            if (newDiscountedPrice != null) {
              item1.setDiscountedPrice(newDiscountedPrice);
            }
            this.eventList[0].options[0].flights[0].hops[0].flightHighlights.withinPolicy =this.bookingService.bookingResponse.changedFlights.flights[0].legs[0].flightHighlights.withinPolicy;
            item1.isWithinPolicy = this.bookingService.bookingResponse.changedFlights.flights[0].legs[0].flightHighlights.withinPolicy;
            this.emailBookingFlowService.policyFlagChangeListener.next(item1.isWithinPolicy);
            item1.priceUpdated = true;
            // 
          }
        }
      }
    } else if (this.searchService1.outsidePolicyselected) {
      for (let i = 0; i < this.eventList.length; i++) {
        for (let j = 0; j < this.eventList[i].options.length; j++) {
          var isSelect = this.isOptionSelected(i, j);
          if (isSelect) {
            this.eventList[i].options[j].price = amount;
            if (this.eventList[i].options[j].travelCreditsInfo && this.eventList[i].options[j].travelCreditsInfo.length > 0) {
              if (this.eventList[i].options[j].travelCreditsInfo[0].creditAmount >= amount) {
                this.totalPayble = 0;
              } else if (this.eventList[i].options[j].travelCreditsInfo[0].creditAmount < amount) {
                this.totalPayble = amount - this.eventList[i].options[j].travelCreditsInfo[0].creditAmount;
              }
            } else {
              this.totalPayble = amount;
            }
            // this.eventList[i].options[j].isDiscounted = amount;
            let newDiscountedPrice = this.bookingService.bookingResponse.changedFlights.flights[0].discountedPrice;
            if (newDiscountedPrice != null) {
              this.eventList[i].options[j].setDiscountedPrice(newDiscountedPrice);
            }
            this.eventList[i].options[j].isWithinPolicy = this.bookingService.bookingResponse.changedFlights.flights[0].legs[0].flightHighlights.withinPolicy;
            // let legCounter = 0;
            this.emailBookingFlowService.policyFlagChangeListener.next(this.eventList[i].options[j].isWithinPolicy);
            this.gallopLocalStorage.setItem("selectedFlightInPolicy", JSON.stringify(this.bookingService.selectedFlight));
            this.eventList[i].options[j].priceUpdated = true;
            if (i == 1) {
              this.eventList[i].options[j].isWithinPolicy = this.bookingService.bookingResponse.changedFlights.flights[0].legs[0].flightHighlights.withinPolicy;
              let legCounter = 0;
              this.emailBookingFlowService.policyFlagChangeListener.next(this.eventList[i].options[j].isWithinPolicy);
              for (let currLeg of this.searchService1.inPolicyFlight[j].legs) {
                currLeg.flightHighlights.withinPolicy =
                  this.bookingService.bookingResponse.changedFlights.flights[0].legs[legCounter].flightHighlights.withinPolicy;
                legCounter++;
              }
              let newDiscountedPrice = this.bookingService.bookingResponse.changedFlights.flights[0].discountedPrice;
              if (newDiscountedPrice != null) {
                this.eventList[i].options[j].setDiscountedPrice(newDiscountedPrice);
              }
              this.searchService1.inPolicyFlight[j].discountedPrice = this.bookingService.bookingResponse.changedFlights.flights[0].discountedPrice;
              this.searchService1.inPolicyFlight[j] = this.bookingService.bookingResponse.changedFlights.flights[0];
              this.searchService1.inPolicyFlight[j]
              this.eventList[i].options[j].priceUpdated = true;
              this.gallopLocalStorage.setItem("selectedFlightInPolicy", JSON.stringify(this.searchService1.inPolicyFlight[j]));
            }
          }
        }
      }
    }
  }
  proceedNextToFlightSelection(selectedFlightOptionDetails: any, emitChange: string,modal?) {
    if (emitChange && emitChange === 'true') {
      showTravellersCard();
      let airlines = [];
      let hotelChains = [];
      let cars = [];
      if ('ffnAirlines' in selectedFlightOptionDetails) {
        airlines = selectedFlightOptionDetails.ffnAirlines;
        this.bookingService.flightSelected = selectedFlightOptionDetails.multiFlightOptions;
      }
      if ('hotelChains' in selectedFlightOptionDetails) {
        hotelChains = selectedFlightOptionDetails.hotelChains;
      }
      if ('multiCarOptions' in selectedFlightOptionDetails) {
        cars = selectedFlightOptionDetails.multiCarOptions;
      }

      this.selectedFlightChange.emit({
        "billingList": this.billingItemList, "airlines": airlines,
        "hotels": hotelChains, "cars": cars, "outsidePolicy": isOutsidePolicy(), "choices": selectedFlightOptionDetails.choice,
        "canAttemptDirectBooking": this.selectedFlightsAvailable && this.getAttemptDirectBookingFlag(),
        "isRevalidationInProgress": this.isSelectedRevalidating,
        "isPassportMandatory": isPassportMandatoryForBooking(), "isZipCodeMandatory": isZipcodeMandatoryForBooking()
      });
      GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
        'flightSelected', 'WebSearchUI'
      );
    }
  }
  goToSearchResults() {
    // this.router.navigate(["flights"],{queryParams: { ...this.activatedRoute.snapshot.queryParams, step:0, userid: this.userid, sToken: this.sToken}, replaceUrl: true});
  }
  goToHomePage() {
    this.router.navigate([this.userAccountInfoService.getDefaultRoutePath()], { queryParams: { userid: this.userAccountInfoService.getUserEmail(), sToken: this.userAccountInfoService.getSToken() }, replaceUrl: true });
  }

  public toggleRebooking() {
    if (this.rebookingConfirm) {
      this.rebookingConfirm = false;
    } else {
      this.rebookingConfirm = true;
    }
    this.bookingService.rebookingConfirm = this.rebookingConfirm;
  }
  public toggleSkip() {
    if (this.skipInPolicyOptions) {
      this.skipInPolicyOptions = false;
    } else {
      this.skipInPolicyOptions = true;
    }
    this.changeSelectionClicked.emit('skipClicked');
    var selectedFlightOptionDetails: any = buildConfirmationBookingData();
    this.rowSelected('true', -1);
    if (!selectionCompleted()) {
      return;
    }
    if (this.searchService1.outsidePolicyselected && this.searchService1.inPolicyFlight.length > 0 && this.skipInPolicyOptions) {
      //this.proceedNextToFlightSelection(selectedFlightOptionDetails, 'true');
    }
    // showTravellersCard();
    let airlines = [];
    let hotelChains = [];
    if ('ffnAirlines' in selectedFlightOptionDetails) {
      airlines = selectedFlightOptionDetails.ffnAirlines;
    }
    if ('hotelChains' in selectedFlightOptionDetails) {
      hotelChains = selectedFlightOptionDetails.hotelChains;
    }
    GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
      'hotelSelected', 'WebSearchUI'
    );

  }


  public showGallopCashBack() {
    let plan: SubscriptionPlan = this.userAccountInfoService.getSubscriptionPlan();
    if (plan && plan != null && plan.id.toString() !== 'subscription_free') {
      return true;
    } else {
      return false;
    }
  }

  public getGallopCashBack() {
    return this.userAccountInfoService.getGallopCashBack(this.totalPayble);
  }
  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }

  getCarCurrencySymbol(item) {
    if (item.displayCurrency) {
      return CommonUtils.getCurrencySymbol(item.displayCurrency);
    } else {
      return CommonUtils.getCurrencySymbol(item.currency);
    }
  }

  public notifyAgentConfirmation() {
    const userid = this.userAccountInfoService.getUserEmail();
    const sToken = this.userAccountInfoService.getSToken();
    let travellerDetails =  this.bookingService.getTravelerDetails('emailFlow');
    let userIdOfImpersonated = travellerDetails.email;
    if (this.bookingService.selectedTag && this.bookingService.selectedTag.length > 0) {
      notifyAgent(false, false, false, this.bookingService.selectedTag.toString(),this.searchService1.tripName,this.bookingService.noteToadmin,userid,sToken,userIdOfImpersonated);
    } else {
      notifyAgent(false, false, false, '',this.searchService1.tripName,this.bookingService.noteToadmin,userid,sToken,userIdOfImpersonated);
    }
  }

  public handleSelectDifferentOption() {
    if (this.pageMode === 'WebSearch') {
      let flightSearchQueryParam: FlightSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("flightSearchRequest")));
      let hotelSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequestForBooking")));
      let carSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("carSearchRequestForBooking")));
      if (flightSearchQueryParam) {
        if (this.searchService1.processedFlightResults && this.searchService1.processedFlightResults.length > 1) {
          this.searchService1.flight = [];
          this.searchService1.outgoingSelectedFlight = undefined;
          this.searchService1.resetOutgoingSelectedFlight(0);
        }
        //  var params = this.searchService1.getSearchRequest(flightSearchQueryParam);
        this.searchService1.seletedIndex = 0;
        this.searchService1.multipleOutgoingSelectedFlight = [];
        this.searchService1.nonComboSelectedFlight = [];
        this.searchService1.flightId = [];
        //  this.searchService1.selectedFilter1 = [];
        this.searchService1.backClick = false;
        // this.searchService.selectedFilter1 = [];
        this.filterService.broadcastgetFilters();
        this.router.navigate(["flights"],
          {
            queryParams:
            {
              query: encodeURIComponent(JSON.stringify(flightSearchQueryParam)),
              step: 0,
              index: this.searchService1.seletedIndex,
              resultFound: 1
            },
            replaceUrl: true
          });
      } else if (hotelSearchQueryParam) {
        this.router.navigate(['/hotelSelection'], { queryParams: {}, replaceUrl: true });
      } else if (carSearchQueryParam) {
        this.router.navigate(["carResult"],
          {
            queryParams:
            {
              query: encodeURIComponent(JSON.stringify(carSearchQueryParam)),
              step: 0,
              resultFound: 1
            },
            replaceUrl: true
          });
      }
    } else {
      this.searchService1.paymentPage = false;
      if (this.eventList.length == 1 && this.eventList[0].getOptions().length == 1) {
        let query = {};
        query['source'] = this.emailQuotOptions.flightLegRequests[0].origin;
        query['destination'] = this.emailQuotOptions.flightLegRequests[0].destination;
        query["departureDate"] = this.emailQuotOptions.flightLegRequests[0].dateTime.replace("T", " ").split(" ")[0] + "T00:00:00.000Z";
        query["constraintTime"] = "Anytime";
        query["passengers"] = this.emailQuotOptions.flightSearchOptions.adultCount
        query["adults"] = this.emailQuotOptions.flightSearchOptions.adultCount;
        query["children"] = this.emailQuotOptions.flightSearchOptions.childCount;
        query["infants"] = this.emailQuotOptions.flightSearchOptions.infantCount;
        query["classTypeSwitch"] = this.emailQuotOptions.flightSearchOptions.algoType;
        let seatClass: String = this.emailQuotOptions.flightSearchOptions.seatClass;
        // if (seatClass === 'AS_POLICY'){
        //   seatClass = TraflaFlightClass.fromValue(multiFlightOptions.get(0).get(0).get(0).getFlightHops().get(0).getType()).name();
        // }
        query["class"] = seatClass;
        query["trip"] = "ONEWAY";
        let flights = [];
        if (this.emailQuotOptions.flightLegRequests[0].comboDetails && this.emailQuotOptions.flightLegRequests[0].comboDetails.length > 1) {
          let legRequest = this.emailQuotOptions.flightLegRequests[0].comboDetails[1];
          if (legRequest.origin === query['destination']) {
            query["trip"] = "ROUNDTRIP";
            query["arrivalDate"] = legRequest.dateTime.replace("T", " ").split(" ")[0] + "T00:00:00.000Z";
            query["constraintTime1"] = "Anytime";
          } else {
            query["trip"] = "MULTICITY";
            let flightLegQuery = {};
            flightLegQuery['source'] = legRequest.origin;
            flightLegQuery['destination'] = legRequest.destination;
            flightLegQuery["departureDate"] = legRequest.dateTime.replace("T", " ").split(" ")[0] + "T00:00:00.000Z";
            flightLegQuery["arrivalDate"] = legRequest.dateTime.replace("T", " ").split(" ")[0] + "T00:00:00.000Z";
            flightLegQuery["constraintTime"] = "Anytime";
            flights.push(flightLegQuery);
          }
        }
        query["flights"] = flights;
        this.router.navigate(['/'+this.userAccountInfoService.getDefaultRoutePath()], { queryParams: { query: JSON.stringify(query) }, replaceUrl: true });
      } else {
        for (let i = 0; i < this.eventList.length; i++) {
          calculateSelectionByEventIndex1(i);
          // this.toggleSkip();
        }
        nextButtonClicked = false;
        this.bookingService.nextButtonClicked = false;
        var selectedOptions = getSelectedFlightIndex();
        for (let key in selectedOptions) {
          autoChangeSelection(key.split("-")[1]);
        }
      }
    }
  }
  bookServiceSubscription: Subscription;
  revalidatedOptionsMap = [];

  handleRevalidateResponse(res, currencyCode, eventIdx: number, eventId: string, optionIdx: string, optionId: string, threadId: string, flightView: MultiFlightViewDTO) {

    this.revalidatedOptionsMap['event-' + eventIdx + '-oIdx' + optionIdx] = 'revalidated';
    flightView.revalidating = false;
    let bookingResponse: BookingResponse = deserialize(res, BookingResponse);
    let responseType = bookingResponse.status.toUpperCase();
    this.revalidationCompleted1 = true;
    if (responseType === Constants.ERROR) {
      responseType = bookingResponse.errors[0].errorType;
    }

    if (responseType === BookingResponseErrorType.ERROR_AVAILABILITY
      || responseType === BookingResponseErrorType.ERROR_INTERNAL
      || responseType === BookingResponseErrorType.ERROR_API) {
      // this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: !this.hasHotelBillingItem() }, backdrop: true, ignoreBackdropClick: true });
      flightView.isAvailable = false;
    } else if (responseType === BookingResponseErrorType.ERROR_PRICE_CHANGE) {
      let newPrice;
      if (bookingResponse.changedFlights.flights[0].displayPrice) {
        newPrice = bookingResponse.changedFlights.flights[0].displayPrice.toFixed(2);
        this.searchService1.displayPrice = bookingResponse.changedFlights.flights[0].displayPrice;
      } else {
        newPrice = bookingResponse.changedFlights.flights[0].price;
      }
      this.bookingService.priceChange = true;
      let newDiscountedPrice = bookingResponse.changedFlights.flights[0].discountedPrice;
      flightView.setPrice(newPrice);
      this.eventList[0].options[0].flights[0].hops[0].flightHighlights.withinPolicy =bookingResponse.changedFlights.flights[0].legs[0].flightHighlights.withinPolicy;
      flightView.isWithinPolicy = bookingResponse.changedFlights.flights[0].legs[0].flightHighlights.withinPolicy;
      this.emailBookingFlowService.policyFlagChangeListener.next(flightView.isWithinPolicy);
      if (newDiscountedPrice != null) {
        flightView.setDiscountedPrice(newDiscountedPrice);
      }
      flightView.priceUpdated = true;
      this.bookingService.priceChanngeFlight = bookingResponse.changedFlights.flights[0];;
      this.bookingService.selectedFlight = bookingResponse.changedFlights.flights[0];
      this.bookingService.selectedFlight1 = bookingResponse.changedFlights.flights[0];
      this.revalidationPriceChanged = true;
    } else {
      flightView.isAvailable = true;
      // let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse);
      // this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, ignoreBackdropClick: true });
    }
    var selectedOptions = getSelectedFlightIndex();


    if (selectedOptions['event-' + eventIdx] === optionIdx) {

      // this.changeSelectionClicked.emit('changeSelection');
      if (this.revalidationPriceChanged) {
        revalidateUpdateFlightPrice(eventIdx, optionIdx, flightView.getPrice(), flightView.getDiscountedPrice(), flightView.getWithinPolicy());
        this.rowSelected('true', -1);
      } else {
        //this.rowSelected('false', -1);
      }
      if (this.bookingService.beforeRevalidation) {
        this.bookingService.nextButtonClicked = true;
      }
      // buildConfirmationBookingData();

      let revalidationCompleted = 'yes';
      for (let key in selectedOptions) {
        if (this.revalidatedOptionsMap[key + '-oIdx' + selectedOptions[key]] != 'revalidated') {
          revalidationCompleted = 'no';
          break;
        }
      }

      if (revalidationCompleted === 'yes' && this.revalidationPriceChanged) {
        this.revalidationCompleted = true;
        let bookingModalRes = {
          description: this.translateService.instant('option.Thetotalfarefortheselecteditineraryhaschangedto') + '<span class="text-fare">' + currencyCode + this.totalPayble + '</span>' + '\n' +
            this.translateService.instant('option.PleaseclickAccepttoproceedwiththenewfareorclickSelectDifferentOption.'),
          nextStep: this.translateService.instant('option.SelectDifferentOption'),
          buttonName: this.translateService.instant('bookingService.ACCEPT'),
          iconSCSS: 'fare-changed',
          errorCode: BookingResponseErrorType.REVALIDATE_PRICE_CHANGE
        };
        if (selectionCompleted()) {
          notifyAgent(true);
        } else if (selectedOptions.length == 0) {
          bookingModalRes = {
            description: this.translateService.instant('option.Pleasenotethatthepricesofsomeoftheoptionshavechanged.'),
            nextStep: '',
            buttonName: this.translateService.instant('option.OK'),
            iconSCSS: 'fare-changed',
            errorCode: BookingResponseErrorType.REVALIDATE_PRICE_CHANGE
          };
        }
        if (this.singleOption == true && this.singleOption1 == true && this.eventCounter > 1) {
          bookingModalRes.description = this.translateService.instant('option.Thetotalfarefortheselecteditineraryhaschangedto') + '<span class="text-fare">$' + this.totalPayble + '</span>' + '\n' +
            this.translateService.instant('option.PleaseclickAccepttoproceedwiththenewfareorclickSelectDifferentOption.');
          bookingModalRes.nextStep = '';
        }
        this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bookingModalRes, backdrop: true, ignoreBackdropClick: true });
        this.bsModalRef.content.acceptClickSubject.subscribe(result => {
          //
          if (result && result === 'nextStepCallBack') {
            this.handleSelectDifferentOption();
          }
          else if (result && result === 'ACCEPT') {
            if (selectionCompleted()) {
              var selectedFlightOptionDetails: any = buildConfirmationBookingData();
              if (this.eventList.length > 1) {
                this.processNextToPersonalDetails()
              } else {
                this.proceedNextToFlightSelection(selectedFlightOptionDetails, 'true');
              }
            }
          }
        });
      }
    }
  }
  revalidateFlight(eventIdx: number, eventId: string, optionIdx: string, optionId: string, threadId: string, flightView: MultiFlightViewDTO, index) {
    if (!canAttemptRevalidate(eventIdx, optionIdx)) return;
    flightView.revalidating = true;
    this.revalidationCompleted = false;
    this.revalidationCompleted1 = false;
    let currencyCode = this.getCurrencySymbol(this.emailQuotOptions.multiFlightOptions[0][0][0].currency);
    this.bookServiceSubscription = this.bookingService.revalidateFlight(
      this.emailQuotOptions.ticketId, this.emailQuotOptions.tripId, eventId, optionId, threadId, index).subscribe((res) => {
        setTimeout(() => {
          this.handleRevalidateResponse(res, currencyCode, eventIdx, eventId, optionIdx, optionId, threadId, flightView);
        }, 800);
      },
        error => {
          flightView.revalidating = false;
        }
      );
  }
  carBookServiceSubscription: Subscription;
  revalidateCar(eventIdx: number, eventId: string, optionIdx: string, optionId: string, threadId: string, flightView, carObject) {
    if (!canAttemptCarRevalidate(eventIdx, optionIdx)) return;
    flightView.revalidating = true;
    this.carBookServiceSubscription =
      this.carBookingService.revalidateCar(
        this.emailQuotOptions.ticketId, this.emailQuotOptions.tripId, eventId, optionId, threadId, carObject
      ).subscribe((res) => {
        this.revalidatedOptionsMap['event-' + eventIdx + '-oIdx' + optionIdx] = 'revalidated';
        flightView.revalidating = false;
        let bookingResponse = deserialize(res);

        //let selectedCar=deserialize(JSON.parse(this.gallopLocalStorage.getItem("selectedCar")),CarResult);

        let responseType = (bookingResponse.status) ? bookingResponse.status.toUpperCase() : '';
        //let responseType =  'ERROR';
        if (this.eventList && this.eventList.length > 1) {
          this.bookingService.priceChange = false;
        }
        if (responseType === 'SUCCESS' && res.carInfo) {
          if (res.carInfo.allowedCard) {
            this.bookingService.cardAllowed = res.carInfo.allowedCard;
          }

          let selectCar = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
          for (let item of this.eventList) {
            if (item.carOptions && item.carOptions.length > 0) {
              for (let car of item.carOptions) {
                if (car.flights.optionId === Number(res.carInfo.optionId)) {
                  car.flights.paymentRequired = res.carInfo.paymentRequired;
                  car.flights.postPay = res.carInfo.postPay;
                  car.flights.zipCodeRequired = res.carInfo.zipCodeRequired;
                  res.carInfo.duplicateBooking = selectCar[Number(res.carInfo.optionId)].duplicateBooking;
                  selectCar[Number(res.carInfo.optionId)] = res.carInfo;
                  if (res.carInfo.displayPrice) {
                    car.flights.displayPrice = res.carInfo.displayPrice;
                    car.flights.displayBasePrice = res.carInfo.displayBasePrice;
                    this.searchService1.displayPrice = res.carInfo.displayPrice;
                    car.flights.displayCurrency = res.carInfo.displayCurrency;
                    this.searchService1.displayCurrency = res.carInfo.displayCurrency;
                  }
                  let carSelectionData = res.carInfo;
                  this.carArray.push(carSelectionData)

                }
              }
            }
          }
          if (this.searchService1.multiTripBooking) {
            // let selectCar = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
            if (selectCar) {

              // this.gallopLocalStorage.removeItem("selectedCar");
              
              this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(selectCar));
            } else {
              this.gallopLocalStorage.removeItem("selectedCar");
              this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(this.carArray));
            }
          } else {
            this.gallopLocalStorage.removeItem("selectedCar");
            this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(this.carArray));
          }
          this.rowSelected('true', -1);
        }
        if (responseType === Constants.ERROR) {
          responseType = bookingResponse.errors[0].errorType;
        }

        if (responseType === BookingResponseErrorType.ERROR_AVAILABILITY
          || responseType === BookingResponseErrorType.ERROR_INTERNAL
          || responseType === BookingResponseErrorType.ERROR_API) {
          //this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: !this.hasHotelBillingItem() }, backdrop: true, ignoreBackdropClick: true });
          flightView.isAvailable = false;
        } else if (responseType === BookingResponseErrorType.ERROR_PRICE_CHANGE) {
          let newPrice;
          if (bookingResponse.carInfo.displayPrice) {
            newPrice = bookingResponse.carInfo.displayPrice;
            this.searchService1.displayPrice = bookingResponse.carInfo.displayPrice;
          } else {
            newPrice = bookingResponse.carInfo.price;
          }
          //let newDiscountedPrice = bookingResponse.changedFlights.flights[0].discountedPrice;

          flightView.setPrice(newPrice);
          flightView.isWithinPolicy = bookingResponse.carInfo.policy;
          let selectCar = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
          for (let item of this.eventList) {
            if (item.carOptions && item.carOptions.length > 0) {
              for (let car of item.carOptions) {
                if (car.flights.optionId === Number(res.carInfo.optionId)) {
                  car.flights.paymentRequired = res.carInfo.paymentRequired;
                  car.flights.postPay = res.carInfo.postPay;
                  car.flights.zipCodeRequired = res.carInfo.zipCodeRequired;
                  res.carInfo.duplicateBooking = selectCar[Number(res.carInfo.optionId)].duplicateBooking;
                  selectCar[Number(res.carInfo.optionId)] = res.carInfo;
                  if (res.carInfo.displayPrice) {
                    car.flights.displayPrice = res.carInfo.displayPrice;
                    car.flights.displayBasePrice = res.carInfo.displayBasePrice;
                    this.searchService1.displayPrice = res.carInfo.displayPrice;
                    car.flights.displayCurrency = res.carInfo.displayCurrency;
                    this.searchService1.displayCurrency = res.carInfo.displayCurrency;
                  }
                  let carSelectionData = res.carInfo;
                  this.carArray.push(carSelectionData)

                }
              }
            }
          }
          if (this.searchService1.multiTripBooking) {
            // let selectCar = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
            if (selectCar) {

              // this.gallopLocalStorage.removeItem("selectedCar");
              
              this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(selectCar));
            } else {
              this.gallopLocalStorage.removeItem("selectedCar");
              this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(this.carArray));
            }
          } else {
            this.gallopLocalStorage.removeItem("selectedCar");
            this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(this.carArray));
          }
          this.rowSelected('true', -1);

          // }
          this.emailBookingFlowService.policyFlagChangeListener.next(flightView.isWithinPolicy);
          //if(newDiscountedPrice!=null){
          //flightView.setDiscountedPrice(newDiscountedPrice);
          //}
          flightView.priceUpdated = true;
          this.carBookingService.selectedCar = bookingResponse.carInfo;
          let carSelectionData = JSON.parse(JSON.stringify(bookingResponse.carInfo));

          let carArray = [];
          carArray.push(carSelectionData)
          // this.gallopLocalStorage.setItem("selectedCar", JSON.stringify(carArray));
          this.revalidationPriceChanged = true;
          this.bookingService.priceChange = true;
        } else {
          flightView.isAvailable = true;
          // let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse);
          // this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, ignoreBackdropClick: true });
        }
        var selectedOptions = getSelectedFlightIndex();


        if (selectedOptions['event-' + eventIdx] === optionIdx) {

          // this.changeSelectionClicked.emit('changeSelection');
          if (this.revalidationPriceChanged) {
            revalidateUpdateCarPrice(eventIdx, optionIdx, flightView.getPrice(), '0', flightView.getWithinPolicy());
            this.rowSelected('true', -1);
          } else {
            this.rowSelected('false', -1);
          }

          // buildConfirmationBookingData();

          let revalidationCompleted = 'yes';
          for (let key in selectedOptions) {
            if (this.revalidatedOptionsMap[key + '-oIdx' + selectedOptions[key]] != 'revalidated') {
              revalidationCompleted = 'no';
              break;
            }
          }

          // if(this.revalidationPriceChanged){
          //   let bookingModalRes = {
          //     description: 'The total fare for the selected itinerary has changed to <span class="text-fare">'+currencyCode+this.totalPayble+'</span>'+'\n'+
          //     'Please click "Accept" to proceed with the new fare or click "Select Different Option".',
          //     nextStep: 'Select Different Option',
          //     buttonName: this.translateService.instant('bookingService.ACCEPT'),
          //     iconSCSS: 'fare-changed',
          //     errorCode:BookingResponseErrorType.REVALIDATE_PRICE_CHANGE
          //   };
          //   if(selectionCompleted()){
          //     notifyAgent(true);
          //   }else if(selectedOptions.length == 0){
          //     bookingModalRes = {
          //       description: 'Please note that the prices of some of the options have changed.',
          //       nextStep: '',
          //       buttonName: 'OK',
          //       iconSCSS: 'fare-changed',
          //       errorCode:BookingResponseErrorType.REVALIDATE_PRICE_CHANGE
          //     };
          //   }
          //   if(this.singleOption== true && this.singleOption1==true && this.eventCounter > 1){
          //     bookingModalRes.description= 'The total fare for the selected itinerary has changed to <span class="text-fare">$'+this.totalPayble+'</span>'+'\n'+
          //     'Please click "Accept" to proceed with the new fare';
          //     bookingModalRes.nextStep='';
          //   }
          //   this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bookingModalRes, backdrop: true, ignoreBackdropClick: true });
          //   this.bsModalRef.content.acceptClickSubject.subscribe(result => {
          //     
          //     if (result && result === 'nextStepCallBack'){
          //       this.handleSelectDifferentOption();
          //     }
          //   });
          // }
        }

      },
        error => {
          flightView.revalidating = false;
        }
      );
  }
  backToList() {
    try {
      if(this.searchService1.multiTripBooking){
        if (this.searchService1.exitFromMultiTrip) {
             
          this.searchService1.multiTripBooking =false;
          this.searchService1.exitFromMultiTrip =false;

        this.router.navigate(['/'+this.userAccountInfoService.getDefaultRoutePath()], {
          queryParams: {},
          replaceUrl: true
        });
      }else if(this.searchService1.tripType==='Flight'){
          let flightSearchQueryParam: FlightSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("flightSearchRequest")));
          let hotelSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequestForBooking")));
          let carSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("carSearchRequestForBooking")));
          if (flightSearchQueryParam) {
            if (this.searchService1.processedFlightResults && this.searchService1.processedFlightResults.length > 1) {
              this.searchService1.flight = [];
              this.searchService1.outgoingSelectedFlight = undefined;
              this.searchService1.resetOutgoingSelectedFlight(0);
            }
            //  var params = this.searchService1.getSearchRequest(flightSearchQueryParam);
            this.searchService1.seletedIndex = 0;
            this.searchService1.multipleOutgoingSelectedFlight = [];
            this.searchService1.nonComboSelectedFlight = [];
            this.searchService1.flightId = [];
            this.searchService1.backClick = false;
            this.filterService.broadcastgetFilters();
            this.router.navigate(["flights"],
              {
                queryParams:
                {
                  query: encodeURIComponent(JSON.stringify(flightSearchQueryParam)),
                  step: 0,
                  index: this.searchService1.seletedIndex,
                  resultFound: 1
                },
                replaceUrl: true
              });
        }
      }else if(this.searchService1.tripType==='Hotel'){
        this.router.navigate(['/hotelSelection'], { queryParams: {}, replaceUrl: true });
        }else if(this.searchService1.tripType==='Car'){
          let carSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("carSearchRequestForBooking")));
          this.router.navigate(["carResult"],
          {
            queryParams:
            {
              query: encodeURIComponent(JSON.stringify(carSearchQueryParam)),
              step: 0,
              resultFound: 1
            },
            replaceUrl: true
          });
          }
        }

      else{
      let flightSearchQueryParam: FlightSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("flightSearchRequest")));
      let hotelSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequestForBooking")));
      let carSearchQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("carSearchRequestForBooking")));
      if (flightSearchQueryParam) {
        if (this.searchService1.processedFlightResults && this.searchService1.processedFlightResults.length > 1) {
          this.searchService1.flight = [];
          this.searchService1.outgoingSelectedFlight = undefined;
          this.searchService1.resetOutgoingSelectedFlight(0);
        }
        //  var params = this.searchService1.getSearchRequest(flightSearchQueryParam);
        this.searchService1.seletedIndex = 0;
        this.searchService1.multipleOutgoingSelectedFlight = [];
        this.searchService1.nonComboSelectedFlight = [];
        this.searchService1.flightId = [];
        this.searchService1.backClick = false;
        this.filterService.broadcastgetFilters();
        this.router.navigate(["flights"],
          {
            queryParams:
            {
              query: encodeURIComponent(JSON.stringify(flightSearchQueryParam)),
              step: 0,
              index: this.searchService1.seletedIndex,
              resultFound: 1
            },
            replaceUrl: true
          });
      } else if (hotelSearchQueryParam) {
        this.router.navigate(['/hotelSelection'], { queryParams: {}, replaceUrl: true });
      } else if (carSearchQueryParam) {
        this.router.navigate(["carResult"],
          {
            queryParams:
            {
              query: encodeURIComponent(JSON.stringify(carSearchQueryParam)),
              step: 0,
              resultFound: 1
            },
            replaceUrl: true
          });
      }
    }
    } catch (error) {
      
    }
  }
  getBrandText(brandClass: string) {
    if (brandClass && brandClass.trim().length > 0) {
      return   brandClass ;
    }
    return '';
  }

  ngOnDestroy() {
    if (this.connectionListener) {
      this.connectionListener.unsubscribe();
    }
    if (this.fetchAccountInfoSubscription) {
      this.fetchAccountInfoSubscription.unsubscribe();
    }
    if (this.bookingService.nextButtonClicked) {
      this.bookingService.nextButtonClicked = false;
    }
  }
  loyalNumberMandatoryOrNot(eIndex,hotel){
    let loyalNumberMandatory = false;
    if(this.pageMode == 'emailflowAgent' && Object.keys(hotel).includes('rateDetail') && Object.keys(hotel.rateDetail).includes('loyaltyNumberMandatory') && hotel.rateDetail.loyaltyNumberMandatory){
    loyalNumberMandatory = hotel.rateDetail.loyaltyNumberMandatory;
    }else{
      loyalNumberMandatory = hotel.loyaltyNumberMandatory;
    }
      if (hotel && loyalNumberMandatory) {
        let item = this.tripDetails[eIndex];
          if (item.type === 'hotel' && item.hlnno && item.hlnno.length > 0) {
            if ((item.hlnno[0].hotel_loyality_number == null || item.hlnno[0].hotel_loyality_number.trim() == '')) {
              return true;
            }
          }
      }
    return false;
  }
public allLoyaltyNumberMandatoryFieldsAreValidOrNOt(){
  for(let eIndex= 0; eIndex <this.eventList.length;eIndex++){
    let event = this.eventList[eIndex];
    if(event.getType() == 'hotel'){
      for(let hotelIndex = 0; hotelIndex< event.hotelOptions.length;hotelIndex++){
        let hotel = event.hotelOptions[hotelIndex];
        if(this.loyalNumberMandatoryOrNot(eIndex,hotel)){
          this.showLoyaltyNumberValidation = true;
          return false;
        }
      }
    }
  }
  return true;
}
policyChangeAfterCreditsRemoved(){
  for(let item of this.eventList){
    if(item.type==='flight'){
       item.options[0].flights[0].hops[0].flightHighlights.withinPolicy=false;;

    }
  }
}
removeCredits(){
  for(let item of this.eventList){
    if(item.type==='flight'){
     delete  item.options[0].travelCreditsInfo;

    }
  }
}  
}
