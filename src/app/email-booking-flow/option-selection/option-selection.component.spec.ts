import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { OptionSelectionComponent } from './option-selection.component';

describe('OptionSelectionComponent', () => {
  let component: OptionSelectionComponent;
  let fixture: ComponentFixture<OptionSelectionComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [OptionSelectionComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(OptionSelectionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
