<section class="content" style="z-index: 8;">
    <div class="container">
        <div class="page-content">
            <!-- <div *ngIf="!this.isMobile" class="visible-xs  d-md-none backLink">
                <a *ngIf="!(addCardMode) || (bookRequestProgress) " (click)="goBack()" class="link-primary"
                    href="javascript:void(0);">{{'paymentDetails.GOBack' | translate}}</a>
            </div> -->
            <div *ngIf="this.isMobile" class="traveller-detail-top-navigation-bar">
                    {{'paymentDetails.Payment' | translate}}
            </div>
            <div class="card">
                <div class="">
                    <div class="card-inner" style="display: none;">
                        <div *ngIf="this.showPaymentDeatils">
                            <div *ngIf="!this.isMobile" class="card-header">
                                <h3>{{'paymentDetails.Payment' | translate}}</h3>
                            </div>
                            <div class="card-body">
                                <div class="payment-options">

                                    <div *ngFor="let cardInfo of getCardsModifiedByStripe(); let rIndex = index;"
                                        class="payment-option-section">
                                        <span *ngIf="!cardInfo.markedDeleted">
                                            <img class="inlineblock_m" style="width:25px;"
                                                src="https://s3.amazonaws.com/images.biztravel.ai/cc/{{cardInfo.brand}}.svg" />
                                            <span style="font-size:.75em; "
                                                class="inlineblock_m">{{cardInfo.brand}}-{{cardInfo.last4}}</span>
                                            <a class="delete-icon" href="javascript:void(0);"
                                                (click)="confirmModifiedByStripeCardDeleteModel(rIndex)">
                                                <!-- onclick="showModal('#deleteConfirm'); setDeleteId(this);"> -->
                                                <img src="assets/images/delete copy.svg" />
                                            </a>
                                            <span
                                                style="margin-left: 5px;font-size:.70em; color:red;">{{'paymentDetails.ThiscardisexpiredPleaseusedifferentcard'
                                                | translate}}</span>
                                        </span>
                                    </div>







                                    <div *ngIf="this.isLoggedIn() && getCardList() == null || getCardList().length == 0"
                                        class="payment-option-section">
                                        <div *ngIf="showBillToCompany() && this.showBillTocompany"
                                            class="payment-option-section">
                                            <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect"
                                                for="patmentMethod0">
                                                <input type="radio" id="patmentMethod0" (click)="setBillToCompany()"
                                                    [checked]="true" class="mdl-radio__button" name="paymentMethod">
                                                <span class="mdl-radio__label">
                                                    <span class="inlineblock_m">{{'paymentDetails.BillToCompany' |
                                                        translate}}</span>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                    <div *ngFor="let cardInfo of getCardList(); let rIndex = index;"
                                        class="payment-option-section">
                                        <div *ngIf="rIndex == 0" class="payment-option-section">
                                            <div *ngIf="showBillToCompany()&& this.showBillTocompany"
                                                class="payment-option-section">
                                                <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect"
                                                    for="patmentMethod0">
                                                    <input type="radio" id="patmentMethod0" (click)="setBillToCompany()"
                                                        [checked]="true" class="mdl-radio__button" name="paymentMethod">
                                                    <span class="mdl-radio__label">
                                                        <span class="inlineblock_m">{{'paymentDetails.BillToCompany' |
                                                            translate}}</span>
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                        <div *ngIf="getUserAllowed() && isUserSelectedUATPSpecificAccountCode()">
                                            <label *ngIf="!cardInfo.markedDeleted"
                                                class="mdl-radio mdl-js-radio mdl-js-ripple-effect"
                                                for="patmentMethod{{rIndex+1}}">
                                                <input type="radio" id="patmentMethod{{rIndex+1}}"
                                                    (click)="setCardIndex(rIndex)"
                                                    [checked]="rIndex === selectedCardIndex" class="mdl-radio__button"
                                                    name="paymentMethod">
                                                <span class="mdl-radio__label">
                                                    <img class="inlineblock_m"
                                                        src="https://s3.amazonaws.com/images.biztravel.ai/cc/{{cardInfo.brand}}.svg" />
                                                    <span
                                                        class="inlineblock_m">{{cardInfo.brand}}-{{cardInfo.last4}}</span>
                                                </span>
                                            </label>
                                            <a *ngIf="(!cardInfo.markedDeleted && rIndex > 0 && !bookRequestProgress)"
                                                class="delete-icon" href="javascript:void(0);"
                                                (click)="confirmCardDeleteModel(rIndex)">
                                                <!-- onclick="showModal('#deleteConfirm'); setDeleteId(this);"> -->
                                                <img src="assets/images/delete copy.svg" />
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="!bookRequestProgress" class="add-card-div">
                                    <div *ngIf="!addCardMode && getUserAllowed()  && isUserSelectedUATPSpecificAccountCode()" class="add-card-link">
                                        <a class="link-primary" href="javascript:void(0);" onclick="addCardForm(this);"
                                            (click)="setCardMode()">
                                            <i class="fa fa-plus link-icon"></i>
                                            <span class="link-text"
                                                style="margin-left: 5px;">{{isLoggedIn()?('paymentDetails.AddNewcard' |
                                                translate):('paymentDetails.Newcard' | translate)}}</span>
                                        </a>
                                    </div>
                                    <add-card-widget *ngIf="addCardMode" [loggedIn]="isLoggedIn()"
                                        [njoySpecificBuild]="this.isNjoySpecificRelease"
                                        (goBackEmitter)='handleBackFromAddCard($event)'></add-card-widget>
                                </div>
                                <div *ngIf="allowGallopCashUsage()" class="walletCashDiv">
                                    <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect"
                                        for="gallopCashCheckbox">
                                        <input
                                            [disabled]="addCardMode || getGallopCash() < 1 || paymentMethod === 'BILL_TO_COMPANY'"
                                            onchange="toggleGallopCashInput(this);" type="checkbox"
                                            id="gallopCashCheckbox" name="gallopCashCheckbox"
                                            class="mdl-checkbox__input">
                                        <span class="mdl-checkbox__label">{{'paymentDetails.ApplyGallopCash' |
                                            translate}} ({{getGallopCash() |
                                            currency: getCurrencySymbol(currencyCode): "code" :'1.2-2'}}
                                            {{'paymentDetails.available' | translate}})</span>
                                    </label>
                                    <div [hidden]="!isLoggedIn()" class="field-input">
                                        <input [formControl]="toUseGallopCashControl" [(ngModel)]="gallopCashApplied"
                                            (ngModelChange)="calculateTotal($event)" class="gallop-cash-input disabled"
                                            type="number" placeholder="$00.00" value="0.00" />
                                        <div *ngIf="(toUseGallopCashControl.touched || toUseGallopCashControl.dirty) && toUseGallopCashControl.hasError('invalidAmount')"
                                            class="payment-msg" style="color: red">
                                            {{'paymentDetails.Pleaseentervalidamountuptoonly' | translate}}
                                            {{getGallopCash() | currency : getCurrencySymbol(currencyCode) : "code" :
                                            '1.0-0'}}.
                                        </div>
                                        <div *ngIf="(toUseGallopCashControl.touched || toUseGallopCashControl.dirty) && toUseGallopCashControl.hasError('exceedingPayable')"
                                            class="payment-msg" style="color: red">
                                            {{'paymentDetails.GallopCashcantbeusedmorethantotalamount' | translate}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div *ngIf="!this.isMobile" class="hidden-xs  d-md-block d-sm-none d-none">
                            <a *ngIf="!(addCardMode || bookRequestProgress)" (click)="goBack()" class="link-primary"
                                href="javascript:void(0);">{{'paymentDetails.GOBack' | translate}}</a>
                        </div> -->
                    </div>
                    <div style="width: 100%;">
                        <div class="card small-card">
                            <div class="row" >
                                  
                                        <div class="col-lg-8 col-md-8 col-sm-12" *ngIf="all_tagset && all_tagset.length > 0" >
                                            
                                                <app-dropdown [dropDownopen]="dropDownopen" [paymentDetailsPage]="true" [tagset]="all_tagset" [selectedTag]="selectedTag" [tagShow]="all_tags" [changing]="changingValue" [newTagSetsArray]="new_tagSets_array" (goBackDropDownOptions)="handleResponseFromDropdwn($event)"></app-dropdown>
                                          
                                    
                                    </div>
                                    <div class="col-lg-4 col-md-4 col-sm-12">
                                    <table class="payment-table" cellpadding="0" cellspacing="0">
                                            <tr *ngIf="showExpensifyField() && this.paymentMethod === 'PERSONAL_CARD' && false"
                                                style="display: flex;justify-content: space-between;margin-top:10px;">
                                                <td class="text-left" >{{'paymentDetails.Submitreceiptto' |
                                                    translate}} <span
                                                        style="font-family: var(--globalFontfamilyr);font-weight: bold;">{{expenseProviders()}}</span></td>
                                                <td class="switch-ui" style="">
                                                    <ui-switch color="gray" [(ngModel)]="switchon" [checked]="switchon"
                                                        (change)="expenseOptionSelected($event)" checkedLabel="{{ 'cards.ON' | translate }}"
                                                        uncheckedLabel="{{ 'cards.OFF' | translate }}"></ui-switch>
        
                                                </td>
                                        </table>
                                        </div>
                            </div>
                            <div class="row">
                                <label>{{'paymentDetails.Travelpurpose'| translate }}:</label>
                                    <div style="margin-top: 0px;position: relative;width: 100%;"> 
                                            <textarea placeholder="" maxlength="512" [(ngModel)]="messageForAdmin" [disabled]="this.bookRequestProgress"
                                            class="modal-textarea input-textfield"  (input)="messageForadmin($event.target.value)" 
                                            style="margin-top: 0px !important;height: 90px !important;color:black !important; line-height:18px;position: relative;padding-right: 55px;padding-top: 20px;"
                                            placeholder="{{'paymentDetails.Explainthepurposeofthistripinfewwords' | translate}}"
                                            ></textarea>
                                            <span *ngIf="messageForAdmin" class="showNumber">
                                                    {{messageForAdmin.length}}/512
                                                  </span>
                                            </div>   
                                            <div *ngIf="messageError"id="error1" class="error">
                                                    {{'personal.Thisfieldisrequired' | translate}}
                                            </div>  
                            </div>
                            <div class="row" >
                                    <div *ngIf="!this.isMobile" class="col-lg-8 col-sm-12">
                                            <div class="card-header" *ngIf="this.billingItemArray && this.billingItemArray.length===1 && this.billingItemArray[0].type==='hotel'">
                                                    <h3   style="visibility: hidden;"
                                                    *ngIf="this.billingItemArray  && this.billingItemArray.length===1 &&  this.billingItemArray[0].number > 1 ||  this.billingItemArray[0].type==='flight' || hasPrepaidItem">
                                                    {{'paymentDetails.Paymentdue' | translate}}</h3>
                                                <h3 style="visibility: hidden;"
                                                    *ngIf="this.billingItemArray && this.billingItemArray.length===1  && this.billingItemArray[0].number===1 &&  this.billingItemArray[0].type!=='flight' ">
                                                   {{'paymentDetails.SummaryofCharges' | translate}}</h3>
                                               
                                                </div>
                                               
                                            <div class="card-body">
                                                <div *ngFor="let billingItem of this.billingItemArray;let i=index">
                                                    <table class="{{priceLineHotel ? 'payment-table1':'payment-table'}}" cellpadding="0"
                                                        cellspacing="0">
                                                        <ng-container
                                                            *ngIf="this.billingItemArray  && billingItem.type==='cars'">
                                                            <tr *ngIf="i===0">
                                                                    <td colspan=2>
                                                                        <div class="text-left"
                                                                            style="margin-top:18px;margin-bottom: 0px;font-size: 12px;">
                                                                            <span style="font-size:12px !important;margin-left: 2px;margin-right: 2px;">{{'paymentDetails.ByclickingBook,Iagreetothe' | translate}}</span>
                                                                           
                                                                           
                                                                            
                                                                           
                                                                            <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                               
                                                                                attr.data-track="HotelTermsAndConditions"
                                                                                href="https://routespring.com/tos.html?&dummy=/TripItAPI/"
                                                                                target="_blank">{{'paymentDetails.TermsandConditions' | translate}}</a>
                                                                                <span style="margin-left: 2px;margin-right: 2px;"> {{'paymentDetails.and' | translate}}</span>
                                                                            <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                               
                                                                                href="https://routespring.com/privacy.html?&dummy=/TripItAPI/"
                                                                                attr.data-track="HotelPrivacyPolicy" target="_blank">
                                                                                {{'paymentDetails.PrivacyPolicy' | translate}}</a>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                    
                                                            <div *ngIf="this.billingItemArray.length  ===1 && isCarAvailable()" style="margin-top:0px;font-size: 13px !important;"> {{'paymentDetails.Thisistotalestimatedcostofyourcarrentalreservation.Actualcostmayvarydependingonspecialrequestanditempurchasedatthecounter.' | translate}}</div>
                                                        </ng-container>
                                                        <ng-container
                                                            *ngIf="this.billingItemArray && billingItem.type==='hotel'">
                                                            <div >
                                                               
                                                                <tr *ngIf="i===0">
                                                                    <td colspan=2>
                                                                        <div class="text-left"
                                                                            style="margin-top:18px;margin-bottom: 10px;font-size: 12px;">
                                                                            <span style="font-size:12px !important;margin-left: 2px;margin-right: 2px;">{{'paymentDetails.ByclickingBook,Iagreetothe' | translate}}</span>
                                                                            <a *ngIf="billingItem.addPolicy"
                                                                                style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;cursor: pointer;"
                                                                                attr.data-track="HotelBookingConditions"
                                                                                (click)="showModal(policyDetails)">{{'paymentDetails.BookingConditions' | translate}}</a>
                                                                            <span *ngIf="billingItem.addPolicy">,</span>
                                                                            <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                *ngIf="billingItem.source==='PriceLine'"
                                                                                attr.data-track="HotelTermsAndConditions"
                                                                                href="https://secure.rezserver.com/hotels/help/terms/?refid=8787&dummy=/TripItAPI/"
                                                                                target="_blank">{{'paymentDetails.TermsandConditions' | translate}} </a>
                                                                            <span *ngIf="billingItem.source==='PriceLine'">  {{'paymentDetails.and' | translate}} </span>
                                                                            <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                *ngIf="billingItem.source==='PriceLine'"
                                                                                href="https://secure.rezserver.com/hotels/help/privacy/?refid=8787&dummy=/TripItAPI/"
                                                                                attr.data-track="HotelPrivacyPolicy" target="_blank">
                                                                                {{'paymentDetails.PrivacyPolicy' | translate}} </a>
                                                                            <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                *ngIf="billingItem.source!=='PriceLine'"
                                                                                attr.data-track="HotelTermsAndConditions"
                                                                                href="https://routespring.com/tos.html?&dummy=/TripItAPI/"
                                                                                target="_blank">{{'paymentDetails.TermsandConditions' | translate}}</a>
                                                                            <span *ngIf="billingItem.source!=='PriceLine'"> {{'paymentDetails.and' | translate}}</span>
                                                                            <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                *ngIf="billingItem.source!=='PriceLine'"
                                                                                href="https://routespring.com/privacy.html?&dummy=/TripItAPI/"
                                                                                attr.data-track="HotelPrivacyPolicy" target="_blank">
                                                                                {{'paymentDetails.PrivacyPolicy' | translate}}</a>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </div>
                                                        </ng-container>
                                                        <div *ngIf="luggageOptions && luggageOptions.length > 0" >
                                                            <app-dropdown [dropDownopen]="dropDownopen" [tagset]="1" [luggageDropDown]="true" [tagShow]="luggageOptions" [changing]="changingValue"  (goBackDropDownOptions)="handleResponseFromDropdwnLuggage($event)"></app-dropdown>
                                                        </div>
                                                        <div *ngIf="returnLuggageOptions && returnLuggageOptions.length > 0" >
                                                            <app-dropdown [dropDownopen]="dropDownopen" [tagset]="1" [luggageDropDown]="true" [tagShow]="returnLuggageOptions" [changing]="changingValue"  (goBackDropDownOptions)="handleResponseFromDropdwnLuggage($event, true)"></app-dropdown>
                                                        </div>
                                                        <div *ngIf="this.billingItemArray  && billingItem.type==='flight'">
                                                                <tr *ngIf="i===0">
                                                                        <td colspan=2>
                                                                            <div class="text-left"
                                                                                style="margin-top:18px;margin-bottom: 10px;font-size: 12px;">
                                                                                <span style="font-size:12px !important;margin-left: 2px;margin-right: 2px;">{{'paymentDetails.ByclickingBook,Iagreetothe' | translate}}</span>
                                                                               
                                                                                <a *ngIf="getAddPolicyitemm()"
                                                                                style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;cursor: pointer;"
                                                                                attr.data-track="HotelBookingConditions"
                                                                                (click)="showModal(policyDetails)">{{'paymentDetails.BookingConditions' | translate}}</a>
                                                                            <span *ngIf="getAddPolicyitemm()">,</span>
                                                                              
                                                                               
                                                                                <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                   
                                                                                    attr.data-track="HotelTermsAndConditions"
                                                                                    href="https://routespring.com/tos.html?&dummy=/TripItAPI/"
                                                                                    target="_blank">{{'paymentDetails.TermsandConditions' | translate}}</a>
                                                                                    <span style="margin-left: 2px;margin-right: 2px;"> {{'paymentDetails.and' | translate}}</span>
                                                                                <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                   
                                                                                    href="https://routespring.com/privacy.html?&dummy=/TripItAPI/"
                                                                                    attr.data-track="HotelPrivacyPolicy" target="_blank">
                                                                                    {{'paymentDetails.PrivacyPolicy' | translate}}</a>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                        
                                                        </div>   
                                                       
                                                   
                                                    </table>
                                                   
                                                    <div [hidden]="hideConfirmationCheckBoxes() || this.paymentMethod === 'BILL_TO_COMPANY'"
                                                        class="payment-msg">
                                                        <div class="payment-option-section">
                                                            <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect"
                                                                for="travelApprovedRadio">
                                                                <input type="radio" id="travelApprovedRadio"
                                                                    (click)="setTravelApprovedRadio(true)" class="mdl-radio__button"
                                                                    name="travelDeclaration">
                                                                <span class="mdl-radio__label">
                                                                    <span
                                                                        class="inlineblock_m">{{'paymentDetails.Mybusinesstravelisapproved'
                                                                        | translate}}</span>
                                                                </span>
                                                            </label>
                                                            <!-- <a class="delete-icon" href="javascript:void(0);" onclick="showModal('#deleteConfirm'); setDeleteId(this);">
                                                                <img src="images/delete copy.svg" />
                                                            </a> -->
                                                        </div>
                                                        <div class="payment-option-section">
                                                            <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect"
                                                                for="personalTravelRadio">
                                                                <input type="radio" id="personalTravelRadio"
                                                                    (click)="setTravelApprovedRadio(false)" class="mdl-radio__button"
                                                                    name="travelDeclaration">
                                                                <span class="mdl-radio__label">
                                                                    <span
                                                                        class="inlineblock_m">{{'paymentDetails.Thisispersonaltravelbooking'
                                                                        | translate}}</span>
                                                                </span>
                                                            </label>
                                                            <!-- <a class="delete-icon" href="javascript:void(0);" onclick="showModal('#deleteConfirm'); setDeleteId(this);">
                                                                <img src="images/delete copy.svg" />
                                                            </a> -->
                                                        </div>
                                                        <div *ngIf="!travelApprovedCheck.value && (travelApprovedCheck.touched || travelApprovedCheck.dirty)"
                                                            class="error">{{'paymentDetails.Pleaseselectoneoftheabove' | translate}}
                                                        </div>
                                                    </div>
                                                    <div [hidden]="hideConfirmationCheckBoxes() || !(this.paymentMethod === 'BILL_TO_COMPANY')"
                                                        class="payment-msg">
                    
                                                        <label
                                                            class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect travelApprovedCheck travel_approval"
                                                            for="travelApprovedCheck">
                                                            <input [formControl]="travelApprovedCheck" type="checkbox"
                                                                id="travelApprovedCheck" name="travelApprovedCheck" value="0"
                                                                class="mdl-checkbox__input">
                                                            <span class="mdl-checkbox__label">{{'paymentDetails.Mybusinesstravelisapproved'
                                                                | translate}}</span>
                                                        </label>
                                                        <div *ngIf="!travelApprovedCheck.value && (travelApprovedCheck.touched || travelApprovedCheck.dirty)"
                                                            class="error">{{'paymentDetails.Pleaseconfirmyourtravelapproval' | translate}}
                                                        </div>
                                                    </div>
                                                    <div *ngIf="showGallopCashBack()" class="payment-msg">
                                                        {{'paymentDetails.Youwillearn' | translate}} {{getGallopCashBack() | currency :
                                                        getCurrencySymbol(currencyCode): "code" :'1.0-0'}}
                                                        {{'paymentDetails.GallopCashonthisbooking' | translate}}
                                                    </div>
                                         </div>
                                         <div *ngIf="this.billingItemArray.length  >1 && isCarAvailable()" >
                                                <div style="margin-top:0px;font-size: 13px !important;"> {{'paymentDetails.ThisincludesthetotalestimatedcostofyourcarrentalreservationActualcostmayvarydependingonspecialrequestanditempurchasedatthecounter' | translate}}</div>
                                         </div>
                                         <!-- <div  class="hidden-xs  d-md-block d-sm-none d-none">
                                                <a *ngIf="!(addCardMode || bookRequestProgress)" (click)="goBack()" class="link-primary"
                                                    href="javascript:void(0);">{{'paymentDetails.GOBack' | translate}}</a>
                                            </div> -->
                                            </div>
                                    </div>
                                         <div class="col-lg-4 col-sm-12" style="
                                         ">
                                         <div *ngFor="let billingItem of this.billingItemArray;index as i">
                                            <div  *ngIf="false && this.getTotalAdditionalFees() != null && i ==0">
                                                <tr  *ngFor="let fee of getTotalAdditionalFeesData()" style="display: flex;justify-content: space-between;padding: 0px;">
                                                    <td class="text-left">{{fee.name}}</td>
                                                    <td class="text-right">
                                                        {{fee.totalAmount
                                                            | currency:
                                                            getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}
                                                    </td>
                                                </tr>
                                            </div>
                                                <ng-container
                                                *ngIf="this.billingItemArray && billingItem.type==='hotel' && billingItem.number===1">
                                                <div  style="text-align: center;" *ngIf="this.billingItemArray && this.billingItemArray.length===1">
                                                       
                                                           
                                                         
                                                          
                                                          
                                                          
                                                           
                                                           
                                                            
                                                            <hr style="visibility: hidden !important;">
                                                        
                                                        <tr [ngStyle]="getUiForTotalAMount()">
                                                                <td class="total-payment-amount mobileTotal">
                                                                        {{'paymentDetails.Total' | translate}}
                                                                    </td>
                                                                    <span style="margin-left:20px;"></span>
                                                                        <td class="total-payment-amount">
                                                                                <b
                                                                                style="margin-left: 5px;"><span *ngIf="!this.isMobile">:</span> {{getTotalCharge(billingItem)
                                                                                | currency:
                                                                                getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</b> 
                                                                                <br *ngIf="getTotalResortFee() > 0 "><span *ngIf="getTotalResortFee() > 0 " style="color: black; font-size: 12px; line-height: 12px;font-family: var(--globalFontfamilyr);font-weight: normal;"> ({{'policy.includingresortfee' | translate }} {{getTotalResortFee() | currency:
                                                                                        getCurrencySymbol(currencyCode): "code" : '1.2-2' }} )</span>
                                                                        </td>
                                                                   
                                                                   
                                                                    
                                                                
                                                            </tr>
                                                            
                                                    </div>
                                                </ng-container>
                                                <ng-container
                                                *ngIf="this.billingItemArray &&  billingItem.type==='cars' && billingItem.number===1">
                                                <div  style="text-align: center" *ngIf="this.billingItemArray && this.billingItemArray.length===1">
                                                        <tr [ngStyle]="getUiForTotalAMount()">
                                                            <td class="total-payment-amount mobileTotal"
                                                                ><span style="margin-right: 0px; "
                                                                *ngIf="billingItem.paymentType ==='POSTPAID'" >
                                                                    <b style="line-height:18px !important;margin-right: 0px; width: 50%;text-align: left !important;">{{getCounterMsg(true)}}</b></span><span style="margin-right: 5px; "
                                                                    *ngIf="billingItem.paymentType !=='POSTPAID'" >
                                                                    <b style="line-height:18px !important;margin-right: 0px;text-align: left !important;"> {{'paymentDetails.Total' | translate}}</b></span></td>
                                                                    <span style="margin-left:20px;"></span>
                                                            <td class="total-payment-amount"
                                                                style="margin-left:0px !important;white-space: nowrap;" [ngStyle]="{'line-height:' : this.showPaymentDeatils ? '18px':'24px'}">
                                                                <b><span *ngIf="!this.isMobile">:</span> {{getDueNow(billingItem) | currency:
                                                                    getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</b>
                                                            </td>
                                                        </tr>
                                                        
                                                    </div>
                                                
                                                       
                                                </ng-container>
                                                <ng-container *ngIf="hasPrepaidItem">
                                                        
                                                        <tr *ngIf="gallopCashApplied>0"
                                                            style="display: flex;justify-content: space-between;">
                                                            <td class="text-left">{{'paymentDetails.GallopCash' | translate}}</td>
                                                            <td class="text-right">- {{gallopCashApplied | currency:
                                                                getCurrencySymbol(currencyCode):'code':'1.2-2'}}</td>
                                                        </tr>
                                                        <tr *ngIf="this.selectedLuggage" style="display: flex;justify-content: space-between;">
                                                            <td class="text-left">{{this.selectedLuggage.displayText}} ({{this.selectedLuggage.leg}})</td>
                                                           
                                                            <td class="text-right">{{this.selectedLuggage.amount | currency:
                                                                getCurrencySymbol(this.selectedLuggage.currency): "code" : '1.2-2' }}</td>
                                                        </tr>
                                                        <tr *ngIf="this.selectedReturnLuggage" style="display: flex;justify-content: space-between;">
                                                            <td class="text-left">{{this.selectedReturnLuggage.displayText}} ({{this.selectedReturnLuggage.leg}})</td>
                                                           
                                                            <td class="text-right">{{this.selectedReturnLuggage.amount | currency:
                                                                getCurrencySymbol(this.selectedReturnLuggage.currency): "code" : '1.2-2' }}</td>
                                                        </tr>
                                                        <div [ngStyle]="getUiForTotalAMount()" *ngIf="this.billingItemArray && this.billingItemArray.length===1">
                                                            <span class="total-payment-amount mobileTotal" style="margin-right: 5px;">{{'paymentDetails.Total' | translate}}</span>
                                                            <span style="display: none;" class="text-left total-payment-amount">
                                                                {{'paymentDetails.DueToday' | translate}}</span><span style="margin-left:20px;"></span>
                                                            <span class="total-payment-amount"><span *ngIf="!this.isMobile">:</span> {{getTotalAmount(billingItemArray[0]) | currency:
                                                                getCurrencySymbol(currencyCode): "code" : '1.2-2' }}</span>
                                                            </div>
                                                    </ng-container>
                                                    <ng-container *ngIf="totalPayableAtHotel>0 && false">
                                                        <tr style="display: flex;justify-content: space-between;">
                                                            <td class="text-left total-payment-label">{{'paymentDetails.Payathotel' |
                                                                translate}}</td>
                                                            <td class="text-right total-payment-label">{{totalPayableAtHotel | currency:
                                                                getCurrencySymbol(currencyCode): "code" : '1.2-2' }}</td>
                                                        </tr>
                                                    </ng-container>
                                                  
                                                    <tr *ngIf="hasTravelportOrSabreHotel() && this.showBillTocompany && i===0">
                                                        <td colspan="2" style="color:darkgrey;font-size:80%;">
                                                           {{'paymentDetails.Travelermayberequiredtocarrythiscardduringhotelcheck-in.'
                                                            | translate}}
                                                        </td>
                                                    </tr>
                                                   
                                         </div>
                                         <div [ngStyle]="getUiForTotalAMount()" *ngIf="this.billingItemArray && this.billingItemArray.length > 1">
                                                <span class="total-payment-amount mobileTotal" style="margin-right: 5px;">{{'paymentDetails.Total' | translate}}</span>
                                                <span style="display: none;" class="text-left total-payment-amount">
                                                    {{'paymentDetails.DueToday' | translate}}</span><span style="margin-left:20px;"></span>
                                                <span class="total-payment-amount"><span *ngIf="!this.isMobile">:</span> {{getTotalAmountForMultibooking() | currency:
                                                    getCurrencySymbol(currencyCode): "code" : '1.2-2' }}
                                                    <br *ngIf="getTotalResortFee() > 0 "><span *ngIf="getTotalResortFee() > 0 " style="color: black; font-size: 12px; line-height: 12px;font-family: var(--globalFontfamilyr);font-weight: normal;"> ({{'policy.includingresortfee' | translate }} {{getTotalResortFee() | currency:
                                                            getCurrencySymbol(currencyCode): "code" : '1.2-2' }} )</span></span>
                                                   
                                                </div>
                                                <div class="button-container payment-button-container hidden-xs">
                                                    <ng-container *ngIf="!bookRequestProgress">
                                                        <button 
                                                            [disabled]="addCardMode || ((toUseGallopCashControl.touched || toUseGallopCashControl.dirt) && toUseGallopCashControl.invalid)"
                                                            class="button btn-secondary1" (click)="confirmBookAndPay(approvalSuccessModal,bookingResponseModal)">{{ this.getBookButtonLabel() }}</button>
                                                    </ng-container>
                                                    <ng-container *ngIf="bookRequestProgress">
                                                        <button class="button btn-secondary1">{{'paymentDetails.Pleasewait' | translate}}</button>
                                                        <div class="loaderClass">
                                                            <loader-dots class="loaderAlign"></loader-dots>
                                                        </div>
                                                    </ng-container>
                                                </div>
                                            </div>
                                            <div *ngIf="this.isMobile" class="col-lg-8 col-sm-12">
                                                    <div class="card-header" *ngIf="this.billingItemArray && this.billingItemArray.length===1 && this.billingItemArray[0].type==='hotel'">
                                                            <h3   style="visibility: hidden;"
                                                            *ngIf="this.billingItemArray &&  this.billingItemArray[0].number > 1 ||  this.billingItemArray[0].type==='flight' || hasPrepaidItem">
                                                            {{'paymentDetails.Paymentdue' | translate}}</h3>
                                                        <h3 style="visibility: hidden;"
                                                            *ngIf="this.billingItemArray   && this.billingItemArray[0].number===1 &&  this.billingItemArray[0].type!=='flight' ">
                                                           {{'paymentDetails.SummaryofCharges' | translate}}</h3>
                                                       
                                                        </div>
                                                       
                                                    <div class="card-body">
                                                            <div *ngFor="let billingItem of this.billingItemArray;let i=index">
                                                            <table class="{{priceLineHotel ? 'payment-table1':'payment-table'}}" cellpadding="0"
                                                                cellspacing="0">
                                                                <ng-container
                                                                    *ngIf="this.billingItemArray  && billingItem.type==='cars'">
                                                                    <tr *ngIf="i===0">
                                                                            <td colspan=2>
                                                                                <div class="text-left"
                                                                                    style="margin-top:18px;margin-bottom: 10px;font-size: 12px;">
                                                                                    <span style="font-size:12px !important">{{'paymentDetails.ByclickingBook,Iagreetothe' | translate}}</span>
                                                                                   
                                                                                   
                                                                                    
                                                                                   
                                                                                    <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                       
                                                                                        attr.data-track="HotelTermsAndConditions"
                                                                                        href="https://routespring.com/tos.html?&dummy=/TripItAPI/"
                                                                                        target="_blank">{{'paymentDetails.TermsandConditions' | translate}}</a>
                                                                                        <span style="margin-left: 2px;margin-right: 2px;"> {{'paymentDetails.and' | translate}}</span>
                                                                                    <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                       
                                                                                        href="https://routespring.com/privacy.html?&dummy=/TripItAPI/"
                                                                                        attr.data-track="HotelPrivacyPolicy" target="_blank">
                                                                                        {{'paymentDetails.PrivacyPolicy' | translate}}</a>
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                            
                                                                    <div *ngIf="this.billingItemArray.length  ===1 && isCarAvailable()"style="margin-top:30px;font-size: 13px !important;"> {{'paymentDetails.Thisistotalestimatedcostofyourcarrentalreservation.Actualcostmayvarydependingonspecialrequestanditempurchasedatthecounter.' | translate}}</div>
                                                                </ng-container>
                                                                <ng-container
                                                                    *ngIf="this.billingItemArray  && billingItem.type==='hotel'">
                                                                    <div >
                                                                       
                                                                        <tr *ngIf="i===0">
                                                                            <td colspan=2>
                                                                                <div class="text-left"
                                                                                    style="margin-top:18px;margin-bottom: 10px;font-size: 12px;">
                                                                                    <span style="font-size:12px !important">{{'paymentDetails.ByclickingBook,Iagreetothe' | translate}}</span>
                                                                                    <a *ngIf="billingItem.addPolicy"
                                                                                        style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;cursor: pointer;"
                                                                                        attr.data-track="HotelBookingConditions"
                                                                                        (click)="showModal(policyDetails)">{{'paymentDetails.BookingConditions' | translate}}</a>
                                                                                    <span *ngIf="billingItem.addPolicy">,</span>
                                                                                    <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                        *ngIf="billingItem.source==='PriceLine'"
                                                                                        attr.data-track="HotelTermsAndConditions"
                                                                                        href="https://secure.rezserver.com/hotels/help/terms/?refid=8787&dummy=/TripItAPI/"
                                                                                        target="_blank">{{'paymentDetails.TermsandConditions' | translate}} </a>
                                                                                    <span *ngIf="billingItem.source==='PriceLine'">  {{'paymentDetails.and' | translate}} </span>
                                                                                    <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                        *ngIf="billingItem.source==='PriceLine'"
                                                                                        href="https://secure.rezserver.com/hotels/help/privacy/?refid=8787&dummy=/TripItAPI/"
                                                                                        attr.data-track="HotelPrivacyPolicy" target="_blank">
                                                                                        {{'paymentDetails.PrivacyPolicy' | translate}} </a>
                                                                                    <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                        *ngIf="billingItem.source!=='PriceLine'"
                                                                                        attr.data-track="HotelTermsAndConditions"
                                                                                        href="https://routespring.com/tos.html?&dummy=/TripItAPI/"
                                                                                        target="_blank">{{'paymentDetails.TermsandConditions' | translate}}</a>
                                                                                    <span *ngIf="billingItem.source!=='PriceLine'"> {{'paymentDetails.and' | translate}}</span>
                                                                                    <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                        *ngIf="billingItem.source!=='PriceLine'"
                                                                                        href="https://routespring.com/privacy.html?&dummy=/TripItAPI/"
                                                                                        attr.data-track="HotelPrivacyPolicy" target="_blank">
                                                                                        {{'paymentDetails.PrivacyPolicy' | translate}}</a>
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    </div>
                                                                </ng-container>
                                                                <div *ngIf="luggageOptions && luggageOptions.length > 0" >
                                                                    <app-dropdown [dropDownopen]="dropDownopen" [tagset]="1" [luggageDropDown]="true" [tagShow]="luggageOptions" [changing]="changingValue"  (goBackDropDownOptions)="handleResponseFromDropdwnLuggage($event)"></app-dropdown>
                                                                </div>
                                                                <div *ngIf="returnLuggageOptions && returnLuggageOptions.length > 0" >
                                                                    <app-dropdown [dropDownopen]="dropDownopen" [tagset]="1" [luggageDropDown]="true" [tagShow]="returnLuggageOptions" [changing]="changingValue"  (goBackDropDownOptions)="handleResponseFromDropdwnLuggage($event, true)"></app-dropdown>
                                                                </div>
                                                                <div *ngIf="this.billingItemArray &&  billingItem.type==='flight'">
                                                                        <tr *ngIf="i===0">
                                                                                <td colspan=2>
                                                                                    <div class="text-left"
                                                                                        style="margin-top:18px;margin-bottom: 10px;font-size: 12px;">
                                                                                        <span style="font-size:12px !important">{{'paymentDetails.ByclickingBook,Iagreetothe' | translate}}</span>
                                                                                       
                                                                                       
                                                                                        
                                                                                       
                                                                                        <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                           
                                                                                            attr.data-track="HotelTermsAndConditions"
                                                                                            href="https://routespring.com/tos.html?&dummy=/TripItAPI/"
                                                                                            target="_blank">{{'paymentDetails.TermsandConditions' | translate}}</a>
                                                                                            <span style="margin-left: 2px;margin-right: 2px;"> {{'paymentDetails.and' | translate}}</span>
                                                                                        <a style="font-family:var(--globalFontfamilyr);font-weight: bold;;text-transform:none !important;font-size:12px;"
                                                                                           
                                                                                            href="https://routespring.com/privacy.html?&dummy=/TripItAPI/"
                                                                                            attr.data-track="HotelPrivacyPolicy" target="_blank">
                                                                                            {{'paymentDetails.PrivacyPolicy' | translate}}</a>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                
                                                                </div>   
                                                                
                                                            </table>
                                                          
                                                            <div [hidden]="hideConfirmationCheckBoxes() || this.paymentMethod === 'BILL_TO_COMPANY'"
                                                                class="payment-msg">
                                                                <div class="payment-option-section">
                                                                    <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect"
                                                                        for="travelApprovedRadio">
                                                                        <input type="radio" id="travelApprovedRadio"
                                                                            (click)="setTravelApprovedRadio(true)" class="mdl-radio__button"
                                                                            name="travelDeclaration">
                                                                        <span class="mdl-radio__label">
                                                                            <span
                                                                                class="inlineblock_m">{{'paymentDetails.Mybusinesstravelisapproved'
                                                                                | translate}}</span>
                                                                        </span>
                                                                    </label>
                                                                    <!-- <a class="delete-icon" href="javascript:void(0);" onclick="showModal('#deleteConfirm'); setDeleteId(this);">
                                                                        <img src="images/delete copy.svg" />
                                                                    </a> -->
                                                                </div>
                                                                <div class="payment-option-section">
                                                                    <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect"
                                                                        for="personalTravelRadio">
                                                                        <input type="radio" id="personalTravelRadio"
                                                                            (click)="setTravelApprovedRadio(false)" class="mdl-radio__button"
                                                                            name="travelDeclaration">
                                                                        <span class="mdl-radio__label">
                                                                            <span
                                                                                class="inlineblock_m">{{'paymentDetails.Thisispersonaltravelbooking'
                                                                                | translate}}</span>
                                                                        </span>
                                                                    </label>
                                                                    <!-- <a class="delete-icon" href="javascript:void(0);" onclick="showModal('#deleteConfirm'); setDeleteId(this);">
                                                                        <img src="images/delete copy.svg" />
                                                                    </a> -->
                                                                </div>
                                                                <div *ngIf="!travelApprovedCheck.value && (travelApprovedCheck.touched || travelApprovedCheck.dirty)"
                                                                    class="error">{{'paymentDetails.Pleaseselectoneoftheabove' | translate}}
                                                                </div>
                                                            </div>
                                                            <div [hidden]="hideConfirmationCheckBoxes() || !(this.paymentMethod === 'BILL_TO_COMPANY')"
                                                                class="payment-msg">
                            
                                                                <label
                                                                    class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect travelApprovedCheck travel_approval"
                                                                    for="travelApprovedCheck">
                                                                    <input [formControl]="travelApprovedCheck" type="checkbox"
                                                                        id="travelApprovedCheck" name="travelApprovedCheck" value="0"
                                                                        class="mdl-checkbox__input">
                                                                    <span class="mdl-checkbox__label">{{'paymentDetails.Mybusinesstravelisapproved'
                                                                        | translate}}</span>
                                                                </label>
                                                                <div *ngIf="!travelApprovedCheck.value && (travelApprovedCheck.touched || travelApprovedCheck.dirty)"
                                                                    class="error">{{'paymentDetails.Pleaseconfirmyourtravelapproval' | translate}}
                                                                </div>
                                                            </div>
                                                            <div *ngIf="showGallopCashBack()" class="payment-msg">
                                                                {{'paymentDetails.Youwillearn' | translate}} {{getGallopCashBack() | currency :
                                                                getCurrencySymbol(currencyCode): "code" :'1.0-0'}}
                                                                {{'paymentDetails.GallopCashonthisbooking' | translate}}
                                                            </div>
                                                 </div>
                                            </div>
                                            <div *ngIf="this.billingItemArray.length  >1 && isCarAvailable()" >
                                                    <div style="margin-top:0px;font-size: 13px !important;"> {{'paymentDetails.ThisincludesthetotalestimatedcostofyourcarrentalreservationActualcostmayvarydependingonspecialrequestanditempurchasedatthecounter' | translate}}</div>
                                             </div>
                                            
                                                 <!-- <div class="hidden-xs">
                                                        <a *ngIf="!(addCardMode || bookRequestProgress)" (click)="goBack()" class="link-primary"
                                                            href="javascript:void(0);">{{'paymentDetails.GOBack' | translate}}</a>
                                                    </div> -->
                                            </div>
                            </div>
                            
                                
                               
                                
                           
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="payment-note">
                <div class="payment-note-inner">
                    Please note that this does not immediately book your itinerary. Once you complete this form, your
                    booking request will be sent to Trip for completion. You card will NOT be charged until this
                    process is completed
                </div>
            </div> -->
            <!-- <div class="button-container payment-button-container visible-xs">
                <button class="button button-primary button-full" onclick="showModal('#confirmBookingModal');">Pay
                    $3,288.00</button>
            </div> -->
        </div>
    </div>
</section>

<ng-template #paymentdetailsModal let-modal>
        <div class="modal-header" style="
       padding: 8px 10px 10px 20px !important;color: #fff;height: 42px;font-size: 14px;font-family: 'apercu-mono';">
       <span
       *ngIf="this.billingItemArray  && this.billingItemArray.length===1 &&  this.billingItemArray[0].number > 1 ||  this.billingItemArray[0].type==='flight' || hasPrepaidItem">
       {{'paymentDetails.Paymentdue' | translate}}</span>
   <span
       *ngIf="this.billingItemArray && this.billingItemArray.length===1  && this.billingItemArray[0].number===1 &&  this.billingItemArray[0].type!=='flight' ">
      {{'paymentDetails.SummaryofCharges' | translate}}</span>

      
                <button type="button" class="close" data-dismiss="modal" (click)="onCancel()">
                    <i class="material-icons" style="color: #fff;">close</i>
                </button>
            </div>
                        <div class="modal-body" style="background-color: #FFF !important;padding:22px 43px; ">
                                <ng-container
                                *ngIf="this.billingItemArray  && this.billingItemArray[this.bookingService.selectedPaymentEventIndex].type==='hotel'">
                                <div *ngFor="let billingItem of this.selectedbillingItem">
                                        <tr style="display: flex;justify-content: space-between;">
                                                <td class="text-left" style="line-height:14px !important; "> {{'paymentDetails.Roomcostpernight' | translate}}:</td>
                                                <td class="text-right" style="line-height:14px !important; ">
                                                    {{billingItem.perNightCharge | currency:
                                                    getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</td>
                                            </tr>
                                            <tr style="display: flex;justify-content: space-between;">
                                                <td class="text-left" style="line-height:14px !important; ">{{'paymentDetails.Nights' | translate}}:</td>
                                                <td class="text-right" style="line-height:14px !important; ">
                                                    {{billingItem.nights}}</td>
                                            </tr>
                                            <tr style="display: flex;justify-content: space-between;">
                                                <td class="text-left" style="line-height:14px !important; "> {{'paymentDetails.Rooms' | translate}}:</td>
                                                <td class="text-right" style="line-height:14px !important; ">
                                                    {{billingItem.rooms}}</td>
                                            </tr>
                                            <tr style="display: flex;justify-content: space-between;">
                                                <td class="text-left" style="line-height:14px !important; ">{{'paymentDetails.Guests' | translate}}:</td>
                                                <td class="text-right" style="line-height:14px !important; ">
                                                    {{billingItem.guest}}</td>
                                            </tr>
                                            <tr style="display: flex;justify-content: space-between;">
                                                <td class="text-left" style="line-height:14px !important; "> {{'paymentDetails.RoomSubtotal' | translate}}:</td>
                                                <td class="text-right" style="line-height:14px !important; ">
                                                    {{getRoomSubtotal(billingItem) | currency:
                                                    getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</td>
                                            </tr>
                                           
                                           
                                                   <tr style="display: flex;justify-content: space-between;">
                                                        <td >{{'paymentDetails.Tax' | translate}}:</td>
                                                        <td class="text-right" style="line-height:14px !important; ">
                                                                {{(getTaxForHotel(billingItem.tax ,billingItem.fee)) | currency:
                                                                    getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</td>
                                                    </tr>
                                                   
                                                    <tr *ngIf="this.billingItemArray[0].type==='hotel' && this.billingItemArray[0].fee!==null"style="display: flex;justify-content: space-between;">
                                                            <td 
                                                            >{{'paymentDetails.Fees' | translate}}:</td>
                                                            <td class="text-right" style="line-height:14px !important; ">
                                                                    {{billingItem.fee}}</td>
                                                        </tr>
                                                       
                                                    
                                            <tr style="display: flex;justify-content: space-between;">
                                                    <td *ngIf="billingItem.source.toLowerCase()==='travelport' && this.userAccountInfoService.confermaStatus"
                                                    class="text-left" style="line-height:14px !important; "><b>{{'paymentDetails.Roomcharges' | translate}}:</b></td>
                                                <td *ngIf="billingItem.source.toLowerCase()==='travelport' &&  !this.userAccountInfoService.confermaStatus"
                                                    class="text-left" style="line-height:14px !important; "><b>{{'paymentDetails.RoomchargesDueathotel' | translate}}:</b></td>
                                                <td *ngIf="billingItem.source.toLowerCase()!=='travelport'"
                                                    class="text-left" style="line-height:14px !important; "><b>{{'paymentDetails.RoomchargesDuenow' | translate}}:</b></td>
                                                <td class="text-right" style="line-height:14px !important; ">
                                                    <b>{{getDueNow(billingItem) | currency:
                                                        getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</b>
                                                </td>
                                            </tr>
                                            <tr *ngIf="getResortFee(billingItem)!==null && getResortFee(billingItem)>0 "
                                                style="display: flex;justify-content: space-between;">
                                                <td class="text-left" style="line-height:14px !important; "><b>{{'paymentDetails.ResortfeeDueatHotel' | translate}}:</b></td>
                                                <td class="text-right" style="line-height:14px !important; ">
                                                    <b>{{getResortFee(billingItem) | currency:
                                                        getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</b>
                                                </td>
                                            </tr>
                                            <hr style="visibility: hidden !important;">
                                        
                                        <tr style="width: 100%;display: flex;
                                        justify-content: space-between;">
                                                <td class="text-left total-payment-amount">
                                                        {{'paymentDetails.TOTALCHARGES' | translate}}
                                                    </td>
                                                        <td class="text-right total-payment-amount">
                                                                <b
                                                                style="font-size:20px !important;">{{getTotalCharge(billingItem)
                                                                | currency:
                                                                getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</b> 
                                                        </td>
                                                   
                                                   
                                                    
                                                
                                            </tr>
                                            <div
                                            style="font-size:12px !important;text-align:center;">
                                            <div *ngIf="getResortFee(billingItem)!==null && getResortFee(billingItem)>0 "
                                                class="col text-center">{{'paymentDetails.Totalhotelchargeswithresortfees' | translate}}</div>
                                            </div>
                                    </div>
                                </ng-container>
                                <ng-container
                                *ngIf="this.billingItemArray  && this.billingItemArray[this.bookingService.selectedPaymentEventIndex].type==='cars'">
                                <div *ngFor="let billingItem of this.selectedbillingItem">
                                        <tr style="display: flex;justify-content: space-between;">
                                            <!-- <td *ngIf="billingItem.tax!==null"class="text-left"style="line-height:14px !important;width:196px !important; ">Daily Rate ({{ billingItem.nights}} x {{getCarPerDayPrice(billingItem) | number : '1.2-2'}} ) :</td> -->
                                            <td *ngIf="billingItem.tax!==null" class="text-left"
                                                style="line-height:14px !important;width:196px !important; "> {{'paymentDetails.BasePrice' | translate}} :</td>
                                            <td *ngIf="billingItem.tax!==null" class="text-right"
                                                style="line-height:14px !important; ">{{ getCarPrice(billingItem) |
                                                currency: getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}
                                            </td>
                                        </tr>
                                       
                                                <tr style="display: flex;justify-content: space-between;">
                                                        <td >{{'paymentDetails.Tax' | translate}}:</td>
                                                        <td class="text-right" style="line-height:14px !important; ">
                                                                {{billingItem.tax | currency: getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</td>
                                                    </tr>
                                                   
                                                   
                                              
                                            
                                       
                                        <tr style="display: flex;justify-content: space-between;">
                                            <td class="text-left total-payment-amount"
                                                style="line-height:14px !important;width:196px !important; "><span
                                                    *ngIf="billingItem.paymentType ==='POSTPAID'" [ngStyle]="{'white-space':!this.isMobile ? 'nowrap':'inherit'}">
                                                    <b>{{getCounterMsg(true)}}:</b></span><span
                                                    *ngIf="billingItem.paymentType !=='POSTPAID'" [ngStyle]="{'white-space':!this.isMobile ? 'nowrap':'inherit'}">
                                                    <b>{{getCounterMsg(false)}}:</b></span></td>
                                            <td class="text-right total-payment-amount"
                                                style="line-height:14px !important; margin-left:5px !important;">
                                                <b>{{getDueNow(billingItem) | currency:
                                                    getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</b>
                                            </td>
                                        </tr>
                                        
                                    </div>
                                
                                       
                                </ng-container>
                                <ng-container *ngIf="hasPrepaidItem">
                                    <div *ngIf="this.billingItemArray  && this.billingItemArray[this.bookingService.selectedPaymentEventIndex].type==='flight'">
                                        <tr *ngFor="let billingItem of this.selectedbillingItem"
                                            style="display: flex;justify-content: space-between;">
                                            <ng-container *ngIf="billingItem.paymentType === 'PREPAID' ">
                                                <td class="text-left">{{billingItem.particular}}</td>
                                                <td class="text-right">{{gettotalAmountOnPopup(billingItem.amount) | currency:
                                                    getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</td>
                                            </ng-container>
                                        </tr>
                                        <ng-container *ngIf="isCreditAppliedInAnyItem(this.selectedbillingItem[0].credit, this.selectedbillingItem[0].amount)">
                                            <tr style="display: flex;justify-content: space-between;">

                                                <td class="text-left">{{'paymentDetails.CreditsUsed' | translate}}</td>
                                                <td class="text-right" style="white-space: nowrap !important;">
                                                    -{{getCreditAmount(this.selectedbillingItem[0]) | currency:
                                                    getCurrencySymbol(currencyCode): "code" : '1.2-2' }}</td>
                                            </tr>
                                            <hr>
                                        </ng-container>
                                        <ng-container *ngIf="isCreditAppliedInAnyItem(this.selectedbillingItem[0].credit, this.selectedbillingItem[0].amount)">
                                            <tr *ngFor = "let item of this.selectedbillingItem[0].credit"
                                                style="display: flex;justify-content: space-between;">

                                                <td *ngIf="isCreditApplied(item)" class="text-left">{{'paymentDetails.TravelCredits' | translate}} ({{item.vendorReference}})</td>
                                                <td *ngIf="isCreditApplied(item)" class="text-right" style="white-space: nowrap !important;">
                                                    -{{ getCreditPrice(item)| currency: getCurrencySymbol(currencyCode):
                                                    "code" : '1.2-2' }}</td>
                                            </tr>
                                            <hr>
                                        </ng-container>
                                        <tr *ngIf="gallopCashApplied>0"
                                            style="display: flex;justify-content: space-between;">
                                            <td class="text-left">{{'paymentDetails.GallopCash' | translate}}</td>
                                            <td class="text-right">- {{gallopCashApplied | currency:
                                                getCurrencySymbol(currencyCode):'code':'1.2-2'}}</td>
                                        </tr>
                                        <tr *ngIf="this.selectedLuggage" style="display: flex;justify-content: space-between;">
                                            <td class="text-left">{{this.selectedLuggage.displayText}} ({{this.selectedLuggage.leg}})</td>
                                           
                                            <td class="text-right">{{this.selectedLuggage.amount | currency:
                                                getCurrencySymbol(this.selectedLuggage.currency): "code" : '1.2-2' }}</td>
                                        </tr>
                                        <tr *ngIf="this.selectedReturnLuggage" style="display: flex;justify-content: space-between;">
                                            <td class="text-left">{{this.selectedReturnLuggage.displayText}} ({{this.selectedReturnLuggage.leg}})</td>
                                           
                                            <td class="text-right">{{this.selectedReturnLuggage.amount | currency:
                                                getCurrencySymbol(this.selectedReturnLuggage.currency): "code" : '1.2-2' }}</td>
                                        </tr>
                                        <tr style="display: flex;justify-content: space-between;">
                                            <td class="text-left total-payment-amount">{{'paymentDetails.Total' | translate}}</td>
                                            <td style="display: none;" class="text-left total-payment-amount">
                                                {{'paymentDetails.DueToday' | translate}}</td>
                                            <td class="text-right total-payment-amount">{{getTotalAmount(this.selectedbillingItem[0]) | currency:
                                                getCurrencySymbol(currencyCode): "code" : '1.2-2' }}</td>
                                        </tr>
                                    </div>
                                    </ng-container>
                            </div>
</ng-template>
<ng-template #bookingResponseModal let-modal>
        <div class="table-view">
            <div class="table-cell-view">
                <div class="modal-dialog modal-dialog-md" role="document">
                    <div class="modal-content">
                       
                        <div class="modal-header">
                            <h5 class="modal-title" id="myModalLabel" style="width: 35px !important;">
                                <img src="../../../assets/images/footer-logo.png" alt="logo" />
                            </h5>
                            <button  type="button" class="close" data-dismiss="modal"
                                (click)="onCancelForMUltipleBooking()">
                                <i class="material-icons">close</i>
                            </button>
                        </div>
                        <div class="modal-body">
                           <div style="text-align: center;font-family: var(--globalFontfamilyr);font-weight: bold;;font-size: 20px;margin-bottom: 25px;text-transform: capitalize;">{{'fuild.Bookingstatus' | translate}} </div>
                         <div *ngIf="this.noOfPassengers > 1">
                          <div class="detailsrow" *ngFor="let item of this.searchService.employeeEmail;let i=index;">
                              <div  style="text-align: left;min-width: 60px;">
                               {{i+1}}.   {{getTravellerName(item.email)}}
                              </div>
                              <div class="bookingDetails" style="text-align: left;">
                                    <loader-dots *ngIf="!this.bookingService.bookingResponse" class="loaderAlign"></loader-dots>
                                    <div *ngIf="this.bookingService.bookingResponse  && this.bookingService.bookingResponse.multiBookingResponse && this.bookingService.bookingResponse.multiBookingResponse.length >0 && this.bookingService.bookingResponse.multiBookingResponse[i].status==='success'"  (click)="goToItinerary(i)">
                                         <span style="margin-right:0px;color:green;">   <i  style="font-size: 15px;"
                                            class="fa fa-check" aria-hidden="true"></i></span>     <span class="addlue" style="margin-right:10px;cursor:pointer;">  {{'successModel.showmeitinerary' | translate}}</span>
                                    </div>
                              
                                <div *ngIf="this.bookingService.bookingResponse  && this.bookingService.bookingResponse.multiBookingResponse && this.bookingService.bookingResponse.multiBookingResponse.length >0 && this.bookingService.bookingResponse.multiBookingResponse[i].status==='error'">
                                      <span style="margin-right:0px;"><i  class="fa fa-exclamation-triangle"
                                        aria-hidden="true"
                                        style="color: orange;font-size: 15px;top: 0px;position: relative;"></i></span>
                                        <span *ngIf= "'ERROR_RETRY' !== this.bookingService.bookingResponse.multiBookingResponse[i].errors[0].errorType" style="text-transform: capitalize;">{{'bookingHistory.Underreview' | translate}} </span>
                                        <span *ngIf= "'ERROR_RETRY' === this.bookingService.bookingResponse.multiBookingResponse[i].errors[0].errorType" style="text-transform: capitalize;">{{'bookingHistory.PleaseTryAgainOnErrorRetry' | translate}} </span>
                                </div>
                            </div>
                          </div>
                          <div *ngIf="isMultipleTravelerHotelBookingFailedForRetry()">
                            <span style="margin-right:10px;cursor:pointer;" class="addlue" (click)="backToHotelSelection()">Retry Booking</span>
                          </div>
                          </div>
                          <div *ngIf="this.searchService.multiTripBooking">
                                <div class="detailsrow" *ngFor="let item of this.tripDetails;let i=index;">
                                    <div  style="text-align: left;text-transform: capitalize;min-width: 60px;">
                                     {{i+1}}.   {{item.type}}:
                                    </div>
                                    <div class="bookingDetails" style="text-align: left;">
                                        
                                          <div >
                                               
                                                    <span *ngIf="item.type==='flight' && this.bookingRequest && this.bookingRequest[i]&& this.bookingRequest[i].type==='flight'">{{getSourceAndDestination(this.bookingRequest[i].bookingDetails.flightsToBook.flights[0])}}, {{getDisplayDate(this.bookingRequest[i].bookingDetails.flightsToBook.flights[0].legs[0].flightHops[0].starts)  | date:'MMM d'}} <span style="margin-left: 5px;">{{getTripType(this.bookingRequest[i].bookingDetails.flightsToBook.flights[0])}}</span></span>
                                                    <span *ngIf="item.type==='car' && this.bookingRequest && this.bookingRequest[i]&& this.bookingRequest[i].type==='cars'">{{ getPickUpLocation(this.tripDetails,i)}}  {{getDropOffLocation(this.tripDetails,i)}}, {{this.bookingRequest[i].bookingDetails.carsToBook[0].pickUpDate | date:'MMM d'}} - {{this.bookingRequest[i].bookingDetails.carsToBook[0].dropOffDate | date:'d'}}</span>
                                                    <span *ngIf="item.type==='hotel' && this.bookingRequest && this.bookingRequest[i]&& this.bookingRequest[i].type==='hotel'">{{getHotelCity(i)}}, {{getDisplayDate1(this.bookingRequest[i].bookingDetails.hotelSearchQuery.checkInDate,'MMM d')}} - {{getDisplayDate1(this.bookingRequest[i].bookingDetails.hotelSearchQuery.checkOutDate,'d')}} </span>
                                                  
                                                    <span *ngIf="this.bookingService.bookingResponse  && this.bookingService.bookingResponse.multiBookingResponse && this.bookingService.bookingResponse.multiBookingResponse.length >0 && this.bookingService.bookingResponse.multiBookingResponse[i].status==='success'">
                                                            
                                                        <span  style="margin-left:5px;color:green;"> 
                                                          <i  style="font-size: 15px;" class="fa fa-check" aria-hidden="true"></i></span>   
                                                          <span class="addlue" style="margin-right:0px;cursor:pointer;" (click)="goToItinerary(i)">  {{'successModel.showmeitinerary' | translate}}   
                                                </span>
                                                </span>
                                                <span *ngIf="this.bookingService.bookingResponse  && this.bookingService.bookingResponse.multiBookingResponse && this.bookingService.bookingResponse.multiBookingResponse.length >0 && this.bookingService.bookingResponse.multiBookingResponse[i].status==='error'">
                                                        <span   style="margin-left:5px;">
                                                            <i  class="fa fa-exclamation-triangle"
                                                          aria-hidden="true"
                                                          style="color: orange;font-size: 15px;top: 0px;position: relative;"></i></span>
                                                          <span style="text-transform: capitalize;">{{'bookingHistory.Underreview' | translate}} </span>
                                                          </span>
                                                        
                                                  
                                          </div>
                                    
                                          <span> <loader-dots *ngIf="!this.bookingService.bookingResponse" class="loaderAlign" style="margin-left: 5px;"></loader-dots></span>
                                         
                                  </div>
                                </div>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ng-template>
    <ng-template #content let-modal>
            <div class="table-view">
                <div class="table-cell-view">
                    <div class="modal-dialog modal-dialog-md" role="document">
                        <div class="modal-content">
                            
                            <div class="modal-header">
                                    <span>
                                            <img class="footerimage" [src]="this.searchService.footerLogo">
                                          </span>
                                <button  type="button" class="close" data-dismiss="modal"
                                    (click)="onCancel()">
                                    <i class="material-icons">close</i>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="modal-content-heading">
                                    <h3></h3>
                                    <h3 [style.color]="color">{{'paymentDetails.OutsidePolicy' | translate}}</h3>
                                </div>
        
                                <div class="modal-content-text modal-content-width">
                                    <p [style.color]="color">
                                        <!--  
                                        One or more of the selected options are <span>outside policy guidelines.</span><br><br>
                                        -->
                                        <span>{{this.formattedText}}</span>
                                        
                                    </p>
                                    
                                </div>
                              
                              
                              
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </ng-template>
<ng-template #approvalConfirmModal let-modal>
    <div class="table-view">
        <div class="table-cell-view">
            <div class="modal-dialog modal-dialog-md" role="document">
                <div class="modal-content">
                    <div *ngIf="this.disabled" class="approval_request_diaglog_bg_clickhandler"></div>
                    <div class="modal-header">
                            <span>
                                    <img class="footerimage" [src]="this.searchService.footerLogo">
                                  </span>
                        <button *ngIf="!this.disabled" type="button" class="close" data-dismiss="modal"
                            (click)="onCancel()">
                            <i class="material-icons">close</i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-content-heading">
                            <h3></h3>
                            <h3 [style.color]="color">{{'paymentDetails.OutsidePolicy' | translate}}</h3>
                        </div>

                        <div class="modal-content-text modal-content-width">
                            <p [style.color]="color">
                                <!--  
                                One or more of the selected options are <span>outside policy guidelines.</span><br><br>
                                -->
                                <span>{{getOutsidePolicyReasonText()}}</span>
                                <br><br>{{'paymentDetails.Toproceed,pleasesubmitrequestforapproval' | translate}}
                            </p>
                            <div style="margin-top: 25px;position: relative;"> 
                                <textarea placeholder="" maxlength="512" [(ngModel)]="messageForAdmin"
                                class="modal-textarea input-textfield"  
                                style="margin-top: 0px !important;height: 90px !important;color:black !important; line-height:18px;position: relative;padding-right: 55px;padding-top: 20px;"
                                placeholder="{{'paymentDetails.Typeanotefortheadminifyouneedtoexplainthereasonforthisapprovalrequestoptional' | translate}}"
                                ></textarea>
                                <span *ngIf="messageForAdmin" class="showNumber">
                                        {{messageForAdmin.length}}/512
                                      </span>
                                </div>
                        </div>
                      
                        <div class="modal-content-button" [ngStyle]="{'margin-bottom': this.disabled ? '20px':'44px'}">
                            <button class="btn btn-secondary"
                                (click)="sendApprovalRequest(approvalSuccessModal);this.disabled=true;"
                                [disabled]="this.disabled"><span class="add">{{'paymentDetails.SENDAPPROVALREQUEST' | translate}}</span>
                            </button>
                            <button *ngIf="false" class="btn btn-normal" (click)="showMoreOptions(forOptionModal)"
                                [disabled]="this.disabled"><span class="add1"> {{'paymentDetails.SELECTDIFFERENTOPTIONS' | translate}}</span></button>
                        </div>
                        <span *ngIf="this.disabled" class="wait">{{'paymentDetails.Pleasewait!Weareprocessingyourrequest' | translate}}</span>
                         <loader-dots *ngIf="this.disabled" class="loaderAlign"></loader-dots>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #approvalSuccessModal let-modal>
    <div class="table-view">
        <div class="table-cell-view">
            <div class="modal-dialog modal-dialog-md" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                            <span>
                                    <img class="footerimage" [src]="this.searchService.footerLogo">
                                  </span>
                    </div>
                    <div class="modal-body">
                        <div class="modal-content-heading">
                            <h3></h3>
                            <h3 [style.color]="color">{{'paymentDetails.Success!' | translate}}</h3>
                        </div>
                        <div class="modal-content-text modal-content-width">
                            <p [style.color]="color">{{'paymentDetails.Approvalrequesthasbeensenttoyouradmin' | translate}}<br><br>{{'paymentDetails.Onceitisapproved,wewillsendyoutheconfirmationemailwithitinerary.' | translate}}</p>
                        </div>
                        <div class="modal-content-button delete-modal-button">
                            <button class="btn btn-normal" (click)="backToSearch()"><span class="add1"> {{'paymentDetails.BOOKANOTHERTRAVEL' | translate}}</span>
                            </button>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>


<ng-template #forOptionModal let-modal>
    <div class="table-view">
        <div class="table-cell-view">
            <div class="modal-dialog modal-dialog-md" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                            <span>
                                    <img class="footerimage" [src]="this.searchService.footerLogo">
                                  </span>
                        <button *ngIf="!disableCancel" type="button" class="close" data-dismiss="modal"
                            (click)="onCancel()">
                            <i class="material-icons">close</i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-content-heading">
                            <h3></h3>
                            <h3 [style.color]="color">{{'paymentDetails.Requestmoreoptions' | translate}}</h3>
                        </div>
                        <div class="modal-content-text modal-content-width">
                            <p [style.color]="color">{{'paymentDetails.Unfortunately,youhadreceivedoneoptionforthis' | translate}}<br> {{'paymentDetails.Pleasetypeyournotetoouragentandsomeonewillgetback' | translate}}</p>
                        </div>
                        <div class="modal-content-button delete-modal-button">
                            <button class="btn btn-secondary" (click)="showMoreOptions(moreOptionsSuccessModal)"><span
                                    class="add">{{'paymentDetails.REQUESTMOREOPTIONS' | translate}}</span>
                            </button>
                            <button class="btn btn-normal" (click)="sendApprovalRequest(approvalSuccessModal)"><span
                                    class="add1">{{'paymentDetails.GOBACK' | translate}}</span></button>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #moreOptionsSuccessModal let-modal>
    <div class="table-view">
        <div class="table-cell-view">
            <div class="modal-dialog modal-dialog-md" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                            <span>
                                    <img class="footerimage" [src]="this.searchService.footerLogo">
                                  </span>
                        <button *ngIf="!disableCancel" type="button" class="close" data-dismiss="modal"
                            (click)="onCancel()">
                            <i class="material-icons">close</i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="modal-content-heading">
                            <h3></h3>
                            <h3 [style.color]="color">{{'paymentDetails.Success!' | translate}}</h3>
                        </div>
                        <div class="modal-content-text modal-content-width">
                            <p [style.color]="color">{{'paymentDetails.Yourrequesthasbeensent' | translate}}<br><br>{{'paymentDetails.youwillreceiveanemailwithmoreoptions' | translate}}</p>
                        </div>
                        <div class="modal-content-button delete-modal-button">
                            <button class="btn btn-normal"><span class="add1">{{'paymentDetails.BOOKANOTHERTRAVEL' | translate}}</span>
                            </button>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>




<ng-template #taxDetails let-modal>
    <div class="table-view">
        <div class="table-cell-view">
            <div class="modal-dialog modal-dialog-md" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                            <span>
                                    <img class="footerimage" [src]="this.searchService.footerLogo">
                                  </span>
                        <button type="button" class="close" data-dismiss="modal" (click)="onCancel1()">
                            <i class="material-icons">close</i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <h3>{{'paymentDetails.TaxesandFees' | translate}}</h3><br><br>
                        <div style="text-align:left !important;padding-left:20px!important; ">
                            <p *ngIf="this.billingItemArray[0].type==='hotel'"> {{'paymentDetails.Hereisbreakdownofthetaxesandfeesthatapplytoyourreservation.Notethatcharges' | translate}}</p><br>
                            <p *ngIf="this.billingItemArray[0].type==='cars'">{{'paymentDetails.Hereisbreakdownoftheestimatedtaxes,feesandsurchargesthatapplytoyourreservationwhenyoupick-upyourrentalcar' | translate}}</p><br>
                            <span style="font-family: var(--globalFontfamilyr);font-weight: bold;;font-size:18px;margin-right:8px">{{'paymentDetails.Tax' | translate}}:</span><span
                                *ngIf="this.billingItemArray[0].type==='hotel'">{{(this.billingItemArray[0].tax
                                -this.billingItemArray[0].fee) | number : '1.2-2'}}</span><span
                                *ngIf="this.billingItemArray[0].type==='cars'">{{this.billingItemArray[0].tax | number :
                                '1.2-2'}}</span><br>
                            <span *ngIf="this.billingItemArray[0].type==='cars'"
                                style="font-family: var(--globalFontfamilyr);font-weight: bold;;font-size:18px;margin-right:8px">
                               {{'paymentDetails.TotalTax' | translate}}: {{this.billingItemArray[0].tax | number : '1.2-2'}}</span>
                            <span style="font-family: var(--globalFontfamilyr);font-weight: bold;;font-size:18px;margin-right:8px"
                                *ngIf="this.billingItemArray[0].type==='hotel' && this.billingItemArray[0].fee!==null">{{'paymentDetails.Fees' | translate}}:</span>{{this.billingItemArray[0].fee}}<br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #policyDetails let-modal>
    <div class="table-view">
        <div class="table-cell-view">
            <div class="modal-dialog modal-dialog-md" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                            <span>
                                    <img class="footerimage" [src]="this.searchService.footerLogo">
                                  </span>
                        <button type="button" class="close" data-dismiss="modal" (click)="onCancel1()">
                            <i class="material-icons">close</i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <h3>{{'paymentDetails.PolicyInformation' | translate}}</h3><br><br>
                        <div style="text-align:left !important;padding-left:20px!important; "
                            *ngFor="let item1 of this.billingItemArray">
                            <div *ngIf="item1.type==='hotel' && item1.addPolicy">
                              <div      *ngFor="let item of item1.addPolicy">
                            <span style="font-family: var(--globalFontfamilyr);font-weight: bold;;font-size:16px;margin-right:8px">{{item.title}}:</span>
                            <span style="font-family: var(--globalFontfamilyr);font-size:12px;" [innerHTML]="item.text"></span>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>