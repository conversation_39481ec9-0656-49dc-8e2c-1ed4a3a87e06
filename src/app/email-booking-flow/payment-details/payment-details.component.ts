import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ChangeDetectorRef, HostListener, ElementRef, TemplateRef } from '@angular/core';
import { Subscription, Observable, BehaviorSubject, throwError, Subject, empty } from 'rxjs';
import { UserAccountInfo } from '../../entity/user-account-info';
import { UserAccountService } from '../../user-account.service';
import { Router, ActivatedRoute } from '@angular/router';
import { deserialize } from '../../util/ta-json/src/methods/deserialize';
import { UserAccountInputs } from '../../entity/user-account-inputs';
import { CardInfo } from '../../entity/card-info';
import { BillingItem } from '../../entity/email-flow/billing-item';
import { BookingService } from '../../booking.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { SuccessModelComponent } from '../success-model/success-model.component';
import { UntypedFormControl, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import * as Script from "../../../assets/js/emailflow/script1.js";
import { EmailFlowBookRequest } from '../../entity/email-flow/email-book-request';
import { ToastrService } from 'ngx-toastr';
import { _ } from 'src/app/util/title';
import { SubscriptionPlan } from '../../entity/subscription-plan';
import { AddCardWidgetComponent } from '../add-card-widget/add-card-widget.component';
import { DeleteCardModelComponent } from '../delete-card-model/delete-card-model.component';
import { BookingResponse } from 'src/app/entity/booking-response';
import { BookingMessageModalComponent } from 'src/app/booking-message-modal/booking-message-modal.component';
import { Constants } from 'src/app/util/constants';
import { BookingResponseErrorType } from 'src/app/enum/booking-response-error.type';
import { TravelerDetails } from 'src/app/entity/traveler-details';
import { GallopLocalStorageService } from 'src/app/gallop-local-storage.service';
import { CommonUtils } from '../../util/common-utils';
import { HotelBookingResponse } from 'src/app/entity/hotel-booking-response';
import { PaymentTypes } from 'src/app/enum/payment-types';
import { environment } from 'src/environments/environment';
import { HotelQueryParam } from 'src/app/entity/hotel-query-param';
import { FlightResult } from 'src/app/entity/flight-result';
import { FlightSearchRequest } from 'src/app/entity/flight-search-request';

import { TranslateService } from "@ngx-translate/core";
import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { GallopAnalyticsUtil } from 'src/app/analytics.service';
import { AlgoTypes } from 'src/app/enum/algo-types';
import { ConnectionService } from 'ng-connection-service';
import { SearchService } from 'src/app/search.service';
import { PopupComponent } from 'src/app/popup/popup.component';
import { HotelResult } from 'src/app/entity/hotel-result';
import { catchError } from 'rxjs/operators';
import { CarBookingService } from 'src/app/car-booking.service';
import { Location, PlatformLocation, LocationChangeEvent } from '@angular/common';
import { DeviceDetailsService } from 'src/app/device-details.service';
import { NgSelectComponent } from '@ng-select/ng-select';
import { DateUtils } from 'src/app/util/date-utils';
import { find } from 'tslint/lib/utils';
import { SeatSelect } from 'src/app/entity/seatSelected';
import { Title } from '@angular/platform-browser';
import { IfStmt } from '@angular/compiler';
import { HotelSearchService } from 'src/app/hotel-search.service';
import { FilterService } from 'src/app/filter.service';
import { SearchResultService } from 'src/app/search-result.service';

declare var updatePaymentDOM: any;
declare var getPaymentToken: any;
declare var notifyAgentWithNewTripParams: any;
declare var notifyAdminForApproval: any;
declare var updateTravelCheckBoxes: any;
declare var updatePriceChangeInBookingResponse: any;
@Component({
    selector: 'payment-details',
    templateUrl: './payment-details.component.html',
    styleUrls: ['./payment-details.component.scss'],
    standalone: false
})
export class PaymentDetailsComponent implements OnInit {

  @Input() _billingItemArray: Array<BillingItem>;

  @Input() eventIdAndOptions: Map<string, string>;
  @Input() emailId: string;
  @Input() sToken: string;
  @Input() ticketId: string;
  @Input() all_tags : Array<any>[];
  @Input() all_tagset = [];
  @Input() new_tagSets_array = [];
  @Input() tripId: string;
  @Input() outsidePolicyFlag: boolean;
  @Input() canAttemptDirectBooking: boolean;
  @Input() uniqueHotels = [];
  @Input() uniqueAirlines = [];
  @Output() goBackEmitter = new EventEmitter();
  @Output() reBookEmitter = new EventEmitter();
  @Input() noOfPassengers: number;
  @Input() dropDownopen =[];
  @Input() tripDetails=[];

  billingItemArray: Array<BillingItem>;
  selectedLuggage:any;
  selectedReturnLuggage:any;
  color: string = 'black';
  classOptions = [{ Name: 'policy.Notdefined', value: 'NOT_DEFINED' }, { Name: 'policy.EconomyBaseandStandardfares', value: 'ECONOMY' },
  { Name: 'policy.PremiumEconomyEnhancedandPremiumfares', value: 'PREMIUM_ECONOMY' }, { Name: 'policy.BusinessPremiumfares', value: 'BUSINESS' }];
  changingValue: Subject<any> = new Subject();
  tempSelectTag=[];
  search='';
  userId :any;
  sToken1:any;
  buttonColor: string = '';
  messageForAdmin:string;
  currentToastId = -1;
  selectedTag = [];
  currentBackToastId = -1;
  airlines;
  flight:any;
  bsModalRef1:BsModalRef;
car:any;
hotel:any;
bookingRequest:any;
  disabled = false;
  showBillTocompany = true;
  creditDetails: any;
  approvalPopOpen = false;
  approvalSuccessPopOpen = false;
  countOfCar = 0;
  isMobile: boolean;
 
  othersCount = 0;
  bookRequestProgress = false;
  pollingStarted=false;
  luggageOptions=[];
  njoySpecificBuild: boolean;
  numOfTravelelrExculidngChild:any;
  returnLuggageOptions = [];
  manuallySwwitchedOff = false;
  bookingRequestStarted = false;
  fetchAccountInfoSubscription: Subscription;
  bookingStatusId: any;
  bookServiceSubscription: Subscription;
  flightFareChangeSubscription: Subscription;
  bilingItemListSubscription: Subscription;
  bilingItemListSubject = new BehaviorSubject<Array<BillingItem>>(null);
  bilingItemListObservable$ = this.bilingItemListSubject.asObservable();
  userAccountInfoObj: UserAccountInfo;
  addCardMode: boolean;
  totalBillPayable: number;
  tempTotalBillPayable: number;
  hasPrepaidItem: boolean = true;
  totalPayableAtHotel: number;
  gallopCashApplied: number = 0;
  currencyCode: string = 'USD';
  selectedCardIndex: number = -1;
  paymentMethod: string;
  bsModalRef: BsModalRef;
  toUseGallopCashControl: UntypedFormControl;
  travelApprovedCheck: UntypedFormControl;
  isNjoySpecificRelease: boolean;
  connectionListener: Subscription;
  isBusinessTrip = true;
  priceLineHotel = false;
  priceLineCar = false;
  switchon = false;
  selectedHotel: HotelResult;
  callAgain = false;
  deviceSubscription: Subscription;
  showPaymentDeatils = true;

  private pageMode: string;
  private isBillToCompany: boolean = false;
  private cardToken: CardInfo;
  private serviceFee: number;
  @ViewChild('chartDepartment') ngselect: NgSelectComponent;
  @ViewChild(AddCardWidgetComponent) addCardChild: AddCardWidgetComponent;
  @ViewChild("content") content!: TemplateRef<any>;
  @ViewChild('paymentdetailsModal') paymentdetailsModal!: TemplateRef<any>;
  @HostListener('window:beforeunload', ['$event'])
  onWindowClose(event: any): void {
    if (this.bookRequestProgress) {
      var value = 'The booking is in progress and cannot be stopped at this stage. Are you sure you want to close?';
      event.preventDefault();
      event.returnValue = value;
    } else if (this.disabled) {
      var value = 'Your approval request is on its way. Are you sure you want to close?';
      event.preventDefault();
      event.returnValue = value;
    }
  }
  constructor(public userAccountInfoService: UserAccountService,
    private gallopLocalStorage: GallopLocalStorageService,
    protected bookingService: BookingService,
    private carBookingService: CarBookingService,
    private modalService: BsModalService,
    private activatedRoute: ActivatedRoute,
    private toastr: ToastrService,
    private titleService: Title,
    public translateService: TranslateService,
    public router: Router,
    private location: Location,
    public connectionService: ConnectionService,
    public searchService: SearchService,
    private cdRef: ChangeDetectorRef,
    public deviceDetailsService: DeviceDetailsService,
    private ngxAnaltics:GoogleAnalyticsService,
    private hotelSearchService : HotelSearchService,
    private searchResultService: SearchResultService,
     private filterService: FilterService,
    location2: PlatformLocation) {
    location2.onPopState(() => {
      if (this.currentBackToastId !== -1) {
        this.toastr.remove(this.currentBackToastId);
      }
      if (this.bookRequestProgress && !this.disabled) {
        this.searchService.paymentPage = true;
        this.bookingService.proceedButton = true;
        history.pushState(null, null, window.location.href);
        this.currentBackToastId = this.toastr.info(this.translateService.instant('paymentDetails.Thebookingisinprogressandcannotbestoppedatthisstage.')).toastId;
      } else if (this.disabled && this.bookRequestProgress) {
        this.searchService.paymentPage = true;
        this.bookingService.proceedButton = true;
        history.pushState(null, null, window.location.href);
        this.currentBackToastId = this.toastr.info(this.translateService.instant('paymentDetails.Yourapprovalrequestisonitswayandcannotbestoppedatthisstage.')).toastId;
      } else if (!this.bookRequestProgress && !this.disabled && this.bsModalRef) {
        if (this.approvalPopOpen) {
          this.bsModalRef.hide();
          this.searchService.paymentPage = false;
          this.bookingService.proceedButton = false;
          this.goBackEmitter.emit('back');
        } else {
          this.searchService.paymentPage = false;
          this.bookingService.proceedButton = false;
          this.goBackEmitter.emit('backToSelection');
        }
      } else if ((!this.bookRequestProgress && this.bsModalRef)) {
        if (this.approvalSuccessPopOpen) {
          this.approvalSuccessPopOpen = false;
          this.bsModalRef.hide();
          this.userAccountInfoService.paymentPageSave = false;
          this.goBackEmitter.emit('backToSearch');
        }
      } else {
        this.searchService.paymentPage = false;
        this.userAccountInfoService.paymentPageSave = false;
        this.goBackEmitter.emit('backToSelection');
      }
    });
  }


  ngAfterViewChecked() {
    // this.cdRef.detectChanges();
  }
  
  selectedbillingItem = [];
  formattedText;
   
  isCreditAppliedInAnyItem(items, amount){
    if (items && items.length > 0) {
      for(let i =0; i < items.length; i++) {
        if (this.isCreditApplied(items[i], amount)) {
          return true;
        }
        }
      }
        return false;
  }
  isCreditApplied(item, amount) {
    let flight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"));
    if (flight[this.bookingService.selectedPaymentEventIndex]) {
      const pricePerPassenger = amount / this.noOfPassengers;
      let residualPrice;
      if (item.displayCreditAmount) {
        residualPrice = item.displayCreditAmount - pricePerPassenger;
      } else {
        residualPrice = item.creditAmount - pricePerPassenger;
      }
      if (residualPrice > item.maxResidual) {
        return false;
    }
    return true;
  }
  }
  public openPaymentdetailsModal(event){
    if(event && event.id==='payment'){
    this.selectedbillingItem=[];
    if(!this.bookRequestProgress){
     
      this.selectedbillingItem.push(this.billingItemArray[this.bookingService.selectedPaymentEventIndex]);
      
        this.bsModalRef=  this.modalService.show(this.paymentdetailsModal);  
    
  
   
    }
  }else if(event && event.id==='policy'){
   this.formattedText = this.getOutsidePolicyReasonText(event.type);
   this.bsModalRef  = this.modalService.show(this.content);  
  }

  }
  getUiForTotalAMount(){
    if(this.isMobile){
      return {'display':'flex','float':'right'}
    }else{
      return {'display':'flex','justify-content':'space-between'}
    }
  }
  postPaidCar =  this.translateService.instant('paymentDetails.DueNow')
  ngOnInit() {
    this.getAllTagsSet();
    this.userId = this.userAccountInfoService.getUserEmail();
    this.sToken1= this.userAccountInfoService.getSToken();
    this.njoySpecificBuild = this.userAccountInfoService.isItNjoyBuild();
    this.isNjoySpecificRelease = this.userAccountInfoService.isItNjoyBuild();
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });
    var airlines = JSON.parse(this.gallopLocalStorage.getItem('airlineNames'));
    if (airlines) {
      this.airlines = airlines;
    }
    this.billingItemArray = this._billingItemArray;
    if(this.searchService.infantBookingAllowed && this.searchService.ageGroupArray.length >0){
    let travellerArray=[];
    for(let i=0;i<this.noOfPassengers;i++){
     if(this.searchService.ageGroupArray[i].id!=='INF'){
      travellerArray.push(i);
      }
    }
    this.noOfPassengers = travellerArray.length;
  }
  this.numOfTravelelrExculidngChild =  this.noOfPassengers;
  if(this.searchService.infantBookingAllowed && this.searchService.ageGroupArray.length >0){
    let travellerArray=[];
    for(let i=0;i<this.noOfPassengers;i++){
     if( ((this.searchService.ageGroupArray[i].id==='ADT' && this.searchService.ageGroupArray[i].superAdult))){
      travellerArray.push(i);
      }
    }
    this.numOfTravelelrExculidngChild = travellerArray.length;
  }
   this.hotel = JSON.parse(this.gallopLocalStorage.getItem("selectedHotelDetailedObj"));
    if (this.billingItemArray && this.billingItemArray.length === 1 && this.billingItemArray[0].type !== 'flight' && this.billingItemArray[0].number === 1) {
      let selectCar = deserialize(JSON.parse(this.gallopLocalStorage.getItem("selectedCar")));
      if(selectCar){
      this.car = selectCar[0];
      }
      if (!selectCar && this.billingItemArray[0].paymentType === 'POSTPAID') {
        this.paymentMethod = 'PERSONAL_CARD'
        this.showBillTocompany = false;
      } else {
        this.showBillTocompany = true;
      }
      this.priceLineHotel = true;
    } else {
      this.priceLineHotel = false;
    }
    this.paymentDetailsPage()

    this.travelApprovedCheck = new UntypedFormControl(false, [Validators.required]);

    this.paymentMethod = "PERSONAL_CARD";
    this.toUseGallopCashControl = new UntypedFormControl('0.00', [this.toUseGallopCashValidator.bind(this)]);
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['emailId'] && params['ticketId'] && params['sToken']) {
        this.emailId = params['emailId'];
        this.ticketId = params['ticketId'];
        this.sToken = params['sToken'];
        this.pageMode = 'emailTicketMode';
      }
      if (params['pageMode']) {
        this.pageMode = params['pageMode'];
        if (this.pageMode === 'WebSearch') {
          this.emailId = this.userAccountInfoService.getUserEmail();
          this.sToken = this.userAccountInfoService.getSToken();
        }
      }
      this.bookingService.pageMode = this.pageMode;
    });

    if (true || this.emailId && this.sToken) {

      this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
        if (!this.userAccountInfoService.paymentPageSave) {
          if (!this.bookingRequestStarted) {
            this.userAccountInfoObj = userAccountInfoObj;
            this.unsetCardMode();
            this.isExpenseIsSelected();
            this.updateUserAccountInfo();
            if (this.bsModalRef) {
              this.bsModalRef.hide();
              this.userAccountInfoService.deletingCard = false;
            }
          } else {
            this.bookingRequestStarted = false;
          }
        }
      });
      this.updateUserAccountInfo();
    }


    // this.calculateTotal('0');

    // this.bilingItemListSubscription = this.bilingItemListObservable$
    //  .subscribe((billingItemList:Array<BillingItem>)=>{
    //     this.processBillingItemList(billingItemList);
    //   });

    // this.bilingItemListSubject.next(this.billingItemArray);
    this.processBillingItemList();
    // this.flightFareChangeSubscription = this.bookingService.flightFareChangeSubject.subscribe((newTransactionAmount)=>{

    //   if(newTransactionAmount != null) {
    //     let newBillItemList = new Array<BillingItem>();
    //     for(let billingItemIndex = 0; billingItemIndex < this.billingItemArray.length; billingItemIndex++){
    //       let billingItem = this.billingItemArray[billingItemIndex];
    //       if (billingItem.type !== 'other'){
    //         if (newTransactionAmount.discountedPrice){
    //           billingItem.amount = newTransactionAmount.discountedPrice;
    //         }else{
    //           billingItem.amount = newTransactionAmount.price;
    //         }
    //       }
    //       newBillItemList.push(billingItem);
    //     }
    //     // this.bilingItemListSubject.next(newBillItemList);
    //     this.billingItemArray =[];
    //     for(let item of newBillItemList){
    //       this.billingItemArray.push(item) ;
    //     }
    //     this.processBillingItemList();
    //   }
    // });
    if (this.showBillToCompany() && !this.getUserAllowed()) {
      setTimeout(() => {
        this.setBillToCompany();
      }, 500);
    }
  }
  back() {
    this.bsModalRef.hide();
    this.router.navigate(['/'+this.userAccountInfoService.getDefaultRoutePath()]);

  }
  getCounterMsg(item){
    if(item){
   return  this.postPaidCar = this.translateService.instant('paymentDetails.Estimatedtotaldueatcounter');
    }else{
     return  this.postPaidCar = this.translateService.instant('paymentDetails.Total');
    }
  }
  paymentDetailsPage() {
    let selectCar = deserialize(JSON.parse(this.gallopLocalStorage.getItem("selectedCar")));
    if (selectCar && selectCar.length >0) {
      for (let car of selectCar) {
        if (!car.postPay) {
          this.showPaymentDeatils = false;
        } else {
          this.countOfCar = 1;
        }
      }
      if (this.billingItemArray.length > 1) {
        for (let item of this.billingItemArray) {
          if (item.type !== 'cars') {
            this.othersCount = 1;
          }
        }
      }
      if (this.othersCount === 1) {
        this.showPaymentDeatils = true;
        this.postPaidCar = this.translateService.instant('paymentDetails.Estimatedtotaldueatcounter');
      } else if (this.countOfCar === 1) {
        this.showPaymentDeatils = true;
        this.postPaidCar = this.translateService.instant('paymentDetails.Estimatedtotaldueatcounter');
      } else {
        this.postPaidCar = this.translateService.instant('paymentDetails.Estimatedtotaldueatcounter');
        this.showPaymentDeatils = false;
      }
    }

  }
  setBillingItemArray(billingItemList: Array<BillingItem>) {
    this.billingItemArray = billingItemList;
    this.processBillingItemList();
  }
  getBillingItemArray() {
    return this.billingItemArray;
  }
  processBillingItemList() {
    // if(!billingItemList) return;
    // this.billingItemArray = [];
    // for(let item of billingItemList){
    //   this.billingItemArray.push(item);
    // }
    // this.billingItemArray=billingItemList;
    if (this.billingItemArray) {
      this.currencyCode = this.billingItemArray[0].currency;
    }
    this.calculateTotal('' + this.gallopCashApplied);

    // this.cdRef.detectChanges();
  }
  setCardMode() {
    this.addCardMode = true;
  }

  unsetCardMode() {
    this.addCardMode = false;
  }

  toUseGallopCashValidator(control: AbstractControl): ValidationErrors | null {
    let result = null;

    // if(control.value && control.valid){
    let floatValue = Number.parseFloat(control.value);
    // 
    if (Number.isNaN(floatValue) || floatValue < 0 || this.getGallopCash() < floatValue) {
      result = { 'invalidAmount': true };
    } else if (floatValue > this.totalBillPayable - this.serviceFee + this.gallopCashApplied) {
      result = { 'exceedingPayable': true };
    }
    // }
    return result;
  }

  updateUserAccountInfo(): void {
    this.userAccountInfoObj = this.userAccountInfoService.getAccountInfo();
    if (!this.bookRequestProgress) {
      this.processUserAccountInfo();
    }
  }

  public processUserAccountInfo() {
    if (this.userAccountInfoObj) {
      let carVendor;
      if (this.billingItemArray[0].type === 'cars') {
        let selectedCar: any = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
        if (selectedCar && selectedCar.length >0) {
          carVendor = selectedCar[0].traflaPartnerCode;
        }
      }
      if(this.pageMode==='emailflowAgent'){
        this.paymentMethod = 'BILL_TO_COMPANY';
        this.showBillTocompany = true;
      
      }else{
        this.paymentMethod = this.userAccountInfoService.getUserPaymentMethod(carVendor);
      }
      this.isExpenseIsSelected();
      if (this.billingItemArray && this.billingItemArray.length === 1 && this.billingItemArray[0].type !== 'flight' && this.billingItemArray[0].number === 1) {
        let selectCar = deserialize(JSON.parse(this.gallopLocalStorage.getItem("selectedCar")));
        if (!selectCar && this.billingItemArray[0].paymentType === 'POSTPAID' && this.userAccountInfoService.confermaStatus && this.userAccountInfoService.canAccessCompanyCard()) {
          this.paymentMethod = 'BILL_TO_COMPANY';
          this.showBillTocompany = true;
          this.gallopLocalStorage.removeItem("expensifyDetails");
          this.gallopLocalStorage.removeItem("expenseDetails");
        } else if (!selectCar && this.billingItemArray[0].paymentType === 'POSTPAID' && this.pageMode!=='emailflowAgent') {
          this.paymentMethod = 'PERSONAL_CARD'
        }
      }
      if (this.paymentMethod === 'PERSONAL_CARD' && this.getUserAllowed()) {
        if (this.userAccountInfoObj.cardList && this.userAccountInfoObj.cardList.card_list) {
          if (this.userAccountInfoObj.cardList.stripe_card && this.userAccountInfoObj.cardList.stripe_card !== null) {
            for (let cIndex in this.userAccountInfoObj.cardList.card_list) {
              if (this.userAccountInfoObj.cardList.card_list[cIndex].id === this.userAccountInfoObj.cardList.stripe_card.id) {
                this.selectedCardIndex = parseInt('' + cIndex);
              }
            }
          } else {
            this.selectedCardIndex = this.userAccountInfoObj.cardList.card_list.length - 1;
          }
        }
      } else {
        this.selectedCardIndex = -1;
        this.isBusinessTrip = true;
      }
      this.isBillToCompany = this.paymentMethod === 'BILL_TO_COMPANY';
      if (this.pageMode === 'WebSearch' || this.pageMode !== 'WebSearch') {
        if(this.tripDetails[0]  && this.tripDetails[0].paymenttype){
          if(this.tripDetails[0].paymenttype.id==='Bill to company'){
            this.paymentMethod = 'BILL_TO_COMPANY';
          }else{
            this.paymentMethod = 'PERSONAL_CARD'
          }
        }
      }
      updatePaymentDOM();
      this.toUseGallopCashControl.updateValueAndValidity();
    }
  }
  public getCardsModifiedByStripe(): Array<CardInfo> {
    if (this.isLoggedIn() && this.userAccountInfoObj.cardsModifiedByStripe
      && this.userAccountInfoObj.cardsModifiedByStripe.card_list
      && this.userAccountInfoObj.cardsModifiedByStripe.card_list.length > 0
    ) {
      return this.userAccountInfoObj.cardsModifiedByStripe.card_list;
    }
    return null;
  }

  public getCardList(): Array<CardInfo> {
    if (this.isLoggedIn() && this.userAccountInfoObj.cardList) {
      return this.userAccountInfoObj.cardList.card_list;
    } else {
      let cardArray = new Array<CardInfo>();
      if (this.cardToken) cardArray.push(this.cardToken);
      return cardArray;
    }
  }

  getUserAllowed(): boolean {
    if (this.billingItemArray[0].type === 'cars') {
      let selectedCar: any = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
      if (selectedCar && selectedCar.length > 0) {
        if (!this.userAccountInfoService.hasBillingIdConfigured(selectedCar[0].traflaPartnerCode)) {
          return true;
        };
      }
    } else {
      if (this.userAccountInfoService.confermaStatus) {
        if (this.isLoggedIn() && this.userAccountInfoObj.userInfo.employeeInfo
          && this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse != null) {
          return this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse;
        }
      } else {
        if (this.billingItemArray[0].type === 'hotel' && this.hasTravelportOrSabreHotel()) {
          return true;
        }
      }
    }
    if (this.isLoggedIn() && this.userAccountInfoObj.userInfo.employeeInfo
      && this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse != null) {
      return this.userAccountInfoObj.userInfo.employeeInfo.allowReimburse;
    } else {
      return true;
    }
  }

  getDeletedCardArray(): Array<string> {
    let deletedCards = new Array<string>();
    if (this.userAccountInfoObj && this.userAccountInfoObj.cardList) {
      for (let card of this.userAccountInfoObj.cardList.card_list) {
        if (card.markedDeleted) {
          deletedCards.push(card.id);
        }
      }
    }
    if (this.userAccountInfoObj && this.userAccountInfoObj.cardsModifiedByStripe) {
      for (let card of this.userAccountInfoObj.cardsModifiedByStripe.card_list) {
        if (card.markedDeleted) {
          deletedCards.push(card.id);
        }
      }
    }
    return deletedCards;
  }

  public getGallopCash(): number {
    if (this.userAccountInfoObj && this.userAccountInfoObj.gallopCash) {
      return this.userAccountInfoObj.gallopCash.amount;
    } else {
      return 0.00;
    }
  }
  getRoomSubtotal(item): any {
    var subTotal = (item.amount - item.tax);
    return subTotal;
  }
  getTaxtotal(item): any {
    var subTotal = item.tax ? item.tax : null;
    if (subTotal && subTotal < 0) {
      let selectCar = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
      if (item.baseprice) {
        subTotal = (item.amount - item.baseprice);
        this.billingItemArray[0].tax = subTotal;
      }
    }
    return this.billingItemArray[0].tax;
  }
  getDueNow(item): any {
    if(item && item.amount){
    var subTotal = (item.amount);
    this.totalBillPayable = item.amount;
    if(this.getTotalAdditionalFees() != null && this.getTotalAdditionalFees() >0){
      return (Number(subTotal)+ Number(this.getTotalAdditionalFees()));
    }
    return subTotal;
    }
  }
  getResortFee(item) {
    return item.resortFee;
  }
  getCarPrice(item) {
    if (item.tax !== null && item.tax > -1) {
      var subTotal = (item.amount - parseFloat(item.tax))
      if (subTotal > item.tax) {
        return subTotal;
      } else {
       // let selectCar = JSON.parse(this.gallopLocalStorage.getItem("selectedCarForBooking"));
        if (item.baseprice) {
          subTotal = item.baseprice;
        }
        return subTotal;
      }
    }

  }
  getTotalCharge(item) {
    var subTotal;
    if (item.resortFee) {
      subTotal = parseFloat(item.amount) + parseFloat(item.resortFee);
    } else {
      subTotal = item.amount;
    }
    if(this.getTotalAdditionalFees() != null && this.getTotalAdditionalFees() >0){
      return (Number(subTotal) + Number(this.getTotalAdditionalFees()))
    }
    return subTotal;
  }
  getCarPerDayPrice(item) {
    if (item.tax !== null && item.tax > -1) {
      var subTotal = (item.amount - parseFloat(item.tax))
      if (subTotal > item.tax) {
        return (subTotal / item.nights);
      } else {
        let selectCar = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
        if (item.baseprice) {
          subTotal = item.baseprice;
        }
        return (subTotal / item.nights);
      }
    }
  }
  handleResponseFromDropdwnLuggage(event, isReturn){
    let flight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlightInPolicy"));
    let flight1 = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"));
       
    if (!isReturn) {
      if (this.selectedLuggage) {
            this.totalBillPayable = this.totalBillPayable - this.selectedLuggage.amount;
        this.selectedLuggage = undefined;
          }
    } else {
      if (this.selectedReturnLuggage) {
        this.totalBillPayable = this.totalBillPayable - this.selectedReturnLuggage.amount;
        this.selectedReturnLuggage = undefined;
      }
    }
    if(event && event.length > 0) {
      for (const item of event) {
        if (item.optionValue > -1) {
          if (item.selected) {
            if (!isReturn) {
         this.selectedLuggage = item;
         this.totalBillPayable = this.totalBillPayable + this.selectedLuggage.amount;
            } else {
              this.selectedReturnLuggage = item;
              this.totalBillPayable = this.totalBillPayable + this.selectedReturnLuggage.amount;
            }
        }
      }
    }
    }
    if(flight){
      if (isReturn) {
        flight.returnLuggageOptions = event;
      } else {
      flight.luggageOptions = event;
      }
      this.gallopLocalStorage.setItem('selectedFlightInPolicy',JSON.stringify(flight));
    }else if(flight1){
      if (isReturn) {
        flight1.returnLuggageOptions = event;
      } else {
      flight1.luggageOptions = event;
      }
      this.gallopLocalStorage.setItem('selectedFlight',JSON.stringify(flight1));
    }
  }
  isAirlineSupportsAccountCodeWithUATPOnly(carrier) {
    // We are no more supporting United PassPlus
    return false;
  }
  isAccountCodeUATPSpecific(item){
    if (item && item.fareWithAccountCode && this.isAirlineSupportsAccountCodeWithUATPOnly(item.legs[0].flightHops[0].carrier)) {
      return true;
    }
    return false;
  }

  isUserSelectedUATPSpecificAccountCode() {
    let selectedCounter=0;
    const flight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlightInPolicy"));
    const flight1 = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"));
    if (this.billingItemArray && this.billingItemArray.length > 1 || this.billingItemArray[0].type === 'flight') {
      if (flight) {
        if(this.searchService.nonCombo){
          for(const item of this.searchService.nonComboSelectedFlight){
            if(this.isAccountCodeUATPSpecific(item)) {
              selectedCounter=1;
            }
          }
        } else {
          if(this.isAccountCodeUATPSpecific(flight)) {
            selectedCounter=1;
          }
        }
      } else if(flight1) {
        if(this.searchService.nonCombo) {
          for(const item of this.searchService.nonComboSelectedFlight) {
            if(this.isAccountCodeUATPSpecific(item)) {
              selectedCounter=1;
            }
          }
        } else {
          if(this.isAccountCodeUATPSpecific(flight1)) {
            selectedCounter=1;
          }
        }
      }
    }
    if(selectedCounter > 0) {
      return false;
    } else {
      return true;
    }
  }
  public calculateTotal(newValue: string): void {
    let total: number = 0.00;
    this.totalPayableAtHotel = 0.00;
    this.hasPrepaidItem = false;
    if (this.billingItemArray && this.billingItemArray.length > 1 || this.billingItemArray[0].type == 'flight' || this.billingItemArray[0].number > 1) {
      for (let billingItem of this.billingItemArray) {
        if (billingItem.paymentType === PaymentTypes.PREPAID) {
          this.hasPrepaidItem = true;
          total = total + Number.parseFloat(billingItem.amount);
        } else {
          this.totalPayableAtHotel += Number.parseFloat(billingItem.amount);
        }
        if (billingItem.type == 'other') {
          // This is service Fee
          this.serviceFee = Number.parseFloat(billingItem.amount);
        }
      }
    }
    this.totalBillPayable = total;
    this.tempTotalBillPayable = total;
    this.gallopCashApplied = 0;
    if (newValue) {
      this.gallopCashApplied = Number.parseFloat(newValue);
      this.totalBillPayable = total - Number.parseFloat(newValue);
      this.tempTotalBillPayable = this.totalBillPayable;
    }

    if (this.billingItemArray && this.billingItemArray[0].type == 'flight') {
      let flight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlightInPolicy"));
      let flight1 = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"));
      if (flight) {
        this.flight =flight;
        if(flight && flight.luggageOptions && flight.luggageOptions.length >0){
          this.luggageOptions = flight.luggageOption;
        }
        if(flight && flight.returnLuggageOptions && flight.returnLuggageOptions.length >0){
          this.returnLuggageOptions = flight.returnLuggageOptions;
        }
        if (flight.travelCreditsInfo && flight.travelCreditsInfo.length > 0) {
          let totalMoney1=0;
          let totalMonney=0
      if(this.searchService.nonCombo){
        let  price1=0;
        // totalMonney = CommonUtils.getPriceAfterTravelCredits(this.searchService.nonComboSelectedFlight[0].price,flight.travelCreditsInfo, this.noOfPassengers);  
        if(this.searchService.nonComboSelectedFlight[0].displayPrice){
            price1 = this.searchService.nonComboSelectedFlight[0].displayPrice;
        }else{
          price1 = this.searchService.nonComboSelectedFlight[0].price;
        }
        [price1,totalMoney1] = this.calculateTravelCredits(price1,totalMoney1);
        totalMonney = CommonUtils.getPriceAfterTravelCredits(price1,flight.travelCreditsInfo, this.searchService.adultCount);  
         this.totalBillPayable = (totalMonney+totalMoney1);
      }else{
        this.totalBillPayable =  CommonUtils.getPriceAfterTravelCredits(this.totalBillPayable, flight.travelCreditsInfo, this.searchService.adultCount);  
      }
        //  this.totalBillPayable = CommonUtils.getPriceAfterTravelCredits(this.totalBillPayable, flight.travelCreditsInfo, this.noOfPassengers);
          this.creditDetails = flight.travelCreditsInfo;
        }
      } else if (flight1) {
        this.flight =flight1;
        if(flight1.luggageOptions && flight1.luggageOptions.length >0){
          this.luggageOptions = flight1.luggageOptions;
        }
        if(flight1.returnLuggageOptions && flight1.returnLuggageOptions.length >0){
          this.returnLuggageOptions = flight1.returnLuggageOptions;
        }
        if (flight1.travelCreditsInfo && flight1.travelCreditsInfo.length > 0 && flight1.travelCreditsInfo[0].creditAmount > 0) {
          let totalMoney1=0;
          let totalMonney=0
      if(this.searchService.nonCombo){
        let price1 =0;
        // totalMonney = CommonUtils.getPriceAfterTravelCredits(this.searchService.nonComboSelectedFlight[0].price,flight1.travelCreditsInfo, this.noOfPassengers);  
        if(this.searchService.nonComboSelectedFlight[0].displayPrice){
          price1 = this.searchService.nonComboSelectedFlight[0].displayPrice;
      }else{
        price1 = this.searchService.nonComboSelectedFlight[0].price;
      }
           [price1,totalMoney1] = this.calculateTravelCredits(price1,totalMoney1);
         totalMonney = CommonUtils.getPriceAfterTravelCredits(price1, flight1.travelCreditsInfo, this.searchService.adultCount);
         this.totalBillPayable = (totalMonney+totalMoney1);
      }else{
        this.totalBillPayable =  CommonUtils.getPriceAfterTravelCredits(this.totalBillPayable, flight1.travelCreditsInfo, this.searchService.adultCount);  
      }
         // this.totalBillPayable = CommonUtils.getPriceAfterTravelCredits(this.totalBillPayable, flight1.travelCreditsInfo, this.noOfPassengers);
          this.creditDetails = flight1.travelCreditsInfo;
        }
      }
    }

  }
  calculateTravelCredits(price,price1){
    let carrier = this.searchService.nonComboSelectedFlight[0].legs[0].flightHops[0].carrier;
     for(let i=1;i<this.searchService.nonComboSelectedFlight.length;i++){
    if(carrier === this.searchService.nonComboSelectedFlight[i].legs[0].flightHops[0].carrier){
      if(this.searchService.nonComboSelectedFlight[i].displayPrice){
        price += this.searchService.nonComboSelectedFlight[i].displayPrice;
    }else{
        price += this.searchService.nonComboSelectedFlight[i].price;
      }
     
    }else{
      if(this.searchService.nonComboSelectedFlight[i].displayPrice){
        price1 += this.searchService.nonComboSelectedFlight[i].displayPrice;
      }else{
        price1 += this.searchService.nonComboSelectedFlight[i].price;
      }
    }
 
     }
     return [price,price1];
 
   }
  getCreditPrice(item){
    if(item && item.displayCreditAmount){
      return item.displayCreditAmount;
    }else{
      return item.creditAmount;
    }
  }

  getAddPolicyitemm(){
    let policyAdded =false;
    for(let item of this.billingItemArray){
      if(item.type==='hotel'){
        if(item.addPolicy){
          policyAdded =true;
          break;
        }
      }
    }
    return policyAdded;
  }
  getTotalResortFee(){
    let totalAmount =0;
    for(let item of this.billingItemArray){
      if(item.type==='hotel' && item.resortFee){
         
        totalAmount = totalAmount + Number(item.resortFee);

      }

    }
return totalAmount;
  }
  getTotalAmountForMultibooking(){
    let totalAmount =0;
    let totalFlight = 0;
    for(let item of this.billingItemArray){
      if(item.type==='flight'){
        if(item.credit && item.credit[0]){
          if(item.credit[0].displayCreditAmount){
          if(item.credit[0].displayCreditAmount > Number(item.amount)){
            totalFlight =totalFlight +  0;
          }else{
            totalFlight = totalFlight + (Number(item.amount) - item.credit[0].displayCreditAmount);
          }
        }else{
          if(item.credit[0].creditAmount > Number(item.amount)){
            totalFlight =totalFlight +  0;
          }else{
            totalFlight = totalFlight + (Number(item.amount) - item.credit[0].creditAmount);
          }
        }
        }else{
          totalFlight =totalFlight +  Number(item.amount);
        }
      }else{
        if(item.type==='hotel'){
          if (item.resortFee) {
            totalAmount =totalAmount + parseFloat(item.resortFee);
          }
        }
          totalAmount = totalAmount + Number(item.amount);
        
      }

    }
    if(this.getTotalAdditionalFees() != null && this.getTotalAdditionalFees() >0){
      return (totalAmount+ totalFlight + Number(this.getTotalAdditionalFees()))
    }
    return (totalAmount + totalFlight)
  }
  gettotalAmountOnPopup(amount){
   
      return amount;
  }
  getTotalAmount(item){
    let total =0
    if(item.credit && item.credit[0]){
      if(item.credit[0].displayCreditAmount){
        let totalMonney=  CommonUtils.getPriceAfterTravelCredits(item.amount, item.credit, this.noOfPassengers)
        if(item.credit[0].displayCreditAmount > Number(totalMonney)){
          total = totalMonney;
        }else{
          total = totalMonney;
        }
      }else{
        let totalMonney=  CommonUtils.getPriceAfterTravelCredits(item.amount, item.credit, this.noOfPassengers)
        if(item.credit[0].creditAmount > Number(totalMonney)){
          total = totalMonney;
        }else{
          total = totalMonney;
        }
      }
    }else{
      total = item.amount;
    }
    if(this.getTotalAdditionalFees() != null && this.getTotalAdditionalFees() >0){
      return Number(total) + Number(this.getTotalAdditionalFees());
    }
    return total;
  }
  creditWithCurrentcurrency(){

  }
  getTaxForHotel(item,item2){
    return (item-item2);
  }
  getCreditAmount(creditDetails) {
    let totalMonney = CommonUtils.getPriceAfterTravelCredits(creditDetails.amount, creditDetails.credit, this.noOfPassengers);
    let total = creditDetails.amount -totalMonney ;
    if(total > 0){
      return total
    }
   

  }
  getCreditVenndorRefrence(creditDetails) {
    let vendorRefrennce = '';
    for (let i = 0; i < creditDetails.length; i++) {
      if (i !== (creditDetails.length - 1)) {
        vendorRefrennce = vendorRefrennce + creditDetails[i].vendorReference + ",";
      } else {
        vendorRefrennce = vendorRefrennce + creditDetails[i].vendorReference;
      }
    }
    return vendorRefrennce;
  }
  public showGallopCashBack() {
    let plan: SubscriptionPlan = this.userAccountInfoService.getSubscriptionPlan();
    if (plan && plan != null && plan.id.toString() !== 'subscription_free') {
      return true;
    } else {
      return false;
    }
  }
  public getGallopCashBack() {
    // 
    return this.userAccountInfoService.getGallopCashBack(this.totalBillPayable + this.totalPayableAtHotel);
  }

  public goBack() {
    let params = "?ua_action=GoBack&ua_page=" + this.searchService.type1;
    this.searchService.letsTrack(params);
    this.searchService.paymentPage =false;
    this.userAccountInfoService.paymentPageSave = false;
    this.goBackEmitter.emit('back');
  }
  isBusinessTrip1() {
    if (!this.gallopLocalStorage.getItem("flightSearchRequestForBooking")) return true;
    let searchQuery: FlightSearchRequest = deserialize(JSON.parse(this.gallopLocalStorage.getItem("flightSearchRequestForBooking")));
    return searchQuery.algoType as AlgoTypes === AlgoTypes.PODUCTIVITY as AlgoTypes;
  }
  showExpensifyField() {
    if (this.isBusinessTrip1() && this.userAccountInfoObj && this.userAccountInfoObj.companySettings
      && this.userAccountInfoObj.companySettings.expensifySupported) {
      if ((this.uniqueAirlines && this.uniqueAirlines.length > 0) || (this.uniqueHotels && this.uniqueHotels.length > 0)) {
        return true;
      }
    }
    return false;
  }
  isExpenseIsSelected() {
    if (false && !this.manuallySwwitchedOff && this.paymentMethod === 'PERSONAL_CARD' && this.isBusinessTrip1() && this.userAccountInfoObj && this.userAccountInfoObj.companySettings
      && this.userAccountInfoObj.companySettings.expensifySupported && this.userAccountInfoObj.companySettings.expenseProviders && this.userAccountInfoObj.companySettings.expenseProviders.length > .0) {
      this.switchon = true;
      const userid = this.userAccountInfoService.getUserEmail();
      this.gallopLocalStorage.setItem("expensifyDetails", JSON.stringify({ 'addToExpensify': true, 'expensifyEmail': userid }));
      let expenseValue = '';
      for (let item of this.userAccountInfoObj.companySettings.expenseProviders) {
        if (item === 'EXPENSE_EXPENSIFY') {
          expenseValue = 'EXPENSE_EXPENSIFY';

        } else if (item === 'EXPENSE_ZOHO') {
          expenseValue = 'EXPENSE_ZOHO';

        } else if (item === 'EXPENSE_CONCUR') {
          expenseValue = 'EXPENSE_CONCUR';

        }
      }
      this.gallopLocalStorage.setItem("expenseDetails", expenseValue);
      
    } else {
      this.switchon = false;
      this.gallopLocalStorage.removeItem("expensifyDetails");
      this.gallopLocalStorage.removeItem("expenseDetails");
    }
  }
  expenseProviders() {
    if (this.isBusinessTrip1() && this.userAccountInfoObj && this.userAccountInfoObj.companySettings
      && this.userAccountInfoObj.companySettings.expensifySupported && this.userAccountInfoObj.companySettings.expenseProviders && this.userAccountInfoObj.companySettings.expenseProviders.length > .0) {
      for (let item of this.userAccountInfoObj.companySettings.expenseProviders) {
        if (item === 'EXPENSE_EXPENSIFY') {
          return 'Expensify';
        } else if (item === 'EXPENSE_ZOHO') {
          return 'Zoho';
        } else if (item === 'EXPENSE_CONCUR') {
          return 'SAP Concur';
        }
      }
    }
  }
  expenseOptionSelected(event) {
    if (event) {
      this.switchon = true;
      const userid = this.userAccountInfoService.getUserEmail();
      this.gallopLocalStorage.setItem("expensifyDetails", JSON.stringify({ 'addToExpensify': true, 'expensifyEmail': userid }));
      let expenseValue = '';
      for (let item of this.userAccountInfoObj.companySettings.expenseProviders) {
        if (item === 'EXPENSE_EXPENSIFY') {
          expenseValue = 'EXPENSE_EXPENSIFY';

        } else if (item === 'EXPENSE_ZOHO') {
          expenseValue = 'EXPENSE_ZOHO';

        } else if (item === 'EXPENSE_CONCUR') {
          expenseValue = 'EXPENSE_CONCUR';

        }
      }
      this.gallopLocalStorage.setItem("expenseDetails", expenseValue);
    } else {
      this.switchon = false;
      this.manuallySwwitchedOff = true;
      this.gallopLocalStorage.removeItem("expensifyDetails");
      this.gallopLocalStorage.removeItem("expenseDetails");
    }
  }
  showModal(modal) {
    if (!this.bookRequestProgress) {
      this.bsModalRef1 = this.modalService.show(modal);
    }
  }
  isCarAvailable(){
    let carAvailable =false;
    for(let item of this.billingItemArray){
      if(item.type=='cars'){
        carAvailable =true;
        break
      }
    }
    return carAvailable;
  }
  getTravellerName(email){
    if(this.searchService.employeeList && this.searchService.employeeList.length > 0){
      let employee  = this.searchService.employeeList.filter(item => item.email === email);
      let name;
      if (employee && employee[0] && employee[0].employeeInfo) {

          name = employee[0].firstName + " " +employee[0].lastName;
          return name
      }
      return email ;
    }
    return email;
  }
 
  getHotelCity(index){
    if(this.bookingRequest[index] && this.bookingRequest[index].bookingDetails && this.bookingRequest[index].bookingDetails.hotelSearchQuery && this.bookingRequest[index].bookingDetails.hotelSearchQuery.destination){
    if(this.searchService.multihotelQuery && this.searchService.multihotelQuery.length > 0){
      let hotel = this.searchService.multihotelQuery.filter(item => item.hotelAddress===this.bookingRequest[index].bookingDetails.hotelSearchQuery.destination) 
     if(hotel && hotel[0] && hotel[0].hotelCity){
return hotel[0].hotelCity
     }
    }
      
 
 
    }
  }
  getPickUpLocation(details,index){
   if(this.bookingRequest[index] && this.bookingRequest[index].bookingDetails && this.bookingRequest[index].bookingDetails.carSearchQuery && this.bookingRequest[index].bookingDetails.carSearchQuery.pickUp || this.bookingRequest[index] && this.bookingRequest[index].bookingDetails && this.bookingRequest[index].bookingDetails.searchQuery && this.bookingRequest[index].bookingDetails.searchQuery.pickUp){
    if(this.bookingRequest[index].bookingDetails.carSearchQuery){
    return this.bookingRequest[index].carSearchQuery.pickUp ;
    }else{
      if(this.bookingRequest[index].bookingDetails.searchQuery){
      return   this.bookingRequest[index].bookingDetails.searchQuery.pickUp
      }
    }
   }
  }
  getDropOffLocation(details,index){
    if(this.bookingRequest[index] && this.bookingRequest[index].bookingDetails && this.bookingRequest[index].bookingDetails.carSearchQuery && this.bookingRequest[index].bookingDetails.carSearchQuery.dropOff || this.bookingRequest[index] && this.bookingRequest[index].bookingDetails && this.bookingRequest[index].bookingDetails.searchQuery && this.bookingRequest[index].bookingDetails.searchQuery.dropOff){
      if(this.bookingRequest[index].bookingDetails.carSearchQuery){
      return ( " - " +this.bookingRequest[index].carSearchQuery.dropOff) ;
      }else{
        if(this.bookingRequest[index].bookingDetails.searchQuery){
        return  ( " - " +this.bookingRequest[index].bookingDetails.searchQuery.dropOff);
        }
      }
     }
  }
  goToHomePage() {
    this.searchService.bookingDone = true;
    //this.titleService.setTitle('Flight Search');
    if((this.userAccountInfoService.isUserCorporateAdmin() || this.userAccountInfoService.isUserIsTravelManager())){
      this.searchService.employeeEmail=[];
      this.searchService.bookingAndApprovalDone =true;
    }
    this.searchService.tripName='';
    this.bookingService.bookingResponse=undefined;
    if(this.searchService.multiTripBooking){
      this.searchService.multiTripBooking =false;
      this.searchService.multiflightQuery =[];
      this.searchService.multicarQuery =[];
   //   this.searchService.tripName='';
      this.gallopLocalStorage.removeItem("selectedSeat");
      this.searchService.multihotelQuery =[];
      this.searchService.previousSelectedCreditAirline=[];
    }
    this.titleService.setTitle(this.translateService.instant('search.FlightSearch'));
    this.router.navigate([this.userAccountInfoService.getDefaultRoutePath()], { queryParams: { userid: this.userId, sToken: this.sToken1 }, replaceUrl: true });
  }
  onCancelForMUltipleBooking(){
    if (this.bookRequestProgress) {
      
      this.currentBackToastId = this.toastr.info(this.translateService.instant('paymentDetails.Thebookingisinprogressandcannotbestoppedatthisstage.')).toastId;
      return ;
    }
    if(this.bookingService.bookingResponse){
this.goToHomePage();
    }
    this.bsModalRef.hide();
  
  }
  onCancel1(){
  
    this.bsModalRef1.hide();
  }
  public setCardIndex(index: number) {
    this.selectedCardIndex = index;
    this.paymentMethod = 'PERSONAL_CARD';
    this.travelApprovedCheck.setValue(false);
    this.isExpenseIsSelected();
    this.travelApprovedCheck.updateValueAndValidity();
    updateTravelCheckBoxes();
  }

  public showBillToCompany() {
    if(this.pageMode==='emailflowAgent'){
      return true;
    }
    if (this.billingItemArray[0].type === 'cars') {
      let selectedCar: any = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
      if (selectedCar && selectedCar.length >0 ) {
        this.showBillTocompany = (this.userAccountInfoService.hasBillingIdConfigured(selectedCar[0].traflaPartnerCode) &&
          this.userAccountInfoService.canAccessCompanyCard());
        return this.showBillTocompany;
      }
    }
    return this.userAccountInfoService.canAccessCompanyCard();
  }
  public isCorporateUser() {
    return this.userAccountInfoService.isCorporateUser();

  }

  // public isBillToCompany(){
  //   return this.paymentMethod === 'BILL_TO_COMPANY' ? true:false;
  // }

  public getCompanyName() {
    return this.userAccountInfoService.getUserCompany();
  }

  public setBillToCompany() {
    if (this.showBillTocompany) {
      this.paymentMethod = 'BILL_TO_COMPANY';
      this.gallopLocalStorage.removeItem("expensifyDetails");
      this.gallopLocalStorage.removeItem("expenseDetails");
    }
    this.selectedCardIndex = -1;
    this.gallopCashApplied = 0;
    this.travelApprovedCheck.setValue(false);
    this.travelApprovedCheck.updateValueAndValidity();
    updateTravelCheckBoxes();
  }

  public confirmModifiedByStripeCardDeleteModel(cardIndex: number) {
    this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
      initialState: {
        title: this.translateService.instant('paymentDetails.Deletingthecardquestion'),
        // message: This payment method will not be displayed in your list of payment options',
        message: this.translateService.instant('paymentDetails.deleteCardWaringMsg'),
        yesButtonSubText: this.translateService.instant('paymentDetails.Delete'),
        deleteCard: true
      }, backdrop: true, ignoreBackdropClick: true
    });
    this.bsModalRef.content.onClose.subscribe(result => {
      // 
      if (result) this.markModifiedByStripeCardDeleted(cardIndex);
    });
  }
  public markModifiedByStripeCardDeleted(cardIndex: number) {

    if (this.userAccountInfoObj && this.userAccountInfoObj.cardsModifiedByStripe) {
      this.userAccountInfoObj.cardsModifiedByStripe.card_list[cardIndex].markedDeleted = true;
      if (this.bsModalRef) {
        this.bsModalRef.hide();
      }
    }
  }

  public confirmCardDeleteModel(cardIndex: number) {
    this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
      initialState: {
        title: this.translateService.instant('paymentDetails.Deletingthecardquestion'),
        // message: This payment method will not be displayed in your list of payment options',
        message: this.translateService.instant('paymentDetails.deleteCardWaringMsg'),
        yesButtonSubText: this.translateService.instant('paymentDetails.Delete'),
        deleteCard: true
      }, backdrop: true, ignoreBackdropClick: true
    });
    this.bsModalRef.content.onClose.subscribe(result => {

      if (result) {
        this.userAccountInfoService.paymentPageSave = false;
        this.markCardDeleted(cardIndex);
      }
    });
  }
  public markCardDeleted(cardIndex: number) {

    if (this.userAccountInfoObj && this.userAccountInfoObj.cardList) {
      this.userAccountInfoService.requestDeleteCard(this.userAccountInfoObj.cardList.card_list[cardIndex].id).subscribe(res => {
        if (res.status === 'success') {
          this.userAccountInfoService.fetchUserAccountInfo(false);
        } else {
          this.toastr.error(res.message, 'Attention!');
        }
      });
    }
  }


  public setTravelApprovedRadio(isBusiness) {
    this.travelApprovedCheck.setValue(true);
    this.isBusinessTrip = isBusiness;
  }

  private isSearchTypeLeisure() {
    if (this.gallopLocalStorage.getItem("flightSearchRequestForBooking")) {
      let searchQuery: FlightSearchRequest = deserialize(JSON.parse(this.gallopLocalStorage.getItem("flightSearchRequestForBooking")));
      return searchQuery.algoType.toString() === AlgoTypes.LEISURE.toString();
    } else {
      return false;
    }


  }
  backToSearch() {
    //history.replaceState(null, null,'/search');
    this.bsModalRef.hide();
    this.bookingService.oldTripSessionId = undefined;
    if((this.userAccountInfoService.isUserIsTravelManager() || this.userAccountInfoService.isUserCorporateAdmin() ) && !this.bookingService.previousTransactionId){
    this.searchService.employeeEmail =[];
    this.searchService.bookingAndApprovalDone =true;
    }
    if(this.approvalSuccessPopOpen){
      this.searchService.selectedEventID =null;
      this.searchService.selectedEventForBooking=null;
    }
    if(this.searchService.multiTripBooking){
      this.searchService.multiTripBooking =false;
      this.searchService.multiflightQuery =[];
      this.searchService.multicarQuery =[];
      this.searchService.multihotelQuery =[];
      this.searchService.previousSelectedCreditAirline=[];
    }
    setTimeout(() => {
      this.searchService.bookingDone = true;
      this.router.navigate([this.userAccountInfoService.getDefaultRoutePath()]);
    }, 200);
  }
  getMaxAllowedPrice(policyFlagObject){
    let maxAllowedPrice;
    if (policyFlagObject.maxAllowedPrice){
      maxAllowedPrice = policyFlagObject.maxAllowedPrice * policyFlagObject.legs;
    }
    if (policyFlagObject.displayMaxAllowedPrice){
      maxAllowedPrice = policyFlagObject.displayMaxAllowedPrice * policyFlagObject.legs;
    }
    return maxAllowedPrice;
  }
  getClaaName(id){
    let findIndex = this.classOptions.findIndex(item => item.value.toLowerCase() ===id.toLowerCase());
    if(findIndex > -1){
      return this.classOptions[findIndex].Name;
    }
  }
  getOutsidePolicyReasonText(type) {
    let selectionType = type;
    let formatedText: string = "";
   if(!this.searchService.multiTripBooking){
    if (selectionType == 'flight') {
      let selectedFlight: FlightResult = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"));
      if (!selectedFlight) {
        selectedFlight = this.bookingService.selectedFlight;
      }
      
      let policyFlagObject;
      let policyFlagReason;
      for(let outOfPolicyLegIndex = 0; outOfPolicyLegIndex < selectedFlight[0].legs.length; outOfPolicyLegIndex++) {
        if (selectedFlight[this.bookingService.selectedPaymentEventIndex].legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext.reason !== 'WITHIN_POLICY' || selectedFlight[this.bookingService.selectedPaymentEventIndex].legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext.withoutCreditReason) {
          policyFlagObject = selectedFlight[this.bookingService.selectedPaymentEventIndex].legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext;
          if(selectedFlight[this.bookingService.selectedPaymentEventIndex].legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext.withoutCreditReason){
            policyFlagReason = selectedFlight[this.bookingService.selectedPaymentEventIndex].legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext.withoutCreditReason;
          }else{
          policyFlagReason = selectedFlight[this.bookingService.selectedPaymentEventIndex].legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext.reason;
          }
          break;
        }
      }
      if (policyFlagObject) {
        let nonComboFix = '';
        if (policyFlagObject.nonCombo) {
          nonComboFix = this.translateService.instant('paymentDetails.atleastoneof');
        }
        if (policyFlagReason === 'NONPOLICY_CLASS') {
          formatedText =this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassisabovethemaximumclassallowed') + this.translateService.instant(`${this.getClaaName(policyFlagObject.maxAllowedClass)}`) + ").";
        } else if (policyFlagReason === 'BASIC_ECO_NOT_ALLOWED') {
          formatedText =this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicybasefaresarerestricted');
        } else if (policyFlagReason === 'NOT_ACCORDING_EVENT_DEF') {
          formatedText =this.translateService.instant('setting.Thisbookingisnotaccordingtoallowedeventparameters');
        } else if (policyFlagReason === 'POLICY_CLASS_FOR_DURATION') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassisallowedONLYiftheflightdurationismorethan ') + (policyFlagObject.minFlightDurationForClass / 60) + this.translateService.instant('paymentDetails.hrs.');
        } else if (policyFlagReason === 'CLASS_APPROVAL_REQUIRED') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassrequiresadminapprovaltoproceed.');
        } else if (policyFlagReason === 'POLICY_CLASS_FOR_INTERNATIONAL') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassisallowedONLYforinternationalflights.');
        } else if (policyFlagReason === 'PRICE_OVER_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,thepriceof ') + nonComboFix +
          this.translateService.instant('paymentDetails.selectedflightismorethanmaximumallowablelimitof ')
            + this.getCurrencySymbol(this.currencyCode) + this.getMaxAllowedPrice(policyFlagObject) + " per traveler.";
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ') +
          this.translateService.instant('paymentDetails.thepriceof ')   + nonComboFix +
          this.translateService.instant('paymentDetails.theselectedflightismorethanthemaximumallowedprice')  + this.getCurrencySymbol(this.currencyCode) +
            Math.floor(policyFlagObject.allowedPriceOverCheapest * policyFlagObject.legs) + " per traveler" +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_PERCENTAGE_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ') +
          this.translateService.instant('paymentDetails.thepriceof ') + nonComboFix +
          this.translateService.instant('paymentDetails.theselectedflightismorethanthemaximumallowedprice')  + this.getCurrencySymbol(this.currencyCode) +
            Math.floor((policyFlagObject.priceOfCheapestFlight * (1 + policyFlagObject.allowedPercentagePriceOverCheapest / 100)) * policyFlagObject.legs) +  " per traveler" +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        }
      }
    } else if (selectionType == 'hotel') {
      let selectedHotelPolicyReasonObj: any = JSON.parse(this.gallopLocalStorage.getItem("selectedHotelPolicyReason"));
      if (selectedHotelPolicyReasonObj && selectedHotelPolicyReasonObj.reason) {
        let policyFlagObject = selectedHotelPolicyReasonObj;
        let policyFlagReason = selectedHotelPolicyReasonObj.reason;

        if (policyFlagReason === 'NONPOLICY_CLASS') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassisabovethemaximumclassallowed')
            + policyFlagObject.maxAllowedClass + ").";
          } else if (policyFlagReason === 'BASIC_ECO_NOT_ALLOWED') {
            formatedText =this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicybasefaresarerestricted');
          } else if (policyFlagReason === 'CLASS_APPROVAL_REQUIRED') {
          formatedText =this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassrequiresadminapprovaltoproceed.');
        } else if (policyFlagReason === 'NON_REFUNDABLE_NOT_ALLOWED') {
          formatedText =this.translateService.instant('paymentDetails.asperyourcompanytravelpolicynonrefundableratesarerestricted');
        } else if (policyFlagReason === 'PRICE_OVER_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,thepriceofselectedhotelismorethanmaximumallowablelimitof$')
          + this.getCurrencySymbol(this.currencyCode)   + this.getMaxAllowedPrice(policyFlagObject) + " per traveler.";
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ') +
          this.translateService.instant('paymentDetails.thepriceoftheselectedhotelroomismorethanthemaximumallowedprice') + this.getCurrencySymbol(this.currencyCode) +
            Math.floor(policyFlagObject.allowedPriceOverCheapest * policyFlagObject.legs) + " per traveler" +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_PERCENTAGE_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ') +
          this.translateService.instant('paymentDetails.thepriceoftheselectedhotelroomismorethanthemaximumallowedprice') + this.getCurrencySymbol(this.currencyCode) +
            Math.floor((policyFlagObject.priceOfCheapestFlight * (1 + policyFlagObject.allowedPercentagePriceOverCheapest / 100)) * policyFlagObject.legs) + " per traveler" +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        }
      }
    } else if (selectionType == 'cars') {
      let selectedCar: any = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
      let selectedCarPolicyReasonObj = selectedCar[this.bookingService.selectedPaymentEventIndex].outOfPolicyReason;
      if (selectedCarPolicyReasonObj && selectedCarPolicyReasonObj.reason) {
        let policyFlagObject = selectedCarPolicyReasonObj;
        let policyFlagReason = selectedCarPolicyReasonObj.reason;
        if (policyFlagReason === 'NONPOLICY_TYPE') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedcartypeisnotallowed.') ;
        } else if (policyFlagReason === 'NONPOLICY_CLASS') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassisabovethemaximumclassallowed') + policyFlagObject.maxAllowedClass + ").";
        } else if (policyFlagReason === 'BASIC_ECO_NOT_ALLOWED') {
          formatedText =this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicybasefaresarerestricted');
        } else if (policyFlagReason === 'CLASS_APPROVAL_REQUIRED') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassrequiresadminapprovaltoproceed.');
        } else if (policyFlagReason === 'PRICE_OVER_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,thepriceofselectedcarismorethanmaximumallowablelimitof$')
          + this.getCurrencySymbol(this.currencyCode)   + this.getMaxAllowedPrice(policyFlagObject) + ".";
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ')  +
          this.translateService.instant('paymentDetails.thepriceoftheselectedcarismorethanthemaximumallowedprice')   + this.getCurrencySymbol(this.currencyCode) +
            Math.floor(policyFlagObject.allowedPriceOverCheapest * policyFlagObject.legs) + " per traveler" +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_PERCENTAGE_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ') +
          this.translateService.instant('paymentDetails.thepriceoftheselectedcarismorethanthemaximumallowedprice')  + this.getCurrencySymbol(this.currencyCode) +
            Math.floor((policyFlagObject.priceOfCheapestFlight * (1 + policyFlagObject.allowedPercentagePriceOverCheapest / 100)) * policyFlagObject.legs) + " per traveler" +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        }
      }
    }
  }else{
    if (selectionType == 'flight') {
      let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"));
      if (!selectedFlight) {
        selectedFlight = this.bookingService.selectedFlight;
      }
      if(selectedFlight && selectedFlight.length > 0){
        selectedFlight = selectedFlight[this.bookingService.selectedPaymentEventIndex]
      }
      let policyFlagObject;
      let policyFlagReason;
      for(let outOfPolicyLegIndex = 0; outOfPolicyLegIndex < selectedFlight.legs.length; outOfPolicyLegIndex++) {
        if (selectedFlight.legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext.reason !== 'WITHIN_POLICY' || selectedFlight.legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext.withoutCreditReason) {
          policyFlagObject = selectedFlight.legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext;
          if(selectedFlight.legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext.withoutCreditReason){
            policyFlagReason = selectedFlight.legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext.withoutCreditReason;
          }else{
          policyFlagReason = selectedFlight.legs[outOfPolicyLegIndex].flightHighlights.outOfPolicyContext.reason;
          }
          break;
        }
      }
      if (policyFlagObject) {
        let nonComboFix = '';
        if (policyFlagObject.nonCombo) {
          nonComboFix = this.translateService.instant('paymentDetails.atleastoneof');
        }
        if (policyFlagReason === 'NONPOLICY_CLASS') {
          formatedText =this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassisabovethemaximumclassallowed') + this.translateService.instant(`${this.getClaaName(policyFlagObject.maxAllowedClass)}`) + ").";
        } else if (policyFlagReason === 'BASIC_ECO_NOT_ALLOWED') {
          formatedText =this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicybasefaresarerestricted');
        } else if (policyFlagReason === 'POLICY_CLASS_FOR_DURATION') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassisallowedONLYiftheflightdurationismorethan ') + (policyFlagObject.minFlightDurationForClass / 60) + this.translateService.instant('paymentDetails.hrs.');
        } else if (policyFlagReason === 'CLASS_APPROVAL_REQUIRED') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassrequiresadminapprovaltoproceed.');
        } else if (policyFlagReason === 'POLICY_CLASS_FOR_INTERNATIONAL') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassisallowedONLYforinternationalflights.');
        } else if (policyFlagReason === 'PRICE_OVER_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,thepriceof ') + nonComboFix +
          this.translateService.instant('paymentDetails.selectedflightismorethanmaximumallowablelimitof ') 
            + this.getCurrencySymbol(this.currencyCode) + this.getMaxAllowedPrice(policyFlagObject) + " per traveler.";
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ') +
          this.translateService.instant('paymentDetails.thepriceof ')   + nonComboFix +
          this.translateService.instant('paymentDetails.theselectedflightismorethanthemaximumallowedprice')  + this.getCurrencySymbol(this.currencyCode) +
            Math.floor(policyFlagObject.allowedPriceOverCheapest * policyFlagObject.legs) +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_PERCENTAGE_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ') +
          this.translateService.instant('paymentDetails.thepriceof ') + nonComboFix +
          this.translateService.instant('paymentDetails.theselectedflightismorethanthemaximumallowedprice')  + this.getCurrencySymbol(this.currencyCode) +
            Math.floor((policyFlagObject.priceOfCheapestFlight * (1 + policyFlagObject.allowedPercentagePriceOverCheapest / 100)) * policyFlagObject.legs) +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        }
      }
    } else if (selectionType == 'hotel') {
      let selectedHotel = JSON.parse(this.gallopLocalStorage.getItem('selectedHotelDetailedObj'));
      if(selectedHotel[this.bookingService.selectedPaymentEventIndex].hotelOutOfPolicyReason  && selectedHotel[this.bookingService.selectedPaymentEventIndex].hotelOutOfPolicyReason.reason &&selectedHotel[this.bookingService.selectedPaymentEventIndex].hotelOutOfPolicyReason.reason!== 'WITHIN_POLICY'){
      //  this.selectedOutpolicyIndex=i;
        this.gallopLocalStorage.setItem("selectedHotelPolicyReason", JSON.stringify(selectedHotel[this.bookingService.selectedPaymentEventIndex].hotelOutOfPolicyReason));
      }
      let selectedHotelPolicyReasonObj: any = JSON.parse(this.gallopLocalStorage.getItem("selectedHotelPolicyReason"));
      if (selectedHotelPolicyReasonObj && selectedHotelPolicyReasonObj.reason) {
        let policyFlagObject = selectedHotelPolicyReasonObj;
        let policyFlagReason = selectedHotelPolicyReasonObj.reason;

        if (policyFlagReason === 'NONPOLICY_CLASS') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassisabovethemaximumclassallowed')
            + policyFlagObject.maxAllowedClass + ").";
          } else if (policyFlagReason === 'BASIC_ECO_NOT_ALLOWED') {
            formatedText =this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicybasefaresarerestricted');
          } else if (policyFlagReason === 'CLASS_APPROVAL_REQUIRED') {
          formatedText =this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassrequiresadminapprovaltoproceed.');
        } else if (policyFlagReason === 'NON_REFUNDABLE_NOT_ALLOWED') {
          formatedText =this.translateService.instant('paymentDetails.asperyourcompanytravelpolicynonrefundableratesarerestricted');
        } else if (policyFlagReason === 'PRICE_OVER_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,thepriceofselectedhotelismorethanmaximumallowablelimitof$')
          + this.getCurrencySymbol(this.currencyCode)   + this.getMaxAllowedPrice(policyFlagObject) + " per traveler.";
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ') +
          this.translateService.instant('paymentDetails.thepriceoftheselectedhotelroomismorethanthemaximumallowedprice') + this.getCurrencySymbol(this.currencyCode) +
            Math.floor(policyFlagObject.allowedPriceOverCheapest * policyFlagObject.legs) +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_PERCENTAGE_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ') +
          this.translateService.instant('paymentDetails.thepriceoftheselectedhotelroomismorethanthemaximumallowedprice') + this.getCurrencySymbol(this.currencyCode) +
            Math.floor((policyFlagObject.priceOfCheapestFlight * (1 + policyFlagObject.allowedPercentagePriceOverCheapest / 100)) * policyFlagObject.legs) +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        }
      }
    } else if (selectionType == 'cars') {
      let selectedCar: any = JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
      let selectedCarPolicyReasonObj = selectedCar[this.bookingService.selectedPaymentEventIndex].outOfPolicyReason;
      if (selectedCarPolicyReasonObj && selectedCarPolicyReasonObj.reason) {
        let policyFlagObject = selectedCarPolicyReasonObj;
        let policyFlagReason = selectedCarPolicyReasonObj.reason;
        if (policyFlagReason === 'NONPOLICY_TYPE') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedcartypeisnotallowed.') ;
        } else if (policyFlagReason === 'NONPOLICY_CLASS') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassisabovethemaximumclassallowed') + policyFlagObject.maxAllowedClass + ").";
        } else if (policyFlagReason === 'BASIC_ECO_NOT_ALLOWED') {
          formatedText =this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicybasefaresarerestricted');
        } else if (policyFlagReason === 'CLASS_APPROVAL_REQUIRED') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,theselectedclassrequiresadminapprovaltoproceed.');
        } else if (policyFlagReason === 'PRICE_OVER_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicy,thepriceofselectedcarismorethanmaximumallowablelimitof$')
          + this.getCurrencySymbol(this.currencyCode)   + this.getMaxAllowedPrice(policyFlagObject) + ".";
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_ABS_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ')  +
          this.translateService.instant('paymentDetails.thepriceoftheselectedcarismorethanthemaximumallowedprice')   + this.getCurrencySymbol(this.currencyCode) +
            Math.floor(policyFlagObject.allowedPriceOverCheapest * policyFlagObject.legs) +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        } else if (policyFlagReason === 'PRICE_OVER_CHEAPEST_PERCENTAGE_MARKUP') {
          formatedText = this.translateService.instant('paymentDetails.Asperyourcompanystravelpolicyandcurrentavailability, ') +
          this.translateService.instant('paymentDetails.thepriceoftheselectedcarismorethanthemaximumallowedprice')  + this.getCurrencySymbol(this.currencyCode) +
            Math.floor((policyFlagObject.priceOfCheapestFlight * (1 + policyFlagObject.allowedPercentagePriceOverCheapest / 100)) * policyFlagObject.legs) +
            this.translateService.instant('paymentDetails.)forthegivensearchinput.');
        }
      }
    }
  }
  
    if(this.userAccountInfoService.approvalRequiredFor === 'ALL_BOOKINGS' && formatedText===''){
      formatedText =this.translateService.instant('employee.Asperyourcompanystravelpolicyallbookingsneedtogototheadminforapproval')
    }
    return formatedText;
  }
  hideConfirmationCheckBoxes() {
    return !this.isCorporateUser() || this.userAccountInfoService.restrictOutsidePolicyBooking();
  }
  onRequestChange(){
    if(this.approvalRequest){
   this.proceedForApproval();
    }else{
      this.proceedForBooking();
    }
    if(this.bsModalRef){
      this.bsModalRef.hide();
    }
  }

  tagsSetData = {
    "isMandotary":false,
    "tags":[]
  }

  getAllTagsSet(){
    this.tagsSetData.tags = [];
    this.new_tagSets_array.forEach(e =>{
      let mandatory = false;
      if(e.mandatory && e.activeTagCount > 0){
        this.tagsSetData.isMandotary = true;
        mandatory = true;
      }
        const obj  = {
          "companyId": e.companyId,
          "tagsetId": e.tagsetId,
          "tagSetName": e.tagSetName,
          "mandatory": mandatory,
          "tags":""
       };

       this.tagsSetData.tags.push(obj)
    })
  }

   

  checkTripTagIsSelected(){
    if(this.tempSelectTag.length > 0){
      this.getAllTagsSet();
      this.tempSelectTag.forEach((a)=>{
        if(a != empty){
          this.all_tags.forEach((b)=>{
            const getTag_id = b.find(c => c['tag_id'] == a);
            if(getTag_id){
              this.tagsSetData.tags.forEach((d,i)=>{
                  if(d.tagsetId == getTag_id.tagsetId){
                    this.tagsSetData.tags[i].tags = getTag_id;
                  };
              });
            }
          });
        };

      });
    }else{
      this.getAllTagsSet();
    }
    
    
  };
tagsUnselectedName = "";
checktripsTagsAreValid(){
  if(this.tagsSetData.isMandotary){
    this.checkTripTagIsSelected();
    for (let i = 0; i < this.tagsSetData.tags.length; i++) {
      const e = this.tagsSetData.tags[i];
      if(e.mandatory){
        if(e.tags == ""){
          this.tagsUnselectedName = e.tagSetName
          return false;
        };
      };
    };
     return true; 
  }else{
    return true;
  }
}
messageForadmin(event){
  if(event && event.length > 0){
    this.messageError =false;
  }
}
messageError =false;
approvalRequest =false;


  private isApprovalRequired() {
    if (this.userAccountInfoService.approvalRequiredFor === 'NONE') {
      return false
    }
    if (this.isBookingForSelf() 
      || (!this.userAccountInfoService.isUserIsTravelManager() && !this.userAccountInfoService.isUserCorporateAdmin())) {
      if (this.outsidePolicyFlag || this.userAccountInfoService.approvalRequiredFor === 'ALL_BOOKINGS') {
        return true;
      }
      return false;
    }
    // Boook for Other by travelmanager
    return false;
  }
  isCreditHandledAutomatically(){
    let count=0;
    for(let item of this.billingItemArray){
      if(item.type==='flight'){
        if(item.credit && item.credit[0]){
          if(!item.credit[0].handledAutomatically){
            count=1;
          }
        }
      }
    }
    if(count > 0){
      return false;
    }
    return true;
  }
  public getBookButtonLabel() {
    if (this.pageMode === 'emailflowAgent'){
      return this.translateService.instant('paymentDetails.Generate');
    }
    if (this.isApprovalRequired()) {
      if ( this.userAccountInfoService.approvalRequiredFor === 'ALL_BOOKINGS') {
        return this.translateService.instant('paymentDetails.SUBMITFORAPPROVAL');
      } else {
        return this.translateService.instant('paymentDetails.SENDAPPROVALREQUEST');
      }
    } else {
      if (!this.isCreditHandledAutomatically()) {
        return this.translateService.instant('paymentDetails.SENDREQUEST');
      } else {
        return this.translateService.instant('paymentDetails.Book');
      }
    }
  }
  responseSeatData: any = [];
  source: Array<any> = [];
  seatData: Array<any>[];
  
  mapSeatWithHop(responseData,selectedSeatArray,index) {
    let j = 0
    for (let i = 0; i < this.source.length; i++) {
      var getIndex;
      let source = this.source[i].source;
      let destination = this.source[i].dest;
      let datetime = this.source[i].group;
      getIndex = responseData.find(item =>
        (item.origin === source && item.destination === destination && item.group === datetime));
      if (getIndex && getIndex.seat) {
        this.responseSeatData[j] = getIndex;
        j = j + 1;
      }
    }
    //  
    if (this.responseSeatData && this.responseSeatData.length > 0) {
      for (let counter = 0; counter < this.responseSeatData.length; counter++) {
        this.seatData[counter] = this.responseSeatData[counter].seat;
        if(this.seatData[counter]){
          for(let item of this.seatData[counter]){
            let index=0;
            for(let seat of item.row){
              if(seat.type==='Seat' && (seat.available==='AVAILABLE' ||  seat.available==='Premium')){
                if(selectedSeatArray[counter].length < this.noOfPassengers) {
                  selectedSeatArray[counter].push(seat.seatCode)
                }
              }
            }

          }

        }
      }
      let seatSelectArray=[];
      let travellersArray = this.bookingService.getTravelerDetails('WebSearch');
      for (let i = 0; i < this.responseSeatData.length; i++) {
        // var passenger = parseInt(this.responseData[i].passengerName.match(/\d+/),10);
        for (let j = 0; j < selectedSeatArray[i].length; j++) {
          let seatObject: SeatSelect = { origin: '', destination: '', airlineCode: '', airlineNumber: '', passengerName: '', group: '', seatNumber: [] };
          seatObject.origin = this.responseSeatData[i].origin;
          seatObject.destination = this.responseSeatData[i].destination;
          seatObject.airlineCode = this.responseSeatData[i].airlineCode;
          seatObject.airlineNumber = this.responseSeatData[i].airlineNumber;
          seatObject.passengerName = travellersArray.travellers[j].firstName;
          seatObject.group = this.responseSeatData[i].group;
          seatObject.seatNumber = selectedSeatArray[i][j];
        seatSelectArray.push(seatObject);
        }
      }
      let selectSeat =  JSON.parse(this.gallopLocalStorage.getItem("selectedSeat"));
      this.searchService.seatForMultipleBooking[index] = [...seatSelectArray];
      let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
      let arrayOfSeat = new Array(selectedFlight.length).fill(null).map(_ => []);;
      if(selectSeat && selectSeat.length > 0){
        arrayOfSeat=[...selectSeat]
        arrayOfSeat[index] = this.searchService.seatForMultipleBooking[index];
      }else{
        arrayOfSeat[index] = this.searchService.seatForMultipleBooking[index];
    
      }
      this.gallopLocalStorage.setItem("selectedSeat", JSON.stringify(arrayOfSeat));
    }

  
  }
  economyTravelFusionSeatMapping(){
    let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"))
   let selectSeat =  JSON.parse(this.gallopLocalStorage.getItem("selectedSeat"));
   let index=0
    if(selectedFlight && selectedFlight.length > 0 && !selectSeat || (selectSeat && selectedFlight && selectedFlight.length !== selectSeat.length) || (selectedFlight && selectedFlight.length > 0 && selectSeat && selectSeat[index] && selectSeat[index].length ===0)){
      for(let item of selectedFlight){
        if(item.handlerType==='TRAVELFUSION' && item.legs[0] && item.legs[0].flightHops[0].fareClassName && item.legs[0].flightHops[0].fareClassName==='ECONOMY WITH ECONOMY PLUS SEAT'){
          
          let responseData = this.bookingService.forMultipleBooking[index];
          this.source=[];
          
          let selectedSeatArray = new Array(responseData.length).fill(null).map(_ => []);
          this.seatData = new Array(responseData.length).fill(null).map(_ => []);
          this.responseSeatData=[];
          if(this.searchService.multiTripBooking){
            let seatArray = JSON.parse(this.gallopLocalStorage.getItem("selectedSeat"));
            if(seatArray && seatArray.length > 0){
              let counter=0;
              if(seatArray[index] && seatArray[index].length > 0){
                
                for(let item of seatArray[index]){
                  let seatTempArray=[];
                  seatTempArray.push(item.seatNumber);
                  this.bookingService.selectedSeatArray[counter] = seatTempArray;
                    counter++;
                  
                }
               // this.selectedSeatArray[this.seatIndex] = seatArray[this.seatIndex]
              }else{
                this.bookingService.selectedSeatArray=[];
              }
        
            }else{
              this.bookingService.selectedSeatArray=[];
            }
          }else{
            this.bookingService.selectedSeatArray=[];
          }
          if (this.bookingService.selectedSeatArray && this.bookingService.selectedSeatArray.length > 0) {
            for (let counter = 0; counter < this.bookingService.selectedSeatArray.length; counter++) {
              selectedSeatArray[counter] = this.bookingService.selectedSeatArray[counter];
            }
          }
          for (let item of responseData) {
            //for(let  item1 of item){
            // for(let  item2 of item1.flight_hops){
            //  flightObject.flightNumber = item2.flightNumber;
            let flightObject = { flightNumber: '', source: '', dest: '', group: '' }
            flightObject.source = item.origin;
            flightObject.dest = item.destination;
            flightObject.group = item.group;
            this.source.push(flightObject);
            //}
            //}
          }
          this.mapSeatWithHop(responseData,selectedSeatArray,index);
          index = index+1;
        }
      }
    
    }
  }
  public confirmBookAndPay(modal,modal1){
    const userid = this.userAccountInfoService.getUserEmail();
    if(this.checktripsTagsAreValid()) {
    //  if (this.userAccountInfoService.isConnected) {
        if(this.bookingService.previousTransactionId && !this.bookingService.rebookingConfirm){
          this.bookingService.bookingButtonClicked =true;
          this.reBookEmitter.emit(true);
          return;
         }
        

       this.economyTravelFusionSeatMapping();
               if(this.bsModalRef) {
          this.bsModalRef.hide();
        }
      if (this.pageMode !== 'emailflowAgent' && this.isApprovalRequired()) {
        if(this.searchService.travelPurposeMandatory){
          if(!this.messageForAdmin){
            this.messageError =true;
            return;
          }
        }
        this.currentModel = modal;
        this.bookRequestProgress =true;
        this.disabled = true;
       this.proceedForApproveTripSession();
      } else {
        this.proceedForBookingTripSession(modal1);
      }
  
  }else{
    this.toastr.error(this.translateService.instant('paymentDetails.RequestToSelectTag') + " " +this.tagsUnselectedName + "!");
   }

  }
 
 getTripType(item){
  if(item.legs && item.legs.length ===1){
    return '';
  }else  if(item.legs && item.legs.length ===2){
    if(item.legs[0].flightHops[0].from === item.legs[1].flightHops[item.legs[1].flightHops.length-1].to && item.legs[0].flightHops[item.legs[0].flightHops.length-1].to === item.legs[1].flightHops[0].from){
      return this.translateService.instant("flightSelected.Roundtrip");
    }else{
      return this.translateService.instant("flightSelected.Multicity");
    }
  }else{
    return this.translateService.instant("flightSelected.Multicity");
  }
}
  getSourceAndDestination(item){
    if(item.legs && item.legs.length ===1){
      return (item.legs[0].flightHops[0].from +"-"+item.legs[0].flightHops[item.legs[0].flightHops.length-1].to);
    }else  if(item.legs && item.legs.length ===2){
      if(item.legs[0].flightHops[0].from === item.legs[1].flightHops[item.legs[1].flightHops.length-1].to && item.legs[0].flightHops[item.legs[0].flightHops.length-1].to === item.legs[1].flightHops[0].from){
        return (item.legs[0].flightHops[0].from +"-"+item.legs[0].flightHops[item.legs[0].flightHops.length-1].to)
      }else{
        let info='';
        let index=0;
        for(let leg of item.legs){
         info = info +  leg.flightHops[0].from +"-"+leg.flightHops[leg.flightHops.length-1].to
         if(index!==(item.legs.length-1)){
           info = info +", "
         }
         index=index+1;
        }
        return info;
      }
    }else{
      let info='';
        let index=0;
        for(let leg of item.legs){
         info = info +  leg.flightHops[0].from +"-"+leg.flightHops[leg.flightHops.length-1].to
         if(index!==(item.legs.length-1)){
           info = info +", "
         }
         index=index+1;
        }
        return info;
    }
  }
  private buildTripSessionBookingList(){
    let tripSessionBookingRequestList =  new Array();
    let tripSessionLoyalityNumbers = {ffn:[],clnno:[],hlnno:[]};
    let flightIndex = -1;
    let hotelIndex = -1;
    let carIndex = -1;
    let paymentMethod = "";
    let seatSelected = JSON.parse(this.gallopLocalStorage.getItem("selectedSeat"));
    for(let i=0;i<this.tripDetails.length;i++){

      if(this.tripDetails[i].ffnno && this.tripDetails[i].ffnno.length > 0){
        tripSessionLoyalityNumbers.ffn = tripSessionLoyalityNumbers.ffn.concat(this.tripDetails[i].ffnno);
      }else if(this.tripDetails[i].hlnno && this.tripDetails[i].hlnno.length >0 ){
        tripSessionLoyalityNumbers.hlnno = tripSessionLoyalityNumbers.hlnno.concat(this.tripDetails[i].hlnno);
      }else if(this.tripDetails[i].clnno && this.tripDetails[i].clnno.length > 0){
        tripSessionLoyalityNumbers.clnno = tripSessionLoyalityNumbers.clnno.concat(this.tripDetails[i].clnno);
      }
    if(this.tripDetails[i]  && this.tripDetails[i].paymenttype){
      if(this.tripDetails[i].paymenttype.id==='Bill to company'){
        paymentMethod = 'BILL_TO_COMPANY';
      }else{
        paymentMethod = 'PERSONAL_CARD'
      }
    let  selectedCardDetails =
      (this.isLoggedIn() && this.tripDetails[i].paymenttype)
        ? this.tripDetails[i].paymenttype : this.tripDetails[i].paymenttype;
        let cardType = this.isLoggedIn() ? 'card' : 'token';
        if (this.tripDetails[i].type === 'flight') {
          flightIndex++;
          // this.bookFlight(selectedCardDetails, !(this.isCorporateUser() && !this.isBusinessTrip), '');
          let bookFlightRequestData = this.bookingService.buildBookFlightRequestData(
            !(this.isCorporateUser() && !this.isBusinessTrip),
           paymentMethod, selectedCardDetails, cardType, this.gallopCashApplied,
            this.ticketId, this.tripId, this.eventIdAndOptions, '', this.tempSelectTag,flightIndex);
            bookFlightRequestData.selectedSeat = (seatSelected && seatSelected.length > 0) ? seatSelected[i]:[];
          tripSessionBookingRequestList.push({"type":"flight","bookingDetails":bookFlightRequestData});
        } else if ((this.tripDetails[i].type === 'hotel')) {
          hotelIndex++;
          // this.bookHotel(selectedCardDetails, !(this.isCorporateUser() && !this.isBusinessTrip), '');
          // details, cardType, gallopCash, ticketId, tripId, eventIdAndOptions, selectedTag,index
          let bookHotelRequestData = this.bookingService.buildHotelTripItemBookingRequest( paymentMethod,
            selectedCardDetails,cardType,this.gallopCashApplied,
            this.ticketId, this.tripId, this.eventIdAndOptions,
            this.tempSelectTag,hotelIndex);
          //   !(this.isCorporateUser() && !this.isBusinessTrip),
          // paymentMethod, selectedCardDetails, cardType, this.gallopCashApplied,
          //  this.ticketId, this.tripId, this.eventIdAndOptions, '', this.tempSelectTag,hotelIndex
          tripSessionBookingRequestList.push({"type":"hotel","bookingDetails":bookHotelRequestData});
        } else if (this.tripDetails[i].type === 'car') {
          carIndex++;
          // this.bookCar(selectedCardDetails, !(this.isCorporateUser() && !this.isBusinessTrip), '');
          let bookCarRequestData = this.carBookingService.buildCarBookingRequest( paymentMethod,
            selectedCardDetails,cardType,this.gallopCashApplied,
            this.ticketId, this.tripId, this.eventIdAndOptions,
            this.tempSelectTag,carIndex);
            tripSessionBookingRequestList.push({"type":"cars","bookingDetails":bookCarRequestData});
        }
    }
   
  }
  return {"tripSessionBookingRequestList":tripSessionBookingRequestList,"tripSessionLoyalityNumbers":tripSessionLoyalityNumbers};
  }
  private proceedForBookingTripSession(modal?) {
   // if (this.userAccountInfoService.isConnected) {
     
      // history.pushState(null, null,window.location.href);
      if (this.pageMode === 'WebSearch') {
        for(let i=0;i<this.tripDetails.length;i++){
         if(!(this.tripDetails[i]  && this.tripDetails[i].paymenttype)){
          let errMsg = this.translateService.instant('paymentDetails.Pleaseprovidecreditcardforpayment').toString();
          this.toastr.error(errMsg, this.translateService.instant('paymentDetails.Attention').toString());
          return;
          }
        }
        let tripSesssionBookingRequestData = this.buildTripSessionBookingList();
        let tripSessionBookingRequestList = tripSesssionBookingRequestData.tripSessionBookingRequestList;
        this.bookingRequest = tripSessionBookingRequestList;
        
        this.bookRequestProgress = true;
        this.changingValue.next({value1:this.bookRequestProgress});
        this.bookingService.bookRequestProgress = true;


        this.bookingService.bookTripSession(tripSessionBookingRequestList, 
          tripSesssionBookingRequestData.tripSessionLoyalityNumbers,this.searchService.tripName,
          !(this.isCorporateUser() && !this.isBusinessTrip), this.tempSelectTag,this.messageForAdmin)
        .subscribe((res) => {
          if (this.bsModalRef) {
            this.bsModalRef.hide();
          }
          if((this.numOfTravelelrExculidngChild >1) &&  (this.numOfTravelelrExculidngChild=== (this.searchService.employeeEmail && this.searchService.employeeEmail.length)) || this.searchService.multiTripBooking){
            this.bsModalRef = this.modalService.show(modal, {
              initialState: {
              }, backdrop: true, ignoreBackdropClick: true
            });
          }
           this.pollingStarted=true;
          this.processBookTripSessionResponse(res);
        }, error => {
          if (error.status != 403) {
            setTimeout(() => {
              if(!this.pollingStarted){
              this.bookRequestProgress =false;
              this.bookingService.bookRequestProgress =false;
            }
              
             
            }, 100);
          }
     //     CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_BOOK,this.translateService);
        });
      }else {
        //   var selectCar=JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
        if (this.getSelectionType() === 'cars' && this.canAttemptDirectBooking && this.pageMode !== 'emailflowAgent') {
          this.bookRequestProgress = true;
          this.changingValue.next({value1:this.bookRequestProgress});
          this.bookingRequestStarted = true;
          let emailbookReq: EmailFlowBookRequest = new EmailFlowBookRequest();
          emailbookReq.appliedGallopCash = this.gallopCashApplied;
          if (this.isLoggedIn()) {
            emailbookReq.selectedCard =
              (this.selectedCardIndex >= 0) ? this.userAccountInfoObj.cardList.card_list[this.selectedCardIndex] : undefined;
          }
          emailbookReq.ticketId = this.ticketId;
          emailbookReq.tripId = this.tripId;
          emailbookReq.paymentMethod = this.paymentMethod;
          emailbookReq.deleted_cards = this.getDeletedCardArray();
          this.bookCar(emailbookReq.selectedCard, !(this.isCorporateUser() && !this.isBusinessTrip), this.bookingStatusId);
        } else {
          this.makeBookingConfirmRequest();
        }
      }
  

  }
 isGuestTravelerPresent(){
  
    this.toastr.success(this.translateService.instant('paymentDetails.ThetravelersprofilehasbeensavedinGuestTravelersdepartment').toString());
   
 }
  private proceedForApproveTripSession() {
  // if (this.userAccountInfoService.isConnected) {
     
      // history.pushState(null, null,window.location.href);
      if (this.pageMode === 'WebSearch') {
        for(let i=0;i<this.tripDetails.length;i++){
          if(!(this.tripDetails[i]  && this.tripDetails[i].paymenttype)){
          let errMsg = this.translateService.instant('paymentDetails.Pleaseprovidecreditcardforpayment').toString();
          this.toastr.error(errMsg, this.translateService.instant('paymentDetails.Attention').toString());
          return;
          }
        }
        this.bookingService.bookRequestProgress = true;
        let tripSesssionBookingRequestData = this.buildTripSessionBookingList();
        let tripSessionBookingRequestList = tripSesssionBookingRequestData.tripSessionBookingRequestList;
        this.bookingService.sendTripSessionBookingApproval(tripSessionBookingRequestList,tripSesssionBookingRequestData.tripSessionLoyalityNumbers,this.messageForAdmin,
          this.searchService.tripName,!(this.isCorporateUser() && !this.isBusinessTrip), this.tempSelectTag)
        .subscribe((res) => {
          // if (this.bsModalRef) {
          //   this.bsModalRef.hide();
          // }
          this.bookingService.previousBooking =undefined;
          this.bookingService.previousTransactionId= undefined;
          this.pollingStarted=true;
          this.processApprovalPolling(res);
  
        }, error => {
          if (error.status != 403) {
            setTimeout(() => {
            if(!this.pollingStarted){
              this.bookRequestProgress =false;
              this.bookingService.bookRequestProgress =false;
              this.pollingStarted=false;
            }
              
             
            }, 100);
          }
        //  CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_BOOK,this.translateService);
        });
      }
   

  }
  public confirmBookAndPayy(modal, modal1?) {
    if(this.checktripsTagsAreValid()){

  //  if (this.userAccountInfoService.isConnected) {
     // this.tempSelectTag = this.selectedTag;
     if(this.bookingService.previousTransactionId && !this.bookingService.rebookingConfirm){
      this.bookingService.bookingButtonClicked =true;
      this.reBookEmitter.emit(true);
      return;
     }
      const userid = this.userAccountInfoService.getUserEmail();
      if ((this.paymentMethod === 'PERSONAL_CARD' && this.selectedCardIndex >= 0)
        && ((this.bookingService.cardAllowed && Object.keys(this.bookingService.cardAllowed).length !== 0) 
        && this.showPaymentDeatils)) {
        const cardName = (this.tripDetails[0].paymenttype.brand.replace(' ', '')).toLowerCase();
        const withoutSpaceCardAllowed = {};
        for(const key in this.bookingService.cardAllowed) {
          if (this.bookingService.cardAllowed.hasOwnProperty(key)) {
            withoutSpaceCardAllowed[key] = (this.bookingService.cardAllowed[key].replace(' ', '')).toLowerCase();
          }
        }
 //        this.bookingService.cardAllowed = withoutSpaceCardAllowed;
        if (!Object.values(withoutSpaceCardAllowed).includes(cardName)) {
          let  str1 =  Object.values(this.bookingService.cardAllowed)[0];
          for(let i=1;i< Object.keys(this.bookingService.cardAllowed).length;i++) {
            if (i === Object.keys(this.bookingService.cardAllowed).length - 1) {
              str1 = str1 + ' or ' + Object.values(this.bookingService.cardAllowed)[i] + '.';
            } else {
              str1 = str1 + ', ' + Object.values(this.bookingService.cardAllowed)[i];
            }
          }
          this.toastr.error(this.translateService.instant('paymentDetails.ThiscardisnotacceptedbytherentalcompanyPleaseuse')+ ' ' + str1,
            this.translateService.instant('paymentDetails.Attention').toString());
          return;
        }
      }
      if (this.tripDetails[0] && !this.tripDetails[0].prepay && Object.keys((this.tripDetails[0].paymenttype)).length ==0) {
        let errMsg = this.translateService.instant('paymentDetails.Pleaseprovidecreditcardforpayment').toString();
        this.toastr.error(errMsg, this.translateService.instant('paymentDetails.Attention').toString());
        return;
      }
     
      if (((!this.userAccountInfoService.isUserIsTravelManager() && !this.userAccountInfoService.isUserCompanyAdmin()) || this.isBookingForSelf())
        && this.userAccountInfoService.approvalRequiredFor === 'ALL_BOOKINGS'
        && this.pageMode !== 'emailflowAgent' && this.isCorporateUser() && !this.outsidePolicyFlag) {
          if(this.messageForAdmin && this.messageForAdmin.length===0){
            this.messageError =true;
            return;
           }
           //this.approvalPopOpen = true;
          this.proceedForApproveTripSession();
        
       // this.proceedForApproval();
      } else if ((!this.userAccountInfoService.isUserCompanyAdmin() || this.isBookingForSelf())
        && this.userAccountInfoService.approvalRequiredFor !== 'NONE'
        && this.pageMode !== 'emailflowAgent'
        && this.isCorporateUser() && this.outsidePolicyFlag && this.userAccountInfoService.restrictOutsidePolicyBooking()) {
      this.disabled= false;
        //history.pushState(null, null,window.location.href);
        if(this.messageForAdmin && this.messageForAdmin.length===0){
          this.messageError =true;
          return;
         }
         //this.approvalPopOpen = true;
        this.proceedForApproveTripSession();
      } else {
        if (this.isCorporateUser() && this.userAccountInfoService.approvalRequiredFor !== 'NONE') {
          this.travelApprovedCheck.markAsTouched();
          this.travelApprovedCheck.updateValueAndValidity();
          if (!this.userAccountInfoService.restrictOutsidePolicyBooking() && !this.travelApprovedCheck.value) return;
          if (!this.userAccountInfoService.isUserCompanyAdmin() && !this.isSearchTypeLeisure() && this.outsidePolicyFlag && this.isBusinessTrip) {

            this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
              initialState: {
                title: this.translateService.instant('paymentDetails.Areyousureyouwanttoproceed'),
                // message: 'The options you selected are outside the company\'s travel policy',

                message: this.translateService.instant('paymentDetails.optionOutsidePolicyText'),
                yesButtonSubText: this.translateService.instant('paymentDetails.Proceed'),
              }, backdrop: true, keyboard: false, ignoreBackdropClick: true
            });
            this.bsModalRef.content.onClose.subscribe(result => {

              if (result) this.proceedForBooking();
            });
          } else{
          this.proceedForBooking();
          }
        } else {
         
          this.proceedForBooking();
        }
      }
   
   }else{
    this.toastr.error(this.translateService.instant('paymentDetails.RequestToSelectTag') + " " +this.tagsUnselectedName + "!");
   }
  }
  public onCancel(): void {
    if (this.bookRequestProgress) {
      this.bookRequestProgress = false;
    }
    this.bsModalRef.hide();
  }
  currentModel;
  onModelCancel(){
    this.bsModalRef.hide();
  }
  getDisplayDate(dateString: string): string {
    return DateUtils.getDisplayDate(dateString);
  }
  getDisplayDate1(dateString: string, format: string): string {
    return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
  }
  getDisplayDateTimeForFlights(dateString: string, format: string): string {
    return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }
  sendApprovalRequest(modal) {
   // if (this.userAccountInfoService.isConnected) {
      this.currentModel = modal;
     
      this.approvalRequest =false;
      // this.proceedForApproval();
      this.proceedForApproveTripSession();
      // history.pushState(null, null,window.location.href);
      this.bookRequestProgress =true;
      this.disabled = true;
   
  }
  showMoreOptions(modal) {
    if(this.bsModalRef){
    this.bsModalRef.hide();
    }
    this.approvalPopOpen = false;
    this.bookingService.priceChange = false;
    this.goBackEmitter.emit('backToSelectDifferentOption');
    //    this.goBackToList()
  }
  isUserisNotAmdinNorManager(){
    if(this.userAccountInfoService.approvalRequiredFor==='ALL_BOOKINGS'){
        return true;
    }
    else if ((!this.userAccountInfoService.isUserCompanyAdmin() && !this.userAccountInfoService.isUserIsTravelManager())
    && this.isNotBookingForSelf()) {
    return true;
  }if ((!this.userAccountInfoService.isUserCompanyAdmin() && !this.userAccountInfoService.isUserIsTravelManager())
  && this.isBookingForSelf()) {
  return true;
}else{
    return false;
  }
  }
  getApprovalAcess() {
    if(this.userAccountInfoService.isUserIsTravelManager()
    && !this.outsidePolicyFlag && !this.isBookingForSelf()){
      return true;
    }
    
    if ((this.userAccountInfoService.isUserCompanyAdmin() || this.userAccountInfoService.isUserIsTravelManager())
    && this.isNotBookingForSelf()) {
    return true;
  }
    if (this.userAccountInfoService.isUserCompanyAdmin()
      && this.isNotBookingForSelf()) {
      return true;
    } else if (!this.isUserisNotAmdinNorManager()){

       return true;
    }
      else if(this.userAccountInfoService.approvalRequiredFor!=='ALL_BOOKINGS' && !this.outsidePolicyFlag){
            return true;
    }
      else {
      return false;
    }
  }
  goBackToList() {
    this.location.back();
  }
  allowGallopCashUsage() {
    if (this.getGallopCash() > 0) {
      if (this.pageMode === 'WebSearch') {
        if (this.getSelectionType() === 'flight') {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    } else {
      return false;
    }
  }
  selectedOutpolicyIndex=-1;
  getSelectionType() {
    if (this.gallopLocalStorage.getItem('selectedFlight')
      && this.gallopLocalStorage.getItem('selectedFlight') !== null) {
      return 'flight';
    } else if (this.gallopLocalStorage.getItem('selectedHotel')
      && this.gallopLocalStorage.getItem('selectedHotel') !== null) {
      return 'hotel';
    } else if (this.gallopLocalStorage.getItem('selectedCar')
      && this.gallopLocalStorage.getItem('selectedCar') !== null) {
      return 'cars';
    } else {
      return 'none';
    }
  }
  getSelectionTypeForPolicyReason() {
    if(!this.searchService.multiTripBooking){
    if (this.gallopLocalStorage.getItem('selectedFlight')
      && this.gallopLocalStorage.getItem('selectedFlight') !== null) {
      return 'flight';
    } else if (this.gallopLocalStorage.getItem('selectedHotel')
      && this.gallopLocalStorage.getItem('selectedHotel') !== null) {
      return 'hotel';
    } else if (this.gallopLocalStorage.getItem('selectedCar')
      && this.gallopLocalStorage.getItem('selectedCar') !== null) {
      return 'cars';
    } else {
      return 'none';
    }
  }else{
    let selectedFlight = JSON.parse(this.gallopLocalStorage.getItem('selectedFlight'));
    let selectedHotel = JSON.parse(this.gallopLocalStorage.getItem('selectedHotelDetailedObj'));
    let selectedCar = JSON.parse(this.gallopLocalStorage.getItem('selectedCar'));
    let type=''
    let count=0
    if(selectedFlight && selectedFlight.length > 0){
      let i=0
      for(let item of selectedFlight[this.bookingService.selectedPaymentEventIndex]){
        if(item.legs[0].flightHighlights.outOfPolicyContext.reason !== 'WITHIN_POLICY'){
          this.selectedOutpolicyIndex=i;
          type = 'flight';
          count=1;
           break;
        }
        if(type!==''){
          break;
        }
        i++;
      }

      }
    if(count===1){

      return type;
      }

     if(count ===0 && selectedHotel && selectedHotel.length > 0){
      let i=0
      for(let item of selectedHotel[this.bookingService.selectedPaymentEventIndex ]){
        if(item.hotelOutOfPolicyReason  && item.hotelOutOfPolicyReason.reason &&item.hotelOutOfPolicyReason.reason!== 'WITHIN_POLICY'){
          this.selectedOutpolicyIndex=i;
          this.gallopLocalStorage.setItem("selectedHotelPolicyReason", JSON.stringify(item.outOfPolicyReasonList[0]));
            type= 'hotel';
            count=1;
            break;

        }
        if(type!==''){
          break;
        }
        i++;

      }
     
    }
    if(count===1){

      return type;
      }
     if(count ===0 && selectedCar && selectedCar.length > 0){
      let i=0
      for(let item of selectedCar[this.bookingService.selectedPaymentEventIndex ]){
        if(item.outOfPolicyReason && item.outOfPolicyReason.reason &&item.outOfPolicyReason.reason!== 'WITHIN_POLICY'){
          this.selectedOutpolicyIndex=i;
            type= 'cars';
            count=1;
            break;
        }
        if(type!==''){
          break;
        }
        i++;

      }
      
    }
    if(count===1){

      return type;
    }
  }
  }
  handleResponseFromDropdwn(event){
    //this.selectedTag=event;
    this.tempSelectTag = event;
   }
  
  private proceedForBooking() {
   // if (this.userAccountInfoService.isConnected) {
      let selectedCardDetails;
      // history.pushState(null, null,window.location.href);
      if (this.pageMode === 'WebSearch') {
        if(this.tripDetails[0]  && this.tripDetails[0].paymenttype){
          if(this.tripDetails[0].paymenttype.id==='Bill to company'){
            this.paymentMethod = 'BILL_TO_COMPANY';
          }else{
            this.paymentMethod = 'PERSONAL_CARD'
          }
         selectedCardDetails =
          (this.isLoggedIn() && this.tripDetails[0].paymenttype)
            ? this.tripDetails[0].paymenttype : this.tripDetails[0].paymenttype;
        }else{
          let errMsg = this.translateService.instant('paymentDetails.Pleaseprovidecreditcardforpayment').toString();
          this.toastr.error(errMsg, this.translateService.instant('paymentDetails.Attention').toString());
        }
        let type = this.isLoggedIn() ? 'card' : 'token';
        if (this.getSelectionType() === 'flight') {
          this.bookFlight(selectedCardDetails, !(this.isCorporateUser() && !this.isBusinessTrip), '');
        } else if ((this.getSelectionType() === 'hotel')) {
          this.bookHotel(selectedCardDetails, !(this.isCorporateUser() && !this.isBusinessTrip), '');
        } else if (this.getSelectionType() === 'cars') {
          this.bookCar(selectedCardDetails, !(this.isCorporateUser() && !this.isBusinessTrip), '');
        }
      } else {
        //   var selectCar=JSON.parse(this.gallopLocalStorage.getItem("selectedCar"));
        if (this.getSelectionType() === 'cars' && this.canAttemptDirectBooking && this.pageMode !== 'emailflowAgent') {
          this.bookRequestProgress = true;
          this.changingValue.next({value1:this.bookRequestProgress});
          this.bookingRequestStarted = true;
          let emailbookReq: EmailFlowBookRequest = new EmailFlowBookRequest();
          emailbookReq.appliedGallopCash = this.gallopCashApplied;
          if (this.isLoggedIn()) {
            emailbookReq.selectedCard =
              (this.selectedCardIndex >= 0) ? this.userAccountInfoObj.cardList.card_list[this.selectedCardIndex] : undefined;
          }
          emailbookReq.ticketId = this.ticketId;
          emailbookReq.tripId = this.tripId;
          emailbookReq.paymentMethod = this.paymentMethod;
          emailbookReq.deleted_cards = this.getDeletedCardArray();
          this.bookCar(emailbookReq.selectedCard, !(this.isCorporateUser() && !this.isBusinessTrip), this.bookingStatusId);
        } else {
          this.makeBookingConfirmRequest();
        }
      }
   

  }
  omit_special_char(event) {
    const allowedKeys = ['Enter', 'Tab','Space']; // Allow Enter and Tab
    const inputElement = event.target as HTMLInputElement;

    if (allowedKeys.indexOf(event.key) !== -1 || /^[a-zA-Z0-9]+$/.test(event.key)) {
      // Allow the key event
    } else {
      event.preventDefault(); // Prevent the default behavior
    }

  }
  private proceedForApproval() {
    // if(this.pageMode === 'WebSearch'){
    let selectedCardDetails ;
    if(this.tripDetails[0]  && this.tripDetails[0].paymenttype){
      if(this.tripDetails[0].paymenttype.id==='Bill to company'){
        this.paymentMethod = 'BILL_TO_COMPANY';
      }else{
        this.paymentMethod = 'PERSONAL_CARD'
      }
      selectedCardDetails =
       (this.isLoggedIn() && this.tripDetails[0].paymenttype)
         ? this.tripDetails[0].paymenttype : this.tripDetails[0].paymenttype;
     }else{
       let errMsg = this.translateService.instant('paymentDetails.Pleaseprovidecreditcardforpayment').toString();
       this.toastr.error(errMsg, this.translateService.instant('paymentDetails.Attention').toString());
     }
    // let type = this.isLoggedIn() ? 'card':'token';
    // if(this.getSelectionType() === 'flight')
    this.bookingService.bookRequestProgress = true;
    
    this.processBookingApproval(selectedCardDetails, !(this.isCorporateUser() && !this.isBusinessTrip), this.getSelectionType(), '');
    // else
    // this.bookHotel(selectedCardDetails,!(this.isCorporateUser() && !this.isBusinessTrip));
    // }else{
    //   this.makeBookingConfirmRequest();
    // }
  }
  private makeBookingConfirmRequest() {
    GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
      'bookButtonClicked', 'WebSearchUI'
    );
    let tripSessionLoyalityNumbers = {ffn:[],clnno:[],hlnno:[]};
    if (this.pageMode !== 'WebSearch') {
      if(this.tripDetails[0]  && this.tripDetails[0].paymenttype){
        if(this.tripDetails[0].paymenttype.id==='Bill to company'){
          this.paymentMethod = 'BILL_TO_COMPANY';
        }else{
          this.paymentMethod = 'PERSONAL_CARD'
        }
      }
      
    for(let i=0;i<this.tripDetails.length;i++){

      if(this.tripDetails[i].ffnno && this.tripDetails[i].ffnno.length > 0){
        tripSessionLoyalityNumbers.ffn = tripSessionLoyalityNumbers.ffn.concat(this.tripDetails[i].ffnno);
      }else if(this.tripDetails[i].hlnno && this.tripDetails[i].hlnno.length >0 ){
        tripSessionLoyalityNumbers.hlnno = tripSessionLoyalityNumbers.hlnno.concat(this.tripDetails[i].hlnno);
      }else if(this.tripDetails[i].clnno && this.tripDetails[i].clnno.length > 0){
        tripSessionLoyalityNumbers.clnno = tripSessionLoyalityNumbers.clnno.concat(this.tripDetails[i].clnno);
      }
    }
    }
    this.bookRequestProgress = true;
    this.changingValue.next({value1:this.bookRequestProgress});
    this.bookingRequestStarted = true;
    this.bookingService.bookRequestProgress = true;
    let emailbookReq: EmailFlowBookRequest = new EmailFlowBookRequest();
    emailbookReq.appliedGallopCash = this.gallopCashApplied;
    if (this.isLoggedIn()) {
      emailbookReq.selectedCard = this.tripDetails[0].paymenttype;
    }
    emailbookReq.ticketId = this.ticketId;
    emailbookReq.tripId = this.tripId;
    let paymentMethodParam = this.paymentMethod;
    if (!this.showPaymentDeatils) {
      paymentMethodParam = null;
    }
    emailbookReq.paymentMethod = paymentMethodParam;
    emailbookReq.deleted_cards = this.getDeletedCardArray();
    // emailbookReq.newCardToken = getPaymentToken();

    this.bookServiceSubscription = this.bookingService.confirmBookFlight(!(this.isCorporateUser() && !this.isBusinessTrip),
      emailbookReq, this.emailId, this.sToken,this.messageForAdmin,tripSessionLoyalityNumbers
    ).subscribe(res => {
      // this.bsModalRef.hide();
      this.bookRequestProgress = false;
      this.changingValue.next({value1:this.bookRequestProgress});
      this.bookingService.bookRequestProgress = false;
      if (res.status === 'success') {
        if (this.pageMode !== 'emailflowAgent' && this.canAttemptDirectBooking) {
          let type = this.isLoggedIn() ? 'card' : 'token';
          this.bookFlight(emailbookReq.selectedCard, !(this.isCorporateUser() && !this.isBusinessTrip), this.bookingStatusId);
        } else {
          if (this.pageMode === 'emailflowAgent') {
            this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { transactionMode: true }, backdrop: false, keyboard: false, ignoreBackdropClick: true });
          } else {
            this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: this.hasHotelBillingItem(), responseType: BookingResponseErrorType.ERROR_API}, backdrop: true, keyboard: false, ignoreBackdropClick: true });
          }
          if (this.tempSelectTag && this.tempSelectTag.length > 0 ) {
            this.bookingService.selectedTag = this.tempSelectTag;
          }
          this.bookingService.noteToadmin = this.messageForAdmin;
          this.goBackEmitter.emit('notifyAgent');
          let params = this.activatedRoute.snapshot.queryParams;
          if (params['next']) {
            setTimeout(() => {
              window.open(params['next']);
            }, 5000);
          }
        }
      } else {
        this.toastr.error(res.message, 'Attention!');
      }
    }
      ,
      error => {
     //   CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_BOOK,this.translateService);
      }
    );
  }

  private cardTokenData: any;
  isLoggedIn(): boolean {
    return this.userAccountInfoService.isLoggedIn();
  }
  addCardFlow: boolean = false;
  public handleBackFromAddCard(data: any) {
    if (!data) {
      this.unsetCardMode();
      return;
    }

    this.userAccountInfoService.paymentPageSave = false;
    let tokenData = JSON.parse(data);
    if (tokenData && tokenData.type === 'newCardAdded') {

      // this.userAccountInfoService.fetchUserAccountInfo(this.emailId, this.sToken);
      let cardTokens: any = tokenData.tokens;
      if (cardTokens && cardTokens.error && cardTokens.error.length > 0) {
        // this.toastr.error(cardTokens.error, 'Card Error!');
        this.addCardChild.setAddCardProgress(false);
        this.addCardChild.setErrorMessage('');
      }

      else if (cardTokens.token && cardTokens.gToken) {
        this.cardTokenData = cardTokens;
        GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
          'cardTokenCreated', 'WebSearchUI'
        );

        if (!this.isLoggedIn()) {
          let travelerDetails: TravelerDetails = this.bookingService.getTravelerDetails('WebSearch');
          this.userAccountInfoService.requestCardInfoFromToken(cardTokens.gToken, cardTokens.token, travelerDetails.email).subscribe(res => {
            if (res.status === 'success') {
              // this.userAccountInfoService.fetchUserAccountInfo(false);
              // this.unsetCardMode();
              let card: CardInfo = deserialize(res.card);
              this.cardToken = card;
              //  this.getCardList().push(card);
              this.selectedCardIndex = 0;
              this.addCardChild.setAddCardProgress(false);
              this.addCardChild.setErrorMessage('');
              this.unsetCardMode();
              updatePaymentDOM();
            } else if (res.status === 'CARDERROR') {
              // this.toastr.error(res.message, 'Card Error!');
              this.addCardChild.setErrorMessage(res.message);
              this.addCardChild.setAddCardProgress(false);

            } else {
              // this.toastr.error(res.message, 'Error!');
              this.addCardChild.setErrorMessage(res.message);
              this.addCardChild.setAddCardProgress(false);
            }
          });
          // this.cardToken = new CardInfo();
          // this.cardToken.name = tokenData.result.card.name;
          // this.cardToken.brand = tokenData.result.card.brand;
          // this.cardToken.address = new AddressDTO();
          // this.cardToken.address.postal_code = tokenData.result.card.address_zip;
          // this.cardToken.exp_month = tokenData.result.card.exp_month;
          // this.cardToken.exp_year = tokenData.result.card.exp_year;
          // this.cardToken.id = cardTokens.token;
          // this.cardToken.last4 = tokenData.result.card.last4;
        } else {
          this.userAccountInfoService.requestSaveCardInfo(cardTokens.token, cardTokens.gToken).subscribe(res => {
            if (res.status === 'success') {
              this.addCardFlow = true;
              this.userAccountInfoService.fetchUserAccountInfo(false);
              // this.unsetCardMode();
            } else if (res.status === 'CARDERROR') {
              // this.toastr.error(res.message, 'Card Error!');
              this.addCardChild.setErrorMessage(res.message);
              this.addCardChild.setAddCardProgress(false);

            } else {
              // this.toastr.error(res.message, 'Error!');
              this.addCardChild.setErrorMessage(res.message);
              this.addCardChild.setAddCardProgress(false);
            }
          });
        }
      } else {
        // this.addCardChild.setErrorMessage(this.translateService.instant('paymentDetails.UnknownErrorPleasetryagain').toString());
        // this.addCardChild.setAddCardProgress(false);
      }
    }

  }

  hasHotelBillingItem() {
    let hasHotel = false;
    if (this.billingItemArray) {
      for (let billingItem of this.billingItemArray) {
        if (billingItem.type == 'hotel') {
          hasHotel = true;
          break;
        }
      }
    }

    return hasHotel;
  }
  processBookHotelResponse(res) {
    let bookingResponse: HotelBookingResponse = deserialize(res, HotelBookingResponse);
    this.gallopLocalStorage.setItem("hotelBookingResponse", JSON.stringify(bookingResponse));
    if (bookingResponse.paymentDetails && bookingResponse.paymentDetails.availableGallopCash) {
      this.userAccountInfoService.updateAvailableGallopCash(bookingResponse.paymentDetails.availableGallopCash);
    }
    this.bookingService.hotelBookingResponse = bookingResponse;
    let responseType = bookingResponse.status.toUpperCase();
    if (bookingResponse.bookingStatusId) {
      this.bookingStatusId = bookingResponse.bookingStatusId;
    }

    if (responseType === Constants.INPROGRESS) {
      this.initiateDelayedPolling(bookingResponse.bookingStatusId, 'bookHotel', bookingResponse.nextCallAfter);
      return;
    }
    this.bookRequestProgress = false;
    this.changingValue.next({value1:this.bookRequestProgress});
    this.bookingService.bookRequestProgress = false;
    if (responseType === Constants.ERROR) {
      responseType = bookingResponse.errors[0].errorType;
    }
    let errorMessage = null;
    if (responseType === BookingResponseErrorType.ERROR_AVAILABILITY
      || responseType === BookingResponseErrorType.ERROR_BOOKING_PENDING) {
      errorMessage = bookingResponse.errors[0].errorMessage;
    }

    if (responseType === BookingResponseErrorType.ERROR_AVAILABILITY
      || responseType === BookingResponseErrorType.ERROR_BOOKING_PENDING
      || responseType === BookingResponseErrorType.ERROR_INTERNAL
      || responseType === BookingResponseErrorType.ERROR_API) {
      this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: !this.hasHotelBillingItem(), responseType: responseType, errorMessage: errorMessage }, backdrop: true, keyboard: false, ignoreBackdropClick: true });
    } else {
      let bsModalRes = this.bookingService.getBookingModalHotelResponse(bookingResponse);
      this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, keyboard: false, ignoreBackdropClick: true });
      this.bsModalRef.content.acceptClickSubject.subscribe(result => {

        if (result && result === 'showRoomList') {
          this.router.navigate(['/hotelSelection'], { queryParams: {} });
        }
      });
    }
    // let travellerDetails : TravelerDetails = this.bookingService.getTravelerDetails('WebSearch');
    // notifyAgentWithNewTripParams(this.userAccountInfoService.getUserEmail()?this.userAccountInfoService.getUserEmail(): travellerDetails.email,
    // bookingResponse.ticketId, bookingResponse.tripId,bookingResponse.ticketNumber,environment.agentNotifyURL
    // ,bookingResponse.eventIdAndOptions);
  }
  processBookCarResponse(res) {
    let bookingResponse: any = deserialize(res);
    this.gallopLocalStorage.setItem("carBookingResponse", JSON.stringify(bookingResponse));
    if (bookingResponse.paymentDetails && bookingResponse.paymentDetails.availableGallopCash) {
      this.userAccountInfoService.updateAvailableGallopCash(bookingResponse.paymentDetails.availableGallopCash);
    }
    let responseType = bookingResponse.status.toUpperCase();
    if (bookingResponse.bookingStatusId) {
      this.bookingStatusId = bookingResponse.bookingStatusId;
    }

    if (responseType === Constants.INPROGRESS) {
      this.initiateDelayedPolling(bookingResponse.bookingStatusId, 'bookCar', bookingResponse.nextCallAfter);
      return;
    }
    this.bookRequestProgress = false;
    this.changingValue.next({value1:this.bookRequestProgress});
    this.bookingService.bookRequestProgress = false;
    if (responseType === Constants.ERROR) {
      responseType = bookingResponse.errors[0].errorType;
    }
    let errorMessage = null;
    if (responseType === BookingResponseErrorType.ERROR_AVAILABILITY
      || responseType === BookingResponseErrorType.ERROR_BOOKING_PENDING) {
      errorMessage = bookingResponse.errors[0].errorMessage;
    }

    if (responseType === BookingResponseErrorType.ERROR_AVAILABILITY
      || responseType === BookingResponseErrorType.ERROR_INTERNAL
      || responseType === BookingResponseErrorType.ERROR_API) {
      this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: false, responseType: responseType }, backdrop: true, keyboard: false, ignoreBackdropClick: true });
    } else if (responseType === BookingResponseErrorType.ERROR_BOOKING_PENDING) {
      let bsModalRes = this.bookingService.getCarBookingModalResponse(bookingResponse, true);
      this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, keyboard: false, ignoreBackdropClick: true });
    } else {
      let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse, true);
      this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, keyboard: false, ignoreBackdropClick: true });
    }
    let travellerDetails: TravelerDetails = this.bookingService.getTravelerDetails('WebSearch');
    if (!(bookingResponse.carInfo && bookingResponse.carInfo.bookingId && bookingResponse.carInfo.bookingId.trim().length > 0)) {
      notifyAgentWithNewTripParams(this.userAccountInfoService.getUserEmail() ? this.userAccountInfoService.getUserEmail() : travellerDetails.email,
        bookingResponse.ticketId, bookingResponse.tripId, bookingResponse.ticketNumber, environment.agentNotifyURL
        , bookingResponse.eventIdAndOptions, this.tempSelectTag,this.searchService.tripName,this.userAccountInfoService.getSToken() );
    }
  }
  bookHotel(details: CardInfo, includeExpensifyDetails: boolean, bookingId) {
    this.bookRequestProgress = true;
    this.changingValue.next({value1:this.bookRequestProgress});
    this.bookingService.bookRequestProgress = true;
    let cardType: string = this.userAccountInfoService.isLoggedIn() ? 'card' : 'token';
    this.bookServiceSubscription = this.bookingService.bookHotel(includeExpensifyDetails, this.paymentMethod, details, cardType, this.gallopCashApplied,
      this.ticketId, this.tripId, this.eventIdAndOptions, bookingId, this.tempSelectTag).subscribe((res) => {
        // this.disablePay=false;
        // this.progressBar.complete(Constants.BOOK);
        if (this.bsModalRef) {
          this.bsModalRef.hide();
        }
        this.processBookHotelResponse(res);
      }, error => {
    //    CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_BOOK,this.translateService);
      });
  }
  bookCar(details: CardInfo, includeExpensifyDetails: boolean, bookingId) {
    this.bookRequestProgress = true;
    this.changingValue.next({value1:this.bookRequestProgress});
    this.bookingService.bookRequestProgress = true;
    let cardType: string = this.userAccountInfoService.isLoggedIn() ? 'card' : 'token';
    let paymentMethodParam = this.paymentMethod;
    
    if (!this.showPaymentDeatils) {
      paymentMethodParam = null;
    }
    this.bookServiceSubscription = this.carBookingService.bookCar(includeExpensifyDetails, paymentMethodParam, details, cardType, this.gallopCashApplied,
      this.ticketId, this.tripId, this.eventIdAndOptions, bookingId, this.tempSelectTag).subscribe((res) => {
        // this.disablePay=false;
        // this.progressBar.complete(Constants.BOOK);
        if (this.bsModalRef) {
          this.bsModalRef.hide();
        }

        this.processBookCarResponse(res);
      }, error => {
     //   CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_BOOK),this.translateService;
      });
  }


  initiateDelayedPolling(bookingRequestId: string, requestType: string, delay: number) {

    setTimeout(() => {

     // if () {
        this.initiateImmediatePolling(bookingRequestId, requestType, delay);
     // } else {
      //  this.initiateDelayedPolling(bookingRequestId, requestType, delay);
      //}
    }, delay);
  }

  initiateImmediatePolling(bookingRequestId: string, requestType: string, delay) {
    try {
      this.bookServiceSubscription = this.bookingService.pollingForBookingResponse(bookingRequestId)
        .pipe(catchError(err => {
          return throwError(err);
        }))
        .subscribe((res) => {
          if (requestType == 'bookFlight') {
            this.processBookFlightResponse(res);
          } else if (requestType == 'bookHotel') {
            this.processBookHotelResponse(res);
          } else if (requestType == 'bookCar') {
            this.processBookCarResponse(res);
          } else if (requestType == 'requestApproval') {
            this.processApprovalPolling(res)
          }else if (requestType == 'bookTripSession') {
            this.processBookTripSessionResponse(res)
          }
        }, error => {
          if (this.currentToastId !== -1) {
            this.toastr.remove(this.currentToastId);
          }
          this.currentToastId = this.toastr.info('Connection lost. Please check your network settings').toastId;
          //        CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_BOOKINPROGRESS);
          this.initiateDelayedPolling(bookingRequestId, requestType, delay);
        });
    } catch (error) {
      if (this.currentToastId !== -1) {
        this.toastr.remove(this.currentToastId);
      }
      this.currentToastId = this.toastr.info('Connection lost. Please check your network settings').toastId;
      this.initiateDelayedPolling(bookingRequestId, requestType, delay);
    }
  }

  processBookFlightResponse(res) {
    // this.disablePay=false;
    // this.progressBar.complete(Constants.BOOK);
    this.bookingService.previousBooking =undefined;
    this.bookingService.previousTransactionId= undefined;
    let bookingResponse: BookingResponse = deserialize(res, BookingResponse);

    let responseType = bookingResponse.status.toUpperCase();

    if (responseType === Constants.INPROGRESS) {
      this.initiateDelayedPolling(bookingResponse.bookingStatusId, 'bookFlight', bookingResponse.nextCallAfter);
      return;
    }
    this.gallopLocalStorage.setItem("bookingResponse", JSON.stringify(bookingResponse));
    if (bookingResponse.paymentDetails && bookingResponse.paymentDetails.availableGallopCash) {
      this.userAccountInfoService.updateAvailableGallopCash(bookingResponse.paymentDetails.availableGallopCash);
      // this.userDetailsService.updateGallopCash(bookingResponse.paymentDetails.availableGallopCash);
    }
    this.bookingService.bookingResponse = bookingResponse;
    this.bookRequestProgress = false;
    this.changingValue.next({value1:this.bookRequestProgress});
    this.bookingService.bookRequestProgress = false;
    if (responseType === Constants.ERROR) {
      if (bookingResponse.errors.length > 0) {
        responseType = bookingResponse.errors[0].errorType;
      } else {
        responseType = BookingResponseErrorType.ERROR_API;
      }
    }
    let errorMessage = null;
    if (responseType === BookingResponseErrorType.ERROR_INTERNAL
      || responseType === BookingResponseErrorType.ERROR_API) {
      this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: this.hasHotelBillingItem(), responseType: responseType }, backdrop: true, keyboard: false, ignoreBackdropClick: true });
    } else {
      // let flightSelected :FlightResult = this.bookingService.selectedFlight;
      let flightSelected: FlightResult = JSON.parse(this.gallopLocalStorage.getItem("selectedFlightInPolicy"));
      if (!flightSelected) {
        flightSelected = this.bookingService.selectedFlight;
      }
      if (flightSelected && responseType === BookingResponseErrorType.ERROR_AVAILABILITY) {
        this.searchService.noAvailability.push(CommonUtils.getAllFlightNumbers(flightSelected.legs));
        this.bookingService.priceChanngeFlight = undefined;
      }
      let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse, true);
      this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, keyboard: false, ignoreBackdropClick: true });
      // this.bsModalRef.content.acceptClickSubject.subscribe(result => {
      // 
      //if (result && result === 'showFlightList'){ 
      //this.router.navigate(['/flights'], { queryParams: { } });
      //}
      //});
    }
    let flightsInResponse: FlightResult[] = null;
    if (bookingResponse.bookedFlights) {
      flightsInResponse = bookingResponse.bookedFlights.flights;
    }
    if (bookingResponse.changedFlights) {
      flightsInResponse = bookingResponse.changedFlights.flights;
      this.bookingService.priceChanngeFlight = bookingResponse.changedFlights.flights[0];
      this.bookingService.selectedFlight = bookingResponse.changedFlights.flights[0];
      this.bookingService.selectedFlight1 = bookingResponse.changedFlights.flights[0];
      this.gallopLocalStorage.setItem("selectedFlight", JSON.stringify(this.bookingService.selectedFlight));
      if (this.searchService.outsidePolicyselected && this.searchService.inPolicyFlight.length > 0) {
        if (this.bookingService.selectedFlight1) {
          this.gallopLocalStorage.setItem("selectedFlightInPolicy", JSON.stringify(this.bookingService.selectedFlight1));
        }
      }
    }
    this.outsidePolicyFlag = !flightsInResponse[0].legs[0].flightHighlights.withinPolicy;
    //Also change the flag in data
    if (this.bookingService.selectedFlight && this.bookingService.selectedFlight.price) {
      this.bookingService.selectedFlight.price = flightsInResponse[0].price;
      if(flightsInResponse[0].displayPrice){
      this.bookingService.selectedFlight.displayPrice = flightsInResponse[0].displayPrice;
      }
      this.bookingService.selectedFlight.discountedPrice = flightsInResponse[0].discountedPrice;
      let legCounter = 0;
      let legCounter1 = 0;
      let legCounter2 = 0;
      if(!this.searchService.nonCombo){
      for (let currLeg of this.bookingService.selectedFlight.legs) {
        currLeg.flightHighlights.withinPolicy =
          flightsInResponse[0].legs[legCounter].flightHighlights.withinPolicy;
        legCounter++;
      }
    }else{
      if (bookingResponse.multiBookingResponse && bookingResponse.multiBookingResponse.length >0) {
      for (let currLeg of this.bookingService.selectedFlight.legs) {
        currLeg.flightHighlights.withinPolicy =
        bookingResponse.multiBookingResponse[legCounter1].bookedFlights.flights[0].legs[0].flightHighlights.withinPolicy;
        legCounter1++;
      }
      for (let currLeg of this.searchService.nonComboSelectedFlight) {
        currLeg.legs[0].flightHighlights.withinPolicy =
        bookingResponse.multiBookingResponse[legCounter2].bookedFlights.flights[0].legs[0].flightHighlights.withinPolicy;
        legCounter2++;
      }
    }
  }
    }
    updatePriceChangeInBookingResponse(flightsInResponse[0].price,
      flightsInResponse[0].discountedPrice,
      flightsInResponse[0].legs[0].flightHighlights.withinPolicy);
  }
  goToItinerary(i) {
   
    let ticketid = this.bookingService.bookingResponse.multiBookingResponse[i].ticketId;
    let tripid =this.bookingService.bookingResponse.multiBookingResponse[i].tripId;
    let transactionid = this.bookingService.bookingResponse.multiBookingResponse[i].transactionId;
    let tripSessionId = this.bookingService.bookingResponse.multiBookingResponse[i].tripSessionId;
    let url = '/bookingHistory?view=detail&type=detail&bookingType=upcoming&from=Itinenary&ticketid='+ticketid+'&tripid='+tripid+'&transactionid='+transactionid+
               '&tripSessionid='+tripSessionId;
    if (this.isMobile || this.njoySpecificBuild) {
      this.bsModalRef.hide();
      if((this.userAccountInfoService.isUserCorporateAdmin() || this.userAccountInfoService.isUserIsTravelManager())){
        this.searchService.employeeEmail=[];
        this.searchService.bookingAndApprovalDone =true;
      }
      this.searchService.tripName='';
      this.bookingService.bookingResponse=undefined;
      if(this.searchService.multiTripBooking){
        this.searchService.multiTripBooking =false;
        this.searchService.multiflightQuery =[];
        this.searchService.multicarQuery =[];
     //   this.searchService.tripName='';
        this.gallopLocalStorage.removeItem("selectedSeat");
        this.searchService.multihotelQuery =[];
        this.searchService.previousSelectedCreditAirline=[];
      }
      this.router.navigate(["bookingHistory"],
      {
        queryParams:
        {
          type: 'detail',
          bookingType: 'upcoming',
          from: 'Itinenary',
          ticketid: ticketid,
          tripid: tripid,
          transactionid: transactionid,
          tripSessionId:tripSessionId
        },
      }
    );
    this.location.replaceState('/'+this.userAccountInfoService.getDefaultRoutePath());
    } else {
      window.open(url, "_blank");
    }
  
  }

  proceedAfterMutliBooking=true;
  preprocessSimulateRetryFailedBooking(bookingResponse){
    bookingResponse.status = "error";
    bookingResponse.errors = ["ERROR_TRIP_BOOKING_PENDING"];
    if(bookingResponse.multiBookingResponse.length > 1){
      let lastIndex = bookingResponse.multiBookingResponse.length - 1;
      bookingResponse.multiBookingResponse[lastIndex].status = "error";
      bookingResponse.multiBookingResponse[lastIndex].errors = [{"errorType":"ERROR_RETRY"}];
    }
    if(bookingResponse.multiBookingResponse.length > 1){
      let lastIndex = bookingResponse.multiBookingResponse.length - 1;
      bookingResponse.multiBookingResponse[lastIndex].status = "error";
      bookingResponse.multiBookingResponse[lastIndex].errors = [{"errorType":"ERROR_RETRY"}];
    }
    return bookingResponse;
  }
  processBookTripSessionResponse(res) {
    // this.disablePay=false;
    // this.progressBar.complete(Constants.BOOK);
    this.bookingService.previousBooking =undefined;
    this.bookingService.previousTransactionId= undefined;
    let bookingResponse: any = deserialize(res, BookingResponse);

    let responseType = bookingResponse.status.toUpperCase();

    if (responseType === Constants.INPROGRESS) {
      this.initiateDelayedPolling(bookingResponse.bookingStatusId, 'bookTripSession', bookingResponse.nextCallAfter);
      return;
    }
    this.gallopLocalStorage.setItem("bookingResponse", JSON.stringify(bookingResponse));
    if (bookingResponse.paymentDetails && bookingResponse.paymentDetails.availableGallopCash) {
      this.userAccountInfoService.updateAvailableGallopCash(bookingResponse.paymentDetails.availableGallopCash);
      // this.userDetailsService.updateGallopCash(bookingResponse.paymentDetails.availableGallopCash);
    } 
    // bookingResponse = this.preprocessSimulateRetryFailedBooking(bookingResponse);   
    this.bookingService.bookingResponse = bookingResponse;
    this.bookRequestProgress = false;
    this.changingValue.next({value1:this.bookRequestProgress});
    this.bookingService.bookRequestProgress = false;

    if (responseType === Constants.ERROR) {
      if (bookingResponse.errors.length > 0) {
        responseType = bookingResponse.errors[0].errorType;
      } else {
        responseType = BookingResponseErrorType.ERROR_API;
      }
    }

    let tripSessionMultiBooking = false;
    if(responseType === BookingResponseErrorType.ERROR_INVALID_INPUT) {
      if(this.bsModalRef){
        this.bsModalRef.hide();
      }
    }
    let singleBookingType = "flight";
    if(bookingResponse && bookingResponse.multiBookingResponse && bookingResponse.multiBookingResponse.length >1){
      tripSessionMultiBooking = true;
    }else{
      tripSessionMultiBooking = false;
      if(bookingResponse.bookedFlights){
        singleBookingType = "flight";
      }else if(bookingResponse.bookedHotels){
        singleBookingType = "hotel";
      }else if(bookingResponse.carInfo){
        singleBookingType = "car";
      }
    }
    if(bookingResponse && bookingResponse.limitedUserCreated) {
      this.isGuestTravelerPresent();
    }
    
    if(!tripSessionMultiBooking && singleBookingType === 'flight'){
      if (responseType === BookingResponseErrorType.ERROR_INTERNAL
        || responseType === BookingResponseErrorType.ERROR_API) {
        this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: this.hasHotelBillingItem(), responseType: responseType }, backdrop: true, keyboard: false, ignoreBackdropClick: true });
      } else {
        // let flightSelected :FlightResult = this.bookingService.selectedFlight;
        let flightSelected: FlightResult = JSON.parse(this.gallopLocalStorage.getItem("selectedFlightInPolicy"));
        if (!flightSelected) {
          flightSelected = this.bookingService.selectedFlight;
        }
        
        if (flightSelected && responseType === BookingResponseErrorType.ERROR_AVAILABILITY) {
        
          this.searchService.noAvailability.push(CommonUtils.getAllFlightNumbers(flightSelected.legs));
          this.bookingService.priceChanngeFlight = undefined;
        }
        let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse, true,'singleflight');
        this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, keyboard: false, ignoreBackdropClick: true });
        // this.bsModalRef.content.acceptClickSubject.subscribe(result => {
        // 
        //if (result && result === 'showFlightList'){ 
        //this.router.navigate(['/flights'], { queryParams: { } });
        //}
        //});
      }
    }else  if(!tripSessionMultiBooking && singleBookingType === 'hotel'){
      let errorMessage = null;
      if (responseType === BookingResponseErrorType.ERROR_ROOM_UNAVAILABLE || BookingResponseErrorType.ERROR_RETRY 
        || responseType === BookingResponseErrorType.ERROR_BOOKING_PENDING || responseType === BookingResponseErrorType.ERROR_TRIP_BOOKING_PENDING) {
          if( bookingResponse && bookingResponse.errors && bookingResponse.errors.length > 0 && bookingResponse.errors[0].errorMessage){
          errorMessage = bookingResponse.errors[0].errorMessage;
          }
      }
  
      if (responseType === BookingResponseErrorType.ERROR_BOOKING_PENDING || responseType === BookingResponseErrorType.ERROR_TRIP_BOOKING_PENDING
        || responseType === BookingResponseErrorType.ERROR_INTERNAL
        || responseType === BookingResponseErrorType.ERROR_API) {
        this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: !this.hasHotelBillingItem(), responseType: responseType, errorMessage: errorMessage }, backdrop: true, keyboard: false, ignoreBackdropClick: true });
      } else {
        let bsModalRes = this.bookingService.getBookingModalHotelResponse(bookingResponse);
        this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, keyboard: false, ignoreBackdropClick: true });
        this.bsModalRef.content.acceptClickSubject.subscribe(result => {
  
          if (result && result === 'showRoomList') {
            this.searchService.paymentPage =false;
            this.router.navigate(['/hotelSelection'], { queryParams: {} });
          }
        });
      }
    }else  if(!tripSessionMultiBooking && singleBookingType === 'car'){
      if (responseType === BookingResponseErrorType.ERROR_INTERNAL
        || responseType === BookingResponseErrorType.ERROR_API) {
        this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: false, responseType: responseType }, backdrop: true, keyboard: false, ignoreBackdropClick: true });
      } else if (responseType === BookingResponseErrorType.ERROR_BOOKING_PENDING) {
        let bsModalRes = this.bookingService.getCarBookingModalResponse(bookingResponse, true);
        this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, keyboard: false, ignoreBackdropClick: true });
      } else {
        let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse, true);
        this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, keyboard: false, ignoreBackdropClick: true });
      }
    }else if(tripSessionMultiBooking){
      if(responseType === BookingResponseErrorType.ERROR_AVAILABILITY || responseType === BookingResponseErrorType.ERROR_INVALID_INPUT || responseType ===BookingResponseErrorType.ERROR_PRICE_CHANGE ||responseType === BookingResponseErrorType.ERROR_ROOM_UNAVAILABLE) {
        if(this.bsModalRef){
          this.bsModalRef.hide();
        }
        this.proceedAfterMutliBooking =false;
      }
      if (this.noOfPassengers===1 && responseType === BookingResponseErrorType.ERROR_PRICE_CHANGE
        || responseType === BookingResponseErrorType.ERROR_INTERNAL
        || responseType === BookingResponseErrorType.ERROR_API
        || responseType === BookingResponseErrorType.ERROR_TRIP_BOOKING_PENDING) {
          if (((this.numOfTravelelrExculidngChild >1) &&  (this.numOfTravelelrExculidngChild=== (this.searchService.employeeEmail && this.searchService.employeeEmail.length)) || this.searchService.multiTripBooking) &&  this.proceedAfterMutliBooking){
            return;
          }
        this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: !this.hasHotelBillingItem(), responseType: responseType }, backdrop: true, keyboard: false, ignoreBackdropClick: true });
      } else {
        if (((this.numOfTravelelrExculidngChild >1) &&  (this.numOfTravelelrExculidngChild=== (this.searchService.employeeEmail && this.searchService.employeeEmail.length))  || this.searchService.multiTripBooking) &&  this.proceedAfterMutliBooking){
          return;
        }
        let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse, true);
        this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, keyboard: false, ignoreBackdropClick: true });
        this.bsModalRef.content.acceptClickSubject.subscribe(result => {

          if (result && result === 'showRoomList') {
            this.searchService.paymentPage =false;
            this.router.navigate(['/hotelSelection'], { queryParams: {} });
          }
        });
      }
    }
 

  
  
  }
  bookFlight(details: CardInfo, includeExpensifyDetails: boolean, bookingId) {
    // this.progressBar.start(Constants.BOOK);
    this.bookRequestProgress = true;
    this.changingValue.next({value1:this.bookRequestProgress});
    this.bookingService.bookRequestProgress = true;
  
    let cardType: string = this.userAccountInfoService.isLoggedIn() ? 'card' : 'token';
    this.bookServiceSubscription = this.bookingService.bookFlight(includeExpensifyDetails, this.paymentMethod, details, cardType, this.gallopCashApplied,
      this.ticketId, this.tripId, this.eventIdAndOptions, bookingId, this.tempSelectTag).subscribe((res) => {
        if (this.bsModalRef) {
          this.bsModalRef.hide();
        }

        this.processBookFlightResponse(res);
      }, error => {
    //    CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_BOOK,this.translateService);
      });
  }
  processApprovalPolling(res) {
    let bookingResponse: BookingResponse = deserialize(res, BookingResponse);
    this.gallopLocalStorage.setItem("bookingResponse", JSON.stringify(bookingResponse));
    if (bookingResponse.paymentDetails && bookingResponse.paymentDetails.availableGallopCash) {
      this.userAccountInfoService.updateAvailableGallopCash(bookingResponse.paymentDetails.availableGallopCash);
      // this.userDetailsService.updateGallopCash(bookingResponse.paymentDetails.availableGallopCash);
    }
    this.bookingService.bookingResponse = bookingResponse;

    let responseType = bookingResponse.status.toUpperCase();
    if (bookingResponse.bookingStatusId) {
      this.bookingStatusId = bookingResponse.bookingStatusId;
    }

    if (responseType === Constants.INPROGRESS) {
      this.initiateDelayedPolling(bookingResponse.bookingStatusId, 'requestApproval', bookingResponse.nextCallAfter);
      return;
    }
    this.bookingService.bookRequestProgress = false;
    this.bookRequestProgress = false;
    this.changingValue.next({value1:this.bookRequestProgress});
    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }
    if (responseType === Constants.ERROR) {
      responseType = bookingResponse.errors[0].errorType;
    }
    let errorMessage = null;
    if (responseType === BookingResponseErrorType.ERROR_PAYMENT
      || responseType === BookingResponseErrorType.ERROR_INVALID_INPUT
      || responseType === BookingResponseErrorType.ERROR_AVAILABILITY_WITHIN_TWOHRS
      || responseType === BookingResponseErrorType.ERROR_AVAILABILITY) {
      let flightSelected: FlightResult = JSON.parse(this.gallopLocalStorage.getItem("selectedFlightInPolicy"));
      if (!flightSelected) {
        flightSelected = this.bookingService.selectedFlight;
      }
      if (flightSelected) {
        this.searchService.noAvailability.push(CommonUtils.getAllFlightNumbers(flightSelected.legs));
        this.bookingService.priceChanngeFlight = undefined;
      }
      let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse, true);
      this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, keyboard: false, backdrop: true, ignoreBackdropClick: true });
    } else {
      let isPaymentError = false;
      let isBookingError = false;
      if (bookingResponse.status.toUpperCase() === Constants.ERROR) {
        isBookingError = true;
        for (let error of bookingResponse.errors) {
          if (error.errorType === BookingResponseErrorType.ERROR_PAYMENT) {
            isPaymentError = true;
          }
        }
      }
      if(bookingResponse && bookingResponse.limitedUserCreated) {
        this.isGuestTravelerPresent();
      }
      this.bsModalRef = this.modalService.show(this.currentModel, {
        initialState: {

        }, backdrop: true, keyboard: false, ignoreBackdropClick: true
      });
      this.approvalPopOpen = false;
      this.approvalSuccessPopOpen = true;
      // This will now be taken care from the server itself

      // notifyAdminForApproval(this.userAccountInfoService.getUserEmail(),
      // bookingResponse.ticketId, bookingResponse.tripId,bookingResponse.ticketNumber,environment.adminNotifyURL
      // ,bookingResponse.eventIdAndOptions,bookingResponse.transactionId,isBookingError,isPaymentError);
    }
  }

  processBookingApproval(details: CardInfo, includeExpensifyDetails: boolean, bookingFlowType: string, bookingStatusId) {
    // this.progressBar.start(Constants.BOOK);
    this.bookRequestProgress = true;
    this.changingValue.next({value1:this.bookRequestProgress});
    let paymentMethodParam = this.paymentMethod;
    if (!this.showPaymentDeatils) {
      paymentMethodParam = null;
    }
    let cardType: string = this.userAccountInfoService.isLoggedIn() ? 'card' : 'token';
    this.bookServiceSubscription = this.bookingService.requestBookingApproval(includeExpensifyDetails, paymentMethodParam, details, cardType, this.gallopCashApplied,
      this.ticketId, this.tripId, this.eventIdAndOptions, bookingFlowType, bookingStatusId, this.messageForAdmin,this.tempSelectTag).subscribe((res) => {
        // this.disablePay=false;
        this.bookingService.previousBooking =undefined;
    this.bookingService.previousTransactionId= undefined;
        // this.progressBar.complete(Constants.BOOK);
        this.processApprovalPolling(res);

        // this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: !this.hasHotelBillingItem() }, backdrop: true, ignoreBackdropClick: true });
        // if(responseType === BookingResponseErrorType.ERROR_AVAILABILITY
        //   || responseType === BookingResponseErrorType.ERROR_INTERNAL
        //   || responseType === BookingResponseErrorType.ERROR_API){
        //     this.bsModalRef = this.modalService.show(SuccessModelComponent, { initialState: { showHotelLink: !this.hasHotelBillingItem() }, backdrop: true, ignoreBackdropClick: true });
        //   }else{
        //     let bsModalRes = this.bookingService.getBookingModalResponse(bookingResponse);
        //     this.bsModalRef = this.modalService.show(BookingMessageModalComponent, { initialState: bsModalRes, backdrop: true, ignoreBackdropClick: true });
        //   }

        // this.bsModalRef.content.acceptClickSubject.subscribe(data => {
        // })
      }
        ,
        error => {
          if (this.bsModalRef) {
            setTimeout(() => {
              this.bsModalRef.hide();
            }, 200);
          }
     //     CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_BOOK,this.translateService);
        });
    // this.connectionListener = this.connectionService.monitor().subscribe(isConnected => {
    // if (!isConnected) {
    // if (this.bsModalRef){
    // setTimeout(()=>{
    //this.bsModalRef.hide();
    //},200);
    //}
    // this.bookServiceSubscription.unsubscribe();
    //CommonUtils.showNetworkErrorPopupModal(this.searchService, this.modalService, PopupComponent.POPUP_ID_NETWORK_ERROR_ON_APPROVAL_REQUEST);
    //}
    ///});
  }
  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }

  ngOnDestroy() {
    if (this.connectionListener) {
      this.connectionListener.unsubscribe();
    }
  }
  hasTravelportOrSabreHotel() { 
    for (let billingItemIndex = 0; billingItemIndex < this.billingItemArray.length; billingItemIndex++) {
      if (this.billingItemArray[billingItemIndex].type.toLowerCase() === 'hotel'
        && this.billingItemArray[billingItemIndex].source
        && (
          this.billingItemArray[billingItemIndex].source.toLowerCase() === 'travelport'
          || this.billingItemArray[billingItemIndex].source.toLowerCase() === 'sabre'
          
          )
      ) {
        return true;
      }
    }
    return false;
  }
  isBookingForSelf() {
    if (this.searchService.employeeEmail
      && this.searchService.employeeEmail.length > 0
      && this.searchService.employeeEmail[0].email === this.userAccountInfoService.getUserEmail()) {
      return true;
    }
    return false;
  }
  isNotBookingForSelf() {
    if (this.searchService.employeeEmail
      && this.searchService.employeeEmail.length > 0
      && this.searchService.employeeEmail[0].email != this.userAccountInfoService.getUserEmail()) {
      return true;
    }
    return false;
  }
  isMultipleTravelerHotelBookingFailedForRetry(){
    if(this.bookingService.bookingResponse  && this.bookingService.bookingResponse.multiBookingResponse && this.bookingService.bookingResponse.multiBookingResponse.length >0){
      for(let i = 0; i < this.bookingService.bookingResponse.multiBookingResponse.length; i++){
        if(this.bookingService.bookingResponse.multiBookingResponse[i].bookedHotels && 
            this.bookingService.bookingResponse.multiBookingResponse[i].status === 'error'
            && "ERROR_RETRY" === this.bookingService.bookingResponse.multiBookingResponse[i].errors[0].errorType){
          return true;
        }
      }
    }
    return false;
  }

  findRetryBookingTravellers(){
    let bookingFailedTravellerList = [];
    if(this.bookingService.bookingResponse  && this.bookingService.bookingResponse.multiBookingResponse 
      && this.bookingService.bookingResponse.multiBookingResponse.length >0){
      for(let i = 0; i < this.bookingService.bookingResponse.multiBookingResponse.length; i++){
        if(this.bookingService.bookingResponse.multiBookingResponse[i].bookedHotels 
          && this.bookingService.bookingResponse.multiBookingResponse[i].status === 'error' 
          && "ERROR_RETRY" === this.bookingService.bookingResponse.multiBookingResponse[i].errors[0].errorType){
          bookingFailedTravellerList.push( this.bookingService.bookingResponse.multiBookingResponse[i].travellerDetails.email);
        }
      }
    }
    return bookingFailedTravellerList;
  }
  retryFailedBookingWork(){
    let bookingFailedTravellerList = this.findRetryBookingTravellers();
    let selectedHotelDetailsObj: any = JSON.parse(this.gallopLocalStorage.getItem("selectedHotelDetailedObj"));
        let failedRatesStr: any = this.gallopLocalStorage.getItem('failedRates');
        let failedRates = {};
        if (failedRatesStr) {
          failedRates = JSON.parse(failedRatesStr);
        }

       if (selectedHotelDetailsObj[0].hotelRateDetail.ratePlanTypePPN != null) {
          failedRates[selectedHotelDetailsObj[0].hotelRateDetail.ratePlanTypePPN] = true;
        } else {
         failedRates[selectedHotelDetailsObj[0].hotelRateDetail.ratePlanType] = true;
        }
        this.gallopLocalStorage.setItem('failedRates', JSON.stringify(failedRates));
    let hotelSearchQueryParam: HotelQueryParam = deserialize(JSON.parse(this.gallopLocalStorage.getItem("hotelSearchRequest")));    
    // hotelSearchQueryParam.travellerEmail.splice(-1);
    // hotelSearchQueryParam.travellerEmail.splice(-1);
    hotelSearchQueryParam.travellerEmail = bookingFailedTravellerList;
    hotelSearchQueryParam.roomCount = ""+hotelSearchQueryParam.travellerEmail.length;
    // hotelSearchQueryParam.travellerEmail = hotelSearchQueryParam.travellerEmail.pop();
    this.gallopLocalStorage.setItem('hotelSearchRequest', JSON.stringify(hotelSearchQueryParam));
    this.hotelSearchService.setPreviousSearch(hotelSearchQueryParam);
    this.hotelSearchService.broadCastHotelRequest(hotelSearchQueryParam);
    let passengerFormData: any = JSON.parse(this.gallopLocalStorage.getItem('passengersFormData'));
    let passengersData = JSON.parse(this.gallopLocalStorage.getItem('passengers'));
    // if(passengerFormData['passengers'].length > 2){
    //   passengerFormData['passengers'].splice(-1);
    //   passengerFormData['passengers'].splice(-1);
    //   passengersData['passengers'].splice(-1);
    //   passengersData['passengers'].splice(-1);
    // }
    let  filterPassengerFormData = passengerFormData['passengers'].filter(item => bookingFailedTravellerList.includes(item.email)); 
    let filterPassengerFormDataObj  = { "passengers":filterPassengerFormData};
    let  filterPassengerData = passengersData['passengers'].filter(item => bookingFailedTravellerList.includes(item.email)); 
    let filterPassengerDataObj  = { "passengers":filterPassengerData};
    this.hotelSearchService.retryHotelFailedBooking = true;
    this.gallopLocalStorage.setItem("passengersFormData", JSON.stringify(filterPassengerFormDataObj));
    this.gallopLocalStorage.setItem("passengers", JSON.stringify(filterPassengerDataObj));
    this.hotelSearchService.selectedHotel.hotelInfo.hotelRooms.length = hotelSearchQueryParam.roomCount;
  }
  backToHotelSelection(){
    this.retryFailedBookingWork();  
    this.searchService.comingFromRetryBooking=true;  
    this.goToSelectRoom();
  }
  goToSelectRoom() {
    this.bsModalRef.hide();
    this.searchService.paymentPage = false;
    this.bookingService.bookingResponse =undefined;
    // this.removeTravellers();
    window.scrollTo(0, 0);
    this.searchService.emptyIntervalID();
    this.location.replaceState("/search");
    this.router.navigate(['/hotelSelection'], { relativeTo: this.activatedRoute, queryParams: {} });
  }
  removeTravellers(){
  //    let passengersDetails = JSON.parse(this.gallopLocalStorage.getItem('passengers'));
    // let employeEmails = this.searchService.employeeEmail;
    // const emailsFromFirstData = employeEmails.map(item => item.email);
  //   const filteredSecondData = passengersDetails['passengers'].filter(item => emailsFromFirstData.includes(item.email));
  //  
  let passengerFormData: any = JSON.parse(this.gallopLocalStorage.getItem('passengersFormData'));
  let passengersData = JSON.parse(this.gallopLocalStorage.getItem('passengers'));
  if(passengerFormData.length < 2){
    passengerFormData.splice(-1);
    passengersData.splice(-1);
  }
  // let  filterPassengerFormData = passengerFormData['passengers'].filter(item => this.bookingFailedTravellerList.includes(item.email)); 
  // let  filterPassengerData = passengersData['passengers'].filter(item => this.bookingFailedTravellerList.includes(item.email)); 
    this.gallopLocalStorage.setItem("passengersFormData", JSON.stringify(passengerFormData));
    this.gallopLocalStorage.setItem("passengers", JSON.stringify(passengersData));
}
getTotalAdditionalFeesData() {
  const flight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"));
  const selectCar = deserialize(JSON.parse(this.gallopLocalStorage.getItem("selectedCar")));
  const selectedHotel = JSON.parse(this.gallopLocalStorage.getItem("selectedHotelDetailedObj"));
  let allAdditionalFees = [];
  if (Array.isArray(flight)) {
    flight.forEach(f => {
      if (Array.isArray(f.appliedAdditonalFees)) {
        allAdditionalFees = allAdditionalFees.concat(f.appliedAdditonalFees);
      }
    });
  }
  if (Array.isArray(selectedHotel)) {
    selectedHotel.forEach(hotel => {
      if (hotel.hotelRateDetail?.appliedAdditonalFees && Array.isArray(hotel.hotelRateDetail.appliedAdditonalFees)) {
        allAdditionalFees = allAdditionalFees.concat(hotel.hotelRateDetail.appliedAdditonalFees);
      }
    });
  }
  if (Array.isArray(selectCar)) {
    selectCar.forEach(car => {
      if (car.appliedAdditonalFees && Array.isArray(car.appliedAdditonalFees)) {
        allAdditionalFees = allAdditionalFees.concat(car.appliedAdditonalFees);
      }
    });
  }
  // code For FIltering the Additional Fees by their type
  // const feesByType = {};
  // if(allAdditionalFees && allAdditionalFees.length >0){
  //   allAdditionalFees.forEach(fee => {
  //     if (!fee?.type || typeof fee.amount !== 'number') return;
  //     if (!feesByType[fee.type]) {
  //       feesByType[fee.type] = {
  //         type: fee.type,
  //         name: fee.displayLabel,
  //         totalAmount: 0
  //       };
  //     }
  //     feesByType[fee.type].totalAmount += fee.amount;
  //   });
  // }
  if(allAdditionalFees != null && allAdditionalFees.length >0){
    return allAdditionalFees.map(fee => ({
      type: fee.type,
      name: fee.displayLabel,
      totalAmount: fee.amount
    }));
  }
  return allAdditionalFees;

  // return Object.values(feesByType)
}
getTotalAdditionalFees(){
  let feesByType = this.getTotalAdditionalFeesData();
  // let  total = Object.values(feesByType).reduce((sum:number, fee) => {
  //   return sum + (fee as { totalAmount: number }).totalAmount;
  // }, 0 as number); 
  // return Number(total);
  return feesByType.reduce((sum, fee) => sum + fee.totalAmount, 0);

}

}
