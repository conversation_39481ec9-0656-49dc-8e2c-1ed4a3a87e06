import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { AddCardWidgetComponent } from './add-card-widget.component';

describe('AddCardWidgetComponent', () => {
  let component: AddCardWidgetComponent;
  let fixture: ComponentFixture<AddCardWidgetComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [AddCardWidgetComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AddCardWidgetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
