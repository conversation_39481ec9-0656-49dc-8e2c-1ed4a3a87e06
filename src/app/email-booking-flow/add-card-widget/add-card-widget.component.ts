import { Component, OnInit, Output, EventEmitter, NgZone, HostListener, Input } from '@angular/core';
import * as Script1 from "../../../assets/js/emailflow/script1.js";
import { UntypedFormControl, Validators } from '@angular/forms';
import { _ } from 'src/app/util/title';
import { TranslateService } from "@ngx-translate/core";
import { environment } from 'src/environments/environment';
import { NavigationUtil } from 'src/app/util/navigation-util';
import { Constants } from 'src/app/util/constants';
import { Subject } from 'rxjs';
import { CommonUtils } from 'src/app/util/common-utils';
import { AdminPanelService } from 'src/app/admin-panel.service';
import { ALL_AIRLINES } from 'src/app/util/airlines';
import { SearchService } from 'src/app/search.service';


declare var initGallopAddCardUI: any;
declare var initGallopAddCardUIForUatp:any;
declare var postSaveCardToPaymentIframe: any;
// declare var getPaymentToken:any;

interface Window { my: any; }
@Component({
    selector: 'add-card-widget',
    templateUrl: './add-card-widget.component.html',
    styleUrls: ['./add-card-widget.component.scss'],
    standalone: false
})
export class AddCardWidgetComponent implements OnInit {

  public errorMessage: string = "";
  @Output() goBackEmitter = new EventEmitter();
  @Input() loggedIn: boolean = false;
  @Input() cardButtonStyle: boolean = false;
  @Input() njoySpecificBuild: boolean = false;
  @Input() changing: Subject<any>;
  @Input() hideGoBackButton = false;
  @Input() cardUpdating =false;
  @Input() cardAddForUapa=false
  @Input() qboCards =[];
  @Input() departmentArray=[];
  @Input() edituatpCard=false;
  @Input() selectedUapaCard:any;
  @Input() selectedAirlines=[];
  nameOnCardControl: UntypedFormControl;
  disabledAirlineDropdown =false;
  cardValue='';
  airlineValue="";
  search='';
  departmentArrayForDropdwon=this.departmentArray;
  departmentValue=[];
  prevToken = "";
  paymentToken = "";
  gallopToken = "";
  uppAccount=false
  cardSubmitRequired = false;
  dropDownopen=false;
  stripeErrorMsg = "";
  all_airlines = Constants.uatpAirlines;
  lastResult = {};
  constructor(private ngZone: NgZone, public adminPanelService: AdminPanelService, public searchService: SearchService,
    public translateService: TranslateService,) {
    this.nameOnCardControl = new UntypedFormControl("", [Validators.required,Validators.pattern(Constants.RGEX_ONLY_ALPHA_AND_SPACE)]);
  }

  ngOnInit() {
    if(!this.edituatpCard){
    if(this.cardAddForUapa){
      if(this.selectedAirlines.length > 0){
        for(let item of this.selectedAirlines){
          this.all_airlines = this.all_airlines.filter(item2 => item2.id!==item)
        }
      }
      this. departmentArrayForDropdwon=this.departmentArray;
      this.onChangeDepartment('All dept', true)
      initGallopAddCardUIForUatp()
    }else{
    initGallopAddCardUI();
    }
  }else{
    this.airlineValue = this.selectedUapaCard.airline;
    this.disabledAirlineDropdown =true;
    this. departmentArrayForDropdwon=this.departmentArray;
    let departMentId = this.selectedUapaCard.departmentIds.split(",");
    for(let dept of departMentId){
      if(dept==='-1'){
        this.onChangeDepartment('All dept', true)
      }else{
        let deptId = +dept
        this.onChangeDepartment(deptId, true)
      }
    }
    if(this.selectedUapaCard.accountCode){
      this.uppAccountValue = this.selectedUapaCard.accountCode;
      this. updateUppAccount(true)
    }
  }
  if(this.changing){
    this.changing.subscribe(resp => { 
      if(resp){
      this.setAddCardProgress(resp.value1);
      this.setErrorMessage(resp.value);
      }
    });
  }
  }
 
  alignTopNeeded() {
    if (this.njoySpecificBuild) {
      return true;
    }
    return false;
  }
  closeDropdown() {
    this.dropDownopen = false;
  }
  searchByAirlineCode(term: string, item: any) {
    term = term.toLowerCase();
    return (item.id && item.id.toLowerCase().indexOf(term) > -1 || item.value && item.value.toLowerCase().indexOf(term) > -1);
  }
  editUatpCard(){
    if(this.cardAddForUapa && this.airlineValue==="" && this.departmentValue.length===0){
      this.showerrorForAirlines =true;
      this.showerrorForDepartment =true;
      return;

    }
    if(this.cardAddForUapa && this.airlineValue===""){
      this.showerrorForAirlines =true;
      return;
    }
    if(this.cardAddForUapa && this.departmentValue.length===0){
      this.showerrorForDepartment =true;
      return;
    }
    if(this.uppAccount && this.uppAccountValue===''){
      this.showErrorUppaccount =true;
      return;
    }
    this.addCardProgress = true;
    this.getBackUaptData();
      let data  = JSON.stringify({gallopTokenId:this.selectedUapaCard.gallopTokenId ,name: this.selectedUapaCard.name,last4 :this.selectedUapaCard.last4,

        expMonth : this.selectedUapaCard.expMonth,
        expYear : this.selectedUapaCard.expYear	,
        
        departmentIds : this.adminPanelService.uatpDept,
        
        airline : this.adminPanelService.uatpAirline,
        accountCode : this.adminPanelService.uatpPPaccount});
      this.goBackEmitter.emit(data);
  }
  getBackUaptData(){
    if(this.cardAddForUapa){
      this.adminPanelService.uatpAirline = this.airlineValue;
      this.adminPanelService.uatpPPaccount = this.uppAccountValue;
      if(this.departmentValue.length===1 && this.departmentValue[0]==='All dept'){
      this.adminPanelService.uatpDept ="-1";
      }else{
        this.adminPanelService.uatpDept =this.departmentValue.toString();
      }
    }
  }
  public goBack(data: any) {
    this.getBackUaptData();
    this.goBackEmitter.emit(data);
    if(this.cardValue!==''){
      this.adminPanelService.selectedqboCardvalue = this.cardValue;
    }
  }
  onChangeDepartment(option, event) {
    if (event) {
      if (option == "All dept") {
        this.departmentValue=[];
        this.departmentValue.push('All dept');
        
      }
      else {
        this.departmentValue = this.departmentValue.filter(dept => {
          if (dept !== 'All dept') return true;
        });
        this.departmentValue.push(option);
      }
    } else if (!event && option == "All dept") {
      this.departmentValue = [];
    } else {
      this.departmentValue = this.departmentValue.filter(dept => {
        if (dept !== option && dept !== 'All dept') return true;
      });
    }
    this.isDeptChecked(option);
  }
  enterppaccount(text){
    if(text!==''){
      this.showErrorUppaccount =false;
    }else{
      this.showErrorUppaccount =true;
    }
  }
  showErrorUppaccount =false;
  uppAccountValue ='';
  updateUppAccount(event){
    this.uppAccount =event;
    if(!event){
    this.showErrorUppaccount =false;
    this.uppAccountValue ='';
    }

  }
  isDeptChecked(option) {
    return this.departmentValue.indexOf(option) > -1;
  }
  public addCardProgress: boolean = false;
  searchByDepartmentNameAndId(term: string, item: any) {
    term = term.toLowerCase();
    return (item.name && item.name.toLowerCase().indexOf(term) > -1)
  }
  setAddCardProgress(flag: boolean) {
    this.addCardProgress = flag;
  }
  getDepartmentName(id){
    if(this.departmentValue && this.departmentValue.length > 0){
    if(id==='All dept'){
      return 'fuild.AllDepartments'
    }else{
    let departmentName = this.adminPanelService.getDepartmentName(id);  
    return departmentName;
    }
  }else{
    return 'employee.Select'
  }
  }
  filterSelectedDeptlist(item) {
    this.departmentArrayForDropdwon= this.departmentArrayForDropdwon.filter(item4 => item4.departmentId !=='All dept'); 
    this.departmentArray = this.departmentArray.filter(item4 => item4.departmentId !=='All dept');
    for (let option of item) {
      this.departmentArrayForDropdwon = this.departmentArrayForDropdwon.filter(item2 => item2.departmentId !== option);
    }
    
   
    for (let option of item) {
      let departmentArray=[];
      if(this.departmentArray.length > 0){
        for(let item of this.departmentArray){
          departmentArray.push(item);
        }
      }
     // departmentArray = this.sortListDept(this.adminPanelService.getDepartments());
      if (departmentArray && departmentArray.length > 0) {
       
        let department = departmentArray.find(item3 => item3.departmentId === option)
        if(department){
        this.departmentArrayForDropdwon.unshift(department);
        }
      }
      
    }
    this.departmentArrayForDropdwon.unshift({ value: 'All Departments', departmentId: 'All dept',name:"fuild.AllDepartments" });
    
  }
  getLabelValueDepartment(){
    if (this.dropDownopen) {
      return 'fuild.Typetosearch';;
    }else if(this.departmentValue && this.departmentValue.length === 0){
      return this.translateService.instant('employee.SelectDepartment');
    } else  {
      return '';
    }   
  }
  getLabelValueUatp(){
    if (this.airlineValue !=='') {
      let findValue = this.all_airlines.findIndex(item => item.id === this.airlineValue);
      let cardString= this.all_airlines[findValue].value;
      return  cardString;
    } else  {
      return this.translateService.instant('employee.SelectAirline');;
    } 
  }
  getLabelValue() {
    if (this.cardValue !=='') {
      let findValue = this.qboCards.findIndex(item => item.categoryId === this.cardValue);
      let cardString= this.qboCards[findValue].name;
      return  cardString;
    } else  {
      return this.translateService.instant('employee.Select');;
    }
  }
  showerror =false;
  showerrorForAirlines =false;
  showerrorForDepartment =false;
  
  public addCard() {
    if(!this.cardAddForUapa && this.qboSyncedCard && this.cardValue==='' &&  this.qboCards && this.qboCards.length >0 ){
      this.showerror =true;
      return;
    }
   
   
    if(this.cardAddForUapa && this.airlineValue==="" && this.departmentValue.length===0){
      this.showerrorForAirlines =true;
      this.showerrorForDepartment =true;
      return;

    }
    if(this.cardAddForUapa && this.airlineValue===""){
      this.showerrorForAirlines =true;
      return;
    }
    if(this.cardAddForUapa && this.departmentValue.length===0){
      this.showerrorForDepartment =true;
      return;
    }
    if(this.uppAccount && this.uppAccountValue===''){
      this.showErrorUppaccount =true;
      return;
    }
    this.nameOnCardControl.markAsTouched();
    this.nameOnCardControl.updateValueAndValidity();
    if (this.nameOnCardControl.invalid) {
      return;
    }
    this.addCardProgress = true;
    postSaveCardToPaymentIframe();

    setTimeout(() => {
      if (!this.lastResult && this.addCardProgress) {
        this.stripeErrorMsg = _("addCardWidghet.RequestcouldnotbecompletedPleasetryagain").toString();
        if(this.disabledAirlineDropdown && !this.edituatpCard){
          this.goBack(JSON.stringify({ 'type': 'newCardAdded','process':'editUapaCard', 'tokens': this.getPaymentToken() }));
        }else{
        this.goBack(JSON.stringify({ 'type': 'newCardAdded', 'tokens': this.getPaymentToken() }));
        }
      }
    }
      , 10000);

  }

  public setErrorMessage(error: string) {
    this.errorMessage = error;
  }


  @HostListener('window:message', ['$event'])
  onMessage(e) {
    let dataStr = '';
    if (typeof e.data === 'string' || e.data instanceof String) {
      dataStr = e.data;
    } else {
      dataStr = JSON.stringify(e.data);
    }
    const msgDataObj = JSON.parse(dataStr);
    if (msgDataObj.data) {
      if (dataStr.indexOf("stripeSuccess") > -1) {
        if (this.prevToken !== msgDataObj.data.id) {
          this.setOutcome(msgDataObj.data);
        }
        this.prevToken = msgDataObj.data.id;
      } else if (dataStr.indexOf("errorStripe") > -1) {
        this.setOutcome(msgDataObj.data);
      } else if (dataStr.indexOf("gallopError") > -1) {
        this.addCardProgress = false;
        const result = { 'error': { 'message': msgDataObj.message } };
        this.setOutcome(result);
      }
    } else {
      if (msgDataObj.event === 'gallopError') {
        this.addCardProgress = false;
      }
    }
  }

  getPaymentToken() {
    return { 'token': this.paymentToken, 'gToken': this.gallopToken, 'error': this.stripeErrorMsg };
  }
  getCurrencySymbol(item): string {
    return CommonUtils.getCurrencySymbol(item);

  }
  qboSyncedCard =true;
  syncQboOrnot(event){
    this.qboSyncedCard =event;
    if(!event){
      this.showerror =false;
      this.cardValue='';
    }
  }
  private setOutcome(result) {
    {
      if (result) {
        this.lastResult = result;
        if (result.error) {
          this.cardSubmitRequired = result.isAllowedToSave;

          this.stripeErrorMsg = result.error.message;

          // $('#paymentToken').val(stripeErrorMsg);
          // $('#payments').validate().form();
          // $(".payment-save").hide();

        } else {
          this.paymentToken = result.id;
          this.gallopToken = result.gallopTokenId;
          this.stripeErrorMsg = "";

          // $('#paymentToken').val(paymentToken);
        }
        if(this.disabledAirlineDropdown && !this.edituatpCard){
          this.goBack(JSON.stringify({ 'type': 'newCardAdded','process':'editUapaCard', 'tokens': this.getPaymentToken(),'result': result }));
        }else{
        this.goBack(JSON.stringify({ 'type': 'newCardAdded', 'tokens': this.getPaymentToken(),'result': result }));
        }
       // this.goBack(JSON.stringify({ 'type': 'newCardAdded', 'tokens': this.getPaymentToken(), 'result': result }));
      }
    }
  }
  showCardChanged(event){
    this.showerror =false;
  }
  showAirlineChanged(event){
    this.showerrorForAirlines =false;
    if(this.airlineValue!=='UA'){
      this.uppAccountValue ='';
      this.uppAccount =false;
    }
  }
  showDepartmentChanged(event){
    this.showerrorForDepartment =false;
  }
  showCCInputHtml(){
    this.edituatpCard =false;
    initGallopAddCardUIForUatp()
  }

}
