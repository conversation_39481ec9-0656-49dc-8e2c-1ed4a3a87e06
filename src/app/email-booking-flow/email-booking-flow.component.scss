@import "../../variables.scss";

:host {
    width: 100vw;
}

::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    opacity: 1;
    color: #AEAEAE;
}

::-moz-placeholder {
    /* Firefox 19+ */
    opacity: 1;
    color: #AEAEAE;
}

:-ms-input-placeholder {
    /* IE 10+ */
    opacity: 1;
    color: #AEAEAE;
}

:host ::ng-deep {

    .ng-select.ng-select-single .ng-select-container .ng-value-container,
    .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
        overflow: visible !important;
        position: relative !important;
        top: -2px !important;
    }

    .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
        position: absolute;
        left: 0;
        width: 100%;
        top: 0px;
    }

    .ng-select .ng-select-container .ng-value-container .ng-input>input {
        box-sizing: content-box;
        background: none;
        border: 0;
        box-shadow: none;
        outline: 0;
        cursor: default;
        width: 100%;
        color: black;
    }
}

:-moz-placeholder {
    /* Firefox 18- */
    opacity: 1;
    color: #AEAEAE;
}

.expense-image {
    height: 30px;
    width: auto;
    padding-bottom: 7px;
}

:host ::ng-deep.ng-clear-wrapper {
    display: none !important;
}

:host ::ng-deep.ng-select-container {
    border: none !important;

    &:after {
        display: none;
    }
}

.formControl99 {
    height: 65px;
    outline: none;
    width: 100% !important;
    background-color: #FFFFFF;
    font-family: "apercu-mono";
    letter-spacing: -0.98px;
    margin-top: 10px;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 0px;
    max-width: 516px;
}

.phone-textfield1 {
    float: left;
    width: auto;
}

.formControl1 {
    height: 65px;
    border-radius: 8px;
    outline: none;
    width: 100%;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    padding-left: 100px;
    font-family: "apercu-mono";
    letter-spacing: -0.98px;
}

.formControl2 {
    height: 65px;
    border-radius: 8px;
    padding: 12px 16px;
    outline: none;
    width: 100%;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    font-family: "apercu-mono";
    letter-spacing: -0.98px;
}

.phone-number {
    position: absolute;
    width: 90px;
    margin-top: 0px;
    padding-top: 14px;
    height: 65px;
    margin-left: 8px;
    border: none;
    cursor: pointer;
    outline: none;
    border-right: 2px solid #E7E6E4;
}

.error {
    margin: 2px 0;
    color: $danger;
}

.seatButton {
    margin-left: 30px !important;
}

.input-with-checkbox.disabled input {
    background-color: #E7E6E4;
    pointer-events: none;
}

.mdl-checkbox:last-child {
    margin-right: 0;
}

span.error {
    color: #f00;
    font-size: 12px;
}


.button-container {
    float: left;
    width: 100%;
}

.button {
    border-radius: 2px !important;
    height: 64px;
    padding: 0 32px;
    border: none;
    text-transform: capitalize;
    font-size: 20px;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    letter-spacing: 0.6px;
    line-height: 25px;
}

.button1 {
    border-radius: 2px !important;
    height: 64px;
    padding: 0 32px;
    border: none;
    text-transform: capitalize;
    font-size: 20px;
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    letter-spacing: 0.6px;
    line-height: 25px;
}

.button-primary {
    cursor: pointer;
    background: $themeColor;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
    color: var(--button-font-color);
}

.button-primary1 {
    cursor: pointer;
    background: transparent;
    box-shadow: none;
    text-shadow: none;
    color: var(--button-font-color);
}

.button-secondary {
    background: transparent;
    color: $themeColor;
    border: 2px solid $themeColor;
}

.button-text {
    background: none;
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0;
    color: $themeColor;
    font-family: $fontRegular;
}

.select-box {
    position: relative;
    float: left;
    width: 100%;
}

.title-select-box {
    max-width: 118px;
}

.select-box select {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    position: relative;
    z-index: 2;
    background-image: url(/../../assets/images/arrow-down1.png);
    background-repeat: no-repeat;
    background-position: right 5px center;
    background-size: 26px;
}

.mdl-radio__ripple-container,
.mdl-radio__outer-circle,
.mdl-radio__inner-circle {
    display: none;
}

.mdl-radio {
    float: left;
    width: 100%;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    height: 50px;
    padding: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.mdl-radio.is-checked {
    background: $themeColor;
    color: #fff;
}

.input-with-checkbox.disabled input {
    background-color: #E7E6E4;
    pointer-events: none;
}

.input-box {
    float: left;
    width: 100%;
    margin-bottom: 12px;
    position: relative;
}

.input-label {
    float: left;
    width: 100%;
    color: #ABA7A4;
    font-size: 14px;
    line-height: 17px;
    margin-bottom: 8px;
    font-family: $fontRegular;
    font-weight: normal;
}

.input-textfield {
    float: left;
    width: 100%;
    border: 2px solid #E7E6E4;
    background-color: #FFFFFF;
    height: 50px;
    font-size: 16px;
    padding: 5px 16px;
    color: $primaryColor;
    resize: none;
    border-radius: 0 !important;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
}

textArea.input-textfield {
    padding-top: 10px;
    padding-bottom: 10px;
}


.input-textfield+.mdl-checkbox {
    width: auto;
    position: absolute;
    right: 2px;
    top: 27px;
    background: #F7F7F7;
    height: 46px;
    padding: 0 19px 0px 38px;
    border: none;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__box-outline {
    top: 14px;
    left: 12px;
    background-color: #FFFFFF;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__focus-helper {
    top: 14px;
    left: 12px;
}

.mdl-checkbox__ripple-container {
    top: 4px;
    left: 2px;
}

.input-with-checkbox input {
    padding-right: 130px;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__label {
    font-size: 14px;
    line-height: 26px;
    font-weight: normal;
    font-family: $fontRegular;
}

.link-icon,
.link-text {
    display: inline-block;
    vertical-align: middle;
}

.mdl-checkbox__box-outline {
    border: 1px solid #979797;
    width: 17px;
    height: 17px;
    border-radius: 0;
}

.mdl-checkbox.is-checked .mdl-checkbox__box-outline {
    border: 1px solid $themeColor;
}

.mdl-checkbox.is-checked .mdl-checkbox__tick-outline {
    background-color: $themeColor;
}

.expensifyLoginEmailCheckbox {
    width: 17px;
    height: 17px;
}

input[type=checkbox]:after {
    content: " ";
    display: inline-block;
    visibility: visible;
    width: 17px;
    height: 17px;
}

input[type=checkbox]:checked:after {
    background-color: $themeColor;
    background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgMSAxIgogICBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWluWU1pbiBtZWV0Ij4KICA8cGF0aAogICAgIGQ9Ik0gMC4wNDAzODA1OSwwLjYyNjc3NjcgMC4xNDY0NDY2MSwwLjUyMDcxMDY4IDAuNDI5Mjg5MzIsMC44MDM1NTMzOSAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IE0gMC4yMTcxNTcyOSwwLjgwMzU1MzM5IDAuODUzNTUzMzksMC4xNjcxNTcyOSAwLjk1OTYxOTQxLDAuMjczMjIzMyAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IgogICAgIGlkPSJyZWN0Mzc4MCIKICAgICBzdHlsZT0iZmlsbDojZmZmZmZmO2ZpbGwtb3BhY2l0eToxO3N0cm9rZTpub25lIiAvPgo8L3N2Zz4K);
}

/*placeholder*/
.input-with-checkbox.disabled input::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    opacity: 0;
}

.input-with-checkbox.disabled input::-moz-placeholder {
    /* Firefox 19+ */
    opacity: 0;
}

.input-with-checkbox.disabled input:-ms-input-placeholder {
    /* IE 10+ */
    opacity: 0;
}

.input-with-checkbox.disabled input:-moz-placeholder {
    /* Firefox 18- */
    opacity: 0;
}

.input-with-checkbox.disabled .mdl-checkbox {
    left: 0;
    right: 0;
    background: #E7E6E4;
}




.inline-checkbox.mdl-checkbox {
    float: none;
    display: inline-block;
    padding-left: 0;
    padding-right: 24px;
}

.inline-checkbox .mdl-checkbox__label {
    color: var(--button-font-color);
    font-size: 12px;
    letter-spacing: 1px;
    line-height: 18px;
    font-family: $fontBold;
    text-transform: uppercase;
}

.inline-checkbox .mdl-checkbox__box-outline {
    right: 0;
    left: auto;
    bottom: 1px;
    top: auto;
}


/*modal css*/
.modal-dialog {
    max-width: 710px;
}

.modal-content {
    border-radius: 0px;
    box-shadow: 0 0 19px 0 rgba(0, 0, 0, 0.13);
    border: none;
    text-align: center;
    max-width: 710px;
    margin: 24px auto;
    width: calc(100% - 48px);
}

.modal-header {
    border: none;
    padding: 22px 17px 15px 25px;
}

.modal-title {
    display: inline-block;
    width: 35px;
    float: left;
}

.modal-title img {
    width: 35px;
}

.close {
    color: #000;
    opacity: 1;
}

.close:hover {
    color: #000;
    opacity: 1;
}

.modal-body {
    padding-bottom: 35px;
}

.modal-icon-container {
    float: left;
    width: 100%;
    text-align: center;
}

.modal-icon {
    background-color: #DDFFF7;
    border: 3px solid #1EBD97;
    width: 48px;
    height: 48px;
    display: inline-flex;
    color: #1EBD97;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
}

.modal-icon i {
    font-size: 30px;
}

.modal-content-heading {
    font-size: 22px;
    letter-spacing: -0.98px;
    line-height: 56px;
    color: #3C413B;
    float: left;
    width: 100%;
}

.modal-content-heading {
    font-size: 22px;
    letter-spacing: -0.98px;
    line-height: 56px;
    color: #3C413B;
    float: left;
    width: 100%;
    margin-top: 15px;
    margin-bottom: 10px;

}

.modal-content-heading h3 {
    margin: 0;
    font-size: 22px;
    color: $primaryColor;
}

.modal-content-text {
    color: $secondaryColor;
    font-size: 18px;
    font-family: $fontRegular;
    line-height: 26px;
    margin-bottom: 50px;
    max-width: 530px;
    margin-left: auto;
    margin-right: auto;
}

.modal-content-text p {
    line-height: 26px;
}


span.error {
    color: #f00;
    font-size: 12px;
    float: left;
    width: 100%;
}

#flyerNumberCheckbox-error,
#knownTravellerCheckbox-error,
#guestNumberCheckbox-error {
    display: none !important;
}

.datepicker {
    background-image: url(/../../assets/images/calendar.svg);
    background-repeat: no-repeat;
    background-position: right 17px center;
}

.input-textfield.datepicker {
    padding-right: 50px;
}

.input-box-icon-container {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    height: 50px;
}

.input-box-icon-container.withLabel {
    margin-top: 25px;
}

.input-box-icon-container.left {
    left: 17px;
}

.input-box-icon-container.right {
    right: 19px;
}

.input-box-icon-container.right+input {
    padding-right: 50px;
}

.input-box-icon-container.left+input {
    padding-left: 50px;
}


.label-inputbox {
    margin-bottom: 16px;
}

.input-label {
    float: left;
    width: 100%;
    color: #ABA7A4;
    font-size: 14px;
    line-height: 17px;
    margin-bottom: 8px;
    font-family: $fontRegular;
    font-weight: normal;
}

.input-textfield {
    float: left;
    width: 100%;
    font-size: 16px;
    background-color: #FFFFFF;
    height: 65px !important;
    border-radius: 8px !important;
    padding: 5px 16px;
}

.input-with-checkbox input {
    padding-right: 130px;
}

.input-textfield+.mdl-checkbox .mdl-checkbox__label {
    font-size: 14px;
    line-height: 17px;
    font-weight: normal;
    font-family: $fontRegular;
    width: 100%;
}

.input-textfield-lg {
    height: 64px;
}

/*for userDetailsPage*/
.mdl-radio,
.mdl-radio.is-upgraded {
    width: auto;
    margin-right: 5px;
    padding: 0 35px;
}

.checkbox-container {
    float: left;
    width: 100%;
}

.checkbox-container .mdl-radio {
    margin-bottom: 8px;
}

/*datepicker*/
.ui-datepicker {
    z-index: 9 !important;
}

.ui-datepicker-month,
.ui-datepicker-year {
    background: #fff;
    border: 2px solid #E7E6E4;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    font-family: $fontRegular  !important;
    font-weight: normal !important;
}

.ui-widget-content {
    color: $primaryColor;
}

.ui-widget {
    font-family: $fontRegular;
    font-weight: normal;
}

.ui-datepicker-month,
.ui-datepicker-year {
    background-image: url(/../../assets/images/arrow-down.png);
    background-repeat: no-repeat;
    background-position: right 5px center;
}

.ui-widget-header {
    border: 1px solid #EEECEB;
    background: #EEECEB;
    color: $primaryColor;
    font-weight: normal;
}

.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
    border: 1px solid #E7E6E4;
    background: #EEECEB;
    color: $primaryColor;
}

.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
    border: 1px solid #E7E6E4;
    background: #EEECEB;
    font-weight: normal;
    color: $primaryColor;
}

.ui-widget-header .ui-icon.ui-icon-circle-triangle-w {
    background-image: url(/../../assets/images/down-icon.png);
    background-position: -4px 4px;
    background-size: 24px auto;
    transform: rotate(90deg);
    height: 24px;
    width: 24px;
}

.ui-widget-header .ui-icon.ui-icon-circle-triangle-e {
    background-image: url(/../../assets/images/down-icon.png);
    background-position: 4px -4px;
    background-size: 24px auto;
    transform: rotate(-90deg);
    height: 24px;
    width: 24px;
}

.ui-datepicker .ui-datepicker-prev-hover {
    left: 2px;
    top: 2px;
}

.ui-datepicker .ui-datepicker-next-hover {
    right: 2px;
    top: 2px;
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
    border: 1px solid $themeColor;
    background: $themeColor;
}

.intl-tel-input {
    width: 100%;
}

.intl-tel-input .selected-flag .iti-arrow {
    border: none !important;
    background-image: url(/../../assets/images/arrow-down1.png);
    background-repeat: no-repeat;
    width: 28px;
    height: 28px;
    background-size: 28px;
    background-position: right center;
    top: 3px;
    height: 100%;
    right: 6px;
}


.intl-tel-input.allow-dropdown .selected-flag,
.intl-tel-input.separate-dial-code .selected-flag {
    width: 75px;
}

.intl-tel-input .selected-flag {
    padding: 0 0 0 19px;
}

/*.intl-tel-input .selected-flag .iti-arrow:after{ content: 'keyboard_arrow_down';font-family: 'Material Icons'; position: absolute;}*/
.intl-tel-input.allow-dropdown input,
.intl-tel-input.allow-dropdown input[type="text"],
.intl-tel-input.allow-dropdown input[type="tel"],
.intl-tel-input.separate-dial-code input,
.intl-tel-input.separate-dial-code input[type="text"],
.intl-tel-input.separate-dial-code input[type="tel"] {
    padding-left: 80px;
}


::placeholder {
    /* Chrome/Opera/Safari */
    color: #AEAEAE;
    opacity: 1;
}

::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    color: #AEAEAE;
    opacity: 1;
}

::-moz-placeholder {
    /* Firefox 19+ */
    color: #AEAEAE;
    opacity: 1;
}

:-ms-input-placeholder {
    /* IE 10+ */
    color: #AEAEAE;
    opacity: 1;
}

:-moz-placeholder {
    /* Firefox 18- */
    color: #AEAEAE;
    opacity: 1;
}


.appleDevice ::placeholder {
    /* Chrome/Opera/Safari */
    /*padding-top: 4px;*/
}

.appleDevice ::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    /*padding-top: 4px;*/
}

.appleDevice ::-moz-placeholder {
    /* Firefox 19+ */
    /*padding-top: 4px;*/
}

.appleDevice :-ms-input-placeholder {
    /* IE 10+ */
    /*padding-top: 4px;*/
}

.appleDevice :-moz-placeholder {
    /* Firefox 18- */
    /*padding-top: 4px;*/
}



.modal-backdrop {
    background-color: rgba(238, 237, 235, 1);
}

.modal-backdrop.in {
    filter: alpha(opacity=80);
    opacity: .8;
}

.table-view {
    display: table;
    height: 100%;
    width: 100%;
}

.table-cell-view {
    display: table-cell;
    vertical-align: middle;
}

.passportDate {
    background-image: url(/../../assets/images/calendar.png);
    background-repeat: no-repeat;
}

.input-textfield {
    margin-bottom: 8px;
}

.line-seperator {
    float: left;
    width: 100%;
    background: #C1BDBD;
    height: 2px;
    margin-bottom: 20px;
}



/*select2*/
.select2-container {
    width: 100% !important;
}

.select2-selection__arrow {
    position: relative;
    z-index: 9999;
}

.select2-selection__arrow b {
    border-width: 0 !important;
}

.select2-selection__arrow:after {
    content: '';
    position: absolute;
    background-image: url(/../../assets/images/arrow-down1.png);
    background-repeat: no-repeat;
    height: 36px;
    width: 36px;
    right: 6px;
    top: 14px;
}

.select2-container--default .select2-selection--single {
    height: 64px;
    border-radius: 0;
    border: 2px solid #E7E6E4;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 60px;
}

.select2-dropdown {
    border: 2px solid #E7E6E4;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: $themeColor;
    color: white;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding-left: 16px;
    padding-right: 40px;
}


@media (max-width:991px) {

    .mdl-radio,
    .mdl-radio.is-upgraded {
        padding: 0 30px;
    }

    .input-textfield {
        font-size: 14px;
        padding: 5px 10px;
        letter-spacing: -1.5px;
        word-spacing: -2px;
        max-width: 516px;
    }

    .mdl-radio {
        font-size: 14px;
        letter-spacing: -1.5px;
        word-spacing: -2px;
    }
}


@media (max-width:767px) {
    .phone-number {
        position: absolute;
        width: 90px;
        margin-top: 8px;
        height: 65px;
        border-radius: 0px;
        margin-left: 8px;
        border: none;
        cursor: pointer;
        outline: none;
        border-right: 2px solid #E7E6E4;
    }

    .formControl1 {
        height: 65px;
        border-radius: 8px;
        outline: none;
        width: 100%;
        border: 2px solid #E7E6E4;
        background-color: #FFFFFF;
        padding-left: 95px !important;
        font-family: "apercu-mono";
        letter-spacing: -0.98px;
    }

    .formControl2 {
        height: 65px;
        border-radius: 8px;
        padding: 12px 16px;
        outline: none;
        width: 100%;
        border: 2px solid #E7E6E4;
        background-color: #FFFFFF;
        font-family: "apercu-mono";
        letter-spacing: -0.98px;
    }

    .phone-textfield1 {
        float: left;
        width: 100%;
    }

    .button {
        height: 46px;
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
        letter-spacing: 1px;
        line-height: 11px;
        font-size: 12px;
        text-transform: uppercase;
        margin-bottom: 20px !important;
    }

    .button1 {
        height: 46px;
        box-shadow: none;
        letter-spacing: 1px;
        line-height: 11px;
        font-size: 12px;
        text-transform: uppercase;
        margin-bottom: 5px !important;
        padding: 0 10px;
    }

    .input-label {
        font-size: 12px;
        line-height: 15px;
    }

    .input-textfield {
        height: 40px;
        font-size: 12px;
        padding: 5px 14px;
        letter-spacing: normal;
    }

    .mdl-radio,
    .mdl-radio.is-upgraded {
        height: 40px;
        font-size: 12px;
        line-height: 15px;
        padding: 0 18px;
    }

    .input-textfield+.mdl-checkbox {
        height: 36px;
        top: 25px;
        padding-right: 17px;
    }

    .input-textfield+.mdl-checkbox {
        top: 21px;
    }

    .inline-checkbox .mdl-checkbox__label {
        line-height: 30px;
    }

    .input-textfield+.mdl-checkbox .mdl-checkbox__label {
        font-size: 10px;
        line-height: 18px;
        vertical-align: top;
    }

    .input-textfield+.mdl-checkbox .mdl-checkbox__box-outline {
        width: 12px;
        height: 12px;
        top: 11px;
        left: 16px;
    }

    textarea.input-textfield {
        height: 80px;
    }

    .mdl-radio {
        letter-spacing: normal;
    }

    .appleDevice .input-textfield {
        font-size: 16px !important;
        letter-spacing: -1.1px !important;
        word-spacing: -2px !important;
        padding-top: 0px;
        padding-bottom: 0;
        padding-left: 8px;
        padding-right: 8px;
    }

    .appleDevice .input-box-icon-container.left {
        left: 10px;
    }

    .appleDevice .input-box-icon-container.left+input {
        padding-left: 32px;
    }

    .appleDevice select.input-textfield {
        padding-top: 0;
    }

    .appleDevice .container {
        padding: 0 10px;
    }

    .appleDevice .card-body {
        padding: 22px 10px 31px 10px;
    }

    .appleDevice .card-header {
        padding: 15px 10px;
    }

    .appleDevice .mdl-radio {
        font-size: 16px;
        line-height: 24px;
    }

    .appleDevice .mdl-radio,
    .appleDevice .mdl-radio.is-upgraded {
        padding: 0 15px;
    }

    .mdl-checkbox__ripple-container {
        height: 24px;
        width: 24px;
        top: 7px;
        left: 10px;
    }

    .appleDevice textarea.input-textfield {
        padding-top: 8px;
        padding-bottom: 8px;
    }

    .select2-container--default .select2-selection--single {
        height: 40px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 37px;
        font-size: 12px;
    }

    .select2-selection__arrow:after {
        background-size: 26px;
    }

    .select2-selection__arrow::after {
        height: 26px;
        width: 26px;
        top: 7px;
    }

    .seatButton {
        margin-left: 10px !important;
    }

    .appleDevice .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 37px;
        font-size: 16px;
        padding-left: 8px;
    }
}

.card-body {
    background-color: #fff;
}

.note2 {
    background-color: var(--dark-bg-color);
    box-shadow: inset 0px -3px 5px 0px rgba(0, 0, 0, 0.19);
}

@media(max-width:576px) {

    .formControl1 {
        height: 65px;
        border-radius: 8px;
        outline: none;
        width: 100%;
        border: 2px solid #E7E6E4;
        background-color: #FFFFFF;
        padding-left: 95px !important;
        font-family: "apercu-mono";
        letter-spacing: -0.98px;
    }

    .formControl2 {
        padding: 12px 16px;
        height: 40px;
        outline: none;
        width: 100%;
        height: 65px;
        border-radius: 8px;
        border: 2px solid #E7E6E4;
        background-color: #FFFFFF;
        font-family: "apercu-mono";
        letter-spacing: -0.98px;
    }

    .phone-number {
        position: absolute;
        width: 90px;
        margin-top: 8px;
        height: 65px;
        border-radius: 0px;
        margin-left: 8px;
        border: none;
        cursor: pointer;
        outline: none;
        border-right: 2px solid #E7E6E4;
    }
}

@media(max-width:320px) {
    .formControl1 {
        height: 40px;
        outline: none;
        width: 100%;
        border: 2px solid #E7E6E4;
        background-color: #FFFFFF;
        padding-left: 60px !important;
        font-family: "apercu-mono";
        letter-spacing: -0.98px;
    }

    .formControl2 {
        padding: 12px 16px;
        height: 65px;
        border-radius: 8px;
        outline: none;
        width: 100%;
        border: 2px solid #E7E6E4;
        background-color: #FFFFFF;
        font-family: "apercu-mono";
        letter-spacing: -0.98px;
    }
}