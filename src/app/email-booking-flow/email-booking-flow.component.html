<app-loader *ngIf="isLoading"></app-loader>
<div *ngIf="!isLoading" class="pageBody">
    <email-header></email-header>
    <section class="content">
        <div class="container">
            <div class="page-content" [ngStyle]="getStyleforPaymentPage()">
                <option-selection (selectedFlightChange)='handleSelectedFlightChange($event)'
                    (changeSelectionClicked)='handleChangeSelectionClicked($event)'
                    (goBackEmitter1)='handleBackFromPaymentPage($event)' [noOfPassengers]="travellersCount"
                    [emailQuotOptions]="emailQuotOptions" (goBackEmitterForModal)="handleToOpenPaymentModal($event)" (goBackEmitterForpaymentChange)="handlePaymentchange($event)" [pageMode]="pageMode"></option-selection>
                <div [hidden]="paymentPage" class="card form-card travellersCard inactive">
                    <div [hidden]="!this.eventIdAndOptions" class="card-inner">
                        <div class="traveller-form">
                            <div class="card-header note2" onclick="toggleForm(this);">
                                <span class="form-number">1</span>
                                <div>
                                    <h3 *ngIf="this.uniqueCars && this.uniqueCars.length ===0" class="exp-text1"
                                        style="color:#fff;">{{'emailBooking.PassengerDetails' |
                                        translate}}
                                    </h3>
                                    <h3 *ngIf="this.uniqueCars && this.uniqueCars.length >0" class="exp-text1"
                                        style="color:#fff;">{{'emailBooking.DriverDetails' |
                                        translate}}
                                    </h3>
                                    <span class="form-arrow-down"><img src="assets/images/emailflow/arrow-down1.png"
                                            alt="" /></span>
                                    <div class="traveller-short-info"></div>
                                </div>
                            </div>
                            <!-- <div class="card-body"> -->
                            <personal-details [noOfPassengers]="travellersCount" [emailQuotOptions]="emailQuotOptions"></personal-details>
                            <!-- </div> -->
                        </div>
                    </div>
                    <div class="card-footer">
                        <button *ngIf="!showPaymentButton && !showPaymentLink && this.bookingService.nextButtonClicked" type="button"
                            class="button button-primary nextButton" (click)="processNext()">{{'emailBooking.Next' |
                            translate}}</button>
                    </div>
                </div>
                <div *ngIf="false" [hidden]="paymentPage" class="card payment-card" id="expensifyCard">
                    <div class="card-header note2">
                        <h3 class="exp-text1" style="color:#fff !important;">Expense management</h3>
                    </div>
                    <div class="card-body">
                        <form method="post" id="expensifyForm" class="expensifyForm">
                            <div class="row">
                                <div class="col-lg-12  col-md-12 col-sm-12 col-xs-12">
                                    <label style="margin-top:30px;"
                                        class="mdl-checkbox expensifyLoginEmailCheckboxDiv input-checkbox"
                                        for="expensifyLoginEmailCheckbox">
                                        <input onchange="enableExpensifyTextbox();"
                                            (change)="expensifyCheckboxChanged()" type="checkbox"
                                            id="expensifyLoginEmailCheckbox" [formControl]="addToExpensifyControl"
                                            [(ngModel)]="addToExpensify" name="expensifyLoginEmailCheckbox"
                                            class="expensifyLoginEmailCheckbox knownTravellerCheckbox traveller-group">
                                        <span class="mdl-checkbox__label">Submit receipt to expense reporting</span>
                                    </label>
                                    <div style="width:100%;margin-top:90px;">
                                        <label style="color:gray;margin-top:0px;margin-bottom: 0px;">Select your
                                            expensing tool:</label>
                                        <div class="formControl99"
                                            [ngStyle]="{'border': !addToExpensify ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}">
                                            <ng-select #expense [items]="expenseProviders" bindLabel=""
                                                dropdownPosition="bottom" bindValue="id" [searchable]="false"
                                                [clearable]="false" [formControl]="expenseEmailControl">
                                                <ng-template ng-header-tmp>
                                                    <div class="selectox-header">
                                                        <span>Expensing tool</span>
                                                        <span class="selectBox-remove" (click)="expense.toggle()"><span
                                                                class="material-icons">clear</span></span>
                                                    </div>
                                                </ng-template>
                                                <ng-template ng-label-tmp let-item="item">
                                                    <img class="expense-image" src="{{item.url}}" />
                                                </ng-template>
                                                <ng-template ng-option-tmp let-item="item" let-index="index">
                                                    <img class="expense-image" src="{{item.url}}" />
                                                </ng-template>
                                            </ng-select>
                                        </div>
                                    </div>
                                    <div class="input-box">
                                        <input [formControl]="expensifyEmailControl"
                                            class="input-textfield expensifyLoginEmail disabled input-textfield-lg"
                                            id="expensifyLoginEmail" name="expensifyLoginEmail"
                                            [ngStyle]="{'border': !addToExpensify ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}"
                                            placeholder="{{getPlaceholder()}}" type="text">
                                    </div>
                                    <div *ngIf="expensifyEmailControl.hasError('required') && (expensifyEmailControl.touched || expensifyEmailControl.dirty)"
                                        class="error">{{'emailBooking.Thisfieldisrequired' | translate}}

                                    </div>
                                    <div *ngIf="expensifyEmailControl.hasError('pattern') && (expensifyEmailControl.touched || expensifyEmailControl.dirty)"
                                        class="error">{{'emailBooking.Pleaseenteravalidemail' | translate}}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!--    <div  *ngIf="showPaymentButton" [hidden]="paymentPage" class="card payment-card inactive" id="emergencyContactCard">
                    <div class="card-header note2">
                        <h3 class="exp-text1" style="color:#fff;">Emergency Contact</h3>
                    </div>
                  <div class="card-body">
                        <h4 style="font-size: 20px;
                        margin-top: 18px;margin-bottom:20px;">Someone trusted to help out in an emergency.</h4>
                        <form method="post" id="emergencyContact" [formGroup]="emergencyContact" style="margin-top: 20px;">
                            <div class="row">
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                                    <input formControlName="name" class="formControl2" (input)="getName($event.target.value)" [ngStyle]="changeStyle(emergencyContact.controls['name'].status)"  (focusout)="getName($event.target.value)" type="text" placeholder="Full Name" />
                                    <div *ngIf="emergencyContact.get('name').hasError('pattern') && (emergencyContact.get('name').touched || emergencyContact.get('name').dirty)" id="title-error" class="error">
                                      Please enter a valid name Numeric or special characters are not allowed</div>
                                <div *ngIf="emergencyContact.get('name').hasError('required') && (emergencyContact.get('name').touched || emergencyContact.get('name').dirty)" id="title-error" class="error">
                                    This field is required.</div>
                                    <div *ngIf="emergencyContact.get('name').hasError('minLengthArray')" class="error">Space not allowed</div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12" [ngStyle]="{'margin-top':isMobile ? '10px':'0px'}">
                                    <input formControlName="relationship" class="formControl2" (input)="getRelationship($event.target.value)" [ngStyle]="changeStyle(emergencyContact.controls['relationship'].status)" (focusout)="getRelationship($event.target.value)" type="text" placeholder="Relationship" />
                                    <div *ngIf="emergencyContact.get('relationship').hasError('pattern') && (emergencyContact.get('relationship').touched || emergencyContact.get('relationship').dirty)"  class="error">
                                            Please enter a valid name Numeric or special characters are not allowed</div>
                                      <div *ngIf="emergencyContact.get('relationship').hasError('required') && (emergencyContact.get('relationship').touched || emergencyContact.get('relationship').dirty)"  class="error">
                                        This field is required.</div>
                                        <div *ngIf="emergencyContact.get('relationship').hasError('minLengthArray')" class="error">Space not allowed</div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                                    <div class="input-box">
                                        <div class="select-box select-dropdown searchable-dropdown phone-select-box">
        
                                                <ng-select (change)="onCountrySelected($event)" class="phone-number" [ngStyle]="{'border-right':emergencyContact.controls['dialCode1'].status==='DISABLED' ? '1px solid #E7E6E4':'1px solid var(--dark-bg-color)'}" dropdownPosition="bottom" [items]="countries" formControlName="dialCode1" bindLabel="name" bindValue="dial_code" [searchFn]="searchByNameOrCode">
                                                        <ng-template ng-header-tmp>
                                                                <div class="selectox-header">
                                                                  <span>{{'personal.CountryName' | translate}}</span>
                                                                    </div>
                                                              </ng-template>
                                                  <ng-template ng-label-tmp let-item="item">
                                                      <img class="flag-label1" src="assets/flags/{{getCountryCode1(emergencyContact.get('dialCode1').value, emergencyContact.get('contactNumber').value)}}.png" />
                                                      <span>{{emergencyContact.get('dialCode1').value}}</span>
                                                  </ng-template>
                                                  <ng-template ng-option-tmp let-item="item">
                                                    <span class="country-name" title="{{item.name}}">{{item.name}}</span>
                                                    <img class="icon-country-flag" src="assets/flags/{{item.code | lowercase}}.png" />
                                                  </ng-template>
                                                </ng-select>
                                              
                                            </div>
                                                <div class="phone-textfield1">
                                                  <input type="tel" mask="{{getPhoneNumberMask(emergencyContact.get('dialCode1').value)}}" (input)="getphoneNumber($event.target.value)" (focusout)="getphoneNumber($event.target.value)"class="formControl1" [ngStyle]="changeStyle(emergencyContact.controls['contactNumber'].status)" placeholder="{{getPhoneNumberPaceHolder(emergencyContact.get('dialCode1').value)}}"
                                                    formControlName="contactNumber" />
                                                    <div *ngIf="emergencyContact.get('contactNumber').hasError('pattern') && (emergencyContact.get('contactNumber').touched || emergencyContact.get('contactNumber').dirty)"
                                                    class="error">{{'profilePersonal.Pleaseenteravalidphonenumber' | translate}}
                                                </div>
                                                </div>
                                                <div>
                                             <span *ngIf="emergencyContact.get('dialCode1').hasError('required')"
                                            class="error" style="width:45% !important;white-space: break-spaces !important;">This field is required.</span>
                                         <span *ngIf="emergencyContact.get('contactNumber').hasError('required') && (emergencyContact.get('contactNumber').touched || emergencyContact.get('contactNumber').dirty)"
                                            class="error" style="width:50% !important;margin-left:0px !important;white-space: break-spaces !important;">This field is required.
                                                    </span>   </div>     
                                                                         
        </div>
        
                                    </div>
                            </div>
                        </form>
                    </div>
                </div>-->
                <div [hidden]="paymentPage" *ngIf="(isSelectionComplete())" class="button-container">
                    <div *ngIf="showCreditNameDoesNotMatchError()" class="error" style="margin-bottom: 10px;">
                            <span *ngIf="this.travellersCount > 1" style="margin-right: 4px;">{{'personal.Passenger' | translate }}{{this.bookingService.travelerIndex}}:</span>        {{'setting.Thetravelernameontheairlinecreditis' |  translate}} {{this.crediterName }} {{'setting.anddoesntmatchtheinformationyouveprovided' |  translate}}
                    </div>
                    <span
                        *ngIf="(showPaymentButton && isSelectionComplete())  || (this.pageMode ==='emailflowAgent') || (this.bookingService.responseData && this.bookingService.responseData.length >0)"
                        id="paymentButton">
                        <button type="button" [disabled]="expensifyEmailControl.invalid" class="button button-primary"
                            (click)="proceedToPay()">
                            <span *ngIf="!showCreditNameDoesNotMatchError()">{{'searchResult.Continue' | translate}}</span><span *ngIf="showCreditNameDoesNotMatchError()">{{'searchResult.ProceedWithutCredits' | translate}}</span>
                        </button>
                    </span>
                    <span
                        *ngIf="(false && travellersCount >1 && this.bookingService.responseData && this.bookingService.responseData.length >0) && (showPaymentLink && this.pageMode !=='emailflowAgent')"
                        id="paymentButton" class="seatButton" style="">
                        <button type="button" [disabled]="expensifyEmailControl.invalid" class="button button-primary"
                            attr.data-track="OpenSeatMap" attr.data-params="mandatoryCheck={{this.seatSelectionPage}}"
                            (click)="selectSeat()">{{'emailBooking.Selectseat' |
                            translate}}
                        </button>
                    </span>
                    <span
                        *ngIf=" ( false && travellersCount >1 && this.bookingService.responseData && this.bookingService.responseData.length >0) &&  (showPaymentLink && this.pageMode !=='emailflowAgent')"
                        id="paymentButton">
                        <button [disabled]="expensifyEmailControl.invalid" class="button1 button-primary1"
                            (click)="proceedToPay()"> {{'emailBooking.Skip&' |
                            translate}} {{'emailBooking.Proceed' | translate}}
                            {{'emailBooking.topay' | translate}} {{getTotalPayble() | currency :
                            getCurrencySymbol(currencyCode) : "code" : '1.2-2' }}</button>
                    </span>
                </div>
            </div>
        </div>
    </section>
    <!-- <app-seat *ngIf="seatSelectionPage" [noOfPassengers]="travellersCount" (goBackEmitterSeat)='handleBackFromSeatPage($event)' (proceedTopay)="handleBackFromSeat($event)"></app-seat>
    <div *ngIf="seatSelectionPage" class="addCardFormTemplate"></div>-->
    <payment-details *ngIf="paymentPage" [tripDetails]="this.optionSelectionChild.tripDetails" [_billingItemArray]="getBillingBreakup()" [dropDownopen]="dropDownopen" [all_tags]="tagShow" [all_tagset]="tagset" [new_tagSets_array]="newTagSetsArray"
        [eventIdAndOptions]="eventIdAndOptions" [emailId]="emailId" [sToken]="sToken"
        [outsidePolicyFlag]="outsidePolicyFlag" [noOfPassengers]="travellersCount" [uniqueAirlines]="uniqueAirlines"
        [uniqueHotels]="uniqueHotels" [canAttemptDirectBooking]="canAttemptDirectBooking"
        [tripId]="emailQuotOptions.tripId" (goBackEmitter)='handleBackFromPaymentPage($event)' (reBookEmitter)='handleBackFromPaymentPage2($event)'></payment-details>
    <div *ngIf="paymentPage" class="addCardFormTemplate"></div>
</div>