.footer-navigation {
  margin-bottom: 20px;
}

.footer-list {
  margin-top: 100px !important;
  float: left;
  margin-left: 20px;
  margin-right: 20px;
  margin-bottom: 20px;
  text-align: center;
}

.container3 {
  width: 100%;
  bottom: 0;
  margin-bottom: 0px !important;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.container {
  max-width: 100%;
  margin-bottom: 0px !important;
}

.list-item {
  display: inline-block;
  margin: 0 20px;
  padding-bottom: 0;
  padding-top: 5px;
}

.list-item a {
  text-transform: capitalize;
}

a {
  color: var(--button-bg-color) !important;
  text-transform: none !important;
}

.image {
  height: 30px;
  width: 30px;
  display: inline-block;
  margin-top: 20px;
  text-align: center;
  align-items: center;
}

.footer-navigation ul li {
  display: inline-block;
  position: relative;
  padding: 0 18px 0 6px;
  margin-right: 10px;
  color: var(--button-bg-color) !important;
}

.footer-navigation ul li:after {
  position: absolute;
  right: 0;
  width: 1px;
  height: 10px;
  background: var(--button-bg-color);
  content: "";
  top: 8px;
}

.social ul li {
  display: inline-block;
  position: relative;
  padding: 0 18px 0 6px;
}

@media(max-width: 768px) {
  .container {
    margin-top: 105px !important;
    margin-bottom: 0px !important;
  }

  .container3 {
    width: 100%;
    margin-bottom: 0px !important;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
  }
}

@media(max-width: 959px) and (min-width: 769px) {
  .container3 {
    width: 100%;
    // position: relative !important;
    margin-bottom: 0px !important;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
  }
}