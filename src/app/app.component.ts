import { Component, ElementRef, HostListener, OnInit, Renderer2 } from '@angular/core';
import { ActivatedRoute, Router, NavigationEnd, Event, Params, NavigationStart } from '@angular/router';
import $ from 'jquery';
import { UserAccountService } from './user-account.service';
import { GallopLocalStorageService } from './gallop-local-storage.service';
import { Subscription, BehaviorSubject } from 'rxjs';
import { UserAccountInfo } from './entity/user-account-info';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { DeleteCardModelComponent } from './email-booking-flow/delete-card-model/delete-card-model.component';
import { deserialize } from './util/ta-json/src/methods/deserialize';
import { TranslateService } from '@ngx-translate/core';
import { CommonUtils } from './util/common-utils';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { SearchService } from './search.service';
import { Title } from '@angular/platform-browser';
import { environment } from 'src/environments/environment';
import { DeviceDetailsService } from './device-details.service';
import { ToastrService, ActiveToast } from 'ngx-toastr';
import { ConnectionService } from 'ng-connection-service';
import { InitClientComponent } from './cancel-success/init-client-component';
import { BookingService } from './booking.service';
import { LoginService } from './login.service';
import { ClientConfiguration } from './client-config.service';
import { FeedbackComponent } from './feedback/feedback.component';
import { filter } from 'rxjs/operators';
import { AdminPanelService } from './admin-panel.service';
import { UserCredentialsService } from './user-credentials.service';
import { Constants } from './util/constants';
import { PopupComponent } from './popup/popup.component';
import { isJsxSelfClosingElement } from 'typescript';
import { timingSafeEqual } from 'crypto';
import { Role } from './entity/employee-info';
import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { GallopAnalyticsUtil } from './analytics.service';
export let browserRefresh = false;
declare var sendLogToRollBar:any;
declare var getCurrentlyOpenNgxSmartModalIds: any;
declare var setNgxSmartModalOpenStateClosed: any;
declare var gtag: any;
declare var stopEventsBehindCal: any;
declare var startEventsBehindCal: any;
declare var isNJoySpecificParamInUrl: any;
declare var getStoreAppVersionName: any;
declare var getAppBuildNumber: any;
declare var getAppPlatform: any;
declare var setUPRollBar: any;
declare var setAppLoaded: any;
declare var changeFavicon:any;
declare var applyStyles:any;
declare var initializeHotJar: any;
declare var addingHeader:any;

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss'],
    standalone: false
})
export class AppComponent implements OnInit {
  private fetchAccountInfoSubscription: Subscription;
  bsModalRef: BsModalRef;
  bsModalHotelreview: BsModalRef;
  njoySpecificBuild: boolean
  navigatorData = this.getUserBrowserInfo();
  isMobile: boolean = false;
  isMobile1: boolean = false;
  connectionListener: Subscription;
  userInitListner: Subscription;
  userDetailsSubscription: Subscription;
  private currentUrl: string = '';
  private currentParams: any;
  public currDatePicker;
  public  userAccountInfoObj: UserAccountInfo;
  private isNavigating = false; // Add flag to prevent redirection loops
  constructor(
    private translate: TranslateService,
    private toastr: ToastrService,
    private loginService: LoginService,
    private adminPanelService: AdminPanelService,
    public userAccountService: UserAccountService,
    private deviceDetailsService: DeviceDetailsService,
    private modalService: BsModalService,
    public connectionService: ConnectionService,
    private ngxAnaltics:GoogleAnalyticsService,
    private gallopLocalStorage: GallopLocalStorageService,
    private route: ActivatedRoute, private router: Router,
    public ngxSmartModalService: NgxSmartModalService,
    public searchService: SearchService,
    private titleService: Title,
    public bookingService: BookingService,
    private userCredentials: UserCredentialsService,
    public el: ElementRef,
    public clientConfig: ClientConfiguration,
    public renderer: Renderer2) {
    renderer.listen('document', 'click', (event) => {
      let action = event.target.getAttribute('attr.data-track');
      let params1 = event.target.getAttribute('attr.data-params') ? ('&' + 'ua_' + event.target.getAttribute('attr.data-params')) : '';
      let params2 = event.target.getAttribute('data-params') ? ('&' + 'ua_' + event.target.getAttribute('data-params')) : '';
      if (params2) {
        let params = '?' + 'ua_action=' + action + params2;
        this.searchService.letsTrack(params);
      }
      else if (action) {
        let params = '?' + 'ua_action=' + action + params1;
        this.searchService.letsTrack(params);
      }
  
    //  this.connectionListener = this.connectionService.monitor().subscribe(isConnected => {
       
    //  });
     
    });
    localStorage.getItem('loginSession')
    this.njoySpecificBuild = this.userAccountService.isItNjoyBuild();
    if (isNJoySpecificParamInUrl()) {
      this.userAccountService.njoySpecificBuild = true;
      this.njoySpecificBuild = true;
    }
    environment.appStoreVersionName = getStoreAppVersionName();
    environment.appStoreBuildNumber = getAppBuildNumber();
    environment.platform = getAppPlatform();
    const navEndEvent$ = this.router.events.pipe(filter(e => e instanceof NavigationEnd));
  }
  
  
  
  setUpAppMode() {
    this.njoySpecificBuild = this.userAccountService.isItNjoyBuild();
    environment.appStoreVersionName = getStoreAppVersionName();
    environment.appStoreBuildNumber = getAppBuildNumber();
    environment.platform = getAppPlatform();
    const appMode = localStorage.getItem('is_app_mode');
    if (appMode){
      this.userAccountService.njoySpecificBuild = true;
      this.njoySpecificBuild = true;
      return;
    }
    if (isNJoySpecificParamInUrl()) {
      this.userAccountService.njoySpecificBuild = true;
      this.njoySpecificBuild = true;
      localStorage.setItem('is_app_mode','true');
    }
  }
  setHtmlBackground(): void {
    const htmlElement = document.querySelector('html');
    if (htmlElement && this.njoySpecificBuild) {
      this.renderer.setStyle(htmlElement, 'overscroll-behavior', 'none');
    }
  }

  ngOnInit() {
    let whitelLabelSetting = JSON.parse(localStorage.getItem('whiteLabelSetting'));
    if(whitelLabelSetting && whitelLabelSetting!=='null'){
      this.setWhiteLabelSetting(whitelLabelSetting)
    }else{
      CommonUtils.changeDeafultWhiteKabeling(this.searchService,this.userAccountService);
    }
    this.setUpAppMode();
    setAppLoaded();
    addingHeader();
  
    this.searchService.calendarOpenEventListsner.asObservable().subscribe(datePicker => {
      this.currDatePicker = datePicker;
      this.searchService.hoverCounter = 0;
      if (this.currDatePicker) {
        stopEventsBehindCal();
      } else {
        setTimeout(() => {
          startEventsBehindCal();
        }, 500);
      }
    })
    let timeNow = Date.now();
    this.connectionListener = this.connectionService.monitor().subscribe(isConnected => {
      let timeNowNoW = Date.now();
      timeNow = timeNowNoW;
    
    });
    let self = this;
    document.addEventListener("visibilitychange", function () {
      if (document.visibilityState === 'visible') {
        self.userAccountService.appVisibleToUser = true;
       
         
        
      } else {
        self.userAccountService.appVisibleToUser = false;
      }
    });
    if (this.userAccountService.isLoggedIn()) {
      this.inItClientRequest();
    }

    this.gallopLocalStorage.initAppSessionKey();
    this.userAccountService.userSessionChangePrompt$.subscribe((loginDetails: any) => {
      if (loginDetails && loginDetails['userid'] && loginDetails['sToken'] && loginDetails['userInfo']) {
        let userInfoObj: UserAccountInfo = deserialize(loginDetails['userInfo'], UserAccountInfo);
        if (!this.isLogoutPage()) {
          this.promptContinueLogin(loginDetails['userid'], loginDetails['sToken'], userInfoObj);
        }
      } else {
        // this.attemptLoginSessionUpdate(undefined,undefined,this.currentUrl);
      }
    });
    this.userAccountService.userAccountInitObjObserver$.subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.loginService.initResponse =resp;
        if ((resp && resp.status === 'success')
          && (
            resp.responseType === 'AllowedToProceed' ||
            resp.responseType === 'NotAllowedToProceed' ||
            resp.responseType === 'AllowedToProceedWithCTA' ||
            resp.responseType === 'NotAllowedToProceedWithCTA'
          )
        ) {
          let ctaTextString = undefined;
          let ctaString = undefined;
          //if(resp && resp.confermaStatus){

          // }
          if (resp.responseType !== 'AllowedToProceedWithCTA' ||
            resp.responseType !== 'NotAllowedToProceedWithCTA'
          ) {
            ctaTextString = resp.ctaText;
            ctaString = resp.cta;
          }

          this.bsModalRef = this.modalService.show(InitClientComponent, {
            initialState: {
              message: resp.errorMessage,
              responseType: resp.responseType,
              cta: ctaString,
              ctaText: ctaTextString
            }, backdrop: true, keyboard: false, ignoreBackdropClick: true
          });
        }
        if(resp.defaultConfig){
          this.searchService.mapSupprted = resp.defaultConfig.mapsSupported;
       
          this.searchService.groupTravelEventsSupported = resp.defaultConfig.groupTravelEventsSupported;
          this.searchService.showFlightTab = resp.defaultConfig.flightsEnabled;
          this.searchService.showCarTab = resp.defaultConfig.carsEnabled;
          this.searchService.showHotelTab = resp.defaultConfig.hotelsEnabled;
          this.searchService.logoIcon = resp.defaultConfig.logoIcon;
          this.searchService.searchPopMsg= resp.defaultConfig.searchLoaderText;
          this.searchService.whiteLabelSupported = resp.defaultConfig.whiteLabelSupported;
          if(this.searchService.whiteLabelSupported){
            this.callDynamicColorOfApp();
          }else{
            localStorage.setItem('whiteLabelSetting',null);
            CommonUtils.changeDeafultWhiteKabeling(this.searchService,this.userAccountService);
          }
          if(this.searchService.searchPopMsg && this.searchService.searchPopMsg.length > 0){
            this.searchService.currentMessage = this.searchService.searchPopMsg[0];
          }
          this.userAccountService.showComingSoonIntegrations=  resp.defaultConfig.showComingSoonIntegrations;
          this.searchService.perDiemSupported = resp.defaultConfig.perDiemSupported;
          this.searchService.chatEnabled = resp.defaultConfig.chatEnabled;
          this.searchService.showTrainsTab = resp.defaultConfig.trainsEnabled;
        }
        this.searchService.euroStarSupported = resp.euroStarSupported;
        this.searchService.allFaresPopupShow = resp.upSellFareSupported;
        this.userAccountService.showLanguageOptions = resp.showLanguageOptions;
        this.userAccountService.showAllBookings = resp.showAllBookings;
        this.userAccountService.showUpSellOnAllLegs = resp.showUpSellOnAllLegs;
        this.userAccountService.initResponseReceived = true;
        this.userAccountService.confermaStatus = resp.confermaStatus;
        if(resp.hotelChangesSupportedFor){
        this.searchService.hotelChangesSupportedFor = resp.hotelChangesSupportedFor;
        }
        if (resp.onBoarded) {
          this.userAccountService.onBoardingTask = resp.onBoarded;
        }
        if (resp.cardSupportEnabled) {
          this.userAccountService.userhascard = resp.cardSupportEnabled;
        }
        if (resp.hotelDetailForFeedback) {
          if (this.loginService.userid && this.loginService.sToken && !this.bsModalHotelreview) {
            this.bsModalHotelreview = this.modalService.show(FeedbackComponent, {
              initialState: {
                hotelName: resp.hotelDetailForFeedback.hotelName,
                checkin: resp.hotelDetailForFeedback.checkIn,
                checkout: resp.hotelDetailForFeedback.checkout,
                tripId: resp.hotelDetailForFeedback.tripId,
                transactionId: resp.hotelDetailForFeedback.transactionId,
                eventId: resp.hotelDetailForFeedback.eventId,
                ticketId: resp.hotelDetailForFeedback.ticketId,
              }, backdrop: true, keyboard: false, ignoreBackdropClick: true
            });
          }
        }
        this.userAccountService.isCarsEnabled = resp.carsEnabled;
        environment.locationSupportedInCarSearch = resp.locationSupportedInCarSearch;
        environment.presentAlternateInPolicyOptions = resp.presentAlternateInPolicyOptions;
        this.userAccountService.isDutyOfCareEnabled = resp.dutyOfCareEnabled;
        // Going forward we will use config service for configuration related things
        this.clientConfig.carsEnabled = resp.carsEnabled;
        this.clientConfig.locationSupportedInCarSearch = resp.locationSupportedInCarSearch;
        this.clientConfig.presentAlternateInPolicyOptions = resp.presentAlternateInPolicyOptions;
        this.clientConfig.dutyOfCareEnabled = resp.dutyOfCareEnabled;
        this.clientConfig.classicReports = resp.classicReports;
        this.clientConfig.bookingAppBaseUrl = resp.bookingAppBaseUrl;
        this.clientConfig.dashboardAppBaseUrl = resp.dashboardAppBaseUrl;
        this.clientConfig.enableReconcile = resp.enableReconcile;
        this.clientConfig.agentEmailId = resp.agentEmailId;
        this.clientConfig.brandName = resp.brandName;
        if (this.clientConfig.bookingAppBaseUrl && this.clientConfig.bookingAppBaseUrl.endsWith('/')) {
          this.clientConfig.bookingAppBaseUrl
            = this.clientConfig.bookingAppBaseUrl.substring(0, this.clientConfig.bookingAppBaseUrl.length - 1);
        }
        if (this.clientConfig.dashboardAppBaseUrl && this.clientConfig.dashboardAppBaseUrl.endsWith('/')) {
          this.clientConfig.dashboardAppBaseUrl
            = this.clientConfig.dashboardAppBaseUrl.substring(0, this.clientConfig.dashboardAppBaseUrl.length - 1);
        }
        const selectedLang = localStorage.getItem('selectedLanguage');
        if (selectedLang && this.userAccountService.showLanguageOptions) {
          this.translate.use(selectedLang);
          this.userCredentials.setLang(selectedLang);
        }
        this.userAccountService.getBookingListForMyTripsPage();
        if (this.userAccountService.getUserEmail()) {
          this.navigatorData['id'] = this.userAccountService.getUserEmail().replace('@', '_').replace('.', '_');
        }
        // sendLogToRollBar(JSON.stringify(this.navigatorData));
        if(this.userAccountService.isShowAllBookings()) {
          this.userAccountService.getBookingList();
        }

      }
    });
  
    //let emailid = this.gallopLocalStorage.getItem("userid");
    this.searchService.getMappingStars().subscribe(res =>{
      if(res && res.data){
       
        this.searchService.NGS_UI_STAR_MAPPING = res.data;
      }
    })
    //let stoken = this.gallopLocalStorage.getItem("sToken");
    this.router.events.subscribe((event: Event) => {
      // if(this.userAccountService.isLoggedIn()) return;
      if (event instanceof NavigationEnd) {
        this.currentUrl = event.url;
        let str = '/search'
        if (this.currentUrl.toLowerCase().search(str) !== -1) {
          this.searchService.isSearchPage = true;
        } else {
          this.searchService.isSearchPage = false;
        }
        this.userAccountService.updateCurrentRouterPath(event.url);
       
        this.currentParams = this.route.snapshot.queryParams;
        if(this.currentParams['eventid'] && this.currentParams['eventid']!=='exitEvent'){
          this.searchService.selectedEventID = this.currentParams['eventid'];
         }
        if (this.userAccountService.isLoggedIn()) {
          return};
        let userid = this.currentParams['userid'] ? this.currentParams['userid'] : this.loginService.userid;
        let sToken = this.currentParams['sToken'] ? this.currentParams['sToken'] : this.loginService.sToken;
        let uRole = this.loginService.uRole;
        if (!userid) userid = this.currentParams['emailId'];
        if (!this.isLogoutPage()) {
          this.attemptLoginSessionUpdate(userid, sToken, uRole,this.currentUrl);
        }
        if(this.isLoginOrLogoutPage()){
          this.userAccountService.showLanguageOptions =true;
        }
      }
      if (event instanceof NavigationStart) {
        browserRefresh = !this.router.navigated;
       
        if((event.url==='/hotelSelection' || event.url==='/emailflow?pageMode=WebSearch') && browserRefresh){
             this.goToHomePage();
        }
        if(event.url==='/emailflow?pageMode=WebSearch'){
          this.searchService.comingFromSelectionPAge = true;
        }
        if (event.navigationTrigger === 'popstate' && event.restoredState) {
          this.searchService.browserBackOrForwardClickListener.next(null);
        }

      }
    });
    this.userAccountService.userAccountInfoObjObserver$.subscribe((userProfile: UserAccountInfo) => {

      if (userProfile) {
       this.setHtmlBackground();
       this.getGeneralSetting();
        if (this.userAccountService.showRadio) {
          if(this.userAccountService.isConnected &&  this.currentUrl!=='/emailflow?pageMode=WebSearch'){
            this.searchService.fetchEmployeesList(this.userAccountService.getUserCompanyId());
          this.adminPanelService.fetchEmployeesList(this.userAccountService.getUserCompanyId());
            if (( this.userAccountService.isUserIsTravelManager()
                  || this.userAccountService.isUserCorporateAdmin()
                ) 
                && !this.bookingService.previousTransactionId) {
            this.searchService.employeeEmail = [];
            if(this.route.snapshot.queryParams['query']){
              this.searchService.bookingAndApprovalDone =true;
            }
          }
          }
        }
      }


    });
    window.addEventListener('storage', this.storageEventListener.bind(this));
    this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });
    this.deviceDetailsService.isMobile2().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });

    if (environment.production) {
      setUPRollBar('b96fe0a6424046608f6d9df2a95ad498', 'production');
    } else {
      setUPRollBar('0f2131c4446f4a81b9b2deb71a8a783c', 'dev');
    }
    if (!CommonUtils.doesPathContain(location.href, '/login')
        &&
        !CommonUtils.doesPathContain(location.href, '/admin')
      ) {
        initializeHotJar(environment.hj_site_id_booking);
    }
  }
  goToHomePage() {
    if((this.userAccountService.isUserCorporateAdmin() || this.userAccountService.isUserIsTravelManager())){
      this.searchService.employeeEmail=[];
      this.searchService.bookingAndApprovalDone =true;
    }
    if (this.userAccountService.getDefaultAppRoutePath() !== '/admin') {
      this.isNavigating = true;
      if(this.searchService.selectedEventID){
        this.router.navigate([this.userAccountService.getDefaultRoutePath()],{ queryParams: { userid: this.userAccountService.getUserEmail(), sToken: this.userAccountService.getSToken(),eventid:this.searchService.selectedEventID },
        replaceUrl: true }).then(() => {this.isNavigating = false;});
      }else{
      this.router.navigate([this.userAccountService.getDefaultRoutePath()],{ queryParams: { userid: this.userAccountService.getUserEmail(), sToken: this.userAccountService.getSToken() },
       replaceUrl: true }).then(() => {this.isNavigating = false;});
    }
  }
  }

  changeStyle() {
    if (this.airportModalOpen()) {
      return { 'min-height': '0px', 'padding-bottom': '0px' };
    } else {
      
        return { 'min-height': '100%', 'padding-bottom': '100px' };
      
    }
  }
  changeStyle1() {
    if (this.airportModalOpen()) {
      return { 'min-height': '0px' };
    } else {
        return { 'min-height': '100vh' };
        
    }
  }
  setWhiteLabelSetting(resp){
    if(resp && resp.status==='success'){
      if(resp.data && resp.data.backGroundColor){
       
        if (resp.data.backGroundColor){
          this.searchService.darkBgColor = resp.data.backGroundColor;
        }else{
          this.searchService.darkBgColor = '8A27F7';
        }
        if (resp.data.favIcon){
          this.searchService.favIcon = resp.data.favIcon;
        }else{
          this.searchService.favIcon = 'src/favicon.ico';
        }
        if (resp.data.primaryButtonColor){
          this.searchService.buttonbgcolor = resp.data.primaryButtonColor;
        }else{
          this.searchService.buttonbgcolor = '03efd8';
        }
        if (resp.data.primaryButtonTextColor){
          this.searchService.buttonfontcolor = resp.data.primaryButtonTextColor;
        }else{
          this.searchService.buttonfontcolor = '315EF6';
        }
        if (resp.data.secondaryButtonColor){
          this.searchService.secondaryButtonbgcolor = resp.data.secondaryButtonColor;
        }else{
          this.searchService.secondaryButtonbgcolor = 'F5F5FA';
        }
        if (resp.data.secondaryButtonTextColor){
          this.searchService.secondaryButtonfontcolor = resp.data.secondaryButtonTextColor;
        }else{
          this.searchService.secondaryButtonfontcolor = '808080';
        }
        if (resp.data.primaryButtonTextColor){
          this.searchService.buttonfontcolor = resp.data.primaryButtonTextColor;
        }else{
          this.searchService.buttonfontcolor = '315EF6';
        }
        if (resp.data.hyperLinkTextColor){
          this.searchService.hyperlinkcolor = resp.data.hyperLinkTextColor;
        }else{
          this.searchService.hyperlinkcolor = '0000FF';
        }
        if (resp.data.primaryBrandColor){
          this.searchService.lightBgColor =resp.data.primaryBrandColor;
          this.searchService.lightBgColor2 = resp.data.primaryBrandColor;
        }else{
          this.searchService.lightBgColor = 'EDFAFC';
        }
         if (resp.data.companyLogo){
          this.searchService.logo = resp.data.companyLogo;
        }else{
          this.searchService.logo = 'assets/images/logo.png';
        }
        if (resp.data.footerLogo){
          this.searchService.footerLogo = resp.data.footerLogo;
        }else{
          this.searchService.footerLogo = 'assets/images/footer-logo.png';
        }
        if (resp.data.companyLogoForDashboard){
          this.searchService.logo2 = resp.data.companyLogoForDashboard;
        }else{
          this.searchService.logo2 = 'assets/images/logo.webp';
        }
     
       applyStyles(resp);
      }else{
        
       CommonUtils.changeDeafultWhiteKabeling(this.searchService,this.userAccountService);
        
      }
    }
  }
 
  callDynamicColorOfApp(){
    this.userAccountService.getWhiteLableColor().subscribe(resp => {
      if(resp && resp.status==='success'){
        if(resp.data && resp.data.backGroundColor){
      localStorage.setItem('whiteLabelSetting',JSON.stringify(resp));
        }else{
          localStorage.setItem('whiteLabelSetting',null);
        }
      }
      this.setWhiteLabelSetting(resp);
    })
  }
  getGeneralSetting(){
    this.searchService.getGeneralSetting().subscribe(resp =>{
      if(resp && resp.status==='success'){
        if(resp.data && resp.data.language){
          this.useLanguage(resp.data.language);
        }
       
      }
    })
  }
  useLanguage(language: string) {
    this.translate.use(language);
    this.userCredentials.setLang(language);
    const oldLang: string = localStorage.getItem('selectedLanguage');
    localStorage.setItem('selectedLanguage', language);
    if (oldLang) {
      localStorage.setItem('oldLanguage', oldLang);
    }
    GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics, 'languageChanged', 'WebSearchUI');
  }
  
  isLoginPage() {
    return this.userAccountService.getCurrentRouterPath().indexOf('login') === 1;
  }
  isSlackconnectRequested() {
   
    return this.userAccountService.getCurrentRouterPath().indexOf('addToSlack') === 1;
  }
  isLoginOrLogoutPage() {
    return this.userAccountService.getCurrentRouterPath().indexOf('login') === 1
      || this.userAccountService.getCurrentRouterPath().indexOf('logout') === 1;
  }
  isLogoutPage() {
    return this.userAccountService.getCurrentRouterPath().indexOf('logout') === 1;
  }
  isDashboardPage() {
    return this.userAccountService.getCurrentRouterPath().indexOf('admin') === 1;
  }

  private promptContinueLogin(newUserId: string, newSToken: string, userInfoObj: UserAccountInfo) {
    let currentSessionUser: string = undefined;
    let currentSessionUserRole: string = undefined;
    if (localStorage.getItem('loginSession') !== 'null' && localStorage.getItem('loginSession')) {
      let prevLoginSession = JSON.parse(localStorage.getItem('loginSession'));
      currentSessionUser = prevLoginSession['userid'];
      currentSessionUserRole = prevLoginSession['uRole'];
    }
    if (currentSessionUser && currentSessionUser !== newUserId) {
      if (this.bsModalRef
        && this.bsModalRef.content
        && this.bsModalRef.content.title
        && this.bsModalRef.content.title ===  this.translate.instant('search.Areyousureyouwanttoproceed?')) {
        return;
      }

      this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
        initialState: {
          title: this.translate.instant('search.Areyousureyouwanttoproceed?'),
          message:  this.translate.instant('search.Youwillbesignedoutof')+ currentSessionUser +  this.translate.instant('search.andsignedinto') + newUserId +  this.translate.instant('search.account'),
          yesButtonSubText:  this.translate.instant('search.Yes'),
          noButtonSubText:  this.translate.instant('search.No'),
          color: 'black',
          login:true,
          buttonColor: this.searchService.hyperlinkcolor
        }, backdrop: true, keyboard: false, ignoreBackdropClick: true
      });
      this.bsModalRef.content.onClose.subscribe(result => {
        this.bsModalRef = null;
        if (result) {
          this.userAccountService.updateUserSession(newUserId, newSToken, currentSessionUserRole,userInfoObj);
          window.location.reload()
        } else {
          //this.titleService.setTitle('Flight Search');
          this.titleService.setTitle(this.translate.instant('search.FlightSearch'));
          // this.router.navigate(['/search'], { queryParams: {} });
          this.router.navigate([this.loginService.getDefaultPath(currentSessionUserRole)], { queryParams: {} });
        }
      });
    } else {
      this.userAccountInfoObj = userInfoObj;
      this.userAccountService.updateUserSession(newUserId, newSToken, currentSessionUserRole,userInfoObj);
      if (this.isLoginOrLogoutPage()) {
        if (this.loginService.currentUrl && this.loginService.currentUrl.trim().length > 0) {
          this.router.navigateByUrl(this.loginService.currentUrl);
        } else {
          //this.titleService.setTitle('Flight Search');
          this.titleService.setTitle(this.translate.instant('search.FlightSearch'));
          this.router.navigate([this.loginService.getDefaultPath(currentSessionUserRole)], { queryParams: {} });
        }
      }
    }
  }

  isuserHascard(): boolean {
    return this.userAccountService.userhascard;
  }
   getUserBrowserInfo() {
    return {
        userAgent: navigator.userAgent,
        language: navigator.language,
        languages: navigator.languages,
        platform: navigator.platform,
        cookiesEnabled: navigator.cookieEnabled,
        onlineStatus: navigator.onLine,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        screenResolution: `${window.screen.width}`+'X'+`${window.screen.height}`,
        colorDepth: window.screen.colorDepth,
        pixelRatio: window.devicePixelRatio,
        serviceWorker: 'serviceWorker' in navigator,
        localStorage: !!window.localStorage,
        sessionStorage: !!window.sessionStorage,
        indexedDB: !!window.indexedDB,
    };
}
  private attemptLoginSessionUpdate(userid: string, sToken: string, uRole:string,currentUrl: string) {
    let currentSessionUser: string = undefined;
    let currentSessionSToken: string = undefined;
    let currentSessionUserRole: string = uRole;
    if (!CommonUtils.doesPathContain(currentUrl, 'emailAction') && !CommonUtils.doesPathContain(currentUrl, 'signup')) {
      if (localStorage.getItem('loginSession') !== 'null' && localStorage.getItem('loginSession')) {
        let prevLoginSession = JSON.parse(localStorage.getItem('loginSession'));
        currentSessionUser = prevLoginSession['userid'];
        currentSessionSToken = prevLoginSession['sToken'];
        currentSessionUserRole = prevLoginSession['uRole'];
        this.loginService.userid = prevLoginSession['userid'];
        this.loginService.sToken = prevLoginSession['sToken'];
        this.loginService.uRole = prevLoginSession['uRole'];
      }
      if (userid && sToken) {
        const dataConsentRequired = localStorage.getItem('dataConsentRequired');
      if (dataConsentRequired === 'true') {
        localStorage.setItem('dataConsentAlreadyAccepted', 'true');
      }
        if (this.isSlackconnectRequested()) {
          window.location.href = (environment.urlForSlackConnect + '?userid=' + userid);
        }
        this.userAccountService.initUserAccountService(userid, sToken, uRole,CommonUtils.doesPathContain(currentUrl, 'emailflow'));
        this.inItClientRequest();
        if(this.router.url && this.router.url.split("?")[0] === this.userAccountService.getDefaultAppRoutePath() && !this.isNavigating) this.goToHomePage();
        // this.titleService.setTitle(this.translate.instant('search.FlightSearch'));
        // this.router.navigateByUrl(this.userAccountService.getDefaultRoutePath(uRole));        
        
      } else if (!this.userAccountService.isLoggedIn() && currentSessionUser && currentSessionSToken) {
        if (this.isSlackconnectRequested()) {
          window.location.href = (environment.urlForSlackConnect + '?userid=' + currentSessionUser);
        }
        this.userAccountService.initUserAccountService(currentSessionUser, currentSessionSToken, currentSessionUserRole,CommonUtils.doesPathContain(currentUrl, 'emailflow'));
        this.inItClientRequest();
        if(this.router.url && this.router.url.split("?")[0] === this.userAccountService.getDefaultAppRoutePath() && !this.isNavigating) this.goToHomePage();
        // this.titleService.setTitle(this.translate.instant('search.FlightSearch'));
        // this.router.navigateByUrl(this.userAccountService.getDefaultRoutePath(currentSessionUserRole));        
      } else if (!this.userAccountService.isLoggedIn()
        && !this.isLoginOrLogoutPage() && !CommonUtils.doesPathContain(currentUrl, 'emailAction')
        && !CommonUtils.doesPathContain(currentUrl, 'signup')) {
        if (!this.userAccountService.isLoginInProgress) {
          this.loginService.currentUrl = currentUrl;
          this.loginService.userid = undefined;
          this.loginService.sToken = undefined;
          this.loginService.uRole = undefined;
          if (CommonUtils.doesPathContain(currentUrl,"bookingHistory")  && this.route.snapshot.queryParams['source'] && this.route.snapshot.queryParams['source'] == 'duplicateBookingEmail') {
            this.toastr.error("Sorry! You don't have relevant permission to view this itinerary");
          }
          this.router.navigateByUrl('/login');
          this.titleService.setTitle(this.translate.instant('login.Login'));
        }
      }
    } else if (CommonUtils.doesPathContain(currentUrl, 'emailAction')) {
      //this.titleService.setTitle('Reset Password');
      this.titleService.setTitle(this.translate.instant('search.ResetPassword'));
    }
  }
  private storageEventListener(storeData: any) {
    if (storeData && storeData.key === 'loginSession') {
      let newLoginSession = undefined;
      let prevLoginSession = undefined;
      if (storeData.newValue && storeData.newValue != null) {
        newLoginSession = JSON.parse(storeData.newValue);
      }
      if (storeData.oldValue && storeData.oldValue != null) {
        prevLoginSession = JSON.parse(storeData.oldValue);
      }

      if (prevLoginSession && (!newLoginSession || newLoginSession['userid'] !== prevLoginSession['userid'])) {
        if (this.bsModalRef
          && this.bsModalRef.content
          && this.bsModalRef.content.title
          && this.bsModalRef.content.title ===  this.translate.instant('search.SignedOut')) {
          return;
        }
        this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
          initialState: {
            title:this.translate.instant('search.SignedOut'),
            message: this.translate.instant('search.Youhavebeensignedoutof') + this.userAccountService.getUserEmail()
            + ' ' + this.translate.instant('search.account.'),
            yesButtonSubText: this.translate.instant('search.OK'),
            color: 'black',
            buttonColor: this.searchService.hyperlinkcolor,
            disableCancel: true,

          }, backdrop: true, keyboard: false, ignoreBackdropClick: true
        });
        this.bsModalRef.content.onClose.subscribe(result => {
          this.bsModalRef = null;
          if (result) {
            this.userAccountService.clearLoggedInData();
            this.attemptLoginSessionUpdate(undefined, undefined, undefined,this.currentUrl);
            //this.titleService.setTitle('Login');
            this.titleService.setTitle(this.translate.instant('login.Login'));
            window.location.href = (this.clientConfig.bookingAppBaseUrl + '/logout');
          }
        });
      } else if (newLoginSession && newLoginSession['userid']) {
        this.attemptLoginSessionUpdate(undefined, undefined, undefined,this.currentUrl);
      }
    }
  }
  private inItClientRequest() {
    let userid = this.userAccountService.getUserEmail();
    let sToken = this.userAccountService.getSToken();
    //if(this.userAccountService.isLoggedIn()){
    this.userAccountService.initializeApiClient();

    //}
  }

  initLogin() {

  }
  ngOnDestroy() {
    this.gallopLocalStorage.clear();
  }
  airportModalOpen() {
    if (this.ngxSmartModalService.getOpenedModals() &&
      (this.ngxSmartModalService.getOpenedModals().length > 0 && this.isMobile)
    ) {
      let modals = this.ngxSmartModalService.getOpenedModals();
      if (modals[0].id.search('search') !== -1 || modals[0].id.search('dest') !== -1 || modals[0].id.search('mobile') !== -1) {
        return true;
      } else {
        return false;
      }
    }
  }
  public handleClickEventForImplementingModalHack() {
    if (this.ngxSmartModalService.getOpenedModals() &&
      this.ngxSmartModalService.getOpenedModals().length > 0
    ) {
      let modals = this.ngxSmartModalService.getOpenedModals();
      for (let index = 0; index < modals.length; index++) {
        var currModalTimeStamps = getCurrentlyOpenNgxSmartModalIds();
        let helpModalAlreadyOpen = false;
        if (currModalTimeStamps[modals[index].id] &&
          new Date().getTime() - currModalTimeStamps[modals[index].id] > 500
        ) {
          if (modals[index].id !== 'helpModal') {
            this.ngxSmartModalService.getModal(modals[index].id).close();
            this.searchService.inputId = '';
            setNgxSmartModalOpenStateClosed(modals[index].id);
          } else {
            if ($('.modalAirportFilterInfo').css('display') !== 'none') {
              $('.modalAirportFilterInfo').slideToggle();
              setNgxSmartModalOpenStateClosed(modals[index].id);
            }
          }
        }
      }
    }
    return true;
  }
  public getWindowHeight() {
    return window.innerHeight + 'px';
  }
  public setTitle(title: string) {
    this.titleService.setTitle(title);
  }
  public closeCalendar() {
    if (this.currDatePicker) {
      setTimeout(() => {
        startEventsBehindCal();
      }, 500);
      this.currDatePicker.hide();
      this.currDatePicker = null;
      setTimeout(() => {
        startEventsBehindCal();
      }, 500);
    }
    return;
  }
}
