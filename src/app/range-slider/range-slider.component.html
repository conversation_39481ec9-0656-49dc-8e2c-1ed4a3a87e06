<h4>{{getHeadingText()}}</h4>
<p *ngIf="filterForDuration">{{getMinDuration()}}  - {{getMaxDuration()}}</p>
<p *ngIf="filterForPrice">{{getMinPrice()}}  - {{getMaxPrice()}}</p>

<p *ngIf="filterForDuration == false && !filterForPrice">{{getMinValueDate() | date:'MMM d,'}} {{getMinValueDate() | date:'h:mma'}} - {{getHighValueDate() | date:'MMM d,'}}
    {{getHighValueDate() | date:'h:mma'}}</p>

    <ngx-slider [(value)]="minValue" [(highValue)]="maxValue" [options]="options" (userChange)="valueChanged()"></ngx-slider>