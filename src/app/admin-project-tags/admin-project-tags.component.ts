import { Component, OnInit, ElementRef } from '@angular/core';
import { UserAccountService } from '../user-account.service';
import { AdminPanelService } from '../admin-panel.service';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { UserAccountInfo } from '../entity/user-account-info';
import { Subscription } from 'rxjs';
import { SlideInOutAnimation } from '../animations';

@Component({
    selector: 'app-admin-project-tags',
    templateUrl: './admin-project-tags.component.html',
    styleUrls: ['./admin-project-tags.component.scss'],
    animations: [SlideInOutAnimation],
    standalone: false
})
export class AdminProjectTagsComponent implements OnInit {

  constructor( public userAccountInfoService: UserAccountService,
    public adminPanelService: AdminPanelService,
    private el: ElementRef,
    private gallopLocalStorage: GallopLocalStorageService,
    public translateService: TranslateService, private toastr: ToastrService, private modalService: BsModalService,) { }
    showLoader = false;
    showLoader1 = false;
    settingSaveProcessing = false;
    responseNotCame=false;
    updatedTag = '';
    alltags = [];
    companyName : '';
    userAccountInfoObj: UserAccountInfo;
  fetchAccountInfoSubscription: Subscription;
    tagShow :Array<any>[];
    newtag = '';
    deleteTag = false;
    selectedTagInndex = -1;
    selectTagSetIndex=-1
    bsModalRef: BsModalRef;
    tagSaveButton =[];
  ngOnInit(): void {
    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      this.userAccountInfoObj = userAccountInfoObj;
     this.responseNotCame =true;
     // this.getAllTags();
     if(this.userAccountInfoObj){
      this.getAllTagSet();
     }

    });

  }
  ngOnDestroy() {
    if(this.fetchAccountInfoSubscription){
      this.fetchAccountInfoSubscription.unsubscribe();
    }
   }
   OriginaltagSet=[];
   tagSet=[];
   tagShowForTagSet=[]
   showAddTagInput(index){
     for(let i=0;i<this.tagSet.length ;i++){
      this.tagSet[i]['showInputButton']=false;
     }
     this.newtag='';
this.tagSet[index]['showInputButton']=true;
this.getDisableButton('',index)
   }
   expandOnboardingPopup(index) {
   
    for (let i = 0; i < this.tagSet.length; i++) {
      this.tagShowForTagSet[i]=true;
      for (let j = 0; j < this.tagShow[i].length; j++) {
        this.tagShow[i][j] = true;
      }
    }
    let myTag = this.el.nativeElement.querySelectorAll(".tagPopOpen"+index)
    if (!myTag[0].classList.contains('removeTransform')) {
      myTag[0].classList.add('removeTransform');
      myTag[0].firstChild.classList.add('removeTransform');
    } else {
      myTag[0].firstChild.classList.remove('removeTransform');
      myTag[0].classList.remove('removeTransform');
    }
    $('.opendiv'+index).slideToggle();
  }
  updatedTagSet='';

  selectCardIndex=-1;
  tagsetKeys: Array<any>[];
   getAllTagSet(item?){
    this.OriginaltagSet=[];
    this.updatedTag='';
    this.adminPanelService.originalalltags = [];
    // this.showLoader =true;
    const companyid = this.userAccountInfoService.getUserCompanyId();
    this.adminPanelService.getTagSet(companyid).subscribe(resp => {
      if (resp.status === 'success') {
        this.showLoader = false;
        this.responseNotCame =false;
       
        if (resp.data && resp.data.tags && resp.data.tags.length > 0) {
          for (let item of resp.data.tags) {
            // this.a&& lltags.push(item);
           
            this.adminPanelService.originalalltags.push(item);
           // this.tagShow.push(true);
          

          }

        
         
          this.alltags = this.adminPanelService.originalalltags;
        }
        if (resp.data && resp.data.tagsets && resp.data.tagsets.length > 0) {
        for (let item of resp.data.tagsets) {
           item['showInputButton']=false;  
           item['showInputTagsets']=true;  
           item['showInputloader']=false;  
          
          this.OriginaltagSet.push(item);
          this.tagSaveButton.push(true);
         // this.tagShowForTagSet.push(true);
        }
        
        for(let i=0;i<this.OriginaltagSet.length;i++){
          this.tagShowForTagSet[i]=true;
        }
        this.tagSet = this.OriginaltagSet;
        this.tagsetKeys = new Array(this.tagSet.length).fill(null).map(_ => [])
        this.tagShow =new Array(this.tagSet.length).fill(null).map(_ => []);
          for(let i=0;i<this.tagSet.length;i++){
            let arrayVAlue = this.tagSet[i];
            for(let item1 of this.alltags){
              if(arrayVAlue.tagsetId===item1.tagsetId){
              this.tagsetKeys[i].push(item1);
              this.tagShow[i].push(true);
              }
            }
            if(!item){
            this.tagsetKeys[i] = this.sortList(this.tagsetKeys[i]);
            }
          }
        
        
        
      }
      if(this.selectCardIndex !== -1){
        setTimeout(() => {
          this.expandOnboardingPopup(this.selectCardIndex);
          this.selectCardIndex =-1;
        }, 200);
      
     
      }
      } else {
        this.showLoader = false;
        this.responseNotCame =false;
        this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntreportdata.Pleasetryagainlater"));
      }
    }, error => {
      if (error.status != 403) {
        this.showLoader = false;
        this.responseNotCame =false;
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
   }
   rowIndex=0;
   sortList(data) {
    data.sort(function (a, b) {
      if (a.tag_name.toLowerCase() < b.tag_name.toLowerCase()) { return -1; }
      if (a.tag_name.toLowerCase() > b.tag_name.toLowerCase()) { return 1; }
      return 0;
    })
    return data;
  }
   omit_special_char(event,item) {
    const allowedKeys = ['Enter', 'Tab']; // Allow Enter and Tab
    const inputElement = event.target as HTMLInputElement;
//
    if (allowedKeys.indexOf(event.key) !== -1 || /^[a-zA-Z0-9-_ ]*$/.test(event.key)) {
      // Allow the key event
      if(item==='tagset'){
      if(this.updatedTagSet && this.updatedTagSet.length>0){
        this.updatedTagSet = this.updatedTagSet.split('.').join("");
      }
    }
         if(item==='tag'){
          if(this.updatedTag && this.updatedTag.length>0){
            this.updatedTag = this.updatedTag.split('.').join("");
          }
        }
       
    } else {
      event.preventDefault(); // Prevent the default behavior
    }

  }
  onModelCancel() {
    this.bsModalRef.hide();
  }
  getDisableButton(item,j) {
    if (item && item.trim().length > 0) {
      this.tagSaveButton[j] = false;
      this.newtag = item;
    } else {
      this.tagSaveButton[j] = true;
    }
  }
  
  addTag(tagID,index,index2) {

    if (this.newtag !== '') {
      this.tagSaveButton[index] = true;
      this.showLoader = true;
      if(this.newtag && this.newtag.length>0){
        this.newtag = this.newtag.split('.').join("");
      }
      this.tagSet[index]['showInputloader']=true;
      const companyid = this.userAccountInfoService.getUserCompanyId();
      this.adminPanelService.createTagSet(tagID, this.newtag).subscribe(resp => {
        if (resp && resp.status === 'success') {
          this.toastr.success( this.translateService.instant("setting.TripTagaddedsuccessfully"));
          this.newtag = '';
          this.selectCardIndex=index;
          if(resp.data && resp.data.tag_name){
            resp.data.visible=true;
            this.tagsetKeys[index].push(resp.data);
            this.tagShow[index].push(true);
            this.showLoader=false;
            this.tagSet[index]['showInputButton']=false;  
           
            this.tagSet[index]['showInputTagsets']=true;  
            this.tagSet[index]['showInputloader']=false;  
            this.adminPanelService.UpdateTag(companyid,resp.data).subscribe(resp => {
              if (resp && resp.status === 'success') {
                //this.getAllTagSet(true);
               
               // this.tagSet[index]['showInputloader']=false;
               
              }
            }, error => {
              if (error.status != 403) {
                setTimeout(() => {
                  let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
                  this.toastr.error(resultErrorMessage);
                }, 100);
              }
            })
          }
          // this.tagSaveButton =false;
        } else {
          this.toastr.error(resp.errorMessage[0]);
          this.settingSaveProcessing = false;
          this.selectCardIndex=index;
          this.tagSet[index]['showInputloader']=false;
          this.showLoader = false;
          //this.tagSaveButton =false;
        }
      }, error => {
        if (error.status != 403) {
          this.settingSaveProcessing = false;
          this.showLoader = false;
          // this.tagSaveButton =false;
          setTimeout(() => {
            let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
            this.toastr.error(resultErrorMessage);
          }, 100);
        }
      })
    }

  }
  updateTagSetFunction(tagId,i){
    if (this.tagSet[i].tagSetName !== this.updatedTagSet) {
      //this.settingSaveProcessing = true;
      if(this.updatedTagSet && this.updatedTagSet.length>0){
        this.updatedTagSet = this.updatedTagSet.split('.').join("");
      }
      this.tagSet[i].tagSetName = this.updatedTagSet;
      this.showLoader1 = true;
      this.adminPanelService.updateCreateTagSetNAme(tagId, this.updatedTagSet, this.tagSet[i].mandatory).subscribe(resp => {
        if (resp && resp.status === 'success') {
          this.toastr.success(this.translateService.instant("setting.TagSetUpdatedsuccessfully"));
          // this.getAllTags();
          this.tagSet[i]['showInputTagsets'] = true;
          this.tagSet[i].tagSetName = this.updatedTagSet;
          this.showLoader1 = false;
        } else {
          this.showLoader1 = false;
          this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntreportdata.Pleasetryagainlater"));
        }
      }, error => {
        if (error.status != 403) {
          this.showLoader1 = false;
          setTimeout(() => {
            let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
            this.toastr.error(resultErrorMessage);
          }, 100);
        }
      })
    }
  }
  enableTag = false;
  disableTag=false;
  updateVisibilityOfTag(i,index){
    if(this.showLoader1){
      return;
    }
    this.updatedTag='';
    this.tagsetKeys[index][i].visible = !this.tagsetKeys[index][i].visible;
    if(this.tagsetKeys[index][i].visible){
      this.enableTag = true;
    }else{
      this.disableTag =true;
    }
    this.updateTagApi(i,index);
  }
  updateTagApi(i,index){
    const companyid = this.userAccountInfoService.getUserCompanyId();
    this.showLoader1 = true;
    this.adminPanelService.UpdateTag(companyid, this.tagsetKeys[index][i]).subscribe(resp => {
      if (resp && resp.status === 'success') {
        if(!this.enableTag && !this.disableTag){
        this.toastr.success(this.translateService.instant("setting.TripTagUpdatedsuccessfully"));
        }else if(this.enableTag){
          this.toastr.success(this.translateService.instant("setting.TripTagenabled")); 
        }
        else if(this.disableTag){
          this.toastr.warning(this.translateService.instant("setting.TripTagdisabled")); 
        }
        // this.getAllTags();
        this.enableTag = false;
  this.disableTag=false;
        this.tagSet[index]['showInputloader']=false;
        this.tagShow[index][i] = true;
        if(this.updatedTag!==''){
          this.tagsetKeys[index][i].tag_name = this.updatedTag;
        }
        this.showLoader1 = false;
      } else {
        this.showLoader1 = false;
        this.tagSet[index]['showInputloader']=false;
        this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntreportdata.Pleasetryagainlater"));
      }
    }, error => {
      if (error.status != 403) {
        this.showLoader1 = false;
        this.tagSet[index]['showInputloader']=false;
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
  }
  addTag1(i,index) {
    
    let originaltagname = JSON.parse(JSON.stringify(this.adminPanelService.originalalltags[i].tag_name))
    if ( this.tagsetKeys[index][i].tag_name !== this.updatedTag) {
      //this.settingSaveProcessing = true;
     // this.selectCardIndex = index;
     if(this.updatedTag && this.updatedTag.length>0){
      this.updatedTag = this.updatedTag.split('.').join("");
    }
     this.tagsetKeys[index][i].tag_name = this.updatedTag;
      this.tagSet[index]['showInputloader']=true;
     this.updateTagApi(i,index);
    }

  }
  cancelTagSet(k) {
    this.tagSet[k]['showInputTagsets']=true;
    this.updatedTagSet=''
  }
  url = "assets/images/check.png";
  enableAllTags(index){
    for(let tgs of this.tagsetKeys[index]){
      tgs.visible =true;
      
    }
    this.enableTag =true;
    this.disableTag =false;
    this.updateAlltags(index);
  }
  updateAlltags(index){
    const companyid = this.userAccountInfoService.getUserCompanyId();
    this.showLoader1 = true;
    this.adminPanelService.UpdateAllTag(companyid, this.tagsetKeys[index]).subscribe(resp => {
      if (resp && resp.status === 'success') {
        if(this.enableTag){
          this.toastr.success(this.translateService.instant("setting.TripTagenabled")); 
        }
        else if(this.disableTag){
          this.toastr.warning(this.translateService.instant("setting.TripTagdisabled")); 
        }
        // this.getAllTags();
        this.enableTag = false;
  this.disableTag=false;
        this.tagSet[index]['showInputloader']=false;
       
        this.showLoader1 = false;
      } else {
        this.showLoader1 = false;
        this.tagSet[index]['showInputloader']=false;
        this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntreportdata.Pleasetryagainlater"));
      }
    }, error => {
      if (error.status != 403) {
        this.showLoader1 = false;
        this.tagSet[index]['showInputloader']=false;
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
  }
  disableAllTags(index){
    for(let tgs of this.tagsetKeys[index]){
      tgs.visible =false;
      
    }
    this.enableTag =false;
    this.disableTag =true;
    this.updateAlltags(index);
  }
  showenaableButton(item){
    let allenabled=false;
    if(item && item.length===0){
      return false;
    }
    for(let tgs of item){
      if(!tgs.visible){
        allenabled =true;
        break;
      }
    }
return allenabled;
  }
  showdisableButton(item){
    let allenabled=true;
    if(item && item.length===0){
      return false;
    }
    for(let tgs of item){
      if(!tgs.visible){
        allenabled =false;
        break;
      }
    }
return allenabled;
  }
  getUrl(item) {
    if(item){
       
    this.url = "assets/images/check.png";
    }else{
         
       this.url = "assets/images/check-mark.png";
            }
            return this.url;
          
        }
        getTagSetDisableButton(event,j){
          this.updatedTagSet = event;
        }
  onEditTagSet(index){
      for(let i=0;i<this.tagSet.length;i++){
        this.tagSet[i]['showInputTagsets']=true; 
      }
    this.tagSet[index]['showInputTagsets']=false;
    this.updatedTagSet = this.tagSet[index].tagSetName  }
  onEditTag(k,j) {
    for (let i = 0; i < this.tagShow.length; i++) {
      for (let m = 0; m < this.tagShow[i].length; m++) {
      this.tagShow[i][m] = true;
      }
    }
    for (let i = 0; i < this.tagSet.length; i++) {
      this.tagShowForTagSet[i]=true;
    }
    this.tagShowForTagSet[j]=false;
    this.tagShow[j][k] = false;
   
    this.updatedTag =  this.tagsetKeys[j][k].tag_name;
  }
  cancelAddTag(i){
    this.tagSet[i]['showInputButton'] =false;
  }
  cancelTag1(k,j) {
    for (let i = 0; i < this.tagShow.length; i++) {
      for (let m = 0; m < this.tagShow[i].length; m++) {
      this.tagShow[i][m] = true;
      }
    }
    for (let i = 0; i < this.tagSet.length; i++) {
      this.tagShowForTagSet[i]=true;
    }
    this.tagShowForTagSet[j]=true;
    this.tagShow[j][k] = true;
    this.updatedTag = '';
  }
  confirmDelete(Modal, i,j) {
    this.selectedTagInndex = i;
    this.selectTagSetIndex=j;
    if (this.bsModalRef) this.bsModalRef.hide();
    this.bsModalRef = this.modalService.show(Modal, {
      initialState: {
      }, backdrop: true, keyboard: false, ignoreBackdropClick: false
    });
  }
  onDeletetag(i) {
    // this.settingSaveProcessing = true;
    this.deleteTag = true;
    let selectedTag =  this.tagsetKeys[this.selectTagSetIndex][this.selectedTagInndex];
    this.tagsetKeys[this.selectTagSetIndex].splice(this.selectedTagInndex, 1);
    this.tagShow[this.selectTagSetIndex].splice(this.selectedTagInndex, 1);
    selectedTag.status = 'archived';
    const companyid = this.userAccountInfoService.getUserCompanyId();
    this.adminPanelService.UpdateTag(companyid, selectedTag).subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.toastr.success(this.translateService.instant("setting.Triptagdeletedsuccessfully"));
        this.deleteTag = false;
        if (this.bsModalRef) {
          this.bsModalRef.hide();
        }
        //this.getAllTags();
      } else {
        // this.showLoader =false;
        this.deleteTag = false;
        if (this.bsModalRef) {
          this.bsModalRef.hide();
        }
        this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntreportdata.Pleasetryagainlater"));
      }
    }, error => {
      if (error.status != 403) {
        // this.showLoader =false;
        this.deleteTag = false;
        if (this.bsModalRef) {
          this.bsModalRef.hide();
        }
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
  }
  triptags_is_mandatory(tagId,i,is_mandatory){
    this.tagSet[i].mandatory = is_mandatory;
    const tagsetName = this.tagSet[i].tagSetName;
    this.adminPanelService.updateISMandatoryOrNot(tagId,tagsetName,is_mandatory).subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.toastr.success(this.translateService.instant("setting.TagSetUpdatedsuccessfully"));
        this.tagSet[i].mandatory = is_mandatory;
      } else {
        this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntreportdata.Pleasetryagainlater"));
      }
    }, error => {
      if (error.status != 403) {
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
   
  }
}
