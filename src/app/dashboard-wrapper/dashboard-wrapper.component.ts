import { Component, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON>roy, EventEmitter, Output, ChangeDetectorRef, ElementRef } from '@angular/core';
import { BsModalService, BsModalRef, ModalDirective } from 'ngx-bootstrap/modal';
import { AbstractControl, FormArray, UntypedFormBuilder, UntypedFormGroup, Validators, FormControl, Form, ValidationErrors, ValidatorFn } from '@angular/forms';
import { Constants } from '../util/constants';
import { countries } from '../util/countries';
import { AddEmployeeComponent } from '../add-employee/add-employee.component';
import { AdminPanelService, CompanyDomain, CompanySettings } from '../admin-panel.service';
import { Subscription } from 'rxjs';
import { UserAccountService } from '../user-account.service';
import { Title } from '@angular/platform-browser';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { UserAccountInfo } from '../entity/user-account-info';
import { environment } from 'src/environments/environment';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { LoginService } from '../login.service';
import { ClientConfiguration } from '../client-config.service';
import { CommonUtils } from '../util/common-utils';
import { DeleteCardModelComponent } from '../email-booking-flow/delete-card-model/delete-card-model.component';
import { DeviceDetailsService } from '../device-details.service';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { DateUtils } from '../util/date-utils';
import { SearchService } from '../search.service';
import { TranslateService } from '@ngx-translate/core';
declare var hideBorderLine: any;
declare var hideArrow: any;
declare var openCardEdit123: any;
declare var initializeHotJar: any;
@Component({
    selector: 'app-dashboard-wrapper',
    templateUrl: './dashboard-wrapper.component.html',
    styleUrls: ['./dashboard-wrapper.component.scss'],
    standalone: false
})
export class DashboardWrapperComponent implements OnInit {
  viewMode1 = 'tab1';
  userName: string = 'User';
  subTab = 'Transactions';
  @Output() getImageUrl = new EventEmitter();
  @Output() goBackToAdmin = new EventEmitter();
  logo = '';
  viewMode11 = 'tab1';
  subType = 'pending';
  subType1 = 'today';
  selectTask = '';
  subType2 = 'Transactions';
  previousDate = '';
  currentParams: any;
  routeSpringUpdateList:any;
  bsModalRef: BsModalRef;
  userDetailsSubscription: Subscription;
  userInItSubscription: Subscription;
  onBoardingTask1 = [];
  approvalReport = false;
  showOnboardingPopup =false;
  emailForm: UntypedFormGroup;
  popData:any;
  showBadge=false;
  maxDoBDate: Date = new Date();
  queryParmsSubscription: Subscription;
  checkEmployeeDetailResponseSubscription: Subscription;
  companySettingsSubscription: Subscription;
  responseErrorSubscription: Subscription;
  localDataSubscription: Subscription;
  isLoading: boolean = true;
  detailView: boolean;
  showApprovalComponet = false;
  deviceSubscription: Subscription;
  deviceSubscription1: Subscription;
  companyApprovalSubscription: Subscription;
  reportData: any;
  isMobile: boolean;
  isMobile1: boolean;
  firstDate: Date;
  lastDate: Date = new Date(new Date().toISOString().split('T')[0]);
  wdrdata: any;
  popupmsg = '';
  domainOptions = [];
  selectedOption = [];
  companySettings: CompanySettings;
  waitingForCompanySettings: boolean;
  constructor(private modalService: BsModalService,
    private activatedRoute: ActivatedRoute,
    private el: ElementRef,
    private searchService: SearchService,
    private cdRef: ChangeDetectorRef,
    private adminPanelService: AdminPanelService,
    public translateService: TranslateService,
    private loginService: LoginService,
    public ngxSmartModalService: NgxSmartModalService,
    private gallopLocalStorage: GallopLocalStorageService,
    private userAccountService: UserAccountService,
    private fb: UntypedFormBuilder,
    public clientConfig: ClientConfiguration,
    public router: Router,
    public deviceDetailsService: DeviceDetailsService,
    private titleService: Title
  ) {
    this.detailView = false;
    this.waitingForCompanySettings = true;
    this.userName = this.userAccountService.getUserFirstName();
    this.loadScripts();
  }
  handleMsg(event){
    this.showOnboardingPopup =event;
    this.popData=undefined;
  }
  isEventSupporting(){
    return this.searchService.groupTravelEventsSupported;
  }
  ngOnInit() {
    this.waitingForCompanySettings = true;
    this.lastDate = new Date();
    var curryear = this.lastDate.getFullYear();
    var currmonth = this.lastDate.getMonth();
    this.firstDate = new Date(curryear, currmonth, 1);
    //  this.titleService.setTitle('Corporatashboard');
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['type'] == 'report') {
        if (params['subType']) {
          this.subType2 = params['subType'];
          if (this.subType2 === 'By Traveler') {
            this.titleService.setTitle(this.translateService.instant('report.ComplianceByTraveler'));
          } else if (this.subType2 === 'By Department') {
            this.titleService.setTitle(this.translateService.instant('report.ComplianceByDepartment'));
          } else {
            this.titleService.setTitle(this.subType2 + ' ' + this.translateService.instant('report.Report'));
          }
          this.routeToReport('report', this.subType2, true);
        }else{
          this.routeToReport('report', 'Transactions', true);
        }
      } else if (params['type'] == 'employees') {
        this.titleService.setTitle(this.translateService.instant('dashboardWrapper.People'));
        if (params['subType']==='activeTravelers' && params['subSubType']) {
          this.routeToEmployee('employees',params['subType'], true,params['subSubType']);
        }else if (params['subType']==='activeTravelers' && !params['subSubType']) {
          this.routeToEmployee('employees',params['subType'], true,'today');
        }else if (params['subType']) {
        this.routeToEmployee('employees',params['subType'], true);
        }
      } else if (params['type'] == 'dashboard') {
        this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Dashboard'));
        this.routeToDashboard('dashboard', true);
      } else if(params['type'] =='GroupTravels'){

        this.titleService.setTitle(this.translateService.instant('setting.GroupTravels'));
        this.routeToEvents(params['type'], true);

      }
        else if (params['type'] == 'policies') {
        this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Policies'));
        this.routeToPolicy('policies', true);
      } else if (params['type'] == 'setting') {
        this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Settings'));
        if (params['subType']==='integrations'&& params['index']) {
          this.routeToSetting('setting','integrations',params['subType2'],params['product'],true,params['index']);
          }else if (params['subType']==='integrations' && params['subType2']) {
            this.routeToSetting('setting','integrations', params['subType2'],params['product'],true);
            }else{
          this.routeToSetting('setting', params['subType'],null,null,true);
          }
       
      } else if (params['type'] == 'managecards') {
        this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ManageCards'));
        if (params['subType']) {
          this.routeToFluidPay('managecards', true, params['subType']);
        } else {
          this.routeToFluidPay('managecards', true);
        }
      } else if (params['type'] == 'CustomFields') {
        this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ProjectTags'));
        this.routeToProjectTags('CustomFields', true);
      }else if (params['type'] == 'paymentMethods') {
        this.titleService.setTitle(this.translateService.instant('dashboardWrapper.PaymentMethods'));
        this.routeToPaymentMethods('paymentMethods', true);
      }else if (params['type'] == 'integrations') {
        if (params['index']) {
        this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Integerations'));
        this.routeToIntegerations('integrations', params['subType'],params['product'],true,params['index']);
        }else{
          this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Integerations'));
        this.routeToIntegerations('integrations', params['subType'],params['product'],true);
        }
      }
      else if (params['type'] == 'messages') {
        this.titleService.setTitle(this.translateService.instant('activeTraveler.Messages'));
        this.routeToMessages('messages', true);
      }
       else if (params['type'] == 'reconcile') {
        this.routeToReconcile('reconcile');
      } else {
        if (params['subType']) {
          this.subType = params['subType'];
          this.titleService.setTitle(this.translateService.instant('dashboardWrapper.PendingApprovals'));
          this.routeToApprovals('approvals', this.subType, true);
        }
      }
    });
    this.currentParams = this.activatedRoute.snapshot.queryParams;
    //this.detailView = false;
    this.subscribeEvents();
  }

  ngOnDestroy() {
    if (this.userAccountService.isUserCorporateAdmin()) {
      this.unSubscribeEvents()
    }
    if(this.userInItSubscription){
      this.userInItSubscription.unsubscribe();
    }
    if (this.userDetailsSubscription) {
      this.userDetailsSubscription.unsubscribe();
    }
    if (this.companyApprovalSubscription) {
      this.companyApprovalSubscription.unsubscribe();
    }
    if (this.companySettingsSubscription) {
      this.companySettingsSubscription.unsubscribe();
    }
    this.gallopLocalStorage.removeItem("from");
    this.adminPanelService.originalTravellerResponse = undefined;
    this.adminPanelService.waitingForCompanySettings = false;
    this.adminPanelService.wdrdata = [];
  }
  loadScripts() {
    const dynamicScripts = [
      "assets/webdatarocks/webdatarocks.toolbar.min.js",
      "assets/webdatarocks/webdatarocks.js",
      "assets/js/plaid.js",
      "assets/js/wdr-reports.js",
      "assets/js/wdr-reconcile.js"
    ];
    for (let i = 0; i < dynamicScripts.length; i++) {
      const node = document.createElement('script');
      node.src = dynamicScripts[i];
      node.type = 'text/javascript';
      node.async = false;
      node.charset = 'utf-8';
      document.getElementsByTagName('head')[0].appendChild(node);
    }
  }
  getAllTags() {
    const companyid = this.userAccountService.getUserCompanyId();
    this.adminPanelService.all_tags = [];
    this.adminPanelService.getTagSet(companyid).subscribe(resp => {
      if (resp.status === 'success') {
        if (resp.data && resp.data.tags && resp.data.tags.length > 0) {
          for (let item of resp.data.tags) {
            this.adminPanelService.all_tags.push(item);

          }
          for (let item of this.adminPanelService.wdrdata) {
            if (this.adminPanelService.all_tags.length > 0) {
              if (item.Title === 'CustomFields') {
                item.showOnReportComponent = true;
              }
            }
          }
        }
      } else {
        //this.toastr.error("Apologies! something went wrong, we could'nt report data. Please try again later");
      }
    }, error => {
      if (error.status != 403) {
        setTimeout(() => {
          // let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          // this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
  }
  subscribeEvents() {
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });
    this.deviceSubscription1 = this.deviceDetailsService.isMobile1().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
    this.userInItSubscription = this.userAccountService.userAccountInitObjObserver$.subscribe(resp => {
      if (resp && resp.status === 'success') {
        if(resp.defaultConfig){
          this.searchService.mapSupprted = resp.defaultConfig.mapsSupported;
          this.searchService.tripFeatureEnabled = resp.defaultConfig.tripFeatureEnabled;
          this.searchService.infantBookingAllowed = resp.defaultConfig.minorBookingSupported;
        }
      }
    });
    this.userDetailsSubscription = this.userAccountService.userAccountInfoObjObserver$.subscribe((userAccountInfo: UserAccountInfo) => {
      if (!userAccountInfo || userAccountInfo == null) return;
      this.userName = this.userAccountService.getUserFirstName();
      this.isLoading = false;
      this.getAllTags();    //  this.OnBoardTaskCompleted();
      var data = require('src/assets/raw1Data.json');
      if (data.success) {
        this.reportData = data.data;
        //this.clientConfig= data.classicReport;
        for (let item of this.reportData) {
          for (let item1 of item.Report) {
            if (this.deviceDetailsService.isMobile2Width) {
              if (item1.type !== 'flat') {
                item1.type = 'compact';
              }
            }
            // if(item1.id!=='Transactions' && item1.id!=='Travel Credits'){
            //  if(this.clientConfig){
            // item1.show = !this.clientConfig.classicReports;
            //  }
            // }
            if(item1.Title==='Approvals History'){
              item1.Title2='report.ApprovalsHistory';
              item1.Title1='report.ApprovalsHistory';
            }
            if(item1.Title==='Transactions'){
              item1.Title2='report.Transactions';
              item1.Title1='report.Transactions';
            }
             if(item1.Title==='Travel Credits'){
              item1.Title2='report.TravelCredits';
              item1.Title1='report.TravelCredits';
            }
            if(item1.Title==='Flights'){
              item1.Title2='report.Flights';
              item1.Title1='report.Flights';
            }
            if(item1.Title==='Hotels'){
              item1.Title2='report.Hotels';
              item1.Title1='report.Hotels';
            }
            if(item1.Title==='Cars'){
              item1.Title2='report.Cars';
              item1.Title1='report.Cars';
            }
            if(item1.Title==='Travelers'){
              item1.Title2='report.Travelers';
              item1.Title1='report.Travelers';
            }

            if(item1.Title==='Departments'){
              item1.Title2='report.Departments';
              item1.Title1='report.Departments';
            }
            if(item1.Title==='CustomFields'){
              item1.Title2='report.ProjectTags';
              item1.Title1='report.ProjectTags';
            }
           
            if(item1.Title1==='By Department'){
              item1.Title2='report.Compliance';
              item1.Title1='report.ByDepartment';
            }
            if(item1.Title1==='By Traveler'){
              item1.Title2='report.Compliance';
              item1.Title1='report.ByTraveler';
            }
            if (this.adminPanelService.all_tags.length > 0) {
              if (item.Title === 'CustomFields') {
                item.showOnReportComponent = true;
              }
            }
            if (this.adminPanelService.wdrdata.find((test) => test.id === item.id) === undefined) {
              this.adminPanelService.wdrdata.push(item1);
            }
          }
        }

        this.adminPanelService.wdrdata = this.filterDuplicate();
      }
      if (!this.userAccountService.isUserCorporateAdmin()) {
        let userid = this.userAccountService.getUserEmail();
        this.titleService.setTitle(this.translateService.instant('search.FlightSearch'));
        let sToken = this.userAccountService.getSToken();
        if (userid === this.currentParams['userid'] && sToken === this.currentParams['sToken']) {
          window.location.href = (this.clientConfig.bookingAppBaseUrl + '/' + this.userAccountService.getDefaultRoutePath()+ "?userid=" + userid + "&sToken=" + sToken);
          //this.router.navigate(['/search'],{ queryParams: { }, replaceUrl: true });
          this.titleService.setTitle(this.translateService.instant('search.FlightSearch'));
        }
      } else {
        // this.onBoardingTask1 = [...this.userAccountService.onBoardingTask];
        if (this.userAccountService.initResponseReceived) {
          this.afteRefreshin();
          this.OnBoardTaskCompleted();
        }
        if (!this.userAccountService.userIsDeptAdmin) {
          this.userAccountService.onBoardTaskCompleted = true;
          this.userAccountService.hideSuccesMsg = true;
          this.userAccountService.showOnBoardingTask = false;
        }
        let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(this.firstDate);
        let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(this.lastDate);
        let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
        let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
        let searchDates = startDate + 'T' + endDate;

        this.approvalReport = false;
        this.adminPanelService.fetchCompanyApprovals(this.userAccountService.getUserCompanyId(), startDate, endDate, 'all');
        this.previousDate = startDate + 'T' + endDate;
        //   this.titleService.setTitle('Corporate Dashboard');
        this.adminPanelService.processCompanySettingsRequest(this.userAccountService.getUserCompanyId());
        this.adminPanelService.fetchEmployeesList(this.userAccountService.getUserCompanyId());
      }
    });
    if (this.isMobile) {
      this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
        initialState: {
          title: 'dashboardWrapper.Forbetterexperiencepleaseuselargerscreendevicessuchaslaptopordesktop',
          // message: This payment method will not be displayed in your list of payment options',
          message: '',
          dashboard: true,
        }, backdrop: true, keyboard: false, ignoreBackdropClick: true
      });
    }
    this.userAccountService.userAccountInitObjObserver$.subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.userAccountService.initResponseReceived = true;
        this.adminPanelService.giftCardsEnabled = resp.giftCardsEnabled;
        this.adminPanelService.perDiemSupported = resp.defaultConfig.perDiemSupported;
       // if (this.userAccountService.userIsDeptAdmin) {
       //   this.afteRefreshin();
         // this.OnBoardTaskCompleted()
      //  }
      }
    });
    this.adminPanelService.getRoutespringList().subscribe(resp=>{
      if(resp && resp.status==='success'){
        
        if(resp.data.showBadge){
          this.showBadge = resp.data.showBadge;
        }
        if(resp.data && resp.data.updatesItemList.length>0){
          this.routeSpringUpdateList = resp.data.updatesItemList;
        }
       
      }
    })
    this.responseErrorSubscription = this.adminPanelService.responseErrorSubject$.subscribe(error => {
      if (error) {
        // window.alert(error);
      }
    });
    //this.checkEmployeeDetailResponseSubscription = this.adminPanelService.employeeCheckDetailResponseObservable$.subscribe(response =>{
    //    if(response){
    // this.showAddEmployeeModal();
    //   }
    // });
    this.adminPanelService.getOnboardingList().subscribe(resp =>{
      if(resp && resp.status==='success'){
        if(resp.data && resp.data.length > 0){
     
      this.popData = resp.data;
      let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
      if(findINdex >-1){
        this.showOnboardingPopup =true;
        initializeHotJar(environment.hj_site_id_dashboard);
      }else{
        this.showOnboardingPopup =false;
      }
        }
        
      }
    })
    this.companyApprovalSubscription = this.adminPanelService.companyApprovalResponseObservable$.subscribe((reportResponse) => {
      if (reportResponse && reportResponse.pendingApprovals && reportResponse.pendingApprovals.length > 0) {
        this.approvalReport = true;
      } else {
        this.approvalReport = false;
      }
    });

    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {
        if (!this.userAccountService.userIsDeptAdmin) {
          this.userAccountService.onBoardTaskCompleted = true;
          this.userAccountService.hideSuccesMsg = true;
          this.userAccountService.showOnBoardingTask = false;
        }
        // this.afteRefreshin();
        this.domainOptions = [];
        this.companySettings = settings;
        if(this.companySettings.companyFeatures){
        
        this.userAccountService.infantBookingAllowed = this.companySettings.companyFeatures.minorBookingSupported;
        }
        if (this.companySettings.company.logo) {
          this.logo = this.companySettings.company.logo;
        }
        let apiDomainList: Array<CompanyDomain> = this.adminPanelService.getDomains();
        if (apiDomainList && apiDomainList.length > 0) {
          this.selectedOption.push({ value: apiDomainList[0].domain });
          for (let domainItem of apiDomainList) {
            this.domainOptions.push({ value: domainItem.domain, id: '' + domainItem.domainId });
          }
        }
        this.waitingForCompanySettings = false;
        this.adminPanelService.waitingForCompanySettings = true;
      }
    });
    this.initializeQueryParamsfromRoute();
  }
  openRouteSpringMenuModal(modal) {
    if (this.userAccountService.initResponseReceived) {
     if(!this.isMobile1){
      this.ngxSmartModalService.getModal(modal).open();
     }else{
      
      $('.routespringModal1').slideToggle();
     }
      this.clickOnRoutespringUpdate();

    }
  }
  clickOnRoutespringUpdate(){
    let ids=[];
    for(let item of this.routeSpringUpdateList){
      ids.push(item.updateItemId)
    }
    this.adminPanelService.getRoutespringUpdateList(ids).subscribe(resp =>{
      if(resp && resp.status==='success'){
      //  
        this.showBadge =false;
      }
    })
  }
  closeRouteSpringMenuModal(){
    $('.routespringModal1').slideToggle();
  }
  updateItems(item,modal){
    this.adminPanelService.getRoutespringUpdateItemList(item.updateItemId).subscribe(resp => {
      if (resp && resp.status === 'success') {
       
        
        
      }
    })
    if (item.type === 'external') {
      let url;
      if (item.passLoginToken) {
        let userid = this.userAccountService.getUserEmail();
        let sToken = this.userAccountService.getSToken();
        let str = item.target;
       
        if(str.indexOf( "?" ) ===-1){
        url = (item.target + "?userid=" + userid + "&sToken=" + sToken);
        
        }else{
         url = (item.target + "&userid=" + userid + "&sToken=" + sToken);
       //   window.open(item.target, "_blank");
        }
      }
      if(url){
     window.open(url , "_blank");
      }else{
        window.open( item.target, "_blank");
      }
    } else if (item.type === 'internal') {
      let userid = this.userAccountService.getUserEmail();
      let sToken = this.userAccountService.getSToken();
      if ((CommonUtils.doesPathContain(item.target, 'admin'))) {
        let path = item.target.split('?')[1];
        path = path.split('=')[1];
        this.router.navigateByUrl(item.target);
       
      } else if ((CommonUtils.doesPathContain(item.target, 'search'))) {
        window.location.href = (this.clientConfig.bookingAppBaseUrl + '/search' + "?userid=" + userid + "&sToken=" + sToken);
      }
      else if ((CommonUtils.doesPathContain(item.target, 'profile'))) {
        window.location.href = (this.clientConfig.bookingAppBaseUrl + '/profile' + "?userid=" + userid + "&sToken=" + sToken);
      }
      if(this.isMobile1){
        $('.routespringModal1').slideToggle();
      }
      if(this.ngxSmartModalService){
      this.ngxSmartModalService.close(modal)
      }
    }
  }
  initializeQueryParamsfromRoute(): void {
    this.queryParmsSubscription = this.activatedRoute.queryParamMap.subscribe((queryParams: Params) => {
      if (queryParams && queryParams.params && Object.keys(queryParams.params).length > 0) {
        if (queryParams.params.view && queryParams.params.view === 'detail') {
          this.detailView = true;
          this.viewMode1 = 'tab2';
        } else if (queryParams.params.view && queryParams.params.view === 'approval') {
          if (queryParams.params.bookingType && (queryParams.params.bookingType === 'upcoming' || queryParams.params.bookingType === 'pending')) {
            this.subTab = 'pending'
            this.titleService.setTitle(this.translateService.instant('dashboardWrapper.PendingApprovals'));
            this.viewMode1 = 'tab3';
          } else {
            this.subTab = 'history'
            this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ApprovalsHistory'));
            this.viewMode1 = 'tab3';
          }
          this.showApprovalComponet = false;
        } else {
          this.detailView = false;
          this.showApprovalComponet = false;
        }
      } else {
        this.detailView = false;
        this.showApprovalComponet = false;
      }

    });
  }
  filterDuplicate() {
    let wdrdata = this.adminPanelService.wdrdata.filter((test, index, array) =>
      index === array.findIndex((findTest) =>
        findTest.id === test.id
      ))
    return wdrdata;
  }
  selectTaskOnMobile(item) {
    if (this.userAccountService.onBoardingTask.indexOf(item) === -1) {
      this.selectTask = item;
    } else {
      this.selectTask = '';
    }
  }
  unSubscribeEvents() {
    if(this.checkEmployeeDetailResponseSubscription){
    this.checkEmployeeDetailResponseSubscription.unsubscribe();
    }
    if(this.responseErrorSubscription){
    this.responseErrorSubscription.unsubscribe();
    }
  }

  onModelCancel() {
    this.bsModalRef.hide();
  }

  showAddEmployeeModal() {
    this.bsModalRef.hide();
    this.bsModalRef = this.modalService.show(AddEmployeeComponent, {
      initialState: {
        mode: 'add'
      }, backdrop: true, keyboard: false, ignoreBackdropClick: true
    });
  }
  handleModalEvents(eventName: string, currentModalId: string) {
    CommonUtils.handleModalEvents(this.ngxSmartModalService, eventName, currentModalId);
  }
  onSmartModelCancel(modelName: string) {
    this.ngxSmartModalService.getModal(modelName).close();
  }
  showAddManagerModal() {
    this.bsModalRef.hide();
    this.bsModalRef = this.modalService.show(AddEmployeeComponent, {
      initialState: {
        mode: 'addManager'
      }, backdrop: true, keyboard: false, ignoreBackdropClick: true
    });
  }
  showLookUpModal(modal) {
    this.emailForm = this.fb.group({
      emailUserName: ['', Validators.compose([Validators.required])],
      domain: ['', Validators.compose([Validators.required])],
      email: ['', Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_EMAIL)])],
    });
    this.bsModalRef = this.modalService.show(modal);
  }




  openMenuModal(modal) {
    if (this.userAccountService.initResponseReceived) {

      this.ngxSmartModalService.getModal(modal).open()

    }
  }
  hideBorder() {
    return hideBorderLine();
  }
  hideArrow() {
    return hideArrow();
  }
  isOnBoardedHasEmployee() {
    return (this.userAccountService.onBoardingTask.indexOf('employees') !== -1)
  }
  isOnBoardedHasBooking() {
    return (this.userAccountService.onBoardingTask.indexOf('booking') !== -1)
  }
  isOnBoardedHasSetting() {
    return (this.userAccountService.onBoardingTask.indexOf('billing') !== -1)
  }
  isOnBoardedHasProfile() {
    return (this.userAccountService.onBoardingTask.indexOf('profile') !== -1)
  }
  isOnBoardedHasPolicy() {
    return (this.userAccountService.onBoardingTask.indexOf('policy') !== -1)
  }
  emailUserNameChange(userName) {
    let domain = this.emailForm.controls['domain'].value;
    this.emailForm.controls['email'].setValue(userName.trim() + '@' + domain);
  }
  emailDomainChange(domain) {
    let userName = this.emailForm.controls['emailUserName'].value;
    this.emailForm.controls['email'].setValue(userName.trim() + '@' + domain.value);
  }
  checkEmployeeDetail() {
    if (this.emailForm.valid) {
      this.adminPanelService.processCheckEmployeeDetail(this.emailForm.controls['email'].value,
        this.userAccountService.getUserCompanyId(), 'addEmployee');
    } else {
      this.emailForm.controls['emailUserName'].markAsTouched();
      this.emailForm.controls['email'].markAsTouched();
      this.emailForm.controls['emailUserName'].updateValueAndValidity();
      this.emailForm.controls['email'].updateValueAndValidity();
    }

  }

  routeToApprovals(type, type11, item) {
    if(this.popData && this.popData.length >0){
    let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
      if(findINdex >-1){
        this.showOnboardingPopup =true;
      }else{
        this.showOnboardingPopup =false;
      }
    }
    this.viewMode1 = 'tab3';
    this.closeNgxModal();
    if (!item && this.userAccountService.userIsDeptAdmin) {
      this.OnBoardTaskCompleted();
    }
    this.selectTask = '';
    if (type11 === 'pending') {
      this.subTab = 'pending'
      this.titleService.setTitle(this.translateService.instant('dashboardWrapper.PendingApprovals'));
    } else {
      this.subTab = 'history';
      this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ApprovalsHistory'));
    }
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
          subType: type11
        },
        replaceUrl: false
      }
    );
  }
  routeToActiveTravellers(type, item) {
    if(this.popData && this.popData.length >0){
    let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
      if(findINdex >-1){
        this.showOnboardingPopup =true;
      }else{
        this.showOnboardingPopup =false;
      }
    }
    this.adminPanelService.gotoDetail = false;
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ActiveTravelers'));
    this.viewMode1 = 'tab9';
    this.closeNgxModal();
    if (!item && this.userAccountService.userIsDeptAdmin) {
      this.OnBoardTaskCompleted();
    }
    this.subTab = 'active';
    this.selectTask = '';
    this.adminPanelService.subTabForBookingDetail = this.subTab;
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
          subType: this.subType1
        },
        replaceUrl: false
      }
    );
  }
  routeToReport(type, type2, item) {
    if(this.popData && this.popData.length >0){
      let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
        if(findINdex >-1){
          this.showOnboardingPopup =true;
        }else{
          this.showOnboardingPopup =false;
        }
      }
    this.adminPanelService.gotoDetail = false;
    if (type2 === 'By Traveler') {
      this.titleService.setTitle(this.translateService.instant('report.ComplianceByTraveler'));
    } else if (type2 === 'By Department') {
      this.titleService.setTitle(this.translateService.instant('report.ComplianceByDepartment'));
    } else {
      if(type2==='Flights'){
        this.titleService.setTitle(this.translateService.instant('report.Flights') + ' ' + this.translateService.instant('report.Report'));
        }
      if(type2==='Cars'){
      this.titleService.setTitle(this.translateService.instant('report.Cars') + ' ' + this.translateService.instant('report.Report'));
    }
    if(type2==='Hotels'){
    this.titleService.setTitle(this.translateService.instant('report.Hotels') + ' ' + this.translateService.instant('report.Report'));
  }
  if(type2==='Travelers'){
    this.titleService.setTitle(this.translateService.instant('report.Travelers') + ' ' + this.translateService.instant('report.Report'));
  }
  if(type2==='Departments'){
    this.titleService.setTitle(this.translateService.instant('report.Departments') + ' ' + this.translateService.instant('report.Report'));
  }
  if(type2==='CustomFields'){
    this.titleService.setTitle(this.translateService.instant('report.ProjectTags') + ' ' + this.translateService.instant('report.Report'));
  }
  if(type2==='Transactions'){
    this.titleService.setTitle(this.translateService.instant('report.Transactions') + ' ' + this.translateService.instant('report.Report'));
  }
  if(type2==='Travel Credit'){
    this.titleService.setTitle(this.translateService.instant('report.TravelCredit') + ' ' + this.translateService.instant('report.Report'));
  }
    }

    this.viewMode1 = 'tab2';
    if (!item && this.userAccountService.userIsDeptAdmin) {
      this.OnBoardTaskCompleted();
    }
    this.subTab = type2;
    this.selectTask = '';
    this.closeNgxModal();
    this.adminPanelService.subTabForBookingDetail = this.subTab;
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
          subType: type2
        },
        replaceUrl: false
      }
    );
  }
  routeToMessages(type, item) {
    if(this.popData && this.popData.length >0){
    let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
      if(findINdex >-1){
        this.showOnboardingPopup =true;
      }else{
        this.showOnboardingPopup =false;
      }
    }
    this.adminPanelService.gotoDetail = false;
    this.titleService.setTitle(this.translateService.instant('activeTraveler.Messages'));
    this.viewMode1 = 'tab7';
    if (!item && this.userAccountService.userIsDeptAdmin) {
      this.OnBoardTaskCompleted();
    }
    this.subTab = 'message';
    if (this.onBoardingTask1.length > 0) {
      let item1 = this.onBoardingTask1[this.onBoardingTask1.length - 1];
      this.ChangeStyle(item1);
      this.userAccountService.onBoardingTask = [...this.onBoardingTask1];
    }
    this.selectTask = '';
    this.closeNgxModal();
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
        },
        replaceUrl: false
      }
    );
  }
  clickOnGettingStarted() {
    window.open('https://routespring.com/help-center/guide-for-admins/', '_blank');
  }
  routeToDashboard(type, item) {
    if(this.popData && this.popData.length >0){
    let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
      if(findINdex >-1){
        this.showOnboardingPopup =true;
      }else{
        this.showOnboardingPopup =false;
      }
    }
    this.adminPanelService.gotoDetail = false;
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Dashboard'));
    this.viewMode1 = 'tab1';
    if (!item) {
      this.OnBoardTaskCompleted();
    }
    this.subTab = '';
    this.selectTask = '';
    this.closeNgxModal();
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
        },
        replaceUrl: false
      }
    );
  }
  ChangeStyle(item) {
    let obj1 = '';
    if (item === 'policy') {
      obj1 = 'pol1';
    } else if (item === 'policy') {
      obj1 = 'pol1';
    } else if (item === 'profile') {
      obj1 = 'pro1';
    } else if (item === 'billing') {
      obj1 = 'set1';
    } else if (item === 'booking') {
      obj1 = 'sea1';
    } else if (item === 'employees') {
      obj1 = 'emp1';
    }
    let obj12 = document.getElementById(obj1);
    setTimeout(() => {
      openCardEdit123(obj12);
    }, 100);
  }
  handleAddEmployeeChange(data) {
    if (data.show) {
      if (!this.isOnBoardedHasEmployee()) {
        let params = "?ua_action=OnboardingTasks&ua_item=employees";
        this.searchService.letsTrack(params);
        if (!this.isMobile1) {
          setTimeout(() => {
            this.onDesktop('employees');
            // this.userAccountService.onBoardingTask= [...this.onBoardingTask1]; 
            this.onBoardingTask1 = [...this.userAccountService.onBoardingTask];
            this.cdRef.detectChanges();
          }, 4000);
          let obj12 = document.getElementById('emp1');
          setTimeout(() => {
            if (this.userAccountService.onBoardingTask.length < 7) {
              if (data.newadmin) {
                this.popupmsg = 'dashboardWrapper.WaytogoYourcolleaguehasbeenadded';
              } else {
                this.popupmsg = 'dashboardWrapper.WaytogoYourcolleaguesarealreadyadded';
              }
              this.isParticularTask('employees');
            }
            openCardEdit123(obj12);
          }, 4100);
        } else {
          this.onMobile('employees');
          this.selectTask = '';
          setTimeout(() => {
            if (this.userAccountService.onBoardingTask.length < 7) {
              if (data.newadmin) {
                this.popupmsg = 'dashboardWrapper.WaytogoYourcolleaguehasbeenadded';
              } else {
                this.popupmsg ='dashboardWrapper.WaytogoYourcolleaguesarealreadyadded';
              }
              this.isParticularTask('employees');
            }
          }, 4100);
        }
      }
      setTimeout(() => {
        this.isOnBoardTaskCompleted();
      }, 5000);
    }
  }
  closepopup() {
    this.userAccountService.hideSuccesMsg = true;
  }
  routeToEmployee(type,subType,item,subType1?) {
    if(this.popData && this.popData.length >0){
    let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
      if(findINdex >-1){
        this.showOnboardingPopup =true;
      }else{
        this.showOnboardingPopup =false;
      }
    }
    this.adminPanelService.gotoDetail = false;
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Travellers'));
    if (!item) {
      this.OnBoardTaskCompleted();
    }
    this.viewMode1 = 'tab4';
    this.subTab = '';
    if (this.isMobile1) {
      this.selectTask = '';
    }
    // this.cdRef.detectChanges();
    this.closeNgxModal();
    if(subType1){
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
          subType:subType,
          subSubType:subType1
        },
        replaceUrl: false
      }
    ); 
    }else{
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
          subType:subType
        },
        replaceUrl: false
      }
    );
    }
  }
  closeNgxModal() {
    if (this.ngxSmartModalService.getOpenedModals() &&
      (this.ngxSmartModalService.getOpenedModals().length > 0)
    ) {
      let modals = this.ngxSmartModalService.getOpenedModals();
      for (let index = 0; index < modals.length; index++) {
        if (modals[index].id === 'mobileModal') {
          this.ngxSmartModalService.close('mobileModal');
        }
      }
    }
  }
  popShown() {
    if (this.userAccountService.onParticularTaskCompleted) {
      return true;
    } else if (this.userAccountService.onBoardTaskCompleted) {
      return true
    } else {
      return false;
    }
  }
  handlePolicyUrl(event) {
    this.OnBoardTaskCompleted();
    let params = "?ua_action=OnboardingTasks&ua_item=policy";
    this.searchService.letsTrack(params);
    if (!this.isMobile1) {

      setTimeout(() => {
        this.onDesktop('policy')
        // this.userAccountService.onBoardingTask= [...this.onBoardingTask1]; 
        this.onBoardingTask1 = [...this.userAccountService.onBoardingTask];
        this.cdRef.detectChanges();
      }, 4000);
      let obj12 = document.getElementById('pol1');
      setTimeout(() => {
        openCardEdit123(obj12);
        if (this.userAccountService.onBoardingTask.length < 7) {
          this.popupmsg = 'dashboardWrapper.GreatYouhavereviewedyourpolicy';
          this.isParticularTask('policy');
        }
      }, 4100);
    } else {
      this.onMobile('policy');
      this.selectTask = '';
      setTimeout(() => {
        if (this.userAccountService.onBoardingTask.length < 7) {
          this.popupmsg ='dashboardWrapper.GreatYouhavereviewedyourpolicy';
          this.isParticularTask('policy');
        }
      }, 4100);
    }
  }
  routeToProjectTags(type,item){
    if(this.popData && this.popData.length >0){
      let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
        if(findINdex >-1){
          this.showOnboardingPopup =true;
        }else{
          this.showOnboardingPopup =false;
        }
      }
      this.viewMode1 = 'tab12';
      this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ProjectTags'));
      if (!item) {
        this.closeNgxModal();
      }
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
        },
        replaceUrl: false
      }
    );
  }
  routeToPaymentMethods(type,item){
    if(this.popData && this.popData.length >0){
      let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
        if(findINdex >-1){
          this.showOnboardingPopup =true;
        }else{
          this.showOnboardingPopup =false;
        }
      }
      this.viewMode1 = 'tab13';
      this.titleService.setTitle(this.translateService.instant('dashboardWrapper.PaymentMethods'));
      if (!item) {
        this.closeNgxModal();
      }
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
        },
        replaceUrl: false
      }
    );
  }
  routeToIntegerations(type,subType,product,item,i?){
    if(this.popData && this.popData.length >0){
      let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
        if(findINdex >-1){
          this.showOnboardingPopup =true;
        }else{
          this.showOnboardingPopup =false;
        }
      }
      this.viewMode1 = 'tab14';
      this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Integerations'));
      if (!item) {
        this.closeNgxModal();
      }
      if(subType && i){
        this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: type,
            subType: subType,
            product:product,
            index:i
          },
          replaceUrl: false
        }
      );
      }else  if(subType){
        this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: type,
            subType: subType,
            product:product,
          },
          replaceUrl: false
        }
      );
      }else{
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
        },
        replaceUrl: false
      }
    );
      }
  }
  routeToFluidPay(type, item, subType1?) {
    if(this.popData && this.popData.length >0){
    let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
      if(findINdex >-1){
        this.showOnboardingPopup =true;
      }else{
        this.showOnboardingPopup =false;
      }
    }
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.ManageCards'));
    this.viewMode1 = 'tab11';
    if (!item) {
      this.closeNgxModal();
    }
    if (!subType1) {
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: type,
          },
          replaceUrl: false
        }
      );
    } else {
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: type,
            subType: subType1
          },
          replaceUrl: false
        }
      );
    }
  }
  routeToEvents(type,item){
    this.viewMode1 = 'tab14';
    this.titleService.setTitle(this.translateService.instant('setting.GroupTravels'));
    if(!item){
      this.closeNgxModal();
    }
    this.router.navigate(["admin"],
    {
      queryParams:
      {
        type: type,
      },
      replaceUrl: false
    }
  );
  
  }
  routeToPolicy(type, item) {
    if(this.popData && this.popData.length >0){
    let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
      if(findINdex >-1){
        this.showOnboardingPopup =true;
      }else{
        this.showOnboardingPopup =false;
      }
    }
    this.adminPanelService.gotoDetail = false;
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Policies'));
    if (!item) {
      this.OnBoardTaskCompleted();
      if (!this.isOnBoardedHasPolicy()) {
        this.handlePolicyUrl(true);
        this.viewMode1 = 'tab8';
        this.subTab = '';
        this.closeNgxModal();
        this.router.navigate(["admin"],
          {
            queryParams:
            {
              type: type,
            },
            replaceUrl: false
          }
        );
      } else {
        this.viewMode1 = 'tab8';
        this.subTab = '';
        this.selectTask = '';
        this.closeNgxModal();
        this.router.navigate(["admin"],
          {
            queryParams:
            {
              type: type,
            },
            replaceUrl: false
          }
        );
      }
    } else {
      this.viewMode1 = 'tab8';
      if (!this.isMobile1) {
        if (this.onBoardingTask1.length > 0) {
          let item1 = this.onBoardingTask1[this.onBoardingTask1.length - 1]
          if (this.userAccountService.onBoardingTask.indexOf(item1) !== -1) {
            this.selectTask = '';
          }
        }
      }
      this.subTab = '';
      this.closeNgxModal();
      this.router.navigate(["admin"],
        {
          queryParams:
          {
            type: type,
          },
          replaceUrl: false
        }
      );
    }
    setTimeout(() => {
      this.isOnBoardTaskCompleted();
    }, 5000);
  }
  handleImageUrl(event) {
    if (event.show) {
      if (!this.isOnBoardedHasSetting()) {
        let params = "?ua_action=OnboardingTasks&ua_item=billing";
        this.searchService.letsTrack(params);
        if (!this.isMobile1) {

          setTimeout(() => {
            this.onDesktop('billing');
            // this.userAccountService.onBoardingTask= [...this.onBoardingTask1]; 
            this.onBoardingTask1 = [...this.userAccountService.onBoardingTask];
            this.cdRef.detectChanges();
          }, 4000);
          let obj12 = document.getElementById('set1');
          setTimeout(() => {
            if (this.userAccountService.onBoardingTask.length < 7) {
              if (event.newadmin) {
                this.popupmsg = 'dashboardWrapper.NiceYourcentralbillingisnowconfigured';
              } else {
                this.popupmsg = 'dashboardWrapper.NiceThecentralbillingisalreadyconfigured';
              }
              this.isParticularTask('billing');
            }
            openCardEdit123(obj12);
          }, 4100);
        } else {
          this.onMobile('billing');
          this.selectTask = '';
          setTimeout(() => {
            if (this.userAccountService.onBoardingTask.length < 7) {
              if (event.newadmin) {
                this.popupmsg = 'dashboardWrapper.NiceYourcentralbillingisnowconfigured';
              } else {
                this.popupmsg = 'dashboardWrapper.NiceThecentralbillingisalreadyconfigured';
              }
              this.isParticularTask('billing');
            }
          }, 4100);
        }
      }
      setTimeout(() => {
        this.isOnBoardTaskCompleted();
      }, 5000);
    }
  }
  routeToSetting(type,subType,subType2,product, item,i?) {
    if(this.popData && this.popData.length >0){
    let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
      if(findINdex >-1){
        this.showOnboardingPopup =true;
      }else{
        this.showOnboardingPopup =false;
      }
    }
    this.adminPanelService.gotoDetail = false;
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Settings'));
    if (!item) {
      this.OnBoardTaskCompleted();
    }
    this.viewMode1 = 'tab5';
    this.subTab = '';
    if (this.isMobile1) {
      this.selectTask = '';
    }
    // this.cdRef.detectChanges();
    this.closeNgxModal();
    if(subType2 && i){
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
          subType: subType,
          subType2: subType2,
          product:product,
          index:i
        },
        replaceUrl: false
      }
    );
    }else  if(subType2){
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
          subType: subType,
          subType2: subType2,
          product:product,
        },
        replaceUrl: false
      }
    );
    }else{
    this.router.navigate(["admin"],
    {
      queryParams:
      {
        type: type,
        subType: subType,
      },
      replaceUrl: false
    }
  );
    }
  }
  onMobile(item) {
    setTimeout(() => {
      if (this.userAccountService.onBoardingTask.indexOf(item) === -1) {
        this.userAccountService.onBoardingTask.push(item);
        this.cdRef.detectChanges();
      }
    }, 4000);

  }
  onDesktop(item) {
    if (this.userAccountService.onBoardingTask.indexOf(item) === -1) {
      this.userAccountService.onBoardingTask.push(item);
    }
    this.selectTask = item;
  }
  routeToReconcile(type) {
    if(this.popData && this.popData.length >0){
    let findINdex = this.popData.findIndex(item=> item.status=== "not_clicked")
      if(findINdex >-1){
        this.showOnboardingPopup =true;
      }else{
        this.showOnboardingPopup =false;
      }
    }
    this.adminPanelService.gotoDetail = false;
    this.viewMode11 = 'tab9';
    this.subTab = '';
    this.closeNgxModal();
    this.router.navigate(["admin"],
      {
        queryParams:
        {
          type: type,
        },
        replaceUrl: false
      }
    );
  }
  isParticularTask(item) {
    this.userAccountService.onParticularTaskCompleted = true;
    this.cdRef.detectChanges();
    if (item == 'profile') {
      let params = "?ua_action=OnboardingTasks&ua_item=profile_success_shown";
      this.searchService.letsTrack(params);
      this.userAccountService.onBoardingTask.push('profile_success_shown');
    } else if (item === 'booking') {
      let params = "?ua_action=OnboardingTasks&ua_item=booking_success_shown";
      this.searchService.letsTrack(params);
      this.userAccountService.onBoardingTask.push('booking_success_shown');
    }
    setTimeout(() => {
      this.userAccountService.onParticularTaskCompleted = false;
    }, 5000);
  }
  isOnBoardTaskCompleted() {
    if (this.userAccountService.onBoardingTask.length === 7) {
      this.userAccountService.onBoardTaskCompleted = true;
      let params = "?ua_action=OnboardingTasks&ua_item=completed";
      this.searchService.letsTrack(params);
    }
  }
  afteRefreshin() {
    if (this.userAccountService.onBoardingTask.length === 7) {
      this.isOnBoardTaskCompleted();

    } else if (this.userAccountService.onBoardingTask.length > 0 && this.userAccountService.onBoardingTask.length < 7) {
      let item = this.userAccountService.onBoardingTask.find(item => item === 'profile');
      let item2 = this.userAccountService.onBoardingTask.find(item => item === 'booking');
      if (item) {
        let item1 = this.userAccountService.onBoardingTask.find(item => item === 'profile_success_shown');
        if (!item1) {
          this.popupmsg = 'dashboardWrapper.AwesomeYouhavereviewedyourprofile';
          this.isParticularTask('profile');
        }
      }
      if (item2) {
        let item1 = this.userAccountService.onBoardingTask.find(item => item === 'booking_success_shown');
        if (!item1) {
          this.popupmsg = 'dashboardWrapper.CoolYouhavewalked-throughbooking';
          this.isParticularTask('booking');
        }
      }

    }

  }
  OnBoardTaskCompleted() {
    if (this.userAccountService.initResponseReceived) {
      if (this.userAccountService.onBoardingTask.length < 7) {
        this.userAccountService.showOnBoardingTask = true;
        this.userAccountService.hideSuccesMsg = false;
      } else {
        this.userAccountService.showOnBoardingTask = false;
        this.userAccountService.hideSuccesMsg = true;
        this.userAccountService.completeOnboardTask = true;
        this.userAccountService.onBoardTaskCompleted = true;
      }
    }
  }
  changeStyle1() {
    if (this.isOnBoardedHasPolicy()) {
      return { 'pointer-events': 'none', 'cursor': 'default' };
    } else {
      return { 'pointer-events': 'auto', 'cursor': 'pointer' }
    }
  }
  changeStyle2() {
    if (this.isOnBoardedHasProfile()) {
      return { 'pointer-events': 'none', 'cursor': 'default' };
    } else {
      return { 'pointer-events': 'auto', 'cursor': 'pointer' }
    }
  }
  changeStyle3() {
    if (this.isOnBoardedHasBooking()) {
      return { 'pointer-events': 'none', 'cursor': 'default' };
    } else {
      return { 'pointer-events': 'auto', 'cursor': 'pointer' }
    }
  }
  changeStyle4() {
    if (this.isOnBoardedHasEmployee()) {
      return { 'pointer-events': 'none', 'cursor': 'default' };
    } else {
      return { 'pointer-events': 'auto', 'cursor': 'pointer' }
    }
  }
  changeStyle5() {
    if (this.isOnBoardedHasSetting()) {
      return { 'pointer-events': 'none', 'cursor': 'default' };
    } else {
      return { 'pointer-events': 'auto', 'cursor': 'pointer' }
    }
  }
  public showSettingsTab() {
    return this.userAccountService.showSettingsTab();
  }

  public showPoliciesTab() {
    return this.userAccountService.showPoliciesTab();
  }
  public showEmployeesTab() {
    return this.userAccountService.showEmployeesTab();
  }
  public showReportsTab() {
    return this.userAccountService.showReportsTab();
  }
  public isDutyOfCareEnabled() {
    return this.userAccountService.isDutyOfCareEnabled;
  }
  goToTrips() {
    let userid = this.userAccountService.getUserEmail();
    let sToken = this.userAccountService.getSToken();
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Bookings'));
    //this.router.navigate(['/bookingHistory'],{ queryParams: { type:'list'},replaceUrl:true });
    window.location.href = (this.clientConfig.bookingAppBaseUrl + '/bookingHistory?type=list' + "&userid=" + userid + "&sToken=" + sToken);
    // this.titleService.setTitle('Bookings');
    return false;
  }
  signOut() {
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Login'));
    this.searchService.employeeEmail = [];
    this.searchService.employeeList = [];
    CommonUtils.signout(this.userAccountService, this.gallopLocalStorage, this.loginService);
    window.location.href = (this.clientConfig.bookingAppBaseUrl + '/logout');
    //this.router.navigate(['/login'],{ queryParams: { },replaceUrl:true });
  }
  goToProfile(item) {
    // this.titleService.setTitle('Profile');
    if (!item) {
      if (this.userAccountService.userIsDeptAdmin) {
        this.OnBoardTaskCompleted();
      };
      if (!this.isOnBoardedHasProfile()) {
        let params = "?ua_action=OnboardingTasks&ua_item=profile";
        this.searchService.letsTrack(params);
        if (!this.isMobile1) {

          setTimeout(() => {
            this.onDesktop('profile');
            this.onBoardingTask1 = [...this.userAccountService.onBoardingTask];
            this.cdRef.detectChanges();
          }, 4000);
          let obj12 = document.getElementById('pro1');
          setTimeout(() => {
            openCardEdit123(obj12);
          }, 4100);
        } else {
          this.onMobile('profile')
          this.selectTask = '';
        }
        this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Profile'));
        let userid = this.userAccountService.getUserEmail();
        let sToken = this.userAccountService.getSToken();
        window.location.href = (this.clientConfig.bookingAppBaseUrl + '/profile' + "?userid=" + userid + "&sToken=" + sToken);
        //this.router.navigate(['/profile'],{ queryParams: { } });
        // this.titleService.setTitle('Profile');
        return false;
      } else {
        if (this.onBoardingTask1.length > 0) {
          let item1 = this.onBoardingTask1[this.onBoardingTask1.length - 1];
          if (item1 !== 'profile') {
            this.ChangeStyle(item1);
            this.userAccountService.onBoardingTask = [...this.onBoardingTask1];
          }
        }
        this.titleService.setTitle('Profile');
        let userid = this.userAccountService.getUserEmail();
        let sToken = this.userAccountService.getSToken();
        window.location.href = (this.clientConfig.bookingAppBaseUrl + '/profile' + "?userid=" + userid + "&sToken=" + sToken);
        //this.router.navigate(['/profile'],{ queryParams: { } });

        // return false;
      }
      this.isOnBoardTaskCompleted();
    } else {
      this.selectTask = '';
      this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Profile'));
      let userid = this.userAccountService.getUserEmail();
      let sToken = this.userAccountService.getSToken();
      window.location.href = (this.clientConfig.bookingAppBaseUrl + '/profile' + "?userid=" + userid + "&sToken=" + sToken);
      //this.router.navigate(['/profile'],{ queryParams: { } });
      // this.titleService.setTitle('Profile');
      // return false;
    }
  }
  openNewTab1(item){
    let userid = this.userAccountService.getUserEmail();
    let sToken = this.userAccountService.getSToken();
    if (item === 'dashboard') {
      return ('/admin?type=dashboard' + "&userid=" + userid + "&sToken=" + sToken);
    }
    else if (item === 'report') {
      return ( '/admin?type=report'+ '&subType=Transactions' + "&userid=" + userid + "&sToken=" + sToken);
    }
    else if (item === 'fuild') {
      return ( '/admin?type=managecards' + "&userid=" + userid + "&sToken=" + sToken);
    }
    else if (item === 'people') {
      return ( '/admin?type=employees'+'&subType=departments' + "&userid=" + userid + "&sToken=" + sToken);
    }
    else if (item === 'tags') {
      return ( '/admin?type=CustomFields' + "&userid=" + userid + "&sToken=" + sToken);
    }
    else if (item === 'policy') {
      return ('/admin?type=policies' + "&userid=" + userid + "&sToken=" + sToken);
    }
    else if (item === 'payments') {
      return ('/admin?type=paymentMethods' + "&userid=" + userid + "&sToken=" + sToken);
    }
   else if (item === 'integeration') {
      return (  '/admin?type=integrations'+'&subType=notifications' + "&userid=" + userid + "&sToken=" + sToken);
 }
    else if (item === 'setting') {
      return ('/admin?type=setting' + "&userid=" + userid + "&sToken=" + sToken);
    }
    else if (item === 'message') {
      return ( '/admin?type=messages' + "&userid=" + userid + "&sToken=" + sToken);
    }
  }
  openNewTab(item) {
    let userid = this.userAccountService.getUserEmail();
    let sToken = this.userAccountService.getSToken();
    if (item === 'search') {
      return (this.clientConfig.bookingAppBaseUrl + '/search' + "?userid=" + userid + "&sToken=" + sToken);
    } else if (item === 'profile') {
      return (this.clientConfig.bookingAppBaseUrl + '/profile' + "?userid=" + userid + "&sToken=" + sToken);
    } else if (item === 'trip') {
      return (this.clientConfig.bookingAppBaseUrl + '/bookingHistory?type=list' + "&userid=" + userid + "&sToken=" + sToken);
    } else if (item === 'cards') {
      return (this.clientConfig.bookingAppBaseUrl + '/cards?' + "userid=" + userid + "&sToken=" + sToken);
    }

    
  }
  goToCards() {
    this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Cards'));
    let userid = this.userAccountService.getUserEmail();
    let sToken = this.userAccountService.getSToken();
    window.location.href = (this.clientConfig.bookingAppBaseUrl + '/cards' + "?userid=" + userid + "&sToken=" + sToken);
  }
  isuserHascard(): boolean {
    return this.userAccountService.userhascard;
  }
  goToSearch(item) {
    if (this.userAccountService.initResponseReceived) {
      if (!item) {
        if (this.userAccountService.userIsDeptAdmin) {
          this.OnBoardTaskCompleted();
        };
        if (!this.isOnBoardedHasBooking()) {
          let params = "?ua_action=OnboardingTasks&ua_item=booking";
          this.searchService.letsTrack(params);
          if (!this.isMobile1) {

            setTimeout(() => {
              this.onDesktop('booking');
              this.onBoardingTask1 = [...this.userAccountService.onBoardingTask];
              this.cdRef.detectChanges();
            }, 4000);
            let obj12 = document.getElementById('sea1');
            setTimeout(() => {
              openCardEdit123(obj12);
            }, 4100);
          } else {
            this.onMobile('booking');
            this.selectTask = '';
          }
          this.titleService.setTitle(this.translateService.instant('search.FlightSearch'));
          let userid = this.userAccountService.getUserEmail();
          let sToken = this.userAccountService.getSToken();
          window.location.href = (this.clientConfig.bookingAppBaseUrl + '/' + this.userAccountService.getDefaultRoutePath()+ "?userid=" + userid + "&sToken=" + sToken);
          //this.router.navigate(['/search'],{ queryParams: { }, replaceUrl: true });
          // this.titleService.setTitle('Flight Search');
          return false;
        } else {
          if (this.onBoardingTask1.length > 0) {
            let item1 = this.onBoardingTask1[this.onBoardingTask1.length - 1];
            if (item1 !== 'booking') {
              this.ChangeStyle(item1);
              this.userAccountService.onBoardingTask = [...this.onBoardingTask1];
            }
          }
          this.titleService.setTitle(this.translateService.instant('search.FlightSearch'));
          let userid = this.userAccountService.getUserEmail();
          let sToken = this.userAccountService.getSToken();
          window.location.href = (this.clientConfig.bookingAppBaseUrl + '/' + this.userAccountService.getDefaultRoutePath()+ "?userid=" + userid + "&sToken=" + sToken);
          //this.router.navigate(['/search'],{ queryParams: { }, replaceUrl: true });
          // this.titleService.setTitle('Flight Search');
          this.selectTask = '';
        }
        this.isOnBoardTaskCompleted();
      } else {
        let userid = this.userAccountService.getUserEmail();
        let sToken = this.userAccountService.getSToken();
        this.titleService.setTitle(this.translateService.instant('search.FlightSearch'));
        window.location.href = (this.clientConfig.bookingAppBaseUrl + '/' + this.userAccountService.getDefaultRoutePath()+ "?userid=" + userid + "&sToken=" + sToken);
        //this.router.navigate(['/search'],{ queryParams: { }, replaceUrl: true });
        //  this.titleService.setTitle('Flight Search');
        // this.titleService.setTitle('Web Search');
        this.selectTask = '';
      }
    }
  }

}
