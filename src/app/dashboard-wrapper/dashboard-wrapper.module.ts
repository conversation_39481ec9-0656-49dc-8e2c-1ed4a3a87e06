import { NgModule } from "@angular/core";
import { CommonModule } from '@angular/common';
import { DashboardWrapperRoutingModule } from './dashboard-wrapper-routing.module';
import { DashboardWrapperComponent } from './dashboard-wrapper.component';
import { ReportsComponent } from '../reports/reports.component';
import { ApprovalComponent } from '../approval/approval.component';
//import { ApprovalDetailComponent } from '../approval-detail/approval-detail.component';
import { bookingDetailComponent } from '../reports/booking-detail.component';
import { NewdashboardComponent } from '../newdashboard/newdashboard.component';
import { ActiveTravellersComponent } from '../active-travellers/active-travellers.component';
import { MessageComponent } from '../message/message.component';
import { ReconcileComponent } from '../reconcile/reconcile.component';
import { PoliciesComponent } from '../policies/policies.component';
import { EmployeeComponent } from '../employee/employee.component';
import { DashboardComponent } from '../dashboard/dashboard.component';
import { SettingComponent } from '../setting/setting.component';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { UiSwitchModule } from 'ngx-ui-switch';
import { NgxPaginationModule } from 'ngx-pagination';
import { TooltipModule } from 'ng2-tooltip-directive';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgChartsModule } from 'ng2-charts';
//import { AgmCoreModule } from '@agm/core';
import { ShareModule } from '../share.module';
import { FuildPayComponent } from '../fuild-pay/fuild-pay.component';
import { AdminPaymentMethodComponent } from '../admin-payment-method/admin-payment-method.component';
import { AdminIntegerationComponent } from '../admin-integeration/admin-integeration.component';
import { AdminProjectTagsComponent } from '../admin-project-tags/admin-project-tags.component';
import { EventComponent } from '../event/event.component';
import { UiScrollModule } from 'ngx-ui-scroll';





@NgModule({
  imports: [
    CommonModule,
    DashboardWrapperRoutingModule,
    NgbModule,
    ShareModule,
    TooltipModule,
    NgChartsModule,
    NgxSmartModalModule,
   // AgmCoreModule.forRoot({ apiKey: 'AIzaSyA8z-JfNhr9cQgPz58usOioKHIXWSJsvy0' }),
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    BsDatepickerModule,
    UiSwitchModule,
    UiScrollModule,
    NgxPaginationModule,
  ],
  declarations: [DashboardWrapperComponent,
    DashboardComponent,
    ReportsComponent,
    ApprovalComponent,
    bookingDetailComponent,
    NewdashboardComponent,
    AdminPaymentMethodComponent,
    EventComponent,
    AdminIntegerationComponent,
    AdminProjectTagsComponent,
    FuildPayComponent,
    ActiveTravellersComponent,
    MessageComponent,
    ReconcileComponent,
    PoliciesComponent,
    SettingComponent,
    EmployeeComponent,],
})
export class DashboardWrapperModule {

}