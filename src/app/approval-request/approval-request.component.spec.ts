import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ApprovalRequest } from './approval-request.component';

describe('AppApprovalComponent', () => {
  let component: ApprovalRequest;
  let fixture: ComponentFixture<ApprovalRequest>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ApprovalRequest ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ApprovalRequest);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
