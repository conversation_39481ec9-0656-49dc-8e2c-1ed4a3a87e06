<email-header *ngIf="!this.userAccountInfoService.njoySpecificBuild"></email-header>
<section class="tab-container">
    <div class="main-wrapper">
      <div class="container">
        <div class="row">
          <div class="col-lg-12">
            <div *ngIf="!showApprovalDetails" class="tab profileTab" id="pageTab">
              <div class="tab-list top-strip">
                <ul>
                  <li class="tab-list-item active" data-tab="1" ><a
                     >Approval  Request</a></li>  
                </ul>
              </div>
              <div class="tab-content">
                <div class="tab-content-item active" id="1">
                  <div class="card-div active shadow">
                    <div class="card-div-inner" style="width:100%;">
                      <div class="booking-container">
                        <div *ngIf=" !approvalRequestData" class="row"
                          style="text-align: center;padding-top:20px;padding-left:15px;">
                          <app-loader *ngIf="!approvalRequestData" style="margin-left: auto;margin-right: auto;"
                            [spinnerStyle]="true"></app-loader>
                        </div>
                        <div *ngIf="!approvalRequestData && approvalRequestData && approvalRequestData.length ===0"
                        style="text-align:center !important;margin-top:15px !important;">
                        <div class="text1 " style="font-size: 18px;color:gray;" ><span *ngIf="(resultPendingErrorMessage!=='' && resultErrorMessage!=='Fetching data')">{{this.resultPendingErrorMessage | translate}}<br></span>{{errormsg3 | translate}}</div>
                      </div>
                      <div *ngIf="approvalRequestData && approvalRequestData.length >0">
                         <div *ngFor="let item of approvalRequestData;let mainIndex=index">
                             <div>
                                 <div class="result-card-box">
                                     <div class="bottomcontainer" >
                                        <div class="mobileBtton">
                                            <div class="origin-destination1" style="display: block;">
                                                    <div [ngStyle]="channgeAlignment()" class="showinUSerdetails"><span style="font-family: var(--globalFontfamilyr);font-weight: bold;;">{{'itinerary.Traveller'| translate}}: </span> 
                                                        <span style="font-family: var(--globalFontfamilyr);">{{getTravellerName(item)}}</span></div>
                                                            
        
                                                </div>
                                                <div *ngIf="this.isMobile" class="booking-view-button">
                                                        <button class="btn primary-button"
                                                            (click)="getDetailView(item,mainIndex)">{{'fuild.View'
                                                            | translate}}</button>
                                                    </div>
                                        </div>
                                        <div class="origin-destination1">

                                            <span [ngStyle]="channgeAlignment()" class="showinUSerdetails">
                                                <span style="font-family: var(--globalFontfamilyr);font-weight: bold;;">Report: </span> <span style="font-family: var(--globalFontfamilyr);">{{getTripName(item)}}</span>
                                            </span>

                                        </div>
                                        <div *ngIf="!this.isMobile" class="booking-view-button">
                                            <button class="btn primary-button"
                                                (click)="getDetailView(item,mainIndex)">{{'fuild.View'
                                                | translate}}</button>
                                        </div>
                                 </div>
                                     
                                 </div>
                             </div>
                         </div> 
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            
  
  
              </div>
            </div>
            <app-approval-request-detail *ngIf="showApprovalDetails" [approvalRequestDetailData]="this.approvalRequestDetailData"   (goBackEmitter)='handleApprovalChange($event)'></app-approval-request-detail>
          </div>
        </div>
      </div>
    </div>
  </section>

  <app-navigation *ngIf="!showApprovalDetails"></app-navigation>