import { Component, OnInit } from '@angular/core';
import { CommonUtils } from '../util/common-utils';
import { TranslateService } from '@ngx-translate/core';
import { DateUtils } from '../util/date-utils';
import { AdminPanelService } from '../admin-panel.service';
import { UserAccountService } from '../user-account.service';
import { CompanyReportResponse } from '../entity/company-report-response';
import { Subscription } from 'rxjs';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { SearchActionType } from '../enum/search-action.type';
import { DeviceDetailsService } from '../device-details.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { SearchService } from '../search.service';
import { debug } from 'console';

@Component({
    selector: 'app-approval-request',
    templateUrl: './approval-request.component.html',
    styleUrls: ['./approval-request.component.scss'],
    standalone: false
})
export class ApprovalRequest implements OnInit {
  approvalRequestDetailData: any;

  constructor(private searchService: SearchService,public progressBar: NgxUiLoaderService,  private titleService: Title,  private activatedRoute: ActivatedRoute,
    private deviceDetailsService: DeviceDetailsService, public router: Router,public translateService: TranslateService,  public userAccountInfoService: UserAccountService,private adminPanelService: AdminPanelService,) { }
  airports;
  responseNotCame=true;
  airlines;
  showApprovalDetails = false;
  approvallist=[];
  approvers: any;
  isMobile: boolean;
  apiReportResponse: CompanyReportResponse;
  queryParmsSubscription: Subscription;
  companyApprovalSubscription: Subscription;
  approvalRequestData :any;
  deviceSubscription: Subscription;
  expenseApprovalSubscription: Subscription;
  previousDate = '';
  users ;
  resultPendingErrorMessage = '';
  startDate: Date = new Date();
  endDate: Date = new Date();
  errormsg3='';
  ngOnInit(): void {
    
    this.titleService.setTitle(this.translateService.instant('Approval Request'));
  //  this.initializeQueryParamsfromRoute();
    this.subscription();
    // this.approvalRequestData = this.userAccountInfoService.approvalRequestData;
    this.queryParmsSubscription =  this.activatedRoute.queryParams.subscribe(params => {
      this.setParameters(params);
    });
  }
  changeMobileDesign(){
    if(this.isMobile){
      return {'position': 'relative','top': '5px'}
    }else{
      return {'position': 'relative','top': '0px'}
    }
  }
  subscription(){
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });

    this.expenseApprovalSubscription = this.userAccountInfoService.approvalRequestDataObserver$.subscribe(data =>{
      this.approvalRequestData = data;
    });
  }
 

  private setParameters(params) {
    if (params['type'] == 'detail' && this.approvalRequestDetailData) {
     this.showApprovalDetails =true;
    }else{
      this.showApprovalDetails =false;
      this.router.navigate(["approvalRequest"],
      {
        queryParams:
        {
          type: 'list',
        },
        replaceUrl: true
      }
    );
    }
  }
  showSecondLoader =false;
  handleApprovalChange(event){
    if(event){
      this.showApprovalDetails =false;
      this.showSecondLoader =true;
      this.approvallist =[];
    }
  }
  channgeAlignment() {
    if (this.isMobile) {
        return { 'text-align': 'left' };
    }
  }
  showPenndingApprovals = false;
  getDisplayDate(dateString: string, format: string): string {
    return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
  }
  getDisplayDateTimeForFlights(dateString: string, format: string): string {
    return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }
  getDetailView(item,mainIndex){ 
      this.showApprovalDetails = true;
      this.approvalRequestDetailData = item;
      this.router.navigate(["approvalRequest"],
      {
        queryParams:
        {
          type: 'detail',
          index: mainIndex
        },
        replaceUrl: true
      }
    );
    }
  

    log(e){
      
    }
    getTripName(e){
      if(e.report_name){
        return e.report_name;
      }
    }
    getTravellerName(data){
       let email = data.submitter;
     return this.searchService.getTravellerName(email);
    }
}
