import { Component, OnInit, EventEmitter, ViewChild, Output, ElementRef } from '@angular/core';
import { AbstractControl, UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormControl, Form, ValidationErrors, ValidatorFn, AsyncValidatorFn } from '@angular/forms';
import { Subscription, Observable, BehaviorSubject } from 'rxjs';
import {  GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { GallopAnalyticsUtil } from 'src/app/analytics.service';
import { UserAccountService } from '../user-account.service';
import { TranslateService } from "@ngx-translate/core";
import { ToastrService, ActiveToast } from 'ngx-toastr';
import { CardInfo } from '../entity/card-info';
import { UserAccountInfo } from '../entity/user-account-info';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AddCardWidgetComponent } from '../email-booking-flow/add-card-widget/add-card-widget.component';
import { DeleteCardModelComponent } from '../email-booking-flow/delete-card-model/delete-card-model.component';
import { Constants } from '../util/constants';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { UserInfo } from '../entity/user-info';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { AdminPanelService, CompanySettings, Department } from '../admin-panel.service';
import { LookupEmployeeByEmailComponent } from '../lookup-employee-by-email/lookup-employee-by-email.component';
import { AddEmployeeComponent } from '../add-employee/add-employee.component';
import { CommonUtils } from '../util/common-utils';
import { constants } from 'http2';
import { DeviceDetailsService } from '../device-details.service';
import { ALL_AIRLINES } from '../util/airlines';
import { ALL_AIRLINES_WITHLOYALITY_LIST,ALL_AIRLINES_LOYALITYPROGRAMME_LIST } from '../util/airlineLoyality';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { SearchService } from '../search.service';
declare var setFocusOnInputField: any;
declare var updateTravelCheckBoxes: any;
declare var addCardForm: any;
@Component({
    selector: 'app-setting',
    templateUrl: './setting.component.html',
    styleUrls: ['./setting.component.scss'],
    standalone: false
})
export class SettingComponent implements OnInit {
  @Output() goBackEmitter = new EventEmitter();
  @Output() getImageUrl = new EventEmitter();
  addCardFlow = false;
  switchon = false;
  newtag = '';
  viewMode2 = 'tab21';
  settingSaveProcessing = false;
  loyalityProgrammeList = ALL_AIRLINES_LOYALITYPROGRAMME_LIST;
  airlineWothLoyalityList = ALL_AIRLINES_WITHLOYALITY_LIST;
  allowedOtherEmployees = false;
  departmentFieldEmpty = false;
  qboEnabled = false;
  deleteCard = false;
  showmstSwitch = false;
  mst_url = '';
  mst_url_original = '';
  disabLeLcc = false;
  deleteTag = false;
  slackEnabled = false;
  tagSaveButton = true;
  expenseProviders = [];
  subType2='';
  selectedTagInndex = -1;
  cardOptions = [];
  cardOptions1 = [];
  cardButtonStyle = false;
  duplicateEntryStatus: boolean = false;
  departmentCardList = []
  departmentArray = [];
  addUserListForDuplicateNotificationForm: UntypedFormGroup;
  lccList = {};
  cardIndex: number;
  expenseBooleanArray = [];
  expensifyArray = [];
  hideGoBackButton = false;
  dropDownopen = false;
  allowedExpensify = [];

  imageSrc: any;
  carOptions = [{ name: 'All cars', value: 'Allow all car rental companies' }, { name: 'Selected  cars', value: 'Allow only below car rental companies' }]
  show = false;
  carRentalCompanies = [{ "value": "TRFL_ZI", "label": "Avis" }, { "value": "TRFL_ZD", "label": "Budget" }, { "value": "TRFL_AL", "label": "Alamo" }, { "value": "TRFL_ZL", "label": "National" }, { "value": "TRFL_ET", "label": "Enterprise" }, { "value": "TRFL_ZE", "label": "Hertz" }, { "value": "TRFL_ZR", "label": "Dollar" },
  { "value": "TRFL_ZT", "label": "Thrifty" }, { "value": "TRFL_ZA", "label": "Payless" }, { "value": "TRFL_AD", "label": "Advantage" }, { "value": "TRFL_FX", "label": "Fox" }, { "value": "TRFL_SX", "label": "Sixt" }];
  show1 = false;
  show2 = false;
  showError = true;
  showEmptyError = false;
  fileToUpload: File = null;
  settingForm: UntypedFormGroup;
  addSettingForBFO: UntypedFormGroup;
  emailNotificationList=[];
  bookingEmailsSentTo=[];
  saveGuestUsers:any;
  minorBookingEnabled:any;
  bookingEmailConfig=[];
  emailNotificationListForBFO=[];
  emailDropdown= Constants.emailDropdown;
  switchDrodpdown=Constants.switchDropdown;
  companyNameForm:UntypedFormControl;
  carCompanyForm: UntypedFormGroup;
  addCardMode = false;
  carCompanies: UntypedFormArray;
  activeToast: ActiveToast<any>;
  userAccountInfoObj: UserAccountInfo;
  fetchAccountInfoSubscription: Subscription;
  companySettingsSubscription: Subscription;
  checkEmployeeDetailResponseSubscription: Subscription;
  bsModalRef: BsModalRef;
  companySettings: CompanySettings;
  existingDepartments: Array<any>;
  newDepartments: Array<string> = [];
  toDeleteDepartments: Array<string>;
  selectedCardIndex: number = -1;
  travelApprovedCheck: UntypedFormControl;
  modifiedLogo: string;
  paymentMethod: string;
  companyName = '';
  carOptionsName = '';
  showLoader = false;
  showLoader1 = false;
  updatedTag = '';
  alltags = [];
  tagShow = [];
  deviceSubscription1: Subscription;


  constructor(private fb: UntypedFormBuilder,
    public userAccountInfoService: UserAccountService,
    private el: ElementRef,
    public adminPanelService: AdminPanelService,
    private activatedRoute: ActivatedRoute,
    private gallopLocalStorage: GallopLocalStorageService,
    public deviceDetailsService: DeviceDetailsService,
    public translateService: TranslateService,
    public searchService: SearchService,
    private ngxAnaltics:GoogleAnalyticsService,
    public router: Router,
    private modalService: BsModalService,
    private titleService: Title,
    private toastr: ToastrService,) { }
  @ViewChild(AddCardWidgetComponent) addCardChild: AddCardWidgetComponent;
  ngOnInit() {
    this.deviceSubscription1 = this.deviceDetailsService.isMobile1().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
    this.switchDrodpdown = this.switchDrodpdown.filter(item=> item.id!=='DEFAULT');
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['type'] == 'setting') {
        if (params['subType']) {
          this.subType2 = params['subType'];
          if (this.subType2 === 'integrations') {
            if (params['index']) {
              this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Integerations'));
              this.routeToIntegerations('integrations', params['subType2'],params['product'],true,params['index']);
              }else{
                this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Integerations'));
              this.routeToIntegerations('integrations', params['subType2'],params['product'],true);
              }
          } else if (this.subType2 === 'paymentMethods') {
            this.TabClicked('tab23',this.subType2);
          } else {
            this.TabClicked('tab21',this.subType2);
          }
          
        }
      } 
    });
    this.addUserListForDuplicateNotificationForm = this.fb.group({
      email: ['', Validators.compose([Validators.pattern(Constants.RGEX_EMAIL)])]
     
    });
    this.addSettingForBFO = this.fb.group({
      notificationEmail: [null, Validators.compose([Validators.pattern(Constants.RGEX_EMAIL)])]
     
    });
    this.loyalityProgrammeList = this.loyalityProgrammeList.sort(function (a, b) {
      if (a.id < b.id) return -1;
      else if (a.id > b.id) return 1;
      else return 0;
    });
    this.carRentalCompanies.sort(function (a, b) {
      if (a.label < b.label) { return -1; }
      if (a.label > b.label) { return 1; }
      return 0;
    })
    this.expenseProviders = Constants.ALL_EXPENSE_PROVIDERS;
    this.paymentMethod = "PERSONAL_CARD";
    this.newDepartments = [];
    this.applyButton =true;
    this.initFormAndValues();
    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      this.userAccountInfoObj = userAccountInfoObj;
     // this.getAllTags();
     this.getUserListForDuplicateNotification();
  this.getAirlineTourCode();
      this.unsetCardMode();
    });

    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {
       // this.initFormAndValues();
        this.companySettings = settings;
        //
        if (this.companySettings.company.allowedExpenseVendors && this.companySettings.company.allowedExpenseVendors.length > 0) {
          this.allowedExpensify = this.companySettings.company.allowedExpenseVendors;

          this.expenseProviders = this.expenseProviders.filter(item1 => {
            for (let item of this.allowedExpensify) {
              if (item1.id === item) {
                //  item1.enable =true;
                return true;
              }
            }
          });
          
          
        }
        if(this.companySettings.defaultSettings){
          this.bookingEmailConfig = this.companySettings.defaultSettings.bookingEmailConfig;
          if(this.companySettings.defaultSettings.bookingEmailConfig[0]!=='DEFAULT'){
            this.bookingEmailsSentTo = this.companySettings.defaultSettings.bookingEmailConfig;
           
          }else{
            this.bookingEmailsSentTo=['TO_MYSELF','TO_TRAVELER'];
          }
          
          if(this.companySettings.defaultSettings.bookingEmailCustomId && this.companySettings.defaultSettings.bookingEmailCustomId.length > 0){
            this.emailNotificationListForBFO = this.companySettings.defaultSettings.bookingEmailCustomId.split(',');
          }else{
            this.emailNotificationListForBFO=[];
          }
          if(this.emailNotificationListForBFO.length===0 && this.isCustomSelectedInEmailSendNotification()){
            this.addSettingForBFO.controls['notificationEmail'].setValidators([Validators.required,Validators.pattern(Constants.RGEX_EMAIL)]);
            this.addSettingForBFO.controls['notificationEmail'].updateValueAndValidity(); 
          }
          this.saveGuestUsers = this.companySettings.defaultSettings.saveGuestUsers;
          this.minorBookingEnabled = this.companySettings.defaultSettings.minorBookingEnabled;
             
        }
        if (this.companySettings.company.ms_teams_channel_url && this.companySettings.company.ms_teams_channel_url.length > 0) {
          this.showmstSwitch = true;
          this.mst_url = this.companySettings.company.ms_teams_channel_url;
          this.mst_url_original = this.mst_url;
        }
        if (this.companySettings.company.expenseProviders && this.companySettings.company.expenseProviders.length > 0) {
          this.expensifyArray = this.companySettings.company.expenseProviders;
          for (let item of this.expensifyArray) {
            for (let item1 of this.expenseProviders) {
              if (item1.id === item) {
                item1.enable = true;
              } else {
                item1.enable = false;
              }
            }
          }
        }
        if (this.companySettings.company) {
          this.allowedOtherEmployees = this.companySettings.company.allowBookForOthers ? this.companySettings.company.allowBookForOthers : false;
          this.disabLeLcc = this.companySettings.company.filterOutLcc;
          this.qboEnabled = this.companySettings.company.qboEnabled;
        }

        if (this.companySettings.company.discount_code && this.companySettings.company.discount_code.length > 0) {
          let control = <UntypedFormArray>this.settingForm.controls.carCompanies;
          for (let item of this.companySettings.company.discount_code) {
            let carCompant = { partnerName: '', discountCode: '', billingNumber: '', partnerCode: '' }
            carCompant.discountCode = item.discountCode;
            carCompant.partnerName = item.partnerName;
            carCompant.partnerCode = item.partnerCode;
            carCompant.billingNumber = item.billingNumber;
            if (carCompant.partnerName && (carCompant.discountCode || carCompant.billingNumber)) {
              let carCompanyForm = this.fb.group({
                partnerCode: [carCompant.partnerCode],
                partnerName: [carCompant.partnerName],
                discountCode: carCompant.discountCode ? [carCompant.discountCode] : null,
                billingNumber: carCompant.billingNumber ? [carCompant.billingNumber] : null,
              });
              this.carCompanies.push(carCompanyForm);

              (<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['partnerCode'].setValidators(
                Validators.compose([Validators.required]));
              (<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['partnerName'].setValidators(
                Validators.compose([Validators.required]));
              if (!carCompant.discountCode) {
                (<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['billingNumber'].setValidators(
                  Validators.compose([Validators.required]));
              }
              if (!carCompant.billingNumber) {
                (<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['discountCode'].setValidators(
                  Validators.compose([Validators.required]));
              }
            }
          }
        }
        if (this.companySettings.cardList && this.companySettings.cardList.card_list.length > 0) {
          this.cardOptions = [];
          this.cardOptions1 = [];
          for (let item of this.companySettings.cardList.card_list) {
            let cardObject = { id: '', value: '', brand: '', last4: '' }
            cardObject.value = item.brand + " " + item.last4;
            cardObject.brand = item.brand;
            cardObject.id = item.id;
            cardObject.last4 = item.last4;
            if (this.cardOptions.find(item => item.id === cardObject.id) === undefined) {
              this.cardOptions.push(cardObject);
            }
            if (this.cardOptions1.find(item => item.id === cardObject.id) === undefined) {
              this.cardOptions1.push(cardObject);
            }
          }


        }
        if (this.bsModalRef) {
          this.bsModalRef.hide();
          this.deleteCard = false;
          this.userAccountInfoService.deletingCard = false;
        }
        this.slackEnabled = this.companySettings.company.slackEnabled;
        this.departmentArray = settings.departments;
        this.companyName = this.companySettings.company.name;
        this.companyNameForm.setValue(this.companyName);
        this.editComapnyName =false;
        if (this.companySettings.company.allowOnlyForSpecificCarCorporatedId) {
          this.carOptionsName = 'Selected  cars'
          this.isCarOptionsRadioSelected(this.carOptionsName);
        } else {
          this.carOptionsName = 'All cars'
          this.isCarOptionsRadioSelected(this.carOptionsName);
        }
        if (this.companySettings.company.logo) {
          this.imageSrc = this.companySettings.company.logo;

        }
        if (this.companySettings.cardList && this.companySettings.cardList.card_list.length > 0) {
          for (let cIndex in this.companySettings.cardList.card_list) {
            if (this.companySettings.cardList.card_list[cIndex].selected) {
              this.selectedCardIndex = parseInt('' + cIndex);

              break;
            }
          }
          if (this.selectedCardIndex == -1) this.selectedCardIndex = 0;
        }
        if ((this.userAccountInfoService.onBoardingTask.indexOf('billing') === -1)) {
          if (this.companySettings.cardList && this.companySettings.cardList.card_list.length === 1 && this.adminPanelService.addNewCard) {
            let dataEmit = { newadmin: true, show: true }
            this.getImageUrl.emit(dataEmit);
          } else if (this.companySettings.cardList && this.companySettings.cardList.card_list.length === 1 && !this.adminPanelService.addNewCard) {
            let dataEmit = { newadmin: false, show: true }
            this.getImageUrl.emit(dataEmit);
          } else if (this.companySettings.cardList && this.companySettings.cardList.card_list.length > 1) {
            let dataEmit = { newadmin: false, show: true }
            this.getImageUrl.emit(dataEmit);
          }
        }
        if (!this.companySettings.cardList) {
          this.setCardMode();
          this.hideGoBackButton = true;
          let object1 = document.getElementById("addCard");
          addCardForm(object1);

        } else {
          this.hideGoBackButton = false;
        }
        if (this.companySettings.departments) {
          for (let department of this.companySettings.departments) {
            //   (<FormArray>this.settingForm.controls['Department']).push(new FormControl(department.name));
            this.existingDepartments.push(department);
          }

        }
        //   this.settingForm.controls['companyName'].setValue(settings.company.name);
        // let settingsObj = {companyName:settings.company.name,
        //   Department : departments
        // };
        // this.settingForm.setValue(settingsObj);

        // if(this.settingForm.controls['companyName'].value){
        //  this.show2 = true;
        //}


      }
    });

    this.checkEmployeeDetailResponseSubscription = this.adminPanelService.employeeCheckDetailResponseObservable$.subscribe(response => {
      if (response && response === 'addAdmin') {
        this.showAddAdminModal();
      }
    });
    // this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
  }
  initFormAndValues() {
    this.toDeleteDepartments = new Array<string>();
    this.existingDepartments = new Array<any>();
    this.modifiedLogo = '';
    this.companyNameForm = new UntypedFormControl('', Validators.compose([Validators.pattern(Constants.RGEX_POLICYNAME)]));
    this.travelApprovedCheck = new UntypedFormControl(false, [Validators.required]);
    this.carCompanies = this.fb.array([]);
    this.settingForm = this.fb.group({
      carCompanies: this.carCompanies
    }, { validator: '' }
      // profileImage: ['', null],
    );
  }
  getAllTags() {
    const companyid = this.userAccountInfoService.getUserCompanyId();
    this.tagShow = [];
    this.adminPanelService.originalalltags = [];
    // this.showLoader =true;
    this.alltags = [];
    this.adminPanelService.getAllTags(companyid).subscribe(resp => {
      if (resp.status === 'success') {
        this.showLoader = false;
        if (resp.data && resp.data.length > 0) {
          for (let item of resp.data) {
            // this.alltags.push(item);
            this.adminPanelService.originalalltags.push(item);
            this.tagShow.push(true);

          }
          this.alltags = this.adminPanelService.originalalltags;
        }
      } else {
        this.showLoader = false;
        this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntreportdata.Pleasetryagainlater"));
      }
    }, error => {
      if (error.status != 403) {
        this.showLoader = false;
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
  }
  applyButton =false;
  airlineTourCard=[];
  airlineValue='';
  all_airlines = Constants.uatpAirlines;
  airlineDropdown = this.all_airlines;
  showerrorForAirlines =false;
  showErrorUppaccount =false;
  selectedAirlines=[];
  showEditAirlineTour=[];
  airlineTourCode='';
  addAirlineTour =false;
  disableSaveButton =true;
  savingAirlineCode=false;
  selectedAirlineTourIndex=-1
  setFocusOnInput(id){
    setFocusOnInputField(id, '');
  }
  enterAirlineTourCode(text){
    if(text!==''){
      this.showErrorUppaccount =false;
    }else{
      this.showErrorUppaccount =true;
    }
    this.isAirlineTourDetailsFulFilled();
  }
  editingAirlineTourCode(){
    this.savingAirlineCode =true;
    this.showLoader1 =true;
    let allairlinesWithLoyalityProgram = this.airlineWothLoyalityList.filter(air => air.code===this.tempLoyaltyList[this.selectedAirlineTourIndex].loyalityCode);
    let loyalityarray=[];
    if(allairlinesWithLoyalityProgram && allairlinesWithLoyalityProgram.length > 0){
    
      for(let item of allairlinesWithLoyalityProgram){
        let loyalityProgramObj:any={};
        loyalityProgramObj['code'] = this.airlineTourCode;
        loyalityProgramObj['airline'] = item.airline;
        loyalityarray.push(loyalityProgramObj);
      }
    }else{
      let loyalityProgramObj:any={};
      loyalityProgramObj['code'] = this.airlineTourCode;
      loyalityProgramObj['airline'] = this.tempLoyaltyList[this.selectedAirlineTourIndex].airline;
      loyalityarray.push(loyalityProgramObj);
    }
    this.adminPanelService.editAirlineTourCard(loyalityarray).subscribe(resp=>{
      if(resp && resp.status==='success'){
        this.addAirlineTour =false;
        this.showLoader1 =false;
        this.getAirlineTourCode();
      }
    }) 
  }
  public showSettingsTab() {
    return this.userAccountInfoService.showSettingsTab();
  }
  addingAirlineTourCode(){
    this.savingAirlineCode =true;
    this.showLoader1 =true;
    let allairlinesWithLoyalityProgram = this.airlineWothLoyalityList.filter(air => air.code===this.airlineValue);
   
    let loyalityarray=[];
    if(allairlinesWithLoyalityProgram && allairlinesWithLoyalityProgram.length > 0){
    
      for(let item of allairlinesWithLoyalityProgram){
        let loyalityProgramObj:any={};
        loyalityProgramObj['code'] = this.airlineTourCode;
        loyalityProgramObj['airline'] = item.airline;
        loyalityarray.push(loyalityProgramObj);
      }
    }
    this.adminPanelService.addAirlineTourCard(loyalityarray).subscribe(resp=>{
      if(resp && resp.status==='success'){
        this.addAirlineTour =false;
        this.showLoader1 =false;
        this.getAirlineTourCode();
      }
    })
  }
  isAirlineTourDetailsFulFilled(){
    if(this.airlineValue !=='' && this.airlineTourCode!==''){
      this.disableSaveButton= false;
    }else{
      this.disableSaveButton= true;
    }
  }
  getLabelValueUatp(){
    if (this.airlineValue !=='') {
      let findValue = this.loyalityProgrammeList.findIndex(item => item.id === this.airlineValue);
      let cardString= this.loyalityProgrammeList[findValue].label;
      return  cardString;
    } else  {
      return this.translateService.instant('setting.SelectAirlineLoyalityProgram');;
    } 
  }
  cancelAddAirlineCode(){
    this.addAirlineTour =false;
    this.airlineValue ='';
    this.airlineTourCode='';
  }
  showAirlineChanged(event){
    this.showerrorForAirlines =false;
    this.isAirlineTourDetailsFulFilled();
  }
  onCancelEditAirlneTourCode(){
    for(let counter=0;counter<this.showEditAirlineTour.length;counter++){
      this.showEditAirlineTour[counter]=false;
    }
    this.airlineValue ='';
    this.airlineTourCode='';
  }
  deletingAirlineTour=false;
 
  onConfirmDeleteAirlineTourCodes(){
    this.deletingAirlineTour=true;
    let allairlinesWithLoyalityProgram = this.airlineWothLoyalityList.filter(air => air.code===this.tempLoyaltyList[this.selectedAirlineTourIndex].loyalityCode);
    let loyalityarray=[];
    if(allairlinesWithLoyalityProgram && allairlinesWithLoyalityProgram.length > 0){
    
    for(let item of allairlinesWithLoyalityProgram){
      let loyalityProgramObj:any={};
      loyalityProgramObj['code'] = this.airlineTourCode;
      loyalityProgramObj['airline'] = item.airline;
      loyalityarray.push(loyalityProgramObj);
    }
  }else{
    let loyalityProgramObj:any={};
    loyalityProgramObj['code'] = this.tempLoyaltyList[this.selectedAirlineTourIndex].code;
    loyalityProgramObj['airline'] = this.tempLoyaltyList[this.selectedAirlineTourIndex].airline;
    loyalityarray.push(loyalityProgramObj);
  }
    this.adminPanelService.deleteAirlineTourCard(loyalityarray).subscribe(resp=>{
      if(resp && resp.status==='success'){
        this.addAirlineTour =false;
        this.showLoader1 =false;
        this.getAirlineTourCode();
      }
    }) 
  }
  onDeletingAirlineTour(index,modal){
    this.selectedAirlineTourIndex=index;
      this.bsModalRef =   this.modalService.show(modal, {
        initialState: {
        }, backdrop: true, ignoreBackdropClick: true
      });
  }
  onEditAirlineTour(index){
    this.selectedAirlineTourIndex=index;
    this.addAirlineTour =false;
    this.disableSaveButton =true;
    this.airlineValue = this.tempLoyaltyList[index].loyalityCode;
    this.airlineTourCode = this.tempLoyaltyList[index].code;
    for(let counter=0;counter<this.showEditAirlineTour.length;counter++){
      this.showEditAirlineTour[counter]=false;
    }
    this.showEditAirlineTour[index]=true;
  }
  loyalityProgrammeMap=[];
  tempLoyaltyList=[];
  closeDropdown() {
    this.dropDownopen = false;
  }
  getAirlineTourCode(){
    this.selectedAirlines=[];
   // this.loyalityProgrammeMap=[];
    this.tempLoyaltyList=[];
   this.airlineDropdown = this.all_airlines;
   this.loyalityProgrammeList = ALL_AIRLINES_LOYALITYPROGRAMME_LIST;
    this.adminPanelService.getAirlineTourList().subscribe(resp=>{
      if(resp && resp.status==='success'){
        this.applyButton =false;
        this.addAirlineTour =false;
        this.savingAirlineCode =false
        this.deletingAirlineTour=false;
        if(this.bsModalRef){
          this.bsModalRef.hide();
        }
        if(resp && resp.data){
          this.airlineTourCard = resp.data
        }
        if(this.airlineTourCard.length > 0){
          for(let item of this.airlineTourCard){
            this.showEditAirlineTour.push(false);
            let findIndex = this.airlineWothLoyalityList.findIndex(air => air.airline===item.airline)
           
            if(findIndex>-1){
              let airlineObject:any={};
              airlineObject['loyalityCode'] = this.airlineWothLoyalityList[findIndex].code;
              airlineObject['airline'] = item.airline;
              airlineObject['code'] = item.code;
              airlineObject['type'] = item.type;
              if(this.tempLoyaltyList.length > 0 && this.tempLoyaltyList.find(air=> air.loyalityCode===this.airlineWothLoyalityList[findIndex].code)===undefined){
              this.tempLoyaltyList.push(airlineObject);
              }else if(this.tempLoyaltyList.length === 0 ){
                this.tempLoyaltyList.push(airlineObject);
              }
            }else{
              let airlineObject:any={};
              airlineObject['loyalityCode'] = this.getAirlineName(item.airline);
              airlineObject['airline'] = item.airline;
              airlineObject['code'] = item.code;
              airlineObject['type'] = item.type;
              if(this.tempLoyaltyList.length > 0 && this.tempLoyaltyList.find(air=> air.loyalityCode===item.code)===undefined){
                this.tempLoyaltyList.push(airlineObject);
                }else if(this.tempLoyaltyList.length === 0 ){
                  this.tempLoyaltyList.push(airlineObject);
                }
            }
            this.selectedAirlines.push(this.airlineWothLoyalityList[findIndex]);
          }
          this.loyalityProgrammeMap = this.tempLoyaltyList;
          for(let item1 of this.loyalityProgrammeMap){
          this.loyalityProgrammeList =this.loyalityProgrammeList.filter(item => item.id!==item1.loyalityCode);
          }
        }
        for(let counter=0;counter<this.showEditAirlineTour.length;counter++){
          this.showEditAirlineTour[counter]=false;
        }
        
      }else{
        this.applyButton =false;
        this.deletingAirlineTour=false;
        this.savingAirlineCode =false
        if(this.bsModalRef){
          this.bsModalRef.hide();
        }
      }
    })
  }
  isAllLoaylitySelected(){
    if(this.loyalityProgrammeList && this.loyalityProgrammeList.length > 0){
      return true;
    }else{
      return false;
    }
  }
  addAirlineTourCode(){
    if(this.selectedAirlines.length > 0){
      for(let item of this.selectedAirlines){
        this.airlineDropdown = this.airlineDropdown.filter(item2 => item2.id!==item)
      }
    }
    for(let counter=0;counter<this.showEditAirlineTour.length;counter++){
      this.showEditAirlineTour[counter]=false;
    }
    this.disableSaveButton =true;
    this.addAirlineTour =true;
    this.airlineValue ='';
    this.airlineTourCode='';
  }
  isMobile1=false;
  getAirlineName(item){
    const airlineName= ALL_AIRLINES.filter(item1=> item1.id===item);
    if (airlineName && airlineName.length > 0){
      return airlineName[0].value;
    }
    return item;
  }
  routeToIntegerations(type,subType2,product,item,i?){
    
      this.titleService.setTitle(this.translateService.instant('dashboardWrapper.Integerations'));
      this.viewMode2 = 'tab22';
      if(subType2 && i){
        this.router.navigate(["admin"],
        {
          queryParams:
          {
            type:'setting',
            subType: type,
            subType2:subType2,
            product:product,
            index:i
          },
          replaceUrl: false
        }
      );
      }else  if(subType2){
        this.router.navigate(["admin"],
        {
          queryParams:
          {
            type:'setting',
            subType: type,
            subType2: subType2,
            product:product,
          },
          replaceUrl: false
        }
      );
      }else{
      this.router.navigate(["admin"],
      {
        queryParams:
        {
          type:'setting',
            subType: type,
        },
        replaceUrl: false
      }
    );
      }
  }
  TabClicked(item,subType) {
    this.viewMode2 = item;
    this.router.navigate(["admin"],
    {
      queryParams:
      {
        type: 'setting',
        subType: subType,
      },
      replaceUrl: false
    }
  );
  }
  addingLowBalanceDetails=false;
  addUserListForDuplicateNotificationSetting(){
    let balanceObject:any=[];
    let email = this.addUserListForDuplicateNotificationForm.controls['email'].value;
    if(email && this.emailNotificationList.find(email)===undefined){
      this.emailNotificationList.push(email);
    }
    
    balanceObject = this.emailNotificationList;
    this.addingLowBalanceDetails=true;
    this.adminPanelService.postEmailListForDuplicateBookingNOtification(balanceObject).subscribe(resp=>{
      if(resp && resp.success){
       // 
        if(this.bsModalRef){
          this.bsModalRef.hide();
        }
        this.addingLowBalanceDetails=false;
        this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"));
        this.getUserListForDuplicateNotification();
      }else{
        this.addingLowBalanceDetails=false;
        this.toastr.error(resp.error_message);
        this.getUserListForDuplicateNotification();
      }
    })
  }
  unselectedEmployee(index) {
   
    this.emailNotificationList.splice(index, 1);
    if(this.emailNotificationList.length===0){
      this.addUserListForDuplicateNotificationForm.controls['email'].setValidators([Validators.pattern(Constants.RGEX_EMAIL)]);
      this.addUserListForDuplicateNotificationForm.markAllAsTouched();
      this.addUserListForDuplicateNotificationForm.controls['email'].updateValueAndValidity(); 
    }
    this.addUserListForDuplicateNotificationSetting();
  }
  addEmailList(){
    if(this.addUserListForDuplicateNotificationForm.controls['email'].value !==null && this.addUserListForDuplicateNotificationForm.controls['email'].value !=='' && this.addUserListForDuplicateNotificationForm.controls['email'].valid){
this.emailNotificationList.push(this.addUserListForDuplicateNotificationForm.controls['email'].value);
this.addUserListForDuplicateNotificationForm.controls['email'].clearValidators();
this.addUserListForDuplicateNotificationForm.controls['email'].setValidators(Validators.pattern(Constants.RGEX_EMAIL));
this.addUserListForDuplicateNotificationForm.controls['email'].updateValueAndValidity();
this.addUserListForDuplicateNotificationForm.controls['email'].setValue(null);
this.addUserListForDuplicateNotificationSetting();
    }
  }
  inputEmailValue(value){
   let val=  value.replace(',', '');
   this.addUserListForDuplicateNotificationForm.controls['email'].setValue(val);
  }
  getUserListForDuplicateNotification(){
    this.adminPanelService.getEmailListForDuplicateBookingNOtification().subscribe(resp=>{
      if(resp && resp.success){
        if(resp.data && resp.data){
          this.emailNotificationList = resp.data;
          if(this.emailNotificationList && this.emailNotificationList.length>0){
            this.addUserListForDuplicateNotificationForm.controls['email'].clearValidators();
this.addUserListForDuplicateNotificationForm.controls['email'].setValidators(Validators.pattern(Constants.RGEX_EMAIL));
this.addUserListForDuplicateNotificationForm.controls['email'].updateValueAndValidity();
          }
        }
      }
    })
  }
  onEmailSendNotificationChangeClicked(item, event) {
    if (event.target.checked) {
      if (this.bookingEmailsSentTo.indexOf(item) === -1) {
        this.bookingEmailsSentTo.push(item);
      }

    } else {
      this.bookingEmailsSentTo = this.bookingEmailsSentTo.filter(item1 => item1 !== item);
     
    }
    let myTag = this.el.nativeElement.querySelector(".error1")
    if (this.bookingEmailsSentTo && this.bookingEmailsSentTo.length===0) {
      myTag.classList.add('removeTransform');
    } else {
      myTag.classList.remove('removeTransform');
    }
    if(this.isCustomSelectedInEmailSendNotification()){
      this.addSettingForBFO.controls['notificationEmail'].setValidators([Validators.required,Validators.pattern(Constants.RGEX_EMAIL)]);
      this.addSettingForBFO.controls['notificationEmail'].updateValueAndValidity(); 
    }else{
      this.emailNotificationListForBFO=[];
      this.addSettingForBFO.controls['notificationEmail'].clearValidators();
      this.addSettingForBFO.controls['notificationEmail'].setValidators(Validators.pattern(Constants.RGEX_EMAIL)); 
      this.addSettingForBFO.controls['notificationEmail'].updateValueAndValidity();
this.addSettingForBFO.controls['notificationEmail'].setValue(null);
    }
  }
  getEmailNotificationLabel(item1){
    let label = this.emailDropdown.filter(item => item.id===item1);
    if(label && label[0]){
      return label[0].value;
    }

  }
  unselectedEmployee2(index) {
   
    this.emailNotificationListForBFO.splice(index, 1);
  }
  inputEmailValue2(value){
    let val=  value.replace(',', '');
    this.addSettingForBFO.controls['notificationEmail'].setValue(val);
   }
  isEmailSendNotificationChecked(item) {
    return this.bookingEmailsSentTo.indexOf(item) > -1;
  }
  
  addEmailList2(){
    if(this.addSettingForBFO.controls['notificationEmail'].value !==null && this.addSettingForBFO.controls['notificationEmail'].value !=='' && this.addSettingForBFO.controls['notificationEmail'].valid){
this.emailNotificationListForBFO.push(this.addSettingForBFO.controls['notificationEmail'].value);
this.addSettingForBFO.controls['notificationEmail'].clearValidators();
this.addSettingForBFO.controls['notificationEmail'].setValidators(Validators.pattern(Constants.RGEX_EMAIL));
this.addSettingForBFO.controls['notificationEmail'].updateValueAndValidity();
this.addSettingForBFO.controls['notificationEmail'].setValue(null);
    }
  }
  
  isCustomSelectedInEmailSendNotification(){
   return this.bookingEmailsSentTo.indexOf('CUSTOM') > -1
  }
  onCompanyChange(i: number) {
    this.duplicateEntryStatus = false;
    if ((<UntypedFormGroup>this.carCompanies.controls[i]).controls['partnerCode'].value === '') {
    } else {
      let carName: string = (<UntypedFormGroup>this.carCompanies.controls[i]).controls['partnerCode'].value;
      let selectedObject = this.carRentalCompanies.find(item => item.value == carName);
      (<UntypedFormGroup>this.carCompanies.controls[i]).controls['partnerCode'].setValue(selectedObject.value);
      (<UntypedFormGroup>this.carCompanies.controls[i]).controls['partnerName'].setValue(selectedObject.label);
      if (i === this.carCompanies.controls.length - 1) {
        //this.addFrequentFlyerEntry(undefined);
      }
    }
  }
  carOptionsSelected(option) {
    this.carOptionsName = option.name;
  }
  isEmptyDiscountCode(index: number) {
    if ((<UntypedFormGroup>this.carCompanies.controls[index]).controls['discountCode'].value === '' && (<UntypedFormGroup>this.carCompanies.controls[index]).controls['billingNumber'].value === '') {
      return true;
    } else {
      return false;
    }
  }
  callSettingApi() {
    if (this.mst_url !== this.mst_url_original) {
      this.addSetting();
    } else {
      return;
    }
  }
  newCompanyName='';
  editComapnyName =false;
  onEditCompanyName(){
  this.newCompanyName = this.companyName;
  this.editComapnyName =true;
  }
  cancelEditCompanyName(){
    this.newCompanyName = '';
  this.editComapnyName =false;
  }
  updateComapnyname(){
    if(this.newCompanyName.length > 0 && this.companyName !==this.newCompanyName){
    this.companyName= this.newCompanyName;
    if(this.companyNameForm.invalid){
      return;
    }
    this.settingSaveProcessing = true;
    this.showLoader1 = true;
      this.processSaveCompanySettings();
    }else if(this.newCompanyName.length === 0){
      this.toastr.error(this.translateService.instant("setting.PleaseEnterCompanyname"));
     
    }
  }
  changeLowCostCarrier(event) {
    if (event) {
      
      this.switchon = false;
    } else {
      this.switchon = true;
    }
    this.disabLeLcc = event
    this.addSetting();
    // }

  }
  searchByAirlineCode(term: string, item: any) {
    term = term.toLowerCase();
    return (item.id && item.id.toLowerCase().indexOf(term) > -1 || item.value && item.value.toLowerCase().indexOf(term) > -1);
  }
  changeQbO(event) {
    if (event) {
      
      this.switchon = false;
    } else {
      this.switchon = true;
    }
    this.qboEnabled = event
    this.addSetting();
  }
  changeSlack(event) {
    if (event) {
      
      this.switchon = false;
    } else {
      this.switchon = true;
    }
    this.slackEnabled = event
    this.addSetting();
  }
  expenseOptionSelected(event, option) {
    if (event) {
      this.switchon = false;
      this.expensifyArray = [];
      //if (this.expensifyArray.indexOf(option) === -1) {
      for (let item of this.expenseProviders) {
        if (item.id === option) {
          item.enable = true;
        } else {
          item.enable = false;
        }
      }
      this.expensifyArray.push(option)
      // }
    } else {
      this.switchon = true;
      for (let item of this.expenseProviders) {
        if (item.id === option) {
          item.enable = false;
        }
      }
      if (this.expensifyArray.length > 0) {
        this.deleteItem(option);
      }
    }
    // this.isExpensifyChecked(option)
    
    this.addSetting();
  }
  openModal(modal) {
    this.bsModalRef = this.modalService.show(modal);
  }
  deleteItem(msg) {
    const index: number = this.expensifyArray.indexOf(msg);
    if (index !== -1) {
      this.expensifyArray.splice(index, 1);
    }
  }
  isExpensifyChecked(item) {
    return this.expensifyArray.indexOf(item) > -1;
  }
  isAllowExpensify(item) {
    return (this.allowedExpensify.indexOf(item) !== -1);
  }
  isEmptyCode(index: number) {
    if ((<UntypedFormGroup>this.carCompanies.controls[index]).controls['partnerCode'].value === '') {
      return true;
    } else {
      return false;
    }
  }
  isEmptyBillingCode(index: number) {
    if ((<UntypedFormGroup>this.carCompanies.controls[index]).controls['billingNumber'].value === '' && (<UntypedFormGroup>this.carCompanies.controls[index]).controls['discountCode'].value === '') {
      return true;
    } else {
      return false;
    }
  }
  isCarOptionsRadioSelected(option) {
    if (this.carOptionsName !== '') {
      return this.carOptionsName === option;
    }
    return false;
  }
  getAdditionalReqeustControl(): AbstractControl[] {
    return (<UntypedFormArray>this.settingForm.controls['carCompanies']).controls;

  }
  onAddCarCompanies() {
    let control = <UntypedFormArray>this.settingForm.controls.carCompanies;
    if (control.length > 0) {
      control.push(
        this.fb.group({
          partnerName: ['', Validators.compose([Validators.required])],
          partnerCode: ['', Validators.compose([Validators.required])],
          billingNumber: ['', this.conditionalValidators((<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['discountCode'].value, [Validators.required])],
          discountCode: ['', this.conditionalValidators((<UntypedFormGroup>this.carCompanies.controls[this.carCompanies.controls.length - 1]).controls['billingNumber'].value, [Validators.required])],

        }));
    } else {
      control.push(
        this.fb.group({
          partnerName: ['', Validators.compose([Validators.required])],
          partnerCode: ['', Validators.compose([Validators.required])],
          billingNumber: [''],
          discountCode: [''],

        }));
    }
  }
  conditionalValidators(condition, validators: ValidatorFn[]) {
    if (condition === '') {
      return Validators.compose(validators);
    }
    return null;
  }
  isDuplicateEntry(index: number) {
    if (index && this.carCompanies
      && this.carCompanies.controls
      && (<UntypedFormGroup>this.carCompanies.controls[index]).controls) {
      if ((<UntypedFormGroup>this.carCompanies.controls[index]).controls['partnerCode'].value !== '') {
        let val: string = (<UntypedFormGroup>this.carCompanies.controls[index]).controls['partnerCode'].value;
        for (var counter = 0; counter < this.carCompanies.controls.length - 1; counter++) {
          if (counter !== index) {
            if (val === (<UntypedFormGroup>this.carCompanies.controls[counter]).controls['partnerCode'].value) {
              this.duplicateEntryStatus = true;
              return true;
            }
          }
        }
      }
    }
    return false;
  }
  onRemoveCompanies(index) {
    let control = <UntypedFormArray>this.settingForm.controls.carCompanies;
    control.removeAt(index);
  }
  showAddAdminModal() {
    this.bsModalRef.hide();
    this.bsModalRef = this.modalService.show(AddEmployeeComponent, {
      initialState: {
        mode: 'addAdmin'
      }, backdrop: true, keyboard: false, ignoreBackdropClick: true
    });
  }
  ngOnDestroy() {
    if(this.companySettingsSubscription){
     this.companySettingsSubscription.unsubscribe();
    }
    if(this.deviceSubscription1){
      this.deviceSubscription1.unsubscribe();
    }
   }
  isAdminAvailable(index: number) {
    return this.companySettings && this.companySettings.departments
      && this.companySettings.departments[index] && this.companySettings.departments[index]['admin'];
  }
  getDepartmentAdminText(index: number) {
    if (this.companySettings && this.companySettings.departments
      && this.companySettings.departments[index] && this.companySettings.departments[index]['admin']) {
      return this.companySettings.departments[index]['admin']['emailId'];
    }
    return undefined;
  }
  addDepartmentAdmin(index: number) {
    // alert("toadd department for index:"+index);
    this.bsModalRef = this.modalService.show(LookupEmployeeByEmailComponent, {
      initialState: {
        mode: 'addManager'
      }, backdrop: true, keyboard: false, ignoreBackdropClick: false
    });
  }
  isLoggedIn(): boolean {
    return this.userAccountInfoService.isLoggedIn();
  }
  public getCardList(): Array<CardInfo> {
    if (this.companySettings && this.companySettings.cardList != null) {
      this.hideGoBackButton = false;
      return this.companySettings.cardList.card_list;
    }
  }
  unsetCardMode() {
    this.addCardMode = false;
    this.show = true;
  }
  setCardMode() {
    this.addCardMode = true;
    this.show = false;
  }
  private cardTokenData: any;
  public handleBackFromAddCard(data: any) {
    if (!data) {
      this.unsetCardMode();
      return;
    }


    let tokenData = JSON.parse(data);
    if (tokenData && tokenData.type === 'newCardAdded') {

      // this.userAccountInfoService.fetchUserAccountInfo(this.emailId, this.sToken);
      let cardTokens: any = tokenData.tokens;
      if (cardTokens && cardTokens.error && cardTokens.error.length > 0) {
        // this.toastr.error(cardTokens.error, 'Card Error!');
        this.addCardChild.setAddCardProgress(false);
        this.addCardChild.setErrorMessage('');
      }

      else if (cardTokens.token && cardTokens.gToken) {
        this.cardTokenData = cardTokens;
        GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
          'cardTokenCreated', 'WebSearchUI'
        );
        this.adminPanelService.requestSaveCardInfo(cardTokens.token, cardTokens.gToken).subscribe(res => {
          // this.userAccountInfoService.requestSaveCardInfo(cardTokens.token, cardTokens.gToken).subscribe(res => {
          if (res.status === 'success') {
            this.addCardFlow = true;
            this.adminPanelService.addNewCard = true;
            // this.userAccountInfoService.fetchUserAccountInfo(false);
            this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
            this.unsetCardMode();
          } else if (res.status === 'CARDERROR') {
            // this.toastr.error(res.message, 'Card Error!');
            this.addCardChild.setErrorMessage(res.message);
            this.addCardChild.setAddCardProgress(false);

          } else {
            // this.toastr.error(res.message, 'Error!');
            this.addCardChild.setErrorMessage(res.message);
            this.addCardChild.setAddCardProgress(false);
          }
        });

      } else {
        // this.addCardChild.setErrorMessage(this.translateService.instant('paymentDetails.UnknownErrorPleasetryagain').toString());
        // this.addCardChild.setAddCardProgress(false);
      }
    }
  }
  getEditableFields() {
    if ((<UntypedFormArray>this.settingForm.controls['departments']).controls
      && (<UntypedFormArray>this.settingForm.controls['departments']).controls.length > 0) {
      return (<UntypedFormArray>this.settingForm.controls['departments']).controls;
    }
    return [];
  }
  removeEditableFields(index) {
    this.showError = true;
    this.showEmptyError = false;
    this.departmentFieldEmpty = false;
    (<UntypedFormArray>this.settingForm.controls['departments']).controls.splice(index, 1);
  }
  validateAllFormFields(formGroup: UntypedFormGroup) {         //{1}
    Object.keys(formGroup.controls).forEach(field => {  //{2}
      const control = formGroup.get(field);             //{3}
      if (control instanceof UntypedFormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof UntypedFormGroup) {        //{5}
        this.validateAllFormFields(control);            //{6}
      }
    });
  }


  addAdmin() {
    if (this.settingForm.valid) {
      let department = { companyId: 0, departmentId: 0, name: '' };
      department.companyId = 123;
      department.departmentId = 456;
      department.name = this.settingForm.controls['departmentname'].value;
      this.departmentArray.push(department);
    } else {
      this.validateAllFormFields(this.settingForm);
    }
  }


  isDepartmentExist(depart): boolean {
    let gotIt;
    if (this.existingDepartments && depart !== undefined) {
      gotIt = this.existingDepartments.find(item => item.toLowerCase() === depart.toLowerCase());
    }

    return gotIt;
  }
  onAddDepartment(msg) {
    this.bsModalRef = this.modalService.show(msg);
  }
  //  handleFileInput(files: FileList) {
  //  
  // this.fileToUpload = files.item(0);
  //
  //}
  selectedFile = null;
  readURL(event): void {
    if (event && event[0]) {
      const file = event[0];
      this.selectedFile = file;
      this.processCompanyImageUpload(this.userAccountInfoService.getUserCompanyId(), this.selectedFile);
    }
  }
  getLccTable(modal) {
    this.adminPanelService.fetchLcc().subscribe(res => {
      if (res) {
        this.lccList = this.getList(res);
        this.lccList = this.sortList(this.lccList);
        
        this.bsModalRef = this.modalService.show(modal);
      }
    });
  }
  sortList(data) {
    data.sort(function (a, b) {
      if (a.value.toLowerCase() < b.value.toLowerCase()) { return -1; }
      if (a.value.toLowerCase() > b.value.toLowerCase()) { return 1; }
      return 0;
    })
    return data;
  }
  getList(data) {
    let keys = []
    for (let k in data) {
      let value = { key: '', value: '' }
      if (data.hasOwnProperty(k)) {
        value.key = k;
        value.value = data[k]
        keys.push(value);
      }
    }
    return keys;
  }
  processSaveCompanySettings() {
    let companySettings: any = {};
    companySettings['companyName'] = this.companyName ? this.companyName : '';
    companySettings['deletedDepartments'] = this.toDeleteDepartments;
    companySettings['departments'] = this.newDepartments;
    if (this.showmstSwitch) {
      companySettings['ms_teams_channel_url'] = this.mst_url;
    } else {
      companySettings['ms_teams_channel_url'] = '';
    }
    if(this.bookingEmailsSentTo && this.bookingEmailsSentTo.length > 0){
      companySettings['bookingEmailConfig'] = this.bookingEmailsSentTo;
    }else{
      companySettings['bookingEmailConfig'] =['DEFAULT']
    }
    if(this.emailNotificationListForBFO && this.emailNotificationListForBFO.length > 0){
      companySettings['bookingEmailCustomId'] = this.emailNotificationListForBFO.toString();
    }else{
      companySettings['bookingEmailCustomId'] =null;
    }
    companySettings['saveGuestUsers']= this.saveGuestUsers;
    companySettings['minorBookingEnabled']= this.minorBookingEnabled
    companySettings['carVendorData'] = this.settingForm.controls['carCompanies'].value;
    companySettings['expenseProviders'] = this.expensifyArray;
    // companySettings['expenseProviders'] = this.expensifyArray;
    companySettings.filterOutLcc = this.disabLeLcc;
    if (companySettings['carVendorData'].length === 0) {
      this.carOptionsName = 'All cars';
    }
    companySettings.qboEnabled = this.qboEnabled;
    companySettings.allowBookForOthers = this.allowedOtherEmployees;
    companySettings.slackEnabled = this.slackEnabled;
    if (this.carOptionsName === 'All cars') {
      companySettings.allowOnlyForSpecificCarCorporatedId = false;
    } else {
      companySettings.allowOnlyForSpecificCarCorporatedId = true;
    }
    if (this.modifiedLogo === '') {
      companySettings['logo'] = this.imageSrc;
    } else {
      companySettings['logo'] = this.modifiedLogo;
    }
    if (this.selectedCardIndex > -1) {
      companySettings['defaultCardId'] = this.getCardList()[this.selectedCardIndex].id;
    }

    this.adminPanelService.saveCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId()
      , companySettings).subscribe(res => {
        if (res && res.success) {
          this.settingSaveProcessing = false;
          this.editComapnyName =false;
          this.showLoader1 = false;
          this.newDepartments = [];
          // this.expensifyArray =[];
          this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
          if (this.switchon) {
            this.toastr.warning(this.translateService.instant("setting.Settingssavedsuccessfully"));
          } else {
            this.toastr.success(this.translateService.instant("setting.Settingssavedsuccessfully"));
          }
        } else if (res && res.error_message) {
          this.settingSaveProcessing = false;
          this.toastr.error(res.error_message);
        } else {
          this.toastr.error(this.translateService.instant("setting.Apologiessomethingwentwrongwecouldntprocessrequest.Pleasetryagainlaterorcontactsupport"));
        }
      }, error => {
        if (error.status != 403) {
          setTimeout(() => {
            this.settingSaveProcessing = false;
            let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
            this.toastr.error(resultErrorMessage);
          }, 100);
        }
      })
  }
  public setCardIndex(index: number) {
    this.selectedCardIndex = index;
    this.paymentMethod = 'PERSONAL_CARD';
    this.switchon = false;
    this.addSetting();
    // this.travelApprovedCheck.setValue(false);
    //this.travelApprovedCheck.updateValueAndValidity();
    //updateTravelCheckBoxes();
  }
  processCompanyImageUpload(companyId: number, imageFile: any) {
    this.adminPanelService.uploadCompanyProfileImageRequest(companyId, imageFile).subscribe(res => {
      if (res && res.success) {
        // this.companySettings = deserialize(res.data, CompanySettings);
        // this.companySettingsResponseSubject.next(this.companySettings);
        this.modifiedLogo = res.data[0];

        this.goBack(this.modifiedLogo);
        this.renderPostUploadFile();
        // window.alert('ImageUploaded Successfully');
      } else if (res && res.error_message) {
        this.toastr.error(res.error_message);
      } else {
        this.toastr.error("Apologies! something went wrong, we could'nt retrive employees list. Please try again later or contact support");
      }
    }, error => {
      if (error.status != 403) {
        setTimeout(() => {
          let resultErrorMessage = this.translateService.instant('searchResult.Pleasecheckyourinternet').toString();
          this.toastr.error(resultErrorMessage);
        }, 100);
      }
    })
  }
  goBack(imageUrl) {
    //this.getImageUrl.emit(imageUrl);
  }
  renderPostUploadFile() {
    const reader = new FileReader();
    const file = this.selectedFile;
    reader.onload = e => this.imageSrc = reader.result;

    reader.readAsDataURL(file);

  }
  onModelCancel() {
    if(this.deletingAirlineTour){
      return;
    }
    this.bsModalRef.hide();
  }
  showModal1(Modal) {
    this.bsModalRef = this.modalService.show(Modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
  }
  editForm() {
    this.show2 = false;
  }
  removeMSt(event) {
    if (event) {
      this.showmstSwitch = true;
      this.switchon = false;
    } else {
      this.showmstSwitch = false;
      this.switchon = true;
      this.addSetting()
    }
  }
  removeDepart(index) {
    // (<FormArray>this.settingForm.controls['Department']).removeAt(index);
    let value = this.existingDepartments[index];
    this.toDeleteDepartments.push(this.existingDepartments[index]);
    this.existingDepartments.splice(index, 1);
    if (this.newDepartments && this.newDepartments.length > 0) {
      this.newDepartments = this.newDepartments.filter(item => {
        return item !== value;
      })
    }
  }
  removeLogo() {
    this.imageSrc = '';
    this.goBack(this.imageSrc);
    this.bsModalRef.hide();
  }
  addSetting() {
    // if(this.settingForm.valid){
    let department;
    for (let i = 0; i < this.carCompanies.controls.length; i++) {
      if ((<UntypedFormGroup>this.carCompanies.controls[i]).controls['discountCode'].value === '') {
        (<UntypedFormGroup>this.carCompanies.controls[i]).get('billingNumber').setValidators([Validators.required]);
        (<UntypedFormGroup>this.carCompanies.controls[i]).get('billingNumber').updateValueAndValidity();
      } else {
        (<UntypedFormGroup>this.carCompanies.controls[i]).get('billingNumber').clearValidators();
        (<UntypedFormGroup>this.carCompanies.controls[i]).get('billingNumber').updateValueAndValidity();
      }
      if ((<UntypedFormGroup>this.carCompanies.controls[i]).controls['billingNumber'].value === '') {
        (<UntypedFormGroup>this.carCompanies.controls[i]).get('discountCode').setValidators([Validators.required]);
        (<UntypedFormGroup>this.carCompanies.controls[i]).get('discountCode').updateValueAndValidity();
      } else {
        (<UntypedFormGroup>this.carCompanies.controls[i]).get('discountCode').clearValidators();
        (<UntypedFormGroup>this.carCompanies.controls[i]).get('discountCode').updateValueAndValidity();
      }
    }
    if (this.getAdditionalReqeustControl().length > 0) {
      if (this.settingForm.invalid)
        return;
      if (this.duplicateEntryStatus)
        return;
      // var arrayControl = this.settingForm.get('carCompanies') as FormArray;
      // 
      if(this.bookingEmailsSentTo && this.bookingEmailsSentTo.length===0){
        return ;
      }
      if(this.isCustomSelectedInEmailSendNotification()){
        if(this.addSettingForBFO.invalid){
          this.addSettingForBFO.controls['notificationEmail'].markAsTouched();
          return;
        }
      }
      this.settingSaveProcessing = true;
      this.processSaveCompanySettings();
    } else {
      if(this.bookingEmailsSentTo && this.bookingEmailsSentTo.length===0){
        return ;
      }
      if(this.isCustomSelectedInEmailSendNotification()){
        if(this.addSettingForBFO.invalid){
          this.addSettingForBFO.controls['notificationEmail'].markAsTouched();
          return;
        }
      }
      this.settingSaveProcessing = true;
      this.processSaveCompanySettings();
    }
  }
  onModelCancel1() {
    this.bsModalRef.hide();
    // this.cardOptions1 = [...this.cardOptions];

  }
  getDepartmentName(item) {
    for (let item1 of this.existingDepartments) {
      if (item1.departmentId === item) {
        return item1.name;
      }
    }
  }
  selectCard(event, i) {
    this.departmentCardList[i].cardId = event;
  }
  public confirmCardDeleteModel(modal, cardIndex: number) {
    this.departmentCardList = [];
    this.cardOptions1 = [...this.cardOptions];
    this.cardOptions1 = this.cardOptions1.filter(item => item.id !== this.companySettings.cardList.card_list[cardIndex].id);
    for (let item of this.existingDepartments) {
      if (item.card_id === this.companySettings.cardList.card_list[cardIndex].id) {
        let departList = { deptId: '', cardId: '' };
        departList.deptId = item.departmentId;
        this.departmentCardList.push(departList);
      }
    }

    this.bsModalRef = this.modalService.show(modal, {
      initialState: {
      }, backdrop: true, ignoreBackdropClick: true
    });
    this.cardIndex = cardIndex;
    //this.bsModalRef.content.onClose.subscribe(result => {

    // if(result){ this.markCardDeleted(cardIndex);}
    // });
  }
  onConfirm() {

    let map = new Map();
    for (let item of this.departmentCardList) {
      if (item.cardId === '') {
        this.toastr.error(this.translateService.instant("setting.Pleaseselectcreditcardforalldepartments"))
        return;
      }
      map.set(item.deptId, item.cardId);
    }
    this.deleteCard = true;
    this.userAccountInfoService.deletingCard = true;
    this.markCardDeleted(this.cardIndex, map)
  }
  public markCardDeleted(cardIndex: number, map) {



    if (this.companySettings && this.companySettings.cardList) {
      this.adminPanelService.requestDeleteCard(this.companySettings.cardList.card_list[cardIndex].id,
        this.companySettings.cardList.card_list[this.selectedCardIndex].id, map).subscribe(res => {

          if (res.status === 'success') {
            this.adminPanelService.processCompanySettingsRequest(this.userAccountInfoService.getUserCompanyId());
          } else {
            this.bsModalRef.hide();
            this.userAccountInfoService.deletingCard = false;
            this.deleteCard = false;
            this.toastr.error(res.message, 'Attention!');
          }
        });
    }
  }
}