<email-header></email-header>
<section class="user-summery">
  <!-- <div  class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card-div" style="padding: 10px 39px 10px 46px;width: 100%;min-height: 169px;margin-top:20px !important;text-align: center;">
                        <div *ngIf="this.cardDetails" class="credit-box">
                            <div class="creditNumber" *ngFor="let item of this.cardNumber"><span style="margin-right: 5px;">{{item}}</span></div>
                            <div class="creditNumber" style="margin-top: 10px;"><span style="margin-right: 5px;font-size: 14px;">Exp:</span> <span style="margin-right: 10px;font-size: 14px;">{{this.cardDetails.expMM}}/{{this.cardDetails.expYY}}</span>
                                <span style="margin-right: 5px;font-size: 14px;">Cvv</span> <span style="margin-right: 10px;font-size: 14px;">{{this.cardDetails.cvv}}</span></div>
                                <div  style="max-width: 100px;margin-right: auto;margin-left: 10px;margin-top: 10px;">
                                        <img src="assets/images/logo 2.webp" />
                                </div>
                               <div  style="margin-top: 0px;position: relative;top: -20px;float: right;">
                                    <span class="dot"></span>
                                    <span class="dot1"></span>
                                </div>
                        </div>
                        <div *ngIf="this.appledevice" style="max-width: 150px;margin-right: auto;margin-left: auto;margin-top: 10px;cursor: pointer;">
                                <img src="assets/images/add-to-apple-wallet-logo.png" />
                        </div>
                        <div *ngIf="!this.appledevice" style="max-width: 150px;margin-right: auto;margin-left: auto;margin-top: 10px;cursor: pointer;">
                                <img src="assets/images/AddtoAndroidWallet.png" />
                        </div>

                       
                    </div>
                </div>
            </div>
        </div>-->
</section>

<section class="tab-container">
  <div class="main-wrapper">
    <div class="container">
      <div class="tab">
        <div class="col-lg-12">
          <div class="tab profileTab" id="pageTab">
            <div class="tab-list top-strip">
              <ul>
                <li class="tab-list-item active" data-tab="1" onclick="activeTab(this);"><a
                    href="javascript:void(0);">{{'cards.MyCards' | translate}}</a></li>
                <li class="tab-list-item" data-tab="2" onclick="activeTab(this);"><a
                    href="javascript:void(0);">{{'cards.Transactions' | translate}}</a></li>
                <!-- <li class="tab-list-item" data-tab="2"  onclick="activeTab(this);" ><a href="javascript:void(0);">Authorized</a></li>
                                        <li class="tab-list-item" data-tab="3" onclick="activeTab(this);" ><a href="javascript:void(0);">Completed</a></li>
                                        <li class="tab-list-item" data-tab="4"   onclick="activeTab(this);"><a href="javascript:void(0);" >Rejected</a></li>-->

              </ul>
            </div>
            <div class="tab-content">
              <div class="tab-content-item active" id="1">
                <div class="card-div active shadow">
                  <div class="card-div-inner" style="width:100%;">
                    <div class="booking-container">
                      <div *ngIf="responseNotCame" class="row"
                        style="text-align: center;padding-top:20px;padding-left:15px;">
                        <app-loader *ngIf="responseNotCame" style="margin-left: auto;margin-right: auto;"
                          [spinnerStyle]="true"></app-loader>
                      </div>
                      <div *ngIf="!this.showAgreementScreen && this.iframeUrl && this.iframeUrl.length===0 && !responseNotCame"
                        style="text-align:center !important;margin-top:15px !important;">
                        <div class="text1 " style="font-size: 18px;color:gray;">{{errormsg3}}</div>
                      </div>
                      <div *ngIf="this.showAgreementScreen">
                        <div >
                            <span class="termsHeading">{{'cards.Pleaseacceptthefollwinguseragreementstostartusingcards' | translate}}:</span>
                        </div>
                        
                        <div style="margin-left: 10px;margin-bottom: 10px;">
                            <span class="guideLink" (click)="openUrl('https://www.celticbank.com/privacy')"  >  <span class="guideLink" >{{ 'cards.CelticBankPrivacyPolicy' | translate }}</span> </span>
                        </div>
                        <div style="margin-left: 10px;">
                            <span class="guideLink" (click)="openUrl('https://stripe.com/legal/issuing/celtic-authorized-user-terms')"  >  <span class="guideLink" >{{ 'cards.CelticBankAuthorizedUserTerms' | translate }}</span> </span>
                        </div>
                        <div>
                            <button class="btn btn-secondary1" [disabled]="AcceptingTerms" (click)="acceptTerms()"><span
                              class="add"> {{ 'cards.AcceptAndContinueToMyCards' | translate}}</span></button>
                        </div>
                      
                      </div>
                      <div *ngIf="!this.showAgreementScreen">
                      <div *ngFor="let item of this.iframeUrl;let i=index">
                        <div *ngIf="i===0" class="row" style="display: block !important;">
                          <div class="filter-row11">
                           <span class="show1" style="font-size: 16px;">{{this.cardList[i].description}}</span>
                           <span class="available" style="color: gray;">{{ 'fuild.Limit' | translate}}: {{getAmount(this.cardList[i].limit) | currency : getCurrencySymbol(this.currency) :
                              'code':
                              '1.2-2'}} {{getItemPeriodLabel(this.cardList[i].period)}} <span > ({{getAmount(this.cardList[i].used) | currency : getCurrencySymbol(this.currency) :
                                'code':
                                '1.2-2'}} {{ 'fuild.utilized' | translate}})</span></span>
                          </div>
                          <iframe class="iframeDiv" [src]="item" scrolling="no"
                            (load)="this.ng_cc_reveal_ui_frame_loaded($event)"></iframe>
                        </div>
                        <div *ngIf="i!==0" class="row" style="display: block !important;">
                            <div class="filter-row11">
                                <span class="show1" style="font-size: 16px;">{{this.cardList[i].description}}</span>
                                <span class="available" style="color: gray;">{{ 'fuild.Limit' | translate}}: {{getAmount(this.cardList[i].limit) | currency : getCurrencySymbol(this.currency) :
                                   'code':
                                   '1.2-2'}} {{getItemPeriodLabel(this.cardList[i].period)}} <span > ({{getAmount(this.cardList[i].used) | currency : getCurrencySymbol(this.currency) :
                                     'code':
                                     '1.2-2'}} {{ 'fuild.utilized' | translate}})</span></span>
                               </div>
                          <iframe class="iframeDiv" [src]="item" scrolling="no"></iframe>
                        </div>
                      </div>
                      </div>
                    </div>
                 
                  </div>
                </div>
              </div>
              <div class="tab-content-item" id="2">
                <div class="card-div active shadow">
                  <div class="card-div-inner" style="width:100%"
                    [ngStyle]="{'padding-bottom':this.transactionReport && this.transactionReport.length > 0  ?'400px':'200px'}">
                    <div *ngIf="this.transactionresponseNotCame" class="row"
                      style="width:100%;text-align: center;padding-top:20px;padding-left:15px;margin-left:  auto !important;margin-right: auto !important;display: inline-block !important;">
                      <app-loader style="text-align:center;margin-left:  auto !important;margin-right: auto !important;"
                        [spinnerStyle]="true"></app-loader>
                    </div>
                    <div
                      *ngIf="this.transactionReport && this.transactionReport.length===0 && !this.transactionresponseNotCame"
                      style="text-align:center !important;margin-top:15px !important;">
                      <div class="text1 " style="font-size: 18px;color:gray;">{{errormsg2}}</div>
                    </div>
                    <div *ngIf="showDeclinneSwitch" class="section">
                      <div class="checkbox-container1" style="padding-left:0px !important;">
                        <div class="row" style="display: flex;justify-content: flex-start;">
                        
                            <span class="switchLabel">{{'cards.Showdeclinedtransactions' | translate}}:</span>
                        
                          <span  style="padding-top:0px !important;text-align: left;position: relative;padding-left: 10px;" [ngStyle]="{'top': this.isMobile ? '-10px':'-5px'}">
                            <ui-switch color="gray" [(ngModel)]="showDelineTransaction"
                              [checked]="showDelineTransaction" (change)="changeTrannsaction($event)" checkedLabel="{{ 'cards.ON' | translate }}"
                              uncheckedLabel="{{ 'cards.OFF' | translate }}"></ui-switch>

                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="section"
                      *ngIf="(this.transactionReport && this.transactionReport.length > 0 && !this.isMobile)">
                      <div class="filter-row"
                        style="justify-content: space-between !important;background-color: #DBDBDB !important;padding-top:10px;height:42px;">
                        <div class="col-auto" style="width:140px;">
                          <span class="show2"> {{'cards.Date' | translate}}</span>
                        </div>
                        <div class="col-auto" style="width:140px;margin-right:4px !important;">
                          <span class="show2"> {{'cards.Merchant' | translate}}</span>
                        </div>
                        <div class="col-auto" style="width:100px;margin-right:4px !important;">
                          <span class="show2">{{'cards.User' | translate}}</span>
                        </div>
                        <div class="col-auto" style="width:150px;margin-right:4px !important;">
                          <span class="show2"> {{'cards.MerchantCategory' | translate}}</span>
                        </div>
                        <div class="col-auto" style="width:240px;margin-right:4px !important;">
                          <span class="show2"> {{'cards.ExpenseCategory' | translate}}</span>
                        </div>
                        <div class="col-auto" style="width:80px;margin-right:4px !important;">
                          <span class="show2">{{'cards.Amount' | translate}}</span>
                        </div>
                        <div *ngIf="showDelineTransaction" class="col-auto"
                          style="width:100px;margin-right:4px !important;">
                          <span class="show2">{{'cards.Status' | translate}}</span>
                        </div>
                        <div *ngIf="!showDelineTransaction" class="col-auto"
                          style="width:50px; padding-top:15px;margin-right:4px !important;">
                        </div>
                        <div class="col-auto" style="width:50px; padding-top:15px;margin-right:4px !important;">
                        </div>
                      </div>
                      <div *ngFor="let trans of this.transactionReport,let i=index;"
                        class="{{i%2==0 ? 'filter-row1':'filter-row2'}}" style="">
                        <div class="col-auto" style="width:140px;padding-top:15px;cursor:pointer !important;">
                          <span class="show1">{{trans.date | date : 'dd MMM yy ,hh:mm' }}</span>
                        </div>
                        <div class="col-auto" style="width:140px; padding-top:15px;margin-right:4px !important;">
                          <span class="show1" style="width:140px;">{{trans.Merchant}}</span>
                        </div>
                        <div class="col-auto" style="width:100px; padding-top:15px;margin-right:4px !important;">
                          <span class="show1" style="width:100px;">{{trans.Name}}</span>
                        </div>
                        <div class="col-auto" style="width:150px; padding-top:15px;margin-right:4px !important;">
                          <span class="show1" style="width:150px;">{{trans.category}} </span>
                        </div>
                        <div class="col-auto" style="width:240px; padding-top:8px;margin-right:4px !important;">

                          <div
                            *ngIf="showExpenseDropDown(trans.cardId,i) && isTransactionIsTopUp(trans.authId) && trans.Status!=='declined'"
                            class="input2" style="padding-right: 5px;bottom: 5px;width: auto !important;"
                            [ngStyle]="{'border-color': isDisabledDropdown(trans,trans.cardId,i) ? '#E7E6E4':this.searchService.darkBgColor}">
                            <div id="expense_DropDown{{i}}">
                              <ng-select #expenselist appendTo="#expense_DropDown{{i}}"
                                style="bottom: 5px; padding-left: 20px;" dropdownPosition="middle" [searchable]="true"
                                placeholder="{{getPlaceHolder(trans.Expense)}}" [clearable]="true"
                                [disabled]="isDisabledDropdown(trans,trans.cardId,i)"
                                (change)="showExpenseTypeChanged($event,trans.authId)"
                                [items]="getDropdownItemslist(trans.cardId,i)" [(ngModel)]="trans.Expense" bindLabel=""
                                bindValue="categoryId" [searchFn]="searchByExpenseName">
                                <ng-template *ngIf="!this.dropDownopen1" ng-label-tmp let-item="item">
                                  <span> {{item.name}}</span>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                                  <span style="padding-right:20px;">{{item.name}}</span>
                                </ng-template>
                              </ng-select>
                              <div class="select-overlay"></div>
                            </div>
                            <svg *ngIf="isDisabledDropdown(trans,trans.cardId,i)" class="down-arrow"
                              (click)="expenselist.toggle()" [ngStyle]="{'top': trans.Expense ? '-68px':'-62px'}"
                              style="position:relative;float:right;right:30px;left:0px;" width="15" height="9"
                              viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                fill="gray" />
                            </svg>
                            <svg *ngIf="!isDisabledDropdown(trans,trans.cardId,i)" class="down-arrow"
                              (click)="expenselist.toggle()" [ngStyle]="{'top': trans.Expense ? '-68px':'-62px'}"
                              style="position:relative;float:right;right:30px;left:0px;" width="15" height="9"
                              viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                fill="#8936F3" />
                            </svg>
                          </div>
                        </div>
                        <div class="col-auto" style="width:80px; padding-top:15px;margin-right:4px !important;">
                          <span class="show1"> {{trans.Amount}}</span>
                        </div>
                        <div *ngIf="showDelineTransaction" class="col-auto"
                          style="width:100px; padding-top:15px;margin-right:4px !important;">
                          <span class="show1"
                            style="width:100px;color: red !important;text-transform: capitalize !important;">
                            {{trans.Status}}</span>
                        </div>
                        <div *ngIf="!showDelineTransaction" class="col-auto"
                          style="width:50px; padding-top:15px;margin-right:4px !important;">
                         
                          <span *ngIf="isTransactionIsTopUp(trans.authId)" [tooltip]="'cards.attachareceipt' | translate" offset="8"
                            placement="top" [display]="true" content-type="string"><input type="file" #fileInput
                              id="receiptFile" accept=".xlsx,.pdf,.jpg,.jpeg,.png" (click)="fileInput.value = null"
                              value="" (change)="readURL($event.target.files,trans.authId);" style="display:none;"
                              single>
                            <img class="memoImage" style="margin-right: 4px;" *ngIf="!trans.receiptMsg"
                              src="assets/images/receipt.png" (click)="this.selectedCardNumber=trans.authId"
                              onclick="document.getElementById('receiptFile').click()"></span>
                          <span *ngIf="isTransactionIsTopUp(trans.authId)" [tooltip]="'cards.viewreceipt' | translate" offset="8"
                            placement="top" [display]="true" content-type="string"
                            (click)="receiptSelector(trans.receiptMsg)"> <img class="memoImage"
                              style="margin-right: 4px;" *ngIf="trans.receiptMsg"
                              src="assets/images/receiptuploaded.png"></span>
                          <span *ngIf="isTransactionIsTopUp(trans.authId)" [tooltip]="'cards.addamemo' | translate" offset="8"
                            placement="top" [display]="true" content-type="string"><img class="memoImage"
                              *ngIf="!trans.memoMsg" (click)="openMemoModal(memoModal,trans.authId,trans)"
                              src="assets/images/memo.png"></span><span *ngIf="isTransactionIsTopUp(trans.authId)"
                            [tooltip]="getMemotext(trans)" offset="8" placement="top" [display]="true"
                            content-type="string"><img class="memoImage"
                              (click)="openMemoModal(memoModal,trans.authId,trans)" *ngIf="trans.memoMsg"
                              src="assets/images/memouploaded.png"></span>


                        </div>
                        <div class="col-auto"
                          style="width:50px; padding-top:15px;margin-right:0px !important;text-align: center;">
                          <span *ngIf="isTransactionIsTopUp(trans.authId)" style="color: gray;cursor: pointer;"
                            (click)="openTransactionDetailModal(trasactionDetailModal,trans.authId)"><i
                              class="fa fa-angle-right"></i></span>
                        </div>
                      </div>


                    </div>
                    <div class="section"
                      *ngIf="(this.transactionReport && this.transactionReport.length > 0 && this.isMobile)">
                      <div *ngFor="let trans of this.transactionReport,let i=index;" class="row"
                        style="margin-bottom: 20px;">
                        <div class="col-8">
                          <div style="font-family: var(--globalFontfamilyr);font-weight: bold;;font-size: 14px;">{{trans.Merchant}}</div>
                          <div class="show1" style="color: gray;">{{trans.date | date : 'dd MMM' }}</div>
                          <div
                            *ngIf="showExpenseDropDown(trans.cardId,i) && isTransactionIsTopUp(trans.authId) && trans.Status!=='declined'"
                            class="input2" style="padding-right: 5px;bottom: 5px;width: auto !important;"
                            [ngStyle]="{'border-color': isDisabledDropdown(trans,trans.cardId,i) ? '#E7E6E4':this.searchService.darkBgColor}">
                            <div id="expense_DropDown{{i}}">
                              <ng-select #expenselist appendTo="#expense_DropDown{{i}}"
                                style="bottom: 5px; padding-left: 20px;" dropdownPosition="middle" [searchable]="true"
                                placeholder="{{getPlaceHolder(trans.Expense)}}" [clearable]="true"
                                [disabled]="isDisabledDropdown(trans,trans.cardId,i)"
                                (change)="showExpenseTypeChanged($event,trans.authId)"
                                [items]="getDropdownItemslist(trans.cardId,i)" [(ngModel)]="trans.Expense" bindLabel=""
                                bindValue="categoryId" [searchFn]="searchByExpenseName">
                                <ng-template *ngIf="!this.dropDownopen1" ng-label-tmp let-item="item">
                                  <span> {{item.name}}</span>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-search="searchTerm" let-index="index">
                                  <span style="padding-right:20px;">{{item.name}}</span>
                                </ng-template>
                              </ng-select>
                              <div *ngIf="trans.Expense && !isDisabledDropdown(trans,trans.cardId,i)" class="crossTag"
                                (click)="showExpenseTypeChanged(null,trans.authId)">x</div>
                              <div class="select-overlay"></div>
                            </div>
                            <svg *ngIf="isDisabledDropdown(trans,trans.cardId,i)" class="down-arrow"
                              (click)="expenselist.toggle()" [ngStyle]="{'top': trans.Expense ? '-68px':'-62px'}"
                              style="position:relative;float:right;right:30px;left:0px;" width="15" height="9"
                              viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                fill="gray" />
                            </svg>
                            <svg *ngIf="!isDisabledDropdown(trans,trans.cardId,i)" class="down-arrow"
                              (click)="expenselist.toggle()" [ngStyle]="{'top': trans.Expense ? '-68px':'-62px'}"
                              style="position:relative;float:right;right:30px;left:0px;" width="15" height="9"
                              viewBox="0 0 15 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.31802 8.43934L0.43934 2.56066C-0.146447 1.97487 -0.146447 1.02513 0.43934 0.43934C1.02513 -0.146446 1.97487 -0.146446 2.56066 0.43934L7.37868 5.25736L12.1967 0.43934C12.7825 -0.146447 13.7322 -0.146447 14.318 0.43934C14.9038 1.02513 14.9038 1.97487 14.318 2.56066L8.43934 8.43934C7.85355 9.02513 6.90381 9.02513 6.31802 8.43934Z"
                                fill="#8936F3" />
                            </svg>
                          </div>

                        </div>
                        <div class="col-4">
                          <div class="show1"
                            style="height: auto !important;padding-top: 8px;font-size: 16px;width:100%;">
                            ${{trans.Amount}}</div>
                          <div *ngIf="showDelineTransaction"
                            style="padding-top:14px;margin-right:4px !important;display: flex;">
                            <span class="show1" style="color: red !important;text-transform: capitalize !important;">
                              {{trans.Status}}</span>
                            <span *ngIf="isTransactionIsTopUp(trans.authId) && showDelineTransaction"
                              style="color: gray;cursor: pointer;margin-left:15px;position: relative;top:-4px;"
                              (click)="openTransactionDetailModal(trasactionDetailModal,trans.authId)"><i
                                class="fa fa-angle-right"></i></span>
                          </div>
                          <div *ngIf="isTransactionIsTopUp(trans.authId)"
                            style="margin-right:4px !important;max-height: 48px;padding-top: 14px;display: flex;justify-content: space-around;">
                            <span *ngIf="!showDelineTransaction"><input type="file" #fileInput id="receiptFile1_"
                                accept=".xlsx,.pdf,.jpg,.jpeg,.png" (click)="fileInput.value = null" value=""
                                (change)="readURL($event.target.files,trans.authId);" style="display:none;" single>
                              <span (click)="this.selectedCardNumber=trans.authId"
                                onclick="document.getElementById('receiptFile1_').click()"> <img class="memoImage"
                                  *ngIf="!trans.receiptMsg" src="assets/images/receipt.png"></span>
                            </span>
                            <span *ngIf="!showDelineTransaction" (click)="receiptSelector(trans.receiptMsg)"> <img
                                class="memoImage" style="margin-right: 4px;" *ngIf="trans.receiptMsg"
                                src="assets/images/receiptuploaded.png"></span>
                            <span *ngIf="!showDelineTransaction"><img class="memoImage" *ngIf="!trans.memoMsg"
                                (click)="openMemoModal(memoModal,trans.authId,trans)" src="assets/images/memo.png"><img
                                class="memoImage" (click)="openMemoModal(memoModal,trans.authId,trans)"
                                *ngIf="trans.memoMsg" src="assets/images/memouploaded.png"></span>
                            <span *ngIf="isTransactionIsTopUp(trans.authId) && !showDelineTransaction"
                              style="color: gray;cursor: pointer;"
                              (click)="openTransactionDetailModal(trasactionDetailModal,trans.authId)"><i
                                class="fa fa-angle-right"></i></span>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>

              </div>


            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<ng-template #trasactionDetailModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title" id="myModalLabel" style="width: 100% !important; text-align: left">
        {{'cards.TransactionDetails' | translate}}</h5>
    <button type="button" style="color: white;" class="close" data-dismiss="modal" (click)="onModelCancel()">
      <i class="material-icons">close</i>
    </button>
    <div *ngIf="this.updateCard" class="approval_request_diaglog_bg_clickhandler" (click)="$event.stopPropagation();">
    </div>
  </div>
  <div class="modal-body" style=" text-align: left !important;padding-bottom: 17px !important;">
    <div class="colItem" style="margin-top: 20px;">
        {{'cards.TransactionDate' | translate}}
    </div>
    <div class="itemcol">
      {{this.transactionSelected[0].date | date : 'dd MMM yy ,hh:mm'}}
    </div>
    <div class="colItem">
       {{'cards.Merchant' | translate}}
    </div>
    <div class="itemcol">
      {{this.transactionSelected[0].Merchant}}
    </div>
    <div class="colItem">
        {{'cards.Name' | translate}}
    </div>
    <div class="itemcol">
      {{this.transactionSelected[0].name}}
    </div>
    <div class="colItem">
        {{'cards.Department' | translate}}
    </div>
    <div class="itemcol">
      {{this.transactionSelected[0].Department}}
    </div>
    <div class="colItem">
       {{'cards.CardNumber' | translate}}
    </div>
    <div class="itemcol">
      {{this.transactionSelected[0].CardNumber}}
    </div>
    <div class="colItem">
        {{'cards.Status' | translate}}
    </div>
    <div class="itemcol">
      {{this.transactionSelected[0].Status}}
    </div>
    <div *ngIf="this.transactionSelected[0].decline!==''" class="colItem">
        {{'cards.Reasonfordecline' | translate}}
    </div>
    <div class="itemcol">
      {{this.transactionSelected[0].decline}}
    </div>
    <div class="colItem">
        {{'cards.Amount' | translate}}
    </div>
    <div class="itemcol">
      {{this.transactionSelected[0].Amount}}
    </div>
    <div
      *ngIf="showExpenseDropDown(this.transactionSelected[0].cardId,0) && !showDelineTransaction && this.transactionSelected[0].Expense"
      class="colItem">
        {{'cards.ExpenseCategory' | translate}}
    </div>
    <div
      *ngIf="showExpenseDropDown(this.transactionSelected[0].cardId,0) && !showDelineTransaction && this.transactionSelected[0].Expense"
      class="itemcol">
      {{this.transactionSelected[0].categoryName}}
    </div>
    <div class="colItem">
        {{'cards.MerchantCategory' | translate}}
    </div>
    <div class="itemcol">
      {{this.transactionSelected[0].category}}
    </div>
    <div *ngIf="!showDelineTransaction" class="colItem">
        {{'cards.Receipt' | translate}}
    </div>
    <div *ngIf="!showDelineTransaction && !this.transactionSelected[0].receiptMsg" class="colItem">
      <input type="file" #fileInput id="receiptFile" accept=".xlsx,.pdf,.jpg,.jpeg,.png"
        (click)="fileInput.value = null" value=""
        (change)="readURL($event.target.files,this.transactionSelected[0].authId);" style="display:none;" single>
      <span style="cursor:pointer" class="addlue" (click)="this.selectedCardNumber=this.transactionSelected[0].authId"
        onclick="document.getElementById('receiptFile').click()">  {{'cards.Addreceipt' | translate}}</span>
    </div>
    <div *ngIf="!showDelineTransaction && this.transactionSelected[0].receiptMsg" class="itemcol">
      <span class="addlue" (click)="receiptSelector(this.transactionSelected[0].receiptMsg)">   {{'cards.clicktoview' | translate}}
      </span><span
        *ngIf="(!this.transactionSelected[0].qboAccess ||  this.transactionSelected[0].qboAccess!=='synced') && !this.updateCard"
        style="margin-left: 10px;cursor: pointer;"> <img src="assets/images/ic_delete.svg"
          (click)="getDeleteReceipt(this.transactionSelected[0].authId)"></span>
    </div>
    <div *ngIf="!showDelineTransaction" class="colItem">
       {{'cards.Memo' | translate}}
    </div>
    <div *ngIf="!showDelineTransaction && !this.transactionSelected[0].memoMsg" class="colItem">
      <span class="addlue" style="cursor:pointer"
        (click)="openMemoModal(memoModal,this.transactionSelected[0].authId,this.transactionSelected[0])">   {{'cards.Addmemo' | translate}}</span>
    </div>
    <div *ngIf="!showDelineTransaction && this.transactionSelected[0].memoMsg" class="itemcol">
      {{this.transactionSelected[0].memoMsg}} <span
        *ngIf="(!this.transactionSelected[0].qboAccess ||  this.transactionSelected[0].qboAccess!=='synced')"
        style="margin-left:5px; cursor: pointer;"> <img src="assets/images/icon_edit.svg" style="margin-right:10px;"
          (click)="openMemoModal(memoModal,this.transactionSelected[0].authId,this.transactionSelected[0])"></span>
    </div>
  </div>
  <div class="modal-form-button">
    <div style="text-align: center;margin-top: 20px;margin-bottom:20px;">
      <button class="btn btn-secondary" (click)="onModelCancel()"><span class="add">  {{'cards.Okay' | translate}}</span> </button>
    </div>
  </div>
</ng-template>
<ng-template #memoModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title" id="myModalLabel" style="width: 100% !important; text-align: left">
      {{'cards.Addamemo' | translate}}</h5>
    <button type="button" style="color: white;" class="close" data-dismiss="modal" (click)="onModelMemoCancel()">
      <i class="material-icons">close</i>
    </button>
    <div *ngIf="this.updateCard" class="approval_request_diaglog_bg_clickhandler" (click)="$event.stopPropagation();">
    </div>
  </div>
  <div class="modal-body requestModalBody">
    <div class="input-field">
      <label>  {{'cards.Pleaseaddamemotothisexpense' | translate}}</label>
      <input maxlength="100" type="text" placeholder="{{'cards.Adddetailsforthisexpense' | translate}}" [(ngModel)]="memoMsg"
        (focus)="getChecked()" class="modal-textarea input-textfield"><span  class="showNumber">
            {{memoMsg.length}}/100
          </span>
    </div>
    <div *ngIf="showChangeRequestErro">
      <span class="text-danger"> {{'cards.Pleaseaddamemotothisexpense' | translate}}</span>
    </div>
    <div class="modal-form-button1">
      <button *ngIf="!this.updateCard" class="btn primary-button" (click)="onAddMemo()" [disabled]="this.updateCard">  {{'cards.Addmemo' | translate}}</button>
      <button *ngIf="this.updateCard" class="btn primary-button" [disabled]="this.updateCard"><span class="add"
          style="font-family: var(--globalFontfamilyr);font-weight: bold !important;">  {{'cards.Wait' | translate}}</span></button><span *ngIf="this.updateCard"
        class="loaderClass">
        <loader-dots class="loaderAlign"></loader-dots>
      </span>
    </div>
  </div>
  <div class="modal-footer1">

  </div>
</ng-template>
<app-navigation></app-navigation>