import { Component, OnInit } from '@angular/core';
import { UserAccountService } from '../user-account.service';
import { BookingService } from '../booking.service';
import { Subscriber, Subscription } from 'rxjs';
import { DateUtils } from '../util/date-utils';
import { Title, DomSanitizer } from '@angular/platform-browser';
import { AdminPanelService } from '../admin-panel.service';
import { ToastrService } from 'ngx-toastr';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { DeviceDetailsService } from '../device-details.service';
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { SearchService } from '../search.service';
import { CommonUtils } from '../util/common-utils';
declare var checkAppleDevices1: any;
declare var cc_ui_host: any;
declare var cc_ui_base_ctx: any;
declare var convertDataToJSON1: any;
declare var disableScroll: any;
declare var enableScroll: any;
@Component({
    selector: 'app-cards',
    templateUrl: './cards.component.html',
    styleUrls: ['./cards.component.scss'],
    standalone: false
})
export class CardsComponent implements OnInit {
  cardDetailSubscription: Subscription;
  cardTransactionSubscription: Subscription;
  bsModalReforMemo: BsModalRef;
  companySettingsSubscription: Subscription;
  deviceSubscription: Subscription;
  cardDetails: any;
  showAgreementScreen =false;
  airports: any;
  airlines: any;
  appledevice = false;
  showDelineTransaction = false;
  showDeclinneSwitch = false;
  applyButton = false;
  bsModalRef: BsModalRef;
  authorizedTransaction = [];
  iframeUrl = [];
  completeTransaction = [];
  rejectTransaction = [];
  originalCardResponse = [];
  cardList = [];
  errormsg = 'No data'
  errormsg1 = 'No data'
  errormsg2 = 'No data';
  responseNotCame = false;
  updateCard = false
  revealLoader1 = false;
  isMobile = false;
  dropDownopen1 = false;
  njoySpecificBuild: boolean;
  expenseCategory: any = [];
  ng_cc_reveal_ui_frame_loaded_count = 0;
  errormsg3 = 'No data';
  cardNumber = [];
  constructor(private userAccountInfoService: UserAccountService,
    private titleService: Title,
    private modalService: BsModalService,
    private translate: TranslateService,
    private toastr: ToastrService,
    private sanitizer: DomSanitizer,
    private searchService: SearchService,
    public deviceDetailsService: DeviceDetailsService,
    private bookingService: BookingService,
    private adminPanelService: AdminPanelService,) { }
    currency = 'USD';
  ngOnInit(): void {
    this.applyButton = true;
    this.njoySpecificBuild = this.userAccountInfoService.isItNjoyBuild();
    this.titleService.setTitle('Cards');
    this.deviceSubscription = this.deviceDetailsService.isMobile1().subscribe(isMobile => {
      this.isMobile = isMobile;

    });
    this.appledevice = checkAppleDevices1();
    this.subscription()
  }
  showChangeRequestErro = false;
  expenseCategoryTree = {};
  memoMsg = '';
  getChecked() {
    this.showChangeRequestErro = false;
  }
  selectedCardNumber = '';
  getMemotext(item) {
    if (item.qboAccess && item.qboAccess === 'synced') {
      return  this.translate.instant('cards.memo');
    } else {
      return this.translate.instant('cards.viewmemo');
    }
  }
  onAddMemo() {
    if (this.memoMsg !== '') {
      this.updateCard = true;
      // this.memoMsg = this.memoMsg.replace(/(\r\n|\n|\r)/gm, "");
      let indexSelect = this.originalTransactionReport.findIndex(item => item['Auth ID'] === this.selectedCardNumber);
      this.adminPanelService.getUpdateMemo(this.originalTransactionReport[indexSelect]['Auth ID'], this.memoMsg).subscribe(resp => {
        if (resp && resp.status === 'success') {
          this.updateCard = false;
          this.originalTransactionReport[indexSelect]['Memo'] = this.memoMsg;
          this.filterTransactionnData();
          //  this.buildTransactionReport(this.originalTransactionReport);
          if (this.transactionSelected.length > 0) {
            this.transactionSelected[0].memoMsg = this.memoMsg;
          }
          this.toastr.success(this.translate.instant('cards.Memoaddedsuccessfully'));
          this.bsModalReforMemo.hide();
        } else {
          this.updateCard = false;
          this.toastr.error(this.translate.instant('cards.Pleasetryagainlater'));
          this.bsModalReforMemo.hide();
        }
      })


    } else {
      this.showChangeRequestErro = true;
      return;
    }
  }
  receiptSelector(url) {
    if (this.njoySpecificBuild) {
      window.location.href = url + '?dummy=/TripItAPI/';
    } else {
      window.open(url, '_blank');
    }
  }
  showExpenseDropDown(item, index) {
    let selectIndex = this.cardList.findIndex(item1 => item1.cardId === item);
    let found = 0;
    if (selectIndex > -1 && this.cardList[selectIndex].childCategories && this.cardList[selectIndex].childCategories.length > 0) {
      return true;
    }
    else {
      return false;
    }
  }
  selectedFile = null;
  imageSrc: any;
  readURL(event, cardNumber): void {
    let indexSelect = this.originalTransactionReport.findIndex(item => item['Auth ID'] === this.selectedCardNumber);
    if (this.originalTransactionReport[indexSelect]['Receipt'] !== "" && this.originalTransactionReport[indexSelect]['Sync Status'] === 'synced' || this.originalTransactionReport[indexSelect]['Status'] === 'declined') {
      return
    }
    if (event && event[0]) {
      const file = event[0];
      this.selectedFile = file;
      this.renderPostUploadFile(cardNumber);
    }
  }
  renderPostUploadFile(cardNumber) {
    const reader = new FileReader();
    const file = this.selectedFile;
    reader.onload = e => this.imageSrc = reader.result;

    reader.readAsDataURL(file);
    let indexSelect = this.originalTransactionReport.findIndex(item => item['Auth ID'] === this.selectedCardNumber);
    // this.buildTransactionReport(this.originalTransactionReport);
    this.adminPanelService.uploadReceipt(this.originalTransactionReport[indexSelect]['Auth ID'], this.selectedFile).subscribe(resp => {
      if (resp && resp.status === 'success') {
        this.originalTransactionReport[indexSelect]['Receipt'] = resp.data;
        this.filterTransactionnData();
        // this.buildTransactionReport(this.originalTransactionReport);
        if (this.transactionSelected.length > 0) {
          this.transactionSelected[0].receiptMsg = resp.data;
        }
        
        this.toastr.success(this.translate.instant('cards.Receiptuploadedsuccessfully'));
      } else {
        if (resp.errorMessage && resp.errorMessage.length > 0) {
          this.toastr.error(resp.errorMessage[0]);
        } else {
          this.toastr.error(this.translate.instant('cards.Pleasetryagainlater'));
       //   this.toastr.error("Please try again later !!!");
        }
      }
    })

  }
  getDeleteReceipt(authid) {
    this.updateCard = true;
    let indexSelect = this.originalTransactionReport.findIndex(item => item['Auth ID'] === authid);
    this.adminPanelService.getDeleteReceipt(authid).subscribe(resp => {
      if (resp && resp.status == 'success') {
        this.updateCard = false;
        this.originalTransactionReport[indexSelect]['Receipt'] = "";
        this.transactionSelected[0].receiptMsg = null;
        this.filterTransactionnData();
        //  this.buildTransactionReport(this.originalTransactionReport);
        //this.toastr.success("Receipt deleted successfully !!!");
        this.toastr.error(this.translate.instant('cards.Receiptdeletedsuccessfully'));
      } else {
        this.updateCard = false;
        this.toastr.error(this.translate.instant('cards.Pleasetryagainlater'));
        //this.toastr.error("Please try again later !!!");
      }
    })
  }
  initializeExpenseCategory(expenseCategoryList: Array<any>) {
    this.expenseCategoryTree = {};
    this.expenseCategory = expenseCategoryList;
    if (expenseCategoryList) {
      for (let i = 0; i < expenseCategoryList.length; i++) {
        this.expenseCategoryTree[expenseCategoryList[i].categoryId] = expenseCategoryList[i];
      }
    }
  }
  searchByExpenseName = (term: string, item: any) => {
    term = term.toLowerCase();
    let originalItem = item;
    let parentID = item.parentId;

    for (let counter = 0; counter < this.expenseCategory.length; counter++) {
      const curr = this.expenseCategory[counter];
      if (curr.parentId === item.categoryId) {
        if (curr.name && curr.name.toLowerCase().indexOf(term) > -1) {
          return true;
        }
      }
    }
    if (item.name && item.name.toLowerCase().indexOf(term) > -1) {
      return true;
    }
    item = originalItem;
    while (parentID) {
      item = this.expenseCategoryTree[parentID];
      if (item.name && item.name.toLowerCase().indexOf(term) > -1) {
        return true;
      }
      parentID = item.parentId;
    }
  }
  isDisabledDropdown(item, item2, index) {
    if (item.qboAccess && item.qboAccess === 'synced') {
      return true;
    }
    let selectIndex = this.cardList.findIndex(item1 => item1.cardId === item2);
    let found = 0;
    if (selectIndex > -1 && this.cardList[selectIndex].childCategories.length > 0) {
      for (let item of this.cardList[selectIndex].childCategories) {
        if (this.transactionReport[index].Expense) {
          if (item.categoryId === this.transactionReport[index].Expense) {
            found = 1;
            break;
          }
        } else {
          return false;
        }
      }
      if (found > 0) {
        return false;
      } else {
        return true;
      }
    }

  }
  onModelMemoCancel() {
    this.bsModalReforMemo.hide();
  }
  getDropdownItemslist(item, index) {
    let selectIndex = this.cardList.findIndex(item1 => item1.cardId === item);
    let found = 0;
    if (selectIndex > -1 && this.cardList[selectIndex].childCategories.length > 0) {
      for (let item of this.cardList[selectIndex].childCategories) {
        if (this.transactionReport[index].Expense) {
          if (item.categoryId === this.transactionReport[index].Expense) {
            found = 1;
            break;
          }
        } else {
          this.initializeExpenseCategory(this.cardList[selectIndex].childCategories);
          return this.cardList[selectIndex].childCategories;
        }
      }
      if (found > 0) {
        this.initializeExpenseCategory(this.cardList[selectIndex].childCategories);
        return this.cardList[selectIndex].childCategories;
      } else {
        let temArr = [];
        let expenseItem = { categoryId: '', name: '', }
        expenseItem.categoryId = this.transactionReport[index].Expense;
        expenseItem.name = this.transactionReport[index].categoryName;
        temArr.push(expenseItem);
        return temArr;
      }
    }
  }
  getPlaceHolder(item) {
    if (item) {
      return "";
    } else {
      return "Select";
    }
  }
  openMemoModal(modal, cardNumber, item) {
    if ((item.qboAccess === 'synced') && item.memoMsg) {
      return;
    }
    if (item.memoMsg) {
      this.memoMsg = item.memoMsg;
    } else {
      this.memoMsg = "";
    }
    this.selectedCardNumber = cardNumber
    this.bsModalReforMemo = this.modalService.show(modal);
  }
  transactionSelected = []
  openTransactionDetailModal(modal, number) {
    this.transactionSelected = [];
    let selectIndex = this.transactionReport.findIndex(item => item.authId === number);
    this.transactionSelected.push(this.transactionReport[selectIndex]);
    this.bsModalRef = this.modalService.show(modal);

  }
  onModelCancel() {
    this.bsModalRef.hide();
  }
  showExpenseTypeChanged(event, id) {
    
    if (event && event.categoryId) {
      this.expenseApiCall(id, event.categoryId, event.name);
    } else {
      this.expenseApiCall(id, "", "");
    }
  }

  expenseApiCall(id, event, item) {
    this.adminPanelService.getUpdateExpense(id, event).subscribe(resp => {
      if (resp && resp.status === 'success') {
        let selectIndex = this.originalTransactionReport.findIndex(item => item['Auth ID'] === id);
        this.originalTransactionReport[selectIndex]['Expense Category'] = event;
        this.originalTransactionReport[selectIndex]['Expense Category Name'] = item;
        this.filterTransactionnData();

        // this.buildTransactionReport(this.originalTransactionReport);
        this.toastr.success(this.translate.instant('cards.Expensecategoryupdatedsuccessfully'))
        disableScroll();
        setTimeout(() => {
          enableScroll();
        }, 100);
      } else {
        this.toastr.error(this.translate.instant('cards.Expensecategorynotupdated.Pleasetryagainlater'))
        disableScroll();
        setTimeout(() => {
          enableScroll();
        }, 100);
      }
    })
  }
  chunkSubstr(str, size) {
    const numChunks = Math.ceil(str.length / size)
    const chunks = new Array(numChunks)

    for (let i = 0, o = 0; i < numChunks; ++i, o += size) {
      chunks[i] = str.substr(o, size)
    }

    return chunks
  }
  dummmyApiCall() {
    this.cardDetailSubscription = this.bookingService.cardDetails().subscribe(res => {
      if (res.success) {

        this.cardDetails = res.cardInfo[0];
        if (this.cardDetails && this.cardDetails.cardNumber) {
          this.cardNumber = this.chunkSubstr(this.cardDetails.cardNumber, 4);
        }
      }
      
    });

    this.cardTransactionSubscription = this.bookingService.cardTransaction().subscribe(res => {
      if (res) {
        
        this.applyButton = false;
        this.airlines = res.airlineNames;
        this.airports = res.airports;
        if (res.authorized && res.authorized.length > 0) {
          this.authorizedTransaction = res.authorized;
        }
        if (res.completed && res.completed.length > 0) {
          this.completeTransaction = res.completed;
        }
        if (res.rejected && res.rejected.length > 0) {
          this.rejectTransaction = res.rejected;
        }
      }

    });
  }
  filterTransactionnData() {
    let originalResponse = JSON.parse(JSON.stringify(this.originalTransactionReport));
    if (!this.showDelineTransaction) {
      originalResponse = originalResponse.filter(item => {
        if (item['Status'] !== 'declined') {
          return true;
        }
      });
    }
    if (this.showDelineTransaction) {
      originalResponse = originalResponse.filter(item => {
        if (item['Status'] === 'declined') {
          return true;
        }
      });
    }
    this.buildTransactionReport(originalResponse);
  }
  transactionReport = [];
  transactionresponseNotCame = false;
  originalTransactionReport = [];
  qboEnabled = false
  userCardDetails() {
    this.transactionresponseNotCame = true;
    this.adminPanelService.getUserUpdateTransactionList().subscribe(resp => {
      if (resp) {
        console.timeLog("resp of users", resp);
        let response = convertDataToJSON1(resp, ';');
        this.originalTransactionReport = response.slice(1);
        var found = 0
        for (let item of this.originalTransactionReport) {
          if (item['Status'] === 'declined') {
            found = 1;
          }
        }
        if (found === 1) {
          this.showDeclinneSwitch = true;

        }
        this.filterTransactionnData();
        // this.buildTransactionReport(this.originalTransactionReport);
        this.transactionresponseNotCame = false;
      } else {
        this.transactionresponseNotCame = false;
      }
    })
  }
  changeTrannsaction(event) {
    this.showDelineTransaction = event;
    this.filterTransactionnData();
  }
  companyApprovalSubscription:Subscription
  subscription() {
    this.companySettingsSubscription = this.adminPanelService.companySettingsResponseObservable$.subscribe((settings) => {
      if (settings) {
        this.qboEnabled = settings.company.qboEnabled;
        this.currency = settings.company.currency;
      }
    });
    
    this.getCardDetails();
    this.userCardDetails();
    this.companyApprovalSubscription = this.adminPanelService.companyApprovalResponseObservable$.subscribe((reportResponse) => {
      if (reportResponse) {
          if (reportResponse.pendingApprovals.length > 0) {
            this.searchService.showApprovalaInMenu =true;
          } else {
            this.searchService.showApprovalaInMenu =false;
          }
    
      } else {
        this.searchService.showApprovalaInMenu =false;
      }
    });
  }
  isTransactionIsTopUp(authId) {
    if (authId) {
      const [item, item2] = authId.split('_');
      if (item && item !== 'tu') {
        return true;
      } else {
        return false;
      }
    }
  }
  buildTransactionReport(resp) {
    this.transactionReport = [];
    // this.availableBalance =0;
    if (resp && resp.length > 0) {
      for (let item of resp) {
        let cardItem = {};
        cardItem['Merchant'] = item['Merchant'];
        //this.availableBalance = this.availableBalance + (item.available_balance) ;
        cardItem['name'] = item['Name']
        cardItem['Department'] = item['Department']
        cardItem['description'] = item['Card description']
        cardItem['CardNumber'] = item['Card number']
        cardItem['Status'] = item['Status']
        cardItem['Amount'] = item['Amount']
        cardItem['decline'] = item['Reason for decline']
        cardItem['category'] = item['Merchant category']
        cardItem['Expense'] = (item['Expense Category'] !== "") ? item['Expense Category'] : null;
        cardItem['date'] = item['ds+Transaction date'];
        cardItem['qboAccess'] = item['Sync Status'];
        cardItem['receiptMsg'] = item['Receipt'] !== "" ? item['Receipt'] : null;
        cardItem['memoMsg'] = item['Memo'] !== "" ? item['Memo'] : null;
        cardItem['authId'] = item['Auth ID'];
        cardItem['cardId'] = item['card id'];
        cardItem['categoryName'] = item['Expense Category Name'];
        this.transactionReport.push(cardItem);
      }
    } else {
      this.errormsg2 = this.translate.instant('cards.Nodata');
    }
    
  }
  AcceptingTerms=false;
  acceptTerms(){
    this.AcceptingTerms =true;
    this.responseNotCame = true;
    this.adminPanelService.getAcceptTermsAndCondition().subscribe((resp)=>{
      this.AcceptingTerms =false;
      this.responseNotCame = false;
      if(resp && resp.status === 'success'){
        this.showAgreementScreen = false;
      } else {
        this.showAgreementScreen = true;
      }
    });
  }
  getCardDetails() {
    this.originalCardResponse = [];
    //this.cardList =[];
    this.responseNotCame = true;
    if (this.userAccountInfoService.njoySpecificBuild) {
      if (this.userAccountInfoService.userIsDeptAdmin){
        let startDate1: Date = new Date();
        let endDate1: Date = new Date();
        let tempdate1 = DateUtils.getFormattedDateWithoutTimeZone(startDate1);
        let tempdate2 = DateUtils.getFormattedDateWithoutTimeZone(endDate1);
        let startDate = DateUtils.getDayAsLocalISODateString(tempdate1);
        let endDate = DateUtils.getDayAsLocalISODateString(tempdate2);
        this.adminPanelService.fetchCompanyApprovals(this.userAccountInfoService.getUserCompanyId(), startDate, endDate, 'all');
      }

    }
    this.adminPanelService.getUserAssignedCards().subscribe((resp) => {
      this.showAgreementScreen = resp.showUserAgreementAcceptanceButton
      if (resp.status === 'success' && resp.data.length > 0) {
        
        // this.responseNotCame =false;
        if(resp.showUserAgreementAcceptanceButton){
          this.responseNotCame =false;
        }
        this.originalCardResponse = resp.data;
        this.buildData(resp.data);
        //this.buildData(resp.data);
      } else {
        this.responseNotCame = false;
        this.errormsg3 = this.translate.instant('cards.Youdonothaveanycardsissued');
      }
    });
  }
  openUrl(url){
    if (this.njoySpecificBuild) {
      window.location.href = url + '?dummy=/TripItAPI/';
    } else {
      window.open(url, "_blank");
    }
  }
  timeOptions = [
    { value: 'Daily', id: 'daily', name: 'fuild.Daily' },
    { value: 'Weekly', id: 'weekly', name: 'fuild.Weekly' },
    { value: 'Monthly', id: 'monthly', name: 'fuild.Monthly' },
    { value: 'Yearly', id: 'yearly', name: 'fuild.Yearly' },
    { value: 'All Time', id: 'all_time', name: 'fuild.AllTime' }
  ];
  getAmount(item) {
    let amount = '';
    if (item) {
      item = item / 100;
      amount = item;
    }else if(item===0){
      return amount=item; 
    }
    return amount;
  }
  getCurrencySymbol(currencyCode: string): string {
    return CommonUtils.getCurrencySymbol(currencyCode);
  }
  getItemPeriodLabel(id){
    let timeOptionName;
    if(id!==''){
    this.timeOptions.map(item => {
      if (item.id.toLowerCase() == id.toLowerCase()) {
        timeOptionName = item.value;
        // return;
      }
    });
  }
  return timeOptionName;
}
  buildData(resp) {
    this.cardList = [];
    // this.availableBalance =0;
    if (resp && resp.length > 0) {
      for (let item of resp) {
        let cardItem = {};
        cardItem['userName'] = item.name;
        //this.availableBalance = this.availableBalance + (item.available_balance) ;
        cardItem['department'] = item.department;
        cardItem['email'] = item.email;
        cardItem['status'] = item.status;
        cardItem['last4'] = item.last4;
        cardItem['limit'] = item.spending_limit ? item.spending_limit.amount : '';
        cardItem['period'] = item.spending_limit ? item.spending_limit.interval : '';
        cardItem['description'] = item.description;
        cardItem['used'] = (item.used);
        cardItem['childCategories'] = item.childCategories;
        cardItem['cardId'] = item.issuing_card_id;
        this.cardList.push(cardItem);
      }
      if (this.cardList.length > 0) {
        this.sortCardList(this.cardList);
        this.getIframeUrl();
      }
    } else {
      this.responseNotCame = false;
      this.errormsg3 = this.translate.instant('cards.Youdonothaveanycardsissued');
      //this.errormsg3 = 'You do not have any cards issued';
    }
    // 
  }
  ng_cc_reveal_ui_frame_loaded(event) {
    this.ng_cc_reveal_ui_frame_loaded_count++;
    if (this.ng_cc_reveal_ui_frame_loaded_count % 2 === 0 || event.target.src !== '') {
      this.responseNotCame = false;
    }
  }
  getIframeUrl() {
    let userid = this.userAccountInfoService.getUserEmail();
    let sToken = this.userAccountInfoService.getSToken();
    for (let item of this.cardList) {
      let url = cc_ui_host + '/' + cc_ui_base_ctx + '/cc-view-small.html?userid=' + userid + "&sToken=" + sToken + '&cardid='
        + item.cardId + '&last4=' + item.last4;
      this.iframeUrl.push(this.sanitizer.bypassSecurityTrustResourceUrl(url));
    }
    

  }
  sortCardList(data) {
    data.sort(function (a, b) {
      // 
      if (a.userName === "" || !a.userName) {
        return 1;
      } else if (b.userName === "" || !b.userName) {
        return -1;
      } else if (a.userName < b.userName) { return -1; }
      else if (a.userName > b.userName) { return 1; }
      return 0;
    })
    return data;
  }
  getAirlineFullName(code) {
    let airlineFullName = null;
    airlineFullName = this.airlines[code];
    return (airlineFullName === '' || airlineFullName === null  || airlineFullName === undefined) ? code : airlineFullName;
  }

  getAirportCity(code) {

    return code && this.airports[code] && this.airports[code]['name'] ? this.airports[code].name : code;
  }
  getDisplayDate(dateString: string, format: string): string {
    return DateUtils.getFormattedDateWithoutTimeZoneFromFormat(new Date(dateString), format);
  }
  ngOnDestroy() {
    if (this.cardTransactionSubscription) {
      this.cardTransactionSubscription.unsubscribe();
    }
    if(this.companyApprovalSubscription){
      this.companyApprovalSubscription.unsubscribe();
    }
    if (this.cardDetailSubscription) {
      this.cardDetailSubscription.unsubscribe();
    }
  }
  getDisplayDateTimeForFlights(dateString: string, format: string): string {
    return DateUtils.getFormattedDateForGivenTimeZone(dateString, format);
  }
  isUserLoggedIn() {
    return this.userAccountInfoService.isLoggedIn();
  }
}


