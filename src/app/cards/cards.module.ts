import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardsRoutingModule } from './cards.routing.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ShareModule } from '../share.module';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { NgSelectModule } from '@ng-select/ng-select';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
//import { AgmCoreModule } from '@agm/core';
import { CardsComponent } from './cards.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TooltipModule } from 'ng2-tooltip-directive';
import { UiSwitchModule } from 'ngx-ui-switch';


@NgModule({
  imports: [
    CommonModule,
    CardsRoutingModule,
    NgbModule,
    ShareModule,
    TooltipModule,
    FormsModule,
    ReactiveFormsModule,
   // AgmCoreModule.forRoot({ apiKey: 'AIzaSyA8z-JfNhr9cQgPz58usOioKHIXWSJsvy0' }),
    NgxSmartModalModule,
    NgSelectModule,
    UiSwitchModule,
    BsDatepickerModule
  ],
  declarations: [
    CardsComponent,

  ],
})
export class CardsModule {

}