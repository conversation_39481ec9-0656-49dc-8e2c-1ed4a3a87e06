@import "../../variables.scss";

.modal-header {
    padding: 16px 17px 15px 10px;
  }

  .modal-content-heading {
    margin-top: 14px;
    margin-bottom: 14px;
  }

  .modal-content-note {
    margin-top: 4px;
    margin-bottom: 62px;
  }

  .modal-content-button {
    margin-bottom: 28px;
  }
  td {
    font-size: 14px;
    letter-spacing: -0.62px;
    line-height: 26px;
    padding: 4px 0;
}
  .total-payment-amount {
    color: var(--dark-bg-color);
    font-size: 20px;
    line-height: 32px;
    font-family: "apercu-b";
    font-weight: bolder;
}
  .modal-icon-container {
    margin-top: 12px;
  }
  .modal-header {
    background-color: var(--hyperlink-color);
    color: #FFFFFF;
    font-size: 14px;
    height: 40px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 6px 6px 0 0;
    padding: 0 8px 0 22px !important;
    border-bottom: none;
  }
  
  .modal-header h5 {
    font-size: 14px;
    font-family: "apercu-mono";
  }