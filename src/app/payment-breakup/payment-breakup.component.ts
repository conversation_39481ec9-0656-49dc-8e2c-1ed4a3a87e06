import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { BookingService } from '../booking.service';
import { CommonUtils } from '../util/common-utils';
import { SearchService } from '../search.service';
import { GallopLocalStorageService } from '../gallop-local-storage.service';
import { UserAccountService } from '../user-account.service';
import { PaymentTypes } from '../enum/payment-types';
import { DeviceDetailsService } from '../device-details.service';
import { Subscription } from 'rxjs';

@Component({
    selector: 'app-payment-breakup',
    templateUrl: './payment-breakup.component.html',
    styleUrls: ['./payment-breakup.component.scss'],
    standalone: false
})
export class PaymentBreakupComponent implements OnInit {
  @Input() billingItemArray;
  @Input() noOfPassengers;
  constructor(public translateService: TranslateService,
    private bsModalRefForUpcomingTrip: BsModalRef,
    public userAccountInfoService: UserAccountService,
    public deviceDetailsService: DeviceDetailsService,
    private gallopLocalStorage: GallopLocalStorageService,
    public searchService: SearchService,
    public bookingService: BookingService,) { }
    selectedbillingItem=[];
    postPaidCar='';
    isMobile =false;
    deviceSubscription: Subscription;
    currencyCode: string = 'USD';
    totalBillPayable=0;serviceFee =0;
    totalPayableAtHotel=0
    hasPrepaidItem = false;
  ngOnInit(): void {
    this.selectedbillingItem=[];
    this.deviceSubscription = this.deviceDetailsService.isMobile().subscribe(isMobile => {
      this.isMobile = isMobile;
    });
    this.selectedbillingItem.push(this.billingItemArray[this.bookingService.selectedPaymentEventIndex]);
    if(this.searchService.infantBookingAllowed && this.searchService.ageGroupArray.length >0){
      let travellerArray=[];
      for(let i=0;i<this.noOfPassengers;i++){
       if(this.searchService.ageGroupArray[i].id!=='INF'){
        travellerArray.push(i);
        }
      }
      this.noOfPassengers = travellerArray.length;
    }
    if (this.billingItemArray) {
      this.currencyCode = this.billingItemArray[0].currency;
    }
    this.calculateTotal();
  }
  public onCancel(): void {
    this.bsModalRefForUpcomingTrip.hide();
  }
  public calculateTotal(): void {
    let total: number = 0.00;
    this.totalPayableAtHotel=0
    this.hasPrepaidItem = false;
    if (this.billingItemArray && this.billingItemArray.length > 1 || this.billingItemArray[0].type == 'flight' || this.billingItemArray[0].number > 1) {
      for (let billingItem of this.billingItemArray) {
        if (billingItem.paymentType === PaymentTypes.PREPAID) {
          this.hasPrepaidItem = true;
          total = total + Number.parseFloat(billingItem.amount);
        } else {
          this.totalPayableAtHotel += Number.parseFloat(billingItem.amount);
        }
        if (billingItem.type == 'other') {
          // This is service Fee
          this.serviceFee = Number.parseFloat(billingItem.amount);
        }
      }
    }
    this.totalBillPayable = total;
  }
  gettotalAmountOnPopup(amount){
   
    return amount;
}
getTaxForHotel(item,item2){
  return (item-item2);
}
getRoomSubtotal(item): any {
  var subTotal = (item.amount - item.tax);
  return subTotal;
}
getTotalCharge(item) {
  var subTotal;
  if (item.resortFee) {
    subTotal = parseFloat(item.amount) + parseFloat(item.resortFee);
  } else {
    subTotal = item.amount;
  }
  return subTotal;
}
getResortFee(item) {
  return item.resortFee;
}
getCarPrice(item) {
  if (item.tax !== null && item.tax > -1) {
    var subTotal = (item.amount - parseFloat(item.tax))
    if (subTotal > item.tax) {
      return subTotal;
    } else {
     // let selectCar = JSON.parse(this.gallopLocalStorage.getItem("selectedCarForBooking"));
      if (item.baseprice) {
        subTotal = item.baseprice;
      }
      return subTotal;
    }
  }

}
getCreditPrice(item){
  if(item && item.displayCreditAmount){
    return item.displayCreditAmount;
  }else{
    return item.creditAmount;
  }
}
getDueNow(item): any {
  if(item && item.amount){
  var subTotal = (item.amount);
  this.totalBillPayable = item.amount;
  return subTotal;
  }
}
getCounterMsg(item){
  if(item){
 return  this.postPaidCar = this.translateService.instant('paymentDetails.Estimatedtotaldueatcounter');
  }else{
   return  this.postPaidCar = this.translateService.instant('paymentDetails.Total');
  }
}
getCurrencySymbol(currencyCode: string): string {
  return CommonUtils.getCurrencySymbol(currencyCode);
}
getCreditAmount(creditDetails) {
  let totalMonney = CommonUtils.getPriceAfterTravelCredits(creditDetails.amount, creditDetails.credit, this.noOfPassengers);
  let total = creditDetails.amount -totalMonney ;
  if(total > 0){
    return total
  }
 

}
isCreditAppliedInAnyItem(items, amount){
  if (items && items.length > 0) {
    for(let i =0; i < items.length; i++) {
      if (this.isCreditApplied(items[i], amount)) {
        return true;
      }
    }
  }
  return false;
}
isCreditApplied(item, amount) {
  let flight = JSON.parse(this.gallopLocalStorage.getItem("selectedFlight"));
  if (flight[this.bookingService.selectedPaymentEventIndex]) {
    const pricePerPassenger = amount / this.noOfPassengers;
    let residualPrice;
    if (item.displayCreditAmount) {
      residualPrice = item.displayCreditAmount - pricePerPassenger;
    } else {
      residualPrice = item.creditAmount - pricePerPassenger;
    }
    if (residualPrice > item.maxResidual) {
      return false;
    }
    return true;
  }
}
getTotalAmount(item){
  let total =0
  if(item.credit && item.credit[0]){
    if(item.credit[0].displayCreditAmount){
      let totalMonney=  CommonUtils.getPriceAfterTravelCredits(item.amount, item.credit, this.noOfPassengers)
      if(item.credit[0].displayCreditAmount > Number(totalMonney)){
        total = totalMonney;
      }else{
        total = totalMonney;
      }
    }else{
      let totalMonney=  CommonUtils.getPriceAfterTravelCredits(item.amount, item.credit, this.noOfPassengers)
      if(item.credit[0].creditAmount > Number(totalMonney)){
        total = totalMonney;
      }else{
        total = totalMonney;
      }
    }
  }else{
    total = item.amount;
  }
  return total;
}
}
