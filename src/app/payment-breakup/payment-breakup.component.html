<div style="display: grid;">
<div class="modal-header" style="
padding: 8px 10px 10px 20px !important;color: #fff;height: 42px;font-size: 14px;font-family: 'apercu-mono';">
<span
*ngIf="this.billingItemArray  && this.billingItemArray.length===1 &&  this.billingItemArray[0].number > 1 ||  this.billingItemArray[0].type==='flight' || hasPrepaidItem">
{{'paymentDetails.Paymentdue' | translate}}</span>
<span
*ngIf="this.billingItemArray && this.billingItemArray.length===1  && this.billingItemArray[0].number===1 &&  this.billingItemArray[0].type!=='flight' ">
{{'paymentDetails.SummaryofCharges' | translate}}</span>


         <button type="button" class="close" data-dismiss="modal" (click)="onCancel()">
             <i class="material-icons" style="color: #fff;">close</i>
         </button>
     </div>
                 <div class="modal-body" style="background-color: #FFF !important;padding:22px 43px; ">
                         <ng-container
                         *ngIf="this.billingItemArray  && this.billingItemArray[this.bookingService.selectedPaymentEventIndex].type==='hotel'">
                         <div *ngFor="let billingItem of this.selectedbillingItem">
                                 <tr style="display: flex;justify-content: space-between;">
                                         <td class="text-left" style="line-height:14px !important; "> {{'paymentDetails.Roomcostpernight' | translate}}:</td>
                                         <td class="text-right" style="line-height:14px !important; ">
                                             {{billingItem.perNightCharge | currency:
                                             getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</td>
                                     </tr>
                                     <tr style="display: flex;justify-content: space-between;">
                                         <td class="text-left" style="line-height:14px !important; ">{{'paymentDetails.Nights' | translate}}:</td>
                                         <td class="text-right" style="line-height:14px !important; ">
                                             {{billingItem.nights}}</td>
                                     </tr>
                                     <tr style="display: flex;justify-content: space-between;">
                                         <td class="text-left" style="line-height:14px !important; "> {{'paymentDetails.Rooms' | translate}}:</td>
                                         <td class="text-right" style="line-height:14px !important; ">
                                             {{billingItem.rooms}}</td>
                                     </tr>
                                     <tr style="display: flex;justify-content: space-between;">
                                         <td class="text-left" style="line-height:14px !important; ">{{'paymentDetails.Guests' | translate}}:</td>
                                         <td class="text-right" style="line-height:14px !important; ">
                                             {{billingItem.guest}}</td>
                                     </tr>
                                     <tr style="display: flex;justify-content: space-between;">
                                         <td class="text-left" style="line-height:14px !important; "> {{'paymentDetails.RoomSubtotal' | translate}}:</td>
                                         <td class="text-right" style="line-height:14px !important; ">
                                             {{getRoomSubtotal(billingItem) | currency:
                                             getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</td>
                                     </tr>
                                    
                                    
                                            <tr style="display: flex;justify-content: space-between;">
                                                 <td >{{'paymentDetails.Tax' | translate}}:</td>
                                                 <td class="text-right" style="line-height:14px !important; ">
                                                         {{(getTaxForHotel(billingItem.tax ,billingItem.fee)) | currency:
                                                             getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</td>
                                             </tr>
                                            
                                             <tr *ngIf="this.billingItemArray[0].type==='hotel' && this.billingItemArray[0].fee!==null"style="display: flex;justify-content: space-between;">
                                                     <td 
                                                     >{{'paymentDetails.Fees' | translate}}:</td>
                                                     <td class="text-right" style="line-height:14px !important; ">
                                                             {{billingItem.fee}}</td>
                                                 </tr>
                                                
                                             
                                     <tr style="display: flex;justify-content: space-between;">
                                             <td *ngIf="billingItem.source.toLowerCase()==='travelport' && this.userAccountInfoService.confermaStatus"
                                             class="text-left" style="line-height:14px !important; "><b>{{'paymentDetails.Roomcharges' | translate}}:</b></td>
                                         <td *ngIf="billingItem.source.toLowerCase()==='travelport' &&  !this.userAccountInfoService.confermaStatus"
                                             class="text-left" style="line-height:14px !important; "><b>{{'paymentDetails.RoomchargesDueathotel' | translate}}:</b></td>
                                         <td *ngIf="billingItem.source.toLowerCase()!=='travelport'"
                                             class="text-left" style="line-height:14px !important; "><b>{{'paymentDetails.RoomchargesDuenow' | translate}}:</b></td>
                                         <td class="text-right" style="line-height:14px !important; ">
                                             <b>{{getDueNow(billingItem) | currency:
                                                 getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</b>
                                         </td>
                                     </tr>
                                     <tr *ngIf="getResortFee(billingItem)!==null && getResortFee(billingItem)>0 "
                                         style="display: flex;justify-content: space-between;">
                                         <td class="text-left" style="line-height:14px !important; "><b>{{'paymentDetails.ResortfeeDueatHotel' | translate}}:</b></td>
                                         <td class="text-right" style="line-height:14px !important; ">
                                             <b>{{getResortFee(billingItem) | currency:
                                                 getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</b>
                                         </td>
                                     </tr>
                                     <hr style="visibility: hidden !important;">
                                 
                                 <tr style="width: 100%;display: flex;
                                 justify-content: space-between;">
                                         <td class="text-left total-payment-amount">
                                                 {{'paymentDetails.TOTALCHARGES' | translate}}
                                             </td>
                                                 <td class="text-right total-payment-amount">
                                                         <b
                                                         style="font-size:20px !important;">{{getTotalCharge(billingItem)
                                                         | currency:
                                                         getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</b> 
                                                 </td>
                                            
                                            
                                             
                                         
                                     </tr>
                                     <div
                                     style="font-size:12px !important;text-align:center;">
                                     <div *ngIf="getResortFee(billingItem)!==null && getResortFee(billingItem)>0 "
                                         class="col text-center">{{'paymentDetails.Totalhotelchargeswithresortfees' | translate}}</div>
                                     </div>
                             </div>
                         </ng-container>
                         <ng-container
                         *ngIf="this.billingItemArray  && this.billingItemArray[this.bookingService.selectedPaymentEventIndex].type==='cars'">
                         <div *ngFor="let billingItem of this.selectedbillingItem">
                                 <tr style="display: flex;justify-content: space-between;">
                                     <!-- <td *ngIf="billingItem.tax!==null"class="text-left"style="line-height:14px !important;width:196px !important; ">Daily Rate ({{ billingItem.nights}} x {{getCarPerDayPrice(billingItem) | number : '1.2-2'}} ) :</td> -->
                                     <td *ngIf="billingItem.tax!==null" class="text-left"
                                         style="line-height:14px !important;width:196px !important; "> {{'paymentDetails.BasePrice' | translate}} :</td>
                                     <td *ngIf="billingItem.tax!==null" class="text-right"
                                         style="line-height:14px !important; ">{{ getCarPrice(billingItem) |
                                         currency: getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}
                                     </td>
                                 </tr>
                                
                                         <tr style="display: flex;justify-content: space-between;">
                                                 <td >{{'paymentDetails.Tax' | translate}}:</td>
                                                 <td class="text-right" style="line-height:14px !important; ">
                                                         {{billingItem.tax | currency: getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</td>
                                             </tr>
                                            
                                            
                                       
                                     
                                
                                 <tr style="display: flex;justify-content: space-between;">
                                     <td class="text-left total-payment-amount"
                                         style="line-height:14px !important;width:196px !important; "><span
                                             *ngIf="billingItem.paymentType ==='POSTPAID'" [ngStyle]="{'white-space':!this.isMobile ? 'nowrap':'inherit'}">
                                             <b>{{getCounterMsg(true)}}:</b></span><span
                                             *ngIf="billingItem.paymentType !=='POSTPAID'" [ngStyle]="{'white-space':!this.isMobile ? 'nowrap':'inherit'}">
                                             <b>{{getCounterMsg(false)}}:</b></span></td>
                                     <td class="text-right total-payment-amount"
                                         style="line-height:14px !important; margin-left:5px !important;">
                                         <b>{{getDueNow(billingItem) | currency:
                                             getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</b>
                                     </td>
                                 </tr>
                                 
                             </div>
                         
                                
                         </ng-container>
                         <ng-container *ngIf="hasPrepaidItem">
                             <div *ngIf="this.billingItemArray  && this.billingItemArray[this.bookingService.selectedPaymentEventIndex].type==='flight'">
                                 <tr *ngFor="let billingItem of this.selectedbillingItem"
                                     style="display: flex;justify-content: space-between;">
                                     <ng-container *ngIf="billingItem.paymentType === 'PREPAID' ">
                                         <td class="text-left">{{billingItem.particular}}</td>
                                         <td class="text-right">{{gettotalAmountOnPopup(billingItem.amount) | currency:
                                             getCurrencySymbol(billingItem.currency):'code':'1.2-2'}}</td>
                                     </ng-container>
                                 </tr>
                                 <ng-container *ngIf="isCreditAppliedInAnyItem(this.selectedbillingItem[0].credit, this.selectedbillingItem[0].amount)">
                                     <tr style="display: flex;justify-content: space-between;">

                                         <td class="text-left">{{'paymentDetails.CreditsUsed' | translate}}</td>
                                         <td class="text-right" style="white-space: nowrap !important;">
                                             -{{getCreditAmount(this.selectedbillingItem[0]) | currency:
                                             getCurrencySymbol(currencyCode): "code" : '1.2-2' }}</td>
                                     </tr>
                                     <hr>
                                 </ng-container>
                                 <ng-container *ngIf="isCreditAppliedInAnyItem(this.selectedbillingItem[0].credit, this.selectedbillingItem[0].amount)">
                                     <tr *ngFor = "let item of this.selectedbillingItem[0].credit"
                                         style="display: flex;justify-content: space-between;">

                                         <td *ngIf="isCreditApplied(item)" class="text-left">{{'paymentDetails.TravelCredits' | translate}} ({{item.vendorReference}})</td>
                                         <td *ngIf="isCreditApplied(item)" class="text-right" style="white-space: nowrap !important;">
                                             -{{ getCreditPrice(item)| currency: getCurrencySymbol(currencyCode):
                                             "code" : '1.2-2' }}</td>
                                     </tr>
                                     <hr>
                                 </ng-container>
                               
                                 <tr style="display: flex;justify-content: space-between;">
                                     <td class="text-left total-payment-amount">{{'paymentDetails.Total' | translate}}</td>
                                     <td style="display: none;" class="text-left total-payment-amount">
                                         {{'paymentDetails.DueToday' | translate}}</td>
                                     <td class="text-right total-payment-amount">{{getTotalAmount(this.selectedbillingItem[0]) | currency:
                                         getCurrencySymbol(currencyCode): "code" : '1.2-2' }}</td>
                                 </tr>
                             </div>
                             </ng-container>
                     </div>
                     </div>