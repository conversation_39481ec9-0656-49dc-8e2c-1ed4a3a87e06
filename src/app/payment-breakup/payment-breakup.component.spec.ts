import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PaymentBreakupComponent } from './payment-breakup.component';

describe('PaymentBreakupComponent', () => {
  let component: PaymentBreakupComponent;
  let fixture: ComponentFixture<PaymentBreakupComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ PaymentBreakupComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PaymentBreakupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
