import { Component, OnInit, EventEmitter, ViewChild, Output, ChangeDetectorRef, HostListener } from '@angular/core';
import { GallopLocalStorageService } from 'src/app/gallop-local-storage.service';
import { CommonUtils } from './../util/common-utils';
import {
  AbstractControl, AsyncValidatorFn, FormArray, UntypedFormBuilder, FormControl, UntypedFormGroup, ValidationErrors, Validator, ValidatorFn,
  Validators
} from '@angular/forms';
import { Subscription, Observable, BehaviorSubject } from 'rxjs';
import { GoogleAnalyticsService } from '@hakimio/ngx-google-analytics';
import { GallopAnalyticsUtil } from 'src/app/analytics.service';
import { UserAccountService } from '../user-account.service';
import { TranslateService } from "@ngx-translate/core";
import { ToastrService, ActiveToast } from 'ngx-toastr';
import { CardInfo } from '../entity/card-info';
import { UserAccountInfo } from '../entity/user-account-info';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AddCardWidgetComponent } from '../email-booking-flow/add-card-widget/add-card-widget.component';
import { DeleteCardModelComponent } from '../email-booking-flow/delete-card-model/delete-card-model.component';
import { Constants } from '../util/constants';
import { deserialize } from '../util/ta-json/src/methods/deserialize';
import { UserInfo } from '../entity/user-info';
import { LoginService } from '../login.service';
import { DeviceDetailsService } from '../device-details.service';
import { environment } from 'src/environments/environment';
import { Role } from '../entity/employee-info';
import { GallopHttpClient } from '../shared/gallop-httpclient.service';
import { CookieService } from 'ngx-cookie-service';
import { ItemsList } from '@ng-select/ng-select/lib/items-list';
import { SearchService } from '../search.service';
import { LANGUAGE_BY_LOCALE } from '../util/locale';
import { UserCredentialsService } from '../user-credentials.service';
@Component({
    selector: 'app-profile-account-settings',
    templateUrl: './profile-account-settings.component.html',
    styleUrls: ['./profile-account-settings.component.scss'],
    standalone: false
})
export class ProfileAccountSettingsComponent implements OnInit {
  @Output() goBackEmitter = new EventEmitter();
  addCardMode = false;
  njoySpecificBuild: boolean;
  activeToast: ActiveToast<any>;
  passwordCorrect = false;
  passwordIncorrect = false;
  bankDetails:any;
  showBankForm=false;
  userAccountInfoObj: UserAccountInfo;
  fetchAccountInfoSubscription: Subscription;
  bsModalRef: BsModalRef;
  emailForm: UntypedFormGroup;
  generalSettingForm: UntypedFormGroup;
  bankDetailsForm:UntypedFormGroup;
  passwordForm: UntypedFormGroup;
  deviceSubscription1: Subscription;
  isMobile1: boolean;
  showForm = false;
  languages_locale = LANGUAGE_BY_LOCALE;
  isRequestInProgress: boolean;
  isRequestInProgress1: boolean;
  private cardToken: CardInfo;
  email = [];
  duplicateError = false;
  confirmPassword = false;
  addToSlackUrl = '';
  disconnectingSlack = false;
  languageSelected='English';
  languageOptions=[{id:'en',value:'English'},{id:'es',value:'Spanish'},{id:'fr',value:'French'},{id:'it',value:'Italian'}]
  constructor(
    private http: GallopHttpClient,
    private cookieService: CookieService,
    public userAccountInfoService: UserAccountService,
    private gallopLocalStorage: GallopLocalStorageService,
    public translateService: TranslateService,
    private modalService: BsModalService,
    public deviceDetailsService: DeviceDetailsService,
    private toastr: ToastrService,
    private ngxAnaltics:GoogleAnalyticsService,
    private userCredentials: UserCredentialsService,
    private loginService: LoginService,
    private cdRef: ChangeDetectorRef,
    private userAccountService: UserAccountService,
    public searchService: SearchService,
    private fb: UntypedFormBuilder) { }
  @ViewChild(AddCardWidgetComponent) addCardChild: AddCardWidgetComponent;
  ngOnInit() {
    this.njoySpecificBuild = this.userAccountService.isItNjoyBuild();
    this.deviceSubscription1 = this.deviceDetailsService.isMobile1().subscribe(isMobile => {
      this.isMobile1 = isMobile;
    });
    this.emailForm = this.fb.group({
      email: ['', Validators.compose([Validators.required, Validators.pattern(Constants.RGEX_EMAIL)])],
    });
    this.generalSettingForm = this.fb.group({
      locale: [navigator.language, ],
      language: ['en', ],
    });
    this.bankDetailsForm = this.fb.group({
      accountHolderName: ['', Validators.compose([Validators.required])],
      accountNumber: ['', Validators.compose([Validators.required,Validators.pattern(Constants.RGEX_ALPHANUMERIC)])],
      routingNumber : [null, Validators.compose([Validators.required,Validators.pattern(Constants.RGEX_ONLY_DIGITS)])],
    }); 
    this.passwordForm = this.fb.group({
      oldPassword: ['', Validators.compose([Validators.required])],
      newPassword: ['', Validators.compose([Validators.required])],
      confirmPassword: ['', Validators.compose([Validators.required])],
    });
    this.fetchAccountInfoSubscription = this.userAccountInfoService.userAccountInfoObjObserver$.subscribe((userAccountInfoObj) => {
      this.userAccountInfoObj = userAccountInfoObj;
      if (this.userAccountInfoObj) {
        if (this.userAccountInfoObj.userInfo.alt_emails) {
          this.email = [...this.userAccountInfoObj.userInfo.alt_emails];
        }
        if (this.bsModalRef) {
          this.bsModalRef.hide();
          this.userAccountInfoService.deletingCard = false;
        }
      }
      this.getGeneralSetting();
      this.getListOfBank();
      this.unsetCardMode();
    });
    this.emailForm.get('email').valueChanges.subscribe(value => {
      this.duplicateError = this.isEmailExist(value);
    }
    );
    this.passwordForm.get('confirmPassword').valueChanges.subscribe(value => {
      if (value === this.passwordForm.controls['newPassword'].value) {
        this.confirmPassword = false;
      } else if (!value) {
        this.confirmPassword = false;
      } else {
        this.confirmPassword = true;
      }
    });
    this.addToSlackUrl = environment.urlForSlackConnect + '?userid=' + this.userAccountInfoService.getAccountInfo().userInfo.email;
  }
  onLocaleChange(){

  }
  getGeneralSetting(){
    this.searchService.getGeneralSetting().subscribe(resp =>{
      if(resp && resp.status==='success'){
        if(resp.data && resp.data.locale){
          this.generalSettingForm.controls['locale'].setValue(resp.data.locale)
        }
        if(resp.data && resp.data.language){
          this.generalSettingForm.controls['language'].setValue(resp.data.language);
          this.useLanguage(resp.data.language);
        }
       
      }
    })
  }
  useLanguage(language: string) {
    this.translateService.use(language);
    this.userCredentials.setLang(language);
    const oldLang: string = localStorage.getItem('selectedLanguage');
    localStorage.setItem('selectedLanguage', language);
    if (oldLang) {
      localStorage.setItem('oldLanguage', oldLang);
    }
    GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics, 'languageChanged', 'WebSearchUI');
  }
  
  setCardMode() {
    this.addCardMode = true;
  }
  statusCheck() {
    if (this.userAccountInfoService
      && this.userAccountInfoService.getAccountInfo()
      && this.userAccountInfoService.getAccountInfo().connectedToSlack != null
    ) {
      if (this.isAdmin()) {
        CommonUtils.checkSlackStatusFromRemote(this.http, this.userAccountInfoService, environment.apiForSlackStatusCheck);
      }
    }
  }
  maxLengthArray(min: number) {
    return (c: AbstractControl): { [key: string]: any } => {
      if (c.value) {
        if (c.value.length == min)
          return null;

        return { 'minLengthArray': { valid: false } };
      }
    }
  }
  returnAccountNumber(str){
    var string = str,
    replaced=  string.slice(0).replace(/.(?=....)/g, '*');

return replaced
  }
  saveBankDetails(){
    if(this.bankDetailsForm.invalid){
      this.bankDetailsForm.markAllAsTouched();
      return;
    }
    this.isRequestInProgress1=true;
  this.userAccountService.getBankDetailsSaving(this.bankDetailsForm.value).subscribe(resp=>{
    if(resp && resp.success){
      this.isRequestInProgress1=false;
      this.showBankForm=false;
      this.toastr.success(this.translateService.instant('profilePage.Profilesavedsuccessfully').toString());
      this.getListOfBank();
    }else{
      this.isRequestInProgress1=false;
    }
  })
  }
  confirmDeleteBankAccount(){
    let bankObj={};
    bankObj['bankAccountId']= this.bankDetails.bankAccountId;
    this.userAccountService.removeBankDetails(bankObj).subscribe(resp=>{
      if(resp && resp.success){
        this.toastr.success(this.translateService.instant('profilePage.Profilesavedsuccessfully').toString());
        this.getListOfBank();
      }
    })
  }
  getListOfBank(){
  this.userAccountService.getBankList().subscribe(resp=>{
    if(resp && resp.success){
      
      if(resp.data && Object.keys(resp.data).length !== 0){
        this.bankDetails = resp.data;
      }else{
        this.bankDetails =null;
      }
    }
  })
  }
  isRequestInProgress2 =false
  saveGeneralSetting(){
    let obj=this.generalSettingForm.value;
    this.isRequestInProgress2 =true
    this.searchService.saveGeneralSetting(obj).subscribe(resp =>{
      if(resp && resp.status==='success'){
        this.isRequestInProgress2 =false;
        this.toastr.success(this.translateService.instant('setting.Settingssavedsuccessfully').toString());
        setTimeout(() => {
          this.useLanguage(obj.language) 
        }, 100);
      
      }
    })
  }
  validateChangePasswordForm() {
    if (!this.passwordCorrect) return;
    if (this.passwordForm.valid) {
      if (this.passwordForm.controls['confirmPassword'].value === this.passwordForm.controls['newPassword'].value) {
        this.confirmPassword = false;
        this.isRequestInProgress1 = true;
        let oldPass = this.passwordForm.controls['oldPassword'].value;
        let newPass = this.passwordForm.controls['newPassword'].value;
        const userid = this.userAccountService.getUserEmail();
        const sToken = this.userAccountService.getSToken();
        this.loginService.resetPasswordFromProfileRequest(userid,sToken, oldPass, newPass).subscribe(resp => {
          if (resp.success) {
            this.toastr.success(this.translateService.instant('Account.Passwordchangedsuccessfully'));
            this.isRequestInProgress1 = false;
            this.passwordCorrect = false;
            this.passwordForm.controls['oldPassword'].setValue('');
            this.passwordForm.controls['newPassword'].setValue('');
            this.passwordForm.controls['confirmPassword'].setValue('');
            this.passwordForm.controls['oldPassword'].clearValidators();
            this.passwordForm.controls['oldPassword'].updateValueAndValidity();
            this.passwordForm.controls['newPassword'].clearValidators();
            this.passwordForm.controls['newPassword'].updateValueAndValidity();
            this.passwordForm.controls['confirmPassword'].clearValidators();
            this.passwordForm.controls['confirmPassword'].updateValueAndValidity();
          }else if(!resp.success){
            if(resp && resp.error_message){
              this.toastr.error(resp.error_message);
              this.isRequestInProgress1 = false;
            }
          }
        }, error => {
          if (error.status === 403) {
            this.toastr.error('Password incorrect!!');
          }
        });
      }
      else {
        this.confirmPassword = true;
      }
    } else {
      this.passwordForm.controls['oldPassword'].markAsTouched();
      this.passwordForm.controls['newPassword'].markAsTouched();
      this.passwordForm.controls['confirmPassword'].markAsTouched();
    }
  }
  getEmailList(): Array<any> {
    if (this.userAccountInfoObj) {
      if (this.userAccountInfoObj.userInfo.alt_emails) {
        return this.userAccountInfoObj.userInfo.alt_emails;
      }
    }
  }
  submitInfo() {
    let p = this.emailForm.value;
    if (this.isEmailExist(p.email)) {
      this.showError1();
    } else if (this.emailForm.valid) {
      this.saveAndUpdateProfileInfo(this.getUpdatedPersonalInfoData(), 'savePersonalInfoBtn');
    } else {
      this.emailForm.controls['email'].markAsTouched();
    }
  }
  showError1() {
    if (this.activeToast) {
      this.toastr.remove(this.activeToast.toastId);
    }
    this.activeToast = this.toastr.success('Error!', 'Duplicate Email');
  }
  private getUpdatedPersonalInfoData(): UserInfo {
    let p = this.emailForm.value;

    let userInfoDTO = this.userAccountInfoService.getAccountInfo().userInfo;
    if (!userInfoDTO.alt_emails) {
      userInfoDTO.alt_emails = [];
    }
    userInfoDTO.alt_emails.push(p.email);
    return userInfoDTO;
  }
  isEmailExist(email): boolean {
    if (this.userAccountInfoObj) {
      if (this.userAccountInfoObj.userInfo.alt_emails) {
        return this.userAccountInfoObj.userInfo.alt_emails.indexOf(email) > -1;
      } else {
        return false;
      }
    }
  }
  confirmDeleteEmail(Index) {
    this.saveAndUpdateProfileInfo(this.getDeletePersonalInfoData(Index), 'savePersonalInfoBtn');
  }
  getDeletePersonalInfoData(index): UserInfo {
    let userInfoDTO = this.userAccountInfoService.getAccountInfo().userInfo;
    let emailDelete = userInfoDTO.alt_emails[index];
    userInfoDTO.alt_emails = userInfoDTO.alt_emails.filter(obj => obj !== emailDelete);
    return userInfoDTO;
  }
  private saveAndUpdateProfileInfo(userInfoDTO: UserInfo, saveButtonId: string) {
    this.isRequestInProgress = true;
    this.showProgress();
    this.fetchAccountInfoSubscription = this.userAccountInfoService.saveAccountInfo(
      userInfoDTO).subscribe(res => {

        this.isRequestInProgress = false;
        if (res.success == true) {
          this.showForm = false;
          this.emailForm.reset();
          this.showSuccess();
          if (res.data) {
            let userAccountInfoObj: UserAccountInfo = deserialize(res.data, UserAccountInfo);
            this.userAccountInfoService.setAccountInfo(userAccountInfoObj);
          }

        } else if (res.status == 'error') {
          this.showError();
        } else {
          this.showError();
        }
      }, error => {
        this.showError();
      });
  }
  lowercharMissing=false;
numbercharMissing=false;
uppercharMissing=false;
specialcharMissing=false;
showPasswordRequirements=false;
hidePasswordRequirementPopup(){
  if ($('.passwordConstraints').css('display') !== 'none') {
    $('.passwordConstraints').hide();
    }
}
isPasswordValid(item){
  if(item && item.length >0 ){
    if(this.lowercharMissing || this.uppercharMissing || this.numbercharMissing || this.specialcharMissing || item.length <8){
      if ($('.passwordConstraints').css('display') == 'none') {
        $('.passwordConstraints').show();
        }
    }else{
      if ($('.passwordConstraints').css('display') !== 'none') {
        $('.passwordConstraints').hide();
        }
    }
  }else if(item && item.length >=8){
    if(!this.lowercharMissing && !this.uppercharMissing && !this.numbercharMissing && !this.specialcharMissing){
      if ($('.passwordConstraints').css('display') !== 'none') {
        $('.passwordConstraints').hide();
        }
    }
  }

}
public conditionValidator(){
  return (c: AbstractControl): { [key: string]: any } => {
    if (c.value) {
      if (c.value=== this.passwordForm.controls['oldPassword'].value)

      return { 'passwordError': { valid: false } };
    }
    return null;
  }
}
  public  strong(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: boolean } | null => {
    let hasNumber = /\d/.test(control.value);
    let hasUpper = /[A-Z]/.test(control.value);
    let hasLower = /[a-z]/.test(control.value);
    let hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(control.value);
  //   
    
    const valid = (hasNumber && hasUpper && hasLower && hasSpecial && (control.value.length>=8));
    if(valid){
      this.showPasswordRequirements =false;
      }
    if(!hasLower){
      this.lowercharMissing =true;
    }else{
      this.lowercharMissing =false;
    }

    if(!hasUpper){
      this.uppercharMissing =true;
    }else{
      this.uppercharMissing =false;
    }

    if(!hasNumber){
      this.numbercharMissing =true;
    }else{
      this.numbercharMissing =false; 
    }

    if(!hasSpecial){
      this.specialcharMissing =true;
    }else{
      this.specialcharMissing =false;
    }
    if (!valid) {
        return { 'pattern': true };
    }
   
  //  
    return null;
  }
}
  isPasswordCorrect(text) {
    if (text !== '') {
      this.passwordForm.controls['oldPassword'].setValidators([Validators.required]);
      this.passwordForm.controls['oldPassword'].updateValueAndValidity();
      this.passwordForm.controls['newPassword'].setValidators([Validators.required,this.strong(),this.conditionValidator()]);
      this.passwordForm.controls['newPassword'].updateValueAndValidity();
      this.passwordForm.controls['confirmPassword'].setValidators([Validators.required]);
      this.passwordForm.controls['confirmPassword'].updateValueAndValidity();
      const userid = this.userAccountService.getUserEmail();
      this.passwordIncorrect = false;
      this.loginService.loginRequest(userid, text, '', '').subscribe(res => {
        if (res.success && res.data) {
          this.passwordCorrect = true;
          this.passwordIncorrect = false;
          this.cdRef.detectChanges();
        } else {
          this.passwordCorrect = false;
          this.passwordIncorrect = true;
        }
      });
    } else {
      this.passwordForm.controls['oldPassword'].clearValidators();
      this.passwordForm.controls['oldPassword'].updateValueAndValidity();
      this.passwordForm.controls['newPassword'].clearValidators();
      this.passwordForm.controls['newPassword'].updateValueAndValidity();
      this.passwordForm.controls['confirmPassword'].clearValidators();
      this.passwordForm.controls['confirmPassword'].updateValueAndValidity();
    }
  }
  showSuccess() {
    this.toastr.remove(this.activeToast.toastId);
    this.activeToast = this.toastr.success(this.translateService.instant('profilePage.Profilesavedsuccessfully').toString());
  }
  showProgress() {
    if (this.activeToast) {
      this.toastr.remove(this.activeToast.toastId);
    }
    this.activeToast = this.toastr.info(this.translateService.instant('profilePage.Pleasewait').toString(), this.translateService.instant('profilePage.Saving').toString());
  }

  showError() {
    if (this.activeToast) {
      this.toastr.remove(this.activeToast.toastId);
    }
    this.activeToast = this.toastr.success('Error!', 'Unknown error while saving profile');
  }

  isLoggedIn(): boolean {
    return this.userAccountInfoService.isLoggedIn();
  }
  public getCardList(): Array<CardInfo> {
    if (this.userAccountInfoObj.cardList != null) {
      return this.userAccountInfoObj.cardList.card_list;
    }
  }
  openCard() {
    this.showForm = true;
  }
  openBankCard(){
    this.showBankForm =true;
  }
  openEditBankCard(){
    this.bankDetailsForm.controls['accountHolderName'].setValue(this.bankDetails.accountHolderName);
    this.bankDetailsForm.controls['accountNumber'].setValue(this.bankDetails.accountNumber);
    this.bankDetailsForm.controls['routingNumber'].setValue(this.bankDetails.routingNumber);
    this.showBankForm =true;
  }
  cancelBankEdit() {
    this.showBankForm = false;
    this.bankDetailsForm.reset();

  }
  cancelEdit() {
    this.showForm = false;
    this.emailForm.reset();

  }
  addCardFlow: boolean = false;
  private cardTokenData: any;
  public handleBackFromAddCard(data: any) {
    if (!data) {
      this.unsetCardMode();
      return;
    }


    let tokenData = JSON.parse(data);
    if (tokenData && tokenData.type === 'newCardAdded') {

      // this.userAccountInfoService.fetchUserAccountInfo(this.emailId, this.sToken);
      let cardTokens: any = tokenData.tokens;
      if (cardTokens && cardTokens.error && cardTokens.error.length > 0) {
        // this.toastr.error(cardTokens.error, 'Card Error!');
        this.addCardChild.setAddCardProgress(false);
        this.addCardChild.setErrorMessage('');
      }

      else if (cardTokens.token && cardTokens.gToken) {
        this.cardTokenData = cardTokens;
        GallopAnalyticsUtil.trackActionWithCategory(this.ngxAnaltics,
          'cardTokenCreated', 'WebSearchUI'
        );
        this.userAccountInfoService.requestSaveCardInfo(cardTokens.token, cardTokens.gToken).subscribe(res => {
          if (res.status === 'success') {
            this.addCardFlow = true;
            this.userAccountInfoService.fetchUserAccountInfo(false);
            // this.unsetCardMode();
          } else if (res.status === 'CARDERROR') {
            // this.toastr.error(res.message, 'Card Error!');
            this.addCardChild.setErrorMessage(res.message);
            this.addCardChild.setAddCardProgress(false);

          } else {
            // this.toastr.error(res.message, 'Error!');
            this.addCardChild.setErrorMessage(res.message);
            this.addCardChild.setAddCardProgress(false);
          }
        });

      } else {
        // this.addCardChild.setErrorMessage(this.translateService.instant('paymentDetails.UnknownErrorPleasetryagain').toString());
        // this.addCardChild.setAddCardProgress(false);
      }
    }
  }
  unsetCardMode() {
    this.addCardMode = false;
  }
  public confirmCardDeleteModel(cardIndex: number) {
    this.bsModalRef = this.modalService.show(DeleteCardModelComponent, {
      initialState: {
        title: this.translateService.instant('paymentDetails.Deletingthecardquestion'),
        // message: This payment method will not be displayed in your list of payment options',
        message: this.translateService.instant('paymentDetails.deleteCardWaringMsg'),
        yesButtonSubText: this.translateService.instant('paymentDetails.Delete'),
        deleteCard: true
      }, backdrop: true, keyboard: false, ignoreBackdropClick: true
    });
    this.bsModalRef.content.onClose.subscribe(result => {

      if (result) { this.markCardDeleted(cardIndex); }
    });
  }
  public markCardDeleted(cardIndex: number) {


    if (this.userAccountInfoObj && this.userAccountInfoObj.cardList) {
      this.userAccountInfoService.requestDeleteCard(this.userAccountInfoObj.cardList.card_list[cardIndex].id).subscribe(res => {

        if (res.status === 'success') {
          this.userAccountInfoService.fetchUserAccountInfo(false);

        } else {
          this.toastr.error(res.message, 'Attention!');
          if (this.bsModalRef) {
            this.bsModalRef.hide()
          }
        }
      });
    }
  }
  public isAdmin(): boolean {
    if (this.userAccountInfoService && this.userAccountInfoService.getAccountInfo()
      && this.userAccountInfoService.getAccountInfo().userInfo
      && this.userAccountInfoService.getAccountInfo().userInfo.employeeInfo
    ) {
      return this.userAccountInfoService.getAccountInfo().userInfo.employeeInfo.role === Role.ADMIN;
    }
    return false;
  }

  public showSlackButton(): boolean {
    //.connectedToSlack);
    if (this.userAccountInfoService
      && this.userAccountInfoService.getAccountInfo()
      && this.userAccountInfoService.getAccountInfo().connectedToSlack != null
    ) {
      return true;
    }
    return false;
  }

  public isConnectedToSlack(): boolean {
    if (this.userAccountInfoService && this.userAccountInfoService.getAccountInfo()
      && this.userAccountInfoService.getAccountInfo().connectedToSlack
    ) {
      return true;
    }
    return false;
  }
  public connectToSlack() {
    this.userAccountInfoService.connectingToSlack = true;
    this.addToSlackUrl += '&' + Constants.PARAM_STOKEN + '=' + this.userAccountService.getSToken();;
    this.addToSlackUrl += '&' + Constants.PARAM_APP_VERSION + '=' + environment.appVersion;
    this.addToSlackUrl += '&' + Constants.PARAM_APPSTORE_VERSION + '=' + environment.appStoreVersionName;
    this.addToSlackUrl += '&' + Constants.PARAM_APP_BUILD_NUMBER + '=' + environment.appStoreBuildNumber;
    this.addToSlackUrl += '&' + Constants.PARAM_APP_PLATFORM + '=' + environment.platform;
    this.addToSlackUrl += '&' + Constants.PARAM_RS_CLIENT_COOKIE + '=' + this.cookieService.get('RS-CLIENT');
    this.addToSlackUrl += '&' + Constants.PARAM_OTA_BUILD_NUMBER + '=' + environment.otaBuildNumber;
    window.open(this.addToSlackUrl, '_self');
  }
  public disconnectSlack() {
    this.userAccountInfoService.connectingToSlack = false;
    this.makeDisconnectSlackRequest().subscribe(response => {
      this.disconnectingSlack = false;
      if (response && response.success) {
        this.userAccountInfoService.getAccountInfo().connectedToSlack = false;
      }
    });
  }

  private makeDisconnectSlackRequest(): Observable<any> {
    this.disconnectingSlack = true;
    return this.http.get(environment.apiForSlackDisconnect);
  }
  @HostListener('document:visibilitychange', ['$event'])
  visibilitychange() {
    if (this.userAccountInfoService
      && this.userAccountInfoService.getAccountInfo()
      && this.userAccountInfoService.getAccountInfo().connectedToSlack != null
    ) {
      this.userAccountInfoService.connectingToSlack = false;
      if (document.hidden) {
      } else {
        if (!this.isConnectedToSlack()) {
          CommonUtils.checkSlackStatusFromRemote(this.http, this.userAccountInfoService, environment.apiForSlackStatusCheck);
        }
      }
    }
  }
  getSubHeading() {
    return this.translateService.instant('policy.wewillsendyourUSDpayoutstothisbankaccount').toString()
      .replace('__CURRENCY__', this.userAccountInfoService.getAccountInfo().userInfo.currency);
  }
}
