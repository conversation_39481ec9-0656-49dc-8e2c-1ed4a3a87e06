import { Component, OnInit } from '@angular/core';
import { ClientConfiguration } from '../client-config.service';

@Component({
    selector: 'app-profile-help',
    templateUrl: './profile-help.component.html',
    styleUrls: ['./profile-help.component.scss'],
    standalone: false
})
export class ProfileHelpComponent implements OnInit {

  constructor(private clientConfig: ClientConfiguration) { }

  ngOnInit() {
  }
  getBrandName() {
    return this.clientConfig.brandName;
  }
}
