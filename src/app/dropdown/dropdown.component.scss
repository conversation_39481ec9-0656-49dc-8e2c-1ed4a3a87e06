
.tagDrodpdown{
  width: 303px;
  margin-right: 10px;
}

.trip-tag-alert{
  color: red;
  font-size: 12px;
  position: absolute;
  left: 10px;
  z-index: 2;
}
.tripPayment{
  display: flex;
  margin-bottom: 35px;
}
.input.unified-input {
  display: flex;
  align-items: center;
  height: 32px;
  gap: 4px;
}
:host ::ng-deep {
  .input2 .ng-dropdown-panel .ng-dropdown-panel-items  {
    // 300px !important; // TODO: Fix this
     max-height: 240px !important
}
ng-select.consistent-select,
.ng-select.consistent-select .ng-select-container {
  height: 32px !important;
  min-height: 32px !important;
  line-height: 32px !important;
}

.input2 .ng-select .ng-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
    #chartDepartment .input2 .ng-select.ng-select-single .ng-select-container .ng-clear-wrapper {
      bottom: 18px !important;
      font-size: 28px !important;
      color: var(--dark-bg-color) !important;
      display: inline-block !important;
      left: 2px;
    }
  
    #chartDepartment .input2 .ng-select .ng-select-container .ng-value-container {
      -ms-flex-align: stretch;
      align-items: stretch;
      padding: 0.4375em 0;
      border-top: 0.14375em solid transparent !important;
    }
  
  .input2 .ng-select .ng-select-container .ng-value-container .ng-placeholder {
      position: absolute;
      color: rgba(0, 0, 0, 0.54);
      -ms-transform-origin: 0 0;
      transform-origin: 0 0;
      transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), color 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      top: 4px !important;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 280px;
      white-space: nowrap;
    }
  
   .ng-dropdown-panel {
      left: 50% !important;
      -ms-transform: translate(-50%, 0);
      transform: translate(-50%, 0);
      max-height: 300px;
      position: absolute !important;
      min-width: 95% !important;
      top: auto !important;
      right: auto !important;
      max-width: 100% !important;
    }
    .ng-dropdown-panel .ng-dropdown-panel-items {
      max-height: 240px !important;
    }  
    .input2 .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
      font-size: 16px !important;
      font-family: var(--globalFontfamilyr) !important;
      min-height:3em !important;
      line-height: 2em !important;
      padding-left: 20px !important;
      height: auto !important;
      border-radius: 8px !important;
      word-break: break-all !important;
      white-space:normal !important;
      display: block !important;
    }
  
    .input2 .ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {
  
      font-size: 16px !important;
    }
  
    .input2 .ng-select .ng-select-container {
      font-size: 16px !important;
      bottom: 16px !important;
      left: -15px !important;
      white-space: nowrap;
    }
  
    .input2 .ng-clear-wrapper {
      display: none !important;
    }
  
    .input2 .ng-select span {
      box-sizing: border-box;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  
    .input2 .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
      display: none !important;
    }
  
    .input2 .ng-select.ng-select-container.ng-value-container.ng-input {
      position: fixed !important;
      top: -14px;
    }
    .input2 .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
      position: absolute;
      left: 0;
      width: 100%;
      top: 12px !important;
  }
    .input, .ng-select, .ng-select-container {
     
      
    }
    .input2 .ng-select .ng-has-value .ng-placeholder, .ng-select.ng-select-opened .ng-placeholder {
      transform: translateY(-1.28125em) scale(0.75) perspective(100px) translateZ(0.001px);
      display: none !important;
  }
  .input2 .ng-select.ng-select-single .ng-select-container .ng-value-container,
    .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
      max-width: 140px !important;
  overflow: hidden !important;
  position: initial !important;
      cursor: pointer;
    }
  
    .input2 .ng-dropdown-panel.ng-select-bottom {
      border: 1px solid var(--dark-bg-color) !important;
    }
  
    .input2 .ng-select-container {
      border: none !important;
  
      &:after {
        display: none;
      }
    }
  }
  .input2 {
    cursor: pointer;
    text-align: center;
    width: 100%;
    padding-top: 5px;
    padding-left: 25px;
    background: #FCFCFC;
    border: 1px solid var(--dark-bg-color);
    border-radius: 8px;
    height: 32px;
    text-align: left;
    font-size: 14px;
  }
  
  .down-arrow {
    left: auto;
    position: absolute;
    top: 10px;
    right: 10px;
    bottom: auto;
    pointer-events: none;
  }
  .labelClass{
    word-break: break-all;
    overflow: hidden !important;
    display: flex;
    font-weight: normal;
    height:auto;
    align-items: center;
}
.labelDiv {
  white-space: nowrap;
  text-overflow: ellipsis !important;
  overflow: hidden !important;
  display: inline-block;
  width: calc(100% - 80px);
    min-width: 120px;
  position: relative;
  top: -70px;
}
:host ::ng-deep {
  
  #chartDepartment .input2 .ng-select.ng-select-single .ng-select-container .ng-value-container,  .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
  position: relative !important;
  overflow: hidden !important;
  top: -2px !important;
}
}
.crossTag {
  color: var(--dark-bg-color) !important;
  cursor: pointer;
  position: relative;
  top: -30px;
  right: 0px;
  float: right;
  left: -35px;
}
.tripDiv{
  display: flex;
  width: 30%;
}
.tagsBox{
  height:32px;margin-top: 30px;display:inline-block;position: relative;
}
.tagSetName{
  overflow: hidden;
}

.tagSetName-report{
  overflow: hidden;
  height: 50px;
  display: flex;
  align-items: flex-end;
}

@media(max-width:1024px){
  
  .labelDiv {
    white-space: nowrap;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    display: inline-block;
    max-width: 70px;
    min-width: 70px;
    top: -70px;
    width: 100%;
  }
  .tripDiv{
    display: block;
    width: 100%;
  }
}
@media(max-width:991px){
  :host ::ng-deep {
  
    #chartDepartment .input2 .ng-select.ng-select-single .ng-select-container .ng-value-container,
    .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
      overflow: visible !important;
      position: relative !important;
      top: -5px !important;
    }
    .input2 .ng-select span {
      box-sizing: border-box;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      max-width: 250px !important;
      display: inline-block;
  }
  .input2 .ng-dropdown-panel {
    left: 50% !important;
    -ms-transform: translate(-50%, 0);
    transform: translate(-50%, 0);
    max-height: 300px;
    position: absolute !important;
    min-width: 100% !important;
    top: auto !important;
    right: auto !important;
    max-width: 100% !important;
  }

    #chartDepartment .input2 .ng-select .ng-select-container .ng-value-container .ng-placeholder {
      position: absolute;
      color: rgba(0, 0, 0, 0.54);
      -ms-transform-origin: 0 0;
      transform-origin: 0 0;
      transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), color 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      top: 8px !important;
    }
   
  }
 
  .down-arrow {
    left: auto;
    position: absolute;
    top: 10px;
    right: 10px;
    bottom: auto;
  }
  .tagDrodpdown{
    width: 250px;
    margin-right: 10px;
  }
  .labelDiv {
    white-space: nowrap;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    display: inline-block;
    max-width: 70px;
    min-width: 70px;
    top: -70px;
    width: 100%;
  }
 
}
@media(max-width:900px){
  .labelDiv {
    white-space: nowrap;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    display: inline-block;
    max-width: 100px;
    min-width: 100px;
    position: relative;
    top: -70px;
  }
  .labelClass{
    white-space: break-all;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 200px;
    display: flex;
    align-items: center;
    font-weight: normal;
}
}
@media(max-width:768px){
  .labelDiv {
    white-space: nowrap;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    display: inline-block;
    max-width: 100px;
    min-width: 100px;
    position: relative;
    top: -70px;
  }
  .labelClass{
    white-space: break-all;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 200px;
    display: flex;
    font-weight: normal;
}
  :host ::ng-deep {
  
    #chartDepartment .input2 .ng-select.ng-select-single .ng-select-container .ng-value-container,
    .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
      overflow: visible !important;
      position: relative !important;
      top: -5px !important;
    }
    .input2 .ng-select span {
      box-sizing: border-box;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      max-width: 250px !important;
      display: inline-block;
  }
    #chartDepartment .input2 .ng-select .ng-select-container .ng-value-container .ng-placeholder {
      position: absolute;
      color: rgba(0, 0, 0, 0.54);
      -ms-transform-origin: 0 0;
      transform-origin: 0 0;
      transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), color 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      top: 8px !important;
    }
    .input2 .ng-dropdown-panel {
      left: 50% !important;
      transform: translate(-50%, 0);
      max-height: 300px;
      position: absolute !important;
      min-width: 100% !important;
      top: auto !important;
      right: auto !important;
    }
  }
  .tagDrodpdown{
    width: 250px;
    margin-right: 10px;
  }
}
@media (max-width: 991px) {
  .tripPayment{
    display: flex;
    flex-direction: column;
    row-gap: 40px;
    margin-bottom: 0px;
    min-height: 250px;
  }
}
@media (max-width: 767px) {
  .down-arrow {
    left: auto;
    position: absolute;
    top: 10px;
    right: 20px;
    bottom: auto;
  }
  .crossTag {
    color: var(--dark-bg-color) !important;
    cursor: pointer;
    position: relative;
    top: -30px;
    right: 0px;
    float: right;
    left: -45px;
  }
    :host ::ng-deep {
  
      #chartDepartment .ng-select.ng-select-single .ng-select-container .ng-value-container,
      .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
        overflow: visible !important;
        position: relative !important;
        top: -5px !important;
      }
      .ng-select span {
        box-sizing: border-box;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        max-width: 250px !important;
        display: inline-block;
    }
      #chartDepartment .ng-select .ng-select-container .ng-value-container .ng-placeholder {
        position: absolute;
        color: rgba(0, 0, 0, 0.54);
        -ms-transform-origin: 0 0;
        transform-origin: 0 0;
        transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), color 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        top: 8px !important;
      }
      .input2 .ng-dropdown-panel {
        left: 50% !important;
        transform: translate(-50%, 0);
        max-height: 300px;
        position: absolute !important;
        min-width: 100% !important;
        top: auto !important;
        right: auto !important;
      }
    }
    .tagDrodpdown{
      width: 100%;
      margin-right: 0px;
    }
    .labelDiv {
        white-space: nowrap;
        text-overflow: ellipsis !important;
        overflow: hidden !important;
        display: inline-block;
        max-width: 190px;
        min-width: 190px;
        top: -70px;
        width: 100%;
      }
      .tagsBox{
        height:32px;margin-top: 10px;display:inline-block;position: relative;
      }

      .tagSetName-report{
        display: block;
        height: auto;
      }
}
@media(max-width:400px){
    .labelDiv {
      white-space: nowrap;
      text-overflow: ellipsis !important;
      overflow: hidden !important;
      display: inline-block;
      max-width: 150px;
      min-width: 150px;
      top: -70px;
      width: 100%;
    }
    .labelClass{
      word-break: break-all;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      max-width: 200px;
      display: flex;
      font-weight: normal;
    }
  }  
 