@import "../../variables.scss";

.back {
  height: 19px;
  width: auto;
  color: #fff !important;
  font-family: "apercu-r";
  font-size: 16px;
  letter-spacing: 0.69px;
  line-height: 20px;
}

.wingsPresent {
  width: 13px;
  background-color: #D8D8D8;
  margin-right: 3px;
}

.wingsNotPresent {
  width: 13px;
  margin-right: 3px;
}

.wingsPresent1 {
  width: 13px;
  background-color: #D8D8D8;
  margin-left: 3px;
}

.wingsNotPresent1 {
  width: 13px;
  margin-left: 3px;
}

.container1 {
  background-color: #FCFCFC;
  width: auto !important;
  height: auto !important;
  text-align: center !important;
  align-items: center !important;
  margin-left: 0px !important;
  margin-right: 0px !important;
  max-width: none !important;
}

.booking-history-heading-link {
  line-height: 10px;
  margin-right: 18px;
}

.top-strip {
  background: $themeColor2;
  float: left;
  width: 100%;
  height: 43px;
}

.booking-history-detail-heading {
  float: left;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 2px 30px;
}

.seat-row-internal {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.filter-row1 {
  display: flex;
  flex-wrap: nowrap;
  //    justify-content: space-between;
}

.filter-row2 {
  display: flex;
  flex-wrap: nowrap;
  border: 1px solid #ED7878;
  height: 40px;
  padding-bottom: 2px;
  padding-top: 2px;
  margin-top: 2px;
  margin-bottom: 2px;
  background-color: rgba(237, 120, 120, 0.2);
  //    justify-content: space-between;
}

.card-div-inner {
  float: none !important;
}

.shadow {
  margin-top: 10px;
  overflow-x: scroll !important;
  padding-bottom: 200px !important;
}

.fa-square-o.item {
  height: 33px;
  width: 33px;
  border-radius: 2px;
  background-color: #D8D8D8
}

.fa-window-close-o {
  font-size: 33px;
}

.fa-square-o:before {
  content: none !important;
}

.fa-square-o.item1 {
  height: 33px;
  width: 33px;
  border-radius: 2px;
  background-color: var(--button-bg-color) !important;

}

.fa-square-o.item2 {
  height: 33px;
  width: 33px;
  border: 1px solid #ED7878;
  border-radius: 2px;
  background-color: rgba(237, 120, 120, 0.2);

}

.fa-square-o.item6 {
  height: 33px;
  width: 33px;
  border-radius: 2px;
  background-color: #D8D8D8;

}

.shape {
  position: absolute;
  left: 14px;
  height: 22px;
  top: 6px;
  width: 22px;
}

.fa-square-o.item3 {
  height: 33px;
  width: 19px;
  border: 1px solid #979797;
  background-color: #D8D8D8;

}

.fa-square-o.item4 {
  height: 21px;
  width: 12px;
  border: none;
  background-color: #D8D8D8;

}

.name {
  height: 14px;
  width: auto;
  color: #413E3B;
  font-family: "apercu-r";
  font-size: 14px;
  line-height: 15px;
}

.name1 {
  height: 10px;
  position: absolute;
  width: 100%;
  color: #fff;
  font-family: "apercu";
  font-size: 12px;
  line-height: 10px;
  left: 0;
  text-align: center;
  cursor: default;
  padding-top: 11px;
}

.fa-square-o.item5 {
  height: 33px;
  width: 33px;
  border-radius: 2px;
  background-color: var(--hyperlink-color);
}

.btn-secondary {
  height: 40px;
  width: auto;
  background-color: var(--button-bg-color) !important;
  color: var(--button-font-color);
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
  margin-top: 15px;
  margin-bottom: 0px;
  border: none;
  white-space: normal !important;
}

.btn-secondary1 {
  height: 40px;
  width: auto;
  background-color: var(--button-bg-color) !important;
  color: var(--button-font-color);
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
  margin-top: 15px;
  margin-bottom: 0px;
  border: none;
  white-space: normal !important;
}
.modal-header{
  background-color: var(--hyperlink-color);
}

.btn-secondary2 {
  height: 40px;
  width: auto;
  background-color: transparent;
  color: var(--hyperlink-color);
  box-shadow: none;
  margin-top: 15px;
  margin-bottom: 0px;
  border: none;
  white-space: normal !important;
}

.rowNo {
  width: 15px;
  font-size: 13px;
  padding-top: 6px;
  min-width: 15px;
}

.add2 {
  height: 20px;
  width: auto;
  font-family: "apercu-r";
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 0.4px;
  line-height: 25px;
  text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}

.add {
  height: 24px;
  width: auto;
  font-family: "apercu-r";
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 0.4px;
  line-height: 25px;
  text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}

.add1 {
  height: 24px;
  width: 168px;
  color: #2D57FA;
  font-family: "apercu-r";
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 0.4px;
  line-height: 25px;
  text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}

@media(max-width:767px) {
  .rowNo {
    width: 15px;
    font-size: 14px;
    padding-top: 0px;
  }

  .section {
    margin-top: 0 !important;
    text-align: center;
    align-items: center;
    margin: 0;
  }

  .filter-row2 {
    display: flex;
    flex-wrap: nowrap;
    border: 1px solid #ED7878;
    height: 27px;
    margin-top: 2px;
    margin-bottom: 2px;
    background-color: rgba(237, 120, 120, 0.2);
    justify-content: space-between;
  }

  .btn-secondary2 {
    height: 40px;
    width: auto;
    background-color: transparent;
    color: var(--hyperlink-color);
    box-shadow: none;
    margin-top: 0px;
    margin-bottom: 0px;
    border: none;
    white-space: normal !important;
  }

  .btn-secondary {
    height: auto !important;
    width: auto !important;
    background-color: var(--button-bg-color) !important;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    margin-top: 15px;
    margin-bottom: 15px !important;
    border: none;
    white-space: normal !important;
  }

  .btn-secondary1 {
    height: auto !important;
    width: auto !important;
    background-color: var(--button-bg-color) !important;
    color: var(--button-font-color);
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14);
    margin-top: 15px;
    margin-bottom: 15px !important;
    border: none;
    white-space: normal !important;
  }

  .add2 {
    height: 14px;
    width: auto !important;
    font-family: "apercu-r";
    font-size: 10px;
    font-weight: bold;
    letter-spacing: 0.3px;
    line-height: 25px;
    text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
  }

  .add {
    height: 14px !important;
    width: auto !important;
    color: #2D57FA;
    font-family: "apercu-r";
    font-size: 10px !important;
    font-weight: bold;
    letter-spacing: 0.1px !important;
    line-height: 15px !important;
    text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
  }

  .add1 {
    height: 14px !important;
    width: auto !important;
    color: #2D57FA;
    font-family: "apercu-r";
    font-size: 10px !important;
    font-weight: bold;
    letter-spacing: 0.1px !important;
    line-height: 15px !important;
    text-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
  }

  .fa-square-o.item {
    height: 21px;
    width: 21px;
    border-radius: 2px;
    background-color: #D8D8D8;
  }

  .fa-square-o.item6 {
    height: 21px;
    width: 21px;
    border-radius: 2px;
    background-color: #D8D8D8;

  }

  .shape {
    position: absolute;
    left: 8px;
    height: 14px;
    top: 3px;
    width: 14px;
  }

  .shape2 {
    position: absolute;
    left: 18px;
    height: 14px;
    top: 3px;
    width: 14px;
  }

  .fa-window-close-o {
    font-size: 21px;
  }

  .fa-square-o:before {
    content: none !important;
  }

  .fa-square-o.item1 {
    height: 21px;
    width: 21px;
    border-radius: 2px;
    background-color: var(--button-bg-color) !important;

  }

  .fa-square-o.item2 {
    height: 21px;
    width: 21px;
    border: 1px solid #ED7878;
    border-radius: 2px;
    background-color: rgba(237, 120, 120, 0.2);

  }

  .fa-square-o.item3 {
    height: 21px;
    width: 12px;
    border: 1px solid #979797;
    background-color: #D8D8D8;

  }

  .fa-square-o.item4 {
    height: 21px;
    width: 12px;
    border: none;
    background-color: #D8D8D8;

  }

  .name {
    height: 14px;
    width: 50px;
    color: #413E3B;
    font-family: "apercu-r";
    font-size: 10px;
    line-height: 15px;
  }

  .name1 {
    height: 10px;
    color: #fff;
    font-family: "apercu";
    font-size: 8px;
    line-height: 10px;
    cursor: default;
    padding-top: 5px;
  }

  .fa-square-o.item5 {
    height: 21px;
    width: 21px;
    border-radius: 2px;
    background-color: var(--hyperlink-color);
  }
}

//@media (max-width: 1350px){
//[class^="col-"], [class^=" col-"] {
// padding-right: 4px !important;
// padding-left: 4px !important;
//}
//}
@media(max-width:500px) {

  [class^="col-"],
  [class^=" col-"] {
    padding-right: 3px;
    padding-left: 4px;
  }

  .card-div.active .card-div-inner {
    padding-top: 10px;
    padding-bottom: 20px;
    padding-left: 2px;
    padding-right: 0px;
  }

  .name1 {
    height: 10px;
    color: #fff;
    font-family: "apercu";
    font-size: 8px;
    line-height: 10px;
    cursor: default;
    padding-top: 5px;
  }
}

@media(max-width:380px) {

  [class^="col-"],
  [class^=" col-"] {
    padding-right: 2px;
    padding-left: 4px;
  }

  .card-div.active .card-div-inner {
    padding-top: 10px;
    padding-bottom: 20px;
    padding-left: 0px;
    padding-right: 0px;
  }

  .fa-square-o.item4 {
    height: 21px;
    width: 10px;
    border: none;
    background-color: #D8D8D8;

  }

  .rowNo {
    width: 15px;
  }
}

@media(max-width:365px) {

  [class^="col-"],
  [class^=" col-"] {
    padding-right: 2px;
    padding-left: 4px;
  }

  .card-div.active .card-div-inner {
    padding-top: 10px;
    padding-bottom: 20px;
    padding-left: 0px;
    padding-right: 0px;
  }

  .fa-square-o.item4 {
    height: 21px;
    width: 5px;
    border: none;
    background-color: #D8D8D8;

  }

  .rowNo {
    width: 15px;
  }

  .wingsPresent {
    width: 5px;
    background-color: #D8D8D8;
    margin-right: 1px;
  }

  .wingsNotPresent {
    width: 5px;
    margin-right: 1px;
  }

  .wingsPresent1 {
    width: 5px;
    background-color: #D8D8D8;
    margin-left: 0px;
  }

  .wingsNotPresent1 {
    width: 5px;
    margin-left: 0px;
  }
}