import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject } from "rxjs";
import { environment } from '../environments/environment';
import { UserProfile } from './entity/user-profile';
import { GallopHttpClient } from './shared/gallop-httpclient.service';

@Injectable()
export class UserDetailsServiceBackup {
  userid: string;
  sToken: string;
  userDetails$ = new BehaviorSubject<UserProfile>(null);

  userDetails: UserProfile;

  constructor(private http: GallopHttpClient) {
  }
  getUserDetails(userid: string, sToken: string) {
    const url = environment.USER_API;
    this.userid = userid;
    this.sToken = sToken;
    return this.http.get(url);
  }


  setUserId(userId: string) {
    this.userid = userId;
  }
  getUserId() {
    return this.userid;
  }
  setStoken(sToken: string) {
    this.sToken = sToken;
  }
  getStoken() {
    return this.sToken;
  }
  setNewUserDetails(userDetails: UserProfile) {
    this.userDetails = userDetails;
    this.userDetails$.next(userDetails);
  }
  clearUserDetails() {
    this.userDetails = null;
    this.userDetails$.next(null);
    this.sToken = null;
    this.userid = null;
  }

  updateGallopCash(amount: number) {
    if (this.userDetails && this.userDetails != null && this.userDetails.gallopCash) {
      this.userDetails.gallopCash.amount = amount;
      this.userDetails$.next(this.userDetails);
    }
  }
}
