<?xml version="1.0" encoding="UTF-8"?>
<svg width="31px" height="41px" viewBox="0 0 31 41" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 53.2 (72643) - https://sketchapp.com -->
    <title>ic_room</title>
    <desc>Created with <PERSON>ket<PERSON>.</desc>
    <defs>
        <path d="M19.5,4 C13.1421429,4 8,9.1645 8,15.55 C8,24.2125 19.5,37 19.5,37 C19.5,37 31,24.2125 31,15.55 C31,9.1645 25.8578571,4 19.5,4 L19.5,4 Z M19.5,19.675 C17.2328571,19.675 15.3928571,17.827 15.3928571,15.55 C15.3928571,13.273 17.2328571,11.425 19.5,11.425 C21.7671429,11.425 23.6071429,13.273 23.6071429,15.55 C23.6071429,17.827 21.7671429,19.675 19.5,19.675 L19.5,19.675 Z" id="path-1"></path>
        <filter x="-30.4%" y="-15.2%" width="160.9%" height="142.4%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="HotelDesktop-2.12.2019" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="02-Results-Desktop-Rating-Map" transform="translate(-644.000000, -565.000000)">
            <g id="ic_room" transform="translate(640.000000, 563.000000)">
                <g id="Shape">
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use fill="#03EFD8" fill-rule="evenodd" xlink:href="#path-1"></use>
                </g>
            </g>
        </g>
    </g>
</svg>