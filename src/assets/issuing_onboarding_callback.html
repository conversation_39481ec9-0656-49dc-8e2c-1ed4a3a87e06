<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Issuing Onboarding</title>
    <script type="text/javascript" src="js/library/jquery/jquery.min.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/library/jquery/jquery.min.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/library/jquery-validate/jquery.validate.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript"
        src="js/library/jquery-validate/additional-methods.min.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/config.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/emailflow/script.js?v=_BUILD_VERSION_"></script>
    <style>
        @font-face {
            font-family: 'apercu-b';
            src: url("../assets/fonts/apercu_bold_pro.otf") format("opentype");
            font-weight: normal;
            font-style: normal;
        }

        .input-mst {
            text-align: center;
            margin: auto;
        }

        .logo-border {
            display: block;
            height: 48px;
            width: 48px;
            background-color: #DDFFF7;
            border-radius: 50%;
            position: relative;
            margin: 0 auto;
            margin-bottom: 20px;
        }

        .logo-border1 {
            display: block;
            height: 48px;
            width: 48px;
            background-color: #FFE8EB;
            border-radius: 50%;
            position: relative;
            margin: 0 auto;
            margin-bottom: 20px;
        }

        .modal-logo {
            text-align: center;
            display: block;
            position: absolute;
            top: 36%;
            left: 35.3%;
        }

        .checkmark {
            border: 3px solid #27c198;
            background-color: #DDFFF7;
        }

        .icon-checkmark {
            color: #27c198;
            font-size: 19px;
            left: 29%;
            top: 23%;
        }

        .cross-bold {
            border: 3px solid #f93d30;
            background-color: #FFE8EB;
        }

        .icon-cross-bold {
            color: #f93d30;
        }


        .spinner {
            margin: 2vh auto 0 auto;
            width: 40px;
            height: 40px;
            text-align: center;
        }

        .spinner12 {
            margin: 0px auto 0 auto;
            width: 80px;
            height: 35px;
            text-align: center;
        }

        .spinner>div {
            background-color: var(--dark-bg-color);
            height: 100%;
            width: 3px;
            display: inline-block;
            margin: 0 1px;
            -webkit-animation: sk-stretchdelay .8s infinite ease-in-out;
            animation: sk-stretchdelay 0.8s infinite ease-in-out;
        }

        .spinner12>div {
            background-color: var(--dark-bg-color);
            height: 100%;
            width: 2px;
            margin: 0 0px;
            display: inline-block;
            -webkit-animation: sk-stretchdelay .8s infinite ease-in-out;
            animation: sk-stretchdelay 0.8s infinite ease-in-out;
        }

        .logo a {
            display: inline-block;
            width: 170px;
        }

        .spinner .rect1 {
            -webkit-animation-delay: -1.2s;
            animation-delay: -1.2s;
        }

        .spinner .rect2 {
            -webkit-animation-delay: -.8s;
            animation-delay: -.8;
        }

        .text-show {
            font-family: var(--globalFontfamilyr);font-weight: bold;;
            font-size: 20px;

        }

        .spinner12 .rect1 {
            -webkit-animation-delay: -1.2s;
            animation-delay: -1.2s;
        }
        .spinner12 .rect2 {
            -webkit-animation-delay: -.8s;
            animation-delay: -.8s;
        }

        span {
            content: "\2713";
        }

        @-webkit-keyframes sk-stretchdelay {

            0%,
            80%,
            100% {
                -webkit-transform: scaleY(0.4);
                opacity: 1
            }

            40% {
                -webkit-transform: scaleY(1.0);
                opacity: .4
            }
        }

        @keyframes sk-stretchdelay {

            0%,
            80%,
            100% {
                transform: scaleY(0.4);
                -webkit-transform: scaleY(0.4);
                ;
                opacity: 1
            }

            40% {
                transform: scaleY(1.0);
                -webkit-transform: scaleY(1.0);
                ;
                opacity: .4
            }
        }
        .button-primary {
  height: 43px !important;
  position: relative !important;
  letter-spacing: 1px;
  z-index: 1000 !important;
  margin: 20px auto;
  cursor: pointer;
  width: 153px !important;
  border-radius: 6px !important;
  border: none !important;
  background-color: var(--button-bg-color) !important;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.14) !important;
  color: var(--button-font-color) !important;
  font-family: "apercu-b" !important;
  font-size: 14px !important;
  line-height: 40px !important;
  text-align: center !important;
}
        .logo-icon {
            width: 200px;
            height: 80px;
            background-image: url(/../assets/images/logo.png);
            background-repeat: no-repeat;
            display: inline-block;
            background-size: 200px;
        }

        @media (max-width:767px) {
            .logo-icon {
                width: 92px;
                height: 30px;
                background-size: 90px;
            }
            .text-show {
            font-family: var(--globalFontfamilyr);font-weight: bold;;
            font-size: 16px !important;

        }
        }
    </style>
    <script>
         var selectedLan='en'
            if (localStorage.getItem("selectedLanguage") != null) {
             selectedLan = localStorage.getItem("selectedLanguage");
            }else{
                selectedLan='en'
           
        }
        $(document).ready(function () {
            $("#success_message").hide();
            $("#success").hide();
            $("#failed").hide();
            $("#error_message").hide();
            var params = parseUrlParam();
            var id = params.id;
            if (params['type'] === 'return') {
                // Notify that the user has exited the onboarding flow.
                var url = getApiBaseUrl('apiForIssuingOnbardingCallBackReturn') + "/issuing/onboarding/return";
                $.ajax({
                    url: url + '?id=' + encodeURIComponent(id),
                    type: 'GET',
                    success: function (data) {
                    }
                });
                $("#success").show();
                
              //  $("#success_message").text(get_i18n('content5',selectedLan));
                $("#success_message").show();
                $("#loaderid").hide();
               // document.getElementById("success_message").innerHTML  = get_i18n('content5',selectedLan);
                $("#error_message").hide();
                $("#failed").hide();
               
                setTimeout(() => {
                    closeTab()
                },5000);
            }else {
                //refresh
                $("#success_message").hide();
                $("#success").hide();
                //document.getElementById("error_message").innerHTML  = get_i18n('content6',selectedLan);
               // $("#error_message").text(get_i18n('content6',selectedLan));
                $("#failed").show();
                $("#error_message").show();
                var url = getApiBaseUrl('apiForIssuingOnbardingCallBackRefresh') + "/issuing/onboarding/refresh";
                $.ajax({
                    url: url + '?id=' + encodeURIComponent(id) ,
                    type: 'GET',
                    success: function (data) {
                        if (data) {
                            var respObj = (data);
                            if (respObj.success) {
                                var redir = data.data;
                                window.location.href = redir;
                            } else {
                              
                                handleError(respObj);
                                setTimeout(() => {
                    closeTab()
                },5000);
                            }
                    }else {
                        handleError();
                        setTimeout(() => {
                    closeTab()
                },5000);
                    }
                    },
                    error: function (data) {
                        handleError();
                        setTimeout(() => {
                    closeTab()
                },5000);
                    }

                });
            }

        });
        var article = {
            title: {
      en: "logging-in",
      es: "Iniciando sesión",
      fr:"se connecter",
      it:"entrando"
    },
    content: {
      en: "Login Successful",
      es: "Inicio de sesión exitosa",
      fr:"Connexion réussie",
      it:"Login Successful"
    },
    content2: {
      en: "You can now close this window and go back to Routespring.",
      es: "Ahora puede cerrar esta ventana y volver a Routespring.",
      fr:"Vous pouvez maintenant fermer cette fenêtre et revenir à Routespring.",
      it:"Ora puoi chiudere questa finestra e tornare a Routespring."
    },
    content3: {
      en: "Close",
      es: "cerca",
      fr:"Fermer",
      it:"Vicina "
    },
    content4: {
      en: "Failed to generate onbaring link. Please talk to the support",
      es: "No se pudo generar el enlace onbaring. Por favor hable con el soporte",
      fr:"Échec de la génération du lien d'activation. Veuillez parler au support",
      it:"Impossibile generare il collegamento di inserimento. Si prega di parlare con l'assistenza"
    },
    content5: {
      en: "Thanks for going through the onboarding. Our team will get back to you with the next steps.",
      es: "Gracias por pasar por la incorporación. Nuestro equipo se comunicará con usted con los próximos pasos.",
      fr:"Merci d'avoir suivi l'intégration. Notre équipe vous répondra avec les prochaines étapes.",
      it:"Grazie per aver seguito l'onboarding. Il nostro team ti ricontatterà con i passaggi successivi."
    },
    content6:{
        en:"This onboarding link has expired. Generating a fresh onbarding link.",
        es:"Este enlace de incorporación ha caducado. Generando un nuevo enlace de incorporación.",
        fr:"Ce lien d'intégration a expiré. Générer un nouveau lien d'intégration.",
        it:"Questo collegamento di onboarding è scaduto. Generazione di un nuovo collegamento di onbarding."
    }
}
function get_i18n(item, lang) {
    if(lang){
        document.write(article[item][lang])
    }
    
}
function closeTab(){
    window.close();
}
        function handleError(data){
            $("#success_message").hide();
            $("#success").hide();
            $("#loaderid").hide();
            if (data && data.error_message) {
                $("#error_message").text(data.error_message);
            } else {
                //$("#error_message").text('Failed to generate onbaring link. Please talk to the support');
            }
            $("#failed").show();
            $("#error_message").show();
        }
    </script>
</head>

<body>
    <div class="input-mst">
        <div style="padding-top:25px;">
            <span class="logo" style="text-align: center;">
                <a>
                    <span class="logo-icon"></span>
                </a>
            </span>
        </div>
        <div id="loaderid" class="">
                <span class="text-show"><script>get_i18n('title',selectedLan);</script></span>
            <div class="spinner">
                <div class="rect1"></div>
                <div class="rect2"></div>
                <div class="rect1"></div>
            </div>
        </div>
        <div id="success" style="display: none;">
            <span class="logo-border">
                <span style="line-height: 45px;
                            font-size: 30px;
                            color: green;">&#10004;</span>
            </span>
        </div>
        <div id="failed" style="display: none;">
            <span class="logo-border1">
                <span style="line-height: 45px;
                            font-size: 30px;
                            color: red;">&#10060;</span>
            </span>
        </div>
        <div id="error_message" ><script>get_i18n('content6',selectedLan);</script></div>
        <div id="success_message" ><span  class="text-show"><script>get_i18n('content5',selectedLan);</script></span><br><span><script>get_i18n('content2',selectedLan);</script></span></div>
                <div  class="button-primary" onclick="closeTab()"><script>get_i18n('content3',selectedLan);</script></div>
               
    </div>

</body>

</html>