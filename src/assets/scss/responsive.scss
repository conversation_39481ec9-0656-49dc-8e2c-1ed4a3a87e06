@import 'variable.scss';

@media (max-width:1439px) and (min-width:1200px){
    .container{width: 100%;}
}

@media (max-width:1199px){
    .container{width: 970px;}
    /*.logo-icon {width: 160px;height: 55px;background-size: 160px;}*/
    .header-left{ padding-left: 0;}
    .row-top-content{ display: block;}
}

@media (max-width:991px){
    .container{width: 740px;}
    /*.logo-icon {width: 160px;height: 55px;background-size: 160px;}*/
    .flight-row-left-section .line {left: 80px;right: 80px;}
    .flight-row-left {width: calc(100% - 170px);}
    .flight-row-right {width: 190px;padding: 10px 10px;background-size: 24px;background-position: right 8px center;}
    /*.header-inner {height: 120px;}*/
    .card-header {padding: 30px 16px;}
    .card-body {padding: 0 20px 20px;}
    .card-footer {padding: 0 20px 24px;}
    .traveller-form .card-header{padding: 17px 16px;}


    .hotal-listing-row .flight-row-left-section .line, .car-listing-row .flight-row-left-section .line{ display: none;}
    .traveller-form .card-body{margin-top: 0; padding-top: 0;}
}

@media (max-width:767px){
    .container{ width: 100%; padding: 0 0;}
    .card-header h3{font-size: 16px;letter-spacing: -0.71px;line-height: 16px;}
    .card-header p{font-size: 10px;line-height: 16px;letter-spacing: -0.45px;}
    .card {background-color: #EEEDEB;float: left;width: 100%;position: relative; margin-bottom: 8px;}
    .card-body{ padding: 0 16px 15px;}
    .card-footer{padding: 0 16px 24px}
    .logo-icon {width: 87.27px;height: 28.95px ;background-size: 87.27px !important;}
    .input-box {margin-bottom: 0;}
    .input-label{ margin-bottom: 4px;}
    .flight-row-left-section .line{ display: none;}
    .flight-row{ flex-direction: column;}
    .flight-row-left{width:100%;border-radius: 6px 6px 0 0;}
    .secondary-text-part{ display: block;}
    .flight-row-right{ width: 100%; justify-content: flex-start; height: auto; border-radius: 0 0 6px 6px; border-top: 2px solid #e3e3e3; box-shadow: none; background-color: #f3f3f3;padding: 10px 16px; background-size: 18px;}
    .card-section-header h4{font-size: 12px;letter-spacing: -0.53px;line-height: 16px;}
    .airport-name{font-size: 12px;line-height: 12px;}
    .secondary-text{font-size: 10px;line-height: 12px;}
    .price{font-size: 14px;letter-spacing: 1.17px;line-height: 14px;}
    .flight-logo-icon{ width: 11px;}
    .flight-icon img{ width: 24px;}
    .flight-icon-inner{ padding: 0;}
    .flight-row-left-section {padding: 0 16px;}
    .flight-row-left-section-inner {padding: 11px 0 16px;}
    .flight-listing-row.selected .flight-row-right{ background-color: #fff;}
    .listing-card  .card-header{padding-bottom: 4px;}
    .card-header p{ margin-top: 1px;}
    .card-section-header h4{ margin-bottom: 7px}
    .flight-summery span{font-size: 10px;line-height: 12px;}
    .flight-row-layover-content{font-size: 10px;line-height: 12px;}
    .flight-row-layover-content {height: 19px;width: 156px;}
    .flight-row-layover-content span{ font-family:"apercu-b";}
    .flight-listing-row.selected .flight-row{box-shadow: 0 0 10px 0 rgba(0,0,0,0.14);}
    .header-inner {height: 71px; padding-top: 0;}
    .user-profile-img,.user-email{ display: none;}
    .gallop-cash{ font-size: 10px;}
    .user-profile-content{ padding-left: 0; width: 100%;}
    .flight-summery {padding-top: 0px;}
    .page-content {padding-bottom: 12px;}
    .flight-row-right::after{right: 10px;}
    .flight-row-layover{ margin-bottom: 7px;}
    .user-name{ text-align: right;}
    .card-sec-header h3{font-size: 12px;line-height: 16px;letter-spacing: -0.53px;}
    .travellerShortDetailsText{ display: block;}
    .seperator{ display: none;}
    .traveller-short-info{font-size: 12px;letter-spacing: -0.53px;line-height: 19px;margin-top: 5px;}
    .form-number {height: 26px;width: 26px;font-size: 14px;letter-spacing: -0.62px;line-height: 14px;}
    .traveller-form .card-body{ margin-bottom: 0;margin-top: -1px; padding-top: 0;}
    .form-arrow-down{ display: none !important;}
    .payment-card .card-header{ padding-top: 18px; padding-bottom: 23px;}
    .card-sec-header {padding-bottom: 11px;padding-top: 11px;}
    .datepicker{ background-size: 12px; background-position: right 14px center;}
    .input-textfield.datepicker {padding-right: 40px;}

    .row-heading {
    font-size: 12px;
    line-height: 12px;
}
.distance-img-text{font-size: 10px;
    line-height: 12px;}
.row-sec-text{font-size: 10px;
    line-height: 12px;}

.distance-label{font-size: 10px;
    line-height: 12px;}
.hotel-date{font-size: 12px;
    line-height: 14px;}

.car-listing-row .flight-center-column{ display: none;}
.star-icon i{ font-size: 8px;}
.star-icon {
    margin-right: 2px;
    line-height: 8px;
}
.total-amount-inner {
    max-width: 185px;
}
.amount{ font-size: 16px; letter-spacing: -0.87px;}
.gallop-cash-text{font-size: 10px;letter-spacing: -0.45px;line-height: 16px;}

.expensifyForm .mdl-checkbox__label{font-size: 12px; line-height: 15px; margin-left: 5px;position: relative;top: -4px;}
.expensifyForm .mdl-checkbox{ line-height: 16px; padding-left: 0px;}
.expensifyForm .mdl-checkbox__box-outline{ height: 12px; width: 12px;}
.paymentButton{ margin-top: 92px;}
.paymentButton button{ width: 100%;}

.price-text {
    line-height: 14px;
    font-size: 12px;
    margin-left: 0;
    margin-top: 5px;
}

.taxi-location{font-size: 12px;line-height: 16px; color: $primaryColor; margin-bottom: 2px; display: inline-block;}


}


@media (max-width:767px){
.card-section-heading{ display: block;}
.viewAllLink{ margin-right: 10px;}
}
