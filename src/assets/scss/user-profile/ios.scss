@import 'variable.scss';

.appleDevice .input-textfield{ font-size: 16px !important;letter-spacing: -1.1px !important;word-spacing: -2px !important;padding-top: 4px; padding-bottom: 0; padding-left: 8px; padding-right: 8px;}
.appleDevice .input-box-with-icon input {padding-left: 45px !important;padding-right: 55px}
.appleDevice .input-box-icon-container.left {left: 10px;}
.appleDevice .input-box-icon-container.left + input {padding-left: 32px;}
.appleDevice select.input-textfield{padding-top: 0;}
.appleDevice .container{ padding: 0 10px;}
.appleDevice .card-body{padding: 22px 10px 31px 10px;}
.appleDevice .card-header {padding: 15px 10px;}
/*.appleDevice .mdl-radio {font-size: 16px;line-height: 24px;}*/
/*.appleDevice .mdl-radio, .appleDevice .mdl-radio.is-upgraded {padding: 0 15px;}*/
.appleDevice textarea.input-textfield{ padding-top: 8px; padding-bottom: 8px;}
.appleDevice .select2-container--default .select2-selection--single .select2-selection__rendered{line-height: 37px;font-size: 16px; padding-left: 8px;}