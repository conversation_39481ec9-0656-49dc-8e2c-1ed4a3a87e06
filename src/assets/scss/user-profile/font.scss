@import 'variable.scss';

@font-face {
    font-family: var(--globalFontfamilyr);
    src: url("../fonts/apercu/apercu_regular_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }
@font-face {
    font-family: 'apercu-i';
    src: url("../fonts/apercu/apercu_regular_italic_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }

@font-face {
    font-family: 'apercu-mono';
    src: url("../fonts/apercu/apercu_mono_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }

@font-face {
    font-family: 'apercu-m';
    src: url("../fonts/apercu/apercu_medium_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }

@font-face {
    font-family: var(--globalFontfamilyr);font-weight: bold;;
    src: url("../fonts/apercu/apercu_bold_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }

@font-face {
    font-family: 'apercu-mi';
    src: url("../fonts/apercu/apercu_medium_italic_pro.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }

@font-face {
    font-family: 'apercu-l';
    src: url("../fonts/apercu/apercu_light_pro_000.otf") format("opentype");
    font-weight: normal;
    font-style: normal; }



/* fallback */
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: local("Material Icons"), local("MaterialIcons-Regular"), url("../fonts/material/fonts/material-icons.woff2") format("woff2"); }
.material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    text-rendering: optimizelegibility;
    -webkit-font-smoothing: antialiased;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -moz-font-feature-settings: 'liga';
    -moz-osx-font-smoothing: grayscale; }