@import 'variable.scss';
@media (max-width:1200px){
  
    .card-div-inner {
        padding: 30px 30px 10px;
    }
    .user-summery-inner {
        padding: 10px 19px 10px 26px;
    }
}

@media (max-width:992px){
    .container {
        padding-left: 16px;
        padding-right: 16px;
    }
    .card-div-inner{padding: 30px 20px 10px;}
    .features{display: none;}
}
@media(max-width:768px){
    .bs-datepicker .bs-media-container {
        flex-direction: row !important;
    }
    .bs-datepicker-multiple + .bs-datepicker-multiple {
        margin-top: 0px !important;
        margin-left: 0;
    }
}
@media (max-width:767px){
    .bs-datepicker .bs-media-container {
        flex-direction: column !important;
    }
    .bs-datepicker-multiple + .bs-datepicker-multiple {
        margin-top: 10px !important;
        margin-left: 0;
    }
    .container {
        padding-left: 7px;
        padding-right: 7px;
    }
/*    .tab-list {
        padding-left: 16px;
        padding-right: 16px;
    }*/
    .user-summery-inner {
        height: auto;
        align-items: flex-start;
        flex-direction: column;
        padding: 0;
    }
    .user-summery-left{ float: left; width: 100%;padding: 15px 15px 20px 15px;}
    .user-summery-right{ float: left; width: 100%;}
    .user-summery-card.card-div.shadow2 {
        box-shadow: none;
        width: 50%;
        margin: 0;
        border-radius: 0;
        border-bottom: none;
        border-left: none;
        height: 85px;
        padding: 4px 10px 23px 15px;
    }
    .user-summery-card.card-div.shadow2:first-child{
        border-radius: 0 0 0 6px;
    }
    .user-summery-card.card-div.shadow2:last-child{
        border-right: none;
        border-radius: 0 0 6px 0;
    }
    .logo a {
        width: 166px;
    }
    .user-img {
        width: 51px;
    }
    .user-name {
        font-size: 16px;
        line-height: 20px;
    }
    .user-email, .user-phone{
        font-size: 12px;
        line-height: 17px;
    }
    .user-summery-card-heading span {
        font-size: 12px;
        line-height: 16px;
    }
    .gallop-cash-div .user-summery-card-content {
        font-size: 18px; line-height: 22px;
    }
    .user-summery-card-content {
        font-size: 14px;
        line-height: 17px;
    }
    .cashback-text-width {
        max-width: none;
    }
    .header-inner{height: 112px;margin-bottom: 0; padding-bottom: 43px;}
    .user-summery-card-content{height: auto; margin-top: 5px;}
    .card-div-header h3{font-size: 16px;letter-spacing: -0.71px;line-height: 22px;}
    .card-div.active{
        width: calc(100% + 32px);
        margin-left: -16px;
        border-radius: 0;
        box-shadow: none;
    }
    .card-div{margin-bottom: 6px;}
/*    .tab-list ul li {
        padding-bottom: 7px;
        margin-bottom: 15px;
        margin-right: 22px;
    }*/
    .card-div-inner{padding: 24px 16px 4px;}
    .card-div.active .card-div-inner{padding: 24px 32px 4px;}
    .airline-div {
        padding-left: 4px;
        padding-right: 4px;
    }
    .airlineCheckboxContainer, .hotelCheckboxContainer{width: 100%;}
    .airlineCheckboxContainer .mdl-checkbox .mdl-checkbox__label .mdl-checkbox__label-img img {
        width: 34px;
        max-width: 50px;
        max-height: 50px;
    }
    .seatselectionContainer .mdl-checkbox__label-img img {
        max-width: 40px;
        max-height: 40px;
    }
    .traveller-short-info {
        font-size: 12px;
        letter-spacing: -0.53px;
        line-height: 12px;
    }

    .card-div-body {
        margin-top: 2px;
    }
    .card-div-footer {
        padding: 16px 0 20px;
    }
    .tab-container{margin-bottom: 24px;}
    .card-div.filled{}
    .accord {
        margin-bottom: 7px;
    }

    .heading-link {
        margin-top: 9px;
        margin-bottom: 0;
    }

    .user-summery {
        margin-top: 0;
    }
    .iti-container {
        width: auto;
    }
    /*.intl-tel-input .country-list{width: 280px !important; max-height: 200px !important;}*/
    .basic-economy-warning-inner{font-size: 14px; line-height: 18px;padding: 10px 20px;}
    .basicEconomyRadioButtons{margin-top: 17px;margin-bottom: 8px;}
    .preffered-home-airport{margin-top: 16px;}
    
    .passwordCheck {
        top: 7px;
    }
    .subscribe-card-div .card-edit{float: left; position: static; margin-top: 14px;}
    .payment {
    padding-left: 0;
    margin-top: 4px;
    margin-bottom: 2px;
}
.payment-inner {
    border: none;
}
.payment-inner {
    align-items: flex-start;
    flex-direction: column-reverse;
}

.payment h4 {
    font-size: 14px;
    line-height: 17px;
}
.payment h4 span {
    display: inline-block;
    vertical-align: middle;
}
.payment h4 span.prices {
    font-size: 16px;
    letter-spacing: -0.87px;
    margin-left: 5px;
}
.promo {
    border-top: 1px solid #e6e6e6;
    border-bottom: 1px solid #e6e6e6;
    float: left;
    width: 100%;
    padding: 9px 0 11px;
    margin-bottom: 16px;
}
.promo .link-font {
    font-size: 10px;
}
.promo-box {
    position: fixed;
    top: auto;
    bottom: 0;
    width: 100%;
    height: auto;
    padding: 32px;
}
.promo-box-input .link-font {
    top: 36px;
}

.card-box {
    padding-left: 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 9;
    margin-top: 0;
    margin-bottom: 0;
}
.card-box-inner {
    background-color: #fff;
    border: 2px solid #E5E5E5;
    box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.14);
    border-radius: 0;
    padding: 15px 32px 40px 32px;
}
.input-box-icon-container {
    height: 40px;
}
/*.subscribe-card-div .card-div-header{ margin-bottom: 0;}*/


.subscription-details ul{ flex-direction: column;}
.subscription-details ul li::after{display: none;}
.subscription-details ul li {
    font-size: 12px;
    margin-bottom: 24px;
}

.subscription-details ul li:last-child{margin-bottom: 0;}
.cancelSubscriptionButton {
    margin-top: 20px;
}

.paymentMethodSection {
    margin-bottom: 8px;
}
.paymentMethodHeading {
    margin-bottom: 15px;
}
.billingRadio .mdl-radio__label{
    font-size: 14px;
    line-height: 22px;
}

.subscribe-card-div.active .card-div-header{margin-bottom: 0;}

.admin-list{right: 0;}
.creditCardBox.creditCardBoxPaymentMethods .card-box-inner {
    padding: 15px 32px 40px 32px;
}

}

@media (max-width:359px){
    .card-div.active .card-div-inner{padding-left: 15px; padding-right: 15px;}
}
@media (max-width : 412px){
    .container{
    padding-left: 7px !important;
    padding-right: 7px !important;
    }
    
}