@import 'variable.scss';

body{ color: $primaryColor;   margin: 0 auto;font-family: $fontRegular; font-size: 16px; width: 100%; overflow-x: hidden;  background: #F6F6F6;}
.pageBody{ margin: 0 auto; max-width: 1440px; width: 100%; min-height: 100vh;}
*{ outline: none !important; list-style: none;}
img{ max-width: 100%; max-height: 100%;}

a{color: var(--hyperlink-color) !important; font-family: $fontBold; text-transform: uppercase; font-size: 12px;}
.link-font{ font-size: 12px;}
a:hover,a:focus,a:active{text-decoration: none;}
p{ margin-bottom: 0; font-size: inherit; line-height: normal;}
ul{ padding: 0; margin: 0;}
h1,h2,h3,h4,h5,h6{font-family: $fontRegular; margin: 0;}
.pad_0{ padding: 0 !important;}
label{ margin-bottom: 0; font-weight: normal;}
*::-moz-focus-inner {border: 0 !important; }
select:-moz-focusring {color: transparent;text-shadow: 0 0 0 #000;}
.table-view{ display: table; float: left; width: 100%; height: 100%;}
.table-cell-view{ display: table-cell; vertical-align: middle;}
strong{ font-weight: normal; font-family: $fontMedium;}
b{font-weight: normal; font-family: $fontBold;}
.inlineblock_m{ display: inline-block; vertical-align: middle;}
.inlineblock_b{ display: inline-block; vertical-align: bottom;}
.inlineblock_t{ display: inline-block; vertical-align: top;}
.row {margin-left: -8px !important;margin-right: -8px !important;}
.table-view{display:table;height: 100%;width: 100%;}
.table-cell-view{display: table-cell;vertical-align: middle;}
.link-icon,.link-text{ display: inline-block; vertical-align: middle;}
.link-icon{margin-right: 7px;}
.flexContainer {display: flex;flex-wrap: wrap;float: left; width: 100%; justify-content: flex-start;}
[class^="col-"], [class^=" col-"] {padding-right: 8px;padding-left: 8px; }
.custom-wd-sm{width: 118px !important; flex: 0 0 118px !important; }
.line-break{float: left;
            width: 100%;
            height: 0px;}
/*.container{padding-left: 64px; padding-right: 64px; max-width: none;}*/
span.error{color: $errorColor; font-size: 12px;}
.stripeElements{ float: left; width: 100%;}
.card-div{border: 1px solid #E1E1E1;border-radius: 6px; background-color: #FFFFFF; margin-bottom: 14px; float: left; width: 100%;}
.shadow{box-shadow: 0 4px 8px 0 rgba(0,0,0,0.19);}
.shadow2{box-shadow: 0 4px 8px 0 rgba(0,0,0,0.1);}

/*body css*/
// header{background: linear-gradient(39.62deg, var(--dark-bg-color) 0%, var(--dark-bg-color) 3.43%, #862EF6 11.42%, #7940F3 25.41%, #655EEE 42.81%, #4888E8 62.15%, #25BDE0 82.83%, var(--button-bg-color) 100%);}
.header-inner{display: flex; justify-content: space-between; align-items: center; height: 114px;margin-bottom: 49px;}
.logo a{display: inline-block; width: 170px;}
.navigation ul li{display: inline-block;margin-right: 19px;}
.navigation ul li:last-child{margin-right: 0;}
.navigation ul li a{color: #FFFFFF;font-size: 12px;letter-spacing: 1px;line-height: 18px; font-family: $fontBold; }
.navigation{ display: inline-block;}
.admin{display: inline-block; margin-left: 28px; position: relative;text-align:left;}
.admin-name-img {
    line-height: normal;
    padding-left: 5px;
}

.admin-profile-container{cursor: pointer; display: flex; align-items: center;}

.admin-name{font-size: 16px;line-height: 17px;color: black; display: inline-block; vertical-align: middle; font-family:$fontRegular;}
.admin-img{display: inline-block;background-color: #E2E2E2; height: 45px; width: 45px; border-radius: 50%; vertical-align: middle;margin-top: 6px;}
.admin-img img{display: inline-block; vertical-align: top; border-radius: 50%;}
.admin-list{ width: 258px; height: auto; display: none; padding: 20px 32px; z-index: 1000; background: #fff; position: relative;border-radius: 6px;border: 1px solid #E1E1E1;box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1); position: absolute; right: -38px; top: 31px;text-align:left;}
.admin-list:before{position: absolute; top: -5px; right: 32px; border-left: 10px solid transparent;border-right: 10px solid transparent;border-bottom: 5px solid #fff; content: '';}
.admin-list ul li{margin-bottom: 8px;}
.admin-list ul li:last-child{margin-bottom: 0;}
.admin-list-icon{color: $themeColor2; font-size: 16px; display: inline-block; vertical-align: middle;}
.admin-list ul li a{font-family: $fontRegular; text-transform: uppercase; font-size: 12px; color: $primaryColor; letter-spacing: 1px; }
.gallop-phone {
    color: #A6A5A4;
    font-family: $fontBold;
}
.user-email{ line-height: normal;}
/*tab*/
/*.tab-list{overflow: auto; position: relative; z-index: 0; background: #000;}*/
.tab-list > ul{white-space: nowrap; overflow-x: scroll;}
.profileTab .tab-content-item{display: none;}
.profileTab .tab-content-item.active{display: block;}
.tab-container{width: 100%; float: left; margin-top: 15px;}
/*.tab-list ul li{ display: inline-block; margin-right: 32px; text-transform: uppercase; cursor: pointer;padding-bottom: 3px; margin-bottom: 0;font-size: 12px;letter-spacing: 1px;line-height: 18px;color: #413E3B;}
.tab-list ul li:last-child{margin-right: 0;}
.tab-list ul li.active{ color: var(--button-font-color); font-family: $fontBold; border-bottom: 1px solid var(--button-font-color);}*/





.top-strip{ background: var(--dark-bg-color); float: left; width: 100%; height: 43px;}
.tab-content{float: left; width: 100%;background-color: #FFFFFF;box-shadow: 0 0 10px 0 rgba(0,0,0,0.14); padding: 0 20px;}
.profileTab .tab-list-item{
    display: inline-block;
    margin-right: 20px;
    margin-left: 20px;
    margin-bottom: 0; padding-bottom: 0;}
.tab-list-item a{cursor: pointer;position: relative;display: inline-block; color: #FFFFFF;font-size: 12px; opacity: 0.6;line-height: 15px;padding: 10px 5px 13px 5px;}

.tab-list-item a img{margin-right: 10px;}
.tab-list-item.active a{opacity: 1;}
.tab-list-item.active a::after {
    position: absolute;
    content: "";
    bottom: 2px;
    background: #fff;
    left: 0;
    right: 0;
    width: auto;
    height: 3px;
}
.profileTab .tab-list{overflow: none;}
.profileTab .tab-content {
    padding: 14px 0 0 0;
    background: transparent;
    box-shadow: none;
}
ngx-smart-modal div.overlay{position: unset;}
.profileTab .tab-content {margin-right: 0px;}

/*card div*/
.card-div-header{float: left; width: 100%; cursor: pointer; margin-bottom: 20px; position: relative; font-family: $fontBold;font-size: 12px;letter-spacing: 1px;line-height: 18px;}
.card-div-body{float: left; width: 100%; margin-top: 10px;}
.multiple-inputs{float: left; width: 100%;}
.card-div-header h3{font-size: 22px;letter-spacing: -0.98px;line-height: 26px; font-family: $fontMono;}
.card-div-inner{float: left;padding: 30px 40px 10px;}
.card-div-footer{float: left; width: 100%; padding: 10px 0 20px;}
.card-edit {position: absolute;right: 0;display: flex;align-items: center;top: 0px;color: var(--button-font-color);cursor: pointer;}
.edit-text{display: none; text-transform: uppercase; padding-right: 3px;}
.edit-icon{font-size: 20px;}
.filled .edit-text{ display: block;}
.filled .edit-icon{display: block !important;}
.card-div.active .edit-icon{transform: rotate(-180deg);}
.card-div.active .edit-text{display: none !important;}
.card-div.active .edit-button{display: none !important;}
.traveller-short-info{font-size: 14px;letter-spacing: -0.62px;line-height: 16px;color: #A6A5A4; font-family: $fontMono;}
.card-div.active .traveller-short-info{ display: none;}
.traveller-short-info-inner{ float: left; width: 100%;padding-top: 3px; line-height: 21px;}
.traveller-short-info-inner span{ display: inline-block; vertical-align: middle;}
.traveller-short-info-inner .shortInfoSeperator:last-child{ display: none;}
.traveller-short-info-inner.shortInfoPrimary{font-size: 16px;line-height: 33px;color: #6C6865;margin-top: 23px;}
/*.card-div.active .card-div-header{ cursor: default;}*/
.accord-header{ float: left; width: 100%; cursor: pointer;}
.accord-header h4{font-size: 16px;letter-spacing: -0.71px;line-height: 22px; font-family: $fontMono;}
.accord-content{ float: left; width: 100%;}
.accord-content h4{}
.accord-content p{}


@media (max-width:1200px){
.admin-list{right: 0;}
}

@media (max-width:992px){

}

@media (max-width:767px){
    .admin-img{ height: 40px; width: 40px;}
    .admin-name {font-size: 12px;line-height: 15px;}
    .filled .edit-text {display: none;}
    .traveller-short-info-inner.shortInfoPrimary{font-size: 12px;letter-spacing: -0.53px;line-height: 12px;color: #A6A5A4;}
    .overflow-hidden{overflow: hidden !important;}
    .overlay{position: fixed;top: 0;bottom: 0;left: 0;right: 0;background: rgba(255,255,255,0.5);z-index: 9;}
    .admin-list{position: static; border:none; border-radius: 0;}
    .admin-list::before{display: none;}
    .admin.active .admin-profile-container{border-bottom: 1px solid #e6e6e6;padding: 9px 30px 14px 30px; align-items: center;}
    /*.admin.active .admin-name-img{}*/
    .admin.active{margin-left: 0;position: fixed;z-index: 9999999999;right: 0;background: #fff;top: 0;}
    .admin.active .admin-name {color: $primaryColor;}
    .gallop-phone{font-size: 12px;}
    ngx-smart-modal div.overlay{position: unset;}
}
