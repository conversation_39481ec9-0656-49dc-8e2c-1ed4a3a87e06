{"success": true, "errors": [], "data": [{"userid": "<EMAIL>", "department": "Angular Department", "outgoingArrivalDate": "2024-10-15T00:00:00.000+05:30", "outgoingArrivalAirport": "JFK International Airport", "outgoingArrivalFlightNumber": "AA123", "returningDepartureAirport": "Heathrow Airport", "returningDepartureDate": "2024-10-25 10:00", "returningDepartureFlightNumber": "BA456", "status": "booked", "costOfBooking": 6969}, {"userid": "<EMAIL>", "department": "Sabre  Department", "outgoingArrivalDate": "2024-10-15T00:00:00.000+05:30", "outgoingArrivalAirport": "JFK International Airport", "outgoingArrivalFlightNumber": "AA123", "returningDepartureAirport": "Heathrow Airport", "returningDepartureDate": "2024-10-25 10:00", "returningDepartureFlightNumber": "BA456", "status": "booked", "costOfBooking": 6970}, {"userid": "<EMAIL>", "department": "Java  Department", "outgoingArrivalDate": "2024-10-15T00:00:00.000+05:30", "outgoingArrivalAirport": "JFK International Airport", "outgoingArrivalFlightNumber": "AA123", "returningDepartureAirport": "Heathrow Airport", "returningDepartureDate": "2024-10-25 10:00", "returningDepartureFlightNumber": "BA456", "status": "pending", "costOfBooking": 6972}]}