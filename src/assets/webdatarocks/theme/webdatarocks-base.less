﻿@import (reference) "default/webdatarocks.less";

/* ===== RESET STYLES ===== */
#wdr-pivot-view,
#wdr-toolbar-wrapper {
    line-height: 1;
    -webkit-font-smoothing: antialiased !important;
    text-rendering: optimizeLegibility !important;
    -webkit-text-size-adjust: none;
}

#wdr-pivot-view input[type=text],
#wdr-toolbar-wrapper input[type=text] {
    -webkit-appearance: none;
}

#wdr-pivot-view div, #wdr-pivot-view span, #wdr-pivot-view applet, #wdr-pivot-view object, #wdr-pivot-view iframe,
#wdr-pivot-view h1, #wdr-pivot-view h2, #wdr-pivot-view h3, #wdr-pivot-view h4, #wdr-pivot-view h5, #wdr-pivot-view h6, #wdr-pivot-view p, #wdr-pivot-view blockquote, #wdr-pivot-view pre,
#wdr-pivot-view a, #wdr-pivot-view abbr, #wdr-pivot-view acronym, #wdr-pivot-view address, #wdr-pivot-view big, #wdr-pivot-view cite, #wdr-pivot-view code,
#wdr-pivot-view del, #wdr-pivot-view dfn, #wdr-pivot-view em, #wdr-pivot-view img, #wdr-pivot-view ins, #wdr-pivot-view kbd, #wdr-pivot-view q, #wdr-pivot-view s, #wdr-pivot-view samp,
#wdr-pivot-view small, #wdr-pivot-view strike, #wdr-pivot-view strong, #wdr-pivot-view sub, #wdr-pivot-view sup, #wdr-pivot-view tt, #wdr-pivot-view var,
#wdr-pivot-view b, #wdr-pivot-view u, #wdr-pivot-view i, #wdr-pivot-view center,
#wdr-pivot-view dl, #wdr-pivot-view dt, #wdr-pivot-view dd, #wdr-pivot-view ol, #wdr-pivot-view ul, #wdr-pivot-view li,
#wdr-pivot-view fieldset, #wdr-pivot-view form, #wdr-pivot-view label, #wdr-pivot-view legend,
#wdr-pivot-view table, #wdr-pivot-view caption, #wdr-pivot-view tbody, #wdr-pivot-view tfoot, #wdr-pivot-view thead, #wdr-pivot-view tr, #wdr-pivot-view th, #wdr-pivot-view td,
#wdr-pivot-view article, #wdr-pivot-view aside, #wdr-pivot-view canvas, #wdr-pivot-view details, #wdr-pivot-view embed,
#wdr-pivot-view figure, #wdr-pivot-view figcaption, #wdr-pivot-view footer, #wdr-pivot-view header,
#wdr-pivot-view menu, #wdr-pivot-view nav, #wdr-pivot-view output, #wdr-pivot-view ruby, #wdr-pivot-view section, #wdr-pivot-view summary,
#wdr-pivot-view time, #wdr-pivot-view mark, #wdr-pivot-view audio, #wdr-pivot-view video, 
#wdr-toolbar-wrapper div, #wdr-toolbar-wrapper span, #wdr-toolbar-wrapper applet, #wdr-toolbar-wrapper object, #wdr-toolbar-wrapper iframe,
#wdr-toolbar-wrapper h1, #wdr-toolbar-wrapper h2, #wdr-toolbar-wrapper h3, #wdr-toolbar-wrapper h4, #wdr-toolbar-wrapper h5, #wdr-toolbar-wrapper h6, #wdr-toolbar-wrapper p, #wdr-toolbar-wrapper blockquote, #wdr-toolbar-wrapper pre,
#wdr-toolbar-wrapper a, #wdr-toolbar-wrapper abbr, #wdr-toolbar-wrapper acronym, #wdr-toolbar-wrapper address, #wdr-toolbar-wrapper big, #wdr-toolbar-wrapper cite, #wdr-toolbar-wrapper code,
#wdr-toolbar-wrapper del, #wdr-toolbar-wrapper dfn, #wdr-toolbar-wrapper em, #wdr-toolbar-wrapper img, #wdr-toolbar-wrapper ins, #wdr-toolbar-wrapper kbd, #wdr-toolbar-wrapper q, #wdr-toolbar-wrapper s, #wdr-toolbar-wrapper samp,
#wdr-toolbar-wrapper small, #wdr-toolbar-wrapper strike, #wdr-toolbar-wrapper strong, #wdr-toolbar-wrapper sub, #wdr-toolbar-wrapper sup, #wdr-toolbar-wrapper tt, #wdr-toolbar-wrapper var,
#wdr-toolbar-wrapper b, #wdr-toolbar-wrapper u, #wdr-toolbar-wrapper i, #wdr-toolbar-wrapper center,
#wdr-toolbar-wrapper dl, #wdr-toolbar-wrapper dt, #wdr-toolbar-wrapper dd, #wdr-toolbar-wrapper ol, #wdr-toolbar-wrapper ul, #wdr-toolbar-wrapper li,
#wdr-toolbar-wrapper fieldset, #wdr-toolbar-wrapper form, #wdr-toolbar-wrapper label, #wdr-toolbar-wrapper legend,
#wdr-toolbar-wrapper table, #wdr-toolbar-wrapper caption, #wdr-toolbar-wrapper tbody, #wdr-toolbar-wrapper tfoot, #wdr-toolbar-wrapper thead, #wdr-toolbar-wrapper tr, #wdr-toolbar-wrapper th, #wdr-toolbar-wrapper td,
#wdr-toolbar-wrapper article, #wdr-toolbar-wrapper aside, #wdr-toolbar-wrapper canvas, #wdr-toolbar-wrapper details, #wdr-toolbar-wrapper embed,
#wdr-toolbar-wrapper figure, #wdr-toolbar-wrapper figcaption, #wdr-toolbar-wrapper footer, #wdr-toolbar-wrapper header,
#wdr-toolbar-wrapper menu, #wdr-toolbar-wrapper nav, #wdr-toolbar-wrapper output, #wdr-toolbar-wrapper ruby, #wdr-toolbar-wrapper section, #wdr-toolbar-wrapper summary,
#wdr-toolbar-wrapper time, #wdr-toolbar-wrapper mark, #wdr-toolbar-wrapper audio, #wdr-toolbar-wrapper video {
    margin: 0;
    padding: 0;
    border: 0;
    font: inherit;
    font-size: 100%;
    vertical-align: baseline;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-transition: none;
    transition: none;
    border-collapse: collapse;
    border-spacing: 0;
}

#wdr-pivot-view ol, #wdr-pivot-view ul,
#wdr-toolbar-wrapper ol, #wdr-toolbar-wrapper ul {
    list-style: none;
}

#wdr-pivot-view table,
#wdr-toolbar-wrapper table {
    border-collapse: collapse;
    border-spacing: 0;
}

#wdr-pivot-view caption, #wdr-pivot-view th, #wdr-pivot-view td,
#wdr-toolbar-wrapper caption, #wdr-toolbar-wrapper th, #wdr-toolbar-wrapper td {
    text-align: left;
    font-weight: normal;
    vertical-align: middle;
}

#wdr-pivot-view q, #wdr-pivot-view blockquote,
#wdr-toolbar-wrapper q, #wdr-toolbar-wrapper blockquote {
    quotes: none;
}

    #wdr-pivot-view q:before, #wdr-pivot-view q:after, #wdr-pivot-view blockquote:before, #wdr-pivot-view blockquote:after,
    #wdr-toolbar-wrapper q:before, #wdr-toolbar-wrapper q:after, #wdr-toolbar-wrapper blockquote:before, #wdr-toolbar-wrapper blockquote:after {
        content: "";
        content: none;
    }

#wdr-pivot-view img,
#wdr-toolbar-wrapper img {
    max-width: 100%;
    height: auto;
    height: initial;
    border: none;
}

#wdr-pivot-view a,
#wdr-toolbar-wrapper a {
    text-decoration: none;
}

#wdr-pivot-view article, #wdr-pivot-view aside, #wdr-pivot-view details, #wdr-pivot-view figcaption, #wdr-pivot-view figure, #wdr-pivot-view footer, #wdr-pivot-view header, #wdr-pivot-view main, #wdr-pivot-view menu, #wdr-pivot-view nav, #wdr-pivot-view section, #wdr-pivot-view summary,
#wdr-toolbar-wrapper article, #wdr-toolbar-wrapper aside, #wdr-toolbar-wrapper details, #wdr-toolbar-wrapper figcaption, #wdr-toolbar-wrapper figure, #wdr-toolbar-wrapper footer, #wdr-toolbar-wrapper header, #wdr-toolbar-wrapper main, #wdr-toolbar-wrapper menu, #wdr-toolbar-wrapper nav, #wdr-toolbar-wrapper section, #wdr-toolbar-wrapper summary {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

#wdr-pivot-view :focus,
#wdr-toolbar-wrapper :focus {
    outline: none;
}

#wdr-pivot-view h1,
#wdr-pivot-view h2,
#wdr-pivot-view h3,
#wdr-pivot-view h4,
#wdr-pivot-view h5,
#wdr-pivot-view h6,
#wdr-toolbar-wrapper h1,
#wdr-toolbar-wrapper h2,
#wdr-toolbar-wrapper h3,
#wdr-toolbar-wrapper h4,
#wdr-toolbar-wrapper h5,
#wdr-toolbar-wrapper h6  {
    font-weight: normal;
}
/*-----------STANDARD RESET END-----------------*/

@font-face {
  font-family: "webdatarocks-icons";
  src: url("../assets/webdatarocks-icons.woff") format("woff"), 
       url("../assets/webdatarocks-icons.ttf") format("truetype"), 
       url("../assets/webdatarocks-icons.svg#webdatarocks-icons") format("svg");
  font-weight: normal;
  font-style: normal;
}

.rotate(@deg: 90) {
    -webkit-transform: rotate(@deg * 1deg);
    -moz-transform: rotate(@deg * 1deg);
    transform: rotate(@deg * 1deg);
    -o-transform: rotate(@deg * 1deg);
    @IEdeg: round(@deg / 90, 0);
    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=@IEdeg);
}

/* === Do NOT remove and do NOT change .wdr-csscheck class === */
.wdr-csscheck {
    font-family: 'webdatarocks';
}

/* ===== PIVOT UI ===== */
#wdr-pivot-view .wdr-ui,
#wdr-pivot-view .wdr-ui span,
#wdr-pivot-view .wdr-ui div,
#wdr-pivot-view .wdr-ui p,
#wdr-pivot-view .wdr-ui a,
#wdr-pivot-view .wdr-ui table,
#wdr-pivot-view .wdr-ui table th,
#wdr-pivot-view .wdr-ui table tr,
#wdr-pivot-view .wdr-ui table td,
#wdr-pivot-view .wdr-ui ul,
#wdr-pivot-view .wdr-ui li,
#wdr-pivot-view .wdr-ui input,
#wdr-pivot-view .wdr-ui textarea,
#wdr-pivot-view .wdr-ui select,
#wdr-toolbar-wrapper .wdr-toolbar-ui,
#wdr-toolbar-wrapper .wdr-toolbar-ui span,
#wdr-toolbar-wrapper .wdr-toolbar-ui div,
#wdr-toolbar-wrapper .wdr-toolbar-ui p,
#wdr-toolbar-wrapper .wdr-toolbar-ui a,
#wdr-toolbar-wrapper .wdr-toolbar-ui table,
#wdr-toolbar-wrapper .wdr-toolbar-ui table th,
#wdr-toolbar-wrapper .wdr-toolbar-ui table tr,
#wdr-toolbar-wrapper .wdr-toolbar-ui table td,
#wdr-toolbar-wrapper .wdr-toolbar-ui ul,
#wdr-toolbar-wrapper .wdr-toolbar-ui li,
#wdr-toolbar-wrapper .wdr-toolbar-ui input,
#wdr-toolbar-wrapper .wdr-toolbar-ui textarea,
#wdr-toolbar-wrapper .wdr-toolbar-ui select {
    font-family: @font-family;
    font-size: @font-size;
    text-align: left;
    color: @theme-text-color;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    padding: 0;
    border-radius: 0px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    text-shadow: none;
    float: none;
    line-height: normal;
}

#wdr-pivot-view .wdr-ui-mobile,
#wdr-pivot-view .wdr-ui-mobile span,
#wdr-pivot-view .wdr-ui-mobile div,
#wdr-pivot-view .wdr-ui-mobile p,
#wdr-pivot-view .wdr-ui-mobile a,
#wdr-pivot-view .wdr-ui-mobile table,
#wdr-pivot-view .wdr-ui-mobile th,
#wdr-pivot-view .wdr-ui-mobile tr,
#wdr-pivot-view .wdr-ui-mobile td,
#wdr-pivot-view .wdr-ui-mobile ul,
#wdr-pivot-view .wdr-ui-mobile li,
#wdr-pivot-view .wdr-ui-mobile input,
#wdr-pivot-view .wdr-ui-mobile textarea,
#wdr-pivot-view .wdr-ui-mobile select,
#wdr-pivot-view .wdr-ui-mobile option {
    font-size: @font-size-mobile;
    -webkit-border-radius: 0;
    border-radius: 0;
}

    #wdr-pivot-view .wdr-ui-mobile input[type="text"] {
        height: 28px;
        line-height: 28px;
        border: @ui-border;
        background-color: rgb(255, 255, 255);
    }

    #wdr-pivot-view .wdr-ui-mobile select {
        background-color: rgb(255, 255, 255);
    }

#wdr-pivot-view .wdr-ui a {
    font-weight: normal;
}

#wdr-pivot-view .wdr-ui ul > li:before {
    width: 0px !important;
    height: 0px !important;
    margin: 0;
    padding: 0;
    border: 0;
}

#wdr-pivot-view a.wdr-ui {
    color: inherit;
    font-weight: normal;
}

    #wdr-pivot-view a.wdr-ui:hover {
        color: inherit;
        font-weight: normal;
        text-decoration: none;
    }

#wdr-pivot-view input.wdr-ui {
    font-size: 12px;
    outline: none;
}

#wdr-pivot-view input.wdr-ui-mobile {
    font-size: @font-size-mobile;
}

#wdr-pivot-view span.wdr-ui {
    font-size: 11px;
}

#wdr-pivot-view span.wdr-ui-mobile {
    font-size: @font-size-mobile;
}

#wdr-pivot-view div.wdr-ui-clear {
    clear: both;
}

#wdr-pivot-view input[type=text].wdr-ui-text-input,
#wdr-pivot-view input[type=number].wdr-ui-text-input {
    /*vertical-align: -webkit-baseline-middle;
    vertical-align: -moz-middle-with-baseline;*/
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    width: 100%;
    height: 38px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding-right: 10px;
    padding-left: 10px;
    text-align: left;
    color: @ui-element-text-color;
    font-size: @ui-element-font-size;
    background: @background-ui-element-base-color;
    border: @ui-element-border;
}

    #wdr-pivot-view input[type=text].wdr-ui-text-input.wdr-ui-prompt {
        color: @ui-prompt-color;
        font-style: italic;
    }

    #wdr-pivot-view input[type=text].wdr-ui-text-input.wdr-ui-mobile {
        height: 30px;
    }

#wdr-pivot-view .wdr-ui textarea.wdr-ui-text-area {
    resize: none;
    border: @ui-border;
    padding: 1px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin: 0;
    color: @ui-text-area-color;
    font-size: @ui-element-font-size;
}

    #wdr-pivot-view .wdr-ui textarea.wdr-ui-text-area:focus {
        outline: none;
    }
// icons
#wdr-pivot-view {
    [class^="wdr-ui-icon-"]:before, 
    [class*=" wdr-ui-icon-"]:before, 
    .wdr-ui-icon:before {
        /* use !important to prevent issues with browser extensions that change fonts */
        font-family: webdatarocks-icons !important;
        speak: none;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .wdr-icon-act_add:before {
        content: "\e900";
    }

    .wdr-icon-act_calc:before {
        content: "\e90a";
    }

    .wdr-icon-act_check:before {
        content: "\e902";
    }

    .wdr-icon-act_close:before {
        content: "\e908";
    }

    .wdr-icon-act_close_small:before {
        content: "\e904";
    }

    .wdr-icon-act_filter:before {
        content: "\e905";
    }

    .wdr-icon-act_font:before {
        content: "\e906";
    }

    .wdr-icon-act_search:before {
        content: "\e913";
    }

    .wdr-icon-act_table_settings:before {
        content: "\e90c";
    }

    .wdr-icon-act_table_sort:before {
        content: "\e90d";
    }

    .wdr-icon-act_trash:before {
        content: "\e908";
    }

    .wdr-icon-arrow_down:before {
        content: "\e90f";
    }

    .wdr-icon-arrow_left:before {
        content: "\e910";
    }

    .wdr-icon-arrow_right:before {
        content: "\e911";
    }

    .wdr-icon-arrow_up:before {
        content: "\e912";
    }

    .wdr-icon-count_down:before {
        content: "\e926";
    }

    .wdr-icon-count_up:before {
        content: "\e927";
    }

    .wdr-icon-dd_connect_csv:before {
        content: "\e928";
    }

    .wdr-icon-dd_connect_json:before {
        content: "\e929";
    }

    .wdr-icon-dd_export_bar:before {
        content: "\e92a";
    }

    .wdr-icon-dd_export_bar_hor:before {
        content: "\e92b";
    }

    .wdr-icon-dd_export_bar_lane:before {
        content: "\e92c";
    }

    .wdr-icon-dd_export_bar_stack:before {
        content: "\e92d";
    }

    .wdr-icon-dd_export_line:before {
        content: "\e92e";
    }

    .wdr-icon-dd_export_pie:before {
        content: "\e92f";
    }

    .wdr-icon-dd_export_scatter:before {
        content: "\e930";
    }

    .wdr-icon-dd_format:before {
        content: "\e931";
    }

    .wdr-icon-dd_format_1:before {
        content: "\e932";
    }

    .wdr-icon-dd_open_local:before {
        content: "\e933";
    }

    .wdr-icon-dd_open_remote:before {
        content: "\e934";
    }

    .wdr-icon-dd_save_exel:before {
        content: "\e935";
    }

    .wdr-icon-dd_save_html:before {
        content: "\e936";
    }

    .wdr-icon-dd_save_image:before {
        content: "\e937";
    }

    .wdr-icon-dd_save_pdf:before {
        content: "\e938";
    }

    .wdr-icon-dd_save_print:before {
        content: "\e939";
    }

    .wdr-icon-direction_direction_y:before {
        content: "\e93a";
    }

    .wdr-icon-menu_connect:before {
        content: "\e93c";
    }

    .wdr-icon-menu_export:before {
        content: "\e93d";
    }

    .wdr-icon-menu_fields:before {
        content: "\e93e";
    }

    .wdr-icon-menu_format:before {
        content: "\e93f";
    }

    .wdr-icon-menu_fullscreen_close:before {
        content: "\e940";
    }

    .wdr-icon-menu_fullscreen_open:before {
        content: "\e941";
    }

    .wdr-icon-menu_grid:before {
        content: "\e942";
    }

    .wdr-icon-menu_open:before {
        content: "\e943";
    }

    .wdr-icon-menu_options:before {
        content: "\e944";
    }

    .wdr-icon-menu_save:before {
        content: "\e945";
    }

    .wdr-icon-notif_arrow:before {
        content: "\e949";
    }

    .wdr-icon-notif_confirm:before {
        content: "\e901";
    }

    .wdr-icon-notif_info:before {
        content: "\e948";
    }

    .wdr-icon-notif_warning:before {
        content: "\e949";
    }
}
.wdr-ui-icon {
    display: block;
    position: absolute;
    font-family: 'webdatarocks-icons' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    width: auto;
    height: auto;
    height: initial;
    background: transparent;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-transition: color 0.3s;
    transition: color 0.3s;
}

// mixins
.wdr-ui-vam {
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
}
.wdr-ui-icon-vam {
    .wdr-ui-icon;
    .wdr-ui-vam;
}

.wdr-ui-ham {
    left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
}

.wdr-ui-icon-ham {
    .wdr-ui-icon;
    .wdr-ui-ham;
}

.wdr-ui-icon-c {
    .wdr-ui-icon;
    .wdr-ui-vam;
    .wdr-ui-ham;
}

.wdr-shadow {
    -webkit-box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

// labels 
#wdr-pivot-view {
    span.wdr-ui-label {
        color: @ui-label-color;
        font-size: @ui-label-font-size;

        * {
            font-size: @ui-label-font-size;
        }

        b {
            font-weight: bold;
        }
    }

    span.wdr-ui-label-light {
        color: @ui-label-color-light;
    }
}
// buttons
#wdr-pivot-view,
#wdr-toolbar-wrapper {
    a.wdr-ui-btn {
        -webkit-appearance: none;
        -moz-appearance: none;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        text-transform: uppercase;
        position: relative;
        outline: none;
        cursor: pointer;
        text-align: center;
        display: inline-block;
        vertical-align: top;
        -webkit-transition: color 0.3s;
        transition: color 0.3s;
        font-size: @ui-btn-font-size;
        font-weight: bold;
        padding: 10px 12px;
        letter-spacing: 0.5px;
        min-width: 90px;
        color: @ui-btn-text-color;
        border-radius: 4px;
        background: @ui-btn-color;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    a.wdr-ui-btn:hover {
        font-weight: bold;
        color: @ui-btn-color-dark-hover;
        background: @ui-btn-color-hover;
        /*-webkit-box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        box-shadow: 0 2px 6px rgba(0,0,0,0.2);*/
    }

    a.wdr-ui-btn:hover,
    a.wdr-ui-btn:focus,
    a.wdr-ui-btn:active {
        outline: none;
    }

    a.wdr-ui-btn-dark {
        color: @ui-btn-dark-text-color;
        background: @ui-background-dark;
    }

    a.wdr-ui-btn-dark:hover {
        font-weight: bold;
        color: @ui-btn-dark-text-color;
        background: @ui-btn-color-dark-hover;
    }

    a.wdr-ui-btn-light {
        background: @ui-background-superlight;
        border: @ui-element-border;
        color: @ui-btn-light-text-color;
        font-weight: normal;
        text-transform: none;
        padding: 11px 11px;
    }

    a.wdr-ui-btn-light:hover {
        background: @ui-btn-color-hover;//#f8f8f8;
        /*border-color: @ui-border-color-light;*/
        color: @ui-btn-light-text-color;
        font-weight: normal;
    }

    a.wdr-ui-btn-superlight {
        background: @background-ui-element-base-color;
        /*border: @ui-element-border;*/
        color: @ui-btn-light-text-color;
        font-weight: normal;
        text-transform: none;
        padding: 11px 11px;
        padding-top: 9px;
    }

    a.wdr-ui-btn-superlight:hover {
        background: @ui-btn-color-light-hover;
        /*border-color: @ui-border-color-light;*/
        color: @ui-btn-light-text-color;
        font-weight: normal;
    }

    a.wdr-ui-btn.wdr-ui-btn-close {
        font-size: 0;
        border: none;
        background: none;
        min-width: auto;
        min-width: initial;
        width: 30px;
        height: 30px;
        padding: 2px;
    }

    a.wdr-ui-btn.wdr-ui-btn-close:before {
        .wdr-ui-icon;
        content: "\e908";
        font-size: 26px;
    }

    .wdr-ui-btns-row {
        font-size: 0;

        .wdr-ui-btn {
            margin-right: 20px;
            height: 38px;
        }

        .wdr-ui-btn:last-child {
            margin-right: 0;
        }
    }

    .wdr-icon-notif_arrow:before {
        content: "\e949";
    }
}

// toggle button
#wdr-pivot-view {
    a.wdr-ui-toggle-btn {
        color: @ui-btn-toggle-text-color;
        font-size: @ui-btn-font-size;
        line-height: 36px;
        display: inline-block;
        text-align: center;
        cursor: pointer;
        border: @ui-element-border;
        background: @background-ui-element-base-color;
    }

    a.wdr-ui-toggle-btn.wdr-selected {
        color: @ui-btn-toggle-selected-text-color;
        background: @ui-background-light;
    }

    a.wdr-ui-toggle-btn:hover {
        color: @ui-btn-toggle-selected-text-color;
        background: @ui-btn-color-hover;
    }

    a.wdr-ui-toggle-btn-dark {
        color: @ui-btn-toggle-selected-text-color;
        background: @ui-background-light;
    }

    a.wdr-ui-toggle-btn-dark.wdr-selected {
        color: @ui-btn-toggle-dark-selected-text-color;
        background: @ui-background-dark;
        border-color: @ui-border-color-dark;
    }

    a.wdr-ui-toggle-btn-dark.wdr-selected:hover {
        background: @ui-btn-color-dark-hover;
        border-color: @ui-btn-color-dark-hover;
    }
}

#wdr-pivot-view a.wdr-ui-link-btn {
    background: none;
    font-size: 12px;
    text-decoration: underline;
    line-height: normal;
    width: auto;
    width: initial;
    height: auto;
    height: initial;
    border: none;
    padding: 0;
    cursor: pointer;
    
    &:hover {
        background: none;
        text-decoration: none;
    }

    &.wdr-ui-pressed {
        background: none;
        text-decoration: underline;
    }
}

// checkbox
#wdr-pivot-view {
    a.wdr-ui-checkbox {
        cursor: pointer;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        display: inline-block;
        vertical-align: middle;

        .wdr-ui-label {
            padding-left: 30px;
            position: relative;
            line-height: 1.2;
            color: @ui-element-text-color;
            font-size: @ui-element-font-size;

            &:before {
                content: '';
                display: inline-block;
                box-sizing: border-box;
                position: absolute;
                left: 0;
                top: 0;
                width: 18px;
                height: 18px;
                border: @ui-element-border;
                background: @background-ui-element-base-color;
            }

            &:before:hover {
                border-color: @ui-border-color;
            }

            &:after {
                .wdr-ui-icon-vam;
                opacity: 0;
                content: '\e901';
                color: #fff;
                font-size: 16px;
                left: 1px;
            }
        }


        &.wdr-selected {
            .wdr-ui-label:after {
                opacity: 1;
            }

            .wdr-ui-label:before {
                border: none;
                background: @ui-background-dark;
            }
        }

        &.wdr-ui-semi-selected {
            .wdr-ui-label:after {
                content: '';
                background: @ui-background-dark;
                width: 10px;
                height: 10px;
                left: 4px;
                top: 9px;
                opacity: 1;
            }
        }
    }
}

// dropdown
#wdr-pivot-view {
    div.wdr-ui-dropdown {
        text-align: left;
        display: inline-block;
        vertical-align: top;
        width: auto;
        width: initial;
        position: relative;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;

        .wdr-ui-dropdown-btn {
            display: block;
            position: relative;
            background-color: @background-ui-element-base-color;
            text-align: left;
            border: @ui-element-border;
            padding: 9px 35px 10px 10px;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            cursor: pointer;
            height: 38px;

            .wdr-ui-label {
                color: @ui-element-text-color;
                font-size: @ui-element-font-size;
                vertical-align: top;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                display: inline-block;
                width: 100%;
            }
        }

        .wdr-ui-dropdown-btn:after {
            .wdr-ui-icon-vam;
            content: "\e902";
            right: 8px;
            font-size: 21px;
            color: @ui-icon-color;
        }

        .wdr-ui-dropdown-btn.wdr-selected:after {
            content: "\e904";
        }

        .wdr-ui-dropdown-btn.wdr-selected + .wdr-ui-dropdown-list {
            margin-top: 5px;
            visibility: visible;
            opacity: 1;
        }

        .wdr-ui-dropdown-list {
            display: block;
            position: absolute;
            z-index: 3;
            left: 0;
            top: 100%;
            border: @ui-element-border;
            margin-top: 20px;
            visibility: hidden;
            width: 100%;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            opacity: 1;
            background: @background-ui-element-base-color;

            ul {
                overflow-y: auto;
                max-height: 300px;

                li {
                    display: block;
                    border-bottom: @ui-element-inner-border;
                    padding: 11px 9px;
                    cursor: pointer;
                    position: relative;

                    .wdr-ui-label {
                        color: @ui-element-text-color;
                        font-size: @ui-element-font-size;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: inline-block;
                        width: 100%;
                    }

                    &:last-child {
                        border-bottom: none;
                    }

                    &:hover {
                        background: @ui-background-light;
                    }

                    &.wdr-selected {
                        background: @ui-background-light;
                        padding-right: 25px;
                        position: relative;

                        &:after {
                            .wdr-ui-icon-vam;
                            content: '\e901';
                            color: @ui-icon-color;
                            right: 8px;
                            font-size: 18px;
                        }
                    }
                }
            }

            &:before,
            &:after {
                content: '';
                position: absolute;
                display: block;
                left: 50%;
                -webkit-transform: translateX(-50%);
                -ms-transform: translateX(-50%);
                transform: translateX(-50%);
            }

            &:before {
                width: 0px;
                height: 0px;
                border-style: solid;
                border-width: 0 5.5px 6px 5.5px;
                border-color: transparent transparent @ui-element-border-color transparent;
                top: -6px;
            }

            &:after {
                width: 0px;
                height: 0px;
                border-style: solid;
                border-width: 0 4.5px 5px 4.5px;
                border-color: transparent transparent @background-ui-element-base-color transparent;
                top: -5px;
            }
        }

    }
}

/* popup */
#wdr-pivot-view {
    div.wdr-ui-window {
        .wdr-shadow;
        max-width: 100%;
        background: @background-base-color;
        margin: 0 auto 0;
        border: @ui-pop-up-border;
        padding: 24px 30px 30px;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        position: absolute;
        z-index: 7;

        .wdr-popup-header {
            min-height: 40px;
            margin-bottom: 10px;
            
            &.wdr-ph-simple {
                margin-bottom: 24px;
            }

            .wdr-popup-icons-row {
                margin-bottom: 25px;
                margin-top: 7px;
            }

            .wdr-ui-btns-row {
               position: absolute;
               right: 30px;
            }
        }

        .wdr-popup-title {
            color: @ui-title-color;
            font-size: @ui-title-font-size;
            display: block;
            padding: 5px 0;
        }

        .wdr-popup-subtitle {
            color: @ui-subtitle-color;
            font-size: @ui-subtitle-font-size;
            margin-top: 5px;
            display: block;
        }
    }

    div.wdr-ui-popup {
        z-index: 12;

        &.wdr-layout-tablet {
            .wdr-popup-header {

            }
        }

        &.wdr-layout-mobile {
            padding: 20px;

            .wdr-popup-subtitle {
                display: none;
            }

            .wdr-ui-btns-row {
                right: 20px;
            }
        }

        &.wdr-layout-mobile-small {
            .wdr-popup-header {
                margin-bottom: 10px;
                
                .wdr-ui-col {
                    width: 100%;
                    margin-bottom: 10px;
                }

                .wdr-ui-btns-row {
                    position: relative;
                    right: initial;
                    right: auto;
                    text-align: left;

                    .wdr-ui-btn {
                        width: ~"calc(50% - 10px)";
                        width: ~"-webkit-calc(50% - 10px)";
                    }
                }

                .wdr-popup-title {
                    font-size: @ui-title-font-size-smaller;
                    line-height: 1;
                }
            }
        }
    }
}

#wdr-toolbar-wrapper,
#wdr-pivot-view {
    .wdr-ui {
        .wdr-ui-row {
            font-size: 0;
        }

        .wdr-ui-col,
        .wdr-ui-col-2,
        .wdr-ui-col-3,
        .wdr-ui-col-9 {
            display: inline-block;
            vertical-align: top;
            min-height: 1px;
        }

        .wdr-ui-col-2 {
            width: 50%;
        }

        .wdr-ui-col-3 {
            margin-right: 30px;
            width: ~"-webkit-calc(33.33% - 20px)";
            width: ~"calc(33.33% - 20px)";
            
            &:nth-child(3n+3) {
                margin-right: 0;
            }
        }

        .wdr-ui-col-9 {
            margin-right: 1px;
            margin-bottom: 1px;
            width: ~"-webkit-calc(11.11% - 0.9px)";
            width: ~"calc(11.11% - 0.9px)";
            
            &:nth-child(9n+9) {
                margin-right: 0;
            }
        }

        .wdr-vam {
            vertical-align: middle;
        }

        .wdr-vat {
            vertical-align: top;
        }

        .wdr-tar {
            text-align: right;
        }

        .wdr-tal {
            text-align: left;
        }

        .wdr-tac {
            text-align: center;
        }

        .wdr-mb30 {
            margin-bottom: 30px;
        }
    }
}

#wdr-pivot-view {
    .wdr-helper {
        .wdr-shadow;
        display: inline-block;
        background: @ui-background-light;
        opacity: 0.9;
        padding: 10px 9px;
        font-family: @font-family;
        font-size: @ui-label-font-size;
        font-weight: bold;
        border: @ui-border-light;
        pointer-events: none;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        max-width: 250px;
        color: @theme-text-color;
    }

    .wdr-header-helper {
        text-transform: uppercase;
        border: none;
        background: @grid-filter-color;
        padding: 8px 6px;
        font-size: @font-size;
        border: @ui-border;
        cursor: move;
    }

    .wdr-drop-indicator {
        height: 2px;
        background: @ui-background-dark;
        pointer-events: none;
    }
}

#wdr-pivot-view div.wdr-ui-panel {
    position: relative;
    background: @background-base-color;
    border: @ui-border;
}

#wdr-pivot-view div.wdr-ui-modal-overlay {
    background-color: white;
    opacity: 0.8;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 11;
    width: 100%;
    height: 100%;
    
    &.wdr-ui-opaque {
        opacity: 1;
    }

    &.wdr-ui-transparent {
        opacity: 0;
    }

}

#wdr-pivot-view div.wdr-ui-toolbar {
    background: @background-base-color;
    border-top: 1px dotted #dcdcdc;
    border-left: none;
    border-right: none;
    border-bottom: none;
    margin-bottom: 0px;
}

#wdr-pivot-view div.wdr-ui-hgroup {
    overflow: hidden;
}

    #wdr-pivot-view div.wdr-ui-hgroup > * {
        float: left;
    }

#wdr-pivot-view div.wdr-ui-vgroup > * {
    display: block;
}

#wdr-pivot-view ul.wdr-ui-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

#wdr-pivot-view .wdr-ui-disabled,
#wdr-toolbar-wrapper .wdr-ui-disabled {
    pointer-events: none;
    cursor: default;
    opacity: 0.5;
}

// virtual list
#wdr-pivot-view {
    div.wdr-ui-vlist {
        position: relative;
        overflow-x: hidden;
        overflow-y: auto;

        ul.wdr-ui-list {
            position: absolute;
            overflow: hidden;
            width: 100%;
            top: 0;
            background: @background-ui-element-base-color;
        }

        div.wdr-ui-vlist-placeholder {
            width: 100%;
        }
    }
}

#wdr-pivot-view div.wdr-ui-divider {
    background-color: @ui-background-light;
    position: absolute;
    z-index: 1;

    &:hover {
        background-color: darken(@ui-background-light, 2%);
    }

    &:before {
        .wdr-ui-icon-c;
        content: "\e910";
        font-size: 13px;
        color: @ui-border-color;
    }

    &.wdr-ui-horizontal {
        left: 0;
        right: 0;
        height: 9px;
        
        &:hover {
            cursor: row-resize;
        }

        &:before {
            top: -2px;
        }
    }
    
    &.wdr-ui-vertical {
        top: 0;
        bottom: 0;
        width: 9px;
        
        &:hover {
            cursor: col-resize;
        }

        &:before {
            left: -2px;
            .rotate(90);
        }
    }
}

#wdr-pivot-view .wdr-ui-toolbar-mobile {
    height: 48px;
    border-bottom: @ui-border;
}

    #wdr-pivot-view .wdr-ui-toolbar-mobile .wdr-ui-header-display {
        pointer-events: none;
        position: absolute;
        top: 12px;
        font-size: 17px;
        font-weight: bold;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        text-align: center;
        display: inline-block;
        width: 100%;
    }

@media only screen and (max-width:560px) {
    #wdr-pivot-view .wdr-ui-toolbar-mobile .wdr-ui-header-display {
        font-size: 14px;
        top: 15px;
    }
}

#wdr-pivot-view .wdr-ui-right {
    position: absolute;
    right: 0;
    top: 0;
}

#wdr-pivot-view .wdr-ui-left {
    position: absolute;
    left: 0;
    top: 0;
}

#wdr-pivot-view a.wdr-ui-btn.wdr-disabled {
    pointer-events: none;
    cursor: default;
    color: #aaa;
    opacity: 0.4;
}

#wdr-pivot-view a.wdr-ui-btn:hover.wdr-disabled {
    background: @background-ui-element-base-color;
}

#wdr-pivot-view a.wdr-ui-btn:active.wdr-disabled {
    background: @background-ui-element-base-color;
}


#wdr-pivot-view .wdr-credits {
    position: absolute;
    right: 0;
    bottom: -12px;
    opacity: 0.5;
    
    a {
        font-size: 9px !important;
    }
}

/* ===== PIVOT UI END ===== */

/* Filter */

#wdr-pivot-view #wdr-filter-view {
    width: 500px;
    min-width: 320px;

    .wdr-popup-header {
        margin-bottom: 25px;

        .wdr-ui-row {
            .wdr-ui-btns-row {
                white-space: nowrap;
                position: absolute;
                right: 0;
            }
        }

        .wdr-popup-title {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
    }

    .wdr-bold-btn {
        font-weight: bold;
        text-transform: uppercase;
    }

    a.wdr-ui-toggle-btn.wdr-sort-btn {
        width: 70px;
        .wdr-bold-btn;
    }

    #wdr-sort-label {
        margin-right: 10px;
    }

    .wdr-sort-btns {
        display: inline-block;
        margin-bottom: 30px;

        .wdr-sort-btn:first-child {
            border-right: none;
        }
    }

    #wdr-top10-btn {
        position: relative;
        width: 140px;
        margin-bottom: 30px;
        .wdr-bold-btn;

        .wdr-btn-canceltopx {
            display: none;
            position: absolute;
            right: 4px;
            top: 8px;
            width: 12px;
            height: 16px;

            &:hover {
                text-decoration: none;
            }

            &:before {
                .wdr-ui-icon;
                content: "\e908";
                font-size: 15px;
                left: -15px;
            }
        }

        &.wdr-not-empty {
            padding-right: 20px;

            .wdr-btn-canceltopx {
                display: block;
            }
        }

        &.wdr-narrow {
            width: 90px;

            &.wdr-not-empty {
                padding-left: 12px;
            }

            .wdr-btn-canceltopx {
                font-size: 0;
                right: 6px;
            }
        }

        &.wdr-selected {
            .wdr-btn-canceltopx {
                color: @background-base-color;
            }
        }
    }

    #wdr-add-group-btn {
        .wdr-bold-btn;
        margin-right: 20px;
        margin-bottom: 30px;
        width: 100px;
    }

    .wdr-filters-table {
        border: @ui-element-border;
        margin-bottom: 30px;

        .wdr-ui-checkbox.wdr-selected {
            font-weight: bold;
        }

        .wdr-filters-table-content {
            position: relative;

            #wdr-members-filter-list {
                height: 100%;
                overflow-y: auto;

                li {
                    border-bottom: @ui-element-inner-border;
                    padding: 10px 9px;
                    cursor: pointer;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;

                    .wdr-expand-toggle-btn {
                        color: @ui-border-color;
                        display: inline-block;
                        font-size: @ui-btn-font-size;
                        position: relative;
                        min-height: 1px;
                        width: 30px;
                    }

                    .wdr-expand-toggle-btn:before {
                        .wdr-ui-icon;
                        content: "\e911";
                        top: -17px;
                        text-align: center;
                        width: 30px;
                        padding: 8px 0;
                    }

                    .wdr-expand-toggle-btn.wdr-selected:before {
                        content: "\e90f";
                    }

                    .wdr-ungroup-btn {
                        position: absolute;
                        right: 10px;
                    }
                }
            }
        }

        .wdr-filters-table-header {
            border-bottom: @ui-element-border;
            background: @ui-background-light;
            padding: 10px 9px;
            position: relative;
            height: 37px;

            .wdr-btn-collapse {
                margin-left: 8px;
            }

            #wdr-select-counter {
                display: inline-block;
                vertical-align: middle;
                margin-left: 12px;
                color: @ui-label-color-light;
                font-size: 12px;
            }

            .wdr-search-wrap {
                position: absolute;
                top: 0;
                right: 0;
                width: 100px;
                height: 100%;
                border-left: @ui-element-border;

                .wdr-search-inp {
                    position: absolute;
                    right: 0;
                    width: 100%;
                    height: 36px;
                    padding-left: 10px;
                    padding-right: 39px;
                    font-size: @ui-element-font-size;
                    background: @background-ui-element-base-color;
                    opacity: 0;
                    border: none;
                    visibility: hidden;
                    border-left: @ui-element-border;
                }

                .wdr-icon-act_close {
                    opacity: 0;
                    visibility: hidden;
                    position: absolute;
                    right: 0;
                    top: 0;
                    height: 100%;
                    width: 39px;
                    cursor: pointer;
                    color: @ui-icon-color;

                    &:before {
                        font-size: 26px;
                        position: absolute;
                        top: 5px;
                        left: 8px;
                    }

                    &:hover {
                        color: @ui-icon-color-dark;
                    }
                }

                .wdr-search-btn {
                    position: absolute;
                    height: 36px;
                    width: 100%;
                    font-size: 14px;
                    text-align: center;
                    cursor: pointer;
                    border: none;
                    background: transparent;
                    color: @ui-label-color-light;
                    padding-top: 10px;
                    padding-right: 16px;

                    .wdr-icon-act_search {
                        color: @ui-icon-color;
                        position: absolute;
                        top: 5px;
                        right: 7px;

                        &:before {
                            font-size: 25px;
                        }
                    }

                    &:hover {
                        color: @ui-label-color;

                        .wdr-icon-act_search,
                        .wdr-icon-act_search:before {
                            color: @ui-label-color;
                        }
                    }
                }

                &.wdr-search-opened {
                    width: 50%;
                    height: 100%;
                    border-left: none;

                    .wdr-icon-act_close,
                    .wdr-search-inp {
                        visibility: visible;
                        opacity: 1;
                    }

                    #wdr-select-counter {
                        display: none;
                    }

                    .wdr-search-btn {
                        display:none;

                        .wdr-icon-act_search:before {
                            color: @ui-prompt-icon-color;
                        }
                    }
                }
            }
        }
    }

    .wdr-filters-table:last-child {
        margin-bottom: 0;
    }

    .wdr-filters-table.wdr-ft-contentheight-220 .wdr-filters-table-content {
        height: 222px;
    }
    // top x
    #wdr-filters-topx-view {
        #wdr-top-amount-input {
            width: 70px;
            vertical-align: top;
        }

        #wdr-topx-btns {
            margin-left: 10px;
            display: inline-block;
            width: 140px;

            a.wdr-ui-toggle-btn.wdr-topx-btn {
                width: 50%;
            }

            .wdr-topx-btn:first-child {
                border-right: none;
            }
        }

        #wdr-top-measures-dropdown {
            width: ~"calc(100% - 230px)";
            width: ~"-webkit-calc(100% - 230px)";
            min-width: 120px;
            display: inline-block;
            vertical-align: top;
            margin-left: 10px;
        }
    }
    // add group
    #wdr-add-group-view {
        #wdr-save-btn {
            width: 80px;
            color: @ui-label-color;
        }

        #wdr-group-name-input {
            width: ~"-webkit-calc(100% - 90px)";
            width: ~"calc(100% - 90px)";
            margin-right: 10px;
        }
    }

    .wdr-filters-subview {
        margin-bottom: 20px;
        margin-top: -10px;
        background: @ui-background-superlight;
        border: @ui-border-superlight;
        position: relative;
        padding: 14px 13px;
        font-size: 0;

        &:before,
        &:after {
            content: '';
            position: absolute;
            display: block;
        }

        &:before {
            width: 0px;
            height: 0px;
            border-style: solid;
            border-width: 0 6.5px 7px 6.5px;
            border-color: transparent transparent @ui-border-color-superlight transparent;
            right: 63px;
            top: -7px;
        }

        &:after {
            width: 0px;
            height: 0px;
            border-style: solid;
            border-width: 0 5.5px 6px 5.5px;
            border-color: transparent transparent @ui-background-superlight transparent;
            right: 64px;
            top: -6px;
        }

        &#wdr-add-group-view {
            &:before {
                right: 150px;
            }

            &:after {
                right: 151px;
            }
        }
    }
    .wdr-filter-pages-layout {
        .wdr-topx-col {
            text-align: left;
        }
        #wdr-add-group-view {
            &:before {
                right: initial;
                left: 45px;
            }

            &:after {
                right: initial;
                left: 46px;
            }
        }
    }
    /* layouts */
    &.wdr-layout-mobile-small {
        .wdr-popup-header {
            .wdr-ui-col, .wdr-ui-col-2 {
                width: 100%;
            }
              
            .wdr-ui-col-2 {
                margin-bottom: 10px;
            }

            .wdr-ui-btns-row {
                position: relative;
            }

            .wdr-popup-title {
                line-height: 1.1;
            }
        }

        a.wdr-ui-toggle-btn.wdr-sort-btn {
            width: 60px;
        }

        #wdr-sort-label {
            display: none;
        }

        #wdr-select-counter {
            display: none !important;
        }

        #wdr-filters-topx-view {
            #wdr-top-amount-input,
            #wdr-topx-btns {
                width: ~"calc(50% - 5px)";
                width: ~"-webkit-calc(50% - 5px)";
            }

            #wdr-top-measures-dropdown {
                width: 100%;
                min-width: auto;
                min-width: initial;
                margin: 0;
                margin-top: 10px;
            }
        }
    }
}

/* Pivot view */

#wdr-pivot-view {
    min-width: 300px;
    min-height: 200px;
    position: relative;
    background: @background-base-color;
    border: @ui-component-border;
    outline: none;
    box-sizing: border-box;
}

    #wdr-pivot-view #wdr-branding-bar {
        background-color: @background-base-color;
        border-top: @ui-component-border;
        width: 100%;
        height: 21px;
        position: absolute;
        bottom: 0px;
        z-index: 2;
    }

        #wdr-pivot-view #wdr-branding-bar > span {
            line-height: 21px;
            color: @ui-label-color-light;

            a {
                color: lighten(#df3800, 30%);
            }
        }

        #wdr-pivot-view #wdr-branding-bar #wdr-version-label {
            float: left;
            margin-left: 5px;
        }

@media only screen and (max-width:660px) {
    #wdr-pivot-view #wdr-branding-bar span, 
    #wdr-pivot-view #wdr-branding-bar span a {
        font-size: 11px;
    }
}

@media only screen and (max-width:520px) {
    #wdr-pivot-view #wdr-branding-bar #wdr-version-label {
        display: none;
    }
}

#wdr-pivot-view #wdr-branding-bar #wdr-link {
    float: right;
    margin-right: 5px;
    vertical-align: top;
}

#wdr-pivot-view #wdr-branding-bar #wdr-info-icon {
    display: inline-block;
    height: 13px;
    width: 13px;
    margin-left: 5px;
    margin-right: 2px;
    color: @ui-border-color-light;
    font-size: 16px;
    cursor: pointer;

    &:before {
        .wdr-ui-icon;
        content: "\ea0c";
    }
}

@media all and (max-width:600px) {
    #wdr-pivot-view {
        min-width: initial;
        min-height: initial;
    }
}

#wdr-pivot-view #wdr-grid-view {
    background-color: @background-base-color;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

#wdr-pivot-view .wdr-grid-column {
    width: 100px;
}

#wdr-pivot-view .wdr-grid-row {
    height: 30px;
}

#wdr-pivot-view .wdr-grid-column-mobile {
    width: 100px;
}

#wdr-pivot-view .wdr-grid-row-mobile {
    height: 30px;
}

#wdr-pivot-view {
    span.wdr-ui-label.wdr-pivot-title {
        text-align: center;
        font-weight: bold;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: block;
        padding: 4px 10px 5px;
    }
}

/* Fields */
#wdr-pivot-view .wdr-fields-view-wrap {
    position: absolute;
    z-index: 11;
    right: 0;
    top: 0;

    &.wdr-fields-opened {
        position: relative;
        z-index: 12;

        #wdr-btn-open-fields {
            display: none;
        }
    }

    #wdr-btn-open-fields {
        font-size: 0;
        border: none;
        border-radius: 0;
        /*border-bottom-left-radius: 4px;*/
        background-color: @background-ui-element-base-color;
        min-width: auto;
        min-width: initial;
        padding: 9px;
        width: 44px;
        height: 44px;
        border-left: @ui-element-border;
        border-bottom: @ui-element-border;
        -webkit-box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

        &:hover {
            background-color: @ui-background-light;
        }

        &:before {
            .wdr-ui-icon;
            padding-left: 1px;
            content: "\e907";
            font-size: 24px;
            color: @ui-icon-color-dark;
        }
    }
}

#wdr-pivot-view #wdr-fields-view {
    &.wdr-pivot-fields {
        width: 780px;
        min-width: 450px;
    }

    .wdr-popup-header {
        .wdr-ui-col:first-child {
            max-width: 300px;

            span.wdr-ui-label {
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }
        }

        #wdr-btn-add-measure {
            padding-right: 40px;
            max-width: 250px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            &:before {
                .wdr-ui-icon-vam;
                right: 4px;
                color: @ui-icon-color;
                font-size: 28px;
            }

            &:hover:before {
                color: @ui-icon-color-dark;
            }
        }
    }

    .wdr-popup-content {
        .wdr-ui-row {
            height: 330px;
        }

        .wdr-ui-col-3 {
            height: 100%;
        }

        .wdr-list-wrap {
            border: @ui-element-border;
            position: relative;

            .wdr-list-header {
                background: @ui-background-light;
                border-bottom: @ui-element-border;
                padding: 5px 10px;
                position: relative;

                span.wdr-ui-label {
                    color: @ui-label-color-light;
                }

                &.wdr-list-header-wide {
                    padding: 0px;
                    height: 35px;
                }

                .wdr-list-header-label-wrap {
                    width: ~"calc(100% - 40px)";
                    width: ~"-webkit-calc(100% - 40px)";
                    padding: 10px;
                }

                #wdr-btn-collapse-expand-all {
                    margin-left: 8px;
                }

                .wdr-search-wrap {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 40px;
                    height: 100%;
                    border-left: @ui-element-border;
                    z-index: 9;

                    .wdr-search-inp {
                        z-index: 2;
                        position: absolute;
                        right: 0;
                        width: 100%;
                        height: 34px;
                        padding-left: 39px;
                        padding-right: 39px;
                        color: @ui-element-text-color;
                        font-size: @ui-element-font-size;
                        background: @background-ui-element-base-color;
                        opacity: 0;
                        border: none;
                        visibility: hidden;
                    }

                    .wdr-icon-act_close {
                        opacity: 0;
                        visibility: hidden;
                        position: absolute;
                        right: 0;
                        top: 0;
                        height: 100%;
                        width: 39px;
                        cursor: pointer;
                        z-index: 3;
                        color: @ui-icon-color;

                        &:before {
                            font-size: 26px;
                            position: absolute;
                            top: 5px;
                            left: 8px;
                        }

                        &:hover {
                            color: @ui-icon-color-dark;
                        }
                    }


                    .wdr-search-btn {
                        position: absolute;
                        height: 36px;
                        width: 100%;
                        font-size: 14px;
                        text-align: center;
                        cursor: pointer;
                        z-index: 3;
                        border: none;
                        background: transparent;
                        color: @ui-label-color-light;
                        padding-bottom: 4px;
                        padding-right: 16px;

                        .wdr-icon-act_search {
                            color: @ui-icon-color;
                            position: absolute;
                            top: 5px;
                            right: 7px;

                            &:before {
                                font-size: 25px;
                            }
                        }

                        &:hover {
                            color: @ui-label-color;

                            .wdr-icon-act_search,
                            .wdr-icon-act_search:before {
                                color: @ui-label-color;
                            }
                        }
                    }

                    &.wdr-search-opened {
                        width: 100%;
                        height: 100%;
                        border-left: none;

                        .wdr-icon-act_close,
                        .wdr-search-inp {
                            visibility: visible;
                            opacity: 1;
                        }

                        #wdr-select-counter {
                            display: none;
                        }

                        .wdr-search-btn {
                            font-size: 0;
                            width: 40px;
                            left: 0;
                            pointer-events: none;

                            .wdr-icon-act_search:before {
                                color: @ui-prompt-icon-color;
                            }
                        }
                    }
                }
            }

            .wdr-list-content {
                min-height: 37px;

                .wdr-ui-list {
                    height: ~"calc(100% - 2px)";
                    height: ~"-webkit-calc(100% - 2px)";
                    overflow-x: hidden;
                    overflow-y: auto;
                    position: relative;
                }

                li {
                    border-bottom: @ui-element-inner-border;
                    padding: 10px 9px;
                    position: relative;
                    cursor: move;
                    background: @background-ui-element-base-color;
                    height: 38px;
                    white-space: nowrap;

                    span {
                        vertical-align: middle;
                    }

                    #wdr-text-display {
                        font-size: @ui-label-font-size;
                        display: inline-block;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }

                    #wdr-drag-handle {
                        .wdr-ui-icon-vam;
                        color: @ui-border-color-light;
                        font-size: 20px;
                        padding: 8px 6px;
                        right: 0;
                    }

                    #wdr-drag-handle:before {
                        content: "\e910";
                    }
                }

                li.wdr-values {
                    padding-left: 26px;

                    &:before {
                        .wdr-ui-icon;
                        content: "\e914";
                        font-size: 24px;
                        color: @ui-border-color-light;
                        left: 2px;
                        top: 6px;
                    }
                }

                li.wdr-selected {
                    #wdr-text-display {
                        font-weight: bold;
                    }
                }

                #wdr-lst-hierarchies {
                    li {
                        #wdr-text-display {
                            position: absolute;
                            left: 30px;
                            right: 30px;
                        }
                    }
                    // check/uncheck
                    .wdr-check-icon {
                        display: inline-block;
                        width: 18px;
                        height: 18px;
                        margin-right: 5px;
                        cursor: pointer;
                        position: relative;
                    }

                    li.wdr-unchecked:not(.wdr-level-folder) {
                        #wdr-icon-display {
                            .wdr-check-icon;
                            border: @ui-border-light;
                            background: @background-ui-element-base-color;
                        }
                    }

                    li.wdr-checked:not(.wdr-level-folder) {
                        #wdr-icon-display {
                            .wdr-check-icon;
                            background: @ui-background-dark;

                            &:before {
                                .wdr-ui-icon;
                                content: '\e901';
                                color: #fff;
                                font-size: 16px;
                                left: 1px;
                            }
                        }
                    }
                    // folder
                    li.wdr-folder {
                        cursor: pointer;

                        #wdr-text-display {
                            right: 10px;
                        }

                        #wdr-icon-display {
                            display: inline-block;
                            width: 17px;
                            height: 17px;
                            margin-right: 5px;
                            position: relative;

                            &:before {
                                .wdr-ui-icon;
                                font-size: 14px;
                                color: @ui-border-color;
                                padding: 2px;
                            }
                        }

                        &.wdr-collapsed #wdr-icon-display:before {
                            content: "\e903";
                        }

                        &.wdr-expanded #wdr-icon-display:before {
                            content: "\e902";
                        }
                    }
                    // level folder
                    li.wdr-level-folder {
                        #wdr-text-display {
                            left: 52px;
                            right: 20px;
                        }

                        &.wdr-unchecked {
                            #wdr-moreicon-display {
                                .wdr-check-icon;
                                border: @ui-border-light;
                                background: @background-ui-element-base-color;
                            }
                        }

                        &.wdr-checked {
                            #wdr-moreicon-display {
                                .wdr-check-icon;
                                background: @ui-background-dark;
                            }

                            #wdr-moreicon-display:before {
                                .wdr-ui-icon;
                                content: '\e901';
                                color: #fff;
                                font-size: 16px;
                            }
                        }
                    }
                    // measures folder
                    li.wdr-measures-folder {
                        &:after {
                            .wdr-ui-icon-vam;
                            content: "\e914";
                            right: 4px;
                            font-size: 24px;
                            color: @ui-border-color-superlight;
                        }

                        #wdr-text-display {
                            right: 20px;
                        }
                    }
                    // measure
                    li.wdr-measure:not(.wdr-calculated) {
                        &:after {
                            .wdr-ui-icon-vam;
                            content: "\e914";
                            right: 24px;
                            font-size: 24px;
                            color: @ui-border-color-superlight;
                        }

                        #wdr-text-display {
                            right: 50px;
                        }
                    }

                    li.wdr-calculated {
                        #wdr-calc-display {
                            .wdr-ui-icon-vam;
                            cursor: pointer;
                            right: 24px;
                            font-size: 24px;
                            color: @ui-border-color-superlight;
                            width: 24px;
                            height: 24px;

                            &:hover {
                                color: @ui-border-color;
                            }

                            &:before {
                                content: "\e914";
                            }
                        }
                    }
                    // kpi folder
                    li.wdr-kpis-folder {
                        &:after {
                            .wdr-ui-icon-vam;
                            content: "\e94a";
                            right: 8px;
                            font-size: 16px;
                            color: @ui-border-color-superlight;
                        }

                        #wdr-text-display {
                            right: 25px;
                        }
                    }
                    // levels
                    .generate-levels(10);

                    .generate-levels(@n, @i: 1) when (@i =< @n) {
                        .wdr-level-@{i} {
                            padding-left: 10px + @i * 10px;

                            #wdr-text-display {
                                left: 32px + @i * 10px;
                            }

                            &.wdr-level #wdr-text-display {
                                left: 44px + @i * 10px;
                            }

                            &.wdr-level-folder #wdr-text-display {
                                left: 54px + @i * 10px;
                            }

                            &.wdr-level #wdr-icon-display {
                                left: 12px;                                
                            }
                        }

                        .generate-levels(@n, (@i + 1));
                    }
                }

                #wdr-lst-measures {
                    position: relative;

                    #wdr-text-display {
                        width: ~"calc(100% - 50px)";
                        width: ~"-webkit-calc(100% - 50px)";
                    }

                    #wdr-aggr-display {
                        width: 32px;
                        height: 100%;
                        position: absolute;
                        right: 30px;
                        top: 0;
                        cursor: pointer;
                        padding-top: 7px;

                        &:before {
                            .wdr-ui-icon;
                            letter-spacing: -8px;
                            content: "\e914\e902";
                            font-size: 24px;
                            color: @ui-icon-color;
                        }

                        &:hover:before {
                            color: @ui-icon-color-dark;
                        }
                    }
                }

                #wdr-lst-pages, #wdr-lst-columns, #wdr-lst-rows {
                    #wdr-text-display {
                        width: ~"calc(100% - 20px)";
                        width: ~"-webkit-calc(100% - 20px)";
                    }
                }
            }

            .wdr-prompt {
                position: absolute;
                bottom: 0;
                width: 100%;
                padding: 12px;
                color: @ui-prompt-color;
                font-style: italic;
                text-align: center;
                z-index: -1;
            }
        }

        .wdr-list-wrap-pages,
        .wdr-list-wrap-cols {
            margin-bottom: 30px;
        }

        .wdr-list-wrap-pages,
        .wdr-list-wrap-cols,
        .wdr-list-wrap-rows,
        .wdr-list-wrap-measures {
            height: ~"-webkit-calc(50% - 15px)";
            height: ~"calc(50% - 15px)";

            .wdr-list-content {
                height: ~"-webkit-calc(100% - 23px)";
                height: ~"calc(100% - 23px)";
            }

            .wdr-dragging-move {
                display: none;
            }
        }

        .wdr-list-wrap-all {
            height: 100%;

            .wdr-list-content {
                height: ~"-webkit-calc(100% - 33px)";
                height: ~"calc(100% - 33px)";
            }
        }
    }

    &.wdr-flat-fields {
        min-width: 320px;
        width: 420px;

        .wdr-popup-header {
            .wdr-ui-col:first-child {
                margin-right: 20px;
            }

            #wdr-btn-add-measure {
                min-width: 40px;
                width: 40px;
                padding: 0;
            }
        }

        .wdr-popup-content {
            .wdr-list-wrap {
                .wdr-list-header {
                    padding: 10px 9px;

                    .wdr-ui-checkbox {
                        span.wdr-ui-label {
                            color: @ui-label-color;
                            padding-left: 26px;
                        }

                        &.wdr-selected {
                            font-weight: bold;
                        }
                    }
                }

                .wdr-list-content {
                    #wdr-lst-hierarchies {
                        max-height: 303px;

                        li {
                            #wdr-text-display {
                                left: 35px;
                            }
                        }

                        .wdr-dragging-move {
                            display: none;
                        }
                    }
                }
            }
        }
    }
    /* layouts */
    &.wdr-layout-tablet {
        .wdr-popup-header {
            .wdr-ui-col:first-child {
                margin-right: 20px;
            }
        }

        .wdr-list-wrap .wdr-list-header.wdr-list-header-wide {
            .wdr-list-header-label-wrap {
                position: absolute;
                top: 50%;
                transform: translate(0,-50%);
            }
            span.wdr-ui-label {
                display: block;
            }

            #wdr-btn-collapse-expand-all {
                margin-left: 0px;
            }
        }
    }

    &.wdr-layout-mobile {
        #wdr-btn-add-measure {
            font-size: 0;
            min-width: 40px;
            width: 40px;
            padding: 0;
        }

        .wdr-ui-col-3 {
            margin-right: 12px;
            width: ~"calc(33.33% - 8px)";
            width: ~"-webkit-calc(33.33% - 8px)";

            &:nth-child(3n+3) {
                margin-right: 0;
            }
        }

        #wdr-wrap-pages,
        #wdr-wrap-columns {
            margin-bottom: 12px;
        }
        #wdr-wrap-pages,
        #wdr-wrap-columns,
        #wdr-wrap-measures,
        #wdr-wrap-rows {
            height: ~"calc(50% - 6px)";
            height: ~"-webkit-calc(50% - 6px)";
        }
    }

    &.wdr-layout-mobile-small {
        .wdr-ui-btns-row {
            .wdr-ui-btn:not(#wdr-btn-add-measure) {
                width: ~"calc(50% - 40px)";
                width: ~"-webkit-calc(50% - 40px)";
            }
        }
    }
}

#wdr-pivot-view {
    #wdr-aggregations-view {
        width: 170px;
        background: @background-ui-element-base-color;
        z-index: 9;
        border: @ui-element-border;
        position: absolute;
        -webkit-box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        padding: 0;

        .wdr-arrow {
            .wdr-ui-vam;
            position: absolute;
            right: 0;

            &:after,
            &:before {
                .wdr-ui-vam;
                content: "";
                display: block;
                position: absolute;
                width: 0px;
                height: 0px;
                border-style: solid;
            }

            &:before {
                border-width: 5.5px 0 5.5px 6px;
                border-color: transparent transparent transparent @ui-border-color-light;
            }

            &:after {
                border-width: 4.5px 0 4.5px 5px;
                border-color: transparent transparent transparent @ui-background-light;
            }
        }

        ul.wdr-ui-list {
            max-height: 300px;
            overflow-y: auto;
            overflow-x: hidden;

            li {
                padding: 10px 9px;
                border-bottom: @ui-element-inner-border;
                cursor: pointer;
                position: relative;

                &:last-child {
                    border-bottom: none;
                }

                &:hover {
                    background: @ui-background-light;
                }

                &.wdr-selected {
                    background: @ui-background-light;

                    &:before {
                        .wdr-ui-icon;
                        content: '\e901';
                        right: 24px;
                        top: 8px;
                        color: @ui-border-color;
                        font-size: 18px;
                    }
                }
            }
        }
    }
}

#wdr-pivot-view {
    div.wdr-ui-modal-overlay.wdr-dt-fields-overlay {
        z-index: 13;
    }

    div.wdr-ui-popup.wdr-dt-fields {
        z-index: 14;

        #wdr-btn-add-measure {
            display: none;
        }
    }
}

/* Drill trough view */
#wdr-pivot-view #wdr-drillthrough-view {
    overflow: hidden;
    min-width: 320px;

    .wdr-header-container {
        position: relative;
        margin-bottom: 10px;
    }

    .wdr-details-container {
        margin-bottom: 20px;

        .wdr-ui-label {
            margin-right: 20px;
            display: inline-block;
            max-width: 33%;
            vertical-align: top;

            b {
                color: @ui-label-color;
            }
        }

        .wdr-ui-label:last-child {
            margin-right: 0;
        }
    }

    .wdr-grid-container {
        border: @ui-border-light;
        position: relative;
    }

    .wdr-popup-title {
        padding-right: 30px;
    }

    .wdr-ui-btn-close {
        position: absolute;
        top: 0;
        right: 0;
    }
}

#wdr-pivot-view input[type=text].wdr-ui-text-input.wdr-editing-cell {
    position: absolute;
    font-size: @font-size;
}

#wdr-pivot-view textarea.wdr-ui-text-area.wdr-editing-cell {
    position: absolute;
    font-size: @font-size;
    padding-left: 4px;
    padding-top: 7px;
    box-sizing: border-box;
    z-index: 1;
}

/* Drill trough view end */

/* Calculated view */
#wdr-pivot-view #wdr-calculated-view {
    z-index: 14;
    min-width: 320px;
    width: 480px;

    .wdr-popup-header {
        .wdr-ui-col:first-child {
            margin-right: 30px;
        }

        #wdr-remove-btn {
            min-width: 40px;
            padding: 0;

            &:before {
                .wdr-ui-icon-vam;
                font-size: 30px;
                right: 4px;
                color: @ui-icon-color;
            }

            &:hover:before {
                color: @ui-icon-color-dark;
            }
        }
    }

    .wdr-popup-content {
        position: relative;

        #wdr-name-input {
            margin-bottom: 20px;
        }

        #wdr-lst-measures {
            border: @ui-element-border;
            margin-bottom: 20px;
            height: 191px;
            overflow-x: hidden;
            overflow-y: auto;
            position: relative;

            li {
                border-bottom: @ui-element-inner-border;
                padding: 10px 9px;
                position: relative;
                cursor: move;
                background: @background-ui-element-base-color;
                height: 38px;
                white-space: nowrap;

                span {
                    vertical-align: middle;
                }

                #wdr-text-display {
                    font-size: @ui-label-font-size;
                    display: inline-block;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: ~"calc(100% - 50px)";
                    width: ~"-webkit-calc(100% - 50px)";
                }

                 #wdr-aggr-display {
                    width: 32px;
                    height: 100%;
                    position: absolute;
                    right: 30px;
                    top: 0;
                    cursor: pointer;
                    padding-top: 7px;

                    &:before {
                        .wdr-ui-icon;
                        letter-spacing: -8px;
                        content: "\e914\e902";
                        font-size: 24px;
                        color: @ui-icon-color;
                    }

                    &:hover:before {
                        color: @ui-icon-color-dark;
                    }
                }

                #wdr-drag-handle {
                    .wdr-ui-icon-vam;
                    color: @ui-border-color-light;
                    font-size: 20px;
                    padding: 8px;
                    right: 0;
                    
                    &:before {
                        content: "\e910";
                    }
                }
            }
        }

        #wdr-func-btn-group {
            margin-top: 20px;
            margin-bottom: 20px;

            .wdr-calc-action {
                height: 38px;
                line-height: 36px;
                font-size: @ui-btn-font-size;
                background: @ui-background;
                position: relative;
                display: inline-block;
                vertical-align: top;
                color: @ui-btn-calc-text-color;
                text-align: center;
                cursor: pointer;

                &:hover {
                    opacity: .85;
                }
            }
        }

        .wdr-formula-wrap {
            position: relative;

            #wdr-formula-input {
                width: 100%;
                height: 80px;
                font-size: @ui-label-font-size;
                border: @ui-element-border;

                &.wdr-droppable-over {
                    background: @ui-background-superlight;
                    border: @ui-border;
                }
            }

            .wdr-prompt {
                position: absolute;
                bottom: 10px;
                pointer-events: none;
                left: 0;
                width: 100%;
                text-align: center;
                color: @ui-prompt-color;
                font-style: italic;
            }
        }
    }

    &.wdr-layout-mobile {
        .wdr-popup-header .wdr-ui-col:first-child {
            margin-right: 10px;
        }
    }
}

#wdr-pivot-view div.wdr-ui-modal-overlay.wdr-calculated-view-overlay {
    z-index: 13;
}
/* PRELOADER VIEW */
#wdr-pivot-view div.wdr-ui-modal-overlay.wdr-overlay-preloader {
    z-index: 20;
}
#wdr-pivot-view #wdr-preloader-view {
    z-index: 21;
    width: 320px;
    height: 100px;

    #wdr-spinner {
        position: relative;        
        &:before {
            .wdr-ui-icon;
            content: "\e97b";
            font-size: 31px;
            left: 7px;
            top: 10px;
            color: @ui-border-color;

            -webkit-animation-name: spin;
            -webkit-animation-duration: 1000ms;
            -webkit-animation-iteration-count: infinite;
            -webkit-animation-timing-function: linear;
            -moz-animation-name: spin;
            -moz-animation-duration: 1000ms;
            -moz-animation-iteration-count: infinite;
            -moz-animation-timing-function: linear;
            -ms-animation-name: spin;
            -ms-animation-duration: 1000ms;
            -ms-animation-iteration-count: infinite;
            -ms-animation-timing-function: linear;
            -o-transition: rotate(3600deg);
        }

        @-moz-keyframes spin {
            from { -moz-transform: rotate(0deg); }
            to { -moz-transform: rotate(360deg); }
        }
        @-webkit-keyframes spin {
            from { -webkit-transform: rotate(0deg); }
            to { -webkit-transform: rotate(360deg); }
        }
        @keyframes spin {
            from {transform:rotate(0deg);}
            to {transform:rotate(360deg);}
        }
    }


    #wdr-message-label {
        display: block;
        padding-left: 70px;
        padding-top: 8px;
    }

    #wdr-details-label {
        display: block;
        color: @ui-label-color-light;
        padding-left: 70px;
        padding-top: 2px;
        font-size: 12px;
    }
}

/* PRELOADER END */

/* ALERT VIEW */
#wdr-pivot-view div.wdr-ui-modal-overlay.wdr-overlay-alert {
    z-index: 16;
}
#wdr-pivot-view #wdr-alert-view {
    z-index: 17;

    .wdr-content {
        margin-left: 100px;
        max-width: 300px;
    }

    .wdr-popup-title {
        margin-bottom: 15px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .wdr-alert-icon {
        position: absolute;
        left: 50px;
        .wdr-ui-vam;
    }

    .wdr-ui-btns-row {
        margin-top: 10px;

        .wdr-ui-btn {
            margin-top: 10px;
        }
    }

    .wdr-circle {
        display: block;
        border-radius: 50%;
        background: @ui-background;
        width: 60px;
        height: 60px;
        position: relative;
        margin-left: -10px;

        .wdr-ui-icon {
            color: #fff;
            font-size: 38px;
            padding: 11px;
        }
    }

    .wdr-hexagon {
        display: block;
        position: relative;
        width: 32.33px;
        height: 56px;
        background-color: @ui-background;
        margin: 16.17px 0;

        .wdr-ui-icon {
            color: #fff;
            font-size: 38px;
            top: 8px;
            left: -2px;
        }
    }

    .wdr-hexagon:before, .wdr-hexagon:after {
        content: "";
        position: absolute;
        width: 0;
        border-top: 28px solid transparent;
        border-bottom: 28px solid transparent;
    }

    .wdr-hexagon:before {
        left: 100%;
        border-left: 16.17px solid @ui-background;
    }

    .wdr-hexagon:after {
        right: 100%;
        width: 0;
        border-right: 16.17px solid @ui-background;
    }

    .wdr-triangle-wrap {
        position: relative;

        .wdr-ui-icon {
            color: #fff;
            font-size: 38px;
            top: -30px;
            left: -1px;
        }

        .wdr-triangle {
            position: relative;
            background-color: @ui-background;
            text-align: left;
            display: block;
            margin-top: -7px;
        }

        .wdr-triangle:before,
        .wdr-triangle:after {
            content: '';
            position: absolute;
            background-color: inherit;
        }

        .wdr-triangle,
        .wdr-triangle:before,
        .wdr-triangle:after {
            width: 35px;
            height: 35px;
            border-top-right-radius: 30%;
        }

        .wdr-triangle {
            -webkit-transform: rotate(-60deg) skewX(-30deg) scale(1, 0.866);
            -ms-transform: rotate(-60deg) skewX(-30deg) scale(1, 0.866);
            transform: rotate(-60deg) skewX(-30deg) scale(1, 0.866);
        }

        .wdr-triangle:before {
            -webkit-transform: rotate(-135deg) skewX(-45deg) scale(1.414, 0.707) translate(0, -50%);
            -ms-transform: rotate(-135deg) skewX(-45deg) scale(1.414, 0.707) translate(0, -50%);
            transform: rotate(-135deg) skewX(-45deg) scale(1.414, 0.707) translate(0, -50%);
        }

        .wdr-triangle:after {
            -webkit-transform: rotate(135deg) skewY(-45deg) scale(0.707, 1.414) translate(50%);
            -ms-transform: rotate(135deg) skewY(-45deg) scale(0.707, 1.414) translate(50%);
            transform: rotate(135deg) skewY(-45deg) scale(0.707, 1.414) translate(50%);
        }
    }

    &.wdr-layout-mobile-small {
        .wdr-content {
            margin-left: 0;
        }
        
        .wdr-alert-icon {
            display: none;
        }
    }
}
/* ALERT VIEW END*/

.wdr-noselect {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

// Context Menu
#wdr-pivot-view #wdr-context-menu {
    .wdr-shadow;
    position: absolute;
    background-color: @background-ui-element-base-color;
    border: @ui-border;
    z-index: 20;

    .wdr-ui-list {
        min-width: 180px;

        li {
            padding: 10px;
            border-bottom: @ui-element-inner-border;
            cursor: pointer;

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background-color: @ui-background-light;
            }
        }
    }
}

/* NEW GRID */
#wdr-pivot-view .wdr-grid-layout {
    position: relative;
    overflow: hidden;
}

#wdr-pivot-view .wdr-grid-layout div.wdr-scroll-pane {
    overflow: auto;
}

    #wdr-pivot-view .wdr-grid-layout div.wdr-scroll-pane div.wdr-scroll-content {
        position: relative;
    }

    #wdr-pivot-view .wdr-grid-layout div.wdr-scroll-pane div.wdr-scroll-placeholder {
        position: relative;
    }

#wdr-pivot-view .wdr-grid-layout div.wdr-cell {
    background-color: @grid-cell-color;
    color: @grid-cell-text-color;
    box-sizing: border-box;
    vertical-align: top;
    border-right: @grid-cell-border;
    border-bottom: @grid-cell-border;
    padding: 7px 4px;
    /*line-height: 22px;*/
}

#wdr-pivot-view .wdr-grid-layout div.wdr-row {
    white-space: nowrap;
    box-sizing: border-box;
}

#wdr-pivot-view .wdr-grid-layout div.wdr-row > div {
    display: inline-block;
}

#wdr-pivot-view .wdr-grid-layout #wdr-rows-sheet {
    div.wdr-row {
        display: flex;

        & > div.wdr-sheet-header {
            display:flex;
            flex-direction: column;
            justify-content: center;
        }
    }
} 

#wdr-pivot-view .wdr-grid-layout div.wdr-scroll-pane div.wdr-scroll-content {
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
    white-space: nowrap;
    /*pointer-events: none;*/
}

#wdr-pivot-view .wdr-grid-layout #wdr-data-sheet,
#wdr-pivot-view .wdr-grid-layout #wdr-rows-sheet,
#wdr-pivot-view .wdr-grid-layout #wdr-cols-sheet,
#wdr-pivot-view .wdr-grid-layout #wdr-sheet-headers {
    position: absolute;
    overflow: hidden;
    background-color: @background-base-color;
}

#wdr-pivot-view .wdr-grid-layout #wdr-data-sheet {
    border-top: @grid-cell-border;
    border-left: @grid-cell-border;
}

    #wdr-pivot-view .wdr-grid-layout .wdr-cell {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    #wdr-pivot-view .wdr-grid-layout #wdr-data-sheet .wdr-cell {
        text-align: right;
    }
    
    #wdr-pivot-view .wdr-grid-layout #wdr-cols-sheet .wdr-scroll-pane,
    #wdr-pivot-view .wdr-grid-layout #wdr-rows-sheet .wdr-scroll-pane {
        overflow: hidden;
    }

#wdr-pivot-view .wdr-grid-layout div.wdr-scroll-pane div.wdr-scroll-content .wdr-cell {
    position: relative;
}

#wdr-pivot-view .wdr-grid-layout div.wdr-scroll-pane div.wdr-scroll-placeholder .wdr-cell {
    position: absolute;
}

#wdr-pivot-view .wdr-grid-layout div.wdr-header {
    background-color: @grid-table-header-color;
    color: @grid-table-header-text-color;
}

#wdr-pivot-view .wdr-grid-layout div.wdr-filter-header {
    cursor: pointer;
}

#wdr-pivot-view .wdr-grid-layout div.wdr-total,
#wdr-pivot-view .wdr-grid-layout div.wdr-grand-total {
    font-weight: bold;
}

#wdr-pivot-view .wdr-grid-layout div.wdr-header.wdr-total {
    font-weight: normal;
}

#wdr-pivot-view .wdr-grid-layout.wdr-flat-view div.wdr-total,
#wdr-pivot-view .wdr-grid-layout.wdr-flat-view div.wdr-grand-total {
    text-align: right;
}

#wdr-pivot-view .wdr-grid-layout .wdr-filters {
    position: absolute;
    background-color: @grid-filter-color;
    color: @grid-filter-text-color;
}

#wdr-pivot-view .wdr-grid-layout #wdr-cols-filter {
    border-right: 1px solid @grid-filter-color;
}
    
    #wdr-pivot-view .wdr-grid-layout .wdr-filters .wdr-header,
    #wdr-pivot-view .wdr-grid-layout.wdr-flat-view .wdr-header {
        background-color: @grid-filter-color;
        color: @grid-filter-text-color;
        font-weight: bold;
        text-transform: uppercase; 
        position: relative;
        border: none;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    #wdr-pivot-view .wdr-grid-layout div.wdr-filter-header,
    #wdr-pivot-view .wdr-grid-layout a.wdr-filter-header {
        padding-right: 20px;
    }

    #wdr-pivot-view .wdr-grid-layout div.wdr-filter-header.wdr-filtered,
    #wdr-pivot-view .wdr-grid-layout a.wdr-filter-header.wdr-filtered {
        padding-right: 35px;
    }

        #wdr-pivot-view .wdr-grid-layout div.wdr-filter-header:hover {
            background-color: @grid-filter-color-hover;
        }

    #wdr-pivot-view .wdr-grid-layout i.wdr-icon {
        position: absolute;
        height: 100%;
        width: 16px;
        top: 0;
    }

    #wdr-pivot-view .wdr-grid-layout i.wdr-icon:before {
        font-family: webdatarocks-icons;
        font-weight: normal;
        font-size: 15px;
        color: @grid-icon-color;
        position: absolute;
        top: 6px;
    }

    #wdr-pivot-view .wdr-grid-layout.wdr-flat-view i.wdr-icon:before {
        color: @grid-filter-icon-color;
    }

    #wdr-pivot-view .wdr-grid-layout .wdr-filter-header i.wdr-filter-icon {
        right: 2px;
        pointer-events: none;
    }

    #wdr-pivot-view .wdr-grid-layout .wdr-filter-header i.wdr-filter-icon:before {
        content: "\e915";
        color: @grid-filter-icon-color;
    }

    #wdr-pivot-view .wdr-grid-layout .wdr-filter-header.wdr-filtered i.wdr-filtered-icon {
        right: 16px;
        pointer-events: none;
    }

    #wdr-pivot-view .wdr-grid-layout .wdr-filter-header.wdr-filtered i.wdr-filtered-icon:before {
        content: "\e90e";
        color: @grid-filter-icon-color;
    }

#wdr-pivot-view .wdr-grid-layout {
    .wdr-filter-header.wdr-dragging-move {
        opacity: 0.5;
    }
    .wdr-header-drop-indicator {
        background-color: @ui-border-color-dark;
    }

    .wdr-draggable:not(.wdr-filter-header) {
        cursor: move;
    }

    .wdr-filters {
        &#wdr-cols-filter {
            .wdr-header-drop-indicator {
                display: inline-block;
                width: 3px;
                height: 100%;
            }
        }

        &#wdr-rows-filter {
            .wdr-header-drop-indicator {
                display: block;
                width: 100%;
                height: 3px;
            }
        }

        &#wdr-page-filter {
            .wdr-header-drop-indicator {
                float: none;
                display: inline-block;
                width: 3px;
                height: 40px;
                vertical-align: top;
                margin-right: 5px;
            }
        }
    }

    &.wdr-classic-view {
        .wdr-filters {
            &#wdr-rows-filter {
                .wdr-header-drop-indicator {
                    display: inline-block;
                    width: 3px;
                    height: 30px;
                }
            }
        }
    }

    &.wdr-flat-view {
        .wdr-header-drop-indicator {
            position:absolute;
            width: 3px;
            height: 30px;
        }
    }
}

/* pages filter */

#wdr-pivot-view .wdr-grid-layout .wdr-filters#wdr-page-filter {
    padding: 5px 0 0 5px;
    border-bottom: @ui-border-light;
    background-color: @grid-sheet-header-color;
    white-space: nowrap;
    overflow-x: auto;
}

#wdr-pivot-view .wdr-grid-layout .wdr-filters#wdr-page-filter a.wdr-filter-header {
    text-align: left;
    border: none;
    background-color: @grid-filter-color;
    margin-bottom: 5px;
    margin-right: 5px;
    padding-top: 5px;
    padding-bottom: 20px;
    width: auto;
    width: initial;
    min-width: 80px;
    padding-left: 5px;
    line-height: 15px;
    white-space: nowrap;
    font-weight: bold !important;
    display: inline-block;
    float: none;
    cursor: pointer;
}

    #wdr-pivot-view .wdr-grid-layout .wdr-filters#wdr-page-filter a.wdr-filter-header:hover {
        background-color: @grid-filter-color-hover;
    }

    #wdr-pivot-view .wdr-grid-layout .wdr-filters#wdr-page-filter i.wdr-icon:before {
        top: 5px;
    }

#wdr-pivot-view .wdr-grid-layout .wdr-filters a.wdr-filter-header .wdr-filter-desc {
    display: block;
    font-weight: normal !important;
    text-transform: none;
    color: @grid-filter-subtext-color;
    font-size: 10px;
    position: absolute;
    bottom: 5px;
}

/* levels */

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-1 {
    padding-left: 15px;
    background-position: 12px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-2 {
    padding-left: 27px;
    background-position: 24px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-3 {
    padding-left: 39px;
    background-position: 36px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-4 {
    padding-left: 51px;
    background-position: 48px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-5 {
    padding-left: 63px;
    background-position: 60px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-6 {
    padding-left: 75px;
    background-position: 72px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-7 {
    padding-left: 87px;
    background-position: 84px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-8 {
    padding-left: 99px;
    background-position: 96px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-9 {
    padding-left: 111px;
    background-position: 108px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-10 {
    padding-left: 123px;
    background-position: 120px center;
}
#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-11 {
    padding-left: 135px;
    background-position: 132px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-12 {
    padding-left: 147px;
    background-position: 144px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-13 {
    padding-left: 159px;
    background-position: 156px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-14 {
    padding-left: 171px;
    background-position: 168px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-15 {
    padding-left: 183px;
    background-position: 180px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-16 {
    padding-left: 195px;
    background-position: 192px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-17 {
    padding-left: 207px;
    background-position: 204px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-18 {
    padding-left: 219px;
    background-position: 216px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-19 {
    padding-left: 231px;
    background-position: 228px center;
}

#wdr-pivot-view .wdr-grid-layout.wdr-compact-view .wdr-header-r.wdr-level-20 {
    padding-left: 243px;
    background-position: 240px center;
}

/* collapsed / expanded */
#wdr-pivot-view .wdr-grid-layout div.wdr-collapsed,
#wdr-pivot-view .wdr-grid-layout div.wdr-expanded {
    cursor: pointer;
}

    #wdr-pivot-view .wdr-grid-layout .wdr-collapsed .wdr-collapsed-icon,
    #wdr-pivot-view .wdr-grid-layout .wdr-expanded .wdr-expanded-icon {
        position: relative;
        display: inline-block;
        height: 12px;
        width: 11px;
    }

    #wdr-pivot-view .wdr-grid-layout .wdr-collapsed .wdr-collapsed-icon:before,
    #wdr-pivot-view .wdr-grid-layout .wdr-expanded .wdr-expanded-icon:before {
        top: 0;
        left: -4px;
    }

    #wdr-pivot-view .wdr-grid-layout .wdr-collapsed .wdr-collapsed-icon:before {
        content: "\e903";
    }   

    #wdr-pivot-view .wdr-grid-layout .wdr-expanded .wdr-expanded-icon:before {
        content: "\e902";
    }

/* sort */

#wdr-pivot-view .wdr-grid-layout i.wdr-icon.wdr-sort-icon,
#wdr-pivot-view #wdr-drillthrough-view i.wdr-icon.wdr-sort-icon {
    cursor: pointer;
    right: 0;
    
    &:before {
        font-size: 15px;
    }
}

#wdr-pivot-view .wdr-grid-layout {
    .wdr-cell.wdr-v-sort,
    .wdr-cell.wdr-h-sort {
        padding-right: 16px;
    }
    .wdr-v-sort:hover .wdr-v-sort-icon:before,
    .wdr-v-sort .wdr-v-sort-asc:before,
    .wdr-v-sort .wdr-v-sort-desc:before,
    .wdr-h-sort:hover .wdr-h-sort-icon:before,
    .wdr-h-sort .wdr-h-sort-asc:before,
    .wdr-h-sort .wdr-h-sort-desc:before {
        content: "\e900";
    }
    .wdr-v-sort .wdr-v-sort-asc:before {
         .rotate(180);
    }
    .wdr-h-sort .wdr-sort-icon:before {
    }
    .wdr-h-sort:hover .wdr-h-sort-icon:before,
    .wdr-h-sort .wdr-h-sort-desc:before {
        right: 1px;
        .rotate(270);
    }
    .wdr-h-sort .wdr-h-sort-asc:before {
        right: 2px;
        .rotate(90);
    }
} 

/* drill down links */

#wdr-pivot-view .wdr-grid-layout span.wdr-hierarchy-link {
    white-space: nowrap;
    color: @grid-drilldown-link-text-color;
    font-size: 10px;
    text-decoration: underline;
    cursor: pointer;
    margin-left: 4px;
}

#wdr-pivot-view .wdr-grid-layout span.wdr-hierarchy-link:before {
    text-indent: 0;
    margin-right: 2px;
    display:inline-block;
}
#wdr-pivot-view .wdr-grid-layout .wdr-drilled-up span.wdr-hierarchy-link:before {
    content: "+";
}
#wdr-pivot-view .wdr-grid-layout .wdr-drilled-down span.wdr-hierarchy-link:before {
    content: "-";
}

/* sheet headers */
#wdr-pivot-view .wdr-grid-layout div.wdr-sheet-header,
#wdr-pivot-view .wdr-grid-layout .wdr-filters div.wdr-sheet-header {
    border-right: @grid-sheet-header-border;
    border-bottom: @grid-sheet-header-border;
    color: @grid-sheet-header-text-color;
    background-color: @grid-sheet-header-color;
    text-align: center;
    line-height: 23px;
    padding: 0 0;
}

#wdr-pivot-view .wdr-grid-layout #wdr-sheet-headers .wdr-sheet-header {
    display: inline-block;
}

/* KPI cells */
#wdr-pivot-view .wdr-grid-layout div.wdr-cell {
    &.wdr-kpi:after {
        .wdr-ui-icon;
        position: relative;
        color: @ui-border-color;
        font-size: 16px;
        padding-top: 3px;
    }
    &.wdr-kpi-trend-bad-icon:after {
        content: "\ea3e";
    }
    &.wdr-kpi-trend-risk-icon:after {
        content: "\ea3d";
    }
    &.wdr-kpi-trend-ok-icon:after {
        content: "\ea3c";
    }
    &.wdr-kpi-trend-rising-icon:after {
        content: "\ea3b";
    }
    &.wdr-kpi-trend-good-icon:after {
        content: "\ea3a";
    }
    &.wdr-kpi-status-bad-icon:after {
        content: "\e951";
    }
    &.wdr-kpi-status-risk-icon:after {
        content: "\e954";
    }
    &.wdr-kpi-status-ok-icon:after {
        content: "\e952";
    }
    &.wdr-kpi-status-rising-icon:after {
        content: "\e953";
    }
    &.wdr-kpi-status-good-icon:after {
        content: "\e950";
    }
}

/* member properties */
#wdr-pivot-view .wdr-grid-layout div.wdr-cell {
    .wdr-member-property-label {
        font-style: italic;
    }
}

/* selection canvas */
#wdr-pivot-view .wdr-grid-layout .wdr-sheet-selection-canvas {
    position: absolute;
    background-color: @grid-selection-canvas-color;
    pointer-events: none;
    box-sizing: border-box;
    border: @grid-selection-canvas-border;
    z-index: 1;
}

#wdr-pivot-view .wdr-grid-layout .wdr-sheet-selection-canvas.wdr-clear-border {
    border: none;
}

#wdr-pivot-view .wdr-grid-layout .wdr-sheet-selection-canvas.wdr-clear-border-right {
    border-right: none;
}

#wdr-pivot-view .wdr-grid-layout .wdr-sheet-selection-canvas.wdr-clear-border-left {
    border-left: none;
}

#wdr-pivot-view .wdr-grid-layout .wdr-sheet-selection-canvas.wdr-clear-border-top {
    border-top: none;
}

#wdr-pivot-view .wdr-grid-layout .wdr-sheet-selection-canvas.wdr-clear-border-bottom {
    border-bottom: none;
}

#wdr-pivot-view .wdr-grid-layout .wdr-auto-calculation-bar {
    position: absolute;
    background-color: @grid-auto-calculation-bar;
    opacity: 1;
    pointer-events: none;
    box-sizing: border-box;
    z-index: 2;
    height: 17px;
    overflow: hidden;
}

#wdr-pivot-view .wdr-grid-layout .wdr-auto-calculation-bar .wdr-auto-calculation-bar-conainer {
    overflow: hidden;
    white-space: nowrap;
}

#wdr-pivot-view .wdr-grid-layout .wdr-auto-calculation-bar .wdr-auto-calculation-bar-content {
    margin-left: 5px;
    margin-right: 5px;
    vertical-align: middle;
    display: inline-block;
}

#wdr-pivot-view .wdr-grid-layout .wdr-auto-calculation-bar .wdr-auto-calculation-bar-content-text {
    color: white;
    text-transform: uppercase;
    margin-right: 5px;
    float: left;
    font-size: 11px;
    line-height: 17px;
}

#wdr-pivot-view .wdr-grid-layout .wdr-auto-calculation-bar .wdr-auto-calculation-bar-content-results {
    color: white;
    font-weight: bold;
    display: inline-block;
    font-size: 11px;
    line-height: 17px;
}

#wdr-pivot-view .wdr-grid-layout .wdr-sheet-selection-header {
    border: none;
}

/* flat view */
#wdr-pivot-view .wdr-grid-layout.wdr-flat-view .wdr-filter-header {
    padding-left: 16px;
}

    #wdr-pivot-view .wdr-grid-layout.wdr-flat-view .wdr-filter-header i.wdr-filter-icon {
        left: 0;
    }

/* resize handles */
#wdr-pivot-view .wdr-grid-layout .wdr-resize-handles {
    position: absolute;

    .wdr-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .wdr-indicator {
        background-color: @grid-accent-color;
        width: 1px;
        height: 1px;
        position: absolute;
        z-index: 1;
    }

    .wdr-handle {
        position: absolute;
        z-index: 1;

        &:hover {
            background-color: @grid-accent-color;
        }

        &.wdr-active {
            background-color: @grid-accent-color;
        }
    }

    &#wdr-cols-resize {
        .wdr-handle {
            width: 7px;
            margin-left: -3px;
            height: 100%;
            min-height: 25px;
            cursor: col-resize;
        }
    }

    &#wdr-rows-resize {
        .wdr-handle {
            height: 7px;
            margin-top: -3px;
            width: 100%;
            min-width: 25px;
            cursor: row-resize;
        }
    }
}


/*
 * 	Toolbar for webdatarocks Pivot Table Component
 */

#wdr-toolbar-wrapper [class^="wdr-"],
#wdr-toolbar-wrapper [class*=" wdr-"] {
    color: @theme-text-color;
    line-height: 1;
}

#wdr-toolbar-wrapper [class^="wdr-"] strong,
#wdr-toolbar-wrapper [class*=" wdr-"] strong {
    font-weight: bold;
}

#wdr-toolbar-wrapper [class^="wdr-"] a,
#wdr-toolbar-wrapper [class*=" wdr-"] a {
    text-decoration: none;
}

#wdr-toolbar-wrapper [class^="wdr-"] *, [class*=" wdr-"] * {
    outline: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

#wdr-toolbar-wrapper [class^="wdr-"] ul,
#wdr-toolbar-wrapper [class^="wdr-"] ol,
#wdr-toolbar-wrapper [class^="wdr-"] p,
#wdr-toolbar-wrapper [class*=" wdr-"] ul,
#wdr-toolbar-wrapper [class*=" wdr-"] ol,
#wdr-toolbar-wrapper [class*=" wdr-"] p {
    list-style: none;
    margin: 0;
    padding: 0;
}

#wdr-toolbar-wrapper [class^="wdr-icon-"]:before,
#wdr-toolbar-wrapper [class*=" wdr-icon-"]:before,
#wdr-toolbar-wrapper .wdr-icon:before {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'webdatarocks-icons' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-button-add .wdr-icon:before,
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-btn .wdr-icon,
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-currentmark,
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-currentmark:before {
    font-size: 30px;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    font-weight: normal;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-condition-row .wdr-cr-delete {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
}

/* TOOLBAR */
#wdr-toolbar-wrapper [class^="wdr-"],
#wdr-toolbar-wrapper [class*=" wdr-"] {
    color: @theme-text-color;
    line-height: 1;
}

#wdr-toolbar-wrapper [class^="wdr-"] strong,
#wdr-toolbar-wrapper [class*=" wdr-"] strong {
    font-weight: bold;
}

#wdr-toolbar-wrapper [class^="wdr-"] a,
#wdr-toolbar-wrapper [class*=" wdr-"] a {
    text-decoration: none;
}

#wdr-toolbar-wrapper [class^="wdr-"] *, [class*=" wdr-"] * {
    outline: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

#wdr-toolbar-wrapper [class^="wdr-"] ul,
#wdr-toolbar-wrapper [class^="wdr-"] ol,
#wdr-toolbar-wrapper [class^="wdr-"] p,
#wdr-toolbar-wrapper [class*=" wdr-"] ul,
#wdr-toolbar-wrapper [class*=" wdr-"] ol,
#wdr-toolbar-wrapper [class*=" wdr-"] p {
    list-style: none;
    margin: 0;
    padding: 0;
}

#wdr-toolbar-wrapper [class^="wdr-icon-"]:before,
#wdr-toolbar-wrapper [class*=" wdr-icon-"]:before,
#wdr-toolbar-wrapper .wdr-icon:before {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'webdatarocks-icons' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-button-add .wdr-icon:before,
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-btn .wdr-icon,
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-currentmark,
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-currentmark:before {
    font-size: 30px;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    font-weight: normal;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-condition-row .wdr-cr-delete {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
}

/* TOOLBAR */
#wdr-toolbar-wrapper {
    height: 80px;
}

    #wdr-toolbar-wrapper #wdr-toolbar {
        width: 100%;
        height: 78px;
        list-style: none;
        background: @background-base-color;
        white-space: nowrap;
    }

        #wdr-toolbar-wrapper #wdr-toolbar,
        #wdr-toolbar-wrapper #wdr-toolbar div,
        #wdr-toolbar-wrapper #wdr-toolbar span,
        #wdr-toolbar-wrapper #wdr-toolbar p,
        #wdr-toolbar-wrapper #wdr-toolbar a,
        #wdr-toolbar-wrapper #wdr-toolbar table,
        #wdr-toolbar-wrapper #wdr-toolbar table th,
        #wdr-toolbar-wrapper #wdr-toolbar table tr,
        #wdr-toolbar-wrapper #wdr-toolbar table td,
        #wdr-toolbar-wrapper #wdr-toolbar ul,
        #wdr-toolbar-wrapper #wdr-toolbar li,
        #wdr-toolbar-wrapper #wdr-toolbar input,
        #wdr-toolbar-wrapper #wdr-toolbar textarea,
        #wdr-toolbar-wrapper #wdr-toolbar select {
            font-family: @font-family;
            font-size: @ui-label-font-size;
            color: @toolbar-text-color;
            text-align: left;
            padding: 0;
            margin: 0;
            font-weight: normal;
            text-shadow: none;
        }

            /* TABS */
            #wdr-toolbar-wrapper #wdr-toolbar > li {
                display: inline-block;
            }

            #wdr-toolbar-wrapper #wdr-toolbar li:before {
                content: none;
            }

            #wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown-content li {
                float: none;
                width: 140px;
            }


#wdr-toolbar-wrapper #wdr-toolbar {
    .wdr-toolbar-group-right {
        position: absolute;
        right: 0;
    }

    li {
        a svg, a svg path {
            transform: translateZ(0);
            fill: @ui-icon-color;
        }

        a:hover svg {
            fill: @ui-icon-color-dark;
        }
    }

    > li,
    > .wdr-toolbar-group-right > li {
        > a {
            div.wdr-svg-icon {
                position: absolute;
                text-align: center;
                bottom: 30px;
                width: 100%;
            }
            span {
                position: absolute;
                top: 55px;
                line-height: 12px;
                left: 0;
                right: 0;
            }
        }
    }

    #wdr-tab-format {
        div.wdr-svg-icon {
            margin-left: 4px;
        }

        .wdr-dropdown-content li {
            width: 210px;
        }

        .wdr-dropdown-content span {
            left: 62px;
        }
    }
}


#wdr-toolbar-wrapper #wdr-alert-view {
    z-index: 17;

    .wdr-content {
        margin-left: 100px;
        max-width: 300px;
    }

    .wdr-popup-title {
        margin-bottom: 15px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .wdr-alert-icon {
        position: absolute;
        left: 50px;
        .wdr-ui-vam;
    }

    .wdr-ui-btns-row {
        margin-top: 10px;

        .wdr-ui-btn {
            margin-top: 10px;
        }
    }

    .wdr-circle {
        display: block;
        border-radius: 50%;
        background: @ui-background;
        width: 60px;
        height: 60px;
        position: relative;
        margin-left: -10px;

        .wdr-ui-icon {
            color: #fff;
            font-size: 38px;
            padding: 11px;
        }
    }

    .wdr-hexagon {
        display: block;
        position: relative;
        width: 32.33px;
        height: 56px;
        background-color: @ui-background;
        margin: 16.17px 0;

        .wdr-ui-icon {
            color: #fff;
            font-size: 38px;
            top: 8px;
            left: -2px;
        }
    }

    .wdr-hexagon:before, .wdr-hexagon:after {
        content: "";
        position: absolute;
        width: 0;
        border-top: 28px solid transparent;
        border-bottom: 28px solid transparent;
    }

    .wdr-hexagon:before {
        left: 100%;
        border-left: 16.17px solid @ui-background;
    }

    .wdr-hexagon:after {
        right: 100%;
        width: 0;
        border-right: 16.17px solid @ui-background;
    }

    .wdr-triangle-wrap {
        position: relative;

        .wdr-ui-icon {
            color: #fff;
            font-size: 38px;
            top: -30px;
            left: -1px;
        }

        .wdr-triangle {
            position: relative;
            background-color: @ui-background;
            text-align: left;
            display: block;
            margin-top: -7px;
        }

        .wdr-triangle:before,
        .wdr-triangle:after {
            content: '';
            position: absolute;
            background-color: inherit;
        }

        .wdr-triangle,
        .wdr-triangle:before,
        .wdr-triangle:after {
            width: 35px;
            height: 35px;
            border-top-right-radius: 30%;
        }

        .wdr-triangle {
            -webkit-transform: rotate(-60deg) skewX(-30deg) scale(1, 0.866);
            -ms-transform: rotate(-60deg) skewX(-30deg) scale(1, 0.866);
            transform: rotate(-60deg) skewX(-30deg) scale(1, 0.866);
        }

        .wdr-triangle:before {
            -webkit-transform: rotate(-135deg) skewX(-45deg) scale(1.414, 0.707) translate(0, -50%);
            -ms-transform: rotate(-135deg) skewX(-45deg) scale(1.414, 0.707) translate(0, -50%);
            transform: rotate(-135deg) skewX(-45deg) scale(1.414, 0.707) translate(0, -50%);
        }

        .wdr-triangle:after {
            -webkit-transform: rotate(135deg) skewY(-45deg) scale(0.707, 1.414) translate(50%);
            -ms-transform: rotate(135deg) skewY(-45deg) scale(0.707, 1.414) translate(50%);
            transform: rotate(135deg) skewY(-45deg) scale(0.707, 1.414) translate(50%);
        }
    }

    &.wdr-layout-mobile-small {
        .wdr-content {
            margin-left: 0;
        }
        
        .wdr-alert-icon {
            display: none;
        }
    }
}

#wdr-toolbar-wrapper #wdr-alert-view span.wdr-ui-alert-label {
    color: #111;
    font-size: 14px;
}

#wdr-toolbar-wrapper #wdr-alert-view div.wdr-ui-alert-title-label {
    margin-right: 141px;
}

#wdr-toolbar-wrapper #wdr-alert-view #wdr-btn-apply.wdr-alert-button {
    margin-right: 109px;
}

#wdr-toolbar-wrapper #wdr-toolbar li#wdr-tab-fullscreen {
    width: 70px;
}

#wdr-toolbar-wrapper.wdr-mobile #wdr-toolbar {
    li#wdr-tab-format-conditional {
        width: 80px;
    }
}

/* Tabs */
#wdr-toolbar-wrapper #wdr-toolbar li {
    display: inline-block;
    background: none;
    vertical-align: top;
    padding: 0;
    position: relative;
    width: 57px;
}

#wdr-toolbar-wrapper #wdr-toolbar a {
    height: 78px;
    display: block;
    text-decoration: none;
    border: none;
}

    #wdr-toolbar-wrapper #wdr-toolbar a > span {
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
        height: 15px;
    }

#wdr-toolbar-wrapper #wdr-toolbar li:first-child a {
    margin-left: 1px;
}

#wdr-toolbar-wrapper #wdr-toolbar li.wdr-divider {
    width: 0px;
    margin: 20px 15px 0px 15px;
    height: 40px;
    border-right: @ui-border-light;
}

#wdr-toolbar-wrapper.wdr-mobile  #wdr-toolbar li.wdr-divider {
    display: none;
}

#wdr-toolbar-wrapper #wdr-toolbar li.wdr-v-divider {
    height: 0px;
    width: 140px;
    border-bottom: 1px dotted #dcdcdc;
}
/* Tab menu */
#wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown {
    position: absolute;
    display: none;
    top: 78px;
    z-index: 25;
}

#wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown-content:after {
    content: '';
    border-width: 5px;
    border-color: transparent transparent @background-ui-element-base-color;
    border-style: outset outset solid;
    border-style: solid;
    top: -10px;
    left: 23px;
    width: 0;
    height: 0;
    position: absolute;
}

#wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown-content:before {
    content: '';
    border-color: transparent transparent @ui-element-border-color;
    border-style: outset outset solid;
    border-width: 6px;
    position: absolute;
    top: -12px;
    left: 22px;
}

#wdr-toolbar-wrapper #wdr-toolbar .wdr-align-rigth .wdr-dropdown-content:after {
    left: inherit;
    right: 20px;
}

#wdr-toolbar-wrapper #wdr-toolbar .wdr-align-rigth .wdr-dropdown-content:before {
    left: inherit;
    right: 19px;
}

#wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown-content {
    position: relative;
    padding: 0;
    list-style: none;
    background: @background-ui-element-base-color;
    border: @ui-element-border;
}

    #wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown-content li {
        width: 155px;
        display: block;
    }

    #wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown-content a {
        text-shadow: none;
        text-align: left;
        padding: 0;
        margin: 0px 1px 1px 1px;
        height: 37px;
    }

    #wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown-content li:first-child a {
        margin-top: 1px;
    }

    /*#wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown-content a.disabled {
        pointer-events: none;
        cursor: default;
        color: #AAA;
    }*/

    #wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown-content a:hover {
        background-color: @ui-background-light;
    }

    #wdr-toolbar-wrapper #wdr-toolbar .wdr-dropdown-content span {
        display: inline;
        position: absolute;
        left: 36px;
        right: 0;
        bottom: 11px;
        text-overflow: ellipsis;
        overflow: hidden;
        text-align: left;
    }

/* POPUP WINDOW */
#wdr-toolbar-wrapper div.wdr-popup {
    position: absolute;
    min-width: 270px;
    z-index: 100;
    font-family: @font-family;
    max-width: 100%;
    background: @background-base-color;
    border: @ui-pop-up-border;
    margin: 0 auto;
    -webkit-box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 24px 30px 30px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

#wdr-toolbar-wrapper div.wdr-popup.wdr-popup-w700 {
    width: 700px;
}

#wdr-toolbar-wrapper div.wdr-popup.wdr-popup-w570 {
    width: 570px;
}

#wdr-toolbar-wrapper div.wdr-popup.wdr-popup-w500 {
    width: 500px;
}

#wdr-toolbar-wrapper div.wdr-popup.wdr-popup-w460 {
    width: 462px;
}

#wdr-toolbar-wrapper div.wdr-panel .wdr-toolbox {
    margin-top: 30px;
    text-align: center;
}

#wdr-toolbar-wrapper div.wdr-panel .wdr-title-bar .wdr-toolbox {
    margin-top: 0;
    text-align: right;
    float: right;
}

#wdr-toolbar-wrapper div.wdr-panel .wdr-toolbox .wdr-ui-btn {
     margin-right: 20px;
     height: 38px;
}

#wdr-toolbar-wrapper div.wdr-panel .wdr-toolbox .wdr-ui-btn:last-child {
    margin-right: 0;
}

#wdr-toolbar-wrapper div.wdr-panel .wdr-panel-content .wdr-title-bar {
    clear: both;
    margin-bottom: 24px;
}

    #wdr-toolbar-wrapper div.wdr-panel .wdr-panel-content .wdr-title-bar {
        .wdr-title-text {
            color: @ui-title-color;
            font-size: @ui-title-font-size;
            text-align: center;
            text-shadow: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &.wdr-ui-col {
                text-align: left;
            }
        }

    } 

#wdr-toolbar-wrapper #wdr-popUp-modal-overlay {
    z-index: 8;
}

    #wdr-toolbar-wrapper #wdr-popUp-modal-overlay .wdr-modal-overlay {
        opacity: 0;
    }

#wdr-toolbar-wrapper div.wdr-modal-overlay {
    background: rgba(255, 255, 255, 0.7);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
}

#wdr-toolbar-wrapper #wdr-portrait-radio {
    margin-left: 14px;
}

#wdr-toolbar-wrapper #wdr-landscape-radio {
    margin-left: 14px;
}

#wdr-toolbar-wrapper div.wdr-panel.wdr-popup {
    &.wdr-layout-mobile {
        padding: 20px;
    }

    &.wdr-layout-mobile-small {
        .wdr-panel-content
        {
            .wdr-toolbox {
                float: none;
                text-align: left;
                width: 100%;

                a.wdr-ui-btn {
                    width: ~"calc(50% - 10px)";
                    width: ~"-webkit-calc(50% - 10px)";
                }

            }
            .wdr-title-bar {
                .wdr-title-text {
                    width: 100%;
                    margin-bottom: 10px;
                    text-align: left;
                    font-size: @ui-title-font-size-smaller;
                }
            

            }
        } 
    }
}

/* Conditional formatting */
#wdr-toolbar-wrapper #wdr-popup-conditional {
    width: 610px;
    min-width: 400px;

    .wdr-panel-content {
        position: relative;
    }

    .wdr-title-text {
        width: ~"-webkit-calc(100% - 270px)";
        width: ~"calc(100% - 270px)";
    }

    .wdr-popup-content {
        overflow-x: hidden;
        overflow-y: auto;
        /*min-height: 149px;*/
        max-height: 298px;
    }

    a.wdr-ui-btn#wdr-add-btn {
        min-width: 40px;
        width: 40px;
    }

    .wdr-condition-row {
        border-top: @ui-border-light;
        padding: 30px 30px;
        margin-left: -30px;
        width: ~"-webkit-calc(100% + 60px)";
        width: ~"calc(100% + 60px)";

        .wdr-wrap-relative {
            position: relative;
        }

        .wdr-cr-delete {
            position: absolute;
            color: @ui-icon-color;
            -webkit-transition: color 0.3s;
            transition: color 0.3s;
            cursor: pointer;
            font-size: 30px;
            right: 0;
        }

        wdr-cr-delete:hover {
            color: @ui-icon-color-dark;
        }

        #wdr-values {
            width: 150px;
        }

        #wdr-conditions {
            width: 150px;
        }

        #wdr-font-family {
            width: 150px;
        }

        #wdr-font-size {
            width: 100px;
        }

        #wdr-sample {
            width: 120px;
        }
    }

    &.wdr-layout-mobile {
        #wdr-values {
            width: ~"calc(100% - 100px)";
            width: ~"-webkit-calc(100% - 100px)";
            margin-bottom: 10px;
        }

        #wdr-conditions {
            width: ~"calc(100% - 230px)";
            width: ~"-webkit-calc(100% - 230px)";
            margin-left: 60px;
        }

        #wdr-font-family {
            width: ~"calc(100% - 100px)";
            width: ~"-webkit-calc(100% - 100px)";
            margin-bottom: 10px;
        }

        #wdr-font-size {
            width: ~"calc(100% - 230px)";
            width: ~"-webkit-calc(100% - 230px)";
            margin-left: 60px;
        }

        #wdr-sample {
            width: 70px;
        }
    }

    &.wdr-layout-mobile-small {
        .wdr-title-text {
            width: 100%;
        }

        .wdr-panel-content .wdr-toolbox {
            a.wdr-ui-btn:not(#wdr-add-btn) {
                width: ~"calc(50% - 42px)";
                width: ~"-webkit-calc(50% - 42px)";
            }
        }
    }
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner {
  font-size: 0;
  margin-bottom: 10px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner:last-child {
  margin-bottom: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner .wdr-cr-lbl {
  display: inline-block;
  vertical-align: top;
  color: @ui-label-color-light;
  font-size: @ui-label-font-size;
  margin-right: 10px;
  text-align: right;
  padding: 11px 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner .wdr-cr-lbl:last-child {
  margin-right: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner .wdr-select, .wdr-cr-inner
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-number-inp, 
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner .wdr-inp, 
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner .wdr-colorpick-wrap {
  display: inline-block;
  vertical-align: top;
  margin-right: 10px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner .wdr-select:last-child, 
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner .wdr-number-inp:last-child, 
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner .wdr-inp:last-child, 
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cr-inner .wdr-colorpick-wrap:last-child {
  margin-right: 0;
}

#wdr-toolbar-wrapper .wdr-icon-act_trash:before {
    content: "\e908";
}

#wdr-toolbar-wrapper .wdr-toolbar-ui span#wdr-and-label {
    display: inline-block;
    text-align: center;
}

/* GENERAL */

#wdr-toolbar-wrapper div.wdr-popup input[type=text],
#wdr-toolbar-wrapper div.wdr-popup input[type=number],
#wdr-toolbar-wrapper div.wdr-popup input[type=password] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: @ui-element-border;
    background-color: @background-ui-element-base-color;
    height: 38px;
    padding: 0 9px;
    color: @ui-element-text-color;
    font-size: @ui-element-font-size;
    width: 100%;
    -webkit-appearance: none;
    border-radius: 0px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin: 0;
    text-align: left;
    font-family: @font-family;
}

#wdr-toolbar-wrapper div.wdr-popup input:disabled {
    opacity: 0.5;
    filter: alpha(opacity=50);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    background-color: @background-ui-element-base-color;
}

#wdr-toolbar-wrapper div.wdr-popup input.wdr-half-input {
    padding-right: 0;
    border-right: none;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui select {
    border: @ui-element-border;
    background-color: @background-ui-element-base-color;
    -webkit-appearance: none;
    -moz-appearance: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    border-radius: 0px;
    margin: 0;
    outline: none;
    height: 38px;
    font-size: @ui-element-font-size;
    font-family: @font-family;
    width: 100%;
    color: @ui-element-text-color;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding: 4px 35px 4px 10px;
    cursor: pointer;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-select {
    position: relative;
}

    #wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-select:after {
        content: "\e902";
        font-family: 'webdatarocks-icons' !important;
        speak: none;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        right: 9px;
        position: absolute;
        font-size: 21px;
        color: @ui-icon-color;
        /*margin-top: 1px;*/
        top: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -webkit-transform: translateY(-50%);
        pointer-events: none;
    }

#wdr-toolbar-wrapper div.wdr-popup select::-ms-expand {
    display: none;
}

#wdr-toolbar-wrapper div.wdr-popup select:disabled {
    opacity: 0.5;
    filter: alpha(opacity=50);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    cursor: none;
    pointer-events: none;
}

#wdr-toolbar-wrapper div.wdr-popup select * {
    margin: 0 !important;
}

#wdr-toolbar-wrapper div.wdr-popup select option.placeholder {
    color: white;
    font-size: 0px;
    display: none;
}

#wdr-toolbar-wrapper div.wdr-clear {
    clear: both;
}

/* Hack for Firefox */
@-moz-document url-prefix() {
    #wdr-toolbar-wrapper select {
        text-indent: 0;
    }
}

/* COLOR PICKER */
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-btn {
  border: @ui-element-border;
  height: 38px;
  position: relative;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  font-size: 28px;
  cursor: pointer;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-btn span {
    font-size: 28px;
}

#wdr-toolbar-wrapper .wdr-icon-act_font:before {
  content: "\e90f";
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-popup {
  background: @background-base-color;
  width: 355px;
  padding: 30px 30px;
  border: @ui-element-border;
  position: absolute;
  -webkit-transition: opacity 0.3s;
  transition: opacity 0.3s;
  opacity: 0;
  visibility: hidden;
  left: 50%;
  margin-left: -178px;
  margin-top: -5px;
  z-index: 99;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-popup:before,
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-popup:after {
    content: '';
    display: block;
    position: absolute;
    left: 50%;
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-popup:before {
  width: 0px;
  height: 0px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-popup:after {
  width: 0px;
  height: 0px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-popup.wdr-arrow-up:before {
  top: -6px;
  border-left: 5.5px solid transparent;
  border-right: 5.5px solid transparent;
  border-bottom: 6px solid @ui-element-border-color;
  border-top: 0 solid transparent;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-popup.wdr-arrow-up:after {
  top: -5px;
  border-left: 4.5px solid transparent;
  border-right: 4.5px solid transparent;
  border-bottom: 5px solid #fff;
  border-top: 0 solid transparent;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-popup.wdr-arrow-down:before {
    bottom: -6px;
    border-left: 5.5px solid transparent;
    border-right: 5.5px solid transparent;
    border-top: 6px solid @ui-element-border-color;
    border-bottom: 0 solid transparent;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-colorpick-popup.wdr-arrow-down:after {
    bottom: -5px;
    border-left: 4.5px solid transparent;
    border-right: 4.5px solid transparent;
    border-top: 5px solid @background-base-color;
    border-bottom: 0 solid transparent;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-popup-opened .wdr-colorpick-popup {
  visibility: visible;
  opacity: 1;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-color-targ-switch {
  border: @ui-border-light;
  font-size: 0;
  margin-bottom: 20px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-color-targ-switch .wdr-cts-item {
  display: inline-block;
  vertical-align: top;
  width: 50%;
  border-right: @ui-border-light;
  line-height: 38px;
  font-weight: bold;
  color: @ui-btn-toggle-text-color;
  background: @background-ui-element-base-color;
  text-transform: uppercase;
  text-align: center;
  font-size: 14px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-color-targ-switch .wdr-cts-item.wdr-current {
  color: @ui-btn-toggle-selected-text-color;
  background: @ui-background-light;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-color-targ-switch .wdr-cts-item:hover {
  color: @ui-btn-toggle-selected-text-color;
  background: @ui-btn-color-hover;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-color-targ-switch .wdr-cts-item:last-child {
  border-right: none;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-sett-row {
  margin-bottom: 20px;
  font-size: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-sett-row .wdr-cp-curr-color {
  border: @ui-element-border;
  height: 38px;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  cursor: pointer;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-sett-row .wdr-cp-2-colors {
  border: @ui-border-light;
  font-size: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-sett-row .wdr-cp-2-colors .wdr-cp2c-item {
  height: 36px;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border-right: @ui-border-light;
  display: inline-block;
  vertical-align: top;
  width: 50%;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-sett-row .wdr-cp-2-colors .wdr-cp2c-item:last-child {
  border-right: none;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-sett-row .wdr-inp, .wdr-cp-sett-row .wdr-cp-curr-color, .wdr-cp-sett-row .wdr-cp-2-colors {
  display: inline-block;
  vertical-align: top;
  margin-right: 13px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-sett-row .wdr-inp:last-child, .wdr-cp-sett-row .wdr-cp-curr-color:last-child, .wdr-cp-sett-row .wdr-cp-2-colors:last-child {
  margin-right: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-btns-row {
    margin-top: 20px;

    .wdr-ui-btn {
        width: ~"calc(50% - 5px)";
        width: ~"-webkit-calc(50% - 5px)";
        margin-right: 10px;

        &:last-child {
            margin-right: 0;
        }
    }
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-currentmark {
  border-radius: 50%;
  width: 20px;
  height: 20px;
  background: #555;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-cp-currentmark:before {
  color: #fff;
  font-size: 16px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-9colors {
  font-size: 0;
  margin-bottom: 20px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-9colors .wdr-r9c-item {
  display: inline-block;
  vertical-align: top;
  position: relative;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  height: 36px;
  width: ~"-webkit-calc(11.11% - 1px)";
  width: ~"calc(11.11% - 1px)";
  margin-right: 1px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-9colors .wdr-r9c-item .wdr-r9c-arrow {
  position: absolute;
  top: 100%;
  border-style: solid;
  border-top-width: 10px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: none;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-9colors .wdr-r9c-item .wdr-cp-currentmark {
  margin-top: 2px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-9colors .wdr-r9c-item .wdr-r9c-arrow, 
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-9colors .wdr-r9c-item .wdr-cp-currentmark {
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-9colors .wdr-r9c-item:nth-child(9n+9) {
  margin-right: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-9colors .wdr-r9c-item.wdr-current .wdr-r9c-arrow,
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-9colors .wdr-r9c-item.wdr-current .wdr-cp-currentmark {
  opacity: 1;
  visibility: visible;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-4colors {
  font-size: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-4colors .wdr-r4c-item {
  display: inline-block;
  vertical-align: top;
  margin-right: 1px;
  margin-bottom: 1px;
  position: relative;
  cursor: pointer;
  height: 36px;
  width: ~"-webkit-calc(25% - 0.75px)";
  width: ~"calc(25% - 0.75px)";
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-4colors .wdr-r4c-item:nth-child(4n+4) {
  margin-right: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-4colors .wdr-r4c-item .wdr-cp-currentmark {
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-row-4colors .wdr-r4c-item.wdr-current .wdr-cp-currentmark {
  opacity: 1;
  visibility: visible;
}

#wdr-toolbar-wrapper.wdr-mobile {
    overflow-x: auto;
}

    /* new ui */

#wdr-toolbar-wrapper .wdr-radio-wrap label:before,
#wdr-toolbar-wrapper .wdr-radio-wrap label:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
}

#wdr-toolbar-wrapper .wdr-toolbar-ui div.wdr-title-2 {
  color: @ui-subtitle-color;
  font-size: @ui-subtitle-font-size;
  text-transform: uppercase;
  margin-bottom: 25px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-radiobtn-list {
  margin-bottom: 52px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-radiobtn-list:last-child {
  margin-bottom: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-radiobtn-list li {
  margin-bottom: 12px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-radio-wrap label {
  display: inline-block;
  cursor: pointer;
  color: @ui-element-text-color;
  font-size: @ui-element-font-size;
  padding: 4px 0;
  padding-left: 35px;
  position: relative;
  line-height: 1.2;
}

    #wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-radio-wrap label:before {
        width: 22px;
        height: 22px;
        border: @ui-element-border;
        background: @background-ui-element-base-color;
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
        left: 0;
        border-radius: 50%;
    }

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-radio-wrap label:after {
  background: @ui-background-dark;
  border-radius: 50%;
  opacity: 0;
  -webkit-transition: opacity 0.3s;
  transition: opacity 0.3s;
  width: 14px;
  height: 14px;
  left: 5px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-radio-wrap label:hover:before {
  border-color: @ui-border-color;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-radio-wrap input {
  display: none;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-radio-wrap input:checked + label {
  font-weight: bold;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-radio-wrap input:checked + label:after {
  opacity: 1;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-inp-group {
  margin-bottom: 30px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-inp-group:last-child {
  margin-bottom: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-inp-row {
  margin-bottom: 10px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-inp-row label {
  color: @ui-label-color-light;
  font-size: @ui-label-font-size;
  margin-bottom: 8px;
  display: block;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-inp-row .wdr-select, 
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-inp-row .wdr-inp {
  width: 100%;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-inp-row:last-child {
  margin-bottom: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-ir-horizontal {
  font-size: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-ir-horizontal label {
  margin-bottom: 0;
  width: 140px;
  margin-right: 20px;
  display: inline-block;
  vertical-align: middle;
  text-align: right;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-ir-horizontal .wdr-select, 
#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-ir-horizontal .wdr-inp {
  width: ~"-webkit-calc(100% - 160px)";
  width: ~"calc(100% - 160px)";
  display: inline-block;
  vertical-align: middle;
  background-color: @background-ui-element-base-color;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-uc {
    text-transform: uppercase;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-button-add {
  padding-right: 30px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-button-add .wdr-icon {
  color: @ui-icon-color;
  position: absolute;
  right: 0;
  width: 40px;
  -webkit-transition: color 0.3s;
  transition: color 0.3s;
  height: 100%;
  top: 0;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-button-add:hover .wdr-icon {
  color: @ui-icon-color-dark;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-icon-act_add:before {
  content: "\e909";
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-icon-act_check:before {
  content: "\e901";
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width20.wdr-width20 {
  width: 20px;
}


#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width40.wdr-width40 {
  width: 40px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width45.wdr-width45 {
  width: 45px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width50.wdr-width50 {
  width: 50px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width85.wdr-width85 {
  width: 85px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width120.wdr-width120 {
  width: 120px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width140.wdr-width140 {
  width: 140px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width180.wdr-width180 {
  width: 180px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width200.wdr-width200 {
  width: 200px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width210.wdr-width210 {
  width: 210px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width220.wdr-width220 {
  width: 220px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width230.wdr-width230 {
  width: 230px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width240.wdr-width240 {
  width: 240px;
}

#wdr-toolbar-wrapper .wdr-toolbar-ui .wdr-width250.wdr-width250 {
  width: 250px;
}

/* Connect to OLAP */
#wdr-toolbar-wrapper #wdr-popup-olap {
    .wdr-ir-horizontal label {
        width: 110px;
    }

    .wdr-ir-horizontal .wdr-select {
        width: ~"-webkit-calc(100% - 130px)";
        width: ~"calc(100% - 130px)";
    }

    #wdr-inp-proxy-url {
        width: ~"-webkit-calc(100% - 230px)";
        width: ~"calc(100% - 230px)";
    }

    #wdr-btn-connect {
        width: 100px;
        height: 38px;
        padding-top: 10px;
    }

    &.wdr-layout-mobile-small {
        .wdr-ir-horizontal {
            label {
                text-align: left;
                width: 100%;
            }

            .wdr-select {
                width: 100%;
            }
        }

        #wdr-inp-proxy-url {
            width: ~"-webkit-calc(100% - 100px)";
            width: ~"calc(100% - 100px)";
        }
    }
}

/* Format cells */
#wdr-toolbar-wrapper #wdr-popup-format-cells {
    width: 460px;

    &.wdr-layout-mobile-small {
        .wdr-ir-horizontal {
            label {
                text-align: left;
                width: 100%;
            }

            .wdr-select, .wdr-inp {
                width: 100%;
            }
        }
    }
}

/* Options */
#wdr-toolbar-wrapper #wdr-popup-options {
    width: 570px;
    min-width: 320px;
}

/* layouts */
#wdr-toolbar-wrapper {
    &.wdr-layout-500 #wdr-toolbar {
        #wdr-tab-fields, #wdr-tab-fullscreen {
            display: none;
        }
    }
    &.wdr-layout-360 #wdr-toolbar {
        #wdr-tab-format {
            display: none;
        }
    }
    &.wdr-layout-300 #wdr-toolbar {
        #wdr-tab-options {
            display: none;
        }
    }
}

.wdr-resize-triggers {
    visibility: hidden;
    opacity: 0;
}

.wdr-resize-triggers, 
.wdr-resize-triggers > div, 
.wdr-contract-trigger:before {
    content: " ";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.wdr-resize-triggers > div {
    background: #eee;
    overflow: auto;
}

.wdr-contract-trigger:before {
    width: 200%;
    height: 200%;
}

#wdr-pivot-view {
    .wdr-resizable-handle {
        position: absolute;
        cursor: w-resize;

        &.wdr-left {
            left: 0;
            top: 0;
            bottom: 0;
            width: 5px;
        }

        &.wdr-right {
            right: 0;
            top: 0;
            bottom: 0;
            width: 5px;
        }
    } 
}