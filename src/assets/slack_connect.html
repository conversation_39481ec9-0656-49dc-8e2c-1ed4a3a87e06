<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slack Landing</title>
    <script type="text/javascript" src="js/library/jquery/jquery.min.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/library/jquery/jquery.min.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/library/jquery-validate/jquery.validate.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript"
        src="js/library/jquery-validate/additional-methods.min.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/config.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/emailflow/script.js?v=_BUILD_VERSION_"></script>
    <script>
          var selectedLan='en'
            if (localStorage.getItem("selectedLanguage") != null) {
             selectedLan = localStorage.getItem("selectedLanguage");
            }else{
                selectedLan='en'
           
        }
        $(document).ready(function () {
            $("#success_message").hide();
            $("#error_message").hide();
            var url = getApiBaseUrl('apiForSlackOAuth') + "/slack/redir";
            var params = parseUrlParam();
            var code = params["code"];
            var state = params["state"];
            $.ajax({
                url: url + '?code=' + code + '&state=' + state,
                type: 'GET',
                success: function (data) {
                    handleResponse(data);
                },
                error: function (data) {
                    handleResponse(data);
                }

            });

        });
        var article = {
    
    content: {
      en: "Slack connect successful",
      es: "Slack conectar exitosa",
      fr:"Connexion Slack réussie",
      it:"Connessione lenta riuscita"
    }
}
        function handleResponse(data) {
            if (data && data.success) {
                $("#success_message").show();
                $("#error_message").hide();
            } else {
                $("#success_message").hide();
                if (data) {
                    $("#error_message").text(data.error_message);
                }
                $("#error_message").show();
            }
            return
        }
    </script>
</head>

<body>
    <div id="error_message">OAuth Failed. Please try again later.</div>
    <div id="success_message"><script>get_i18n('content',selectedLan);</script></div>

</body>

</html>