[{"code": "ZWE", "city": "Antwerp", "airport": "ZWE, Antwerp Central, BE", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "ZWE, Antwerp, BE", "label": "ZWE, Antwerp, BE", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "ZYR", "city": "Brussels", "airport": "ZYR, Brussels Midi, BE", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "ZYR, Brussels, BE", "label": "ZYR, Brussels, BE", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "XHN", "city": "Liège", "airport": "<PERSON>H<PERSON>, <PERSON><PERSON>, BE", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "XHN, Liège, BE", "label": "XHN, Liège, BE", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "XHJ", "city": "Aachen", "airport": "XHJ, Aachen / Aix-la-Chapelle Hbf, DE", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "XHJ, Aachen, DE", "label": "XHJ, Aachen, DE", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "QKL", "city": "Cologne", "airport": "QKL, Cologne Hbf, DE", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "QKL, Cologne, DE", "label": "QKL, Cologne, DE", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "QDU", "city": "Düsseldorf", "airport": "QDU, Duesseldorf Hbf, DE", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "QDU, Düsseldorf, DE", "label": "QDU, Düsseldorf, DE", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "DUS", "city": "Düsseldorf", "airport": "DUS, Duesseldorf Airport Hbf, DE", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "DUS, Düsseldorf, DE", "label": "DUS, Düsseldorf, DE", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "DUI", "city": "Duisburg", "airport": "DUI, Duisburg Hbf, DE", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "DUI, Duisburg, DE", "label": "DUI, Duisburg, DE", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "DTZ", "city": "Dortmund", "airport": "DTZ, Dortmund Hbf, DE", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "DTZ, Dortmund, DE", "label": "DTZ, Dortmund, DE", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "ESZ", "city": "Essen", "airport": "ESZ, Essen Hbf, DE", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "ESZ, Essen, DE", "label": "ESZ, Essen, DE", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "XDB", "city": "Lille", "airport": "XDB, Lille Europe, FR", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "XDB, Lille, FR", "label": "XDB, Lille, FR", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "XED", "city": "Marne-la-Vallée", "airport": "XED, Marne-la-Vallée, Disneyland, FR", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "XED, Marne-la-Vallée, FR", "label": "XED, Marne-la-Vallée, FR", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "XPG", "city": "Paris", "airport": "XPG, Paris Gare du Nord, FR", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "XPG, Paris, FR", "label": "XPG, Paris, FR", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "CDG", "city": "Paris", "airport": "CDG, Paris Charles de Gaulle Airport Station, FR", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "CDG, Paris, FR", "label": "CDG, Paris, FR", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "QQS", "city": "London", "airport": "QQS, London St Pancras International, GB", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "QQS, London, GB", "label": "QQS, London, GB", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "ZYA", "city": "Amsterdam", "airport": "ZYA, Amsterdam Centraal, NL", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "ZYA, Amsterdam, NL", "label": "ZYA, Amsterdam, NL", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "QRH", "city": "Rotterdam", "airport": "QRH, Rotterdam Centraal, NL", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "QRH, Rotterdam, NL", "label": "QRH, Rotterdam, NL", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "AMS", "city": "Amsterdam", "airport": "AMS, Schiphol Airport Train Station, NL", "location": {"lat": "35.88007899999999", "lon": "-78.7879963"}, "keyWord": "AMS, Amsterdam, NL", "label": "AMS, Amsterdam, NL", "region": "Europe", "trainLine": ["Eurostar"], "trainLineCode": ["EUS"]}, {"code": "BOS", "city": "Boston, MA", "airport": "Boston South", "location": {"lat": "42.3522", "lon": "-71.0552"}, "keyWord": "BOS", "label": "BOS, Boston South", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "BBY", "city": "Boston, MA", "airport": "Boston Back Bay", "location": {"lat": "42.3473", "lon": "-71.0757"}, "keyWord": "BBY", "label": "BBY, Boston Back Bay", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "RTE", "city": "<PERSON>, MA", "airport": "Westwood Route 128", "location": {"lat": "42.2102", "lon": "-71.1473"}, "keyWord": "RTE", "label": "RTE, Westwood Route 128", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "PVD", "city": "Providence, RI", "airport": "Providence", "location": {"lat": "41.824", "lon": "-71.4128"}, "keyWord": "PVD", "label": "PVD, Providence", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "NHV", "city": "New Haven, CT", "airport": "New Haven Union Station", "location": {"lat": "41.2987", "lon": "-72.9286"}, "keyWord": "NHV", "label": "NHV, New Haven Union Station", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "STM", "city": "Stamford, CT", "airport": "Stamford", "location": {"lat": "41.0468", "lon": "-73.5423"}, "keyWord": "STM", "label": "STM, Stamford", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "NYP", "city": "New York, NY", "airport": "New York Penn Station", "location": {"lat": "40.7506", "lon": "-73.9935"}, "keyWord": "NYP", "label": "NYP, New York Penn Station", "region": "USA", "trainLine": ["North East Regional / Keystone", "Empire Service"], "trainLineCode": ["NER", "EMS"]}, {"code": "NWK", "city": "Newark, NJ", "airport": "Newark Penn Station", "location": {"lat": "40.7346", "lon": "-74.1641"}, "keyWord": "NWK", "label": "NWK, Newark Penn Station", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "MET", "city": "Iselin, NJ", "airport": "Iselin Metropark", "location": {"lat": "40.5684", "lon": "-74.3297"}, "keyWord": "MET", "label": "MET, Iselin Metropark", "region": "USA", "trainLine": ["<PERSON><PERSON>"], "trainLineCode": ["ACL"]}, {"code": "PHL", "city": "Philadelphia, PA", "airport": "Philadelphia 30th Street", "location": {"lat": "39.9556", "lon": "-75.1810"}, "keyWord": "PHL", "label": "PHL, Philadelphia 30th Street", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "WIL", "city": "Wilmington, DE", "airport": "Wilmington", "location": {"lat": "39.738", "lon": "-75.5515"}, "keyWord": "WIL", "label": "WIL, Wilmington", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "BAL", "city": "Baltimore, MD", "airport": "Baltimore Penn Station", "location": {"lat": "39.3072", "lon": "-76.6158"}, "keyWord": "BAL", "label": "BAL, Baltimore Penn Station", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "BWI", "city": "Baltimore, MD", "airport": "Baltimore BWI", "location": {"lat": "39.1909", "lon": "-76.6684"}, "keyWord": "BWI", "label": "BWI, Baltimore BWI", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "WAS", "city": "Washington, DC", "airport": "Washington Union Station", "location": {"lat": "38.8977", "lon": "-77.0065"}, "keyWord": "WAS", "label": "WAS, Washington Union Station", "region": "USA", "trainLine": ["<PERSON><PERSON>", "North East Regional / Keystone"], "trainLineCode": ["ACL", "NER"]}, {"code": "BRP", "city": "Bridgeport, CT", "airport": "Bridgeport", "location": {"lat": "41.1792", "lon": "-73.1890"}, "keyWord": "BRP", "label": "BRP, Bridgeport", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "HFD", "city": "Hartford, CT", "airport": "Hartford", "location": {"lat": "41.7637", "lon": "-72.6851"}, "keyWord": "HFD", "label": "HFD, Hartford", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "KNB", "city": "Berlin, CT", "airport": "Kensington Berlin", "location": {"lat": "41.6144", "lon": "-72.7952"}, "keyWord": "KNB", "label": "KNB, Kensington Berlin", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "MDN", "city": "Meriden, CT", "airport": "Meriden", "location": {"lat": "41.5382", "lon": "-72.8070"}, "keyWord": "MDN", "label": "MDN, Meriden", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "MYS", "city": "Mystic, CT", "airport": "Mystic", "location": {"lat": "41.3747", "lon": "-71.9579"}, "keyWord": "MYS", "label": "MYS, Mystic", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "NLC", "city": "New London, CT", "airport": "New London", "location": {"lat": "41.3542", "lon": "-72.0912"}, "keyWord": "NLC", "label": "NLC, New London", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "OSB", "city": "Old Saybrook, CT", "airport": "Old Saybrook", "location": {"lat": "41.2902", "lon": "-72.3570"}, "keyWord": "OSB", "label": "OSB, Old Saybrook", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "NST", "city": "New Haven, CT", "airport": "New Haven State Street", "location": {"lat": "41.3076", "lon": "-72.9276"}, "keyWord": "NST", "label": "NST, New Haven State Street", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "WFD", "city": "Wallingford, CT", "airport": "Wallingford", "location": {"lat": "41.4540", "lon": "-72.8197"}, "keyWord": "WFD", "label": "WFD, Wallingford", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "WND", "city": "Windsor, CT", "airport": "Windsor", "location": {"lat": "41.8680", "lon": "-72.6541"}, "keyWord": "WND", "label": "WND, Windsor", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "WNL", "city": "Windsor Locks, CT", "airport": "Windsor Locks", "location": {"lat": "41.9313", "lon": "-72.6400"}, "keyWord": "WNL", "label": "WNL, Windsor Locks", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "NRK", "city": "Newark, DE", "airport": "Newark", "location": {"lat": "39.6837", "lon": "-75.7484"}, "keyWord": "NRK", "label": "NRK, Newark", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "SPG", "city": "Springfield, MA", "airport": "Springfield", "location": {"lat": "42.1015", "lon": "-72.5898"}, "keyWord": "SPG", "label": "SPG, Springfield", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "ABE", "city": "Aberdeen, MD", "airport": "Aberdeen", "location": {"lat": "39.5095", "lon": "-76.1655"}, "keyWord": "ABE", "label": "ABE, Aberdeen", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "NCR", "city": "New Carrollton, MD", "airport": "New Carrollton", "location": {"lat": "38.9573", "lon": "-76.8804"}, "keyWord": "NCR", "label": "NCR, New Carrollton", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "MET", "city": "Iselin, NJ", "airport": "Iselin Metropark", "location": {"lat": "40.5659", "lon": "-74.2864"}, "keyWord": "MET", "label": "MET, Iselin Metropark", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "NBK", "city": "New Brunswick, NJ", "airport": "New Brunswick", "location": {"lat": "40.4862", "lon": "-74.4513"}, "keyWord": "NBK", "label": "NBK, New Brunswick", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "PRN", "city": "Princeton, NJ", "airport": "Princeton", "location": {"lat": "40.3583", "lon": "-74.6672"}, "keyWord": "PRN", "label": "PRN, Princeton", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "TRE", "city": "Trenton, NJ", "airport": "Trenton", "location": {"lat": "40.2315", "lon": "-74.7561"}, "keyWord": "TRE", "label": "TRE, Trenton", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "NRO", "city": "New Rochelle, NY", "airport": "New Rochelle", "location": {"lat": "40.9115", "lon": "-73.7824"}, "keyWord": "NRO", "label": "NRO, New Rochelle", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "CWH", "city": "Cornwells Heights, PA", "airport": "Cornwells Heights", "location": {"lat": "40.1156", "lon": "-74.9932"}, "keyWord": "CWH", "label": "CWH, Cornwells Heights", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "PHN", "city": "Philadelphia, PA", "airport": "North Philadelphia", "location": {"lat": "39.9795", "lon": "-75.1578"}, "keyWord": "PHN", "label": "PHN, North Philadelphia", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "ARD", "city": "Ardmore, PA", "airport": "<PERSON><PERSON><PERSON>", "location": {"lat": "40.0115", "lon": "-75.2900"}, "keyWord": "ARD", "label": "ARD, Ardmore", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "PAO", "city": "Paoli, PA", "airport": "<PERSON><PERSON>", "location": {"lat": "40.0223", "lon": "-75.4811"}, "keyWord": "PAO", "label": "PAO, Paoli", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "EXT", "city": "Exton, PA", "airport": "Exton", "location": {"lat": "40.0354", "lon": "-75.6172"}, "keyWord": "EXT", "label": "EXT, Exton", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "DOW", "city": "Downingtown, PA", "airport": "Downingtown", "location": {"lat": "40.0069", "lon": "-75.7530"}, "keyWord": "DOW", "label": "DOW, Downingtown", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "COT", "city": "Coatesville, PA", "airport": "Coatesville", "location": {"lat": "39.9993", "lon": "-75.8235"}, "keyWord": "COT", "label": "COT, Coatesville", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "PAR", "city": "Parkesburg, PA", "airport": "Parkesburg", "location": {"lat": "39.9983", "lon": "-75.9319"}, "keyWord": "PAR", "label": "PAR, Parkesburg", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "LNC", "city": "Lancaster, PA", "airport": "Lancaster", "location": {"lat": "40.0379", "lon": "-76.3030"}, "keyWord": "LNC", "label": "LNC, Lancaster", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "MJY", "city": "Mount Joy, PA", "airport": "Mount Joy", "location": {"lat": "40.1052", "lon": "-76.5375"}, "keyWord": "MJY", "label": "MJY, Mount Joy", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "ELZ", "city": "Elizabethtown, PA", "airport": "Elizabethtown", "location": {"lat": "40.1522", "lon": "-76.6123"}, "keyWord": "ELZ", "label": "ELZ, Elizabethtown", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "MID", "city": "Middletown, PA", "airport": "Middletown", "location": {"lat": "40.2000", "lon": "-76.7326"}, "keyWord": "MID", "label": "MID, Middletown", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "HAR", "city": "Harrisburg, PA", "airport": "Harrisburg", "location": {"lat": "40.2672", "lon": "-76.8836"}, "keyWord": "HAR", "label": "HAR, Harrisburg", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "KIN", "city": "Kingston, RI", "airport": "Kingston", "location": {"lat": "41.4826", "lon": "-71.5268"}, "keyWord": "KIN", "label": "KIN, Kingston", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "WLY", "city": "Westerly, RI", "airport": "Westerly", "location": {"lat": "41.3743", "lon": "-71.8004"}, "keyWord": "WLY", "label": "WLY, Westerly", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "ALX", "city": "Alexandria, VA", "airport": "Alexandria", "location": {"lat": "38.8048", "lon": "-77.0469"}, "keyWord": "ALX", "label": "ALX, Alexandria", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "BCV", "city": "Burke, VA", "airport": "<PERSON>", "location": {"lat": "38.7810", "lon": "-77.2725"}, "keyWord": "BCV", "label": "<PERSON><PERSON>, <PERSON>", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "CLP", "city": "Culpeper, VA", "airport": "Culpeper", "location": {"lat": "38.4788", "lon": "-77.9983"}, "keyWord": "CLP", "label": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "CVS", "city": "Charlottesville, VA", "airport": "Charlottesville", "location": {"lat": "38.0293", "lon": "-78.4767"}, "keyWord": "CVS", "label": "CVS, Charlottesville", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "LYH", "city": "Lynchburg, VA", "airport": "Lynchburg", "location": {"lat": "37.4138", "lon": "-79.1422"}, "keyWord": "LYH", "label": "LYH, Lynchburg", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "RNK", "city": "Roanoke, VA", "airport": "Roanoke", "location": {"lat": "37.2707", "lon": "-79.9414"}, "keyWord": "RNK", "label": "RNK, Roanoke", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "MSS", "city": "Manassas, VA", "airport": "Manassas", "location": {"lat": "38.7509", "lon": "-77.4740"}, "keyWord": "MSS", "label": "MSS, Manassas", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "WDB", "city": "Woodbridge, VA", "airport": "Woodbridge", "location": {"lat": "38.6583", "lon": "-77.2655"}, "keyWord": "WDB", "label": "WDB, Woodbridge", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "QAN", "city": "Quantico, VA", "airport": "Quantico", "location": {"lat": "38.5060", "lon": "-77.2905"}, "keyWord": "QAN", "label": "QAN, Quantico", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "FBK", "city": "Fredericksburg, VA", "airport": "Fredericksburg", "location": {"lat": "38.3032", "lon": "-77.4600"}, "keyWord": "FBK", "label": "FBK, Fredericksburg", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "RVR", "city": "Richmond, VA", "airport": "Richmond Staples Mill Road", "location": {"lat": "37.6104", "lon": "-77.5226"}, "keyWord": "RVR", "label": "RVR, Richmond Staples Mill Road", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "RVM", "city": "Richmond, VA", "airport": "Richmond Main Street", "location": {"lat": "37.5395", "lon": "-77.4337"}, "keyWord": "RVM", "label": "RVM, Richmond Main Street", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "WBG", "city": "Williamsburg, VA", "airport": "Williamsburg", "location": {"lat": "37.2707", "lon": "-76.7075"}, "keyWord": "WBG", "label": "WBG, Williamsburg", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "NPN", "city": "Newport News, VA", "airport": "Newport News", "location": {"lat": "37.0871", "lon": "-76.4922"}, "keyWord": "NPN", "label": "NPN, Newport News", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "PTB", "city": "Petersburg, VA", "airport": "Petersburg", "location": {"lat": "37.2275", "lon": "-77.4009"}, "keyWord": "PTB", "label": "PTB, Petersburg", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "NFK", "city": "Norfolk, VA", "airport": "Norfolk", "location": {"lat": "36.8508", "lon": "-76.2859"}, "keyWord": "NFK", "label": "NFK, Norfolk", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "MCO", "city": "Orlando, FL", "airport": "Orlando International Airport", "location": {"lat": "28.4237", "lon": "-81.3080"}, "keyWord": "MCO", "label": "MCO, Orlando International Airport", "region": "USA", "trainLine": ["Brightline Florida"], "trainLineCode": ["BLF"]}, {"code": "WPB", "city": "West Palm Beach, FL", "airport": "West Palm Beach", "location": {"lat": "26.7153", "lon": "-80.0534"}, "keyWord": "WPB", "label": "WPB, West Palm Beach", "region": "USA", "trainLine": ["Brightline Florida"], "trainLineCode": ["BLF"]}, {"code": "BCR", "city": "Boca Raton, FL", "airport": "Boca Raton", "location": {"lat": "26.3683", "lon": "-80.1280"}, "keyWord": "BCR", "label": "BCR, Boca Raton", "region": "USA", "trainLine": ["Brightline Florida"], "trainLineCode": ["BLF"]}, {"code": "FTL", "city": "Fort Lauderdale, FL", "airport": "Fort Lauderdale", "location": {"lat": "26.1223", "lon": "-80.1434"}, "keyWord": "FTL", "label": "FTL, Fort Lauderdale", "region": "USA", "trainLine": ["Brightline Florida"], "trainLineCode": ["BLF"]}, {"code": "AVT", "city": "Aventura, FL", "airport": "Aventura", "location": {"lat": "25.9577", "lon": "-80.1370"}, "keyWord": "AVT", "label": "AVT, Aventura", "region": "USA", "trainLine": ["Brightline Florida"], "trainLineCode": ["BLF"]}, {"code": "MIA", "city": "Miami, FL", "airport": "MiamiCentral", "location": {"lat": "25.7767", "lon": "-80.1918"}, "keyWord": "MIA", "label": "MIA, MiamiCentral", "region": "USA", "trainLine": ["Brightline Florida"], "trainLineCode": ["BLF"]}, {"code": "SLO", "city": "San Luis Obispo, CA", "airport": "San Luis Obispo", "location": {"lat": "35.2828", "lon": "-120.6596"}, "keyWord": "SLO", "label": "SLO, San Luis Obispo", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "GVB", "city": "Grover Beach, CA", "airport": "Grover Beach", "location": {"lat": "35.1203", "lon": "-120.6195"}, "keyWord": "GVB", "label": "GVB, Grover Beach", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "GUA", "city": "Guadalupe, CA", "airport": "Guadalupe", "location": {"lat": "34.9592", "lon": "-120.5892"}, "keyWord": "GUA", "label": "GUA, Guadalupe", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "LPS", "city": "Lompoc, CA", "airport": "Lompoc–Surf", "location": {"lat": "34.6395", "lon": "-120.4570"}, "keyWord": "LPS", "label": "LPS, Lompoc–Surf", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "GTA", "city": "Goleta, CA", "airport": "<PERSON><PERSON><PERSON>", "location": {"lat": "34.4447", "lon": "-119.8278"}, "keyWord": "GTA", "label": "GTA, Goleta", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "SBA", "city": "Santa Barbara, CA", "airport": "Santa Barbara", "location": {"lat": "34.4208", "lon": "-119.6982"}, "keyWord": "SBA", "label": "SBA, Santa Barbara", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "CPN", "city": "Carpinteria, CA", "airport": "Carpinteria", "location": {"lat": "34.3856", "lon": "-119.5151"}, "keyWord": "CPN", "label": "CPN, Carpinteria", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "VEC", "city": "Ventura, CA", "airport": "Ventura", "location": {"lat": "34.2746", "lon": "-119.2290"}, "keyWord": "VEC", "label": "VEC, Ventura", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "OXN", "city": "Oxnard, CA", "airport": "<PERSON><PERSON><PERSON>", "location": {"lat": "34.1975", "lon": "-119.1771"}, "keyWord": "OXN", "label": "OXN, Oxnard", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "MPK", "city": "Moorpark, CA", "airport": "Moorpark", "location": {"lat": "34.2842", "lon": "-118.8583"}, "keyWord": "MPK", "label": "MPK, Moorpark", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "SIM", "city": "Simi Valley, CA", "airport": "Simi Valley", "location": {"lat": "34.2694", "lon": "-118.7815"}, "keyWord": "SIM", "label": "SIM, Simi Valley", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "CWT", "city": "Chatsworth, CA", "airport": "Chatsworth", "location": {"lat": "34.2583", "lon": "-118.6091"}, "keyWord": "CWT", "label": "CWT, Chatsworth", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "NRT", "city": "Northridge, CA", "airport": "Northridge", "location": {"lat": "34.2361", "lon": "-118.5371"}, "keyWord": "NRT", "label": "NRT, Northridge", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "VNC", "city": "<PERSON>, CA", "airport": "<PERSON>", "location": {"lat": "34.1867", "lon": "-118.4461"}, "keyWord": "VNC", "label": "VNC, <PERSON>", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "BUR", "city": "Burbank, CA", "airport": "Hollywood Burbank Airport", "location": {"lat": "34.2006", "lon": "-118.3584"}, "keyWord": "BUR", "label": "BUR, Hollywood Burbank Airport", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "BND", "city": "Burbank, CA", "airport": "Burbank Downtown", "location": {"lat": "34.1829", "lon": "-118.3089"}, "keyWord": "BND", "label": "BND, Burbank Downtown", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "GDL", "city": "Glendale, CA", "airport": "Glendale", "location": {"lat": "34.1426", "lon": "-118.2551"}, "keyWord": "GDL", "label": "GDL, Glendale", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "LAX", "city": "Los Angeles, CA", "airport": "Los Angeles Downtown", "location": {"lat": "34.0522", "lon": "-118.2437"}, "keyWord": "LAX", "label": "LAX, Los Angeles Downtown", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "FUL", "city": "Fullerton, CA", "airport": "<PERSON><PERSON>", "location": {"lat": "33.8703", "lon": "-117.9245"}, "keyWord": "FUL", "label": "FUL, Fullerton", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "ANA", "city": "Anaheim, CA", "airport": "Anaheim", "location": {"lat": "33.8366", "lon": "-117.9143"}, "keyWord": "ANA", "label": "ANA, Anaheim", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "SNA", "city": "Santa Ana, CA", "airport": "Santa Ana", "location": {"lat": "33.7455", "lon": "-117.8677"}, "keyWord": "SNA", "label": "SNA, Santa Ana", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "IRV", "city": "Irvine, CA", "airport": "Irvine", "location": {"lat": "33.6846", "lon": "-117.8265"}, "keyWord": "IRV", "label": "IRV, Irvine", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "SNC", "city": "San Juan Capistrano, CA", "airport": "San Juan Capistrano", "location": {"lat": "33.4956", "lon": "-117.6637"}, "keyWord": "SNC", "label": "SNC, San Juan Capistrano", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "SNP", "city": "San Clemente, CA", "airport": "San Clemente Pier", "location": {"lat": "33.4268", "lon": "-117.6114"}, "keyWord": "SNP", "label": "SNP, San Clemente Pier", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "OSD", "city": "Oceanside, CA", "airport": "Oceanside", "location": {"lat": "33.1955", "lon": "-117.3797"}, "keyWord": "OSD", "label": "OSD, Oceanside", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "SOL", "city": "Solana Beach, CA", "airport": "Solana Beach", "location": {"lat": "33.0286", "lon": "-117.2717"}, "keyWord": "SOL", "label": "SOL, Solana Beach", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "OLT", "city": "San Diego, California", "airport": "San Diego Old Town", "location": {"lat": "32.7552", "lon": "-117.2403"}, "keyWord": "OLT", "label": "OLT, San Diego Old Town", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "SAN", "city": "San Diego, CA", "airport": "San Diego", "location": {"lat": "32.7157", "lon": "-117.1611"}, "keyWord": "SAN", "label": "SAN, San Diego", "region": "USA", "trainLine": ["Pacific Surfliner"], "trainLineCode": ["PSL"]}, {"code": "YNK", "city": "Yonkers, NY", "airport": "Yonkers", "location": {"lat": "40.9312", "lon": "-73.8988"}, "keyWord": "YNK", "label": "YNK, Yonkers", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "CRT", "city": "Croton-on-Hudson, NY", "airport": "Croton-Harmon", "location": {"lat": "41.1851", "lon": "-73.8876"}, "keyWord": "CRT", "label": "CRT, Croton-Harmon", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "POU", "city": "Poughkeepsie, NY", "airport": "Poughkeepsie", "location": {"lat": "41.7059", "lon": "-73.9216"}, "keyWord": "POU", "label": "POU, Poughkeepsie", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "RHI", "city": "Rhinecliff, NY", "airport": "Rhinecliff-Kingston", "location": {"lat": "42.0366", "lon": "-73.9279"}, "keyWord": "RHI", "label": "RHI, Rhinecliff-Kingston", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "HUD", "city": "Hudson, NY", "airport": "<PERSON>", "location": {"lat": "42.2551", "lon": "-73.7837"}, "keyWord": "HUD", "label": "HUD, Hudson", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "ALB", "city": "Albany, NY", "airport": "Albany-Renssel<PERSON>r", "location": {"lat": "42.6526", "lon": "-73.7562"}, "keyWord": "ALB", "label": "ALB, Albany-Rensselaer", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "SDY", "city": "Schenectady, NY", "airport": "Schenectady", "location": {"lat": "42.8142", "lon": "-73.9396"}, "keyWord": "SDY", "label": "SDY, Schenectady", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "UCA", "city": "Utica, NY", "airport": "Utica", "location": {"lat": "43.1009", "lon": "-75.2327"}, "keyWord": "UCA", "label": "UCA, Utica", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "ROM", "city": "Rome, NY", "airport": "Rome", "location": {"lat": "43.2082", "lon": "-75.4550"}, "keyWord": "ROM", "label": "ROM, Rome", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "SYR", "city": "Syracuse, NY", "airport": "Syracuse", "location": {"lat": "43.0481", "lon": "-76.1474"}, "keyWord": "SYR", "label": "SYR, Syracuse", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "ROC", "city": "Rochester, NY", "airport": "Rochester", "location": {"lat": "43.1610", "lon": "-77.6109"}, "keyWord": "ROC", "label": "ROC, Rochester", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "BUF", "city": "Buffalo, NY", "airport": "Buffalo-Depew", "location": {"lat": "42.9028", "lon": "-78.7329"}, "keyWord": "BUF", "label": "BUF, Buffalo-Depew", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "BFX", "city": "Buffalo, NY", "airport": "Buffalo-Exchange Street", "location": {"lat": "42.8876", "lon": "-78.8785"}, "keyWord": "BFX", "label": "BFX, Buffalo-Exchange Street", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "NFL", "city": "Niagara Falls, NY", "airport": "Niagara Falls", "location": {"lat": "43.0962", "lon": "-79.0377"}, "keyWord": "NFL", "label": "NFL, Niagara Falls", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "ARN", "city": "Auburn, CA", "airport": "Auburn", "location": {"lat": "38.8808", "lon": "-121.0763"}, "keyWord": "ARN", "label": "ARN, Auburn", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "RLN", "city": "Rocklin, CA", "airport": "<PERSON><PERSON>", "location": {"lat": "38.7909", "lon": "-121.2350"}, "keyWord": "RLN", "label": "RLN, Rocklin", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "RSV", "city": "Roseville, CA", "airport": "Roseville", "location": {"lat": "38.7521", "lon": "-121.2880"}, "keyWord": "RSV", "label": "RSV, Roseville", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "SAC", "city": "Sacramento, CA", "airport": "Sacramento", "location": {"lat": "38.5816", "lon": "-121.4944"}, "keyWord": "SAC", "label": "SAC, Sacramento", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "DAV", "city": "<PERSON>, CA", "airport": "<PERSON>", "location": {"lat": "38.5449", "lon": "-121.7397"}, "keyWord": "DAV", "label": "<PERSON><PERSON>, <PERSON>", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "FFV", "city": "Fairfield, CA", "airport": "Fairfield-Vacaville", "location": {"lat": "38.2494", "lon": "-122.0390"}, "keyWord": "FFV", "label": "FFV, Fairfield-Vacaville", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "SUI", "city": "Suisun City, CA", "airport": "Suisun-Fairfield", "location": {"lat": "38.2554", "lon": "-122.0390"}, "keyWord": "SUI", "label": "SUI, Suisun-Fairfield", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "MTZ", "city": "<PERSON>, CA", "airport": "<PERSON>", "location": {"lat": "38.0190", "lon": "-122.1349"}, "keyWord": "MTZ", "label": "MTZ, Martinez", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "RIC", "city": "Richmond, CA", "airport": "Richmond", "location": {"lat": "37.9353", "lon": "-122.3478"}, "keyWord": "RIC", "label": "RIC, Richmond", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "BKY", "city": "Berkeley, CA", "airport": "Berkeley", "location": {"lat": "37.8716", "lon": "-122.2727"}, "keyWord": "BKY", "label": "BKY, Berkeley", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "EMY", "city": "Emeryville, CA", "airport": "Emeryville", "location": {"lat": "37.8315", "lon": "-122.2880"}, "keyWord": "EMY", "label": "EMY, Emeryville", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "OKJ", "city": "Oakland, CA", "airport": "Oakland Jack London Square", "location": {"lat": "37.7955", "lon": "-122.2761"}, "keyWord": "OKJ", "label": "OKJ, Oakland Jack London Square", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}, {"code": "OAC", "city": "Oakland, CA", "airport": "Oakland Coliseum", "location": {"lat": "37.7514", "lon": "-122.1903"}, "keyWord": "OAC", "label": "OAC, Oakland Coliseum", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "HAY", "city": "<PERSON>, CA", "airport": "<PERSON>", "location": {"lat": "37.6688", "lon": "-122.0808"}, "keyWord": "HAY", "label": "HAY, Hayward", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "FMT", "city": "Fremont, CA", "airport": "Fremont", "location": {"lat": "37.5483", "lon": "-121.9886"}, "keyWord": "FMT", "label": "FMT, Fremont", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "GAC", "city": "Santa Clara, CA", "airport": "Santa Clara-Great America", "location": {"lat": "37.3772", "lon": "-121.9731"}, "keyWord": "GAC", "label": "GAC, Santa Clara-Great America", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "SJC", "city": "San Jose, CA", "airport": "San Jose Dirid<PERSON>", "location": {"lat": "37.3300", "lon": "-121.8852"}, "keyWord": "SJC", "label": "SJC, San Jose Diridon", "region": "USA", "trainLine": ["Capitol Corridor"], "trainLineCode": ["CCR"]}, {"code": "ASD", "city": "Ashland, VA", "airport": "Ashland", "location": {"lat": "37.7599", "lon": "-77.4875"}, "keyWord": "ASD", "label": "ASD, Ashland", "region": "USA", "trainLine": ["North East Regional / Keystone"], "trainLineCode": ["NER"]}, {"code": "NYF", "city": "New York State Fair", "airport": "New York State Fair", "location": {"lat": "37.7599", "lon": "-77.4875"}, "keyWord": "NYF", "label": "NYF, New York State Fair", "region": "USA", "trainLine": ["Empire Service"], "trainLineCode": ["EMS"]}]