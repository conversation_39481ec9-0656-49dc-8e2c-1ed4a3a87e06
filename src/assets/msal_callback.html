<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign in with Microsoft</title>
    <script type="text/javascript" src="js/library/jquery/jquery.min.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/library/jquery/jquery.min.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/library/jquery-validate/jquery.validate.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript"
        src="js/library/jquery-validate/additional-methods.min.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/config.js?v=_BUILD_VERSION_"></script>
    <script type="text/javascript" src="js/emailflow/script.js?v=_BUILD_VERSION_"></script>
    <style>
        @font-face {
            font-family: 'apercu-b';
            src: url("../assets/fonts/apercu_bold_pro.otf") format("opentype");
            font-weight: normal;
            font-style: normal;
        }

        .input-mst {
            text-align: center;
            margin: auto;
        }

        .logo-border {
            display: block;
            height: 48px;
            width: 48px;
            background-color: #DDFFF7;
            border-radius: 50%;
            position: relative;
            margin: 0 auto;
            margin-bottom: 20px;
        }

        .logo-border1 {
            display: block;
            height: 48px;
            width: 48px;
            background-color: #FFE8EB;
            border-radius: 50%;
            position: relative;
            margin: 0 auto;
            margin-bottom: 20px;
        }

        .modal-logo {
            text-align: center;
            display: block;
            position: absolute;
            top: 36%;
            left: 35.3%;
        }

        .checkmark {
            border: 3px solid #1EBD97;
            background-color: #DDFFF7;
        }

        .icon-checkmark {
            color: #1EBD97;
            font-size: 19px;
            left: 29%;
            top: 23%;
        }

        .cross-bold {
            border: 3px solid #f93d30;
            background-color: #FFE8EB;
        }

        .icon-cross-bold {
            color: #f93d30;
        }


        .spinner {
            margin: 2vh auto 0 auto;
            width: 40px;
            height: 40px;
            text-align: center;
        }

        .spinner12 {
            margin: 0px auto 0 auto;
            width: 80px;
            height: 35px;
            text-align: center;
        }

        .spinner>div {
            background-color: var(--dark-bg-color);
            height: 100%;
            width: 3px;
            display: inline-block;
            margin: 0 1px;
            -webkit-animation: sk-stretchdelay .8s infinite ease-in-out;
            animation: sk-stretchdelay 0.8s infinite ease-in-out;
        }

        .spinner12>div {
            background-color: var(--dark-bg-color);
            height: 100%;
            width: 2px;
            margin: 0 0px;
            display: inline-block;
            -webkit-animation: sk-stretchdelay .8s infinite ease-in-out;
            animation: sk-stretchdelay 0.8s infinite ease-in-out;
        }

        .logo a {
            display: inline-block;
            width: 170px;
        }

        .spinner .rect1 {
            -webkit-animation-delay: -1.2s;
            animation-delay: -1.2s;
        }

        .spinner .rect2 {
            -webkit-animation-delay: -.8s;
            animation-delay: -.8;
        }

        .text-show {
            font-family: var(--globalFontfamilyr);font-weight: bold;;
            font-size: 20px;

        }

        .spinner12 .rect1 {
            -webkit-animation-delay: -1.2s;
            animation-delay: -1.2s;
        }

        .spinner12 .rect2 {
            -webkit-animation-delay: -.8s;
            animation-delay: -.8s;
        }

        span {
            content: "\2713";
        }

        @-webkit-keyframes sk-stretchdelay {

            0%,
            80%,
            100% {
                -webkit-transform: scaleY(0.4);
                opacity: 1
            }

            40% {
                -webkit-transform: scaleY(1.0);
                opacity: .4
            }
        }

        @keyframes sk-stretchdelay {

            0%,
            80%,
            100% {
                transform: scaleY(0.4);
                -webkit-transform: scaleY(0.4);
                ;
                opacity: 1
            }

            40% {
                transform: scaleY(1.0);
                -webkit-transform: scaleY(1.0);
                ;
                opacity: .4
            }
        }

        .logo-icon {
            width: 200px;
            height: 80px;
            background-image: url(/../assets/images/logo.png);
            background-repeat: no-repeat;
            display: inline-block;
            background-size: 200px;
        }

        @media (max-width:767px) {
            .logo-icon {
                width: 92px;
                height: 30px;
                background-size: 90px;
            }
            .text-show {
            font-family: var(--globalFontfamilyr);font-weight: bold;;
            font-size: 16px !important;

        }
        }
    </style>
    <script>
         var selectedLan='en'
            if (localStorage.getItem("selectedLanguage") != null) {
             selectedLan = localStorage.getItem("selectedLanguage");
            }else{
                selectedLan='en'
           
        }
        $(document).ready(function () {
            $("#success_message").hide();
            $("#success").hide();
            $("#failed").hide();
            $("#error_message").hide();
            var params = parseUrlParam();
            var code = params["code"];
            var state = params["state"];
            var session_state = params["session_state"];
            var url = getApiBaseUrl('apiForADSignIn') + "/msalHelper/handleResponseFromMSAL" 
                + '?code=' + code 
                + '&state=' + state 
                + '&session_state=' + session_state 
                + '&selectedLanguage=' +selectedLan;

            var dataConsentAlreadyAccepted = localStorage.getItem('dataConsentAlreadyAccepted');
            var dataConsentRequired = localStorage.getItem('dataConsentRequired');
            if (dataConsentAlreadyAccepted !== 'true' && dataConsentRequired === 'true') {
                url += '&dataConsentAccepted=' + true;
            }
            $.ajax({
                url: url ,
                type: 'GET',
                success: function (data) {
                    handleResponse(data);
                },
                error: function (data) {
                    handleResponse(data);
                }

            });

        });
        var article = {
    title: {
      en: "logging-in",
      es: "Iniciando sesión",
      fr:"se connecter",
      it:"entrando"
    },
    content: {
      en: "Login Successful",
      es: "Inicio de sesión exitosa",
      fr:"Connexion réussie",
      it:"Login Successful"
    },
    content2: {
      en: "You can now close this window and go back to Routespring.",
      es: "Ahora puede cerrar esta ventana y volver a Routespring.",
      fr:"Vous pouvez maintenant fermer cette fenêtre et revenir à Routespring.",
      it:"Ora puoi chiudere questa finestra e tornare a Routespring."
    }
}
function get_i18n(item, lang) {
    if(lang){
        document.write(article[item][lang])
    }
    
}
        function handleResponse(data) {
            if (data && data.success) {
                $("#success").show();
                $("#success_message").show();
                $("#loaderid").hide();
                $("#error_message").hide();
                $("#success_message1").show();
                $("#failed").hide();
               $("#success_message1").text( " " + data.userid);
                setTimeout(() => {
                    window.close();
                }, 1000);
                var dataConsentRequired = localStorage.getItem('dataConsentRequired');
                if (dataConsentRequired === 'true') {
                    localStorage.setItem('dataConsentAlreadyAccepted', 'true');
                }
            } else {
                $("#success_message").hide();
                $("#success").hide();
                $("#success_message1").hide();
                $("#loaderid").hide();
                if (data) {
                    $("#error_message").text(data.error_message);
                }
                $("#failed").show();
                $("#error_message").show();
                setTimeout(() => {
                    window.close();
                }, 1000);
            }
            return
        }
    </script>
</head>

<body>
    <div class="input-mst">
        <div style="padding-top:25px;">
            <span class="logo" style="text-align: center;">
                <a>
                    <span class="logo-icon"></span>
                </a>
            </span>
        </div>
        <div id="loaderid" class="">
            <span class="text-show"><script>get_i18n('title',selectedLan);</script></span>
            <div class="spinner">
                <div class="rect1"></div>
                <div class="rect2"></div>
                <div class="rect1"></div>
            </div>
        </div>
        <div id="success" style="display: none;">
            <span class="logo-border">
                <span style="line-height: 45px;
                            font-size: 30px;
                            color: green;">&#10004;</span>
            </span>
        </div>
        <div id="failed" style="display: none;">
            <span class="logo-border1">
                <span style="line-height: 45px;
                            font-size: 30px;
                            color: red;">&#10060;</span>
            </span>
        </div>
        <div id="error_message" class="text-show">OAuth Failed. Please try again later.</div>
        <div><span id="success_message" class="text-show"><script>get_i18n('content',selectedLan);</script></span><span id="success_message1" class="text-show"></span><br><span><script>get_i18n('content2',selectedLan);</script></span></div>
    </div>

</body>

</html>