{"Account": {"PaymentMethod": "Payment Method", "Passwordchangedsuccessfully": "Password changed successfully !!!", "Email": "Email", "ChangePassword": "Change Password", "OldPassword": "Old Password", "NewPassword": "New Password", "confirmPassword": "confirm Password", "Confirmpasswordisnotsame": "Confirm password is not same !!", "Change": "Change", "SlackIntegration": "Slack Integration", "Getnotificationsaboutapprovalrequestsrightintoyourslackapp.": "Get notifications about approval requests right into your slack app.", "Getalltheinformationataglanceincludingthepolicydetails.": "Get all the information at a glance including the policy details.", "AddtoSlack": "Add to Slack", "Disable": "Disable", "AddSecondaryemail": "Add Secondary email", "EnterSecondaryEmail": "Enter Secondary Email", "Pleaseenteravalidemail": "Please enter a valid email.", "Thisfieldisrequired": "This field is required.", "Save": "Save", "Cancel": "Cancel", "DuplicateEmail": "Duplicate Email"}, "profileUser": {"GallopCash": "GallopCash", "Signout": "Sign out", "Removeprofileimage": "Remove profile image", "Removeprofileimage?": "Remove profile image ?", "Remove": "Remove", "Removing": "Removing", "Cancel": "Cancel"}, "carmodal": {"Searchingforthecarsthatbestmatchyoupreference": "Searching for cars that best match your preferences", "Helpingyoutofindthebestoptions": "Helping you to find the best options", "SearchingForcars": "Searching For cars", "Pickupdate": "Pick up date", "Dropoffdate": "Drop off date"}, "reset": {"Forgotpassword": "Forgot password ?", "oldpasswordandnewpasswordcannotbesame": "Old password and new password cannot be same.", "pleaseenteravalidpassword": "please enter a valid password", "Passwordrequirements": "Password requirements", "UppercaselettersAZ": "Uppercase letters (A-Z)", "Lowercaselettersaz": "Lowercase letters (a-z)", "Numbers09": "Numbers (0-9)", "Specialcharacters": "Special characters (!@#$%^&*()_+-=[]{};':\"|,.<>/?)", "IagreetodataprocessingintheUnitedStates": "I agree to data processing in the United States", "Confirmnewpassword": "Confirm new password", "Enternewpassword": "Enter new password", "save&gotoprofile": "save & go to profile", "Pleaseaccepttheterms": "Please accept the terms", "PrivacyPolicy": "Privacy Policy", "Termsofuse": "Terms of use", "Iagreeto": "I agree to", "Pleasesetuppasswordtoyouraccountwithemail": "Please setup password to your account with email", "Thankyouforjoining": "Thank you for joining", "Enteranewpasswordforyouraccount": "Enter a new password for your account.", "thisfieldisrequired": "This field is required .", "passwordMismatch": "password Mismatch", "RESET": "RESET", "and": "and", "PasswordandConfirmPassworddonotmatch": "Password and Confirm Password do not match.", "Thankyou!Youremailhasbeensuccessfullyverified": "Thankyou ! Your email has been successfully verified.", "Pleasetryagain": "Please try again", "IfUseraccountexistsfor": "If an account exists for ", "ane-mailwillbesentwithfurtherinstructions.": ", an e-mail will be sent with further instructions.", "Passwordhasbeenchangedsuccessfully!": "Password has been changed successfully!", "Pleasecheckyourinternetandtryagain": "Please check your internet and try again", "ConfirmPassword": "Confirm Password", "EnterPassword": "Enter Password"}, "cards": {"MyCards": "My Cards", "Pleaseacceptthefollwinguseragreementstostartusingcards": "Please accept the following user agreements to start using cards", "StripeESignDisclosure": "Stripe E-Sign Disclosure", "CelticBankPrivacyPolicy": "Celtic Bank Privacy Policy", "AcceptAndContinueToMyCards": "Accept And Continue To My Cards", "CelticBankAuthorizedUserTerms": "Celtic Bank Authorized User Terms", "PENDINGAPPROVALS": "PENDING APPROVALS", "OFF": "OFF", "ON": "ON", "Memoaddedsuccessfully": "Memo added successfully!!!", "Pleasetryagainlater": "Please try again later !!!", "Receiptuploadedsuccessfully": "Receipt uploaded successfully !!!", "Receiptdeletedsuccessfully": "Receipt deleted successfully !!!", "Expensecategoryupdatedsuccessfully": "Expense category updated successfully !!!", "Expense categorynotupdated.Pleasetryagainlater": "Expense  category not updated . Please try again later !!!", "Youdonothaveanycardsissued": "You do not have any cards issued", "Nodata": "No data", "Adddetailsforthisexpense": "Add details for this expense", "viewmemo": "view memo", "memo": "memo", "Transactions": "Transactions", "TransactionDetails": "Transaction Details", "TransactionDate": "Transaction Date", "Name": "Name", "Department": "Department", "CardNumber": "Card Number", "Reasonfordecline": "Reason for decline", "Receipt": "Receipt", "Addreceipt": "Add receipt", "clicktoview": "click to view", "Memo": "Memo", "Addmemo": "Add memo", "Okay": "Okay", "Addamemo": "Add a memo", "Pleaseaddamemotothisexpense": "Please add a memo to this expense", "Wait": "Wait", "Showdeclinedtransactions": "Show declined transactions", "Date": "Date", "Merchant": "Merchant", "User": "User", "MerchantCategory": "Merchant Category", "ExpenseCategory": "Expense Category", "Amount": "Amount", "Status": "Status", "attachareceipt": "attach a receipt", "addamemo": "add a memo", "viewreceipt": "view receipt"}, "carview": {"Pick-update": "Pick-up date", "Drop-offdate": "Drop-off date"}, "login": {"Signin": "Sign in", "AuthorizingusingGoogleAccount": "Authorizing using Google Account", "AuthorizingusingMicrosoftAccount": "Authorizing using Microsoft Account", "Userlogin": "User login", "Login": "<PERSON><PERSON>", "SigninwithMicrosoft": "Sign in with Microsoft", "createanaccount": "create an account", "Email": "Email", "CorpEmail": "Work Email", "Password": "Password", "pleaseenteravalidemail": "Please enter a valid email.", "thisfieldisrequired": "This field is required.", "SigninwithGoogle": "Sign in with Google", "Forgotpassword": "Forgot password?", "pleaseentervalidfirstname": "Please enter valid first name .", "pleaseentervalidlastname": "Please enter valid last name .", "SignUp": "Sign Up", "Firstname": "First name", "Lastname": "Last name", "Iagreetothe": "I agree to the", "Termsofuse": "Terms of use", "Privacypolicy": "Privacy policy", "and": "and", "Subscribe": "Subscribe", "SignUpwithofffice365": "Sign Up with offfice365", "SignUpwithGoogle": "Sign Up with Google", "Alreadyhaveanaccount": "Already have an account", "Enteryouremailaddresstoresetyourpassword": "Enter your email address to reset your password", "Submit": "Submit", "Or": "Or", "Pleaseaccepttheterms": "Please accept the terms."}, "airport": {"Airportin": "Airport in", "Nomatchinglocationsfound": "No matching locations found"}, "bookingHistory": {"continue": "Continue", "Ticketstatus": "Ticket status: ", "ExistingBookingOverlappingDates": "Existing Booking Overlapping Dates", "Checkedin": "Checked in", "Autocancellation": "Auto cancellation: ", "Notsupported": "Not supported", "Youllreceiveanemailupdateoncecancellationiscomplete": "You’ll receive an email update once cancellation is complete.", "Autocancellationnotsupported": "Auto-cancellation not supported", "caneclApprovalMessage": "Approval will be cancelled and you will need to initiate booking again!", "supportdesk": "support desk", "hotelfor": "hotel for", "Thereisanexistingbookingforthetravelerslistedbelowthatoverlapswiththegiventraveldates": "There is an existing booking for the traveler(s) listed below that overlaps with the given travel dates.", "carfor": "car for", "flightfor": "flight for", "flightsfor": "flights for", "Rebooking": "Rebooking", "ExitRebooking": "Exit Rebooking", "REBOOKANDCANCEL": "REBOOK AND CANCEL", "RebookAndCancel": "Rebook And Cancel", "PROCEEDTOREBOOK": "PROCEED TO REBOOK", "CONFIRMREBOOKINGCANCELLATION": "CONFIRM REBOOKING & CANCELLATION", "Onceyourebookthebelowitinerarywillbecancelledanditcannotbeundone": "Once you rebook, the below itinerary will be cancelled and it cannot be undone", "Pleasenoteonceyourebookthisreservationwillbecancelledanditcannotbeundone.": "Please note, once you rebook, this reservation will be cancelled and it cannot be undone.", "NoBookings": "No Bookings", "BacktoBookings": "Back to bookings", "semergencycontact": "s emergency contact", "Travelercontactdetails": "Traveler contact details", "Back": "Back", "Backto": "Back to", "sUpcomingTrip": "s Upcoming Trip", "AirlineFare": "Airline Fare", "HotelFare": "Hotel Fare", "FlightFare": "Flight Fare", "SelectTag": "Select Custom Field", "RequestToSelectTag": "Please select a Custom Fields", "SelectProjectTagoptional": "Select Custom Fields", "Vendorcontactdetails": "<PERSON>endor contact details", "Baseprice": "Base price", "approvalrequest": "approval request", "LoadingTrips": "Loading Trips", "AllowedToProceed": "AllowedToProceed", "Expensesentsuccessfully": "Expense sent successfully !!", "Yourapprovalrequesthasbeensuccessfullycancelled.": "Your approval request has been successfully cancelled.", "Yourcancellationisunderprocess.Youwillshortlyreceiveanemailswithdetailsoffunds.": "Your cancellation is under process. You will shortly receive an email with details of funds.", "car": "car", "day": "day", "days": "days", "Hotel": "Hotel", "Yourreservationisstilleligibleforfullrefund.": "Your reservation is still eligible for full refund.", "YourreservationmayNOTbeeligibleforrefund.Youwillreceiveanemailwithdetailsoftravelfunds.": "Your reservation may NOT be eligible for refund. You will receive an email with details of travel funds.", "NoResultsFound": "No Results Found !!", "Expensingtool": "Expensing tool", "EnteryourSAPConcurverifiedemail": "Enter your SAP Concur verified email", "Enteryouruniqueforwardingemail": "Enter your unique forwarding email", "EnteryourExpensifyverifiedemail": "Enter your Expensify verified email", "OPENMAP": "OPEN MAP", "Days": "Days", "Day": "Day", "Cancelled": "Cancelled", "Refunded": "Refunded", "Voided": "Voided", "CNCLunderreview": "CNCL under review", "Pick-Up": "Pick-Up", "Booked": "Booked", "Underreview": "Under Review", "PleaseTryAgainOnErrorRetry": "Please try again", "pendingApproval": "Pending Approval", "Roomcost(pernight)": "Room cost (per night)", "YouhaveearnedUSD": "You have earned", "Phonenumberisnotavailable": "Phone number is not available", "BookedForMyself": "Booked For Myself", "BookedForOthers": "Booked For Others", "Pick-update": "Pick up ", "GallopCashApplied": "Gallop Cash Applied", "YouwillearnUSD": "You will earn ", "onthisitinerary": "on this itinerary", "Expensifyreceiptoncereceipthasbeensentsuccessfully": "Expensify receipt has been sent successfully !!", "YourcancellationRequesthasbeensentsuccessfully.": "Your cancellation request has been sent successfully.", "Yourchangerequesthasbeensentsuccessfully": "Your change request has been sent successfully.", "Adddetailsforyourchange": "Add details for your change", "youmusthavetowritethechangemessage": "Please enter details of your change request.", "Retrievingresultsofyourbookinghistory": "Retrieving results of your booking history !", "UpcomingTrips": "Upcoming Trips", "PastTrips": "Past and canceled trips", "ViewDetail": "View Detail", "hotel": "HOTEL", "flight": "FLIGHT", "Pick-up": "Pick-up", "Drop-off": "Drop-off", "Pick-uptime": "Pick-up time", "Drop-Offtime": "Drop-Off time", "Sameaspick-up": "Same as pick-up", "BasePrice": "Base Price", "CheckIn": "Check In", "Night": "Night", "Room": "Room", "TaxesandFees": "Taxes and Fees", "Paymentdetails": "Payment details", "Guests": "Guests", "CarFare": "Car Fare", "Resortfees": "Resort fees", "Rooms": "Rooms", "Nights": "Nights", "Travelers": "Travelers", "Pleasenoteonceflightiscancelleditcannotbeundone.": "Please note, once flight is cancelled, it cannot be undone.", "Confirm": "Confirm", "Cancellation": "Cancellation", "Pleasenoteonce": "Please note, once", "iscancelleditcannotbeundone.": "is cancelled, it cannot be undone.", "Cancelyour": "Cancel your", "SendReceipttoExpense": "Send Receipt to Expense", "Expense": "Expense", "Selectyourexpensingtool": "Select your expensing tool", "Youdonthaveanyupcomingtrips": "You don't have any upcoming trips.", "Youdonthaveanypasttrips": "You don't have any past trips.", "BOOKNOW": "BOOK NOW", "BacktoTrips": "Back to Trips", "Confirmation": "Confirmation", "TotalDuration": "Total Duration:", "Seat": "Seat:", "operatedby": "operated by", "Layoverin": "Layover in", "FLIGHTFARE": "FLIGHT FARE", "HOTELFARE": "Hotel fare", "TotalPrice": "Total", "CheckOut": "Check Out", "AddtoCalender": "Add to Calendar", "AddtoTripIt": "Add to TripIt", "AddtoExpensify": "Add to Expensify", "DOWNLOADINVOICE": "DOWNLOAD INVOICE", "REQUESTCHANGES": "MODIFY", "CANCELRESERVATION": "CANCEL", "ExpensifyAddDetails": "Expensify Add Details", "ExpensifyLoginEmail": "Expensify Login Email", "Thisfieldisrequired": "This field is required.", "Pleaseenteravalidemail": "Please enter a valid email.", "Pleaseselectanyofcheckboxdefineabove": "Please select any of checkbox define above.", "SendReceipttoExpensify": "Send Receipt to Expense", "RequestChanges": "Request Changes", "PleasetelluswhatchangeyouwouldlikeforyourTrip": "Please tell us what change you would like for your itinerary.", "from": "from", "to": "to", "SubmitChangeRequest": "Modify dates", "Sendingarequestwillnotimpactormodifyyourreservationwithoutfurtheraction.YouwillreceiveanemailfromTripwiththebestoptionsandapplicablechangefeeorfaredifferencesforyoutorespondtobeforeachangeisexecuted": "Sending a request will not impact or modify your reservation without further action.You will receive an email from Trip with the best options and applicable change fee or fare differences for you to confirm before a change is executed.", "Note": "Note:", "RequestCancellation": "Request Cancellation", "Tripcanhelpyoucancelyourreservationforyourbooking": "Trip can help you cancel your reservation for your booking.", "SubmitCancellationRequest": "Submit Cancellation Request", "Thisisonlyarequestforcancellationandwontcancelyourflight/hotelrightaway.YouwillreceiveanemailfromTripwiththedetailsofthecancellationpenaltyandapplicablerefundsbeforethisiscancelled": "This is only a request for cancellation and wont cancel your flight/hotel right away.You will receive an email from <PERSON> with the details of the cancellation penalty and applicable refunds before this is cancelled.", "HOTEL": "HOTEL", "FLIGHT": "FLIGHT", "youmusthavetoselectoneoftheabove": "you must have to select one of the above."}, "addCard": {"Add": "Add", "AddNewCard": "Add New Card", "GOBack": "GO Back", "Name": "Name", "Next": "Next", "Thisfieldisrequired": "This field is required.", "wait": "wait..."}, "addCardWidghet": {"RequestcouldnotbecompletedPleasetryagain": "Request could not be completed. Please try again."}, "bookingMsg": {"ACCEPT": "ACCEPT"}, "bookingService": {"ACCEPT": "ACCEPT", "Selectdifferentoptions": "Select different options", "Searchagain": "Search again", "Flightnotavailable": "Flight not available", "Aww…Snap": "Aww…Snap", "Youwillreceiveanemailshortlywithitineraryandinvoice": "You will receive an email shortly with itinerary and invoice.", "BookingSuccessful": "Booking Successful!", "Bookingcompletebutnotconfirmed": "Booking complete but not confirmed!", "FINDALTERNATIVE": "FIND ALTERNATIVE", "FINDALTERNATIVE_ROOM": "Select different room", "SEARCHAGAIN": "SEARCH AGAIN", "TRYAGAIN": "TRY AGAIN", "VIEWMYITINERARY": "VIEW MY ITINERARY", "ViewItinerary": "View Itinerary", "BookTravel": "Book Travel", "Youwillreceiveanemailshortlywithyouritineraryandinvoice": "You will receive an email shortly with your itinerary and invoice", "apiErrorDescription": "Unfortunately this booking has failed due to a technical issue. We have received your request and will get back to you as soon as We are able to confirm your request. \n\nPlease note that your credit card will not be charged until the itinerary is fully booked. In the rare occasion of a change in availability or fare, We will inform you immediately before proceeding.\n\nIf you have any questions, please feel free to text us at <strong> {{GALLOP_HELP_CONTACT_NO}} </strong> or email us at {{GALLOP_HELP_EMAIL}} and We will be happy to assist you.", "bookingSuccessDescription": "You will receive an email shortly with your itinerary and invoice.", "flightFareChangedDescriptionPart1": "The new fare for the selected dates will be ", "flightFareChangedDescriptionPart2": ". \n Please click \" Accept \" to proceed with the new fare or click \"Find alternative\" to search best alternative options.", "flightUnavailableDescription": "is no longer available. Please click on \" Search again \" to find the best alternative option.", "flightyouselected": "Op<PERSON>! the flight you selected ", "fromspace": "from ", "hotelUnavailableDescription": "Oops!! The hotel you selected is no longer available. Please click on \" Search again \" to find the best alternative option.", "hotelRoomNotAvailable": "Oops! The requested room is no longer available.", "hotelRoomsNotAvailable": "Oops! The requested rooms is no longer available.", "Flighstnotavailable": "Flights not available", "Carnotavailable": "Car not available", "Carsnotavailable": "Cars not available", "Selectedoptionsnotavailable": "Selected options not available", "internalErrorDescription": "Unfortunately this booking has failed due to a technical issue. We have received your request and will get back to you as soon as We are able to confirm your request. \n\nPlease note that your credit card will not be charged until the itinerary is fully booked. In the rare occasion of a change in availability or fare, We will inform you immediately before proceeding.\n\nIf you have any questions, please feel free to text us at <strong> {{GALLOP_HELP_CONTACT_NO}} </strong> or email us at {{GALLOP_HELP_EMAIL}} and We will be happy to assist you.", "spacetospace": " to ", "ticketingErrorDescription": "We will email you the itinerary and receipt once the booking is confirmed (usually within an hour). In a rare case of failure, we will immediately refund you the amount charged on your credit card. \n\n If you have any questions, please feel free to text us at {{GALLOP_HELP_CONTACT_NO}} or email us at {{GALLOP_HELP_EMAIL}}", "Pleaseselectcheckincheckoutdate": "Please select check-in and check-out date", "Thetotalfarefortheselecteditineraryhaschangedto": "The total fare for the selected itinerary has changed to", "Pleaseclickaccepttoproceedwithnewfare": "Please click accept to proceed with  new fare", "Roomnotavailbleonmodifieddates": "Room not available on modified dates", "Pleaseselectdifferentdatefromcurrentcheckinandcheckoutdate": "Please select different date from current  Check-in and Check-out Date"}, "emailBooking": {"Addreceipttomyexpensify": "Add receipt to my expensify", "Expensify": "Expensify", "PassengerDetails": "Passenger Details", "DriverDetails": "Driver Details", "Skip&": "Skip &", "Selectseat": "Select seat", "ExpensifyLoginEmail": "Expensify Login Email", "Next": "Next", "PersonalDetails": "Personal Details", "Pleaseenteravalidemail": "Please enter a valid email.", "Proceed": "Proceed", "Thisfieldisrequired": "This field is required", "topay": "to pay"}, "feedback": {"Hotelreview": "Hotel review", "Howwasyourexperience": "How was your experience ?", "Hotel": "Hotel", "Check-IN": "Check-IN", "Check-OUT": "Check-OUT", "Yourreviewissubmitted.": "Your review is submitted.", "Thankyou": "Thank you !", "Whatwentwrong": "What went wrong?", "Location": "Location", "Bookingapp": "Booking app", "Hotelcheck-in": "Hotel check-in", "Cleanliness": "Cleanliness", "Service": "Service", "Submit": "Submit", "Save": "Save"}, "seat": {"OPEN": "OPEN", "TAKEN": "TAKEN", "SELECTED": "SELECTED", "PREMIUM": "PREMIUM", "EXIT": "EXIT", "WINGS": "WINGS", "back": "back", "Next": "Next", "Selectseatsfor": "Select seats for", "skip": "skip", "PROCEEDTOPAY": "PROCEED TO PAY", "Nofreeseatsavailable": "No free seats available!!"}, "emailBookingFlow": {"AirFare": "Air Fare", "Pleasefillallmandatoryfields": "Please fill all mandatory fields", "YES": "YES", "NO": "NO", "Youseemtohaveaddedorupdatedsomeinformation.Wouldyouliketosaveittoyourprofile?": "You seem to have added or updated some information. Would you like to save it to your profile?", "Youseemtohaveaddedorupdatedsomeinformation.Wouldyouliketosaveitto": "You seem to have added or updated some information. Would you like to save it to", "profile?": "profile?", "HotelFare": "Hotel Fare", "ServiceFee": "Service fee", "Pleasewaitwhilefarevalidationinprogress": "Please wait while fare validation in progress!"}, "errorComponent": {"403Message": "This link has expired. Please contact {{GALLOP_HELP_EMAIL}} ", "403Title": "looks like you are using old link", "404Message": "The page you are looking for is not available!", "404Title": "looks like you are lost", "alreadyConfirmedMessage": "You may have already confirmed the booking of this itinerary,or you may have clicked on an old link.Please email {{GALLOP_HELP_EMAIL}}  for help.", "alreadyConfirmedTitle": "THIS LINK HAS EXPIRED!", "unknownErrorMessage": "Please email {{GALLOP_HELP_EMAIL}} for help.", "unknownErrorTitle": "SORRY! SOMETHING WENT WRONG"}, "filter": {"AIRLINES": "AIRLINES", "AIRPORTS": "AIRPORTS", "Airlines": "Airlines", "Alliance": "Alliance", "Apply": "Apply", "DEMO": "DEMO", "Departure": "Departure", "Destination": "Destination", "FilterbyAirlines": "Filter by Airlines", "FilterbyAirports": "Filter by Airports", "FilterbyNumberofStops": "Filter by Number of Stops", "FilterbyPolicy": "Filter by Policy", "FilterbyTime": "Filter by Time", "POLICY": "POLICY", "STOPS": "STOPS", "PRICERANGE": "PRICE RANGE", "FilterbyPriceRange": "Filter by price range", "ShowAllAirlines": "Show All Airlines", "TIME": "TIME"}, "filterService": {"Flightarrivaltime": "Arrival time", "Flightdeparturetime": "Departure time", "flightDuration": "Duration", "flightPrice": "Price"}, "flightBox": {"Class": "Class", "Continue": "Continue", "Passenger": "Passenger", "Passengers": "Passengers", "layoverin": "layover in", "with": "with"}, "flightChart": {"Airline": "Airline", "Feeforchanges": "Fee for changes", "Feeforcarryon": "Fee for carry-on", "Feeforrefund": "Fee for refund", "whywasflightcreditnotappliedtothisflight": "why was flight credit not applied to this flight ?", "CreditApplied": "Credit Applied", "WiFi": "WiFi", "InPolicy": "In Policy", "Seatchoiceincluded": "Seat selection (included)", "Seatchoice": "Seat selection", "NoSeatchoice": "No Seat selection", "NOWiFi": "No WiFi", "freeMeal": "Free Meal", "FreeWiFi": "Free WiFi", "NoCarryonbag": "No Carry-on", "NolegRoom": "No Extra leg room", "NoChanges": "No Changes", "NOMeal": "No Meal", "Meal": "Free Meal", "NotRefundable": "Not Refundable", "atcost": " (at cost)", "Changesincluded": "Changes (included)", "Carrybagincluded": "Carry on bag (included)", "Legroomincluded": "Extra-Legroom (included)", "notincluded": " (not included)", "NotinPolicy": "Not in Policy", "NooptionsAvailable": "No options available", "Details": "Details", "FareDetails": "Fare Details", "1checkedbagperadult": "1 checked bag (per seat)", "checkedbagsperadult": "checked bags (per seat)", "Nocheckedbags": "No checked bags", "peradult": "(per seat)", "creditappliedonthetotalfareof": "<PERSON><PERSON> without credits: ", "BasicEconomy": "Basic Economy", "Mixedclass": "Mixed class", "Creditapplied": "Credit applied", "GrossFare": "Gross fare", "Airport": "Airport", "Alliance": "Alliance", "Carryonbag": "Carry-on", "legRoom": "Extra leg room", "Changeofairport": "Change of airport", "Changes": "Changes", "Checkedbag": "Checked-bag", "Departure": "Departure", "Layoverin": "Layover in", "Longlayover": "<PERSON><PERSON> over", "Overnightlayover": "Overnight layover", "Policy": "Policy", "Redeye": "Red-eye", "Refundable": "Refundable", "SELECTFLIGHT": "SELECT FLIGHT", "Select": "Select", "Shortlayover": "Short layover", "TotalDuration": "Total Time", "nonstop": "Non-stop"}, "flightSelected": {"1stop": "1 stop", "Airline": "Airline", "one-way": "one-way", "Multicity": "Multicity", "Airport": "Airport", "Alliance": "Alliance", "Cancellations": "Cancellations", "Carryonbag": "Carry-on bag", "Changeofairport": "Change of airport", "Changes": "Changes", "change": "change", "Checkedbag": "Checked bag", "Departure": "Departure", "Layoverin": "Layover in", "Longlayover": "Long layover", "Overnightlayover": "Overnight layover", "PersonalItem": "Personal Item", "Policy": "Policy", "Redeye": "Red-eye", "Roundtrip": "Roundtrip", "Seatchoice": "Seat choice", "Shortlayover": "Short layover", "TotalDuration": "Total Duration:", "non-stop": "non-stop", "nonstop": "non-stop", "spacestops": " stops"}, "header": {"MyCards": "My Cards", "CompanyDashboard": "Company Dashboard", "BookTravel": "Book Travel", "Logout": "Logout", "Dashboard": "DashBoard", "ManageTrips": "My Trips", "Profile": "Profile", "SigningIn": "Signing In", "SignIn": "SignIn", "Call": "Call", "forprivatefares": "for private fares"}, "hotelModal": {"SearchingForHotels": "Searching For Hotels", "HotelAmenitieshere": "Hotel Amenities here", "Searchingforhotelsthatbestmatchyourpreferences": "Searching for hotels that best match your preferences !"}, "hotelRequestView": {"AnyHotelChain": "Any Hotel Chain", "Checkin": "Check-in", "Checkout": "Check-out", "Room": "Room", "Rooms": "Rooms"}, "hotelResult": {"AMENITIES": "AMENITIES", "Companyblacklisted": "Company blacklisted", "Hotelremovedfromcompanyblacklist": "Hotel removed from company blacklist.", "Hotelblockedfromcompany": "Hotel updated as company blacklisted.", "MarkAsCompanyRecommended": "Mark As Company Recommended", "MarkAsCompanyBlacklisted": "Mark As Company Blacklisted", "Removefromcompanyblacklisted": "Remove from company blacklisted", "Recommendedhotels": "Recommended / Blacklisted", "Corporaterate": "Corporate rate", "MustInclude": "Must Include", "Hotelupdatedascompanyrecommended": "Hotel updated as company recommended", "Hotelremovedfromcompanyrecommendation": "Hotel removed from company recommendation", "Hotelsnotfound": "Hotels not found", "List": "List", "Map": "Map", "Companyrecommended": "Company recommended", "ClearAllFilter": "Clear All Filter", "Searchthisarea": "Search this area", "Removefromcompanyrecommendation": "Remove from company recommendation", "TOPRECOMMENDATIONFORYOU": "TOP RECOMMENDATION FOR YOU", "Addascompanyrecommended": "Add as company recommended", "Markthishotelascompanyrecommendedtopriortizeamongstallotherhotelsintheareaofyourallemployees": "Mark this hotel as company recommended to priortize amongst all other hotels in the area of your all employees", "Any": "Any", "Apply": "Apply", "BRAND": "BRAND", "BestOption": "Best Option ", "FilterbyAmenities": "Filter by Amenities", "FilterbyHotelBrand": "Filter by Hotel Brand", "FilterbyStarRating": "Filter by Star Rating", "LIST": "LIST", "MAP": "MAP", "Nohotelsfound": "No hotels found", "Other": "Other", "Pleasecheckyourinternet": "Please check your internet", "Policy": "Policy", "Preferred": "Preferred", "Preferredstarshotel": "Preferred stars hotel", "Preferrredhotelbrand": "Preferrred hotel brand", "RATING": "RATING", "SEEMOREOPTIONS": "SEE MORE OPTIONS", "SearchbyName": "Search by Name", "SelectHotel": "Select Hotel", "SelectRoom": "Select Room", "Sortby": "Sort by", "miles": "miles", "min": "min"}, "hotelSelect": {"AboutHotel": "About Hotel", "Hiderateswithnoloyalitybenefits": "Hide rates with no loyalty benefits", "ShowAAAratesonly": "Show AAA rates only", "Noroomswithaaarateswerefound": "No rooms with AAA rates were found", "OPENMAP": "OPEN MAP", "Noroomsavailable.": "No rooms available.", "ShowGovernmentratesonly": "Show Government rates only", "Noroomswithgovernment/militaryrateswerefound": "No rooms with government/ military rates were found !!!", "resortfee": "resort fee", "CancellationPolicy": "Cancellation Policy", "Payathotel": "Pay at hotel", "Amenities": "Amenities", "Bar": "Bar", "Breakfast": "Breakfast", "Concierge": "Concierge", "FreeWiFi": "Free WiFi", "Gym": "Gym", "NoRoomsAvailable": "No Rooms Available !!!", "Policy": "Policy", "Night": "Night", "Nights": "Nights", "Pool": "Pool", "Restaurant": "Restaurant", "RetrivingRoomsList": "Retrieving Rooms List", "Select": "Select", "SelectARoom": "Select A Room", "Showless": "Show less", "Showmore": "Show more", "StarHotel": "Star Hotel", "avgnight": "avg/night", "incltaxesandfees": "incl taxes and fees:", "milesfromyourdestination": "miles from your destination", "min": "min"}, "itinerary": {"Adult": "Adult", "Adults": "Adults", "PaidinFull": "Paid in Full", "PayatHotel": "Pay at Hotel", "BOOKANOTHERFLIGHT": "BOOK ANOTHER FLIGHT", "BOOKANOTHERHOTEL": "BOOK ANOTHER HOTEL", "BilledToCompany": "Billed To Company", "Cancellationpolicy": "Cancellation policy :", "Checkin": "Check in", "Checkout": "Check out", "Class": "Class", "Confirmation": "Confirmation", "FLIGHTS": "FLIGHTS", "FlightBookingDetails": "Flight Booking Details", "HOTELS": "HOTELS", "HotelBookingDetails": "Hotel Booking Details", "Night": "Night", "Nights": "Nights", "layoverin": "layover In", "Operatedby": "Operated by", "Room": "Room", "Terminal": "Terminal", "TotalTime": "Total Time", "TravelTime": "Travel Time", "Traveller": "Traveler", "Travellers": "Travelers", "to": "to"}, "ngOption": {"Last12months": "Last 12 months", "Yesterday": "Yesterday", "Basefaresprice": "Base fares price", "Standardfaresprice": "Standard fares price", "Enhancedfaresprice": "Enhanced fares price", "Premiumfaresprice": "Premium fares price", "SelectTarinLine": "Select Train Line", "Today": "Today", "Last14Days": "Last 14 days", "Next14Days": "Next 14 days", "Next7Days": "Next 7 days", "Shuttle": "Airport shuttle service", "AnyTimeInFuture": "Anytime In Future", "AnyTimeInPast": "Anytime In Past", "Next30Days": "Next 30 days", "Currentmonth": "Current month (MTD)", "Currentquarter": "Current quarter (QTD)", "Currentyear": "Current year (YTD)", "Last7days": "Last 7 days", "Last30days": "Last 30 days", "Last90days": "Last 90 days", "Lastmonth": "Last month", "Multicity": "Multicity", "MustInclude": "Must Include", "Adults": "Adults", "SelectHotelChain": "Select Hotel Chain", "SelectCarBrand": "Select Car Brand", "SelectAirline": "Select Airline", "3AM": "3AM", "4AM": "4AM", "5AM": "5AM", "6AM": "6AM", "7AM": "7AM", "8AM": "8AM", "9AM": "9AM", "10AM": "10AM", "11AM": "11AM", "12PM": "12PM", "1PM": "1PM", "2PM": "2PM", "3PM": "3PM", "4PM": "4PM", "5PM": "5PM", "6PM": "6PM", "7PM": "7PM", "8PM": "8PM", "9PM": "9PM", "10PM": "10PM", "11PM": "11PM", "00:00AM": "00:00 AM", "00:30AM": "00:30 AM", "01:00AM": "01:00 AM", "01:30AM": "01:30 AM", "02:00AM": "02:00 AM", "02:30AM": "02:30 AM", "03:00AM": "03:00 AM", "03:30AM": "02:30 AM", "04:00AM": "04:00 AM", "04:30AM": "04:30 AM", "05:00AM": "05:00 AM", "05:30AM": "05:30 AM", "06:00AM": "06:00 AM", "06:30AM": "06:30 AM", "07:00AM": "07:00 AM", "07:30AM": "07:30 AM", "08:00AM": "08:00 AM", "08:30AM": "08:30 AM", "09:00AM": "09:00 AM", "09:30AM": "09:30 AM", "10:00AM": "10:00 AM", "10:30AM": "10:30 AM", "11:00AM": "11:00 AM", "11:30AM": "11:30 AM", "12:00PM": "12:00 PM", "12:30PM": "12:30 PM", "01:00PM": "01:00 PM", "01:30PM": "01:30 PM", "02:00PM": "02:00 PM", "02:30PM": "02:30 PM", "03:00PM": "03:00 PM", "03:30PM": "03:30 PM", "04:00PM": "04:00 PM", "04:30PM": "04:30 PM", "05:00PM": "05:00 PM", "05:30PM": "05:30 PM", "06:00PM": "06:00 PM", "06:30PM": "06:30 PM", "07:00PM": "07:00 PM", "07:30PM": "07:30 PM", "08:00PM": "08:00 PM", "08:30PM": "08:30 PM", "09:00PM": "09:00 PM", "09:30PM": "09:30 PM", "10:00PM": "10:00 PM", "10:30PM": "10:30 PM", "11:00PM": "11:00 PM", "11:30PM": "11:30 PM", "Children": "Children", "Infants": "Infants", "Aged2to12": "Aged 2 to 12", "Above12": "Above 12", "Aged0to2": "Aged 0 to 2", "MarriottHotels": "Marriott Hotels", "HiltonHotels": "Hilton Hotels", "InterContinentalHotelGroup": "InterContinental Hotel Group", "WyndhamHotelGroup": "Wyndham Hotel Group", "ChoiceHotels": "Choice Hotels", "BestWestern": "Best Western", "HyattGroup": "Hyatt Group", "CarlsonHospitalityGroup": "Carlson Hospitality Group", "BoutiqueGroup": "Boutique Group", "1Passenger": "1 Passenger", "1Room": "1 Room", "2Passengers": "2 Passengers", "2Rooms": "2 Rooms", "3Passengers": "3 Passengers", "3Rooms": "3 Rooms", "4Passengers": "4 Passengers", "AllCancellationPolicies": "All Cancellation Policies", "Any": "Any", "Anynumberofstops": "Any number of stops", "Anytime": "Anytime", "ArrivalTime": "Arrival Time", "Arrive": "Arrive", "Bar": "Bar", "Breakfast": "Breakfast", "Business": "Business", "Concierge": "Concierge", "Depart": "<PERSON><PERSON><PERSON>", "DepartureTime": "Departure Time", "Distance": "Distance", "Duration": "Duration", "Economy": "Economy", "Evening": "Evening", "FirstClass": "First Class", "FreeWiFi": "Free WiFi", "Gym": "Gym", "MidDay": "Mid Day", "Morning": "Morning", "Night": "Night", "Non-stoponly": "Non-stop only", "One-way": "One-way", "Oneorfewerstops": "One or fewer stops", "Pool": "Pool", "PremiumEconomy": "Premium Economy", "Price": "Price", "Recommended": "Recommended", "Restaurant": "Restaurant", "Roundtrip": "Roundtrip", "ShowAllOptions": "Show All Options", "WithinPolicyOnly": "Within Policy Only", "Mr": "Mr", "Mrs": "Mrs", "Ms": "Ms", "Miss": "Miss", "Master": "Master", "Lord": "Lord", "Infant": "Infant", "Lady": "Lady", "Doctor": "Doctor", "HotelBrand": "Any Hotel Brand", "Acela": "<PERSON><PERSON>", "NorthEastRegional/Keystone": "North East Regional / Keystone", "BrightlineFlorida": "Brightline Florida", "PacificSurfliner": "Pacific Surfliner", "EmpireService": "Empire Service", "CapitolCorridor": "Capitol Corridor", "Amtrak": "Amtrak", "Eurostar": "Eurostar"}, "option": {"1Passenger": "1 Passenger", "changeseat": "Change Seat", "ToBeCANCELLED": "To Be CANCELLED", "PleaseConfirmRebooking": "Please Confirm Rebooking", "Confirmrequestedchanges": "Confirm requested changes", "OldItinerary": "Old Itinerary", "Reviewyouroldandnewitinerary": "Review your old and new itinerary", "IconfirmthatthenewitineraryaboveistheoneIdliketobookandunderstandthatmyolditinerarywillbecancelledfirstandthenmynewitinerarybooked": "I confirm that the new itinerary above is the one I’d like to book and understand that my old itinerary will be cancelled first and then my new itinerary booked.", "Thetotalfarefortheselecteditineraryhaschangedto": "The total fare for the selected itinerary has changed to", "SelectDifferentOption": "Select Different Option", "OK": "OK", "YouwilllosethistripifyoumoveawayAreyousureyourwanttoexitthistripbooking?": "You will lose this trip if you move away. Are you sure you want to exit this trip booking?", "Pleasenotethatthepricesofsomeoftheoptionshavechanged.": "Please note that the prices of some of the options have changed.", "PleaseclickAccepttoproceedwiththenewfareorclickSelectDifferentOption.": "Please click 'Accept' to proceed with the new fare or click 'Select Different Option'.", "SelectedItinerary": "Selected Itinerary", "Selectseat": "Select seat", "Seat": "<PERSON><PERSON>", "Resortfee": "Resort fee", "CancellationPolicyInformation": "Cancellation Policy Information", "Cancellationpolicy": "Cancellation policy", "Sameaspick-up": "Same as pick-up", "DROP-OFFDETAILS": "DROP-OFF DETAILS", "PICK-UPDETAILS": "PICK-UP DETAILS", "Actualcarmayvaryfromtheoneinshowninimage": "Actual car may vary from the one in shown in image", "Noloyaltyrewards": "No loyalty rewards", "Creditapplied.Grossfare": "Credit applied. Gross fare", "Pleaseconfirmifyouwishtoproceedwithaboveoutofpolicyoption.": "Please confirm if you wish to proceed with above out of policy option.", "Idontwanttoconsidertheseoptions.Iwillproceedwithaboveoutofpolicyoption.": "I don't want to consider these options. I will proceed with above out of policy option.", "BeforeProceeding,pleaseconsiderbelowwithinpolicyoptionsforyourtrip": "Before Proceeding, please consider below within policy options for your trip", "Distancefromdestination": "Distance from destination:", "Flight": "Flight", "GallopCashonthisbooking": "GallopCash on this booking", "Next": "Next", "NightStay": "Night Stay", "OUTSIDEPOLICY": "OUTSIDE POLICY", "Operatedby": "Operated by", "Passengers": "Passengers", "Prepareyouritinerary": "Review your itinerary", "SELECT": "SELECT", "SELECTED": "SELECTED", "SelectFlight": "Select Flight", "Selecthotel": "Select hotel", "Total": "Total", "Skip": "<PERSON><PERSON>", "change": "change", "Traveltime": "Travel time", "Updated": "Updated", "ViewAll": "View All", "ViewLess": "View Less", "WITHINPOLICY": "WITHIN POLICY", "Youwillearn": "You will earn", "hotel": "Hotel", "layoverin": "layover in", "min": "min", "to": "to", "in": "in", "warning": "warning"}, "footer": {"ContactUs": "Contact Us", "BranchLocations": "Branch Locations", "PrivacyPolicy": "Privacy Policy", "TermsofUse": "Terms of Use", "AboutUs": "About Us", "AmnetNewYorkInc2019": "©Amnet New York, Inc. 2019"}, "optionSelection": {"prepay": "PRE-PAY", "payathotel": "PAY AT HOTEL", "CancellationPolicy": "Cancellation Policy", "selectOptionOrSkip": "Please \" Select \" an option to book from each set OR \" Skip \" if you wish to skip any set of options.", "spacefeeafterspace": " fee after "}, "passenger": {"Address": "Address", "Agecannotbelessthan12years": "Age should be more than 11 years.", "AreaZipCode": "Area Zip Code", "DateofBirth": "Date of Birth", "Email": "Email", "EnterPassportNumber": "Enter Passport Number", "ExpiryDate": "Expiry Date", "Female": "Female", "FlyerNumber": "Flyer Number", "FrequentFlyerNumber": "Frequent Flyer Number", "Gender": "Gender", "IssuanceCountry": "Issuance Country", "KTNNumber": "KTN Number", "KnownTravelerNumber": "Known Traveler Number(USA)", "LastName": "Last Name", "MMDDYY": "MM/DD/YY", "Male": "Male", "MiddlenameifshownonID": "Middle name(if shown on ID)", "Name": "Name", "Nationality": "Nationality", "NotAvailable": "Not Available", "Other": "Other", "PassportNumber": "Passport Number", "PhoneNumber": "Phone Number", "PleaseenteravalidFrequentFlyerNumber": "Please enter a valid Frequent Flyer Number.", "PleaseenteravalidKnownTravelerNumber": "Please enter a valid Known Traveler Number.", "Pleaseenteravalidemail": "Please enter a valid email .", "PleaseenteravalidfirstnameNumericorspecialcharactersarenotallowed": "Please enter a valid first name .Numeric or special characters are not allowed", "PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed": "Please enter a valid last name .Numeric or special characters are not allowed", "PleaseenteravalidmiddlenameNumericorspecialcharactersarenotallowed": "Please enter a valid middle name .Numeric or special characters are not allowed", "Pleaseenteravalidpassportnumber": "Please enter a valid passport number.", "Pleaseenteravalidphonenumber": "Please enter a valid phone number.", "Pleaseenteravalidzipcode": "Please enter a valid zip code .", "SelectIssuanceCountry": "Select Issuance Country", "SelectNationality": "Select Nationality", "Thisfieldcannotbelessthan9characters": "This field cannot be less than 9 characters.", "Thisfieldisrequired": "This field is required.", "Title": "Title", "Writeyourhomeaddresshere": "Write your home address here...", "ZipCode": "Zip Code", "firstName": "First Name"}, "payment": {"Cardisrequired": "Card is required", "Nameisrequired": "Name is required", "Nameoncard": "Name on card", "PAY": "PAY", "Payment": "Payment", "Pleaseenteravalidname": "Please enter a valid name"}, "paymentDetails": {"Travelpurpose": "Travel purpose", "ThetravelersprofilehasbeensavedinGuestTravelersdepartment": "The traveler's profile has been saved as a 'Guest Profile'", "Explainthepurposeofthistripinfewwords": "Explain the purpose of this trip in few words.", "Estimatedtotaldueatcounter": "Estimated total (due at counter)", "ThisincludesthetotalestimatedcostofyourcarrentalreservationActualcostmayvarydependingonspecialrequestanditempurchasedatthecounter": "This includes the total estimated cost of your car rental reservation. Actual cost may vary depending on special request and item purchased at the counter.", "Seedetails": "See details", "NotetoAdmin": "Note to Admin", "Typeanotefortheadminifyouneedtoexplainthereasonforthisapprovalrequestoptional": "Type a note for the admin if you need to explain the reason for this approval request (optional)", "Addanotetolettheapproverknowwhythisoutofpolicyoptionneedstobebooked": "Add a note to let the approver know why this out of policy option needs to be booked.", "AddNewcard": "Add New card", "ThiscardisnotacceptedbytherentalcompanyPleaseuse": "This card is not accepted by the rental company. Please use ", "DueNow": "Due Now", "SelectTag": "Select Custom Field", "RequestToSelectTag": "Please Select", "atleastoneof": "atleast one of ", "SelectProjectTagoptional": "Select Custom Fields", "Onceitisapproved,wewillsendyoutheconfirmationemailwithitinerary.": "Once it is approved, we will send you the confirmation email with itinerary.", "Asperyourcompanystravelpolicybasefaresarerestricted": "As per your company's travel policy, Base fares are restricted.", "Asperyourcompanystravelpolicy,theselectedclassisabovethemaximumclassallowed": "As per your companys travel policy, the selected class is above the maximum class allowed (", "Asperyourcompanystravelpolicy,theselectedclassisallowedONLYiftheflightdurationismorethan ": "As per your company's travel policy,the selected class is allowed ONLY if the flight duration is more than ", "asperyourcompanytravelpolicynonrefundableratesarerestricted": "As per your company's travel policy, non-refundable rates are restricted.", "hrs.": " hrs.", "thepriceof ": "the price of ", "thepriceoftheselectedcarismorethanthemaximumallowedprice": "the price of the selected car is more than the maximum allowed price (", "Asperyourcompanystravelpolicy,theselectedcartypeisnotallowed.": "As per your company's travel policy, the selected car type is not allowed.", "Asperyourcompanystravelpolicy,thepriceofselectedcarismorethanmaximumallowablelimitof$": "As per your company's travel policy, the price of selected car is more than maximum allowable limit of ", "thepriceoftheselectedhotelroomismorethanthemaximumallowedprice": "the price of the selected hotel room is more than the maximum allowed price (", "Asperyourcompanystravelpolicy,thepriceofselectedhotelismorethanmaximumallowablelimitof$": "As per your company's travel policy, the price of selected hotel is more than maximum allowable limit of ", ")forthegivensearchinput.": ") for the given search input", "theselectedflightismorethanthemaximumallowedprice": "the selected flight is more than the maximum allowed price (", "Asperyourcompanystravelpolicyandcurrentavailability, ": "As per your company's travel policy and current availability, ", "Asperyourcompanystravelpolicy,theselectedclassrequiresadminapprovaltoproceed.": "As per your company's travel policy, the selected class requires admin approval to proceed.", "Asperyourcompanystravelpolicy,theselectedclassisallowedONLYforinternationalflights.": "As per your company's travel policy,the selected class is allowed ONLY for international flights.", "Asperyourcompanystravelpolicy,thepriceof ": "As per your company's travel policy,the price of ", "selectedflightismorethanmaximumallowablelimitof ": "selected flight is more than maximum allowable limit of ", "Profileinformationupdatedsuccessfully": "Profile information updated successfully!", "Thebookingisinprogressandcannotbestoppedatthisstage.": "The booking is in progress and cannot be stopped at this stage.", "Yourapprovalrequestisonitswayandcannotbestoppedatthisstage.": "Your approval request is on its way and cannot be stopped at this stage.", "Yourapprovalrequestisonitsway.Areyousureyouwanttoclose": "Your approval request is on its way. Are you sure you want to close?", "ApplyGallopCash": "Apply GallopCash", "Areyousureyouwanttoproceed": "Are you sure you want to proceed?", "BillToCompany": "Bill To Company", "Attention": "Attention !", "Book": "BOOK", "Tax": "Tax", "TotalTax": "Total Tax", "pernightperroom": "per night, per room", "Hereisbreakdownoftheestimatedtaxes,feesandsurchargesthatapplytoyourreservationwhenyoupick-upyourrentalcar": "Here is breakdown of the estimated taxes,fees and surcharges that apply to your reservation when you pick-up your rental car.", "Hereisbreakdownofthetaxesandfeesthatapplytoyourreservation.Notethatcharges": "Here is breakdown of the taxes and fees that apply to your reservation.", "Fees": "Fees", "Total": "Total", "cancel": "cancel", "PolicyInformation": "Policy Information", "Approvalrequesthasbeensenttoyouradmin": "Approval request has been sent to your admin.", "Requestmoreoptions": "Request more options!", "Pleasetypeyournotetoouragentandsomeonewillgetback": "Please type your note to our agent and someone will get back", "Unfortunately,youhadreceivedoneoptionforthis": "Unfortunately, you had received one option for this.", "REQUESTMOREOPTIONS": "REQUEST MORE OPTIONS", "GOBACK": "GO BACK", "Success!": "Success!", "Yourrequesthasbeensent": "Your request has been sent.", "youwillreceiveanemailwithmoreoptions.": "you will receive an email with more options.", "BOOKANOTHERTRAVEL": "BOOK ANOTHER TRAVEL", "Pleasewait!Weareprocessingyourrequest": "Please wait! We are processing your request...", "SELECTDIFFERENTOPTIONS": "SELECT DIFFERENT OPTIONS", "SENDAPPROVALREQUEST": "SEND APPROVAL REQUEST", "Toproceed,pleasesubmitrequestforapproval": "To proceed, please submit request for approval", "OutsidePolicy": "Outside Policy", "Delete": "Delete", "SummaryofCharges": "Summary of Charges", "Thisistotalestimatedcostofyourcarrentalreservation.Actualcostmayvarydependingonspecialrequestanditempurchasedatthecounter.": "This is total estimated cost of your car rental reservation. Actual cost may vary depending on special request and item purchased at the counter.", "totaldueatcounter": "total due at counter", "BasePrice": "Base Price", "Nights": "Nights", "Roomcostpernight": "Room cost (per night)", "Rooms": "Rooms", "RoomSubtotal": "Room Subtotal", "Guests": "Guests", "TaxesandFees": "Taxes and Fees", "RoomchargesDueathotel": "Room charges (Due at hotel)", "RoomchargesDuenow": "Room charges (Due now)", "Roomcharges": "Room charges", "PrivacyPolicy": "Privacy Policy", "and": "and", "ResortfeeDueatHotel": "Resort fee (Due at Hotel)", "TOTALCHARGES": "TOTAL CHARGES", "Totalhotelchargeswithresortfees": "(Total hotel charges with resort fees)", "ByclickingBook,Iagreetothe": "By clicking 'Book', I agree to the", "BookingConditions": "Booking Conditions", "SUBMITFORAPPROVAL": "SUBMIT FOR APPROVAL", "SENDBOOKINGREQUEST": "SEND BOOKING REQUEST", "SENDREQUEST": "SEND REQUEST", "TermsandConditions": "Terms and Conditions", "CreditsUsed": "Credits Used", "TravelCredits": "Travel Credits", "Travelermayberequiredtocarrythiscardduringhotelcheck-in.": " Traveler may be required to carry this card during hotel check-in.", "Submitreceiptto": "Submit receipt to", "Deletingthecardquestion": "Deleting the card question", "DueToday": "Due Today", "GOBack": "GO Back", "GallopCash": "GallopCash", "GallopCashcantbeusedmorethantotalamount": "GallopCash can't be used more than total amount.", "GallopCashonthisbooking": "GallopCash on this booking", "Generate": "Generate", "Mybusinesstravelisapproved": "My business travel is approved.", "Newcard": "New card", "Payathotel": "Pay at hotel", "Payment": "Payment", "Paymentdue": "Payment due", "Pleaseconfirmyourtravelapproval": "Please confirm your travel approval.", "Pleaseentervalidamountuptoonly": "Please enter valid amount upto only.", "Pleaseprovidecreditcardforpayment": "Please provide credit card for payment.", "Pleaseselectoneoftheabove": "Please select one of the above.", "Pleasewait": "Please wait...", "Proceed": "Proceed", "ThiscardisexpiredPleaseusedifferentcard": "This card is expired Please use different card.", "Thisispersonaltravelbooking": "This is personal travel booking.", "UnknownErrorPleasetryagain": "Unknown Error!1 Please try again", "Youwillearn": "You will earn", "available": "available", "deleteCardWaringMsg": "This payment method will not be displayed in your list of payment options", "optionOutsidePolicyText": "The options you selected are outside of the company travel policy"}, "personal": {"Address": "Address", "Pleaseclickoneditinformationtomakechanges": "Please click on 'edit information'  to make changes", "Relationship": "Relationship", "FullName": "Full Name", "Thisdiscountedfaredoesntallowloyaltyrewards.": "This discounted fare doesn't allow loyalty rewards.", "Unspecified": "Unspecified", "Adultpassenger": "Traveler #", "CarLoyaltyNumber": "Car Loyalty Number", "PleaseenteravalidCarLoyalityNumber": "Please enter a valid Car Loyality Number", "Someonetrustedtohelpoutinanemergency.": "Someone trusted to help out in an emergency.", "Agecannotbelessthan21years.": "Age cannot be less than 21 years.", "EditInformation": "Edit Information", "Savechangestoprofile": "Save changes to profile", "Cancel": "Cancel", "Saving...": "Saving...", "Minimum2lettersrequiredforfirstname.": "Minimum 2 letters required for first name.", "Agecannotbelessthan12years": "Age should be more than 11 years.", "Agecannotbelessthan18years": "Age should be more than 18 years.", "Agecannotbelessthan3years": "Age should be less than 2 years.", "Ageshouldbeup2yearsonly": "Age should be greater than 2 years and less than 12 years.", "Aisle": "Aisle", "AreaZipCode": "Area Zip Code", "CountryName": "Country Name", "DateofBirth": "Date of Birth", "Email": "Email", "EnterPassportNumber": "Enter Passport Number", "ExpiryDate": "Expiry Date", "Female": "Female", "FlyerNumber": "Flyer Number", "FrequentFlyerNumber": "Frequent Flyer Number", "Gender": "Gender", "HotelLoyaltyNumber": "Hotel Loyalty Number", "IssuanceCountry": "Issuance Country", "KTNNumber": "KTN Number", "KnownTravelerNumber": "Known Traveler Number(USA)", "LastName": "Last Name", "LoyaltyNumber": "Loyalty Number", "MMDDYY": "MM/DD/YY", "Male": "Male", "MiddlenameifshownonID": "Middle name(if shown on ID)", "Name": "Name", "Nationality": "Nationality", "NoPreference": "No Preference", "NotAvailable": "Not Available", "PassportInformation": "Passport Information", "PassportNumber": "Passport Number", "PhoneNumber": "Phone Number", "PleaseenteravalidFrequentFlyerNumber": "Please enter a valid Frequent Flyer Number.", "PleaseenteravalidHotelLoyaltyNumber": "Please enter a valid Hotel Loyalty Number.", "PleaseenteravalidKnownTravelerNumber": "Please enter a valid Known Traveler Number.", "Pleaseenteravalidemail": "Please enter a valid email.", "PleaseenteravalidfirstnameNumericorspecialcharactersarenotallowed": "Please enter a valid first name. Numeric or special characters are not allowed.", "PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed": "Please enter a valid last name. Numeric or special characters are not allowed.", "PleaseenteravalidmiddlenameNumericorspecialcharactersarenotallowed": "Please enter a valid middle name. Numeric or special characters are not allowed.", "Pleaseenteravalidpassportnumber": "Please enter a valid passport number.", "Pleaseenteravalidphonenumber": "Please enter a valid phone number.", "Pleaseenteravalidzipcode": "Please enter a valid zip code.", "Pleaseenteravalidzipcodewithoutspace": "Please enter a valid zip code without space.", "SeatPreference": "Seat Preference", "SelectIssuanceCountry": "Select Issuance Country", "SelectNationality": "Select Nationality", "Thisfieldcannotbelessthan9characters": "This field cannot be less than 8 characters.", "Thisfieldisrequired": "This field is required.", "Title": "Title", "Window": "Window", "Writeyourhomeaddresshere": "Write your home address here..", "ZipCode": "Zip Code", "firstName": "First Name", "PassengerDetails": "Traveler", "LoyalityNo": "Loyality No", "Passenger": "Traveler  #", "AdultPassenger": "Traveler", "ChildPassenger": "Child Passenger", "InfantPassenger": "Infant Passenger", "Room": "Room", "Adult": "Adult", "Child": "Child", "Minimum2lettersrequiredforlastname": "Minimum 2 letters required for last name."}, "profileLoyalty": {"Airline": "Airline", "TrainLoyaltyPrograms": "Train Loyalty Programs", "Trainline": "Train Line", "PleaseenteravalidTainLoyalitynlyalphanumericcharactorsallowed": "Please enter a valid train loyalty number .only alpha-numeric charactors allowed.", "Locale": "Locale", "CarLoyaltyNumber": "Car Loyalty Number", "Cars": "Cars", "DuplicateEntry": "Duplicate Entry", "Flyernumber": "Flyer number", "FrequentFlyerNumber": "Frequent Flyer Number", "FrequentGuestNumber": "Frequent Guest Number", "GuestNumber": "Guest Number", "Hotels": "Hotels", "LoyaltyNumber": "Loyalty Number", "PleaseWait": "Please Wait", "Pleaseenteravalidfrequentflyernumberonlyalphanumericcharactorsallowed": "Please enter a valid frequent flyer number .only alpha-numeric charactors allowed.", "Pleaseenteravalidloyaltynumberonlyalphanumericcharactorsallowed": "Please enter a valid loyalty number .only alpha-numeric charactors allowed.", "Save": "Save", "Thisfieldisrequired": "This field is required.", "InValidCharacters": "Special characters are not allowed", "remove": "remove"}, "profilePage": {"Pleasewait": "Please wait...", "Profilesavedsuccessfully": "Profile saved successfully!!", "Saving": "Saving...", "Successful": "Successful!"}, "profilePersonal": {"Ageshouldbeatleast12years": "Age should be atleast 12 years.", "Country": "Country", "DOB": "DOB", "Number": "Number-", "Exp": "Exp-", "Pleaseupdateallfieldsorleaveitempty": "Please update all fields or leave it empty.", "Spacenotallowed": "Space not allowed", "EmergencyContact": "Emergency Contact", "Pleasekeepyouremergencycontactdetailsuptodate.": "Please keep your emergency contact details upto date.", "Someonetrustedtohelpoutinanemergency.": "Someone trusted to help out in an emergency.", "Enteryourhomeaddresshere": "Enter your home address here", "Enteryourworkaddresshere": "Enter your work address here", "Female": "Female", "FirstName": "First Name", "Gender": "Gender", "HomeAddress": "Home Address", "KTNNumber": "KTN Number", "KnownTravelerNumber": "Known Traveler Number(USA)", "LastName": "Last Name", "MMDDYY": "MM/DD/YY", "Male": "Male", "MiddleNameIfshownonid": "Middle Name (If shown on id)", "Name": "Name", "Nationality": "Nationality", "Passport": "Passport", "PassportExpiryDate": "Passport Expiry Date", "PassportNumber": "Passport Number", "PersonalInformation": "Personal Information", "PhoneNumber": "Phone Number", "PleaseWait": "Please Wait", "PleaseenteravalidKnownTravelerNumber": "Please enter a valid Known Traveler Number.", "PleaseenteravalidfirstnameNumericorspecialcharactersarenotallowed": "Please enter a valid first name. Numeric or special characters are not allowed.", "PleaseenteravalidlastnameNumericorspecialcharactersarenotallowed": "Please enter a valid last name. Numeric or special characters are not allowed.", "PleaseenteravalidmiddlenameNumericorspecialcharactersarenotallowed": "Please enter a valid middle name. Numeric or special characters are not allowed.", "Pleaseenteravalidpassportnumber": "Please enter a valid passport number.", "Pleaseenteravalidphonenumber": "Please enter a valid phone number.", "Save": "Save", "Thisfieldcannotbelessthan9characters": "This field cannot be less than 8 characters.", "Title": "Title", "Usesameasinidincludingmiddlename": "*Use same as in id including middle name.", "WorkAddress": "Work Address"}, "profilePref": {"Car": "Car", "Van": "<PERSON>", "Convertible": "Convertible", "SUV": "SUV", "Pickup": "Pickup", "Aisle": "Aisle", "AnyAirline": "Any Airline", "BasicEconomy": "Basic Economy", "Business": "Business", "CompactSlashEconomy": "Compact/Economy", "Economy": "Economy", "First": "First", "Fullsize": "Full size", "Intermediate": "Intermediate", "Luxury": "Luxury", "NoPreference": "No Preference", "Premium": "Premium", "Premium First": "Premium First", "PremiumEconomy": "Premium Economy", "Standard": "Standard", "Window": "Window", "spacestar": " star"}, "profilePreference": {"3star": "3 star", "4star": "4 star", "5star": "5 star", "Address": "Address", "Aisle": "Aisle", "AnyAirline": "Any Airline", "Whatisyourpreferredcarclass": "What is your preferred car class ?", "Whatisyourpreferredcartype": "what is your preferred car type?", "Basiceconomyisarestrictiveclasswhichmaynotprovideadvanceseatselectionoranyupgrades": "Basic economy is a restrictive class which may not provide advance seat selection or any upgrades.", "Doyouhaveanypreferredairlines": "Do you have any preferred airlines?", "Doyoupreferflyingonthebasiceconomywhenavailable": "Do you prefer flying on the basic economy when available?", "No": "No", "NoPreference": "No Preference", "PleaseWait": "Please Wait", "PreferredAirlines": "Preferred Airlines", "PreferredClass": "Preferred Class", "PreferredHomeAirport": "Preferred Home Airport", "PreferredHotel": "Preferred Hotel", "PreferredSeat": "Preferred Seat", "Preferredcar": "Preferred car", "Save": "Save", "Whatisyourfavoritecarbrand": "What is your favorite car brand ?", "Whatisyourfavoritehotelbrand": "What is your favorite hotel brand ?", "Whatisyourpreferredcarsize": "What is your preferred car size?", "Whatisyourpreferredclass": "What is your preferred class?", "Whatisyourpreferredseat": "What is your preferred seat ?", "Whatsyourhomeaddress": "What's your home address ?", "Whatsyourpreferredhomeairport": "What's your preferred home airport ?", "Whatsyourpreferredhotelclass": "What's your preferred hotel class ?", "Window": "Window", "Yes": "Yes"}, "profileTablist": {"FAQs": "FAQs", "Loyaltynumbers": "Loyalty numbers", "Preferences": "Preferences", "TravelProfile": "Travel Profile", "Account": "Account"}, "result": {"DepartureFlight": "Departure Flight", "FetchingyourTrainspleasewait": "Fetching your trains. Please wait...", "Notrainsfound": "No trains found", "TRAINSTATIONS": "TRAIN STATIONS", "RatelimitexceededPleasetryafterfewminutes": "Rate limit exceeded. Please try after few minutes.", "Base": "Base", "FetchingyourHotelspleasewait": "Fetching your hotels. Please wait...", "FetchingyourCarspleasewait": "Fetching your cars. Please wait...", "FetchingyourFlightspleasewait": "Fetching your flights. Please wait...", "Selectlugagge": "Select lugagge", "Pleaseselectluggageoption": "Luggage option for", "FetchingmoreTrainspleasewait": "Fetching more trains. Please wait...", "Trainfare": "Train Fare", "Wehavereceivedyourtrainbookingrequest.Pleaseallowus20minutestoprocessandconfirmthetrain.Ifyouhaveanyquestions,pleasecontactcustomersupport.": "We have received your train booking request. Please allow us 20 minutes to process and confirm the train. If you have any questions, please contact customer support.", "FetchingmoreFlightspleasewait": "Fetching more flights. Please wait...", "Standard": "Standard", "Enhanced": "Enhanced", "Premium": "Premium", "ConnectionLost": "Connection Lost", "CHECKTRIPS": "CHECK TRIPS", "TRYAGAIN": "TRY AGAIN", "Pleasecheckyournetworksettings": "Please check your network settings", "OK": "OK", "detailsPleasetryagain": "details. \n Please try again", "Sorrywelosttheconnectionandcouldnotloadtheroom": "Sorry, we lost the connection and could not load the room ", "Networkdisconnected": "Network disconnected", "Pleasetryagain": " \n Please try again", "Sorrywelosttheconnection": "Sorry, we lost the connection. ", "Sorrywelosttheconnectionwhilebookingwasinprogress": "Sorry, we lost the connection while booking was in progress.  ", "Pleasecheckyourconnectionandchecktripssectionorcontactsupporttoknowthestatusofyourbooking": " \n Please check your connection and check trips section or contact support to know the status of your booking. ", "ReturnFlight": "Return Flight", "Firstclassfares": "First class fares", "Businessclassfares": "Fares with additional premium benefits. Typically these are business or first class fares (or premium economy fares depending on the airline)", "Premiumeconomyfares": "Fares with more than standard legroom. Typically these are premium economy fares (or economy fares depending on the airline)", "Economyfareswithcomplimentaryseatassignment": "Fares with standard legroom with complimentary seat assignment. Typically these are economy fares.", "Economyfareswithnocomplimentaryseatassignment": "Fares that may have restrictions such as no free seat choice, baggage, change policy, and more. Typically these are basic economy fares.", "SEEMOREOPTIONS": "SEE MORE OPTIONS", "Select": "Select", "Sortby": "Sort by"}, "roomGallery": {"SELECTROOM": "SELECT ROOM"}, "carresult": {"avgday": "avg/day", "Nocarsfound": "No cars found", "Pick-uplocation": "Pick-up location", "miles": " miles", "Drop-offlocation": "Drop-off location", "notsame": "notsame", "All": "All", "Any": "Any", "AutomaticTransmission": "Automatic Transmission", "AirConditioning": "Air Conditioning", "Specification": "Specification", "CarOptions": "Car Options", "BrandType": "Brand Type", "Airportlocations": "Airport locations", "Airportlocationsatterminal": "Airport locations(at terminal)", "Airportlocationsoutsideairport": "Airport locations(outside airport)", "Trainstationlocations": "Train station locations", "Portlocations": "Port locations", "Citylocations": "City locations", "VehicleType": "Vehicle Type", "SelectCar": "Select Car", "Searchbylocationaddress": "Search by location address", "LocationType": "Location Type", "sameaspick-up": "same as pick-up", "Select": "Select", "CarLocation": "Car Location", "CarSpecification": "Car Specification", "RentalCompany": "Rental Company", "Unlimitedmileage": "Unlimited mileage", "Automatictransmission": "Automatic transmission", "Pick-up": "Pick-up", "Drop-off": "Drop-off", "FreeCancellation": "(Free Cancellation)", "Includingtaxandfees": "Including tax and fees"}, "search": {"NetworkError": "Network Error", "TryAgain": "Try Again", "Traveldates": "Travel dates", "All": "All", "Pleasecheckyourinternetconnectionandtryagain.": "Please check your internet connection and try again.", "GuestTraveler": "Guest Traveler", "Guest": "Guest", "Myself": "Myself", "tab_bookings": "Bookings", "ALLBookings": "ALL Bookings", "OtherEmployees": "Other Employees", "Experiences": "Experiences", "AM": "AM", "PM": "PM", "FetchingDetails": "Fetching Details...", "Bookingforothersisnotallowedforyou.Pleasecontactyouadministratortoenabletheabilitytobookforothers.": "Booking for others is not allowed for you. Please contact your administrator to enable the ability to book for others.", "Areyousureyouwanttoproceed?": "Are you sure you want to proceed ?", "Youwillbesignedoutof": "You will be signed out of ", "andsignedinto": " and signed in to ", "account": " account", "SignedOut": "Signed Out", "Yes": "Yes", "No": "No", "account.": "account.", "OK": "OK", "ResetPassword": "Reset Password", "Youhavebeensignedoutof": "You have been signed out of ", "Slackconnectedsuccessfully.": "<PERSON><PERSON><PERSON> connected successfully.", "Bookings": "Bookings", "Apologies!somethingwentwrong,wecouldntretriveemployeeslist.Pleasetryagainlaterorcontactsupport": "Apologies! something went wrong,we couldn't retrive employees list.Please try again late ror contact support", "Pleasecheckyourinternet": "Please check your internet", "HotelResults": "Hotel Results", "TravellerDetails": "Traveller Details", "FlightResults": "Flight Results", "PaymentDetails": "Payment Details", "CarResults": "Car Results", "Drop-offDate": "Drop-off Date", "Pick-upDate": "Pick-up Date", "Flight1": "Flight1", "Flight": "Flight", "Flight3": "Flight3", "Flight4": "Flight4", "Pleaseentercityaddress.": "Please enter city address.", "Locationsharingisdisabled": "Location sharing is disabled!", "FlightSearch": "Flight Search", "HotelSearch": "Hotel Search", "CarSearch": "Car Search", "TrainSearch": "Train Search", "tab_cars": "Cars", "ADDAFLIGHT ": "ADD A FLIGHT ", "Yourcurrentlocation": "Your current location", "AddTraveler": "Add Traveler", "Dashboard": "Dashboard", "PendingApprovals": "Pending Approvals", "Drop-off": "Drop-off", "Pickup": "Pick-up", "Pick-upandDrop-offlocation": "Pick-up and Drop-off location", "Thedrop-offmustoccuratleastonehourafterthepick-up": "The drop-off must occur at least one hour after the pick-up.", "Addadifferentdrop-offlocation": "Add a different drop-off location.", "Pleaseenteravalidlocationorairport": "Please enter a valid location or airport", "Sorrywedonotsupportmorethan9passengers": "Sorry, we do not support more than 9 passengers.", "Youmusthaveatleastoneadultpertwoinfants": "You must have at least one adult per two infants.", "Retreats": "Retreats", "USA": "USA", "emptyString": "Aged 2 to 12", "TravellerDetail": "Traveller Detail", "Apply": "Apply", "Selecttraveler": "Select traveler", "Japan": "Japan", "Check-in": "Check-in", "Check-out": "Check-out", "Destination": "Destination", "DestinationCityAirportorAddress": "Destination: City or Address", "HotelChains": "Hotel Chains", "NumberofPeople": "Number of People", "NumberofRooms": "Number of Rooms", "Origin": "Origin", "Pleaseentercityaddress": "Please enter city address.", "Search": "Search", "SelectArrivalDate": "Arrival", "SelectClass": "Select Class", "SelectDate": "Select Date", "SelectCheckinDate": "Check-in", "SelectCheckoutDate": "Check-out", "SelectDepartureDate": "Departure", "SelectNumberofPassengers": "Select Number of Passengers", "SelectTime": "Select Time", "SelectTimeType": "Select Time Type", "SourceandDestinationcannotbesame": "Source and Destination can not be same.", "TipEnteryourdestinationaddress": "Tip: Enter your destination address.", "select_ticket_type": "Select Ticket Type", "tab_flights": "Flights", "tab_hotels": "Hotels", "Pleaseselectfromdropdown": "Please enter a valid airport code", "Business": "Business", "Leisure": "Leisure"}, "searchModal": {"SearchingForFlights": "Searching For Flights", "Searchingforflightsthatbestmatchyourpreferences": "Searching for flights that best match your preferences!", "Theseiconswill": "These icons will", "highlightyourpreferences": "highlight your preferences"}, "searchResult": {"Book": "Book", "Continue": "Continue", "Noflightsfound": "No flights found", "Pleasecheckyourinternet": "Please check your internet", "ProceedWithutCredits": "Proceed without credits"}, "successModel": {"showmeitinerary": "View Itinerary", "close": "close", "Pleasewait,RedirectingyoubacktoTripDetailsassoonasTransactionisgenerated.": "Please wait, Redirecting you back to Trip Details as soon as Transaction is generated.", "Thankyouforcompletingyourpassengerprofile,yourtransactionrequestisonitsway.": "Thank you for completing your passenger profile, your transaction request is on itsway.", "Wehavereceivedyourflightbookingrequest.Pleaseallowus20minutestoprocessandconfirmtheflight.Ifyouhaveanyquestions,pleasecontactcustomersupport.": "We have received your flight booking request. Please allow us 20 minutes to process and confirm the flight. If you have any questions, please contact customer support.", "Inthemeantimewouldyoulikemetosendyouhoteloptionsforyourtrip": "In the mean time ,would you like me to send you hotel options for your trip?", "Iwillsendyouaconfirmationandreceiptsoonasyourticketsarebooked": "I will send you a confirmation and receipt soon as your tickets are booked.", "Pleasenoteyourcreditcardwillnotbechargeduntilyourbookingiscompleted": "Please note: Your credit card will not be charged until your booking is completed.", "ThankyouforcompletingyourpassengerprofileYourbookingrequestisonitsway": "Thank you for completing your passenger profile. Your booking request is on its way", "yesShowmehotels": "yes,Show me hotels", "searchAgain": "Search Again", "hotelUnavailableMessage": "Unfortunately  the selected itinerary is no longer available. Please click \"Search Again\" to book an alternative option or contact support for help.", "hotelBookingPendingMessage": "We have received your request to book the selected itinerary. Please allow us 20 minutes to process the booking. If you have any questions, please contact customer support."}, "errors": {"NetworkError": "Network Error, Please try again OR contact {{GALLOP_HELP_EMAIL}} "}, "footer1": {"Termsofservice": "Terms of service", "Home": "Home", "Privacy": "Privacy"}, "navigation": {"Trips": "Trips", "Book": "Book", "Approvals": "Approvals", "Profile": "Profile", "Support": "Support", "Cards": "Cards"}, "help": {"Support": "Support", "Help": "Help", "Werehereforyou": "We're here for you 24/7"}, "support": {"Phone": "Phone", "Text": "Text", "Email": "Email", "ChatWithUs": "Chat with us"}, "dashboardWrapper": {"PaymentMethods": "Payment Methods", "ProjectTags": "Custom Fields", "Integerations": "Integrations", "Travellers": "Travelers", "WaytogoYourcolleaguehasbeenadded": "Way to go! Your colleague has been added.", "WaytogoYourcolleaguesarealreadyadded": "Way to go! Your colleagues are already added.", "GreatYouhavereviewedyourpolicy": "Great! You have reviewed your policy.", "NiceYourcentralbillingisnowconfigured": "Nice! Your central billing is now configured.", "NiceThecentralbillingisalreadyconfigured": "Nice! The central billing is already configured.", "AwesomeYouhavereviewedyourprofile": "Awesome! You have reviewed your profile.", "CoolYouhavewalked-throughbooking": "Cool! You have walked-through booking.", "PendingApprovals": "Pending Approvals", "ApprovalsHistory": "Approvals History", "Messages": "Messages", "Bookings": "Bookings", "Login": "<PERSON><PERSON>", "ManageCards": "Manage Cards", "Pending": "Pending", "History": "History", "Forbetterexperiencepleaseuselargerscreendevicessuchaslaptopordesktop": "For better experience, please use larger screen devices, such as laptop or desktop.", "Dashboard": "Dashboard", "GettingStarted": "Getting Started", "Reports": "Reports", "ActiveTravellers": "Active Traveler", "ActiveTravelers": "Active Travelers", "Approvals": "Approvals", "People": "People", "Cards": "Cards", "Policies": "Policies", "Settings": "Settings", "MyTrips": "My Trips", "BookTravel": "Book Travel", "Profile": "Profile", "MyCards": "My Cards", "LogOut": "Log Out", "Congrats!Youreallsettogo!": "Congrats! You're all set to go!", "Startsettingup!": "Start setting up!", "CustomizePolicy": "Customize Policy", "SetUpYourProfile": "Set Up Your Profile", "Walk-ThroughBooking": "Walk-Through Booking", "AddColleagues": "Add Colleagues", "ConfigureCentralBilling": "Configure Central Billing"}, "activeTraveler": {"DutyofCare": "Duty of Care", "Messages": "Messages", "To": "To", "Searchbynameoremail": "Search by name or email", "Travelers": "Travelers", "FilterbyDate": "Filter by Date", "All": "All", "Today": "Today", "Next7days": "Next 7 days", "Next30days": "Next 30 days", "Clearfilters": "Clear filters", "Typeyourmessage": "Type your message", "send": "send", "Traveldate": "Travel date", "Name": "Name", "ActiveTravellers": "Active Traveler", "ActiveTravelers": "Active Travelers", "TODAY": "TODAY", "NEXT7DAYS": "NEXT 7 DAYS", "NEXT30DAYS": "NEXT 30 DAYS", "Period": "Period", "SearchbynameConfirmation": "Search by name or Confirmation no. or trip name", "Searchbyname": "Search by name", "FetchingData": "Fetching Data", "0travelersfound": "0 travelers found"}, "dashboard": {"Disapproved": "Disapproved", "Yoursessionhasexpired": "Your session has expired.", "GeneralSetting": "General Settings", "Language": "Language", "UploadMetadata": "Upload Metadata", "Howtoconfigure": "How to configure ", "SAMLSSO": " SAML SSO?", "MetadataFileuploadedsuccessfully": "Metadata File uploaded successfully", "Yourrequestforactivating": "Your request for activating", "SSOhasbeenreceivedOurteamwillreviewandenabletheintegrationForanyquestionspleasereachouttotriproutespringcom": "SSO has been received. Our team will review and enable the integration. For any questions, please reach <NAME_EMAIL>", "RequestActivation": "Request Activation", "RequestReceived": "Request Received", "Deactivate": "Deactivate", "Subscribetoevents": "Subscribe to events:", "UserdeactiviatedUserdeletedUserunassignedfromappUsersuspended": "User deactivated, User deleted, User unassigned from app, User suspended", "CreateEventHookstoautomaticallydeleteterminatedusersfromRoutespring(Optional)": "Create Event Hooks to automatically delete terminated users from Routespring (Optional)", "ConfigureUserAssignmentHooks": "Configure User Assignment Hooks", "UserAssignmentHook": "User Assignment Hook", "HookUrl": "URL", "Autheticationfield": "Authentication field", "AuthenticationSecret": "Authentication Secret", "Copiedtoclipboard": "Copied to clipboard", "Continue": "Continue", "Active": "Active", "copy": "copy", "manage": "manage", "Pending": "Pending", "Configure": "Configure", "SigninwithSSo": "Sign in with SSO", "Change": "Change", "WorkEmail": "Work Email", "MetadataURL": "Metadata File", "Helparticleonhowtosetitupon": "Help article on how to set it upon ", "integrationandsavetimeonboardingyourusers": " integration and save time onboarding your users", "SecurelyandeffortlesslyprovisionaccesstoRoutespringthrough": "Securely and effortlessly provision access to Routespring through ", "IntegratingRoutespringwith": "Integrating Routespring with ", "streamlinesusermanagementbyautomatingprovisioningandensuressecuresinglesignonSSOaccessThisintegrationnotonlyenhancessecuritybutalsosimplifiestheuserexperienceeliminatingtheneedformultiplepasswordsandacceleratingtheonboardingprocess": " streamlines user management by automating provisioning and ensures secure, single sign-on (SSO) access. This integration not only enhances security but also simplifies the user experience, eliminating the need for multiple passwords and accelerating the onboarding process.", "IntegratingRoutespringwithGooglessostreamlinesusermanagementbyautomatingprovisioningandensuressecuresinglesignonSSOaccessThisintegrationnotonlyenhancessecuritybutalsosimplifiestheuserexperienceeliminatingtheneedformultiplepasswordsandacceleratingtheonboardingprocess": "Integrating Routespring with Google_sso streamlines user management by automating provisioning and ensures secure, single sign-on (SSO) access. This integration not only enhances security but also simplifies the user experience, eliminating the need for multiple passwords and accelerating the onboarding process.", "MetaDatafileuploaded": "MetaData file uploaded", "ATTRIBUTEMAPPINGS": "Attributes Mapping", "<EMAIL>": "Thank you for completing the Zoho integration process. Our team will review and enable the integration. For any questions, please reach <NAME_EMAIL>.", "RequireallusersinthisdepartmenttologinwithSSO": "Require all users in this department to login with SSO", "Name": "Name", "NameFormat": "Name Format", "Value": "Value", "HelparticleonhowtosetituponMsal": "Learn more about how to set it up on Microsoft Entra", "IntegratingRoutespringwithMsalstreamlinesusermanagementbyautomatingprovisioningandensuressecuresinglesignonSSOaccessThisintegrationnotonlyenhancessecuritybutalsosimplifiestheuserexperienceeliminatingtheneedformultiplepasswordsandacceleratingtheonboardingprocess": "Integrating Routespring with Msal streamlines user management by automating provisioning and ensures secure, single sign-on (SSO) access. This integration not only enhances security but also simplifies the user experience, eliminating the need for multiple passwords and accelerating the onboarding process.", "SecurelyandeffortlesslyprovisionaccesstoRoutespringthroughMsalintegrationandsavetimeonboardingyourusers": "Securely and effortlessly provision access to Routespring through Msal integration, and save time onboarding your users.", "SecurelyandeffortlesslyprovisionaccesstoRoutespringthroughZohointegrationandsavetimeonboardingyourusers": "Securely and effortlessly provision access to Routespring through Zoho integration, and save time onboarding your users.", "EnableyourtravelerstoautomaticallyfeedRoutespringreceiptstocreateexpensereport": "Enable your travelers to automatically feed Routespring receipts to create expense report.", "AutomateyouraccountingbyautomaticallysyncingyourRoutespringtransactionswithyourQuickBooksaccount": "Automate your accounting by automatically syncing your Routespring transactions with your QuickBooks account.", "SecurelyandeffortlesslyprovisionaccesstoRoutespringthroughOktaintegrationandsavetimeonboardingyourusers": "Securely and effortlessly provision access to Routespring through Okta integration, and save time onboarding your users.", "GetapprovalnotificationsstraighttoyourMSTeamschannelGetalltheinformationsuchaspolicy&travelnotesbeforeapprovinganytripinasingleclick": "Get approval notifications straight to your MS Teams channel. Get all the information, such as policy & travel notes, before approving any trip in a single click.", "GetapprovalnotificationsstraighttoyourSlackaccountGetalltheinformationsuchaspolicy&travelnotesbeforeapprovinganytripinasingleclick": "Get approval notifications straight to your Slack account. Get all the information, such as policy & travel notes, before approving any trip in a single click.", "SSOintegrationname": "SSO integration name", "HelparticleonhowtosetituponOkta": "Learn more about how to set it up on Okta", "HelparticleonhowtosetituponZoho": "Learn more about how to set it up on Zoho", "SPEntityID": "SP Entity ID", "NameIDformat": "Name ID format", "SinglesignonURLSPAssertionConsumerServiceURL": "Single sign-on URL (SP Assertion Consumer Service URL)", "LogintoyourSAMLidentityproviderandlocatethefieldslistedbelowCopytheinformationfromRoutespringintoyourproviderscorrespondingfield.": "Log in to your SAML identity provider and locate the fields listed below. Copy the information from Routespring into your providers corresponding field.", "IntegratingRoutespringwithOktastreamlinesusermanagementbyautomatingprovisioningandensuressecuresinglesignonSSOaccessThisintegrationnotonlyenhancessecuritybutalsosimplifiestheuserexperienceeliminatingtheneedformultiplepasswordsandacceleratingtheonboardingprocess": "Integrating Routespring with Okta streamlines user management by automating provisioning and ensures secure, single sign-on (SSO) access. This integration not only enhances security but also simplifies the user experience, eliminating the need for multiple passwords and accelerating the onboarding process.", "IntegratingRoutespringwithZohostreamlinesusermanagementbyautomatingprovisioningandensuressecuresinglesignonSSOaccessThisintegrationnotonlyenhancessecuritybutalsosimplifiestheuserexperienceeliminatingtheneedformultiplepasswordsandacceleratingtheonboardingprocess": "Integrating Routespring with Zoho streamlines user management by automating provisioning and ensures secure, single sign-on (SSO) access. This integration not only enhances security but also simplifies the user experience, eliminating the need for multiple passwords and accelerating the onboarding process.", "HumanResourcesIntegration": "Human Resources Integrations", "ApprovalNotifications": "Approval Notifications", "BacktoIntegrations": "Back to Integrations", "AccountingIntegrations": "Accounting Integrations", "Overview": "Overview", "ApprovedOn": "Approved on", "Approved": "Approved", "Expired": "Expired", "Previousperiod": "Previous period", "Previousmonth": "Previous month", "Previousquarter": "Previous quarter", "Previousyear": "Previous year", "Therearenoactivetravelersfortoday": "There are no active travelers for today.", "Therearenoactivetravelersforthenextsevendays": "There are no active travelers for the next seven days.", "TopAirlines": "Top Airlines", "TopVehicleTypes": "Top Vehicle Types", "TopHotelChains": "Top Hotel Chains", "SeeAllTravelers": "See All Travelers", "PendingApprovals": "Pending Approvals", "ManageAll": "Manage All", "Therearenopendingapprovalsatthistime.": "There are no pending approvals at this time.", "expiresin": "expires in", "expiresat": "expires at", "Period": "Period", "Compareto": "Compare to", "Department": "Department", "change": "change", "Flights": "Flights", "Total": "Total", "Hotels": "Hotels", "flight": "flight", "car": "car", "hotel": "hotel", "Cars": "Cars", "Spend": "Spend", "na": "n/a", "bookings": " bookings", "Compliance": "Compliance", "Overall": "Overall", "TotalTravelers": "Total Travelers", "Avg.Price": "Avg. Price/", "Day": "Day", "Night": "Night", "Booking": "Booking", "Travelers": "Travelers", "approvals": "approvals", "Topspenders": "Top spenders", "on": "on", "Uniquetravelers": "Unique travelers"}, "approval": {"AllEmployees": "All Employees", "PendingApprovals": "Pending Approvals", "ApprovalsHistory": "Approvals History", "Lookslikeyoumanagedeverythingwithinpolicy": "Looks like you managed everything within policy 😃", "Yay": "🎉Yay!!🎉", "Youdonthaveanypendingapprovals": "You don't have any pending approvals", "Approvals": "Approvals", "Approval": "Approval", "Pending": "Pending", "History": "History", "Showpendingapprovalsforalladmins.": "Show pending approvals for all admins.", "Name": "Name", "BookingDate": "Booking Date", "BookingType": "Booking Type", "DepartureCheck-in": "Departure/Check-in", "Destination": "Destination", "Approver": "Approver", "Review": "Review", "Period": "Period", "show": "show", "APPLY": "APPLY", "Reviewedby": "Reviewed by", "Reviewdate ": "Review date ", "Approved": "Approved?", "Note": "Note"}, "approvalDetail": {"Back": "Back", "ReviewersNote": "Reviewers note", "Policystatus": "Policy status: ", "Travelersnote": "Traveler's note: ", "Bookername": "<PERSON>'s name: ", "Bookeremail": "<PERSON>'s email: ", "Typeanoteforthetraveleroptional": "Type a note for the traveler", "withareasonforapproval.": "with a reason for approval.", "Pleaseenterapprovalnote": "Please enter approval note", "itineraryforApproval": "itinerary for Approval", "Followingitinerarywasalreadyapproved.": "Following itinerary was already approved.", "Followingitinerarywasalreadyrejected.": "Following itinerary was already rejected.", "Followingitineraryhasexpired.": "Following itinerary has expired.", "Congratulations": "Congratulations!!", "willshortlyreceivetheconfirmationemail.": "will shortly receive the confirmation email.", "Followingitineraryhasbeenapproved.": "Following itinerary has been approved.", "Theitineraryisnomoreavailableand": "The itinerary is no more available and", "hasbeeninstructedtobookagain.": "has been instructed to book again.", "Bookingisinprogress": "Booking is in progress!", "approvalishandeled": "Approval for the itinerary has been handeled!", "willbeinformedwiththestatusshortly.Foranyquestionspleasecontactsupport": "will be informed with the status shortly. For any questions, please contact support", "Thedisapprovalnotehasbeensentto": "The disapproval note has been sent to", "andthereservationactionforthefollowingitineraryhasbeencancelled.": "and the reservation action for the following itinerary has been cancelled.", "TheapprovalperiodhasEXPIREDand": "The approval period has EXPIRED and", "isinformedtobookanalternativeoption.": "is informed to book an alternative option.", "PleaseclickApproveTraveltoapprovethereservationoffollowingitineraryandbookthetravelorclickDisapproveifyoudonotwanttoapprovethisreservation.": "Please click 'Approve Travel' to approve the reservation of following itinerary and book the travel, or click 'Disapprove' if you do not want to approve this reservation.", "APPROVETRAVEL": "APPROVE TRAVEL", "DISAPPROVE": "DISAPPROVE", "Includeanotefor": "Include a note for", "withareasonfordisapproval.": "with a reason for disapproval.", "Pleaseenterdisapprovalnote": "Please enter disapproval note ", "SENDNOTE": "SEND NOTE", "CANCEL": "CANCEL", "Flightto": "Flight to", "outsideofpolicy": "(outside of policy)", "EffectivePrice": "Effective Price", "Hotelin": "Hotel in", "Cancellationpolicy": "Cancellation policy", "Carto": "Car to", "Actualcarmayvaryfromtheoneinshowninimage": "Actual car may vary from the one in shown in image", "Unlimitedmileage": "Unlimited mileage", "Automatictransmission": "Automatic transmission", "PICK-UPDETAILS": "PICK-UP DETAILS", "DROP-OFFDETAILS": "DROP-OFF DETAILS", "Sameaspick-up": "Same as pick-up"}, "report": {"TransactionsReport": "Transactions Report", "Transactiondates": "Transaction dates", "Traveldates": "Travel dates", "CardTransactions": "Card Transactions", "ApprovalsHistory": "Approvals History", "CreditReport": "Credit Report", "FetchingData": "Fetching Data", "Notransactionfound": "No transaction found", "NoData": "No Data", "Nocreditsfound": "No credits found", "Reports": "Reports", "Report": "Report", "TravelCredit": "Travel Credit", "Transactions": "Transactions", "Flights": "Flights", "TravelCredits": "Travel Credits", "Hotels": "Hotels", "Cars": "Cars", "Travelers": "Travelers", "Departments": "Departments", "ProjectTags": "Custom Fields", "ComplianceByDepartment": "Compliance By Department", "ComplianceByTraveler": "Compliance By Traveler", "Compliance": "Compliance", "DownloadforQuickbooks": "Download for QuickBooks", "ByDepartment": "By Department", "ByTraveler": "By Traveler", "Period": "Period", "Presets": "Presets", "Custom": "Custom"}, "employee": {"Default": "<PERSON><PERSON><PERSON>", "Atleastonecheckboxneedstobechecked": "At least one checkbox needs to be checked !!!", "EmployeeId": "Employee Id", "cardDetails": "Card Details", "ViewCardDetails": "View Card Details", "ViewAuthorizationForm": "View Authorization Form", "Sendbookingemailsto": "Send booking emails to:", "EnableBookingforminors": "Enable Booking for minors", "SaveguestusersbookedasSharedProfile": "Save guest traveler booked as 'Guest Profile' ", "Marksuseraslimitedprofile": "Marks user as limited profile", "Bookforotherscannotbedisabledfortravelmanagerandadminusers": "Book for others cannot be disabled for travel manager and admin users", "SelectDepartment": "Select Department", "SelectAirline": "Select Airline", "EligibleforAAAratesforhotels": "Eligible for AAA rates for hotels", "DeleteSelected": "Delete Selected", "theseemployees": "these users", "thisemployees": "this user", "ReviewPendingREQUEST": "Review Pending REQUESTS", "ThankyouforresolvingthisapprovalrequestThereare": "Thank you for resolving this approval request.", "morerequestspendingforapproval": "more requests pending for approval.", "Dismisstheguide": "Dismiss the guide", "Asperyourcompanystravelpolicyallbookingsneedtogototheadminforapproval": "As per your company's travel policy, all bookings need to go to the admin for approval", "GetstartedwithHotjar": "Get started with Routespring", "Popupclosesuccessfully": "Popup close successfully !!!", "Ifyoudismissityouwontsee": "If you dismiss it, you won't see", "again": "again.", "cancel": "Cancel", "Dismissguide": "Dismiss guide", "Allowusingcompanyscentralpaymentmethod": "Allow using company's central payment method", "DiscountCode": "Discount Code", "CompanyTravelers": "Users", "BillingNumber": "Billing Number", "Select": "Select", "EmployeeAddedSuccessfully": "User Added Successfully !!", "EmployeeUpdatedSuccessfully": "User Updated Successfully !!", "Fetchingemployeelist": "Fetching user list", "Noemployeehasbeenfound": "No user has been found.", "EmployeeDeletedSuccessfully": "User Deleted Successfully", "Departments": "Departments", "ADDDEPARTMENT": "ADD DEPARTMENT", "Nodepartmenthasbeenaddedyet": " No department has been added yet", "DepartmentName": "Department Name", "DefaultApprover": "<PERSON><PERSON><PERSON>", "PrimaryPolicy": "Primary Policy", "Centralbillingmethod": "Central billing method", "Carrentalbillingnumbers": "Car rental billing numbers", "Employees": "Employees", "ADDUSER": "ADD USER", "ADDEMPLOYEE": "ADD USER", "Searchbyname": "Search by name", "FirstName": "First Name", "LastName": "Last Name ", "Email": "Email", "Department": "Department", "Role": "Role", "Policy": "Policy", "Approver": "Approver", "PaymentOptions": "Payment Options", "Removinganemployee": "Removing user", "ConfirmRemoval": "Confirm Removal", "Areyousureyouwanttoremove": "Are you sure you want to remove", "fromcompanyaccount?": "from company account ?", "CONFIRMDELETE": "CONFIRM & DELETE", "WAIT": "WAIT", "Wait": "WAIT", "Cancel": "Cancel", "Deletedepartment": "Delete department", "ConfirmDeleteDepartment": "Confirm Delete Department", "Areyousureyouwanttodelete": "Are you sure you want to delete", "AddDepartment": "Add Department", "Enterdepartmentname": "Enter department name", "Thisfieldisrequired": "This field is required", "Approverchange": "Approver change", "Approversforfollowingemployeeswerechangedfromthedefaultdepartmentsettings.Wouldyouliketoapplychangestoanyofthefollowingemployeesaswell?PleaseselectandclickUpdatetoproceed": "Approver(s) for following userss were changed from the default department settings. Would you like to apply changes to any of the following users as well? Please select and click Update to proceed.", "Add": "Add", "Update": "Update", "DepartmentSettings": "Department Settings", "Pleaseentervalidtext.": "Please enter valid text.", "SetDefaultPolicy": "Set Default Policy", "SetDepartmentApprover": "Set Department Approver", "Allowemployeestobookforothertravelers": "Allow users to book for other travelers", "on": "on", "off": "off", "Whenenabled,bydefaulttheemployeesinthisdepartmentwillbeallowedtobookforothertravelers.Youcanalsoturnonoffthisabilityatemployeelevelaswell.": "When enabled, by default the users in this department will be allowed to book for other travelers. You can also turn on/off this ability at user level as well.", "Payment": "Payment", "Nocardsaresaved.Goto“Settings”andsavecorporatecreditcardtoaddithere.": "No cards are saved. Go to “Settings” and save corporate credit card to add it here.", "CreditCardforDepartment": "Credit Card for Department", "CarRentalSettings": "Car Rental Settings", "Carrentalcompany": "Car rental company", "Discountcode": "Discount code", "Billingnumber": "Billing number", "Save": "Save", "AddNewCarRental": "Add New Car Rental", "Restrictcarrentalcompaniestoabovecontractsonly.": "Restrict car rental companies to above contracts only.", "AddanEmployee": "Add a User", "EditEmployee": "Edit User", "AddEmployeeDetails": "Add User Details", "EditEmployeeDetails": "Edit User Details", "Pleaseentervalidfirstname.": "Please enter valid first name.", "Pleaseentervalidlastname.": "Please enter valid last name.", "Alloweddomainst": "Allowed domain list", "Pleaseenterapermittedemailaddress.": "Please enter a permitted email address.", "Admin": "Admin", "TravelManager": "Travel Manager", "Updateaspernewdepartment": "Update as per new department", "Allowusingcorporatecard": "Allow using corporate card", "Allowusingpersonalcard": "Allow using personal card", "Allowbookingforotheremployees": "Allow booking for other users", "Eligibleforgovernmentratesforhotels": "Eligible for government rates for hotels", "ADDSENDINVITE": "ADD & SEND INVITE", "UPDATE": "UPDATE", "Norecordavailabletogeneratereport": "No record available to generate report", "Userwithemailid": "User with email id '", "isalreadydefaultadmin": "' is already default admin", "ApprovercannotbethesameasemployeePleasechooseadifferentapprover": "Approver cannot be the same as user. Please choose a different approver.", "Department '": "Department '", "alreadyexists": "' already exists", "ApologiessomethingwentwrongwecouldntprocessrequestPleasetryagainlaterorcontactsupport": "Apologies! something went wrong, we couldn't process the request. Please try again later or contact customer support", "Duplicatedepartment": "Duplicate department !!"}, "policy": {"Enteramount": "Enter amount", "PerDiemPolicy": "Per-Diem Policy", "Proceedanyways": "Proceed anyways", "editBankAccount": "Edit Bank Account", "Pleaseenteravalidroutingnumber": "Please enter a valid routing number", "Pleaseenteravalidaccountnumber": "Please enter a valid account number", "SaveAccount": "Save Account", "automaticallyreimburseperdiemoncompletionofthetrip": "Automatically re-imburse per diem on completion of the trip", "AddBankAccount": "Add Bank Account", "ByaddingyourbankaccounttoyourRoutespringaccountandclickingbelowyouautorizeRoutespringtoDebitandcredityourbankasdescribedthese": "By adding your bank account to your Routespring account and clicking below, you authorize Routespring to debit and credit your bank as described these", "terms": " terms", "Routingnumber": "Routing Number", "DirectDebitandcreditauthorization": "Direct Debit and Credit authorization", "Yourbankaccountmustbeacheckingaccount": "Your bank account must be a checking account", "AccountNumber": "Account Number", "Mustmatchonyourbankaccountexactly": "Must match on your bank account exactly", "expensereimbursementaccount": "Expense reimbursement account", "wewillsendyourUSDpayoutstothisbankaccount": "We'll send your __CURRENCY__ payouts to this bank account", "AccountHolderName": "Account Holder Name", "YourBankAccount": "Your Bank Account", "Destination": "Destination", "AddPerDiempolicy": "Add Per-Diem policy", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "SelectCountry": "Select Country", "Fullday": "Full day", "Amount": "Amount", "Adddestination": "Add destination", "Nodeductiondefined": "No destination defined", "Maketravelpurposenotemandatory": "Make travel purpose note mandatory", "Whenenabledtravelerswillberequiredtosendatravelpurposenotewhensendingtheapprovalrequest.": "When enabled, travelers will be required to send a travel purpose note when sending the approval request.", "includingresortfee": "including resort fee ", "Absolutepricelimitonewayairfare": "Absolute price limit (one-way airfare)", "Absolutepricelimitperdayincludingtaxesfees": "Absolute price limit (per day including taxes & fees)", "Absolutepricelimitpernightincludingtaxesfees": "Absolute price limit (per night including taxes & fees)", "ThisisthedefaultcompanypolicyPleasechangethedefaultpolicybeforedeletingthispolicy": "This is the default company policy. Please change the default policy before deleting this policy.", "Pleaseenterpolicyname": "Please enter policy name", "Policywithsamenamealreadyexists": "Policy with same name already exists", "Enteramountincludingtaxesandfees": "Enter amount(including taxes and fees)", "Entervalueinhours": "Enter value in hours", "MiniEconomyorCompact": "Mini, Economy or Compact", "Intermediate": "Intermediate", "Standard": "Standard", "EconomyBaseandStandardfares": "Standard fares", "PremiumEconomyEnhancedandPremiumfares": "Enhanced fares", "BusinessPremiumfares": "Premium fares", "FullSize": "Full Size", "Premium": "Premium", "Luxury": "Luxury", "CompanyPolicies": "Company Policies", "AddNewPolicy": "Add New Policy", "Policyname": "Policy name", "Default": "Default?", "Setasdefault": "Set as default", "Backtopolicylist": "Back to policy list", "Thisfieldisrequired": "This field is required", "Save": "Save", "DOMESTICFLIGHTS": "DOMESTIC FLIGHTS", "INTERNATIONALFLIGHTS": "INTERNATIONAL FLIGHTS", "DOMESTIC": "DOMESTIC", "INTERNATIONAL": "INTERNATIONAL", "HOTELS": "HOTELS", "CARS": "CARS", "APPROVALS": "APPROVALS", "Approvalprocess": "Approval process", "on": "on", "off": "off", "Whenenabledyoucanselectwhenmanagersshouldapprovetravelbookings.Disablingthiswillallowemployeestobooktravelwithoutprocessingapprovals.": "When enabled, you can select when managers should approve travel bookings. Disabling this will allow travelers to book travel without processing approvals.", "Whenenabledtravelersmustselectflightssameasorlowerthandefinedclasstobeconsideredwithinpolicy.": "When enabled,travelers must select flights same as or lower than defined class to be considered within policy.", "Conditiononmaxclass": "Condition on max class", "Whenenabledtravelerscanbypassthepricerestrictionssetbelowiftheflightdurationismorethanthesetvalue.": "When enabled,travelers can by pass the price restrictions set below if the flight duration is more than the set value.", "Allowmaxclassbookingiftheflightdurationmorethan": "Allow max class booking if the flight duration more than", "Pleaseentervaluemorethan": "Please enter value more than ", "Disablebasiceconomy": "Restrict base fares", "Basiceconomyisveryrestrictiveclass.Disablingitwillremoveanyrestrictivefaresfromthetraveloptions.": "Base fares typically do not include free seat selection, flight changes, and other amenities. By enabling this restriction, base fares will always be flagged as outside policy.", "Absolutepricelimit": "Absolute price limit", "Whenenabledtravelersmustselectflightscheaperthanthedefinedcaptobeconsideredwithinpolicy.": "When enabled,travelers must select flights cheaper than the defined cap to be considered within policy.", "Relativepricelimit": "Relative price limit", "Whenenabledtravelersaregiveapercentageordollaramountflexibilitytobookabovethelowestlogicalfareavailableoptiontobeconsideredwithinpolicy.": "When enabled travelers are give a percentage or dollar amount flexibility to book above the cheapest available option to be considered with in policy.", "Whenenabledtravelersaregiveapercentageordollaramountflexibilitytobookabovethecheapestavailableoptiontobeconsideredwithinpolicy.": "When enabled, travelers are given a percentage or dollar amount flexibility to book above the lowest logical fare to be considered within policy.", "Whenenabledtravelersaregiveapercentageordollaramountflexibilitytobookabovethecheapestavailableoptiontobeconsideredwithinpolicy_hotels.": "When enabled, travelers are given a percentage or dollar amount flexibility to book above the median price for a given location to be considered within policy.", "Maximumclassallowance": "Maximum class allowance", "Whenenabledtravelersmustselectflightssameasorlowerthandefinedclasstobeconsideredwithinpolicy": "When enabled,travelers must select flights same as or lower than defined class to be considered within policy.", "Whenenabledtravelerscanbypassthepricerestrictionssetbelowiftheflightdurationismorethanthesetvalue": "When enabled,travelers can by pass the price restrictions set below if the flight duration is more than the set value.", "Whenenabledtravelersmustselecthotelssameasorlowerthandefinedclasstobeconsideredwithinpolicy.": "When enabled,travelers must select hotels same as or lower than defined class to be considered within policy.", "Whenenabledtravelersmustselecthotelscheaperthanthedefinedcaptobeconsideredwithinpolicy": "When enabled,travelers must select hotels cheaper than the defined cap to be considered within policy.", "Conditiononpaymenttype": "Condition on payment type", "Whenenabledyoucanenableroomoptionswithspecificpaymenttypeforyourtravelers.": "When enabled,you can enable room options with specific payment type for your travelers.", "Prepaidroomsonly": "Prepaid rooms only", "Postpaidroomsonly": "Postpaid rooms only", "Disablenon-refundablerooms": "Disable non-refundable rooms", "Bydefaulttravelerwillreceiverefundableaswellasnon-refundableroomoptions.Youcandisablenon-refundableroomstoallowtravelerstobookroomswithflexiblecancellationpolicy": "By default, traveler will receive refundable as well as non-refundable room options.You can disable non-refundable rooms to allow travelers to book rooms with flexible cancellation policy .", "Whenenabledtravelersmustselectcarssameasorlowerthandefinedclasstobeconsideredwithinpolicy": " When enabled,travelers must select cars same as or lower than defined class to be considered within policy.", "Vehicletypeallowance": "Vehicle type allowance", "Bydefaultallvehicletypesareallowed.Butyoucanrestricttooneormorevehicletypes.Whenenabledtravelersmustselectallowedvehicletypestobeconsideredwithinpolicy": "By default, all vehicle types are allowed. But you can restrict to one or more vehicle types.When enabled, travelers must select allowed vehicle type(s) to be considered within policy", "Whenenabledtravelersmustselectcarscheaperthanthedefinedcaptobeconsideredwithinpolicy": "When enabled,travelers must select cars cheaper than the defined cap to be considered within policy.", "Deletepolicy": "Delete policy", "ConfirmDeletePolicy": "Confirm Delete Policy", "Changethepolicyforthe": "Change the policy for the", "affectedemployeesto": "affected travelers to", "Next": "Next", "Cancel": "Cancel", "Clickconfirmtodeletepolicy": "Click 'confirm' to delete policy", "andchangetheappliedpolicyfor": "and change the applied policy for", "Confirm": "Confirm", "Thispolicyaffects": "This policy affects", "employees.Doyouwishtoproceedandchangeappliedpolicyfortheseemployees": "employees. Do you wish to proceed and change applied policy for these employees?", "Proceed": "Proceed", "Onlyforoutofpolicytravelbookings": "Only for out of policy travel bookings", "Foralltravelbookings": "For all travel bookings", "Notdefined": "Not defined", "Economy": "Economy", "PremiumEconomy": "Premium Economy", "Business": "Business", "Always": "Always", "BasedOnFlightDurationexcludinglayover": "Based On Flight Duration,excluding layover", "Asperpricerestrictionssetbelow": "As per price restrictions set below", "Yes": "Yes", "No": "No", "Policydeletedsuccessfully": "Policy deleted successfully !", "Policyaddedsuccessfully": "Policy added successfully !!", "Policyupdatedsuccessfully": "Policy updated successfully !!", "Policysavedsuccessfully": "Policy saved successfully !"}, "fuild": {"Update": "Update", "utilized": "utilized", "Issueasgiftcard": "Issue as gift card", "email": "Email", "Allbookingtype": "All booking type", "Bookingstatus": "Booking status", "Allbookingstatus": "All booking status", "firstname": "First Name", "lastname": "Last Name", "Guest": "Guest", "Yes": "Yes", "No": "No", "Processed": "Processed", "Pending": "Pending", "Limit": "Limit", "Clear": "Clear", "Typetosearch": "Type to search", "Searchbyemployeecardname": "Search by user, card name", "RequestAccessToFluidPay": "Request Access To FluidCash", "FluidPayAccessApproved": "FluidCash Access Approved", "CompleteOnboarding": "Complete Onboarding", "OnboardingcompleteYouarealmostthere": "Onboarding complete. You are almost there.", "ConnectBankAccount": "Connect Bank Account", "ExporttoQuickBooks": "Export to QuickBooks", "ExportingtoQuickBooksonlinePleasewait": "Exporting to QuickBooks online. Please wait...", "Export": "Export", "Allstatus": "All status", "AllDepartments": "All Departments", "Allcards": "All cards", "WithoutReceipt": "Without Receipt", "WithReceipt": "With Receipt", "Receipts": "Receipts", "Daily": "Daily", "Weekly": "Weekly", "Monthly": "Monthly", "Yearly": "Yearly", "AllTime": "All Time", "Fetchingdata": "Fetching data", "Searchbyemployeecardnameorlast4digitsofcardnumber": "Search by user, card name or last 4 digits of card number", "Nodata": "No data", "ManageCards": "Manage Cards", "Cards": "Cards", "Transactions": "Transactions", "Currentbalance": "Current balance", "Availablebalance": "Available balance", "Statementbalance": "Statement balance", "Lastpayment": "Last payment", "TransferFunds": "Transfer Funds", "AddCard": "Add Card", "ConnectQuickBooksOnline": "Connect QuickBooks Online", "DisconnectQuickBooksOnline": "Disconnect QuickBooks Online", "User": "User", "Department": "Department", "Card": "Card", "Utilization": "Utilization", "Status": "Status", "View": "View", "Suspend": "Suspend", "Activate": "Activate", "Cancel": "Cancel", "LostorStolen": "Lost or Stolen", "YouarealmostreadytomodernizeyourpaymentmanagementusingFluidPay": " You are almost ready to modernize your payment management using FluidCash!", "WeareverifyingtheinformationandwewillsoonactivateFluidPayforyoutypicallywithin5-7businessdays": " We are verifying the information, and we will soon activate FluidCash for you (typically within 5-7 business days).", "FluidPayASmarterWayToManagePayments": "FluidCash - A Smarter Way To Manage Payments", "FluidPayisamodernpaymentmanagementsolution.WithFluidPayyouremployeeswillgetasimplewaytopayforwhatsneededwhilefinanceteamsgetoneplacetobettercontrolmanageandtrackbusinessspend.Thebuilt-incontrolsonsmartcardsensurespendcompliancereal-timespendvisibilityandautomatedexpensereporting.Thissolutionisdesignedtoproviderefreshingexperiencewhetheremployeespayfortravelroutineexpensesspotpurchasesorsoftwaresubscriptions": "FluidCash is a modern payment management solution. With FluidCash, your employees will get a simple way to pay for what’s needed while finance teams get one place to better control, manage and track business spend. The built-in controls on smart cards ensure spend compliance, real-time spend visibility, and automated expense reporting. This solution is designed to provide refreshing experience whether employees pay for travel, routine expenses, spot purchases, or software subscriptions.", "FluidPayislimitedaccessfeatureToenableFluidPayforyourcompanyrequesttheaccessbelow": "FluidCash is limited access feature. To enable FluidCash for your company, request the access below.", "YourrequestissubmittedOurteamwillreachouttoyoushortly": "Your request is submitted. Our team will reach out to you shortly.", "Qbosyncerror": "Qbo sync error", "Transferfunds": "Transfer funds", "Transferfrom": "Transfer from", "Amount": "Amount", "Morethanzerorequired": "More than zero required.", "Verificationcodesentto": "Verification code (sent to", "ResendCode": "Resend Code", "ReviewTransfer": "Review Transfer", "ConfirmTransfer": "Confirm Transfer", "Wait": "Wait", "Create": "Create", "details": "details", "Edit": "Edit", "Issuecardto": "Issue card to", "Carddescription": "Card description", "Maxmium40lettersrequiredforCardDescription.": "Maxmium 40 letters required for Card Description.", "Expensecategory": "Expense category", "Transactionlimit": "Transaction limit", "Merchantrestrictions": "Merchant restrictions", "optional": "optional", "CancelCard": "Cancel Card", "Cancelcardfor": "<PERSON><PERSON> card for", "Acancelledcardwillrejectfuturetransactionsandcannotbereactivated": "A cancelled card will reject future transactions and cannot be reactivated.", "Pleaseselectreasonforcancelingcard": "Please select reason for canceling card", "None": "None", "Lost": "Lost", "Stolen": "<PERSON><PERSON><PERSON>", "CANCELCARD": "CANCEL CARD", "NEVERMIND": "NEVERMIND", "Accountlinkedsuccessfully": "Account linked successfully !!!", "Fundstransferredsuccessfully": "Funds transferred successfully !!!", "Unabletoverifyapprovalcode.Pleasetryagainlate": "Unable to verify approval code. Please try again late !!!", "Cardfor": "Card for", "is": "is", "Noexpensetransactionswereselected": "No expense transactions were selected !!!", "Pleaseremovethedeclinedtransactionsforexport": "Please remove the declined transactions for export !!!", "Notransactionsareselected": "No transactions are selected !!!", "ExportCompleteFewtransactionsdidnotupload.Seetheerrormessageoneachtransactionfordetails": "Export Complete, Few transactions did not upload. See the error message on each transaction for details !!!", "ExportComplete": "Export Complete !!!", "Problemwhiledownloadingthefile": "Problem while downloading the file."}, "setting": {"Settings": "Settings", "Relativepricelimitisupto": "Relative price limit is upto ", "morefromthelowestlogicalfareinaparticularcategory": " more from the lowest logical fare in a particular category", "ofthelowestlogicalfareinaparticularcategory": " of the lowest logical fare in a particular category", "Pleaseselectatleast1adulttravelerwithagegreaterthan16": "Please select at least 1 adult traveler with age greater than 16", "Totalperroom": "Total per room", "Maximumclassallowance": "Maximum class allowance:", "Nonrefundableroomratesarerestricted": "Nonrefundable room rates are restricted", "Vehicletypeallowance": "Vehicle type allowance:", "Absolutepricelimitperdayincludingtaxesfees": "Absolute price limit (per day including taxes & fees):", "ReviewYourPolicy": "Review Your Policy", "Basefaresarerestricted": "Base fares are restricted", "Relativepricelimitforcurrenttravel": "Relative price limit for current travel:", "Absolutepricelimitonewayairfare": "Absolute price limit (one-way airfare):", "Maximumclassallowedonlyifflightdurationismorethan": "Maximum class allowed only if flight duration is more than", "eticket": "eticket: ", "Thetravelernameontheairlinecreditis": "The traveler name on the airline credit is ", "anddoesntmatchtheinformationyouveprovided": " and doesn't match the information you've provided.", "Return": "Return", "HowtoconfigureapprovalnotificationsusingMicrosoftTeams": "How to configure approval notifications using Microsoft Teams?", "Company": "Company", "Bookforotherssetting": "Book for others settings", "MyInvites": "My Invites", "Invitationaccepted": "Invitation accepted", "Partiallybooked": "Partially booked", "Booked": "Booked", "ifyouknowthehotelwhereeveryonewillstay orshouldstaythenpleaseentertheexactlocationofthehotel": "if you know the  hotel where everyone will stay (or should stay), then please enter the exact location of the hotel.", "NobookingsFound": "No bookings found", "BookingList": "Booking List", "Department": "Department", "Email": "Email", "EventStatus": "Event Status", "CostofBooking": "Cost of Booking", "ReturningDepartureDate": "Returning Departure Date", "OutgoingArrivalAirport": "Outgoing Arrival Airport", "OutgoingArrivalDate": "Outgoing Arrival Date", "Outgoingarrivalflightnumber": "Outgoing arrival flight number", "Returningdepartureairport": "Returning departure airport", "Returningdepartureflightnumber": "Returning departure flight number", "Bookings": "Bookings", "Thisbookingisnotaccordingtoallowedeventparameters": "An event booking should be done for the event destination within allowed travel window for invited travelers only", "YouwilllosethistripifyoumoveawayAreyousureyourwanttoexitthisEventbooking?": "You will lose this trip if you move away. Are you sure you want to exit this event booking", "Bookingdone": "Booking done", "hasinvitedyoutojoin": " has invited you to join", "Scheduleflighttoarrive": "Schedule flight to arrive by", "Scheduleflighttodepartafter": "Schedule flight to depart after", "EventdeletedSucessfull": "Event deleted successfully", "Eventstarts": "Event starts", "Eventends": "Event ends", "Invitationpending": "Invitation pending", "Imdonebooking": "I've booked travel outside Routespring", "Setarrivalandreturndatesforparticipantsandsuggestarrivalandreturntimes": "Set arrival and return dates for participants and suggest arrival and return times", "DeclineIvebookedtraveloutsideRoutespring": "Decline: I've booked travel outside Routespring", "Declined": "Decline", "Decline": "Declined", "Atendeelist": "Attendee list", "Attendeeaddedsucessfully": "<PERSON><PERSON><PERSON> added successfully", "Attendeedeletedsucessfully": "<PERSON><PERSON><PERSON> deleted successfully", "Attendeeinvitedsucessfully": "Attendee invited successfully", "Attendeeupdatedsucessfully": "Attend<PERSON> updated successfully", "Invite": "Invite", "Reinvite": "Re-invite", "Eventsetting": "Event settings", "selectapprover": "Select approver", "participant": "Participant", "Departingfrom": "Departing from", "Status": "Status", "participants": "Participants", "1AM": "1AM", "OverrideUserPaymentMethodsforthisEvent": "Override User’s Payment Methods for this Event", "OverrideApproverforthisEvent": "Override Approver for this Event", "Travelpolicy": "Travel policy", "OverrideUsersTravelPolicyforthisEvent": "Override User’s Travel Policy for this Event", "Travelwindow": "Travel window", "Othersetting": "Other settings", "2AM": "2AM", "Departure": "Departure", "DeleteEvent": "Delete event", "Suggestlatestarrivaltime": "Suggest latest arrival time to destination", "Arrival": "Arrival", "DestinationAirport": "Destination Airport", "AirTravelDates": "Air Travel Dates", "Suggestearliestreturningtime": "Suggest earliest returning time from destination", "Saveandcontinue": "Save and continue", "Createanevent": "Create an event", "addEvents": "add Events", "SelectAirports": "Select Airports", "EventupdatedSucessfull": "Event updated successfully", "EventaddedSucessfull": "Event added successfully", "EventName": "Event Name", "EventLocation": "Event Location", "Startdate": "Start date", "Enddate": "End date", "Organizegrouptravelformeetingsoffsitesoreventswewillsendinvitestoyourtravelersshowyoutheirbookingprogressandkeepyouinformedofestimateandactualbookingcost": "Organize group travel for meetings, offsites or events. We'll send invites to your travelers, show you their booking progress, and keep you informed of estimated and actual booking costs.", "GroupTravels": "Events", "Type": "Type", "SingleSignOn": "Single Sign On", "incltaxesandfees": "Total incl taxes and fees", "or": "or", "about": " about ", "NoVendorspecificconfigurationfound": "No Vendor specific configuration found", "updateCarRentalSetting": "Update Car Rental Settings", "orcarrentalsettings": "car rental settings", "DeleteCarRentalSetting": "Delete Car Rental Settings", "AddCarRental": "Add Car Rental", "AddUATPCard": "Add Airline UATP Card", "AddCarRentalSetting": "Add Car Rental Settings", "SelectCarRentalCompany": "Select Car Rental Company", "SaveandAddanother": "Save and Add another", "Permitcarrentalin": "Permit car rental in", "UATPorBillingCode": "UATP or Billing Code", "AccoutorDiscountCode": "Accout or Discount Code", "Addsetting": "Add settings", "Notificationsettings": "(Notification settings)", "BalanceNotificationSetting": "Balance Notification Settings", "Thresholdamount": "Threshold amount:", "Separatemultipleemailsaddresseswithcommas": "Separate multiple email addresses with commas.", "Enteremail": "Enter email:", "Sendnotificationto": "Send notification to:", "Sendduplicatebookingsnotificationto": "Send duplicate bookings notification to:", "Whentheaccountbalancefallsbelowtheconfiguredthresholdanemailnotificationwillbesent": "When the account balance falls below the configured threshold, an email notification will be sent.", "Corporatecredits": "Corporate credits", "Wireinstruction": "Wire instructions", "Downloadwireinstruction": "Download wire instructions", "Typicalcreditgivenisequalto2oftheaveragemonthlytravelspendwithRoutespringoverlastmonths": "Typical credit given is equal to 1% of the average monthly travel spend with Routespring over last 12 months", "Pleaseenterapickuplocation": "Please enter a pickup location", "Pleaseenteranoriginairport": "Please enter an origin airport", "Pleaseenteradestination": "Please enter a destination", "DeleteAirlineCorporateLoyaltyProgram": "Delete Airline Corporate Loyalty Program", "SelectAirlineLoyalityProgram": "Select Airline Loyalty Program", "AddLoyalityProgram": "Add Loyalty Program", "AirlineCorporateLoyalityProgram": "Airline Corporate Loyalty Program", "Enableall": "Enable all", "Disableall": "Disable all", "abouttravelpolicyconfigurationsettings": "about travel policy configuration settings.", "UATPCardNUmber": "UATP Card Number", "Entertourcode": "Enter tour code", "AddTourCode": "Add Tour Code", "VendorProgram": "Vendor Programs", "DeleteAirlineTourCodes": "Delete Airline Tour Codes", "Airlinestourcodes": "Airline tour codes", "Pleaseselectaairline": "Please select a airline", "PermitCardin": "Permit card usage in", "Airlines": "Airlines", "UATP": "UATP", "DeleteUatpcard": "Delete UATP card", "UpdateUATPcard": "Update UATP card", "AccoutCode": "Account Code", "Departmentswithcardusagepermission": "Departments with card usage permission", "PleaseenterPassPlusAccountCode": "Please enter PassPlus Account Code", "DoyoualsohaveUnitedPassPlusaccount": "Do you also have United PassPlus account?", "EnteryourPassplusaccountcode": "Enter your PassPlus account code", "Pleaseselectadepartment": "Please select a department", "aboutUATPpayments": "UATP payment method", "AddaUATPCard": "Add a UATP Card", "AddaNewUATPCard": "Add a New UATP Card", "aboutTripTags": "about Custom Field.", "ConfigureTripTagsforyourreportingneeds": "Configure Custom Fields for your reporting needs.", "AddTripTag": "+Add  Custom Field", "SynctransactionsfromthiscardtoQuickBooks": "Sync transactions from this card to QuickBooks", "Selectthecreditcardaccountinwhichyouwanttransactionstosyncto": "Select the credit card account in which you want transactions to sync to.", "SelectthedefaultexpensecategoryunderwhichRoutespringtransactionsshouldbesynced": "Select the default expense category under which Routespring booking transactions should be synced", "Balance": "Balance:", "None": "None", "EnterwebhookURL": "Enter webhook URL", "CongratulationsYourQuickBooksOnlineisnowsuccessfullyconnectedAnyfuturetransactionswillnowbeautomaticallysyncedtoyourQuickBooksOnlineaccount": "Congratulations! Your QuickBooks Online is now successfully connected. Any future transactions will now be automatically synced to your QuickBooks Online account", "AhSomethingwentwrongandyourQuickBooksOnlineaccountfailedtoconnectPleasetryagainoremailusattriproutespringcom": "Ah! Something went wrong and your QuickBooks Online account failed to connect. Please try again or email <NAME_EMAIL>.", "Donotsynctransactionsfromthiscard": "Do not sync transactions from this card", "Pleaseselectaquickbooksaccount": "Please select a quickbooks account", "Paymentmethod": "Payment method", "QuickBooksOnlineAccountMapping": "QuickBooks Online Account Mapping", "YoucanmaptransactionsfromRoutespringspaymentmethodwithspecificaccountslistedinyourQuickBooksOnlineaccount": "You can map transactions from Routespring’s payment method(s) with specific accounts listed in your QuickBooks Online account.", "SynctransactionsfromthiscardtobelowaccountinQuickBooks": "Sync transactions from this card to below account in QuickBooks:", "YoucangotoIntegrationsAccountingIntegrationsQuickBookstocompleteintegrationanytimelater": "You can go to Integrations > Accounting Integrations > QuickBooks to complete integration anytime later.", "DoyouwanttoautomaticallysyncyourcorporatecardstransactionswithRoutespringtoyourQuickBooksIfyeswerecommendyoutocompleteQuickBooksintegrationbeforeaddingcorporatecards": "Do you want to automatically sync your corporate cards transactions with Routespring to your QuickBooks? If yes, we recommend you to complete QuickBooks integration before adding corporate cards.", "GoToQuickBooksIntegration": "Go To QuickBooks Integration", "MaybeLater": "Maybe Later", "YesdisconnectQuickBooksOnline": "Yes, disconnect QuickBooks Online", "AreyousureyouwanttodisconnectyourQuickBooksOnlineintegrationOncedisconnectedyourfutureRoutespringtransactionswillnotsynctoyourQuickBooksOnlineaccount": "Are you sure you want to disconnect your QuickBooks Online integration? Once disconnected, your future Routespring transactions will not sync to your QuickBooks Online account.", "QuickBooksOnline": "QuickBooks Online", "ConnectyouraccountingtoolwithRoutespringandkeepyouraccountingdatainsync": "Connect your accounting tool with Routespring and keep your accounting data in sync.", "UpdatesfromRoutespring": "Updates from Routespring", "GetstartedwithRoutespring": "Get started with Routespring", "ApplyforTravelNowPayLater": "Application for Travel Now, Pay Later", "Thankyouforsubmittingyourapplication.Ourfinanceteamwillevaluateandproposecreditpaymenttermsbasedonthefinancialhistory.Youwillhearbackfromuswithinbusinessdays": "Thank you for submitting your application. Our finance team will evaluate and propose credit & payment terms based on the financial history. You will hear back from us within 7-9 business days.", "CompanysLegalName": "Company's Legal Name", "Pleaseprovideusbelowinformationanduploadrelevantfiles": "Please provide us below information and upload relevant files.", "ApplicationSubmitSuccessfully": "Application Submit Successfully !!!", "Pleaseuploadbelowrecommendedfiles": "Please upload below recommended files", "CurrentandpreviousyearcompanyPLandBalancesheetbrokenoutatleastquarterly": "Current and previous year company P&L and Balance sheet (broken out atleast quarterly)", "Latestmonthsofbankstatements": "Latest 6 months of bank statements", "Latsmonthstravelspendingreportsifavailble": "Last 12 months' travel spending reports (if available)", "Projectedtravelspendforthenextmonthsbrokenoutbymonthlyflightshotelsandcarrentals": "Projected travel spending for the next 6 months (broken out by monthly spending on flights, hotels, and car rentals)", "EmployerIdentificationNumber": "Employer Identification Number", "CompanyAddress": "Company Address", "EIN": "EIN", "UploadFiles": "Upload Files", "SubmitApplication": "Submit Application", "Global": "Global", "Updatecard": "Update card", "UploadedDocuments": "Uploaded Documents", "Applycompanysdefaulttoalldepartments": "Apply company's default to all departments", "UpdateComapanysDefaultCreditCard": "Update Company's Default Credit Card", "Onlychangethepaymentmethodforselecteddepartments": "Only change the payment method for selected departments", "Nopaymentmethodselectedforanydepartment": "No payment method selected for any department", "AddNewcard": "Add New Card", "PaymentMethodUpdating": "Payment method updating !!!. Please wait...", "PaymentMethodUpdatedSucessfull": "Payment method  updated successfully !!!", "BankAccounts": "Bank Accounts", "ConnectedBankAccounts": "Connected Bank Accounts", "ConnectBank": "Connect Bank", "UploaddocumentstoapplyforTravelNowPayLater": "Upload documents to apply for Travel Now Pay Later", "Pleaseuploadyourcompanybankaccountslatest6monthsstatementsCompanybalancesheetandCompanyPLstatement": "Please upload your company bank account's latest 6 months statements, Company balance sheet and Company P&L statement.", "Upload": "Upload", "SelectCard": "Select card", "ileuploadedsuccessfully": "File uploaded successfully !!!", "Dailycredit": "Daily credit", "Availablebalance": "Available balance", "aboutcentralpaymentmethods": " about central payment methods", "Paymenttype": "Payment type", "Primary": "Primary", "Backup": "Backup", "Travelnowpaylater": "Travel now, pay later", "Corporatecards": "Corporate cards", "Dailybankdebits": "Daily bank debits", "Prepayment": "Prepayment", "LearnMore": "Learn more", "ViewEditspaymentmethods": "View/Edit payment methods", "VendorSpecific": "Vendor Specific", "Guideforpaymentmethods": "Guide for payment methods", "Configureprimaryandbackuppaymentmethods": "Configure default primary and backup payment methods for the company", "Notifications": "Notifications", "Expenses": "Expenses", "TagSavedSuccessfully": "Tag Saved Successfully!!!", "Lowcostcarriers": "Low cost carriers", "General": "General", "Integrations": "Integrations", "ProjectTags": "Custom Fields", "CompanyName": "Company Name", "CorporateCreditCard": "Corporate Credit Card", "Remove": "Remove", "Disablelowcostcarriers": "Disable low cost carriers", "Disablinglowcostcarrierswillremovethemfromflightsoptions": "Disabling low cost carriers will remove them from flights options.", "Listoflowcostcarriers": "List of low cost carriers", "ExpenseIntegrations": "Expense Integrations", "EnableintegrationwithyourexpensemanagementtooltoallowyouremployeestoautomaticallyuploadallRoutespringreceiptsdirectlytotheirexpenseaccounts": "Enable integration with your expense management tool to allow your users to automatically upload all Routespring receipts directly to their expense accounts.", "Expenseproviders": "Expense providers", "EnableSlackIntegration": "Enable <PERSON>ck <PERSON>", "WhenenabledyouradminscanconnecttheirRoutespringaccountwithSlackProfileAccountSlackIntegrationOnceconnectedadminscanprocesstravelapprovalsstraightfromSlack": "When enabled, your admins can connect their Routespring account with Slack (Profile > Account > Slack Integration). Once connected, admins can process travel approvals straight from Slack.", "EnableMSTeamsIntegration": "Enable MSTeams Integration", "on": "on", "off": "off", "PleaseprovidetheURLoftheincomingwebhookforyourchannelwhereyouwishtoreceivethesenotifications": "Create a channel in Microsoft Teams where you wish to receive the approval notifications and add your travel approvers to this channel. In order to start receiving these notifications, turn on the integration and add the URL of the incoming webhook for this channel.", "HowtogetincomingwebhookURL": "How to get incoming webhook URL", "EnablethisintegrationtosyncalltheFuildPaytransactionsinyouraccountingsoftwarebycorrectlymappingthemwithyouraccountingcodes": "Enable this integration to sync all the FuildPay transactions in your accounting software by correctly mapping them with your accounting codes.", "ProjectTagsallowyoutocategorizetravelspendbasedontheprojectseventsoranyotherreportingneedsYourtravelersandadminscanusethesetagsforanytravelbookingtoappropriatelyreportthetravelspend": "Project Tags allow you to categorize travel spend based on the projects, events or any other reporting needs. Your travelers and admins can use these tags for any travel booking to appropriately report the travel spend.", "Tagname": "Tag name", "Save": "Save", "Update": "Update", "Cancel": "Cancel", "Airlinecode": "Airline code", "Airlinename": "Airline name", "Deletecreditcard": "Delete credit card", "Areyousurethatyouwanttoproceed": "Are you sure that you want to proceed?", "Thiscard": "This card", "isbeingusedascentralpaymentmethodforfollowingdepartmentsPleaseupdatebeforedeleting": "is being used as central payment method for following department(s). Please update before deleting", "Departments": "Departments", "CreditCard": "Credit Card", "Confirm&Delete": "Confirm & Delete", "Howtocreatewebhook": "How to create webhook", "CreateIncomingWebhook": "Create Incoming Webhook", "ToaddanIncomingWebhooktoaTeamschannel": "To add an Incoming Webhook to a Teams channel", "Gotothechannelwhereyouwanttoaddthewebhookandselect": "Go to the channel where you want to add the webhook and select", "Moreoptions": "Moreoptions", "fromthetopnavigationbar": "from the top navigation bar.", "fromthedropdownmenu": "from the dropdown menu", "Select": "Select", "Connectors": "Connectors", "Searchfor": "Searchfor", "IncomingWebhook": "Incoming Webhook", "andselect": "and select", "Add": "Add", "Configure": "Configure", "provideanameanduploadanimageforyourwebhookifrequired": "provide a name, and upload an image for your webhook if required", "ThedialogwindowpresentsauniqueURLthatmapstothechannel.CopyandsavethewebhookURLtosendinformationtoMicrosoftTeamsandselect": "The dialog window presents a unique URL that maps to the channel. Copy and save the webhook URL, to send information to Microsoft Teams and select", "Done": "Done", "Deletetag": "Delete Custom Field", "TripTagenabled": "Custom Field enabled", "TagSetUpdatedsuccessfully": "Custom Group Updated successfully", "TripTagUpdatedsuccessfully": "Custom Field Updated successfully", "Triptagdeletedsuccessfully": "Custom Field deleted successfully", "TripTagdisabled": " Custom Field disabled", "Apologiessomethingwentwrongwecouldntreportdata.Pleasetryagainlater": "Apologies! something went wrong we couldn't report data. Please try again later", "TripTagaddedsuccessfully": "Custom Field added successfully!!!", "addedsuccessfully": "added successfully!!!", "PleaseEnterCompanyname": "Please enter company name !!!", "Updatedsuccessfully": "Updated successfully!!!", "Tagdeletedsuccessfully": "Tag deleted successfully!!!", "Settingssavedsuccessfully": "Settings saved successfully !!", "Apologiessomethingwentwrongwecouldntprocessrequest.Pleasetryagainlaterorcontactsupport": "Apologies! something went wrong, we couldn't process the request. Please try again later or contact customer support", "Pleaseselectcreditcardforalldepartments": "Please select credit card for all departments"}}