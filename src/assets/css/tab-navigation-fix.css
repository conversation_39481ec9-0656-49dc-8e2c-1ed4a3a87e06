/**
 * Tab Navigation Fix CSS
 * 
 * Ensures that focusable elements are properly styled and accessible
 * for keyboard navigation in both development and production builds.
 */

/* Ensure all focusable elements have proper focus indicators */
input:focus,
button:focus,
select:focus,
textarea:focus,
a:focus,
[tabindex]:focus,
[contenteditable]:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Ensure elements with tabindex=0 are focusable */
[tabindex="0"] {
    outline: none;
}

[tabindex="0"]:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Fix for elements that might have their focus styles overridden */
.tab-list-item:focus,
.tab-list-item a:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
    z-index: 1000;
}

/* Ensure modal and popup elements are properly focusable */
.modal:focus,
.modal-dialog:focus,
.modal-content:focus {
    outline: none;
}

.modal input:focus,
.modal button:focus,
.modal select:focus,
.modal textarea:focus,
.modal a:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Fix for dropdown and select elements */
.dropdown-menu:focus,
.dropdown-item:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Ensure custom components are focusable */
.card-div:focus,
.card-div-header:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Fix for airport selection and search components */
.airportItem:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
    background-color: #f8f9fa !important;
}

/* Ensure form controls are properly focusable */
.form-control:focus,
.form-select:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* Fix for custom styled buttons */
.btn:focus,
.button:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Ensure navigation elements are focusable */
.nav-link:focus,
.navbar-nav .nav-link:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Fix for search and filter components */
.search-input:focus,
.filter-input:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Accessibility improvements for screen readers */
.sr-only:focus {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.sr-only:focus:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: inherit !important;
    margin: inherit !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: inherit !important;
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Fix for elements that might be preventing focus */
.no-focus {
    pointer-events: none;
}

.no-focus:focus {
    outline: none !important;
}

/* Ensure tab order is logical */
[tabindex="-1"] {
    outline: none !important;
}

/* Fix for dynamic content that might not be focusable */
.dynamic-content input,
.dynamic-content button,
.dynamic-content select,
.dynamic-content textarea,
.dynamic-content a[href] {
    outline: none;
}

.dynamic-content input:focus,
.dynamic-content button:focus,
.dynamic-content select:focus,
.dynamic-content textarea:focus,
.dynamic-content a[href]:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    input:focus,
    button:focus,
    select:focus,
    textarea:focus,
    a:focus,
    [tabindex]:focus,
    [contenteditable]:focus {
        outline: 3px solid #000 !important;
        outline-offset: 2px !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *:focus {
        transition: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    input:focus,
    button:focus,
    select:focus,
    textarea:focus,
    a:focus,
    [tabindex]:focus,
    [contenteditable]:focus {
        outline: 2px solid #66b3ff !important;
        outline-offset: 2px !important;
    }
}
