var __apiBaseUrl__ = "https://beta.routespring.com/dev-api/";
//var __apiBaseUrl__ = "https://beta.routespring.com/dev-api";

var cc_ui_host = "https://beta.routespring.com";
var cc_ui_base_ctx = "gallop-cc-ui";
var developerMode = false;  
function getApiBaseUrl( apiName){
    if (window.location.host.indexOf('localhost') == -1) {
        switch (apiName) { // 
            case 'apiCompanyApprovalsList': 
            case 'apiForBookingHistory': 
            case 'apiForGraphData': 
            case 'apiChartsData': 
            case 'apiForTravelManagerManagement': 
            {
                return __apiBaseUrl__.replace('https://', 'https://reports');
            }
        }
    }
    if ((window.location.host.indexOf('localhost') > -1 && window.location.href.indexOf('developerMode') > -1) ){
        switch(apiName){
            case 'apiUserAccountUrlforSaveCard':
                    return 'https://beta.routespring.com/dev-api';
        }
    }
    return __apiBaseUrl__;
}