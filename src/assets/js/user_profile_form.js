function validateUserDetailsForm(thisObj) {
}
function validateKnownTravellerForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        rules: {
//            travellerNumber: {
//                required: true
//            }
        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });
    var isFormValid = $(formId).valid();

    if (isFormValid) {
        goToNextStep(thisObj);
        var shortInfoTemplate = `<div class="traveller-short-info-inner">${$('input[name="travellerNumber"]').val()}</div>`;
        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);

    } else {
        return false;
    }
    return false;
}

function validatePassportForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        rules: {
//            passportNumber: {
//                required: true
//            },
//            passportExpiryDate: {
//                required: true
//            },
//            nationality: {
//                required: true
//            },
//            country: {
//                required: true
//            }
        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });
    var isFormValid = $(formId).valid();

    if (isFormValid) {
        goToNextStep(thisObj);

        var passportNumber = $('input[name="passportNumber"]').val();
        var passportExpiryDate = convertDate($('input[name="passportExpiryDate"]').val());
        var country = $('[name="country"] option:selected').text();

        


        var shortInfoTemplate = `<div class="traveller-short-info-inner">
                                   ${shortInfoTemplate123("Number",passportNumber)}${shortInfoTemplate123("Exp",passportExpiryDate)}${shortInfoTemplate123("",country)}
                                </div>`;

        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
    } else {
        return false;
    }
    return false;
}

function shortInfoTemplate123(string,val){
   if(val == '' || val == 'undefined' || val == 'Invalid date' ||  val == null){
       return '';
   }
   else{
       if(string == ""){
           return `<span>${val}</span><span class="shortInfoSeperator">|</span>`;
       }

       else{
            return `<span>${string}-${val}</span><span class="shortInfoSeperator">|</span>`;
       }
   }
}


function submitFrequentFlyerForm(thisObj){
    if(validateFrequentFlyerForm(thisObj)){
        goToNextStep(thisObj);
        var infoList = [];
        if($(window).width() < 768){
            $(thisObj).closest('.card-div').find('.flyer').each(function(){
                infoList.push($(this).find('.flyer-airline').text());
            });
        }
        var airlineOption;
        var flyerNumber;
        $(thisObj).closest('.card-div').find('.airlineFlyerNumberDiv').each(function(){
            airlineOption = $(this).find('.airlineOptions  option:selected').text();
            flyerNumber = $(this).find('.flyerNumber').val();
            if(airlineOption != ''){
                infoList.push(`${airlineOption} | ${flyerNumber}`);
            }
        });
        var infoTemplate = infoList.join(', ');
        var shortInfoTemplate = `<div class="traveller-short-info-inner">${infoTemplate}</div>`;
        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
    }
    return false;
}



function validateFrequentFlyerForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });

//    $('#airlineOptions').rules("add", "required");
    $('#flyerNumber').rules("add", "required");
    var isFormValid = $(formId).valid();
    if (isFormValid) {
        return true;
    }
    else {
        return false;
    }
    return false;
}


function submitFrequentGuestForm(thisObj){
    if(validateFrequentGuestForm(thisObj)){
    goToNextStep(thisObj);
    var infoList = [];
    if($(window).width() < 768){
        $(thisObj).closest('.card-div').find('.flyer').each(function(){
            infoList.push($(this).find('.flyer-airline').text());
        });
    }
    var hotelOption;
    var guestNumber;
    $(thisObj).closest('.card-div').find('.hotelFlyerNumberDiv').each(function(){
        hotelOption = $(this).find('.hotelOptions  option:selected').text();
        guestNumber = $(this).find('.guestNumber').val();
        if(hotelOption != ''){
            infoList.push(`${hotelOption} | ${guestNumber}`);
        }
    });
    var infoTemplate = infoList.join(', ');
    var shortInfoTemplate = `<div class="traveller-short-info-inner">${infoTemplate}</div>`;
    $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
}
    return false;
}


function validateFrequentGuestForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });


//    $('#hotelOptions').rules("add", "required");
    $('#guestNumber').rules("add", "required");
    var isFormValid = $(formId).valid();

    if (isFormValid) {
        return true;

    } else {
        return false;
    }
    return false;
}

function validateAirlinePreferenceForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        rules: {
            prefferedAirlines: {
                checkbox: true

            }
        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });

    var isFormValid = $(formId).valid();

    if (isFormValid) {
        goToNextStep(thisObj);
        var shortInfoTemplate = `<div class="traveller-short-info-inner">${getSelectedAirlines()}</div>`;
        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);

    } else {
        return false;
    }
    return false;
}

function validateClassPreferenceForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        rules: {
            prefferedClass: {
                required: true
            },
            economyPreference: {
                required: true
            }
        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });
    var isFormValid = $(formId).valid();
    if (isFormValid) {
        goToNextStep(thisObj);

        var classList = [];
        $('input[name="prefferedClass"]:checked').each(function(){
            classList.push($(this).attr('data-value'));
        });


        var shortInfoTemplate = `<div class="traveller-short-info-inner">${classList.join(',')}</div>`;
        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
    } else {
        return false;
    }
    return false;
}

function validateSeatPreferenceForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        rules: {
            seatPreference: {
                required: true
            }
        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });
    var isFormValid = $(formId).valid();
    if (isFormValid) {
        goToNextStep(thisObj);
        var shortInfoTemplate = `<div class="traveller-short-info-inner">${$('input[name="seatPreference"]:checked').attr('data-value')}</div>`;
        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
    } else {
        return false;
    }
    return false;
}


function validateAirportSelectionForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        rules: {
            mapAddress: {
                required: true
            },
            preferredAirport: {
                required: true
            }
        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });
    var isFormValid = $(formId).valid();
    if (isFormValid) {
        goToNextStep(thisObj);
        var shortInfoTemplate = `<div class="traveller-short-info-inner">${$('input[name="mapAddress"]').val()}  |  ${$('input[name="preferredAirport"]:checked').val()}</div>`;
        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
        return false;
    } else {
        return false;
    }
    return false;
}

function validateHotelPreferenceForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        rules: {
            prefferedHotel: {
                checkbox: true
            },
            hotelRating: {
                checkbox: true
            }

        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });
    var isFormValid = $(formId).valid();
    if (isFormValid) {
        goToNextStep(thisObj);
        var shortInfoTemplate = `<div class="traveller-short-info-inner">${getSelectedHotels()}</div>`;
        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
    } else {
        return false;
    }
    return false;
}

function validateExpensifyEmail(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        rules: {
            expensifyEmail: {
                required: true,
                email: true
            }
        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });
    var isFormValid = $(formId).valid();
    if (isFormValid) {
        goToNextStep(thisObj);
        var shortInfoTemplate = `<div class="traveller-short-info-inner">${$('input[name="expensifyEmail"]').val()}</div>`;
        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
        return false;
    } else {
        return false;
    }
    return false;
}

function validateChangePasswordForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        rules: {
            oldPassword: {
                required: true,
            },
            newPassword: {
                required: true,
            },
            confirmPassword: {
                required: true,
                equalTo: newPassword
            }
        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });
    var isFormValid = $(formId).valid();
    if (isFormValid) {
        goToNextStep(thisObj);
        var shortInfoTemplate = `<div class="traveller-short-info-inner shortInfoPrimary flexShortInfoTemplate"><img class="link-icon" src="images/green-tick.png" /><span class="link-text">Password changed successfully</span</div>`;
        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
    } else {
        return false;
    }
    return false;
}

function validateSecondaryEmailForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');
    $(formId).validate({
        focusInvalid: true,
        rules: {
//            primaryEmail: {
//                required: true,
//                email: true
//            },
//            secEmail: {
//                required: true,
//                email: true
//            }
        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });

    $('#primaryEmail').rules("add", {
        required:true,
        email:true
    });
    $(`#secEmail${secondaryEmailCount}`).rules("add", {
      required:true,
      email:true
  });

    var isFormValid = $(formId).valid();
    if (isFormValid) {
        if($('.addPrimaryEmail .input-box').hasClass('emailEdit')){
            userPrimaryEmail = $('.addPrimaryEmail .input-box .input-textfield').val();
            $('.addPrimaryEmail').html(primaryEmailFilledtemplate(userPrimaryEmail));
        }




//        if($('.addSecondaryEmail .input-box').hasClass('emailEdit')){
//            userSecondaryEmail = $('.addSecondaryEmail .input-box .input-textfield').val();
//            $('.addSecondaryEmail').html(secondaryEmailFilledtemplate(userSecondaryEmail));
//        }

            $('.addSecondaryEmail').each(function(){
                if($(this).find('.input-box').hasClass('emailEdit')){
                    var userSecondaryEmail = $(this).find('.input-box .input-textfield').val();

                    if(userSecondaryEmail != ''){
                        $(this).html(secondaryEmailFilledtemplate(userSecondaryEmail));
                    }
                    else{
                        $(this).remove();
                    }

                }
            });

        $(thisObj).closest('form').find('.card-div-footer').hide();

    } else {
        return false;
    }
    return false;
}

function validateAddTravellerForm(thisObj) {
    var formId = "#" + $(thisObj).closest('form').attr('id');

    $(thisObj).closest('form').find('.accord').each(function () {
        $(this).addClass('active');
        $(this).find('.accord-content').slideDown();
    });


    $(formId).validate({
        focusInvalid: true,
        rules: {
            title: {
                required: true
            },
            firstName: {
                required: true
            },
            dob: {
                required: true
            },
            gender: {
                required: true
            },

            email: {
                required: true,
                email: true
            },
//            travellerNumber: {
//                required: true
//            },
//            passportNumber: {
//                required: true
//            },
//            passportExpiryDate: {
//                required: true
//            },
//            nationality: {
//                required: true
//            },
//            country: {
//                required: true
//            }

        },
        messages: {

        },
        errorPlacement: function (error, element) {
            errorPlacement(error, element);
        }
    });

    $(formId + " select").on('change',function(){
        $(this).valid();
    });

    var isFormValid = $(formId).valid();
    if (isFormValid) {
        $(thisObj).closest('.card-div').removeClass('shadow').addClass('filled').removeClass('active').find('.card-div-body').slideUp();
        var shortInfoTemplate = `<div class="traveller-short-info-inner">${$(formId + ' input[name="gender"]:checked').val()} | DOB-${convertDate($(formId + ' [name="dob"]').val())}</div>`;
        $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
        var personTemplate = `${$(formId + ' [name="title"]').val() + " " + $(formId + ' input[name="firstName"]').val() + " " + $(formId + ' input[name="middleName"]').val() + " " + $(formId + ' input[name="lastName"]').val()}`;
        $(formId).find('.card-div-header h3').html(personTemplate);
        var cardLength = $('.addTravellerContent .personalDetailsCard').length;

        if ($('#addAnotherTravellerDiv').length <= 0) {
            $('.addTravellerContent').append(addNewTravellerTemplate());
        }
//
        else{
            $('#addAnotherTravellerDiv').show();
        }

    } else {
        return false;
    }
    return false;
}


function addPaymentMethod(thisObj){
    goToNextStep(thisObj);
    var shortInfoTemplate = `<div class="traveller-short-info-inner paymentMethodAddition"><img class="link-icon" src="images/ae.png" /><span class="link-text">AMEX-2200</span</div>`;
    $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
}


function errorPlacement(error, element) {
    var placement = element.closest('.input-box, .checkbox-div, .checkbox-radio-container');
    if (!placement.get(0)) {
        placement = element;
    }
    if (error.text() !== '') {
        placement.append(error);
    }
}

$.validator.addMethod("checkbox", function (value, elem, param) {
    return $("input[name='" + elem.name + "'][type='checkbox']:checkbox:checked").length > 0;
}, "Atleast one selection is required");

//here modify the current email validation method of jQuery
$.validator.methods.email = function (value, element) {
    return this.optional(element) || /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(value);
}
