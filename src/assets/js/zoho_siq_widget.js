window._STATICURLS = ["js.zohocdn.com", "css.zohocdn.com", "https://us4-files.zohopublic.com", "salesiq.zohopublic.com", "salesiq.zoho.com", "salesiq.zohopublic.com"]; NEW_STATIC_URLS = [["https://js.zohocdn.com/salesiq/RESOURCE_BUNDLES/embedcw/js/embed.pWOohYG7npfnPhbNF3u1_s4s1mePNbZiucypLnMaDDVloDPuFPxKXAVjPunLIzo1.js", "https://js.zohocdn.com/salesiq/RESOURCE_BUNDLES/embedcw/js/chunk-vendors.6B_hUyu0L4M7ZLFGC5MfMzm6E0xrq_UezAt_59bon9WOnev9t6msfIJS92JENLC5.js", "https://js.zohocdn.com/salesiq/RESOURCE_BUNDLES/embedcw/js/embed~rtl.ACavnd0RoRkEZibm0ciWW5bEmZ0PEK-zQQWH3Wmp7QzvusNm3Q708jF-iMjjLzxg.js"], ["https://js.zohocdn.com/salesiq/RESOURCE_BUNDLES/embedcw/js/embed~modern.v6hwNgnAmYmBwK67n3Z7jBxN_k0XE6HSbIkHYl_gVTaq4CMw-R_gaEzErEr3UyQJ.js", "https://js.zohocdn.com/salesiq/RESOURCE_BUNDLES/embedcw/js/chunk-vendors~modern.x1ldsJu61RC-5eAXjlVUAYHJgd3Aa0lXAyepiMq49zHZNvIlj-d86SIoKWQaMyR3.js", "https://js.zohocdn.com/salesiq/RESOURCE_BUNDLES/embedcw/js/embed~modern~rtl.2JXFTFVgeY3lTEyvP4-GH3AHvx7NfjhVU4J5oDK_yqWlKXgXTHOFgu9ZbH4viD9s.js"], ["https://css.zohocdn.com/salesiq/RESOURCE_BUNDLES/embedcw/css/embed.YQSFl800c7i5z2Gdw_BOwgKhv_x9oG4yno3OsPsO23uS9oCaSwm8R-0PVZo-g6nZ.css", "https://css.zohocdn.com/salesiq/RESOURCE_BUNDLES/embedcw/css/embed~rtl.KyUFksH5awsa5rEckGRO4m50dg9KYDWuqKzUVu8u3haw5Gk0boHL-06NgUZs6MR0.css"]]; _STATIC_URL = 'https://static.zohocdn.com'; _NEW_MEDIARTC_URLS = ["https://js.zohocdn.com/salesiq/js/embedmedia-rtc-new_ith_NFAvS8fb3OpSses5cWd-KpUY54mKjkoQkKoqF2J3ykG-aE4rzdDBuNCGj3sY_.js", "https://js.zohocdn.com/salesiq/thirdparty/js/jquery.min.js"]; var $ZSIQLSDB = $ZSIQLSDB || function () { var t = function (t) { try { return window[t].setItem("test", "1"), window[t].removeItem("test"), !!window[t] } catch (t) { return !1 } }, e = t("sessionStorage"), o = t("localStorage"); return { set: function (e, o, i) { if ($ZSIQLSDB.isLocalStorageAvail()) try { this.storeInLocalStorage(e, o, i) } catch (t) { $ZSIQCookie.set(e, $ZSIQLSDB.toString(o), i) } else $ZSIQCookie.set(e, $ZSIQLSDB.toString(o), i) }, storeInLocalStorage: function (t, e, o) { var i = JSON.parse(localStorage.siqlsdb || "{}"); o && (e.ttl = o, e.time = (new Date).getTime()), e = $ZSIQLSDB.toString(e), i[t] = e, localStorage.siqlsdb = JSON.stringify(i) }, isLocalStorageAvail: function () { return o }, get: function (t) { return $ZSIQLSDB.isLocalStorageAvail() && $ZSIQLSDB.getFromLocalStorage(t) || $ZSIQUtil.parseToJSON($ZSIQCookie.get(t)) }, isSessionStorageAvail: function () { return e }, storeInSession: function (t, e) { if (!this.isSessionStorageAvail()) return !1; var o = $ZSIQUtil.parseToJSON(sessionStorage.utsdb || "{}"); return window._ZSIQ_RESET_ESSENTIAL && (o = $ZSIQUtil.parseToJSON(o)), o[t] = e, sessionStorage.utsdb = JSON.stringify(o), !0 }, getFromSession: function (t) { if (this.isSessionStorageAvail()) return $ZSIQUtil.parseToJSON(sessionStorage.utsdb || "{}")[t] }, getFromLocalStorage: function (e) { var o = $ZSIQUtil.parseToJSON(localStorage.siqlsdb || "{}")[e]; if (!o) return null; if ("ZLDPERSONALIZE" === e) return o; try { if ((o = $ZSIQUtil.parseToJSON(o)).ttl && o.ttl + o.time < (new Date).getTime()) return $ZSIQLSDB.remove(e), null } catch (t) { LSDebugger.postDebugInfo("Value: " + o + "Key" + e, null, null, !0) } return o }, remove: function (e, t) { var o = t && $zohosq._domain; if ($ZSIQLSDB.isLocalStorageAvail() && !o) try { var i = $ZSIQUtil.parseToJSON(localStorage.siqlsdb || "{}"); i[e] && delete i[e], localStorage.siqlsdb = JSON.stringify(i) } catch (t) { $ZSIQCookie.remove(e) } else $ZSIQCookie.remove(e, $zohosq._domain) }, removeAllFAQValuesFromSession: function (t) { if (this.isSessionStorageAvail()) try { var e = JSON.parse(sessionStorage.utsdb || "{}"); for (var t in e) e.hasOwnProperty(t) && (t.startsWith("article_") || t.startsWith("faq_")) && delete e[t]; sessionStorage.utsdb = JSON.stringify(e) } catch (t) { } }, removeSessionStore: function (t) { if (this.isSessionStorageAvail()) try { var e = JSON.parse(sessionStorage.utsdb || "{}"); e[t] && (delete e[t], sessionStorage.utsdb = JSON.stringify(e)) } catch (t) { } }, toString: function (e) { if (!e) return e; if ("string" == typeof e) return e; try { return t = e, Object.toJSON ? Object.toJSON(t) : JSON.stringify(t) } catch (t) { var o, i = []; for (var r in e) o = e[r].replace('"', '\\"'), i.push('"' + r + '":"' + o + '"'); return "{" + i + "}" } var t }, removeAllStorage: function () { for (var t = document.cookie.split("; "), e = $ZSIQUtil.getkeylist(), o = 0; o < t.length; o++)for (var i = 0; i < e.length; i++)if (-1 != t[o].search(new RegExp(e[i], "i"))) { $ZSIQCookie.remove(t[o]); break } this.isSessionStorageAvail() && (sessionStorage.utsdb = "{}"), this.isLocalStorageAvail() && (localStorage.siqlsdb = "{}", localStorage.utsdb = "{}", localStorage.siq_embed = "{}"), $ZSIQWidget && delete $ZSIQWidget.avuid }, setCookie: function (t, e, o) { $zohosq._domain ? $ZSIQCookie.set(t, $ZSIQLSDB.toString(e), o, !0, $zohosq._domain) : this.set(t, e, o) } } }(); var $ZSIQCookie = $ZSIQCookie || { get: function (e) { var o = e; e += "="; var i = document.cookie.indexOf(e), t = ""; -1 != i && -1 != (t = document.cookie.substr(i + e.length)).indexOf(";") && (t = t.substring(0, t.indexOf(";"))); var n = decodeURIComponent(t); return n || $ZSIQLSDB.getFromSession(o) || n }, set: function (e, o, i, t, n) { if ($ZSIQNotifyCookie.isCookieAllowed(e)) { var r = new Date; null != i && "" != i || (i = 18e4), r.setTime(r.getTime() + i), e += "= " + encodeURIComponent(o) + ";expires= " + r.toGMTString() + ";", t && (e += "path=/;"), e = n ? e + "domain=" + n + ";" : e; try { $ZSIQChat.isEmbedFeatureConfEnabled() && (e += "secure") } catch (e) { } return e = $ZSIQUtil.setSameSiteCookie(e), document.cookie = e, !0 } $ZSIQLSDB.storeInSession(e, o) }, remove: function (e, o) { if ($ZSIQNotifyCookie.isCookieAllowed(e)) { var i = new Date; if (i.setTime(i.getTime() - 1), o) { var t = e + "=;expires=" + i.toGMTString() + ";"; t += "path=/;", document.cookie = t + "domain=" + o + ";" } else document.cookie = e + "=;expires=" + i.toGMTString() + ";" } else $ZSIQLSDB.removeSessionStore(e) } }; var IframeHandler = function () { var a = function () { return window._IS_REVAMP }; function o() { return $ZSIQUtil.getIframe() } function r() { return o().$Support || {} } function d(e) { var t = $zoho.salesiq.clientactions[e.clientaction_name]; t && t(e) } function c(e) { $ZSIQUTSAction.rechattrigger = !0, $ZSIQUTSAction.handle(e) } function s(e) { document.querySelector('[data-id="zsiqembed"]').classList[e ? "add" : "remove"]("attach-preview") } function u(e) { var t = $ZSIQChat.getWidgetData(); t.embedobj.annonid = e, $ZSIQUtil.storeDetails(t) } function e(e) { var t, n, i, a, o = e.data; try { t = "string" == typeof o ? JSON.parse(o) : o } catch (e) { } t && "zsiqcwframe" === t.src && (n = t, i = $ZSIQChatWindow, (a = { cwdrag: i.handleDrag, upcwdragpos: i.cacheChatDivPosition, minimizecw: i.minimizeChatWindow, updateannonid: u, jsapicb: $ZSIQWidget.handleCallBacks, opencw: i.openChatWindow, closetriggerloading: i.RemoveLoadingForTrigger, togglecwdimension: function (e) { i.expandChatWindowDimension(e.expand) }, getzvvalue: function () { g($zv) }, block: function () { var e = function (e) { var t = document.querySelector(e); t && t.remove() }; e(".siq_media"), e(".siqembed"), e('[data-id="zsalesiq"]') }, umsgcount: $ZSIQWidgetUI.updateCount, clientaction: d, triggerreopen: c, openfilepreview: s, blinkTitle: $ZSIQUtil.blinkTitle, stopTitleBlink: $ZSIQUtil.stopBlinking, onload: function () { $ZSIQChatWindow.handleRevampLoad() }, setLSDB: function (e) { var t = $ZSIQChat.getWidgetData().embedobj.einfo.embedid; $ZSIQLSDB.set("ZLD" + t + e.key, e.value) }, updateCustomEvent: function (e) { $ZSIQUTS.notifyCustomEvent(e) }, handleTriggerReopen: function () { l("triggerapi", !0) } })[n.action] && a[n.action](n.data)) } function l(e, t) { var n = o(); n && n.postMessage && n.postMessage(JSON.stringify({ src: "zsiqfloat", action: e, data: t })) } function g(e, t) { if (a()) return l("jsapi", e); r().handleApiData(e, t) } return { updateJsApiValue: g, updatePreview: function (e, t) { l("preview", { data: e, type: t }) }, sendPostMessage: l, handleRevampLoad: function () { l("jsapi", $zv) }, getTriggerCookie: function () { return a() ? "" : r().getTriggerCookie() }, incrementUnreadCount: function () { !a() && r().incrementUnreadCount() }, handleTrigger: function (e, t) { a() ? l("trigger", t) : r().Util.handleTriggers(e, t) }, addMessageListener: function () { window.addEventListener("message", e) }, updateUVID: function (e) { if (a()) return l("uvid", e); r().setUVID(e) }, handleBotTrigger: function (e) { if (a()) return l("bottrigger", e); r().Util.handleBotTriggers(e) }, handleBotMessage: function (e) { if (a()) return l("botmessage", e) }, getLsId: function () { return a() ? r().getLsId() : r().EmbedObj.livelsid }, handleProActiveChat: function (e) { var t = r(); if (a()) { if (t.isChatExist && t.isChatExist() || !e.msg) return; var n = $ZSIQChatWindow; function i() { l("proactivechat", e) } return o() ? i() : n.constructIframe(!1, function () { n.openChatWindow(), i() }) } return t.Util.handleTrackChat(e) }, loadExternalFiles: function () { if (!a()) { var e = r(); e.Util && e.Util.loadExternalFiles() } }, clearAPIValues: function () { a() && l("resetVisitorInfo"), $ZSIQUtil.clearApiValues() }, chatwindowReload: function () { if (a()) return l("chatwindowReload"); r().reload() }, isCallOnline: function () { return r().EmbedObj.call_status } } }(); try { if (!window.$zoho) { var $zoho = window.$zoho || {}; $zoho.salesiq = $zoho.salesiq || { values: {}, widgetcode: document.currentScript.src.split("widgetcode=")[1] } } _ZSIQ = window._ZSIQ || { JSAPI: {} }, $ZSIQAnalytics = {}, $ZSIQAutopick = {}, _ZSIQ.isagentschat || ($zohosq = $zoho.livedesk || $zoho.salesiq, $zohosq._callbacks = {}, $zcb = $zohosq._callbacks, $zv = $zohosq.values, $zlm = {}, $zlch = {}, $zla = "handleAnalyticEvents", $zohosq.utsvalues = $zohosq.utsvalues || {}, $ZSIQ_UTSinitialized = null), $zohosq._invoke = function (e, t) { for (var o in $ZSIQAnalytics) if ($ZSIQAnalytics.hasOwnProperty(o)) { var n = $ZSIQAnalytics[o]; $ZShandleEvent(e, t, n) } if ($zcb[e] && "function" == typeof $zcb[e]) { if ("object" != typeof t) { if ($zlm[e] === t) return !1; $zlm[e] = t } var i; if (t.visitid) i = $zcb[e](t.visitid, t); else { if ("visitor.trigger" === e) return $zcb[e](t.triggername, t.visitorinfo); i = $zcb[e](t) } i = null != i ? i : -1; var a = {}; a[e] = i, $zlch[e] && $zohosq.setValue("callback", a), $zlm[e] = {} } else "custom.field" == e && $zohosq.customfield.handleCallbacks(t); return !1 }, $zohosq.clientactions = {}, $zohosq.jwttoken = { logintoken: function (e) { try { if (!ZSIQJWTVerify.isJWTEnabled()) return; $zv.jwttokenval = e, ZSIQJWTVerify.verifyJWTtoken(e) } catch (e) { } }, logout: function (e) { try { if (!ZSIQJWTVerify.isJWTEnabled()) return; ZSIQJWTVerify.logoutJWT(e) } catch (e) { } }, jwttokengetter: function (e) { try { if (!ZSIQJWTVerify.isJWTEnabled()) return; $zcb.jwttokengetter = e } catch (e) { } }, logoutcomplete: function (e) { try { if (!ZSIQJWTVerify.isJWTEnabled()) return; $zcb.jwtlogoutcallback = e } catch (e) { } } }, $zohosq.visitor = { destroy: function (e) { try { e = e || Float.lsobject.lsid } catch (e) { } $ZDestroyFloatData(e), $ZNotifyTracking(6) }, autopick: function (e) { return "boolean" == typeof e && $zohosq.setValue("autopick", { isenabled: e }, 3), $zv.autopick }, referer: function (e) { return e && e.trim() && $zohosq.setValue("referer", e, 3), $zv.referer }, cpage: function (e) { return e && e.trim() && $zohosq.setValue("cpage", e, 3), $zv.cpage }, pagetitle: function (e) { return e && e.trim() && $zohosq.setValue("pagetitle", e, 3), $zv.pagetitle }, customaction: function (e, t) { if (e && 0 != e.trim().length && !$ZSisThresholdExceeded("ca", 50)) { var o = {}; o.field = e, o.value = t || {}, $zv.customaction = o, $ZNotifyTracking(4, o) } }, uniqueid: function () { return $zv.uvid }, uniqueuserid: function () { return $zv.uuid }, handleinfo: function (e) { $zohosq.setValue("handleinfo", e, 3); var t = e.name; t && t.trim() && $zohosq.setValue("name", t); var o = e.email; null != o && /^([\w]([\w\-\.\+\'\/]*)@([\w\-\.]*)(\.[a-zA-Z]{2,22}(\.[a-zA-Z]{2}){0,2}))$/.test(o) && $zohosq.setValue("email", o); var n = e.phone; null != n && $ZSIQUtil.isValidPhoneNo(n) && $zohosq.setValue("phone", n) }, name: function (e) { if (!$zv.jwttokenval) { e && e.trim() && $zohosq.setValue("name", e, 3); var t = $ZSIQUtil.getIframeSupportObj(); return (t.getVisitorName ? t.getVisitorName() : "") || $zv.name } }, email: function (e) { if (!$zv.jwttokenval) { return null != e && /^([\w]([\w\-\.\+\'\/]*)@([\w\-\.]*)(\.[a-zA-Z]{2,22}(\.[a-zA-Z]{2}){0,2}))$/.test(e) && $zohosq.setValue("email", e, 3), $ZSIQUtil.getIframeSupportObj().visitoremail || $zv.email } }, id: function (e) { return e && e.trim() && $zohosq.setValue("id", e), $ZSIQUtil.getAPIValues().id = e, $ZSIQUtil.checkStoredIdAndRemoveStorage(e), $zv.id }, question: function (e) { return null != e && $zohosq.setValue("question", e), $zv.question }, contactnumber: function (e) { if (!$zv.jwttokenval) return null != e && $ZSIQUtil.isValidPhoneNo(e) && $zohosq.setValue("phone", e, 3), $ZSIQUtil.getIframeSupportObj().visitorphone || $zv.phone }, info: function (e) { return e && ($zv.info = e), $zohosq.setValue("info", $zv.info, 3), $zv.info }, setlocation: function (e) { return e && $zohosq.setValue("seclocation", e), $zv.seclocation }, authkey: function (e) { return e && ($zv.authkey = e), $zv.authkey }, chat: function (e) { $zcb["visitor.chat"] = e }, attend: function (e) { $zcb["visitor.attend"] = e }, missed: function (e) { $zcb["visitor.missed"] = e }, agentsoffline: function (e) { $zcb["visitor.offline"] = e }, offlineMessage: function (e) { $zcb["visitor.offline"] = e }, chatmessage: function (e) { $zcb["visitor.chatmessage"] = e }, chatcomplete: function (e) { $zcb["visitor.chatcomplete"] = e }, rating: function (e) { $zcb["visitor.rating"] = e }, feedback: function (e) { $zcb["visitor.feedback"] = e }, idleTime: function (e) { isNaN(e) || ($zv.idletime = e, $ZNotifyTracking(2, e)) }, idle: function (e) { $zcb["visitor.idle"] = e }, active: function (e) { $zcb["visitor.active"] = e }, onNavigate: function (e) { return e }, trigger: function (e) { $zcb["visitor.trigger"] = e }, triggeredchat: function (e) { $zcb["visitor.triggeredchat"] = e }, ongoingchat: function () { var e = $ZSIQUtil.getIframe(); return !(!e || !e.$Support) && !!e.$Support.isChatExist() }, getGeoDetails: function () { $zv.fetchgeolocation = !0 } }, $zohosq.article = { content: function (e) { return e && ($zv.articlecontent = e), $zv.articlecontent } }, $zohosq.chat = { enableCrossDomain: function (e) { return e && e.trim() && $zohosq.setValue("enablecrossdomain", e), $zv.enablecrossdomain }, restrictAnalyticsValue: function (e) { return e && e.trim() && $zohosq.setValue("restrictanalyticsvalue", e), $zv.restrictanalyticsvalue }, mode: function (e) { return null != e && $zohosq.setValue("chatmode", e), $zv.chatmode }, sendmessage: function (e) { Float && e && Float.sendMessage(e) }, department: function (e) { return null != e && $zohosq.setValue("department", e), $zv.department }, defaultdepartment: function (e) { return e && ($zv.defaultdepartment = e), $zv.defaultdepartment }, agent: function (e) { return null != e && $zohosq.setValue("agent", e), $zv.agent }, messages: function (e) { return e && ($zv.chatmessages = e), $zv.chatmessages }, systemmessages: function (e) { return e && ($zv.chatmessages = e), $zv.chatmessages }, title: function (e, t) { return e && ($zv.title = e), "[object Object]" === Object.prototype.toString.call(t) && ($zv.titlestyle = t), $zv.title }, messagehint: function (e) { return e && ($zv.messagehint = e), $zv.messagehint }, online: function (e) { $zcb["chat.online"] = e }, offline: function (e) { $zcb["chat.offline"] = e }, logo: function (e, t) { return e && ($zv.clogo = e), t && ($zv.cwebsite = t), $zv.clogo }, waitinghandler: function (e) { var t = "chat.waitinghandler"; $zcb[t] = $zlch[t] = e, $zv[t] = null != e }, start: function (e) { $zohosq.setValue("chatStart", !0), e && $zohosq.setValue("chatStart", !1); var t = function () { _ZSIQ.JSAPI.broadcastMessage("chatstart", {}), IframeHandler.sendPostMessage("chatstart") }; if (!$ZSIQUtil.getIframe()) return $ZSIQChatWindow.clearIframeLoader(), void $ZSIQChatWindow.populateIframe(t); t() }, forward: function (e) { return null != e && $zohosq.setValue("forward", e), $zv.forward }, attend: function (e) { $zcb["visitor.attend"] = e }, agentMessage: function (e) { $zcb["visitor.chatmessage"] = e }, triggerMessage: function (e) { $zv.triggermsg = !0, $zcb["visitor.chattrigger"] = e }, complete: function (e) { if (!e) { var t = !1, o = $ZSIQUtil.getIframe(); return _ZSIQ.JSAPI.broadcastMessage("chatend", {}), IframeHandler.sendPostMessage("chatend"), o && o.$Support && o.$Support.isChatExist && (t = o.$Support.isChatExist()), t } $zcb["visitor.chatcomplete"] = e }, missed: function (e) { $zcb["visitor.missed"] = e }, theme: function (e) { !function (e) { if (!e) return; $zv.buttontheme = e + "-btn", $zv.sbonlinebg = "", $zv.sbonlinebdr = "", $zv.sbicobg = "" }(e), function (e) { if (!e) return; var t = { black: ["black", "black"], gray: ["gray", "gray"], blue: ["blue", "blue"], green: ["green", "green"], red: ["red", "red"], purple: ["purple", "purple"] }; t.hasOwnProperty(e) ? ($zv.embedtheme = t[e][0], $zv.embedheadertheme = t[e][1]) : $zv.embedtheme = e }(e) }, floatingwindow: function (e) { $zv.forcefloatingwindow = "all" === e || !("ipad" === e && /ipad/.test(navigator.userAgent.toLowerCase())) }, transferchat: function (e) { $zcb["agent.transferchat"] = e }, accepttransfer: function (e) { $zcb["agent.accepttransfer"] = e }, waitime: function (e) { e && ($zv.waitime = e) }, waittime: function (e) { e && ($zv.waitime = e) }, continue: function (e) { e ? $zcb["chat.continue"] = e : _ZSIQ.JSAPI.broadcastMessage("showchatui", {}) } }, $zohosq.call = { initiatecall: function (e) { return null != e && $zohosq.setValue("initiatecall", e), $zv.agent }, department: function (e) { return null != e && $zohosq.setValue("call_department", e), $zv.call_department }, start: function () { var t = $ZSIQUtil.getIframe(); if (!t.SiqAVRouter && t.$Support.EmbedObj.staticfiles_delayloading_enabled) return $zohosq.setValue("isDirectCall", !0), void t.$Support.getMediaLibraryJS(); var e = { conversationtype: 0, successCbk: function (e) { $zohosq._directcall = !0, _ZSIQ.JSAPI.broadcastMessage("callstart", {}), t.SiqAVRouter.container.uiHandler.initiateDirectCallUI(e) }.bind(this) }; if (!IframeHandler.isCallOnline()) return $zoho.salesiq.floatwindow.visible("show"), t.$Support.Util.showMessageAreaUI(), void t.$Support.Util.checkAndFocusComposer(); t.SiqAVRouter.container.audioImpl.startApiDirectCall(e), t.SiqAVRouter.container.uiHandler.initiateDirectCallUI() }, isavsupported: function (e) { $zcb["call.isavsupported"] = e }, attended: function (e) { $zcb["call.attended"] = e }, completed: function (e) { $zcb["call.completed"] = e }, missed: function (e) { $zcb["call.missed"] = e }, cancelled: function (e) { $zcb["call.cancelled"] = e } }, $zohosq.rating = { visible: function (e) { e && $zohosq.setValue("rating.visible", e) } }, $zohosq.feedback = { visible: function (e) { e && $zohosq.setValue("feedback.visible", e) } }, $zohosq.integ = { requestid: function (e) { return null != e && $zohosq.setValue("requestid", e), $zv.requestid } }, $zohosq.chatbubble = { visible: function (e) { return e && ($zv.bubblevisible = e), $zv.bubblevisible }, animate: function (e) { return e && ($zv.bubbleanimatetimer = e), $zv.bubbleanimatetimer }, src: function (e) { return e && ($zv.bubblesrc = e), $zv.bubblesrc }, close: function (e) { $zcb["chatbubble.close"] = e } }, $zohosq.chatbutton = { texts: function (e) { return e && ($zv.buttontexts = e), $zv.buttontexts }, icon: function (e) { return e && ($zv.buttonicon = e), $zv.buttonicon }, visible: function (e) { e && $zohosq.setValue("buttonvisible", e), $ZSIQCookie.set("isiframeenabled", !0, 864e5, !0); try { zhandleLiveEvent("buttonvisible", e) } catch (e) { } return $zv.buttonvisible }, onlineicon: { src: function (e) { return e && ($zv.buttononlineicon = e), $zv.buttononlineicon } }, offlineicon: { src: function (e) { return e && ($zv.buttonofflineicon = e), $zv.buttonofflineicon } }, click: function (e) { $zcb["chatbutton.click"] = e }, width: function (e) { return e && ($zv.bwidth = e), $zv.bwidth } }, $zohosq.floatbutton = { closebutton: function (e) { $zohosq.setValue("closeicon", e) }, position: function (e) { return e && ($zv.floatposition = e), $zv.floatposition }, visible: function (e) { e && $zohosq.setValue("floatvisible", e), $ZSIQCookie.set("isiframeenabled", !0, 864e5, !0); try { $ZSIQWidget && $ZSIQChatWindow.handleIframeLoading(function () { $ZSIQWidgetUI.handleWidgetVisible(e) }) } catch (e) { } return $zv.floatvisible }, onlineicon: { src: function (e) { return e && ($zv.floatbuttononlinesrc = e), $zv.floatbuttononlinesrc } }, offlineicon: { src: function (e) { return e && ($zv.floatbuttonofflinesrc = e), $zv.floatbuttonofflinesrc } }, click: function (e) { $zcb["floatbutton.click"] = e }, coin: { hidetooltip: function () { try { $zv.ishidetooltip = !0, $ZSIQWidget && $ZSIQChatWindow.handleIframeLoading(function () { $ZSIQWidgetUI.checkAndHideCoinToolTip() }) } catch (e) { } } } }, $zohosq.chatwindow = { visible: function (e) { e && ($zv.chatwindowvisible = e), $ZSIQCookie.set("isiframeenabled", !0, 864e5, !0), "hide" != e && ($zohosq.fileloadtime = 0); try { zhandleLiveEvent("chatwindowvisible", e) } catch (e) { } return $zv.chatwindowvisible }, reload: function () { IframeHandler.chatwindowReload() }, closebutton: function (e) { "hide" === e && ($zv.closebutton = e) }, open: function () { $zv.isChatwindowOpen = !0 } }, $zohosq.floatwindow = { visible: function (e, t, o) { e && ($zv.floatwindowvisible = e), $ZSIQCookie.set("isiframeenabled", !0, 864e5, !0), "hide" != e && ($zohosq.fileloadtime = 0); try { if (!$ZSIQUtil.getIframe() && (window._IS_REVAMP || $ZSIQChatWindow.getIframeLoader()) && "hide" != e) return $ZSIQChatWindow.clearIframeLoader(), void $ZSIQChatWindow.populateIframe(function () { $ZSIQChatWindow.handleChatWindowVisible(e, !1, t, o, !0) }); $ZSIQWidget && $ZSIQChatWindow.handleIframeLoading(function () { $ZSIQChatWindow.handleChatWindowVisible(e, !1, t, o, !0) }) } catch (e) { } return $zv.floatwindowvisible }, open: function (e) { e ? $zcb["chat.open"] = e : this.visible("show") }, close: function (e) { e ? $zcb["chat.close"] = e : this.visible("hide") }, minimize: function (e) { if (e) $zcb["floatwindow.minimize"] = e; else try { $ZSIQChatWindow.minimizeChatWindow(!0) } catch (e) { } }, onlinetitle: function (e) { return null != e && $zohosq.setValue("floatwindowonlinetitle", e), $zv.floatwindowonlinetitle }, offlinetitle: function (e) { return null != e && $zohosq.setValue("floatwindowofflinetitle", e), $zv.floatwindowofflinetitle }, fields: function (e) { return null != e && ($zv.floatwindowfields = e), $zv.floatwindowfields }, defaultview: function (e) { return null != e && ($zv.defaultview = e), $zv.defaultview } }, $zohosq.custom = { html: function (e, t) { if (e && t) { $zv.customhtml = [e, t]; try { $ZSIQChatWindow.drawCustomHTML() } catch (e) { } } return $zv.floatbuttondraw } }, $zohosq.customfield = { add: function (e) { var t = "customfield"; if (e) { var o = $zv[t] = $zv[t] || []; this._splice([e.name], o), $zv[t].push(e), _ZSIQ.JSAPI.broadcastMessage(t, $zv[t]) } }, clear: function (e) { var t = "customfield"; e || ($zv[t] = [], e = []), "[object Array]" === Object.prototype.toString.call(e) && this._splice(e, $zv[t] || []) }, handleCallbacks: function (e) { if (e) { var t = this._getObject(e.name); t && t.callback && t.callback(e.val) } }, _getObject: function (e) { var t = $zv.customfield; if (t && !(t.length < 1)) for (var o = 0; o < t.length; o++)if (t[o].name == e) return t[o] }, _splice: function (e, t) { for (var o = "clearfield", n = 0; n < e.length; n++) { for (var i = 0; i < t.length; i++)if (t[i].name == e[n]) { t.splice(i, 1), _ZSIQ.JSAPI.broadcastMessage("clearcustomfield", [e[n]]); break } /^(siq_name|siq_email|siq_phone|)$/.exec(e[n]) && ($zv[o] && $zv[o].push(e[n]), _ZSIQ.JSAPI.broadcastMessage(o, [e[n]])) } } }, $zohosq.getWidgetDetails = function () { var e = (($ZSIQChat.getWidgetData() || {}).embedobj || {}).pinfo || {}; return { soid: e.soid, screenName: e.screenname } }, $zohosq.reset = function (e) { var t = (e || {}).essential; $zohosq.resetEssential = t, $zv.reset = "on"; var o = $ZSIQUtil.getIframe(), n = $ZSIQWidget.getWidgetObject(), i = n.lsid; if (!t && o && o.$Support && o.$Support.isChatExist() && o.$Support.quitConnetcedChats(), $ZSIQUTS.clearUtsApivalues(), $ZSIQUTS.clearLocalValues(), $ZSIQUTSAction.clearLocalValues(), $ZSIQUtil.checkTrackingEnabled()) { t ? $ZNotifyTracking(6) : $zohosq.visitor.destroy(i), $ZNotifyTracking(1, "off"); var a = $ZSIQWidget.getEmbedObject().pinfo.pinfo; t || 1 == JSON.parse(a.isgdprenabled) && 0 != JSON.parse(a.trackingprivacyconfig) && 1 == n.isgdprenabled || $ZNotifyTracking(1, "on") } !t && $ZSIQLSDB.removeAllStorage(), IframeHandler.clearAPIValues(), $zv = $zohosq.values = {}, $ZSIQChat.init() }, $zohosq.pastchat = { visible: function (e) { e && $zohosq.setValue("pastchatvisible", e) } }, $zohosq.field = { clear: function (e) { $zv.clearfield = [], $zohosq.customfield.clear(e) } }, $zohosq.tracking = { on: function () { $zv.tracking = "on", $ZSIQUtil.getAPIValues().tracking = "on", $ZNotifyTracking(1, "on") }, off: function () { $zv.tracking = "off", $ZSIQUtil.getAPIValues().tracking = "off", $ZNotifyTracking(1, "off") }, domain: function (e) { var t = window.location.hostname; e && -1 !== t.indexOf(e, t.length - e.length) && ($zohosq.utsvalues.trackingdomain = e) }, allowMultiTrigger: function (e) { $zv.mtrigger = $ZSIQUtil.getAPIValues().mtrigger = !!e }, eqaulHosts: function (e) { if (!e) return $zohosq.utsvalues.equalhosts; var t = e.split(","); $zohosq.utsvalues.equalhosts || ($zohosq.utsvalues.equalhosts = []); for (var o = 0; o < t.length; o++) { var n = t[o].trim(), i = !1; if ($zohosq.utsvalues.equalhosts) for (var a = 0; a < $zohosq.utsvalues.equalhosts.length; a++) { if ($zohosq.utsvalues.equalhosts[a] == n) { i = !0; break } } i || $zohosq.utsvalues.equalhosts.push(n) } return $zohosq.utsvalues.equalhosts }, getsiqid: function () { return $UTSHandler.getSIQID() }, forcesecure: function (e) { e && ($zohosq.utsvalues.forcesecure = !0) } }, $zohosq.language = function (e) { return e && ($zv.language = e), $zv.language }, $zohosq.personalize = { agentorder: function (e) { return null != e && $zohosq.setValue("agentorder", e), $zv.agentorder } }, $zohosq.set = function (e) { for (var t in e) try { for (var o = t.split("."), n = this[o[0]], i = 1; i < o.length; i++)o[i] && (n = n[o[i]]); "function" == typeof n && n(e[t]) } catch (e) { } }, $zohosq.setValue = function (e, t, o) { if (null != t && e && ($zv[e] = t, _ZSIQ.JSAPI.broadcastMessage(e, t), o)) { var n = {}; n[e] = t, $ZNotifyTracking(o, n) } }, $zoho.ld = {}, $zoho.ld.handle = { customClick: function (e) { var t = $zohosq.values.customhtml; if (!t) return !1; var o = t[1][e + ".click"]; if ("function" == typeof o) o(); else try { $ZSIQChatWindow.openChatWindow() } catch (e) { try { zlsHandleCustomClick() } catch (e) { } } } }, $zohosq.privacy = { trackingbannercontent: function (e) { return e && ($zv.trackbannerobj = e), $zv.trackbannerobj }, chatbannercontent: function (e) { return e && ($zv.textbannerobj = e), $zv.textbannerobj }, content: function (e) { return e && ($zv.chatprivacycontent = e), $zv.chatprivacycontent }, getCookieList: function () { for (var e = $ZSIQUtil.deepClone($ZSIQNotifyCookie.getCookieList()), t = { performance: [], analytics: [], essential: [] }, o = e.length, n = 0; n < o; n++) { var i = e[n], a = i.type; "functionality" === a ? (i.type = "performance", t.performance.push(i)) : "analytics" === a ? t.analytics.push(i) : (i.type = "essential", t.essential.push(i)) } return t }, updateCookieConsent: function (e) { $zohosq.setValue("cookieconsent", e), window.$ZSIQWidget ? $ZSIQUtil.setTrackingStatus() : $ZSIQUtil.manageCookie = !0 } }, $zohosq.department = { bannerText: function (e) { var t = e.offline, o = e.engaged; t && $zohosq.setValue("offlineBannerText", t), o && $zohosq.setValue("engagedBannerText", o) } }, $zohosq.domain = function (e) { $zohosq.tracking.domain(e), $zohosq._domain = e }, $zohosq.vanish = function () { localStorage.siqlsdb = JSON.stringify({}), localStorage.utsdb = JSON.stringify({}), sessionStorage.utsdb = JSON.stringify({}); for (var e = $ZSIQNotifyCookie.cookieList(), t = 0; t < e.length; t++) { var o = keys[t], n = $UTSHandler.getDomain(), i = new Date; if (i.setTime(i.getTime() - 1), !n) return void (document.cookie = o + "=;expires=" + i.toGMTString() + ";"); var a = o + "=;expires=" + i.toGMTString() + ";"; a += "path=/;", document.cookie = a + "domain=" + n + ";" } for (var s = ["$ZSIQLSDB", "$ZSIQCookie", "IframeHandler", "$zsalobj", "$zsalobjrestricted", "UDHandler", "ZSIQJWTVerify", "$ZSIQUtil", "$ZSIQLicence", "handleIframeFunction", "$UTSHandler", "$ZSIQUTS", "$ZSIQUTSAction", "ResponseFormatter", "$ZSIQNotifyCookie", "$ZSIQChat", "isdomloadhandled", "WEBSITE_VISITOR_API_FLOW_ENABLED", "NEW_STATIC_URLS", "_STATIC_URL", "_ZSIQ", "$ZSIQAnalytics", "$ZSIQAutopick", "$zohosq", "$zcb", "$zv", "$zlm", "$zlch", "$zla", "$ZSIQ_UTSinitialized", "$ZSisThresholdExceeded", "$ZDestroyFloatData", "$ZNotifyTracking", "$ZShandleEvent", "SIQ_FLOAT", "SIQ_BUTTON", "SIQ_PERSONALIZE", "_WINDOW_REPOPULATE", "api_lang", "_WEBSITEAPIRESPONSE", "_IS_REVAMP", "iscdnenabled", "cssjslist", "actualcssfile", "actualjsfile", "$ZSIQChatWindow", "zsiqdrag", "$ZSIQTemplate", "$ZSIQWidgetUI", "$ZSIQWidget"], r = 0; r < s.length; r++)window[s[r]] = void 0, delete window[s[r]]; delete window.$zoho.salesiq, document.querySelector('[data-id="zsiqembed"]').remove(), document.getElementById("zsiqchat").remove(); var l = document.querySelectorAll('[data-id="zsalesiq"'); for (r = 0; r < l.length; r++)l[r].remove() } } catch (e) { } $ZSisThresholdExceeded = function (e, t) { var o = Math.floor((new Date).getTime() / 6e4); if (o = e + "_" + o, "undefined" == typeof $ZSIQUTS) return !1; var n = $UTSHandler.getFromSession(o); return n || (n = 0), $UTSHandler.storeInSession(o, ++n), t < n }, $ZDestroyFloatData = window.$ZDestroyFloatData || function (e) { try { sessionStorage.lsdb = JSON.stringify("{}") } catch (e) { } try { localStorage.lsdb = JSON.stringify("{}") } catch (e) { } try { for (var t = ["zld" + e + "float", "zld" + e + "dragpos", "ZLD" + e + "WTIME", "ZLD" + e, "ZLD" + e + "WAITING", "ZLDTRIGGER" + e, "ZLD" + e + "VISITORINFO"], o = 0; o < t.length; o++) { var n = t[o] + "= ;expires= " + new Date(-1).toGMTString(); try { var i = Float.getDomain(); n += i ? ";domain=" + i : "" } catch (e) { } document.cookie = n, n += "path=/;", document.cookie = n } } catch (e) { } }, $ZNotifyTracking = window.$ZNotifyTracking || function (e, t) { if ($ZSIQ_UTSinitialized || window.$UTSConnector) try { $UTSHandler.handleApiChange(e, t) } catch (e) { } }, $ZShandleEvent = window.$ZShandleEvent || function (e, t, o) { try { $zohosq.handleAnalyticEvents(e, t, o) } catch (e) { } }, _ZSIQ.JSAPI.broadcastMessage = window._ZSIQ.JSAPI.broadcastMessage || function (e, t) { if ($ZSIQ_UTSinitialized) try { var o = {}; o[e] = t, $ZSIQUtil.updateApiValues(), IframeHandler.updateJsApiValue(o, !0) } catch (e) { } try { AgentsChat.broadcastMessage(e, t) } catch (e) { } try { zlsWinBroadcastMessage(e, t) } catch (e) { } try { zlsBtnBroadcastMessage(e, t) } catch (e) { } }, _ZSIQ.JSAPI.identifySIQParam = window._ZSIQ.JSAPI.identifySIQParam || function () { var e, t, o, n = function (e) { return decodeURIComponent((e + "").replace(/\+/g, "%20")) }; try { var i = {}, a = window.location.search, s = window.location.hash; if (!a && s) { var r = s.split("?"); r[1] && (a = r[1]) } if (!a) return; for (var l = (a = a.replace(/^\?/g, "")).split("&"), c = [], u = 0; u < l.length; u++)i[n((c = l[u].split("="))[0])] = n(c[1]); null != i.siq_eemail ? $zohosq.setValue("e_email", i.siq_eemail) : null != i.siq_email ? $zohosq.visitor.email(i.siq_email) : null != i.om_email && $zohosq.visitor.email(i.om_email), null != i.siq_ename ? $zohosq.setValue("e_name", i.siq_ename) : null != i.siq_name ? $zohosq.visitor.name(i.siq_name) : null != i.om_name && $zohosq.visitor.name(i.om_name), e = location.href.split("?")[0], t = location.search, o = location.hash, t && (e += (t = (t = (t = t.replace(/^\?/g, "")).replace(/(^|&)siq_(name|email|ename|eemail)=[^&]*/g, "")).replace(/^&/g, "")) ? "?" + t + o : o, window.history.replaceState(window.history.state, "", e)) } catch (e) { } }; try { var $zsalobj = window.$zsalobj || {}, $zsalobjrestricted = window.$zsalobjrestricted || {}; $zohosq.init = function () { $zsalobj && 0 == Object.keys($zsalobj).length && ($zsalobj = { "chatbutton.click": ["Button Clicked", "Click on the " + _ZSIQ.brandname + " SalesIQ chat button."], "floatbutton.click": ["Button Clicked", "Click on the " + _ZSIQ.brandname + " SalesIQ chat button."], "chat.close": ["Chat Closed", "Click on the Close (X) icon in the chat window."], "floatwindow.minimize": ["Chat Minimized", "Click to minimize the " + _ZSIQ.brandname + " SalesIQ chat window."], "chatbubble.close": ["Bubble Closed", "Click on the Close(X) icon on the chat bubble."], "visitor.attend": ["Chat Connected", "Chat sessions with the visitors.", "waitingduration"], "visitor.chatcomplete": ["Chat Ended", "Chat ended by the visitors.", "chatduration"], "chat.file": ["File Transferred", "File transfers made by the visitors."], "chat.print": ["Chat Printed", "Chat transcripts printed by the visitors."], "chat.mail": ["Chat Mail sent", "Chat transcripts emailed by the visitor."], "visitor.rating": ["Rating Submitted", "Rating submitted by visitors.", "rating"], "visitor.feedback": ["Feedback Submitted", "Feedback messages submitted by visitors."], "visitor.chattrigger": ["Chat Triggerred", "Auto and proactive chat initiated to the visitors."], "visitor.triggerresponded": ["Trigger Responded", "Visitor responded to trigger or proactive chat."], "visitor.chat": ["Chat Initiated", "Visitor initiated chats."], "visitor.offline": ["Offline Message Submitted", "Offline messages submitted by the visitors."], "visitor.missed": ["Chat Missed", "Chat missed by the SalesIQ users."], "chat.visitorend": ["Chat Ended by visitor", "Chats ended by the visitors."] }, $zsalobjrestricted = { "chatbutton.click": ["Button Clicked", "Click on the " + _ZSIQ.brandname + " SalesIQ chat button."], "floatbutton.click": ["Button Clicked", "Click on the " + _ZSIQ.brandname + " SalesIQ chat button."], "chat.close": ["Chat Closed", "Click on the Close (X) icon in the chat window."], "floatwindow.minimize": ["Chat Minimized", "Click to minimize the " + _ZSIQ.brandname + " SalesIQ chat window."], "chatbubble.close": ["Bubble Closed", "Click on the Close(X) icon on the chat bubble."], "visitor.attend": ["Chat Connected", "Chat sessions with the visitors."], "visitor.chatcomplete": ["Chat Ended", "Chat ended by the visitors."], "chat.file": ["File Transferred", "File transfers made by the visitors."], "chat.print": ["Chat Printed", "Chat transcripts printed by the visitors."], "chat.mail": ["Chat Mail sent", "Chat transcripts emailed by the visitor."], "visitor.rating": ["Rating Submitted", "Rating submitted by visitors."], "visitor.feedback": ["Feedback Submitted", "Feedback messages submitted by visitors."], "visitor.chattrigger": ["Chat Triggerred", "Auto and proactive chat initiated to the visitors."], "visitor.triggerresponded": ["Trigger Responded", "Visitor responded to trigger or proactive chat."], "visitor.chat": ["Chat Initiated", "Visitor initiated chats."], "visitor.offline": ["Offline Message Submitted", "Offline messages submitted by the visitors."], "visitor.missed": ["Chat Missed", "Chat missed by the SalesIQ users."], "chat.visitorend": ["Chat Ended by visitor", "Chats ended by the visitors."] }) }, $zohosq.handleAnalyticEvents = window.$zohosq.handleAnalyticEvents || function (t, e, i) { var a = $zsalobj[t]; if ("true" == $zv.restrictanalyticsvalue && (a = $zsalobjrestricted[t]), a) { var s = e[a[2]]; if (s && (s = parseInt(s)), 1 == i) try { "function" == typeof gtag ? 3 === a.length ? gtag("event", a[0], { event_category: "SalesIQ", event_label: a[1], value: s }) : gtag("event", a[0], { event_category: "SalesIQ", event_label: a[1] }) : "function" == typeof ga ? 3 === a.length ? ga("send", "event", "Sales IQ", a[0], a[1], s) : ga("send", "event", "Sales IQ", a[0], a[1]) : "undefined" != typeof _gaq && (3 === a.length ? _gaq.push(["_trackEvent", "Sales IQ", a[0], a[1], s]) : _gaq.push(["_trackEvent", "Sales IQ", a[0], a[1]])) } catch (t) { } else if (2 == i) 3 === a.length ? clicky.log("Sales IQ-" + a[0] + "-" + s) : clicky.log("Sales IQ-" + a[0]); else if (3 == i) { if (3 === a.length) (n = {})[a[1]] = s, _kmq.push(["record", "Sales IQ " + a[0], n]); else _kmq.push(["record", "Sales IQ " + a[0]]) } else if (4 == i) { if (3 === a.length) (n = {})[a[1]] = s, window.optimizely.push(["trackEvent", "Sales IQ " + a[0], n]); else window.optimizely.push(["trackEvent", "Sales IQ " + a[0]]) } else if (5 == i) { if (3 === a.length) (n = {})[a[1]] = s, woopra.track("Sales IQ " + a[0], n); else woopra.track("Sales IQ " + a[0]) } else if (6 == i) 3 === a.length ? _paq.push(["trackEvent", "Sales IQ", a[0], "", s]) : _paq.push(["trackEvent", "Sales IQ", a[0]]); else if (7 == i) { if (3 === a.length) (n = {})[a[1]] = s, mixpanel.track("Sales IQ " + a[0], n); else mixpanel.track("Sales IQ " + a[0]) } else if (8 == i) { var n; if (3 === a.length) (n = {})[a[1]] = s, _hsq.push(["trackEvent", "Sales IQ " + a[0], n]); else _hsq.push(["trackEvent", "Sales IQ " + a[0]]) } else if (9 == i) { var o = { event: "Sales IQ Events", category: "Sales IQ", action: a[0], label: a[1], value: 0 }; 3 === a.length && (o.value = s), dataLayer.push(o) } } } } catch (t) { } var UDHandler = { getDownLoadLink: function (e, n, o) { (o = o || {})["x-siq-downloadtype"] = n || "default"; var a = window.UDSERVER_EVENT_BASEDURL ? window.SIQ_UD_SERVICE_NAME || $ZSIQChat.getWidgetData().commondata.siqUDServiceName : window._SIQSERVICENAME || $ZSIQChat.getWidgetData().commondata.siqservicename, d = window.UDSERVER_EVENT_BASEDURL | $ZSIQChat.getWidgetData().commondata.udEventBasedURL ? (null == window._ZSIQDOWNLOADSERVERURL || window._ZSIQDOWNLOADSERVERURL.includes("public") ? "/public/" : "/v1/") + a + "/download/" + e + "?" : window._ZSIQDOWNLOADSERVERURL ? _ZSIQDOWNLOADSERVERURL + "/webdownload?x-service=" + a + "&event-id=" + e + "&" : "/public?x-service=" + a + "&event-id=" + e + "&"; return (window._ZSIQDOWNLOADSERVERURL ? _ZSIQDOWNLOADSERVERURL + d : $ZSIQChat.getWidgetData().commondata.downloadserver + d) + "x-cli-msg=" + encodeURIComponent(JSON.stringify(o)) }, getUDDownLoadLink: function (n) { var o = ["d"], a = function (e) { return n["x-siq-" + e] };["soid", "module", "parentid"].forEach(function (e) { "parentid" == e ? o.push(a(e) || a("conversation" == a("module") ? "type" : "resourceid") || a("type")) : o.push(a(e)) }), o = o.join("_"); var e = window.SIQ_UD_SERVICE_NAME || $ZSIQChat.getWidgetData().commondata.siqUDServiceName, d = (null == window._ZSIQDOWNLOADSERVERURL || window._ZSIQDOWNLOADSERVERURL.includes("public") ? "/public/" : "/v1/") + e + "/download/" + o + "?"; return (window._ZSIQDOWNLOADSERVERURL || $ZSIQChat.getWidgetData().commondata.downloadserver) + d + "x-cli-msg=" + encodeURIComponent(JSON.stringify(n)) }, getUploadEndpoint: function () { return window._ZSIQUPLOADSERVERURL ? window._ZSIQUPLOADSERVERURL + "/webupload" : $ZSIQChat.getWidgetData().commondata.uploadserver + "/publicupload" }, parseAndUpdateUDParam: function (e, n) { if (e && -1 != e.indexOf("x-cli-msg=")) { e = decodeURIComponent(e); var o = new RegExp("[?&]x-cli-msg=([^&#]*)", "i").exec(e); for (var a in o = JSON.parse(o[1]), n) n.hasOwnProperty(a) && (o[a] = n[a]); return UDHandler.getUDDownLoadLink(o) } return e } }; var ZSIQJWTVerify = function () { var l, g, u, S, d = function (e) { S = !1; var t = JSON.parse(e).data, a = $ZSIQUtil.getAvuid(); $ZSIQLSDB.set(a + "_jwttoken", JSON.stringify(t)); var i = $zv; i.jwtExpiry = parseInt(t.expiry); var n = t.visitor; if (n) var r = n.name, o = n.phone, s = n.email; r && (i.name = r), o && (i.phone = o), s && (i.email = s); var c = $ZSIQUtil.getIframe(); if (c) { var d = c.$EmbedManger; d && (r && d.setVisitorName(r), o && d.setVisitorPhone(o), s && d.setVisitorEmail(s)); var l = c.$Support, g = l.resendProps; if (l.noOfTimeFailed = 0, g.length) for (var u = 0; u < g.length; u++)v(g[u]); c.$Support.resendProps = [] } }, v = function (e) { var t = $ZSIQUtil.getIframe(), a = e.url, i = e.payload, n = e.successcb, r = e.failiurecb, o = t.$Support, s = t.LSMessanger; switch (delete o.retryCount, e.callbackMethod) { case "commonutil": t.CommonUtil.handleAjaxReq(a, i, n, r, e.method, !0); break; case "support": o.handleAjax(a, i, !0, n, r, e.loadingmsg, e.channelheader, e.queryparam); break; case "conversation": o.PopulateConversationList(n, e.startindex); break; case "setrating": o.setRating(e.stardiv, e.currwinobj); break; case "getmessages": s.getMessages(a, e.chid, e.isallowcredential, e.gettranscriptcb); break; case "gettranscript": o.getTranscripts(a, e.chid, e.gettranscriptcb); break; case "sendformmessage": t.ZSIQConversationForm.sendFormMessage(e.msg, e.meta, e.isignore); break; case "rejoinchat": o.rejoinchats(e.chatlist); break; case "sendfeedback": o.sendFeedback(e.curr, e.isratingedit, e.iscallonly); break; case "read": o.sendReadCall(e.msgid, !0); break; case "joinchat": s.joinchat(e.chatid, e.wmsid, e.isntwrkreconnect, e.isserverupwithoutserverdown); break; case "fileupload": t.CommonUtil.v2handleFileUpload(e.data, e.actionobj) } }, f = function (e) { var t = $ZSIQChat.getWidgetData() || {}; if ($ZSIQLSDB.removeSessionStore(($zv.cvuid || $ZSIQLSDB.get("ZLD" + t.widgetobj.lsid + "avuid")) + "_conversation"), !e) { var a = t.embedobj.einfo.embedid; $ZSIQLSDB.remove("ZLD" + a + "WAITING", !0), $ZSIQLSDB.remove("ZLD" + a, !0) } }, b = function () { var e = $ZSIQUtil.getAvuid(); $ZSIQCookie.remove(e + "_jwttoken"); try { "function" == typeof $zcb.jwtlogoutcallback && $zcb.jwtlogoutcallback(), "undefined" != typeof SiqAVRouter && SiqAVRouter.container.detailsHandler.handleJWTLogout() } catch (e) { } }; return { verifyJWTtoken: function (e, t) { if (!S) { var a = ($ZSIQChat.getWidgetData() || {}).embedobj, i = a.schema, n = $ZSIQUtil.staticURL[4], r = a.screenname; l = i + "://" + n + "/visitor/v2/" + r + "/jwtauthentication", g = a.einfo.embedid, u = e && e.token, f(!0); var o = ZSIQJWTVerify.getOngoingChatId(), s = { app_id: g, token: u }; o && -1 != o && (s.conversation_id = o); var c = { "x-siq-channel": "website" }; t && (c["X-SIQ-ACCESSTOKEN"] = $ZSIQUtil.getJWTToken(), c["X-SIQ-APPID"] = a.einfo.embedid), S = !0, $ZSIQUtil.postAjax(l, JSON.stringify(s), d, function (e) { var t = e.responseText && JSON.parse(e.responseText) || {}, a = t.error && t.error.code; a && (S = !1), 401 === e.status && 6205 === a && ZSIQJWTVerify.failiureCallback(); var i = $ZSIQUtil.getIframe(); if (i && a) { var n = i.$Support.resendProps; if (n.length) for (var r = 0; r < n; r++)v(n[r]); i.$Support.resendProps = [] } }, "", c) } }, logoutJWT: function (e) { var t = ($ZSIQChat.getWidgetData() || {}).embedobj; f(); var a = $ZSIQUtil.getAvuid(), i = $ZSIQUtil.getJWTToken(); $ZSIQLSDB.remove(a + "_jwttoken"); var n = t.schema, r = t.servername, o = t.screenname, s = (l = n + "://" + r + "/visitor/v2/" + o + "/jwtauthentication") + "/logout", c = { app_id: t.einfo.embedid, token: (e || {}).token || u }, d = { "x-siq-channel": "website", "X-SIQ-ACCESSTOKEN": i, "X-SIQ-APPID": g }; ZSIQJWTVerify.handleAjaxRequest(s, c, "PUT", b, "", d) }, getOngoingChatId: function () { var e = ($ZSIQChat.getWidgetData() || {}).embedobj, t = $ZSIQLSDB.get("ZLD" + e.einfo.embedid) || {}; return t.ongoingchats ? (Object.values(t.ongoingchats)[0] || {}).vid : "" }, handleAjaxRequest: function (e, t, a, i, n, r) { var o = new XMLHttpRequest; if (o.onreadystatechange = function () { 200 === o.status || 204 === o.status ? i && i(o.responseText && JSON.parse(o.responseText)) : n && n(o.responseText) }, o.open(a, e), !r && o.setRequestHeader("x-siq-channel", "website"), r) for (var s in r) r.hasOwnProperty(s) && o.setRequestHeader(s, r[s]); o.send(JSON.stringify(t)) }, failiureCallback: function () { S || "function" == typeof $zcb.jwttokengetter && $zcb.jwttokengetter() }, isJWTEnabled: function () { return ($ZSIQChat.getWidgetData().embedobj.jwt_authentication || {}).enabled } } }(); var $ZSIQUtil = $ZSIQUtil || function () { var n, o = ["en", "ar", "da", "de", "el", "es", "fr", "ga", "he", "hu", "it", "iw", "ja", "ko", "nb", "nl", "pl", "pt", "pt_PT", "ro", "ru", "sv", "th", "tr", "zh", "hr", "cs", "sk", "sl", "vi", "hy", "ka", "pt_PT", "zh_TW", "bg", "fa_IR", "fi", "pa", "hi", "or", "gu", "kn", "ml", "mr", "ta", "te", "bn", "uk", "kk", "uz", "id", "ca"], r = { zh_hant: "zh_TW", zh_tw: "zh_TW" }, a = { 0: "inherit", 1: "salesiq-font", 2: "ZSIQ Open Sans", 3: "ZSIQ Oxygen", 4: "ZSIQ Roboto", 5: "ZSIQ Puvi" }, t = {}, s = !1, c = "", d = null; return { istitleblinking: s, documenttitle: c, setIntegrityAttr: function (e, t) { return e.setAttribute("integrity", t), e.setAttribute("crossorigin", "anonymous"), e }, cookieConfig: { isFunctionality: !1, isAnalytics: !1 }, STATUS_ENABLE: 1, STATUS_DISABLE: 0, initializeAPIValues: function () { _ZSIQ.JSAPI.identifySIQParam(); try { $zohosq.internalready(), t = $zohosq.values } catch (e) { } try { $zohosq.ready(), this.updateApiValues() } catch (e) { } }, getURLProtocol: function (e) { var t = "http"; return 0 === e.indexOf(t) ? e : t + "://" + e }, getJWTToken: function () { var e = $ZSIQLSDB.get($ZSIQUtil.getAvuid() + "_jwttoken"); return e && ((e = "string" == typeof e ? JSON.parse(e) : e) || {}).access_token || "" }, deepClone: function (e) { if (null === e || "object" != typeof e) return e; var t = Array.isArray(e) ? [] : {}; for (var i in e) e.hasOwnProperty(i) && (t[i] = $ZSIQUtil.deepClone(e[i])); return t }, setSameSiteCookie: function (e) { var t = $ZSIQChat.getWidgetData(); return t && t.embedobj.is_cookie_samesite_strict && (e += ";SameSite=Strict"), e }, updateTrackingStatus: function (e) { var t = JSON.parse($ZSIQWidget.getEmbedObject().pinfo.pinfo.trackingprivacyconfig), i = e.includes("analytics"), n = e.includes("performance"); $ZSIQUtil.cookieConfig = { isFunctionality: n, isAnalytics: i }; var o = $UTSHandler.getAPIValuesForUTS(), r = $ZSIQWidget.getWidgetObject(), a = o.tracking && "off" == o.tracking || !r.istracking, s = i || a || 2 !== t ? 0 : 1; $ZSIQUtil.updateCookieForTracking(s, "2") }, handleSelfManagedCookie: function () { var e = ZohoCookie(), t = $zohosq.values.cookieconsent || []; if (e.isCookieSet()) t = e.getSelectedCookies(); else { if (document.getElementById("consent_container")) return void e.showConsent({ handler: function (e, t) { $ZSIQUtil.updateTrackingStatus(t.selected_cookies) } }); t.push("essential"), e.setValue("allowed_cookies", t) } var i = $ZSIQWidget.getEmbedObject().pinfo, n = $ZSIQUtil.cookieConfig; n.isAnalytics = t.includes("analytics"), n.isFunctionality = t.includes("performance"); var o = $UTSHandler.getAPIValuesForUTS(), r = $ZSIQWidget.getWidgetObject(); r.isgdprenabled || ($ZSIQUtil.cookieConfig = { isAnalytics: !0, isFunctionality: !0 }); var a = o.tracking && "off" == o.tracking || !r.istracking, s = JSON.parse(i.pinfo.trackingprivacyconfig); $ZSIQUtil.cookieConfig.isAnalytics || a || 2 != s ? $ZSIQUtil.updateCookieForTracking(0, "2") : $ZSIQUtil.updateCookieForTracking(1, "2") }, appendCookieBannerJS: function (e) { var t = document.createElement("script"); t.src = "https://static.zohocdn.com/zoho-cookiehandler/v1/CookieConsent.min.js", t.onload = function () { e() }, document.body.appendChild(t) }, setTrackingStatus: function () { var e = $ZSIQWidget.getEmbedObject().pinfo; if ("custom" == e.cookiePreference) if ($ZSIQWidget.getEmbedObject().centralized_cookie_handler_enabled) $ZSIQUtil.appendCookieBannerJS($ZSIQUtil.handleSelfManagedCookie); else { var t = $ZSIQUtil.cookieConfig, i = $zohosq.values.cookieconsent || []; t.isAnalytics = i.includes("analytics"), t.isFunctionality = i.includes("performance"); var n = $UTSHandler.getAPIValuesForUTS(), o = $ZSIQWidget.getWidgetObject(); o.isgdprenabled || ($ZSIQUtil.cookieConfig = { isAnalytics: !0, isFunctionality: !0 }); var r = n.tracking && "off" == n.tracking || !o.istracking, a = JSON.parse(e.pinfo.trackingprivacyconfig); $ZSIQUtil.cookieConfig.isAnalytics || r || 2 != a ? $ZSIQUtil.updateCookieForTracking(0, "2") : $ZSIQUtil.updateCookieForTracking(1, "2") } }, updateApiValues: function () { if (t && $zohosq.values) for (var e in $zohosq.values) t[e] = $zohosq.values[e]; else t = $zohosq.values; t.uvid = $UTSHandler.get("_zldt") }, isZohoCampaignEnabled: function () { var e = $ZSIQWidget.getEmbedObject().pinfo.integid; return !!e && -1 != JSON.parse(e).indexOf(6) }, isDowngradedToFreePlan: function () { try { var e = "true" == $ZSIQChat.getWidgetData().embedobj.pinfo.pinfo.isdowngradedtofreeplan, t = $ZSIQLicence.isFreePlan(); return e && t && !$ZSIQChat.isPreview() } catch (e) { } return !1 }, getCompanyLogo: function (e) { return $ZSIQUtil.isDowngradedToFreePlan() ? $ZSIQUtil.getStaticURL("/salesiq/images/logo_Zg8I0qSkbAqR2WFHt3p6CTuqpyXMFPubPcD2OT02zFN43Cy9FUNNG3NEPhM_Q1qe_.png") : e }, getStaticURL: function (e) { return e ? $ZSIQChat.getWidgetData().commondata.commonstaticurl + e : e }, getAPIValues: function () { return $ZSIQUtil.isDowngradedToFreePlan() && delete t.clogo, t }, clearApiValues: function () { t = {} }, isCSSTransformSupport: function () { try { for (var e = "transform WebkitTransform MozTransform OTransform msTransform".split(" "), t = document.createElement("div"), i = 0; i < e.length; i++)if (t && void 0 !== t.style[e[i]]) return e[i] } catch (e) { return !1 } return !1 }, onCDNFailure: function (e) { var t = $ZSIQWidget.getWidgetObject().cssstaticserver, i = $ZSIQChat.getWidgetData().jsstaticserver, n = "", o = (e.href ? e.href : e.src).match(/\.([^.]+)$/), r = $zohosq.nonce; o && (n = o[1]), "js" == n ? (link = document.createElement("script"), link.src = i) : "css" == n && (link = document.createElement("link"), link.rel = "stylesheet", link.href = t, link.type = "text/css"), r && link.setAttribute("nonce", r), document.getElementsByTagName("head")[0].appendChild(link) }, getSourceID: function () { }, fadeInAnimate: function (e, t) { var i = 0, n = setInterval(function () { 1 <= i && (clearInterval(n), e.style.display = ""), i += .1, e.style.opacity = i }, t) }, fadeOutAnimate: function (e, t, i) { var n = 1, o = setInterval(function () { n <= 0 && (clearInterval(o), e.style.display = "none", i && i()), n -= .1, e.style.opacity = n }, t) }, storeCookieVal: function (e, t, i) { if ($ZSIQNotifyCookie.isCookieAllowed(e)) { var n = new Date, o = 5; 0 == i && (o = 1440), n.setTime((new Date).getTime() + 60 * o * 1e3); var r, a = "zld" + $ZSIQWidget.getWidgetObject().lsid + e + "=" + escape(t) + ";expires= " + n.toGMTString() + ";path=/"; try { var s = ((r = $zohosq.utsvalues.trackingdomain) || (r = location.hostname.toString().replace(/^w{3}\./, ""), /^[a-zA-Z0-9-\.]+$/.test(r) || (r = "")), r || ""); a += s ? ";domain=" + s : "", $ZSIQChat.isEmbedFeatureConfEnabled() && (a += ";secure") } catch (e) { } a = $ZSIQUtil.setSameSiteCookie(a), document.cookie = a } else $ZSIQLSDB.storeInSession(e, t) }, getCookieValue: function (e) { var t = e, i = (e = "zld" + $ZSIQWidget.getWidgetObject().lsid + e, document.cookie.indexOf(e)), n = document.cookie.length; if (-1 == i) return $ZSIQLSDB.getFromSession(t) || ""; var o = i + e.length; return unescape(document.cookie.substr(o + 1, n).split(";")[0]) }, containsClass: function (e, t) { return !!e && (e.classList ? e.classList.contains(t) : new RegExp("(^| )" + t + "( |$)", "gi").test(e.classname)) }, bindClickEvent: function (e, t) { window.addEventListener ? e.addEventListener("click", t) : e.attachEvent("onclick", t) }, bindResizeEvent: function (e) { window.addEventListener ? window.addEventListener("resize", e) : window.attachEvent("resize", e) }, bindFocusEvent: function (e, t) { window.addEventListener ? e.addEventListener("focus", t) : e.attachEvent("onfocus", t) }, getBrowserLanguage: function () { for (var e = "", t = [(document.documentElement.lang || "").replace(/-/g, "_"), (navigator.language || navigator.userLanguage || "").replace(/-/g, "_")], i = 0; i < t.length; i++) { var n = t[i]; if (n) { if ("zxx" === n) continue; return e = (n = n.replace(/-/g, "_")).split("_")[0].toLowerCase(), -1 < o.indexOf(n) ? n : r[n] ? r[n] : -1 < o.indexOf(e) ? e : "" } } }, getAPILanguage: function () { return api_lang = (($ZSIQUtil.getAPIValues() || {}).language || "").replace(/-/g, "_") }, getLiveLSID: function () { return $ZSIQChat.getWidgetData().embedobj.einfo.embedid }, isChatExist: function () { try { return $ZSIQUtil.getIframe().$Support.isChatExist() } catch (e) { return "" } }, getkeylist: function () { var e = $ZSIQChat.getWidgetData(), t = e.widgetobj.lsid, i = $ZSIQUtil.getLiveLSID(), n = $ZSIQUtil.getAvuid(); return ["isiframeenabled", "DNbanner", $ZSIQUtil.getGDPRBannerCookieKey(), "ZLD" + i, "av_call", "ZLD_ApiAVCall", "ZLDTRIGGER" + i, n + "_accesstime", "ZSIQ", "ZLDPERSONALIZE", "ZLD" + t, e.widgetobj.screenname + "-"] }, checkTrackingEnabled: function () { var e = $ZSIQWidget.getWidgetObject().istracking, t = $ZSIQUtil.getAPIValues() || {}; return e || "on" == t.tracking }, isFetchGeoDetails: function () { return 1 == $ZSIQUtil.getAPIValues().fetchgeolocation }, startAfterReady: function (e) { try { var t = $ZSIQChat.getWidgetData(); $zohosq.afterReady(e, { widgetHideInfo: $zv.embedHideDetails, status: t && t.widgetobj.status }), $ZSIQUtil.updateApiValues() } catch (e) { } }, startOnLoad: function () { try { $zohosq.onload(), $ZSIQUtil.updateApiValues() } catch (e) { } }, startChatwindowOnload: function () { $zohosq.values.chatStart && !window._IS_REVAMP && $zohosq.chat.start(!0) }, idetifyVisitorData: function () { var e = $ZSIQWidget.getWidgetObject(); if (e.visitorname || e.visitoremail) { var t = $ZSIQUtil.getAPIValues(), i = $zohosq.visitor; e.visitorname && !t.name && i.name(e.visitorname), e.visitoremail && !t.email && i.email(e.visitoremail) } }, getAPIVisitorID: function () { return $ZSIQUtil.getAPIValues().id }, getIframe: function () { var e = document.getElementById("siqiframe"); return !!e && (e.contentWindow || e.contentDocument.document || e.contentDocument) }, getConfigFromStorage: function () { var e = "ZSIQ" + $zohosq.widgetcode + "data"; if (e) return $ZSIQLSDB.get(e) }, setAVUIDCookie: function (e, t) { if (e && $zohosq._domain) { var i = "ZLD" + e + "avuid", n = $ZSIQLSDB.get(i); if (n) { var o = $zohosq._domain ? 63072e6 : 31e4; $ZSIQLSDB.setCookie(i, n, o), !t && $ZSIQLSDB.setCookie("ZLD" + $zohosq.widgetcode + "avuid", n, 31e4) } } }, checkAndSetZldp: function (e, t) { $ZSIQUtil.getZldp(t) || $ZSIQUtil.setZldp(e, t) }, getReferrer: function () { return document.referrer }, setZldp: function (e, t) { $ZSIQLSDB.setCookie(t + "-_zldp", e, 31e4) }, getZldp: function (e) { return $UTSHandler.getZldp() || $ZSIQLSDB.get(e + "-_zldp") }, storeDetails: function (e) { var t = "ZSIQ" + $zohosq.widgetcode + "data"; $ZSIQLSDB.set(t, e, 864e5) }, ajax: function (e, t, i, n, o) { var r = t || function () { }, a = i || function () { }, s = new XMLHttpRequest; if ("withCredentials" in s) s.onreadystatechange = function () { 4 == this.readyState && (200 == this.status ? r(this.response) : a(this.response)) }, s.open("GET", e, !0), n && (s.withCredentials = !0); else if ("undefined" != typeof XDomainRequest) { s = new XDomainRequest; try { s.onload = function () { r(this.response) }, s.onerror = function () { a(this.response) } } catch (e) { } s.open("GET", e), n && (s.withCredentials = !0) } s.send() }, postAjax: function (e, t, i, n, o, r) { var a = i || function () { }, s = n || function () { }, c = new XMLHttpRequest; if ("withCredentials" in c) c.onreadystatechange = function () { 4 == this.readyState && 200 == this.status ? a(this.response) : s(this.response) }, c.open("POST", e), o && (c.withCredentials = !0); else if ("undefined" != typeof XDomainRequest) { c = new XDomainRequest; try { c.onload = function () { a(this.response) }, c.onerror = function () { s(this.response) } } catch (e) { } c.open("POST", e), o && (c.withCredentials = !0) } if (r) for (var d in r) r.hasOwnProperty(d) && c.setRequestHeader(d, r[d]); c.setRequestHeader("Content-Type", "application/json"), c.send(t) }, parseToJSON: function (e) { try { if (!e || "object" == typeof e) return e; e = JSON.parse(e) } catch (e) { } return e }, setText: function (e, t) { document.getElementsByTagName("body")[0]; var i = document.getElementById(e); "textContent" in document.body ? i.textContent = t : i.innerText = t }, getWebFontFamily: function () { var e = document.body, t = "", i = $ZSIQWidget.getWidgetObject(); return i.font[0] == $ZSIQUtil.STATUS_ENABLE && i.font[1].default && "0" != i.font[1].default ? t = a["" + i.font[1].default] : e.currentStyle ? t = e.currentStyle.fontFamily : document.defaultView && (t = document.defaultView.getComputedStyle(e, null).getPropertyValue("font-family")), t || "salesiq-font" }, getImageURL: function (e, t) { var i = t.fpath, n = "", o = "undefined" != typeof $ZSIQChat && $ZSIQChat.getWidgetData().usedownloadserver, r = t.ispreview || !1; if (o && i) { var a = i.split("/")[0]; if ("fembedcss" == a || "floatimagepreview" == a || "floatimage" == a || "embedcss" == a || "fgravatar" == a || "fsticker_online" == a || "fsticker_offline" == a || "bsticker_online" == a || "bsticker_offline" == a || "ssticker_online" == a || "ssticker_offline" == a || "sgallery" == a || "psticker_online" == a || "psticker_offline" == a || "apps_custom_sticker" == a || "0_embedcss" == a || "8_embedcss" == a) { var s = $ZSIQWidget.getEmbedObject().pinfo.soid, c = i.split("/")[1], d = t.fname ? t.fname : t.pfname; if ($ZSIQChat.getWidgetData().commondata.UDServerRevamp) { var l = { "x-siq-soid": s, "x-siq-module": "brands", "x-siq-type": a, "x-siq-parentid": $ZSIQChat.getWidgetData().embedobj.einfo.embedid, "x-siq-resourceid": c, "x-siq-filename": d, "x-siq-mode": r ? "preview" : "view" }; n = UDHandler.getUDDownLoadLink(l) } else { var u = { "x-siq-filetype": a, "x-siq-lsid": c.split("_")[1], "x-siq-soid": s, "x-siq-ispreview": r, "x-siq-pfname": d }; n = UDHandler.getDownLoadLink(c, "default", u) } return n } } return n = $ZSIQChat.getWidgetData().embedobj.schema + "://" + $ZSIQWidget.getEmbedObject().embedserverurl + "/" + e.screenname + "/" + i + "/photo.ls", 1 == t.ispreview && (n += "?ispreview=true"), n }, blinkTitle: function (e) { if (!e) return !1; s || (c = document.title, s = !0), clearTimeout(n); var t, i = ""; n = setInterval(function () { t = "title" == t ? (i = e, "chatmsg") : (i = c, "title"), document.title = i }, 500) }, stopBlinking: function () { s && (clearTimeout(n), s = !1, document.title = c) }, setShrinkImageCSS: function (e, t, i) { var n, o, r, a, s = (n = e.naturalHeight, o = e.naturalWidth, r = { WIDTH: o, HEIGHT: n }, a = [i / o, t / n], 1 < (a = Math.min(a[0], a[1])) ? r : r = { WIDTH: o * a, HEIGHT: n * a }); e.style.height = s.HEIGHT + "px", e.style.width = s.WIDTH + "px" }, isValidPhoneNo: function (e) { return !!e && new RegExp("^[+0-9():.\\-\\[\\] ]+$").test(e) }, getProductURLForImage: function () { if (!$ZSIQChat.getWidgetData().commondata.isdev) return ""; var e = $ZSIQChat.getWidgetData().embedobj; return e.schema + "://" + e.producturl }, getURLParameterByName: function (e) { if (d) return d[e]; d = {}; for (var t = window.location.search.replace(/^\?/g, "").split("&"), i = [], n = 0; n < t.length; n++)i = t[n].split("="), d[i[0]] = i[1]; return d[e] }, linkifySubString: function (e) { try { e = e.replace(/((?:href|src)=["']?)?((https?:\/\/(?:www\.)?|www\.)[a-z0-9][^@]*?)(?=&quot;|&#x27;|&lt;|[\s"'<]|$)/gi, function (e, t, i, n) { var o = "www." === n ? "http://" + i : i; window.location.protocol, $ZSIQWidget.getEmbedObject().embedserverurl; return t ? e : "<a rel='noopener noreferrer' href='" + o + "' target='_blank' class='zsiq_hyperlink' >" + i + "</a>" }) } catch (e) { } return e }, isEmpty: function (e) { for (var t in e) if (e.hasOwnProperty(t)) return !1; return !0 }, checkAndGetParsedVal: function (e) { var t = $ZSIQUtil.parseToJSON(e); return "object" == typeof t || "boolean" == typeof t ? t : e }, formWidgetObject: function (e) { var t = {}; for (var i in e) t[i] = $ZSIQUtil.checkAndGetParsedVal(e[i]); return $ZSIQAnalytics = t.analytics, $ZSIQAutopick = $zv.autopick || t.autopick, _ZSIQ.brandname = t.brandname, t }, formEmbedObject: function (e) { var t = {}; for (var i in e) t[i] = $ZSIQUtil.checkAndGetParsedVal(e[i]); return t }, getObjectKeys: function (e) { if (Object.keys) return Object.keys(e); var t = []; for (var i in e) e.hasOwnProperty(i) && t.push(i); return t }, getGDPRBanner: function (e, t) { var i, n = t.trackingprivacystatement ? t.trackingprivacystatement : e.i18nkeys["gdpr.banner.notify"], o = e.i18nkeys["gdpr.banner.button.ok"], r = e.i18nkeys["gdpr.banner.button.donottrack"], a = (t.trackingprivacystatement ? t.trackingprivacystatement : e.i18nkeys["gdpr.banner.donottrack"], e.i18nkeys["gdpr.banner.learnmore"]), s = !((!(i = $UTSHandler.getAPIValuesForUTS()).tracking || "off" != i.tracking) && e.istracking), c = "", d = e.i18nkeys["gdpr.banner.preferences"], l = e.i18nkeys["gdpr.banner.cookie.acceptall"]; if ((i = $ZSIQUtil.getAPIValues()).trackbannerobj) { var u = i.trackbannerobj; if (n = u.description && 0 != u.description.trim().length ? u.description : n, o = u.acceptbutton && 0 != u.acceptbutton.trim().length ? u.acceptbutton : o, r = u.declinebutton && 0 != u.declinebutton.trim().length ? u.declinebutton : r, u.policytextandlink) { var g = u.policytextandlink; a = g.text && 0 != g.text.trim().length ? g.text : a, t.cookiepolicyurl = g.link && 0 != g.link.trim().length && $ZSIQUtil.isValidURL(g.link) ? g.link : t.cookiepolicyurl } } t.cookiepolicyurl && (c = "<a class='siq-lnmor' id='gdprbannerurl' target='_blank' rel='noopener noreferrer'>" + $ZSIQUtil.getEncodedText(a) + "</a>"), n = $ZSIQUtil.getEncodedText(n); var p = $ZSIQWidget.getEmbedObject().pinfo, f = p.cookiePreference; if (p.cookieSegregation) { if ("custom" == f) return ""; if ($ZSIQWidget.getEmbedObject().centralized_cookie_handler_enabled) return $ZSIQUtil.appendCookieBannerJS(function () { $ZSIQUtil.handleOneCookie(e, t) }), ""; if (f && "default" == f) switch (JSON.parse(t.trackingprivacyconfig)) { case 1: return "<div id='gdprbanner' class='siq-msgbanr'><span class='dib-mid'>" + n + c + "</span><div class='siq-clkoptn'><div class='siq-okbtn dib-mid' type=0 config=" + t.trackingprivacyconfig + " docclick='updateCookieForTracking'>" + o + "</div></div></div>"; case 2: return "<div id='gdprbanner' class='siq-msgbanr'><span class='dib-mid'>" + n + c + "</span><div class='siq-clkoptn'><div class='siq-okbtn siq-white dib-mid' type=0 config=" + t.trackingprivacyconfig + "  docclick='updateCookieForTracking' >" + l + "</div><div class='siq-okbtn dib-mid' type=0 docclick = 'showCookiePreferences' > " + d + "</div > </div></div>" } } else switch (JSON.parse(t.trackingprivacyconfig)) { case 1: return "<div id='gdprbanner' class='siq-msgbanr'><span class='dib-mid'>" + n + c + "</span><div class='siq-clkoptn'><div class='siq-okbtn dib-mid' type=0 config=" + t.trackingprivacyconfig + " docclick='updateCookieForTracking'>" + o + "</div></div></div>"; case 2: return "<div id='gdprbanner' class='siq-msgbanr'><span class='dib-mid'>" + n + c + "</span><div class='siq-clkoptn'><div class='siq-okbtn dib-mid' type=0 config=" + t.trackingprivacyconfig + "  docclick='updateCookieForTracking' >" + o + "</div>" + (s ? "" : "<div class='siq-trkbtn dib-mid' type=1 config=" + t.trackingprivacyconfig + " docclick='updateCookieForTracking'>" + r + "</div>") + "</div></div>" } }, handleOneCookie: function (e, t) { var i = ZohoCookie(); if (i.isCookieSet()) $ZSIQUtil.updateTrackingStatus(i.getSelectedCookies()); else { var n = e.i18nkeys, o = $ZSIQWidget.getWidgetObject().i18nChatwindowKeys, r = JSON.parse(t.trackingprivacyconfig), a = [], s = o["cookie.learnmore"], c = "http_cookie", d = t.cookiepolicyurl, l = $ZSIQUtil.getEncodedText(t.trackingprivacystatement) || n["gdpr.banner.notify"], u = n["gdpr.banner.learnmore"], g = n["gdpr.banner.cookie.acceptall"], p = n["gdpr.banner.preferences"], f = n["gdpr.banner.button.ok"], S = $zv.trackbannerobj; if (S) { var h = S.policytextandlink || {}; l = $ZSIQUtil.getEncodedText(S.description) || l, u = $ZSIQUtil.getEncodedText(h.text) || u, d = h.link || d; var m = $ZSIQUtil.getEncodedText(S.acceptbutton); g = m || g, f = m || f, p = $ZSIQUtil.getEncodedText(S.preferencebutton) || p } d && (d = $ZSIQUtil.getURLProtocol(d)), 1 === r ? a.push({ title: f }) : 2 === r && a.push({ title: g, style: { "background-color": "#fff", color: "#333" } }, { title: p }), i.showConsent({ title: l, linkTitle: u, linkUrl: d, buttons: a, tableButtons: [{ title: n["gdpr.banner.decline"] }, { title: n["gdpr.banner.accept"] }], header: { title: o["cookie.your.preference"], description: o["cookie.your.preference.desc"], linkTitle: o["cookie.policy"], openInNewTab: !0, linkUrl: d || "https://www.zoho.com/salesiq/cookies-policy.html" }, cookie_types: { essential: { title: o["cookie.essential"], description: o["cookie.essential.desc"], linkTitle: s, meta: [{ name: "'JSESSIONID'", expiry: "When the browsing session ends", purpose: "This cookie is generated by servlet containers like Tomcat and used for session management for the HTTP protocol", url: c }, { name: "'LS_CSRF_TOKEN'", expiry: "When the browsing session ends", purpose: "This cookie is used for security purposes in order to avoid Cross-Site Request Forgery, (CSRF) for the AJAX calls made by the visitor", url: c }, { name: "'gdpr_'+<@screenname@>+'__donottrack'", expiry: "1 month", purpose: "This cookie stores the GDPR consent from the visitor - enable/disable tracking", url: c }, { name: "e3de1f7d42(random checksum)", expiry: "When the browsing session ends", purpose: "This cookie is used for internal load balancing of SalesIQ servers", url: c }, { name: "'fbpgsr_'+<@lsid@>", expiry: "Until cleared", purpose: "Used in the Facebook app to authenticate the SalesIQ API request", url: c }, { name: "uesign", expiry: "1 Month", purpose: "This cookie is used to manage the security of the applications.", url: c }] }, performance: { title: o["cookie.performance"], description: o["cookie.performance.desc"], linkTitle: s, meta: [{ name: "isiframeenabled", expiry: "1 day", purpose: "This cookie is set when the Live Chat feature is disabled by proactive chats/trigger/JSAPI", url: c }, { name: "'zld'+<@lsid@>+'Article_'+<@article_id@>", expiry: "1 day", purpose: "This cookie stores if an article/FAQ is liked or not by the visitor", url: c }, { name: "'ZLD'+<@appid@>+'WAITING'", expiry: "Embed waiting time", purpose: "This cookie stores the waiting time of the chat time details for a chat", url: c }, { name: "'ZLD'+<@appid@>+'WTIME'", expiry: "Embed waiting time", purpose: "This cookie stores the remaining waiting time details for a chat", url: c }, { name: "cdn_status", expiry: "2 days", purpose: "This cookie stores the cdn status for getting the static files", url: c }, { name: "<@chatid@>+'_translate'", expiry: "1 day", purpose: "This cookie stores whether translation is enabled or not", url: c }, { name: "'gdpr_'+<@screenname@>+'_trackingconfig'", expiry: "1 Month", purpose: "This cookie stores GDPR consent configured in portal side to handle the visitor end widget accordingly with consent banners", url: c }] }, analytics: { title: o["cookie.analytics"], description: o["cookie.analytics.desc"], linkTitle: s, meta: [{ name: "<@screenname@>+'-'_zldp'", expiry: "2 years", purpose: "This cookie identifies the unique visitors for the website", url: c }, { name: "<@screenname@>+'-'_zldt'", expiry: "1 day", purpose: "This cookie identifies unique visits for a visitor in the website", url: c }, { name: "<@screenname@>+'-'_siqid'", expiry: "2 years", purpose: "This cookie helps you track users across all domains", url: c }, { name: "<@screenname@>+'-'_uuid'", expiry: "2 years", purpose: "This cookie provides user id needed in REST API to get the data of the current user.", url: c }, { name: "<@screenname@>+'-'mc_cid'", expiry: "1 day", purpose: "This cookie tracks Mailchimp details about the visitor via campaign id & member email's unique id", url: c }, { name: "<@screenname@>+'-'mc_eid'", expiry: "1 day", purpose: "This cookie tracks Mailchimp details about the visitor via unique id & member email's unique id", url: c }, { name: "_zldvfp", expiry: "6 months", purpose: "Used to identify visitors referred from email campaigns", url: c }, { name: "'ZLD'+<@appid@>", expiry: "1 week", purpose: "This cookie stores the Live Chat attender details and visitor details", url: c }, { name: "'ZLD'+<@lsid@>+'avuid'", expiry: "1 week", purpose: "This cookie stores the avuid unique id to denote a visitor", url: c }, { name: "'ZLDTRIGGER'+<@appid@>", expiry: "1 week", purpose: "This cookie stores the trigger details when the visitor is contacted", url: c }, { name: "ZLDPERSONALIZE", expiry: "180 seconds", purpose: "This cookie stores attender ID in cookie for personalized chat", url: c }, { name: "'ZLD'+<@lsid@>+'tabowner'", expiry: "180 seconds", purpose: "This cookie stores WMS session ID", url: c }, { name: "<@avuid@>+'_accesstime'", expiry: "1 day", purpose: "This cookie stores the last fetched conversation's end time", url: c }] } }, preferenceButtons: [{ title: o["cookie.use.essential"] }, { title: n["gdpr.banner.accept"] }], handler: function (e, t) { $ZSIQUtil.updateTrackingStatus(t.selected_cookies) } }) } }, getGDPRBannerCookieKey: function (e) { return "gdpr_" + (e = e || $ZSIQWidget.getWidgetObject().screenname) }, updateCookieForTracking: function (e, t) { var i = $ZSIQUtil.getGDPRBannerCookieKey(), n = ($ZSIQUtil.getGDPRBannerCookieKey(), $ZSIQUtil.getAPIValues() || {}); i += "__donottrack"; $ZSIQWidget.getEmbedObject().cookie_optimization; var o = function () { handleIframeFunction(function (e) { e.$Support.handleDomainStorage("updatecorsdata", "cookie") }) }, r = $ZSIQChat.getWidgetData().embedobj.einfo.embedid; switch ($ZSIQLSDB.storeInLocalStorage("ZSIQ" + r + "-cookie_config", $ZSIQUtil.cookieConfig), e) { case 0: $ZSIQNotifyCookie.handleGotit(), $ZSIQCookie.set(i, 0, 2592e6, !0), $ZSIQUtil.hideGDPRBanner(), "on" == n.reset && $ZSIQUtil.checkTrackingEnabled() && $ZNotifyTracking(1, "on"), $UTSHandler.init(!0), o(); break; case 1: $ZSIQNotifyCookie.handleDNT(), $ZSIQCookie.set(i, 1, 2592e6, !0), $ZNotifyTracking(1, "off"), $ZSIQUtil.hideGDPRBanner(), o() } }, checkGDPRBannerStatus: function (e, t, i) { var n = i.pinfo.pinfo.trackingprivacyconfig, o = $ZSIQUtil.getGDPRBannerCookieKey(t); switch (e) { case 0: return 0 != $ZSIQCookie.get(o + "__donottrack").length; case 1: return 0 == $ZSIQCookie.get(o + "__donottrack"); case 2: return 1 == $ZSIQCookie.get(o + "__donottrack") && 2 == n }return !1 }, hideGDPRBanner: function () { var e = document.getElementById("gdprbanner"); e && $ZSIQWidgetUI.addClass(e, "gdprbanner_slide") }, getGDPRPendingStatus: function (e, t, i) { var n = !1; try { if (t.pinfo && t.pinfo.pinfo) { var o = t.pinfo.pinfo, r = JSON.parse(o.isgdprenabled), a = 1 == e.widgetobj.isgdprenabled, s = o.trackingprivacyconfig; r && 0 != s && !$ZSIQUtil.checkGDPRBannerStatus(0, i, t) && a && (n = !0) } } catch (e) { } return n }, isValidURL: function (e) { var t = new RegExp(/^(((http|https):\/\/(www.){0,1})|www.){1}[a-zA-Z0-9]+[-a-zA-Z0-9@:/%_+.~#?&/=]*[^.]$/); return void 0 !== e && t.test(e) }, hyperLinksSubString: function (e, t, i) { try { if (-1 !== e.indexOf(t) && 0 != i.length) { var n = e.length, o = t.length, r = e.substring(0, e.indexOf(t)), a = e.substring(e.indexOf(t), e.indexOf(t) + o), s = e.substring(e.indexOf(t) + o, n); e = r + (a = "<a class='siq-lnmor' target='_blank' rel='noopener noreferrer' href='" + (i = -1 == i.indexOf("http") ? "http://" + i : i) + "'>" + a + "</a>") + s } else 0 != i.length && (e += " <a class='siq-lnmor' rel='noopener noreferrer' target='_blank' href='" + (i = -1 == i.indexOf("http") ? "http://" + i : i) + "'>" + t + "</a>") } catch (e) { } return e }, stringify: function (e) { try { return Object.toJSON ? Object.toJSON(e) : JSON.stringify(e) } catch (e) { } }, getNavigationObj: function (e) { var t = e ? e.document : null, i = ""; if (s && c && (i = c), t && t.location && t.location.protocol) { if (-1 == e.location.protocol.indexOf("http") && e.parent && e.parent != e) return $ZSIQUtil.getNavigationObj(e.parent); var n; (n = {}).current_page = $zohosq.visitor.cpage() || t.location.href.substring(0, 3072), n.page_title = $zohosq.visitor.pagetitle() || i || t.title.substring(0, 3072), n.referer = $zohosq.visitor.referer() || t.referrer.substring(0, 3072); try { n.lsid = $ZSIQWidget.getWidgetObject().lsid } catch (e) { } return n } (n = {}).current_page = $zohosq.visitor.cpage() || document.location.href.substring(0, 3072), n.page_title = $zohosq.visitor.pagetitle() || i || document.title.substring(0, 3072), n.referer = $zohosq.visitor.referer() || document.referrer.substring(0, 3072); try { n.lsid = $ZSIQWidget.getWidgetObject().lsid } catch (e) { } return n }, getAvuid: function () { return "undefined" == typeof $ZSIQWidget || window._ZSIQ_RESET_ESSENTIAL ? $ZSIQLSDB.get("ZLD" + $ZSIQChat.getWidgetData().widgetobj.lsid + "avuid") : ($ZSIQWidget.avuid || ($ZSIQWidget.avuid = $ZSIQChat.avuidval || $ZSIQLSDB.get("ZLD" + $ZSIQChat.getWidgetData().widgetobj.lsid + "avuid")), $ZSIQWidget.avuid) }, getLiveLSIDFromStorage: function (e) { var t = $ZSIQUtil.getConfigFromStorage(); try { return "rawid" == e ? t.widgetobj.lsid : t.embedobj.einfo.embedid } catch (e) { return "" } }, checkStoredIdAndRemoveStorage: function (e) { var t = $ZSIQUtil.getLiveLSIDFromStorage("rawid"), i = $ZSIQLSDB.get("ZLD" + $zohosq.widgetcode + "cvuid"), n = $ZSIQUtil.getConfigFromStorage(); if (n && n.embedobj && n.embedobj.conversation_newflow_enabled && i && i != e) { var o = $ZSIQUtil.getLiveLSIDFromStorage(); if (!o) return; var r = ["ZLD" + o, "ZLD" + o + "WAITING", "ZLDTRIGGER" + o, "ZLD" + o + "QUEUEINGCHATS", "ZLD" + o + "WTIME"], a = r.length; $ZSIQLSDB.removeSessionStore($ZSIQLSDB.get("ZLD" + t + "avuid") + "_formcontextinitiated"), $ZSIQLSDB.removeSessionStore("ZLD" + o + "lastview"); for (var s = 0; s < a; s++)$ZSIQLSDB.remove(r[s]) } var c = $zohosq._domain ? 63072e6 : ""; $ZSIQLSDB.setCookie("ZLD" + $zohosq.widgetcode + "cvuid", e, c) }, getElementHeight: function (e) { var t = getComputedStyle(e).display; e.style.display = "inline-block"; var i = e.offsetHeight; return e.style.display = t, i }, isObjectdeepEqual: function (e, t) { if (e === t) return !0; if (void 0 === t || void 0 === t) return !1; var i = Object.getOwnPropertyNames(e), n = Object.getOwnPropertyNames(t); if (i.length != n.length) return !1; for (var o = 0; o < i.length; o++) { var r = i[o]; switch (typeof e[r]) { case "object": if (!this.isObjectdeepEqual(e[r], t[r])) return !1; break; case "function": if (void 0 === t[r] || e[r].toString() != t[r].toString()) return !1; break; case "number": if (isNaN(e[r]) && isNaN(t[r])) break; default: if (e[r] != t[r]) return !1 } } return !0 }, bindEventsForImgPreview: function () { document.getElementById("zsiqimagepreview").addEventListener("click", function (e) { switch (e.target.getAttribute("docclick")) { case "zsiqclose": $ZSIQChatWindow.closeImagePreview(e); break; case "zsiqstartchat": $ZSIQChatWindow.startChat(e); break; case "zsiqfaqvote": $ZSIQChatWindow.updateVote(e, e.target); break; case "toggletocview": $ZSIQChatWindow.toggleTocContainer(); break; case "toggletocchild": $ZSIQChatWindow.toggleTocChildView(e.target); break; case "articlesccrolltoview": $ZSIQChatWindow.articleScrollToView(e.target) } }) }, preConnectDomains: function () { if (Array.isArray(window._STATICURLS)) { for (var e = $ZSIQChat.getScriptSource().split("://")[0] + "://", t = _STATICURLS.length, i = $zohosq.nonce, n = 0; n < t; n++) { var o = _STATICURLS[n]; -1 == o.indexOf("https://") && (o = e + o); var r = document.createElement("link"); r.rel = "preconnect", r.href = o, i && r.setAttribute("nonce", i), document.head.appendChild(r), r.parentNode.removeChild(r), r = null } window._STATICURLS.length && ($ZSIQUtil.staticURL = _STATICURLS), delete window._STATICURLS } }, getEncodedText: function (e) { try { e && "string" == typeof e && (e = (e = e.replace(/&#39;|&#x27;/g, "'").replace(/&quot;/g, '"').replace(/&gt;/g, ">").replace(/&lt;/g, "<").replace(/&amp;/g, "&").replace(/&#x2F;/g, "/")).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#x27;").replace(/\//g, "&#x2F;")) } catch (e) { } return e }, bindEventsCookieBanner: function () { var e = document.getElementById("gdprbanner"); e && e.addEventListener("click", function (e) { var t = e.target, i = t.getAttribute("docclick"); if ("updateCookieForTracking" == i) return $ZSIQUtil.cookieConfig = { isFunctionality: !0, isAnalytics: !0 }, void $ZSIQUtil.updateCookieForTracking(JSON.parse(t.getAttribute("type")), t.getAttribute("config")); "showCookiePreferences" != i || $ZSIQNotifyCookie.populateUI() }) }, getUrlParamObj: function (e) { var t = {}; if (e) { for (var i = (e.split("?")[1] || "").split("&"), n = 0; n < i.length; n++) { var o = i[n].split("="); t[o[0]] = o[1] } return t } }, getIframeSupportObj: function () { var e = $ZSIQUtil.getIframe(); return e && e.$Support && e.$Support.EmbedObj ? e.$Support.EmbedObj : {} } } }(), $ZSIQLicence = { verify: function (e) { var t = $ZSIQUtil.getIframe().$Support.EmbedObj.linfo[e]; return t && JSON.parse(t) || !1 }, isFreePlan: function () { var e = parseInt($ZSIQChat.getWidgetData().embedobj.linfo.planid); return -1 < [1, 7, 12, 29].indexOf(e) } }; function handleIframeFunction(e) { var t = $ZSIQUtil.getIframe(); t && t.$Support && e(t) } var $UTSHandler = $UTSHandler || function () { var S = function (t, e) { var i, n = {}; n[t] = e; try { i = $ZSIQUTS.getFromSession("utssynccache") || $ZSIQUtil.stringify([]), (i = JSON.parse(i)).push(n) } catch (t) { i = [] } $ZSIQUTS.storeInSession("utssynccache", $ZSIQUtil.stringify(i)) }, a = function (t, e) { var i = e[t]; switch (parseInt(t)) { case 1: $UTSHandler.handleTriggerCustomAction(function () { $UTSHandler.handleChatTriggers(i.type, i.data) }); break; case 2: $ZSIQChatWindow.openChatWindow(!0); break; case 3: $zohosq.floatbutton.visible("show"); break; case 4: $ZSIQUTSAction.handleButtonGlow(); break; case 5: $ZSIQUTSAction.handleAnimate(); break; case 6: $ZSIQUtil.getIframe().$Support.setUVID(i.uvid); break; case 7: $UTSHandler.handleTrackChat(i.data); break; case 8: $UTSHandler.handleUTSConnect(i.data); break; case 9: $UTSHandler.handleTriggerCustomAction(function () { $UTSHandler.handleBotTrigger(i) }) } }; return { notifyCustomEvent: function (t) { $ZSIQUTS.notifyCustomEvent(t) }, handleTriggerCustomAction: function (t) { if (window._IS_REVAMP && !$ZSIQUtil.getIframe()) return void $ZSIQChatWindow.populateIframe(t); t() }, getUTSCachedata: function () { var t = $ZSIQUTS.getFromSession("utssynccache"); if (t) { for (var e in t = JSON.parse(t)) if (t.hasOwnProperty(e)) { var i = t[e]; for (var n in i) i.hasOwnProperty(n) && a(n, i) } $ZSIQUTS.storeInSession("utssynccache", $ZSIQUtil.stringify([])) } }, storeInSession: function (t, e) { $ZSIQUTS.storeInSession(t, e) }, get: function (t) { return $ZSIQUTS.get(t) }, getFromSession: function (t) { return $ZSIQUTS.getFromSession(t) }, updateAction: function (t) { $ZSIQUTS.updateAction(t) }, handleApiChange: function (t, e) { $ZSIQUTS.handleApiChange(t, e) }, init: function (t) { $ZSIQUTS.init(t) }, getUTSActionData: function () { return $ZSIQUTSAction.getUTSActionData() }, handleChatTriggers: function (e, i) { var t = $ZSIQChat.getWidgetData(); if (1 != $ZSIQUtil.formWidgetObject(t.widgetobj).hideembed) try { var n = $ZSIQUtil.getAPIValues(), a = $ZSIQUtil.getAvuid(); if ($ZSIQLSDB.getFromSession(a + "_formcontextinitiated")) return; IframeHandler.getTriggerCookie() && n.triggermsg && $ZSIQWidgetUI.getWidgetState() != $ZSIQWidgetUI.F_WINDOW ? IframeHandler.incrementUnreadCount() : $ZSIQWidgetUI.setWidgetState($ZSIQWidgetUI.F_WINDOW), IframeHandler.handleTrigger(e, i) } catch (t) { var r = {}; r.type = e, r.data = i, S(1, r) } }, openChatWindow: function () { try { $ZSIQChatWindow.openChatWindow(!0) } catch (t) { S(2, {}) } $UTSHandler.updateAction({ type: "1" }) }, showButton: function () { try { $zohosq.floatbutton.visible("show") } catch (t) { S(3, {}) } }, getFloatStatus: function () { try { return $ZSIQWidget.getWidgetStatus() } catch (t) { var e = $ZSIQChat.getWidgetData(); return $ZSIQUtil.formWidgetObject(e.widgetobj).status } }, getFloatDiv: function (e) { try { return $ZSIQWidgetUI.getWidgetDiv() } catch (t) { return S(e, {}), null } }, getFloatState: function () { return $ZSIQWidgetUI.getWidgetState() }, setUVID: function (e) { $ZSIQUtil.getAPIValues().uvid = e; try { IframeHandler.updateUVID(e) } catch (t) { S(6, { uvid: e }) } $zv.uvid = e }, setUUID: function (t) { t && ($ZSIQUtil.getAPIValues().uuid = t, $zv.uuid = t) }, getDomain: function () { var t = $zohosq.utsvalues.trackingdomain; return t || (t = location.hostname.toString().replace(/^w{3}\./, ""), /^[a-zA-Z0-9-\.]+$/.test(t) || (t = "")), t || "" }, handleTrackChat: function (e) { try { if ("object" != typeof $ZSIQChatWindow) return void setTimeout(function () { $UTSHandler.handleTrackChat(e) }, 1e3); $ZSIQChatWindow.handleIframeLoading(); var t = $ZSIQUtil.getAvuid(); if ($ZSIQLSDB.getFromSession(t + "_formcontextinitiated")) return; var i = 1 == $ZSIQWidgetUI.getWidgetState() ? $ZSIQWidgetUI.F_BUTTON : $ZSIQWidgetUI.F_WINDOW; $ZSIQWidgetUI.setWidgetState(i), IframeHandler.handleProActiveChat(e) } catch (t) { var n = {}; n.data = e, S(7, n) } }, handleUTSConnect: function (e) { try { $ZSIQUtil.getIframe().$Support.Util.handleUTSConnect(e) } catch (t) { var i = {}; i.data = e, S(8, i) } }, handleBotTrigger: function (e) { try { var t = $ZSIQChat.getWidgetData(), i = $ZSIQUtil.formWidgetObject(t.widgetobj), n = $ZSIQUtil.getAvuid(); if (1 == i.hideembed || $ZSIQLSDB.getFromSession(n + "_formcontextinitiated")) return; var a = $ZSIQUtil.getAPIValues(), r = $ZSIQUtil.getIframe().$Support; if (r.isChatExist()) return; var o = IframeHandler.getTriggerCookie(); if (e.lsid && e.lsid != IframeHandler.getLsId()) return; if (o && a.triggermsg && $ZSIQWidgetUI.getWidgetState() != $ZSIQWidgetUI.F_WINDOW ? r.incrementUnreadCount() : $ZSIQWidgetUI.setWidgetState($ZSIQWidgetUI.F_WINDOW), IframeHandler.getTriggerCookie()) return; IframeHandler.handleBotTrigger(e); try { $UTSHandler.updateAction({ type: "7" }) } catch (t) { } } catch (t) { S(9, e) } }, formatDataForBotTrigger: function (t) { return { aboutme: t.aboutme, attender: t.attender, attender_imagefkey: t.attender_imagefkey, msglist: t.msglist, istrigmsg: !0, isbottrigmsg: !0, isBotChat: !0, winid: t.time } }, getAPIValuesForUTS: function () { var t = $ZSIQUtil.getAPIValues(), e = $ZSIQChat.getWidgetData(), i = $ZSIQUtil.formWidgetObject(e.widgetobj); return t.name = t.name || i.visitorname, t.email = t.email || i.visitoremail, t }, setTrackedfrommailchimp: function (t) { $ZSIQChat.istrackedfrommailchimp = t }, isTrackedfrommailchimp: function () { return $ZSIQChat.istrackedfrommailchimp }, getUTSSID: function () { return $ZSIQUTS.getUTSSID() }, getUTSName: function () { return $ZSIQUTS.getUTSName() }, getEqualDomains: function () { return $zohosq.utsvalues.equalhosts }, setSIQID: function (t) { t && ($zohosq.utsvalues.siqid = t) }, getSIQID: function () { return $zohosq.utsvalues.siqid }, isForceSecure: function () { return $zohosq.utsvalues.forcesecure }, getZldp: function () { try { return $ZSIQUTS.get("_zldp") } catch (t) { if ("undefined" != typeof $UTSConnector) return $UTSConnector.util.db.getFromCookie({ key: "_zldp" }) || $UTSConnector.util.db.getFromStorage({ type: "localStorage", key: "_zldp" }) } } } }(); var $ZSIQUTS = $ZSIQUTS || function (h) { var T, b, d, y, u, U, $, a, s, t, m, o = !1, r = 0, c = 1, l = 2, n = 0, e = 1, i = -1, f = c, g = 63072e6, S = function (e, t) { var n = t ? "ws" : "http"; return !e && e.location ? n + "://" : e.location.protocol ? -1 == e.location.protocol.indexOf("http") && e.parent && e.parent != e ? S(e.parent, t) : -1 != e.location.protocol.indexOf("https") ? n + "s://" : n + "://" : S(e.parent, t) }, p = { net: {}, util: {}, moninterval: 1e4 }, v = (re = navigator.userAgent, ae = re.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+[^\s|\$]+)/i) || [], /trident/i.test(ae[1]) ? "IE " + ((oe = /\brv[ :]+(\d+)/g.exec(re) || [])[1] || "") : "Chrome" === ae[1] && null != (oe = re.match(/\b(OPR|Edge)\/(\d+)/)) ? oe.slice(1).join(" ").replace("OPR", "Opera") : (ae = ae[2] ? [ae[1], ae[2]] : [navigator.appName, navigator.appVersion, "-?"], null != (oe = re.match(/[v|V]ersion\/(\d+[^\s|\$]+)/i)) && ae.splice(1, 1, oe[1]), ae.join(" "))), I = !1; /ios 10.1/i.test(v) && (I = !0), "undefined" != typeof WebSocket && !1 in WebSocket.prototype && (I = !0), navigator.userAgent.match(/Lumia.*/) && (I = !0); var q = function () { return d }, _ = function (e) { return g < e ? g : e }, k = function () { return t }, C = function (e) { t = e }; p.seqmsg = { servercount: -1, clientcount: -1, isconnectionmade: !1, maxexpirytime: 6e5, maxdelay: 6e4 }, p.seqmsg.init = function (e) { e || 0 == e || (e = -1), p.seqmsg.servercount = e, p.seqmsg.getSequenceNo(), -1 == p.seqmsg.clientcount && (p.seqmsg.clientcount = e) }, p.seqmsg.getServerUpdatedcount = function () { return p.seqmsg.servercount }, p.seqmsg.updateServerUpdatedcount = function (e) { e || 0 == e || (p.seqmsg.servercount = -1), p.seqmsg.servercount = e }, p.seqmsg.getSequenceNo = function () { return -1 == p.seqmsg.clientcount && (p.seqmsg.clientcount = p.seqmsgdb.getSequenceNumber(!0)), p.seqmsg.clientcount }, p.seqmsg.incrementSequenceNo = function () { p.seqmsg.clientcount++ }, p.seqmsg.enroll = function (e, t) { if (p.seqmsg.isconnectionmade) { p.seqmsg.incrementSequenceNo(); var n = p.seqmsgdb.getFromDB(), i = {}; i.data = e, i.time = (new Date).getTime() + p.seqmsg.maxexpirytime, n[p.seqmsg.clientcount] = i, p.seqmsgdb.updateInDB(n, p.seqmsg.maxexpirytime) } }, p.seqmsg.asyncResend = function (t) { if (p.seqmsg.isconnectionmade && t) { var e = ""; try { e = t.shift() } catch (e) { p.notifier.doLog("Exception array shift -> " + e.message + " stack -> " + e.stack + " name -> " + e.name + " missedseq -> " + t) } if (e) { var n = p.seqmsgdb.getFromDB()[e]; if (n) { var i = n.data; p.notifier.sendData(i, void 0, !1), p.seqmsg.clear(e) } setTimeout(function () { p.seqmsg.asyncResend(arguments[0]) }, 0, t) } } }, p.seqmsg.clear = function (e) { var t = p.seqmsgdb.getFromDB(); e ? delete t[e] : t = {}, p.seqmsgdb.updateInDB(t, p.seqmsg.maxexpirytime) }, p.seqmsgdb = {}, p.seqmsgdb.getFromDB = function (e) { var t = $ZSIQUTS.getFromSession("seq_msgs") || "{}"; return t = JSON.parse(t), e ? t[e] || {} : t }, p.seqmsgdb.getSequenceNumber = function (e) { var t, n = p.seqmsgdb.getFromDB(); for (var i in n) i = parseInt(i), t || (t = i), e ? t < i && (t = i) : i < t && (t = i); return t || -1 }, p.seqmsgdb.updateInDB = function (e, t) { $ZSIQUTS.storeInSession("seq_msgs", JSON.stringify(e), t) }; var O = function () { try { return !!h.sessionStorage && (sessionStorage.setItem("quota_test", "1"), sessionStorage.removeItem("quota_test"), !0) } catch (e) { } return !1 }, w = function () { try { return !!h.localStorage && (localStorage.setItem("quota_test", "1"), localStorage.removeItem("quota_test"), !0) } catch (e) { } return !1 }, Q = function (e, t, n) { if (e = z(e), $ZSIQNotifyCookie.isCookieAllowed(e)) { var i = new Date; n = n || 864e5, i.setTime(i.getTime() + n); var o = e + "= " + t + ";expires= " + i.toGMTString(); o += ";path=/"; try { var r = $UTSHandler.getDomain(); o += r ? ";domain=" + r : "", $ZSIQChat.isEmbedFeatureConfEnabled() && (o += ";secure") } catch (e) { } o = $ZSIQUtil.setSameSiteCookie(o), document.cookie = o } else $ZSIQUTS.storeInSession(e, t) }, Z = function (e, t, n) { if (!w()) return !1; e = z(e); var i = JSON.parse(localStorage.utsdb || "{}"), o = {}; return o[e] = t, n && (o.ttl = n, o.time = (new Date).getTime()), i[e] = o, localStorage.utsdb = JSON.stringify(i), !0 }, E = function (e) { if (!w()) return !1; var t = e, n = JSON.parse(localStorage.utsdb || "{}"), i = n[e = z(t)]; if (i) delete n[F(t)], localStorage.utsdb = JSON.stringify(n); else { if (!(i = n[e = F(t)])) return null; i[z(t)] = i[e], delete i[F(t)], n[z(t)] = i, delete n[F(t)], localStorage.utsdb = JSON.stringify(n) } return i ? i.ttl && i.ttl + i.time < (new Date).getTime() ? (delete n[z(t)], delete n[F(t)], localStorage.utsdb = JSON.stringify(n), null) : i[e] : null }, N = function (e) { var t = e; e = z(t), e += "="; var n = document.cookie.indexOf(e), i = ""; return -1 !== n ? -1 !== (i = document.cookie.substr(n + e.length)).indexOf(";") && (i = i.substring(0, i.indexOf(";"))) : (e = F(t), e += "=", -1 !== (n = document.cookie.indexOf(e)) && (-1 !== (i = document.cookie.substr(n + e.length)).indexOf(";") && (i = i.substring(0, i.indexOf(";"))), Q(t, i, 864e5))), i || ($ZSIQUTS.getFromSession(e) || i) }, z = function (e) { return D(e) }, F = function (e) { return W(e) }, D = function (e) { return b + "-" + e }, W = function (e) { return b + "-" + T + "-" + e }, B = function (e) { return Array.isArray && Array.isArray(e) || "[object Array]" === Object.prototype.toString.call(e) || "[object Array Iterator]" === Object.prototype.toString.call(e) }, x = null, L = null, M = 0, A = !1, J = !1, R = !1, j = S(h), P = i; p.net = { disabledbyserver: !1, fallbacktimer: -1, monitortimer: -1, reconnectwhenactive: !1, lpt: 0, writeenabled: !1, connAlive: function () { p.net.lpt = new Date }, connMonitor: function () { X.logFine("inside connMonitor"); var e = new Date; try { e - p.net.lpt > 3 * p.moninterval && ("hidden" != H.cur_st ? A || J ? !1 === A && p.net.reconnect() : p.net.reconnect() : X.logFine("skipped by tab status")), p.util.dbconsistencychecked || p.net.disabledbyserver || p.util.checkConsistency() } finally { p.net.scheduleMonitor() } }, scheduleMonitor: function () { X.logFine("inside scheduleMonitor"), clearTimeout(p.net.monitortimer), p.net.monitortimer = setTimeout(function () { p.net.connMonitor() }, p.moninterval) }, reconnect: function () { X.logFine("inside reconnect - $ZUserStatus.isactive : " + V.isactive + " - " + H.cur_st + " - " + p.net.disabledbyserver), p.net.disabledbyserver || !V.isactive && null != V.isactive || "hidden" == H.cur_st || (V.init(), p.net.disableConnection(), p.init()) }, scheduleFallback: function () { clearTimeout(p.net.fallbacktimer), p.net.fallbacktimer = setTimeout(p.net.fallbackToCR, 2e4) }, unscheduleFallback: function () { clearTimeout(p.net.fallbacktimer) }, fallbackToCR: function () { Y._WEBSOCK.readyState === Y.readystate.OPEN && Y.initialized || (Y.disableConnection(), ee.init()) }, handleUTSEvent: function (e) { var t = e.d, n = 0; switch (e.o) { case 0: L = t._zldp || L, x = t._zldt || $ZSIQUTS.get("_zldt"), Y.setInitialized(), p.net.writeenabled = !0, p.seqmsg.isconnectionmade = !0, p.notifier.notifyQueued(), $UTSHandler.setUVID(x), $UTSHandler.setUUID(t.uuid); try { n = $ZSIQUTSAction.handle(t.triggers) } catch (e) { p.notifier.doLog("Exception in triggering message - " + e.message + " stack - " + e.stack + " name:" + e.name) } R = !0, t.isnewconnection = !0, K.handleConnect(t), $ZSIQUTSAction.includeBeforeSubmit(t), y.customaction && p.notifier.updateCustomAction(y.customaction), t.sseqno && p.seqmsg.init(parseInt(t.sseqno)), p.notifier.identifyByCampaign(), p.notifier.doLog(" isLocalstorage: " + w() + " isSessionStorage: " + O() + " Tab status - " + H.latime + " - " + H.cur_st), a = t.name, s = t.token, n / 1e3 < 5 && t.triggers && "object" == typeof $ZSIQChatWindow && $ZSIQChatWindow.populateIframe(), $ZSIQUTSAction.handleInfo(t); break; case 1: Y.ping(), L = t._zldp, x = t._zldt, U = t.sid, k() && $ZSIQUTS.storeInSession("sid", U), $UTSHandler.setUUID(t.uuid), $UTSHandler.setUVID(x), p.net.writeenabled = !0, p.seqmsg.isconnectionmade = !0, p.notifier.notifyQueued(), R = !0, K.handleConnect(t), $ZSIQUTSAction.handle(t.triggers), $ZSIQUTSAction.includeBeforeSubmit(t), t.sseqno && p.seqmsg.init(parseInt(t.sseqno)), $UTSHandler.isTrackedfrommailchimp() || p.notifier.identifyByCampaign(), a = t.name, s = t.token, $ZSIQUTSAction.handleInfo(t); break; case 2: L = t._zldp, x = t._zldt, U = t.sid; try { L = L ? L.trim() : L, x = x ? x.trim() : x, U = U ? U.trim() : U, $ZSIQUTS.store("_zldp", L, _(63072e6)), $ZSIQUTS.store("_zldt", x, _(864e5)), k() && $ZSIQUTS.storeInSession("sid", U) } catch (e) { } R = !0, p.util.checkMode(l) || p.notifier.acknowledge(); break; case 8: p.net.disableConnection(), L = t._zldp, x = t._zldt, U = t.sid; try { L = L ? L.trim() : L, x = x ? x.trim() : x, U = U ? U.trim() : U, $ZSIQUTS.store("_zldp", L, _(63072e6)), $ZSIQUTS.store("_zldt", x, _(864e5)), k() && $ZSIQUTS.storeInSession("sid", U) } catch (e) { } R = !0, Y.init(); break; case 3: $ZSIQUTSAction.handle(t.triggers); break; case 4: if (p.util.checkMode(l)) return; if (t && t.seqno) return void p.seqmsg.clear(t.seqno); if (t && t.sseqno) { if (p.seqmsg.updateServerUpdatedcount(parseInt(t.sseqno)), t.missedseq) { var i = JSON.parse(t.missedseq); p.seqmsg.asyncResend(i) } return } break; case 100: t.uvid = $ZSIQUTS.get("_zldt") || x, $UTSHandler.handleTrackChat(t); break; case 101: $ZSIQUTS.handleCustomAction(t); break; case 102: var o = t.triggers; !t.triggers && t.botTriggers && "botTriggers" == t.module && (o = t), (n = $ZSIQUTSAction.handle(o)) / 1e3 < 5 && "object" == typeof $ZSIQChatWindow && $ZSIQChatWindow.populateIframe(); break; case -1: if (p.util.checkMode(l)) return; t && t.issidclear && ($ZSIQUTS.removeFromSession("sid"), U = ""); var r = t && t.duration || ""; p.net.disableConnection(r), p.net.disabledbyserver = !0; break; case 11: $ZSIQUTSAction.handleInfo(t) } }, handleResponse: function (e, t) { if (J = !0, e) if ((e = JSON.parse(e)) && e.seqno) p.seqmsg.clear(e.seqno); else if (e && e.sseqno) { if (p.seqmsg.updateServerUpdatedcount(parseInt(e.sseqno)), e.missedseq) { var n = JSON.parse(e.missedseq); p.seqmsg.asyncResend(n) } } else if (!t && h.SharedWorker && X.sendResponseToOthers(e), B(e)) for (var i = 0; i < e.length; i++)this.handleUTSEvent(e[i]); else this.handleUTSEvent(e) }, scheduleRecon: (te = [30, 60, 90, 120, 300], ne = 0, ie = -1, function (e, t) { var n, i; e ? te[ne = 0] = (n = 5, i = 30, Math.floor(Math.random() * (i - n + 1) + n)) : ne < te.length - 1 && ne++; var o = te[ne]; clearTimeout(ie), ie = setTimeout(t, 1e3 * o) }), goOffline: function () { if (3 <= M || P === n) { 3 < M && (J = !1); var e = !(A = !0); 3 !== M && P !== n || (e = !0), P = i, this.scheduleRecon(e, this.reconnect) } }, requestSuccess: function () { (3 <= M || P === e) && (P = i, A = !1) }, disableConnection: function (e) { if (A = !0, e) { var t = 60 * e * 1e3, n = (new Date).getTime() + t; $ZSIQUTS.store("nextrecon", n), setTimeout(function () { X.logFine("disable connect settimeout"); try { p.net.disabledbyserver = !1 } catch (e) { } p.init() }, t) } p.util.checkMode(c) ? Y.disableConnection() : p.util.checkMode(r) && ee.disableConnection(), p.net.writeenabled = !1, h.$UTSConnector && $UTSConnector.util.net.terminate() } }, p.util = { dbconsistencychecked: !1, offlinetimer: -1, addOnOffEvent: function (e, t) { var n = document.body; n && n.attachEvent ? document.body.attachEvent("on" + e, t) : h.addEventListener && h.addEventListener(e, t) }, checkMode: function (e) { return f === e }, parseQueryString: function (e) { var t = {}; if (!e || !e.trim()) return t; for (var n = (e = e.replace(/^\?/g, "")).split("&"), i = [], o = function (e) { return decodeURIComponent((e + "").replace(/\+/g, "%20")) }, r = 0; r < n.length; r++)t[o((i = n[r].split("="))[0])] = o(i[1]); return t }, composeUrl: function () { var i = []; function e(e, t) { var n; t && i.push(e + "=" + encodeURIComponent((n = t) ? n.replace(/^\s+|\s+$/gm, "") : n)) } try { Float.stopBlinking() } catch (e) { } var t = $ZSIQUtil.getNavigationObj(h), n = t.current_page, o = t.page_title; try { var r = $zohosq.visitor.onNavigate({ url: n, title: o }); r && (n = r.url || n, o = r.title || o) } catch (e) { } var a = $ZSIQUtil.getURLParameterByName("mc_cid"), s = $ZSIQUtil.getURLParameterByName("mc_eid"); if (null == a && null == s) { var c = N("mailchimp"); if (c || (c = N("zohocampaign")), c) { var l = JSON.parse(c); y.name = l.name, y.email = l.email } } var d = t.referer, u = { "x-e": "" + T, "x-s": "" + b, cpage: n, "x-sid": U, ptitle: o, _zldp: L || $ZSIQUTS.get("_zldp"), _zldt: x || $ZSIQUTS.get("_zldt"), name: y.name, email: y.email ? y.email.toLowerCase() : "", e_name: $zv.e_name, e_email: $zv.e_email, phone: y.phone, localtime: function (e) { try { return (new Date).toTimeString().split(" ").slice(1).join(" ") } catch (e) { } return "" }(), gmttime: function () { try { return (new Date).toTimeString().split(" ").slice(1, 2)[0] } catch (e) { } return "" }(), docref: d.substring(0, 3072), resolution: "screen" in h && void 0 !== screen.width && void 0 !== screen.height ? screen.width + "x" + screen.height : "" }; try { delete $zv.e_name, delete $zv.e_email } catch (e) { } try { var m = $ZSIQChat.getWidgetData(), f = $ZSIQUtil.formWidgetObject(m.widgetobj).lsid; f && (u.lsid = "" + f) } catch (e) { } $ZSIQUtil.getAPILanguage() ? u.lang_embed = $ZSIQUtil.getAPILanguage() : $ ? u.lang_embed = $ : $ZSIQUtil.getBrowserLanguage() && (u.lang_embed = $ZSIQUtil.getBrowserLanguage()); try { var g = (new Date).getTime(), S = $ZSIQUTS.getFromSession("con_id"); S ? g = S : $ZSIQUTS.storeInSession("con_id", g), u.con_id = "" + g } catch (e) { } for (var p in u.referer = null, $ZSIQUTS.getFromSession("siq_isdirect") || ($ZSIQUTS.getFromSession("siq_ref") ? u.referer = $ZSIQUTS.getFromSession("siq_ref") : d ? (d = d.substring(0, 3072), $ZSIQUTS.storeInSession("siq_ref", d), u.referer = d) : $ZSIQUTS.storeInSession("siq_isdirect", !0)), u) u.hasOwnProperty(p) && e(p, u[p]); try { Float.blinkTitle(Float.blinkmsg) } catch (e) { } if (R) e("recon", "true"); else if (y.info) { var v = y.info; "object" == typeof v && (v = JSON.stringify(v)), e("cinfo", v) } return i.join("&") }, bindEvents: function () { try { function t(e) { e = e || {}; var t = {}, n = $ZSIQUtil.getNavigationObj(h), i = e[2]; if ((i = i ? i.trim() : i) && ("/" == i.substring(0, 1) ? i = h.location.origin + i : "?" == i.substring(0, 1) ? -1 !== (i = h.location.origin + h.location.pathname + i).indexOf("#") && (i += h.location.hash) : i = "#" == i.substring(0, 1) ? h.location.origin + h.location.pathname + h.location.search + i : null), t.cpage = i || n.current_page, t.ptitle = e[1] || n.page_title, t.referer = n.referer, t.lsid = n.lsid, h.$UTSConnector) { var o = $History.getList(); o && 0 != o.length ? ($UTSConnector.util.db.storeInStorage({ type: "localStorage", key: "h_cpage", value: t.cpage }), $History.push({ referer: t.referer, cpage: t.cpage, title: t.ptitle, lsid: t.lsid }), $UTSConnector.util.net.checkHistory()) : p.notifier.updateNavigtion(t) } else p.notifier.updateNavigtion(t) } i = h.history, o = i.pushState, i.pushState = function (e) { var t = o.apply(i, arguments); return "function" == typeof i.onpushstate && i.onpushstate(arguments), t }, G(h, "hashchange", t), h.history.onpushstate = function (e) { try { t(e) } catch (e) { } }, -1 !== navigator.userAgent.indexOf("Firefox") && (h.onbeforeunload = (n = h.onbeforeunload, function (e) { return p.net.disableConnection(), "function" == typeof n ? n() : e.returnValue ? e.returnValue : void 0 })) } catch (e) { } var n, i, o }, checkConsistency: function () { x === $ZSIQUTS.get("_zldt") && L === $ZSIQUTS.get("_zldp") || (U = null, p.net.reconnect()), p.util.dbconsistencychecked = !0 } }, p.util.addOnOffEvent("online", function () { p.util.offlinetimer && -1 != p.util.offlinetimer ? clearTimeout(p.util.offlinetimer) : V.isactive ? p.net.reconnect() : p.net.reconnectwhenactive = !0, P = e }), p.util.addOnOffEvent("offline", function () { p.util.offlinetimer && -1 != p.util.offlinetimer && clearTimeout(p.util.offlinetimer), p.util.offlinetimer = setTimeout(function () { P = n, p.net.reconnectwhenactive = !1, p.net.goOffline(), p.util.offlinetimer = -1 }, 2e3) }), p.notifier = { qdata: [], maxsize: 100, lastupdatedinfo: {} }, p.notifier.notifyServer = function (e, t) { try { e && !e.event_time && (e.event_time = "" + (new Date).getTime()) } catch (e) { } h.$UTSConnector ? $UTSConnector.util.net.trackEvent(e) : p.util.checkMode(l) || (p.net.writeenabled || t) && (Y._WEBSOCK.readyState === Y.readystate.OPEN || p.util.checkMode(r)) ? p.notifier.sendData(e, void 0, !0) : p.notifier.qdata.length > p.notifier.maxsize || (p.notifier.removeIfExists(e), p.notifier.qdata.push(e)) }, p.notifier.removeIfExists = function (e) { try { for (var t = 0; t < p.notifier.qdata.length; t++)JSON.stringify(p.notifier.qdata[t]) == JSON.stringify(e) && p.notifier.qdata.splice(t, 1) } catch (e) { } }, p.notifier.waitForSocketConnection = function (e, t) { setTimeout(function () { e.readyState !== Y.readystate.OPEN ? p.notifier.waitForSocketConnection(e, t) : void 0 !== t && t() }, 500) }, p.notifier.sendData = function (e, t, n) { if (p.seqmsg.enroll(e, n), p.util.checkMode(c)) { var i = e; p.seqmsg.isconnectionmade && ((i = {}).data = e, i.seqno = p.seqmsg.getSequenceNo()), Y._WEBSOCK.readyState === Y.readystate.OPEN ? Y._WEBSOCK.send(JSON.stringify(i)) : p.notifier.waitForSocketConnection(Y._WEBSOCK, function () { Y._WEBSOCK.send(JSON.stringify(i)) }) } else p.util.checkMode(r) ? ee.bind(JSON.stringify(e), t) : p.util.checkMode(l) && X.send(e) }, p.notifier.updateNavigtion = function (e) { this.notifyServer({ opr: "nav", navdata: e }) }, p.notifier.updateCustomAction = function (e) { var t = {}, n = $ZSIQUtil.getNavigationObj(h); t.cpage = n.current_page, t.ptitle = n.page_title, t.lsid = n.lsid, this.notifyServer({ opr: "customaction", action: e, navdata: t }); try { delete $zohosq.values.customaction, delete $ZSIQUtil.getAPIValues().customaction } catch (e) { } }, p.notifier.doLog = function (e) { this.notifyServer({ opr: "dolog", logdata: e }) }, p.notifier.enrollInfo = function (e, t) { if (e && t) { var n = {}; n[e] = t, n.uptime = (new Date).getTime(), p.notifier.lastupdatedinfo[e] = n } }, p.notifier.isUpdated = function (e, t) { var n = p.notifier.lastupdatedinfo[e]; return !!n && (n[e] == t && ((new Date).getTime() - n.uptime < 6e4 || (delete p.notifier.lastupdatedinfo[e], !1))) }, p.notifier.updateGuessInfo = function (e) { var t = {}, n = {}; (e = e.details || {}).isError && (t.isError = e.isError), e.name && e.name.trim() && (e.name = e.name.trim(), t.name = e.name), e.email && e.email.trim() && (e.email = e.email.trim(), t.email = e.email.toLowerCase()), e.phone && e.phone.trim() && (e.phone = e.phone.trim(), t.phone = e.phone), e.company && (n.company = e.company), this.notifyServer({ opr: "updateguessinfo", detail: t, cinfo: JSON.stringify(n) }) }, p.notifier.updateInfo = function (e) { var t, n, i = e.name; e.email && (t = e.email.toLowerCase()), e.phone && $ZSIQUtil.isValidPhoneNo(e.phone) && (n = e.phone), p.notifier.isUpdated("name", i) && (i = void 0), p.notifier.isUpdated("email", t) && (t = void 0), p.notifier.isUpdated("phone", n) && (n = void 0), (e.info || i || t || n) && (p.notifier.enrollInfo("name", i), p.notifier.enrollInfo("email", t), p.notifier.enrollInfo("phone", n), this.notifyServer({ opr: "updateinfo", name: i, email: t, phone: n, cinfo: e.info })) }, p.notifier.identifyByCampaign = function () { if ($ZSIQChat.getWidgetData().embedobj.ismailchimpenabled) { var e = $ZSIQUtil.getURLParameterByName("mc_cid"), t = $ZSIQUtil.getURLParameterByName("mc_eid"); if (null != e && null != t) Q("mc_cid", e), Q("mc_eid", t); else N("mailchimp") || (e = N("mc_cid"), t = N("mc_eid")); e && t && (this.notifyServer({ opr: "identifybycampaign", campaigndata: { mc_cid: e, mc_eid: t } }), $UTSHandler.setTrackedfrommailchimp(!0)) } else if ($ZSIQUtil.isZohoCampaignEnabled()) { var n = $ZSIQUtil.getURLParameterByName("zc_cid"); if (null != n) Q("zc_cid", n); else N("zohocampaign") || (n = N("zc_cid")); n && (this.notifyServer({ opr: "identifybycampaign", campaigndata: { zc_cid: n } }), $UTSHandler.setTrackedfrommailchimp(!0)) } var i = $zv.e_name, o = $zv.e_email; o && i && this.notifyServer({ opr: "identifybycampaign", campaigndata: { e_name: i, e_email: o } }) }, p.notifier.updateStatus = function (e) { if (e) try { this.notifyServer({ opr: "status", status: "" + e }) } catch (e) { } }, p.notifier.acknowledge = function () { var e = $ZSIQUTS.get("_zldt"); if (e) { var t = { opr: "ack", uvid: e }; p.notifier.notifyServer(t, !0) } }, p.notifier.notifyQueued = function () { p.notifier.qdata.length && p.net.writeenabled && (p.notifier.sendData(p.notifier.qdata, !0, !0), p.notifier.qdata = []) }; var K = { handleConnect: function (e) { e.chid ? (e.uvid = x, e, $UTSHandler.handleTrackChat(e)) : $UTSHandler.handleUTSConnect(e) } }, H = { cur_st: "visible", is_init: !1 }; H.latime = (new Date).getTime(), H.init = function () { if (!H.is_init) { H.is_init = !0; var t, n = ["moz", "ms", "o", "webkit"], e = !0, i = function () { for (var e = 0; e < n.length; e++)if (a(n[e]) in document) return n[e]; return null }(), o = a(i), r = (i || "") + "visibilitychange"; document.addEventListener(r, l, !1), document.addEventListener("focus", function () { l(!0) }, !1), document.addEventListener("blur", function () { l(!1) }, !1), h.addEventListener("focus", function () { l(!0) }, !1), h.addEventListener("blur", function () { l(!1) }, !1), document.addEventListener("mousemove", u, !1), document.addEventListener("mousedown", u, !1), document.addEventListener("keypress", u, !1), document.addEventListener("DOMMouseScroll", u, !1), document.addEventListener("mousewheel", u, !1), document.addEventListener("touchmove", u, !1), document.addEventListener("MSPointerMove", u, !1), d() } function a(e) { return e ? e + "Hidden" : "hidden" } function s() { e || (e = !0, H.cur_st = "visible") } function c() { e && (e = !1, H.cur_st = "hidden") } function l(e) { return "boolean" == typeof e ? e ? s() : c() : document[o] ? c() : s() } function d() { t = h.setTimeout(m, 1e4) } function u(e) { h.clearTimeout(t), H.latime = (new Date).getTime(), s(), d() } function m() { 36e5 < (new Date).getTime() - H.latime && c(), d() } }; var V = { updateIdle: function () { var e = V; if (e.isactive) { p.notifier.updateStatus(e.idle), e.isactive = !1; try { $zohosq._invoke("visitor.idle", !0) } catch (e) { } } }, init: function () { if (H.init(), void 0 === this.isactive) { var e = y.idletime; e && !isNaN(e) && (e = 60 * e * 1e3), this.idletime = e || 9e5, this.timer = this.timer || -1, this.online = 1, this.idle = 4, this.isactive = !0; for (var t = ["keydown", "keypress", "mouseout", "mouseenter", "click"], n = 0; n < t.length; n++)G(document, t[n], V.active); this.active() } }, active: function () { var e = V; if (e.updateTimer(), !e.isactive) { e.isactive = !0, p.net.reconnectwhenactive ? (p.net.reconnectwhenactive = !1, A = !1) : p.seqmsg.isconnectionmade || p.net.reconnect(), p.notifier.updateStatus(e.online); try { $zohosq._invoke("visitor.active", !0) } catch (e) { } } }, updateTimer: function () { var e = V; clearTimeout(e.timer), e.timer = setTimeout(function () { V.updateIdle() }, V.idletime) } }, G = function (e, t, n) { e.attachEvent ? e.attachEvent("on" + t, n) : e.addEventListener(t, n, !0) }, X = { initialized: !1, workerid: 0, utsworkerurl: null, mastertabworkerid: 0, utsworker: null, latimeupdater: null, lapolltimer: null, connecttimer: null, _enablelog: !1, logFine: function (e) { X._enablelog && console.log("%c " + e, "color: blue") }, logTrace: function (e) { X._enablelog && console.log("%c " + e, "color: red") }, logInfo: function (e) { X._enablelog && console.log("%c " + e, "color: green") }, logObj: function (e) { X._enablelog && console.log(e) }, send: function (e) { X.sendMessage("tomaster", { id: X.workerid, data: e }) }, sendResponseToOthers: function (e) { X.sendMessage("tononmaster", { id: X.workerid, data: JSON.stringify(e) }) }, sendMessage: function (e, t) { e && t && (X.utsworker ? (X.logTrace("sendMessage " + JSON.stringify({ operation: e, payload: JSON.stringify(t) })), X.utsworker.port.postMessage({ operation: e, payload: JSON.stringify(t) })) : X.logTrace("utsworker itself not initialised" + X.utsworker)) }, disableConnection: function () { p.net.disableConnection() }, constructPayload: function (e, t) { var n = {}; if (n.id = workerid, n.isNotifyTabs = !1, isNotifyOtherTab && (n.isNotifyTabs = !0), !e || Object.keys(e).length < 1) return n; for (var i in e) n[i] = e[i]; return n } }, Y = { id: null, wsScheme: S(h, !0), _WEBSOCK: {}, pingTimer: {}, recontimer: null, readystate: { CONNECTING: 0, OPEN: 1, CLOSING: 2, CLOSED: 3 }, initialized: !1, init: function () { if (X.logInfo("inside ws init"), p.seqmsg.isconnectionmade = !1, f = c, "off" !== y.tracking) { p.net.scheduleFallback(); try { var e = Y.wsScheme + q() + "/watchws?"; e += p.util.composeUrl(); var t = $ZSIQUTS.getFromSession("connection_count") || 0; t = parseInt(t), t++; var n = function () { return null != m && m < t }, i = function (e) { $ZSIQUTS.storeInSession("connection_count", "" + (e || 0)) }; if (!o && n() && i(), o && n()) return i(); o = !0, $ZSIQUTS.storeInSession("connection_count", JSON.stringify(t)), e += "&connection_count=" + t; try { "WebSocket" in h && void 0 !== h.WebSocket ? Y._WEBSOCK = new WebSocket(e) : "MozWebSocket" in h && (Y._WEBSOCK = new MozWebSocket(e)) } catch (e) { return X.logFine("Exception on new websocket " + e), clearTimeout(p.net.fallbacktimer), void (p.net.fallbacktimer = setTimeout(p.net.fallbackToCR, 0)) } X.logFine("BEFORE WS.id - " + Y.id), Y._WEBSOCK.id = (new Date).getTime(), Y.id = Y._WEBSOCK.id, X.logFine("WS.id - " + Y.id), Y._WEBSOCK.onopen = function (e) { Y.onOpen(e) }, Y._WEBSOCK.onclose = function (e) { X.logFine(" WS._WEBSOCK.onclose - " + this.id), X.logObj(e), Y.id === this.id && (Y.onClose(e), p.seqmsg.isconnectionmade = !1) }, Y._WEBSOCK.onerror = function (e) { Y.onError(e) }, Y._WEBSOCK.onmessage = function (e) { try { Y.onMessage(e) } catch (e) { p.notifier.doLog("Exception in WebSocket: message - " + e.message + " stack - " + e.stack + " name:" + e.name) } } } catch (e) { } } }, onMessage: function (e) { X.logFine(" WS.onMessage - " + JSON.stringify(e) + " WS.initialized -> " + Y.initialized + " WS.id -> " + Y.id), p.net.connAlive(), M = 0, p.net.requestSuccess(), Y.initialized || p.net.scheduleFallback(); var t = e.data; if ("//1//" !== t) { if ("" !== t) { if ("," === t) return X.logFine("data = ,"), void Y.init(); p.net.handleResponse(t), clearTimeout(Y.pingTimer), Y.pingTimer = setTimeout(Y.pingcb, 1e4) } } else Y.setInitialized() }, pingcb: function () { if (15e3 < new Date - p.net.lpt) try { Y._WEBSOCK.readyState === Y.readystate.OPEN && Y._WEBSOCK.send(JSON.stringify({ data: "-", seqno: p.seqmsg.getSequenceNo() })) } catch (e) { p.notifier.doLog("Exception in WebSocket: ping - " + e.message + " stack - " + e.stack + " name:" + e.name) } Y.pingTimer = setTimeout(Y.pingcb, 1e4) }, onOpen: function (e) { X.logFine("inside ws.onopen - WS.recontimer - must be cleared"), clearTimeout(Y.recontimer), V.init() }, onError: function (e) { X.logFine("inside ws.onError - evt -> " + JSON.stringify(e)), X.logObj(e) }, onClose: function (e) { if (X.logFine("inside ws.close - evt -> " + JSON.stringify(e)), p.util.checkMode(c)) { M++, p.net.goOffline(), clearTimeout(Y.recontimer); var t = (new Date).getTime() % 20; Y.recontimer = setTimeout(function () { Y.bind() }, 1e3 * t) } }, bind: function () { !0 !== A && p.util.checkMode(c) && (Y._WEBSOCK.readyState !== Y.readystate.CONNECTING && Y._WEBSOCK.readyState !== Y.readystate.OPEN || (X.logFine("websocet closed - WS._WEBSOCK.readyState - " + Y._WEBSOCK.readyState), Y._WEBSOCK.close(), p.seqmsg.isconnectionmade = !1), clearTimeout(Y.recontimer), X.logFine("inside ws.bind"), Y.init(), p.net.connAlive()) }, disableConnection: function () { try { X.logFine("websocket closed - by disableconnection"), Y._WEBSOCK.close(), p.seqmsg.isconnectionmade = !1 } catch (e) { } }, ping: function () { p.util.checkMode(c) && (Y.initialized = !1, Y._WEBSOCK.readyState === Y.readystate.OPEN && Y._WEBSOCK.send("--1--")) }, setInitialized: function () { p.util.checkMode(c) && (Y.initialized = !0, p.net.unscheduleFallback()) } }, ee = { lastevttime: 0, reqobjs: [], fullTripTimer: null, respTimer: null, ajax: function (e) { var t, n = e.type || "GET", i = e.async || !0, o = e.onSuccess || function () { }, r = e.onFailure || function () { }, a = e.url, s = e.options || {}; if ("withCredentials" in (t = new XMLHttpRequest)) { (t = new XMLHttpRequest).open(n, a, i), t.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8"), s.ajaxreq = t; try { t.onreadystatechange = function () { 4 === t.readyState && (200 === t.status ? o(s) : r(s)) } } catch (e) { throw clearTimeout(ee.respTimer), ee.respTimer = setTimeout(function () { ee.bind() }, 1e4), M++, p.net.goOffline(), e } } else if ("undefined" != typeof XDomainRequest) { (t = new XDomainRequest).open("GET", a), s.ajaxreq = t; try { t.onprogress = function () { }, t.ontimeout = function () { }, t.onload = function () { o(s) }, t.onerror = function () { r(s) } } catch (e) { throw clearTimeout(ee.respTimer), ee.respTimer = setTimeout(function () { ee.bind() }, 1e4), M++, p.net.goOffline(), e } } return setTimeout(function () { t.send(e.qp) }, 0), t }, sendReq: function (e, t) { var n = (new Date).getTime(), i = { url: e, qp: t, type: "POST", onSuccess: function (e) { var t = e.evtstarttime, n = e.ajaxreq.responseText; if (!(4e4 < ee.lastevttime - t && "" === n) && (p.net.handleResponse(n), p.seqmsg.isconnectionmade && p.net.connAlive(), p.net.requestSuccess(), M = 0, "" !== n)) { try { var i = JSON.parse(n); if (null != i.sseqno) return; if (B(i)) for (var o = 0; o < i.length; o++) { var r, a = i[o]; if ("4" == a.o && (r = a.d) && null != r.sseqno) return } else if ("4" == i.o && (r = i.d) && null != r.sseqno) return } catch (e) { return } ee.bind() } }, onFailure: function (e) { var t = e.ajaxreq.status; ee.reqobjs[0] && ee.reqobjs[0].abort(), clearTimeout(ee.respTimer), ee.respTimer = setTimeout(function () { ee.bind() }, 1e4), 500 !== t && 304 !== t && 404 !== t && 400 !== t && (M++, p.net.goOffline()) }, options: { evtstarttime: ee.lastevttime = n } }, o = ee.ajax(i); ee.reqobjs[0] = ee.reqobjs[1], ee.reqobjs[1] = o }, init: function () { if (p.seqmsg.isconnectionmade = !1, f = r, "off" !== y.tracking) { var e = j + q() + "/watch", t = $ZSIQUTS.getFromSession("connection_count") || 0; t = parseInt(t), t++, $ZSIQUTS.storeInSession("connection_count", JSON.stringify(t)); try { ee.sendReq(e, p.util.composeUrl() + "&nocache=" + (new Date).getTime() + "&connection_count=" + t) } catch (e) { return } V.init() } }, bind: function (e, t) { if (!0 !== A && (null !== ee.respTimer && clearTimeout(ee.respTimer), U)) { var n = j + q() + "/bind", i = "sid=" + encodeURIComponent(U) + "&nocache=" + (new Date).getTime(); e && (i += "&", t && (i += "q"), i += "data=" + encodeURIComponent(e), i += "&", i += "seqno=" + p.seqmsg.getSequenceNo()), p.seqmsg.isconnectionmade && p.net.connAlive(), ee.sendReq(n, i), clearTimeout(ee.fullTripTimer), ee.fullTripTimer = setTimeout(function () { ee.bind() }, 2e4) } }, disableConnection: function () { clearTimeout(ee.fullTripTimer), clearTimeout(ee.respTimer), p.seqmsg.isconnectionmade = !1 } }; p.init = function (e, t) { X.logFine("uts -init"), e && null, e && p.util.bindEvents(); var n = !(A = !1); $ZSIQUTS.get("nextrecon") && (n = $ZSIQUTS.get("nextrecon") - (new Date).getTime() < 0), n && (h.$UTSConnector ? (setTimeout(function () { $UTSConnector.util.net.disabled_by_server && $zohosq.tracking.on() }, 0), V.init()) : !I && "WebSocket" in h && void 0 !== h.WebSocket || "MozWebSocket" in h ? (X.logFine("websocket - init"), Y.init()) : ("withCredentials" in new XMLHttpRequest || h.XDomainRequest) && (X.logFine("CORS - init"), ee.init())) }; var te, ne, ie, oe, re, ae; return { $TabStatus: H, SW: X, store: function (e, t, n) { Q(e, t, n), Z(e, t, n) }, storeInLocalStorage: Z, getFromLocalStorage: E, removeFromSession: function (e) { if (!O()) return !1; var t = JSON.parse(sessionStorage.utsdb || "{}"); return delete t[z(e)], delete t[F(e)], sessionStorage.utsdb = JSON.stringify(t), !0 }, storeInSession: function (e, t) { if (!O()) return !1; var n = e; e = z(n); var i = JSON.parse(sessionStorage.utsdb || "{}"); return i[e] = t, delete i[F(n)], sessionStorage.utsdb = JSON.stringify(i), !0 }, get: function (e) { return N(e) || E(e) }, getFromSession: function (e) { if (O()) { var t = e; e = z(e); var n = JSON.parse(sessionStorage.utsdb || "{}"), i = n[e]; return i ? (delete n[e = F(t)], sessionStorage.utsdb = JSON.stringify(n)) : i = n[e = F(t)], i } }, updateAction: function (e) { var t = { opr: "action", type: e.type }; e.triggered_id && (t.triggered_id = e.triggered_id), e.action_type && (t.action_type = e.action_type), p.notifier.notifyServer(t) }, notifyCustomEvent: function (e) { var t = { opr: "customevent", data: e }; p.notifier.notifyServer(t) }, handleApiChange: function (e, t) { switch (e) { case 1: if ("on" === t) { if (p.init(), h.$UTSConnector) { var n = function (e, t) { $UTSConnector.util.net.disabled_by_server ? ($UTSConnector.util.net.disabled_by_server = !1, $UTSConnector.util.net.monitorConnection(!0)) : t < 20 && setTimeout(e, 2e3, e, ++t) }; $UTSConnector.util.net.disabled_by_server ? n(n, 1) : setTimeout(n, 1e3, n, 1) } } else "off" === t && p.net.disableConnection(); break; case 2: isNaN(t) || (V.idletime = 60 * t * 1e3, V.updateTimer()); break; case 3: t && t.handleinfo && (t = t.handleinfo), p.notifier.updateInfo(t); break; case 4: p.notifier.updateCustomAction(t); break; case 5: p.notifier.updateGuessInfo(t); break; case 6: !function () { try { sessionStorage.utsdb = JSON.stringify("{}") } catch (e) { } try { localStorage.utsdb = JSON.stringify("{}") } catch (e) { } try { for (var e = ["_zldp", "_zldt", "nextrecon", "sid", "siq_ref", "siq_isdirect", "siq_name", "siq_email", "siq_phone"], t = 0; t < e.length; t++) { var n = e[t], i = (n = z(n)) + "= ;expires= " + new Date(-1).toGMTString(); i += ";path=/"; try { var o = $UTSHandler.getDomain(); o && (document.cookie = i), i += o ? ";domain=" + o : "" } catch (e) { } i = $ZSIQUtil.setSameSiteCookie(i), document.cookie = i } } catch (e) { } U = "", x = L = null }(); break; case 7: p.notifier.doLog(t) } }, init: function (e) { if (!T || e) { var t = $ZSIQChat.getWidgetData(), n = $ZSIQUtil.formWidgetObject(t.widgetobj), i = t.embedobj; T = n.embedname, b = n.screenname, m = i.socket_reconnection_count; var o = $ZSIQUtil.formEmbedObject(i); o && o.einfo && o.einfo.embedstatus && o.einfo.embedstatus.longlastingcookiettl && "1" == o.einfo.embedstatus.longlastingcookiettl && (g = 31536e6, h.$UTSConnector && ($UTSConnector.cookie_max_age = 31536e6, $UTSConnector.util.db.reupdateCookieTTL())), $ = i.lang || i.language, d = n.utsserver, u = n.zldvp; var r = $ZSIQUtil.getGDPRPendingStatus(t, i, b); if ((n.forcehttps || $UTSHandler.isForceSecure()) && (h.$UTSConnector ? $UTSConnector.enableForceSecure() : (j = "https://", Y.wsScheme = "wss://")), y = $UTSHandler.getAPIValuesForUTS(), $ZSIQ_UTSinitialized = !0, function () { U = $ZSIQUTS.getFromSession("sid"); try { h.opener && h.parent.$ZSIQUTS.getFromSession("sid") === U && (U = "") } catch (e) { } try { if (h.top != h.self && h.top.$ZSIQUTS && h.top.$ZSIQUTS.getUTSSID() == $ZSIQUTS.getUTSSID()) return U = "", C(!1), p.notifier.doLog("loaded inside iframe tab. So, disabling data storing") } catch (e) { } C(!0) }(), !h.$UTSConnector) try { if (!$ZSIQUTS.getFromSession("siq_isdirect") && !$ZSIQUTS.getFromSession("siq_ref")) { var a = $ZSIQUtil.getNavigationObj(h).referer; a ? $ZSIQUTS.storeInSession("siq_ref", a) : $ZSIQUTS.storeInSession("siq_isdirect", !0) } } catch (e) { } if (!(n.istracking || y.tracking && "on" == y.tracking) || "off" == y.tracking || $ZSIQUtil.checkGDPRBannerStatus(2, b, i) || r) { if (h.$UTSConnector) { $UTSConnector.util.net.terminate(); var s = $UTSConnector.util.db.getFromCookie({ key: "_zldt" }) || $UTSConnector.util.db.getFromStorage({ type: "localStorage", key: "_zldt" }); s && $UTSHandler.setUVID(s); var c = $UTSConnector.util.db.getFromCookie({ key: "_uuid" }) || $UTSConnector.util.db.getFromStorage({ type: "localStorage", key: "_uuid" }); c && $UTSHandler.setUUID(c) } } else { !e && h.$UTSConnector && $zohosq.tracking.on(), x = x || $ZSIQUTS.get("_zldt"), L = (L = L || $ZSIQUTS.get("_zldp")) || u, $zv.customaction && $ZSIQUTS.handleApiChange(4, $zv.customaction), $zv.idletime && $ZSIQUTS.handleApiChange(2, $zv.idletime), ($zv.name || $zv.email || $zv.phone || $zv.info) && $ZSIQUTS.handleApiChange(3, $zv), X.logFine("very first time uts init"); var l = $UTSHandler.getEqualDomains(); l ? (X.logFine("falling to load after to " + l), setTimeout(p.init, 5e3, !0)) : p.init(!0), h.$UTSConnector || p.net.scheduleMonitor() } } else X.logFine("uts init already called - must be called twice") }, getUTSSID: function () { return h.$UTSConnector ? $UTSConnector.getUTSSID() : U }, clearUtsApivalues: function () { y = {} }, getUTSName: function () { var e = {}; return e.name = a, e.token = s, e }, handleCustomAction: function (e) { null != e.action ? ("mailchimp" == e.action ? Q("mailchimp", JSON.stringify({ email: e.email, name: e.name })) : "zohocampaign" == e.action ? Q("zohocampaign", JSON.stringify({ email: e.email, name: e.name })) : "edata" == e.action && (delete $zv.e_name, delete $zv.e_email), e.name && $zohosq.setValue("name", e.name), e.email && $zohosq.setValue("email", e.email)) : $ZSIQUTSAction.handle(e.triggers) }, identifyByCampaign: function () { p.notifier.identifyByCampaign() }, clearLocalValues: function () { $ = "" }, isTrackingLive: function () { return !!h.$UTSConnector || p.seqmsg.isconnectionmade }, updateTrackingLive: function () { p.seqmsg.isconnectionmade = !0 } } }(window); var $ZSIQUTSAction = $ZSIQUTSAction || function (p) { var o, e = { 1: "left", 2: "right", 3: "bottom", 4: "top" }, s = 0, l = 0, m = "", u = {}, c = {}, f = function () { if (0 != $UTSHandler.getFloatStatus() || -1 != [10, 16].indexOf(s)) { var e = !0; switch (m.time = (new Date).getTime(), s) { case 2: $UTSHandler.handleTriggerCustomAction(function () { $UTSHandler.handleChatTriggers(s, m) }); break; case 3: $UTSHandler.openChatWindow(); break; case 4: case 5: break; case 6: r(); break; case 7: $UTSHandler.showButton(); break; case 8: a(); break; case 9: $UTSHandler.handleChatTriggers(s, m); break; case 16: $UTSHandler.handleTriggerCustomAction(function () { $UTSHandler.handleBotTrigger(m) }); break; case 10: !1 === i() && (e = !1) }try { if (e && !n(s)) { var t = $ZSIQUTSAction.rechattrigger ? "14" : "7"; $ZSIQUTS.updateAction({ type: t, action_type: s, triggered_id: o }), delete $ZSIQUTSAction.rechattrigger } } catch (e) { } } }, i = function () { try { var e = { triggername: m.triggername, visitorinfo: u }; return $zohosq._invoke("visitor.trigger", e) } catch (e) { return !1 } }, n = function (e) { var t = $ZSIQUtil.getAvuid(); return !(-1 == [2, 16].indexOf(e) || !$ZSIQLSDB.getFromSession(t + "_formcontextinitiated")) }, d = function (e) { try { var t = String(e).toLowerCase(); if (-1 != t.search(/hour/i)) return 36e5 * parseInt(t); if (-1 != t.search(/minute/i)) return 6e4 * parseInt(t); if (-1 != t.search(/second/i)) return 1e3 * parseInt(t) } catch (e) { try { $ZSIQUTS.handleApiChange(7, "Exception in trigger parseTime - " + e.message + " stack - " + e.stack + " name:" + e.name) } catch (e) { } } return 0 }, r = function () { var n = $UTSHandler.getFloatDiv(5); if (null != n) { var r, a = 0, o = { 1: "right", 2: "left", 3: "top", 4: "bottom" }, s = e[m.animatetype]; "left" === s || "right" === s ? (r = p.innerWidth || document.body.clientWidth, r -= n.offsetWidth + 10) : (r = p.innerHeight || document.body.clientHeight, r -= n.offsetHeight + 10); var l = d(m.duration) || 50, u = n.currentStyle ? n.currentStyle[s] : document.defaultView.getComputedStyle(n, null)[s], c = o[m.animatetype]; c = n.currentStyle ? n.currentStyle[c] : document.defaultView.getComputedStyle(n, null)[c], n.style[o[m.animatetype]] = "auto", n.style[s] = "0px", function e() { var t = parseInt(n.style[s]), i = $UTSHandler.getFloatState(); if (t < a || r < t || "2" === i) return n.style[s] = u, void (n.style[o[m.animatetype]] = c); n.style[s] = t + 10 + "px", setTimeout(e, l) }() } }, a = function () { var e = $UTSHandler.getFloatDiv(4); if (null != e) { var g = e.querySelector(".zsiq_float"), h = g.currentStyle ? g.currentStyle.backgroundColor : document.defaultView.getComputedStyle(g, null).backgroundColor; if (-1 !== h.indexOf("#")) { var t = h; t = t.replace("#", ""); var i = parseInt(t, 16); h = [i >> 16 & 255, i >> 8 & 255, 255 & i] } else h = h.replace("rgb(", "").replace(")", "").split(","); for (var n = 0; n < h.length; n++)h[n] = Number(h[n]); var b = !1; !function () { var e = r(); !function e(t, i, n) { var r = g, a = $UTSHandler.getFloatState(); if (b && t === h || "2" === a) r.style.backgroundColor = "rgb(" + h + ")"; else { var o = n / 100, s = (i[0] - t[0]) / o, l = (i[1] - t[1]) / o, u = (i[2] - t[2]) / o, c = t[0], m = t[1], f = t[2], d = this; this.step = function () { 0 < --o ? (c = Math.floor(c + s), m = Math.floor(m + l), f = Math.floor(f + u), r.style.backgroundColor = "rgb(" + c + "," + m + "," + f + ")", p.setTimeout(function () { d.step() }, 100)) : (r.style.backgroundColor = "rgb(" + i + ")", timer = setTimeout(function () { e(i, t, n) }, 500)) }, step() } }(h, e, 1e3); var t = d(m.duration) || 6e4; setTimeout(function () { b = !0 }, t) }() } function r() { var e, t = h, i = (e = .2126 * h[0] + .7152 * h[1] + .0722 * h[2], parseInt(e) < 128); function n(e) { return e = parseInt(e), i ? Math.round(.6 * (255 - e) + e) : Math.round(.2 * (0 - e) + e) } var r = n(t[0]), a = n(t[1]), o = n(t[2]); return 255 < r || 255 < a || 255 < o ? t : [r, a, o] } }, g = { isloadedfirst: !0, zsiqformnames: ["name", "yourname", "subscribername", "contactname", "customername"], zsiqformemails: ["email", "emailid", "youremail", "contactemail", "subscriberemail", "mail", "customeremail", "emailaddress"], zsiqformpnos: ["phone", "pno", "phone no", "phoneno", "phno", "tel", "mobilenumber", "contactphone"], zsiqattriblist: ["id", "name", "type", "siqatrib"], zsiqformcompany: ["company"], zsiqfirstname: ["first_name", "firstname", "first name"], zsiqlastname: ["last_name", "lastname", "last name"] }; return g.submit = g.submit || {}, g.isnameset = !1, g.isemailset = !1, g.isphoneset = !1, g.iscompanyset = !1, g.isElementAvailable = function (e) { return !(!e || 0 == e.length) }, g.isEquals = function (e, t) { return e == t }, g.replaceSpecialChars = function (e) { return e = e.replace(/[_.-]/g, "") }, g.formatAttribValue = function (e) { return e = e.toLowerCase().trim(), e = g.replaceSpecialChars(e) }, g.checkInDOMAttrib = function (e, t, i) { for (var n, r = 0; r < e.length; r++) { var a = i.getAttribute(e[r]); if (a) { a = g.formatAttribValue(a); for (var o = 0; o < t.length; o++)if (g.isEquals(a, t[o])) { n = i.value; break } if (n) break } } return n || "" }, g.getName = function (e) { if (!g.isnameset) return g.checkInDOMAttrib(g.zsiqattriblist, g.zsiqformnames, e) }, g.getEmail = function (e, t) { if (!g.isemailset) return g.checkInDOMAttrib(g.zsiqattriblist, g.zsiqformemails, e) }, g.getPhoneNo = function (e, t) { if (!g.isphoneset) return g.checkInDOMAttrib(g.zsiqattriblist, g.zsiqformpnos, e) }, g.getFirstName = function (e, t) { return g.checkInDOMAttrib(g.zsiqattriblist, g.zsiqfirstname, e) }, g.getLastName = function (e, t) { return g.checkInDOMAttrib(g.zsiqattriblist, g.zsiqlastname, e) }, g.getCompany = function (e, t) { if (!g.iscompanyset) return g.checkInDOMAttrib(g.zsiqattriblist, g.zsiqformcompany, e) }, g.getDetails = function (e) { var t = {}; if (!g.isElementAvailable(e)) return t; var i = e.getElementsByTagName("input"); if (!g.isElementAvailable(i)) return t; for (var n = "", r = "", a = "", o = "", s = "", l = "", u = 0; u < i.length; u++) { var c = i[u]; c.name; c.value && ("" == n && (n = g.getName(c)), "" == r && (r = g.getEmail(c)), "" == a && (a = g.getPhoneNo(c)), "" == o && (o = g.getFirstName(c)), "" == s && (s = g.getLastName(c)), "" == l && (l = g.getCompany(c))) } if (n) t.name = n, g.isnameset = !0; else if (o) { var m = o; s && (m = m + " " + s), t.name = m, g.isnameset = !0 } return l && (t.company = l, g.iscompanyset = !0), r && /^([\w]([\w\-\.\+\'\/]*)@([\w\-\.]*)(\.[a-zA-Z]{2,22}(\.[a-zA-Z]{2}){0,2}))$/.test(r) && (t.email = r, g.isemailset = !0), a && /^[+0-9A-Za-z():.\-\[\] ]{1,30}$/.test(a) && (t.phone = a, g.isphoneset = !0), t }, g.getObjKeyCount = function (e) { var t = 0; if (null === e || "object" != typeof e) return t; for (var i in e) e.hasOwnProperty(i) && t++; return t }, g.autoPickAndNotifyUTS = function (e) { var t = g.getDetails(e); 0 !== g.getObjKeyCount(t) && $zohosq.setValue("details", t, 5) }, g.overridedOnsubmit = function (e) { try { e = e || p.event; var t = this; t || (t = e ? e.target || e.srcElement : ""); try { g.autoPickAndNotifyUTS(t) } catch (e) { } var i = t.getAttribute("siq_id"); if (i) { var n, r = this.onsubmit; return this.onsubmit = g.submit[i], this.onsubmit && (n = this.onsubmit(e)), !1 === n && (e.preventDefault ? e.preventDefault() : e.returnValue = !1), this.onsubmit = r, n } } catch (e) { $ZNotifyTracking(5, { details: { isError: !0 } }) } }, g.attachFormSubmit = function (e) { if (g.isElementAvailable(e)) for (var t = 0; t < e.length; t++) { var i = e[t]; if (g.isElementAvailable(i)) { var n, r = 0; do { if (n = "autopick_" + Math.floor(1e4 * Math.random()), !g.submit[n]) break; r++ } while (r < 10); i.setAttribute("siq_id", n), g.submit[n] = i.onsubmit, i.onsubmit = g.overridedOnsubmit } } }, g.includeInForms = function (e) { e = e || document.forms, g.isElementAvailable(e) && g.attachFormSubmit(e) }, g.includeInFrames = function (e) { e = e || document.getElementsByTagName("iframe"); if (g.isElementAvailable(e)) for (var t = 0; t < e.length; t++)try { var i = (e[t].contentDocument || e[t].contentWindow.document).getElementsByTagName("form"); g.includeInForms(i) } catch (e) { } }, g.isAutoPickDisabled = function () { return !$ZSIQAutopick.isenabled || !g.isloadedfirst || $ZSIQUTS.get("autopickdisabled") || 0 != g.getObjKeyCount(g.submit) }, g.isGuestToken = function (e, t) { if (!e || e == t) return !0; var i = e.split(" "); return !isNaN(e) || "Guest" === e || "Visitor" === i[0] && i[1] && !isNaN(i[1]) }, { handleButtonGlow: a, handleAnimate: r, clearLocalValues: function () { c = {} }, handleInfo: function (e) { var t = $zohosq.widgetcode + "zsiq_zforms_vinfo", i = $ZSIQLSDB.getFromSession(t); $ZSIQLSDB.removeSessionStore(t), i ? $zohosq.visitor.handleinfo(i) : e && p.$zohosq && (g.isGuestToken(e.name, e.token) || $zohosq.setValue("name", e.name), e.email && $zohosq.setValue("email", e.email), e.phone && $zohosq.setValue("phone", e.phone)) }, handle: function (e) { if (e) if (this.rechattrigger && (c = {}), e.botTriggers && "botTriggers" == e.module) { var t = e.botTriggers; for (var i in t) t.hasOwnProperty(i) && "widget_interaction" == t[i].event && ($ZSIQUTSAction.widget_interaction = !0) } else if (s = parseInt(e.type), e.value) { m = JSON.parse(e.value); var n = c[e.triggerid]; if (n ? (m.msglist = n.msglist, delete c[e.triggerid]) : c[e.triggerid] = m, e.visitorinfo) { for (var r in e.visitorinfo) if (e.visitorinfo.hasOwnProperty(r)) { var a = e.visitorinfo[r]; isNaN(a) || (e.visitorinfo[r] = parseInt(a)) } u = e.visitorinfo } if (l = "BOT_MESSAGE" == m.mode ? m.time ? parseInt(m.time) - e.time : 0 : d(m.time) - e.time, "object" == typeof $ZSIQChatWindow) return $ZSIQChatWindow.handleIframeLoading(), 0 < l ? setTimeout(f, l) : f(), o = e.triggerid, l; setTimeout(function () { $ZSIQUTSAction.handle(e) }, 1e3) } }, getUTSActionData: function () { }, includeBeforeSubmit: function (e) { if (g.isnameset = !g.isGuestToken(e.name, e.token), g.isemailset = e.isemailset || !1, g.isphoneset = e.isphoneset || !1, g.iscompanyset = e.iscompanyset || !1, !(g.isnameset && g.isemailset && g.isphoneset && g.iscompanyset || g.isAutoPickDisabled())) try { g.includeInForms(), g.isloadedfirst = !1 } catch (e) { } } } }(window); var ResponseFormatter = function () { var q = function (e) { return -1 == e.indexOf("chat") && -1 != e.indexOf("call") }, T = function (e) { if (!e) return []; for (var i = [], a = 0; a < e.length; a++) { var s = e[a]; i.push({ DNAME: s.display_name, NAME: s.display_name, ID: s.id, ENGAGED: s.engaged, STATUS: s.status }) } return i }, a = function (e, i) { var a, s = e.cdn || {}, n = e.static_urls || {}, t = e.dynamic_configurations || {}, r = e.portal_config || {}, o = e.i18nkeys || {}, _ = o.chat_widget, l = o.chat_window, c = e.integration || {}, d = e.visitor_info || {}, m = e.wms_related_info || {}, g = e.zmap || {}, p = e.company_info || {}, u = e.departments || [], f = e.call_departments || [], b = i.embedobj, h = i.widgetobj, v = h.privacyconfig, y = D(e.language); return r.trackingprivacyconfig = v.notify_cookies.code, r.trackingprivacystatement = v.notify_cookies.banner_content, r.cookiepolicyurl = v.notify_cookies.link_url, r.chatprivacyconfig = v.notify_terms.code, r.chatcookiepolicyurl = v.notify_terms.url, b.einfo.embedid = e.app_id, h.csscdnstaticserver = s.css_static_server, i.commondata.jscdnstaticserver = s.js_static_server, i.commondata.mediafilescdnhashes = s.media_file_hashes, i.commondata.isCDNEnabled = s.enabled, h.embedname = b.embedname = e.unique_name, h.visitorname = d.name, h.visitoremail = d.email, h.clogo_src = p.logo_source, b.pinfo.clogo_fkey = p.clogo_fkey, b.pinfo.pinfo = r, b.einfo.embedstatus.cinfo = b.cinfo = p, b.pinfo.companyname = e.company_name, b.einfo.embedstatus.DEPTLIST = x(u), b.einfo.embedstatus.CALL_DEPTLIST = T(f), b.einfo.embedstatus.DEPARTMENTID = 1 < x(a = u).length ? "-1" : x(a)[0] && x(a)[0].ID, b.einfo.embedstatus.CALL_DEPARTMENTID = 1 < T(f).length ? "-1" : T(f)[0] && T(f)[0].ID, b.pinfo.isbetaportal = t.isbetaportal, b.widget_performance_optimised = t.widget_performance_optimised, b.cookie_optimization = t.cookie_optimization, b.whitelisted_attachements = t.whitelisted_attachements, b.chat_without_questionenabled = t.hide_prechatform_question, b.subresourceintegrity_enabled = t.subresourceintegrity_enabled, b.isFixedHeaderEnabled = t.is_chat_window_fixed, b.is_white_labelling_enabled = t.is_white_labelling_enabled, b.is_replyCard_delayTime_enabled = b.is_replyCard_delayTime_enabled, b.slider_default_value_enabled = b.slider_default_value_enabled, b.restrict_visitor_input = b.restrict_visitor_input, b.all_sri_hash_value = e.wms_related_info.all_sri_hash_value, b.is_cookie_samesite_strict = e.is_cookie_samesite_strict, b.wmslite_js_sri_hash_value = e.wms_related_info.wmslite_js_sri_hash_value, b.trigger_bot_combine = t.subresourceintegrity_enabled, b.centralized_cookie_handler_enabled = t.centralized_cookie_handler_enabled, b.transfer_waiting_banner = t.waiting_banner_enabled, b.triggerOnChatnow = t.display_trigger_message, b.ratingOverrideDisabled = t.disallow_ratings_overriden, b.message_sanitization_enabled = t.message_sanitization_enabled, b.is_widget_drag_supported = t.is_widget_drag_supported, b.jwt_config_enabled = t.jwt_enabled, b.bot_rendering_ux_revamp = t.bot_rendering_ux_revamp, b.scroll_enabled = t.scroll_enabled, b.wms_anon_register = t.wms_anon_register, b.is_zoho_maps_v3_enabled = t.is_zoho_maps_v3_enabled, b.company_input_enhancment = t.company_input_enhancment, b.range_slider_text_input_supported = t.range_slider_text_input_supported, b.wms_debug_logs = t.wms_debug_logs, b.conversation_newflow_enabled = t.conversation_newflow_enabled, b.pinfo.article_revamp_enabled = t.article_revamp_enabled, b.pinfo.multilevel_category_supported = t.multilevel_category_supported, b.homepage_configs = e.homepage_configs, b.socket_reconnection_count = t.socket_reconnection_count, h.gapimapurl = e.google_map, b.linfo = e.license_info, h.lsid = b.einfo.lsid = e.lsid, i.commondata.siqservicename = e.siq_service_name, i.components = e.components, h.status = b.einfo.embedstatus.STATUS = "online" == e.status, h.call_status = b.einfo.embedstatus.call_status = e.call_status, b.einfo.uniquename = e.unique_name, h.wmsjsstaticserver = i.commondata.wmsjsstaticserver = m.js_static_server, i.commondata.wmspublicdomain = m.public_domain, b.lsprdcode = m.product_code, b.wmsjsstaticdomain = m.js_static_domain, h.zmapstaticurl = g.static_url, h.zmapapicss = g.css_url, h.zmapapiurl = g.api_url, h.zmapapikey = g.api_key, h.hideembed = JSON.parse(e.is_business_hours_enabled), e.avuid && ((i.widgetobj || {}).avuid = e.avuid), e.geo_details && (i.geodetails = e.geo_details), e.user_details && (h.userdetails = e.user_details, b.einfo.userdetails = e.user_details), i.commondata.imgstaticserver = n.image_server, h.cssstaticserver = n.css_server, i.commondata.cssjsmapping = n.css_js_mapping, n.widget_css_version && (h.widcssversion = n.widget_css_version), b.einfo.embedstatus.formremoval = t.formremoval, b.einfo.embedstatus.isvoicenotesenabled = t.isvoicenotesenabled, b.params = { country: d.country, remote_ip: d.remote_ip, country_name: d.country, pagetitle: "", type: "float" }, l && (b.jsresource = l), b.lang = y, b.language = y, b.isRtlLang = E(y) && t.rtl_language_support, h.resourcefile = n.resource_file, _ && (h.i18nkeys = _), l && (h.i18nChatwindowKeys = l), null != e.language_changed_in_api && (h.langchangeviaapi = e.language_changed_in_api), b.einfo.embedstatus.ismailchimpenabled = c.mailchimp.enabled, b.ismailchimpenabled = c.mailchimp.enabled, b.isaudiocallallowed = r.audiocall, b.issiqscreenshare = r.siqscreensharing, b.rtcp_info = e.rtcp_related_info, b.resource_dept = e.resource, [{ objString: i }] }, D = function (e) { return "default" == (e = $zv.language || e) ? $ZSIQUtil.getBrowserLanguage() : e }, E = function (e) { return -1 != ["ar", "iw", "fa_IR", "ur", "he", "pa", "ks"].indexOf(e) }, x = function (e) { for (var i = e.length, a = [], s = 0; s < i; s++) { var n = e[s], t = n.bot_users; t = t.length ? t : "", a.push({ STATUS: n.status, ONLYBOTSAVAILABLE: n.is_only_bot_available, ID: n.id, ENGAGED: n.is_engaged, USERS: n.users, DNAME: n.dname, NAME: n.name, BOTUSER: t }) } return a }, A = function (e) { var i = {}, a = e.campaign || {}, s = e.mailchimp || {}; return a.enabled && (i[6] = a), s.enabled && (i[10] = s), i }, L = function (e) { var i, a = e.components || [], s = e.chat || {}, n = e.call || {}, t = s.agents_engaged || {}, r = s.rating || {}, o = s.feedback || {}, _ = n.feedback || {}, l = s.custom_css || {}, c = s.agents_offline || {}, d = s.reaction || {}, m = n.rating || {}, g = s.agents_busy || {}, p = (s.messages || {}).chat_transfer || [], u = p[1] ? p[1].text : "", f = e.faq || {}, b = f.categorize || {}, h = l.url || "", v = $ZSIQUtil.getUrlParamObj(h), y = void 0 === f.show_creator_image || f.show_creator_image, w = { allow_visitor_to_like_article: f.allow_visitor_to_like_article || { enabled: !0 }, show_creator_image: y }; return v = v && v.css_id || "", h = (h = h.split("/")[5] || "").split("?")[0] || "", { icmail: JSON.stringify([e.mail_transcript ? 1 : 0, {}]), isaudiocall: JSON.stringify([-1 == a.indexOf("call") ? 0 : 1, { ismandatory: 0 }]), color: JSON.stringify([1, { code: e.chat.color }]), isscreenshare: JSON.stringify([e.share_screen ? 1 : 0, {}]), engagedmsg: JSON.stringify([0, { msg: t.message, resmsg: t.response }]), rating: JSON.stringify([r.enabled ? 1 : 0, { msg1: r.msg_1, msg2: r.msg_2, msg3: r.msg_3, msg4: r.msg_4, msg5: r.msg_5 }]), seasonaltheme: e.seasonal_theme, showsmiley: JSON.stringify([e.show_emojis ? 1 : 0, {}]), siq_channels_screen_share_enabled: -1 != a.indexOf("screen_share"), feedback: JSON.stringify([o.enabled ? 1 : 0, { msg: o.message }]), call_feedback: JSON.stringify([_.enabled ? 1 : 0, { msg: _.message }]), iscustomcss: JSON.stringify([l.enabled ? 1 : 0, { fname: l.file_name, fpath: "embedcss/" + v + "_" + h, pfname: "" }]), isname: JSON.stringify([1, { ismandatory: 1 }]), isphoneno: JSON.stringify([1, { ismandatory: 1 }]), isemail: JSON.stringify([1, { ismandatory: 1 }]), issiqscreenshare: JSON.stringify([s.siq_share_screen ? 1 : 0, {}]), icsound: JSON.stringify([s.mute_sound ? 1 : 0, {}]), iclogo: JSON.stringify([s.show_company_logo ? 1 : 0, {}]), icfile: JSON.stringify([e.share_file ? 1 : 0, {}]), lang: JSON.stringify([1, { code: "default" == e.language ? "-1" : e.language }]), isconversation: JSON.stringify([e.view_conversation ? 1 : 0, {}]), thanksmsg: JSON.stringify([0, { msg: s.thanking_message }]), offlinemsg: JSON.stringify([0, { msg: c.message, resmsg: c.response }]), icphoto: JSON.stringify([s.show_operator_image ? 1 : 0, {}]), reaction: JSON.stringify([d.enabled ? 1 : 0, { happy: d.happy, neutral: d.neutral, sad: d.sad }]), call_reaction: JSON.stringify([m.enabled ? 1 : 0, { happy: m.happy, neutral: m.neutral, sad: m.sad }]), faqaccess: JSON.stringify([-1 == a.indexOf("faq") ? 0 : 1, { orderbycategory: b.enabled ? 1 : 0, label: f.title }]), icprint: JSON.stringify([s.print ? 1 : 0, {}]), size: JSON.stringify([1, { val: "medium" == s.size ? "2" : "3" }]), formtype: JSON.stringify([1, { val: (i = e.form_type, { classic: "1", general: "2", conversation: "3" }[i]) }]), busymsg: JSON.stringify([0, { msg: g.message, resmsg: g.response }]), transfermsg: JSON.stringify([0, { msg: u, resmsg: "" }]), waitingmsg: JSON.stringify([0, { msg: s.waiting_message }]), call_messages: n.messages, call_waiting_time: n.waiting_time, call_recording: e.call_recording, voice_notes_enabled: e.voice_notes_enabled, credit_card_masking: e.credit_card_masking, resourcebaseconfigs: w, allow_multiple_live_chat: e.allow_multiple_live_chat, agentname: '[0,{"msg":"","name":""}]', question: '[0,{"msg":"","name":""}]', deptmsg: '[0,{"msg":"","resmsg":""}]' } }, I = function (e) { var i = e.online_content || {}, a = e.offline_content || {}; return JSON.stringify([1, { offline: a.header, offline_byline: a.sub_header, online: i.header, online_byline: i.sub_header }]) }, U = function (e) { var i = [], s = e.lsid, a = e.widget || {}, n = e.widget_type, t = a[n], r = { float: ["coin", "", "unicorn", "dream", "twinkle", "doller", "topaz", "link", "sassy", "noel", "bagel"], button: ["cord", "blueivy", "zen", "atom", "coffee", "paradise", "meme", "leo", "ruby", "cookie", "fancy", "cent", "poppy", "apollo"], personalized: ["phoenix", "snow", "fame"] }[n] || [], o = t.custom_sticker || {}; i.push(o.enabled ? 0 : 1); var _ = o.online || {}, l = $ZSIQUtil.getUrlParamObj(_.url), c = o.offline || {}, d = $ZSIQUtil.getUrlParamObj(c.url), m = r.indexOf(t.sticker) + 1, g = { float: "fsticker", button: "bsticker" }[n]; if (!o.enabled) return JSON.stringify([1, { offline: {}, default: m, online: {} }]); var p = { online: "", offline: "", default: m }; function u(e, i, a) { return { fname: "", fpath: g + "_" + i + "/" + e.stickerid + "_" + s, pfname: a } } return l && (p.online = u(l, "online", _.file_name)), d && (p.offline = u(d, "offline", c.file_name)), i.push(p), JSON.stringify(i) }; return { formatAndGetEmbedDetils: function (e, i) { return e.version_changed ? function (e, i) { var a = e.components || [], s = e.static_urls || {}, n = e.cdn || {}, t = e.zmap || {}, r = e.wms_related_info || {}, o = e.upload_download_server || {}, _ = e.media || {}, l = e.portal_config || {}; l.trackingprivacyconfig = e.notify_cookies.code, l.trackingprivacystatement = e.notify_cookies.banner_content, l.cookiepolicyurl = e.notify_cookies.link_url, l.chatprivacyconfig = e.notify_terms.code, l.chatcookiepolicyurl = e.notify_terms.url; var c = e.visitor_info || {}, d = e.timezone_offset || {}, m = e.widget || {}, g = e.integration || {}, p = e.company_info || {}, u = e.chat || {}, f = m[e.widget_type] || {}, b = f.gravatar || {}, h = e.dynamic_configurations || {}, v = e.departments || [], y = $ZSIQChat.getScriptSource().split("://"), w = y[0], k = e.i18nkeys || {}, S = k.chat_widget || {}, O = k.chat_window || {}, N = y[1].split("/")[0] || ""; N = N.substr(N.indexOf(".") + 1); var j = s.css_server || ""; j = (j = j.split("://")[1] || "").split("/")[0]; var J = D(e.language), z = T(e.call_departments); return [{ objString: { widgettype: { float: 1, button: 2, personalized: 3 }[e.widget_type], widgetobj: { cssstaticfile: s.css_file, color: JSON.stringify([1, { code: f.color }]), islivechat: -1 == a.indexOf("chat") && -1 == a.indexOf("call") || 1 != e.enabled ? 0 : 1, utsserver: e.uts_server, jsstaticfile: s.js_file, brandname: "Zoho", title: I(e), csscdnstaticserver: n.css_static_server, lsid: e.lsid, analytics: g.analytics, resourcefile: s.resource_file, gravatar: JSON.stringify([b.enabled ? 1 : 0, { fname: b.file_name, fpath: b.fpath, no: b.no, pfname: b.pfname }]), zmapstaticurl: t.static_url, mdevice_hide: JSON.stringify([f.hide_in_mobile_device ? 1 : 0]), isDraggable: f.draggable, zmapapicss: t.css_url, widcssversion: s.widget_css_version, screenname: l.name, isgdprenabled: e.privacy ? 1 : 0, autopick: { isenabled: e.form_fields_auto_pick_enabled }, visitorname: c.name, visitoremail: c.email, gapimapurl: e.google_map, privacyconfig: { trackdnt: e.track_dnt_websites, notifygt: e.notify_googletranslator_usage, notify_cookies: e.notify_cookies, isgdprenabled: 1 == e.privacy, notify_terms: e.notify_terms, maskip: e.mask_visitor_ip }, hideembed: JSON.parse(e.is_business_hours_enabled), clogo_src: p.logo_source, zmapapiurl: t.api_url, forcehttps: e.forcehttps, sticker: U(e), wmsjsstaticserver: r.js_static_server, clogo: JSON.stringify([f.show_company_logo ? 1 : 0, {}]), langchangeviaapi: e.language_changed_in_api, hideoffline: JSON.stringify([e.hide_when_offline ? 1 : 0]), hide_tooltip: f.hide_tooltip, i18nkeys: S, i18nChatwindowKeys: O, _zldp: e._zldp, applogo: "", isshowcallbubble: 0, embedname: e.unique_name, avuid: e.avuid, cssstaticserver: s.css_server, istracking: -1 != a.indexOf("proactive"), zmapapikey: t.api_key, position: JSON.stringify([1, { no: { bottom_right: "1", bottom_middle: "2", bottom_left: "3", left_bottom: "4", left_middle: "5", left_top: "6", top_left: "7", top_middle: "8", top_right: "9", right_top: "10", right_middle: "11", right_bottom: "12" }[f.position] }]), status: "online" == e.status, call_status: e.call_status, font: JSON.stringify([1, { default: { "Default System Font": "0", Lato: "1", Opensans: "2", Oxygen: "3", Roboto: "4", Puvi: "5" }[e.font] }]), userdetails: e.user_details, usercount: JSON.stringify([0, { count: f.user_count }]) }, usedownloadserver: o.enabled, commondata: { schema: w, siqservicename: e.siq_service_name, jscdnstaticserver: n.js_static_server, jsstaticserver: s.js_server, cssstatic: j, wmsjsstaticserver: r.js_static_server, uploadserver: o.upload_server, wmspublicdomain: r.public_domain, cssjsmapping: s.css_js_mapping, isdev: e.is_development_setup, imgstaticserver: s.image_server, isCDNEnabled: n.enabled, downloadserver: o.download_server, udEventBasedURL: o.event_based_url, UDServerRevamp: o.ud_revamp, siqUDServiceName: o.siq_ud_servicename, mediafilescdnhashes: n.media_file_hashes, commonstaticurl: n.enabled ? n.common_static_url : s.common_static_url }, geodetails: e.geo_details, embedobj: { schema: w, pinfo: { soid: l.id, clogo_fkey: p.clogo_fkey, pinfo: l, companyname: e.company_name, integid: g.enabled_integrations, screenname: l.name, isbetaportal: h.isbetaportal, app_newconfig_enabled: h.app_newconfig_enabled, only_call_enabled: h.only_call_enabled, call_monitor_enabled: h.call_monitor_enabled, call_transfer_enabled: h.call_transfer_enabled, multilevel_category_supported: h.multilevel_category_supported, article_revamp_enabled: h.article_revamp_enabled, screenshare_parallelchat: h.screenshare_parallelchat, rtcp_enabled: h.rtcp_enabled, hide_float_tooltip: h.embed_mobile_view, cookiePreference: e.cookie_preference, cookieSegregation: h.cookie_segregation }, jquery_version_updated: h.jquery_version_updated, cookie_optimization: h.cookie_optimization, widget_performance_optimised: h.widget_performance_optimised, whitelisted_attachements: h.whitelisted_attachements, chat_without_questionenabled: h.hide_prechatform_question, subresourceintegrity_enabled: h.subresourceintegrity_enabled, centralized_cookie_handler_enabled: h.centralized_cookie_handler_enabled, isFixedHeaderEnabled: h.is_chat_window_fixed, wmslite_js_sri_hash_value: e.wms_related_info.wmslite_js_sri_hash_value, all_sri_hash_value: e.wms_related_info.all_sri_hash_value, is_cookie_samesite_strict: e.is_cookie_samesite_strict, triggerOnChatnow: h.display_trigger_message, trigger_bot_combine: h.intelligent_trigger_enabled, transfer_waiting_banner: h.waiting_banner_enabled, ratingOverrideDisabled: h.disallow_ratings_overriden, message_sanitization_enabled: h.message_sanitization_enabled, conversation_newflow_enabled: h.conversation_newflow_enabled, accessibility_enabled: h.accessibility_enabled, is_widget_drag_supported: h.is_widget_drag_supported, scroll_enabled: h.scroll_enabled, jwt_config_enabled: h.jwt_enabled, bot_rendering_ux_revamp: h.bot_rendering_ux_revamp, wms_anon_register: h.wms_anon_register, is_zoho_maps_v3_enabled: h.is_zoho_maps_v3_enabled, company_input_enhancment: h.company_input_enhancment, range_slider_text_input_supported: h.range_slider_text_input_supported, wms_debug_logs: h.wms_debug_logs, mediaserverurl: _.server, jwt_authentication: e.jwt_authentication, ratingfeedbacktitles: u.labels || {}, messageactions: e.message_actions, visitor_question: e.visitor_question, consent_banner_content: e.consent_banner_content, language: J, RTL_supported_language: e.RTL_supported_language, isRtlLang: E(J) && h.rtl_language_support, is_white_labelling_enabled: h.is_white_labelling_enabled, is_replyCard_delayTime_enabled: h.is_replyCard_delayTime_enabled, slider_default_value_enabled: h.slider_default_value_enabled, restrict_visitor_input: h.restrict_visitor_input, isUseEmojis: h.use_emojis, rtcp_info: e.rtcp_related_info, gtattributionlink: e.google_translate_attribution_url, annonregistertimeout: e.annon_register_timeout, lsprdcode: r.product_code, jsresource: O, read_receipt: e.read_receipt, white_labelled: e.white_labelled, socket_reconnection_count: h.socket_reconnection_count, einfo: { lsid: e.lsid, signaturechat: !0, uniquename: e.unique_name, brandname: e.appname, waitingtime: e.waiting_time, reopen_enabled: e.allow_chat_reopen, embedid: e.app_id, embedstatus: { STATUS: "online" == e.status, call_status: e.call_status, issiqscreenshare: JSON.stringify(u.siq_share_screen), DEPARTMENTID: 1 < x(v).length ? "-1" : x(v)[0] && x(v)[0].ID, CALL_DEPARTMENTID: 1 < z.length ? "-1" : z[0] && z[0].ID, cinfo: p, isaudiocallallowed: l.audiocall, issiqscreenshare: l.siqscreensharing, DEPTLIST: x(v), CALL_DEPTLIST: z, ismailchimpenabled: g.mailchimp.enabled, longlastingcookiettl: l.longlastingcookiettl, formremoval: h.formremoval, isvoicenotesenabled: h.isvoicenotesenabled, call_recording: e.recording, staticfiles_delayloading_enabled: h.staticfiles_delayloading_enabled }, props: L(e) }, resource_dept: e.resource, resources_config: e.resources, uapache: e.use_apache, cfcname: e.csrf_cookie, productname: e.product_name, theme: { ribbon: "1", crest: "2", crayon: "3", lloyd: "4", airy: "5", crown: "6", connect: "7", pattern: "8", pattern_straight: "9", pattern_curve: "10" }[u.theme], ucomp: e.use_compression, lang: D(e.language), screenname: l.name, producturl: e.product_url, siqhelplink: e.siq_help_link, embedserverurl: e.public_domain, cfparamname: e.csrf_param, stun_turnurls: e.stun_turn_url, ip: c.ip, wmsjsstaticdomain: r.js_static_domain, ismailchimpenabled: g.mailchimp.enabled, lssubdomain: N, params: { country: c.country, remote_ip: c.remote_ip, country_name: c.country, pagetitle: "", type: "float" }, freelogolink: e.free_logo_url, ccode: c.country_code, embedname: e.unique_name, tzsoffset: d.server, cinfo: p, isaudiocallallowed: l.audiocall, issiqscreenshare: l.siqscreensharing, campaignenabled: !0, servername: e.product_url, integprops: { integinfo: A(g) }, linfo: e.license_info, tzpoffset: d.portal, homepage_configs: e.homepage_configs }, version: e.version, last_modified: e.modified_time, isonlycall: q(e.components || []), components: e.components || [] }, module: "getembeddetails", objType: "object" }] }(e) : a(e, i) }, formateCallDept: T } }(); var $ZSIQNotifyCookie = function () { var o = function (i) { return document.getElementById(i) }, a = function (i, e, t) { o(i).setAttribute(e, t) }, i = "analytics", e = "functionality", c = "strictlynecessary", n = { isFunctionality: !1, isAnalytics: !1 }, t = "", r = {}, s = { strictlynecessary: {}, functionality: {}, analytics: {}, learnmore: "" }, l = [{ name: "isiframeenabled", ttl: "1 day", purpose: "This cookie is set when the Live Chat feature is disabled by proactive chats/trigger/JSAPI", type: e }, { name: "'zld'+<@lsid@>+'Article_'+<@article_id@>", ttl: "1 day", purpose: "This cookie stores if an article/FAQ is liked or not by the visitor", type: e }, { name: "'ZLD'+<@appid@>+'WAITING'", ttl: "Embed waiting time", purpose: "This cookie stores the waiting time of the chat time details for a chat", type: e }, { name: "'ZLD'+<@appid@>+'WTIME'", ttl: "Embed waiting time", purpose: "This cookie stores the remaining waiting time details for a chat", type: e }, { name: "cdn_status", ttl: "2 days", purpose: "This cookie stores the cdn status for getting the static files", type: e }, { name: "<@chatid@>+'_translate'", ttl: "1 day", purpose: "This cookie stores whether translation is enabled or not", type: e }, { name: "'gdpr_'+<@screenname@>+'_trackingconfig'", ttl: "1 Month", purpose: "This cookie stores GDPR consent configured in portal side to handle the visitor end widget accordingly with consent banners", type: e }, { name: "'JSESSIONID'", ttl: "When the browsing session ends", purpose: "This cookie is generated by servlet containers like Tomcat and used for session management for the HTTP protocol", type: c }, { name: "'LS_CSRF_TOKEN'", ttl: "When the browsing session ends", purpose: "This cookie is used for security purposes in order to avoid Cross-Site Request Forgery, (CSRF) for the AJAX calls made by the visitor", type: c }, { name: "'gdpr_'+<@screenname@>+'__donottrack'", ttl: "1 month", purpose: "This cookie stores the GDPR consent from the visitor - enable/disable tracking", type: c }, { name: "e3de1f7d42(random checksum)", ttl: "When the browsing session ends", purpose: "This cookie is used for internal load balancing of SalesIQ servers", type: c }, { name: "'fbpgsr_'+<@lsid@>", ttl: "Until cleared", purpose: "Used in the Facebook app to authenticate the SalesIQ API request", type: c }, { name: "uesign", ttl: "1 Month", purpose: "This cookie is used to manage the security of the applications.", type: c }, { name: "<@screenname@>+'-'_zldp'", ttl: "2 years", purpose: "This cookie identifies the unique visitors for the website", type: i }, { name: "<@screenname@>+'-'_zldt'", ttl: "1 day", purpose: "This cookie identifies unique visits for a visitor in the website", type: i }, { name: "<@screenname@>+'-'_siqid'", ttl: "2 years", purpose: "This cookie helps you track users across all domains", type: i }, { name: "<@screenname@>+'-'_uuid'", ttl: "2 years", purpose: "This cookie provides user id needed in REST API to get the data of the current user.", type: i }, { name: "<@screenname@>+'-'mc_cid'", ttl: "1 day", purpose: "This cookie tracks Mailchimp details about the visitor via campaign id & member email's unique id", type: i }, { name: "<@screenname@>+'-'mc_eid'", ttl: "1 day", purpose: "This cookie tracks Mailchimp details about the visitor via unique id & member email's unique id", type: i }, { name: "_zldvfp", ttl: "6 months", purpose: "Used to identify visitors referred from email campaigns", type: i }, { name: "'ZLD'+<@appid@>", ttl: "1 week", purpose: "This cookie stores the Live Chat attender details and visitor details", type: i }, { name: "'ZLD'+<@lsid@>+'avuid'", ttl: "1 week", purpose: "This cookie stores the avuid unique id to denote a visitor", type: i }, { name: "'ZLDTRIGGER'+<@appid@>", ttl: "1 week", purpose: "This cookie stores the trigger details when the visitor is contacted", type: i }, { name: "ZLDPERSONALIZE", ttl: "180 seconds", purpose: "This cookie stores attender ID in cookie for personalized chat", type: i }, { name: "'ZLD'+<@lsid@>+'tabowner'", ttl: "180 seconds", purpose: "This cookie stores WMS session ID", type: i }, { name: "<@avuid@>+'_accesstime'", ttl: "1 day", purpose: "This cookie stores the last fetched conversation's end time", type: i }], d = { functionality: [], analytics: [], strictlynecessary: [] }, p = function () { o("zsiqfunctionalitychbox").checked = n.isFunctionality, o("zsiqanalyticschbox").checked = n.isAnalytics }, u = function () { var i = s.learnmore, e = $ZSIQWidget.getEmbedObject().pinfo.pinfo.cookiepolicyurl; return e = e ? $ZSIQUtil.getURLProtocol(e) : "https://www.zoho.com/salesiq/cookies-policy.html", '<div class="zsiq-cookiecont-div" id="zsiqcookieui"><div class="zsiq_pLR30 zsiq_zpsubmain"><div class="zsiq_zphdr">' + r["cookie.your.preference"] + '</div><div class="zsiq_clr6">' + r["cookie.your.preference.desc"] + '<a href="' + e + '" zsqa="cookiepolicy_url" target="_blank" rel="noopener noreferrer">' + r["cookie.policy"] + '</a>.</div></div><div class="zsiq-cookie-pref"><div class="zsiq_zpsubmain"><label class="zsiq_zpsubhdr"><input type="checkbox" id="strictlynecessarychbox" checked disabled><span>' + s.strictlynecessary.title + '</span></label><div class="zsiq_zpsubdtl">' + s.strictlynecessary.subtitle + '<span class="details" id="strictlynecessary">' + i + '</span></div></div><div class="zsiq_zpsubmain"><label class="zsiq_zpsubhdr curp"><input type="checkbox" id="zsiqfunctionalitychbox"><span>' + s.functionality.title + '</span></label><div class="zsiq_zpsubdtl">' + s.functionality.subtitle + '<span class="details" id="functionalitydetails">' + i + '</span></div></div><div class="zsiq_zpsubmain"><label class="zsiq_zpsubhdr curp"><input type="checkbox" id="zsiqanalyticschbox"><span>' + s.analytics.title + '</span></label><div class="zsiq_zpsubdtl">' + s.analytics.subtitle + '<span class="details" id="analyticsdetails">' + i + "</span></div></div></div></div>" }, y = function () { w("zsiqimagepreview", function (i) { "zsiqclose" === i.target.getAttribute("docclick") && v(i) }), w("functionalitydetails", function () { f(e) }), w("analyticsdetails", function () { f(i) }), w(c, function () { f(c) }), w("confirmmychoice", T), w("zsiqacceptall", S), w("zsiqclose", v), w("zsiqfunctionalitychbox", z), w("zsiqanalyticschbox", z) }, v = function (i) { t = "", $ZSIQUtil.cookieConfig = { isFunctionality: !1, isAnalytics: !1 }, $ZSIQChatWindow.closeImagePreview(i, !0), $ZSIQUtil.bindEventsCookieBanner() }, f = function (i) { var e = i === c ? "hide-footer" : "", t = '<div id="zsiqfaqpreview" class="zsiq_zpmain">' + h(i) + '<div class="zsiq-footer ' + e + '">' + k(i) + '</div><em class="siqico-larrow" data-type="' + i + '" id="zsiq-larrow"></em><em class="siqico-close" id="close-cookie" zsiqclose="true" docclick="zsiqclose"></em></div>'; o("zsiqimagepreview").innerHTML = t, m() }, h = function (i) { return '<div class="zsiq_pLR30 zsiq-cookiebody-div"><div class="zsiq_zpsubmain zsiq-zptabl" style="margin-top:40px;"><div class="zsiq_zphdr">' + s[i].title + '</div><div class="zsiq_clr6">' + s[i].subtitle + '</div></div><div class="zsiq-cookiecont-div">' + _(l, i) + "</div></div>" }, m = function () { w("acpt-cookie", g), w("decl-cookie", b), w("close-cookie", function (i) { t = "", $ZSIQChatWindow.closeImagePreview(i, !0), $ZSIQUtil.bindEventsCookieBanner() }), w("zsiq-larrow", q) }, k = function (i) { return i === c ? "" : '<div class="zsiq-cbtn zsiq-wbtn" data-type="' + i + '" id="decl-cookie">Decline</div><div class="zsiq-cbtn" id="acpt-cookie" data-type="' + i + '">Accept</div>' }, z = function () { A(); var i = x("zsiqfunctionalitychbox"), e = x("zsiqanalyticschbox"), t = r["cookie.save.accept"], s = r["cookie.discard.changes"], o = "save", n = "discard"; i && e && (t = r["cookie.accept.all"], s = r["cookie.use.essential"], o = "accept", n = "decline"), a("zsiqacceptall", "proptype", o), a("confirmmychoice", "proptype", n), I(t, s) }, q = function () { o("zsiqimagepreview").innerHTML = t, y(), p(), z() }, b = function (i) { i.target.getAttribute("data-type") === e ? n.isFunctionality = !1 : n.isAnalytics = !1, q() }, g = function (i) { i.target.getAttribute("data-type") === e ? n.isFunctionality = !0 : n.isAnalytics = !0, q() }, _ = function (i, e) { var t = ""; for (var s in i) if (i.hasOwnProperty(s)) { var o = i[s]; o.type === e && (t += '<div class="zsiq_tr"><div class="zsiq_td">' + o.name + '</div><div class="zsiq_td zsiq_wpro">' + o.purpose + '</div><div class="zsiq_td">http_cookie  </div><div class="zsiq_td">' + o.ttl + "</div></div>") } return '<div class="zsiq_table"><div class="zsiq_th"><div class="zsiq_td">Name</div><div class="zsiq_td zsiq_wpro">Purpose</div><div class="zsiq_td">URL</div><div class="zsiq_td">Expiry</div></div>' + t + "</div>" }, w = function (i, e) { o(i) && o(i).addEventListener("click", e) }, T = function (i) { var e = i.target.getAttribute("proptype"); "decline" === e ? (n = { isFunctionality: !1, isAnalytics: !1 }, $ZSIQUtil.cookieConfig = n, Z()) : "discard" === e && (n = { isFunctionality: !0, isAnalytics: !0 }, p(), a("zsiqacceptall", "proptype", "accpet"), a("confirmmychoice", "proptype", "decline"), I(r["cookie.accept.all"], r["cookie.use.essential"])) }, I = function (i, e) { o("zsiqacceptall").innerText = i, o("confirmmychoice").innerText = e }, S = function (i) { var e = x("zsiqfunctionalitychbox"), t = x("zsiqanalyticschbox"); n = { isFunctionality: e, isAnalytics: t }, $ZSIQUtil.cookieConfig = n, Z() }, A = function () { n = { isFunctionality: x("zsiqfunctionalitychbox"), isAnalytics: x("zsiqanalyticschbox") } }, x = function (i) { return o(i).checked }, Z = function () { o("zsiqimagepreview").remove(); var i = $UTSHandler.getAPIValuesForUTS(), e = $ZSIQWidget.getWidgetObject(), t = !((!i.tracking || "off" != i.tracking) && e.istracking), s = JSON.parse($ZSIQWidget.getEmbedObject().pinfo.pinfo.trackingprivacyconfig); n.isAnalytics || t || 2 != s ? $ZSIQUtil.updateCookieForTracking(0, "2") : $ZSIQUtil.updateCookieForTracking(1, "2") }, L = function (i, e) { n = { isFunctionality: i, isAnalytics: e } }; return { init: function (i, e) { for (var t = 0; t < l.length; t++) { var s = l[t].name; s = s.split("'").join("").replace(/\+?<@lsid@>\+?/g, i.lsid).replace(/\+?<@screenname@>\+?/g, i.screenname).replace(/\+?<@appid@>\+?/g, e), d[l[t].type].push(s) } }, getCookieList: function () { return l }, updateCookie: function (i) { Object.assign(n, i) }, isCookieAllowed: function (i) { var e, t = { functionality: "isFunctionality", analytics: "isAnalytics" }, s = ($ZSIQChat.getWidgetData() || {}).embedobj; s && (e = $ZSIQLSDB.getFromLocalStorage("ZSIQ" + s.einfo.embedid + "-cookie_config")); var o = e || $ZSIQUtil.cookieConfig; if (-1 != i.indexOf("state") || -1 != i.indexOf("dragpos")) return !1; for (var n in d) if (d.hasOwnProperty(n)) { if (-1 != i.indexOf("Article_") || -1 != i.indexOf("isiframeenabled") || -1 != i.indexOf("_translate")) return o.isFunctionality; if (-1 != i.indexOf("avuid")) return o.isAnalytics; if (-1 != d[n].indexOf(i)) return n === c || o[t[n]] } return !0 }, populateUI: function () { n = { isFunctionality: !0, isAnalytics: !0 }, r = $ZSIQWidget.getWidgetObject().i18nChatwindowKeys, s.strictlynecessary.title = r["cookie.essential"], s.strictlynecessary.subtitle = r["cookie.essential.desc"], s.functionality.title = r["cookie.performance"], s.functionality.subtitle = r["cookie.performance.desc"], s.analytics.title = r["cookie.analytics"], s.analytics.subtitle = r["cookie.analytics.desc"], s.learnmore = r["cookie.learnmore"]; var i = '<div class="zsiq_prv_main notify-cookie" zsiqclose="true" id="zsiqimagepreview" docclick="zsiqclose"><div id="zsiqfaqpreview" class="zsiq_zpmain">' + u() + '<div class="zsiq-footer"><div class="zsiq-cbtn zsiq-wbtn" id="confirmmychoice" proptype="decline" style="color:#888;">' + r["cookie.use.essential"] + '</div><div class="zsiq-cbtn" id="zsiqacceptall" proptype="accpet">' + r["cookie.accept.all"] + '</div></div><em class="siqico-close" id="zsiqclose" zsiqclose="true" docclick="zsiqclose"></em></div><div id="zsiqcookielist"></div></div>'; t || o("gdprbanner").insertAdjacentHTML("afterend", i), t = i, p(), y(), z() }, handleGotit: function () { L(!0, !0) }, handleDNT: function () { L(!1, !1) }, closeBanner: v, cookieList: function () { return d } } }(); var $ZSIQChat = $ZSIQChat || function () { var y; SIQ_FLOAT = 1, SIQ_BUTTON = 2, SIQ_PERSONALIZE = 3, _WINDOW_REPOPULATE = !1; var D = {}, $ = {}; D[SIQ_FLOAT] = ["/styles/floatbuttonWIDGTHEME.css"], D[SIQ_BUTTON] = ["/styles/buttonthemeWIDGTHEME.css"], D[SIQ_PERSONALIZE] = ["/styles/personalizethemeWIDGTHEME.css"], $[SIQ_FLOAT] = ["/js/floatbuttonWIDGTHEME.js"], $[SIQ_BUTTON] = ["/salesiq/js/newbutton_re2MZXbavOdB_RGBN3X6I1PGM4JeiO__j7uJKTdAcwuQGihZ3BS9vwf5-IjuMzkr_.js"]; var Q = !($[SIQ_PERSONALIZE] = ["/salesiq/js/personalizewidget_yu7VSCzbApSBoxnstsZu0eoneDgRt14FiLs2oaUlIacxdPFfDe8wuNpFF5780p1c_.js"]), e = !1, l = !1, m = {}, d = !0; try { Q = _IS_PREVIEW } catch (e) { } try { e = _IS_SIGNATURE_CHAT } catch (e) { } var Z = function () { var e = window._STATICURLS || $ZSIQUtil.staticURL; if (e) return "https://" + e[4] + "/widget"; var t = document.getElementById("zsiqscript"); return t.src || t.querySelector("src") }, c = function (e, t) { if (null != t.status) return e.widgetobj.status = e.widgetobj.call_status = e.embedobj.einfo.embedstatus.STATUS = t.status, e; if ((5 == _WIDGETTYPE || 6 == _WIDGETTYPE || 7 == _WIDGETTYPE || t.color && window._SIQ_NEW_FLOAT) && (e.widgetobj = t.theme || t.integprops ? e.embedobj : e.embedobj.einfo.props, t.hasOwnProperty("show_brand_logo") && (e.widgetobj = e.embedobj.homepage_configs)), t.embedstatus) e.embedobj.einfo.embedstatus = t.embedstatus; else for (var i in t) e.widgetobj[i] = t[i]; return e.embedobj.einfo.embedstatus.STATUS = !t.toggleoffline, e }, O = function (e, i, a, s) { e.onerror = function () { var e = "", t = $zohosq.nonce; "js" == s ? (e = document.createElement("script")).src = a : "css" == s && ((e = document.createElement("link")).rel = "stylesheet", e.href = a), t && e.setAttribute("nonce", t), i[0].appendChild(e) } }; return { init: function () { try { $ZSIQUtil.preConnectDomains(), $ZSIQChat.loadPostMessage(), y = WIDGET_DATA } catch (e) { $ZSIQUtil.initializeAPIValues(), $ZSIQChat.getEmbedDetails() } }, getWidgetData: function () { return y }, isLivePreview: function () { try { return null != parent.document.getElementById("preview") } catch (e) { return !1 } }, isOnboarding: function () { return Q ? window.parent._IS_ONBOARDING : window._IS_ONBOARDING }, getEmbedDetails: function () { $ZSIQChat.fetchEmbedDetails() }, getDomain: function () { var e = ""; return e || (e = location.hostname.toString().replace(/^w{3}\./, ""), /^[a-zA-Z0-9-\.]+$/.test(e) || (e = "")), e ? location.protocol + "//" + e : "" }, fetchEmbedDetails: function (S) { var e = Z(), t = (e = e.split("?")[0]).replace("/widget", ""), i = $ZSIQUtil.staticURL; i && i[5] && (t = "https://" + i[5]), t += "/visitor/v2/channels/website", t += "?widgetcode=" + $zohosq.widgetcode, t += "&internal_channel_req=true"; var v = $ZSIQUtil.getConfigFromStorage(), a = ""; !v || !v.last_modified || Q || this.isLivePreview() || S || (t = t + "&last_modified_time=" + v.last_modified); var s = $ZSIQLSDB.get("ZLD" + $zohosq.widgetcode + "avuid"); v && v.widgetobj && (s = $ZSIQLSDB.get("ZLD" + v.widgetobj.lsid + "avuid")), s ? $ZSIQChat.avuidval = s : a += "avuid"; var o = $zohosq.values && $zohosq.values.agent; o && (t = t + "&attender_email=" + encodeURIComponent(o)), v && v.version && (t = t + "&version=" + v.version); var n = $ZSIQUtil.getAPILanguage(); n && (t = t + "&api_language=" + n); var r = $ZSIQUtil.getBrowserLanguage(), d = "ZLSLANG" + $zohosq.widgetcode, c = $ZSIQLSDB.get(d); (0 == n.length && v.widgetobj && 1 == v.widgetobj.langchangeviaapi || 0 != n.length && v.embedobj && v.embedobj.lang != n || c != r) && (t = t + "&language_api=" + !0), r && (t = t + "&browser_language=" + r, $ZSIQLSDB.set(d, r, 864e5)); var l = v && v.geodetails; $ZSIQUtil.isFetchGeoDetails() && !l && (a += ",geo_details"); var m = this.getDomain(), g = window.document.title; m && (t = t + "&current_domain=" + encodeURIComponent(m)), t = t + "&pagetitle=" + encodeURIComponent(g).substring(0, 3e3); try { t = t + "&is_signaturechat=" + _IS_SIGNATURE_CHAT } catch (e) { } Q && 0 < _WIDGETTYPE && _WIDGETTYPE < 4 && (t = t + "&widget_type=" + _WIDGETTYPE), window._SIQ_WIDGET_LIVE_PREVIEW && (t += "&siq_widget_preview=true"), Q && (t = t + "&is_preview=" + Q), a && (t = t + "&include_fields=" + a); $ZSIQUtil.ajax(t, function (e) { var t = $ZSIQUtil.parseToJSON(e); if (window._WEBSITEAPIRESPONSE = t.data, $ZSIQChat.isOnboarding()) { for (var i = t.data, a = i.components, s = a.length, o = [], n = 0; n < s; n++)-1 == ["call", "screen_share"].indexOf(a[n]) && o.push(a[n]); i.components = o, (i.dynamic_configurations || {}).isvoicenotesenabled = !1, window._WEBSITEAPIRESPONSE = i } var r = t.data.dynamic_configurations; window._IS_REVAMP = window._SIQ_NEW_FLOAT || r.revamp_enabled || r.embed_revamp, window._IS_REVAMP && IframeHandler.addMessageListener(); try { var d = t.data, c = ResponseFormatter.formatAndGetEmbedDetils(d, v)[0].objString, l = c.widgetobj, m = d.notify_cookies || {}, g = d.dynamic_configurations.cookie_segregation, b = 2 === m.code && g; "default" == d.cookie_preference && ($ZSIQUtil.cookieConfig = { isFunctionality: !b, isAnalytics: !b }), !1 === d.privacy && ($ZSIQUtil.cookieConfig = { isAnalytics: !0, isFunctionality: !0 }); var u = $ZSIQLSDB.getFromLocalStorage("ZSIQ" + d.app_id + "-cookie_config"); u ? $ZSIQUtil.cookieConfig = u : $ZSIQLSDB.storeInLocalStorage("ZSIQ" + d.app_id + "-cookie_config", $ZSIQUtil.cookieConfig); var p = d.app_id; if ($ZSIQNotifyCookie.init(l, p), l) { var f = l._zldp, h = l.embedname; f && $ZSIQUtil.checkAndSetZldp(f, h) } $zv.embedHideDetails = { brandDisabled: !1, restrictedURI: !1, allDepartmentsDisabled: !1, outsideBusinessHours: l.hideembed, hideWidgetOnMobile: 1 === JSON.parse(l.mdevice_hide)[0], operatorsBusy: 1 === JSON.parse(l.hideoffline)[0] }, $ZSIQChat.handleEmbedDetails(c, v, S, p) } catch (e) { } }, function (e) { if (e) { var t = ((e = JSON.parse(e)).error || {}).code; t && !$zv.embedHideDetails && ($zv.embedHideDetails = { brandDisabled: 1406 === t, restrictedURI: 1432 === t, allDepartmentsDisabled: 1431 === t, outsideBusinessHours: !1, hideWidgetOnMobile: !1, operatorsBusy: !1 }, $ZSIQUtil.startAfterReady()) } }, !0, "x-siq-internal-channel") }, handleEmbedDetails: function (e, t, i, a) { if (e.not_modified && !i) t.widgetobj.status = e.STATUS, t.widgetobj.hideembed = e.hideembed, t.commondata.jsstaticserver = e.jsstaticserver, t.commondata.imgstaticserver = e.imgstaticserver, t.widgetobj.cssstaticserver = e.cssstaticserver, t.commondata.jscdnstaticserver = e.jscdnstaticserver, t.widgetobj.csscdnstaticserver = e.csscdnstaticserver, t.commondata.isCDNEnabled = e.isCDNEnabled, t.commondata.isCDNEnabled && (t.commondata.cssjsmapping = e.cssjsmapping), t.commondata.mediafilescdnhashes = e.mediafilescdnhashes, t.commondata.cwrmjsstaticserver = e.cwrmjsstaticserver, t.commondata.cwumjsstaticserver = e.cwumjsstaticserver, t.commondata.zmapapiurl = e.zmapapiurl, t.commondata.zmapapicss = e.zmapapicss, t.commondata.zmapstaticurl = e.zmapstaticurl, t.commondata.zmapapikey = e.zmapapikey, t.widgetobj.wmsjsstaticserver = e.wmsjsstaticserver, t.widgetobj.widcssversion = e.widcssversion, t.embedobj.linfo = e.linfo, t.embedobj.einfo.embedstatus.STATUS = e.STATUS, t.embedobj.einfo.embedstatus.DEPTLIST = e.DEPTLIST, t.embedobj.einfo.embedstatus.formremoval = e.formremoval, t.embedobj.einfo.embedstatus.DEPARTMENTID = e.DEPARTMENTID, t.embedobj.params = e.params, e.pinfo && t.embedobj.pinfo.pinfo && (t.embedobj.pinfo.pinfo.screensharing = e.pinfo.screensharing), t.embedobj.issiqscreenshare = e.issiqscreenshare, e.userdetails && (t.widgetobj.userdetails = e.userdetails, t.embedobj.einfo.userdetails = e.userdetails), e.geodetails && (t.geodetails = e.geodetails), e.refreshapilang && (t.embedobj.jsresource = e.jsresource, t.embedobj.lang = e.lang, t.embedobj.language = e.language, t.widgetobj.resourcefile = e.resourcefile, t.widgetobj.i18nkeys = e.i18nkeys), e.hasOwnProperty("isgdprenabled") && (t.embedobj.pinfo.pinfo.isgdprenabled = e.isgdprenabled, t.embedobj.pinfo.pinfo.trackingprivacyconfig = e.trackingprivacyconfig, t.embedobj.pinfo.pinfo.chatprivacyconfig = e.chatprivacyconfig, t.embedobj.pinfo.pinfo.cookiepolicyurl = e.cookiepolicyurl, t.embedobj.pinfo.pinfo.chatcookiepolicyurl = e.chatcookiepolicyurl, t.embedobj.pinfo.pinfo.trackingprivacystatement = e.trackingprivacystatement, t.embedobj.pinfo.pinfo.gtpolicyurl = e.gtpolicyurl), t.widgetobj.langchangeviaapi = e.langchangeviaapi, t.embedobj.ismailchimpenabled = e.ismailchimpenabled, t.embedobj.isaudiocallallowed = e.isaudiocallallowed, t.embedobj.cinfo = e.cinfo, t.embedobj.homepage_configs = e.homepage_configs, delete e.not_modified; else { var s = (t = e).widgetobj.avuid; if (s) { $ZSIQChat.avuidval = s; var o = $zohosq._domain ? 63072e6 : ""; $ZSIQLSDB.setCookie("ZLD" + t.widgetobj.lsid + "avuid", s, o), delete t.widgetobj.avuid } if (i) return } if (!window._IS_REVAMP) { var n = ($ZSIQLSDB.get("ZLD" + a) || {}).ongoingchats; Object.keys(n || {}).length && (t.widgetobj.hideembed = !1) } y = $ZSIQChat.updateSeasonalTheme(t), Q && ((y = c(y, _MODIFIED_WIDGET_OBJ)).commondata.isCDNEnabled || (y.commondata.jscdnstaticserver = t.commondata.jsstaticserver, y.widgetobj.csscdnstaticserver = t.widgetobj.cssstaticserver)), function (e) { e.embedobj && e.embedobj.pinfo; d = !0 }(t); var r = $zohosq.resetEssential; $UTSHandler.init(r), $ZSIQUtil.startAfterReady(t.geodetails || {}, t), function () { var e = y.widgettype, t = JSON.parse(y.widgetobj.sticker)[1].default, i = document.getElementsByTagName("head"); i && 0 != i.length || ((i = [])[0] = document.createElement("head"), document.insertBefore(document.body, i[0])); var a = y.commondata.isdev, s = y.commondata.schema, o = Z().split("://")[1].replace("/widget", ""), n = (y.widgetobj.widcssversion, y.commondata.cssstatic, y.commondata.jsstaticserver), r = y.widgetobj.cssstaticserver, d = y.widgetobj.csscdnstaticserver, c = y.commondata.jscdnstaticserver; window.iscdnenabled = y.commondata.isCDNEnabled; var l = $zohosq.nonce; iscdnenabled && (window.cssjslist = JSON.parse(y.commondata.cssjsmapping)), a && (d = r = n = c = s + "://" + o, window.iscdnenabled = !1, D = Filedatagetter.getcsslistmapping(), $ = Filedatagetter.getjslistmapping()), Q && (D[SIQ_BUTTON] = ["/salesiq/styles/buttonpreviewtheme_YuZ53S4oVgd9kZbxTmNOWK8tuLOuhnxSjipkxGjdFYRAD09Cc6gNUAARpMd2ZY9k_.css"], D[SIQ_PERSONALIZE] = ["/salesiq/styles/personalizepreviewtheme_8Gpq9fmOlRRX-p1vg1p5HVWOAsgy3CWr4R000KFmFTWYY51Sh5DArO7tgnEbTa9m_.css"], D[SIQ_FLOAT] = ["/salesiq/styles/floatbuttonpreview_-jplXEa7HieU8ho4egbc3DxV0t6r9L_-C0VANooGF6mOmyfz0dIlYhVuu3rHhqWX_.css"], $[SIQ_FLOAT] = ["/salesiq/js/floatbuttonpreview_vs-7qmGwQN_TD1Nbqz-v8zNKqlju7i87PtzN0DP2Qv-8pvbSKGRSTU0-S_uJ5j-t_.js"]); for (var m = D[e], g = $[e], b = 0; b < m.length; b++) { var u = iscdnenabled ? m[b] : m[b].replace(/WIDGTHEME/g, t), p = r + u, f = ""; if (iscdnenabled) if (-1 != u.indexOf("WIDGTHEME")) { var h = u.split("WIDGTHEME"); actualcssfile = h.join(t); var S = cssjslist[actualcssfile]; void 0 !== S && (p = d + (u = "/salesiq" + h[0] + t + "_" + S + "_" + h[1]), f = $ZSIQChat.getSubResourceIntegrityAttr(S)) } else p = d + u; var v = document.createElement("link"); v.rel = "stylesheet", v.href = p, O(v, i, (r + m[b]).replace(/WIDGTHEME/g, t), "css"), l && v.setAttribute("nonce", l), f && (v = $ZSIQUtil.setIntegrityAttr(v, f)), i[0].appendChild(v) } for (b = 0; b < g.length; b++) { var I = document.createElement("script"), w = iscdnenabled ? g[b] : g[b].replace(/WIDGTHEME/g, t), _ = n + w, j = ""; if (iscdnenabled) { if (-1 != w.indexOf("WIDGTHEME")) { var E = w.split("WIDGTHEME"); actualjsfile = E.join(t); var T = cssjslist[actualjsfile]; void 0 !== T && (w = "/salesiq" + E[0] + t + "_" + T + "_" + E[1], j = $ZSIQChat.getSubResourceIntegrityAttr(T)) } _ = c + w } I.src = _, j && (I.setAttribute("integrity", j), I.setAttribute("crossorigin", "anonymous")), O(I, i, (n + w).replace(/WIDGTHEME/g, t), "js"), a && (I.async = !1), l && I.setAttribute("nonce", l), i[0].appendChild(I) } }(), $ZSIQUtil.setAVUIDCookie(t.widgetobj.lsid, t.embedobj && t.embedobj.cookie_optimization) }, updateSeasonalTheme: function (t) { try { var e = t.embedobj.einfo.props.seasonaltheme || {}, i = t.embedobj.theme, a = new Date; a.setHours(0, 0, 0, 0); var s = a.getTime(); if (e.enabled && e.themes) { for (var o = {}, n = e.themes.length, r = 0; r < n; r++) { var d = e.themes[r]; if (d.enabled) { o = d; break } } o.expiry_time && parseInt(o.start_time) <= s && parseInt(o.expiry_time) >= s && (t["seasonal-type"] = o.type, m = o, l = !0) } else l = !1; if (l && "4" == i && !this.isPreview() && (t.embedobj.einfo.props.color = '[1,{"code":"#000000"}]'), l && 1 == t.widgettype) { var c = $ZSIQUtil.parseToJSON(t.widgetobj.sticker); c[1].default = 1, t.widgetobj.sticker = JSON.stringify(c) } return t } catch (e) { return t } }, getSeasonalThemeObj: function () { return m }, isSeasonalTheme: function () { return l }, isPreview: function () { return Q }, isSignatureChat: function () { return e }, isAgentSpcificSignatureChat: function () { return e && $zohosq.values.agent }, updatePreviewConfig: function () { y = c(y, _MODIFIED_WIDGET_OBJ) }, loadPostMessage: function () { window.addEventListener ? window.addEventListener("message", $ZSIQChat.handlePostMessage, !1) : window.attachEvent("onmessage", $ZSIQChat.handlePostMessage) }, getScriptSource: Z, isEmbedFeatureConfEnabled: function () { return d && -1 != location.protocol.indexOf("https") }, getSubResourceIntegrityAttr: function (e) { return (y || {}).embedobj.subresourceintegrity_enabled ? "sha384-" + e.replace(/-/g, "+").replace(/_/g, "/") : "" }, handlePostMessage: function (e) { try { !e.data.message || "ZOHOCLIQ-EXTN-CHECK" !== e.data.message.type && "ZOHOCLIQ-SCEEN-SOURCE-ID" !== e.data.message.type || $ZSIQUtil.getIframe().PeerConnection.screenShareExtnSuccsess(e); var t = JSON.parse(e.data); if ("zoho.salesiq.apimessage" === t.type) { if (t.websiteredirection) return $ZSIQLSDB.storeInSession($zohosq.widgetcode + "zsiq_zforms_vinfo", t), void window.addEventListener("unload", function () { var e = t.visitor; e.phone = e.contactnumber, $zohosq.visitor.handleinfo(e) }); var i = t.visitor; i.phone = i.contactnumber, $zohosq.visitor.handleinfo(i) } else if ("zoho.salesiq.gettrackingdetails" === t.type) { var a = $UTSHandler.get("_zldt"); if (!a) return; var s = { type: "zoho.salesiq.trackingdetails" }; s.trackingdetails = { uvid: a }, e.source.postMessage(JSON.stringify(s), "*") } } catch (e) { } }, notifyOnCDNFailure: O } }(); if (!$zoho.salesiq.init_called) { var isdomloadhandled = !1; try { "async" != $zoho.salesiq.mode && ($ZSIQChat.init(), isdomloadhandled = !0, $zoho.salesiq.init_called = !0) } catch (e) { isdomloadhandled = !1 } isdomloadhandled || ($zoho.salesiq.init_called = !0, "complete" === document.readyState ? $ZSIQChat.init() : window.addEventListener ? window.addEventListener("load", $ZSIQChat.init) : window.attachEvent("onload", $ZSIQChat.init)) }