var pivot;
var wdr_load_status = false;
var fileData =false;
function initializeWdrFileDataForReconcile(reportsDataUrl) {
    wdr_load_status = false;
    if (document.getElementById("wdr-component1") != null) {
        var reportData = convertDataFromFileForReconcile(reportsDataUrl, ',')
            initializeWdrWidthFileDataForReconcile(reportData);
            if(reportData && reportData.length  > 0){
                fileData =true;
            }
        }
}


function initializeWdrWidthFileDataForReconcile(reportData) {
    if (wdr_load_status === false) {
        pivot = new WebDataRocks({
            container: "#wdr-component1",
            customizeCell: customizeCellFunction1,
            height: 700,
            toolbar: true,
            report: {
                dataSource: {
                    data:reportData ,
                    fieldSeparator: ","
                },
                formats: [{
                    name: "formatPriceColumn",
                    decimalPlaces: 2,
                    nullValue: "0",
                    currencySymbol: "$"
                },
                {
                    maxDecimalPlaces: 2
                }],
                tableSizes: {
                    "columns": [
                        {
                            "tuple": [],
                            "measure": "file",
                            "width": 180
                        }
                    ]
                },
                options: {
                    'grid': {
                        'showHeaders': false,
                        type: 'flat',
                        "showTotals": "off"
                    },
                    "showAggregationLabels": false,
                },
                "slice": {
                    "rows": [
                        {
                            "uniqueName": "Gallop Reference",
                        },
                        {
                            "uniqueName": "Swipe Date (UTC)",
                            "caption": "Swipe date"
                        },
                        {
                            "uniqueName": "Posted Date (UTC)",
                            "caption": "Posted date"
                        },
                        {
                            "uniqueName": "Amount"
                        },
                        {
                            "uniqueName": "User"
                        },
                        {
                            "uniqueName": "Department"
                        },
                        {
                            "uniqueName": "Statement Descriptor",
                        },
                        {
                            "uniqueName": "Merchant Name",
                            "filter": {
                                "members": [
                                    "Merchant Name.Enterprise Rent-A-Car",
                                    "Merchant Name.TripActions",
                                    "Merchant Name.Docusign",
                                    "Merchant Name.\"\"\"Manhattan Mini Storage \"\"\""
                                ],
                                "negation": true
                            }
                        },
                        {
                            "uniqueName": "Brex Category",
                        },
                        {
                            "uniqueName": "Integration Category",
                        },
                        {
                            "uniqueName": "Integration Department",
                        },
                        {
                            "uniqueName": "Integration Class",
                        },
                        {
                            "uniqueName": "Integration Date",
                        },
                        {
                            "uniqueName": "Integration Category Fully Qualified Name",
                        },
                        {
                            "uniqueName": "Id",
                        }
                    ],
                    "columns": [
                        {
                            "uniqueName": "Measures"
                        }
                    ],
                    "measures": [
                        {
                            "uniqueName": "Amount",
                            "aggregation": "sum",
                            "format": "formatPriceColumn"
                        }
                    ],
                    "flatOrder": [
                        "Gallop Reference",
                        "Swipe Date (UTC)",
                        "Posted Date (UTC)",
                        "Amount",
                        "User",
                        "Department",
                        "Statement Descriptor"
                    ]
                }
            },
            reportcomplete: function () {
                pivot.off("reportcomplete");
                $("#wdr-tab-fields").attr("style", "display: inline-block !important;");
                wdr_load_status = true;
            },
        });
    }
}


function isItADateColumnForReconcile(columnName){
    if (columnName === 'Swipe Date (UTC)' 
        || columnName === 'Posted Date (UTC)' 
        || columnName === 'Integration Date' 
        ){
        return true;
    }
    return false;
}
function customizeCellFunction1(cellBuilder, cellData) {
    if (cellData.type && cellData.type == "value") {
        cellBuilder.addClass('alterLeft');
        if (cellData.rowIndex % 2 == 0) {
            cellBuilder.addClass("alter1");
        } else {
            cellBuilder.addClass("alter2");
        }
        if (cellData.hierarchy && cellData.hierarchy !== null
            && isItADateColumnForReconcile(cellData.hierarchy.uniqueName)){
            if (cellData.label !== ''
                && !isNaN(cellData.value)){
                try{
                    var dateParts = new Date(cellData.value).toDateString().split(' ');
                    cellBuilder.text = dateParts[2] + ' ' + dateParts[1] + ' ' + dateParts[3].substring(2);
                }catch(ex){
    
                }
            }else{
                cellBuilder.text = '';
            }
        }
    }
    }
function pivotOffButtonForReconcile() {
    wdr_load_status = true;
    //fileData = false;
}

function isFileDataUplodedForReconcile(){
    return fileData;
}
function convertDataFromFileForReconcile(data, separator) {
    if (data && data.trim().length > 0) {
        var reportData = [];
            var dataLines = data.split("\n");
        var dataTypeObject = {};
        var indexTypeMap = {};
        var dataTypeParts = dataLines[0].split(separator);
            for (var cols = 0; cols < dataTypeParts.length; cols++) {
                var def = {};
                def.type = 'level';
                if(dataTypeParts[cols] !==""){
                if (dataTypeParts[cols].search('Date') !== -1) {
                    def.type = 'date string';
                    dataTypeObject[dataTypeParts[cols]] = def;
                } else if (dataTypeParts[cols].search('Amount') !== -1) {
                    def.type = 'number';
                    dataTypeObject[dataTypeParts[cols]] = def;
                } else{
                    dataTypeObject[dataTypeParts[cols]] = def;
                }
                indexTypeMap[cols] = def.type;
            }
        }
            reportData.push(dataTypeObject);
            for (var lines = 1; lines < dataLines.length; lines++) {
                var dataObject = {};
                var dataLineParts = dataLines[lines].split(separator);
                for (var cols = 0; cols < dataLineParts.length; cols++) {
                    if (indexTypeMap[cols] == "number") {
                        dataObject[dataTypeParts[cols]] = dataLineParts[cols];
                    } else if (indexTypeMap[cols] == "date string") {
                        dataObject[dataTypeParts[cols]] = dataLineParts[cols];
                    } else {
                        dataObject[dataTypeParts[cols]] = dataLineParts[cols];
                    }
                }
                reportData.push(dataObject);
            }
         
        return reportData;
    }
    return null;
}
