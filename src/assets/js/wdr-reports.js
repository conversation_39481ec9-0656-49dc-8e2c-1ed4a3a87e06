

var pivot;
var wdr_load_status = false;
var currReportConfig;
var previousDate;
var previousReportDataMap = {};
var previousSliceColumns = [];
var oopIndices = {};
var indicesToBookingTypeMap = {};
var ticketNumberClickHandlerFN = null;
var ticketNumberClickHandlerClass = null;
var saveData = [];
var filtersRemoved = false;
var previousFilteredDta = {};
var userid;
var sToken;
var startdate;
var enddate;
var userEmailId;
var reportType;
var prevWdrdata;
var ticketTransactionIdMap = {};
var ticketTransactionIdMapMap = {};
var callLoadCardTransactionData = null;
var filteredDataExists = false;

var currency = '';
function getApidata(loadReport, handlerClass) {
    callLoadCardTransactionData = loadReport;
    ticketNumberClickHandlerClass = handlerClass;
}
function setTicketNumberClickHandlerFN(handlerFN, handlerClass) {
    ticketNumberClickHandlerFN = handlerFN;
    ticketNumberClickHandlerClass = handlerClass;
}
function initializeWdr(reportsDataUrl, wdrData, userId,userEmail, curre,selectedReportDate,stoken) {
    wdr_load_status = false;
    reportType = selectedReportDate;
    currReportConfig = wdrData;
    prevWdrdata = JSON.parse(JSON.stringify((wdrData.slice)));
    userid = userId;
    sToken = stoken;
    userEmailId=userEmail;
    var tempSlice = localStorage.getItem("slice")
    if (tempSlice && tempSlice !== 'undefined') {
        tempSlice = JSON.parse(localStorage.getItem("slice"));
    }
    currency = curre;
    if (tempSlice && tempSlice.configid === currReportConfig.id) {
        currReportConfig.slice = JSON.parse(JSON.stringify((tempSlice.slice)));
        
    }
    filteredDataExists = false;
    apiIntiate = true;
    oopIndices = {};
    indicesToBookingTypeMap = {};
    previousDate = reportsDataUrl;
    if (document.getElementById("wdr-component") != null) {
        var previousSavedData = previousReportDataMap[reportsDataUrl];
        if(reportType && reportType==='travel'){
            const searchParams = new URLSearchParams(reportsDataUrl);
            for (const param of searchParams) {
                if(param[0]==='startDate'){
                startdate = new Date (param[1].split('T')[0]);
                }
                if(param[0]==='endDate'){
                enddate = new Date (param[1].split('T')[0]);
                }
              }
              
            }
        if (!previousSavedData || previousSavedData.length == 0) {
            if (previousSliceColumns.length > 0 && currReportConfig.id !== 'Card Transactions') {
                addPreviousColumnFilter(currReportConfig);
            }
           
            getData(reportsDataUrl, initializeWdrWidthData);
           
        } else {
            //if(currReportConfig.id==='Transactions'){
            apiIntiate = false;
            var filteredData = applyFiltersOnData(previousSavedData);
            addPreviousColumnFilter(currReportConfig);
            if (filteredData != null && (filteredData && filteredData.length>1)) {
                filteredDataExists = true;
                applySuffixToVendorForBookingTypeIdentification(filteredData);
                initializeWdrWidthData(filteredData);

            } else {
                filteredDataExists = false;
                wdr_load_status = true;
            }
        }
        ticketTransactionIdMap = ticketTransactionIdMapMap[reportsDataUrl];
    }
}

if (window.addEventListener) {
    window.addEventListener('load', setupTicketNumberClickListener);
} else {
    window.attachEvent('onload', setupTicketNumberClickListener);
}

function isApiCompleted() {
    return apiIntiate;
}
function setupTicketNumberClickListener() {
    $(document).off('click').on('click', '.transaction-ticket-number', function (event) {
        
        var transactionId = event.target.getAttribute('attr-data');
        ticketNumberClickHandlerFN(transactionId, ticketNumberClickHandlerClass);
    });
}
function getCurrency() {
    return currency;
}
function initializeWdrWidthData(reportData) {
    if (wdr_load_status === false) {
        pivot = new WebDataRocks({
            container: "#wdr-component",
            beforetoolbarcreated: customizeToolbar,
            customizeCell: customizeCellFunction,
            height: 700,
            toolbar: true,
            report: {
                dataSource: {
                    data: reportData,
                    fieldSeparator: ";"
                },
                formats: [{
                    name: "formatForPriceColumn",
                    decimalPlaces: 2,
                    nullValue: "0",
                    infinityValue: "NA",
                    divideByZeroValue: "NA",
                    thousandsSeparator: ",",
                    currencySymbol: getCurrency()
                },
                {
                    maxDecimalPlaces: 2
                },
                {
                    name: "formatForPolicyColumn",
                    decimalPlaces: 0,
                    nullValue: "0",
                    divideByZeroValue: "0",
                },
                {
                    name: "formatForPolicyPercColumn",
                    decimalPlaces: 2,
                    nullValue: "0",
                    infinityValue: "NA",
                    divideByZeroValue: "NA",
                    currencySymbol: "%",
                    currencySymbolAlign: "right"
                }],
                tableSizes: currReportConfig.tableSizes,
                options: {
                    datePattern: "MM/dd/yyyy",
                    dateTimePattern: "MM/dd/yyyy HH:mm:ss",
                    'grid': {
                        'showHeaders': false,
                        type: currReportConfig.type,
                        "showTotals": "off",
                        "showGrandTotals": currReportConfig.showGrandTotals
                    },
                    "showAggregationLabels": currReportConfig.showLabel,
                },
                "slice": currReportConfig.slice
            },
            reportcomplete: function () {
                pivot.off("reportcomplete");
                if (currReportConfig.supportedToolbarButtons && currReportConfig.supportedToolbarButtons.length > 0) {
                    for (var i = 0; i < currReportConfig.supportedToolbarButtons.length; i++) {
                        $('#' + currReportConfig.supportedToolbarButtons[i]).attr("style", "display: inline-block !important;");
                    }
                }
                // if (currReportConfig.showFieldsTab) {
                //     $("#wdr-tab-fields").attr("style", "display: inline-block !important;");
                //     $("#fm-tab-newtab").attr("style", "margin-right: -48px !important;");
                // }
                wdr_load_status = true;
            },
        });
        webdatarocks.on("reportchange", 'reportChanged');
        webdatarocks.on("aftergriddraw", 'redraw');
    }
}
var clearFiltersIcon = '<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="122.88px" height="110.668px" viewBox="0 0 122.88 110.668" enable-background="new 0 0 122.88 110.668" xml:space="preserve"><g><path fill-rule="evenodd" clip-rule="evenodd" d="M91.124,15.645c12.928,0,23.406,10.479,23.406,23.406 c0,12.927-10.479,23.406-23.406,23.406c-12.927,0-23.406-10.479-23.406-23.406C67.718,26.125,78.197,15.645,91.124,15.645 L91.124,15.645z M2.756,0h117.322c1.548,0,2.802,1.254,2.802,2.802c0,0.848-0.368,1.622-0.996,2.139l-10.667,13.556 c-1.405-1.375-2.95-2.607-4.614-3.672l6.628-9.22H9.43l37.975,46.171c0.59,0.516,0.958,1.254,0.958,2.102v49.148l21.056-9.623 V57.896c1.651,1.9,3.548,3.582,5.642,4.996v32.133c0,1.105-0.627,2.064-1.586,2.506l-26.476,12.758 c-1.327,0.773-3.023,0.332-3.798-1.033c-0.258-0.441-0.368-0.92-0.368-1.4V55.02L0.803,4.756c-1.07-1.106-1.07-2.839,0-3.945 C1.355,0.258,2.056,0,2.756,0L2.756,0z M96.93,28.282c1.328-1.349,3.489-1.355,4.825-0.013c1.335,1.342,1.341,3.524,0.013,4.872 l-5.829,5.914l5.836,5.919c1.317,1.338,1.299,3.506-0.04,4.843c-1.34,1.336-3.493,1.333-4.81-0.006l-5.797-5.878l-5.807,5.889 c-1.329,1.349-3.489,1.355-4.826,0.013c-1.335-1.342-1.341-3.523-0.013-4.872l5.83-5.913l-5.836-5.919 c-1.317-1.338-1.3-3.507,0.04-4.843c1.339-1.336,3.492-1.333,4.81,0.006l5.796,5.878L96.93,28.282L96.93,28.282z"/></g></svg>';
function myWdrCsvExportHandler() {
    if (currReportConfig.id === 'Transactions') {
        callLoadCardTransactionData(ticketNumberClickHandlerClass)
    }
}
function customizeToolbar(toolbar) {
    globalToolbarObj = toolbar;
    var tabs = toolbar.getTabs();
    toolbar.getTabs = function () {
        var supportedTabs = tabs.filter(function (item) {
            if (currReportConfig.supportedToolbarButtons.includes(item.id)) {
                if (item.id === "wdr-tab-export" && currReportConfig.id === 'Transactions') {
                    var export_csv_item = { title: "To CSV", id: "wdr-tab-export-csv", icon: exportCsvIcon, handler: myWdrCsvExportHandler };
                    item.menu[0] = export_csv_item;
                }
                return true;
            }
            return false;
        });
        supportedTabs.push({
            id: "fm-tab-newtab",
            title: "Reset all filters",
            handler: newtabHandlerblue,
            rightGroup: true,
            icon: clearFiltersIcon
        });
        return supportedTabs;
    }
}
function newtabHandlerblue() {
    clearFilters();
};
function clearPreviousSliceColumn() {
    previousSliceColumns = [];
}
function clearFilters() {
    let sliceValue1 = {};
    let index1 = saveData.findIndex((test) => test.id === currReportConfig.id)
    if (index1 > -1) {
        saveData[index1].slice = prevWdrdata;
        localStorage.setItem("slice", null);
    }

    for (let i = 0; i < previousSliceColumns.length; i++) {
        if (previousSliceColumns && previousSliceColumns[i].id === currReportConfig.id) {
            previousSliceColumns.splice(i, 1);
            let index1 = saveData.findIndex((test) => test.id === currReportConfig.id)
            sliceValue1 = saveData[index1];
            if (saveData[index1].reportFilters) {
                sliceValue1.slice['reportFilters'] = saveData[index1].slice.reportFilters;
            }
            // previousSliceColumns.push(sliceValue1);
            var filteredData = applyFiltersOnData(previousReportDataMap[previousDate]);
            clearPreviousColumnFilter(currReportConfig);
            if (filteredData != null) {
                filteredDataExists = true;
                wdr_load_status = false;
                applySuffixToVendorForBookingTypeIdentification(filteredData);
                initializeWdrWidthData(filteredData)
            } else {
                filteredDataExists = false;
                wdr_load_status = true;
            }
        }
    }

}
function isFilterApplied() {
    var found = false;
    for (let item of previousSliceColumns) {
        if (item.configid && item.configid === currReportConfig.id) {
            for (let item1 of saveData) {
                if (item.id === item1.id) {
                    // 
                    if (!objectsAreSame(item.slice, item1.slice, currReportConfig.id)) {
                        found = true;
                        break;
                    }
                }
                //found= true;
            }
        }
    }
    return found;
}
function objectsAreSame(x, y, id) {
    var objectsAreSame = true;
    for (var propertyName in x) {
        // 
        if (propertyName === 'sorting') {
            delete x[propertyName];
        }
        if (y[propertyName]) {
            if (x[propertyName].length !== y[propertyName].length) {
                objectsAreSame = false;
                
                break;
            } else {
                if ((propertyName !== 'expands' && propertyName !== 'reportFilters') && propertyName.length > 0) {
                    let rowValue = x[propertyName];
                    let rowValue1 = y[propertyName];
                    for (let i = 0; i < rowValue.length; i++) {
                        if ((rowValue[i].filter && !rowValue1[i].filter) || (!rowValue[i].filter && rowValue1[i].filter)) {
                            
                            objectsAreSame = false;
                            break;
                        } else {
                            if ((rowValue[i].filter && rowValue1[i].filter) && (rowValue[i].filter.members[0].split('.')[1] !== rowValue1[i].filter.members[0].split('.')[1])) {
                                
                                objectsAreSame = false;
                                break;
                            }

                        }
                    }
                }
                if (id === 'Transactions' && objectsAreSame) {
                    if ((propertyName === 'flatOrder')) {
                        let rowValue = x[propertyName];
                        let rowValue1 = y[propertyName];
                        for (let i = 0; i < rowValue.length; i++) {
                            if (rowValue[i] !== rowValue1[i]) {
                                objectsAreSame = false;
                                break;
                            }
                        }
                        if (x[propertyName].length !== y[propertyName].length) {
                            objectsAreSame = false;
                            
                            break;
                        }
                    }
                    if ((propertyName === 'rows')) {
                        let rowValue = x[propertyName];
                        let rowValue1 = y[propertyName];
                        for (let i = 0; i < rowValue.length; i++) {
                            if (rowValue1[i] && rowValue[i]['uniqueName'] === rowValue1[i]['uniqueName']) {
                                if (!rowValue[i]['sort']) {
                                    rowValue[i]['sort'] = 'sorted';
                                }
                                if (!rowValue1[i]['sort']) {
                                    rowValue[i]['sort'] = 'sorted';
                                }
                                if (rowValue[i]['sort'] !== rowValue1[i]['sort']) {
                                    objectsAreSame = false;
                                    break;
                                }

                            }
                        }
                    }
                }

            }
        } else {
            objectsAreSame = false;
            break;
        }
    }
    if (objectsAreSame)
        return objectsAreSame;
}
function pivotOffButton() {
    wdr_load_status = true;
}
function isFilteredReportDataEmptyJS() {
    return !filteredDataExists;
}
function addPreviousColumnFilter(report) {
    for (let item of previousSliceColumns) {
        if (item && item.id === currReportConfig.id) {
            report.slice = item.slice;
            break;
        }
    }
}
function clearPreviousColumnFilter(report) {
    for (let item of saveData) {
        if (item && item.id === currReportConfig.id) {
            report.slice = item.slice;
            break;
        }
    }
}
function emptyPreviousReportData() {
    previousReportDataMap = {};
}
function isItADateColumn(columnName) {
    if (columnName === 'Start Date'
        || columnName === 'End Date'
        || columnName === 'Booking Date'
        || columnName === 'Transaction Date'
        || columnName === 'Rebooking Date'
        || columnName === 'Expiry Date'
        || columnName === 'Cancellation Date'
        || columnName === 'Transaction date'
        || columnName === 'Departure/Check-in'
        || columnName === 'Returning Departure Date'
        || columnName === 'Outgoing Arrival Date'
    ) {
        return true;
    }
    return false;
}
function getPreviousPivotData() {
    return previousReportDataMap[previousDate];
}
function getPreviousfilteredData() {
    var filteredData = applyFiltersOnData(previousReportDataMap[previousDate]);
    if (filteredData && filteredData.length > 0) {
        filteredData = filteredDataAsSlice();

    }
    return filteredData;

}
function getItemValueAfterDot(itemValue){
    if (itemValue) {
        const dotIndex = itemValue.indexOf('.');
        if ( dotIndex > -1 && itemValue.length > dotIndex) {
            return itemValue.substring(dotIndex + 1);
        }
    }
    return itemValue;
}
function filteredDataAsSlice() {
    var filteredData = applyFiltersOnData(previousReportDataMap[previousDate]);
    var filters = currReportConfig.slice;
    var filteredDataByWDRFilters = [];
    for (let item of filteredData) {
        let keepTheRow = true;
        for (let key in item) {
            let index = filters.rows.findIndex(item2 => item2.uniqueName == key);
            if (index === -1) {
                delete item[key];
            }else{
                if ('filter' in filters.rows[index] && 'members' in filters.rows[index]['filter']){
                    if (filters.rows[index]['filter']['negation'] == true ){
                        for(let val of filters.rows[index]['filter']['members']){
                            if (item[key] === this.getItemValueAfterDot(val)) {
                                keepTheRow = false;
                                break
                            }
                        }
                    }else{
                        let valFound = false;
                        for(let val of filters.rows[index]['filter']['members']){
                            if (item[key] === this.getItemValueAfterDot(val)) {
                                valFound = true;
                                break
                            }
                        }
                        if (!valFound){
                            keepTheRow = false;
                        }
                    }
                }
            }
            if (!keepTheRow){
                break;
            }
        }
        if (keepTheRow){
            filteredDataByWDRFilters.push(item);
        }
    }

    return filteredDataByWDRFilters;

}
function customizeCellFunction(cellBuilder, cellData) {
    if (cellData.rowIndex == 0 && cellData.columnIndex == 0) {
        oopIndices = {};
        indicesToBookingTypeMap = {};
    }

    if (cellData.type == "value") {
        cellBuilder.addClass('alterLeft');
        if (cellData.rowIndex % 2 == 0) {
            cellBuilder.addClass("alter1");
        } else {
            cellBuilder.addClass("alter2");
        }
        if (cellData.hierarchy && cellData.hierarchy !== null
            && isItADateColumn(cellData.hierarchy.uniqueName)) {
            if (cellData.label !== ''
                && !isNaN(cellData.value)
                && !(cellData.isClassicTotalRow
                    || cellData.isDrillThrough
                    || cellData.isGrandTotal
                    || cellData.isGrandTotalRow
                    || cellData.isTotal
                    || cellData.isTotalRow)

            ) {
                try {
                    var correction = (new Date().getTimezoneOffset() - new Date(cellData.value).getTimezoneOffset()) * 60 * 1000
                    var dateParts = new Date(cellData.value).toDateString().split(' ');
                    cellBuilder.text = dateParts[2] + ' ' + dateParts[1] + ' ' + dateParts[3].substring(2);
                    if (cellData.hierarchy.uniqueName == 'Outgoing Arrival Date'  || cellData.hierarchy.uniqueName == 'Returning Departure Date'  || cellData.hierarchy.uniqueName == 'Start Date' || cellData.hierarchy.uniqueName == 'End Date' || cellData.hierarchy.uniqueName == 'Transaction date') {
                        var timePart = new Date(cellData.value).toTimeString().substring(0, 5);
                        cellBuilder.text += '<span class="time-part-' + cellBuilder.attr['data-r'] + '"> ' + timePart + '</span>';
                    }


                } catch (ex) {

                }
            } else {
                cellBuilder.text = '';
            }
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Approved By' && cellData.label !== '') {
            if (cellData.label.endsWith('-NONE-')) {
                cellBuilder.text = ''
            }
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Modified By' && cellData.label !== '') {
            if (cellData.label.endsWith('-NONE-')) {
                cellBuilder.text = ''
            }
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Vendor' && cellData.label !== '') {
            if (cellData.label.endsWith('_Hotel')) {
                cellBuilder.text = hotelTypeSVG1 + " " + cellData.label.slice(0, -6);
            }
            if (cellData.label.endsWith('_Car')) {
                cellBuilder.text = carTypeSVG1 + " " + cellData.label.slice(0, -4);
            }
            if (cellData.label.endsWith('_Air')) {

                var airlineCode = cellData.label.substring(cellData.label.indexOf('(') + 1, cellData.label.indexOf(')'));
                cellBuilder.text = airlineImgTag.replace('__AIRLINE_IMAGE_URL__', getAirlineImageWithFallback(airlineCode))
                    + airlineImgNameTag.replace('__AIRLINE_NAME__', getAirlineName(airlineCode));
            }
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Booking Type' && cellData.label !== '') {
            if (cellData.label == 'Air (one-way)') {
                cellBuilder.text = flightTypeSVG + ' Air (one-way)';
            } else if (cellData.label == 'Air (round-trip)') {
                cellBuilder.text = flightTypeSVG + ' Air (round-trip)';
            } else if (cellData.label == 'Air (multi-city)') {
                cellBuilder.text = flightTypeSVG + ' Air (multi-city)';
            } else if (cellData.label == 'Hotels') {
                cellBuilder.text = hotelTypeSVG + ' Hotel';
            } else if (cellData.label == 'Cars') {
                cellBuilder.text = carTypeSVG + ' Car';
            }
            cellBuilder.addClass("alterLeft");
            indicesToBookingTypeMap[cellBuilder.attr['data-r']] = cellData.label;
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Reference' && cellData.label !== '') {
            let [text1, text2] = (cellBuilder.text.split('-'));
            text1 = text1 + text2;
            cellBuilder.text = '<a href="' + getUrl(ticketTransactionIdMap[text1],ticketTransactionIdMap) + '" style="cursor:pointer; text-decoration: underline;"  target = "_blank" >' + cellBuilder.text + '</a>';
            cellBuilder.addClass("alterLeft");
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Adjustment file' && cellData.label !== '' && cellBuilder.text !== '' && cellBuilder.text !== '-NA-') {
            // let [text1, text2] = (cellBuilder.text.split('-'));
            // text1 = text1 + text2;
            cellBuilder.text = '<a href="' + cellBuilder.text + '" style="cursor:pointer; text-decoration: underline;"  target = "_blank" >View File</a>';
            cellBuilder.addClass("alterLeft");
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Review' && cellData.label !== '') {
            var text1 = 'Review'
            cellBuilder.text = '<a href="' + getApprovalUrl(cellBuilder.text) + '" style="cursor:pointer; text-decoration: underline;"  target = "_blank" >' + text1 + '</a>';
            cellBuilder.addClass("alterLeft");
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Status' && cellBuilder.text === 'approved') {
            cellBuilder.text = '<span' + '" style="color:green;"  >' + cellBuilder.text + '</span>';
            // cellBuilder.addClass("alterLeft");
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Policy Status' && cellData.label !== '') {

            if (cellData.isClassicTotalRow
                || cellData.isDrillThrough
                || cellData.isGrandTotal
                || cellData.isGrandTotalRow
                || cellData.isTotal
                || cellData.isTotalRow
            ) {
                cellBuilder.text = '';
            } else {
                if (cellData.label == 'N') {
                    cellBuilder.text = untickMarkSVG + ' N';
                    oopIndices[cellData.rowIndex] = true;
                } else if (cellData.label == 'Y') {
                    cellBuilder.text = tickMarkSVG + ' Y';
                } else {
                    cellBuilder.text = '';
                }
            }
            cellBuilder.addClass("alterLeft");
            if (cellData.hierarchy.uniqueName == cellData.hierarchy.caption) {
                cellData.hierarchy.caption = 'In Policy?';
            }
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Credit Amount' && cellData.label !== '') {
            cellBuilder.text = getCurrency() + cellData.label;
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Booking fee' && cellData.label !== '') {
            if (cellData.label != '-NA-') {
                cellBuilder.text = getCurrency() + cellData.label;
            } 
            cellBuilder.addClass("alterRight");
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Spend' && cellData.label !== '' || (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Credit Amount' && cellData.label !== '') || (cellData.measure && cellData.measure !== null && cellData.measure && cellData.measure.uniqueName == 'Average price per day' && cellData.label !== '') || (cellData.measure && cellData.measure !== null && cellData.measure && cellData.measure.uniqueName == 'Average price per leg' && cellData.label !== '') || (cellData.measure && cellData.measure !== null && cellData.measure && cellData.measure.uniqueName == 'Average price per night' && cellData.label !== '') || (cellData.measure && cellData.measure !== null && cellData.measure && cellData.measure.uniqueName == 'Average Spend' && cellData.label !== '')) {

            cellBuilder.addClass("alterRight");

        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Pre-tax fare' && cellData.label !== '') {
            if (cellData.label.startsWith('-')) {
                let [text1, text2] = (cellBuilder.text.split('-'));
                text2 = Number(text2);
                let numberFormatter = Intl.NumberFormat('en-US');
                let formatted = numberFormatter.format(text2);
                cellBuilder.text = '-' + getCurrency() + formatted;
            } else if (Number(cellBuilder.text) === 0) {

                cellBuilder.text = '-NA-';
            } else {
                text2 = Number(cellData.label);
                let numberFormatter = Intl.NumberFormat('en-US');
                let formatted = numberFormatter.format(text2);
                cellBuilder.text = getCurrency() + formatted;
            }
            cellBuilder.addClass("alterRight");

        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Taxes & fees' && cellData.label !== '') {
            if (Number(cellBuilder.text) === 0) {

                cellBuilder.text = '-NA-';
            } else {
                cellBuilder.text = getCurrency() + cellData.label;
            }
            cellBuilder.addClass("alterRight");

        }
        if (cellData.hierarchy && cellData.hierarchy !== null && cellData.hierarchy.uniqueName == 'Airline' && cellData.label !== '') {
            if (cellData.label.trim().length == 2) {
                cellBuilder.text = airlineImgTag.replace('__AIRLINE_IMAGE_URL__', getAirlineImageWithFallback(cellData.label.toUpperCase()))
                    + airlineImgNameTag.replace('__AIRLINE_NAME__', getAirlineName(cellData.label.toUpperCase()));
            } else {
                cellBuilder.text = nonImgPlaceHolder + cellData.label;
            }
        }
        if (cellData.hierarchy && cellData.hierarchy !== null && (cellData.hierarchy.uniqueName == 'Policy Details' || cellData.hierarchy.uniqueName == 'Travel Purpose'  || cellData.hierarchy.uniqueName == 'Adjustment note' || cellData.hierarchy.uniqueName == "Adjustment type") && cellData.label !== '') {
            if(cellData.label.trim() != 'Null'){
                cellBuilder.text = `<span   data-placement="top" title="${cellData.label}" data-toggle="tooltip">${cellData.label}</span>`;
            }
        }
      
    }

}
function getWDRLoadStatus() {
    return wdr_load_status;
}
function reportChanged() {
    oopIndices = {};
    indicesToBookingTypeMap = {};

    //getReport();
}
function getUrl(id2,check) {
    let url = '/admin?view=detail&type=detail&name=' + '' + '&bookingType=upcoming&userEmail=' + userEmailId + '&ticketid=' + '' + '&tripid=' + '' + '&transactionid=' + id2;
    return url;
}
function getApprovalUrl(id2) {
    let url = '/admin?' + id2;
    return url;
}
function getReport() {
    if (pivot && pivot.getReport()) {
        let sliceValue = pivot.getReport();
        if (currReportConfig.id === 'Transactions') {
            for (let item of sliceValue.slice.rows) {
                if (item.uniqueName === 'Policy Status') {
                    if (!item.caption) {
                        item['caption'] = 'In Policy?'
                    }
                }
                if (item.uniqueName === 'Spend') {
                    if (!item.caption) {
                        item['caption'] = 'Price'
                    }
                }
            }
        }
        sliceValue['id'] = currReportConfig.id;
        sliceValue['configid'] = currReportConfig.id;

        if (currReportConfig.slice.reportFilters) {
            sliceValue.slice['reportFilters'] = currReportConfig.slice.reportFilters;
        }
        if (currReportConfig.slice.measures) {
            for (let i = 0; i < currReportConfig.slice.measures.length; i++) {
                if (currReportConfig.slice.measures[i]) {
                    sliceValue.slice.measures[i] = currReportConfig.slice.measures[i];
                }
            }
        }
        if (sliceValue.id === currReportConfig.id) {
            if (previousSliceColumns.length > 0) {
                if (previousSliceColumns.find((test) => test.id === currReportConfig.id) !== undefined) {
                    let index1 = previousSliceColumns.findIndex((test) => test.configid === currReportConfig.id)
                    previousSliceColumns.splice(index1, 1);
                    previousSliceColumns.push(sliceValue);
                } else {
                    previousSliceColumns.push(sliceValue);
                }
            } else {
                previousSliceColumns.push(sliceValue);
            }
            if (saveData.length > 0) {
                if (saveData.find((test) => test.id === currReportConfig.id) === undefined) {
                    sliceValue['slice'] = JSON.parse(JSON.stringify(currReportConfig.slice));
                    saveData.push(sliceValue);
                }
            } else {
                saveData.push(sliceValue);
            }
        }
    }
    if (isFilterApplied() && currReportConfig.id === 'Transactions') {
        for (let item of previousSliceColumns) {
            if (item.configid === 'Transactions' && item.slice.flatOrder && !item.slice.reportFilters) {
                var dataToSave = JSON.parse(JSON.stringify(item));
                dataToSave.dataSource.data = [];
                localStorage.setItem("slice", JSON.stringify(dataToSave));
                currReportConfig.slice = JSON.parse(JSON.stringify(item.slice));
                

            }
        }

    }
    // 
    if (currReportConfig.id !== 'Transactions' && currReportConfig.id !== 'Travel Credits' && currReportConfig.id !== 'Approvals History') {
        var found = false;
        for (let item of previousSliceColumns) {
            if (item.configid && item.configid === currReportConfig.id) {
                for (let item1 of saveData) {
                    if (item.id === item1.id) {
                        // 
                        if (!objectsAreSame(item.slice, item1.slice, currReportConfig.id)) {
                            found = true;
                            break;
                        }
                    }
                    //found= true;
                }
            }
        }
        if (found) {
            $('#fm-tab-newtab a .wdr-svg-icon svg').show();
            $('#fm-tab-newtab').show();
        } else {
            $('#fm-tab-newtab').hide();
        }
    }

}
function redraw() {
    setTimeout(() => {
        getReport();

    }, 10);

    var keys = Object.keys(oopIndices);
    if (currReportConfig.id === 'Transactions') {
        for (var iter = 0; iter < keys.length; iter++) {
            $("div[data-r=" + keys[iter] + "]").addClass("alterRed");
            $("div[data-r=" + keys[iter] + "]").children().each(function () {
                $(this).addClass("alterRed");
            });
        }
    }
    keys = Object.keys(indicesToBookingTypeMap);
    for (var iter = 0; iter < keys.length; iter++) {
        if ((indicesToBookingTypeMap[keys[iter]] === 'Air (one-way)' || indicesToBookingTypeMap[keys[iter]] === 'Air (round-trip)' || indicesToBookingTypeMap[keys[iter]] === 'Air (multi-city)')) {
            $(".time-part-" + keys[iter]).show();
        } else {
            $(".time-part-" + keys[iter]).hide();
        }
    }

    setUpAirlineImageFallBack();
    var filteredElements = $('#wdr-component').find('.wdr-filtered-icon');
    var tempSlice1 = localStorage.getItem("slice")
    if (tempSlice1 && tempSlice1 !== 'undefined') {
        tempSlice1 = JSON.parse(localStorage.getItem("slice"));
    }
    if (isFilterApplied() || (tempSlice1 && tempSlice1 !== 'undefined' && currReportConfig.id === 'Transactions')) {
        $('#fm-tab-newtab a .wdr-svg-icon svg').show();
        $('#fm-tab-newtab').show();
    } else {
        $('#fm-tab-newtab').hide();
    }
}
var nonImgPlaceHolder = '<div style="width:30px;display:inline-block;"></div>';
var airlineImgNameTag = '<div style="padding: 0 5px 0 5px;height: 30px;display: inline-block;vertical-align: inherit;">__AIRLINE_NAME__</div>';
var airlineImgTag = '<div style="display: inline-block;position: relative;height: 30px;top: -7px; width:15px;"><img class="wdr-airline-img" style="height: 20px;padding-top: 5px" src="__AIRLINE_IMAGE_URL__"/></div>';
var tickMarkSVG = '<svg style="width: 15px; color: green;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="svg-inline--fa fa-check fa-w-16 fa-7x"><path fill="currentColor" d="M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z" class=""></path></svg>';
var untickMarkSVG = '<svg style="width: 15px; color: red;" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="times-circle" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="svg-inline--fa fa-times-circle fa-w-16 fa-5x"><path fill="currentColor" d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm121.6 313.1c4.7 4.7 4.7 12.3 0 17L338 377.6c-4.7 4.7-12.3 4.7-17 0L256 312l-65.1 65.6c-4.7 4.7-12.3 4.7-17 0L134.4 338c-4.7-4.7-4.7-12.3 0-17l65.6-65-65.6-65.1c-4.7-4.7-4.7-12.3 0-17l39.6-39.6c4.7-4.7 12.3-4.7 17 0l65 65.7 65.1-65.6c4.7-4.7 12.3-4.7 17 0l39.6 39.6c4.7 4.7 4.7 12.3 0 17L312 256l65.6 65.1z" class=""></path></svg>';
var carTypeSVG = '<svg style="width: 15px; color: indigo; aria-hidden="true" focusable="false" data-prefix="fas" data-icon="car" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="svg-inline--fa fa-car fa-w-16 fa-5x"><path fill="currentColor" d="M499.99 176h-59.87l-16.64-41.6C406.38 91.63 365.57 64 319.5 64h-127c-46.06 0-86.88 27.63-103.99 70.4L71.87 176H12.01C4.2 176-1.53 183.34.37 190.91l6 24C7.7 220.25 12.5 224 18.01 224h20.07C24.65 235.73 16 252.78 16 272v48c0 16.12 6.16 30.67 16 41.93V416c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32v-32h256v32c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32v-54.07c9.84-11.25 16-25.8 16-41.93v-48c0-19.22-8.65-36.27-22.07-48H494c5.51 0 10.31-3.75 11.64-9.09l6-24c1.89-7.57-3.84-14.91-11.65-14.91zm-352.06-17.83c7.29-18.22 24.94-30.17 44.57-30.17h127c19.63 0 37.28 11.95 44.57 30.17L384 208H128l19.93-49.83zM96 319.8c-19.2 0-32-12.76-32-31.9S76.8 256 96 256s48 28.71 48 47.85-28.8 15.95-48 15.95zm320 0c-19.2 0-48 3.19-48-15.95S396.8 256 416 256s32 12.76 32 31.9-12.8 31.9-32 31.9z" class=""></path></svg>';
var hotelTypeSVG = '<svg style="width: 15px; color: indigo; aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bed" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" class="svg-inline--fa fa-bed fa-w-20 fa-3x"><path fill="currentColor" d="M176 256c44.11 0 80-35.89 80-80s-35.89-80-80-80-80 35.89-80 80 35.89 80 80 80zm352-128H304c-8.84 0-16 7.16-16 16v144H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v352c0 8.84 7.16 16 16 16h32c8.84 0 16-7.16 16-16v-48h512v48c0 8.84 7.16 16 16 16h32c8.84 0 16-7.16 16-16V240c0-61.86-50.14-112-112-112z" class=""></path></svg>';
var flightTypeSVG = '<svg style="width: 15px; color: indigo; aria-hidden="true" focusable="false" data-prefix="fas" data-icon="plane-departure" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" class="svg-inline--fa fa-plane-departure fa-w-20 fa-3x"><path fill="currentColor" d="M624 448H16c-8.84 0-16 7.16-16 16v32c0 8.84 7.16 16 16 16h608c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16zM80.55 341.27c6.28 6.84 15.1 10.72 24.33 10.71l130.54-.18a65.62 65.62 0 0 0 29.64-7.12l290.96-147.65c26.74-13.57 50.71-32.94 67.02-58.31 18.31-28.48 20.3-49.09 13.07-63.65-7.21-14.57-24.74-25.27-58.25-27.45-29.85-1.94-59.54 5.92-86.28 19.48l-98.51 49.99-218.7-82.06a17.799 17.799 0 0 0-18-1.11L90.62 67.29c-10.67 5.41-13.25 19.65-5.17 28.53l156.22 98.1-103.21 52.38-72.35-36.47a17.804 17.804 0 0 0-16.07.02L9.91 230.22c-10.44 5.3-13.19 19.12-5.57 28.08l76.21 82.97z" class=""></path></svg>';
var carTypeSVG1 = '<svg style="width: 15px; color: rgb(99,99,99);; aria-hidden="true" focusable="false" data-prefix="fas" data-icon="car" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="svg-inline--fa fa-car fa-w-16 fa-5x"><path fill="currentColor" d="M499.99 176h-59.87l-16.64-41.6C406.38 91.63 365.57 64 319.5 64h-127c-46.06 0-86.88 27.63-103.99 70.4L71.87 176H12.01C4.2 176-1.53 183.34.37 190.91l6 24C7.7 220.25 12.5 224 18.01 224h20.07C24.65 235.73 16 252.78 16 272v48c0 16.12 6.16 30.67 16 41.93V416c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32v-32h256v32c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32v-54.07c9.84-11.25 16-25.8 16-41.93v-48c0-19.22-8.65-36.27-22.07-48H494c5.51 0 10.31-3.75 11.64-9.09l6-24c1.89-7.57-3.84-14.91-11.65-14.91zm-352.06-17.83c7.29-18.22 24.94-30.17 44.57-30.17h127c19.63 0 37.28 11.95 44.57 30.17L384 208H128l19.93-49.83zM96 319.8c-19.2 0-32-12.76-32-31.9S76.8 256 96 256s48 28.71 48 47.85-28.8 15.95-48 15.95zm320 0c-19.2 0-48 3.19-48-15.95S396.8 256 416 256s32 12.76 32 31.9-12.8 31.9-32 31.9z" class=""></path></svg>';
var hotelTypeSVG1 = '<svg style="width: 15px; color: rgb(99,99,99);; aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bed" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" class="svg-inline--fa fa-bed fa-w-20 fa-3x"><path fill="currentColor" d="M176 256c44.11 0 80-35.89 80-80s-35.89-80-80-80-80 35.89-80 80 35.89 80 80 80zm352-128H304c-8.84 0-16 7.16-16 16v144H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v352c0 8.84 7.16 16 16 16h32c8.84 0 16-7.16 16-16v-48h512v48c0 8.84 7.16 16 16 16h32c8.84 0 16-7.16 16-16V240c0-61.86-50.14-112-112-112z" class=""></path></svg>';
var exportCsvIcon = '<div style="display: block;width:30px;height:30px;position: relative;left: 2px;margin-top: 4px; "><img style="margin-top: 3px;" src="assets/images/icons8-csv-50.png"/></div>';
function padNum(input) {
    return (input / Math.pow(10, 2)).toFixed(2).split('.')[1];
}
function getData(path, getDataCallBack) {
    // return the ajax Deferred object, so manual calls can be chained with .then(callback)
    return $.ajax({
        type: "GET",
        url: path,
        beforeSend: function(xhr){xhr.setRequestHeader('Authorization', 'Bearer'+' '+userEmailId+':'+sToken)},
        complete: function (data) {
            if (previousDate == path) {
                var previousReportData = convertDataToJSON(data.responseText, ';');
                previousReportDataMap[path] = previousReportData;
                apiIntiate = false;

                var localTicketTransactionIdMap = {};
                for (let counter = 1; counter < previousReportData.length; counter++) {
                    var transactionDone = false;
                    var transactionCounter = 1;
                    while (!transactionDone) {
                        if (!(localTicketTransactionIdMap[previousReportData[counter]['Reference'] + padNum(transactionCounter)])) {
                            localTicketTransactionIdMap[previousReportData[counter]['Reference'] + padNum(transactionCounter)] = previousReportData[counter]['Transaction Id'];
                            previousReportData[counter]['Reference'] = previousReportData[counter]['Reference'] + "-" + padNum(transactionCounter);
                            transactionDone = true;
                        } else {
                            transactionCounter++;
                        }
                    }
                }
                ticketTransactionIdMapMap[path] = localTicketTransactionIdMap;
                ticketTransactionIdMap = localTicketTransactionIdMap;
                var filteredData = applyFiltersOnData(previousReportData);

                if (filteredData != null && (filteredData && filteredData.length>1)) {
                    filteredDataExists = true;
                    applySuffixToVendorForBookingTypeIdentification(filteredData);
                    getDataCallBack(filteredData);
                } else {
                    filteredDataExists = false;
                    wdr_load_status = true;
                }
            }
        },
        dataType: "text/html",
    });
}
function applySuffixToVendorForBookingTypeIdentification(reportData) {
    for (var counter1 = 1; counter1 < reportData.length; counter1++) {
        if (reportData[counter1]['Booking Type'] === 'Hotels') {
            var name = reportData[counter1]['Vendor'];
            reportData[counter1]['Vendor'] = name + '_Hotel'
        }
        if (reportData[counter1]['Booking Type'] === 'Cars') {

            var name = reportData[counter1]['Vendor'];
            reportData[counter1]['Vendor'] = name + '_Car'
        }
        if (reportData[counter1]['Booking Type'] && (reportData[counter1]['Booking Type'] === 'Air (one-way)' || reportData[counter1]['Booking Type'] === 'Air (round-trip)' || reportData[counter1]['Booking Type'] === 'Air (multi-city)')) {

            var name = airlineImgNameTag.replace('__AIRLINE_NAME__', getAirlineName(reportData[counter1]['Vendor']));
            reportData[counter1]['Vendor'] = name + '(' + reportData[counter1]['Vendor'] + ')' + '_Air'
        }
    }

}
function getTimeZoneOffset(inputDate) {
    const currentDate = new Date(inputDate);
    const offsetMinutes = currentDate.getTimezoneOffset();
    
    const sign = offsetMinutes > 0 ? '-' : '+';
    const hours = Math.abs(Math.floor(offsetMinutes / 60));
    const minutes = Math.abs(offsetMinutes % 60);
  
    const formattedOffset = `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  
    return formattedOffset;
  }
function convertDataToJSON(data, separator) {
    if (data && data.trim().length > 0) {
        var reportData = [];
        if (currReportConfig && currReportConfig.id === 'Travel Credits') {
            var data1 = JSON.parse(data);
            var dataLines = data1.data.split("\n");
        } else {
            var dataLines = data.split("\n");
        }
        var dataTypeObject = {};
        var indexTypeMap = {};
        var dataTypeParts = dataLines[0].split(separator);
        if (currReportConfig && currReportConfig.id !== 'Travel Credits') {
            for (var cols = 0; cols < dataTypeParts.length; cols++) {
                var def = {};
                def.type = 'level';
                if (dataTypeParts[cols].indexOf('ds+') == 0) {
                    def.type = 'datetime';
                    dataTypeObject[dataTypeParts[cols].substring(3)] = def;
                } else if (dataTypeParts[cols].indexOf('-') == 0) {
                    def.type = 'number';
                    dataTypeObject[dataTypeParts[cols].substring(1)] = def;
                } else {
                    dataTypeObject[dataTypeParts[cols]] = def;
                }
                indexTypeMap[cols] = def.type;
            }
            reportData.push(dataTypeObject);
            for (var lines = 1; lines < dataLines.length; lines++) {
                var dataObject = {};
                var dataLineParts = dataLines[lines].split(separator);
                for (var cols = 0; cols < dataLineParts.length; cols++) {
                    if (indexTypeMap[cols] == "number") {
                        dataObject[dataTypeParts[cols].substring(1)] = dataLineParts[cols];
                    } else if (indexTypeMap[cols] == "date string" || indexTypeMap[cols] == "datetime") {
                        var val = dataLineParts[cols];
                        if (val){
                            if (!val.endsWith('Z')){
                                var datePart = val.substring(0, val.length-6);
                                val = datePart;
                            }
                        }
                        dataObject[dataTypeParts[cols].substring(3)] = val;
                    } else {
                        dataObject[dataTypeParts[cols]] = dataLineParts[cols];
                    }
                }
                // if (dataObject['Status'] === 'refund') {
                //     if (dataObject['Policy Status']) {
                //         dataObject['Policy Status'] = -1;
                //     }
                // }
                reportData.push(dataObject);
            }
        } else {
            for (var cols = 0; cols < dataTypeParts.length; cols++) {
                var def = {};
                def.type = 'level';
                dataTypeParts[cols] = dataTypeParts[cols].trim();
                if (dataTypeParts[cols].indexOf('+') == 0) {
                    def.type = 'date string';
                    dataTypeObject[dataTypeParts[cols].substring(1)] = def;
                }
                else {
                    dataTypeObject[dataTypeParts[cols]] = def;
                }
                indexTypeMap[cols] = def.type;
            }
            reportData.push(dataTypeObject);
            for (var lines = 1; lines < dataLines.length; lines++) {
                if (dataLines[lines] !== "") {
                    var dataObject = {};
                    var dataLineParts = dataLines[lines].split(separator);
                    for (var cols = 0; cols < dataLineParts.length; cols++) {
                        if (indexTypeMap[cols] == "number") {
                            dataObject[dataTypeParts[cols].substring(1)] = dataLineParts[cols];
                        } else if (indexTypeMap[cols] == "date string") {
                            dataObject[dataTypeParts[cols].trim().substring(1)] = dataLineParts[cols];
                        } else {
                            dataObject[dataTypeParts[cols]] = dataLineParts[cols];
                        }
                    }
                    reportData.push(dataObject);
                }
            }
        }
        return reportData;
    }
    return null;
}
function setUpAirlineImageFallBack() {
    $(".wdr-airline-img").each(function () {
        var imgUrl = $(this).attr("src");
        var style = $(this).attr("style");
        $(this).replaceWith('<img src="'
            + imgUrl + '" '
            + ' style= "'
            + style + '" '
            + 'onerror="this.src=\'data:image/svg+xml;base64,PHN2ZyBzdHlsZT0id2lkdGg6IDE1cHg7IGNvbG9yOiByZ2IoOTksOTksOTkpOyIgYXJpYS1oaWRkZW49IiB0cnVlIiBmb2N1c2FibGU9ImZhbHNlIiBkYXRhLXByZWZpeD0iZmFzIiBkYXRhLWljb249InBsYW5lLWRlcGFydHVyZSIgcm9sZT0iaW1nIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2NDAgNTEyIiBjbGFzcz0ic3ZnLWlubGluZS0tZmEgZmEtcGxhbmUtZGVwYXJ0dXJlIGZhLXctMjAgZmEtM3ggd2RyLXVpLWVsZW1lbnQiPjxwYXRoIGZpbGw9ImN1cnJlbnRDb2xvciIgZD0iTTYyNCA0NDhIMTZjLTguODQgMC0xNiA3LjE2LTE2IDE2djMyYzAgOC44NCA3LjE2IDE2IDE2IDE2aDYwOGM4Ljg0IDAgMTYtNy4xNiAxNi0xNnYtMzJjMC04Ljg0LTcuMTYtMTYtMTYtMTZ6TTgwLjU1IDM0MS4yN2M2LjI4IDYuODQgMTUuMSAxMC43MiAyNC4zMyAxMC43MWwxMzAuNTQtLjE4YTY1LjYyIDY1LjYyIDAgMCAwIDI5LjY0LTcuMTJsMjkwLjk2LTE0Ny42NWMyNi43NC0xMy41NyA1MC43MS0zMi45NCA2Ny4wMi01OC4zMSAxOC4zMS0yOC40OCAyMC4zLTQ5LjA5IDEzLjA3LTYzLjY1LTcuMjEtMTQuNTctMjQuNzQtMjUuMjctNTguMjUtMjcuNDUtMjkuODUtMS45NC01OS41NCA1LjkyLTg2LjI4IDE5LjQ4bC05OC41MSA0OS45OS0yMTguNy04Mi4wNmExNy43OTkgMTcuNzk5IDAgMCAwLTE4LTEuMTFMOTAuNjIgNjcuMjljLTEwLjY3IDUuNDEtMTMuMjUgMTkuNjUtNS4xNyAyOC41M2wxNTYuMjIgOTguMS0xMDMuMjEgNTIuMzgtNzIuMzUtMzYuNDdhMTcuODA0IDE3LjgwNCAwIDAgMC0xNi4wNy4wMkw5LjkxIDIzMC4yMmMtMTAuNDQgNS4zLTEzLjE5IDE5LjEyLTUuNTcgMjguMDhsNzYuMjEgODIuOTd6IiBjbGFzcz0iIj48L3BhdGg+PC9zdmc+\' " />')
    });
}
function applyFiltersOnData(reportData) {
    if (reportData == null || !reportData || reportData.length < 2) {
        return null;
    }
    var filteredData = JSON.parse(JSON.stringify(reportData));
    var filters = currReportConfig.slice.reportFilters;
    if (filters && filters.length > 0 && currReportConfig.id !== 'Card Transactions') {
        var found = true;
        for (var filterDef of filters) {
            filteredData = applyFilterMemberOnData(filterDef, filteredData);
            if (filteredData == null) {
                break;
            }
        }
    }
    if (filters && filters.length > 0 && currReportConfig.id === 'Card Transactions') {
        var found = true;
        for (var filterDef of filters) {
            var cardfilteredData = applyFilterMemberOnData(filterDef, filteredData);
            let [item1, item2] = filterDef.filter.members[0].split(".");
            if (cardfilteredData == null) {
                toastr.error("There are no transaction on the card" + " " + item2 + " " + "in the selected period .");
                $('#fm-tab-newtab').hide();
                currReportConfig.slice.reportFilters = [];
            }
        }
    }
    if (filteredData != null && reportType && reportType==='travel' && currReportConfig.id !== 'Travel Credits'  && currReportConfig.id !== 'Approvals History') {
         filteredData = filteredData.filter(function (item,index) {
            if (index == 0) {
                return true;
            }
            let itemVal = new Date(item['Start Date'].split('T')[0]);
            if((itemVal.getTime() > startdate.getTime()) && (itemVal.getTime() < enddate.getTime())){
                return true;
            }
            return false;
        });
        

    }
    if (filteredData != null) {
        reportsColumns = currReportConfig.hiddenColumns;
        if (reportsColumns && reportsColumns.length > 0) {
            for (dataItem of filteredData) {
                for (colDef of reportsColumns) {
                    delete dataItem[colDef];
                }
            }
        }
    }
    return filteredData;
}
function applyFilterMemberOnData(filterObj, reportData) {
    var filteredData = reportData.filter(function (item, index) {
        if (index == 0) {
            return true;
        }
        var itemVal = item[filterObj.uniqueName];
        var found = false;
        for (var filterMember of filterObj.filter.members) {
            if (currReportConfig.id === 'Events' && itemVal !== filterMember.split('.')[1]) {
                found = true;
                break;
            }else  if (currReportConfig.id !== 'Events' && itemVal === filterMember.split('.')[1]) {
                found = true;
                break;
            }
        }
        return found;
    });
    if (filteredData && filteredData.length > 1) {
        return filteredData;
    }
    return null;
}
function getAirlineName(airlineCode) {
    var airlineObj = allAirlineIsoCodeToNameMap[airlineCode];
    if (airlineObj && airlineObj['name']) {
        return airlineObj['name'];
    }
    return airlineCode;
}