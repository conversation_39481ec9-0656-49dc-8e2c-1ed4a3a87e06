(function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
// Generated by LiveScript 1.4.0
var presets, simpleStr, wrap, slice$ = [].slice, toString$ = {}.toString;
presets = require('presets').presets;
simpleStr = function(arr){
  return arr.join('');
};
wrap = function(content){
  return "data:image/svg+xml;base64," + btoa(content);
};
(function(){
  var make, handler, ldBar;
  make = {
    head: function(viewBox){
      return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"" + viewBox + "\">";
    },
    gradient: function(dir, dur){
      var colors, ret, len, gx, gy, x, y, i$, i, idx;
      dir == null && (dir = 45);
      dur == null && (dur = 1);
      colors = slice$.call(arguments, 2);
      ret = [this.head("0 0 100 100")];
      len = colors.length * 4 + 1;
      dir = dir * Math.PI / 180;
      gx = Math.pow(Math.cos(dir), 2);
      gy = Math.sqrt(gx - Math.pow(gx, 2));
      if (dir > Math.PI * 0.25) {
        gy = Math.pow(Math.sin(dir), 2);
        gx = Math.sqrt(gy - Math.pow(gy, 2));
      }
      x = gx * 100;
      y = gy * 100;
      ret.push("<defs><linearGradient index=\"gradient\" x1=\"0\" x2=\"" + gx + "\" y1=\"0\" y2=\"" + gy + "\">");
      for (i$ = 0; i$ < len; ++i$) {
        i = i$;
        idx = i * 100 / (len - 1);
        ret.push("<stop offset=\"" + idx + "%\" stop-color=\"" + colors[i % colors.length] + "\"/>");
      }
      ret.push("</linearGradient></defs>\n<rect x=\"0\" y=\"0\" width=\"400\" height=\"400\" fill=\"url(#gradient)\">\n<animateTransform attributeName=\"transform\" type=\"translate\" from=\"-" + x + ",-" + y + "\"\nto=\"0,0\" dur=\"" + dur + "s\" repeatCount=\"indefinite\"/></rect></svg>");
      return wrap(ret.join(""));
    },
    stripe: function(c1, c2, dur){
      var ret, i;
      c1 == null && (c1 = '#b4b4b4');
      c2 == null && (c2 = '#e6e6e6');
      dur == null && (dur = 1);
      ret = [this.head("0 0 100 100")];
      ret = ret.concat([
        "<rect fill=\"" + c2 + "\" width=\"100\" height=\"100\"/>", "<g><g>", (function(){
          var i$, results$ = [];
          for (i$ = 0; i$ < 13; ++i$) {
            i = i$;
            results$.push(("<polygon fill=\"" + c1 + "\" ") + ("points=\"" + (-90 + i * 20) + ",100 " + (-100 + i * 20) + ",") + ("100 " + (-60 + i * 20) + ",0 " + (-50 + i * 20) + ",0 \"/>"));
          }
          return results$;
        }()).join(""), "</g><animateTransform attributeName=\"transform\" type=\"translate\" ", "from=\"0,0\" to=\"20,0\" dur=\"" + dur + "s\" repeatCount=\"indefinite\"/></g></svg>"
      ].join(""));
      return wrap(ret);
    },
    bubble: function(c1, c2, count, dur, size, sw){
      var ret, i$, i, idx, x, r, d;
      c1 == null && (c1 = '#39d');
      c2 == null && (c2 = '#9cf');
      count == null && (count = 15);
      dur == null && (dur = 1);
      size == null && (size = 6);
      sw == null && (sw = 1);
      ret = [this.head("0 0 200 200"), "<rect x=\"0\" y=\"0\" width=\"200\" height=\"200\" fill=\"" + c1 + "\"/>"];
      for (i$ = 0; i$ < count; ++i$) {
        i = i$;
        idx = -(i / count) * dur;
        x = Math.random() * 184 + 8;
        r = (Math.random() * 0.7 + 0.3) * size;
        d = dur * (1 + Math.random() * 0.5);
        ret.push(["<circle cx=\"" + x + "\" cy=\"0\" r=\"" + r + "\" fill=\"none\" stroke=\"" + c2 + "\" stroke-width=\"" + sw + "\">", "<animate attributeName=\"cy\" values=\"190;-10\" times=\"0;1\" ", "dur=\"" + d + "s\" begin=\"" + idx + "s\" repeatCount=\"indefinite\"/>", "</circle>", "<circle cx=\"" + x + "\" cy=\"0\" r=\"" + r + "\" fill=\"none\" stroke=\"" + c2 + "\" stroke-width=\"" + sw + "\">", "<animate attributeName=\"cy\" values=\"390;190\" times=\"0;1\" ", "dur=\"" + d + "s\" begin=\"" + idx + "s\" repeatCount=\"indefinite\"/>", "</circle>"].join(""));
      }
      return wrap(ret.join("") + "</svg>");
    }
  };
  handler = {
    queue: {},
    running: false,
    main: function(timestamp){
      var keepon, removed, k, ref$, func, ret, this$ = this;
      keepon = false;
      removed = [];
      for (k in ref$ = this.queue) {
        func = ref$[k];
        ret = func(timestamp);
        if (!ret) {
          removed.push(func);
        }
        keepon = keepon || ret;
      }
      for (k in ref$ = this.queue) {
        func = ref$[k];
        if (removed.indexOf(func) >= 0) {
          delete this.queue[k];
        }
      }
      if (keepon) {
        return requestAnimationFrame(function(it){
          return this$.main(it);
        });
      } else {
        return this.running = false;
      }
    },
    add: function(key, f){
      var this$ = this;
      if (!this.queue[key]) {
        this.queue[key] = f;
      }
      if (!this.running) {
        this.running = true;
        return requestAnimationFrame(function(it){
          return this$.main(it);
        });
      }
    }
  };
  window.ldBar = ldBar = function(selector, option){
    var xmlns, root, cls, idPrefix, id, domTree, newNode, x$, config, attr, that, isStroke, parseRes, dom, svg, text, group, length, path0, path1, patimg, img, ret, size, this$ = this;
    option == null && (option = {});
    xmlns = {
      xlink: "http://www.w3.org/1999/xlink"
    };
    root = toString$.call(selector).slice(8, -1) === 'String' ? document.querySelector(selector) : selector;
    if (!root.ldBar) {
      root.ldBar = this;
    } else {
      return root.ldBar;
    }
    cls = root.getAttribute('class') || '';
    if (!~cls.indexOf('ldBar')) {
      root.setAttribute('class', cls + " ldBar");
    }
    idPrefix = "ldBar-" + Math.random().toString(16).substring(2);
    id = {
      key: idPrefix,
      clip: idPrefix + "-clip",
      filter: idPrefix + "-filter",
      pattern: idPrefix + "-pattern",
      mask: idPrefix + "-mask",
      maskPath: idPrefix + "-mask-path"
    };
    domTree = function(n, o){
      var k, v;
      n = newNode(n);
      for (k in o) {
        v = o[k];
        if (k !== 'attr') {
          n.appendChild(domTree(k, v || {}));
        }
      }
      n.attrs(o.attr || {});
      return n;
    };
    newNode = function(n){
      return document.createElementNS("http://www.w3.org/2000/svg", n);
    };
    x$ = document.body.__proto__.__proto__.__proto__;
    x$.text = function(t){
      return this.appendChild(document.createTextNode(t));
    };
    x$.attrs = function(o){
      var k, v, ret, results$ = [];
      for (k in o) {
        v = o[k];
        ret = /([^:]+):([^:]+)/.exec(k);
        if (!ret || !xmlns[ret[1]]) {
          results$.push(this.setAttribute(k, v));
        } else {
          results$.push(this.setAttributeNS(xmlns[ret[1]], k, v));
        }
      }
      return results$;
    };
    x$.styles = function(o){
      var k, v, results$ = [];
      for (k in o) {
        v = o[k];
        results$.push(this.style[k] = v);
      }
      return results$;
    };
    x$.append = function(n){
      var r;
      return this.appendChild(r = document.createElementNS("http://www.w3.og/2000/svg", n));
    };
    x$.attr = function(n, v){
      if (v != null) {
        return this.setAttribute(n, v);
      } else {
        return this.getAttribute(n);
      }
    };
    config = {
      "type": 'stroke',
      "img": '',
      "path": 'M10 10L90 10',
      "fill-dir": 'btt',
      "fill": '#25b',
      "fill-background": '#ddd',
      "fill-background-extrude": 3,
      "pattern-size": null,
      "stroke-dir": 'normal',
      "stroke": '#25b',
      "stroke-width": '3',
      "stroke-trail": '#ddd',
      "stroke-trail-width": 0.5,
      "duration": 1,
      "easing": 'linear',
      "value": 0,
      "img-size": null,
      "bbox": null,
      "set-dim": true,
      "aspect-ratio": "xMidYMid"
    };
    config["preset"] = root.attr("data-preset") || option["preset"];
    if (config.preset != null) {
      import$(config, presets[config.preset]);
    }
    for (attr in config) {
      if (that = that = root.attr("data-" + attr)) {
        config[attr] = that;
      }
    }
    import$(config, option);
    if (config.img) {
      config.path = null;
    }
    isStroke = config.type === 'stroke';
    parseRes = function(v){
      var parser, ret;
      parser = /data:ldbar\/res,([^()]+)\(([^)]+)\)/;
      ret = parser.exec(v);
      if (!ret) {
        return v;
      }
      return ret = make[ret[1]].apply(make, ret[2].split(','));
    };
    config.fill = parseRes(config.fill);
    config.stroke = parseRes(config.stroke);
    if (config["set-dim"] === 'false') {
      config["set-dim"] = false;
    }
    dom = {
      attr: {
        "xmlns:xlink": 'http://www.w3.org/1999/xlink',
        preserveAspectRatio: config["aspect-ratio"],
        width: "100%",
        height: "100%"
      },
      defs: {
        filter: {
          attr: {
            id: id.filter,
            x: -1,
            y: -1,
            width: 3,
            height: 3
          },
          feMorphology: {
            attr: {
              operator: +config["fill-background-extrude"] >= 0 ? 'dilate' : 'erode',
              radius: Math.abs(+config["fill-background-extrude"])
            }
          },
          feColorMatrix: {
            attr: {
              values: '0 0 0 0 1    0 0 0 0 1    0 0 0 0 1    0 0 0 1 0',
              result: "cm"
            }
          }
        },
        mask: {
          attr: {
            id: id.mask
          },
          image: {
            attr: {
              "xlink:href": config.img,
              filter: "url(#" + id.filter + ")",
              x: 0,
              y: 0,
              width: 100,
              height: 100,
              preserveAspectRatio: config["aspect-ratio"]
            }
          }
        },
        g: {
          mask: {
            attr: {
              id: id.maskPath
            },
            path: {
              attr: {
                d: config.path || "",
                fill: '#fff',
                stroke: '#fff',
                filter: "url(#" + id.filter + ")"
              }
            }
          }
        },
        clipPath: {
          attr: {
            id: id.clip
          },
          rect: {
            attr: {
              'class': 'mask',
              fill: '#000'
            }
          }
        },
        pattern: {
          attr: {
            id: id.pattern,
            patternUnits: 'userSpaceOnUse',
            x: 0,
            y: 0,
            width: 300,
            height: 300
          },
          image: {
            attr: {
              x: 0,
              y: 0,
              width: 300,
              height: 300
            }
          }
        }
      }
    };
    svg = domTree('svg', dom);
    text = document.createElement('div');
    text.setAttribute('class', 'ldBar-label');
    root.appendChild(svg);
    root.appendChild(text);
    group = [0, 0];
    length = 0;
    this.fit = function(){
      var that, box, d, rect;
      if (that = config["bbox"]) {
        box = that.split(' ').map(function(it){
          return +it.trim();
        });
        box = {
          x: box[0],
          y: box[1],
          width: box[2],
          height: box[3]
        };
      } else {
        box = group[1].getBBox();
      }
      if (!box || box.width === 0 || box.height === 0) {
        box = {
          x: 0,
          y: 0,
          width: 100,
          height: 100
        };
      }
      d = Math.max.apply(null, ['stroke-width', 'stroke-trail-width', 'fill-background-extrude'].map(function(it){
        return config[it];
      })) * 1.5;
      svg.attrs({
        viewBox: [box.x - d, box.y - d, box.width + d * 2, box.height + d * 2].join(" ")
      });
      if (config["set-dim"]) {
        ['width', 'height'].map(function(it){
          if (!root.style[it] || this$.fit[it]) {
            root.style[it] = (box[it] + d * 2) + "px";
            return this$.fit[it] = true;
          }
        });
      }
      rect = group[0].querySelector('rect');
      if (rect) {
        return rect.attrs({
          x: box.x - d,
          y: box.y - d,
          width: box.width + d * 2,
          height: box.height + d * 2
        });
      }
    };
    if (config.path) {
      if (isStroke) {
        group[0] = domTree('g', {
          path: {
            attr: {
              d: config.path,
              fill: 'none',
              'class': 'baseline'
            }
          }
        });
      } else {
        group[0] = domTree('g', {
          rect: {
            attr: {
              x: 0,
              y: 0,
              width: 100,
              height: 100,
              mask: "url(#" + id.maskPath + ")",
              fill: config["fill-background"],
              'class': 'frame'
            }
          }
        });
      }
      svg.appendChild(group[0]);
      group[1] = domTree('g', {
        path: {
          attr: {
            d: config.path,
            'class': isStroke ? 'mainline' : 'solid',
            "clip-path": config.type === 'fill' ? "url(#" + id.clip + ")" : ''
          }
        }
      });
      svg.appendChild(group[1]);
      path0 = group[0].querySelector(isStroke ? 'path' : 'rect');
      path1 = group[1].querySelector('path');
      if (isStroke) {
        path1.attrs({
          fill: 'none'
        });
      }
      patimg = svg.querySelector('pattern image');
      img = new Image();
      img.addEventListener('load', function(){
        var box, that;
        box = (that = config["pattern-size"])
          ? {
            width: +that,
            height: +that
          }
          : img.width && img.height
            ? {
              width: img.width,
              height: img.height
            }
            : {
              width: 300,
              height: 300
            };
        svg.querySelector('pattern').attrs({
          width: box.width,
          height: box.height
        });
        return patimg.attrs({
          width: box.width,
          height: box.height
        });
      });
      if (/.+\..+|^data:/.exec(!isStroke
        ? config.fill
        : config.stroke)) {
        img.src = !isStroke
          ? config.fill
          : config.stroke;
        patimg.attrs({
          "xlink:href": img.src
        });
      }
      if (isStroke) {
        path0.attrs({
          stroke: config["stroke-trail"],
          "stroke-width": config["stroke-trail-width"]
        });
        path1.attrs({
          "stroke-width": config["stroke-width"],
          stroke: /.+\..+|^data:/.exec(config.stroke)
            ? "url(#" + id.pattern + ")"
            : config.stroke
        });
      }
      if (config.fill && !isStroke) {
        path1.attrs({
          fill: /.+\..+|^data:/.exec(config.fill)
            ? "url(#" + id.pattern + ")"
            : config.fill
        });
      }
      length = path1.getTotalLength();
      this.fit();
      this.inited = true;
    } else if (config.img) {
      if (config["img-size"]) {
        ret = config["img-size"].split(',');
        size = {
          width: +ret[0],
          height: +ret[1]
        };
      } else {
        size = {
          width: 100,
          height: 100
        };
      }
      group[0] = domTree('g', {
        rect: {
          attr: {
            x: 0,
            y: 0,
            width: 100,
            height: 100,
            mask: "url(#" + id.mask + ")",
            fill: config["fill-background"]
          }
        }
      });
      svg.querySelector('mask image').attrs({
        width: size.width,
        height: size.height
      });
      group[1] = domTree('g', {
        image: {
          attr: {
            width: size.width,
            height: size.height,
            x: 0,
            y: 0,
            preserveAspectRatio: config["aspect-ratio"],
            "clip-path": config.type === 'fill' ? "url(#" + id.clip + ")" : '',
            "xlink:href": config.img,
            'class': 'solid'
          }
        }
      });
      img = new Image();
      img.addEventListener('load', function(){
        var ret, size;
        if (config["img-size"]) {
          ret = config["img-size"].split(',');
          size = {
            width: +ret[0],
            height: +ret[1]
          };
        } else if (img.width && img.height) {
          size = {
            width: img.width,
            height: img.height
          };
        } else {
          size = {
            width: 100,
            height: 100
          };
        }
        svg.querySelector('mask image').attrs({
          width: size.width,
          height: size.height
        });
        group[1].querySelector('image').attrs({
          width: size.width,
          height: size.height
        });
        this$.fit();
        this$.set(undefined, false);
        return this$.inited = true;
      });
      img.src = config.img;
      svg.appendChild(group[0]);
      svg.appendChild(group[1]);
    }
    svg.attrs({
      width: '100%',
      height: '100%'
    });
    this.transition = {
      value: {
        src: 0,
        des: 0
      },
      time: {},
      ease: function(t, b, c, d){
        t = t / (d * 0.5);
        if (t < 1) {
          return c * 0.5 * t * t + b;
        }
        t = t - 1;
        return -c * 0.5 * (t * (t - 2) - 1) + b;
      },
      handler: function(time, doTransition){
        var ref$, dv, dt, dur, v, node, style, box, dir;
        doTransition == null && (doTransition = true);
        if (this.time.src == null) {
          this.time.src = time;
        }
        ref$ = [this.value.des - this.value.src, (time - this.time.src) * 0.001, +config["duration"] || 1], dv = ref$[0], dt = ref$[1], dur = ref$[2];
        text.textContent = v = doTransition
          ? Math.round(this.ease(dt, this.value.src, dv, dur))
          : this.value.des;
        if (isStroke) {
          node = path1;
          style = {
            "stroke-dasharray": config["stroke-dir"] === 'reverse'
              ? "0 " + length * (100 - v) * 0.01 + " " + length * v * 0.01 + " 0"
              : v * 0.01 * length + " " + ((100 - v) * 0.01 * length + 1)
          };
        } else {
          box = group[1].getBBox();
          dir = config["fill-dir"];
          style = dir === 'btt' || !dir
            ? {
              y: box.y + box.height * (100 - v) * 0.01,
              height: box.height * v * 0.01,
              x: box.x,
              width: box.width
            }
            : dir === 'ttb'
              ? {
                y: box.y,
                height: box.height * v * 0.01,
                x: box.x,
                width: box.width
              }
              : dir === 'ltr'
                ? {
                  y: box.y,
                  height: box.height,
                  x: box.x,
                  width: box.width * v * 0.01
                }
                : dir === 'rtl' ? {
                  y: box.y,
                  height: box.height,
                  x: box.x + box.width * (100 - v) * 0.01,
                  width: box.width * v * 0.01
                } : void 8;
          node = svg.querySelector('rect');
        }
        node.attrs(style);
        if (dt >= dur) {
          delete this.time.src;
          return false;
        }
        return true;
      },
      start: function(src, des, doTransition){
        var ref$, this$ = this;
        ref$ = this.value;
        ref$.src = src;
        ref$.des = des;
        !!(root.offsetWidth || root.offsetHeight || root.getClientRects().length);
        if (!doTransition || !(root.offsetWidth || root.offsetHeight || root.getClientRects().length)) {
          this.time.src = 0;
          this.handler(1000, false);
          return;
        }
        return handler.add(id.key, function(time){
          return this$.handler(time);
        });
      }
    };
    this.set = function(v, doTransition){
      var src, des;
      doTransition == null && (doTransition = true);
      src = this.value || 0;
      if (v != null) {
        this.value = v;
      } else {
        v = this.value;
      }
      des = this.value;
      return this.transition.start(src, des, doTransition);
    };
    this.set(+config.value || 0, false);
    return this;
  };
  return window.addEventListener('load', function(){
    var i$, ref$, len$, node, results$ = [];
    for (i$ = 0, len$ = (ref$ = document.querySelectorAll('.ldBar')).length; i$ < len$; ++i$) {
      node = ref$[i$];
      if (!node.ldBar) {
        results$.push(node.ldBar = new ldBar(node));
      }
    }
    return results$;
  }, false);
})();
function import$(obj, src){
  var own = {}.hasOwnProperty;
  for (var key in src) if (own.call(src, key)) obj[key] = src[key];
  return obj;
}



},{"presets":2}],2:[function(require,module,exports){
// Generated by LiveScript 1.4.0
var presets, out$ = typeof exports != 'undefined' && exports || this;
out$.presets = presets = {
  rainbow: {
    "type": 'stroke',
    "path": 'M10 10L90 10',
    "stroke": 'data:ldbar/res,gradient(0,1,#a551df,#fd51ad,#ff7f82,#ffb874,#ffeb90)',
    "bbox": "10 10 80 10"
  },
  energy: {
    "type": 'fill',
    "path": 'M15 5L85 5A5 5 0 0 1 85 15L15 15A5 5 0 0 1 15 5',
    "stroke": '#f00',
    "fill": 'data:ldbar/res,gradient(45,2,#4e9,#8fb,#4e9)',
    "fill-dir": "ltr",
    "fill-background": '#444',
    "fill-background-extrude": 1,
    "bbox": "10 5 80 10"
  },
  stripe: {
    "type": 'fill',
    "path": 'M15 5L85 5A5 5 0 0 1 85 15L15 15A5 5 0 0 1 15 5',
    "stroke": '#f00',
    "fill": 'data:ldbar/res,stripe(#25b,#58e,1)',
    "fill-dir": "ltr",
    "fill-background": '#ddd',
    "fill-background-extrude": 1,
    "bbox": "10 5 80 10"
  },
  text: {
    "type": 'fill',
    "img": "data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"70\" height=\"20\" viewBox=\"0 0 70 20\"><text x=\"35\" y=\"10\" text-anchor=\"middle\" dominant-baseline=\"central\" font-family=\"arial\">LOADING</text></svg>",
    "fill-background-extrude": 1.3,
    "pattern-size": 100,
    "fill-dir": "ltr",
    "img-size": "70,20",
    "bbox": "0 0 70 20"
  },
  line: {
    "type": 'stroke',
    "path": 'M10 10L90 10',
    "stroke": '#25b',
    "stroke-width": 3,
    "stroke-trail": '#ddd',
    "stroke-trail-width": 1,
    "bbox": "10 10 80 10"
  },
  fan: {
    "type": 'stroke',
    "path": 'M10 90A40 40 0 0 1 90 90',
    "fill-dir": 'btt',
    "fill": '#25b',
    "fill-background": '#ddd',
    "fill-background-extrude": 3,
    "stroke-dir": 'normal',
    "stroke": '#25b',
    "stroke-width": '3',
    "stroke-trail": '#ddd',
    "stroke-trail-width": 0.5,
    "bbox": "10 50 80 40"
  },
  circle: {
    "type": 'stroke',
    "path": 'M50 10A40 40 0 0 1 50 90A40 40 0 0 1 50 10',
    "fill-dir": 'btt',
    "fill": '#25b',
    "fill-background": '#ddd',
    "fill-background-extrude": 3,
    "stroke-dir": 'normal',
    "stroke": '#25b',
    "stroke-width": '3',
    "stroke-trail": '#ddd',
    "stroke-trail-width": 0.5,
    "bbox": "10 10 80 80"
  },
  bubble: {
    "type": 'fill',
    "path": 'M50 10A40 40 0 0 1 50 90A40 40 0 0 1 50 10',
    "fill-dir": 'btt',
    "fill": 'data:ldbar/res,bubble(#39d,#cef)',
    "pattern-size": "150",
    "fill-background": '#ddd',
    "fill-background-extrude": 2,
    "stroke-dir": 'normal',
    "stroke": '#25b',
    "stroke-width": '3',
    "stroke-trail": '#ddd',
    "stroke-trail-width": 0.5,
    "bbox": "10 10 80 80"
  }
};



},{}]},{},[1])
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
