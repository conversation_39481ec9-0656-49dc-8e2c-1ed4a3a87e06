function flyerTemplate(flyerObj){
    return `<div class="flyer">
                <div class="flyer-left">
                    <span class="flyer-airline">${flyerObj.airline} &nbsp;|&nbsp; ${flyerObj.flyerNumber}</span>
                </div>
                <div class="flyer-right">
                    <span class="flyer-remove" onclick="removeFlyer(this);"><img src="assets/images/cross.png" alt="" /></span>
                </div>
            </div>`;
}


function guestTemplate(guestObj){
    return `<div class="flyer">
                <div class="flyer-left">
                    <span class="flyer-airline">${guestObj.hotel} &nbsp;|&nbsp; ${guestObj.guestNumber}</span>
                </div>
                <div class="flyer-right">
                    <span class="flyer-remove" onclick="removeFlyer(this);"><img src="assets/images/cross.png" alt="" /></span>
                </div>
            </div>`;
}


function promoHtml(){
    return `Have a promo code? <a class="link-font" href="javascript:void(0);" onclick="showPromoBox(this);">apply</a>`;
}

function promoApliedHtml(promoValue){
    return `<img class="inlineblock_m" src="assets/images/green-tick.png" /><span class="inlineblock_m">${promoValue} Applied</span> <a class="link-font inlineblock_m" href="javascript:void(0);" onclick="removePromoCode(this);">Remove</a>`;
}

function promoSavingHtml(){
    return `You saved $41`;
}

function addNewTravellerTemplate(){
    return `<div class="card-div" id="addAnotherTravellerDiv">
                <div class="card-div-inner">
                    <div class="traveller-form">
                        <div class="card-div-header">
                            <a class="card-header-link" href="javascript:void(0);" onclick="addAnotherTraveller(this);">+ Add Another Traveller</a>
                        </div>
                    </div>
                </div>
            </div>`;
}

function economyRadioButtons(){
    return `<div class="input-box">
                <label class="input-label primary-label">Do you prefer flying on the basic economy when available</label>
                <div class="checkbox-radio-container">
                    <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="economyYes">
                        <input onchange="economySelection(this);" type="radio" id="economyYes" class="mdl-radio__button" name="economyPreference" value="1">
                        <span class="mdl-radio__label">Yes</span>
                    </label>
                    <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="economyNo">
                        <input onchange="economySelection(this);" type="radio" id="economyNo" class="mdl-radio__button" name="economyPreference" value="0">
                        <span class="mdl-radio__label">No</span>
                    </label>
                </div>
            </div>`;
}



function addTravellerTemplate(obj,count){
    var template = ``;
     template+=`<div class="card-div active shadow personalDetailsCard" id="personalDetailsCard${count}">
            <div class="card-div-inner">
                <form method="post" id="personalDetailsForm${count}" class="personalDetailsForm">
                    <div class="traveller-form">
                        <div class="card-div-header" onclick="openCardEdit(this);">
                            <h3>Personal Information</h3>
                            <div class="card-edit">
                                <span class="edit-text">Edit</span>
                                <span class="edit-icon"><i class="fa fa-angle-down"></i></span>
                            </div>
                            <div class="traveller-short-info">
                                <div class="traveller-short-info-inner"></div>
                            </div>
                        </div>
                        <div class="card-div-body">
                            <div class="row">
                                <div class="col-lg-2 col-md-3 col-sm-4">
                                    <div class="input-box">
                                        <label class="input-label">Title</label>
                                        <div class="select-box title-select-box">
                                            <select class="input-textfield title" id="title${count}" name="title" onblur="updateTopInfo(this);">
                                                <option>Mr.</option>
                                                <option>Mrs.</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                    <div class="input-box input-box-multiple">
                                        <label class="input-label">Name<span class="label-note">*Use same as in id, including middle name</span></label>
                                        <div class="multiple-inputs">
                                            <div class="row">
                                                <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                                    <input class="input-textfield firstName" id="firstName${count}" name="firstName" type="text" placeholder="First Name" onblur="updateTopInfo(this);" />
                                                </div>
                                                <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                                    <input class="input-textfield middleName" id="middleName${count}" name="middleName" type="text" placeholder="Middle Name (If shown on id)" onblur="updateTopInfo(this);" />
                                                </div>
                                                <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                                    <input class="input-textfield lastName" id="lastName${count}" name="lastName" type="text" placeholder="Last Name" onblur="updateTopInfo(this);" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-2 custom-wd-sm">
                                    <div class="input-box">
                                        <label class="input-label">Suffix</label>
                                        <div class="select-box title-select-box">
                                            <select class="input-textfield title" id="suffix${count}" name="suffix${count}">
                                                <option>1</option>
                                                <option>2</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                    <div class="input-box">
                                        <label class="input-label">DOB</label>
                                        <input style="cursor: pointer;" class="input-textfield dob datepicker shortInfoField" id="dob${count}" name="dob" type="tel" placeholder="mm/dd/yyyy" readonly/>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-12 col-sm-12 col-xs-12">
                                    <div class="input-box">
                                        <label class="input-label">Gender</label>
                                        <div class="checkbox-container">
                                            <label class="mdl-radio selection-checkbox checkbox2 checkbox-seperate mdl-js-radio mdl-js-ripple-effect" for="male${count}">
                                                <input type="radio" id="male${count}" class="mdl-radio__button shortInfoField" name="gender" value="Male">
                                                <span class="mdl-radio__label">Male</span>
                                            </label>
                                            <label class="mdl-radio selection-checkbox checkbox2 checkbox-seperate mdl-js-radio mdl-js-ripple-effect" for="female${count}">
                                                <input type="radio" id="female${count}" class="mdl-radio__button shortInfoField" name="gender" value="Female">
                                                <span class="mdl-radio__label">Female</span>
                                            </label>
                                            <label class="mdl-radio selection-checkbox checkbox2 checkbox-seperate mdl-js-radio mdl-js-ripple-effect" for="other${count}">
                                                <input type="radio" id="other${count}" class="mdl-radio__button shortInfoField" name="gender" value="Other">
                                                <span class="mdl-radio__label">Other</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                    <div class="input-box">
                                        <label class="input-label">Email</label>
                                        <input class="input-textfield" type="text" id="email${count}" name="email" placeholder="Email" />
                                    </div>
                                </div>
                            </div>
                            <div class="accord">
                                <div class="accord-header">
                                    <a href="javascript:void(0);" class="heading-link" onclick="openContent(this, {});">
                                        <span>Known Traveller Number</span>
                                        <img src="assets/images/arrow-down1.png" alt="" />
                                    </a>
                                </div>
                                <div class="accord-content">
                                    <div class="row">
                                        <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                            <div class="input-box">
                                                <input maxlength="10" class="input-textfield" id="travellerNumber${count}" name="travellerNumber" type="text" placeholder="KTN Number">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="accord">
                                <div class="accord-header">
                                    <a href="javascript:void(0);" class="heading-link" onclick="openContent(this, {});">
                                        <span>Passport details</span>
                                        <img src="assets/images/arrow-down1.png" alt="" />
                                    </a>
                                </div>
                                <div class="accord-content">
                                    <div class="row">
                                        <div class="col-lg-4  col-md-6 col-sm-6 col-xs-12">
                                            <div class="input-box">
                                                <label class="input-label">Passport Number</label>
                                                <input class="input-textfield shortInfoField" type="text" id="passportNumber${count}" name="passportNumber${count}" placeholder="Passport Number" />
                                            </div>
                                        </div>
                                        <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                            <div class="input-box">
                                                <label class="input-label">Passport Expiry Date</label>
                                                <input style="cursor: pointer;" class="input-textfield shortInfoField passportDate datepicker" id="passportExpiryDate${count}" name="passportExpiryDate${count}" type="tel" placeholder="mm/dd/yyyy" readonly/>
                                            </div>
                                        </div>
                                        <div class="line-break"></div>
                                        <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                            <div class="input-box">
                                                <label class="input-label">Nationality</label>
                                                <div class="select-box">
                                                    <select class="input-textfield js-example-basic-single countryDropdown" id="nationality${count}" name="nationality${count}"></select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                            <div class="input-box">
                                                <label class="input-label">Country</label>
                                                <div class="select-box">
                                                    <select class="input-textfield js-example-basic-single countryDropdown" id="country${count}" name="country${count}"></select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="accord">
                                <div class="accord-header">
                                    <a href="javascript:void(0);" class="heading-link" onclick="openContent(this, {});">
                                        <span>Frequent Flyer Number</span>
                                        <img src="assets/images/arrow-down1.png" alt="" />
                                    </a>
                                </div>
                                <div class="accord-content airlineFlyerNumberDiv-container">
                                    <div class="adderFlyers"></div>
                                    <div class="row d-none d-md-flex">
                                        <div class="col-lg-4 col-md-5  col-sm-12 col-xs-12">
                                            <label class="input-label">Airline</label>
                                        </div>
                                        <div class="col-lg-4  col-md-5  col-sm-12 col-xs-12">
                                            <label class="input-label">Flyer number</label>
                                        </div>
                                    </div>
                                    <div class="airlineFlyerNumberDiv-container">
                                        <div class="row airlineFlyerNumberDiv">
                                            <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12">
                                                <div class="input-box">
                                                    <label class="input-label d-block d-md-none">Airline</label>
                                                    <div class="select-box">
                                                        <select onchange="selectAirlineChange(this);" class="input-textfield airlineOptions js-example-basic-single" id="airlineOptionsTraveller${count}" name="airlineOptionsTraveller${count}"></select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-4  col-md-5 col-sm-12 col-xs-12">
                                                <div class="input-box">
                                                    <label class="input-label d-block d-md-none">Flyer number</label>
                                                    <input class="input-textfield disabledInput flyerNumber" type="text" id="flyerNumberTraveller${count}" name="flyerNumberTraveller${count}" placeholder="" disabled="" />
                                                </div>
                                            </div>
                                            <div class="removeFlyerNumberDiv hide">
                                                <a href="javascript:void(0);" onclick="removeAirlineDiv(this);">remove</a>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                                <button style="margin-left:0;" type="button" class="button button-text disabled d-inline-block d-md-none airlineDisabledButton" onclick="addOtherFFN(this);">Add Another FFN</button>
                                            </div>
                                        </div>
    
                                    </div>
                                </div>
                            </div>
                            <div class="accord">
                                <div class="accord-header">
                                    <a href="javascript:void(0);" class="heading-link" onclick="openContent(this, {});">
                                        <span>Frequent Guest Number</span>
                                        <img src="assets/images/arrow-down1.png" alt="" />
                                    </a>
                                </div>
                                <div class="accord-content">
                                    <div class="adderGuest"></div>
                                    <div class="row d-none d-md-flex">
                                        <div class="col-lg-4 col-md-5 col-sm-12 col-xs-12">
                                            <label class="input-label">Hotels</label>
                                        </div>
                                        <div class="col-lg-4  col-md-5 col-sm-12 col-xs-12">
                                            <label class="input-label">Guest Number</label>
                                        </div>
                                    </div>
                                    <div class="hotelFlyerNumberDiv-container">
                                        <div class="row hotelFlyerNumberDiv">
                                            <div class="col-lg-4 col-md-5  col-sm-12 col-xs-12">
                                                <div class="input-box">
                                                    <label class="input-label d-block d-md-none">Hotels</label>
                                                    <div class="select-box">
                                                        <select onchange="selectHotelChange(this);" class="input-textfield hotelOptions js-example-basic-single" id="hotelOptionsTraveller${count}" name="hotelOptionsTraveller${count}"></select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-4  col-md-5  col-sm-12 col-xs-12">
                                                <div class="input-box">
                                                    <label class="input-label d-block d-md-none">Guest Number</label>
                                                    <input class="input-textfield disabledInput guestNumber" type="text" id="guestNumberTraveller${count}" name="guestNumberTraveller${count}" placeholder="" disabled="" />
                                                </div>
                                            </div>
                                            <div class="removeFlyerNumberDiv hide">
                                                <a href="javascript:void(0);" onclick="removeHotelDiv(this);">remove</a>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                                <button style="margin-left:0;" type="button" class="button button-text disabled d-inline-block d-md-none hotelDisabledButton" onclick="addOtherFGN(this);">Add Another FGN</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="accord-buttton">
                                <div class="row">
                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                        <div class="card-div-footer">
                                            <button type="button" class="button button-primary saveButton" onclick="validateAddTravellerForm(this);">Save</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>`;
    return template;
}


function addTravellerBlankTemplate(){
    var template = ``;
    template+=`<div class="card-div shadow">
                    <div class="add-traveller-blank">
                        <div class="add-traveller-blank-img"><img src="assets/images/user-icon.png" alt=""></div>
                        <div class="add-traveller-blank-text">No traveler added</div>
                        <div class="add-traveller-blank-link"><a href="javascript:void(0);" onclick="addNewTravellerForblankTemplate(this);">Add Traveller</a></div>
                    </div>
                </div>`;
    return template;
}


function loaderTemplate(){
    return `<div class="loader-div"><div class="mdl-spinner mdl-spinner--single-color mdl-js-spinner is-active"></div></div>`;
}




function paymentMethodChangeTemplate(){
    return `<form method="post" id="hotelPreferenceForm1" class="hotelPreferenceForm paymentform-div-left">
                <div class="paymentMethodChange">
                    <div class="paymentMethodHeading">
                        <h4>Change Payment Method</h4>
                    </div>
                    <div class="paymentMethodSection">
                        <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="amax">
                            <input type="radio" id="amax" class="mdl-radio__button" name="paymentMethod" value="amax" checked>
                            <span class="mdl-radio__label">
                                <img class="inlineblock_m" src="assets/images/ae.png" />Amex-2200
                            </span>
                        </label>
                    </div>
                    <div class="paymentMethodSection">
                        <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="applePay">
                            <input type="radio" id="applePay" class="mdl-radio__button" name="paymentMethod" value="applePay">
                            <span class="mdl-radio__label">Apple Pay</span>
                        </label>
                    </div>
                    <div class="paymentMethodSection">
                        <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="paypal">
                            <input type="radio" id="paypal" class="mdl-radio__button" name="paymentMethod" value="paypal">
                            <span class="mdl-radio__label">Paypal</span>
                        </label>
                    </div>

                    <div class="paymentMethodButton">
                        <button onclick="savePaymentMethod(this);" type="button" class="button button-primary" >Save</button>
                    </div>
                </div>
            </form>`;
}

function subscriptionTemplate(){
    return `<form method="post" id="hotelPreferenceForm1" class="hotelPreferenceForm paymentform-div-left">
                <div class="subscription-details subscriptionDiv" id="subscriptionDiv">
                    <ul>
                        <li>
                            <label>Renewal date:</label>
                            <span>06/23/2019</span>
                        </li>
                        <li>
                            <label>Billing Cycle:</label>
                            <span>Yearly ($49/year)</span>
                        </li>
                        <li>
                            <label>Payment Method:</label>
                            <span>
                                <img class="inlineblock_m" src="assets/images/ae.png" />
                                Amex-1008
                                <a onclick="changePaymentMethod();" href="javascript:void(0);" style="padding-left: 5px;">change</a>
                            </span>
                        </li>
                    </ul>
                    <div class="cancelSubscriptionButton">
                        <button onclick="cancelSubscription(this);" type="button" class="button button-text" style="margin-left: 0;">Cancel Subscription</button>
                    </div>
                </div>
            </form>`;
}


function noSubscriptionTemplate(){
    return `<form method="post" id="hotelPreferenceForm1" class="hotelPreferenceForm paymentform-div-left">
                <div id="subscriptionOtherDiv" class="subscriptionOtherDiv">
                    <div class="checkbox-radio-container billingRadio">
                        <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="annually">
                            <input type="radio" id="annually" class="mdl-radio__button" name="billingCycle" value="annually" checked>
                            <span class="mdl-radio__label">Bill annually ($49/year)</span>
                        </label>
                        <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="monthly">
                            <input type="radio" id="monthly" class="mdl-radio__button" name="billingCycle" value="monthly">
                            <span class="mdl-radio__label">Bill monthly ($9/month)</span>
                        </label>
                    </div>
                    <div class="payment">
                        <div class="payment-inner">
                            <h4>
                                <span id="priceText">Pay annually</span>
                                <span class="prices">
                                    <span class="old-price">$49</span>
                                    <span class="new-price hidden">$8</span>
                                </span>
                                <span class="promo-saving" id="promoSaving"></span>
                            </h4>
                            <p class="promo" id="promo">Have a promo code? <a class="link-font" href="javascript:void(0);" onclick="showPromoBox(this);">apply</a></p>
                            <div class="promo-box" id="promoBox">
                                <div class="remove" onclick="removePromoBox(this);">
                                    <img src="assets/images/cross.png" alt="remove">
                                </div>
                                <div class="promo-box-input">
                                    <label class="input-label">Enter Code</label>
                                    <input id="promoCodeInput" class="input-textfield" type="text">
                                    <a class="link-font" href="javascript:void(0);" onclick="applyPromoCode(this);">apply</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-box creditCardBox" id="creditCardBox">
                        <div class="card-box-inner">
                            <div class="remove d-none d-md-block" onclick="removeCreditCardBox1(this);">
                                <img src="assets/images/cross.png" alt="remove">
                            </div>
                            <div class="card-box-heading">
                                <h4>Credit Card</h4>
                            </div>
                            <div class="card-box-content">
                                <div class="stripeElements">
                                    <div class="row">
                                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                                            <div class="input-box input-box-with-icon">
                                                <input class="input-textfield" name="cardNumber" id="cardNumber" placeholder="Card Number" type="text">
                                                <span class="input-box-icon-container">
                                                    <img class="input-box-icon" src="assets/images/credit-card.png">
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-6">
                                            <div class="input-box">
                                                <input class="input-textfield" name="cardExpDate" id="cardExpDate" placeholder="MM/YY" type="text">
                                            </div>
                                        </div>
                                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-6">
                                            <div class="input-box">
                                                <input class="input-textfield" name="cvv" id="cvv" placeholder="CVV" type="text">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="cc-button-container">
                                    <div class="row">
                                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                                            <button type="button" class="button button-primary" onclick="applySubscription(this);">Subscribe</button>
                                            <button type="button" class="button button-text d-md-none" onclick="removeCreditCardBox1(this);">cancel</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="pay-buttons" id="payButtons1">
                        <button type="button" class="button button-primary" onclick="getCreditCardBox1(this);">Pay with credit card</button>
                        <button class="button image-button button-black">
                            <img src="assets/images/pay with apple pay.png" alt="apple pay">
                        </button>
                        <button class="button image-button button-yellow">
                            <img src="assets/images/Paypal.png" alt="paypal">
                        </button>
                        <div class="closeSubscriptionDiv">
                            <a href="javascript:void(0);" class="" onclick="closeSubscriptionDiv(this);">cancel</a>
                        </div>
                    </div>
                </div>
            </form>
            <div class="features subscriptionOtherDiv">
                <ul class="features-list">
                    <li class="features-list-item">Stay at preferred hotels</li>
                    <li class="features-list-item">Search best flights and hotels</li>
                    <li class="features-list-item">Fly on preferred airlines</li>
                    <li class="features-list-item">Always earn frequent flyer miles and hotel loyalty rewards</li>
                    <li class="features-list-item">Automatic flight check-in</li>
                    <li class="features-list-item">TSA Pre expedited screening</li>
                    <li class="features-list-item">Best seat assignment</li>
                </ul>
            </div>`;
}

function selectAirlineTemplate(c,ffnNumber){
    return `<div class="row airlineFlyerNumberDiv" id="airlineFlyerNumberDiv${c}">
                <div class="col-lg-4 col-md-5  col-sm-12 col-xs-12">
                    <div class="input-box">
                        <div class="select-box">
                            <select onchange="selectAirlineChange(this);" class="input-textfield airlineOptions js-example-basic-single" id="airlineOptions${c}" name="airlineOptions${c}"></select>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4  col-md-5  col-sm-12 col-xs-12">
                    <div class="input-box">
                        <input class="input-textfield disabledInput flyerNumber" type="text" id="flyerNumber${c}" name="flyerNumber${c}" placeholder="" disabled="" value = "${ffnNumber}"/>
                    </div>
                </div>
                <div class="removeFlyerNumberDiv hide">
                    <a href="javascript:void(0);" onclick="removeAirlineDiv(this);">remove</a>
                </div>
            </div>`;
}

function selectHotelTemplate(c){
    return `<div class="row hotelFlyerNumberDiv">
                <div class="col-lg-4 col-md-5  col-sm-12 col-xs-12">
                    <div class="input-box">
                        
                        <div class="select-box">
                            <select onchange="selectHotelChange(this);" class="input-textfield hotelOptions js-example-basic-single" id="hotelOptions${c}" name="hotelOptions${c}">

                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4  col-md-5 col-sm-12 col-xs-12">
                    <div class="input-box">
                        
                        <input class="input-textfield disabledInput guestNumber" type="text" id="guestNumber${c}" name="guestNumber${c}" placeholder="" disabled="" />
                    </div>
                </div>
                <div class="removeFlyerNumberDiv hide">
                    <a href="javascript:void(0);" onclick="removeHotelDiv(this);">remove</a>
                </div>
            </div>`;
}

function hotelTemplate(obj, type) {
    var checked = "";
    if (obj.value == "ANY") {
        checked = "checked"
    } else {
        checked = "";
    }
    var template = `<div class="airline-div">
                        <label class="mdl-checkbox mdl-js-checkbox selection-checkbox mdl-js-ripple-effect" for="${obj.value}">
                            <input value="${obj.value}" data-value="${obj.label}" type="checkbox" id="${obj.value}" name="prefferedHotel[]" class="mdl-checkbox__input" ${checked}> 
                            <span class="mdl-checkbox__label">
                                <span class="mdl-checkbox__label-img">
                                    <img class="d-none d-md-block" src="assets/images/hotels/${obj.value}.png" alt="${obj.value}" />
                                    <img class="d-block d-md-none" src="assets/images/hotels/${obj.value}_xs.png" alt="${obj.value}" />
                                </span>
                                <span class="mdl-checkbox__label-text">${obj.label}</span>
                            </span>
                        </label>
                    </div>`;
    return template;
}


function primaryEmailtemplate(email){
    return `<div class="input-box emailEdit">
                <label class="input-label">Primary email</label>
                <input class="input-textfield" type="text" id="primaryEmail" name="primaryEmail" value="${email}" placeholder="Email" />
            </div>`;
}

function secondaryEmailtemplate(email,c){
    return `<div class="col-lg-4 col-md-4 col-sm-6 addSecondaryEmail">
                <div class="input-box emailEdit">
                    <label class="input-label">Add secondary email</label>
                    <input class="input-textfield" type="text" id="secEmail${c}" name="secEmail${c}" value="${email}" placeholder="Email" />
                </div>
            </div>
            <div class="line-break"></div>`;
}


function primaryEmailFilledtemplate(email){
    return `<div class="input-box">
                <label class="input-label text-label">Primary email</label>
                <span class="input-text">${email}</span>
                <a href="javascript:void(0);" onclick="editPrimaryEmail(this);" style="margin-left:6px;">Edit</a>
            </div>`;
}

function secondaryEmailFilledtemplate(email){
    return `                                                            
                <div class="input-box">
                    <label class="input-label text-label">Secondary email</label>
                    <span class="input-text">${email}</span>
                    <a href="javascript:void(0);" onclick="editSecondaryEmail(this);" style="margin-left:6px;">Edit</a>
                    <span style="position:relative; top:1px;">|</span>
                    <a href="javascript:void(0);" onclick="deleteSecondaryEmail(this);">Delete</a>
                </div>`;
}




function secondaryEmailLinktemplate(){
    return `<a href="javascript:void(0);" onclick="addSecondaryEmail(this);">+ Add Secondary email</a>`;
}