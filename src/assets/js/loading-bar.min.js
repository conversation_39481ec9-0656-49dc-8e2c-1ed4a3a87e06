!function t(e,i,r){function a(n,s){if(!i[n]){if(!e[n]){var h="function"==typeof require&&require;if(!s&&h)return h(n,!0);if(l)return l(n,!0);var d=new Error("Cannot find module '"+n+"'");throw d.code="MODULE_NOT_FOUND",d}var o=i[n]={exports:{}};e[n][0].call(o.exports,function(t){var i=e[n][1][t];return a(i||t)},o,o.exports,t,e,i,r)}return i[n].exports}for(var l="function"==typeof require&&require,n=0;n<r.length;n++)a(r[n]);return a}({1:[function(t,e,i){function r(t,e){var i={}.hasOwnProperty;for(var r in e)i.call(e,r)&&(t[r]=e[r]);return t}var a,l,n=[].slice,s={}.toString;a=t("presets").presets,l=function(t){return"data:image/svg+xml;base64,"+btoa(t)},function(){var t,e,i;t={head:function(t){return'<?xml version="1.0" encoding="utf-8"?>\n        <svg xmlns="http://www.w3.org/2000/svg" viewBox="'+t+'">'},gradient:function(t,e){var i,r,a,s,h,d,o,u,f,c;for(null==t&&(t=45),null==e&&(e=1),i=n.call(arguments,2),r=[this.head("0 0 100 100")],a=4*i.length+1,t=t*Math.PI/180,s=Math.pow(Math.cos(t),2),h=Math.sqrt(s-Math.pow(s,2)),t>.25*Math.PI&&(h=Math.pow(Math.sin(t),2),s=Math.sqrt(h-Math.pow(h,2))),d=100*s,o=100*h,r.push('<defs><linearGradient index="gradient" x1="0" x2="'+s+'" y1="0" y2="'+h+'">'),u=0;u<a;++u)c=100*(f=u)/(a-1),r.push('<stop offset="'+c+'%" stop-color="'+i[f%i.length]+'"/>');return r.push('</linearGradient></defs>\n<rect x="0" y="0" width="400" height="400" fill="url(#gradient)">\n<animateTransform attributeName="transform" type="translate" from="-'+d+",-"+o+'"\nto="0,0" dur="'+e+'s" repeatCount="indefinite"/></rect></svg>'),l(r.join(""))},stripe:function(t,e,i){var r,a;return null==t&&(t="#b4b4b4"),null==e&&(e="#e6e6e6"),null==i&&(i=1),r=[this.head("0 0 100 100")],r=r.concat(['<rect fill="'+e+'" width="100" height="100"/>',"<g><g>",function(){var e,i=[];for(e=0;e<13;++e)a=e,i.push('<polygon fill="'+t+'" points="'+(20*a-90)+",100 "+(20*a-100)+",100 "+(20*a-60)+",0 "+(20*a-50)+',0 "/>');return i}().join(""),'</g><animateTransform attributeName="transform" type="translate" ','from="0,0" to="20,0" dur="'+i+'s" repeatCount="indefinite"/></g></svg>'].join("")),l(r)},bubble:function(t,e,i,r,a,n){var s,h,d,o,u,f;for(null==t&&(t="#39d"),null==e&&(e="#9cf"),null==i&&(i=15),null==r&&(r=1),null==a&&(a=6),null==n&&(n=1),s=[this.head("0 0 200 200"),'<rect x="0" y="0" width="200" height="200" fill="'+t+'"/>'],h=0;h<i;++h)d=-h/i*r,o=184*Math.random()+8,u=(.7*Math.random()+.3)*a,f=r*(1+.5*Math.random()),s.push(['<circle cx="'+o+'" cy="0" r="'+u+'" fill="none" stroke="'+e+'" stroke-width="'+n+'">','<animate attributeName="cy" values="190;-10" times="0;1" ','dur="'+f+'s" begin="'+d+'s" repeatCount="indefinite"/>',"</circle>",'<circle cx="'+o+'" cy="0" r="'+u+'" fill="none" stroke="'+e+'" stroke-width="'+n+'">','<animate attributeName="cy" values="390;190" times="0;1" ','dur="'+f+'s" begin="'+d+'s" repeatCount="indefinite"/>',"</circle>"].join(""));return l(s.join("")+"</svg>")}},e={queue:{},running:!1,main:function(t){var e,i,r,a,l,n,s=this;e=!1,i=[];for(r in a=this.queue)(n=(l=a[r])(t))||i.push(l),e=e||n;for(r in a=this.queue)l=a[r],i.indexOf(l)>=0&&delete this.queue[r];return e?requestAnimationFrame(function(t){return s.main(t)}):this.running=!1},add:function(t,e){var i=this;if(this.queue[t]||(this.queue[t]=e),!this.running)return this.running=!0,requestAnimationFrame(function(t){return i.main(t)})}},window.ldBar=i=function(i,l){var n,h,d,o,u,f,c,g,p,w,m,k,b,x,v,y,M,q,A,B,C,S,_,L,N=this;if(null==l&&(l={}),n={xlink:"http://www.w3.org/1999/xlink"},(h="String"===s.call(i).slice(8,-1)?document.querySelector(i):i).ldBar)return h.ldBar;h.ldBar=this,~(d=h.getAttribute("class")||"").indexOf("ldBar")||h.setAttribute("class",d+" ldBar"),o="ldBar-"+Math.random().toString(16).substring(2),u={key:o,clip:o+"-clip",filter:o+"-filter",pattern:o+"-pattern",mask:o+"-mask",maskPath:o+"-mask-path"},f=function(t,e){var i,r;t=c(t);for(i in e)r=e[i],"attr"!==i&&t.appendChild(f(i,r||{}));return t.attrs(e.attr||{}),t},c=function(t){return document.createElementNS("http://www.w3.org/2000/svg",t)},(g=document.body.__proto__.__proto__.__proto__).text=function(t){return this.appendChild(document.createTextNode(t))},g.attrs=function(t){var e,i,r,a=[];for(e in t)i=t[e],(r=/([^:]+):([^:]+)/.exec(e))&&n[r[1]]?a.push(this.setAttributeNS(n[r[1]],e,i)):a.push(this.setAttribute(e,i));return a},g.styles=function(t){var e,i,r=[];for(e in t)i=t[e],r.push(this.style[e]=i);return r},g.append=function(t){return this.appendChild(document.createElementNS("http://www.w3.og/2000/svg",t))},g.attr=function(t,e){return null!=e?this.setAttribute(t,e):this.getAttribute(t)},(p={type:"stroke",img:"",path:"M10 10L90 10","fill-dir":"btt",fill:"#25b","fill-background":"#ddd","fill-background-extrude":3,"pattern-size":null,"stroke-dir":"normal",stroke:"#25b","stroke-width":"3","stroke-trail":"#ddd","stroke-trail-width":.5,duration:1,easing:"linear",value:0,"img-size":null,bbox:null,"set-dim":!0,"aspect-ratio":"xMidYMid"}).preset=h.attr("data-preset")||l.preset,null!=p.preset&&r(p,a[p.preset]);for(w in p)(m=m=h.attr("data-"+w))&&(p[w]=m);return r(p,l),p.img&&(p.path=null),k="stroke"===p.type,b=function(e){var i,r;return i=/data:ldbar\/res,([^()]+)\(([^)]+)\)/,(r=i.exec(e))?r=t[r[1]].apply(t,r[2].split(",")):e},p.fill=b(p.fill),p.stroke=b(p.stroke),"false"===p["set-dim"]&&(p["set-dim"]=!1),x={attr:{"xmlns:xlink":"http://www.w3.org/1999/xlink",preserveAspectRatio:p["aspect-ratio"],width:"100%",height:"100%"},defs:{filter:{attr:{id:u.filter,x:-1,y:-1,width:3,height:3},feMorphology:{attr:{operator:+p["fill-background-extrude"]>=0?"dilate":"erode",radius:Math.abs(+p["fill-background-extrude"])}},feColorMatrix:{attr:{values:"0 0 0 0 1    0 0 0 0 1    0 0 0 0 1    0 0 0 1 0",result:"cm"}}},mask:{attr:{id:u.mask},image:{attr:{"xlink:href":p.img,filter:"url(#"+u.filter+")",x:0,y:0,width:100,height:100,preserveAspectRatio:p["aspect-ratio"]}}},g:{mask:{attr:{id:u.maskPath},path:{attr:{d:p.path||"",fill:"#fff",stroke:"#fff",filter:"url(#"+u.filter+")"}}}},clipPath:{attr:{id:u.clip},rect:{attr:{class:"mask",fill:"#000"}}},pattern:{attr:{id:u.pattern,patternUnits:"userSpaceOnUse",x:0,y:0,width:300,height:300},image:{attr:{x:0,y:0,width:300,height:300}}}}},v=f("svg",x),(y=document.createElement("div")).setAttribute("class","ldBar-label"),h.appendChild(v),h.appendChild(y),M=[0,0],q=0,this.fit=function(){var t,e,i,r;if((t=p.bbox)?(e=t.split(" ").map(function(t){return+t.trim()}),e={x:e[0],y:e[1],width:e[2],height:e[3]}):e=M[1].getBBox(),e&&0!==e.width&&0!==e.height||(e={x:0,y:0,width:100,height:100}),i=1.5*Math.max.apply(null,["stroke-width","stroke-trail-width","fill-background-extrude"].map(function(t){return p[t]})),v.attrs({viewBox:[e.x-i,e.y-i,e.width+2*i,e.height+2*i].join(" ")}),p["set-dim"]&&["width","height"].map(function(t){if(!h.style[t]||N.fit[t])return h.style[t]=e[t]+2*i+"px",N.fit[t]=!0}),r=M[0].querySelector("rect"))return r.attrs({x:e.x-i,y:e.y-i,width:e.width+2*i,height:e.height+2*i})},p.path?(M[0]=k?f("g",{path:{attr:{d:p.path,fill:"none",class:"baseline"}}}):f("g",{rect:{attr:{x:0,y:0,width:100,height:100,mask:"url(#"+u.maskPath+")",fill:p["fill-background"],class:"frame"}}}),v.appendChild(M[0]),M[1]=f("g",{path:{attr:{d:p.path,class:k?"mainline":"solid","clip-path":"fill"===p.type?"url(#"+u.clip+")":""}}}),v.appendChild(M[1]),A=M[0].querySelector(k?"path":"rect"),B=M[1].querySelector("path"),k&&B.attrs({fill:"none"}),C=v.querySelector("pattern image"),(S=new Image).addEventListener("load",function(){var t,e;return t=(e=p["pattern-size"])?{width:+e,height:+e}:S.width&&S.height?{width:S.width,height:S.height}:{width:300,height:300},v.querySelector("pattern").attrs({width:t.width,height:t.height}),C.attrs({width:t.width,height:t.height})}),/.+\..+|^data:/.exec(k?p.stroke:p.fill)&&(S.src=k?p.stroke:p.fill,C.attrs({"xlink:href":S.src})),k&&(A.attrs({stroke:p["stroke-trail"],"stroke-width":p["stroke-trail-width"]}),B.attrs({"stroke-width":p["stroke-width"],stroke:/.+\..+|^data:/.exec(p.stroke)?"url(#"+u.pattern+")":p.stroke})),p.fill&&!k&&B.attrs({fill:/.+\..+|^data:/.exec(p.fill)?"url(#"+u.pattern+")":p.fill}),q=B.getTotalLength(),this.fit(),this.inited=!0):p.img&&(L=p["img-size"]?{width:+(_=p["img-size"].split(","))[0],height:+_[1]}:{width:100,height:100},M[0]=f("g",{rect:{attr:{x:0,y:0,width:100,height:100,mask:"url(#"+u.mask+")",fill:p["fill-background"]}}}),v.querySelector("mask image").attrs({width:L.width,height:L.height}),M[1]=f("g",{image:{attr:{width:L.width,height:L.height,x:0,y:0,preserveAspectRatio:p["aspect-ratio"],"clip-path":"fill"===p.type?"url(#"+u.clip+")":"","xlink:href":p.img,class:"solid"}}}),(S=new Image).addEventListener("load",function(){var t,e;return e=p["img-size"]?{width:+(t=p["img-size"].split(","))[0],height:+t[1]}:S.width&&S.height?{width:S.width,height:S.height}:{width:100,height:100},v.querySelector("mask image").attrs({width:e.width,height:e.height}),M[1].querySelector("image").attrs({width:e.width,height:e.height}),N.fit(),N.set(void 0,!1),N.inited=!0}),S.src=p.img,v.appendChild(M[0]),v.appendChild(M[1])),v.attrs({width:"100%",height:"100%"}),this.transition={value:{src:0,des:0},time:{},ease:function(t,e,i,r){return(t/=.5*r)<1?.5*i*t*t+e:(t-=1,.5*-i*(t*(t-2)-1)+e)},handler:function(t,e){var i,r,a,l,n,s,h,d,o;return null==e&&(e=!0),null==this.time.src&&(this.time.src=t),i=[this.value.des-this.value.src,.001*(t-this.time.src),+p.duration||1],r=i[0],a=i[1],l=i[2],y.textContent=n=e?Math.round(this.ease(a,this.value.src,r,l)):this.value.des,k?(s=B,h={"stroke-dasharray":"reverse"===p["stroke-dir"]?"0 "+q*(100-n)*.01+" "+q*n*.01+" 0":.01*n*q+" "+(.01*(100-n)*q+1)}):(d=M[1].getBBox(),h="btt"!==(o=p["fill-dir"])&&o?"ttb"===o?{y:d.y,height:d.height*n*.01,x:d.x,width:d.width}:"ltr"===o?{y:d.y,height:d.height,x:d.x,width:d.width*n*.01}:"rtl"===o?{y:d.y,height:d.height,x:d.x+d.width*(100-n)*.01,width:d.width*n*.01}:void 0:{y:d.y+d.height*(100-n)*.01,height:d.height*n*.01,x:d.x,width:d.width},s=v.querySelector("rect")),s.attrs(h),!(a>=l)||(delete this.time.src,!1)},start:function(t,i,r){var a,l=this;return a=this.value,a.src=t,a.des=i,h.offsetWidth||h.offsetHeight||h.getClientRects().length,r&&(h.offsetWidth||h.offsetHeight||h.getClientRects().length)?e.add(u.key,function(t){return l.handler(t)}):(this.time.src=0,void this.handler(1e3,!1))}},this.set=function(t,e){var i,r;return null==e&&(e=!0),i=this.value||0,null!=t?this.value=t:t=this.value,r=this.value,this.transition.start(i,r,e)},this.set(+p.value||0,!1),this},window.addEventListener("load",function(){var t,e,r,a,l=[];for(t=0,r=(e=document.querySelectorAll(".ldBar")).length;t<r;++t)(a=e[t]).ldBar||l.push(a.ldBar=new i(a));return l},!1)}()},{presets:2}],2:[function(t,e,i){(void 0!==i&&i||this).presets={rainbow:{type:"stroke",path:"M10 10L90 10",stroke:"data:ldbar/res,gradient(0,1,#a551df,#fd51ad,#ff7f82,#ffb874,#ffeb90)",bbox:"10 10 80 10"},energy:{type:"fill",path:"M15 5L85 5A5 5 0 0 1 85 15L15 15A5 5 0 0 1 15 5",stroke:"#f00",fill:"data:ldbar/res,gradient(45,2,#4e9,#8fb,#4e9)","fill-dir":"ltr","fill-background":"#444","fill-background-extrude":1,bbox:"10 5 80 10"},stripe:{type:"fill",path:"M15 5L85 5A5 5 0 0 1 85 15L15 15A5 5 0 0 1 15 5",stroke:"#f00",fill:"data:ldbar/res,stripe(#25b,#58e,1)","fill-dir":"ltr","fill-background":"#ddd","fill-background-extrude":1,bbox:"10 5 80 10"},text:{type:"fill",img:'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="70" height="20" viewBox="0 0 70 20"><text x="35" y="10" text-anchor="middle" dominant-baseline="central" font-family="arial">LOADING</text></svg>',"fill-background-extrude":1.3,"pattern-size":100,"fill-dir":"ltr","img-size":"70,20",bbox:"0 0 70 20"},line:{type:"stroke",path:"M10 10L90 10",stroke:"#25b","stroke-width":3,"stroke-trail":"#ddd","stroke-trail-width":1,bbox:"10 10 80 10"},fan:{type:"stroke",path:"M10 90A40 40 0 0 1 90 90","fill-dir":"btt",fill:"#25b","fill-background":"#ddd","fill-background-extrude":3,"stroke-dir":"normal",stroke:"#25b","stroke-width":"3","stroke-trail":"#ddd","stroke-trail-width":.5,bbox:"10 50 80 40"},circle:{type:"stroke",path:"M50 10A40 40 0 0 1 50 90A40 40 0 0 1 50 10","fill-dir":"btt",fill:"#25b","fill-background":"#ddd","fill-background-extrude":3,"stroke-dir":"normal",stroke:"#25b","stroke-width":"3","stroke-trail":"#ddd","stroke-trail-width":.5,bbox:"10 10 80 80"},bubble:{type:"fill",path:"M50 10A40 40 0 0 1 50 90A40 40 0 0 1 50 10","fill-dir":"btt",fill:"data:ldbar/res,bubble(#39d,#cef)","pattern-size":"150","fill-background":"#ddd","fill-background-extrude":2,"stroke-dir":"normal",stroke:"#25b","stroke-width":"3","stroke-trail":"#ddd","stroke-trail-width":.5,bbox:"10 10 80 80"}}},{}]},{},[1]);
