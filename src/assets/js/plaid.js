







var linkToken = "";
var plaidInnItUrl ='';
var plaidIssuingUrl='';
var userid = '';
var linkHandler;
var sToken;
plaidEnvironment='';
var showToastr=null;
var ticketNumberClickHandlerClass = null;
function accountSuccesfullyMsg(loadToaster,handlerClass){
    showToastr = loadToaster;
    ticketNumberClickHandlerClass = handlerClass;
}
function intiatePlaid(initUrl,IssuinngUrl,id,plaidEnv,stoken){
    plaidInnItUrl = initUrl;
    plaidIssuingUrl = IssuinngUrl;
    userid =id;
    sToken = stoken;
    plaidEnvironment= plaidEnv;
    $.ajax({
        url : plaidInnItUrl,
        type: 'GET',
        beforeSend: function(xhr){xhr.setRequestHeader('Authorization', 'Bearer'+' '+userid+':'+sToken)},
        success : function(data){
            if (data && data.link_token){
                linkToken = data.link_token;
                setUpPlaidLink();
                $("#error_message").text('');
                linkHandler.open();
            }		
        }
    });
}


function setUpPlaidLink(){
    linkHandler = Plaid.create({
    env: plaidEnvironment,
    clientName: 'Routespring',
    token: linkToken,
    product: ['transactions'],
    selectAccount: true,
    onSuccess: function(public_token, metadata) {
      // Send the public_token and account ID to your app server.
      
      
      $.ajax({
          url : plaidIssuingUrl + "?publicToken=" + public_token 
                  + "&user_email=" + encodeURIComponent(userid)
                  ,
          dataType : 'JSON',
          type : 'POST',
          contentType: 'application/json',
          beforeSend: function(xhr){xhr.setRequestHeader('Authorization', 'Bearer'+' '+userid+':'+sToken)},
          data : JSON.stringify(metadata),
          success : function(data) {
              if (data && data.status == 'error' && data.errorMessage && data.errorMessage.length > 0){
                  $("#error_message").text(data.errorMessage[0]);
                  showToastr(false,ticketNumberClickHandlerClass,data.errorMessage[0]);
              }else{
                  $("#error_message").text("Account linked succesfully");
                  showToastr(true,ticketNumberClickHandlerClass,'');
              }
              
          }
      });
    },
    onExit: function(err, metadata) {
      // The user exited the Link flow.
      showToastr(false,ticketNumberClickHandlerClass,'');
      if (err != null) {
        // The user encountered a Plaid API error prior to exiting.
      }
    },
    onEvent: function(eventName, metadata) {
        // The user exited the Link flow.
        
        

      }
  });
  }