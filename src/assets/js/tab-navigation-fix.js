/**
 * Tab Navigation Fix for Production Builds
 * 
 * This script ensures that tab navigation works correctly in production builds
 * where script loading order and event handler conflicts can interfere with
 * normal keyboard navigation.
 */

(function() {
    'use strict';
    
    var isInitialized = false;
    var originalPreventDefault = Event.prototype.preventDefault;
    
    function initTabNavigationFix() {
        if (isInitialized) {
            return;
        }
        
        console.log('Initializing tab navigation fix...');
        
        // Override preventDefault to allow tab navigation
        Event.prototype.preventDefault = function() {
            // Allow tab navigation to proceed even if other handlers try to prevent it
            if (this.type === 'keydown' && this.key === 'Tab') {
                console.log('Tab key detected, allowing navigation...');
                // Don't prevent default for tab key unless absolutely necessary
                return;
            }
            
            // Call original preventDefault for other events
            return originalPreventDefault.call(this);
        };
        
        // Ensure all focusable elements are properly configured
        ensureFocusableElements();
        
        // Set up a global tab handler as a fallback
        setupGlobalTabHandler();
        
        // Watch for dynamically added elements
        setupMutationObserver();
        
        isInitialized = true;
        console.log('Tab navigation fix initialized successfully');
    }
    
    function ensureFocusableElements() {
        var focusableSelectors = [
            'input:not([disabled]):not([tabindex="-1"])',
            'button:not([disabled]):not([tabindex="-1"])',
            'select:not([disabled]):not([tabindex="-1"])',
            'textarea:not([disabled]):not([tabindex="-1"])',
            'a[href]:not([tabindex="-1"])',
            '[tabindex]:not([tabindex="-1"])',
            '[contenteditable]:not([tabindex="-1"])'
        ];
        
        var elements = document.querySelectorAll(focusableSelectors.join(','));
        
        for (var i = 0; i < elements.length; i++) {
            var element = elements[i];
            
            // Ensure element has proper tabindex
            if (!element.hasAttribute('tabindex') && shouldBeFocusable(element)) {
                element.setAttribute('tabindex', '0');
            }
        }
    }
    
    function shouldBeFocusable(element) {
        var tagName = element.tagName.toLowerCase();
        var focusableTags = ['input', 'button', 'select', 'textarea', 'a'];
        
        if (focusableTags.indexOf(tagName) !== -1) {
            var style = window.getComputedStyle(element);
            return style.display !== 'none' && 
                   style.visibility !== 'hidden' && 
                   !element.hasAttribute('disabled');
        }
        
        return false;
    }
    
    function setupGlobalTabHandler() {
        document.addEventListener('keydown', function(event) {
            if (event.key !== 'Tab') {
                return;
            }
            
            // Ensure tab navigation works
            var focusableElements = getFocusableElements();
            
            if (focusableElements.length === 0) {
                return;
            }
            
            var currentIndex = -1;
            for (var i = 0; i < focusableElements.length; i++) {
                if (focusableElements[i] === document.activeElement) {
                    currentIndex = i;
                    break;
                }
            }
            
            if (currentIndex === -1) {
                // No element focused, focus the first one
                event.preventDefault();
                focusableElements[0].focus();
                return;
            }
            
            if (event.shiftKey) {
                // Shift+Tab - go to previous element
                if (currentIndex === 0) {
                    // Wrap to last element
                    event.preventDefault();
                    focusableElements[focusableElements.length - 1].focus();
                }
            } else {
                // Tab - go to next element
                if (currentIndex === focusableElements.length - 1) {
                    // Wrap to first element
                    event.preventDefault();
                    focusableElements[0].focus();
                }
            }
        }, true); // Use capture phase to ensure we get the event first
    }
    
    function getFocusableElements() {
        var focusableSelectors = [
            'input:not([disabled]):not([tabindex="-1"])',
            'button:not([disabled]):not([tabindex="-1"])',
            'select:not([disabled]):not([tabindex="-1"])',
            'textarea:not([disabled]):not([tabindex="-1"])',
            'a[href]:not([tabindex="-1"])',
            '[tabindex]:not([tabindex="-1"])',
            '[contenteditable]:not([tabindex="-1"])'
        ];
        
        var elements = Array.prototype.slice.call(
            document.querySelectorAll(focusableSelectors.join(','))
        );
        
        // Filter out hidden elements
        return elements.filter(function(element) {
            var style = window.getComputedStyle(element);
            return style.display !== 'none' &&
                   style.visibility !== 'hidden' &&
                   element.offsetParent !== null &&
                   !element.hasAttribute('disabled');
        });
    }
    
    function setupMutationObserver() {
        if (!window.MutationObserver) {
            return; // Not supported in older browsers
        }
        
        var observer = new MutationObserver(function(mutations) {
            var shouldUpdate = false;
            
            for (var i = 0; i < mutations.length; i++) {
                var mutation = mutations[i];
                if (mutation.type === 'childList') {
                    for (var j = 0; j < mutation.addedNodes.length; j++) {
                        var node = mutation.addedNodes[j];
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (containsFocusableElements(node)) {
                                shouldUpdate = true;
                                break;
                            }
                        }
                    }
                }
                if (shouldUpdate) break;
            }
            
            if (shouldUpdate) {
                setTimeout(function() {
                    ensureFocusableElements();
                }, 50);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    function containsFocusableElements(element) {
        var focusableSelectors = [
            'input', 'button', 'select', 'textarea', 'a[href]', '[tabindex]', '[contenteditable]'
        ];
        
        for (var i = 0; i < focusableSelectors.length; i++) {
            var selector = focusableSelectors[i];
            if (element.matches && element.matches(selector)) {
                return true;
            }
            if (element.querySelector && element.querySelector(selector)) {
                return true;
            }
        }
        
        return false;
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initTabNavigationFix, 100);
        });
    } else {
        setTimeout(initTabNavigationFix, 100);
    }
    
    // Also initialize after a delay to catch any late-loading scripts
    setTimeout(function() {
        if (!isInitialized) {
            initTabNavigationFix();
        }
    }, 1000);
    
    // Expose a global function to manually reinitialize if needed
    window.reinitializeTabNavigation = function() {
        isInitialized = false;
        initTabNavigationFix();
    };
    
})();
