var countryOptionsTemplate = `<option></option>`;
var selectedCountry = "";
var airlineOptionsTemplate = `<option></option>`;
var selectedAirline = "NONE";
var hotelOptionsTemplate = `<option></option>`;
var selectedHotel = "";
var personalDetailText = "Personal Information";
var notSubscribedText = `Subscribe and earn 5% cash back on every flight booking`;
var notSubscribeHeading = `You are not subscribed`;
var openSubscribeHeading = `Choose billing cycle and Pay`;
var subscribedHeading = `Premium Subscription`;
var isSubscribed = false;

var userPrimaryEmail = `<EMAIL>`;
var userSecondaryEmail = ``;

var airlineSelectedArray = [];
var hotelSelectedArray = [];

var airlineCount = 0;
var hotelCount = 0;
var secondaryEmailCount = 1;

$(document).ready(function () {

    checkAppleDevices();
    initilizeDatepicker();
    $('.googleAddress').bind('keydown', function (e) {
        if (e.keyCode == 13) {
            e.preventDefault();
        }
    });

    // $(".phoneNumber").intlTelInput();
    $('.paymentform-div').html(noSubscriptionTemplate());
})


function getSelectedAirlines() {
    var airlines = [];
    var template = ``;
    $('.airlineCheckboxContainer .mdl-checkbox__input:checked').each(function () {
        var value = $(this).attr('data-value');
        airlines.push(value);
    });
    template = airlines.join(" | ");
    return template;
}

function getSelectedHotels() {
    // var hotels = [];
    // var ratings = [];
    // var template = ``;
    // $('.hotelCheckboxContainer .mdl-checkbox__input:checked').each(function () {
    //     var value = $(this).attr('data-value');
    //     hotels.push(value);
    // });

    // $('.hotelRatingContainer .mdl-checkbox__input:checked').each(function () {
    //     var value = $(this).attr('data-value');
    //     ratings.push(value);
    // });

    // template = hotels.join(", ") + "  |   " + ratings.join(", ");
    // return template;
}


function linkFunction(thisObj) {
    var tabId = $(thisObj).attr('data-tab');
    $(`#pageTab .tab-list-item[data-tab="${tabId}"]`).click();
    $('.admin').removeClass('active');
    $('.admin-list').hide();
    $('.tab-list').scrollTo(0, 0);
    var scrollTab = $(`#pageTab .tab-list-item[data-tab="${tabId}"]`).position().left;
    $('.tab-list').scrollTo(scrollTab, 0);
}

function activeTab(thisObj) {
    if (!($(thisObj).hasClass('active'))) {
        var attr = $(thisObj).attr('data-tab');
        $('.tab-list-item').removeClass('active');
        $(thisObj).addClass('active');
        $('.tab-content-item').removeClass('active');
        $(`#${attr}`).addClass('active');
    }

}


// $.ajax({
//     url: "countryData.json",
//     type: 'POST',
//     dataType: 'json',

//     success: function (data) {
//         countryTemplate(data);
//     },
//     error: function (data) {

//     }
// })


function countryTemplate(countryArray) {
    var selectedText = ``;
    $.each(countryArray, function (key, value) {
        //        if (value.i == selectedCountry) {
        //            selectedText = `selected`;
        //        } else {
        //            selectedText = ``;
        //        }

        countryOptionsTemplate += `<option value="${value.i.toUpperCase()}" ${selectedText}>${value.n}</option>`;
    });

    $('.countryDropdown').html(countryOptionsTemplate);
    $('.countryDropdown').select2({
        allowClear: true,
        placeholder: "Select country"

    });
}




function selectChange(thisObj) {
    //    $(thisObj).valid();
    var value = $(thisObj).val();
    if (value != "") {
        $(thisObj).closest('.row').find('.input-textfield').removeAttr('disabled');
    } else {
        $(thisObj).closest('.row').find('.input-textfield').attr('disabled');
    }
}


function setFFNList(ffnMapping) {
    $.each(ffnMapping, function (item) {
        if (airlineCount == 0) {
            $('#airlineFlyerNumberDiv0').closest('select').val(item.airline_code);
            $('#airlineFlyerNumberDiv0').closest('flyerNumber').val(item.frequent_flyer_number);
        } else {
            $('#airlineFlyerNumberDiv' + (airlineCount - 1)).closest('select').val(item.airline_code);
            $('#airlineFlyerNumberDiv0').closest('flyerNumber').val(item.frequent_flyer_number);
            $('#airlineFlyerNumberDiv0').find('.removeFlyerNumberDiv').removeClass('hide');
        }
        $('#airlineFlyerNumberDiv0').after(selectAirlineTemplate(airlineCount, item.frequent_flyer_number));
        airlineCount++;
    });
}

function selectAirlineChange(thisObj) {
    var c = $(thisObj).closest('.card-div').find('.airlineFlyerNumberDiv').length;
    var index = $(thisObj).closest('.airlineFlyerNumberDiv').index();
    var value = $(thisObj).val();
    if (value != "") {
        $(thisObj).closest('.airlineFlyerNumberDiv').find('.input-textfield').removeAttr('disabled');
    } else {
        $(thisObj).closest('.airlineFlyerNumberDiv').find('.input-textfield').attr('disabled');
    }


    if (((index + 1) == c) && $(window).width() >= 768) {
        $(thisObj).closest('.airlineFlyerNumberDiv').find('.removeFlyerNumberDiv').removeClass('hide');
        $(thisObj).closest('.airlineFlyerNumberDiv').after(selectAirlineTemplate(airlineCount, ""));

        $(thisObj).closest('.card-div').find(`#airlineOptions${airlineCount}`).html(airlineOptionsTemplate);
        $(thisObj).closest('.card-div').find(`#airlineOptions${airlineCount}`).select2({
            placeholder: "Search airline"
        });

        if ($(thisObj).closest('form').attr('id') == 'frequentFlyerForm') {
            $(thisObj).closest('.card-div').find('#frequentFlyerForm').validate();
            $(thisObj).closest('.card-div').find('#flyerNumber' + airlineCount).rules("add", "required");
        }
        airlineCount++;
    } else {
        $(thisObj).closest('.card-div').find('.airlineDisabledButton').removeClass('disabled');
    }
}



function selectHotelChange(thisObj) {
    $(thisObj).valid();
    var c = $(thisObj).closest('.card-div').find('.hotelFlyerNumberDiv').length;
    var index = $(thisObj).closest('.hotelFlyerNumberDiv').index();
    var value = $(thisObj).val();
    if (value != "") {
        $(thisObj).closest('.hotelFlyerNumberDiv').find('.input-textfield').removeAttr('disabled');
    } else {
        $(thisObj).closest('.hotelFlyerNumberDiv').find('.input-textfield').attr('disabled');
    }
    if (((index + 1) == c) && $(window).width() >= 768) {
        $(thisObj).closest('.hotelFlyerNumberDiv').find('.removeFlyerNumberDiv').removeClass('hide');
        $(thisObj).closest('.hotelFlyerNumberDiv').after(selectHotelTemplate(hotelCount));
        $(thisObj).closest('.card-div').find(`#hotelOptions${hotelCount}`).html(hotelOptionsTemplate);
        $(thisObj).closest('.card-div').find(`#hotelOptions${hotelCount}`).select2({
            placeholder: "Search hotel"
        });

        if ($(thisObj).closest('form').attr('id') == 'frequentGuestForm') {
            $(thisObj).closest('.card-div').find('#frequentGuestForm').validate();
            $(thisObj).closest('.card-div').find(`#guestNumber${hotelCount}`).rules("add", "required");
        }
        hotelCount++;
    } else {
        $(thisObj).closest('.card-div').find('.hotelDisabledButton').removeClass('disabled');
    }
}


function addOtherFFN(thisObj) {
    if ($(thisObj).closest('form').attr('id') == 'frequentFlyerForm') {
        if (validateFrequentFlyerForm(thisObj)) {
            var flyerObj = {
                airline: $(`#airlineOptions option:selected`).text(),
                flyerNumber: $(`#flyerNumber`).val()
            }
            $(thisObj).closest('.card-div').find('.adderFlyers').append(flyerTemplate(flyerObj));
            $(thisObj).closest('.card-div').find(`#airlineOptions`).val('');
            $(thisObj).closest('.card-div').find(`#flyerNumber`).val('').attr('disabled', 'disabled');
            $(thisObj).closest('.card-div').find(`#airlineOptions`).select2({
                placeholder: "Search airline"
            });
            $(thisObj).closest('.card-div').find('.airlineDisabledButton').addClass('disabled');
        }
    }
    else {
        var flyerObj = {
            airline: $(`#airlineOptionsTraveller1 option:selected`).text(),
            flyerNumber: $(`#flyerNumberTraveller1`).val()
        }
        $(thisObj).closest('.card-div').find('.adderFlyers').append(flyerTemplate(flyerObj));
        $(thisObj).closest('.card-div').find(`#airlineOptionsTraveller1`).val('');
        $(thisObj).closest('.card-div').find(`#flyerNumberTraveller1`).val('').attr('disabled', 'disabled');
        $(thisObj).closest('.card-div').find(`#airlineOptionsTraveller1`).select2({
            placeholder: "Search airline"
        });
        $(thisObj).closest('.card-div').find('.airlineDisabledButton').addClass('disabled');
    }


}

function addOtherFGN(thisObj) {
    if ($(thisObj).closest('form').attr('id') == 'frequentGuestForm') {
        if (validateFrequentGuestForm(thisObj)) {
            var guestObj = {
                hotel: $(`#hotelOptions option:selected`).text(),
                guestNumber: $(`#guestNumber`).val()
            }
            $(thisObj).closest('.card-div').find('.adderGuest').append(guestTemplate(guestObj));
            $(thisObj).closest('.card-div').find(`#hotelOptions`).val('');
            $(thisObj).closest('.card-div').find(`#guestNumber`).val('').attr('disabled', 'disabled');
            $(thisObj).closest('.card-div').find(`#hotelOptions`).select2({
                placeholder: "Search hotel"
            });
            $(thisObj).closest('.card-div').find('.hotelDisabledButton').addClass('disabled');
        }
    }

    else {
        var guestObj = {
            hotel: $(`#hotelOptionsTraveller1 option:selected`).text(),
            guestNumber: $(`#guestNumberTraveller1`).val()
        }
        $(thisObj).closest('.card-div').find('.adderGuest').append(guestTemplate(guestObj));
        $(thisObj).closest('.card-div').find(`#hotelOptionsTraveller1`).val('');
        $(thisObj).closest('.card-div').find(`#guestNumberTraveller1`).val('').attr('disabled', 'disabled');
        $(thisObj).closest('.card-div').find(`#hotelOptionsTraveller1`).select2({
            placeholder: "Search hotel"
        });
        $(thisObj).closest('.card-div').find('.hotelDisabledButton').addClass('disabled');
    }
}


function removeAirlineDiv(thisObj) {
    $(thisObj).closest('.airlineFlyerNumberDiv').remove();
}

function removeHotelDiv(thisObj) {
    $(thisObj).closest('.hotelFlyerNumberDiv').remove();
}



// function initilizeDatepicker($this) {
//     $('.dob').datepicker({
//         changeMonth: true,
//         changeYear: true,
//         minDate: "-200Y",
//         maxDate: "today",
//         shortYearCutoff: 1,
//         yearRange: "1800:3000",
//         onSelect: function (event, ui) {
//             $(this).closest('.input-box').find('span.error').hide();
//         }
//     });

//     $('.passportDate').datepicker({
//         changeMonth: true,
//         changeYear: true,
//         minDate: "today",
//         maxDate: "+20Y",
//         shortYearCutoff: 1,
//         yearRange: "1800:3000",
//         onSelect: function (event, ui) {
//             $(this).closest('.input-box').find('span.error').hide();
//         }
//     });
// }

function goToNextStep(thisObj) {
    $(thisObj).closest('.card-div').removeClass('shadow').addClass('filled').removeClass('active').find('.card-div-body').slideUp();
    $(thisObj).closest('.card-div').nextAll('.card-div').each(function () {
        if (!($(this).hasClass('filled'))) {
            $(this).addClass('shadow active').find('.card-div-body').addClass('active').slideDown();
            return false;
        }
    });
}

function getShortInfo(thisObj) {
    $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate(thisObj));
}

function shortInfoTemplate(thisObj) {
    var template = ``;
    $(thisObj).closest('form').find('.shortInfoField').each(function () {
        var value = $(this).val();
        template += `${value} |`;
    });
    return template;
}

function openModal() {
    setTimeout(() => {
        $('#helpPoupupWrapper').show();
        $('.modalAirportFilterInfo').slideToggle();
    }, 10);
  
}
function toggleAccordion(list, item, index) {
    if (!list) {
        for (let i = 0; i < item; i++) {
            if (i !== index) {
                $("#passenger" + i + "accordion > div >  div.panel-collapse ").css("display", "none")
            }
        }

        $("#passenger" + index + "accordion > div >  div.panel-collapse ").css("display", "block")
    }

}
function toggleAccordionClose(list, item, index) {
    if (list) {
        for (let i = 0; i < item; i++) {
            if (i === index) {
            $("#passenger" + i + "accordion > div > div > div > div.accordion-toggle").attr("aria-expanded", "false");
            $("#passenger" + i + "accordion > div >  div.panel-collapse ").attr("aria-expanded", "false")
            $("#passenger" + i + "accordion > div >  div.panel-collapse ").attr("aria-expanded", "true")
            $("#passenger" + i + "accordion > div >  div.panel-collapse ").css("display", "none")
            }
        }
    }

}
function openCardEdit(thisObj) {
    if (!($(thisObj).closest('.card-div').hasClass('active'))) {
        $(thisObj).closest('.card-div').prevAll('.card-div.active').each(function () {
            // $(this).find('.saveButton').click();
        });
        $(thisObj).closest('.card-div').addClass('active').addClass('shadow').find('.card-div-body').slideDown();
        //        $(thisObj).closest('.card-div').find('.traveller-short-info').hide();
        if ($(thisObj).closest('form').hasClass("personalDetailsForm")) {
            $(thisObj).closest('form').find('.card-div-header h3').html(personalDetailText);
        }
        if ($(thisObj).closest('.card-div').attr('id') == "subscribeCard") {
            $('#subscribeCard .card-div-header h3').html(openSubscribeHeading);
        }
    }

    else {
        $(thisObj).closest('.card-div').removeClass('active').removeClass('shadow').find('.card-div-body').slideUp();
        if (!isSubscribed) {
            $('#subscribeCard .card-div-header h3').html(notSubscribeHeading);
            $('#subscribeCard .traveller-short-info').show().find('.traveller-short-info-inner').html(notSubscribedText);
        }
    }

    // if($(thisObj).closest('.card-div').hasClass('paymentMethodCardDiv')){
    //     getOverlay();
    //     $('body').addClass('overflow-hidden');
    // }
}
var hideLine = false;
var hideLine1 = false;
function hideBorderLine() {
    return hideLine;
}
function hideArrow() {
    return hideLine1;
}
function openCardEdit1(thisObj) {
    if (!($(thisObj).closest('.card-div11').hasClass('active'))) {
        $(thisObj).closest('.card-div11').addClass('active').find('.card-div-body').slideDown();
        hideLine = true;
        return true;
    }

    else {
        $(thisObj).closest('.card-div11').removeClass('active').find('.card-div-body').slideUp();
        hideLine = false;
        return false;
    }

}
function openUniqueBox(thisObj) {
    if (!($(thisObj).closest('.uniquetravelerbox').hasClass('active'))) {
        var obj1 = document.getElementById("travelerDetails");
        $(thisObj).closest('.uniquetravelerbox').addClass('active');
        $(thisObj.parentNode).addClass('active');
        obj1.scrollTo(0, document.body.scrollHeight);
    } else {
        $(thisObj).closest('.uniquetravelerbox').removeClass('active');
        $(thisObj.parentNode).removeClass('active');
        var obj1 = document.getElementById("travelerDetails");
        obj1.scrollTo(0, 0);
    }
}

function openCardEdit12(thisObj) {
    if (!($(thisObj).closest('.middle-panel').hasClass('active'))) {
        $(thisObj).closest('.middle-panel').find('.navigation').addClass('active');
        $(thisObj).closest('.middle-panel').find('.outer-panel').addClass('active');
        $(thisObj).closest('.middle-panel').find('.outer-panel1').addClass('active');
        $(thisObj).closest('.middle-panel').find('.friends').addClass('active');
        $(thisObj).closest('.middle-panel').find('.people').addClass('active');
        $(thisObj).closest('.middle-panel').find('.credit').addClass('active');
        $(thisObj).closest('.middle-panel').find('.policy').addClass('active');
        $(thisObj).closest('.middle-panel').find('.check').addClass('active');
        $(thisObj).closest('.middle-panel').find('.check1').addClass('active');
        $(thisObj).closest('.middle-panel').find('.drop-arrow').addClass('active');
        $(thisObj).closest('.middle-panel').find('.arrow1').addClass('active');
        $(thisObj).closest('.middle-panel').find('.middle-panel1').addClass('active');
        $(thisObj).closest('.middle-panel').find('.middle-panel11').addClass('active');
        $(thisObj).closest('.middle-panel').find('.traveler').addClass('active');
        $(thisObj).closest('.middle-panel').find('.middle-box').addClass('active');
        $(thisObj).closest('.middle-panel').find('.inner-box').addClass('active');
        $(thisObj).closest('.middle-panel').addClass('active');
        hideLine1 = true;
    }

    else {
        $(thisObj).closest('.middle-panel').find('.navigation').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.outer-panel').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.friends').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.outer-panel1').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.people').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.credit').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.policy').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.middle-panel1').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.policy1').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.check').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.drop-arrow').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.check1').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.middle-panel11').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.arrow1').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.traveler').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.middle-box').removeClass('active');
        $(thisObj).closest('.middle-panel').find('.inner-box').removeClass('active');
        $(thisObj).closest('.middle-panel').removeClass('active');
        hideLine1 = false;
    }

}
function openCardEdit123(thisObj) {
    if (($(thisObj).closest('.middle-box').hasClass('active'))) {
        $(thisObj).closest('.middle-box').find('.check').addClass('active');
        $(thisObj).closest('.middle-box').find('.check1').addClass('active');
        $(thisObj).closest('.middle-box').find('.arrow1').addClass('active');
        $(thisObj).closest('.middle-panel').find('.outer-panel111').addClass('active');
        $(thisObj).closest('.middle-panel').find('.outer-panel').addClass('active');
    }

}
function convertDate(date) {
    var dateData = new Date(date);
    var dateValue = moment(dateData).format('MMM DD,YYYY');
    return dateValue;
}

function initGoogleMapOnElement() {
    $('.googleAddress').each(function () {
        var id = $(this).attr('id');
        initilizeGoogleAddressTextbox(id);
    });
}
function initilizeGoogleAddressTextbox(id) {
    var address = (document.getElementById(id));
    var options = {
        types: ['geocode']
    };
    var autocomplete = new google.maps.places.Autocomplete(address, options);
    google.maps.event.addListener(autocomplete, 'place_changed', function () {
        if (id == 'mapAddress') {
            var place = autocomplete.getPlace();
            $('.map').css('height', '196px');
            initMap(place.geometry.location.lat(), place.geometry.location.lng());
            getHomeAirport();
        }
    });
}

function getHomeAirport() {
    var addressInput = $('#mapAddress').val();
    if (addressInput && addressInput.trim().length > 0 && addressInput !== undefined && addressInput !== 'undefined'){
        showLoader('#airportSelectionForm');
        $.ajax({
            url: getApiBaseUrl('nearby_airportserach_api') + `/api/nearbysearch/airports?address=${$('#mapAddress').val()}`,
            type: 'POST',
            dataType: 'json',
            success: function (data) {
                if (data.status == "success") {
                    var homeAirports = JSON.parse(data.data);
                    $('.preffered-home-airport').removeClass('hidden');
                    $('.homeAirportContainer').html(homeAirportTemplate(homeAirports));
                    componentHandler.upgradeDom();
                } else {
                    $('#mapAddress').val('');
                    $('#map').css('height', 'auto').html('');
                    $('.preffered-home-airport').addClass('hidden');
                    $('.homeAirportContainer').html('');
                }
                removeLoader('#airportSelectionForm');
            },
            error: function (data) {
                removeLoader('#airportSelectionForm');
            }
        });
    }
}
var selectedHomeAirport = [];
function setSelectedHomeAirport(hAirport, hAddress) {
    selectedHomeAirport = [];
    if (hAirport && hAirport !== null && hAirport !== '' && hAirport.trim().length > 0) {
        selectedHomeAirport.push(hAirport);
    }
    if (hAddress && hAddress.trim().length > 0) {
        $('#mapAddress').val(hAddress);
        getHomeAirport();
        var geocoder = new google.maps.Geocoder();

        geocoder.geocode({ 'address': hAddress }, function (results, status) {
            if (status === 'OK') {
                $('.map').css('height', '196px');
                initMap(results[0].geometry.location.lat(), results[0].geometry.location.lng());
            }
        });
    }
}
function getSelectedHomeAirport() {
    return selectedHomeAirport;
}
function homeAirportTemplate(data) {
    var template = ``;
    var addSelectedAirport = 1;
    var selectedElementFoundInData = false;
    var selectedHomeAirportCode = selectedHomeAirport[0];
    for (var index = 0; index < data.length; index++) {
        var value = data[index];
        if (data[index].code === selectedHomeAirportCode) {
            template += `<label class="mdl-radio mdl-js-radio selection-checkbox checkbox2 mdl-js-ripple-effect is-checked" for="${value.code}">
        <input value="${value.code}" type="radio" id="${value.code}" name="preferredAirport" class="mdl-radio__button" onclick="setSelectedHomeAirport('${value.code}')" checked>
        <span class="mdl-radio__label">${value.code}</span>
        </label>`;
            selectedElementFoundInData = true;
            
        } else {
            template += `<label class="mdl-radio mdl-js-radio selection-checkbox checkbox2 mdl-js-ripple-effect" for="${value.code}">
        <input value="${value.code}" type="radio" id="${value.code}" name="preferredAirport" class="mdl-radio__button" onclick="setSelectedHomeAirport('${value.code}')">
        <span class="mdl-radio__label">${value.code}</span>
        </label>`;
        }
    }
    if (!selectedElementFoundInData && selectedHomeAirportCode && selectedHomeAirportCode.trim().length > 0) {
        template += `<label class="mdl-radio mdl-js-radio selection-checkbox checkbox2 mdl-js-ripple-effect is-checked" for="${selectedHomeAirportCode}">
    <input value="${selectedHomeAirportCode}" type="radio" id="${selectedHomeAirportCode}" name="preferredAirport" class="mdl-radio__button" onclick="setSelectedHomeAirport('${selectedHomeAirportCode}')" checked>
    <span class="mdl-radio__label">${selectedHomeAirportCode}</span>
    </label>`;
    }
    return template;
}

function initMap(lat, lng) {
    var cordnates = { lat: lat, lng: lng };
    let divElement = document.getElementById('map');
    if(divElement){
    var map = new google.maps.Map(
        document.getElementById('map'), {
            zoom: 10,
        zoomControl: false,
        mapTypeControl: false,
        navigationControl: false,
        keyboardShortcuts: false,
        scaleControl: false,
        streetViewControl: false,
        rotateControl: false,
        fullscreenControl: false,
        scrollwheel: false,
        draggable: false,
        center: cordnates
    });
    var marker = new google.maps.Marker({ position: cordnates, map: map });
}
}

var predefinedAirlines = [];
var otherAirlines = [];
var totalAirlines = [];
// $(function () {
//     totalAirlines = jQuery.extend(true, [], airlineList);
//     otherAirlines = airlineList;
//     $('.airlineCheckboxContainer').html(predefinedAirlinesTemplate(predefinedAirlines));
//     componentHandler.upgradeDom();
//     $("#airlineSearch").autocomplete({
//         source: totalAirlines,
//         autoFocus: true,
//         focus: function (event, ui) {
//             return false;
//         },
//         select: function (event, ui) {
//             var l = $('.airlineCheckboxContainer > div').length;
//             $('.airlineCheckboxContainer > div').eq(l - 2).after(chipTemplate(ui.item, 'search'));
// //                    $('.airlineCheckboxContainer').append(chipTemplate(ui.item, 'search'));
//             $.each(totalAirlines, function (keyInner, valueInner) {
//                 if (valueInner.value == ui.item.value) {
//                     totalAirlines.splice(keyInner, 1);
//                     return false;
//                 }
//             });
//             event.preventDefault();
//             $('#airlineSearch').val('');
// //                    $('#searchContainer').removeClass('active');
// //                    $("#airlinesBack").click();
//             componentHandler.upgradeDom();
//         }
//     }).focus(function () {
//         $(this).data("uiAutocomplete").search($(this).val());

//     });
// });
// $(function () {
//     $.ajax({
//         url: 'assets/images/airlines.json',
//         type: 'POST',
//         dataType: 'json',
//         success: function (data) {
//             predefinedAirlines = data.predefined;
//             otherAirlines = data.other;
//             totalAirlines = jQuery.extend(true, [], otherAirlines);

//             $('.airlineCheckboxContainer').html(predefinedAirlinesTemplate(predefinedAirlines));
//             componentHandler.upgradeDom();
//             $("#airlineSearch").autocomplete({
//                 source: totalAirlines,
//                 autoFocus: true,
//                 focus: function (event, ui) {
//                     return false;
//                 },
//                 select: function (event, ui) {
//                     var l = $('.airlineCheckboxContainer > div').length;
//                     $('.airlineCheckboxContainer > div').eq(l - 2).after(chipTemplate(ui.item, 'search'));
// //                    $('.airlineCheckboxContainer').append(chipTemplate(ui.item, 'search'));
//                     $.each(totalAirlines, function (keyInner, valueInner) {
//                         if (valueInner.value == ui.item.value) {
//                             totalAirlines.splice(keyInner, 1);
//                             return false;
//                         }
//                     });
//                     event.preventDefault();
//                     $('#airlineSearch').val('');
// //                    $('#searchContainer').removeClass('active');
// //                    $("#airlinesBack").click();
//                     componentHandler.upgradeDom();
//                 }
//             }).focus(function () {
//                 $(this).data("uiAutocomplete").search($(this).val());

//             });
//         },
//         error: function (data) {

//         }
//     });
// });

function predefinedAirlinesTemplate(array) {
    var template = '';

    $.each(array, function (key, value) {
        template += `${chipTemplate(value, 'predefined')}`;
    })
    return template;
}

function chipTemplate(obj, type) {
    var checked = "";
    var image = "";
    var anyClass = ""


    if (obj.value == "ANY" && type == 'predefined') {
        checked = "checked";
    } else
        if (obj.value != "ANY" && type == 'search') {
            checked = "checked";
            $('#ANY').removeAttr('checked');
            $('#ANY').closest('.mdl-checkbox').get(0).MaterialCheckbox.uncheck();

            //            $('#airlineWelcomeBox').html(userWelcomeTemplate(airlineText));
            //            airlineArray.push(obj.label);
            //            airlineArrayString = airlineArray.join(', ');
            //            $('.selectedAirlinesText').html(airlineArrayString);
            componentHandler.upgradeDom();

            //            $('#ANY').closest('.airline-div').hide();
        } else {
            checked = "";
        }

    if (obj.value == "ANY") {
        anyClass = "any";
        image = `<img class="selected" src="images/airlines/ANY.png" alt="${obj.value}" />
                <img class="notSelected" src="images/airlines/ANY_NS.png" alt="${obj.value}" />`;
    } else {
        anyClass = "";
        if (availableIcons[obj.value]) {
            image = `<img src="https://s3.amazonaws.com/airlines.images.biztravel.ai/icons/${obj.value}_128x128.png" alt="${obj.value}" />`;
        } else {
            image = `<img src="https://goprivate.wspan.com/sharedservices/images/airlineimages/logoAir${obj.value}.gif" alt="${obj.value}" />`;
        }

    }

    var template = `<div class="airline-div" data-type="${type}">
                        <label class="mdl-checkbox mdl-js-checkbox selection-checkbox mdl-js-ripple-effect ${anyClass}" for="${obj.value}">
                            <input onchange="getAirlineText(this);" value="${obj.value}" data-value="${obj.label}" type="checkbox" id="${obj.value}" name="prefferedAirlines[]" class="mdl-checkbox__input" ${checked}> 
                            <span class="mdl-checkbox__label">
                              <span class="mdl-checkbox__label-img">
                                ${image}
                              </span>
                                <span class="mdl-checkbox__label-text">${obj.label}</span>
                            </span>
                        </label>
                    </div>`;
    return template;
}

function onAirlineChangeJS() {
    componentHandler.upgradeDom();
}
function getAirlineText(thisObj) {
    if ($(thisObj).prop('checked') && $(thisObj).val() != "ANY") {
        $('#ANY').removeAttr('checked');
        $('#ANY').closest('.mdl-checkbox').get(0).MaterialCheckbox.uncheck();
        componentHandler.upgradeDom();
    } else {
        if (!($(thisObj).prop('checked')) && $(thisObj).val() != "ANY") {
            var checkedLength = $('.airlineCheckboxContainer').find('.mdl-checkbox__input:checked').not('#ANY').length;
            if (checkedLength <= 0) {
                $('#ANY').closest('.airline-div').show();
                $('#ANY').attr('checked');
                $('#ANY').closest('.mdl-checkbox').get(0).MaterialCheckbox.check();
            }
        } else {
            $(".airlineCheckboxContainer").find('.mdl-checkbox__input').not("#ANY").each(function () {
                $(this).removeAttr('checked');
                $(this).closest('.mdl-checkbox').get(0).MaterialCheckbox.uncheck();
            });
        }
        componentHandler.upgradeDom();
    }
}




function callHotelListingApi() {
    // $.ajax({
    //     url: 'hotel-chains.json',
    //     type: 'POST',
    //     dataType: 'json',
    //     success: function (data) {
    //         $('.hotelCheckboxContainer').html(predefinedHotelsTemplate(data.other));
    //         componentHandler.upgradeDom();
    //     },
    //     error: function (data) {

    //     }
    // });
}


function predefinedHotelsTemplate(array) {
    var template = '';

    $.each(array, function (key, value) {
        template += `${hotelTemplate(value, 'predefined')}`;
    })
    return template;
}



$(function () {
    callHotelListingApi();
});

function showPromoBox(thisObj) {
    getOverlay();
    $('#promoBox').show();
    $('body').addClass('overflow-hidden');
}

function removePromoBox(thisObj) {
    removeOverlay();
    $('#promoBox').hide();
    $('body').removeClass('overflow-hidden');
}
function applyPromoCode(thisObj) {
    removeOverlay();
    var promoValue = $('#promoCodeInput').val();
    if (promoValue != "") {
        removePromoBox();
        $('#promo').html(promoApliedHtml(promoValue));
        $('.old-price').addClass('strike');
        $('.new-price').removeClass('hidden');
        $('#promoSaving').html(promoSavingHtml());
        $('#promoCodeInput').val('');

    }
}

function removePromoCode() {
    $('#promo').html(promoHtml());
    $('.old-price').removeClass('strike');
    $('.new-price').addClass('hidden');
    $('#promoSaving').html('');
}

function addSecondaryEmail(thisObj) {
    $(thisObj).closest('form').find('.card-div-footer').show();
    $('.addSecondaryEmailLink').before(secondaryEmailtemplate('', secondaryEmailCount));
    $(`#secEmail${secondaryEmailCount}`).rules("add", {
        required: true,
        email: true
    });

    secondaryEmailCount++;
}

function editPrimaryEmail(thisObj) {
    $(thisObj).closest('form').find('.card-div-footer').show();
    $('.addPrimaryEmail').html(primaryEmailtemplate(userPrimaryEmail));
}


function editSecondaryEmail(thisObj) {
    $(thisObj).closest('form').find('.card-div-footer').show();
    var secondaryEmail = $(thisObj).closest('.addSecondaryEmail').find('.input-text').text();
    $(thisObj).closest('.addSecondaryEmail').replaceWith(secondaryEmailtemplate(secondaryEmail));
}


function deleteSecondaryEmail(thisObj) {
    //    $(thisObj).closest('form').find('.card-div-footer').show();
    $(thisObj).closest('.addSecondaryEmail').remove();
}


$(document).ready(function () {
    $('.addPrimaryEmail').html(primaryEmailFilledtemplate(userPrimaryEmail));



    if (userSecondaryEmail == '') {
        //        $('.addSecondaryEmail').html(secondaryEmailLinktemplate());

    } else {
        $('.addSecondaryEmail').html(secondaryEmailFilledtemplate(userSecondaryEmail));
    }
})


function closeSubscriptionDiv(thisObj) {
    $(thisObj).closest('.card-div').find('.traveller-short-info').show();
    $(thisObj).closest('.card-div').find('.card-div-header h3').html(notSubscribeHeading);
    goToNextStep(thisObj);
}


$(document).ready(function () {
    // $.ajax({
    //     url: "userData.json",
    //     type: 'POST',
    //     dataType: 'json',

    //     success: function (data) {
    //         if (data.length > 0) {
    //             addTravellerList(data);
    //         } else {
    //             $('.addTravellerContent').html(addTravellerBlankTemplate());
    //         }
    //     },
    //     error: function (data) {

    //     }
    // })
})


function addTravellerList(travellerArray) {
    var template = ``;
    var count = 1;
    $.each(travellerArray, function (key, value) {
        template += `${addTravellerTemplate(value, count)}`;
        count++;
    })
    $('.addTravellerContent').html(template);
}

function addNewTravellerForblankTemplate() {
    var count = 1;
    $('.addTravellerContent').html(addTravellerTemplate({}, count));
    updateDom(count);
}


function addAnotherTraveller(thisObj) {
    var count = $('.addTravellerContent .personalDetailsCard').length;
    count++;
    $(thisObj).closest('.card-div').before(addTravellerTemplate({}, count));
    $('#addAnotherTravellerDiv').hide();
    updateDom(count);
}

function updateDom(count) {
    componentHandler.upgradeDom();
    initilizeDatepicker();
    $('.personalDetailsCard').eq(count - 1).find('.airlineOptions').html(airlineOptionsTemplate);
    $('.personalDetailsCard').eq(count - 1).find('.airlineOptions').select2({
        placeholder: "Search airline"
    });
    $('.personalDetailsCard').eq(count - 1).find('.hotelOptions').html(hotelOptionsTemplate);
    $('.hotelOptions').select2({
        placeholder: "Search hotel"
    });
    $('.personalDetailsCard').eq(count - 1).find('.countryDropdown').html(countryOptionsTemplate);
    $('.personalDetailsCard').eq(count - 1).find('.countryDropdown').select2({
        allowClear: true,
        placeholder: "Select country"
    });
}

function openContent(thisObj, obj) {
    if ($(thisObj).closest('.accord').hasClass('active')) {
        $(thisObj).closest('.accord').removeClass('active');
        $(thisObj).closest('.accord').find('.accord-content').slideUp();
    } else {
        if (obj.hideOther) {
            $(thisObj).closest('.accordian').find('.accord').not($(thisObj).closest('.accord')).removeClass('active');
            $(thisObj).closest('.accordian').find('.accord-content').not($(thisObj).closest('.accord').find('.accord-content')).slideUp();
        }
        $(thisObj).closest('.accord').addClass('active');
        $(thisObj).closest('.accord').find('.accord-content').slideDown();
    }
}

function economySelection(thisObj) {
    // if ($(thisObj).val() == "1") {
    //     $('.basic-economy-warning-inner').show();
    // } else {
    //     $('.basic-economy-warning-inner').hide();
    // }
}


function checkEconomy(thisObj) {
    // if ($(thisObj).prop('checked')) {
    //     $('.basicEconomyRadioButtons').html(economyRadioButtons());
    //     componentHandler.upgradeDom();
    // } else {
    //     $('.basicEconomyRadioButtons').html('');
    //     $('.basic-economy-warning-inner').hide();
    // }
}

function checkPassword(thisObj) {
    var confirmPassword = $(thisObj).val();
    var newPassword = $('#newPassword').val();
    if (confirmPassword == newPassword) {
        $(thisObj).closest('.input-box').find('.passwordCheck').show();
    } else {
        $(thisObj).closest('.input-box').find('.passwordCheck').hide();
    }
}

function sideTabItemClick(thisobj) {
    var index = $(thisobj).index();
    var length = $('#helpTab .side-tab-content').length;
    $('.side-tab-list-item').removeClass('active');
    $(thisobj).addClass('active');
    $('#helpTab .side-tab-content').removeClass('active');
    for (var i = index; i < length; i++) {
        $('#helpTab .side-tab-content').eq(i).addClass('active');
    }
}


function removeFlyer(thisObj) {
    $(thisObj).closest('.flyer').remove();
}

function getCreditCardBox1(thisObj) {
    getOverlay();
    $('#payButtons1').hide();
    $('#creditCardBox').show();
}

function removeCreditCardBox1(thisObj) {
    removeOverlay();
    $('#creditCardBox').hide();
    $('#payButtons1').show();
}

function removeCreditCardBox2(thisObj) {
    $(thisObj).closest('.card-div').removeClass('active').removeClass('shadow').find('.card-div-body').hide();
    removeOverlay();
}

function cancelSubscription(thisObj) {
    isSubscribed = false;
    $(thisObj).closest('.card-div').find('.card-div-header h3').html(openSubscribeHeading);
    $('.paymentform-div').html(noSubscriptionTemplate());
    componentHandler.upgradeDom();
}

function applySubscription(thisObj) {
    isSubscribed = true;
    removeOverlay();
    $(thisObj).closest('.card-div').find('.card-div-header h3').html(subscribedHeading);
    $('.paymentform-div').html(subscriptionTemplate());
    componentHandler.upgradeDom();

}

function disableKeys(event) {
    if (event.keyCode == 13) {
        event.preventDefault();
    }
}

function changePaymentMethod() {
    $('.paymentform-div').html(paymentMethodChangeTemplate());
    componentHandler.upgradeDom();
}

function savePaymentMethod() {
    $('.paymentform-div').html(subscriptionTemplate());
    componentHandler.upgradeDom();
}

function savePaymentDetails(thisObj) {
    var shortInfoTemplate;
    if (isSubscribed) {
        $(thisObj).closest('.card-div').find('.card-edit').hide();
        $(thisObj).closest('.card-div').find('.card-div-header h3').html(`${subscribedHeading} <img class="link-icon" src="images/green-tick.png" />`);
        shortInfoTemplate = `<div class="traveller-short-info-inner">Renewal date: 23 June, 2019</div>`;
    } else {
        $(thisObj).closest('.card-div').find('.card-edit').show();
        $(thisObj).closest('.card-div').find('.card-div-header h3').html(`${notSubscribeHeading}`);
        shortInfoTemplate = `<div class="traveller-short-info-inner">${notSubscribedText}</div>`;
    }


    $(thisObj).closest('.traveller-form').find('.traveller-short-info').show().html(shortInfoTemplate);
    goToNextStep(thisObj);

}

function getOverlay() {
    $('body').addClass('overflow-hidden');
    $('.overlay').removeClass('hidden');
}

function removeOverlay() {
    $('body').removeClass('overflow-hidden');
    $('.overlay').addClass('hidden');
}

function showLoader(formId) {
    $(formId).append(loaderTemplate());
    componentHandler.upgradeDom();
}

function removeLoader(formId) {
    $(formId).find('.loader-div').remove();
}

function toggleAdminList(thisObj) {
    if ($(thisObj).closest('.admin').hasClass('active')) {
        $(thisObj).closest('.admin').removeClass('active');
        $(thisObj).closest('.admin').find('.admin-list').hide();
    }
    else {
        $(thisObj).closest('.admin').addClass('active');
        $(thisObj).closest('.admin').find('.admin-list').show();
    }


    //    $(thisObj).closest('.admin').find('.admin-list').toggle();
}

$(document).mouseup(function (e) {
    var container = $('.admin');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
        container.removeClass('active');
        container.find('.admin-list').hide();
    }
});


// function initStripe(initParams) {
// 	{
// 		var newCardUIName ;
// 		if (initParams && initParams.newCardUIName){
// 			newCardUIName = initParams.newCardUIName;
// 		}
// // $("#gallopPaymentCard").html(gallop.createElement(newCardUIName));
// 		$("#gallopPaymentCard").html(gallop.createCCElement(initParams));
// 		window.addEventListener("message", function(e) {
// 			if (e.origin === gallop_cc_originurl) {
// 				if (e.data.indexOf("stripeSuccess") > -1) {
// 					if (prevToken != JSON.parse(e.data).data.id){
// 						setOutcome(JSON.parse(e.data).data, initParams);
// 					}
// 					prevToken = JSON.parse(e.data).data.id
// 				} else if (e.data.indexOf("errorStripe") > -1
// 						|| e.data.indexOf("gallopError") > -1) {
// 					$(".payment-save").hide();
// 					setOutcome(JSON.parse(e.data).data, initParams);
// 				}
// 			}
// 		}, false);

// 	}
// }