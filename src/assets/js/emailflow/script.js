var noOfTravellers = 2;
var countryOptionsTemplate = ``;
var selectedCountry = "us";

// Smart Modal hack to make it dimissable
var ngxSmartModalIds={};
function setNgxSmartModalOpen(ngxSmartModalIdInput){
        ngxSmartModalIds[ngxSmartModalIdInput]= new Date().getTime();
}
function setNgxSmartModalOpenStateClosed(ngxSmartModalIdInput){
    delete ngxSmartModalIds[ngxSmartModalIdInput];
}
function getCurrentlyOpenNgxSmartModalIds(){
    return ngxSmartModalIds;
}
// ENd ngx smart modal hack
function parseUrlParam(){
    var data = location.href;
    return parseParamsFromURLString(data);
}

function parseParamsFromURLString(data){
	data = data.split('#')[0]
	var get_param_value_dict = {};
	if (data && data!=undefined && data!=null) {
		data = data.split("?");
		if (data.length > 1) {
			data = data[1].split("&");
			if (data.length > 0) {
				for (i = 0; i < data.length; i++) {
					var param_value = data[i];
					param_value = param_value.split("=");
					if (param_value.length > 1) {
						var decodedParamVal = data[i].substring(param_value[0].length+1);
						if (decodedParamVal.indexOf("%25") != -1) {
							decodedParamVal = decodeURIComponent(decodeURIComponent(decodedParamVal));
						} else {
							decodedParamVal = decodeURIComponent(decodedParamVal);
						}
						get_param_value_dict[param_value[0]] = decodedParamVal;
					}

				}
			}
		}
	}
	return get_param_value_dict;
}
function isNJoySpecificParamInUrl(){
    var params = parseUrlParam();
    if (params && params.deployedFor && params.deployedFor === 'NJOY'){
        return true;
    }
    return false;
}
function getStoreAppVersionName(){
    var params = parseUrlParam();
    if (params && params.appStoreVersionName && params.appStoreVersionName.trim().length > 0){
        return params.appStoreVersionName.trim();
    }
    return "1.0.0";
}
function addNavigationComponentPadding(){
//    $("body").css("padding-bottom", "80px");
}
function removeNavigationComponentPadding(){
//    $("body").css("padding-bottom","0px");
}
function disableScroll() {
    // To get the scroll position of current webpage
    TopScroll = window.pageYOffset || document.documentElement.scrollTop;
    LeftScroll = window.pageXOffset || document.documentElement.scrollLeft,
    
    // if scroll happens, set it to the previous value
    window.onscroll = function() {
    window.scrollTo(LeftScroll, TopScroll);
            };
    }
    function enableScroll() {
        window.onscroll = function() {};
        }
var maxCardHeight = 0;
function adjustMapHeightForFullScreen(){
    var cardHeight = $(".hotel-map-item").height();
    if (cardHeight > maxCardHeight){
        maxCardHeight = cardHeight;
    }
    if($(".agm-map-container-inner")[0]){
    $(".agm-map-container-inner")[0].style.removeProperty('height');
    $(".agm-map-container-inner")[0].style.setProperty('height', 'calc(100vh - '+ maxCardHeight + 'px)', 'important');
    }
}
function getAppPlatform(){
    var params = parseUrlParam();
    if (params && params.platform && params.platform.trim().length > 0){
        return params.platform.trim();
    }
    return "web";
}
function getAppBuildNumber(){
    var params = parseUrlParam();
    try{
        if (params && params.appBuildNumber && params.appBuildNumber.trim().length > 0){
            return parseInt(params.appBuildNumber.trim());
        }
    }catch(err){

    }
    return 0;
}
function focusAndOpenKeyboardIos(el) {
    // Align temp input element approximately where the input element is
    // so the cursor doesn't jump around
    var __tempEl__ = document.createElement('input');
    __tempEl__.style.position = 'absolute';
    __tempEl__.style.top = (el.offsetTop + 7) + 'px';
    __tempEl__.style.left = el.offsetLeft + 'px';
    __tempEl__.style.height = 0;
    __tempEl__.style.opacity = 0;
    // Put this temp element as a child of the page <body> and focus on it
    document.body.appendChild(__tempEl__);
    __tempEl__.focus();

    // The keyboard is open. Now do a delayed focus on the target element
    setTimeout(function() {
    $('#' + el).focus();
    $('#' + el).click();
    // Remove the temp element
    document.body.removeChild(__tempEl__);
    }, 200);
  }


function setFocusOnInputField(inputFieldElementId,value1){
    $("#" + inputFieldElementId).focus();
    var childInputs = $("#" + inputFieldElementId).find("input");
    if(inputFieldElementId==='airportInputOnFullScreen' && value1 !==''){
    document.getElementById('airportInputOnFullScreen').value = value1;
    }
    if (childInputs){
        childInputs.focus();
    }
}
function setInputOnReadonly(inputFieldElementId){
    $('#'+inputFieldElementId).attr('readonly', true);
}
function unsetsetFocusOnInputField(inputFieldElementId){
    $("#" + inputFieldElementId).blur();
}
function setFocusOnHotelInputField(){
   $("#hotelAddressCustomeLocationInputField").focus();
}
function checkMobileDevice() {
    if ((/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))) {
        return true;
    } else {
        return false;
    }
}

$(document).ready(function () {
    checkAppleDevices();
});

// $.ajax({
//     url: "countryData.json",
//     type: 'POST',
//     dataType: 'json',

//     success: function (data) {
//         countryTemplate(data);
//     },
//     error: function (data) {

//     }
// })
function seatModalHack(){
    $('.modal').css('position', 'absolute');
    setTimeout(() => {
        $('.modal').css('position', 'fixed');
    }, 500);

}
var searchResultsData = {}
var emailId = undefined;
var bookTicketId;
var bookThreadId;
var bookTicketNumber;
var agentNotifyURL;
var travellerData;
var travellerCount = 1;
var reminderMode = false;

function setRequestParam(email, ticketId, threadId, ticketNumber, reqUrl,travellers,travellerTypeData,reminderFlow) {
    emailId = email;
    bookTicketId = ticketId;
    bookThreadId = threadId;
    bookTicketNumber = ticketNumber;
    agentNotifyURL = reqUrl;
    travellerCount = travellers;
    if(travellerTypeData) travellerData = travellerTypeData;
    if(reminderFlow) reminderMode = reminderFlow;
    sessionid = new Date().getTime()+"_"+generateUUID();
   // registerMixPannelUUIdAndSession();


}

function updateDOM() {
    setTimeout(function () {
        componentHandler.upgradeDom();
        console.log("dom updated");
    }, 500);

}
function countryTemplateForScriptjs(countryArray) {
    var selectedText = ``;
    $.each(countryArray, function (key, value) {
        if (value.i == selectedCountry) {
            selectedText = `selected`;
        }

        else {
            selectedText = ``;
        }

        countryOptionsTemplate += `<option value="${value.i.toUpperCase()}" ${selectedText}>${value.n}</option>`
    });

    $('#country').html(countryOptionsTemplate);
    $('#nationality').html(countryOptionsTemplate);
    $('.js-example-basic-single').select2();
}


function changeInput(thisObj) {
    if ($(thisObj).prop('checked')) {
        $(thisObj).closest('.input-with-checkbox').find('.input-textfield').val('');
        $(thisObj).closest('.input-with-checkbox').addClass('disabled');
    } else {
        $(thisObj).closest('.input-with-checkbox').removeClass('disabled');
    }
}
function getWindowWidth(){
	return $(window).width();
}
 function addingHeader(){
    var params = window.location.href;
    if(params.toLowerCase().search('query') === -1 || params.toLowerCase().search('login') !== -1 || params.toLowerCase().search('logout') !== -1){
     document.getElementById("mobile-top-clipper").style.display = "none";
    }else{
     document.getElementById("mobile-top-clipper").style.display = "block";
    }
 }
function checkAppleDevices() {
    if ((/iPhone|iPad|iPod/i.test(navigator.userAgent))) {
        $('body').addClass('appleDevice');
    } else {
        $('body').removeClass('appleDevice');
    }
}
function checkAppleDevices1() {
    if ((/iPhone|iPad|iPod|Mac/i.test(navigator.userAgent))) {
        return true;
    } else {
        return false;
    }
}
function validatePreferencesForm(thisObj) {
    $('#airlineSearch').val('');
    $("#preferencesForm").validate({
        focusInvalid: false,
        rules: {
            airlineSearch: {
                required: function () {
                    if ($('#selectAirlines .chip').length <= 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            },
            homeAddress: {
                required: true
            }
        },
        messages: {
            airlineSearch: 'Atleast one selection is required'
        },
        errorPlacementForScriptjs: function (error, element) {
            errorPlacementForScriptjs(error, element);
        }
    });
    var isFormValid = $("#preferencesForm").valid();

    if (isFormValid) {
        postPreferencesForm(thisObj);
    } else {
        return false;
    }
    return false;
}



function validateExpensifyForm(thisObj) {
    $("#expensifyForm").validate({
        focusInvalid: false,
        rules: {
            expensifyLoginEmail: {
                email: true
            }
        },
        errorPlacementForScriptjs: function (error, element) {
            errorPlacementForScriptjs(error, element);
        }
    });
    var isFormValid = $("#expensifyForm").valid();
    if (isFormValid) {
        sendPageJsonData(thisObj);
    } else {
        return false;
    }
    return false;
}


function sendPageJsonData(thisObj) {
    var pageData = {
        itinerary: [],
        personal_details: [],
        expensify: []
    };




    $('.listing-card').find('.card-section').each(function () {
        var itinerary = {};
        var sectionId = $(this).attr('id');
        itinerary.sectionId = sectionId;
        if ($(this).hasClass('selectionActive')) {
            var selectionId = $(this).find('.flight-listing-row.selected').attr('id');
            itinerary.status = 1;
            itinerary.selectedId = selectionId;
        }
        else
            if ($(this).hasClass('skipActive')) {
                itinerary.status = 0;
                itinerary.selectedId = "";
            }
        pageData.itinerary.push(itinerary);
    });


    $('.form-card').find('.traveller-form').each(function () {
        var passergerDetails = {};
        passergerDetails.title = $(this).find('[name="title"]').val();
        passergerDetails.firstName = $(this).find('[name="firstName"]').val();
        passergerDetails.middleName = $(this).find('[name="middleName"]').val();
        passergerDetails.lastName = $(this).find('[name="lastName"]').val();
        passergerDetails.suffix = $(this).find('[name="suffix"]').val();
        passergerDetails.gender = $(this).find('[name="gender"]:checked').val();
        passergerDetails.dob = $(this).find('[name="dob"]').val();
        passergerDetails.email = $(this).find('[name="email"]').val();
        passergerDetails.number = $(this).find('[name="number"]').val();
        passergerDetails.homeAddress = $(this).find('[name="homeAddress"]').val();
        passergerDetails.homeZip = $(this).find('[name="homeZip"]').val();
        passergerDetails.workAddress = $(this).find('[name="workAddress"]').val();
        passergerDetails.workZip = $(this).find('[name="workZip"]').val();
        passergerDetails.travellerNumber = $(this).find('[name="travellerNumber"]').val();
        passergerDetails.flyerNumber = $(this).find('[name="flyerNumber"]').val();
        passergerDetails.guestNumber = $(this).find('[name="guestNumber"]').val();
        passergerDetails.passportNumber = $(this).find('[name="passportNumber"]').val();
        passergerDetails.passportExpiryDate = $(this).find('[name="passportExpiryDate"]').val();
        passergerDetails.nationality = $(this).find('[name="nationality"]').val();
        passergerDetails.country = $(this).find('[name="country"]').val();
        pageData.personal_details.push(passergerDetails);
    });

    pageData.expensify = $('[name="expensifyLoginEmail"]').val();
    window.location.href = "payment.html";
}


function errorPlacementForScriptjs(error, element) {
    var placement = element.closest('.input-box, .checkbox-container');
    if (!placement.get(0)) {
        placement = element;
    }
    if (error.text() !== '') {
        placement.append(error);
    }
}


jQuery.validator.addMethod("alphanumeric", function (value, element) {
    return this.optional(element) || /^[0-9a-zA-Z]+$/i.test(value);
}, "Letters and numbers only");
//here modify the current email validation method of jQuery
$.validator.methods.email = function (value, element) {
    return this.optional(element) || /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(value);
}



function toggleFlightSummery(thisObj) {
    if ($(thisObj).closest('.flight-row-container').hasClass('active')) {
        $(thisObj).closest('.flight-row-container').removeClass('active');
        $(thisObj).closest('.flight-row-container').find('.flight-row.detailed').hide();
        $(thisObj).closest('.flight-row-container').find('.flight-row.summery').fadeIn();
    } else {
        $(thisObj).closest('.flight-row-container').addClass('active');
        $(thisObj).closest('.flight-row-container').find('.flight-row.summery').hide();
        $(thisObj).closest('.flight-row-container').find('.flight-row.detailed').fadeIn();
    }
}

function toggleFlightSummaryNew(thisObj){
    toggleFlightSummery(thisObj);
}
function toggleListByIndex(eventIndex){
    toggleList($('#flightPreference-'+eventIndex).find('.card-section-heading'));
}

function toggleList(thisObj) {
    if ($(thisObj).closest('.card-section').hasClass('active')) {
        $(thisObj).closest('.card-section').removeClass('active');
        $(thisObj).closest('.card-section').find('.card-section-body').hide();
    //    $(thisObj).closest('.card-section').find('.viewAllLink').text($('#viewAllText').text());

    } else {

        // $(thisObj).closest('.card').find('.card-section').not($(thisObj).closest('.card-section') && $(thisObj).closest('.card').find('.card-section.selectionActive')).removeClass('active');
        // $(thisObj).closest('.card').find('.card-section').not($(thisObj).closest('.card-section') && $(thisObj).closest('.card').find('.card-section.selectionActive')).find('.card-section-body').hide();
        // $(thisObj).closest('.card').find('.card-section').not($(thisObj).closest('.card-section') && $(thisObj).closest('.card').find('.card-section.selectionActive')).find('.viewAllLink').text('view all');

        $(thisObj).closest('.card-section').addClass('active')
        $(thisObj).closest('.card-section').find('.card-section-body').slideDown();
        // $(thisObj).closest('.card-section').find('.viewAllLink').text($('#viewLessText').text());
        //        $(thisObj).closest('.card-section').find('.skipCheckbox').prop('checked', false);
        $(thisObj).closest('.card-section').find('.skipCheckbox .mdl-checkbox__input').prop('checked', false);
        if($(thisObj).closest('.card-section').find('.skipCheckbox').length > 0){
            $(thisObj).closest('.card-section').find('.skipCheckbox').get(0).MaterialCheckbox.uncheck();
        }
    }
}


function selectRowByIndexes(eventIndex, optionIndex) {
    var rowElement = $('#flightPreference-' + eventIndex).find('.card-section-body').find('ul').find('.flight-listing-row').get(optionIndex);
    selectRow(rowElement);
}
function selectRow(thisObj) {

    if ($(thisObj).closest('.card-section').find('.skipCheckbox').length > 0) {
        $(thisObj).closest('.card-section').find('.skipCheckbox .mdl-checkbox__input').prop('checked', false);
        $(thisObj).closest('.card-section').find('.skipCheckbox').get(0).MaterialCheckbox.uncheck();
    }

    $(thisObj).closest('.flight-listing-row').addClass('selected');
    $(thisObj).closest('.card-section').addClass('selectionActive');
    $(thisObj).closest('ul').find('.flight-listing-row').not($(thisObj).closest('.flight-listing-row')).hide();
    $(thisObj).closest('.card-section').find('.card-section-header .toggle-link').hide();
    $(thisObj).closest('.card-section').find('.changeButton').show();
    if ($(thisObj).closest('.flight-row-container').hasClass('active')) {
        $(thisObj).closest('.flight-row-container').removeClass('active');
        $(thisObj).closest('.flight-row-container').find('.flight-row.detailed').hide();
        $(thisObj).closest('.flight-row-container').find('.flight-row.summery').fadeIn();
    }
    openNextSection(thisObj);
    getTopSelection(thisObj);
}


function getEventIds(){
  return searchResultsData.eventIds;
}

var selectedFlightsIndex = {};


function revalidateUpdateFlightPrice(eventIdx,optionIdx,updatedPrice,updatedDiscountedPrice, isWithinPolicy){
    searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx][0].price = updatedPrice;
    searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx][0].discountedPrice = updatedDiscountedPrice;
//    searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx][0].withinPricePolicy = isWithinPolicy;
    searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx][0].withinPolicy = isWithinPolicy;
    if(searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx].length > 1){
        searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx][1].withinPolicy = isWithinPolicy;
//        searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx][1].withinPricePolicy = isWithinPolicy;
    }
}
function revalidateUpdateCarPrice(eventIdx,optionIdx,updatedPrice,updatedDiscountedPrice, isWithinPolicy){
    searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx].price = updatedPrice;
    searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx].discountedPrice = updatedDiscountedPrice;
//    searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx][0].withinPricePolicy = isWithinPolicy;
    searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx].policy = isWithinPolicy;
    if(searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx].length > 1){
        searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx][1].policy = isWithinPolicy;
//        searchResultsData.eventListTypeMap[eventIdx].eventOptions[optionIdx][1].withinPricePolicy = isWithinPolicy;
    }
}
function updatePriceChangeInBookingResponse(price, discountedPrice, withinPolicy){
    var optionId= 0; var eventId = 0;
    for (val in selectedFlightsIndex) {
        optionId = selectedFlightsIndex[val]
        eventId = parseInt(val.split('-')[1]);
        break;
    }
    let bookedOption = searchResultsData.eventListTypeMap[eventId].eventOptions[optionId];
    bookedOption[0].price = price;
    bookedOption[0].discountedPrice = discountedPrice;
    bookedOption[0].withinPolicy = withinPolicy;
    if (bookedOption.length > 1){
        bookedOption[1].withinPolicy = withinPolicy;
    }
}


function buildConfirmationBookingData(eventId) {
    var data = {};
    var choice = {};
    data.indexSelected = [];
    var totalPrice = 0.00;
    var airlineArray = [];
    var hotelChainArray = [];
    var carBrandArray=[];
    var eventIds = [];
    if (searchResultsData.eventListTypeMap && selectedFlightsIndex) {
        data.multiFlightOptions = [];
        data.multiHotelOptions = [];
        data.multiCarOptions = [];

        for (val in selectedFlightsIndex) {
            var i = selectedFlightsIndex[val]
            var e = parseInt(val.split('-')[1]);
            if (searchResultsData.eventListTypeMap[e].eventType === 'flight') {
                if(!searchResultsData.eventListTypeMap[e].eventOptions[i][0].eventId 
                    || searchResultsData.eventListTypeMap[e].eventOptions[i][0].eventId === '0'){
                        searchResultsData.eventListTypeMap[e].eventOptions[i][0].eventId = eventId;
                        if(searchResultsData.eventListTypeMap[e].eventOptions[i].length > 1){
                            searchResultsData.eventListTypeMap[e].eventOptions[i][1].eventId = eventId;
                        }                        
                }
                data.multiFlightOptions.push(searchResultsData.eventListTypeMap[e].eventOptions[i]);
                data.indexSelected.push(i + "_" + e + "_" + searchResultsData.eventListTypeMap[e].eventOptions[i][0].eventId + "_" + searchResultsData.eventListTypeMap[e].eventOptions[i][0].optionId);
                choice[searchResultsData.eventListTypeMap[e].eventOptions[i][0].eventId] = searchResultsData.eventListTypeMap[e].eventOptions[i][0].optionId;
                eventIds.push(searchResultsData.eventListTypeMap[e].eventOptions[i][0].eventId);
                if ('discountedPrice' in searchResultsData.eventListTypeMap[e].eventOptions[i][0]) {
                    totalPrice = totalPrice + parseFloat(searchResultsData.eventListTypeMap[e].eventOptions[i][0].discountedPrice);
                }
                else if ('price' in searchResultsData.eventListTypeMap[e].eventOptions[i][0]) {
                    totalPrice = totalPrice + parseFloat(searchResultsData.eventListTypeMap[e].eventOptions[i][0].price);
                }
                var found = airlineArray.some(function (el) {
                    return el.code === searchResultsData.eventListTypeMap[e].eventOptions[i][0].airline;
                });

                if(found) continue;
                // if (ffnAirlines.length > 0) {
                //     ffnAirlines = ffnAirlines + ',' + searchResultsData.eventListTypeMap[e].eventOptions[i][0].airline;
                //     ffnAirlineNames = ffnAirlineNames + ',' + searchResultsData.eventListTypeMap[e].eventOptions[i][0].airlineName;
                // } else {
                //     ffnAirlines = searchResultsData.eventListTypeMap[e].eventOptions[i][0].airline
                //     ffnAirlineNames = searchResultsData.eventListTypeMap[e].eventOptions[i][0].airlineName;
                // }
                var airlineObj = {};
                airlineObj['code'] = searchResultsData.eventListTypeMap[e].eventOptions[i][0].airline;
                airlineObj['name'] = searchResultsData.eventListTypeMap[e].eventOptions[i][0].airlineName;
                airlineArray.push(airlineObj);
            }else if(searchResultsData.eventListTypeMap[e].eventType === 'cars'){
                data.multiCarOptions.push(searchResultsData.eventListTypeMap[e].eventOptions[i]);
                data.indexSelected.push(i + "_" + e + "_" + searchResultsData.eventListTypeMap[e].eventOptions[i].eventId + "_" + searchResultsData.eventListTypeMap[e].eventOptions[i].optionId);
                choice[searchResultsData.eventListTypeMap[e].eventOptions[i].eventId] = searchResultsData.eventListTypeMap[e].eventOptions[i].optionId;
                eventIds.push(searchResultsData.eventListTypeMap[e].eventOptions[i].eventId);
                totalPrice = totalPrice + parseFloat(searchResultsData.eventListTypeMap[e].eventOptions[i].price);
            } 
            else {
                data.multiHotelOptions.push(searchResultsData.eventListTypeMap[e].eventOptions[i]);
                data.indexSelected.push(i + "_" + e + "_" + searchResultsData.eventListTypeMap[e].eventOptions[i].eventId + "_" + searchResultsData.eventListTypeMap[e].eventOptions[i].optionId);
                choice[searchResultsData.eventListTypeMap[e].eventOptions[i].eventId] = searchResultsData.eventListTypeMap[e].eventOptions[i].optionId;
                eventIds.push(searchResultsData.eventListTypeMap[e].eventOptions[i].eventId);
                if ('discountedPrice' in searchResultsData.eventListTypeMap[e].eventOptions[i]) {
                    totalPrice = totalPrice + parseFloat(searchResultsData.eventListTypeMap[e].eventOptions[i].discountedPrice);
                }
                else if ('price' in searchResultsData.eventListTypeMap[e].eventOptions[i]) {
                    totalPrice = totalPrice + parseFloat(searchResultsData.eventListTypeMap[e].eventOptions[i].price);
                }

                var tempHotelChain = searchResultsData.eventListTypeMap[e].eventOptions[i].hotelChain;
                var tempHotelTraflaChain = searchResultsData.eventListTypeMap[e].eventOptions[i].traflaHotelChain;
                var tempHotelChainName = searchResultsData.eventListTypeMap[e].eventOptions[i].hotelName;
                var hotelHandler = searchResultsData.eventListTypeMap[e].eventOptions[i].source;
                var loyaltyPointsSupported = searchResultsData.eventListTypeMap[e].eventOptions[i].loyaltyPointsSupported;
                if(!tempHotelChain || tempHotelChain.trim().length == 0 || tempHotelChain.trim() === 'NO CHAIN'){
                    tempHotelChain = searchResultsData.eventListTypeMap[e].eventOptions[i].hotelName;
                }
                 var found = hotelChainArray.some(function (el) {
                    return el.code === searchResultsData.eventListTypeMap[e].eventOptions[i].tempHotelChain;
                });

                if(found) continue;
                // if (hotelChains.length > 0) {
                //     hotelChains = hotelChains + ',' + tempHotelChain;
                //     hotelChainsNames = hotelChainsNames + ',' + tempHotelChainName;
                // } else {
                //     hotelChains = tempHotelChain;
                //     hotelChainsNames = tempHotelChainName;
                // }
                var hotelChainObj = {};
                hotelChainObj['code']=tempHotelChain;
                hotelChainObj['traflaChainCode']=tempHotelTraflaChain;
                hotelChainObj['name']=tempHotelChainName;
                hotelChainObj['handler'] = hotelHandler;
                hotelChainObj['loyaltyPointsSupported'] = loyaltyPointsSupported;

                hotelChainArray.push(hotelChainObj);
            }
        }
    }

    data.ffnAirlines = airlineArray;
    data.hotelChains = hotelChainArray;
    data.totalPrice = totalPrice.toFixed(2);
    data.appointment_location = searchResultsData.appointment_location;
    data.choice = choice;
    data.eventIds = eventIds;
    return data;
}


function isZipcodeMandatoryForBooking() {
    if (selectedFlightsIndex) {
        var zipcodeMandatory = false;
        var counter = 0;
        $.each(searchResultsData.eventListTypeMap, function (i, event) {
            if (selectedFlightsIndex['event-' + i]) {
                if(event.eventType == 'flight'){
                    var retOpt = event.eventOptions[selectedFlightsIndex['event-' + i]][0];
                     if ('zipcodeMandatory' in retOpt && retOpt.zipcodeMandatory) {
                        zipcodeMandatory = true;
                    }
                    else if ('isZipcodeMandatory' in retOpt && retOpt.isZipcodeMandatory) {
                        zipcodeMandatory = true;
                        
                    } else if ('isZipCodeRequired' in retOpt && retOpt.isZipCodeRequired) {
                        zipcodeMandatory = true;
                        
                    }
                }else  if(event.eventType == 'cars'){
                    var retOpt = event.eventOptions[selectedFlightsIndex['event-' + i]];
                     if ('zipCodeRequired' in retOpt && retOpt.zipCodeRequired) {
                        zipcodeMandatory = true;
                        counter = 1;
                        
                    }else{
                        zipcodeMandatory=false;
                       
                    }
                    if(counter >0){
                        zipcodeMandatory=true;
                        
                    }
                }
                else{
                    var retOpt = event.eventOptions[selectedFlightsIndex['event-' + i]];
                    if ('isZipCodeRequired' in retOpt && retOpt.isZipCodeRequired) {
                        zipcodeMandatory = true;
                        counter = 1;
                        
                    }
                }
            }
        });
        return zipcodeMandatory;
    }
    return false;
}

function isOutsidePolicy() {
    if (selectedFlightsIndex) {
        var isOutsidePolicyFlag = false;
        var count=0
        $.each(searchResultsData.eventListTypeMap, function (i, event) {
            if (selectedFlightsIndex['event-' + i]) {
                if(event.eventType == 'flight'){
                    var retOpt = event.eventOptions[selectedFlightsIndex['event-' + i]][0];
                     if ('withinPolicy' in retOpt) {
                        isOutsidePolicyFlag = !retOpt.withinPolicy;
                    }
                    if(isOutsidePolicyFlag){
                        count =1;
                    }
                }else if(event.eventType == 'cars'){
                    var retOpt = event.eventOptions[selectedFlightsIndex['event-' + i]];
                     if ('policy' in retOpt) {
                        isOutsidePolicyFlag = !retOpt.policy;
                    }
                    if(isOutsidePolicyFlag){
                        count =1;
                    }
                }else{
                    var retOpt = event.eventOptions[selectedFlightsIndex['event-' + i]];
                     if ('inPolicy' in retOpt) {
                        isOutsidePolicyFlag = !retOpt.inPolicy;
                    }
                    if(isOutsidePolicyFlag){
                        count =1;
                    }
                }
                if(isOutsidePolicyFlag){
                    return false;
                }
            }
        });
        if(count===1){
            isOutsidePolicyFlag =true;    
        }
        return isOutsidePolicyFlag;
    }
    return false;
}

function canAttemptDirectBooking(){
    var canAttempt = false;
    if (selectedFlightsIndex && Object.keys(selectedFlightsIndex).length == 1) {
        $.each(searchResultsData.eventListTypeMap, function (i, event) {
            if (selectedFlightsIndex['event-' + i]) {
                if(event.eventType == 'flight'){
                    var retOpt = event.eventOptions[selectedFlightsIndex['event-' + i]][0];
                    canAttempt =  'handlerName' in retOpt
                    && (retOpt.handlerName.toUpperCase() === 'TRAVELPORT' || retOpt.handlerName.toUpperCase() === 'MYSTIFLY')
                    && (!('currency' in retOpt) || retOpt.currency == "USD")
                    && !(retOpt.flight_hops[0].brandClass && retOpt.flight_hops[0].brandClass.trim().length > 0)
                    ;
                }else if(event.eventType == 'cars'){
                    var retOpt = event.eventOptions[selectedFlightsIndex['event-' + i]];
                    canAttempt =  'source' in retOpt
                    && ((retOpt.source === 'PriceLine') || (retOpt.source === 'TravelPort'))
                    && (!('currency' in retOpt) || retOpt.currency == "USD")
                    ;
                }
            }
        });
    }
    return canAttempt;
}

function canAttemptRevalidate(eventIdx, optionIdx){
    var canAttempt = false;
        $.each(searchResultsData.eventListTypeMap, function (i, event) {
            // if ( i === eventIdx) {
                if(i === eventIdx && event.eventType == 'flight'){
                    var retOpt = event.eventOptions[optionIdx][0];
                    canAttempt =  'handlerName' in retOpt
                    && (retOpt.handlerName.toUpperCase() === 'TRAVELPORT' || retOpt.handlerName.toUpperCase() === 'MYSTIFLY')
                    && (!(retOpt.flight_hops[0].brandClass && retOpt.flight_hops[0].brandClass.trim().length > 0))
                    ;
                }
            // }
        });
    return canAttempt;
}
function canAttemptCarRevalidate(eventIdx, optionIdx){
    var canAttempt = false;
        $.each(searchResultsData.eventListTypeMap, function (i, event) {
            // if ( i === eventIdx) {
            if(i === eventIdx && event.eventType == 'cars'){
                    var retOpt = event.eventOptions[optionIdx];
                    canAttempt =  'source' in retOpt
                    && (retOpt.source === 'PriceLine' || retOpt.source === 'TravelPort' || retOpt.source === 'Sabre')
                    ;
                }
            // }
        });
    return canAttempt;
}
function isPassportMandatoryForBooking() {

    if (selectedFlightsIndex) {
        var passportRequired = false;
        $.each(searchResultsData.eventListTypeMap, function (i, event) {
            if (selectedFlightsIndex['event-' + i]) {
                if(event.eventType == 'flight'){
                    var retOpt = event.eventOptions[selectedFlightsIndex['event-' + i]][0];
                    if ('passportMandatory' in retOpt && retOpt.passportMandatory) {
                        passportRequired = true;
                    }
                    if ('isPassportMandatory' in retOpt && retOpt.isPassportMandatory) {
                        passportRequired = true;
                    }
                    if (!passportRequired && event.eventOptions[selectedFlightsIndex['event-' + i]].length > 1) {
                    var retOpt = event.eventOptions[selectedFlightsIndex['event-' + i]][1];

                    if ('passportMandatory' in retOpt && retOpt.passportMandatory) {
                        passportRequired = true;
                    }
                    if ('isPassportMandatory' in retOpt && retOpt.isPassportMandatory) {
                        passportRequired = true;
                    }
                }
                }
            }
        });
        return passportRequired;
    }
    return false;
}

function notifyAgentWithNewTripParams(userid, ticketId, tripId,pticketNumber,notifyURL,eventIdAndOptionsMap,projectTag,tripSessionGroupName,sToken){
    bookTicketId = ticketId;
    emailId = userid;
    searchResultsData.tripId = tripId;
    bookTicketNumber = pticketNumber;
    agentNotifyURL =notifyURL;
    notifyAgent(false,eventIdAndOptionsMap,true,projectTag,tripSessionGroupName,userid,sToken);
}
function notifyAgent(hidden,pChoice,hotelBooking,projectTag,tripSessionGroupName,noteToAdmin,userid,sToken,impersonatedEmailId) {
    if(!(bookTicketId)) return;
    // trafla.common.get_parameter_dict(location.href);

    // var param_val = trafla.common.get_param_value_dict;
    var searchType = "Flight"



    // for (val in currentHotelIndex){
    // 	var multiDivId = val;
    // 	var e = parseInt(val.split('_')[1]);
    // 	var i = currentHotelIndex[val];
    // 	if(i>0)
    // 	hotelInfo.push(currentHotelEventData[e][parseInt(i)-1]);
    // }



    var hotelInfo = [];
    var carData = [];
    var flightSelection  = buildConfirmationBookingData();

    var globalRedirectUrlBookingPage = window.location.search
    +"&confirmedOptions="+encodeURI(JSON.stringify(selectedFlightsIndex));

    if(flightSelection.multiHotelOptions) hotelInfo = flightSelection.multiHotelOptions;
    if(flightSelection.multiCarOptions) carData = flightSelection.multiCarOptions;
    if(pChoice){
        flightSelection.choice = pChoice;
    }
    let bookedFor= undefined;
    if(impersonatedEmailId && impersonatedEmailId != emailId){
        bookedFor = impersonatedEmailId;
    }
    var notifyAgentReq = {
        ticketId: bookTicketId,
        threadId: bookThreadId,
        ticketNumber: bookTicketNumber,
        carData: JSON.stringify(carData),
        flightData: JSON.stringify(flightSelection),
        hotelInfo: JSON.stringify(hotelInfo),
        choice: JSON.stringify(flightSelection.choice),
        userId: emailId,
        bookedFor: bookedFor,
        "searchType": searchType,
        "hidden": hidden ? true : false,
        "tripId": searchResultsData.tripId,
        "isUserConfirmBooking": hidden ? false : true,
        "hotelWebBooking" : hotelBooking ? true : false,
        "globalRedirectUrlBookingPage": globalRedirectUrlBookingPage
        };
    if(projectTag!==''){
         Object.assign(notifyAgentReq,{"projectTagId":(projectTag)});
    }
    if(tripSessionGroupName && tripSessionGroupName.trim()!==''){
        Object.assign(notifyAgentReq,{"groupTagName":(tripSessionGroupName)});
    } 
    if(noteToAdmin && noteToAdmin.trim()!==''){
        Object.assign(notifyAgentReq,{"noteToAdmin":noteToAdmin});
    } 


    $.ajax(
        {
            url: agentNotifyURL,
            dataType: 'JSON',
            jsonCallback: 'callback',
            data: notifyAgentReq,
            type: 'POST',
            beforeSend: function(xhr){xhr.setRequestHeader('Authorization', 'Bearer'+' '+userid+':'+sToken)},
            success: function () {

            },
            error: function (jqXHR, exception) {

            },
        });
}

function notifyAdminForApproval(userid, ticketId, tripId,pticketNumber,notifyURL,
    eventIdAndOptionsMap,transactionId,isBookingError, isPaymentError) {
    if(!(ticketId)) return;
    var searchType = "Flight"
    var hotelInfo = [];
    var flightSelection  = buildConfirmationBookingData(Object.keys(eventIdAndOptionsMap)[0]);
    var globalRedirectUrlBookingPage = window.location.search
    +"&confirmedOptions="+encodeURI(JSON.stringify(selectedFlightsIndex));

    if(flightSelection.multiHotelOptions) hotelInfo = flightSelection.multiHotelOptions;
    if(eventIdAndOptionsMap){
        flightSelection.choice = eventIdAndOptionsMap
    }
    var notifyAgentReq = {
        ticketId: ticketId,
        ticketNumber: pticketNumber,
        flightData: JSON.stringify(flightSelection),
        hotelInfo: JSON.stringify(hotelInfo),
        choice: JSON.stringify(flightSelection.choice),
        userEmail: userid,
        "searchType": searchType,
        "hidden": false,
        "tripId": tripId,
        "transactionId": transactionId,
        "isBookingError": isBookingError,
        "isPaymentError": isPaymentError,
        "globalRedirectUrlBookingPage": globalRedirectUrlBookingPage
    };

    $.ajax(
        {
            url: notifyURL,
            dataType: 'JSON',
            jsonCallback: 'callback',
            data: notifyAgentReq,
            type: 'POST',
            success: function () {

            },
            error: function (jqXHR, exception) {

            },
        });
}


function calculateSelectionByEventIndex(index,flag){
 var checkbox__input = $('#flightPreference-' + index).find('.card-section-header').find('.mdl-checkbox__input');
 checkbox__input.prop('checked',true)
 if($(checkbox__input).closest('.card-section').find('.skipCheckbox').length > 0){
            $(checkbox__input).closest('.card-section').find('.skipCheckbox').get(0).MaterialCheckbox.check();
    };
 calculateSelection(checkbox__input);
}
function calculateSelectionByEventIndex1(index,flag){
    var checkbox__input = $('#flightPreference-' + index).find('.card-section-header').find('.mdl-checkbox__input');
    checkbox__input.prop('checked',true)
    if($(checkbox__input).closest('.card-section').find('.skipCheckbox').length > 0){
               $(checkbox__input).closest('.card-section').find('.skipCheckbox').get(0).MaterialCheckbox.check();
       };
       if(checkbox__input){
        $(checkbox__input).closest('.card-section').addClass('active');
        $(checkbox__input).closest('.card-section').find('.card-section-body').slideDown();
        $(checkbox__input).closest('.card-section').removeClass('skipActive');
        $(checkbox__input).closest('.card-section').find('.skipCheckbox').get(0).MaterialCheckbox.uncheck();
       }
       getTopSelection(checkbox__input);
   }
var skipAlertShown = false;
function calculateSelection(thisObj) {
    skipAlertShown = false;
    if ($(thisObj).prop('checked')) {
        changeSelection($(thisObj).closest('.card-section-header').find('.changeButton'));
        $(thisObj).closest('.card-section').removeClass('active');
        $(thisObj).closest('.card-section').find('.card-section-body').hide();
        $(thisObj).closest('.card-section').addClass('skipActive');
        openNextSection(thisObj);
    } else {
        $(thisObj).closest('.card-section').addClass('active');
        $(thisObj).closest('.card-section').find('.card-section-body').slideDown();
        $(thisObj).closest('.card-section').removeClass('skipActive');
    }
    getTopSelection(thisObj);
}

function animatedAfterDelay(elementId){
    console.log('Animate: animatedAfterDelay');
    // setTimeout(function(){
    //     var scrollToPos = $('#' + elementId).offset().top;
    //     console.log('scrollToPos: '+scrollToPos);
    //     $('html, body').animate({
    //         scrollTop: scrollToPos
    //     }, 500);
    // }, 500);
}
function openNextSection(thisObj) {
    var sectionsLength = $(thisObj).closest('.card').find('.card-section').length;
    var indexCount = $(thisObj).closest('.card-section').index();
    var divBase = $(thisObj).closest('.card');
    var c = indexCount == sectionsLength - 1 ? 0 : indexCount + 1;
    for (var i = 0; i < sectionsLength; i++) {
        if (!(divBase.find('.card-section').eq(c).is('.selectionActive, .skipActive'))) {
            divBase.find('.card-section').eq(c).addClass('active');
            divBase.find('.card-section').eq(c).find('.card-section-body').slideDown();
            // divBase.find('.card-section').eq(c).find('.viewAllLink').text($('#viewLessText').text());
            animatedAfterDelay('flightPreference-' + c);
            break;
        }
        if (c == sectionsLength - 1) {
            c = -1;
        }
        c++;
    }
}


function getSelectedFlightIndex() {
    return selectedFlightsIndex;
}
function alignNextButtonToBottom(){
    console.log('Animate: alignNextButtonToBottom');
    if (!selectionComplete) return;
    // setTimeout(function(){
    //     var nextButtonOffset = $(".nextButton").offset().top - $(window).height() + $(".nextButton").height();
    //     if ($('html, body').scrollTop() < nextButtonOffset ||
    //         $('html, body').scrollTop() > nextButtonOffset + $(window).height()){
    //         $('html, body').animate({
    //             scrollTop: nextButtonOffset
    //         }, 500);
    //     }
    // }, 500);
}
function selectionCompleted() {
    return selectionComplete;
}
var selectionComplete = false;
var nextButtonClicked = false;
function getTopSelection(thisObj) {
    nextButtonClicked = false;
    selectionComplete = false;
    selectedFlightsIndex = {};
    var selectionArrayLength = $(thisObj).closest('.card-body').find('.card-section').length;
    var selectionCount = 0;
    $(thisObj).closest('.card-body').find('.card-section').each(function () {
        if ($(this).hasClass('selectionActive') || $(this).find('.skipCheckbox .mdl-checkbox__input').prop('checked')) {
            selectionCount++;
            // var optionIndex = parseInt($(thisObj).closest('.flight-listing-row').attr('id').split('-')[1];
            if (!$(this).find('.skipCheckbox .mdl-checkbox__input').prop('checked') && $(this).hasClass('selectionActive')) {
                var optionIndex = $(this).find('.card-section-body').find('ul').find('.flight-listing-row.selected').attr('id').split('-')[1];
                var eventIndex = parseInt($(this).attr('id').split('-')[1]);
                selectedFlightsIndex['event-' + eventIndex] = optionIndex;
            }
        }
        if (selectionCount == selectionArrayLength) {
            if ($(thisObj).closest('.card-body').find('.skipCheckbox .mdl-checkbox__input:checked').length == selectionArrayLength) {
                $('.travellersCard').addClass('inactive');
                if (!skipAlertShown){
                    alert('Atleast one selection is necessary');
                    skipAlertShown = true;
                }
            } else {
                // $(thisObj).closest('.card').next('.card').removeClass('inactive');

                if(!reminderMode) notifyAgent(true);
                // $('.total-amount').hide();
                $('.travellersCard').addClass('inactive');
                selectionComplete = true;
            }
        } else {
            // $(thisObj).closest('.card').next('.card').addClass('inactive');
            // $('.total-amount').hide();
            $('.travellersCard').addClass('inactive');
        }
    });
    console.log(selectedFlightsIndex);
}
function delayedScrollToTravellerDetails(i){
    // if(!$('#passenger'+i+'accordion').offset()) return;
    // var scrollToPos =  $('#passenger'+i+'accordion').offset().top;
    //             $('html, body').animate({
    //                 scrollTop: scrollToPos
    //             }, 1200);
}
function animateScrollToTravellerDetails(i){
    // if (selectionCompleted()){
    //     console.log('Animate: animateScrollToTravellerDetails');
    //     // setTimeout(function(){delayedScrollToTravellerDetails(i)}, 500);
    // }
}
function showTravellersCard(){
    $('.travellersCard').removeClass('inactive');
    // $('.total-amount').show();
}

function hideTravellersCard(){
    // $('.total-amount').hide();
    $('.travellersCard').addClass('inactive');
}
function changeSelectionNew(thisObj){
    var eIndex = $(thisObj).attr('name');
    changeSelection($('#flight'+eIndex+'ChangeLink'));
}

function autoChangeSelection(eventIndex){
    console.log('autoChangeSelection(flight'+eventIndex+'ChangeLink)');
    changeSelection($('#flight'+eventIndex+'ChangeLink'));
}
function selectRowNew(thisObj){
    var flightRowLeftRef = 'flightRowLeft-'+$(thisObj).attr('name');
    selectRow($('#'+flightRowLeftRef));
}

function changeSelection(thisObj) {
    $(thisObj).closest('.card-section').find('.flight-listing-row').not($(thisObj).closest('.card-section').find('.flight-listing-row.selected')).slideDown();
    $(thisObj).closest('.card-section').find('.flight-listing-row.selected').removeClass('selected');
    $(thisObj).closest('.card-section').removeClass('selectionActive');
    $(thisObj).closest('.card-section').find('.toggle-link').show();
    $(thisObj).closest('.card-section').find('.changeButton').hide();
    var selectionArrayLength = $(thisObj).closest('.card-body').find('.card-section').length;
    var selectionCount = 0;
    if (selectionCount != selectionArrayLength) {
        // $('.total-amount').hide();
    }

    getTopSelection(thisObj);
}

function validatePersonalDetailsForm(thisObj) {
    var formValidArray = [];
    $('.personalDetailsForm').each(function () {
        var id = $(this).attr('id');
        formValidArray.push(userFormValidation(id));
    });
    var isFormValid = formValidArray.every(Boolean);
    if (isFormValid) {
        return true;
    } else {
        return false;
    }
    return false;
}


function userFormValidation(formId) {
    var validator = $("#" + formId).validate({
        //            focusInvalid: true,
        rules: {
            firstName: {
                required: true
            },
            gender: {
                required: true
            },
            dob: {
                required: true,
            },
            email: {
                required: true,
                email: true
            },
            homeZip: {
                required: true,
                alphanumeric: true
            },

            flyerNumber: {
                require_from_group: [1, ".flyer-group"]
            },
            flyerNumberCheckbox: {
                require_from_group: [1, ".flyer-group"]
            },
            travellerNumber: {
                alphanumeric: true,
                minlength: 9,
                maxlength: 9,
                require_from_group: [1, ".traveller-group"]
            },
            knownTravellerCheckbox: {
                require_from_group: [1, ".traveller-group"]
            },
            guestNumber: {
                require_from_group: [1, ".guest-group"]
            },
            guestNumberCheckbox: {
                require_from_group: [1, ".guest-group"]
            },
            passportNumber: {
                required: true,
            },
            passportExpiryDate: {
                required: true,
            },
            nationality: {
                required: true,
            },
            country: {
                required: true,
            }
        },
        messages: {
            travellerNumber: {
                minlength: "Enter {0} charactor value",
                maxlength: "Enter {0} charactor value",
            },
        },
        errorPlacementForScriptjs: function (error, element) {
            errorPlacementForScriptjs(error, element);
        }
    });

    var isFormValid = $("#" + formId).valid();
    if (isFormValid) {
        return true;
    } else {
        validator.focusInvalid();
        return false;
    }
    return false;
}




function toggleForm(thisObj) {
    //    if ($(thisObj).closest('.traveller-form').index() == ($('.traveller-form').length - 1)) {
    if (!($(thisObj).closest('.traveller-form').hasClass('traveller-form-filled'))) {
        return false;
    } else {
        if ($(thisObj).closest('.traveller-form').hasClass('traveller-form-closed')) {
            $(thisObj).closest('.traveller-form').removeClass('traveller-form-closed');
            $(thisObj).closest('.traveller-form').find('.card-body').slideDown();
            $(thisObj).closest('.card').find('.card-footer').slideDown();
            $(thisObj).find('.traveller-short-info').html('');
        } else {

            var formId = $(thisObj).closest('.traveller-form').find('.personalDetailsForm').attr('id');
            if (userFormValidation(formId)) {
                $(thisObj).closest('.traveller-form').addClass('traveller-form-closed');
                $(thisObj).closest('.traveller-form').find('.card-body').hide();
                var dateData = new Date($(thisObj).closest('.traveller-form').find('.dob').val());
                var dateValue = moment(dateData).format('MMM DD,YYYY');
                var KTNvalue = $(thisObj).closest('.traveller-form').find('.travellerNumber').val();
                var FFNvalue = $(thisObj).closest('.traveller-form').find('.flyerNumber').val();

                var travellerShortDetails = travellerShortDetailsTemplate(dateValue, KTNvalue, FFNvalue);
                $(thisObj).find('.traveller-short-info').html(travellerShortDetails);
            }

            if ($('.traveller-form').not('.traveller-form.traveller-form-closed').length == 0) {
                $(thisObj).closest('.card').find('.card-footer').hide();
            }


        }
    }
}


function travellerShortDetailsTemplate(dateValue, KTNvalue, FFNvalue) {
    var template = ``;
    if (dateValue != "") {
        template += `<span class="travellerShortDetailsText">DOB-${dateValue}</span>`;
    }
    if (KTNvalue != "") {
        template += `<span class="seperator">|</span>
                    <span class="travellerShortDetailsText">KTN-${KTNvalue}</span>`;
    }
    if (FFNvalue != "") {
        template += `<span class="seperator">|</span>
                <span class="travellerShortDetailsText">FFN-${FFNvalue}<span>`;
    }
    return template;
}


function addNewTraveller(thisObj) {
    var formCount = $('.traveller-form').length;
    if (validatePersonalDetailsForm(thisObj)) {

        $('.traveller-form.traveller-form-filled').each(function () {
            if (!($(this).hasClass('traveller-form-closed'))) {
                updateTopInfo(this);
                $(this).find('.card-header').click();
            }
        });






        var travellerBase = $(thisObj).closest('.card').find('.traveller-form:last-child');
        var travellerName = travellerBase.find('.title').val() + " " + travellerBase.find('.firstName').val() + " " + travellerBase.find('.middleName').val() + " " + travellerBase.find('.lastName').val();
        var dateData = new Date(travellerBase.find('.dob').val());
        var dateValue = moment(dateData).format('MMM DD,YYYY');
        var KTNvalue = travellerBase.find('.travellerNumber').val();
        var FFNvalue = travellerBase.find('.flyerNumber').val();
        var travellerShortDetails = travellerShortDetailsTemplate(dateValue, KTNvalue, FFNvalue);
        travellerBase.find('.card-header h3').html(travellerName);
        travellerBase.find('.traveller-short-info').html(travellerShortDetails);
        travellerBase.find('.card-body').hide();
        travellerBase.addClass('traveller-form-closed traveller-form-filled');



        if (formCount < noOfTravellers) {
            $(thisObj).closest('.card').find('.card-inner').append(travellerform());



            console.log('Animate: addNewTraveller');
            // $('html, body').animate({
            //     scrollTop: $(".traveller-form").offset().top
            // }, 2000);
            componentHandler.upgradeDom();
            $(".phoneNumber").intlTelInput();
            initilizeDatepicker();


            $('.traveller-form').each(function () {
                $(this).find('.card-header .form-number').text(($(this).index() + 1));
            });
            if ($('.traveller-form').length >= 2) {
                $('.traveller-form .removeTravellerButton').removeClass('hidden');
                $('.customer-count').removeClass('hidden');
                $('.customer-count-number').text($('.traveller-form').length);
                $('.card-footer').addClass('multipleChild');
            } else {
                $('.card-footer').removeClass('multipleChild');
            }

            $('.googleAddress').each(function () {
                var id = $(this).attr('id');
                initilizeGoogleAddressTextbox(id);
            });

            $('.js-example-basic-single').select2();
        } else {
            $(thisObj).closest('.card').find('.card-footer').hide();
            formNextStep(thisObj);
        }










    }
}

function formNextStep(thisObj) {
    $('#expensifyCard').removeClass('inactive');
    $('#paymentButton').show();
}

function initilizeDatepicker($this) {
    $('.dob').datepicker({
        changeMonth: true,
        changeYear: true,
        minDate: "-200Y",
        maxDate: "today",
        shortYearCutoff: 1,
        yearRange: "1800:3000",
        onSelect: function (event, ui) {
            $(this).closest('.input-box').find('span.error').hide();
        }
    });

    $('.passportDate').datepicker({
        changeMonth: true,
        changeYear: true,
        minDate: "today",
        maxDate: "+20Y",
        shortYearCutoff: 1,
        yearRange: "1800:3000",
        onSelect: function (event, ui) {
            $(this).closest('.input-box').find('span.error').hide();
        }
    });
}
function convertDataToJSON1(data, separator) {
    if (data && data.trim().length > 0) {
        var reportData = [];
            var dataLines = data.split("\n");
        var dataTypeObject = {};
        var indexTypeMap = {};
        var dataTypeParts = dataLines[0].split(separator);
            for (var cols = 0; cols < dataTypeParts.length; cols++) {
                var def = {};
                def.type = 'level';
                dataTypeParts[cols] = dataTypeParts[cols].trim();
                if (dataTypeParts[cols].indexOf('+') == 0) {
                    def.type = 'date string';
                    dataTypeObject[dataTypeParts[cols].substring(1)] = def;
                }
                else {
                    dataTypeObject[dataTypeParts[cols]] = def;
                }
                indexTypeMap[cols] = def.type;
            }
            reportData.push(dataTypeObject);
            for (var lines = 1; lines < dataLines.length; lines++) {
                if (dataLines[lines] !== "") {
                    var dataObject = {};
                    var dataLineParts = dataLines[lines].split(separator);
                    for (var cols = 0; cols < dataLineParts.length; cols++) {
                        if (indexTypeMap[cols] == "number") {
                            dataObject[dataTypeParts[cols].substring(1)] = dataLineParts[cols];
                        } else if (indexTypeMap[cols] == "date string") {
                            dataObject[dataTypeParts[cols].trim().substring(1)] = dataLineParts[cols];
                        } else {
                            dataObject[dataTypeParts[cols]] = dataLineParts[cols];
                        }
                    }
                    reportData.push(dataObject);
                }
            }
        return reportData;
    }
    return null;
}

function updateTopInfo(thisObj) {
    if ($(thisObj).closest('.traveller-form').hasClass('traveller-form-filled')) {
        var travellerBase = $(thisObj).closest('.traveller-form');
        var travellerName = travellerBase.find('.title').val() + " " + travellerBase.find('.firstName').val() + " " + travellerBase.find('.middleName').val() + " " + travellerBase.find('.lastName').val();
        travellerBase.find('.card-header h3').html(travellerName);


        var dateData = new Date(travellerBase.find('.dob').val());
        var dateValue = moment(dateData).format('MMM DD,YYYY');
        var KTNvalue = travellerBase.find('.travellerNumber').val();
        var FFNvalue = travellerBase.find('.flyerNumber').val();
        var travellerShortDetails = travellerShortDetailsTemplate(dateValue, KTNvalue, FFNvalue);

        travellerBase.find('.traveller-short-info').html(travellerShortDetails);

    }





}

function enableExpensifyTextbox() {
    if ($("#expensifyLoginEmailCheckbox").prop('checked')) {
        $('#expensifyLoginEmail').removeClass('disabled');
    } else {
        $('#expensifyLoginEmail').addClass('disabled');
        $('#expensifyLoginEmail').val('');
    }
}

var currentHotelEventData = {}
var totalNumberOfCount = 0;
var hotelData = [];
var currentHotelIndex = {};

function processQuotationResponseInternal(emailQuoteOption) {
    if (emailQuoteOption) {
        searchResultsData = emailQuoteOption;
    } else {
        return;
    }
    currentHotelEventData = {}
    currentHotelIndex = {};
    let eventListTypeMap = [];
    hotelData = [];
    var newEventList = [];
    if (!("eventList" in searchResultsData)) {
        var eventList = []
        var eventIdAndTypeMap = {};

        if (searchResultsData.multiFlightOptions !== undefined && searchResultsData.multiFlightOptions.length > 0) {
            for (flightItr in searchResultsData.multiFlightOptions) {
                eventList.push(searchResultsData.multiFlightOptions[flightItr][0][0].eventId)
                eventIdAndTypeMap[searchResultsData.multiFlightOptions[flightItr][0][0].eventId] = 'flight';
            }
        }
        if (searchResultsData.hotelOptionsList !== undefined && searchResultsData.hotelOptionsList.length > 0) {
            for (hotelItr in searchResultsData.hotelOptionsList) {
                eventList.push(searchResultsData.hotelOptionsList[hotelItr][0].eventId)
                eventIdAndTypeMap[searchResultsData.hotelOptionsList[hotelItr][0].eventId] = 'hotel';
            }
        }
        if (searchResultsData.carOptionsList !== undefined && searchResultsData.carOptionsList.length > 0) {
            for (carItr in searchResultsData.carOptionsList) {
                eventList.push(searchResultsData.carOptionsList[carItr][0].eventId)
                eventIdAndTypeMap[searchResultsData.carOptionsList[carItr][0].eventId] = 'cars';
            }
        }
        searchResultsData.eventList = eventList;
        searchResultsData.eventIdAndTypeMap = eventIdAndTypeMap;
    }


    for (eventItr in searchResultsData.eventList) {
        var eventId = searchResultsData.eventList[eventItr];
        let eventData = {}
        eventData.eventId = eventId;
        eventData.eventType = searchResultsData.eventIdAndTypeMap[eventId];
        if (eventId in searchResultsData.eventIdAndTypeMap && searchResultsData.eventIdAndTypeMap[eventId] === 'hotel') {
            if (searchResultsData.hotelOptionsList.length > 0) {
                hotelData = searchResultsData.hotelOptionsList;
                for (i = 0; i < hotelData.length; i++) {

                    if (!(hotelData[i][0].eventId in currentHotelEventData)) {
                        currentHotelEventData[hotelData[i][0].eventId] = hotelData[i]
                    }
                }
                travellerData = hotelData[0][0].hotelRooms[0].hotelRates[0].roomOccupancyDTO;
                var priceText = "";
                var averageNumber = "";
                if (travellerData.length == 1) {
                    if (hotelData[0][0].stay > 1) {
                        priceText = "per night"
                        averageNumber = hotelData[0][0].stay;
                    }
                }
                else if (travellerData.length > 1) {
                    if (hotelData[0][0].stay == 1) {
                        priceText = "per room";
                        averageNumber = travellerData.length;
                    } if (hotelData[0][0].stay > 1) {
                        priceText = "per room per night"
                        averageNumber = travellerData.length * hotelData[0][0].stay;
                    }
                }
                else {
                    averageNumber = hotelData[0].stay;
                }

                var text = " ";
                if (travellerData != null && travellerData.length > 0) {
                    var roomsCount = travellerData.length;
                    var adultCount = 0;
                    var childCount = 0;
                    for (var i = 0; i < roomsCount; i++) {
                        adultCount += travellerData[i].numberOfAdult;
                        childCount += travellerData[i].numberOfChildren;
                    }
                    if (adultCount + childCount > 1) {
                        text = ((roomsCount > 0) ? (roomsCount + " Room" + ((roomsCount > 1) ? "s" : "")) : "")
                            + ((adultCount > 0) ? (((roomsCount > 0) ? ", " : "") + adultCount + " Adult" + ((adultCount > 1) ? "s" : "")) : "")
                            + ((childCount > 0) ? (", " + childCount + ((childCount > 1) ? " Children" : " Child")) : "");
                    }
                }


                eventData.travellersBriefDesc = text;
                eventData.locationAddress = currentHotelEventData[eventId][0].appointmentAddress;

                for (itr = 0; itr < searchResultsData.hotelOptionsList.length; itr++) {
                    if (searchResultsData.hotelOptionsList[itr][0].eventId === eventId) {
                        var j = itr;
                        for (i = 0; i < searchResultsData.hotelOptionsList[j].length; i++) {

                            searchResultsData.hotelOptionsList[j][i].index = i + 1;
                            var netGSA = searchResultsData.hotelOptionsList[j][i].gsaRate * searchResultsData.hotelOptionsList[j][i].stay;
                            if (netGSA >= searchResultsData.hotelOptionsList[j][i].price) {
                                searchResultsData.hotelOptionsList[j][i].GSA = true;
                            }
                            else {
                                searchResultsData.hotelOptionsList[j][i].GSA = false;
                            }
                            var averagePrice = parseFloat(searchResultsData.hotelOptionsList[j][i].price) / averageNumber;
                            if (priceText !== "") {
                                averagePrice = Math.round(averagePrice * 100) / 100;

                                searchResultsData.hotelOptionsList[j][i].priceText = "( Average $" + averagePrice + " " + priceText + " )";
                            }
                            var starsAvailable = [1, 2, 3, 4, 5];
                            searchResultsData.hotelOptionsList[j][i].starData = "";
                            var starAvailable = parseInt(searchResultsData.hotelOptionsList[j][i].category);
                            for (var k = 0; k < starsAvailable.length; k++) {
                                if (starAvailable >= starsAvailable[k])
                                    searchResultsData.hotelOptionsList[j][i].starData += '<img style="display: inline-block;float: left;" src="https://s3.amazonaws.com/images.biztravel.ai/fts-template/hotels/star-fill.jpg" />';
                                else
                                    searchResultsData.hotelOptionsList[j][i].starData += '<img style="display: inline-block;float: left;" src="https://s3.amazonaws.com/images.biztravel.ai/fts-template/hotels/star.jpg" />';

                            }

                        }
                        eventData.eventOptions = searchResultsData.hotelOptionsList[j];
                        break;
                    }
                }

            }
        }  else if (eventId in searchResultsData.eventIdAndTypeMap && searchResultsData.eventIdAndTypeMap[eventId] === 'cars') {
            let totalNumberOfCount = 1;
            for (optionItr in searchResultsData.carOptionsList) {
                // var val = optionItr;
                console.log("javascriptFile123",searchResultsData.carOptionsList[optionItr],searchResultsData.carOptionsList[optionItr][0].eventId,eventId);
                if (searchResultsData.carOptionsList[optionItr][0].eventId === eventId) {
                    eventData.travellersBriefDesc = totalNumberOfCount + 'Passengers';
                    if(searchResultsData.carOptionsList[optionItr][0].pickUpLocation){
                    const [loc1,loc2,loc3,loc4,loc5] = searchResultsData.carOptionsList[optionItr][0].pickUpLocation.split(',');
                      if(searchResultsData.carOptionsList[optionItr][0].destinationCity && searchResultsData.carOptionsList[optionItr][0].destinationCity !==''){
                        eventData.locationAddress = searchResultsData.carOptionsList[optionItr][0].destinationCity ; 
                    }else if(searchResultsData.carOptionsList[optionItr][0].airportCity && searchResultsData.carOptionsList[optionItr][0].airportCity !==''){
                        eventData.locationAddress = searchResultsData.carOptionsList[optionItr][0].airportCity ; 
                    }else{
                    if(loc5){
                    var location = loc2+","+loc3 + ","+loc5;
                    }else{
                        var location = loc2+","+loc4;  
                    }
                    eventData.locationAddress = location;
                }
            }
                    eventData.eventOptions = searchResultsData.carOptionsList[optionItr];
                    break;
                }
            }
        } 
        else if ("flightSearchOptions" in searchResultsData && searchResultsData.flightSearchOptions) {
            let totalNumberOfCount = searchResultsData.flightSearchOptions.adultCount + searchResultsData.flightSearchOptions.infantCount + searchResultsData.flightSearchOptions.childCount
            for (optionItr in searchResultsData.multiFlightOptions) {
                // var val = optionItr;
                if (searchResultsData.multiFlightOptions[optionItr][0][0].eventId === eventId) {
                    eventData.travellersBriefDesc = totalNumberOfCount + 'Passengers';
                    eventData.locationAddress = searchResultsData.multiFlightOptions[optionItr][0][0].destinationText;
                    eventData.eventOptions = searchResultsData.multiFlightOptions[optionItr];
                    // for (var multidata=0; multidata< searchResultsData.multiFlightOptions[val].length;multidata++){
                    // if(multidata==0){
                    // 	searchResultsData.multiFlightOptions[val][multidata].isPrimary = true;
                    // }
                    // else{
                    // 	searchResultsData.multiFlightOptions[val][multidata].isPrimary = false;
                    // }
                    // searchResultsData.multiFlightOptions[val][multidata].priceText = "";


                    // 	if(searchResultsData.multiFlightOptions[val][multidata].length>1 && searchResultsData.multiFlightOptions[val][multidata][0].from_airport_code === searchResultsData.multiFlightOptions[val][multidata][1].to_airportCode){
                    // 		searchResultsData.multiFlightOptions[val][multidata].isRoundTrip  = true;
                    // 	}

                    // 	else{
                    // 		searchResultsData.multiFlightOptions[val][multidata].isRoundTrip  = false;
                    // 	}
                    // 	if(searchResultsData.leg2Destination===" "){
                    // 		searchResultsData.multiFlightOptions[val][multidata].isRoundTrip  = true;
                    // 	}
                    // if(totalNumberOfCount>1){

                    // 	var num  = 0;
                    // 	if('discountedPrice' in searchResultsData.multiFlightOptions[val][multidata]){
                    // 		 num  =  searchResultsData.multiFlightOptions[val][multidata].discountedPrice/totalNumberOfCount;
                    // 	}
                    // 	else{
                    // 		 num  =  searchResultsData.multiFlightOptions[val][multidata].price/totalNumberOfCount;
                    // 	}

                    // 	searchResultsData.multiFlightOptions[val][multidata].priceText = "( Average $" +Math.round(num* 100) / 100 + " per person )";
                    // }

                    // if(searchResultsData.multiFlightOptions[val][multidata].length>=2){
                    // 	for(var intD=0;intD<searchResultsData.multiFlightOptions[val][multidata].length;intD++){

                    // 		searchResultsData.multiFlightOptions[val][multidata][intD].priceText = "";
                    // 		if( !("tripType" in  searchResultsData.multiFlightOptions[val][multidata][intD]) || searchResultsData.multiFlightOptions[val][multidata][intD].tripType==="ROUND_TRIP"){
                    // 		if(intD==0){
                    // 			searchResultsData.multiFlightOptions[val][multidata][intD].flightLabel = "DEPARTURE";
                    // 		}else{
                    // 			searchResultsData.multiFlightOptions[val][multidata][intD].flightLabel ="RETURN";
                    // 		}
                    // 		}
                    // 		else{
                    // 			searchResultsData.multiFlightOptions[val][multidata][intD].flightLabel = searchResultsData.multiFlightOptions[val][multidata][intD].num_stops + " Flight"
                    // 		}

                    // 	}
                    // }
                    // else{
                    // 	searchResultsData.multiFlightOptions[val][multidata][0].flightLabel = searchResultsData.multiFlightOptions[val][multidata][0].num_stops + " Flight"
                    // }
                    // }
                    break;
                }
            }
        }
       
        eventListTypeMap.push(eventData);
    }
    console.log("emailflowquotations",eventListTypeMap);
    searchResultsData.eventListTypeMap = eventListTypeMap;
    return eventListTypeMap;

}

function generateUUID() {
	  var text = "";
	  var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

	  for (var i = 0; i < 5; i++)
	    text += possible.charAt(Math.floor(Math.random() * possible.length));

	  return text;
}

var sessionid = "SESSIONID";
var utm_source = "utm_source";
var utm_medium = "utm_medium";
var utm_campaign = "utm_campaign";

function getSessionID(){
  return sessionid;
}
function isCurrentUrlADashboardUrl(){
    if (window.location.href.replace("https://", "").startsWith("dashboard") && window.location.href.indexOf("/search") == -1){
        return true;
    }
    return false;
}

function stopEventsBehindCal(){
    $(".content").css('pointerEvents', 'none');
}
function startEventsBehindCal(){
    $(".content").css('pointerEvents', 'auto');
}

function updateAddToExpensifyUICheckbox(value){
    if (value === true){
        $('.expensifyLoginEmailCheckboxDiv').each(function (index, element) { 
            element.MaterialCheckbox.check();
        })
        $(".expensifyLoginEmailCheckboxDiv").addClass("is_checked");
    }else{
        $('.expensifyLoginEmailCheckboxDiv').each(function (index, element) { 
            element.MaterialCheckbox.uncheck();
        })
        $(".expensifyLoginEmailCheckboxDiv").removeClass("is_checked");
    }


}
function travellerform() {
    var c = $('.traveller-form').length + 1;
    return `<div class="traveller-form">
                                    <div class="card-header" onclick="toggleForm(this);">
                                        <span class="form-number"></span>
                                        <div>
                                            <h3>Personal Details</h3>
                                            <span class="form-arrow-down"><img src="images/arrow-down1.png" alt="" /></span>
                                            <div class="traveller-short-info"></div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <form method="post" id="personalDetailsForm${c}" class="personalDetailsForm">
                                            <div class="row">
                                                <div class="col-lg-2 col-md-3 col-sm-4">
                                                    <div class="input-box">
                                                        <label class="input-label">Title</label>
                                                        <div class="select-box title-select-box">
                                                            <select class="input-textfield title" id="title${c}" name="title" onblur="updateTopInfo(this);">
                                                                <option>Mr.</option>
                                                                <option>Mrs.</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                                    <div class="input-box input-box-multiple">
                                                        <label class="input-label">Name</label>
                                                        <div class="row">
                                                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                                                <input class="input-textfield firstName" id="firstName${c}" name="firstName" type="text" placeholder="First Name" onblur="updateTopInfo(this);" />
                                                            </div>
                                                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                                                <input class="input-textfield middleName" id="middleName${c}" name="middleName" type="text" placeholder="Middle Name (If shown on id)" onblur="updateTopInfo(this);" />
                                                            </div>
                                                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                                                <input class="input-textfield lastName" id="lastName${c}" name="lastName" type="text" placeholder="Last Name" onblur="updateTopInfo(this);" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Suffix</label>
                                                        <input class="input-textfield" id="suffix${c}" name="suffix" type="text" placeholder="" />
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Gender</label>
                                                        <div class="checkbox-container">
                                                            <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="male${c}">
                                                                <input type="radio" id="male${c}" class="mdl-radio__button" name="gender" value="male">
                                                                <span class="mdl-radio__label">Male</span>
                                                            </label>
                                                            <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="female${c}">
                                                                <input type="radio" id="female${c}" class="mdl-radio__button" name="gender" value="female">
                                                                <span class="mdl-radio__label">Female</span>
                                                            </label>
                                                            <label class="mdl-radio mdl-js-radio mdl-js-ripple-effect" for="other${c}">
                                                                <input type="radio" id="other${c}" class="mdl-radio__button" name="gender" value="other">
                                                                <span class="mdl-radio__label">Other</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Date of Birth</label>
                                                        <input style="cursor: pointer;" class="input-textfield dob datepicker" id="dob${c}" name="dob" type="tel" placeholder="mm/dd/yyyy" readonly/>
                                                    </div>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Email Id</label>
                                                        <input class="input-textfield" id="email${c}" name="email" type="text" placeholder="Email Id" />
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Phone Number</label>
                                                        <input class="input-textfield phoneNumber" type="tel" id="number${c}" name="number" placeholder="0000000000" />
                                                    </div>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-lg-8 col-md-9 col-sm-9 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Home Address</label>
                                                        <textarea class="input-textfield googleAddress" id="homeAddress${c}" name="homeAddress" placeholder="Write your home address here.."></textarea>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4  col-md-3 col-sm-3 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Zip Code</label>
                                                        <input class="input-textfield" type="text" id="homeZip${c}" name="homeZip" placeholder="Area Zip Code" />
                                                    </div>
                                                </div>
                                                <div class="col-lg-8 col-md-9 col-sm-9 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Work Address</label>
                                                        <textarea class="input-textfield googleAddress" id="workAddress${c}" name="workAddress" placeholder="Write your work address here.."></textarea>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4  col-md-3 col-sm-3 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Zip Code</label>
                                                        <input class="input-textfield" type="text" id="workZip${c}" name="workZip" placeholder="Area Zip Code" />
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box input-with-checkbox">
                                                        <label class="input-label">Known Traveler Number</label>
                                                        <input class="input-textfield travellerNumber traveller-group" id="travellerNumber${c}" name="travellerNumber" type="text" placeholder="KTN Number" onchange="changeInput(this);" />
                                                        <label class="mdl-checkbox input-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="knownTravellerCheckbox${c}">
                                                            <input type="checkbox" id="knownTravellerCheckbox${c}" name="knownTravellerCheckbox" value="knownTravellerCheckbox" class="mdl-checkbox__input traveller-group" onchange="changeInput(this);">
                                                            <span class="mdl-checkbox__label">Not Available</span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box input-with-checkbox">
                                                        <label class="input-label">Frequent flyer number:</label>
                                                        <input class="input-textfield flyerNumber flyer-group" id="flyerNumber${c}" name="flyerNumber" type="text" placeholder="Flyer Number" onchange="changeInput(this);" />
                                                        <label class="mdl-checkbox input-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="flyerNumberCheckbox${c}">
                                                            <input type="checkbox" id="flyerNumberCheckbox${c}" name="flyerNumberCheckbox" value="flyerNumberCheckbox" class="mdl-checkbox__input flyer-group" onchange="changeInput(this);">
                                                            <span class="mdl-checkbox__label">Not Available</span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box input-with-checkbox">
                                                        <label class="input-label">Frequent guest number:</label>
                                                        <input class="input-textfield guestNumber guest-group" id="guestNumber${c}" name="guestNumber" type="text" placeholder="Flyer Number" onchange="changeInput(this);" />
                                                        <label class="mdl-checkbox input-checkbox mdl-js-checkbox mdl-js-ripple-effect" for="guestNumberCheckbox${c}">
                                                            <input type="checkbox" id="guestNumberCheckbox${c}" name="guestNumberCheckbox" value="guestNumberCheckbox" class="mdl-checkbox__input guest-group" onchange="changeInput(this);">
                                                            <span class="mdl-checkbox__label">Not Available</span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                                    <div class="card-sec-header">
                                                        <h3>Passport</h3>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4  col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Passport Number</label>
                                                        <input class="input-textfield input-textfield-lg" type="text" id="passportNumber${c}" name="passportNumber" placeholder="Passport Number" />
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Passport Expiry Date</label>
                                                        <input style="cursor: pointer;" class="input-textfield input-textfield-lg passportDate datepicker" id="passportExpiryDate${c}" name="passportExpiryDate" type="tel" placeholder="mm/dd/yyyy" readonly/>
                                                    </div>
                                                </div>
                                                <div class="clearfix"></div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Nationality</label>
                                                        <div class="select-box">
                                                            <select class="input-textfield js-example-basic-single input-textfield-lg title" id="nationality${c}" name="nationality">
                                                                ${countryOptionsTemplate}
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                                                    <div class="input-box">
                                                        <label class="input-label">Country</label>
                                                        <div class="select-box">
                                                            <select class="input-textfield js-example-basic-single input-textfield-lg title" id="country${c}" name="country">
                                                                ${countryOptionsTemplate}
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>`;
}
function getTraflaHotelChainCode(inputChain){
    return inputChain;
}
function getTraflaHotelChainCodeOld(inputChain){
	switch(inputChain){
	case "SW":
	case "AK":
	case "BG":
	case "EB":
	case "RZ":
	case "XR":
	case "MC":
	case "BR":
	case "TX":
	case "WH":
	case "WI":
	case "CY":
	case "GE":
	case "MD":
	case "RC":
	case "SI":
	case "AR":
	case "AL":
	case "EL":
	case "FN":
	case "XV":
	case "TO":
	case "OX":
	case "PR":
		return "SW";
	case "HL":
	case "ES":
	case "DT":
	case "HX":
	case "GI":
	case "HG":
	case "HT":
	case "CN":
	case "RU":
		return "HL";
	case "IC":
	case "HI":
	case "CP":
	case "YZ":
	case "YO":
		return "IC";
	case "WY":
	case "DX":
	case "RA":
	case "BU":
	case "HJ":
	case "OZ":
	case "DI":
	case "BH":
	case "KG":
	case "MT":
	case "TL":
	case "WG":
		return "WY";
	case "ACC":
	case "BY":
	case "FA":
	case "SB":
	case "PU":
		return "ACC";
	case "UB":
	case "CC":
	case "CI":
	case "EO":
	case "QI":
	case "RI":
	case "SZ":
		return "UB";
	case "BW":
		return "BW";
	case "HY":
		return "HY";
	case "CW":
		return "CW";
	case "BOUH":
		return "BOUH";
	}
	return inputChain;
}
$("img").on('error', function() {
    $(this).attr('src', 'https://s3.amazonaws.com/airlines.images.biztravel.ai/icons/transparent-pixel.png');    // show a fallback image if there is an error
});
function getAirlineImageWithFallback(airlineCode){
    if (gallopAirlineIcons[airlineCode]){
        return 'https://s3.amazonaws.com/airlines.images.biztravel.ai/icons/' + airlineCode + '_128x128.png';
    }
    if (travelportAirlineIcons[airlineCode]){
        return 'https://goprivate.wspan.com/sharedservices/images/airlineimages/logoAir' + airlineCode + '.gif';
    }
    return 'data:image/svg+xml;base64,PHN2ZyBzdHlsZT0id2lkdGg6IDE1cHg7IGNvbG9yOiByZ2IoOTksOTksOTkpOyIgYXJpYS1oaWRkZW49IiB0cnVlIiBmb2N1c2FibGU9ImZhbHNlIiBkYXRhLXByZWZpeD0iZmFzIiBkYXRhLWljb249InBsYW5lLWRlcGFydHVyZSIgcm9sZT0iaW1nIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2NDAgNTEyIiBjbGFzcz0ic3ZnLWlubGluZS0tZmEgZmEtcGxhbmUtZGVwYXJ0dXJlIGZhLXctMjAgZmEtM3ggd2RyLXVpLWVsZW1lbnQiPjxwYXRoIGZpbGw9ImN1cnJlbnRDb2xvciIgZD0iTTYyNCA0NDhIMTZjLTguODQgMC0xNiA3LjE2LTE2IDE2djMyYzAgOC44NCA3LjE2IDE2IDE2IDE2aDYwOGM4Ljg0IDAgMTYtNy4xNiAxNi0xNnYtMzJjMC04Ljg0LTcuMTYtMTYtMTYtMTZ6TTgwLjU1IDM0MS4yN2M2LjI4IDYuODQgMTUuMSAxMC43MiAyNC4zMyAxMC43MWwxMzAuNTQtLjE4YTY1LjYyIDY1LjYyIDAgMCAwIDI5LjY0LTcuMTJsMjkwLjk2LTE0Ny42NWMyNi43NC0xMy41NyA1MC43MS0zMi45NCA2Ny4wMi01OC4zMSAxOC4zMS0yOC40OCAyMC4zLTQ5LjA5IDEzLjA3LTYzLjY1LTcuMjEtMTQuNTctMjQuNzQtMjUuMjctNTguMjUtMjcuNDUtMjkuODUtMS45NC01OS41NCA1LjkyLTg2LjI4IDE5LjQ4bC05OC41MSA0OS45OS0yMTguNy04Mi4wNmExNy43OTkgMTcuNzk5IDAgMCAwLTE4LTEuMTFMOTAuNjIgNjcuMjljLTEwLjY3IDUuNDEtMTMuMjUgMTkuNjUtNS4xNyAyOC41M2wxNTYuMjIgOTguMS0xMDMuMjEgNTIuMzgtNzIuMzUtMzYuNDdhMTcuODA0IDE3LjgwNCAwIDAgMC0xNi4wNy4wMkw5LjkxIDIzMC4yMmMtMTAuNDQgNS4zLTEzLjE5IDE5LjEyLTUuNTcgMjguMDhsNzYuMjEgODIuOTd6IiBjbGFzcz0iIj48L3BhdGg+PC9zdmc+';
}
function fn_init_hotjar(h,o,t,j,site_id){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:site_id,hjsv:6};
        var a=o.getElementsByTagName('head')[0];
        var r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
}
function getHotjarUserId(){
    try{
        if (hj && hj.globals && hj.globals.get("userId")){
            return  hj.globals.get("userId").split("-")[0]
        }
    }catch(err){}
    return undefined;
}
function initializeHotJar(site_id){
    fn_init_hotjar(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=', site_id);
}

var gallopAirlineIcons = {
    "VA":true,
    "VS":true,
    "VX":true,
    "ZL":true,
    "YO":true,
    "XY":true,
    "WY":true,
    "WS":true,
    "WN":true,
    "WB":true,
    "US":true,
    "UA":true,
    "U2":true,
    "TN":true,
    "TL":true,
    "TG":true,
    "TB":true,
    "SX":true,
    "ST":true,
    "SS":true,
    "SN":true,
    "SK":true,
    "SA":true,
    "RO":true,
    "QR":true,
    "PR":true,
    "PG":true,
    "OV":true,
    "OU":true,
    "OA":true,
    "NK":true,
    "NF":true,
    "ML":true,
    "MI":true,
    "MH":true,
    "ME":true,
    "LY":true,
    "LO":true,
    "KM":true,
    "KL":true,
    "JU":true,
    "JQ":true,
    "JP":true,
    "JE":true,
    "J9":true,
    "IZ":true,
    "IY":true,
    "IR":true,
    "IG":true,
    "HV":true,
    "HR":true,
    "HA":true,
    "GL":true,
    "GF":true,
    "G4":true,
    "FJ":true,
    "F9":true,
    "EY":true,
    "ET":true,
    "DY":true,
    "DL":true,
    "DE":true,
    "DD":true,
    "CY":true,
    "BT":true,
    "BI":true,
    "BA":true,
    "B6":true,
    "AY":true,
    "AS":true,
    "AM":true,
    "AC":true,
    "AB":true,
    "AA":true,
    "6H":true,
    "4U":true,
    "WG":true,
    "TS":true
};	
var travelportAirlineIcons = {
    "0B": true,
    "0D": true,
    "1T": true,
    "2B": true,
    "2D": true,
    "2F": true,
    "2I": true,
    "2J": true,
    "2K": true,
    "2L": true,
    "2M": true,
    "2N": true,
    "2P": true,
    "2U": true,
    "2W": true,
    "2Z": true,
    "3B": true,
    "3C": true,
    "3E": true,
    "3H": true,
    "3K": true,
    "3L": true,
    "3O": true,
    "3R": true,
    "3S": true,
    "3U": true,
    "3X": true,
    "3Y": true,
    "4A": true,
    "4B": true,
    "4C": true,
    "4D": true,
    "4G": true,
    "4H": true,
    "4I": true,
    "4J": true,
    "4M": true,
    "4N": true,
    "4O": true,
    "4Q": true,
    "4T": true,
    "4U": true,
    "5B": true,
    "5C": true,
    "5D": true,
    "5E": true,
    "5H": true,
    "5J": true,
    "5L": true,
    "5N": true,
    "5O": true,
    "5P": true,
    "5Q": true,
    "5S": true,
    "5T": true,
    "5U": true,
    "5W": true,
    "6E": true,
    "6H": true,
    "6J": true,
    "6L": true,
    "6S": true,
    "6T": true,
    "6Y": true,
    "7A": true,
    "7B": true,
    "7C": true,
    "7D": true,
    "7E": true,
    "7F": true,
    "7G": true,
    "7H": true,
    "7I": true,
    "7J": true,
    "7Q": true,
    "7R": true,
    "7S": true,
    "7V": true,
    "7W": true,
    "7Y": true,
    "7Z": true,
    "8A": true,
    "8F": true,
    "8J": true,
    "8L": true,
    "8M": true,
    "8N": true,
    "8O": true,
    "8P": true,
    "8T": true,
    "8U": true,
    "8Z": true,
    "9B": true,
    "9C": true,
    "9D": true,
    "9F": true,
    "9H": true,
    "9J": true,
    "9K": true,
    "9M": true,
    "9N": true,
    "9T": true,
    "9U": true,
    "9V": true,
    "9W": true,
    "9Y": true,
    "A3": true,
    "A4": true,
    "A5": true,
    "A6": true,
    "A7": true,
    "A9": true,
    "AA": true,
    "AB": true,
    "AC": true,
    "AD": true,
    "AE": true,
    "AF": true,
    "AG": true,
    "AH": true,
    "AI": true,
    "AJ": true,
    "AK": true,
    "AM": true,
    "AP": true,
    "AQ": true,
    "AR": true,
    "AS": true,
    "AT": true,
    "AU": true,
    "AV": true,
    "AX": true,
    "AY": true,
    "AZ": true,
    "B0": true,
    "B2": true,
    "B6": true,
    "B7": true,
    "B8": true,
    "BA": true,
    "BD": true,
    "BE": true,
    "BF": true,
    "BG": true,
    "BH": true,
    "BI": true,
    "BJ": true,
    "BL": true,
    "BM": true,
    "BN": true,
    "BP": true,
    "BR": true,
    "BS": true,
    "BT": true,
    "BU": true,
    "BV": true,
    "BW": true,
    "BY": true,
    "C2": true,
    "C6": true,
    "C9": true,
    "CA": true,
    "CB": true,
    "CD": true,
    "CE": true,
    "CF": true,
    "CG": true,
    "CI": true,
    "CJ": true,
    "CL": true,
    "CM": true,
    "CN": true,
    "CO": true,
    "CP": true,
    "CS": true,
    "CT": true,
    "CU": true,
    "CV": true,
    "CX": true,
    "CY": true,
    "CZ": true,
    "D2": true,
    "D3": true,
    "D4": true,
    "D6": true,
    "D7": true,
    "D8": true,
    "D9": true,
    "DB": true,
    "DC": true,
    "DD": true,
    "DE": true,
    "DG": true,
    "DJ": true,
    "DL": true,
    "DN": true,
    "DP": true,
    "DR": true,
    "DT": true,
    "DV": true,
    "DX": true,
    "DY": true,
    "E8": true,
    "ED": true,
    "EF": true,
    "EG": true,
    "EH": true,
    "EI": true,
    "EK": true,
    "EL": true,
    "EN": true,
    "EP": true,
    "EQ": true,
    "ES": true,
    "ET": true,
    "EU": true,
    "EV": true,
    "EW": true,
    "EX": true,
    "EY": true,
    "EZ": true,
    "F7": true,
    "F9": true,
    "FB": true,
    "FC": true,
    "FD": true,
    "FG": true,
    "FI": true,
    "FJ": true,
    "FL": true,
    "FM": true,
    "FN": true,
    "FP": true,
    "FQ": true,
    "FR": true,
    "FS": true,
    "FT": true,
    "FU": true,
    "FV": true,
    "FW": true,
    "FY": true,
    "FZ": true,
    "G3": true,
    "G4": true,
    "G5": true,
    "G8": true,
    "G9": true,
    "GA": true,
    "GE": true,
    "GF": true,
    "GH": true,
    "GI": true,
    "GK": true,
    "GL": true,
    "GM": true,
    "GR": true,
    "GS": true,
    "GU": true,
    "GV": true,
    "GW": true,
    "GX": true,
    "GY": true,
    "GZ": true,
    "H1": true,
    "H2": true,
    "H4": true,
    "H6": true,
    "H7": true,
    "H9": true,
    "HA": true,
    "HD": true,
    "HE": true,
    "HF": true,
    "HG": true,
    "HI": true,
    "HM": true,
    "HO": true,
    "HP": true,
    "HR": true,
    "HS": true,
    "HU": true,
    "HV": true,
    "HW": true,
    "HX": true,
    "HY": true,
    "HZ": true,
    "I5": true,
    "IA": true,
    "IB": true,
    "IC": true,
    "IE": true,
    "IF": true,
    "IG": true,
    "IJ": true,
    "IQ": true,
    "IR": true,
    "IT": true,
    "IV": true,
    "IW": true,
    "IX": true,
    "IY": true,
    "IZ": true,
    "J0": true,
    "J1": true,
    "J2": true,
    "J3": true,
    "J8": true,
    "J9": true,
    "JA": true,
    "JC": true,
    "JD": true,
    "JE": true,
    "JH": true,
    "JI": true,
    "JJ": true,
    "JK": true,
    "JL": true,
    "JM": true,
    "JO": true,
    "JP": true,
    "JQ": true,
    "JR": true,
    "JS": true,
    "JT": true,
    "JU": true,
    "JV": true,
    "JX": true,
    "JY": true,
    "JZ": true,
    "K2": true,
    "K3": true,
    "K5": true,
    "K6": true,
    "K9": true,
    "KA": true,
    "KB": true,
    "KC": true,
    "KE": true,
    "KF": true,
    "KG": true,
    "KK": true,
    "KL": true,
    "KM": true,
    "KN": true,
    "KP": true,
    "KQ": true,
    "KS": true,
    "KU": true,
    "KX": true,
    "L4": true,
    "LA": true,
    "LB": true,
    "LE": true,
    "LF": true,
    "LG": true,
    "LH": true,
    "LI": true,
    "LJ": true,
    "LM": true,
    "LN": true,
    "LO": true,
    "LP": true,
    "LQ": true,
    "LR": true,
    "LS": true,
    "LT": true,
    "LU": true,
    "LV": true,
    "LW": true,
    "LX": true,
    "LY": true,
    "LZ": true,
    "M3": true,
    "M7": true,
    "M9": true,
    "MA": true,
    "MD": true,
    "ME": true,
    "MF": true,
    "MH": true,
    "MI": true,
    "MJ": true,
    "MK": true,
    "MN": true,
    "MO": true,
    "MP": true,
    "MQ": true,
    "MS": true,
    "MT": true,
    "MU": true,
    "MV": true,
    "MW": true,
    "MX": true,
    "MY": true,
    "MZ": true,
    "N2": true,
    "N7": true,
    "NF": true,
    "NG": true,
    "NH": true,
    "NI": true,
    "NJ": true,
    "NK": true,
    "NL": true,
    "NM": true,
    "NN": true,
    "NO": true,
    "NQ": true,
    "NR": true,
    "NT": true,
    "NU": true,
    "NW": true,
    "NX": true,
    "NY": true,
    "NZ": true,
    "O2": true,
    "O4": true,
    "O8": true,
    "OA": true,
    "OB": true,
    "OC": true,
    "OD": true,
    "OF": true,
    "OH": true,
    "OJ": true,
    "OK": true,
    "OL": true,
    "OM": true,
    "ON": true,
    "OP": true,
    "OQ": true,
    "OR": true,
    "OS": true,
    "OT": true,
    "OU": true,
    "OV": true,
    "OW": true,
    "OX": true,
    "OY": true,
    "OZ": true,
    "P0": true,
    "P2": true,
    "P4": true,
    "P5": true,
    "P8": true,
    "P9": true,
    "PB": true,
    "PC": true,
    "PD": true,
    "PE": true,
    "PF": true,
    "PG": true,
    "PH": true,
    "PJ": true,
    "PK": true,
    "PM": true,
    "PN": true,
    "PP": true,
    "PR": true,
    "PS": true,
    "PU": true,
    "PV": true,
    "PW": true,
    "PX": true,
    "PY": true,
    "PZ": true,
    "Q8": true,
    "QC": true,
    "QE": true,
    "QF": true,
    "QH": true,
    "QI": true,
    "QK": true,
    "QL": true,
    "QM": true,
    "QQ": true,
    "QR": true,
    "QS": true,
    "QV": true,
    "QX": true,
    "QZ": true,
    "R2": true,
    "R3": true,
    "R5": true,
    "R6": true,
    "R7": true,
    "RA": true,
    "RB": true,
    "RE": true,
    "RF": true,
    "RH": true,
    "RI": true,
    "RJ": true,
    "RO": true,
    "RQ": true,
    "RR": true,
    "RT": true,
    "RV": true,
    "RW": true,
    "RY": true,
    "S0": true,
    "S2": true,
    "S3": true,
    "S4": true,
    "S7": true,
    "S8": true,
    "SA": true,
    "SB": true,
    "SC": true,
    "SD": true,
    "SE": true,
    "SF": true,
    "SG": true,
    "SH": true,
    "SJ": true,
    "SK": true,
    "SL": true,
    "SN": true,
    "SP": true,
    "SQ": true,
    "SS": true,
    "ST": true,
    "SU": true,
    "SV": true,
    "SW": true,
    "SX": true,
    "SY": true,
    "SZ": true,
    "T3": true,
    "T5": true,
    "T7": true,
    "TA": true,
    "TB": true,
    "TC": true,
    "TD": true,
    "TF": true,
    "TG": true,
    "TH": true,
    "TJ": true,
    "TK": true,
    "TL": true,
    "TM": true,
    "TN": true,
    "TO": true,
    "TP": true,
    "TQ": true,
    "TR": true,
    "TS": true,
    "TT": true,
    "TU": true,
    "TV": true,
    "TW": true,
    "TX": true,
    "TY": true,
    "TZ": true,
    "U2": true,
    "U3": true,
    "U5": true,
    "U6": true,
    "U7": true,
    "U8": true,
    "U9": true,
    "UA": true,
    "UB": true,
    "UC": true,
    "UD": true,
    "UE": true,
    "UF": true,
    "UG": true,
    "UK": true,
    "UL": true,
    "UM": true,
    "UN": true,
    "UO": true,
    "UP": true,
    "US": true,
    "UT": true,
    "UU": true,
    "UX": true,
    "UY": true,
    "UZ": true,
    "V0": true,
    "V3": true,
    "V5": true,
    "V6": true,
    "V7": true,
    "VA": true,
    "VB": true,
    "VC": true,
    "VF": true,
    "VG": true,
    "VH": true,
    "VI": true,
    "VK": true,
    "VN": true,
    "VO": true,
    "VR": true,
    "VS": true,
    "VT": true,
    "VU": true,
    "VV": true,
    "VW": true,
    "VX": true,
    "VY": true,
    "VZ": true,
    "W2": true,
    "W3": true,
    "W5": true,
    "W6": true,
    "W9": true,
    "WA": true,
    "WB": true,
    "WC": true,
    "WE": true,
    "WF": true,
    "WG": true,
    "WH": true,
    "WJ": true,
    "WK": true,
    "WM": true,
    "WN": true,
    "WO": true,
    "WP": true,
    "WS": true,
    "WT": true,
    "WU": true,
    "WW": true,
    "WX": true,
    "WY": true,
    "X3": true,
    "X8": true,
    "XE": true,
    "XF": true,
    "XJ": true,
    "XK": true,
    "XL": true,
    "XM": true,
    "XN": true,
    "XQ": true,
    "XR": true,
    "XT": true,
    "XU": true,
    "XW": true,
    "XY": true,
    "XZ": true,
    "Y4": true,
    "Y6": true,
    "Y8": true,
    "Y9": true,
    "YE": true,
    "YI": true,
    "YJ": true,
    "YM": true,
    "YN": true,
    "YO": true,
    "YQ": true,
    "YS": true,
    "YU": true,
    "YV": true,
    "YW": true,
    "YX": true,
    "Z2": true,
    "Z5": true,
    "Z6": true,
    "ZB": true,
    "ZC": true,
    "ZH": true,
    "ZI": true,
    "ZJ": true,
    "ZK": true,
    "ZL": true,
    "ZN": true,
    "ZS": true,
    "ZU": true,
    "ZW": true,
    "DI": true
  };
